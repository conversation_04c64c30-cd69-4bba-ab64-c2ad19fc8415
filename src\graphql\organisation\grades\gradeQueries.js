import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const LIST_PARENT_GRADES = gql`
  query listParentGrades($gradeId: Int!) {
    listParentGrades(gradeId: $gradeId) {
      errorCode
      message
      listParentGradesData {
        parentGradeId
        parentGradeCode
        parentGrade
      }
    }
  }
`;

export const LIST_EMPLOYEE_GRADE = gql`
  query listEmployeeGrade {
    listEmployeeGrade {
      errorCode
      message
      listEmployeeGradeData {
        gradeId
        gradeCode
        grade
        parentGradeId
        parentGrade
        minExperience
        maxExperience
        minHourWages
        maxHourWages
        minOvertimeWages
        maxOvertimeWages
        description
        minAnnualSalary
        maxAnnualSalary
        overTimeFixedAmount
        eligibleOvertime
        overTimeAllocation
        overTimeWageIndex
        addedOn
        addedByName
        updatedOn
        updatedByName
        level
      }
    }
  }
`;

export const ADD_UPDATE_EMPLOYEE_GRADE = gql`
  mutation addUpdateEmployeeGrade(
    $gradeId: Int!
    $parentId: Int
    $gradeCode: String
    $employeeGrade: String!
    $minHourlyWages: Float
    $maxHourlyWages: Float
    $minOvertimeWages: Float
    $maxOvertimeWages: Float
    $description: String
    $minAnnualSalary: Float!
    $maxAnnualSalary: Float!
    $overtimeFixedAmount: Float
    $eligibleOvertime: Int
    $overtimeAllocation: String
    $overtimeWageIndex: Float
    $level: Int
  ) {
    addUpdateEmployeeGrade(
      gradeId: $gradeId
      parentId: $parentId
      gradeCode: $gradeCode
      employeeGrade: $employeeGrade
      minHourlyWages: $minHourlyWages
      maxHourlyWages: $maxHourlyWages
      minOvertimeWages: $minOvertimeWages
      maxOvertimeWages: $maxOvertimeWages
      description: $description
      minAnnualSalary: $minAnnualSalary
      maxAnnualSalary: $maxAnnualSalary
      overtimeFixedAmount: $overtimeFixedAmount
      eligibleOvertime: $eligibleOvertime
      overtimeAllocation: $overtimeAllocation
      overtimeWageIndex: $overtimeWageIndex
      level: $level
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_EMPLOYEE_GRADE = gql`
  mutation deleteEmployeeGrade($gradeId: Int!) {
    deleteEmployeeGrade(gradeId: $gradeId) {
      errorCode
      message
    }
  }
`;
