<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template v-slot:topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="8" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="false"
              ></EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList && !isLoading"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !isLoading"
            key="no-results-screen"
            :main-title="
              originalList?.length
                ? 'There are no records for the selected filters/searches.'
                : ''
            "
            :image-name="originalList?.length === 0 ? '' : 'common/no-records'"
            :isSmallImage="originalList.length === 0"
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center align-center">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="The 'My Payslip' feature in the Employee Self-Service (ESS) portal allows you to securely view and download your monthly salary slips anytime, anywhere. With just a few clicks, you can access detailed information about your earnings, deductions, and net pay for any selected month. This convenient feature helps you stay informed about your payroll details without needing to contact HR."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="You can also access and download previous payslips for your records, with options to print or save them as PDF files. Designed to promote transparency and ease of use, the 'My Payslip' section ensures that all your salary-related information is just a click away—secure, accessible, and always up to date."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      class="mt-1"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter()"
                    >
                      Reset Filter/Search
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div
              v-if="originalList.length > 0"
              class="d-flex align-center my-3"
              :class="
                isMobileView ? 'justify-center flex-column' : 'justify-end'
              "
            >
              <v-btn class="bg-white my-2" rounded="lg">
                <template v-slot:prepend>
                  <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                </template>
                {{ formattedSelectedMonth }}
                <v-menu
                  activator="parent"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                >
                  <Datepicker
                    v-model="selectedYear"
                    :value="selectedYear"
                    :inline="true"
                    :format="'YYYY'"
                    maximum-view="year"
                    minimum-view="year"
                    :disabled-dates="getDisabledDates"
                    @update:modelValue="onChangeDate($event)"
                  />
                </v-menu>
              </v-btn>
              <v-btn
                rounded="lg"
                class="mt-1"
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList('Refetch List')"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    class="mt-1 ml-n3 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action"
                    @click="onMoreAction(action.key)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                        >
                          <v-tooltip :text="action.message">
                            <template v-slot:activator="{ props }">
                              <div v-bind="action.message ? props : ''">
                                <v-icon size="15" class="pr-2">{{
                                  action.icon
                                }}</v-icon>
                                {{ action.key }}
                              </div>
                            </template>
                          </v-tooltip>
                        </v-list-item-title>
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
            <v-row>
              <v-col cols="12" class="mb-12">
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Salary Month
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{ checkNullValue(item.Salary_Period) }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Total Earnings
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{
                            checkNullValue(item.Total_Earnings) === "-"
                              ? item.File_Path
                                ? "0"
                                : "-"
                              : checkNullValue(item.Total_Earnings)
                          }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Total Deductions
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{
                            checkNullValue(item.Total_Deductions) === "-"
                              ? item.File_Path
                                ? "0"
                                : "-"
                              : checkNullValue(item.Total_Deductions)
                          }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Net Pay
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{
                            checkNullValue(item.Net_Pay) === "-"
                              ? item.File_Path
                                ? "0"
                                : "-"
                              : checkNullValue(item.Net_Pay)
                          }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        ></div>
                        <section
                          :style="isMobileView ? 'max-width: 60%' : ''"
                          class="text-blue text-decoration-underline"
                          @click="onClickGenerate('generate', item)"
                        >
                          View Payslip
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
  <AppLoading v-if="isLoading" />
  <file-preview-modal
    v-if="showPayslip"
    :heading="'Payslip ' + selectedItem.Salary_Period"
    :fileName="loginEmployeeId + '/' + selectedItem.File_Path + '.pdf'"
    :folderName="'Syntrum Salary Payslip'"
    @close-preview-modal="showPayslip = false"
  ></file-preview-modal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import {
  GET_MY_PAYSLIP,
  GENERATE_MY_PAYSLIP,
} from "@/graphql/my-finance/myPayslip";

const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

import FileExportMixin from "@/mixins/FileExportMixin";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
export default {
  name: "MyPayslips",
  components: {
    NotesCard,
    Datepicker,
    EmployeeDefaultFilterMenu,
    FilePreviewModal,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "",
      allForms: [346, 345],
      listLoading: false,
      isLoading: false,
      isErrorInList: false,
      errorContent: "",
      originalList: [],
      itemList: [],
      openMoreMenu: false,
      selectedYear: new Date(),
      dateOfJoin: "",
      selectedItem: null,
      showPayslip: false,
    };
  },
  computed: {
    mainTabs() {
      let tabs = [];
      tabs = this.allVisibleTabs.map((tab) => {
        return tab.formName;
      });
      return tabs;
    },
    allVisibleTabs() {
      let tabs = [];
      tabs = this.allForms
        .map((tab) => {
          let form = this.accessRights(tab);
          if (
            form?.accessRights?.view ||
            form.customFormName === this.landedFormName
          )
            return { formName: form.customFormName, formId: form.formId };
        })
        .filter((tab) => tab);
      return tabs;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    landedFormName() {
      return this.accessRights(345)?.customFormName;
    },
    formAccess() {
      let formAccessRights = this.accessRights(345);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      return [{ key: "Export", icon: "fas fa-file-export" }];
    },
    formattedSelectedMonth() {
      return moment(this.selectedYear).format("YYYY");
    },
    getDisabledDates() {
      return {
        from: new Date(),
        to: new Date(this.dateOfJoin),
      };
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    tableHeaders() {
      let headers = [
        {
          title: "Salary Month",
          align: "start",
          key: "Salary_Period",
          width: "20%",
        },
        {
          title: "Total Earnings",
          key: "Total_Earnings",
          width: "20%",
        },
        {
          title: "Total Deductions",
          key: "Total_Deductions",
          width: "20%",
        },
        {
          title: "Net Pay",
          key: "Net_Pay",
          width: "20%",
        },
        {
          title: "Payslip",
          sortable: false,
          width: "20%",
        },
      ];
      return headers;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchPayslips();
  },
  methods: {
    checkNullValue,
    onTabChange(tabName) {
      let form = this.allVisibleTabs.find((tab) => tab.formName === tabName);
      if (form && form.formId) {
        if (form.formId == 346) {
          this.$router.push("/my-finance/my-pay");
        }
      }
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.resetFilter();
      this.fetchPayslips();
    },
    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onMoreAction(action) {
      this.openMoreMenu = false;
      if (action?.toLowerCase() === "export") {
        this.exportReportFile();
      }
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: "Salary Month",
          key: "Salary_Period",
        },
        {
          header: "Total Earnings",
          key: "Total_Earnings",
        },
        {
          header: "Total Deductions",
          key: "Total_Deductions",
        },
        {
          header: "Net Pay",
          key: "Net_Pay",
        },
        {
          header: "Added On",
          key: "Added_On",
        },
        {
          header: "Added By",
          key: "Added_By",
        },
      ];
      let dataList = this.itemList.map((item) => {
        return {
          ...item,
          Total_Earnings: item.File_Path ? item.Total_Earnings : "",
          Total_Deductions: item.File_Path ? item.Total_Deductions : "",
          Net_Pay: item.File_Path ? item.Net_Pay : "",
          Added_On: moment(item.Added_On).isValid()
            ? convertUTCToLocal(item.Added_On)
            : "",
        };
      });

      const exportOptions = {
        fileExportData: dataList,
        fileName: "Payslip Report",
        sheetName: "Payslips",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
      }
    },
    onChangeDate(event) {
      this.selectedMonthYear = event;
      this.fetchPayslips();
    },
    fetchPayslips() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_MY_PAYSLIP,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            year: parseInt(moment(this.selectedYear).format("YYYY")),
            formId: 345,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listSalaryPayslip &&
            !response.data.listSalaryPayslip.errorCode
          ) {
            vm.itemList = JSON.parse(
              response.data.listSalaryPayslip.payslipDetails
            );
            vm.originalList = JSON.parse(
              response.data.listSalaryPayslip.payslipDetails
            );
            let { employeeDateOfJoin, payrollStartDate } =
              response.data.listSalaryPayslip;
            vm.dateOfJoin =
              new Date(employeeDateOfJoin) > new Date(payrollStartDate)
                ? employeeDateOfJoin
                : payrollStartDate;
            vm.listLoading = false;
          } else {
            vm.originalList = [];
            vm.itemList = [];
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.isErrorInList = true;
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "payslips",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
        });
    },
    onClickGenerate(type, item) {
      this.selectedItem = item;
      if (type?.toLowerCase() === "generate") {
        this.generatePayslip();
      }
    },
    generatePayslip() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: GENERATE_MY_PAYSLIP,
          client: "apolloClientAH",
          variables: {
            employeeId: vm.loginEmployeeId,
            month: vm.selectedItem.Salary_Month,
            year: vm.selectedItem.Salary_Year,
            formId: 345,
          },
        })
        .then((response) => {
          vm.isLoading = false;
          if (response?.data?.generateSyntrumPayslipDetails?.data) {
            this.selectedItem.File_Path =
              response.data.generateSyntrumPayslipDetails.data;
            this.showPayslip = true;
            vm.refetchList();
          } else {
            vm.showAlert({
              isOpen: true,
              message: "Payslip generation failed",
              type: "warning",
            });
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          if (err) {
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              form: "payslip",
              action: "generating",
              isListError: false,
            });
          }
        });
    },
    showAlert(data) {
      this.$store.commit("OPEN_SNACKBAR", data);
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
