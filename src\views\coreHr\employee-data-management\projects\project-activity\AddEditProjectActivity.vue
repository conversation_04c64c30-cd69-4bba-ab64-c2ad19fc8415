<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">Activity</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              variant="elevated"
              class="mb-2 primary"
              @click="validateForm"
              >Save</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Save</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" class="mr-1"> fas fa-times </v-icon>
        </div>
      </div>
      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        :style="
          isMobileView ? 'height: calc(100vh - 260px); overflow: scroll' : ''
        "
      >
        <v-card-text>
          <v-form ref="projectActivityForm">
            <v-row>
              <v-col cols="12" sm="6" md="6">
                <v-text-field
                  v-model="activityName"
                  type="text"
                  variant="solo"
                  @update:model-value="isFormDirty = true"
                  :rules="[
                    required('Activity', activityName),
                    activityName
                      ? validateWithRulesAndReturnMessages(
                          activityName,
                          'activityName',
                          'Activity'
                        )
                      : true,
                  ]"
                  style="max-width: 400px"
                >
                  <template v-slot:label>
                    <span>Activity</span>
                    <span class="ml-1" style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6">
                <div class="d-flex flex-column">
                  <span class="v-label">Billable</span>
                  <v-switch
                    color="primary"
                    class="mt-n2"
                    v-model="billable"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                  ></v-switch>
                </div>
              </v-col>
              <v-col cols="12" lg="6" md="6">
                <div class="v-label mb-4">Description</div>
                <v-textarea
                  v-model="description"
                  variant="solo"
                  auto-grow
                  rows="1"
                  :rules="[
                    description
                      ? validateWithRulesAndReturnMessages(
                          description,
                          'description',
                          'Description'
                        )
                      : true,
                  ]"
                  @update:model-value="isFormDirty = true"
                  style="max-width: 400px"
                  :counter="600"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 primary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>
<script>
import validationRules from "@/mixins/validationRules";
// Queries
import { ADD_UPDATE_PROJECT_ACTIVITIES } from "@/graphql/corehr/projectActivityQueries.js";
export default {
  name: "AddEditProjectActivities",
  mixins: [validationRules],
  props: {
    selectedItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["close-form", "refetch-data"],
  data() {
    return {
      isMounted: false,
      isLoadingDetails: false,
      openConfirmationPopup: false,
      isFormDirty: false,
      validationMessages: [],
      showValidationAlert: false,
      activityName: null,
      description: "",
      billable: "No",
      activityId: null,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    if (this.isEdit) {
      const { activityId, activityName, description, isBillable } =
        this.selectedItem;
      (this.activityId = activityId ? activityId : null),
        (this.description = description ? description : "");
      this.activityName = activityName ? activityName : "";
      this.billable = isBillable ? isBillable : "No";
    }
    this.isFormDirty = false;
    this.isMounted = true;
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },
    async validateForm() {
      const { valid } = await this.$refs.projectActivityForm.validate();
      if (valid) {
        this.addUpdateData();
      }
    },
    addUpdateData() {
      let vm = this;
      vm.isLoadingDetails = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_PROJECT_ACTIVITIES,
            variables: {
              activityId: vm.activityId ? parseInt(vm.activityId) : 0,
              activityName: vm.activityName ? vm.activityName : "",
              isBillable: vm.billable ? vm.billable : null,
              description: vm.description ? vm.description : null,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoadingDetails = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.isEdit
                ? "Activity details updated successfully."
                : "Activity details added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.isLoadingDetails = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: "project activity",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
