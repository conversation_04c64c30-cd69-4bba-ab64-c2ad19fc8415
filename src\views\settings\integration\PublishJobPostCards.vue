<template>
  <v-card
    :style="isMobileView ? '' : 'width:500px'"
    class="rounded-lg mb-10 py-3"
  >
    <div class="mx-7 d-flex justify-space-between" style="height: 50px">
      <div class="d-flex" style="width: 200px">
        <img
          :src="logo"
          style="width: 50px; height: auto"
          class=""
          :alt="name"
        />
        <v-card-title>{{ name }}</v-card-title>
      </div>
      <JobPostPublishModal
        :jobPostId="jobPostId"
        :name="name"
        :jobPostData="jobPostData"
      >
        ></JobPostPublishModal
      >
    </div>
    <div style="background-color: rgb(247 247 247)" class="pa-1 my-2">
      <div bg-color="grey-lighten-2" class="ma-4 text-justify text-subtitle-1">
        Irukka, your employment-centric social platform, simplifies job postings
        and connects with the right candidates. Ensure your profile's security
        with OTP verification; click 'Integrate Now' to begin
      </div>
    </div>
  </v-card>
</template>
<script>
import { defineAsyncComponent } from "vue";
const JobPostPublishModal = defineAsyncComponent(() =>
  import("./recruitment/JobPostPublishModal.vue")
);
export default {
  components: { JobPostPublishModal },
  data: () => {
    return {
      configureSwitch: false,
      overlay: false,
      clientId:
        "572faa62f864861464fd06518b8a4f5e2f8f762c7f6a93b640858defe48bff5b",
      secretKey: null,
      authCode: null,
      refreshToken: null,
      accessToken: null,
    };
  },
  props: {
    logo: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
    isMobileView: {
      type: Boolean,
      default: false,
    },
    jobPostId: {
      type: Number,
      required: true,
    },
    jobPostData: {
      type: Object,
      required: true,
    },
  },
  watch: {},
};
</script>

<style scoped lang="scss">
.right-sticky-bar {
  padding: 0px;
  margin: 0px;
  position: fixed;
  right: 0px;
  top: 0px;
  height: 99%;
  background: white;
  z-index: 5;
  box-shadow: 0px 1px 15px rgba(15, 84, 136, 0.14) !important;
}
.right-sticky-bar-content {
  margin-left: 6px;
  max-width: 300px;
  position: absolute;
}
#overlay-card {
  height: 100%;
  width: 100%;
  background: white;
}
.v-overlay__content {
  height: 100%;
  width: 500px;
}
#overlay-head {
  background: rgb(238, 236, 236);
  height: 7%;
  width: 100%;
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: x-large;
  font-weight: 500;
}
#overlay-body {
  padding: 15px;
}
</style>
