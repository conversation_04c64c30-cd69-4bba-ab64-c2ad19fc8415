<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "Add" }}
          </v-chip>
          <span class="text-subtitle-1 font-weight-bold">Shift Swap</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-tooltip
              text="Either you do not have any shift assigned on the selected date or you do not have any manager"
              v-if="isFormDirty"
            >
              <template v-slot:activator="{ props }">
                <v-btn
                  rounded="lg"
                  variant="elevated"
                  v-bind="disableSaveButton ? props : ''"
                  class="mb-2 primary"
                  :class="disableSaveButton ? 'cursor-not-allow' : ''"
                  :disabled="preRequisites"
                  @click="validateForm()"
                  >Submit</v-btn
                >
              </template>
            </v-tooltip>

            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Submit</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon color="primary" @click="closeAddForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-5'"
        style="height: calc(100vh - 260px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="shiftSwapForm">
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-text-field
                  label="Employee Name"
                  variant="solo"
                  disabled
                  v-model="employeeName"
                >
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-select
                  label="Approver"
                  variant="solo"
                  disabled
                  v-model="swapApprover"
                  :rules="[required('Approver', swapApprover)]"
                  item-title="managerName"
                  item-value="managerId"
                  :loading="managerLoading"
                >
                </v-select>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-menu
                  v-model="swapDate"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="swapDate"
                      v-model="formatedSwapDate"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[required('Swap Date', formatedSwapDate)]"
                      readonly
                      v-bind="props"
                      variant="solo"
                    >
                      <template v-slot:label>
                        Shift Swap Date
                        <span class="text-red">*</span>
                      </template>
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="selectedDate"
                    :min="minDate"
                    :max="maxDate"
                    @update:model-value="onshiftChange()"
                  />
                </v-menu>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <custom-select
                  label="Shift Type"
                  :items="shiftList"
                  item-title="Shift_Name"
                  item-value="Shift_Id"
                  :loading="shiftListLoading"
                  :rules="[required('Shift Type', selectedShift)]"
                  :isRequired="true"
                  :item-selected="selectedShift"
                  @selected-item="
                    (selectedShift = $event), (isFormDirty = true)
                  "
                ></custom-select>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-select
                  label="Shift assigned on the selected date"
                  :loading="shiftListLoading"
                  item-title="Shift_Name"
                  item-value="Shift_Id"
                  variant="solo"
                  :rules="[required('Shft assigned', currentShift)]"
                  disabled
                  v-model="currentShift"
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-textarea
                  v-model="selectedReason"
                  ref="reason"
                  variant="solo"
                  auto-grow
                  :rules="[
                    required('Reason', selectedReason),
                    validateWithRulesAndReturnMessages(
                      selectedReason,
                      'shiftSwapReason',
                      'Reason'
                    ),
                  ]"
                  @update:model-value="
                    (selectedReason = $event), (isFormDirty = true)
                  "
                  rows="2"
                >
                  <template v-slot:label
                    >Reason<span class="text-red">*</span></template
                  ></v-textarea
                >
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>
<script>
import moment from "moment";
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { RETRIEVE_SECONDLINE_MANAGER } from "@/graphql/onboarding/individualQueries.js";
import {
  RETRIEVE_SHIFT_LIST,
  ADD_EDIT_SHIFT_SWAP,
  LIST_OVERTIME_PREREQUISITES,
  GET_SHIFT_SWAP_RANGE,
} from "@/graphql/roster-management/ShiftSwapQueries.js";
export default {
  name: "AddEditShiftSwap",
  emits: ["close-form", "added-record"],
  mixins: [validationRules],
  components: {
    CustomSelect,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    selectedItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      isFormDirty: false,
      isLoading: false,
      managerLoading: false,
      swapApprover: null,
      employeeName: "",
      swapDate: false,
      formatedSwapDate: "",
      selectedDate: null,
      selectedShift: null,
      selectedReason: "",
      shiftList: [],
      shiftListLoading: false,
      currentShift: null,
      preRequisites: false,
      minDate: "",
      maxDate: "",
    };
  },
  computed: {
    loginEmployee() {
      return this.$store.state.orgDetails.userDetails;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    disableSaveButton() {
      return !(this.currentShift && this.swapApprover);
    },
  },
  watch: {
    selectedDate(val) {
      if (val) {
        this.swapDate = false;
        let dateValue = this.formatDate(val);
        this.formatedSwapDate = dateValue;
      }
    },
  },

  mounted() {
    this.employeeName =
      this.loginEmployee.employeeFullName + "-" + this.loginEmployee.employeeId;
    if (this.isEdit) {
      this.selectedDate = new Date(this.selectedItem.Swap_Date);
      this.swapApprover = {
        managerId: this.selectedItem.Approver_Id,
        managerName: this.selectedItem.Approver_Name,
      };
      this.selectedReason = this.selectedItem.Reason;
    } else {
      let currentDate = moment().add(1, "days").format("YYYY-MM-DD");
      this.selectedDate = new Date(currentDate);
      this.getManagerDetails();
    }
    this.fetchDateRange();
    this.fetchShiftList();
  },
  methods: {
    closeAddForm() {
      this.$emit("close-form");
    },
    getManagerDetails() {
      let vm = this;
      vm.managerLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_SECONDLINE_MANAGER,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: { employeeId: this.loginEmployee.employeeId },
        })
        .then(({ data }) => {
          vm.managerLoading = false;
          if (data && data.retrieveManagerDetails) {
            const response = data.retrieveManagerDetails;
            if (response.errorCode === "" || response.errorCode === null) {
              vm.swapApprover = response;
            } else {
              vm.handleManagerError(response.message);
            }
          } else {
            vm.handleManagerError();
          }
        })
        .catch((err) => {
          vm.handleManagerError(err);
        });
    },
    handleManagerError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "individuals",
          isListError: false,
        })
        .then((errorMessages) => {
          var snackbarData = {
            isOpen: true,
            type: "warning",
            message: errorMessages,
          };
          vm.showAlert(snackbarData);
        });
    },

    fetchShiftList() {
      let vm = this;
      vm.shiftListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_SHIFT_LIST,
          client: "apolloClientC",
          fetchPolicy: "no-cache",
          variables: {
            swapDate:
              this.selectedDate &&
              this.selectedDate !== "0000-00-00" &&
              this.selectedDate != "Invalid Date"
                ? moment(this.selectedDate).format("YYYY-MM-DD")
                : "",
            employeeId: this.loginEmployee.employeeId,
            isDropDown: 1,
          },
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.listShiftType &&
            data.listShiftType.shiftType &&
            data.listShiftType.shiftType.length > 0
          ) {
            let shiftTypeList = data.listShiftType.shiftType.filter((item) => {
              return (
                item.loginEmployeeIdShift !== "true" &&
                item.Status.toLowerCase() === "active"
              );
            });
            vm.currentShift = data.listShiftType.shiftType.find((item) => {
              return item.loginEmployeeIdShift == "true";
            });
            vm.shiftList = shiftTypeList;
          }
          if (this.isEdit && !this.isFormDirty) {
            this.selectedShift = this.selectedItem.Swap_Shift_Type_Id;
          }
          vm.shiftListLoading = false;
        })
        .catch(() => {
          vm.shiftListLoading = false;
        });
    },

    async validateForm() {
      let { valid } = await this.$refs.shiftSwapForm.validate();
      if (valid) {
        this.addEditShiftSwap();
      }
    },

    addEditShiftSwap() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_EDIT_SHIFT_SWAP,
          variables: {
            employeeId: this.loginEmployee.employeeId,
            swapDate: moment(this.selectedDate).format("YYYY-MM-DD"),
            swapShiftTypeId: this.selectedShift,
            reason: vm.selectedReason,
            approverId: this.swapApprover.managerId,
            status: "Applied",
            swapId: this.isEdit ? this.selectedItem.Swap_Id : 0,
          },
          client: "apolloClientM",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.addUpdateEmployeeShiftSwap &&
            !data.addUpdateEmployeeShiftSwap.errorCode
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Shift Swap added successfully!",
            };
            vm.showAlert(snackbarData);
            vm.$emit("added-record");
            vm.isLoading = false;
          } else {
            vm.handleAddError(data.addUpdateEmployeeShiftSwap.errorCode);
          }
        })
        .catch((error) => {
          this.handleAddError(error);
        });
    },
    handleAddError(error) {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "adding",
        form: "shift swap",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onshiftChange() {
      this.isFormDirty = true;
      this.selectedShift = null;
      this.fetchShiftList();
      this.getUserShiftDetails();
    },
    getUserShiftDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: LIST_OVERTIME_PREREQUISITES,
          client: "apolloClientC",
          fetchPolicy: "no-cache",
          variables: {
            employeeId: this.loginEmployee.employeeId,
            otStartTime: "",
            shiftDate: this.selectedDate
              ? moment(this.selectedDate).format("YYYY-MM-DD")
              : "",
          },
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.getOvertimePrerequisites &&
            data.getOvertimePrerequisites.otPreRequisites
          ) {
            let { otPreRequisites } = data.getOvertimePrerequisites;
            otPreRequisites = JSON.parse(otPreRequisites);
            let attendancePresent = false,
              compOffPresent = false,
              leavePresent = false;
            let msg = "";
            if (
              otPreRequisites.attendanceDetails &&
              otPreRequisites.attendanceDetails.length > 0
            ) {
              attendancePresent = true;
              msg =
                "You cannot apply for a shift swap as attendance has already been marked for the selected date.";
            }
            if (
              otPreRequisites.compOffDetails &&
              otPreRequisites.compOffDetails.length > 0
            ) {
              compOffPresent = true;
              msg =
                "You cannot apply for a shift swap as a comp-off has already been applied for the selected date.";
            }
            if (
              otPreRequisites.leaveDetails &&
              otPreRequisites.leaveDetails.length > 0
            ) {
              leavePresent = true;
              msg =
                "You cannot apply for a shift swap as a leave has already been applied for the selected date.";
            }
            vm.preRequisites =
              attendancePresent || compOffPresent || leavePresent;
            if (vm.preRequisites) {
              let snackbarData = {
                isOpen: true,
                type: "warning",
                message: msg,
              };
              vm.showAlert(snackbarData);
            }
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          this.handleRetrievePrerequisitesError(err);
        });
    },
    handleRetrievePrerequisitesError(error = "") {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: "shift swap prerequisites",
        isListError: false,
      });
    },
    fetchDateRange() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_SHIFT_SWAP_RANGE,
          client: "apolloClientL",
          fetchPolicy: "no-cache",
          variables: {
            employeeId: this.loginEmployee.employeeId,
          },
        })
        .then((response) => {
          let { data } = response;
          if (data && data.getShiftSwapDateRange) {
            let { minimumDate, maximumDate } = data.getShiftSwapDateRange;
            vm.minDate = minimumDate;
            vm.maxDate = maximumDate;
          } else {
            vm.minDate = moment().add(1, "days").format("YYYY-MM-DD");
            vm.maxDate = "";
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.minDate = moment().add(1, "days").format("YYYY-MM-DD");
          vm.maxDate = "";
          this.$store.dispatch("handleApiErrors", {
            error,
            action: "retrieving",
            form: "shift swap date range",
            isListError: false,
          });
        });
    },
  },
};
</script>
