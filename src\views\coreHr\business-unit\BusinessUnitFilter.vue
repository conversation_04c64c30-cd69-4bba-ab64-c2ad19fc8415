<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12">
            <v-text-field
              v-model="businessUnitCode"
              label="Business Unit / Cost Center Code"
              density="compact"
              single-line
              variant="solo"
            >
            </v-text-field
          ></v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12">
            <v-text-field
              v-model="parentCode"
              label="Parent Unit Id"
              density="compact"
              single-line
              variant="solo"
            >
            </v-text-field
          ></v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12">
            <v-text-field
              v-model="businessUnit"
              label="Business Unit / Cost Center"
              density="compact"
              single-line
              variant="solo"
            >
            </v-text-field
          ></v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12">
            <v-radio-group inline color="primary" v-model="status">
              <template v-slot:label> Status </template>
              <v-radio label="Active" value="Active"></v-radio>
              <v-radio label="Inactive" value="InActive"></v-radio>
            </v-radio-group>
          </v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "FormFilter",
  data: () => ({
    openFormFilter: false,
    businessUnitCode: null,
    businessUnit: "",
    parentCode: null,
    status: null,
  }),

  props: {},

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {},
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        businessUnitCode: this.businessUnitCode,
        businessUnit: this.businessUnit,
        parentCode: this.parentCode,
        status: this.status,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.businessUnitCode = null;
      this.businessUnit = "";
      this.parentCode = null;
      this.status = null;
    },
  },
};
</script>
