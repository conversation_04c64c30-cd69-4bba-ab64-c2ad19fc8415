import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_TAX_RELIEF_DECLARATIONS = gql`
  query listTaxReliefDeclarations($assessmentYear: String!) {
    listTaxReliefDeclarations(assessmentYear: $assessmentYear) {
      errorCode
      message
      taxReliefDeclaration
    }
  }
`;

export const LIST_TAX_RELIEF_CATEGORIES = gql`
  query listTaxReliefCategories($assessmentYear: String!) {
    listTaxReliefCategories(assessmentYear: $assessmentYear) {
      errorCode
      message
      taxReliefCategories
    }
  }
`;

export const RETRIEVE_MAX_PAYSLIP_MONTH = gql`
  query retrieveMaxPayslip($salaryYear: Int, $employeeIds: [Int]) {
    retrieveMaxPayslip(salaryYear: $salaryYear, employeeIds: $employeeIds) {
      errorCode
      message
      maxPayslip {
        User_Defined_EmpId
        Employee_Id
        Max_Payslip
      }
    }
  }
`;

export const GET_ASSESSMENT_YEARS = gql`
  query getHousePropertyStaticData {
    getHousePropertyStaticData {
      errorCode
      message
      housePropertyStaticData
    }
  }
`;
// ===============
// Mutations
// ===============
export const ADD_UPDATE_TAX_RELIEF = gql`
  mutation addUpdateTaxReliefDeclaration(
    $Tax_Relief_Declaration_Id: Int
    $Tax_Relief_Category_Id: Int!
    $Employee_Id: Int!
    $Mon_Jan: String
    $Mon_Feb: String
    $Mon_Mar: String
    $Mon_Apr: String
    $Mon_May: String
    $Mon_Jun: String
    $Mon_Jul: String
    $Mon_Aug: String
    $Mon_Sep: String
    $Mon_Oct: String
    $Mon_Nov: String
    $Mon_Dec: String
    $Assessment_Year: String!
    $Documents: [String]
  ) {
    addUpdateTaxReliefDeclaration(
      Tax_Relief_Declaration_Id: $Tax_Relief_Declaration_Id
      Tax_Relief_Category_Id: $Tax_Relief_Category_Id
      Employee_Id: $Employee_Id
      Mon_Jan: $Mon_Jan
      Mon_Feb: $Mon_Feb
      Mon_Mar: $Mon_Mar
      Mon_Apr: $Mon_Apr
      Mon_May: $Mon_May
      Mon_Jun: $Mon_Jun
      Mon_Jul: $Mon_Jul
      Mon_Aug: $Mon_Aug
      Mon_Sep: $Mon_Sep
      Mon_Oct: $Mon_Oct
      Mon_Nov: $Mon_Nov
      Mon_Dec: $Mon_Dec
      Assessment_Year: $Assessment_Year
      Documents: $Documents
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_TAX_RELIEF_STATUS = gql`
  mutation updateTaxReliefStatus(
    $taxReliefDeclarationId: Int!
    $approvalStatus: String!
    $approvedAmount: String
    $comment: String
  ) {
    updateTaxReliefStatus(
      taxReliefDeclarationId: $taxReliefDeclarationId
      approvalStatus: $approvalStatus
      approvedAmount: $approvedAmount
      comment: $comment
    ) {
      errorCode
      message
    }
  }
`;

export const IMPORT_TAX_RELIEF = gql`
  mutation importTaxRelief($taxReliefData: [taxRelief]) {
    importTaxRelief(taxReliefData: $taxReliefData) {
      errorCode
      message
      validationError
    }
  }
`;
