<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div
        v-if="!showEmployeesList"
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">LOP Recovery</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              variant="elevated"
              class="mb-2 primary"
              @click="validateLopRecoveryForm"
              >Save</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Save</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" color="primary" class="mr-1">
            fas fa-times
          </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="overflow: scroll"
        :style="
          showEmployeesList
            ? 'height: calc(100vh - 200px)'
            : 'height: calc(100vh - 260px)'
        "
      >
        <v-card-text>
          <v-form v-if="!showEmployeesList" ref="lopRecoveryForm">
            <v-row>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="openCustomGroupDropDown"
              >
                <div class="d-flex">
                  <CustomSelect
                    :items="customGroupList"
                    label="Custom Group"
                    :isRequired="true"
                    :itemSelected="customGroupId"
                    @selected-item="
                      onChangeIsFormDirty($event, 'customGroupId')
                    "
                    item-title="Custom_Group_Name"
                    item-value="Custom_Group_Id"
                    :is-auto-complete="true"
                    :is-loading="customGroupLoading"
                    :disabled="customGroupLoading"
                    :rules="[required('Custom Group', customGroupId)]"
                    style="max-width: 300px"
                  >
                  </CustomSelect>
                  <v-btn
                    rounded="lg"
                    class="ml-2 mt-2 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="retrieveCustomGroups()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
                <div class="mb-1">
                  <v-btn
                    color="primary"
                    variant="text"
                    :href="baseUrl + 'in/core-hr/custom-employee-groups'"
                    target="_blank"
                  >
                    <v-icon size="14" class="mr-1">fas fa-plus</v-icon> Add
                    Custom Group
                  </v-btn>
                </div>
              </v-col>
              <v-col
                v-if="customGroupId"
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div v-if="isLoadingCard">
                  <v-skeleton-loader
                    type="list-item-two-line"
                    class="ml-n4 mt-n2"
                    width="80%"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >Employees - {{ empListInSelectedGroup.length }}</span
                  >
                  <div
                    v-if="empListInSelectedGroup.length === 0"
                    class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  >
                    <v-icon color="warning" size="25"
                      >fas fa-exclamation-triangle</v-icon
                    >
                    <span
                      v-if="errorInFetchEmployeesList"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                      >Something went wrong while fetching the employees list.
                      Please try again.
                      <a class="text-primary" @click="fetchCustomEmployeesList"
                        >Refresh
                      </a>
                    </span>
                    <span
                      v-else-if="isNoEmployees"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                    >
                      It seems like there are no employees associated with the
                      selected custom group. Please add some employees under the
                      selected group or try choosing an another group.</span
                    >
                  </div>
                  <div v-else class="d-flex align-center">
                    <AvatarOrderedList
                      v-if="empListInSelectedGroup.length > 0"
                      class="mt-2"
                      :ordered-list="empListInSelectedGroup"
                    ></AvatarOrderedList>
                    <v-btn
                      rounded
                      color="primary"
                      size="small"
                      class="mt-2"
                      @click="openCustomGroupEmpList()"
                    >
                      View All
                    </v-btn>
                  </div>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex">
                  <CustomSelect
                    :items="workflowApprovalList"
                    label="Workflow Approval"
                    :isRequired="true"
                    item-title="Workflow_Name"
                    item-value="Workflow_Id"
                    :is-loading="workflowApprovalLoading"
                    :itemSelected="workflowId"
                    :disabled="workflowApprovalLoading"
                    :is-auto-complete="true"
                    :rules="[required('Workflow Approval', workflowId)]"
                    @selected-item="onChangeIsFormDirty($event, 'workflowId')"
                    style="max-width: 300px"
                    listWidth="max-width: 300px !important"
                  ></CustomSelect>

                  <v-btn
                    color="white"
                    rounded
                    class="ml-2 mt-2"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="retrieveWorkflowDetails()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
                <div class="mb-1">
                  <v-btn
                    color="primary"
                    variant="text"
                    :href="baseUrl + 'workflow/workflow-builder'"
                    target="_blank"
                  >
                    <v-icon size="14" class="mr-1">fas fa-plus</v-icon> Add
                    Workflow
                  </v-btn>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex flex-column">
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >LOP Recovery Applicable For Auto Initiated LOP</span
                  >
                  <v-switch
                    color="primary"
                    class="ml-2"
                    v-model="autoLopApplicable"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                  ></v-switch>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex flex-column">
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >LOP Recovery Applicable For Attendance Shortage</span
                  >
                  <v-switch
                    color="primary"
                    class="ml-2"
                    v-model="attendanceShortageApplicable"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                  ></v-switch>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex flex-column">
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >LOP Recovery Applicable For Late Attendance</span
                  >
                  <v-switch
                    color="primary"
                    class="ml-2"
                    v-model="lateAttendanceApplicable"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                  ></v-switch>
                </div>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="isEdit"
              >
                <div class="v-label ml-2 mb-2">Status</div>
                <AppToggleButton
                  button-active-text="Active"
                  button-inactive-text="InActive"
                  button-active-color="#7de272"
                  button-inactive-color="red"
                  id-value="gab-analysis-based-on"
                  :current-value="status === 'Active' ? true : false"
                  @chosen-value="onChangeStatus($event)"
                ></AppToggleButton>
              </v-col>
            </v-row>
          </v-form>
          <div v-if="showEmployeesList">
            <div class="d-flex mt-n8 mb-1">
              <v-btn rounded color="primary" @click="showEmployeesList = false">
                <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
                Back
              </v-btn>
            </div>
            <EmployeeListCard
              v-if="showEmployeesList"
              :show-modal="showEmployeesList"
              modal-title="Custom Group Employee(s)"
              :employeesList="empListForComponent"
              :selectable="false"
              :showFilter="false"
              :showFilterSearch="true"
              :isApplyFilter="true"
              @close-modal="showEmployeesList = false"
            ></EmployeeListCard>
          </div>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import EmployeeListCard from "@/components/helper-components/EmployeeListCard.vue";
// Queries
import { ADD_UPDATE_LOP_RECOVERY } from "@/graphql/settings/core-hr/lopRecoveryConfigurationQueries.js";

export default {
  name: "AddEditLopRecovery",
  mixins: [validationRules],
  components: {
    CustomSelect,
    AvatarOrderedList,
    EmployeeListCard,
  },
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isListEmpty: {
      type: Boolean,
      default: false,
    },
    coverage: {
      type: String,
      required: false,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      autoLopApplicable: "No",
      attendanceShortageApplicable: "No",
      workflowId: null,
      workflowIdRetrieved: null,
      workflowApprovalList: [],
      workflowApprovalLoading: false,
      lateAttendanceApplicable: "No",
      status: "Active",
      isMounted: false,
      isLoadingDetails: false,
      openConfirmationPopup: false,
      isFormDirty: false,
      customGroupId: null,
      customGroupRetrieved: null,
      customGroupList: [],
      customGroupLoading: false,
      showValidationAlert: false,
      validationMessages: [],
      lopSettingsId: null,
      empListForComponent: [],
      empListInSelectedGroup: [],
      showEmployeesList: false,
      errorInFetchEmployeesList: false,
      isNoEmployees: false,
      isLoadingCard: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    openCustomGroupDropDown() {
      if (this.isEdit) {
        return this.customGroupRetrieved !== null ? true : false;
      } else {
        return this.coverage === "Custom Group" ? true : false;
      }
    },
  },
  watch: {
    customGroupId(val) {
      if (!val) {
        this.empListInSelectedGroup = [];
      } else {
        this.fetchCustomEmployeesList();
      }
    },
  },
  mounted() {
    if (this.isEdit) {
      const {
        Auto_LOP_Applicable,
        Attendance_Shortage_Applicable,
        Configuration_Status,
        Late_Attendance_Applicable,
        Workflow_Id,
        Custom_Group_Id,
        Lop_Settings_Id,
      } = this.editFormData;
      this.lopSettingsId = Lop_Settings_Id ? Lop_Settings_Id : 0;
      this.autoLopApplicable = Auto_LOP_Applicable ? Auto_LOP_Applicable : "";
      this.attendanceShortageApplicable = Attendance_Shortage_Applicable
        ? Attendance_Shortage_Applicable
        : "";
      this.status = Configuration_Status ? Configuration_Status : "Active";
      this.lateAttendanceApplicable = Late_Attendance_Applicable
        ? Late_Attendance_Applicable
        : "No";

      this.workflowIdRetrieved = Workflow_Id ? Workflow_Id : null;
      this.customGroupRetrieved = Custom_Group_Id ? Custom_Group_Id : null;
      if (this.customGroupRetrieved) {
        this.retrieveCustomGroups("edit");
      }
      this.retrieveWorkflowDetails("edit");
    } else {
      if (this.coverage == "Custom Group") {
        this.retrieveCustomGroups();
      }
      this.retrieveWorkflowDetails();
    }
    this.isMounted = true;
  },
  methods: {
    // open employees list to view the employees when the coverage is custom-group
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    // on changing the custom group we need to fetch the employees list relevant to the selected group
    async fetchCustomEmployeesList() {
      if (this.customGroupId) {
        let vm = this;
        vm.isLoadingCard = true;
        await vm.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(vm.customGroupId),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
      this.showEmployeesList = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },
    onChangeIsFormDirty(val, field) {
      if (field == "workflowId") {
        this.workflowId = val;
      } else if (field == "customGroupId") {
        this.customGroupId = val;
      }
      this.isFormDirty = true;
    },
    async validateLopRecoveryForm() {
      const { valid } = await this.$refs.lopRecoveryForm.validate();
      if (valid) {
        this.addUpdateLopRecovery();
      }
    },
    addUpdateLopRecovery() {
      let vm = this;
      vm.isLoadingDetails = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_LOP_RECOVERY,
            variables: {
              Lop_Settings_Id: vm.lopSettingsId
                ? parseInt(vm.lopSettingsId)
                : 0,
              Auto_LOP_Applicable: vm.autoLopApplicable
                ? vm.autoLopApplicable
                : "No",
              Attendance_Shortage_Applicable: vm.attendanceShortageApplicable
                ? vm.attendanceShortageApplicable
                : "No",
              Late_Attendance_Applicable: vm.lateAttendanceApplicable
                ? vm.lateAttendanceApplicable
                : "No",
              Workflow_Id: vm.workflowId ? parseInt(vm.workflowId) : null,
              Configuration_Status:
                vm.status === "InActive" ? "InActive" : "Active",
              CustomGroup_Id: vm.customGroupId ? vm.customGroupId : null,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoadingDetails = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.isEdit
                ? "LOP recovery configuration updated successfully."
                : "LOP recovery configuration added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("edit-updated");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.isLoadingDetails = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: "LOP recovery configuration",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    async retrieveCustomGroups(action) {
      this.customGroupLoading = true;
      this.customGroupList = [];

      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "LOP Recovery",
        })
        .then((groupList) => {
          if (groupList && groupList.length > 0) {
            this.customGroupList = groupList;
            if (action == "edit") {
              this.customGroupId = this.customGroupRetrieved;
            }
          }
          this.customGroupLoading = false;
        })
        .catch(() => {
          this.customGroupLoading = false;
        });
    },
    async retrieveWorkflowDetails(action) {
      this.workflowApprovalLoading = true;
      this.workflowApprovalList = [];
      await this.$store
        .dispatch("listWorkflowDetailsBasedOnFormName", {
          formName: "LOP Recovery",
          formId: 253,
        })
        .then((workflowList) => {
          if (workflowList && workflowList.length > 0) {
            this.workflowApprovalList = workflowList;
            if (action == "edit") {
              this.workflowId = this.workflowIdRetrieved;
            }
          }
          this.workflowApprovalLoading = false;
        })
        .catch(() => {
          this.workflowApprovalLoading = false;
        });
    },
  },
};
</script>
