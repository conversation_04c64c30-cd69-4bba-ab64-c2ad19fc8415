<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
      >
        <template
          v-if="originalList.length > 0 && !showAddEditForm && !showViewForm"
          #topBarContent
        >
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <FormFilter
                ref="formFilterRef"
                :itemList="originalList"
                @reset-filter="resetSearchFilter()"
                @apply-filter="applyFilter($event)"
              >
              </FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="dynamic-form-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList('Dynamic form builder error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="Introducing our dynamic form builder functionality – a powerful tool designed to empower HR administrators in tailoring bespoke forms to suit their specific needs. With this feature, HR teams can effortlessly craft custom forms for a variety of purposes, whether it's exit interview questionnaires, checklists, supplier onboarding forms, or activity handover forms."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="The flexibility of our dynamic form builder allows for seamless customization and association of forms within the system, providing a streamlined solution for diverse form creation needs. Say goodbye to rigid templates and hello to a world of personalized forms tailored precisely to your organization's requirements."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddDynamicForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Add New
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="white"
                      rounded="lg"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <div
                v-if="
                  originalList.length > 0 && !showAddEditForm && !showViewForm
                "
                class="d-flex align-center"
                :class="
                  windowWidth < 580 ? 'mt-2 justify-center' : 'justify-start'
                "
              >
                <v-row>
                  <!-- <v-col :cols="windowWidth < 580 ? '12' : '6'">
                    <div
                      :class="
                        windowWidth < 580
                          ? 'd-flex justify-center'
                          : 'd-flex justify-start mb-1'
                      "
                    >
                      <v-menu
                        id="dynamic_form_builder"
                        :close-on-content-click="true"
                        transition="scale-transition"
                        offset-y
                        v-model="openFormForMenu"
                        min-width="290px"
                      >
                        <template #activator="{ props }">
                          <v-btn
                            class="white my-2 ml-1"
                            rounded="lg"
                            dense
                            v-bind="props"
                          >
                            <v-icon color="grey" class="mr-1" size="17"
                              >fas fa-file-alt</v-icon
                            >
                            <span class="px-1">Form For:</span>
                            <span class="text-secondary">
                              {{ selectedForm ? selectedForm : "-" }}
                            </span>
                            <v-icon
                              class="pl-3"
                              size="14"
                              v-if="openFormForMenu"
                              >fas fa-chevron-up</v-icon
                            >
                            <v-icon class="pl-3" size="14" v-else
                              >fas fa-chevron-down</v-icon
                            >
                          </v-btn>
                        </template>
                        <div
                          style="
                            max-height: 300px;
                            min-height: 50px;
                            overflow-y: scroll;
                            background-color: white;
                          "
                        >
                          <div
                            v-for="(group, i) in [
                              'All',
                              'Individual',
                              'Vendor',
                            ]"
                            :key="i + group"
                            class="pa-2 cursor-pointer primary--text select-work-schedule"
                            @click="onSelectFormFor(group)"
                          >
                            <v-hover>
                              <template v-slot:default="{ isHovering, props }">
                                <div
                                  v-bind="props"
                                  class="pa-2 my-0 rounded-lg cursor-pointer"
                                  :class="{
                                    'bg-pink-lighten-5 mx-0':
                                      isHovering && selectedForm !== group,
                                    'bg-pink-lighten-1 text-white mx-0':
                                      selectedForm === group,
                                    'bg-grey-lighten-4 text-primary mx-0':
                                      !isHovering && selectedForm !== group,
                                  }"
                                >
                                  <div class="text-body-2">
                                    {{ group }}
                                  </div>
                                </div>
                              </template>
                            </v-hover>
                          </div>
                        </div>
                      </v-menu>
                    </div>
                  </v-col> -->

                  <v-col :cols="windowWidth < 580 ? '12' : '12'"
                    ><div
                      :class="
                        windowWidth < 580
                          ? 'd-flex justify-center pb-2 py-2 pt-0'
                          : 'd-flex justify-end mb-1 pa-2'
                      "
                    >
                      <v-btn
                        v-if="formAccess.add"
                        prepend-icon="fas fa-plus"
                        color="primary"
                        rounded="lg"
                        @click="onAddDynamicForm"
                      >
                        <template v-slot:prepend>
                          <v-icon color="white"></v-icon>
                        </template>
                        Add New
                      </v-btn>
                      <v-btn
                        color="transparent"
                        variant="flat"
                        class="ml-2 px-1"
                        @click="refetchList('Refetch List')"
                      >
                        <v-icon color="grey">fas fa-redo-alt</v-icon>
                      </v-btn>
                      <v-menu
                        v-if="itemList.length > 0"
                        class="mb-1"
                        v-model="openMoreMenu"
                        transition="scale-transition"
                      >
                        <template v-slot:activator="{ props }">
                          <v-btn
                            variant="plain"
                            class="ml-n3 mr-n5"
                            v-bind="props"
                          >
                            <v-icon color="grey" v-if="!openMoreMenu"
                              >fas fa-ellipsis-v</v-icon
                            >
                            <v-icon v-else>fas fa-caret-up</v-icon>
                          </v-btn>
                        </template>
                        <v-list>
                          <v-list-item
                            v-for="action in moreActions"
                            :key="action.key"
                            @click="onMoreAction(action.key)"
                          >
                            <v-hover>
                              <template v-slot:default="{ isHovering, props }">
                                <v-list-item-title
                                  v-bind="props"
                                  class="pa-3"
                                  :class="{
                                    'pink-lighten-5': isHovering,
                                  }"
                                  ><v-icon size="15" class="pr-2">{{
                                    action.icon
                                  }}</v-icon
                                  >{{ action.key }}</v-list-item-title
                                >
                              </template>
                            </v-hover>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </div>
                  </v-col>
                </v-row>
              </div>

              <v-row>
                <v-col
                  v-if="
                    originalList.length > 0 && !showViewForm && !showAddEditForm
                  "
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView
                            ? 'v-data-table__mobile-table-row ma-0 mt-2'
                            : ''
                        "
                      >
                        <td id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Dynamic Form Template
                          </div>
                          <section class="d-flex align-center">
                            <v-tooltip
                              :text="item.templateName"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-body-2 font-weight-medium text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.templateName.length > 50 ? props : ''
                                  "
                                >
                                  {{ item.templateName }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Form For
                          </div>
                          <section class="text-body-2 text-primary">
                            {{ checkNullValue(item.formFor) }}
                          </section>
                        </td>
                        <td id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Required
                          </div>
                          <section class="text-body-2 text-primary">
                            {{ item.required ? "Yes" : "No" }}
                          </section>
                        </td>
                        <td id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Status
                          </div>
                          <section
                            class="text-body-2 font-weight-bold"
                            :class="
                              item.status === 'Active'
                                ? 'text-green'
                                : 'text-red'
                            "
                          >
                            {{ checkNullValue(item.status) }}
                          </section>
                        </td>
                        <td class="text-body-2" style="width: 150px">
                          <div class="d-flex align-center">
                            <ActionMenu
                              @selected-action="onActions($event, item)"
                              :actions="['Edit']"
                              :access-rights="formAccess"
                              iconColor="grey"
                            ></ActionMenu>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="12" v-if="showViewForm">
                  <ViewDynamicForm
                    :selectedItem="selectedItem"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>

                <v-col cols="12" v-if="showAddEditForm">
                  <AddEditDynamicForm
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :access-rights="formAccess"
                    :templateName="addTemplateName"
                    :formFor="addFormFor"
                    :template-required="addFormRequired"
                    @close-form="closeAddEditForm()"
                    @form-updated="
                      refetchList('Dynamic Form Template was added/updated')
                    "
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      v-model="showAddDialog"
      @click:outside="closeAddDialogForm"
      max-width="500px"
    >
      <v-card min-height="300" color="primary" class="rounded-lg">
        <v-form ref="addDynamicForm">
          <v-card-title>
            <div class="d-flex justify-end" style="width: 100%">
              <v-icon
                size="20"
                class="mr-2 mt-2 primary"
                @click="closeAddDialogForm()"
                >fas fa-times</v-icon
              >
            </div>
          </v-card-title>
          <div class="mt-2 d-flex justify-center">
            <v-text-field
              v-model="addTemplateName"
              style="max-width: 60%"
              maxlength="50"
              variant="solo"
              :rules="[
                required('Dynamic Form Template', addTemplateName),
                validateWithRulesAndReturnMessages(
                  addTemplateName,
                  'projectName',
                  'Dynamic Form Template'
                ),
              ]"
            >
              <template v-slot:label>
                <span>Dynamic Form Template</span>
                <span class="ml-1" style="color: red">*</span>
              </template>
              <template v-slot:message="{ message }">
                <span style="color: white">{{ message }}</span>
              </template>
            </v-text-field>
          </div>
          <v-card-text class="d-flex justify-center">
            <CustomSelect
              :items="['Individual', 'Vendor']"
              :itemSelected="addFormFor"
              style="max-width: 300px; height: 50px"
              @selected-item="addFormFor = $event"
              :isRequired="true"
              label="Form For"
              :rules="[required('Form For', addFormFor)]"
              messageColor="white"
            ></CustomSelect>
          </v-card-text>
          <div class="d-flex justify-center">
            <v-checkbox
              v-model="addFormRequired"
              label="Required"
              color="white"
            ></v-checkbox>
          </div>
          <v-card-text class="d-flex justify-center">
            <v-btn
              color="secondary"
              rounded="lg"
              @click="validateAddDynamicForm()"
            >
              <v-icon color="primary" class="mr-1">fas fa-plus</v-icon>
              Create
            </v-btn>
          </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="deleteDynamicForm()"
    ></AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewDynamicForm = defineAsyncComponent(() =>
  import("./ViewDynamicForm.vue")
);
const AddEditDynamicForm = defineAsyncComponent(() =>
  import("./AddEditDynamicForm.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import FileExportMixin from "@/mixins/FileExportMixin";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import FormFilter from "./FormFilter";
import { checkNullValue } from "@/helper.js";
import moment from "moment";
// Queries
import {
  LIST_DYNAMIC_FORM,
  RETRIEVE_DYNAMIC_FORM,
  DELETE_DYNAMIC_FORM,
  CHECK_ALREADY_EXISTS,
} from "@/graphql/workflow/dynamicFormBuilderQueries.js";
import validationRules from "@/mixins/validationRules";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "DynamicFormBuilder",
  mixins: [validationRules, FileExportMixin],
  components: {
    EmployeeDefaultFilterMenu,
    AddEditDynamicForm,
    ViewDynamicForm,
    NotesCard,
    ActionMenu,
    CustomSelect,
    FormFilter,
  },
  data: () => ({
    // list
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    openWarningModal: false,
    showAddDialog: false,
    // add/update
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
    //add
    addTemplateName: null,
    addFormFor: null,
    addFormRequired: 0,
    //filter
    selectedForm: "All",
    openFormForMenu: false,
    //export
    openMoreMenu: false,
  }),
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    formAccess() {
      let formAccess = this.accessRights("188");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    accessFormName() {
      let fAccess = this.accessRights("188");
      if (fAccess && fAccess.customFormName) {
        return fAccess.customFormName;
      } else return "Dynamic Form Builder";
    },
    mainTabs() {
      if (this.formAccess) {
        return [this.accessFormName];
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    tableHeaders() {
      return [
        {
          title: "Dynamic Form Template",
          align: "start",
          key: "templateName",
        },
        {
          title: "Form For",
          key: "formFor",
        },
        {
          title: "Required",
          key: "required",
        },
        {
          title: "Status",
          key: "status",
        },
        {
          title: "Action",
          key: "action",
          sortable: false,
        },
      ];
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText =
          "There are no dynamic form templates for the selected filters/searches.";
      }
      return msgText;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.accessFormName);
    this.fetchList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    applyFilter(filteredArray) {
      this.itemList = filteredArray;
    },
    resetSearchFilter() {
      this.itemList = this.originalList;
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        mixpanel.track("DynamicFormBuilder-export-click");
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },

    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        coverage: this.projectCoverage,
        addedOn: el.addedOn ? this.formatDate(el.addedOn) : "",
        updatedOn: el.updatedOn ? this.formatDate(el.updatedOn) : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.accessFormName,
        sheetName: this.accessFormName,
        header: [
          { key: "templateName", header: "Dynamic Form Template" },
          { key: "formFor", header: "Form For" },
          { key: "addedOn", header: "Added On" },
          { key: "addedByUserName", header: "Added By" },
          { key: "updatedOn", header: "Updated On" },
          { key: "updatedByUserName", header: "Updated By" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },

    errorCaptured(err, vm, info) {
      let url = window.location.href;
      console.error("Dynamic Form Builder Error:", err);
      let msg = `Something went wrong while loading the ${this.accessFormName} form. Please try after some time.`;
      if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
        msg = err + " " + info;
      }
      mixpanel.track("DynamicFormBuilder-UI-Error - " + err + " " + info);
      let snackbarData = {
        isOpen: true,
        message: msg,
        type: "warning",
      };
      this.showAlert(snackbarData);
      return false;
    },

    onSelectFormFor(form) {
      this.selectedForm = form;
      if (form === "All") {
        this.itemList = this.originalList;
      } else {
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return item.formFor === form;
        });
        this.itemList = searchItems;
      }
    },

    closeAddDialogForm() {
      this.showAddDialog = false;
      this.addTemplateName = null;
      this.addFormFor = null;
    },

    async validateAddDynamicForm() {
      let isFormValid = await this.$refs.addDynamicForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.validateAlreadyExists();
      }
    },

    validateAlreadyExists() {
      let vm = this;
      vm.isLoading = true;
      const templateName = this.addTemplateName;
      vm.$apollo
        .mutate({
          mutation: CHECK_ALREADY_EXISTS,
          variables: {
            templateName,
          },
          client: "apolloClientAL",
        })
        .then((response) => {
          vm.isLoading = false;
          if (response.data && response.data.checkTemplateNameAlreadyExist) {
            if (!response.data.checkTemplateNameAlreadyExist.result.success) {
              vm.addTemplateName = "";
              let snackbarData = {
                isOpen: true,
                message:
                  "Dynamic form template already exists. Please provide a unique name for your dynamic form template.",
                type: "warning",
              };
              vm.showAlert(snackbarData);
            } else {
              vm.showAddDialog = false;
              vm.openAddDynamicForm();
            }
          } else {
            vm.handleAlreadyExistError();
          }
        })
        .catch((err) => {
          vm.handleAlreadyExistError(err);
        });
    },

    handleAlreadyExistError(err = "") {
      mixpanel.track("DynamicForm-already-exist");
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "validating",
        form: "dynamic form template name",
        isListError: false,
      });
    },

    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        this.onDelete(item);
      } else {
        this.onEdit(item);
      }
    },

    onEdit(item) {
      this.openViewForm(item, true);
    },

    onDelete(item) {
      mixpanel.track("Dynamic Form Template Delete Triggered");
      this.openWarningModal = true;
      this.selectedItem = item;
    },

    // function close the warning modal
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedItem = null;
    },

    deleteDynamicForm() {
      let vm = this;
      vm.isLoading = true;
      const { templateId } = this.selectedItem;
      vm.$apollo
        .mutate({
          mutation: DELETE_DYNAMIC_FORM,
          variables: {
            templateId,
          },
          client: "apolloClientAL",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Dynamic form template deleted successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.refetchList("Dynamic Form Template deleted");
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      mixpanel.track("DynamicForm-delete-error");
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "dynamic form template",
        isListError: false,
      });
      this.closeAllForms();
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },

    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = this.originalList;
      this.selectedForm = "All";
    },

    openEditForm() {
      mixpanel.track("Dynamic form template edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item, isEdit = false) {
      mixpanel.track("Dynamic form template view form opened");
      this.selectedItem = item;
      this.retrieveFormTemplate(item, isEdit);
    },

    retrieveFormTemplate(item, isEdit) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_DYNAMIC_FORM,
          client: "apolloClientAL",
          fetchPolicy: "no-cache",
          variables: {
            templateId: item.templateId,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getDynamicFormTemplate &&
            !response.data.getDynamicFormTemplate.error
          ) {
            let data = response.data.getDynamicFormTemplate.result;
            if (data) {
              data.formFor =
                data.formFor && data.formFor.toLowerCase() === "vend"
                  ? "Vendor"
                  : "Individual";
              vm.isLoading = false;
              vm.selectedItem = data;
              if (!isEdit) {
                vm.showViewForm = true;
                vm.showAddEditForm = false;
              } else {
                vm.openEditForm();
              }
            }
          } else {
            vm.handleViewError();
          }
        })
        .catch((err) => {
          vm.handleViewError(err);
        });
    },

    onAddDynamicForm() {
      mixpanel.track("Dynamic form template add form opened");
      this.showAddDialog = true;
    },

    openAddDynamicForm() {
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAddEditForm() {
      //Calling this to reset css
      this.fetchList();
      if (this.isEdit) {
        this.isEdit = false;
        this.showAddEditForm = false;
        this.showViewForm = true;
        this.openWarningModal = false;
        this.showAddDialog = false;
        this.addFormFor = null;
        this.addTemplateName = null;
        this.selectedForm = "All";
      } else {
        this.closeAllForms();
      }
    },

    closeAllForms() {
      mixpanel.track("Dynamic form template all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.openWarningModal = false;
      this.showAddDialog = false;
      this.addFormFor = null;
      this.addTemplateName = null;
      this.selectedForm = "All";
    },

    fetchList(refetch = false) {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_DYNAMIC_FORM,
          client: "apolloClientAL",
          fetchPolicy: "no-cache",
          variables: {
            filter: {
              limit: 20000,
              offset: 0,
            },
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getAllDynamicFormTemplate &&
            !response.data.getAllDynamicFormTemplate.error
          ) {
            let data = response.data.getAllDynamicFormTemplate.result.map(
              (el) => {
                el.formFor =
                  el.formFor && el.formFor.toLowerCase() === "vend"
                    ? "Vendor"
                    : "Individual";
                return el;
              }
            );
            vm.itemList = data;
            vm.originalList = data;
            vm.listLoading = false;
            vm.onApplySearch();
            if (refetch && vm.$refs.formFilterRef) {
              vm.$refs.formFilterRef.filterItemList = data;
              vm.$refs.formFilterRef.fnApplyFilter();
            }
            mixpanel.track("Dynamic Form template list retrieved");
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      mixpanel.track("Dynamic Form template error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "dynamic form templates",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    handleViewError(err = "") {
      mixpanel.track("Dynamic Form template error in list API");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "dynamic form template",
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList(true);
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.dynamic-form-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .dynamic-form-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
