<template>
  <AppLoading v-if="isLoading"></AppLoading>
  <div v-else class="fill-height">
    <v-main class="app-background">
      <AppSnackBar
        v-if="snackbarData.isOpen"
        :show-snack-bar="snackbarData.isOpen"
        :snack-bar-msg="snackbarData.message"
        :image-name="snackbarData.image"
        :snack-bar-type="snackbarData.type"
        @close-snack-bar="closeSnackbarAlert"
      >
      </AppSnackBar>
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </v-main>
  </div>
</template>

<script>
import Config from "@/config.js";
import { GET_FORM_FIELDS_NO_AUTH } from "@/graphql/commonQueries";

export default {
  name: "UnAuthenticateLayout",

  data: () => {
    return {
      isLoading: false,
    };
  },

  computed: {
    //show/hide snackbar based on store data
    snackbarData() {
      return this.$store.getters.getSnackbarData;
    },
  },

  created() {
    let configDetails = Config;
    const { production } = configDetails;
    let orgCode =
      this.$store.getters.orgCode === "127" ||
      this.$store.getters.orgCode === "localhost"
        ? "infotech"
        : this.$store.getters.orgCode;
    // cookies
    window.$cookies.set("isProduction", production, "90d");
    // localstorage
    localStorage.setItem("pageUrl", this.$store.getters.baseUrl);
    localStorage.setItem("isProduction", production);
    localStorage.setItem("orgCode", orgCode);
    if (!localStorage.getItem("changeLoginPwdDisplay"))
      localStorage.setItem("changeLoginPwdDisplay", 0);
    if (!localStorage.getItem("HrappDepartmentClassificationId"))
      localStorage.setItem("HrappDepartmentClassificationId", 0);
    localStorage.setItem("TzTime", "07 : 06 : 12 pm");
    localStorage.setItem("TzDate", "09/03/2021");
    this.retrieveCustomLabelNames();
  },

  methods: {
    //function to close the snackbar alert
    closeSnackbarAlert() {
      this.$store.commit("CLOSE_SNACKBAR");
    },
    // fetch custom labels
    retrieveCustomLabelNames() {
      let self = this;
      self.isLoading = true;
      try {
        self.$apollo
          .query({
            query: GET_FORM_FIELDS_NO_AUTH,
            client: "apolloClientAS",
            variables: {
              form_Id: 0,
            },
          })
          .then((fieldDetails) => {
            let { formFields } = fieldDetails.data.getFormFeildsByFormIdAndTab;
            const formedFields = formFields.reduce((acc, curr) => {
              const { Field_Id, ...rest } = curr;
              acc[Field_Id] = rest;
              return acc;
            }, {});
            if (formedFields) {
              self.$store.commit("UPDATE_CUSTOM_FIELDS", formedFields);
            }
            self.isLoading = false;
          })
          .catch(() => {
            self.$store.commit("UPDATE_CUSTOM_FIELDS", []);
            self.isLoading = false;
          });
      } catch {
        self.$store.commit("UPDATE_CUSTOM_FIELDS", []);
        self.isLoading = false;
      }
    },
  },
};
</script>

<style lang="scss">
@import "../assets/css/VueCommon.css";
@import url("https://cdn.jsdelivr.net/npm/vue-tel-input/dist/vue-tel-input.css");

.app-background {
  background-color: var(--v-grey-lighten3);
  height: 100%;
}
.ps {
  height: 90%;
}
</style>
