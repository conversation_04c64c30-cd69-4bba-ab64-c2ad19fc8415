<template>
  <div>
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row
            justify="center"
            v-if="preApprovalsFormAccess && preApprovalData.length > 0"
          >
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                v-if="preApprovalData.length > 0"
                class="justify-end"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <!-- kept for future use -->
              <FormFilter
                ref="formFilterRef"
                :items="preApprovalData"
                :callingFrom="callingFrom"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              ></FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="pre-approvals-container">
      <v-window v-model="currentTabItem" v-if="preApprovalsFormAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="isGridLoading || isListLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="preApprovalTableData.length == 0 && emptyFilterScreen"
            image-name="common/no-records"
            main-title="There are no pre approval for the selected filters/searches."
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center">
                <v-btn
                  color="primary"
                  variant="elevated"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click.stop="resetFilter()"
                >
                  Reset Filter/Search
                </v-btn>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchPreApprovalList()"
            image-name="common/human-error-image"
          >
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="configExist"
            icon-name="fas fa-redo-alt"
            :button-text="Retry"
            @button-click="refetchPreApprovalList()"
          >
            <template #contentSlot>
              <div
                :style="isMobileView ? 'max-width: 80%;' : 'max-width: 80%;'"
              >
                <v-row
                  v-if="userIs !== 'admin'"
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="Your organization currently does not have preapproval configurations enabled. For assistance, please get in touch with your HR administrator."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                      ><v-btn
                        variant="elevated"
                        color="primary"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="refetchPreApprovalList()"
                      >
                        <v-icon size="15" class="pr-1">fas fa-redo-alt</v-icon>
                        Retry
                      </v-btn></v-col
                    >
                  </v-col>
                </v-row>

                <v-row
                  v-else
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="Your organization's preapproval configuration is currently inactive. To enable and update it, please go to the 'Settings' section."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <v-btn
                        :href="settingRedirectionUrl"
                        variant="elevated"
                        color="primary"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                      >
                        <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                        Add Configuration
                      </v-btn>

                      <v-btn
                        variant="elevated"
                        color="primary"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="refetchPreApprovalList()"
                      >
                        <v-icon size="15" class="pr-1">fas fa-redo-alt</v-icon>
                        Retry
                      </v-btn>
                    </v-col>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="preApprovalData.length === 0"
            key="no-results-screen"
          >
            <template #contentSlot>
              <div
                v-if="!AddEditButtonClicked"
                :style="isMobileView ? 'max-width: 80%;' : 'max-width: 80%;'"
              >
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col cols="12">
                    <NotesCard
                      notes="Pre-approvals configuration empowers employees to request prior approval for working during week-offs, holidays, and remote work. This functionality ensures compliance with organizational policies and transparency in work arrangements. With configurable business rules, organizations can define eligibility criteria, restrict sandwich pre-approvals, and limit the number of days for which approvals can be requested."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Employees can submit pre-approval requests, seeking official authorization before engaging in specific activities. application's flexible configuration allows organizations to define which employees are eligible for pre-approvals, ensuring the option is available to the appropriate individuals. By setting restrictions on sandwich pre-approvals and limiting requested days, organizations can maintain a balanced work schedule and align employee requests with operational requirements. Overall, pre-approvals configuration streamlines the process, ensures compliance, and provides transparency for work arrangements within defined parameters."
                      backgroundColor="transparent"
                      class="mb-2"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="preApprovalsFormAccess.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="emptyPageAddEditForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Initiate Request
                    </v-btn>
                    <v-btn
                      color="white"
                      rounded="lg"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchPreApprovalList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <div
                v-if="!showViewForm"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center' : 'justify-end'"
              >
                <v-btn
                  prepend-icon="fas fa-plus"
                  color="primary rounded-lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="openAddForm"
                  v-if="preApprovalsFormAccess.add"
                >
                  <template v-slot:prepend>
                    <v-icon color="white"></v-icon>
                  </template>
                  Initiate Request
                </v-btn>
                <v-btn
                  color="white"
                  rounded="lg"
                  class="ml-2 mt-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click.stop="refetchPreApprovalList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n2 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="exportReportFile()"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
              <v-row>
                <v-col :cols="showViewForm === true ? '5' : ''">
                  <v-data-table
                    v-model="selectedData"
                    :headers="headers"
                    :sort-by="[{ key: 'startDate', order: 'desc' }]"
                    :items="preApprovalTableData"
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        preApprovalTableData
                      )
                    "
                    class="elevation-1"
                    style="box-shadow: none !important"
                  >
                    <template v-slot:item="{ item, index }">
                      <tr
                        style="z-index: 200"
                        class="data-table-tr bg-white cursor-pointer"
                        @click="openViewForm(item, index)"
                        :class="[
                          isMobileView
                            ? ' v-data-table__mobile-table-row ma-0 mt-2'
                            : '',
                        ]"
                      >
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'font-weight-small'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            EmployeeId
                          </div>
                          <div class="d-flex align-center">
                            <div
                              v-if="
                                selectedItem &&
                                !AddEditButtonClicked &&
                                !isMobileView
                                  ? selectedItem.preApprovalId ===
                                    item.preApprovalId
                                  : false
                              "
                              class="data-table-side-border d-flex"
                              style="height: 4em"
                            ></div>
                            <v-tooltip
                              location="top"
                              :disabled="item.userDefinedEmpId.length < 20"
                            >
                              <template v-slot:activator="{ props }">
                                <section v-bind="props">
                                  <div
                                    class="text-body-2 pa-2 font-weight-regular text-truncate"
                                    :style="
                                      isMobileView
                                        ? 'max-width: 150px'
                                        : 'max-width: 150px'
                                    "
                                  >
                                    {{ item.userDefinedEmpId }}
                                  </div>
                                </section>
                              </template>
                              <span color="grey">{{
                                item.userDefinedEmpId
                              }}</span>
                            </v-tooltip>
                          </div>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5 font-weight-medium'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            employeeName
                          </div>
                          <v-tooltip
                            location="top"
                            :disabled="item.employeeName.length < 20"
                          >
                            <template v-slot:activator="{ props }">
                              <section v-bind="props">
                                <div
                                  class="text-body-2 font-weight-regular text-truncate"
                                  :style="
                                    isMobileView
                                      ? 'max-width: 150px'
                                      : 'max-width: 150px'
                                  "
                                >
                                  {{ item.employeeName }}
                                </div>
                              </section>
                            </template>
                            <span color="grey">{{ item.employeeName }}</span>
                          </v-tooltip>
                        </td>
                        <td
                          v-if="
                            labelList[464]?.Field_Visiblity?.toLowerCase() ===
                            'yes'
                          "
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            Pre-approval Type
                          </div>
                          <section>
                            <span
                              class="text-body-2 font-weight-regular text-truncate"
                            >
                              {{ item.preApprovalType }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!showViewForm"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            Start Date
                          </div>
                          <section>
                            <span
                              class="text-body-2 font-weight-regular text-truncate"
                            >
                              {{ formatDate(item.startDate) }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!showViewForm"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            End Date
                          </div>
                          <section>
                            <span
                              class="text-body-2 font-weight-regular text-truncate"
                            >
                              {{ formatDate(item.endDate) }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!showViewForm"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            Total Days
                          </div>
                          <section>
                            <span class="text-body-2 font-weight-regular">
                              {{ item.totalDays }}
                            </span>
                          </section>
                        </td>

                        <td
                          v-if="!showViewForm"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            Status
                          </div>
                          <section
                            class="d-flex align-center justify-space-between"
                          >
                            <div class="d-flex align-center">
                              <span
                                id="w-80"
                                :class="getStatusClass(item.status)"
                                class="text-body-2 font-weight-regular d-flex align-center text-center"
                              >
                                {{ item.status }}
                              </span>
                            </div>
                          </section>
                        </td>
                        <td
                          v-if="!showViewForm"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            Actions
                          </div>
                          <section>
                            <v-tooltip location="top" text="Cancel Request">
                              <template v-slot:activator="{ props }">
                                <v-icon
                                  v-bind="props"
                                  color="red"
                                  v-if="
                                    preApprovalsFormAccess.view &&
                                    (item.status === 'Approved' ||
                                      item.status === 'Applied')
                                  "
                                  icon="far fa-times-circle"
                                  @click.stop="
                                    openCancelStatusModal(
                                      item,
                                      'far fa-times-circle'
                                    )
                                  "
                                  :style="
                                    isMobileView
                                      ? 'margin-left: 12px;'
                                      : 'margin-right: 15px;'
                                  "
                                  :class="isMobileView ? 'ml-4' : 'mr-10'"
                                ></v-icon> </template
                            ></v-tooltip>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col
                  cols="7"
                  v-if="showViewForm && !AddEditButtonClicked && !isMobileView"
                >
                  <ViewPreApproval
                    @close-split-view="closePreApprovalViewTab()"
                    :access-rights="preApprovalsFormAccess"
                    @reload-view="setSelectedItem"
                    :selectedItem="selectedItem"
                    @close-edit-form="closeEditView"
                    @open-edit-form="openEditView"
                    :preApprovalData="preApprovalData"
                    :isEdit="isEdit"
                    @refresh-list="getEmployeesList"
                  />
                </v-col>
                <v-dialog
                  class="pl-4"
                  v-model="preApprovalView"
                  v-if="isMobileView && !AddEditButtonClicked"
                  width="900"
                  @click:outside="closePreApprovalViewTab()"
                >
                  <ViewPreApproval
                    @close-split-view="closePreApprovalViewTab()"
                    :access-rights="preApprovalsFormAccess"
                    @reload-view="setSelectedItem"
                    :selectedItem="selectedItem"
                    @close-edit-form="closeEditView"
                    @open-edit-form="openEditView"
                    :preApprovalData="preApprovalData"
                    :isEdit="isEdit"
                    @refresh-list="getEmployeesList"
                  />
                </v-dialog>

                <v-col cols="7" v-if="AddEditButtonClicked && !isMobileView">
                  <AddEditPreApprovals
                    @close-split-view="closePreApprovalViewTab()"
                    :selectedItem="selectedItem"
                    :preApprovalEmployeeObject="preApprovalEmployeeObject"
                    :isEdit="isEdit"
                    :callingFrom="callingFrom"
                    :open-loader="(isLoading = true)"
                    :close-loader="(isLoading = false)"
                    @refetch-list="getEmployeesList"
                  />
                </v-col>
                <v-dialog
                  class="pl-4"
                  v-if="isMobileView"
                  v-model="AddEditButtonClicked"
                  width="900"
                  @click:outside="closePreApprovalViewTab()"
                >
                  <AddEditPreApprovals
                    @close-split-view="closePreApprovalViewTab()"
                    :selectedItem="selectedItem"
                    :preApprovalEmployeeObject="preApprovalEmployeeObject"
                    :isEdit="isEdit"
                    :callingFrom="callingFrom"
                    :open-loader="(isLoading = true)"
                    :close-loader="(isLoading = false)"
                    @refetch-list="getEmployeesList"
                  />
                </v-dialog>
              </v-row>

              <AppWarningModal
                v-if="openWarningModal"
                :open-modal="openWarningModal"
                :confirmation-heading="warningText"
                :icon-name="warningIconClass"
                @close-warning-modal="onCloseWarningModal()"
                @accept-modal="deleteItem(deleteSingleItem)"
              >
              </AppWarningModal>
              <AppWarningModal
                v-if="openCancelStatusWarningModal"
                :open-modal="openCancelStatusWarningModal"
                :confirmation-heading="cancelStatusText"
                :icon-name="warningIconClass"
                @close-warning-modal="onCloseCancelStatusWarningWarningModal()"
                @accept-modal="changeStatus(statusChangedItem)"
              >
              </AppWarningModal>
            </div>
          </div>

          <AddEditPreApprovals
            v-if="preApprovalData.length == 0 && AddEditButtonClicked"
            :style="
              isMobileView
                ? 'margin-top: -90px'
                : 'margin-top: -140px !important;'
            "
            :class="isMobileView ? 'mx-1' : 'mx-8'"
            @close-split-view="closePreApprovalViewTab()"
            :selectedItem="selectedItem"
            :isEmpty="isEmpty"
            :isEdit="isEdit"
            :callingFrom="callingFrom"
            @refetch-list="getEmployeesList"
            :isListEmpty="isListEmpty"
            :preApprovalEmployeeObject="preApprovalEmployeeObject"
            :open-loader="(isLoading = true)"
            :close-loader="(isLoading = false)"
          />
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>

    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewPreApproval = defineAsyncComponent(() =>
  import("./ViewPreApproval.vue")
);
const AddEditPreApprovals = defineAsyncComponent(() =>
  import("./AddEditPreApproval.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import moment from "moment";
import { getErrorCodes, getErrorCodesWithValidation } from "@/helper.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import FormFilter from "./FormFilter.vue";
import { convertUTCToLocal } from "@/helper.js";
import {
  GET_PRE_APPROVAL_REQUESTS,
  ADD_UPDATE_PREAPPROVAL,
  GET_PRE_APPROVAL_EMPLOYEE_LIST,
} from "@/graphql/settings/core-hr/preApprovalQueries";
export default {
  name: "Pre-Approvals",
  components: {
    EmployeeDefaultFilterMenu,
    AddEditPreApprovals,
    ViewPreApproval,
    NotesCard,
    FormFilter,
  },
  props: {
    callingFrom: {
      type: String,
      default: "employee",
    },
  },
  mixins: [FileExportMixin],
  data: () => ({
    openMoreMenu: false,
    isListEmpty: true,
    isListLoading: false,
    isGridLoading: false,
    isLoading: false,
    isEdit: false,
    errorContent: "",
    isErrorInList: false,
    showViewForm: false,
    preApprovalView: false,
    AddEditButtonClicked: false,
    openCancelStatusWarningModal: false,
    showRetryBtn: true,
    showSelect: false,
    confirmDeleteAll: false,
    selectedData: [],
    selectedItemIndex: null,
    currentTabItem: "tab-0",
    preApprovalData: [],
    preApprovalTableData: [],
    backupPreApprovalData: [],
    emptyFilterScreen: false,
    selectedItem: null,
    configExist: false,
    openWarningModal: false,
    warningText: "Are you sure to delete this record",
    cancelStatusText: "Are you sure to cancel the pre-approval request",
    deleteSingleItem: null,
    statusChangedItem: null,
    warningIconClass: "",
    openMultipleWarningModal: false,
    isEmpty: false,
    userIs: "",
    startDate: "",
    endDate: "",
    groupBy: [{ key: "employeeId", order: "asc" }],
    preApprovalEmployeeObject: {},
  }),
  computed: {
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
    formId() {
      let fId = this.callingFrom === "team" ? "257" : "247";
      return parseInt(fId);
    },
    preApprovalsFormAccess() {
      let preApprovalAccess = this.formIdAccessRights(this.formId);
      if (
        preApprovalAccess &&
        preApprovalAccess.accessRights &&
        preApprovalAccess.accessRights["view"]
      ) {
        return preApprovalAccess.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    settingRedirectionUrl() {
      return this.baseUrl + "v3/settings/core-hr/pre-approvals";
    },
    formIdAccessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    approvalFormAccess() {
      let formAccess = this.accessRights("approval-management");
      if (formAccess && formAccess.accessRights["view"]) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    accessRightsBasedOnId() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    accessFormName() {
      let projectForm = this.accessRightsBasedOnId(this.formId);
      if (projectForm && projectForm.customFormName) {
        return projectForm.customFormName;
      } else return "Pre-approval";
    },
    mainTabs() {
      if (
        this.preApprovalsFormAccess &&
        this.approvalFormAccess &&
        this.callingFrom === "team"
      ) {
        return [this.accessFormName, "Approvals"];
      } else {
        return [this.accessFormName];
      }
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    formatDate() {
      return (date, withTime = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    headers() {
      if (this.showViewForm || this.AddEditButtonClicked) {
        return [
          {
            title: "Employee Id",
            align: "start",
            sortable: true,
            key: "employeeId",
          },

          {
            title: "Employee Name",
            key: "employeeName",
            sortable: true,
          },
          ...(this.labelList[464]?.Field_Visiblity?.toLowerCase() === "yes"
            ? [
                {
                  title: "Pre-approval Type",
                  key: "preApprovalType",
                  sortable: true,
                },
              ]
            : []),
        ];
      } else {
        return [
          {
            title: "Employee Id",
            align: "start",
            sortable: true,
            key: "employeeId",
          },

          {
            title: "Employee Name",
            key: "employeeName",
            sortable: true,
          },
          ...(this.labelList[464]?.Field_Visiblity?.toLowerCase() === "yes"
            ? [
                {
                  title: "Pre-approval Type",
                  key: "preApprovalType",
                  sortable: true,
                },
              ]
            : []),
          {
            title: "Start Date",
            key: "startDate",
          },
          {
            title: "End Date",
            key: "endDate",
          },
          {
            title: "Total Days",
            key: "totalDays",
          },
          {
            title: "Status",
            key: "status",
          },
          {
            title: "Actions",
            key: "actions",
          },
        ];
      }
    },
  },

  watch: {
    preApprovalData(val) {
      this.preApprovalTableData = val;
      this.onApplySearch(this.searchValue);
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  mounted() {
    if (this.preApprovalsFormAccess) {
      this.getEmployeesList();
      this.getPreApprovalEmployeeList();
      this.setUser();
      this.onApplySearch();
    }
  },

  methods: {
    convertUTCToLocal,
    exportReportFile() {
      let preApprovalList = this.preApprovalTableData.map((item) => ({
        ...item,
      }));

      // Sort the array based on startDate in descending order
      preApprovalList.sort((a, b) => {
        // Convert the date strings to Date objects for comparison
        const dateA = new Date(a.startDate);
        const dateB = new Date(b.startDate);

        // Compare in descending order
        return dateB - dateA;
      });
      // Modify the copied array
      preApprovalList.forEach((item) => {
        item.startDate = this.formatDate(item.startDate);
        item.endDate = this.formatDate(item.endDate);
        item.addedOn = item.addedOn ? this.convertUTCToLocal(item.addedOn) : "";
        item.approvedOn = item.approvedOn
          ? this.convertUTCToLocal(item.approvedOn)
          : "";
        item.updatedOn = item.updatedOn
          ? this.convertUTCToLocal(item.updatedOn)
          : "";
      });

      let fileName = "Pre Approval";
      let exportHeaders = [];

      exportHeaders.push(
        {
          header: "Employee Id",
          key: "userDefinedEmpId",
        },
        {
          header: "Employee Name",
          key: "employeeName",
        },
        {
          header: "Designation",
          key: "Designation_Name",
        },
        {
          header: "Department",
          key: "Department_Name",
        },
        {
          header: "Employee Type",
          key: "Employee_Type",
        },
        { header: "Location", key: "Location_Name" },
        { header: "Pre-approval Type", key: "preApprovalType" },
        { header: "Start Date", key: "startDate" },
        { header: "End Date", key: "endDate" },
        { header: "Total Days", key: "totalDays" },
        { header: "Duration", key: "duration" },
        { header: "Period", key: "period" },
        { header: "Status", key: "status" },
        { header: "Reason", key: "reason" },
        { header: "Added By", key: "addedByName" },
        { header: "Added On", key: "addedOn" },
        { header: "Update By", key: "updatedByName" },
        { header: "Update On", key: "updatedOn" },
        { header: "Approved By", key: "approvedByName" },
        { header: "Approved On", key: "approvedOn" }
      );

      let exportOptions = {
        fileExportData: preApprovalList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    resetFilter() {
      this.preApprovalTableData = this.backupPreApprovalData;
      this.preApprovalData = this.backupPreApprovalData;
      this.emptyFilterScreen = false;
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    applyFilter(filterParams) {
      this.preApprovalTableData = filterParams;
      if (this.preApprovalTableData.length == 0) {
        this.emptyFilterScreen = true;
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.preApprovalTableData = this.preApprovalData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.preApprovalData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.preApprovalTableData = searchItems;
        if (this.$refs.formFilterRef) {
          this.$refs.formFilterRef.resetAllModelValues();
        }
        if (this.preApprovalTableData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    setUser() {
      let userDetails = this.accessRights("pre-approvals");
      if (userDetails.accessRights.admin === "admin") {
        this.userIs = "admin";
      } else if (userDetails.accessRights.isManager === 1) {
        this.userIs = "manager";
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getPreApprovalEmployeeList() {
      this.employeeNameList = [];
      let vm = this;
      vm.isGridLoading = true;
      try {
        vm.$apollo
          .query({
            query: GET_PRE_APPROVAL_EMPLOYEE_LIST,
            client: "apolloClientI",
            fetchPolicy: "no-cache",
            variables: {
              formId: vm.formId,
            },
          })
          .then((orgUserData) => {
            this.preApprovalEmployeeObject =
              orgUserData?.data?.getPreApprovalEmployeeList?.employeeDetails;
            this.configExist = this.isNoConfigurationForEmployee(
              this.preApprovalEmployeeObject
            );
            vm.isGridLoading = false;
          })
          .catch((fetchOrganizationError) => {
            this.handleEmployeeListError(fetchOrganizationError);
            vm.isGridLoading = false;
          });
      } catch {
        this.handleEmployeeListError();
        vm.isGridLoading = false;
      }
    },
    handleEmployeeListError(err) {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "CHR0059": // Technical error
            snackbarData.message =
              "Error while validating the work from home pre-approval request.";
            break;
          case "_DB0100": // This employee does not have admin or manager access.
            snackbarData.message =
              "Sorry you don't have access rights to view pre-approvals details. Please contact HR administrator";
            break;
          default:
            snackbarData.message =
              "Something went wrong while validating the work from home pre-approval request.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while validating the work from home pre-approval request.";
      }
      this.showAlert(snackbarData);
    },
    isNoConfigurationForEmployee(data) {
      const { workFromHome, workDuringHoliday, workDuringWeekOff, onDuty } =
        data;
      return (
        workFromHome.length === 0 &&
        workDuringHoliday.length === 0 &&
        workDuringWeekOff.length === 0 &&
        onDuty.length === 0
      );
    },
    openEditView() {
      this.isEdit = true;
      this.AddEditButtonClicked = true;
    },
    closeEditView() {
      this.isEdit = false;
    },
    getStatusClass(status) {
      if (status === "Approved") {
        return "text-green";
      } else if (status === "Cancel Applied") {
        return "text-amber";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },

    openWarningPopUp(item, warningIcon) {
      if (item === null) {
        this.warningIconClass = warningIcon;
        this.openWarningModal = true;
        return;
      } else {
        this.warningIconClass = warningIcon;
        this.openWarningModal = true;
        this.deleteSingleItem = item;
      }
    },
    openCancelStatusModal(item, warningIcon) {
      if (item === null) {
        this.warningIconClass = warningIcon;
        this.openCancelStatusWarningModal = true;
        return;
      }

      this.warningIconClass = warningIcon;
      this.openCancelStatusWarningModal = true;
      this.statusChangedItem = item;
    },

    openViewForm(item, index) {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      if (this.isEdit) {
        return;
      }
      this.selectedItemIndex = index;
      this.selectedItem = item;
      if (!this.isMobileView) this.showViewForm = true;
      this.isEdit = false;
      this.preApprovalView = true;
    },
    setSelectedItem(id) {
      this.getEmployeesList();
      this.selectedItem = this.preApprovalTableData.find(
        (item) => item.preApprovalId === id
      );
    },
    openAddForm() {
      this.AddEditButtonClicked = true;
      this.showViewForm = true;
      this.isEdit = false;
    },
    emptyPageAddEditForm() {
      this.AddEditButtonClicked = true;
      this.isEdit = false;
    },
    closePreApprovalViewTab() {
      this.AddEditButtonClicked = false;
      this.selectedItemIndex = null;
      this.showViewForm = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.isEdit = false;
      this.preApprovalView = false;
      if (!this.isMobileView) this.selectedItem = null;
    },
    //function to delete single file
    deleteItem(item) {
      const index = this.preApprovalData.findIndex((d) => d === item);
      if (index !== -1) {
        this.preApprovalData.splice(index, 1);
      }
      this.openWarningModal = false;
    },
    // function close the warning modal
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.deleteSingleItem = null;
    },
    onCloseCancelStatusWarningWarningModal() {
      this.warningIconClass = "";
      this.openCancelStatusWarningModal = false;
      this.statusChangedItem = null;
    },
    refetchPreApprovalList() {
      if (this.preApprovalsFormAccess) {
        this.isErrorInList = false;
        this.errorContent = "";
        this.getEmployeesList();
        this.getPreApprovalEmployeeList();
        if (
          this.preApprovalData.length !== 0 &&
          this.preApprovalTableData.length !== 0 &&
          this.$refs.formFilterRef
        ) {
          this.$refs.formFilterRef.resetAllModelValues();
        }
      }
    },
    async getEmployeesList() {
      let vm = this;
      vm.isListLoading = true;
      await vm.$apollo
        .query({
          query: GET_PRE_APPROVAL_REQUESTS,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: {
            formId: vm.formId,
          },
        })
        .then((response) => {
          if (response && response.data.listPreApprovalRequests) {
            this.preApprovalData = [
              ...response.data.listPreApprovalRequests.preApprovalRequests,
            ];
            this.preApprovalTableData = this.preApprovalData;
            this.backupPreApprovalData = this.preApprovalData;
            vm.isListLoading = false;
          }
        })
        .catch((err) => {
          vm.isListLoading = false;
          vm.handleFetchPreApprovalError(err);
        });
    },
    async changeStatus(item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_PREAPPROVAL,
          variables: {
            preApprovalId: item.preApprovalId,
            employeeId: item.employeeId,
            preApprovalType: item.preApprovalType,
            duration: item.duration,
            period: item.period,
            startDate: moment(item.startDate).format("yyyy-MM-DD"),
            endDate: moment(item.endDate).format("yyyy-MM-DD"),
            totalDays: parseFloat(item.totalDays),
            reason: item.reason,
            status: item.status === "Applied" ? "Cancelled" : "Cancel Applied",
            formId: vm.formId,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              item.status === "Applied"
                ? `${this.accessFormName} request canceled successfully`
                : "Cancellation applied for the pre-approval request successfully.",
            type: "success",
          };
          this.onCloseCancelStatusWarningWarningModal();
          vm.showAlert(snackbarData);
          this.refetchPreApprovalList();
        })
        .catch((err) => {
          vm.isLoading = false;

          vm.handleAddEditError(err);
          this.onCloseCancelStatusWarningWarningModal();
        });
    },
    handleAddEditError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodesWithValidation(err);
        var validationErrors = errorCode[1];
        var validationMessages = "";
        var errCode = "";
        switch (errorCode[0]) {
          case "CHR0050": // Technical error
            snackbarData.message = `Error while Add / Update the ${this.accessFormName} Request`;
            break;
          case "CHR0051": // This employee does not have admin or manager access.
            snackbarData.message =
              "The pre-approval configuration does not exist for the selected employee.";
            break;
          case "CHR0052": // Organization details does not exists.
            snackbarData.message =
              "Error while deleting the workflow details. Please contact the system admin.";
            break;
          case "CHR0053":
            snackbarData.message =
              "There are some difficulties to add / update the pre-approval request. Please contact the system admin.";
            break;
          case "CHR0054":
            snackbarData.message =
              "Error while retrieving the workflow details. Please contact the system admin.";
            break;
          case "CHR0055":
            snackbarData.message =
              "Error while retrieving the pre-approval details. Please contact the system admin.";
            break;
          case "CHR0065":
            validationMessages = err.message;
            if (validationMessages) {
              snackbarData.message = validationMessages;
            }
            break;
          case "BAD_USER_INPUT":
            // add all the backend validation error messages as single sentence to present it to the users
            if (validationErrors) {
              for (errCode in validationErrors) {
                // IVE0316 - message: "Start date should not be empty.",message1: "Start date should not be less than the advance notification days".
                //  code: 'IVE0317',message: "End date should not be empty.", message1: "End date should be greater than start date.", message2: "You have already applied the pre-approval request for the selected date range",message3: "You are not allowed to apply more than the configuration limit"
                if (
                  errCode === "IVE0312" ||
                  errCode === "IVE0313" ||
                  errCode === "IVE0316" ||
                  errCode === "IVE0317"
                ) {
                  validationMessages =
                    validationMessages + " " + validationErrors[errCode];
                }
              }
            }
            if (validationMessages) {
              snackbarData.message = validationMessages;
            }
            // other validation errors are not handled by users. So as of now it was considers as common error.
            else {
              // IVE0083 - Please provide a valid Employee Id.
              snackbarData.message = `Something went wrong while adding/updating ${this.accessFormName}-request. If you continue to see this issue please contact the platform administrator.`;
            }

            break;
          default:
            snackbarData.message = `Something went wrong while adding/updating ${this.accessFormName}-request. If you continue to see this issue please contact the platform administrator.`;
            break;
        }
      } else {
        snackbarData.message = `Something went wrong while adding/updating ${this.accessFormName}-request. Please try after some time.`;
      }
      this.showAlert(snackbarData);
    },

    handleFetchPreApprovalError(err = "") {
      this.isListLoading = false;
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0100": // This employee does not have admin or manager access.
            this.errorContent =
              "Sorry you don't have access rights to view pre-approvals details. Please contact HR administrator";
            break;
          case "CHR0036": // Technical error
          default:
            this.errorContent =
              "Something went wrong while retrieving the pre-approval details. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        this.errorContent =
          "Something went wrong while retrieving the pre-approval details. Please try after some time.";
      }
      this.isErrorInList = true;
    },

    onCloseMultipleWarningModal() {
      this.openMultipleWarningModal = false;
    },
    onTabChange(tabName) {
      if (tabName === "Approvals") {
        this.isLoading = true;
        this.$router.push("/approvals/approval-management?form_id=244");
      }
      this.isLoading = false;
    },
  },
};
</script>

<style scoped>
.pre-approvals-container {
  padding: 5em 2em 0em 3em;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .pre-approvals-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
