import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_STATUS_LIST = gql`
  query getAtsStatusList($formId: Int!, $conditions: [commonApplyCondition]) {
    getAtsStatusList(formId: $formId, conditions: $conditions) {
      errorCode
      message
      statusList {
        Id
        Status
        Form_Id
        Is_Default
        Stage_Id
        Stage
        Override
        Order
      }
    }
  }
`;

export const ADD_EDIT_STATUS = gql`
  mutation addUpdateStatus(
    $statusId: Int!
    $status: String!
    $formId: Int!
    $stageId: Int!
  ) {
    addUpdateStatus(
      statusId: $statusId
      status: $status
      formId: $formId
      stageId: $stageId
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_STATUS = gql`
  mutation deleteAtsStatus($statusId: Int!) {
    deleteAtsStatus(statusId: $statusId) {
      errorCode
      message
    }
  }
`;
