import gql from "graphql-tag";

export const RETRIEVE_EMP_MONITOR_SCREENSHOT_FREQUENCY_SETTINGS = gql`
  query retrieveScreenshotsFrequencySettings($formId: Int) {
    retrieveScreenshotsFrequencySettings(formId: $formId) {
      errorCode
      message
      screenshotsFrequency {
        organizationLevelScreenshotsFrequencySettings {
          capture_screenshot
          screenshot_frequency
          no_of_screenshots_per_frequency
          screenshot_frequency_type
          additional_screenshots_per_frequency
        }
        employeeLevelScreenshotsFrequencySettings {
          employee_id
          capture_screenshot
          screenshot_frequency
          no_of_screenshots_per_frequency
          additional_screenshots_per_frequency
          screenshot_frequency_type
          photo_path
          user_defined_empid
          employee_name
          emp_email
        }
      }
    }
  }
`;

export const UPDATE_EMP_MONITOR_SCREENSHOT_FREQUENCY_SETTINGS = gql`
  mutation updateScreenshotFrequencySettings(
    $formId: Int
    $organizationSettings: [organizationScreenshotFrequencyInput]!
    $employeeSettings: [employeeScreenshotFrequencyInput]!
  ) {
    updateScreenshotFrequencySettings(
      formId: $formId
      organizationSettings: $organizationSettings
      employeeSettings: $employeeSettings
    ) {
      errorCode
      message
    }
  }
`;
