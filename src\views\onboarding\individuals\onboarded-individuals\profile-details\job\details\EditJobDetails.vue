<template>
  <v-dialog
    v-model="showRolesAssociation"
    v-if="showRolesAssociation"
    @click:outside="onCloseRoleAccessModal()"
    max-width="1300px"
  >
    <RoleAccessRights
      :selectedItem="selectedRole"
      action-type="empView"
      @close-form="onCloseRoleAccessModal()"
    ></RoleAccessRights
  ></v-dialog>
  <div v-if="isMounted">
    <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
      >Job Info</span
    >
    <v-form ref="editJobDetailsForm" class="pa-2">
      <v-row class="mb-8">
        <v-col cols="12" md="4" sm="6">
          <div class="d-flex mt-n2">
            <p class="custom-label">
              Role (Access Rights)<span style="color: red">*</span>
            </p>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon class="ml-1" v-bind="props" size="small" color="info">
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="max-width: 350px !important">
                Role access defines the specific permissions and privileges
                granted to users based on their assigned roles. These
                permissions determine what actions a user can perform and what
                data they can access within the application.
              </div>
            </v-tooltip>
          </div>
          <div v-if="!higherHierachyRole && !dropdownListFetching">
            <p class="text-subtitle-1 font-weight-regular mt-3">
              {{ checkNullValue(jobDetails.Roles_Name) }}
            </p>
          </div>
          <CustomSelect
            v-else
            :items="roles"
            label=""
            :isRequired="true"
            :rules="[
              required('Role (Access Rights)', editedJobDetails.Roles_Id),
            ]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Roles_Id"
            itemValue="Roles_Id"
            itemTitle="Roles_Name"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="roleAccess"
            :disabledValue="
              isSuperAdmin ? [] : ['Super Admin', 'Super Admin(T)']
            "
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="onChangeCustomSelectField($event, 'Roles_Id', null)"
          ></CustomSelect>
          <v-btn
            v-if="higherHierachyRole"
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openRolesForm"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Role</v-btn
          >
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <v-menu
            v-model="dojMenu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                ref="Date of Joining"
                v-model="formattedDateOfJoin"
                prepend-inner-icon="fas fa-calendar"
                :rules="[required('Date of Joining', formattedDateOfJoin)]"
                readonly
                v-bind="props"
                variant="solo"
              >
                <template v-slot:label>
                  Date of Joining
                  <span style="color: red">*</span>
                </template></v-text-field
              >
            </template>
            <v-date-picker
              v-model="editedJobDetails.Date_Of_Join"
              :min="selectedEmpDobDate"
            />
          </v-menu>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="designations"
            label="Designation"
            :isRequired="true"
            :rules="[required('Designation', editedJobDetails.Designation_Id)]"
            :isLoading="designationListLoading"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Designation_Id"
            itemValue="Designation_Id"
            itemTitle="Designation_Name"
            placeholder="Type minimum 3 characters to list"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="designations"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="onChangeCustomSelectField($event, 'Designation_Id')"
            @update-search-value="callDesignationList($event)"
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openDesignationsForm"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
            Designation</v-btn
          >
        </v-col>
        <v-col
          v-if="labelList[425]?.Field_Visiblity.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <CustomSelect
            ref="selectedServiceProvider"
            v-model="selectedJobRoles"
            :items="jobRolesList"
            :isLoading="dropdownListLoading"
            item-title="Job_Role"
            item-value="Job_Role_Id"
            :label="labelList[425].Field_Alias"
            :isAutoComplete="true"
            :isRequired="labelList[425].Mandatory_Field.toLowerCase() === 'yes'"
            :rules="[
              labelList[425].Mandatory_Field.toLowerCase() == 'yes'
                ? selectedJobRoles
                  ? required(
                      `${labelList[425].Field_Alias}`,
                      selectedJobRoles[0]
                    )
                  : required(`${labelList[425].Field_Alias}`, selectedJobRoles)
                : true,
            ]"
            variant="solo"
            :selectProperties="{
              multiple: true,
              chips: true,
              closableChips: true,
              clearable: true,
            }"
            :itemSelected="selectedJobRoles"
            @selected-item="selectedJobRoles = $event"
            @update:model-value="onChangeFields"
          ></CustomSelect>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="departments"
            label="Department"
            :isRequired="true"
            :rules="[required('Department', editedJobDetails.Department_Id)]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Department_Id"
            itemValue="Department_Id"
            itemTitle="Department_Name"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="department"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="onChangeCustomSelectField($event, 'Department_Id')"
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openDepartmentsForm"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
            Department</v-btn
          >
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="locations"
            label="Location"
            :isRequired="true"
            :rules="[required('Location', editedJobDetails.Location_Id)]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Location_Id"
            itemValue="Location_Id"
            itemTitle="Location_Name"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="location"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="onChangeCustomSelectField($event, 'Location_Id')"
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openLocationsForm"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
            Location</v-btn
          >
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="workSchedules"
            label="Work Schedule"
            :isRequired="true"
            :rules="[required('Work Schedule', editedJobDetails.Work_Schedule)]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Work_Schedule"
            itemValue="WorkSchedule_Id"
            itemTitle="Title"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="workSchedule"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="onChangeCustomSelectField($event, 'Work_Schedule')"
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openWorkScheduleForm"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Work
            Schedule</v-btn
          >
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="employeeTypes"
            label="Employee Type"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.EmpType_Id"
            itemValue="EmpType_Id"
            itemTitle="Employee_Type"
            :isRequired="true"
            :rules="[required('Employee Type', editedJobDetails.EmpType_Id)]"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="employeeType"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="onChangeCustomSelectField($event, 'EmpType_Id')"
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openEmployeeTypeForm"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Employee
            Type</v-btn
          >
        </v-col>
        <v-col v-if="editedJobDetails.Field_Force" cols="12" md="4" sm="6">
          <div class="d-flex mt-n2">
            <p class="custom-label">
              {{ getCustomFieldName(115, "Service Provider")
              }}<span style="color: red">*</span>
            </p>
            <v-tooltip location="right">
              <template v-slot:activator="{ props }">
                <v-icon class="ml-1" v-bind="props" size="small" color="info">
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="max-width: 350px !important">
                To transfer an employee from one
                {{ getCustomFieldName(115, "Service Provider").toLowerCase() }}
                to another, kindly initiate the resignation process
              </div>
            </v-tooltip>
          </div>
          <CustomSelect
            :items="serviceProviders"
            label=""
            :isRequired="true"
            :rules="[
              required(
                getCustomFieldName(115, 'Service Provider'),
                editedJobDetails.Service_Provider_Id
              ),
            ]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Service_Provider_Id"
            itemValue="Service_Provider_Id"
            itemTitle="Service_Provider_Name"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="serviceProvider"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="
              onChangeCustomSelectField($event, 'Service_Provider_Id')
            "
          ></CustomSelect>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <v-text-field
            v-model.trim="editedJobDetails.Emp_Email"
            variant="solo"
            :rules="
              selectedCandidateDetails.Allow_User_Signin &&
              !selectedCandidateDetails.Enable_Sign_In_With_Mobile_No
                ? [
                    required('Work Email', editedJobDetails.Emp_Email),
                    validateWithRulesAndReturnMessages(
                      editedJobDetails.Emp_Email,
                      'empEmail',
                      'Work Email'
                    ),
                    alreadyExistErrMsg['Emp_Email'],
                  ]
                : checkFieldAvailability(editedJobDetails.Emp_Email)
                ? [
                    validateWithRulesAndReturnMessages(
                      editedJobDetails.Emp_Email,
                      'empEmail',
                      'Work Email'
                    ),
                    alreadyExistErrMsg['Emp_Email'],
                  ]
                : [true]
            "
            ref="workEmail"
            @update:model-value="onChangeFields"
            @change="validateFieldAlreadyExist('Emp_Email', 'Work Email')"
          >
            <template v-slot:label>
              Work Email<span
                v-if="
                  selectedCandidateDetails.Allow_User_Signin &&
                  !selectedCandidateDetails.Enable_Sign_In_With_Mobile_No
                "
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList[446]?.Field_Visiblity === 'Yes'"
        >
          <div class="d-flex mt-n2">
            <p class="custom-label">
              {{ labelList[446].Field_Alias
              }}<span
                v-if="labelList[446].Mandatory_Field === 'Yes'"
                style="color: red"
                >*</span
              >
            </p>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon class="ml-1" v-bind="props" size="small" color="info">
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="max-width: 350px !important">
                The state-imposed tax based on the employee's profession. The
                tax amount is determined by the nature of the profession-
                whether they are classified as a Director or a Employee(Other
                Professional)
              </div>
            </v-tooltip>
          </div>
          <CustomSelect
            :items="empProfessions"
            label=""
            :isRequired="labelList[446].Mandatory_Field === 'Yes'"
            :rules="[
              labelList[446].Mandatory_Field === 'Yes'
                ? required(
                    labelList[446].Field_Alias,
                    editedJobDetails.Emp_Profession
                  )
                : true,
            ]"
            :isLoading="empProfessionListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Emp_Profession"
            itemValue="Profession_Id"
            itemTitle="Profession_Name"
            tooltipMessage=""
            appendIcon="fas fa-redo-alt"
            ref="employeeProfession"
            @append-icon-clicked="retrieveEmpProfessions()"
            @selected-item="onChangeCustomSelectField($event, 'Emp_Profession')"
          ></CustomSelect>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="managers"
            label="Manager"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="
              editedJobDetails.Manager_Id ? editedJobDetails.Manager_Id : ''
            "
            itemValue="Manager_Id"
            itemTitle="managerIdName"
            :selectProperties="{
              clearable: true,
            }"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="manager"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="onChangeCustomSelectField($event, 'Manager_Id')"
          ></CustomSelect>
        </v-col>
        <v-col
          v-if="labelList[384]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <CustomSelect
            :items="businessUnits"
            :label="labelList[384]?.Field_Alias"
            :isLoading="businessUnitListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :isRequired="
              labelList[384]?.Mandatory_Field?.toLowerCase() === 'yes'
            "
            :rules="
              labelList['384']?.Mandatory_Field?.toLowerCase() === 'yes'
                ? [
                    required(
                      labelList['384']?.Field_Alias,
                      editedJobDetails.Business_Unit_Id
                    ),
                  ]
                : [true]
            "
            :itemSelected="editedJobDetails.Business_Unit_Id"
            itemValue="businessUnitId"
            itemTitle="businessUnit"
            :selectProperties="{
              clearable: true,
            }"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="businessUnit"
            @append-icon-clicked="retrieveBusinessUnit()"
            @selected-item="
              onChangeCustomSelectField($event, 'Business_Unit_Id')
            "
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openBusinessUnitForm"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
            {{ labelList[384]?.Field_Alias }}</v-btn
          >
        </v-col>
        <v-col
          v-if="benefitsApplicable && labelList[304].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            v-model.trim="editedJobDetails.Pf_PolicyNo"
            label="PF Number"
            :rules="[
              checkFieldAvailability(editedJobDetails.Pf_PolicyNo)
                ? alreadyExistErrMsg['Pf_PolicyNo']
                : true,
              checkFieldAvailability(editedJobDetails.Pf_PolicyNo)
                ? validateWithRulesAndReturnMessages(
                    editedJobDetails.Pf_PolicyNo,
                    'pf',
                    labelList[304].Field_Alias
                  )
                : true,
              labelList[304].Mandatory_Field == 'Yes'
                ? required(
                    labelList[304].Field_Alias,
                    editedJobDetails.Pf_PolicyNo
                  )
                : true,
            ]"
            variant="solo"
            ref="pfNumber"
            @update:model-value="onChangeFields('Pf_PolicyNo')"
            @change="validateFieldAlreadyExist('Pf_PolicyNo', 'PF Number')"
          >
            <template v-slot:label>
              {{ labelList[304].Field_Alias }}
              <span v-if="labelList[304].Mandatory_Field == 'Yes'">*</span>
            </template>
          </v-text-field>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <v-text-field
            v-model="editedJobDetails.Job_Code"
            label="Job Code"
            variant="solo"
            :rules="[
              checkFieldAvailability(editedJobDetails.Job_Code)
                ? validateWithRulesAndReturnMessages(
                    editedJobDetails.Job_Code,
                    'jobCode',
                    'Job Code'
                  )
                : true,
            ]"
            ref="jobCode"
            @update:model-value="onChangeFields"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <v-menu
            v-model="probationMenu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                ref="Probation Date"
                v-model="formattedProbationDate"
                prepend-inner-icon="fas fa-calendar"
                :rules="[required('Probation Date', formattedProbationDate)]"
                readonly
                :disabled="
                  !editedJobDetails.Date_Of_Join ||
                  !editedJobDetails.Designation_Id
                "
                v-bind="props"
                variant="solo"
              >
                <template v-slot:label>
                  Probation Date
                  <span style="color: red">*</span>
                </template></v-text-field
              >
            </template>
            <v-date-picker
              v-model="editedJobDetails.Probation_Date"
              :min="probationMin"
            />
          </v-menu>
        </v-col>
        <v-col
          v-if="labelList['151'] && labelList['151'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <CustomSelect
            ref="selectedOrganizationGroup"
            v-model="selectedOrganizationGroup"
            :items="orgGroupList"
            :isLoading="orgGroupListLoading"
            item-title="organizationGroupFullName"
            item-value="organizationGroupId"
            :label="labelList['151'].Field_Alias"
            :isAutoComplete="true"
            :isRequired="
              labelList['151'].Mandatory_Field === 'Yes' ? true : false
            "
            :rules="
              labelList['151'].Mandatory_Field === 'Yes'
                ? [
                    required(
                      labelList['151'].Field_Alias,
                      selectedOrganizationGroup
                    ),
                  ]
                : [true]
            "
            variant="solo"
            :itemSelected="editedJobDetails.Organization_Group_Id"
            @selected-item="
              onChangeCustomSelectField($event, 'Organization_Group_Id')
            "
          />
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Confirmed</p>
          <v-switch
            color="primary"
            v-model="editedJobDetails.Confirmed"
            :true-value="1"
            :false-value="0"
            @update:model-value="onChangeFields"
          ></v-switch>
        </v-col>
        <v-col v-if="editedJobDetails.Confirmed" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Confirmation Date
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                The confirmation date is automatically updated overnight via an
                automated process, which is determined by the probation end
                date. It's essential to review and, if necessary, adjust the
                probation end date to ensure accuracy when confirming an
                employee.
              </div>
            </v-tooltip>
          </p>
          <p class="text-subtitle-1 font-weight-regular pt-3">
            {{ formatDate(confirmationDate) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Commission Based Employee
          </p>
          <v-switch
            color="primary"
            v-model="editedJobDetails.Commission_Employee"
            :true-value="1"
            :false-value="0"
            @update:model-value="onChangeFields"
          ></v-switch>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <div class="d-flex">
            <p class="text-subtitle-1 text-grey-darken-1">
              Attendance Enforced Payment
            </p>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                Enabling this flag generates the payslip for this employee only
                if this employee added attendance, leave or the compoff details
                for all the working days.
              </div>
            </v-tooltip>
          </div>
          <v-switch
            color="primary"
            v-model="editedJobDetails.Attendance_Enforced_Payment"
            :true-value="1"
            :false-value="0"
            @update:model-value="onChangeFields"
          ></v-switch>
        </v-col>
        <v-col cols="12" md="4" sm="6" v-if="labelList[306].Field_Visiblity">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[306].Field_Alias }}
            <span v-if="labelList[306].Mandatory_Field == 'Yes'">*</span>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                Activating a TDS (Tax Deducted at Source) exemption will suspend
                the calculation and withholding of tax for an employee until
                such exemption is revoked. It's important to note that the
                responsibility for deducting tax at the time of payment and
                subsequently remitting it to the Tax Authority rests with the
                employer.
              </div>
            </v-tooltip>
          </p>
          <v-switch
            color="primary"
            v-model="editedJobDetails.TDS_Exemption"
            :true-value="1"
            :false-value="0"
            @update:model-value="onChangeFields"
          ></v-switch>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <div class="d-flex">
            <p class="text-subtitle-1 text-grey-darken-1">Employee Status</p>
            <p class="text-subtitle-1 font-weight-regular mt-2">
              {{ editedJobDetails.Emp_Status }}
            </p>
          </div>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="custom-label mt-n3 mb-n2">Previous Experience</p>
          <section class="d-flex text-body-2 mt-2">
            <v-text-field
              v-model="prevExpInYear"
              variant="solo"
              type="number"
              :rules="[
                checkFieldAvailability(prevExpInYear)
                  ? validateWithRulesAndReturnMessages(
                      prevExpInYear,
                      'previousExpYear',
                      'Previous Year',
                      true
                    )
                  : true,
              ]"
              ref="previousYear"
              @update:model-value="onChangeFields"
            ></v-text-field>
            <span class="px-1 mt-5 text-grey">Years</span>
            <v-text-field
              v-model="prevExpInMonth"
              variant="solo"
              type="number"
              :rules="[
                checkFieldAvailability(prevExpInMonth)
                  ? validateWithRulesAndReturnMessages(
                      prevExpInMonth,
                      'previousExpMonth',
                      'Previous Month',
                      true
                    )
                  : true,
              ]"
              ref="previousMonth"
              @update:model-value="onChangeFields"
            ></v-text-field>
            <span class="px-1 mt-4 text-grey">Months</span>
          </section>
        </v-col>
      </v-row>
    </v-form>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          size="small"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
  <AppWarningModal
    v-if="showRoleChangeAlert"
    :open-modal="showRoleChangeAlert"
    confirmation-heading="Assigning roles determines application access and may grant elevated privileges. Please review the access before making the role update"
    icon-name=""
    acceptButtonText="Review"
    closeButtonText="Close"
    @close-warning-modal="onCloseRoleModal()"
    @accept-modal="openRoleAccessModal()"
  ></AppWarningModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_UPDATE_JOB_DETAILS,
  VALIDATE_FIELD_AVAILABILITY,
  RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION,
  VALIDATE_BENEFIT_APPLICABLE_BASED_ON_EMP_TYPE,
} from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import { RETRIEVE_ORGANISATION_GROUP_LIST } from "@/graphql/corehr/organisationGroupQueries";
import {
  LIST_EMP_PROFESSION,
  LIST_BUSINESS_UNIT,
} from "@/graphql/dropDownQueries";
import { checkNullValue, getCustomFieldName } from "@/helper";
const RoleAccessRights = defineAsyncComponent(() =>
  import("../../../../../../settings/coreHr/roles/RoleAccessRights.vue")
);
import { LIST_JOB_ROLES } from "@/graphql/onboarding/individualQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "EditJobDetails",
  mixins: [validationRules],
  emits: ["close-edit-form", "edit-updated"],
  props: {
    jobDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
  },
  components: {
    CustomSelect,
    RoleAccessRights,
  },
  data() {
    return {
      editedJobDetails: {},
      isFormDirty: false,
      isMounted: false,
      prevExpInYear: null,
      prevExpInMonth: null,
      alreadyExistErrMsg: {
        Emp_Email: true,
        Pf_PolicyNo: true,
      },
      benefitsApplicable: false,
      openWarningModal: false,
      //Date-picker
      formattedDateOfJoin: "",
      formattedProbationDate: "",
      dojMenu: false,
      probationMenu: false,
      // list
      managers: [],
      locations: [],
      workSchedules: [],
      employeeTypes: [],
      designations: [],
      departments: [],
      serviceProviders: [],
      empProfessions: [],
      businessUnits: [],
      roles: [],
      orgGroupList: [],
      dropdownListFetching: false,
      empProfessionListFetching: false,
      businessUnitListFetching: false,
      // Organization Group
      orgGroupListLoading: false,
      selectedOrganizationGroup: null,
      // roles
      showRoleChangeAlert: false,
      showRolesAssociation: false,
      // edit
      openBottomSheet: true,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      probationMinDate: "",
      jobRolesList: [],
      selectedJobRoles: [],
    };
  },

  computed: {
    higherHierachyRole() {
      return this.roles.some(
        (el) =>
          el.Roles_Id == this.selectedCandidateDetails.Roles_Id ||
          this.selectedCandidateDetails.Roles_Id === null ||
          this.isSuperAdmin
      );
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // returns baseurl of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    selectedEmpDobDate() {
      if (
        this.selectedCandidateDOB &&
        this.selectedCandidateDOB !== "0000-00-00"
      ) {
        return moment(this.selectedCandidateDOB).format("YYYY-MM-DD");
      } else return null;
    },
    probationMin() {
      if (this.probationMinDate && this.probationMinDate !== "0000-00-00") {
        return new Date(this.probationMinDate);
      } else if (
        this.editedJobDetails.Date_Of_Join &&
        this.editedJobDetails.Date_Of_Join !== "0000-00-00"
      ) {
        return moment(this.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD");
      } else return null;
    },
    previousExperience() {
      let yearsToMonth = this.prevExpInYear * 12;
      yearsToMonth = parseInt(yearsToMonth);
      let months = this.prevExpInMonth ? this.prevExpInMonth : 0;
      months = parseInt(months);
      return yearsToMonth + months;
    },
    confirmationDate() {
      let editedProbationDate = this.editedJobDetails.Probation_Date
        ? moment(this.editedJobDetails.Probation_Date).format("YYYY-MM-DD")
        : "";
      let retrivedProbationDate = this.jobDetails.Probation_Date
        ? moment(this.jobDetails.Probation_Date).format("YYYY-MM-DD")
        : "";
      if (
        (editedProbationDate !== retrivedProbationDate ||
          this.editedJobDetails.Confirmation_Date == "0000-00-00" ||
          !this.editedJobDetails.Confirmation_Date) &&
        this.editedJobDetails.Probation_Date
      ) {
        return moment(this.editedJobDetails.Probation_Date)
          .add(1, "day")
          .format("YYYY-MM-DD");
      } else {
        return this.editedJobDetails.Confirmation_Date;
      }
    },
    selectedRole() {
      let filteredRoles = this.roles.filter(
        (el) => el.Roles_Id === this.editedJobDetails.Roles_Id
      );
      return filteredRoles[0];
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return true;
      } else return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", val);
    },
    "editedJobDetails.Date_Of_Join": function (val) {
      if (val) {
        this.dojMenu = false;
        this.formattedDateOfJoin = this.formatDate(val);
      }
    },
    "editedJobDetails.Probation_Date": function (val) {
      if (val) {
        this.probationMenu = false;
        this.formattedProbationDate = this.formatDate(val);
      }
    },
    "editedJobDetails.Designation_Id": {
      handler(val) {
        if (val) {
          this.fetchJobRoles(val);
        }
      },
      immediate: true,
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.editedJobDetails = JSON.parse(JSON.stringify(this.jobDetails));
    if (this.editedJobDetails.Date_Of_Join) {
      this.formattedDateOfJoin = this.formatDate(
        this.editedJobDetails?.Date_Of_Join
      );
      this.editedJobDetails.Date_Of_Join = this.editedJobDetails.Date_Of_Join
        ? new Date(this.editedJobDetails.Date_Of_Join)
        : null;
    }
    if (this.editedJobDetails.Probation_Date) {
      this.formattedProbationDate = this.formatDate(
        this.editedJobDetails?.Probation_Date
      );
      this.editedJobDetails.Probation_Date = this.editedJobDetails
        .Probation_Date
        ? new Date(this.editedJobDetails.Probation_Date)
        : null;
    }
    this.selectedOrganizationGroup = this.editedJobDetails?.Organization_Group;
    this.prevExpInYear = parseInt(
      this.editedJobDetails["Previous_Employee_Experience"] / 12
    );
    this.prevExpInMonth = parseInt(
      this.editedJobDetails["Previous_Employee_Experience"] % 12
    );
    this.designations = [
      {
        Designation_Id: this.selectedCandidateDetails.Designation_Id,
        Designation_Name: this.selectedCandidateDetails.Designation_Name,
      },
    ];
    this.retrieveDropdownDetails();
    this.retrieveEmpProfessions();
    this.retrieveBusinessUnit();
    this.validateBenefitApplicable();
    this.fetchOrganizationGroups();
    this.isMounted = true;
  },

  methods: {
    checkNullValue,
    getCustomFieldName,
    checkFieldAvailability(value) {
      if (value) {
        let strValue = value.toString();
        return strValue.trim().length > 0;
      } else return false;
    },
    onChangeFields(dateErrMsgVariable = "", field = "") {
      if (field === "Date_Of_Join") {
        if (
          moment(this.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD") !==
          this.jobDetails.Date_Of_Join
        ) {
          this.retrieveProbationDate();
        }
      }
      if (dateErrMsgVariable) this[dateErrMsgVariable] = "";
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field, isFormDirty = true) {
      this.editedJobDetails[field] = value;
      if (isFormDirty) {
        this.onChangeFields();
      } else {
        this.isFormDirty = false;
      }
      if (field === "Designation_Id") {
        this.retrieveProbationDate();
      }
      if (field === "EmpType_Id") {
        this.validateBenefitApplicable();
      }
      if (field === "Roles_Id") {
        this.showRoleChangeAlert = true;
      }
    },
    onCloseRoleModal() {
      this.showRoleChangeAlert = false;
      this.isFormDirty = true;
    },

    openRoleAccessModal() {
      this.showRoleChangeAlert = false;
      this.showRolesAssociation = true;
      this.isFormDirty = false;
    },

    onCloseRoleAccessModal() {
      this.isFormDirty = true;
      this.showRolesAssociation = false;
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.$emit("close-edit-form");
      }
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.editJobDetailsForm.validate();
      mixpanel.track("Onboarded-candidate-job-edit-submit-clicked");
      if (
        isFormValid &&
        isFormValid.valid &&
        (!this.editedJobDetails.Confirmed || this.confirmationDate)
      ) {
        this.updateJobDetails();
      } else {
        // Check the validity of each field
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "roleAccess",
                "designations",
                "department",
                "location",
                "workSchedule",
                "employeeType",
                "serviceProvider",
                "employeeProfession",
                "manager",
                "businessUnit",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 2, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },

    updateJobDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_JOB_DETAILS,
          variables: {
            Candidate_Id: vm.selectedCandidateId,
            Roles_Id: vm.editedJobDetails.Roles_Id
              ? vm.editedJobDetails.Roles_Id
              : null,
            Designation_Id: vm.editedJobDetails.Designation_Id,
            Department_Id: vm.editedJobDetails.Department_Id,
            Location_Id: vm.editedJobDetails.Location_Id,
            Job_Code: vm.editedJobDetails.Job_Code,
            Emp_Email: vm.editedJobDetails.Emp_Email,
            Manager_Id: vm.editedJobDetails.Manager_Id,
            Emp_Profession: vm.editedJobDetails.Emp_Profession,
            Confirmed: vm.editedJobDetails.Confirmed,
            Confirmation_Date:
              vm.editedJobDetails.Confirmed &&
              moment(vm.confirmationDate).isValid()
                ? moment(vm.confirmationDate).format("YYYY-MM-DD")
                : null,
            Probation_Date: moment(vm.editedJobDetails.Probation_Date).isValid()
              ? moment(vm.editedJobDetails.Probation_Date).format("YYYY-MM-DD")
              : null,
            EmpType_Id: vm.editedJobDetails.EmpType_Id,
            Commission_Employee: vm.editedJobDetails.Commission_Employee,
            Work_Schedule: vm.editedJobDetails.Work_Schedule,
            TDS_Exemption: vm.editedJobDetails.TDS_Exemption,
            Attendance_Enforced_Payment:
              vm.editedJobDetails.Attendance_Enforced_Payment,
            Previous_Employee_Experience: vm.previousExperience,
            Service_Provider_Id: vm.editedJobDetails.Service_Provider_Id
              ? vm.editedJobDetails.Service_Provider_Id
              : 0,
            Date_Of_Join: moment(vm.editedJobDetails.Date_Of_Join).isValid()
              ? moment(vm.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD")
              : null,
            Business_Unit_Id: vm.editedJobDetails.Business_Unit_Id,
            Pf_PolicyNo: vm.editedJobDetails.Pf_PolicyNo
              ? vm.editedJobDetails.Pf_PolicyNo
              : "",
            Organization_Group_Id: vm.editedJobDetails.Organization_Group_Id,
            Job_Role_Ids: vm.selectedJobRoles,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-job-edit-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Job details updated successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
          vm.$emit("edit-updated");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-job-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "job details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveDropdownDetails() {
      this.dropdownListFetching = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              locations,
              employeeType,
              workSchedules,
              managers,
              serviceProvider,
              roles,
            } = res.data.getDropDownBoxDetails;
            this.departments = departments;
            this.locations = locations;
            this.employeeTypes = employeeType;
            this.workSchedules = workSchedules;
            this.managers =
              managers && managers.length > 0
                ? managers.filter((el) => {
                    return el.Manager_Id != this.selectedCandidateId;
                  })
                : [];
            this.managers = this.managers.map((item) => ({
              ...item,
              managerIdName:
                item.Manager_Name + " - " + item.Manager_User_Defined_EmpId,
            }));
            this.serviceProviders = serviceProvider;
            this.roles = roles;
            if (this.editedJobDetails.Roles_Id) {
              let roleExist =
                roles && roles.length > 0
                  ? roles.filter((el) => {
                      return el.Roles_Id == this.editedJobDetails.Roles_Id;
                    })
                  : [];
              if (!roleExist || roleExist.length === 0) {
                this.editedJobDetails.Roles_Id = null;
              }
            }
            if (this.jobDetails.Manager_Id) {
              let isManagerAvailable = this.managers.some(
                (el) => el.Manager_Id === this.jobDetails.Manager_Id
              );
              if (!isManagerAvailable) {
                this.onChangeCustomSelectField(null, "Manager_Id");
              }
            }
          }
          this.dropdownListFetching = false;
        })
        .catch(() => {
          this.dropdownListFetching = false;
        });
    },

    //retrieve Organization Group data
    fetchOrganizationGroups() {
      let vm = this;
      vm.orgGroupListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_ORGANISATION_GROUP_LIST,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: { formId: 178 },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listOrganizationGroup &&
            !response.data.listOrganizationGroup.errorCode
          ) {
            const { organizationGroupObject } =
              response.data.listOrganizationGroup;
            if (organizationGroupObject) {
              const orgList = organizationGroupObject.organizationGroupList
                ? organizationGroupObject.organizationGroupList
                : [];
              vm.orgGroupList = orgList.filter(
                (group) => group.status === "Active"
              );
            } else {
              vm.orgGroupList = [];
            }
          } else {
            vm.orgGroupList = [];
          }
          vm.orgGroupListLoading = false;
        })
        .catch((err) => {
          vm.orgGroupListLoading = false;
          vm.handleOrgGroupError(err);
        });
    },
    handleOrgGroupError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "organization unit configuration",
        isListError: false,
      });
    },

    retrieveEmpProfessions() {
      let vm = this;
      vm.empProfessionListFetching = true;
      vm.$apollo
        .query({
          query: LIST_EMP_PROFESSION,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listEmpProfession &&
            !response.data.listEmpProfession.errorCode
          ) {
            const { professions } = response.data.listEmpProfession;
            vm.empProfessions =
              professions && professions.length > 0 ? professions : [];
            if (
              vm.empProfessions.length > 0 &&
              !vm.editedJobDetails.Emp_Profession
            ) {
              vm.editedJobDetails.Emp_Profession = 1;
            }
          }
          vm.empProfessionListFetching = false;
        })
        .catch(() => {
          vm.empProfessionListFetching = false;
        });
    },

    retrieveBusinessUnit() {
      let vm = this;
      vm.businessUnitListFetching = true;
      vm.$apollo
        .query({
          query: LIST_BUSINESS_UNIT,
          client: "apolloClientI",
          variables: {
            action: "active",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBusinessUnitInDropdown &&
            !response.data.listBusinessUnitInDropdown.errorCode
          ) {
            const { settings } = response.data.listBusinessUnitInDropdown;
            vm.businessUnits = settings && settings.length > 0 ? settings : [];
          }
          vm.businessUnitListFetching = false;
        })
        .catch(() => {
          vm.businessUnitListFetching = false;
        });
    },
    validateFieldAlreadyExist(field, label) {
      let vm = this;
      if (
        vm.editedJobDetails[field] &&
        vm.editedJobDetails[field] !== vm.jobDetails[field]
      ) {
        vm.$apollo
          .query({
            query: VALIDATE_FIELD_AVAILABILITY,
            client: "apolloClientAC",
            variables: {
              employeeId: 0,
              columnValue: vm.editedJobDetails[field],
              columnName: field,
              tableName: "emp_job",
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateCommonAvailability &&
              !response.data.validateCommonAvailability.errorCode
            ) {
              const { isAvailable } = response.data.validateCommonAvailability;
              if (!isAvailable) {
                vm.alreadyExistErrMsg[field] = label + " already exist";
              } else {
                vm.alreadyExistErrMsg[field] = true;
              }
            }
            vm.$refs.editJobDetailsForm.validate();
          })
          .catch((err) => {
            vm.alreadyExistErrMsg[field] = true;
            vm.$refs.editJobDetailsForm.validate();
            let fieldLabel = label.toLowerCase();
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: fieldLabel,
              isListError: false,
            });
          });
      }
    },
    retrieveProbationDate() {
      let vm = this;
      if (
        vm.editedJobDetails.Designation_Id &&
        vm.editedJobDetails.Date_Of_Join
      ) {
        vm.$apollo
          .query({
            query: RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION,
            client: "apolloClientAC",
            variables: {
              designationId: vm.editedJobDetails.Designation_Id,
              dateOfJoin: vm.editedJobDetails.Date_Of_Join,
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveProbationDate &&
              !response.data.retrieveProbationDate.errorCode
            ) {
              const { probationDate } = response.data.retrieveProbationDate;
              vm.editedJobDetails.Probation_Date = new Date(probationDate);
            } else {
              vm.$store.dispatch("handleApiErrors", {
                error: "",
                action: "retrieving",
                form: "probation date",
                isListError: false,
              });
            }
          })
          .catch((err) => {
            this.$store.dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: "probation date",
              isListError: false,
            });
          });
      }
    },
    validateBenefitApplicable() {
      let vm = this;
      if (vm.editedJobDetails.EmpType_Id) {
        vm.$apollo
          .query({
            query: VALIDATE_BENEFIT_APPLICABLE_BASED_ON_EMP_TYPE,
            client: "apolloClientAC",
            variables: {
              employeeTypeId: parseInt(vm.editedJobDetails.EmpType_Id),
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateBenefitsApplicable &&
              !response.data.validateBenefitsApplicable.errorCode
            ) {
              const { benefitsApplicable } =
                response.data.validateBenefitsApplicable;
              vm.benefitsApplicable = benefitsApplicable;
            } else {
              this.$store.dispatch("handleApiErrors", {
                error: "",
                action: "validating",
                form: "benefits applicable",
                isListError: false,
              });
            }
          })
          .catch((err) => {
            this.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: "benefits applicable",
              isListError: false,
            });
          });
      }
    },

    callDesignationList(searchString) {
      if (searchString.length >= 3 && !this.editedJobDetails.Designation_Id) {
        this.getDesignationList(searchString);
      }
    },

    async getDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designations = designationResult;
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.dropdownDesignation = [];
        });
    },

    openRolesForm() {
      window.open(this.baseUrl + "v3/settings/core-hr/roles", "_blank");
    },

    openDesignationsForm() {
      window.open(this.baseUrl + "in/core-hr/designations", "_blank");
    },

    openDepartmentsForm() {
      window.open(this.baseUrl + "v3/core-hr/department-hierarchy", "_blank");
    },

    openLocationsForm() {
      window.open(this.baseUrl + "v3/core-hr/locations", "_blank");
    },

    openWorkScheduleForm() {
      window.open(this.baseUrl + "in/core-hr/work-schedule", "_blank");
    },

    openBusinessUnitForm() {
      window.open(this.baseUrl + "v3/core-hr/business-unit", "_blank");
    },

    openEmployeeTypeForm() {
      window.open(this.baseUrl + "v3/core-hr/employee-type", "_blank");
    },
    fetchJobRoles(designationId) {
      let vm = this;
      vm.dropdownListLoading = true;
      vm.isErrorInSeconLineManager = false;
      vm.$apollo
        .query({
          query: LIST_JOB_ROLES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
          variables: { formId: 178, designationId: parseInt(designationId) },
        })
        .then(({ data }) => {
          vm.dropdownListLoading = false;
          if (data && data.listJobRoles) {
            const response = data.listJobRoles;
            if (!response.errorCode) {
              vm.jobRolesList = JSON.parse(response.jobRoles);
              const jobRoleIdsSet = new Set(
                this.jobRolesList.map((job) => job.Job_Role_Id)
              );
              this.selectedJobRoles = (
                this.selectedCandidateDetails?.Job_Role_Details?.map(
                  (role) => role.Job_Role_Id
                ) || []
              ).filter((id) => jobRoleIdsSet.has(id));
            } else {
              vm.jobRolesList = [];
            }
          } else {
            vm.jobRolesList = [];
          }
        })
        .catch(() => {
          vm.dropdownListLoading = false;
          vm.jobRolesList = [];
        });
    },
  },
};
</script>
