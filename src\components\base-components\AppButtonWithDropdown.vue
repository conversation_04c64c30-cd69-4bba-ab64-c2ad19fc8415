<template>
  <div>
    <v-menu v-model="openPagesMenu" :disabled="isDisabled">
      <template v-slot:activator="{ props }">
        <v-btn
          :id="isOverrideButton ? 'override-button' : 'button-with-dropdown'"
          rounded="lg"
          :color="backgroundColor"
          min-width="100"
          v-bind="props"
          :size="windowWidth <= 1280 ? 'small' : 'default'"
          variant="elevated"
          :style="
            windowWidth <= 1280
              ? 'background: rgb(var(--v-theme-primary)); height: 40px; display: flex; color: white;'
              : ''
          "
        >
          <span v-if="itemLabel">{{ itemLabel }}: </span>
          {{ itemValue }}
          <v-icon v-if="!openPagesMenu" class="pl-1" size="18"
            >fas fa-caret-down</v-icon
          >
          <v-icon v-else class="pl-1" size="18">fas fa-caret-up</v-icon>
        </v-btn>
      </template>
      <v-list>
        <v-list-item
          v-for="item in listItems"
          :key="item"
          class="pages-list"
          @click="selectItem(item)"
        >
          <v-list-item-title class="pa-2">{{ item }}</v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "AppButtonWithDropdown",
  props: {
    listItems: {
      type: Array,
      default() {
        return [20, 50, 100, "All"]; // in default, this component is act as pagination count selector
      },
    },
    buttonValue: {
      type: [String, Number],
      default: 50,
    },
    backgroundColor: {
      type: String,
      default: "white",
    },
    isOverrideButton: {
      type: Boolean,
      default: false,
    },
    fromPagination: {
      type: Boolean,
      default: false,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    itemLabel: {
      type: String,
      default: "",
    },
  },
  data: () => ({
    openPagesMenu: false,
    itemValue: null,
  }),

  watch: {
    buttonValue(val) {
      this.itemValue = val;
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  mounted() {
    this.itemValue = this.buttonValue;
  },

  methods: {
    //  any of the list item is clicked
    selectItem(val) {
      // update button text immediately when this component was called for pagination
      if (this.fromPagination) {
        this.itemValue = val;
      }
      //  emit the selected value to its parent component
      this.$emit("selected-value", val);
    },
  },
};
</script>

<style scoped>
.pages-list:hover {
  background: #ffebeb;
}
.button-dropdown-list {
  max-height: 300px;
}
::v-deep #override-button > .v-btn__content {
  display: contents !important;
}
</style>
