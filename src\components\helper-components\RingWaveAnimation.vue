<template>
  <div
    class="mr-2"
    :class="
      isButton
        ? 'button-circle-animation'
        : isSmall
        ? 'number-small-circle-animation'
        : 'number-circle-animation'
    "
    :style="userStyle"
    @click="isButton ? $emit('on-button-click') : {}"
  >
    <div
      class="d-flex justify-center align-center"
      :class="{ 'mt-2': isButton }"
      style="height: 100%"
    >
      {{ displayText }}
    </div>
  </div>
</template>

<script>
export default {
  name: "RingWaveAnimation",

  props: {
    displayText: {
      type: [Number, String],
      required: true,
    },

    bgColorCodes: {
      type: Array,
      default() {
        return [
          "#0395f7",
          "rgba(93, 204, 231, 0.5)",
          "rgba(93, 204, 231, 0.7)",
        ];
      },
    },

    isButton: {
      type: Boolean,
      default: false,
    },

    isSmall: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    userStyle() {
      return {
        "--animate-primary-bg": this.bgColorCodes[0],
        "--animate-primary-shadow": this.bgColorCodes[1],
        "--animate-secondary-shadow": this.bgColorCodes[2],
      };
    },
  },
};
</script>

<style scoped>
.number-circle-animation {
  z-index: 1;
  text-align: center;
  font-size: 15px;
  width: 38px;
  height: 38px;
  border-radius: 100%;
  background: var(--animate-primary-bg);
  color: white;
  transition: all 0.2s;
  -webkit-animation: r5 1s 0s ease-out infinite;
  animation: r5 1s 0s ease-out infinite;
  font-weight: bold;
}

.number-small-circle-animation {
  z-index: 1;
  text-align: center;
  font-size: 14px;
  width: 33px;
  height: 33px;
  border-radius: 100%;
  background: var(--animate-primary-bg);
  color: white;
  transition: all 0.2s;
  -webkit-animation: r5 1s 0s ease-out infinite;
  animation: r5 1s 0s ease-out infinite;
  font-weight: bold;
  overflow: hidden;
}

.button-circle-animation {
  z-index: 1;
  text-align: center;
  font-size: 15px;
  min-width: 140px;
  height: 50px;
  border-radius: 30px;
  background: var(--animate-primary-bg);
  color: white;
  transition: all 0.2s;
  -webkit-animation: r5 1s 0s ease-out infinite;
  animation: r5 1s 0s ease-out infinite;
  cursor: pointer;
  font-weight: bold;
}

@-webkit-keyframes r5 {
  0% {
    box-shadow: 0 0 8px 6px var(--animate-primary-shadow),
      0 0 0px 0px transparent, 0 0 0px 0px var(--animate-secondary-shadow);
  }
  10% {
    transform: scale(1, 1);
    box-shadow: 0 0 8px 6px var(--animate-primary-shadow),
      0 0 12px 10px transparent, 0 0 12px 14px var(--animate-secondary-shadow);
  }
  100% {
    box-shadow: 0 0 8px 6px rgba(26, 140, 255, 0), 0 0 0px 40px transparent,
      0 0 0px 40px rgba(26, 140, 255, 0);
  }
}
@-moz-keyframes r5 {
  0% {
    box-shadow: 0 0 8px 6px var(--animate-primary-shadow),
      0 0 0px 0px transparent, 0 0 0px 0px var(--animate-secondary-shadow);
  }
  10% {
    box-shadow: 0 0 8px 6px var(--animate-primary-shadow),
      0 0 12px 10px transparent, 0 0 12px 14px var(--animate-secondary-shadow);
  }
  100% {
    box-shadow: 0 0 8px 6px rgba(26, 140, 255, 0), 0 0 0px 40px transparent,
      0 0 0px 40px rgba(26, 140, 255, 0);
  }
}
@keyframes r5 {
  0% {
    box-shadow: 0 0 8px 6px var(--animate-primary-shadow),
      0 0 0px 0px transparent, 0 0 0px 0px var(--animate-secondary-shadow);
  }
  10% {
    box-shadow: 0 0 8px 6px var(--animate-primary-shadow),
      0 0 12px 10px transparent, 0 0 12px 14px var(--animate-secondary-shadow);
  }
  100% {
    box-shadow: 0 0 8px 6px rgba(26, 140, 255, 0), 0 0 0px 40px transparent,
      0 0 0px 40px rgba(26, 140, 255, 0);
  }
}
</style>
