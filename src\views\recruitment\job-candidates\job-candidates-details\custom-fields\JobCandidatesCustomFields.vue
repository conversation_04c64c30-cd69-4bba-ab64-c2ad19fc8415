<template>
  <div class="my-4">
    <div v-if="isLoading">
      <div v-for="i in 5" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else-if="originalList?.length">
      <div v-if="showViewForm" class="d-flex justify-end mt-n2 mr-n2">
        <v-icon @click="fetchDynamicFieldValues()" size="15" color="grey"
          >fas fa-redo-alt</v-icon
        >
      </div>
      <div class="d-flex">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="primary"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Additional Details</span
          >
        </div>
      </div>

      <v-form
        ref="dynamicFieldValuesForm"
        class="py-5"
        v-if="showEditForm && !showViewForm"
      >
        <v-row>
          <v-col
            v-for="field in fieldsWithConditions"
            :key="field.Custom_Field_Id"
            cols="12"
            md="4"
            sm="6"
          >
            <!-- Single Choice / Multiple Choice -->
            <v-autocomplete
              v-if="
                field.Custom_Field_Type?.toLowerCase() === 'single choice' ||
                field.Custom_Field_Type?.toLowerCase() === 'multiple choice'
              "
              v-model="field.Value"
              :ref="`Custom Select` + field.Custom_Field_Id"
              :items="parseDropdownItems(field.Dropdown_Values)"
              :label="field.Custom_Field_Name"
              variant="solo"
              :isAutoComplete="true"
              :itemSelected="field.Value"
              :listWidth="100"
              :multiple="
                field.Custom_Field_Type?.toLowerCase() === 'multiple choice'
              "
              :chips="
                field.Custom_Field_Type?.toLowerCase() === 'multiple choice'
              "
              :closable-chips="
                field.Custom_Field_Type?.toLowerCase() === 'multiple choice'
              "
              clearable
              :rules="[
                field.Mandatory?.toLowerCase() === 'yes'
                  ? required(field.Custom_Field_Name, field.Value?.length)
                  : true,
              ]"
              @update:model-value="onChangeFields($event, field.Value, field)"
            >
              <template v-slot:label>
                <div class="d-flex">
                  <span class="text-truncate">{{
                    field.Custom_Field_Name
                  }}</span>
                  <span
                    v-if="field.Mandatory?.toLowerCase() === 'yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </div>
              </template>
              <template v-slot:item="{ item, props }">
                <div class="py-1 px-2" v-bind="props">
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <div
                        v-bind="props"
                        class="pa-3 rounded-lg text-truncate cursor-pointer"
                        :class="
                          isHovering
                            ? 'bg-hover'
                            : field.Value == item.value
                            ? 'bg-primary'
                            : 'bg-grey-lighten-4'
                        "
                        :style="listWidth"
                      >
                        <v-list-item>
                          {{ item.value }}
                        </v-list-item>
                      </div>
                    </template>
                  </v-hover>
                </div>
              </template></v-autocomplete
            >
            <!-- URL Link -->
            <v-btn
              v-else-if="field.Custom_Field_Type?.toLowerCase() === 'url'"
              @click="urlRedirection(field?.Url_Link)"
              variant="text"
              color="primary"
              class="text-subtitle-1 font-weight-regular text-decoration-underline ml-n4"
            >
              {{ checkNullValue(field?.Custom_Field_Name) }}
            </v-btn>
            <!-- Number Field -->
            <div
              v-else-if="field.Custom_Field_Type?.toLowerCase() === 'number'"
            >
              <v-text-field
                :ref="`number` + field.Custom_Field_Id"
                v-model="field.Value"
                :min="field.Min_Validation"
                :max="field.Max_Validation"
                :rules="[
                  field.Mandatory?.toLowerCase() === 'yes'
                    ? required(field.Custom_Field_Name, field.Value)
                    : true,
                  field.Value
                    ? minMaxNumberValidation(
                        field.Custom_Field_Name,
                        field.Value,
                        field.Min_Validation,
                        field.Max_Validation
                      )
                    : true,
                ]"
                @update:model-value="onChangeFields($event, field.Value, field)"
                variant="solo"
                type="number"
              >
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">{{
                      field.Custom_Field_Name
                    }}</span>
                    <span
                      v-if="field?.Mandatory?.toLowerCase() === 'yes'"
                      style="color: red"
                      class="ml-1"
                      >*</span
                    >
                  </div>
                </template>
              </v-text-field>
            </div>
            <!-- Text Area -->
            <v-textarea
              v-else-if="field.Custom_Field_Type?.toLowerCase() === 'text area'"
              rows="1"
              v-model="field.Value"
              :ref="`text area` + field.Custom_Field_Id"
              :rules="[
                field.Mandatory?.toLowerCase() === 'yes'
                  ? required(field.Custom_Field_Name, field.Value)
                  : true,
                field.Value?.length
                  ? minLengthValidation(
                      field.Custom_Field_Name,
                      field.Value,
                      field.Min_Validation
                    )
                  : true,
                field.Value?.length
                  ? maxLengthValidation(
                      field.Custom_Field_Name,
                      field.Value,
                      field.Max_Validation
                    )
                  : true,
                field.Value?.length
                  ? dynamicValidationRule(field.Value, field.Validation_Id)
                  : true,
              ]"
              @update:model-value="onChangeFields($event, field.Value, field)"
              clearable
              variant="solo"
              color="primary"
              ><template v-slot:label>
                <div class="d-flex">
                  <span class="text-truncate">{{
                    field.Custom_Field_Name
                  }}</span>
                  <span
                    v-if="field?.Mandatory?.toLowerCase() === 'yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </div>
              </template>
            </v-textarea>
            <!-- Date -->
            <v-menu
              v-else-if="field.Custom_Field_Type?.toLowerCase() === 'date'"
              :model-value="field.dateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  v-model="field.formattedDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[
                    field.Mandatory?.toLowerCase() === 'yes'
                      ? required(field.Custom_Field_Name, field.Value)
                      : true,
                  ]"
                  :ref="`date` + field.Custom_Field_Id"
                  readonly
                  v-bind="props"
                  variant="solo"
                  ><template v-slot:label>
                    <div class="d-flex">
                      <span class="text-truncate">{{
                        field.Custom_Field_Name
                      }}</span>
                      <span
                        v-if="field.Mandatory?.toLowerCase() === 'yes'"
                        style="color: red"
                        class="ml-1"
                        >*</span
                      >
                    </div>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-model="field.Value"
                @update:modelValue="onChangeFields($event, field.Value, field)"
              />
            </v-menu>
            <!-- Text Field -->
            <v-text-field
              v-else-if="
                field.Custom_Field_Type?.toLowerCase() === 'text field'
              "
              v-model="field.Value"
              :rules="[
                field.Mandatory?.toLowerCase() === 'yes'
                  ? required(field.Custom_Field_Name, field.Value)
                  : true,
                field.Value?.length
                  ? minLengthValidation(
                      field.Custom_Field_Name,
                      field.Value,
                      field.Min_Validation
                    )
                  : true,
                field.Value?.length
                  ? maxLengthValidation(
                      field.Custom_Field_Name,
                      field.Value,
                      field.Max_Validation
                    )
                  : true,
                field.Value?.length
                  ? dynamicValidationRule(field.Value, field.Validation_Id)
                  : true,
              ]"
              clearable
              :ref="`text field` + field.Custom_Field_Id"
              variant="solo"
              @update:model-value="onChangeFields($event, field.Value, field)"
              ><template v-slot:label>
                <div class="d-flex">
                  <span class="text-truncate">{{
                    field.Custom_Field_Name
                  }}</span>
                  <span
                    v-if="field.Mandatory?.toLowerCase() === 'yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </div>
              </template>
            </v-text-field>
          </v-col>
        </v-row>
      </v-form>
      <!-- View Form -->
      <v-row
        v-if="!showEditForm && showViewForm"
        class="pa-4 ma-2 card-blue-background"
      >
        <v-col
          v-for="field in fieldsWithConditions"
          :key="field?.Custom_Field_Id"
          cols="12"
          md="4"
          sm="6"
        >
          <v-btn
            v-if="field.Custom_Field_Type?.toLowerCase() === 'url'"
            @click="urlRedirection(field?.Url_Link)"
            variant="text"
            color="primary"
            class="text-subtitle-1 font-weight-regular text-decoration-underline ml-n4"
          >
            {{ checkNullValue(field?.Custom_Field_Name) }}
          </v-btn>
          <div v-else>
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ field?.Custom_Field_Name }}
            </p>
            <p
              v-if="field.Custom_Field_Type?.toLowerCase() === 'date'"
              class="text-subtitle-1 font-weight-regular"
            >
              {{ checkNullValue(formatDate(field?.Value)) }}
            </p>
            <p
              v-else-if="
                field.Custom_Field_Type?.toLowerCase() === 'multiple choice'
              "
              class="text-subtitle-1 font-weight-regular"
            >
              {{ checkNullValue(structDropdownValues(field?.Value)) }}
            </p>
            <p v-else class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(field?.Value) }}
            </p>
          </div>
        </v-col>
      </v-row>
    </div>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="mt-n5 primary"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { checkNullValue, listInputValidations } from "@/helper";
// import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { RETRIEVE_DYNAMIC_FIELD_VALUES } from "@/graphql/settings/general/customFieldsQuries.js";

export default {
  name: "JobCandidatesCustomFields",
  mixins: [validationRules],
  components: {
    // CustomSelect,
  },
  props: {
    customFormName: {
      type: [String, Array],
      required: true,
    },
    formId: {
      type: Number,
      required: true,
    },
    primaryId: {
      type: Number,
      required: true,
    },
    showViewForm: {
      type: Boolean,
      default: true,
    },
    showEditForm: {
      type: Boolean,
      default: false,
    },
    isUserLogedIn: {
      type: Boolean,
      default: false,
    },
    passPreviewValue: {
      type: String,
      default: "",
    },
  },
  emits: ["update-additional-details"],
  data() {
    return {
      // Custom Fields
      isFormDirty: false,
      isLoading: false,
      originalList: [],
      selectedItem: [],
      // Edit Form
      listValidations: [],
      openWarningModal: false,
      validationMessages: [],
      showValidationAlert: false,
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    fieldsWithConditions() {
      let originalList =
        this.selectedItem?.length > 0 ? this.selectedItem : this.originalList;

      originalList = originalList.filter((field) => {
        if (field.Visibility_Condition) {
          const visibilityCondition =
            typeof field.Visibility_Condition === "string"
              ? JSON.parse(field.Visibility_Condition)
              : field.Visibility_Condition;

          // Get the parent field value
          const parentField = originalList.find(
            (item) =>
              item.Custom_Field_Id === visibilityCondition?.Custom_Field_Id
          );
          const fieldValue = parentField?.Value;

          // If Type is "Any", show the field if parent field has any value
          if (visibilityCondition.Type?.toLowerCase() !== "specific") {
            // For different field types, check if they have a value
            if (
              parentField?.Custom_Field_Type?.toLowerCase() ===
              "multiple choice"
            ) {
              return Array.isArray(fieldValue) && fieldValue.length > 0;
            } else {
              return !!fieldValue;
            }
          }

          // Otherwise, check for specific value match (Type="Specific" or undefined for backward compatibility)
          if (field?.Custom_Field_Type?.toLowerCase() === "date") {
            const formattedFieldValue = this.formatDate(fieldValue);
            const formattedConditionValue = this.formatDate(
              visibilityCondition.Value
            );
            return formattedFieldValue === formattedConditionValue;
          }
          if (
            typeof visibilityCondition.Value === "string" &&
            visibilityCondition.Value.startsWith("[") &&
            visibilityCondition.Value.endsWith("]")
          ) {
            let valueArray = JSON.parse(visibilityCondition.Value);
            if (Array.isArray(fieldValue)) {
              return fieldValue.some((val) => valueArray.includes(val));
            } else {
              return valueArray.includes(fieldValue);
            }
          } else return fieldValue == visibilityCondition?.Value;
        }
        return true;
      });

      return originalList;
    },
  },
  mounted() {
    if (!this.passPreviewValue) this.fetchDynamicFieldValues();
    this.listValidations = this.listInputValidations();
  },
  watch: {
    selectedItem: {
      handler(val) {
        val.forEach((field) => {
          if (field.Custom_Field_Type?.toLowerCase() === "date") {
            field.dateMenu = false;
            field.formattedDate = this.formatDate(field.Value);
          }
        });
        if (this.showEditForm)
          this.$emit(
            "update-additional-details",
            { customFieldInputs: JSON.stringify(val), formName: "additional" },
            "additional"
          );
      },
      deep: true,
    },
    passPreviewValue: {
      handler(val) {
        if (val) {
          this.originalList = JSON.parse(val);
          this.formattedStructure();
        }
      },
      immediate: true,
    },
    isUserLogedIn: {
      handler(val) {
        if (val) this.fetchDynamicFieldValues();
      },
      immediate: true,
    },
    showEditForm(val) {
      if (!val) {
        this.selectedItem.length = 0;
      } else {
        this.formattedStructure();
      }
    },
  },
  methods: {
    checkNullValue,
    listInputValidations,
    parseDropdownItems(value) {
      return JSON.parse(value);
    },
    formattedStructure() {
      let tempStruct = JSON.parse(JSON.stringify(this.originalList));
      tempStruct = tempStruct.map((item) => {
        // Handle date type
        if (item.Custom_Field_Type?.toLowerCase() === "date") {
          item.dateMenu = false;
          item.formattedDate = item.Value ? this.formatDate(item.Value) : "";
          item.Value = item.Value ? new Date(item.Value) : null;
        }

        // Ensure visibility condition is properly parsed
        if (
          item.Visibility_Condition &&
          typeof item.Visibility_Condition === "string"
        ) {
          item.Visibility_Condition = JSON.parse(item.Visibility_Condition);
        }

        return item;
      });

      this.selectedItem = tempStruct;
    },

    onChangeFields(newValue, oldValue, field) {
      this.isFormDirty = true;

      // Get the changed field
      let changedField;

      // If we're dealing with a field object directly
      if (typeof field === "object" && field?.Custom_Field_Id) {
        changedField = field;

        // Update the field's value to ensure it's properly set
        // This is especially important for multiple choice fields
        changedField.Value = newValue;
      }
      // If we can't determine the field, try to find it by matching oldValue
      else {
        changedField = this.selectedItem.find(
          (item) => item.Value === oldValue
        );
        if (!changedField) return;
      }

      // Find all fields that have this field as their parent
      const childFields = this.selectedItem.filter((item) => {
        if (!item.Visibility_Condition) return false;

        const visibilityCondition =
          typeof item.Visibility_Condition === "string"
            ? JSON.parse(item.Visibility_Condition)
            : item.Visibility_Condition;

        return (
          visibilityCondition.Custom_Field_Id === changedField.Custom_Field_Id
        );
      });

      // For each child field, check if the condition is still met
      childFields.forEach((childField) => {
        const visibilityCondition =
          typeof childField.Visibility_Condition === "string"
            ? JSON.parse(childField.Visibility_Condition)
            : childField.Visibility_Condition;

        let conditionMet = false;

        // If Type is "Any", check if the parent field has any value
        if (visibilityCondition.Type?.toLowerCase() === "any") {
          if (
            changedField.Custom_Field_Type?.toLowerCase() === "multiple choice"
          ) {
            conditionMet = Array.isArray(newValue) && newValue.length > 0;
          } else {
            conditionMet = !!newValue;
          }
        }
        // Otherwise, check for specific value match (Type="Specific" or undefined for backward compatibility)
        else {
          // Check if the condition is still met based on field type
          if (changedField.Custom_Field_Type?.toLowerCase() === "date") {
            const formattedFieldValue = this.formatDate(newValue);
            const formattedConditionValue = this.formatDate(
              visibilityCondition.Value
            );
            conditionMet = formattedFieldValue === formattedConditionValue;
          }
          // Handle multiple choice parent field
          else if (
            changedField.Custom_Field_Type?.toLowerCase() === "multiple choice"
          ) {
            // If the parent field is multiple choice, we need to check if the selected values
            // include the condition value
            if (Array.isArray(newValue)) {
              if (
                typeof visibilityCondition.Value === "string" &&
                visibilityCondition.Value.startsWith("[") &&
                visibilityCondition.Value.endsWith("]")
              ) {
                // If condition value is an array
                let valueArray = JSON.parse(visibilityCondition.Value);
                conditionMet = newValue.some((val) => valueArray.includes(val));
              } else {
                // If condition value is a single value
                conditionMet = newValue.includes(visibilityCondition.Value);
              }
            } else {
              conditionMet = false; // If newValue is not an array but should be
            }
          }
          // Handle condition value as array
          else if (
            typeof visibilityCondition.Value === "string" &&
            visibilityCondition.Value.startsWith("[") &&
            visibilityCondition.Value.endsWith("]")
          ) {
            let valueArray = JSON.parse(visibilityCondition.Value);
            if (Array.isArray(newValue)) {
              conditionMet = newValue.some((val) => valueArray.includes(val));
            } else {
              conditionMet = valueArray.includes(newValue);
            }
          }
          // Simple equality check for other cases
          else {
            conditionMet = newValue == visibilityCondition.Value;
          }
        }

        // If condition is no longer met, reset the child field's value
        if (!conditionMet && childField.Value) {
          // Reset based on field type
          if (childField.Custom_Field_Type?.toLowerCase() === "date") {
            childField.Value = null;
            childField.formattedDate = "";
          } else if (
            childField.Custom_Field_Type?.toLowerCase() === "multiple choice"
          ) {
            childField.Value = [];
          } else {
            childField.Value = null;
          }
        }
      });
    },
    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
    },
    dynamicValidationRule(value, Validation_Id) {
      if (!value) return true;
      // Find the selected validation object
      const selectedValidation = this.listValidations.find(
        (validation) => validation.Validation_Id === Validation_Id
      );
      if (selectedValidation && selectedValidation.Regular_Expression) {
        const regex = new RegExp(selectedValidation.Regular_Expression, "u");
        return regex.test(value) ? true : `${selectedValidation.Description}`;
      }
      return true;
    },
    async validateCustomFieldsEditForm() {
      if (!this.selectedItem.length)
        return {
          Primary_Id: this.primaryId || 0,
          Form_Id: this.formId,
          Field_Value: "",
        };
      else {
        let isFormValid = await this.$refs.dynamicFieldValuesForm?.validate();
        if (isFormValid && isFormValid.valid) {
          return this.addUpdateFormStructure();
        } else {
          let arrayList = [];
          for (let i = 0; i < this.selectedItem.length; i++) {
            const item = this.selectedItem[i];
            if (
              item.Custom_Field_Type?.toLowerCase() === "single choice" ||
              item.Custom_Field_Type?.toLowerCase() === "multiple choice"
            ) {
              arrayList.push(`Custom Select${item.Custom_Field_Id}`);
            }
          }
          this.handleFocusFunction(arrayList);
        }
      }
    },
    addUpdateFormStructure() {
      let fieldValues = this.selectedItem.reduce((acc, field) => {
        let fieldValue = field.Value;

        // Handle the "Date" type
        if (field.Custom_Field_Type?.toLowerCase() === "date") {
          fieldValue = moment(fieldValue).isValid()
            ? moment(fieldValue).format("YYYY-MM-DD")
            : null;
        }

        acc[`${field.Custom_Field_Id}`] = fieldValue;
        return acc;
      }, {});
      fieldValues = JSON.stringify(fieldValues);
      let customFieldInputs = {
        Primary_Id: this.primaryId || 0,
        Form_Id: this.formId,
        Field_Value: fieldValues || "",
      };
      return customFieldInputs;
    },
    fetchDynamicFieldValues() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_DYNAMIC_FIELD_VALUES,
          client: "apolloClientAP",
          variables: {
            formId: parseInt(vm.formId),
            primaryId: parseInt(vm.primaryId),
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveDynamicFieldValues &&
            response.data.retrieveDynamicFieldValues.dynamicFieldValues &&
            !response.data.retrieveDynamicFieldValues.errorCode
          ) {
            const tempData = JSON.parse(
              response.data.retrieveDynamicFieldValues.dynamicFieldValues
            );
            vm.originalList = tempData;
            vm.formattedStructure();
          } else {
            vm.handleListError(
              response.data.retrieveDynamicFieldValues.errorCode
            );
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: this.customFormName,
        isListError: true,
      });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    urlRedirection(redirectionPath) {
      if (redirectionPath) {
        window.open(redirectionPath, "_blank");
      }
    },
    handleFocusFunction(arrayList) {
      const invalidFields = [];
      Object.keys(this.$refs).forEach((refName) => {
        const field = this.$refs[refName];
        if (field && field.rules) {
          let allTrue = field.rules.every((value) => value === true);
          if (field.rules.length > 0 && !allTrue) {
            invalidFields.push(refName);
          }
        }
      });
      // Log or handle the invalid fields
      if (invalidFields.length > 0) {
        const firstErrorField = invalidFields[0];
        this.$nextTick(() => {
          const fieldRef = this.$refs[firstErrorField];
          if (fieldRef) {
            let selectFields = arrayList;
            if (selectFields.includes(firstErrorField)) {
              fieldRef.onFocusCustomSelect
                ? fieldRef.onFocusCustomSelect()
                : fieldRef.focus();
            } else {
              // except for select
              fieldRef.focus();
            }
            if (fieldRef.$el) {
              const rect = fieldRef.$el.getBoundingClientRect();
              window.scrollTo({
                top: (window.scrollY + rect.top) * 0.4, // Adjust as needed
                behavior: "smooth",
              });
            }
          }
        });
      }
    },
    structDropdownValues(value) {
      if (Array.isArray(value) && value.length) {
        return value.join(", ");
      } else if (value) {
        return value;
      }
      return null;
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
