<template>
  <Handle type="target" :position="targetPosition"> </Handle>
  <Handle type="source" :position="sourcePosition" />
  <v-sheet class="user_node" v-if="data.type === 'parent'" style="">
    <div
      style="
        display: flex;
        align-items: center;
        position: relative;
        width: 100%;
      "
    >
      <div
        style="
          background-color: #bbcbce;
          height: 18px;
          width: 22px;
          display: flex;
          border-radius: 5px;
          justify-content: center;
          align-items: center;
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 448 512"
          height="8"
          width="8"
          fill="#000097"
        >
          <path
            d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"
          />
        </svg>
      </div>

      <div class="user_text">
        <!-- <v-text-field
          v-model="title"
          placeholder="User task"
          class="header_text px-1 py-0 workflow_input"
          label=""
          hide-details
          variant="underlined"
          single-line
          min-width="160"
          density="compact"
        >
        </v-text-field> -->
        <div
          class="header_text"
          style="max-width: 100px; white-space: nowrap; overflow: hidden"
        >
          {{ title }}
        </div>
        <div class="close_icon" @click="confirmationModel = true">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="#ffffff"
            height="7"
            width="7"
            viewBox="0 0 384 512"
          >
            <path
              d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            />
          </svg>
        </div>
      </div>
    </div>
    <v-divider class="my-1"></v-divider>
    <v-sheet class="d-flex align-center justify-end pb-1">
      <v-btn
        size="x-small"
        class="text-none"
        color="primary"
        text="Submit"
        variant="elevated"
        rounded="md"
        @click="() => handleChange()"
      >
        <h5>Change</h5>
      </v-btn>
    </v-sheet>
    <div class="" style="padding: 0px 5px 4px"></div>

    <div
      style="
        position: absolute;
        bottom: -10px;
        z-index: 99999;
        left: 50%;
        transform: translate(-50%);
      "
    >
      <div class="" style="position: relative">
        <div
          :class="
            'glow-button' +
            (!resendMenu ? ' glow-button-active' : ' dark-button')
          "
          v-click-outside="() => (resendMenu = false)"
          @click="() => (resendMenu = !resendMenu)"
        >
          <v-icon class="white" size="8">fas fa-plus</v-icon>
        </div>
        <v-expand-x-transition>
          <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
            <ResendMenuItems
              v-if="resendMenu"
              @handleProcessNode="handleToResendNode"
            ></ResendMenuItems>
          </div>
        </v-expand-x-transition>
      </div>
    </div>
  </v-sheet>
  <div :class="data.addNew ? 'user_node1' : ''" v-if="data.type === 'child'">
    <div
      style="
        position: absolute;
        bottom: -10px;
        z-index: 99999;
        left: 50%;
        transform: translate(-50%);
      "
    >
      <div class="" style="position: relative">
        <div
          :class="
            'glow-button' + (!showMenu ? ' glow-button-active' : ' dark-button')
          "
          v-click-outside="() => (showMenu = false)"
          @click="() => (showMenu = !showMenu)"
        >
          <v-icon class="white" size="8">fas fa-plus</v-icon>
        </div>
        <v-expand-x-transition>
          <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
            <MenuItems
              v-if="showMenu"
              @handleProcessNode="handleToStartNode"
            ></MenuItems>
          </div>
        </v-expand-x-transition>
      </div>
    </div>
    <!-- <div>
      <div
        style="
          background-color: #defffe;
          height: 20px;
          width: 20px;
          display: flex;
          border-radius: 5px;
          justify-content: center;
          align-items: center;
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 448 512"
          height="10"
          width="10"
          fill="#007472"
        >
          <path
            d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"
          />
        </svg>
      </div>
    </div> -->
  </div>
  <div>
    <v-dialog v-model="dialog" width="auto" class="d-flex task-model">
      <v-card max-width="auto" width="85%">
        <template v-slot:actions>
          <WorkflowForm
            :data="data"
            :selectedFormId="selectedFormId"
            :workflowModule="workflowModule"
            @onSubmit="onSubmitForm"
            @onClose="dialog = false"
            @onChangeTitle="(e) => onChangedTitle(e)"
          />
        </template>
      </v-card>
    </v-dialog>
  </div>
  <AppWarningModal
    v-if="confirmationModel"
    :open-modal="confirmationModel"
    iconName="fas fa-trash"
    confirmation-heading="Are you sure to delete?"
    @close-warning-modal="confirmationModel = false"
    @accept-modal="deleteNode()"
  >
  </AppWarningModal>
</template>
<script>
import { Position, Handle } from "@vue-flow/core";
import MenuItems from "../components/menus/MainMenu.vue";
import WorkflowForm from "../components/form-builder/WorkflowFormBuilder.vue";
import ResendMenuItems from "../components/menus/ResendMenu.vue";
export default {
  name: "AddUserTaskNode",
  emits: [
    "selectedUserNodes",
    "handleToStart",
    "deleteNode",
    "updateNodeTitle",
    "onSubmitFormData",
  ],
  props: {
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    sourcePosition: {
      type: String,
      required: true,
    },
    targetPosition: {
      type: String,
      required: true,
    },
    workflowModule: {
      type: String,
      required: true,
    },
    selectedFormId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      leftPosition: Position.Top,
      rightPosition: Position.Bottom,
      showMenu: false,
      addNewNode: false,
      resendMenu: false,
      dialog: false,
      confirmationModel: false,
      title: this.data?.formData?.modalTaskData?.title
        ? this.data.formData?.modalTaskData.title
        : "",
    };
  },
  methods: {
    handleToStartNode(type) {
      this.$emit("handleToStart", type, this.data, true, 0);
    },
    handleToResendNode(type) {
      this.$emit("handleToStart", type, this.data, false, 0);
    },
    onChangedTitle(nodeTitle) {
      this.title = nodeTitle;
      this.$emit("updateNodeTitle", {
        title: nodeTitle,
        nodeId: this.data.id,
      });
    },
    deleteNode() {
      this.confirmationModel = false;
      this.$emit("deleteNode", this.data);
    },
    onSubmitForm(formData) {
      this.$emit("onSubmitFormData", formData);
      this.dialog = false;
    },
    handleChange() {
      this.$emit("selectedUserNodes", this.data.id);
      this.dialog = true;
    },
  },

  components: {
    MenuItems,
    Handle,
    ResendMenuItems,
    WorkflowForm,
  },
};
</script>
<style>
.user_node1 {
  border-radius: 6px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.user_node {
  background-color: #ffffff;
  border-radius: 6px;
  min-width: 120px;
  max-width: 150px;
  padding: 5px;
  align-items: center;
  /* box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); */
}

.user_node::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background: linear-gradient(to right, #000097, #002b33);
  height: 4px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.user_text {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.user_node:hover .close_icon {
  visibility: visible;
}

.task-model .v-overlay__content {
  justify-content: center !important;
  align-items: center !important;
}
</style>
