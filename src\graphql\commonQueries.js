import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_EMPLOYEES_LIST = gql`
  query getEmployeesDetailsBasedOnRole(
    $formName: String!
    $formId: Int
    $serviceProviderId: Int
    $customGroupId: Int
    $flag: String
    $isSalaryEdit: Boolean
  ) {
    getEmployeesDetailsBasedOnRole(
      formName: $formName
      formId: $formId
      serviceProviderId: $serviceProviderId
      customGroupId: $customGroupId
      flag: $flag
      isSalaryEdit: $isSalaryEdit
    ) {
      errorCode
      message
      employeeDetails {
        employeeId
        userDefinedEmpId
        employeeName
        designationId
        designationName
        departmentId
        Emp_Email
        departmentName
        locationId
        locationName
        dateOfJoin
        empStatus
        empTypeId
        managerId
        probationDate
        workSchedule
        resignationDate
        isRecruiter
        Service_Provider_Id
        Service_Provider_Name
        Enable_Work_Place
        Last_SalaryDate
        eligibleForPf
        eligibleForPension
        eligibleForESI
        eligibleForInsurance
        eligibleForNps
        eligibleForGratuity
        minimumWage
      }
    }
  }
`;
export const GET_ALL_EMPLOYEES = gql`
  query listAllEmployeeDetails(
    $designationId: [Int]
    $departmentId: [Int]
    $empTypeId: [Int]
    $workScheduleId: [Int]
    $locationId: [Int]
    $formName: String
    $onlyActiveEmployees: Int
  ) {
    listAllEmployeeDetails(
      designationId: $designationId
      departmentId: $departmentId
      empTypeId: $empTypeId
      workScheduleId: $workScheduleId
      locationId: $locationId
      formName: $formName
      onlyActiveEmployees: $onlyActiveEmployees
    ) {
      errorCode
      message
      employeeList {
        employee_id
        emp_status
        user_defined_empid
        employee_name
        emp_email
        designation_name
        department_name
        Designation_Id
        Department_Id
        Location_Id
        EmpType_Id
        Work_Schedule
        location_name
        employee_type
        work_schedule_name
        Business_Unit
        Service_Provider_Name
      }
    }
  }
`;

export const RETRIEVE_FORM_LEVEL_COVERAGE = gql`
  query ($formName: String!, $Form_Id: Int!) {
    retrieveFormLevelCoverage(formName: $formName, Form_Id: $Form_Id) {
      errorCode
      message
      formLevelCoverage {
        Coverage_Id
        Form_Id
        Coverage
        Updated_By
        Updated_On
      }
    }
  }
`;

export const GET_EMPLOYEES_BASED_ON_CUSTOM_GROUP = gql`
  query getEmployeeDetailsBasedOnGroup($customGroupId: [Int]!) {
    getEmployeeDetailsBasedOnGroup(customGroupId: $customGroupId) {
      errorCode
      message
      employeeDetails {
        employeeId
        userDefinedEmpId
        employeeName
        designationName
        departmentName
        groupId
        groupName
      }
    }
  }
`;
export const CUSTOM_COLOR_PICKER = gql`
  query customColorPicker {
    customColorPicker {
      errorCode
      message
      colorResult {
        Primary_Color
        Secondary_Color
        Hover_Color
        Table_Header_Color
        Table_Header_Text_Color
        Career_Page_Caption
        Org_Name
        Date_Format
        Field_Force
      }
    }
  }
`;
export const RETRIEVE_DROPDOWN_DETAILS = gql`
  query retrieveDropdownDetails(
    $formId: Int
    $key: [String]!
    $conditionDetails: [conditionDetails]
  ) {
    retrieveDropdownDetails(
      formId: $formId
      key: $key
      conditionDetails: $conditionDetails
    ) {
      errorCode
      message
      dropdownDetails
    }
  }
`;
export const RETRIEVE_PAYROLL_GENERAL_SETTINGS = gql`
  query listPayrollGeneralSettings {
    listPayrollGeneralSettings {
      errorCode
      message
      listPayrollGeneralSettingsData {
        Settings_Id
        Slab_Wise_PF
        Slab_Wise_NPS
        Country_Name
        Country_Code
        Payroll_Currency
      }
    }
  }
`;

export const GET_FORM_FIELDS_BY_FORM_ID = gql`
  query getFormFieldsByFormId($form_Id: Int!) {
    getFormFieldsByFormId(form_Id: $form_Id) {
      errorCode
      message
      formFields {
        Field_Id
        Field_Name
        Field_Alias
        Field_Visiblity
        Mandatory_Field
        Predefined
      }
    }
  }
`;

export const GET_FORM_FIELDS_NO_AUTH = gql`
  query getFormFeildsByFormIdAndTab($form_Id: Int!) {
    getFormFeildsByFormIdAndTab(form_Id: $form_Id) {
      errorCode
      message
      formFields {
        Field_Id
        Form_Id
        Field_Name
        Field_Alias
        Field_Visiblity
        Mandatory_Field
        Tab_Name
        Predefined
      }
    }
  }
`;
// ===============
// Mutation
// ===============
export const ADD_ERROR_LOG = gql`
  mutation addErrorLog($errorMessage: String!, $url: String!) {
    addErrorLog(errorMessage: $errorMessage, url: $url) {
      message
    }
  }
`;
export const GET_PRESIGNED_URL = gql`
  mutation (
    $fileName: String!
    $action: String!
    $type: String
    $destinationBucket: String
    $destinationFileKey: String
    $data: String
  ) {
    getPresignedUrl(
      fileName: $fileName
      action: $action
      type: $type
      destinationBucket: $destinationBucket
      destinationFileKey: $destinationFileKey
      data: $data
    ) {
      errorCode
      message
      presignedUrl
      s3DocumentDetails
    }
  }
`;

export const DELETE_PRESIGNED_URL = gql`
  mutation ($fileName: String!, $type: String) {
    deleteS3Files(fileName: $fileName, type: $type) {
      errorCode
      message
    }
  }
`;

export const UPDATE_FORM_LEVEL_COVERAGE = gql`
  mutation updateFormLevelCoverage(
    $Coverage: String!
    $Coverage_Id: Int!
    $formName: String!
  ) {
    updateFormLevelCoverage(
      Coverage: $Coverage
      Coverage_Id: $Coverage_Id
      formName: $formName
    ) {
      errorCode
      message
    }
  }
`;
export const CREATE_WORKFLOW_MODULE = gql`
  mutation MyMutation(
    $workflowName: String!
    $eventId: String!
    $workflowUniqueId: String
    $workflowModuleId: Int!
    $description: String!
    $isDefault: Int!
    $workflowImage: String
    $addedBy: Int!
    $designationIds: [Int]
  ) {
    insertWorkflow(
      workflowName: $workflowName
      eventId: $eventId
      workflowUniqueId: $workflowUniqueId
      workflowModuleId: $workflowModuleId
      description: $description
      isDefault: $isDefault
      workflowImage: $workflowImage
      addedBy: $addedBy
      designationIds: $designationIds
    ) {
      errorCode
      message
      validationError
      data
    }
  }
`;
export const GET_WORKFLOW_MODULE = gql`
  {
    getWorkflowModules {
      errorCode
      message
      Modules {
        Workflow_Module_Id
        Form_Id
        Form_Name
        Module_Id
        Module_Name
        Workflow_Schema
        Workflow_End_Point_Key
      }
    }
  }
`;
export const GET_GROUP_APPROVE = gql`
  query ($formId: Int!) {
    getCustomGroups(formId: $formId) {
      errorCode
      message
      groupData {
        Group_Id
        Group_Name
      }
    }
  }
`;

export const GET_FORM_BUILDER = gql`
  query getAllMinimalDynamicFormTemplat {
    getAllMinimalDynamicFormTemplate {
      error {
        code
        message
      }
      result {
        templateId
        templateName
      }
    }
  }
`;

export const GET_EMAIL_NOTIFICATION_TEMPLATE = gql`
  query ($type: String!, $formIds: [Int]) {
    getNotificationTemplates(type: $type, formIds: $formIds) {
      errorCode
      message
      notificationTemplates {
        Template_Id
        TemplateName
      }
    }
  }
`;

export const DELETE_WORKFLOW_DETAILS = gql`
  mutation MyMutation($workflowId: Int!, $employeeId: Int!) {
    deleteWorkflow(workflowId: $workflowId, employeeId: $employeeId) {
      errorCode
      message
      validationError
    }
  }
`;
export const EDIT_WORKFLOW_MODULE = gql`
  mutation MyMutation(
    $workflowId: Int!
    $workflowName: String!
    $eventId: String!
    $workflowUniqueId: String
    $workflowModuleId: Int!
    $description: String!
    $isDefault: Int!
    $workflowImage: String
    $updatedBy: Int!
    $designationIds: [Int]
  ) {
    updateWorkflow(
      workflowId: $workflowId
      workflowName: $workflowName
      eventId: $eventId
      workflowUniqueId: $workflowUniqueId
      workflowModuleId: $workflowModuleId
      description: $description
      isDefault: $isDefault
      workflowImage: $workflowImage
      designationIds: $designationIds
      updatedBy: $updatedBy
    ) {
      errorCode
      message
      validationError
    }
  }
`;
export const CREATE_WORKFLOW = gql`
  mutation MyMutation(
    $workflowName: String!
    $eventId: String!
    $workflowUniqueId: String
    $workflowModuleId: Int!
    $description: String!
    $isDefault: Int!
    $workflowImage: String
    $addedBy: Int!
    $designationIds: [Int]
  ) {
    insertWorkflow(
      workflowName: $workflowName
      eventId: $eventId
      workflowUniqueId: $workflowUniqueId
      workflowModuleId: $workflowModuleId
      description: $description
      isDefault: $isDefault
      workflowImage: $workflowImage
      addedBy: $addedBy
      designationIds: $designationIds
    ) {
      errorCode
      message
      validationError
      data
    }
  }
`;
export const GET_DESIGNATION_LIST = gql`
  query getAllDesignationBasedOnWorkflowModule($moduleId: Int) {
    getAllDesignationBasedOnWorkflowModule(moduleId: $moduleId) {
      errorCode
      message
      designations {
        Designation_Id
        Designation_Name
      }
    }
  }
`;
export const RETRIEVE_LOGO_PATH = gql`
  query retrieveLogoPath {
    retrieveLogoPath {
      errorCode
      message
      logoPath
    }
  }
`;
export const RETRIEVE_PERSONAL_INFO = gql`
  query retrievePersonalInfoDropdown {
    retrievePersonalInfoDropdown {
      errorCode
      message
      genderList
      maritalList
      languageList
      taxCodeList
      religionList
      nationalityList
    }
  }
`;

export const RETRIEVE_CONTACT_INFO = gql`
  query retrieveContactInfoDropdown {
    retrieveContactInfoDropdown {
      errorCode
      message
      cityList
      stateList
      countryList
    }
  }
`;

export const TRIGGER_CONTROLLER_FUNCTION = gql`
  mutation triggerControllerFunction($encryptedData: String!, $iv: String!) {
    triggerControllerFunction(encryptedData: $encryptedData, iv: $iv) {
      response
    }
  }
`;
