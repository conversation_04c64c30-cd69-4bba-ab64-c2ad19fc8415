<template>
  <div>
    <v-card class="rounded-lg" min-height="300">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? $t("settings.edit") : $t("settings.new") }}
          </v-chip>
          <span class="pt-1 font-weight-bold"> {{ landedFormName }}</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              variant="elevated"
              class="mb-2 primary"
              @click="validateWorkLocationForm"
              >{{ $t("settings.save") }}</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >{{ $t("settings.save") }}</v-btn
                >
              </template>
              <div>{{ $t("settings.noChangesToUpdate") }}</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" class="mr-1"> fas fa-times </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="height: calc(100vh - 460px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="workLocationForm">
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-text-field
                  v-model="workLocation"
                  variant="solo"
                  :rules="[
                    required($t('settings.workLocation'), workLocation),
                    multilingualNameNumericValidation(
                      $t('settings.workLocation'),
                      workLocation
                    ),
                    minLengthValidation(
                      $t('settings.workLocation'),
                      workLocation,
                      3
                    ),
                    maxLengthValidation(
                      $t('settings.workLocation'),
                      workLocation,
                      100
                    ),
                  ]"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    <span>{{ $t("settings.workLocation") }}</span>
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-row>
                  <v-col cols="8" class="pr-0">
                    <v-text-field
                      v-model="ipAddress"
                      variant="solo"
                      :rules="[
                        required($t('settings.cidr'), ipAddress),
                        cidrValidation($t('settings.cidr'), ipAddress + code),
                      ]"
                      @update:model-value="isFormDirty = true"
                    >
                      <template v-slot:label>
                        <span>{{ $t("settings.cidr") }}</span>
                        <span style="color: red">*</span>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="4" class="pl-0">
                    <v-select
                      variant="solo"
                      :items="items"
                      v-model="code"
                      @update:model-value="isFormDirty = true"
                    ></v-select>
                  </v-col>
                </v-row>
              </v-col>

              <v-col cols="12" sm="4" md="4" class="px-md-6 pb-0 mb-2">
                <div class="v-label mr-4 mb-1">{{ $t("settings.status") }}</div>
                <AppToggleButton
                  :button-active-text="$t('settings.active')"
                  :button-inactive-text="$t('settings.inactive')"
                  button-active-color="#7de272"
                  button-inactive-color="red"
                  id-value="gab-analysis-based-on"
                  :current-value="status === 'Active' ? true : false"
                  @chosen-value="onChangeStatus($event)"
                ></AppToggleButton>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
      <div>
        <v-row class="px-sm-8 px-md-10 mt-2 mb-2">
          <v-col v-if="moreDetailsList.length > 0" cols="12">
            <MoreDetails
              :more-details-list="moreDetailsList"
              :open-close-card="openMoreDetails"
              @on-open-close="openMoreDetails = $event"
            ></MoreDetails> </v-col
        ></v-row>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      :confirmation-heading="$t('settings.confirmExitForm')"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            {{ $t("settings.close") }}
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { ADD_UPDATE_WORK_LOCATION } from "@/graphql/settings/data-loss-prevention/locationTrackingSettingQueries";
import moment from "moment";
export default {
  name: "AddEditLocationTracking",
  components: {
    MoreDetails,
  },
  mixins: [validationRules],

  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      // add/update
      workLocation: "",
      ipAddress: "",
      status: "Active",
      code: "/32",
      isFormDirty: false,
      moreDetailsList: [],
      openMoreDetails: true,
      // loading/error/other
      isLoading: false,
      openConfirmationPopup: false,
      validationMessages: [],
      showValidationAlert: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    items() {
      let items = Array.from({ length: 32 }, (_, i) => 32 - i);
      const newItems = items.map((num) => `/${num}`);
      return newItems;
    },
  },
  mounted() {
    if (this.editFormData) {
      this.workLocation = this.editFormData.workLocationName;
      if (this.editFormData.ipRange) {
        this.ipAddress = this.editFormData.ipRange.split("/")[0];
        this.code = "/" + this.editFormData.ipRange.split("/")[1];
      }
      this.status = this.editFormData.status;
    }
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);
        this.prefillMoreDetails();
      },
    },
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },

    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },

    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },

    // change the mode of performance management
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },

    async validateWorkLocationForm() {
      const { valid } = await this.$refs.workLocationForm.validate();
      if (valid) {
        this.addUpdateWorkLocation();
      }
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.editFormData?.addedOn + ".000Z")
        ),
        addedByName = this.editFormData?.addedBy,
        updatedByName = this.editFormData?.updatedByName,
        updatedOn = this.formatDate(
          new Date(this.editFormData?.updatedOn + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: this.$t("settings.added"),
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: this.$t("settings.updated"),
        });
      }
    },
    addUpdateWorkLocation() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_WORK_LOCATION,
          variables: {
            officeIpAddressId: vm.isEdit
              ? vm.editFormData.officeIpAddressId
              : 0,
            workLocationName: vm.workLocation,
            ipRange: vm.ipAddress + vm.code,
            status: vm.status,
          },
          client: "apolloClientR",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.isEdit
              ? this.$t("settings.workLocationAddedSuccess")
              : this.$t("settings.workLocationUpdatedSuccess"),
          };
          vm.showAlert(snackbarData);
          vm.$emit("edit-updated");
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.landedFormName.toLowerCase(),
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
