import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_BUSINESS_UNIT_SETTINGS = gql`
  query listBusinessUnit {
    listBusinessUnit {
      errorCode
      message
      settings {
        level
        businessUnitId
        businessUnit
        status
        description
        addedOn
        updatedOn
        addedByName
        updatedByName
        businessUnitCode
        businessUnitParentId
        businessUnitParentName
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_BUSINESS_UNIT_SETTINGS = gql`
  mutation addUpdateBusinessUnit(
    $businessUnitId: Int!
    $businessUnit: String!
    $status: String!
    $description: String!
    $oldStatus: String!
    $isAdd: Int!
    $businessUnitCode: String
    $businessUnitParentId: Int
    $level: Int
  ) {
    addUpdateBusinessUnit(
      businessUnitId: $businessUnitId
      businessUnit: $businessUnit
      status: $status
      description: $description
      oldStatus: $oldStatus
      isAdd: $isAdd
      businessUnitCode: $businessUnitCode
      businessUnitParentId: $businessUnitParentId
      level: $level
    ) {
      errorCode
      message
    }
  }
`;
