<template>
  <div class="my-4">
    <div class="d-flex">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="green"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >License Details</span
        >
      </div>

      <span
        v-if="formType === 'add' && enableEdit && !isApprovalView"
        class="d-flex justify-end ml-auto"
      >
        <v-btn color="primary" variant="text" @click="openEditDialog">
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
        >
      </span>
      <div
        v-if="
          formType === 'edit' && !showEditForm && enableEdit && !isApprovalView
        "
        class="d-flex justify-end ml-auto"
      >
        <v-btn @click="openEditDialog" color="primary" variant="text">
          <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
        </v-btn>
      </div>
    </div>
    <div
      v-if="
        !showEditForm && licenseDetailsData && licenseDetailsData.length === 0
      "
      class="d-flex align-center mt-2 ml-4 text-h6 text-grey"
    >
      No license details have been added
    </div>
    <v-form ref="drivingLicenseDetailsForm" v-if="showEditForm">
      <v-row>
        <v-col
          v-if="labelList[116].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            v-model="editedLicenseDetails.Driving_License_No"
            :rules="[
              required(
                `${labelList[116].Field_Alias}`,
                editedLicenseDetails.Driving_License_No
              ),
              editedLicenseDetails.Driving_License_No
                ? validateWithRulesAndReturnMessages(
                    editedLicenseDetails.Driving_License_No,
                    'drivingLicenseNo',
                    `${labelList[116].Field_Alias}`
                  )
                : true,
            ]"
            variant="solo"
            @update:model-value="onChangeFields()"
          >
            <template v-slot:label>
              <span>{{ labelList[116].Field_Alias }}</span>
              <span
                v-if="labelList[116].Mandatory_Field == 'Yes'"
                class="ml-1"
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>
        <v-col
          v-if="labelList[117].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-menu
            v-model="issueDateMenu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                ref="Issue Date"
                v-model="formattedIssueDate"
                prepend-inner-icon="fas fa-calendar"
                :rules="[
                  required(labelList['117'].Field_Alias, formattedIssueDate),
                ]"
                readonly
                v-bind="props"
                variant="solo"
              >
                <template v-slot:label>
                  {{ labelList["117"].Field_Alias }}
                  <span
                    v-if="labelList['117'].Mandatory_Field === 'Yes'"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </template>
            <v-date-picker
              v-model="editedLicenseDetails.License_Issue_Date"
              :min="selectedEmpDobDate"
              :max="currentDate"
              @update:modelValue="onChangeFields()"
            />
          </v-menu>
        </v-col>
        <v-col
          v-if="labelList[118].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-menu
            v-model="expiryDateMenu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                ref="Expiry Date"
                v-model="formattedExpiryDate"
                prepend-inner-icon="fas fa-calendar"
                :rules="[
                  required(labelList['118'].Field_Alias, formattedExpiryDate),
                ]"
                readonly
                v-bind="props"
                :disabled="!editedLicenseDetails.License_Issue_Date"
                variant="solo"
              >
                <template v-slot:label>
                  {{ labelList["118"].Field_Alias }}
                  <span
                    v-if="labelList['118'].Mandatory_Field === 'Yes'"
                    style="color: red"
                    >*</span
                  ><span style="color: red">*</span>
                </template></v-text-field
              >
            </template>
            <v-date-picker
              v-model="editedLicenseDetails.License_Expiry_Date"
              :min="licenseExpiryMinDate"
              @update:modelValue="onChangeFields()"
            />
          </v-menu>
        </v-col>
        <v-col
          v-if="labelList[119].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            v-model="editedLicenseDetails.Issuing_Authority"
            variant="solo"
            :rules="[
              required(
                `${labelList[119].Field_Alias}`,
                editedLicenseDetails.Issuing_Authority
              ),
              editedLicenseDetails.Issuing_Authority
                ? validateWithRulesAndReturnMessages(
                    editedLicenseDetails.Issuing_Authority,
                    'issuingAuthority',
                    `${labelList[119].Field_Alias}`
                  )
                : true,
            ]"
            @update:model-value="onChangeFields()"
          >
            <template v-slot:label>
              <span>{{ labelList[119].Field_Alias }}</span>
              <span
                v-if="labelList[119].Mandatory_Field == 'Yes'"
                class="ml-1"
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <v-file-input
            prepend-icon=""
            :model-value="fileContent"
            append-inner-icon="fas fa-paperclip"
            variant="solo"
            label="Document"
            accept="image/png, image/jpeg, image/jpg, application/pdf"
            :rules="[required('Document', fileContentRuleValue)]"
            @update:modelValue="onChangeFiles"
            @click:clear="removeFiles"
          >
          </v-file-input>
        </v-col>
        <v-col
          v-if="labelList[120].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <CustomSelect
            :items="countryList"
            :label="labelList[120].Field_Alias"
            :isAutoComplete="true"
            :isLoading="countryListLoading"
            :noDataText="
              countryListLoading ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedLicenseDetails.Issuing_Country"
            itemValue="Country_Code"
            itemTitle="Country_Name"
            :isRequired="labelList[120].Mandatory_Field == 'Yes'"
            :rules="[
              required(
                `${labelList[120].Field_Alias}`,
                editedLicenseDetails.Issuing_Country
              ),
            ]"
            @selected-item="
              onChangeCustomSelectField($event, 'Issuing_Country')
            "
          ></CustomSelect>
        </v-col>
        <v-col
          v-if="labelList[122].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            v-model="editedLicenseDetails.Issuing_State"
            variant="solo"
            :rules="[
              required(
                `${labelList[122].Field_Alias}`,
                editedLicenseDetails.Issuing_State
              ),
              editedLicenseDetails.Issuing_State
                ? validateWithRulesAndReturnMessages(
                    editedLicenseDetails.Issuing_State,
                    'issuingState',
                    `${labelList[122].Field_Alias}`
                  )
                : true,
            ]"
            @update:model-value="onChangeFields()"
          >
            <template v-slot:label>
              <span>{{ labelList[122].Field_Alias }}</span>
              <span
                v-if="labelList[122].Mandatory_Field == 'Yes'"
                class="ml-1"
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>

        <v-col
          v-if="labelList[121].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            v-model="editedLicenseDetails.Vehicle_Type"
            variant="solo"
            :rules="[
              required(
                `${labelList[121].Field_Alias}`,
                editedLicenseDetails.Vehicle_Type
              ),
              editedLicenseDetails.Vehicle_Type
                ? validateWithRulesAndReturnMessages(
                    editedLicenseDetails.Vehicle_Type,
                    'vehicleType',
                    `${labelList[121].Field_Alias}`,
                    true
                  )
                : true,
            ]"
            @update:model-value="onChangeFields()"
          >
            <template v-slot:label>
              <span>{{ labelList[121].Field_Alias }}</span>
              <span
                v-if="labelList[121].Mandatory_Field == 'Yes'"
                class="ml-1"
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>
      </v-row>
    </v-form>

    <v-row
      v-if="!showEditForm && formType === 'edit'"
      class="pa-4 ma-2 card-blue-background"
    >
      <FieldDiff
        v-if="labelList[116]?.Field_Visiblity?.toLowerCase() == 'yes'"
        :oldDataAvailable="oldDrivingLicenseDetailsData ? true : false"
        :label="labelList[116].Field_Alias || 'Driving License Number'"
        :newValue="drivingLicenseDetails.Driving_License_No"
        :oldValue="oldDrivingLicenseDetailsData?.Driving_License_No"
      />
      <FieldDiff
        v-if="labelList[117]?.Field_Visiblity?.toLowerCase() == 'yes'"
        :oldDataAvailable="oldDrivingLicenseDetailsData ? true : false"
        :label="labelList[117].Field_Alias || 'Issue Date'"
        :newValue="formatDate(drivingLicenseDetails?.License_Issue_Date)"
        :oldValue="
          oldDrivingLicenseDetailsData
            ? formatDate(oldDrivingLicenseDetailsData.License_Issue_Date)
            : null
        "
      />
      <FieldDiff
        v-if="labelList[118]?.Field_Visiblity?.toLowerCase() == 'yes'"
        :oldDataAvailable="oldDrivingLicenseDetailsData ? true : false"
        :label="labelList[118].Field_Alias || 'Expiry Date'"
        :newValue="formatDate(drivingLicenseDetails.License_Expiry_Date)"
        :oldValue="
          oldDrivingLicenseDetailsData
            ? formatDate(oldDrivingLicenseDetailsData.License_Expiry_Date)
            : null
        "
      />
      <FieldDiff
        v-if="labelList[119]?.Field_Visiblity?.toLowerCase() == 'yes'"
        :oldDataAvailable="oldDrivingLicenseDetailsData ? true : false"
        :label="labelList[119].Field_Alias || 'Issuing Authority'"
        :newValue="drivingLicenseDetails.Issuing_Authority"
        :oldValue="
          oldDrivingLicenseDetailsData
            ? oldDrivingLicenseDetailsData.Issuing_Authority
            : null
        "
      />
      <FieldDiff
        v-if="labelList[120]?.Field_Visiblity?.toLowerCase() == 'yes'"
        :oldDataAvailable="oldDrivingLicenseDetailsData ? true : false"
        :label="labelList[120].Field_Alias || 'Issuing Country'"
        :newValue="drivingLicenseDetails?.Country_Name"
        :oldValue="
          oldDrivingLicenseDetailsData
            ? oldDrivingLicenseDetailsData.Country_Name
            : null
        "
      />
      <FieldDiff
        v-if="labelList[122]?.Field_Visiblity?.toLowerCase() == 'yes'"
        :oldDataAvailable="oldDrivingLicenseDetailsData ? true : false"
        :label="labelList[122].Field_Alias || 'Issuing State'"
        :newValue="drivingLicenseDetails.Issuing_State"
        :oldValue="
          oldDrivingLicenseDetailsData
            ? oldDrivingLicenseDetailsData.Issuing_State
            : null
        "
      />
      <FieldDiff
        v-if="labelList[121]?.Field_Visiblity?.toLowerCase() == 'yes'"
        :oldDataAvailable="oldDrivingLicenseDetailsData ? true : false"
        :label="labelList[121].Field_Alias || 'Vehicle Type'"
        :newValue="drivingLicenseDetails.Vehicle_Type"
        :oldValue="
          oldDrivingLicenseDetailsData
            ? oldDrivingLicenseDetailsData.Vehicle_Type
            : null
        "
      />
      <v-col
        v-if="
          (drivingLicenseDetails && drivingLicenseDetails.File_Name) ||
          (oldDrivingLicenseDetailsData &&
            oldDrivingLicenseDetailsData.File_Name)
        "
        cols="12"
        md="4"
        sm="6"
      >
        <div class="d-flex flex-column">
          <span
            v-if="hasFileChanged && oldDrivingLicenseDetailsData?.File_Name"
            class="text-subtitle-1 font-weight-regular text-decoration-line-through text-error mr-1"
          >
            <span
              style="text-decoration: underline; cursor: pointer"
              @click="retrieveDocuments(oldDrivingLicenseDetailsData.File_Name)"
              class="text-error"
            >
              Previous Document
            </span>
          </span>
          <span
            v-if="drivingLicenseDetails?.File_Name"
            class="text-subtitle-1 font-weight-regular text-success"
          >
            <span
              style="text-decoration: underline; cursor: pointer"
              @click="retrieveDocuments(drivingLicenseDetails.File_Name)"
            >
              View Document
            </span>
          </span>
        </div>
      </v-col>
    </v-row>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            >Cancel</v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1"
            color="primary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            Save
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employees Document Upload"
    fileRetrieveType="documents"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import { defineAsyncComponent } from "vue";
import moment from "moment";
import { checkNullValue } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { ADD_UPDATE_LICENSE_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
const FieldDiff = defineAsyncComponent(() =>
  import("@/components/custom-components/FieldDiff.vue")
);
import Config from "@/config.js";

export default {
  name: "DrivingLicenseDetails",
  mixins: [validationRules],
  props: {
    licenseDetailsData: {
      type: [Array, Object],
      required: true,
    },
    oldDrivingLicenseDetailsData: {
      type: [Array, Object],
      required: false,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    actionType: {
      type: String,
      default: "",
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    isApprovalView: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    CustomSelect,
    FilePreviewModal,
    FieldDiff,
  },
  emits: ["refetch-personal-details", "edit-opened", "edit-closed"],
  data() {
    return {
      showEditForm: false,
      drivingLicenseDetails: {
        Driving_License_No: "",
        License_Expiry_Date: null,
        License_Issue_Date: null,
        Issuing_Authority: "",
        Issuing_Country: "",
        Issuing_State: "",
        Vehicle_Type: "",
        Country_Name: "",
      },
      editedLicenseDetails: {},
      issuingCountries: ["India", "Australia", "Japan"],
      isFormDirty: false,
      //Date-picker
      formattedIssueDate: "",
      issueDateMenu: false,
      expiryDateMenu: false,
      formattedExpiryDate: "",
      openWarningModal: false,
      // edit
      formType: "",
      openBottomSheet: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      // list
      countryList: [],
      countryListLoading: false,
      retrievedFileName: "",
      openModal: false,
      isFileChanged: false,
      fileContent: null,
    };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    currentTimeStamp() {
      return moment().unix();
    },
    selectedEmpDobDate() {
      if (
        this.selectedEmployeeDob &&
        this.selectedEmployeeDob !== "0000-00-00"
      ) {
        return moment(this.selectedEmployeeDob).format("YYYY-MM-DD");
      } else return null;
    },
    licenseExpiryMinDate() {
      if (
        this.editedLicenseDetails.License_Issue_Date &&
        this.editedLicenseDetails.License_Issue_Date !== "0000-00-00"
      ) {
        return moment(this.editedLicenseDetails.License_Issue_Date).format(
          "YYYY-MM-DD"
        );
      }
      return this.selectedEmpDobDate;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    enableEdit() {
      if (this.empFormUpdateAccess) {
        return this.empFormUpdateAccess;
      } else if (this.formAccess && this.formAccess.admin === "admin") {
        let myTeamFormAccess =
          this.formType === "add"
            ? this.formAccess.add
            : this.formAccess.update;
        return this.empFormUpdateAccess || myTeamFormAccess;
      } else return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    hasFileChanged() {
      return (
        this.drivingLicenseDetails?.File_Name !==
        this.oldDrivingLicenseDetailsData?.File_Name
      );
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.licenseDetailsData &&
      ((Array.isArray(this.licenseDetailsData) &&
        this.licenseDetailsData.length > 0) ||
        (!Array.isArray(this.licenseDetailsData) &&
          Object.keys(this.licenseDetailsData).length > 0))
    ) {
      const licenseObj = Array.isArray(this.licenseDetailsData)
        ? this.licenseDetailsData[0]
        : this.licenseDetailsData;

      if (licenseObj?.File_Name) {
        this.fileContent = {
          name: this.formattedFileName(licenseObj.File_Name),
          size: 100,
        };
      } else {
        this.fileContent = null;
      }

      this.formType = "edit";
      this.drivingLicenseDetails = licenseObj;
    } else {
      this.formType = "add";
      this.fileContent = null;
      this.drivingLicenseDetails = {};
    }
  },

  watch: {
    "editedLicenseDetails.License_Issue_Date": function (val) {
      if (val) {
        this.issueDateMenu = false;
        this.formattedIssueDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedLicenseDetails.License_Expiry_Date": function (val) {
      if (val) {
        this.expiryDateMenu = false;
        this.formattedExpiryDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  methods: {
    checkNullValue,

    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.editedLicenseDetails[field] = value;
    },

    editUpdated() {
      this.openBottomSheet = false;
      this.showEditForm = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.$emit("refetch-personal-details");
    },

    openEditDialog() {
      this.showEditForm = true;
      this.openBottomSheet = true;
      this.editedLicenseDetails = JSON.parse(
        JSON.stringify(this.drivingLicenseDetails)
      );
      if (this.editedLicenseDetails.License_Issue_Date) {
        this.formattedIssueDate = this.formatDate(
          this.editedLicenseDetails?.License_Issue_Date
        );
        this.editedLicenseDetails.License_Issue_Date = this.editedLicenseDetails
          .License_Issue_Date
          ? new Date(this.editedLicenseDetails.License_Issue_Date)
          : null;
      }
      if (this.editedLicenseDetails.License_Expiry_Date) {
        this.formattedExpiryDate = this.formatDate(
          this.editedLicenseDetails?.License_Expiry_Date
        );
        this.editedLicenseDetails.License_Expiry_Date = this
          .editedLicenseDetails.License_Expiry_Date
          ? new Date(this.editedLicenseDetails.License_Expiry_Date)
          : null;
      }
      this.retrieveCountries();
      mixpanel.track("EmpProfile-license-edit-opened");
      this.$emit("edit-opened");
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.showEditForm = false;
        mixpanel.track("EmpProfile-license-edit-close");
        this.$emit("edit-closed");
      }
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.drivingLicenseDetailsForm.validate();
      mixpanel.track("EmpProfile-license-submit-btn-clicked");
      if (isFormValid && isFormValid.valid) {
        this.updateDrivingLicenseDetails();
      }
    },

    updateDrivingLicenseDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_LICENSE_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            drivingLicenseNo: vm.editedLicenseDetails.Driving_License_No,
            licenseIssueDate: moment(
              vm.editedLicenseDetails.License_Issue_Date
            ).isValid()
              ? moment(vm.editedLicenseDetails.License_Issue_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            licenseExpiryDate: moment(
              vm.editedLicenseDetails.License_Expiry_Date
            ).isValid()
              ? moment(vm.editedLicenseDetails.License_Expiry_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            issuingAuthority: vm.editedLicenseDetails.Issuing_Authority,
            issuingCountry: vm.editedLicenseDetails.Issuing_Country,
            fileName: vm.editedLicenseDetails["File_Name"],
            issuingState: vm.editedLicenseDetails.Issuing_State,
            vehicleType: vm.editedLicenseDetails.Vehicle_Type,
            formId: vm.callingFrom === "profile" ? 18 : 243,
            formStatus: vm.actionType === "edit" ? 1 : 0,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateDrivingLicenseDetails;
          mixpanel.track("EmpProfile-license-update-success");
          if (vm.editedLicenseDetails["File_Name"] && vm.isFileChanged) {
            vm.uploadFileContents();
          }
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: message?.includes("approval")
              ? "Driving license details is submitted for approval."
              : vm.formType === "edit"
              ? "Driving license details updated successfully"
              : "Driving license details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.editUpdated();
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-license-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "driving license details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    async retrieveCountries() {
      this.countryListLoading = true;
      this.countryList = [];
      await this.$store
        .dispatch("listCountries")
        .then((langList) => {
          this.countryList = langList;
          this.countryListLoading = false;
        })
        .catch(() => {
          this.countryListLoading = false;
        });
    },
    async uploadFileContents() {
      mixpanel.track("Onboarded-candidate-doc-file-upload-start");
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employees Document Upload/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.editedLicenseDetails["File_Name"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .then(() => {
          vm.isLoading = false;
          vm.editUpdated();
        })
        .catch(() => {
          mixpanel.track("Onboarded-candidate-doc-file-upload-error");
          vm.isLoading = false;
          vm.editUpdated();
        });
    },
    onChangeFiles(value) {
      this.fileContent = value;
      if (this.fileContent && this.fileContent.name) {
        mixpanel.track("Onboarded-candidate-doc-file-changed");
        this.editedLicenseDetails["File_Name"] =
          this.selectedEmpId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent.name;
        this.isFileChanged = true;
      }
      this.onChangeFields();
    },
    removeFiles() {
      mixpanel.track("Onboarded-candidate-doc-file-removed");
      this.editedLicenseDetails["File_Name"] = "";
      this.fileContent = null;
      this.onChangeFields();
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },
    retrieveDocuments(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
