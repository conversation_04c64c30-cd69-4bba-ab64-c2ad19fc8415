import gql from "graphql-tag";

export const RETRIEVE_APPS_URLS_SETTINGS = gql`
  query retrieveAppUrlSettings($formId: Int) {
    retrieveAppUrlSettings(formId: $formId) {
      errorCode
      message
      appUrlSettings {
        organizationLevelAppUrlSettings {
          track_app_url
          key_logging
          capture_control_keys
        }
        employeeLevelAppUrlSettings {
          employee_id
          track_app_url
          photo_path
          user_defined_empid
          employee_name
          emp_email
          key_logging
          capture_control_keys
        }
        applicationCategoryCount {
          productiveApplicationCount
          unproductiveApplicationCount
          uncategorizedApplicationCount
        }
        urlCategoryCount {
          productiveUrlCount
          unproductiveUrlCount
          uncategorizedUrlCount
        }
      }
    }
  }
`;

export const UPDATE_APPS_URLS_CATEGORY_SETTINGS = gql`
  mutation updateAppUrlSettings(
    $formId: Int
    $organizationSettings: [organizationAppUrlInput]!
    $employeeSettings: [employeeAppUrlInput]!
  ) {
    updateAppUrlSettings(
      formId: $formId
      organizationSettings: $organizationSettings
      employeeSettings: $employeeSettings
    ) {
      errorCode
      message
    }
  }
`;
