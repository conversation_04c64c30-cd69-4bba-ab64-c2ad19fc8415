<template>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppAccessDenied v-else-if="isAccessDenied"></AppAccessDenied>
</template>

<script>
export default {
  name: "PayrollSetUp",

  data() {
    return {
      isLoading: true,
      isAccessDenied: false,
    };
  },

  computed: {
    // returns baseurl of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let payrollGenralConfigAccess = this.accessRights("261");
      if (
        payrollGenralConfigAccess &&
        payrollGenralConfigAccess.accessRights &&
        payrollGenralConfigAccess.accessRights["view"]
      ) {
        return payrollGenralConfigAccess.accessRights;
      } else return false;
    },
  },

  mounted() {
    if (this.formAccess) {
      this.redirectToRelevantForm();
    } else {
      this.isLoading = false;
      this.isAccessDenied = true;
    }
  },

  methods: {
    redirectToRelevantForm() {
      this.$router.push("/settings/payroll/general");
    },
  },
};
</script>
