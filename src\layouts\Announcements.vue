<template>
  <div>
    <v-menu
      v-model="isOpenAnnouncement"
      :open-on-hover="!isMobileView"
      location="bottom"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
    >
      <template v-slot:activator="{ props }">
        <v-badge
          bordered
          class="mr-sm-6 ml-5"
          color="primary"
          :content="announcementList ? announcementList.length.toString() : '0'"
          v-bind="props"
        >
          <div style="width: 20px; height: 30px">
            <v-icon
              class="mt-2"
              color="primary"
              style="font-size: 18px; cursor: pointer"
            >
              fa fa-bullhorn
            </v-icon>
          </div>
        </v-badge>
      </template>
      <v-card
        class="pa-5"
        width="350"
        min-height="300"
        style="border-radius: 5px !important"
      >
        <!-- Announcement Loading -->
        <div v-if="announcementLoading">loader</div>
        <div v-else>
          <!-- Announcement Error Presentation -->

          <div v-if="errorInAnnouncement" class="pa-4 mt-4 d-flex flex-column">
            <img
              style="width: 40%; height: 50%"
              :src="getImageUrl"
              alt="announcement error image"
              class="mx-auto"
            />
            <div class="pa-3 text-center">
              {{ $t("authLayout.technicalDifficulties") }}
            </div>
            <v-btn
              id="refresh_announcement"
              size="small"
              dense
              rounded="lg"
              color="primary"
              class="mx-auto d-flex mt-2"
              @click="fnRefetchAnnouncements()"
            >
              <v-icon class="mr-1" style="font-size: 14px; color: white"
                >fas fa-redo-alt</v-icon
              >
              <span class="font-weight-bold" style="font-size: 10px">{{
                $t("authLayout.refresh")
              }}</span>
            </v-btn>
          </div>
          <!-- Announcement Content Presentation -->

          <div v-else-if="announcementList.length > 0">
            <div>
              <v-icon color="secondary" size="20">trip_origin</v-icon>
              <span class="text-body-1 ml-2 text-primary font-weight-bold">{{
                $t("authLayout.announcementCount", {
                  count: announcementList
                    ? announcementList.length.toString()
                    : "0",
                })
              }}</span>
            </div>
            <div style="overflow-x: hidden; max-height: 300px">
              <ListActionCard
                v-for="(announcement, i) in announcementList"
                :key="i + '-announcements'"
                :card-property="cardProperty[i % 2]"
                :title="announcement.title"
                :list-index="i"
                :is-clickable="true"
                :icon-name="
                  announcement.announcementType === 'Video'
                    ? 'fas fa-video'
                    : 'fas fa-align-left'
                "
                style="margin-top: 10px"
                @action-triggered="fnShowAnnouncement($event)"
              ></ListActionCard>
            </div>
          </div>
          <!-- No Announcement Empty Presentation -->

          <div v-else class="pa-4 mt-4 d-flex flex-column">
            <div class="pa-3 text-center text-primary font-weight-bold">
              {{ $t("authLayout.caughtUpAnnouncements") }}
            </div>
            <img
              style="width: 40%; height: 50%"
              :src="getImageUrl"
              alt="announcement empty image"
              class="mx-auto my-4"
            />
            <div class="pa-3 text-center">
              {{ $t("authLayout.newContentNotification") }}
            </div>
          </div>
        </div>
      </v-card>
    </v-menu>
    <v-dialog
      v-model="announcementModal"
      width="550"
      @click:outside="fnCloseAnnouncementModal()"
    >
      <v-card
        max-height="400"
        class="rounded-lg pb-4"
        style="border: 2px solid rgb(var(--v-theme-secondary))"
      >
        <img
          width="150"
          height="120"
          :src="announcementImage"
          alt="announcement"
          class="mx-auto mt-n12"
          style="position: fixed"
        />
        <div class="d-flex justify-end">
          <v-icon
            class="mt-1 pr-2"
            color="secondary"
            @click="fnCloseAnnouncementModal()"
            >fas fa-times</v-icon
          >
        </div>
        <v-card-title
          class="d-flex justify-center mt-n1 text-h5 text-primary font-weight-bold"
        >
          {{ selectedAnnouncement.title }}
        </v-card-title>
        <perfect-scrollbar
          v-if="selectedAnnouncement.announcementType === 'Text'"
          class="scroll-area sidebar-scroll"
        >
          <div
            id="announcementContent"
            class="text-center px-sm-10 mt-4 mb-10"
            style="max-height: 200px"
          ></div>
        </perfect-scrollbar>
        <div v-else class="pb-4 my-3">
          <section v-if="srcContent" class="text-center">
            <iframe
              width="85%"
              height="250"
              :src="srcContent"
              frameborder="0"
              allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
              allowfullscreen
            ></iframe>
          </section>
          <section v-else class="pt-10 mt-4 d-flex flex-column">
            <img
              width="30%"
              height="50%"
              :src="getImageUrl"
              alt="iframe empty image"
              class="mx-auto mb-3"
            />
            <div class="text-center pa-4">
              {{ $t("authLayout.invalidEmbedUrl") }}
            </div>
          </section>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
// components
const ListActionCard = defineAsyncComponent(() =>
  import("@/components/helper-components/ListActionCard")
);
// queries
import { LIST_ANNOUNCEMENT } from "@/graphql/layout/layoutQueries";

export default defineComponent({
  name: "LayoutAnnouncement",
  components: { ListActionCard },

  data: () => ({
    announcementModal: false,
    srcContent: "",
    announcementList: [],
    selectedAnnouncement: {},
    isOpenAnnouncement: false,
    errorInAnnouncement: false,
    cardProperty: [
      {
        style: "background: #FFFFFF",
        bg: "pink-lighten-4",
        color: "pink-lighten-2",
      },
      {
        style: "background: #FFFFFF",
        bg: "blue-grey-lighten-4",
        color: "blue-grey-lighten-2",
      },
    ],
  }),

  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    // get announcement image url
    getImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/announcement-icon.webp");
      else return require("@/assets/images/layout/announcement-icon.png");
    },
    announcementImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/announcement.webp");
      else return require("@/assets/images/layout/announcement.png");
    },
    // loading when the announcement is fetch
    announcementLoading() {
      return this.$apollo.queries.listAnnouncements.loading;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  apollo: {
    // list announcement query
    listAnnouncements: {
      query: LIST_ANNOUNCEMENT,
      client: "apolloClientC",

      result({ data }) {
        try {
          // check data is empty or not (when error, data is returned as empty)
          if (Object.keys(data).length !== 0) {
            if (data.listAnnouncements) {
              let announceDetails = data.listAnnouncements.announcementDetails;
              this.announcementList = announceDetails;
            }
            this.errorInAnnouncement = false;
          } else {
            // if any error occurs, data is returned empty object
            this.errorInAnnouncement = true;
          }
        } catch {
          this.errorInAnnouncement = true;
        }
      },
      // Error handling
      error() {
        this.errorInAnnouncement = true; // we didn't check any rights for announcement. So other errors are not handled here
      },
    },
  },
  methods: {
    bindContent() {
      setTimeout(() => {
        let content = this.selectedAnnouncement.announcementText;
        let element = document.getElementById("announcementContent");
        element.innerHTML = content;
      }, 1000);
    },
    // refresh the announcement query when click refresh button(displayed when any error occur while listing announcement)
    fnRefetchAnnouncements() {
      this.errorInAnnouncement = false;
      this.$apollo.queries.listAnnouncements.refetch();
    },
    // function to find src in iframe html tag
    fnFindSrcFromIframe() {
      let data = this.selectedAnnouncement.embedUrl;
      if (data) {
        try {
          let splitData = data.split(" "); // split tha embed url data
          let checkSrcAttribute = splitData.filter((sas) =>
            sas.includes("src")
          ); // check any src attribute is available ("src = 'https://someurl'")
          let frameSrc = checkSrcAttribute[0].split("="); // then split src attribute by = ("src", ""https://someurl"")
          this.srcContent = frameSrc[1]; // get src url (""https://someurl"")
          this.srcContent = this.srcContent.replace(/[""]+/g, ""); // replace " by empty (https://someurl)
        } catch (e) {
          this.srcContent = ""; // any error occurs in above operation, we set src content as empty
        }
      }
    },
    // function to display announcement popup
    fnShowAnnouncement(index) {
      this.selectedAnnouncement = this.announcementList[index];
      this.fnFindSrcFromIframe();
      this.isOpenAnnouncement = false;
      this.announcementModal = true;
      this.bindContent();
    },
    // reset values when the popup is closed
    fnCloseAnnouncementModal() {
      this.srcContent = "";
      this.announcementModal = false;
    },
  },
});
</script>
