<template>
  <div
    v-if="experienceDetails && experienceDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No experience details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in experienceDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `width:450px; border-left: 7px solid ${generateRandomColor()}; height:300px; `
        : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip :text="data.Prev_Company_Name" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="data.Prev_Company_Name ? props : ''"
                    >
                      {{ checkNullValue(data.Prev_Company_Name) }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>

      <div class="card-columns w-100 mt-n6">
        <span
          :style="!isMobileView ? 'width:67%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Designation </b>
              <v-tooltip
                :text="data.Designation"
                location="bottom"
                max-width="400"
              >
                <template v-slot:activator="{ props }">
                  <span class="pb-1 pt-1" v-bind="props">
                    <div
                      :style="
                        isMobileView ? 'max-width: 200px' : 'max-width:140px'
                      "
                      class="text-truncate"
                    >
                      {{ checkNullValue(data.Designation) }}
                    </div></span
                  >
                </template>
              </v-tooltip>
            </div>
            <div
              v-if="isMobileView"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start">Location </b>
              <span class="py-2">
                {{ checkNullValue(data.Prev_Company_Location) }}</span
              >
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">From </b>
              <span class="pb-1 pt-1">{{ formatDate(data.Start_Date) }}</span>
            </div>
            <div
              v-if="!isMobileView"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start">Duration </b>
              <span class="pb-1 pt-1">
                {{ durationInDays(data) }}
              </span>
            </div>
            <div
              v-if="labelList[366]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <v-menu open-on-hover location="bottom" offset-y>
                <template v-slot:activator="{ props }">
                  <div
                    :style="
                      isMobileView ? 'max-width: 200px' : 'max-width:140px'
                    "
                    class="text-truncate"
                    v-bind="props"
                  >
                    <v-btn
                      variant="text"
                      class="text-decoration-underline px-0"
                      color="primary"
                    >
                      Reference(s)
                    </v-btn>
                  </div>
                </template>

                <v-card class="pa-3" max-width="500px">
                  <div
                    v-if="
                      !data.Experience_Reference ||
                      data.Experience_Reference.length === 0
                    "
                  >
                    No references available
                  </div>
                  <div v-else>
                    <div
                      v-for="(reference, index) in data.Experience_Reference"
                      :key="index"
                      class="mb-2"
                    >
                      <!-- Reference Name -->
                      <div v-if="labelList[366]?.Field_Visiblity === 'Yes'">
                        {{ labelList[366]?.Field_Alias }}:
                        {{ checkNullValue(reference.Reference_Name) }}
                      </div>

                      <!-- Reference Email -->
                      <div v-if="labelList[367]?.Field_Visiblity === 'Yes'">
                        {{ labelList[367]?.Field_Alias }}:
                        {{ checkNullValue(reference.Reference_Email) }}
                      </div>

                      <!-- Reference Number -->
                      <div v-if="labelList[368]?.Field_Visiblity === 'Yes'">
                        {{ labelList[368]?.Field_Alias }}:
                        {{ checkNullValue(reference.Reference_Number) }}
                      </div>

                      <!-- Separator for multiple references -->
                      <v-divider
                        v-if="index < data.Experience_Reference.length - 1"
                        class="my-2"
                      />
                    </div>
                  </div>
                </v-card>
              </v-menu>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              v-if="!isMobileView"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start">Location </b>
              <v-tooltip
                :text="data.Prev_Company_Location"
                location="bottom"
                max-width="400"
              >
                <template v-slot:activator="{ props }">
                  <span class="py-2" v-bind="props">
                    <div
                      :style="
                        isMobileView ? 'max-width: 200px' : 'max-width:140px'
                      "
                      class="text-truncate"
                    >
                      {{ checkNullValue(data.Prev_Company_Location) }}
                    </div></span
                  >
                </template>
              </v-tooltip>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">To </b>
              <span class="pb-1 pt-1"> {{ formatDate(data.End_Date) }}</span>
            </div>
            <div
              v-if="isMobileView"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start">Duration </b>
              <span class="pb-1 pt-1">
                {{
                  data.Duration
                    ? convertMonthToYearMonthsDays(data.Duration)
                    : "-"
                }}
              </span>
            </div>
            <div
              v-if="data.File_Name"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span class="text-blue-grey-darken-3 font-weight-bold"></span>
              <span class="text-blue-grey-darken-6">
                <span
                  style="text-decoration: underline"
                  @click="retrieveExpDocuments(data.File_Name)"
                  class="text-green"
                >
                  View Document</span
                >
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employee Experience"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import {
  generateRandomColor,
  checkNullValue,
  convertMonthToYearMonthsDays,
} from "@/helper";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ExperienceDetails",
  props: {
    experienceDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  components: { ActionMenu, FilePreviewModal },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      openModal: false,
      retrievedFileName: "",
      havingAccess: {},
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return this.formAccess && this.formAccess.update;
    },
    durationInDays() {
      return (experienceDetails) => {
        if (experienceDetails.Start_Date && experienceDetails.End_Date) {
          let startDate = moment(experienceDetails.Start_Date);
          let endDate = moment(experienceDetails.End_Date);
          let totalDays = endDate.diff(startDate, "days");

          // Break down the duration into years, months, and days
          let years = Math.floor(totalDays / 365);
          totalDays %= 365;
          let months = Math.floor(totalDays / 30);
          let days = totalDays % 30;
          let durationString = [];
          if (years > 0) {
            durationString.push(`${years} years`);
          }
          if (months > 0) {
            durationString.push(`${months} months`);
          }
          if (days > 0) {
            durationString.push(`${days} days`);
          }
          return durationString.join(" ");
        } else return "-";
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      return this.havingAccess;
    },
    convertMonthToYearMonthsDays,
    handleActions(action, index) {
      let selectedActionItem = this.experienceDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    retrieveExpDocuments(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
  },
};
</script>
