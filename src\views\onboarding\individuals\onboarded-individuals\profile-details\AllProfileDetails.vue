<template>
  <div>
    <ProfileCard class="my-5">
      <FormTab :model-value="activeTab" grow :hide-slider="true">
        <v-tab
          v-for="tab in profileTabs"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          @click="onChangeProfileTabs(tab.value)"
        >
          <div
            :class="[
              isActiveTab(tab)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            {{ tab.label }}
            <div
              v-if="isActiveTab(tab)"
              class="mt-3 mb-n4"
              style="border-bottom: 4px solid; width: 10rem; margin: 0 auto"
            ></div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>

    <ProfileCard
      v-if="isMounted"
      class="my-5"
      v-on:scroll="handleScroll"
      ref="profileContent"
    >
      <div
        v-if="!personalOpenedEditForm && activeTab === 'Personal_Info'"
        class="d-flex pl-4 pr-4 mb-n2"
        style="justify-content: end"
      >
        <!-- <v-btn variant="text" color="secondary" @click="goBackToList()">
          <v-icon class="pr-3">fas fa-angle-left fa-lg</v-icon>
          Back</v-btn
        > -->
        <v-icon @click="refetchCount += 1" size="17" color="grey" class="mt-2"
          >fas fa-redo-alt</v-icon
        >
      </div>
      <div class="profile-section-height">
        <v-card-text class="fill-height">
          <PersonalInfo
            v-if="activeTab === 'Personal_Info'"
            :selectedCandidateId="candidateId"
            :formAccess="formAccess"
            :refetchCount="refetchCount"
            @selected-emp-dob="candidateDOB = $event"
            @close-add-form="$emit('close-add-form')"
            @details-retrieved="enableTabBasedOnData(1, $event)"
            @details-updated="$emit('details-updated')"
            @selected-candidate-id="onNewEmpAdd($event)"
            @opened-edit-form="personalOpenedEditForm = $event"
          />
          <JobInfo
            v-else-if="activeTab === 'Job_Info'"
            :selectedCandidateId="candidateId"
            :formAccess="formAccess"
            :selectedCandidateDOB="candidateDOB"
            :selectedCandidateDetails="candidateDetails"
            :candidateList="candidateList"
            @details-retrieved="enableTabBasedOnData(2, $event)"
            @details-updated="$emit('details-updated')"
          />
          <ContactInfo
            v-else-if="activeTab === 'Contact_Info'"
            :selectedCandidateId="candidateId"
            :formAccess="formAccess"
            @details-retrieved="enableTabBasedOnData(3, $event)"
            @details-updated="$emit('details-updated')"
          />
          <CareerInfo
            v-else-if="activeTab === 'Career_Info'"
            :selectedCandidateId="candidateId"
            :formAccess="formAccess"
            :selectedCandidateDOB="candidateDOB"
            @details-retrieved="enableTabBasedOnData(4, [$event])"
          />
          <DocumentAndAccreditation
            v-else-if="activeTab === 'Document/Accreditations'"
            :selectedCandidateId="candidateId"
            :formAccess="formAccess"
            :selectedCandidateDOB="candidateDOB"
            @details-retrieved="enableTabBasedOnData(5, [$event])"
          />
          <Others
            v-else-if="activeTab === 'Other'"
            :selectedCandidateId="candidateId"
            :formAccess="formAccess"
            @details-retrieved="enableTabBasedOnData(5, [$event])"
          />
        </v-card-text>
      </div>
    </ProfileCard>
    <!-- Scroll to bottom button -->
    <v-btn
      v-if="showScrollButton"
      @click="scrollToBottom"
      class="scroll-to-bottom-button"
      variant="outlined"
      icon="fas fa-chevron-down"
      size="x-small"
    >
    </v-btn>
  </div>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="changeTab()"
  ></AppWarningModal>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import PersonalInfo from "./personal/PersonalInfo.vue";
const JobInfo = defineAsyncComponent(() => import("./job/JobInfo.vue"));
const ContactInfo = defineAsyncComponent(() =>
  import("./contact/ContactInfo.vue")
);
const CareerInfo = defineAsyncComponent(() =>
  import("./career/CareerInfo.vue")
);
const DocumentAndAccreditation = defineAsyncComponent(() =>
  import("./document-accreditations/DocumentAndAccreditation.vue")
);
const Others = defineAsyncComponent(() => import("./others/Others.vue"));
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default defineComponent({
  name: "AllProfileDetails",
  components: {
    PersonalInfo,
    JobInfo,
    ContactInfo,
    CareerInfo,
    DocumentAndAccreditation,
    Others,
  },
  props: {
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    candidateList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    candidateChangeCount: {
      type: Number,
      default: 0,
    },
  },
  emits: [
    "close-add-form",
    "candidate-id-retrieved",
    "details-updated",
    "candidate-details-updated",
    "close-profile",
  ],
  data() {
    return {
      activeTab: "Personal_Info",
      profileTabs: [
        {
          label: "Personal Info",
          value: "Personal_Info",
          disable: false,
        },
        {
          label: "Job Info",
          value: "Job_Info",
          disable: true,
        },
        {
          label: "Contact Info",
          value: "Contact_Info",
          disable: true,
        },
        {
          label: "Career Info",
          value: "Career_Info",
          disable: true,
        },
        {
          label: "Document/Accreditations",
          value: "Document/Accreditations",
          disable: true,
        },
        {
          label: "Bank & Other Info",
          value: "Other",
          disable: true,
        },
      ],
      candidateDOB: "",
      candidateId: 0,
      isMounted: false,
      openWarningModal: false,
      changedTab: "",
      candidateDetails: {},
      showScrollButton: false,
      personalOpenedEditForm: "",
      refetchCount: 0,
    };
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  watch: {
    candidateDetails(val) {
      this.$emit("candidate-details-updated", val);
    },
    candidateChangeCount(count) {
      if (count > 0) {
        this.assignValues(true);
      }
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.enableAllTabs();
    this.assignValues();
    if (this.labelList[451]?.Field_Alias) {
      this.profileTabs[4].label =
        "Document/" + this.labelList[451]?.Field_Alias || "Accreditations";
    }
    this.isMounted = true;
  },
  methods: {
    assignValues(isEmpChanged = false) {
      this.candidateId = this.selectedCandidateId;
      this.candidateDOB = this.selectedCandidateDOB;
      this.candidateDetails = this.selectedCandidateDetails;
      this.activeTab = "Personal_Info";
      if (this.candidateId) {
        this.$nextTick(() => {
          this.scrollToBottom(); // Optional: Scroll to bottom on component mount
        });
      }
      if (isEmpChanged) this.refetchCount += 1;
    },
    goBackToList() {
      mixpanel.track("Onboarded-candidate-profileCard-backTo-team-list");
      this.$emit("close-profile");
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_OPENED_COUNT", "0-false");
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
    },
    handleScroll() {
      const profileContentEl = this.$refs.profileContent;
      if (profileContentEl) {
        // Use the inner container for Vuetify components
        const innerContainer = profileContentEl.$el || profileContentEl;
        // Check if the scroll position is close to the bottom
        this.showScrollButton =
          innerContainer.scrollTop + innerContainer.clientHeight <
          innerContainer.scrollHeight;
      }
    },
    scrollToBottom() {
      const profileContentEl = this.$refs.profileContent;
      if (profileContentEl) {
        const innerContainer = profileContentEl.$el || profileContentEl;
        innerContainer.scrollTop = innerContainer.scrollHeight;
        this.handleScroll();
      }
    },
    isActiveTab(tab) {
      return this.activeTab === tab.value;
    },
    enableTabBasedOnData(index, type) {
      if (type && type.length === 2) {
        const combinedObj = { ...this.candidateDetails, ...type[1] };
        this.candidateDetails = combinedObj;
      }
      if (type[0] === "update") {
        this.$emit("details-updated");
        if (index >= 3) {
          this.enableAllTabs();
        } else {
          this.profileTabs[index]["disable"] = false;
        }
      }
    },
    onNewEmpAdd(candidateId) {
      this.candidateId = candidateId;
      this.scrollToBottom();
      this.$emit("candidate-id-retrieved", candidateId);
    },
    enableAllTabs() {
      this.profileTabs = this.profileTabs.map((item) => {
        item["disable"] = false;
        return item;
      });
    },

    onChangeProfileTabs(val) {
      let isEditFormChanged = this.$store.state.onboarding.isEditFormChanged;
      if (isEditFormChanged) {
        this.changedTab = val;
        this.openWarningModal = true;
      } else {
        this.activeTab = val;
      }
      mixpanel.track("Onboarded-candidate-tab-changed");
    },

    onCloseWarningModal() {
      this.changedTab = "";
      this.openWarningModal = false;
    },

    changeTab() {
      this.activeTab = this.changedTab;
      this.openWarningModal = false;
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
    },
  },
});
</script>

<style scoped>
.scroll-to-bottom-button {
  position: fixed;
  bottom: 5%;
  right: 15%;
  cursor: pointer;
}
.profile-section-height {
  height: calc(100vh - 240px);
  overflow: hidden;
  overflow-y: scroll;
}

.profile-section-height :deep() .v-tabs--density-default {
  --v-tabs-height: 55px !important;
}
</style>
