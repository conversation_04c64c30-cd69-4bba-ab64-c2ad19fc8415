import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const GET_ENCRYPTION_KEY = gql`
  query getEncryptionKey {
    getEncryptionKey {
      errorCode
      message
      indeedEncryptionKeyData
    }
  }
`;

export const GET_INDEED_AUTH_CREDENTIALS = gql`
  query getIndeedAuthCredentials {
    getIndeedAuthCredentials {
      errorCode
      message
      data {
        clientId
        secretKey
        indeedApplyToken
      }
    }
  }
`;

export const GET_INDEED_AUTH_TOKEN = gql`
  query getIndeedAuthToken($scope: String!, $grantType: String!) {
    getIndeedAuthToken(scope: $scope, grantType: $grantType) {
      errorCode
      message
      data
      sourceName
    }
  }
`;

export const RETRIEVE_INDEED_JOBPOST_DETAILS = gql`
  query retrieveIndeedJobPostDetails($jobPostId: Int!) {
    retrieveIndeedJobPostDetails(jobPostId: $jobPostId) {
      errorCode
      message
      data {
        integrationId
        jobPostId
        dynamicFormId
        employerJobId
        benefits
        contactName
        contactType
        contactPhone
        contactEmail
        status
        cityId
        cityName
        stateId
        stateName
        workplaceType
      }
    }
  }
`;

// ===============
// MUTATIONS
// ===============

export const PUBLISH_JOBPOST_TO_INDEED = gql`
  mutation publishJobPostToIndeed(
    $integrationId: Int
    $jobPostId: Int
    $dynamicFormId: Int
    $status: String
    $cityId: Int
    $cityName: String
    $stateId: Int
    $stateName: String
    $countryCode: String
    $workplaceType: String
    $updateLocation: String
    $input: CreateSourcedJobPostingsInput
  ) {
    publishJobPostToIndeed(
      integrationId: $integrationId
      jobPostId: $jobPostId
      dynamicFormId: $dynamicFormId
      status: $status
      cityId: $cityId
      cityName: $cityName
      stateId: $stateId
      stateName: $stateName
      countryCode: $countryCode
      workplaceType: $workplaceType
      updateLocation: $updateLocation
      input: $input
    ) {
      errorCode
      results
    }
  }
`;

export const CLOSE_JOBPOST_TO_INDEED = gql`
  mutation closeJobpostToIndeed(
    $sourcedPostingId: String!
    $integrationId: Int
  ) {
    closeJobpostToIndeed(
      sourcedPostingId: $sourcedPostingId
      integrationId: $integrationId
    ) {
      errorCode
      results
    }
  }
`;
