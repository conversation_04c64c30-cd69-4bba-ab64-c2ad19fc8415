import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_ORGANISATION_GROUP_LIST = gql`
  query listOrganizationGroup($formId: Int) {
    listOrganizationGroup(formId: $formId) {
      errorCode
      message
      organizationGroupObject {
        maxOrgCode
        organizationGroupList {
          level
          organizationGroupId
          description
          status
          organizationGroupCode
          organizationGroupFullName
          organizationGroup
          addedOn
          addedByName
          updatedOn
          updatedByName
        }
      }
    }
  }
`;

//Mutations

export const ADD_UPDATE_ORGANIZATION_GROUP = gql`
  mutation addUpdateorganizationGroup(
    $organizationGroupId: Int!
    $description: String
    $organizationGroupCode: String
    $organizationGroup: String!
    $level: Int
  ) {
    addUpdateorganizationGroup(
      organizationGroupId: $organizationGroupId
      description: $description
      organizationGroupCode: $organizationGroupCode
      organizationGroup: $organizationGroup
      level: $level
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_ORGANIZATION_GROUP_STATUS = gql`
  mutation updateOrganizationStatus(
    $organizationGroupId: Int!
    $Status: String!
  ) {
    updateOrganizationStatus(
      organizationGroupId: $organizationGroupId
      Status: $Status
    ) {
      errorCode
      message
    }
  }
`;
