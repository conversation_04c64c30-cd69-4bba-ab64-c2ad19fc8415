<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs('error')"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else-if="isMounted">
    <PersonalDetails
      v-if="!openedEditForm || openedEditForm === 'personalDetails'"
      ref="personalDetails"
      :personalDetailsData="personalDetailsData"
      :formAccess="formAccess"
      :empFormUpdateAccess="empFormUpdateAccess"
      :selectedEmpStatus="selectedEmpStatus"
      :actionType="actionType"
      :callingFrom="callingFrom"
      :selectedEmpId="employeeIdSelected"
      @refetch-personal-details="refetchAPIs('update', $event)"
      @edit-opened="openedEditForm = 'personalDetails'"
      @edit-closed="openedEditForm = ''"
      @close-add-form="$emit('close-add-form')"
    />
    <DrivingLicenseDetails
      v-if="!openedEditForm || openedEditForm === 'drivingLicenseDetails'"
      ref="drivingLicenseDetails"
      :licenseDetailsData="licenseDetailsData"
      :selectedEmpId="employeeIdSelected"
      :formAccess="selectedEmpStatus === 'Active' ? formAccess : false"
      :actionType="actionType"
      :empFormUpdateAccess="empFormUpdateAccess"
      :selectedEmployeeDob="selectedEmployeeDob"
      :callingFrom="callingFrom"
      @refetch-personal-details="refetchAPIs('update')"
      @edit-opened="openedEditForm = 'drivingLicenseDetails'"
      @edit-closed="openedEditForm = ''"
    />
    <PassportDetails
      v-if="!openedEditForm || openedEditForm === 'passportDetails'"
      ref="passportDetails"
      :passportDetailsData="passportDetailsData"
      :selectedEmpId="employeeIdSelected"
      :actionType="actionType"
      :formAccess="selectedEmpStatus === 'Active' ? formAccess : false"
      :empFormUpdateAccess="empFormUpdateAccess"
      :selectedEmployeeDob="selectedEmployeeDob"
      :callingFrom="callingFrom"
      @refetch-personal-details="refetchAPIs('update')"
      @edit-opened="openedEditForm = 'passportDetails'"
      @edit-closed="openedEditForm = ''"
    />
    <DependentDetails
      v-if="!openedEditForm"
      ref="dependentDetails"
      :dependentDetailsData="dependentDetailsData"
      :selectedEmpId="employeeIdSelected"
      :formAccess="selectedEmpStatus === 'Active' ? formAccess : false"
      :empFormUpdateAccess="empFormUpdateAccess"
      :actionType="actionType"
      :callingFrom="callingFrom"
      :selectedEmpMaritalStatus="selectedEmpMaritalStatus"
      @refetch-personal-details="refetchAPIs('update')"
    />
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
// components
const PersonalDetails = defineAsyncComponent(() =>
  import("./details/PersonalDetails.vue")
);
const PassportDetails = defineAsyncComponent(() =>
  import("./passport/PassportDetails.vue")
);
const DrivingLicenseDetails = defineAsyncComponent(() =>
  import("./license/DrivingLicenseDetails.vue")
);
const DependentDetails = defineAsyncComponent(() =>
  import("./dependent/DependentDetails.vue")
);
import { RETRIEVE_EMP_PERSONAL_INFO } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default defineComponent({
  name: "PersonalInfo",
  components: {
    PersonalDetails,
    PassportDetails,
    DrivingLicenseDetails,
    DependentDetails,
  },
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    refetchCount: {
      type: Number,
      default: 0,
    },
  },
  emits: [
    "selected-emp-dob",
    "close-add-form",
    "details-retrieved",
    "selected-emp-id",
    "details-updated",
    "opened-edit-form",
  ],
  data() {
    return {
      openedEditForm: "",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      personalDetailsData: [],
      licenseDetailsData: [],
      passportDetailsData: [],
      dependentDetailsData: [],
      selectedEmployeeDob: "",
      isLoading: false,
      employeeIdSelected: 0,
      isMounted: false,
      isUpdated: false,
      selectedEmpMaritalStatus: 0,
    };
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  watch: {
    openedEditForm(val) {
      this.$emit("opened-edit-form", val);
    },
    refetchCount(count) {
      if (count > 0) {
        this.employeeIdSelected = this.selectedEmpId;
        this.refetchAPIs("refresh");
      }
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.employeeIdSelected = this.selectedEmpId;
    if (this.selectedEmpId || this.callingFrom === "profile") {
      this.getPersonalDetails();
    } else {
      this.openedEditForm = "personalDetails";
    }
    this.isMounted = true;
  },
  methods: {
    refetchAPIs(type, empId) {
      if (empId) {
        this.employeeIdSelected = empId;
        this.$emit("selected-emp-id", empId);
      }
      this.openedEditForm = "";
      this.isErrorInList = false;
      mixpanel.track("EmpProfile-personalDetails-refetch");
      if (type === "update") {
        this.$emit("details-updated");
      }
      this.getPersonalDetails(type);
    },
    getPersonalDetails(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_PERSONAL_INFO,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.employeeIdSelected,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-personalDetails-fetch-success");
          if (response && response.data && response.data.retrievePersonalInfo) {
            const {
              personalInfoDetails,
              drivingLicenseDetails,
              passportDetails,
              dependentDetails,
            } = response.data.retrievePersonalInfo;
            vm.personalDetailsData = personalInfoDetails
              ? [JSON.parse(personalInfoDetails)]
              : [];
            if (vm.personalDetailsData && vm.personalDetailsData.length > 0) {
              vm.selectedEmployeeDob = vm.personalDetailsData[0].DOB;
              vm.$emit("selected-emp-dob", vm.selectedEmployeeDob);
              vm.selectedEmpMaritalStatus =
                vm.personalDetailsData[0].Marital_Status;
            }
            vm.licenseDetailsData = drivingLicenseDetails
              ? JSON.parse(drivingLicenseDetails)
              : [];
            vm.passportDetailsData = passportDetails
              ? JSON.parse(passportDetails)
              : [];
            vm.dependentDetailsData = dependentDetails
              ? JSON.parse(dependentDetails)
              : [];
            vm.$emit("details-retrieved", [
              type,
              vm.personalDetailsData && vm.personalDetailsData.length > 0
                ? vm.personalDetailsData[0]
                : {},
            ]);
            if (type === "update") {
              vm.openedEditForm = "";
            } else if (!this.selectedEmpId) {
              vm.openedEditForm = "personalDetails";
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("EmpProfile-personalDetails-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "personal details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
});
</script>
