<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>
    <v-container fluid class="nps-container">
      <v-window v-if="formAccess && isSuperAdmin" v-model="currentTabItem">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading || slabWiseLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchData()"
          >
          </AppFetchErrorScreen>
          <div v-else>
            <v-card
              class="py-9 rounded-lg fill-height"
              :class="isMobileView ? '' : 'px-5'"
              elevation="5"
            >
              <v-card-text>
                <v-row>
                  <v-col v-if="!isEdit && !listLoading" cols="12">
                    <ViewNpsFund
                      :editFormData="npsFundData"
                      @open-edit="openEditForm()"
                      :accessFormName="accessFormName"
                      :getFieldAlias="labelList"
                      :formAccess="formAccess"
                    ></ViewNpsFund>
                  </v-col>
                  <v-col v-if="isEdit && !listLoading" cols="12">
                    <EditNpsFund
                      :editFormData="npsFundData"
                      @refetch-data="refetchData()"
                      @close-form="closeEditForm()"
                      :accessFormName="accessFormName"
                      :getFieldAlias="labelList"
                    >
                    </EditNpsFund>
                  </v-col>
                  <v-col v-if="npsSlabData && !listLoading" cols="12">
                    <NpsFundSlabList
                      :slabList="npsSlabData"
                      :getFieldAlias="labelList"
                      @refetch-list="refetchData()"
                    ></NpsFundSlabList>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
// components
import NpsFundSlabList from "./NpsFundSlabList.vue";
import ViewNpsFund from "./ViewNpsFund";
import EditNpsFund from "./EditNpsFund.vue";
// Queries
import {
  RETRIEVE_NPS_FUND_RULES,
  RETRIEVE_NPS_FUND_SLABES,
} from "@/graphql/tax-and-statutory-compliance/npsRules";
export default {
  name: "NPSMainForm",
  components: {
    NpsFundSlabList,
    ViewNpsFund,
    EditNpsFund,
  },
  data() {
    return {
      isLoading: false,
      currentTabItem: "tab-0",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      showSlabList: "Yes",
      isSlabWise: "No",
      npsFundData: {},
      npsSlabData: {},
      isEdit: false,
      slabList: [],
      slabWiseLoading: false,
    };
  },
  computed: {
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formAccess() {
      let npsRulesForm = this.accessRights("260");
      if (
        npsRulesForm &&
        npsRulesForm.accessRights &&
        npsRulesForm.accessRights["view"]
      ) {
        return npsRulesForm.accessRights;
      } else return false;
    },
    accessFormName() {
      let npsRulesForm = this.accessRights("260");
      if (npsRulesForm && npsRulesForm.customFormName) {
        return npsRulesForm.customFormName;
      } else return "HDMF Rules";
    },
    npsPaymentFormAccess() {
      let npsRulesForm = this.accessRights("127");
      if (
        npsRulesForm &&
        npsRulesForm.accessRights &&
        npsRulesForm.accessRights["view"]
      ) {
        return npsRulesForm.accessRights;
      } else return false;
    },
    npsPaymentFormName() {
      let npsRulesForm = this.accessRights("127");
      if (npsRulesForm && npsRulesForm.customFormName) {
        return npsRulesForm.customFormName;
      } else return "NPS Payment Tracker";
    },
    npsConfigFormAccess() {
      let npsConfigAccess = this.accessRights("126");
      if (
        npsConfigAccess &&
        npsConfigAccess.accessRights &&
        npsConfigAccess.accessRights["view"]
      ) {
        return npsConfigAccess.accessRights;
      } else return false;
    },
    npsConfigFormName() {
      let npsConfigAccess = this.accessRights("126");
      if (npsConfigAccess && npsConfigAccess.customFormName) {
        return npsConfigAccess.customFormName;
      } else return "HDMF Configuration";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    mainTabs() {
      if (
        this.npsPaymentFormAccess &&
        this.npsConfigFormAccess &&
        this.formAccess
      ) {
        return [
          this.npsConfigFormName,
          this.npsPaymentFormName,
          this.accessFormName,
        ];
      } else if (this.npsPaymentFormAccess && this.npsConfigFormAccess) {
        return [this.npsConfigFormName, this.npsPaymentFormName];
      } else if (this.npsPaymentFormAccess && this.formAccess) {
        return [this.npsPaymentFormName, this.accessFormName];
      } else if (this.npsConfigFormAccess && this.formAccess) {
        return [this.npsConfigFormName, this.accessFormName];
      } else if (this.npsPaymentFormAccess) {
        return [this.npsPaymentFormName];
      } else if (this.npsConfigFormAccess) {
        return [this.npsConfigFormName];
      } else if (this.formAccess) {
        return [this.accessFormName];
      } else {
        return [];
      }
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("NPS Error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.accessFormName);
    if (this.formAccess && this.isSuperAdmin) {
      this.getSlabWiseNps();
      this.fetchNpsFundDetails();
    }
  },
  methods: {
    openEditForm() {
      this.isEdit = true;
    },
    closeEditForm() {
      this.isEdit = false;
    },
    onTabChange(tab) {
      if (tab !== this.accessFormName) {
        if (tab == this.npsPaymentFormName) {
          window.location.href = this.baseUrl + "payroll/etf";
        } else if (tab === this.npsConfigFormName) {
          this.$router.push("/tax-and-statutory-compliance/nps-configuration");
        }
      }
    },
    refetchData() {
      this.closeEditForm();
      this.getSlabWiseNps();
      this.fetchNpsFundDetails();
    },
    async getSlabWiseNps() {
      let vm = this;
      vm.slabWiseLoading = true;
      await this.$store
        .dispatch("retrievePayrollGeneralSettings")
        .then((response) => {
          if (response) {
            vm.isSlabWise = response[0].Slab_Wise_NPS;
            if (vm.isSlabWise == "Yes") {
              vm.fetchNpsSlabDetails();
            }
          }
          vm.slabWiseLoading = false;
        })
        .catch(() => {
          vm.slabWiseLoading = false;
        });
    },
    fetchNpsFundDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_NPS_FUND_RULES,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listNpsRules) {
            let npsFundData = JSON.parse(
              response.data.listNpsRules.npsRulesDetails
            );

            vm.npsFundData = npsFundData[0];
            vm.listLoading = false;
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    fetchNpsSlabDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_NPS_FUND_SLABES,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listNpsSlabsDetails) {
            let npsSlabData = JSON.parse(
              response.data.listNpsSlabsDetails.npsSlabDetails
            );
            vm.npsSlabData = npsSlabData;
            vm.listLoading = false;
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.nps-container {
  padding: 5em 3em 0em 3em;
}
@media screen and (max-width: 805px) {
  .nps-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
