<template>
  <v-container>
    <div class="menu_items">
      <div>
        <div class="menu_10">
          <div
            class="ta_event_btn ta-tooltip-left ta-tooltip-left-bottom"
            @click="() => addResendTask('addRejectTask')"
            data-title=""
          >
            <div class="menus_icon">
              <i class="fas fa-undo text-white wf_menu_icons"></i>
            </div>
            <span class="ta-tooltip">Users can reject tasks</span>
          </div>
        </div>
        <div class="menu_11">
          <div
            class="ta_event_btn ta-tooltip-bottom ta-tooltip-bottom-left"
            @click="() => addResendTask('addResendTask')"
            data-title=""
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 384 512"
                height="10"
                width="10"
                fill="#ffffff"
              >
                <path
                  d="M350 177.5c3.8-8.8 2-19-4.6-26l-136-144C204.9 2.7 198.6 0 192 0s-12.9 2.7-17.4 7.5l-136 144c-6.6 7-8.4 17.2-4.6 26s12.5 14.5 22 14.5h88l0 192c0 17.7-14.3 32-32 32H32c-17.7 0-32 14.3-32 32v32c0 17.7 14.3 32 32 32l80 0c70.7 0 128-57.3 128-128l0-192h88c9.6 0 18.2-5.7 22-14.5z"
                />
              </svg>
            </div>
            <span class="ta-tooltip">Users can resend tasks</span>
          </div>
        </div>
        <div class="menu_12">
          <div
            class="ta_event_btn ta-tooltip-right ta-tooltip-right-bottom"
            @click="() => addResendTask('addApproveTask')"
            data-title=""
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
                height="10"
                width="10"
                fill="#ffffff"
              >
                <path
                  d="M323.8 34.8c-38.2-10.9-78.1 11.2-89 49.4l-5.7 20c-3.7 13-10.4 25-19.5 35l-51.3 56.4c-8.9 9.8-8.2 25 1.6 33.9s25 8.2 33.9-1.6l51.3-56.4c14.1-15.5 24.4-34 30.1-54.1l5.7-20c3.6-12.7 16.9-20.1 29.7-16.5s20.1 16.9 16.5 29.7l-5.7 20c-5.7 19.9-14.7 38.7-26.6 55.5c-5.2 7.3-5.8 16.9-1.7 24.9s12.3 13 21.3 13L448 224c8.8 0 16 7.2 16 16c0 6.8-4.3 12.7-10.4 15c-7.4 2.8-13 9-14.9 16.7s.1 15.8 5.3 21.7c2.5 2.8 4 6.5 4 10.6c0 7.8-5.6 14.3-13 15.7c-8.2 1.6-15.1 7.3-18 15.2s-1.6 16.7 3.6 23.3c2.1 2.7 3.4 6.1 3.4 9.9c0 6.7-4.2 12.6-10.2 14.9c-11.5 4.5-17.7 16.9-14.4 28.8c.4 1.3 .6 2.8 .6 4.3c0 8.8-7.2 16-16 16H286.5c-12.6 0-25-3.7-35.5-10.7l-61.7-41.1c-11-7.4-25.9-4.4-33.3 6.7s-4.4 25.9 6.7 33.3l61.7 41.1c18.4 12.3 40 18.8 62.1 18.8H384c34.7 0 62.9-27.6 64-62c14.6-11.7 24-29.7 24-50c0-4.5-.5-8.8-1.3-13c15.4-11.7 25.3-30.2 25.3-51c0-6.5-1-12.8-2.8-18.7C504.8 273.7 512 257.7 512 240c0-35.3-28.6-64-64-64l-92.3 0c4.7-10.4 8.7-21.2 11.8-32.2l5.7-20c10.9-38.2-11.2-78.1-49.4-89zM32 192c-17.7 0-32 14.3-32 32V448c0 17.7 14.3 32 32 32H96c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32H32z"
                />
              </svg>
            </div>
            <span class="ta-tooltip">Users can approve tasks</span>
          </div>
        </div>
      </div>
    </div>
  </v-container>
</template>
<script>
// import "../../../assets/css/tooltip.css";
export default {
  emits: ["handleProcessNode"],
  name: "ResendMenuItems",
  data() {
    return {};
  },
  methods: {
    addResendTask(type) {
      this.$emit("handleProcessNode", type);
    },
  },
};
</script>
<style>
.menu_items {
  position: relative;
}

.menus_icon {
  width: 1.3rem;
  height: 1.3rem;
  border-radius: 1.3rem;
  background-color: #ec4079c2;
  display: flex;
  justify-content: center;
  align-items: center;
}

.wf_menu_icons {
  font-size: 12px !important;
}

.menu_10 {
  position: absolute;
  bottom: -80px;
  left: -50px;
  /* transform: translate(-30%); */
}
.menu_11 {
  position: absolute;
  bottom: -120px;
  left: 60%;
  transform: translate(-30%);
}
.menu_12 {
  position: absolute;
  bottom: -80px;
  left: 40px;
  /* transform: translate(-30%); */
}
.ta-tooltip {
  font-size: 8px !important;
}
</style>
