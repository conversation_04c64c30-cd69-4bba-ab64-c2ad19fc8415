<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2 bg-hover" size="35" variant="elevated">
            <v-icon color="primary" size="20">fas fa-file-alt</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              {{ landedFormName }}
            </div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-btn
            @click="$emit('open-edit-form')"
            size="small"
            color="primary"
            variant="elevated"
            rounded="lg"
            v-if="accessRights.update"
            >Edit</v-btn
          >
          <v-icon class="mx-1" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <div
        :style="
          isMobileView
            ? 'height: calc(100vh - 400px); overflow: scroll'
            : 'height: auto'
        "
      >
        <v-card-text>
          <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
            <v-col
              v-if="
                labelList[314]?.Field_Visiblity?.toLowerCase() === 'yes' ||
                entomoIntegrationEnabled
              "
              cols="12"
              sm="6"
              lg="6"
              :class="isMobileView ? ' ml-4' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ labelList[314]?.Field_Alias || "Department Code" }}
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Department_Code) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Department Name</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Department_Name) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                Department Header
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Organization_Type) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                Parent Department
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Parent_Name) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Bonus Type</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Bonus_Type) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Status</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Department_Status) }}
              </p>
            </v-col>
            <v-col cols="12" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Description</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Description) }}
              </p>
            </v-col>
          </v-row>
        </v-card-text>
      </div>
      <div class="mt-5">
        <v-row class="px-sm-8 px-md-10 mt-2 mb-2">
          <v-col v-if="moreDetailsList.length > 0" cols="12">
            <MoreDetails
              :more-details-list="moreDetailsList"
              :open-close-card="openMoreDetails"
              @on-open-close="openMoreDetails = $event"
            ></MoreDetails> </v-col
        ></v-row>
      </div>
    </v-card>
  </div>
</template>

<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "ViewDepartmentHierarchy",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    selectedFormData: {},
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
  },

  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);
        this.prefillMoreDetails();
      },
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    closeEditForm() {
      this.$emit("close-form");
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.selectedItem.Added_On),
        addedByName = this.selectedItem.Added_By_Name,
        updatedByName = this.selectedItem.Updated_By_Name,
        updatedOn = this.convertUTCToLocal(this.selectedItem.Updated_On);
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>
