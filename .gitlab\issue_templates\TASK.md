DEVELOPMENT TEAM :

1.  Requirement:

2.  Existing System :

3.  Proposed System :

4.  [ ] I prepared the MySQL query and attached here :

          [ ] I updated the access rights query when the new form is added or removed :

          [ ] I updated the dependent form query when the new form is added or removed for plan details :

5.  [ ] I checked the impact on payslip:

6.  [ ] I checked the impact on existing reports:

7.  [ ] I checked the impact on data import modules:

8.  [ ] I checked the impact on data import template files:

8.  [ ] I checked the impacted module other than above forms/steps:

10. [ ] I checked the impact on access flow :  

       * Employee level access change :
       * Manager  level access change :
       * Admin    level access change :

11.  [ ] I checked the design in the user interface :

      * Form names: 
      * Fields names :
      * Grid header names:
      * Search :
      * Filter :
      * Sorting :
      * Message :
      * Tooltip :
      * Validations (Min, Max, Alphanumeric):

12.  [ ] I prepared the wireFrame and attached it here :

13.  [ ] I prepared the mind map and attached it here :

14.  [ ] I updated/uploaded the reference link and other requirement files :

15.  [ ] I have done the code level analysis and updated the estimation :

16.  [ ] I added/updated all the id's related for testing  :

17.  [ ] I checked table alias name/table name are  in the same case(Aurora DB is case sensitive) :

18.  [ ] I have handled the session expiration for all the ajax/API requests :

19.  [ ] I have set the lock flag in form edit and reset it during the login and the page refresh :

20.  [ ] I run the developer test before sending the code to review :

21.  [ ] I provided the steps/demo for this ticket : 

TESTING TEAM :

1. [ ]  I analyze this task and its impacts

2. [ ]  I validate the analysis and prepare the test cases(#Description Mandatory)

3. [ ]  I update the testCases in ticket and respective document(with ticket number):

4. [ ]  This test cases reviewed by the lead 

5. [ ]  I test all the test cases which you documented

6. [ ]  I test the dependent changes of this task

7. [ ]  I test the application in all(Chrome,Edge,FireFox,IE,Safari) the browser

8. [ ]  The functionality covers the requirement

9. [ ]  I closed this issue after completed the testing

10. [ ] I tested the task for test suit(n-1) preparation.