<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedDesignation"
              color="primary"
              :items="listDesignation"
              label="Designation"
              variant="solo"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedDepartment"
              color="primary"
              :items="listDepartments"
              label="Department"
              variant="solo"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedLocations"
              color="primary"
              :items="listLocation"
              label="Location"
              variant="solo"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedTypes"
              color="primary"
              :items="listtypes"
              label="Employee Type"
              variant="solo"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-menu
              v-model="resignationMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="formattedResignation"
                  v-model="formattedResignation"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  density="compact"
                  label="Date Of Resignation"
                  v-bind="props"
                  :clearable="true"
                  variant="solo"
                />
              </template>
              <v-date-picker v-model="selectedResignationDate"></v-date-picker>
            </v-menu>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-menu
              v-model="exitDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="formattedExitDate"
                  v-model="formattedExitDate"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  density="compact"
                  label="Date Of Exit"
                  v-bind="props"
                  :clearable="true"
                  variant="solo"
                />
              </template>
              <v-date-picker v-model="selectedExitDate"></v-date-picker>
            </v-menu>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="listStatus"
              label="Status"
              variant="solo"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "ExitManagementFilter",
  data: () => ({
    openFormFilter: false,
    // Date Picker
    resignationMenu: false,
    exitDateMenu: false,
    formattedResignation: "",
    formattedExitDate: "",
    selectedResignationDate: null,
    selectedExitDate: null,
    selectedDesignation: [],
    selectedDepartment: [],
    selectedStatus: [],
    selectedLocations: [],
    selectedTypes: [],
  }),

  props: {
    itemList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    listDesignation() {
      const filteredItemList = this.itemList.filter(
        (item) =>
          item.employeeDesignationName &&
          item.employeeDesignationName.trim() !== ""
      );
      // Use Set to filter out duplicate employeeDesignationName values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.employeeDesignationName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    listDepartments() {
      const filteredItemList = this.itemList.filter(
        (item) =>
          item.employeeDepartmentName &&
          item.employeeDepartmentName.trim() !== ""
      );
      // Use Set to filter out duplicate employeeDepartmentName values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.employeeDepartmentName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    listStatus() {
      const filteredItemList = this.itemList.filter(
        (item) => item.resignationStatus && item.resignationStatus.trim() !== ""
      );
      // Use Set to filter out duplicate resignationStatus values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.resignationStatus)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    listLocation() {
      const filteredItemList = this.itemList.filter(
        (item) => item.locationName && item.locationName.trim() !== ""
      );
      // Use Set to filter out duplicate locationName values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.locationName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    listtypes() {
      const filteredItemList = this.itemList.filter(
        (item) => item.employeeType && item.employeeType.trim() !== ""
      );
      // Use Set to filter out duplicate employeeType values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.employeeType)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
  },
  watch: {
    selectedResignationDate(val) {
      if (val) {
        this.resignationMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedResignation = dateValue;
      }
    },
    selectedExitDate(val) {
      if (val) {
        this.exitDateMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedExitDate = dateValue;
      }
    },
    formattedResignation(val) {
      if (!val) {
        this.selectedResignationDate = null;
      }
    },
    formattedExitDate(val) {
      if (!val) {
        this.selectedExitDate = null;
      }
    },
  },
  mounted() {},
  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        selectedDesignation: this.selectedDesignation,
        selectedDepartment: this.selectedDepartment,
        selectedResignationDate: this.selectedResignationDate
          ? moment(this.selectedResignationDate).format("YYYY-MM-DD")
          : null,
        selectedExitDate: this.selectedExitDate
          ? moment(this.selectedExitDate).format("YYYY-MM-DD")
          : null,
        selectedStatus: this.selectedStatus,
        selectedLocations: this.selectedLocations,
        selectedTypes: this.selectedTypes,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedDesignation = [];
      this.selectedDepartment = [];
      this.formattedResignation = "";
      this.formattedExitDate = "";
      this.selectedStatus = [];
      this.selectedLocations = [];
      this.selectedTypes = [];
    },
  },
};
</script>
