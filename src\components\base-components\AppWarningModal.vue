<template>
  <v-dialog
    v-model="openWarningModal"
    width="550px"
    :persistent="persistModal"
    @click:outside="persistModal ? '' : closeModal(true)"
  >
    <v-card class="rounded-lg" :min-height="windowWidth > 700 ? 350 : ''">
      <div class="d-flex justify-end">
        <v-icon
          color="primary"
          class="pr-4 pt-4 font-weight-bold"
          @click="closeModal(true)"
          >fas fa-times</v-icon
        >
      </div>
      <v-card-text class="d-flex align-center justify-center flex-column">
        <div v-if="imgUrl">
          <img
            style="width: 70%; height: auto"
            :src="imgUrlFullPath"
            alt="form exit"
          />
        </div>
        <div v-else-if="iconClass || iconName" class="text-center">
          <i v-if="iconClass" :class="iconClass"></i>
          <v-icon v-else-if="iconName" :size="iconSize" :color="iconColor">{{
            iconName
          }}</v-icon>
          <br />
          <br />
        </div>
        <div
          v-if="confirmationHeading"
          class="justify-center font-weight-bold modal-heading text-primary text-center"
        >
          {{ confirmationHeading }}
          <br />
        </div>
        <div
          v-if="confirmationText"
          class="justify-center text-body-1 text-primary text-center px-6 pt-2"
        >
          {{ confirmationText }}
          <br />
        </div>
        <!-- extra message to display contents in popup -->
        <div
          v-if="confirmationSubText"
          class="justify-center text-body-1 text-grey text-center px-6 py-2"
        >
          {{ confirmationSubText }}
        </div>
        <div v-if="notes" class="text-grey text-center px-6 py-1">
          {{ notes }}
        </div>
        <slot name="warningModalContent"></slot>
      </v-card-text>
      <div v-if="closeButtonText || acceptButtonText" class="text-center pb-10">
        <v-btn
          v-if="closeButtonText"
          rounded="lg"
          variant="text"
          color="primary"
          elevation="4"
          class="mr-2"
          @click="closeModal()"
        >
          {{ closeButtonText }}
        </v-btn>
        <v-btn
          v-if="acceptButtonText"
          :disabled="acceptButtonDisable"
          rounded="lg"
          color="primary"
          variant="elevated"
          @click="submitClick()"
        >
          {{ acceptButtonText }}
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: "AppWarningModal",
  props: {
    openModal: {
      type: Boolean,
      required: true,
    },
    iconColor: {
      type: String,
      default: "red-lighten-1",
    },
    iconName: {
      type: String,
      default: "",
    },
    iconSize: {
      type: String,
      default: "100",
    },
    imgUrl: {
      type: String,
      default: "",
    },
    iconClass: {
      type: String,
      default: "",
    },
    confirmationHeading: {
      type: String,
      default: "Are you sure to delete the selected record(s)?",
    },
    confirmationText: {
      type: String,
      default: "",
    },
    confirmationSubText: {
      type: String,
      default: "",
    },
    notes: {
      type: String,
      default: "",
    },
    closeButtonText: {
      type: String,
      default: "No",
    },
    acceptButtonText: {
      type: String,
      default: "Yes",
    },
    acceptButtonDisable: {
      type: Boolean,
      default: false,
    },
    persistModal: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      isMounted: false,
      openWarningModal: false,
    };
  },
  computed: {
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    //Load webp if browser support webp image format or load png
    imgUrlFullPath() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.imgUrl + ".webp");
      else return require("@/assets/images/" + this.imgUrl + ".png");
    },
  },
  watch: {
    openModal(val) {
      this.openWarningModal = val;
    },
  },
  mounted() {
    this.openWarningModal = this.openModal;
  },
  methods: {
    // function called when click yes
    submitClick() {
      this.$emit("accept-modal");
    },
    closeModal(onlyCloseModal = false) {
      this.openWarningModal = false;
      this.$emit("close-warning-modal", onlyCloseModal);
    },
  },
};
</script>

<style lang="scss" scoped>
.v-text-field--outlined fieldset {
  top: -25px !important;
}
.vqb-group-heading {
  color: var(--v-secondary-base) !important;
  font-size: 18px !important;
}
.modal-heading {
  font-size: 1.3em;
}
</style>
