<template>
  <div v-if="itemList.length > 0">
    <v-data-table
      v-model="selectedEmpRecords"
      :headers="tableHeaders"
      :items="itemList"
      :items-per-page="50"
      :show-select="false"
      :sort-by="[{ key: 'employeeId', order: 'asc' }]"
      :height="
        $store.getters.getTableHeightBasedOnScreenSize(
          isSmallTable ? 220 : 290,
          itemList,
          true
        )
      "
      :items-per-page-options="[
        { value: 50, title: '50' },
        { value: 100, title: '100' },
        { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
      ]"
      fixed-header
      item-value="employeeId"
    >
      <!-- <template v-slot:[`header.data-table-select`]="{ selectAll }">
        <v-checkbox-btn
          v-model="selectAllBox"
          color="primary"
          false-icon="far fa-circle"
          true-icon="fas fa-check-circle"
          indeterminate-icon="fas fa-minus-circle"
          class="mt-1"
          @change="selectAll(selectAllBox)"
        ></v-checkbox-btn>
      </template> -->
      <template v-slot:item="{ item }">
        <tr
          @click="onSelectItem(item)"
          class="data-table-tr bg-white cursor-pointer"
          :class="
            isMobileView ? 'v-data-table__mobile-table-row ma-0 mt-2' : ''
          "
        >
          <!-- <td v-if="isSuperAdmin" :class="isMobileView ? 'mt-3 mb-n5' : ''">
            <v-checkbox-btn
              v-model="item.isSelected"
              color="primary"
              false-icon="far fa-circle"
              true-icon="fas fa-check-circle"
              class="mt-n2 ml-n2"
              @click.stop="
                {
                }
              "
              @change="checkAllSelected()"
            ></v-checkbox-btn>
          </td> -->
          <td
            id="mobile-view-td pl-0"
            :style="
              item.eligibleLeaveId === selectedEmployeeId && isSmallTable
                ? 'padding-left: 0px'
                : ''
            "
          >
            <div id="mobile-header" class="font-weight-bold mt-2">Employee</div>
            <section
              style="max-width: 200px"
              class="text-truncate d-flex align-center pl-0"
            >
              <div
                v-if="
                  item.eligibleLeaveId === selectedEmployeeId && isSmallTable
                "
                class="data-table-side-border selected-item-border-color"
              ></div>
              <span class="text-primary text-body-2 font-weight-medium">
                <v-tooltip :text="item.employeeName" location="bottom">
                  <template v-slot:activator="{ props }">
                    <span
                      v-bind="
                        item.employeeName && item.employeeName.length > 20
                          ? props
                          : ''
                      "
                      >{{ item.employeeName }}</span
                    >
                  </template>
                </v-tooltip>
                <v-tooltip
                  :text="String(item.userDefinedEmpId)"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      v-if="item.employeeId"
                      v-bind="
                        item.employeeId && item.employeeId.length > 20
                          ? props
                          : ''
                      "
                      class="text-grey"
                    >
                      {{ item.userDefinedEmpId }}
                    </div>
                  </template>
                </v-tooltip>
              </span>
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Leave Type
            </div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.leaveType) }}
            </section>
          </td>
          <td id="mobile-view-td" v-if="!isSmallTable">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Current Year Leave Entitlement
            </div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.currentYearTotalEligibleDays) }}
            </section>
          </td>
          <td id="mobile-view-td" v-if="!isSmallTable">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Carry Over Balance
            </div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.lastCOBalance) }}
            </section>
          </td>
          <td id="mobile-view-td" v-if="!isSmallTable">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Leaves Taken
            </div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.leavesTaken) }}
            </section>
          </td>
          <td id="mobile-view-td" v-if="!isSmallTable">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Pending Approval
            </div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.totalAppliedLeaveDays) }}
            </section>
          </td>
          <td id="mobile-view-td" v-if="!isSmallTable">
            <div id="mobile-header" class="font-weight-bold mt-2">Action</div>
            <section class="d-flex align-center justify-space-between">
              <ActionMenu
                :access-rights="formAccess"
                :actions="['History']"
                @selected-action="handleActions($event, item)"
              ></ActionMenu>
              <!-- <v-btn
                rounded="lg"
                variant="outlined"
                size="small"
                color="primary"
                class="ml-2 mt-n1"
                @click="openHistory()"
                >History</v-btn
              > -->
            </section>
          </td>
        </tr>
      </template>
    </v-data-table>
  </div>

  <AppFetchErrorScreen
    v-else
    key="no-results-screen"
    main-title="There are no leave override matched for the selected employee."
    image-name="common/no-records"
  >
    <template #contentSlot>
      <div style="max-width: 80%">
        <v-row class="rounded-lg pa-5 mb-4">
          <v-col cols="12" class="d-flex align-center justify-center mb-4">
            <v-btn
              color="primary"
              variant="elevated"
              class="ml-4 mt-1"
              rounded="lg"
              :size="windowWidth <= 960 ? 'small' : 'default'"
              @click="$emit('refetch-list')"
            >
              Refresh
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </template>
  </AppFetchErrorScreen>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineComponent } from "vue";
import { checkNullValue, getCustomFieldName } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default defineComponent({
  name: "LeaveOverride",

  emits: ["on-select-item", "show-history", "refetch-list"],
  mixins: [FileExportMixin],

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    originalList: {
      type: Array,
      required: true,
      default: () => [],
    },
    formAccess: {
      type: Object,
      required: true,
    },
    isPayrollAdmin: {
      type: Boolean,
      default: false,
    },
    isSmallTable: {
      type: Boolean,
      required: true,
    },
  },

  components: {
    ActionMenu,
  },

  data: () => ({
    // list
    itemList: [],
    openMoreMenu: false,
    openExportMenu: false,
    selectAllBox: false,
    selectedEmpRecords: [],
    // role
    selectedEmpIds: [],
    selectedRoleId: null,
    selectedRole: {},
    rolesList: [],
    showRolesAssociation: false,
    openRolesSelectionPopup: false,
    // delete/warning
    openWarningModal: false,
    selectedActionItem: null,
    selectedEmployee: "",
    selectedEmployeeId: "",

    // clone
    openCloneModal: false,

    resetFilterCount: 0,
    applyFilterCount: 0,
    isLoading: false,
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },
    dojHeader() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      return "Date of Join (" + orgDateFormat + ")";
    },
    tableHeaders() {
      let headers = [];
      if (this.isSmallTable) {
        headers = [
          {
            title: "Employee",
            key: "employeeName",
          },
          {
            title: "Leave Type",
            key: "leaveType",
          },
        ];
        return headers;
      } else {
        headers = [
          {
            title: "Employee",
            key: "employeeName",
          },
          {
            title: "Leave Type",
            key: "leaveType",
          },
          {
            title: "Current Year Leave Entitlement",
            key: "currentYearLeaveEntitlement",
          },
          { title: "Carry Over Balance", key: "carryOverBalance" },
          { title: "Leaves Taken", key: "leavesTaken" },
          { title: "Pending Approval", key: "pendingApproval" },
          { title: "Action", key: "action" },
        ];
        return headers;
      }
    },
    activeEmployees() {
      let empList = this.originalList.filter(
        (el) => el.empStatusAll === "Active"
      );
      return empList.length;
    },
    draftEmployees() {
      let empList = this.originalList.filter(
        (el) => el.empStatusAll === "Draft"
      );
      return empList.length;
    },
    inactiveEmployees() {
      let empList = this.originalList.filter(
        (el) => el.empStatusAll === "InActive"
      );
      return empList.length;
    },
    moreActions() {
      let actions = [
        {
          key: "Export Leave Override",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return true;
      } else return false;
    },
    selectedItems() {
      let selected = this.itemList.filter((el) => el.isSelected === true);
      return selected && selected.length > 0 ? selected : [];
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.items.length) {
      this.itemList = this.items;
      this.itemList = this.itemList.map((item) => ({
        ...item,
        isSelected: false,
      }));
    }
  },

  watch: {
    selectedEmpRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.employeeId)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
      }
    },
    items(val) {
      this.itemList = val;
    },
  },

  methods: {
    checkNullValue,
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    onSelectItem(item) {
      this.selectedEmployeeId = item.eligibleLeaveId;
      this.$emit("on-select-item", item);
    },
    onMoreAction(actionType) {
      if (actionType === "Export employees") {
        mixpanel.track("MyTeam-export-employees-click");
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },

    onAssociateSuccess() {
      this.showRolesAssociation = false;
      this.selectAllBox = false;
      this.itemList = this.itemList.map((item) => {
        item["isSelected"] = false;
        return item;
      });
      mixpanel.track("MyTeam-role-assign-success");
    },
    handleActions(menu, selectedData) {
      if (menu === "History") {
        this.$emit("show-history", selectedData);
      }
    },

    exportReportFile() {
      let exportHeaders = [
        {
          header: "Employee Id",
          key: "userDefinedEmpId",
        },
        {
          header: "First Name",
          key: "empNameWithSalutation",
        },
        {
          header: "Middle Name",
          key: "employeeMiddleName",
        },
        {
          header: "Last Name",
          key: "employeeLastName",
        },
        {
          header: "Biometric Integration Id",
          key: "externalEmpId",
        },
        { header: "Date of Birth", key: "dateOfBirth" },
        { header: "Gender", key: "gender" },
        { header: "Marital Status", key: "maritalStatusName" },
        { header: "Blood Group", key: "bloodGroup" },
        { header: "Personal Email", key: "personalEmail" },
        { header: "Mobile Number", key: "mobileNo" },
        { header: "Work Email", key: "Emp_Email" },
        { header: this.dojHeader, key: "dateOfJoin" },
        { header: "Is Manager", key: "isManager" },
        {
          header: "Designation",
          key: "designationName",
        },
        { header: "Department", key: "departmentName" },
        { header: "Location", key: "locationName" },
        { header: "Business Unit / Cost Center", key: "businessUnitName" },
      ];
      if (this.fieldForce) {
        exportHeaders.push({
          header: getCustomFieldName(115, "Service Provider"),
          key: "Service_Provider_Name",
        });
      }
      exportHeaders.push(
        { header: "Probation Date", key: "probationDateFormatted" },
        { header: "Confirmation Date", key: "confirmationDate" },
        { header: "Allow User Signin", key: "allowUserSignin" },
        { header: "Roles", key: "rolesName" },
        { header: "Status", key: "empStatusAll" }
      );

      let exportOptions = {
        fileExportData: this.itemList,
        fileName: "My Team Members",
        sheetName: "My Team Members",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
      mixpanel.track("MyTeam-employees-exported");
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
<style>
.data-table-side-border {
  /* margin-left: -1.1em; */
  margin-right: 10px;
  min-height: 3.5em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
.selected-item-border-color {
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
