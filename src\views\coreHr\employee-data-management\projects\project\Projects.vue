<template>
  <div>
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else-if="isErrorInList">
      <AppFetchErrorScreen
        image-name="common/human-error-image"
        :content="errorContent"
        icon-name="fas fa-redo-alt"
        button-text="Retry"
        @button-click="refetchProjectList()"
      >
      </AppFetchErrorScreen>
    </div>
    <div v-else class="pt-4">
      <v-row>
        <v-col :cols="isAddViewEditFormOpened && windowWidth >= 1264 ? 5 : 12">
          <div>
            <div
              v-if="!projectsListBackup || projectsListBackup.length === 0"
              class="mt-12"
            >
              <AppFetchErrorScreen
                key="no-results-screen"
                icon-name="fas fa-plus"
                :main-title="`There are no ${projectLabelSmallCase}s found`"
                image-name="common/no-records"
                :button-text="
                  formAccess.add && !showAddForm ? 'Add ' + projectLabel : ''
                "
                @button-click="
                  formAccess.add && !showAddForm ? onOpenAddForm() : ''
                "
              >
              </AppFetchErrorScreen>
            </div>
            <AppFetchErrorScreen
              v-else-if="!projectsList || projectsList.length === 0"
              key="no-results-screen"
              :main-title="`There are no ${projectLabelSmallCase}s matched for the selected filters/searches.`"
              image-name="common/no-records"
            >
              <template #contentSlot>
                <div style="max-width: 80%">
                  <v-row class="rounded-lg pa-5 mb-4">
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <v-btn
                        color="primary"
                        variant="elevated"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="windowWidth <= 960 ? 'small' : 'default'"
                        @click="resetFilter()"
                      >
                        Reset Filter/Search
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>
            <ListProjects
              v-else
              :items="projectsList"
              :is-small-table="isAddViewEditFormOpened"
              :form-access="formAccess"
              :project-coverage="projectCoverage"
              @open-add-form="onOpenAddForm()"
              @delete-success="refetchProjectList()"
              @on-grid-click="onOpenViewForm($event)"
              @refetch-data="refetchProjectList()"
              @on-clone="onClone($event)"
            ></ListProjects>
          </div>
        </v-col>
        <v-col v-if="isAddViewEditFormOpened && windowWidth >= 1264" cols="7">
          <ViewProjects
            v-if="showViewForm"
            :form-access="formAccess"
            :project-details="selectedProject"
            :project-coverage="projectCoverage"
            @close-view-form="closeViewForm()"
            @edit-project="onOpenEditForm()"
            @accreditation-retrieved="projectMappedAccreditations = $event"
            @project-closed="closeViewAndRefetchProjects()"
          ></ViewProjects>
          <AddEditProjects
            v-else
            :is-edit="showEditForm"
            :project-details="selectedProject"
            :openCloneModal="openCloneModal"
            :project-coverage="projectCoverage"
            :project-mapped-accreditations="projectMappedAccreditations"
            :form-access="formAccess"
            @close-add-edit-form="closeAddEditForm($event)"
            @add-update-success="onAddUpdateSuccess()"
            @refetch-projects="closeAddAndRefetchProjects()"
          >
          </AddEditProjects>
        </v-col>
      </v-row>
    </div>
    <v-dialog
      v-if="openFormInModal"
      :model-value="openFormInModal"
      width="900"
      @click:outside="closeAllForms()"
    >
      <ViewProjects
        v-if="showViewForm"
        :form-access="formAccess"
        :project-details="selectedProject"
        :project-coverage="projectCoverage"
        @close-view-form="closeViewForm()"
        @edit-project="onOpenEditForm()"
        @accreditation-retrieved="projectMappedAccreditations = $event"
        @project-closed="closeViewAndRefetchProjects()"
      ></ViewProjects>
      <AddEditProjects
        v-else
        :is-edit="showEditForm"
        :project-details="selectedProject"
        :openCloneModal="openCloneModal"
        :project-coverage="projectCoverage"
        :project-mapped-accreditations="projectMappedAccreditations"
        :form-access="formAccess"
        @close-add-edit-form="closeAddEditForm($event)"
        @add-update-success="onAddUpdateSuccess()"
        @refetch-projects="closeAddAndRefetchProjects()"
      >
      </AddEditProjects>
    </v-dialog>
    <v-dialog
      v-model="openProjectSelectionPopup"
      @click:outside="closeProjectCloneDialog()"
      max-width="500px"
    >
      <v-card min-height="300" color="primary" class="rounded-lg">
        <v-form ref="cloneForm">
          <v-card-title>
            <div class="d-flex justify-end" style="width: 100%">
              <v-icon
                color="white"
                size="20"
                class="mr-2 mt-2"
                @click="closeProjectCloneDialog()"
                >fas fa-times</v-icon
              >
            </div>
          </v-card-title>
          <div class="mt-2 d-flex justify-center">
            <v-text-field
              v-model="cloneProjectName"
              color="primary"
              style="max-width: 60%"
              maxlength="50"
              variant="solo"
              :rules="[
                required('New ' + projectLabel + ' Name', cloneProjectName),
                validateWithRulesAndReturnMessages(
                  cloneProjectName,
                  'projectName',
                  'New ' + projectLabel + ' Name'
                ),
              ]"
            >
              <template v-slot:label>
                <span>New {{ projectLabel }} Name</span>
                <span class="ml-1" style="color: red">*</span>
              </template>
              <template v-slot:message="{ message }">
                <span style="color: white">{{ message }}</span>
              </template>
            </v-text-field>
          </div>
          <v-card-text class="d-flex justify-center">
            <CustomSelect
              :items="[projectLabel + ' only', projectLabel + ' and activity']"
              :itemSelected="selectedProjectCloneOption"
              style="max-width: 300px; height: 50px"
              @selected-item="onSelectCloneOption($event)"
              :isRequired="true"
              label="What do you like to clone?"
              :rules="[required('This field', selectedProjectCloneOption)]"
              messageColor="white"
            ></CustomSelect>
          </v-card-text>
          <v-card-text class="d-flex justify-center">
            <v-btn color="secondary" rounded="lg" @click="validateCloneForm()">
              <v-icon color="primary" class="mr-1">fas fa-clone</v-icon>
              Clone
            </v-btn>
          </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineAsyncComponent } from "vue";
import ListProjects from "./ListProjects.vue";
import {
  LIST_PROJECTS,
  CLONE_PROJECTS,
} from "@/graphql/corehr/projectsQueries";
import { getErrorCodes } from "@/helper";
const ViewProjects = defineAsyncComponent(() => import("./ViewProjects.vue"));
const AddEditProjects = defineAsyncComponent(() =>
  import("./AddEditProjects.vue")
);
import validationRules from "@/mixins/validationRules";

import CustomSelect from "@/components/custom-components/CustomSelect.vue";
export default {
  name: "ProjectsMain",
  mixins: [validationRules],

  components: {
    ListProjects,
    ViewProjects,
    AddEditProjects,
    CustomSelect,
  },

  props: {
    formAccess: {
      type: Object,
      required: true,
    },
    applyFilterCount: {
      type: Boolean,
      required: true,
    },
    resetFilterCount: {
      type: Boolean,
      required: true,
    },
    projectsFilteredList: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      // data
      projectsList: [],
      projectsListBackup: [],
      selectedProject: {},
      projectCoverage: "",
      projectMappedAccreditations: [],
      allEmployees: [],
      // loading/error
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      // show hide
      showAddForm: false,
      showEditForm: false,
      showViewForm: false,
      openProjectSelectionPopup: false,
      selectedProjectCloneOption: null,
      validationMessages: [],
      showValidationAlert: false,
      cloneProject: {},
      selectedCloneProject: {},
      isLoading: false,
      openCloneModal: false,
      cloneProjectName: "",
    };
  },

  computed: {
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isAddViewEditFormOpened() {
      return this.showViewForm || this.showAddForm || this.showEditForm;
    },
    openFormInModal() {
      if (this.isAddViewEditFormOpened && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
    projectLabel() {
      return this.$store.state.projectLabel;
    },
    projectLabelSmallCase() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel ? pLabel.toLowerCase() : pLabel;
    },
  },

  watch: {
    applyFilterCount(count) {
      if (count > 0) {
        this.projectsList = this.projectsFilteredList;
      }
    },
    resetFilterCount(count) {
      if (count > 0) {
        this.projectsList = this.projectsListBackup;
      }
    },
  },

  mounted() {
    this.listProjects();
  },

  methods: {
    closeProjectCloneDialog() {
      this.selectedProjectCloneOption = null;
      this.cloneProjectName = "";
      this.openProjectSelectionPopup = false;
    },
    onClone(item) {
      this.openProjectSelectionPopup = true;
      this.selectedCloneProject = item;
    },
    async validateCloneForm() {
      let isFormValid = await this.$refs.cloneForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.cloneNewProject();
      }
    },
    onSelectCloneOption(value) {
      this.selectedProjectCloneOption = value;
    },
    cloneNewProject() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: CLONE_PROJECTS,
            variables: {
              projectId: vm.selectedCloneProject["projectId"]
                ? parseInt(vm.selectedCloneProject["projectId"])
                : 0,
              cloneOption: vm.selectedProjectCloneOption
                ? vm.selectedProjectCloneOption
                : "",
              projectName: vm.cloneProjectName,
            },
            client: "apolloClientJ",
          })
          .then((response) => {
            if (response.data && response.data.cloneProject) {
              vm.cloneProject = response.data.cloneProject.projectDetails;
              vm.selectedProject = {
                projectId: vm.cloneProject["Project_Id"],
                projectName: vm.cloneProject["Project_Name"],
                description: vm.cloneProject["Description"],
                clientName: vm.cloneProject["Client_Name"],
                locationId: vm.cloneProject["Location_Id"],
                managerId: vm.cloneProject["Manager_Id"],
                employeeId:
                  vm.cloneProject["Employee_Id"] &&
                  vm.cloneProject["Employee_Id"].length > 0
                    ? vm.cloneProject["Employee_Id"].join(",")
                    : "",
                customGroupId:
                  vm.cloneProject["Custom_Group_Id"] &&
                  vm.cloneProject["Custom_Group_Id"].length > 0
                    ? vm.cloneProject["Custom_Group_Id"][0]
                    : vm.cloneProject["Custom_Group_Id"],
                accreditationId: vm.cloneProject["Accreditation_Id"],
                Status: vm.cloneProject["Status"],
              };
              vm.isLoading = false;
              vm.openProjectSelectionPopup = false;
              this.openCloneModal = true;
              vm.selectedProjectCloneOption = "";
              vm.cloneProjectName = "";
              vm.onOpenEditForm();
            }
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: this.projectLabel + " cloned successfully.",
            };
            vm.showAlert(snackbarData);
          })
          .catch((error) => {
            vm.handleError(error);
          });
      } catch {
        vm.handleError();
      }
    },
    handleError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "cloning",
          form: this.projectLabelSmallCase,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    onOpenViewForm(selectedItem) {
      this.selectedProject = selectedItem;
      this.showAddForm = false;
      this.showEditForm = false;
      this.showViewForm = true;
    },

    closeViewForm() {
      this.selectedProject = {};
      this.showViewForm = false;
      this.openCloneModal = false;
    },

    closeViewAndRefetchProjects() {
      this.closeViewForm();
      this.refetchProjectList();
    },

    closeAddAndRefetchProjects() {
      this.closeAddEditForm();
      this.refetchProjectList();
    },

    onOpenAddForm() {
      this.showEditForm = false;
      this.showViewForm = false;
      this.showAddForm = true;
    },

    closeAddEditForm(val) {
      this.showAddForm = false;
      this.showEditForm = false;
      this.openCloneModal = false;
      this.selectedProjectCloneOption = "";
      if (val) {
        this.refetchProjectList();
      }
    },

    onAddUpdateSuccess() {
      this.closeAddEditForm();
      this.refetchProjectList();
    },

    onOpenEditForm() {
      this.showViewForm = false;
      this.showAddForm = false;
      this.showEditForm = true;
    },

    closeAllForms() {
      this.closeViewForm();
      this.closeAddEditForm();
    },

    resetFilter() {
      this.projectsList = this.projectsListBackup;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$emit("reset-project-filter");
    },

    listProjects() {
      this.listLoading = true;
      this.$apollo
        .query({
          query: LIST_PROJECTS,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data) {
            const { projectDetails, projectCoverage } =
              res.data.listProjectDetails;
            const projectArray =
              projectDetails && projectDetails.length > 0 ? projectDetails : [];
            this.projectsList = projectArray;
            this.projectsListBackup = projectArray;
            this.projectCoverage = projectCoverage;
            this.$emit("projects-retrieved", projectArray);
          }
          this.listLoading = false;
        })
        .catch((err) => {
          this.listLoading = false;
          this.handleListError(err);
        });
    },

    refetchProjectList() {
      this.isErrorInList = false;
      this.listProjects();
    },

    // handle list projects error from BE
    handleListError(err) {
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          // technical errors
          case "_DB0000":
            this.errorContent =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0100":
            this.errorContent = `Sorry, you don't have access rights to view the ${this.projectLabelSmallCase} details. Please contact the HR administrator`;
            break;
          case "CHR0005": // Error while retrieving the project details
            this.errorContent = `Something went wrong while retrieving the ${this.projectLabelSmallCase} details. Please try after some time.`;
            break;
          case "CHR0006": // Error while retrieving the custom group associated to the project.
            this.errorContent = `Something went wrong while retrieving the custom group associated to the ${this.projectLabelSmallCase} details. Please try after some time.`;
            break;
          case "CHR0007": // Error while retrieving the project coverage settings.
          case "CHR0004": // Project coverage settings is not valid.
          case "CHR0003": // Project coverage settings is not valid.
          case "CHR0002": // Project coverage settings does not exist.
            this.errorContent = `Something went wrong while retrieving the ${this.projectLabelSmallCase} coverage settings. Please try after some time.`;
            break;
          // functional errors
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0002": // Error while checking the employee access rights
          case "_UH0001": // unhandled error
          case "CGH0001": // Error while processing the request to list the project details.
          default:
            this.errorContent = `Something went wrong while fetching the ${this.projectLabelSmallCase}s. If you continue to see this issue, please contact the platform administrator.`;
            break;
        }
      } else {
        this.errorContent = `Something went wrong while fetching the ${this.projectLabelSmallCase}s. Please try after some time.`;
      }
      this.isErrorInList = true;
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style>
.projects-container {
  padding: 4em 2em 0em 3em;
}
.v-dialog {
  box-shadow: none;
}
@media screen and (max-width: 805px) {
  .projects-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
