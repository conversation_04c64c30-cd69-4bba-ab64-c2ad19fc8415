<template>
  <v-sheet class="pa-2 pl-4 d-flex justify-space-between">
    <div class="text-primary font-weight-bold text-h6 text-truncate">
      {{ checkNullValue(workflowName) }}
    </div>
  </v-sheet>
  <VueFlow
    v-model:nodes="nodes"
    v-model:edges="edges"
    :min-zoom="1"
    :max-zoom="2"
    fit-view-on-init
    @edge-update="onEdgeUpdate"
    @connect="onConnect"
    connectionRadius="10"
    v-if="!loading"
    :style="'overflow: scroll; height: ' + $store.getters.getTableHeight(170)"
  >
    <template #node-start_node="{ id, data, sourcePosition, targetPosition }">
      <AddStartNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
      />
    </template>
    <template #node-add_process="{ id, data, sourcePosition, targetPosition }">
      <ProcessNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
      />
    </template>
    <template #node-goto_node="{ id, data, sourcePosition, targetPosition }">
      <GotoNode
        :nodesData="nodes"
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
        @gotoNodes="(e) => handleGotoNode(e)"
      />
    </template>
    <template #node-call_node="{ id, data, sourcePosition, targetPosition }">
      <AddCallActivityNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
        @updateNodeTitle="
          (e) => {
            handleUpdateTitle(e);
          }
        "
      />
    </template>
    <template #node-service_node="{ id, data, sourcePosition, targetPosition }">
      <AddServiceNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        :serviceAPIList="serviceAPIList"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
        @handleServiceAPI="(e) => handleServiceAPI(e)"
        @updateNodeTitle="
          (e) => {
            handleUpdateTitle(e);
          }
        "
      />
    </template>
    <template #node-end_node="{ id, data, sourcePosition, targetPosition }">
      <AddEndNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
      />
    </template>
    <template
      #node-exclusive_node="{ id, data, sourcePosition, targetPosition }"
    >
      <AddExclusiveNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
        @openQueryBuilder="(e) => handleExpressionBuilder(e)"
        @updateNodeTitle="
          (e) => {
            handleUpdateTitle(e);
          }
        "
      />
    </template>
    <template
      #node-parallel_node="{ id, data, sourcePosition, targetPosition }"
    >
      <AddParallelNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @handleToParent="handleAddParentNode"
        @deleteNode="handleDeleteNode"
      />
    </template>
    <template
      #node-user_task_node="{ id, data, sourcePosition, targetPosition }"
    >
      <AddUserTaskNode
        :id="id"
        :data="data"
        :selectedFormId="selectedFormId"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
        @selectedUserNodes="(e) => (selectedUserNode = e)"
        @onSubmitFormData="handleSubmitForm"
        :workflowModule="workflowModule"
        @updateNodeTitle="
          (e) => {
            handleUpdateTitle(e);
          }
        "
      />
    </template>
    <template #node-resend_node="{ id, data, sourcePosition, targetPosition }">
      <AddResendNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
      />
    </template>
    <template #node-approve_node="{ id, data, sourcePosition, targetPosition }">
      <AddApproveNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
      />
    </template>
    <template #node-reject_node="{ id, data, sourcePosition, targetPosition }">
      <AddRejectNode
        :id="id"
        :data="data"
        :sourcePosition="sourcePosition"
        :targetPosition="targetPosition"
        @handleToStart="handleAddNode"
        @deleteNode="handleDeleteNode"
      />
    </template>
    <template #connection-line="{ sourceX, sourceY, targetX, targetY }">
      <CustomConnectionLine
        :source-x="sourceX"
        :source-y="sourceY"
        :target-x="targetX"
        :target-y="targetY"
      />
    </template>
    <template #edge-custom="buttonEdgeProps">
      <CustomEdge
        :edgeData="buttonEdgeProps"
        :id="buttonEdgeProps.id"
        :source-x="buttonEdgeProps.sourceX"
        :source-y="buttonEdgeProps.sourceY"
        :target-x="buttonEdgeProps.targetX"
        :target-y="buttonEdgeProps.targetY"
        :source-position="buttonEdgeProps.sourcePosition"
        :target-position="buttonEdgeProps.targetPosition"
        :marker-end="buttonEdgeProps.markerEnd"
        :style="buttonEdgeProps.style"
      />
    </template>
    <Background pattern-color="#000000" bgColor="#e5e9ff" :gap="3" />
  </VueFlow>
  <div class="d-flex justify-space-between px-3 pt-3 pb-2">
    <v-btn
      class="text-none primary mr-3"
      text="Cancel"
      elevation="1"
      rounded="lg"
      variant="outlined"
      @click="openConfirmationPopup = true"
    ></v-btn>
    <v-btn
      class="text-none mr-2"
      color="primary"
      text="Submit"
      variant="elevated"
      rounded="lg"
      @click="handleSubmit()"
    ></v-btn>
  </div>
  <div style="position: relative">
    <transition name="fade" appear>
      <div
        class="modal-overlay"
        v-if="openExpressionModal"
        @click="openExpressionModal = false"
      ></div>
    </transition>
    <transition name="pop" appear>
      <div class="modal" role="dialog" v-if="openExpressionModal">
        <div class="d-flex justify-space-between">
          <v-col cols="6" class="pa-0 pt-2 pl-2">
            <p class="font-weight-bold text-left float-left">
              Expression Builder
            </p>
          </v-col>
          <v-col cols="6" class="d-flex justify-end item-center">
            <v-icon
              color="primary"
              class="pr-4 pt-2 mb-2 font-weight-bold"
              @click="openExpressionModal = false"
              >fas fa-times</v-icon
            >
          </v-col>
        </div>
        <v-divider></v-divider>
        <v-col cols="6" class="pa-0 pt-2 pl-2">
          <v-text-field
            label="Title"
            placeholder="Enter title"
            density="compact"
            variant="solo"
            maxlength="100"
            min-width="100"
            v-model="expressionTitle"
          ></v-text-field>
        </v-col>
        <ExpressionBuilder
          @submitQueryValue="handleQueryValue"
          @onCloseModal="openExpressionModal = false"
          :selectedQueryData="selectedQueryData"
          :expressionTitle="expressionTitle"
          :eventId="eventId"
        />
      </div>
    </transition>
  </div>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this workflow builder?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onCloseBuilder()"
  >
  </AppWarningModal>
</template>

<script>
import "../../../assets/css/customWorkflow.css";
import { Position, VueFlow, useVueFlow } from "@vue-flow/core";
import { Background } from "@vue-flow/background";
import Config from "@/config.js";
import axios from "axios";

import ProcessNode from "./nodes/ProcessNode.vue";
import GotoNode from "./nodes/GotoNode.vue";
import AddCallActivityNode from "./nodes/AddCallActivityNode.vue";
import AddStartNode from "./nodes/AddStartNode.vue";
import AddServiceNode from "./nodes/AddServiceNode.vue";
import AddEndNode from "./nodes/EndNode.vue";
import AddExclusiveNode from "./nodes/AddExclusiveNode.vue";
import AddParallelNode from "./nodes/AddParallelNode.vue";
import AddUserTaskNode from "./nodes/AddUserTaskNode.vue";
import AddResendNode from "./nodes/AddResendNode.vue";
import CustomConnectionLine from "./connection-line/CustomConnectionLine.vue";
import CustomEdge from "./edges/CustomEdges.vue";
import ExpressionBuilder from "./components/expression-builder/ExpressionBuilder.vue";
import AddApproveNode from "./nodes/AddApproveNode.vue";
import AddRejectNode from "./nodes/AddRejectNode.vue";
import { checkNullValue } from "@/helper";
// import { getOrigin } from "@/helper";

export default {
  name: "WorkflowBuilder",
  emits: ["submitWorkflow", "onCloseWorkflow"],
  props: {
    editWorkflow: {
      type: Boolean,
      required: false,
    },
    editFlowData: {
      type: Array,
      required: false,
    },
    eventId: {
      type: String,
      required: true,
    },
    workflowName: {
      type: String,
      required: true,
    },
    workflowModule: {
      type: String,
      required: true,
    },
    selectedFormId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      latestNode: 3,
      loading: false,
      deleted: false,
      openExpressionModal: false,
      expressionTitle: "",
      serviceAPIList: [],
      exclusiveNode: 1,
      selectedUserNode: "",
      selectedQueryData: "",
      openConfirmationPopup: false,
      nodes: [], //initial node
      edges: [],
    };
  },
  components: {
    VueFlow,
    Background,
    ProcessNode,
    GotoNode,
    AddCallActivityNode,
    AddStartNode,
    AddServiceNode,
    AddEndNode,
    AddExclusiveNode,
    AddParallelNode,
    AddUserTaskNode,
    AddResendNode,
    CustomConnectionLine,
    CustomEdge,
    ExpressionBuilder,
    AddRejectNode,
    AddApproveNode,
  },
  computed: {
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    this.getServiceAPI();
    this.prepareEditData();
  },
  methods: {
    checkNullValue,
    onCloseBuilder() {
      this.openConfirmationPopup = false;
      this.$emit("onCloseWorkflow");
    },
    handleAddNode(data, nodeData, replaceNode, extraData) {
      this.loading = true;
      let tempNodes = [];
      const templateNode = this.nodes.find((li) => li.id == nodeData.id);
      // let tempNode=replaceNode?this.nodes.filter((li)=>li.id!=nodeData.id):this.nodes;
      let tempNode = this.nodes;
      let tempEdge = this.edges;
      this.nodes = [];
      this.edges = [];
      const currentNodePosition =
        templateNode.id === "node_2"
          ? { ...templateNode, position: { x: 0, y: 0 } }
          : templateNode;
      const currentNode = replaceNode ? nodeData.id : "node_" + this.latestNode;
      const currentEdges = "edges_" + this.latestNode;
      const positionObject = {
        x:
          extraData !== 0
            ? extraData > 2
              ? currentNodePosition.position.x + extraData * 90
              : currentNodePosition.position.x + extraData * 60
            : currentNodePosition.position.x,
        y: replaceNode
          ? nodeData.position.y
          : extraData !== 0
          ? currentNodePosition.position.y + 80
          : currentNodePosition.position.y + 100,
      };
      switch (data) {
        case "addUserTask":
          tempNode.push({
            id: currentNode,
            position: positionObject,
            label: "",
            type: "user_task_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: positionObject,
              id: currentNode,
              type: "parent",
              addNew: true,
              name: "user_task_node",
              resend: true,
              nodeType: "userTask",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          // replace current node with new node
          if (!replaceNode) {
            tempEdge.push({
              id: currentEdges,
              source: nodeData.id,
              target: currentNode,
              type: "custom",
            });
          }
          // creating default two node [1,2]
          [1, 2].forEach((item) => {
            const positionObjects = {
              x:
                item == 1
                  ? currentNodePosition.position.x - 50
                  : currentNodePosition.position.x + 150,
              y: replaceNode
                ? currentNodePosition.position.y + 100
                : currentNodePosition.position.y + 250,
            };
            tempNode.push({
              id: "node_" + this.latestNode + item,
              position: positionObjects,
              label: "",
              type: "user_task_node",
              targetPosition: Position.Top,
              sourcePosition: Position.Bottom,
              data: {
                position: positionObjects,
                id: "node_" + this.latestNode + item,
                type: "child",
                addNew: true,
                name: "user_task_node",
                nodeType: "",
                parentNode: currentNode,
                approveType: item == 1 ? "Reject" : "Approve",
              },
            });
            // item 1 is reject and item 2 is approve
            if (item === 1) {
              tempEdge.push({
                id: currentEdges + 1,
                source: currentNode,
                label: "Reject",
                labelStyle: {
                  fill: "#ffffff",
                  fontWeight: 400,
                  fontSize: "7px",
                },
                labelBgPadding: [8, 4],
                labelBgBorderRadius: 4,
                labelBgStyle: {
                  fill: "#ff2600",
                  color: "#fff",
                  fillOpacity: 1,
                },
                style: { stroke: "#ff2600" },
                target: "node_" + this.latestNode + item,
                type: "custom",
              });
            } else if (item === 2) {
              tempEdge.push({
                id: currentEdges + 2,
                source: currentNode,
                label: "Approve",
                labelStyle: {
                  fill: "#ffffff",
                  fontWeight: 400,
                  fontSize: "7px",
                },
                labelBgPadding: [8, 4],
                labelBgBorderRadius: 4,
                labelBgStyle: {
                  fill: "#00a200",
                  color: "#fff",
                  fillOpacity: 1,
                },
                style: { stroke: "#00a200" },
                target: "node_" + this.latestNode + item,
                type: "custom",
              });
            }
          });
          break;
        case "addCallActivity":
          tempNode.push({
            id: currentNode,
            position: positionObject,
            label: "",
            type: "call_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: positionObject,
              id: currentNode,
              addNew: true,
              name: "call_node",
              title: "",
              callAPI: "",
              nodeType: "callActivity",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          if (!replaceNode) {
            tempEdge.push({
              id: currentEdges,
              source: nodeData.id,
              target: currentNode,
              type: "custom",
            });
          }
          break;
        case "addServiceTask":
          tempNode.push({
            id: currentNode,
            position: positionObject,
            label: "",
            type: "service_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: positionObject,
              id: currentNode,
              addNew: true,
              name: "service_node",
              title: "",
              serviceApi: "",
              nodeType: "serviceTask",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          if (!replaceNode) {
            tempEdge.push({
              id: currentEdges,
              source: nodeData.id,
              target: currentNode,
              type: "custom",
              data: { nodeId: currentNode },
            });
          }
          break;
        case "addParallelGateway":
          tempNode.push({
            id: currentNode,
            position: positionObject,
            label: "",
            type: "parallel_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: positionObject,
              id: currentNode,
              type: "parent",
              addNew: true,
              name: "parallel_node",
              nodeType: "parallelGateway",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          if (!replaceNode) {
            tempEdge.push({
              id: currentEdges,
              source: nodeData.id,
              target: currentNode,
              type: "custom",
            });
          }
          [1, 2].forEach((item) => {
            const positionObjects = {
              x:
                item == 1
                  ? currentNodePosition.position.x - 50
                  : currentNodePosition.position.x + 50,
              y: replaceNode
                ? currentNodePosition.position.y + 100
                : currentNodePosition.position.y + 180,
            };
            tempNode.push({
              id: "node_" + this.latestNode + item,
              position: positionObjects,
              label: "",
              type: "parallel_node",
              targetPosition: Position.Top,
              sourcePosition: Position.Bottom,
              data: {
                position: positionObjects,
                id: "node_" + this.latestNode + item,
                type: "child",
                addNew: true,
                name: "parallel_node",
                parentNode: currentNode,
              },
            });
            tempEdge.push({
              id: currentEdges + item,
              source: currentNode,
              target: "node_" + this.latestNode + item,
              type: "custom",
            });
          });
          break;
        case "addExclusiveGateway":
          this.exclusiveNode = 1;
          tempNode.push({
            id: currentNode,
            position: positionObject,
            label: "",
            type: "exclusive_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: positionObject,
              id: currentNode,
              type: "parent",
              addNew: true,
              name: "exclusive_node",
              nodeType: "exclusiveGateway",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          if (!replaceNode) {
            tempEdge.push({
              id: currentEdges,
              source: nodeData.id,
              target: currentNode,
              type: "custom",
            });
          }
          [1, 2].forEach((item) => {
            const positionObjects = {
              x:
                item == 1
                  ? currentNodePosition.position.x - 100
                  : currentNodePosition.position.x + 50,
              y: replaceNode
                ? currentNodePosition.position.y + 100
                : currentNodePosition.position.y + 180,
            };
            tempNode.push({
              id: "node_" + this.latestNode + item,
              position: positionObjects,
              label: "",
              type: "exclusive_node",
              targetPosition: Position.Top,
              sourcePosition: Position.Bottom,
              data: {
                position: positionObjects,
                id: "node_" + this.latestNode + item,
                type: "child",
                addNew: true,
                name: "exclusive_node",
                nodeType: "conditionNode",
              },
            });
            tempEdge.push({
              id: currentEdges + item,
              source: currentNode,
              target: "node_" + this.latestNode + item,
              type: "custom",
            });
          });
          break;
        case "addChildExclusiveGateway":
          [1, 2].forEach((item) => {
            const positionObjects = {
              x:
                item == 1
                  ? currentNodePosition.position.x + 250 * this.exclusiveNode
                  : currentNodePosition.position.x + 300 * this.exclusiveNode,
              y: currentNodePosition.position.y + 100,
            };
            tempNode.push({
              id: "node_" + this.latestNode + item,
              position: positionObjects,
              label: "",
              type: "exclusive_node",
              targetPosition: Position.Top,
              sourcePosition: Position.Bottom,
              data: {
                position: positionObjects,
                id: "node_" + this.latestNode + item,
                type: "child",
                addNew: true,
                name: "exclusive_node",
                nodeType: "conditionNode",
                approveTypes: nodeData?.approveType ? nodeData.approveType : "",
              },
            });
            tempEdge.push({
              id: currentEdges + item,
              source: currentNode,
              target: "node_" + this.latestNode + item,
              type: "custom",
            });
          });
          this.exclusiveNode = this.exclusiveNode + 1;
          break;
        case "addGotoNode":
          tempNode.push({
            id: currentNode,
            position: positionObject,
            label: "",
            type: "goto_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: positionObject,
              id: currentNode,
              addNew: true,
              name: "goto_node",
              nodeType: "goto",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          if (!replaceNode) {
            tempEdge.push({
              id: currentEdges,
              source: nodeData.id,
              target: currentNode,
              type: "custom",
            });
          }
          break;
        case "addEndNode":
          tempNode.push({
            id: currentNode,
            position: positionObject,
            label: "",
            type: "end_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: positionObject,
              id: currentNode,
              addNew: true,
              nodeType: "end",
              name: "end_node",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          if (!replaceNode) {
            tempEdge.push({
              id: currentEdges,
              source: nodeData.id,
              target: currentNode,
              type: "custom",
            });
          }
          break;
        case "addResendTask":
          tempNode.push({
            id: currentNode,
            position: {
              x: currentNodePosition.position.x + 53,
              y: currentNodePosition.position.y + 150,
            },
            label: "",
            type: "resend_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: {
                x: currentNodePosition.position.x + 53,
                y: currentNodePosition.position.y + 150,
              },
              id: currentNode,
              addNew: true,
              name: "resend_node",
              approveType: "Resend",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          tempEdge.push({
            id: currentEdges + 1,
            source: nodeData.id,
            label: "Resend",
            labelStyle: { fill: "#ffffff", fontWeight: 400, fontSize: "7px" },
            labelBgPadding: [8, 4],
            labelBgBorderRadius: 4,
            labelBgStyle: { fill: "#e7943a", color: "#fff", fillOpacity: 1 },
            style: { stroke: "#e7943a" },
            target: currentNode,
            type: "custom",
          });
          break;
        case "addApproveTask":
          tempNode.push({
            id: currentNode,
            position: {
              x: currentNodePosition.position.x + 53,
              y: currentNodePosition.position.y + 150,
            },
            label: "",
            type: "approve_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: {
                x: currentNodePosition.position.x + 53,
                y: currentNodePosition.position.y + 150,
              },
              id: currentNode,
              addNew: true,
              name: "approve_node",
              approveType: "Approve",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          tempEdge.push({
            id: currentEdges + 1,
            source: nodeData.id,
            label: "Approve",
            labelStyle: {
              fill: "#ffffff",
              fontWeight: 400,
              fontSize: "7px",
            },
            labelBgPadding: [8, 4],
            labelBgBorderRadius: 4,
            labelBgStyle: {
              fill: "#00a200",
              color: "#fff",
              fillOpacity: 1,
            },
            style: { stroke: "#00a200" },
            target: currentNode,
            type: "custom",
          });
          break;
        case "addRejectTask":
          tempNode.push({
            id: currentNode,
            position: {
              x: currentNodePosition.position.x + 53,
              y: currentNodePosition.position.y + 150,
            },
            label: "",
            type: "reject_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: {
                x: currentNodePosition.position.x + 53,
                y: currentNodePosition.position.y + 150,
              },
              id: currentNode,
              addNew: true,
              name: "reject_node",
              approveType: "Reject",
              approveTypes: nodeData?.approveType ? nodeData.approveType : "",
            },
          });
          tempEdge.push({
            id: currentEdges + 1,
            source: nodeData.id,
            label: "Reject",
            labelStyle: {
              fill: "#ffffff",
              fontWeight: 400,
              fontSize: "7px",
            },
            labelBgPadding: [8, 4],
            labelBgBorderRadius: 4,
            labelBgStyle: {
              fill: "#ff2600",
              color: "#fff",
              fillOpacity: 1,
            },
            style: { stroke: "#ff2600" },
            target: currentNode,
            type: "custom",
          });
          break;
      }

      if (templateNode.id === "node_2") {
        tempEdge.push({
          id: "e1-11",
          source: "node_1",
          target: "node_3",
          type: "custom",
        });
      }

      // if (data === "addResendTask") {
      //   tempNode.forEach((item) => {
      //     if (item.id === nodeData.id) {
      //       tempNodes.push({ ...item, data: { ...item.data, resend: false } });
      //     } else {
      //       tempNodes.push(item);
      //     }
      //   });
      // } else {
      tempNode.forEach((item) => {
        if (item.id !== "node_2") {
          if (tempEdge.find((edgeItem) => edgeItem.source === item.id)) {
            tempNodes.push({
              ...item,
              data: { ...item.data, addNew: false },
            });
          } else {
            tempNodes.push(item);
          }
        }
      });
      // }
      this.nodes = tempNodes;
      this.edges = tempEdge;
      tempNodes = [];
      this.latestNode = this.latestNode + 1;
      this.loading = false;
    },
    handleAddParentNode(type, data) {
      this.handleAddNode(
        type,
        data,
        false,
        this.edges.filter((list) => list.source === data.id).length
      );
    },

    //delete items
    handleDeleteNode(data) {
      this.loading = true;
      const parentNode = [];
      let allNodes = [];
      let allEdges = this.edges;
      const tempNode = this.nodes.filter((li) => li.id !== data.id);
      this.edges = [];
      this.nodes = [];
      const findSourceNode = allEdges.find((li) => data.id === li.target);
      tempNode.forEach((item) => {
        if (item?.data?.parentNode === data.id) {
          parentNode.push(item.id);
        } else if (findSourceNode && findSourceNode.source === item.id) {
          allNodes.push({ ...item, data: { ...item.data, addNew: true } });
        } else {
          allNodes.push(item);
        }
      });
      const addStart = allEdges.filter(
        (ed) => ed.source == "node_1" && ed.target !== data.id
      );
      if (
        data.id === "node_3" ||
        tempNode.length === 1 ||
        addStart.length === 0
      ) {
        allNodes.push({
          id: "node_2",
          position: { x: 30, y: 100 },
          label: "",
          type: "add_process",
          targetPosition: Position.Top,
          sourcePosition: Position.Bottom,
          data: {
            position: { x: 20, y: 0 },
            id: "node_2",
            addNew: true,
          },
        });
        allEdges = allEdges.filter((ed) => ed.source !== "node_1");
        allEdges.push({
          id: "e1-12",
          source: "node_1",
          target: "node_2",
          type: "custom",
        });
        this.latestNode = ++this.latestNode;
      }
      this.nodes = allNodes;
      allEdges.forEach((prepareEdges) => {
        if (
          prepareEdges.target !== data.id &&
          !parentNode.includes(prepareEdges.target)
        ) {
          this.edges.push(prepareEdges);
        }
      });
      // this.edges = allEdges.filter((ed) => ed.target !== data.id);
      this.loading = false;
    },
    handleExpressionBuilder(nodeId) {
      this.selectedQueryData = "";
      this.selectedUserNode = nodeId;
      const selectedNodes = this.nodes.find((list) => list?.id === nodeId);
      this.expressionTitle = selectedNodes.data.title;
      this.selectedQueryData = selectedNodes;
      this.openExpressionModal = true;
    },
    handleUpdateTitle(value) {
      this.nodes = this.nodes.map((items) => {
        if (items.id === value.nodeId) {
          return {
            ...items,
            data: {
              ...items.data,
              title: value.title,
            },
          };
        } else {
          return items;
        }
      });
    },
    handleQueryValue(value) {
      this.nodes = this.nodes.map((items) => {
        if (items.id === this.selectedUserNode) {
          return {
            ...items,
            queryValue: value,
            data: { ...items.data, title: this.expressionTitle },
          };
        } else {
          return items;
        }
      });
      this.expressionTitle = "";
    },
    handleServiceAPI(value) {
      this.nodes = this.nodes.map((items) => {
        if (items.id === value.id) {
          return {
            ...items,
            data: {
              ...items.data,
              serviceApi: value.api,
            },
          };
        } else {
          return items;
        }
      });
    },
    handleSubmitForm(formData) {
      this.nodes = this.nodes.map((items) => {
        if (items.id === this.selectedUserNode) {
          return {
            ...items,
            data: { ...items.data, formData: formData },
          };
        } else {
          return items;
        }
      });
    },
    handleGotoNode(nodeDetail) {
      this.nodes = this.nodes.map((items) => {
        if (items.id === nodeDetail.id) {
          return {
            ...items,
            data: { ...items.data, nodeId: nodeDetail.goto },
          };
        } else {
          return items;
        }
      });
    },
    prepareEditData() {
      if (this.editWorkflow && this.editFlowData && this.editFlowData.length) {
        const editData = Object.values(
          JSON.parse(this.editFlowData[0]?.ui_config)?.nodes
        );
        const editApprove = JSON.parse(
          this.editFlowData[0]?.ui_config
        )?.approveTypes;
        this.loading = true;
        this.nodes = [];
        this.edges = [];
        let tempNode = [];
        let tempEdge = [];
        editData.forEach((list) => {
          const nextExpResult = list.nextEp.length
            ? list.nextEp[0].includes("ep_top")
              ? list.nextEp
              : []
            : [];
          const targetEdges = list.nextEp.length
            ? list.nextEp[0].includes("ep_top")
              ? list.nextEp
              : list.next
            : list.next;
          let prepareNode = {};
          prepareNode = {
            id: list.nodeId === "taWorkStart" ? "node_1" : list.nodeId,
            type:
              list.type === "start"
                ? "start_node"
                : list.type === "serviceTask"
                ? "service_node"
                : list.type === "userTask"
                ? "user_task_node"
                : list.type === "end"
                ? "end_node"
                : list.type === "exclusiveGateway"
                ? "exclusive_node"
                : list.type === "conditionNode"
                ? "exclusive_node"
                : list.type === "goto"
                ? "goto_node"
                : list.type === "parallelGateway"
                ? "parallel_node"
                : "",
            position: {
              x: this.dotRemove(list.left),
              y: this.dotRemove(list.top),
            },
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: {
                x: this.dotRemove(list.left),
                y: this.dotRemove(list.top),
              },
              title: list?.data?.title,
              nodeType: list.type,
              type:
                targetEdges.length >= 2 ||
                list.type === "parallelGateway" ||
                list.type === "userTask"
                  ? "parent"
                  : list.type === "conditionNode"
                  ? "child"
                  : list.type,
              id: list.nodeId,
              addNew: targetEdges.length ? false : true,
              label: "",
              approveTypes: list?.approveType ? list.approveType : "",
            },
          };
          if (list.type === "conditionNode") {
            prepareNode["queryValue"] = list.data.condition;
          } else if (list.type === "userTask") {
            prepareNode.data = { ...prepareNode.data, formData: list?.data };
          } else if (list.type === "serviceTask") {
            prepareNode.data = {
              ...prepareNode.data,
              serviceTitle: list?.data.title,
              serviceApi: list?.data.serviceApi,
            };
          } else if (list.type === "goto") {
            prepareNode.data = {
              ...prepareNode.data,
              nodeId: list?.data?.goto,
            };
          }
          tempNode.push(prepareNode);
          if (targetEdges.length) {
            targetEdges.map((edges) => {
              let prepareEdges = {
                id: "ed_" + edges,
                type: "custom",
                source: list.nodeId === "taWorkStart" ? "node_1" : list.nodeId,
                target: nextExpResult.length ? "node_" + edges.slice(6) : edges,
                labelBgPadding: [8, 4],
                labelBgBorderRadius: 4,
              };
              if (
                editApprove[edges] === "Reject" ||
                editApprove[edges] === "reject"
              ) {
                prepareEdges["labelStyle"] = {
                  fill: "#ffffff",
                  fontWeight: 400,
                  fontSize: "7px",
                };
                prepareEdges["label"] = "Reject";
                prepareEdges["labelBgStyle"] = {
                  fill: "#ff2600",
                  color: "#fff",
                  fillOpacity: 1,
                };
                prepareEdges["style"] = { stroke: "#ff2600" };
              } else if (
                editApprove[edges] === "Approve" ||
                editApprove[edges] === "approve"
              ) {
                prepareEdges["label"] = "Approve";
                prepareEdges["labelStyle"] = {
                  fill: "#ffffff",
                  fontWeight: 400,
                  fontSize: "7px",
                };
                prepareEdges["labelBgStyle"] = {
                  fill: "#00a200",
                  color: "#fff",
                  fillOpacity: 1,
                };
                prepareEdges["style"] = { stroke: "#00a200" };
              } else if (
                editApprove[edges] === "Resend" ||
                editApprove[edges] === "resend"
              ) {
                prepareEdges["label"] = "Resend";
                prepareEdges["labelStyle"] = {
                  fill: "#ffffff",
                  fontWeight: 400,
                  fontSize: "7px",
                };
                prepareEdges["labelBgStyle"] = {
                  fill: "#e7943a",
                  color: "#fff",
                  fillOpacity: 1,
                };
                prepareEdges["style"] = { stroke: "#e7943a" };
              }
              tempEdge.push(prepareEdges);
            });
          }
        });
        this.latestNode = editData.length + 2;
        this.nodes = tempNode;
        this.edges = tempEdge;
        this.loading = false;
      } else {
        this.nodes = [
          {
            id: "node_1",
            position: { x: 0, y: 0 },
            label: "start",
            type: "start_node",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: { x: 0, y: 0 },
              id: "node_1",
              addNew: true,
            },
          },
          {
            id: "node_2",
            position: { x: 30, y: 100 },
            label: "",
            type: "add_process",
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
            data: {
              position: { x: 20, y: 0 },
              id: "node_2",
              addNew: true,
            },
          },
        ]; //initial node
        this.edges = [
          { id: "e1-2", source: "node_1", target: "node_2", type: "custom" },
        ]; //initial edges
      }
    },
    dotRemove(value) {
      let toStr = value.toString();
      if (toStr.split(".").length > 1) {
        return this.pxRemove(toStr.substring(0, toStr.indexOf(".")));
      } else {
        return this.pxRemove(value);
      }
    },
    pxRemove(value) {
      if (typeof value === "string" && value.includes("px")) {
        return value.slice(0, -2);
      } else {
        return value;
      }
    },
    handleSubmit() {
      let validData = [];
      let resultData = {};
      let userTask = {};
      let requestPayload = {
        nodes: [],
        startNode: "taWorkStart",
        approveTypes: {},
      };
      this.nodes.forEach((nodeItems) => {
        if (nodeItems?.data?.nodeType || nodeItems?.id == "node_1") {
          let sourceNode = [];
          this.edges.filter((node) => {
            if (node.source == nodeItems?.id) {
              const original = this.nodes.find(
                (nextNode) => nextNode.data.id === node.target
              );
              if (original.data.nodeType) {
                sourceNode.push(node.target);
              }
            }
          });
          if (nodeItems?.id == "node_1") {
            resultData["taWorkStart"] = {
              nodeId: "taWorkStart",
              type: "start",
              top: this.dotRemove(nodeItems.position.y),
              left: this.dotRemove(nodeItems.position.x),
              next: sourceNode,
              nextEp: sourceNode,
              approveType: "",
            };
          } else {
            let preparedNodeData = {
              nodeId: nodeItems?.id,
              type: nodeItems?.data?.nodeType,
              top: this.dotRemove(nodeItems.position.y),
              left: this.dotRemove(nodeItems.position.x),
              nodeNo: nodeItems?.id,
              data: {},
              next: sourceNode,
              nextEp: sourceNode,
              approveType: nodeItems?.data?.approveTypes
                ? nodeItems?.data?.approveTypes
                : "",
            };
            if (
              nodeItems.data.nodeType === "parallelGateway" ||
              nodeItems.data.nodeType === "exclusiveGateway"
            ) {
              preparedNodeData.data["title"] = nodeItems?.data?.title;
            }
            if (
              nodeItems?.data?.approveTypes &&
              (nodeItems?.data?.approveTypes === "reject" ||
                nodeItems?.data?.approveTypes === "Reject" ||
                nodeItems?.data?.approveTypes === "approve" ||
                nodeItems?.data?.approveTypes === "Approve" ||
                nodeItems?.data?.approveTypes === "Resend" ||
                nodeItems?.data?.approveTypes === "resend")
            ) {
              userTask[nodeItems?.data?.id] = nodeItems?.data?.approveTypes;
            }
            if (nodeItems?.queryValue) {
              preparedNodeData.data["condition"] = nodeItems?.queryValue;
              preparedNodeData.data["title"] = nodeItems?.data?.title;
            } else if (nodeItems?.data?.formData) {
              preparedNodeData.data = nodeItems?.data?.formData;
            }
            if (nodeItems?.data?.nodeType === "serviceTask") {
              if (
                nodeItems?.data?.title === "" ||
                nodeItems?.data?.serviceApi === "" ||
                nodeItems?.data?.title === undefined ||
                nodeItems?.data?.serviceApi === undefined
              ) {
                validData.push(nodeItems?.data?.title);
              } else {
                preparedNodeData.data = {
                  ...preparedNodeData.data,
                  title: nodeItems?.data?.title,
                  serviceApi: nodeItems?.data?.serviceApi,
                  type: "serviceTask",
                };
              }
            } else if (nodeItems?.data?.nodeType === "goto") {
              preparedNodeData.data = {
                goto: nodeItems?.data?.nodeId,
                type: "goto",
              };
            }
            resultData[nodeItems?.id] = preparedNodeData;
          }
        }
      });
      requestPayload["nodes"] = resultData;
      requestPayload["approveTypes"] = userTask;
      if (validData.length === 0) {
        if (Object.keys(requestPayload.nodes)?.length > 1) {
          this.$emit("submitWorkflow", JSON.stringify(requestPayload));
        } else {
          let snackbarData = {
            isOpen: true,
            message: "Please add at least one more child task to proceed",
            type: "warning",
          };
          window.scrollTo(0, 0);
          this.showAlert(snackbarData);
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please enter service title and service API",
          type: "warning",
        };
        window.scrollTo(0, 0);
        this.showAlert(snackbarData);
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async getServiceAPI() {
      let vm = this;
      const payload = {
        query_key: "query.api.endpoint",
        limit: 100,
        offset: 0,
        filter: [
          {
            key: "status_id",
            value: "1001",
            op: "=",
          },
        ],
        sort: "created_date",
        order: "desc",
      };
      await axios
        .post(Config.workflowUrl + "/master/query", JSON.stringify(payload), {
          headers: {
            org_code: vm.orgCode,
            employee_id: vm.loginEmployeeId,
            Db_prefix: vm.domainName,
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            Authorization: window.$cookies.get("accessToken")
              ? window.$cookies.get("accessToken")
              : "",
          },
        })
        .then((res) => {
          if (res && res.data && res.data.results) {
            this.serviceAPIList = res.data.results;
          }
        })
        .catch(() => {});
    },
  },
  setup() {
    const { updateEdge, addEdges } = useVueFlow();

    //    function onEdgeUpdateStart(edge) {  //edge update start
    //    }

    //    function onEdgeUpdateEnd(edge) {  //edge update end
    //    }

    function onEdgeUpdate({ edge, connection }) {
      updateEdge(edge, connection);
    }

    function onConnect(params) {
      addEdges([{ ...params, type: "custom" }]);
    }
    return {
      onConnect,
      onEdgeUpdate,
    };
  },
};
</script>
<style>
.modal {
  position: absolute;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  text-align: center;
  width: fit-content;
  height: fit-content;
  overflow: hidden;
  overflow-y: scroll !important;
  max-width: 110rem;
  width: 60%;
  /* padding: 1rem 2rem; */
  border-radius: 0.3rem;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
  background: #fff;
  z-index: 1009;
  height: fit-content;
  transform: none;
  padding: 1rem;
}

.modal-overlay {
  content: "";
  position: absolute;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1008;
  background: #2c3e50;
  opacity: 0.6;
  padding: 1rem;
}

/* ---------------------------------- */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s linear;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.pop-enter-active,
.pop-leave-active {
  transition: transform 0.4s cubic-bezier(0.5, 0, 0.5, 1), opacity 0.4s linear;
}

.pop-enter,
.pop-leave-to {
  opacity: 0;
  transform: scale(0.3) translateY(-50%);
}

.group-header {
  margin-top: 0px !important;
}
</style>
