<template>
  <div class="position-relative">
    <div
      id="org-chart-container"
      v-if="orgList && orgList.length > 0"
      class="chart-container"
      ref="chartContainer"
      :style="'overflow: scroll; height: ' + $store.getters.getTableHeight(200)"
    ></div>
    <div class="position-absolute top-0 pa-2 right-0">
      <v-tooltip text="Center View" location="top" max-width="400">
        <template v-slot:activator="{ props }">
          <v-btn
            v-bind="props"
            rounded="pill"
            variant="elevated"
            color="grey-lighten-3"
            class="tab-action-btn-disabled px-0 mr-2"
            size="default"
            @click="centerView()"
          >
            <v-icon class="add-icon">
              <v-icon size="18">fas fa-undo-alt</v-icon>
            </v-icon>
          </v-btn>
        </template>
      </v-tooltip>
      <v-tooltip
        :text="compactFlag ? 'Compact View' : 'Horizontal View'"
        location="top"
        max-width="400"
      >
        <template v-slot:activator="{ props }">
          <v-btn
            v-bind="props"
            rounded="pill"
            variant="elevated"
            color="grey-lighten-3"
            class="tab-action-btn-disabled px-0 mr-2"
            size="default"
            @click="compactView(compactFlag)"
          >
            <v-icon class="add-icon">
              <v-icon size="18">
                {{
                  compactFlag ? "fas fa-arrows-alt-v" : "fas fa-arrows-alt-h"
                }}</v-icon
              >
            </v-icon>
          </v-btn>
        </template>
      </v-tooltip>
      <!-- <v-tooltip text="Download PDF" location="top" max-width="400">
        <template v-slot:activator="{ props }">
          <v-btn
            v-bind="props"
            rounded="pill"
            variant="elevated"
            color="grey-lighten-3"
            class="tab-action-btn-disabled px-0 mr-2"
            size="default"
            @click="downloadPDF()"
          >
            <v-icon class="add-icon">
              <v-icon size="18">far fa-file-pdf</v-icon>
            </v-icon>
          </v-btn>
        </template>
      </v-tooltip> -->
      <v-tooltip
        :text="expandFlag ? 'Collapse All' : 'Expand All'"
        location="top"
        max-width="400"
      >
        <template v-slot:activator="{ props }">
          <v-btn
            v-bind="props"
            rounded="pill"
            variant="elevated"
            color="grey-lighten-3"
            class="tab-action-btn-disabled"
            size="default"
            @click="collapseAll(expandFlag)"
          >
            <v-icon class="add-icon">
              <v-icon size="18">{{
                !expandFlag
                  ? "fas fa-expand-arrows-alt"
                  : "fas fa-compress-arrows-alt"
              }}</v-icon>
            </v-icon>
          </v-btn>
        </template>
      </v-tooltip>
    </div>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { ref, onMounted } from "vue";
import * as d3 from "d3";
import { OrgChart } from "d3-org-chart";
import * as html2pdf from "html2pdf.js";
export default {
  name: "OrganizationView",
  props: {
    orgList: {
      type: Array,
      required: true,
    },
    parentOrgId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const expandFlag = ref(true);
    const compactFlag = ref(false);
    const chartInstance = ref(null);
    const chartContainer = ref(null);
    const isLoading = ref(false);
    function renderOrgChart(container, data) {
      if (!container.value || data.length === 0) {
        return;
      }
      const chart = new OrgChart()
        .container(container.value)
        .data(data)
        .scaleExtent([0.1, 2])
        .setActiveNodeCentered(true)
        .nodeHeight(() => 110)
        .nodeWidth(() => 240)
        .childrenMargin(() => 70)
        .compactMarginBetween(() => 20)
        .compactMarginPair(() => 120)
        .siblingsMargin(() => 120)
        .nodeUpdate(function (d) {
          d3.select(this)
            .select(".node-rect")
            .attr("width", 50)
            .attr("height", 50)
            .attr(
              "stroke-width",
              d.data._highlighted || d.data._upToTheRootHighlighted ? 8 : 2
            )
            .attr("y", -3)
            .attr("x", -3)
            .attr("stroke-linejoin", "round")
            .style("stroke-alignment", "outer");
        })
        .linkUpdate(function (d) {
          d3.select(this)
            .attr("stroke", (d) =>
              d.data._upToTheRootHighlighted ? "#4285F4" : "#9e9e9e"
            )
            .attr("stroke-width", (d) =>
              d.data._upToTheRootHighlighted ? 10 : 1
            );
          if (d.data._upToTheRootHighlighted) {
            d3.select(this).raise();
          }
        })
        .onNodeClick((d) => {
          // clickedNodeID.value = d.id;
          markNode(d.id);
          centerNode(d.id);
        })
        .nodeButtonX(() => -8)
        .nodeButtonY(() => -10)
        .buttonContent(({ node }) => {
          const foreignObject = d3.selectAll(".node-button-foreign-object");
          foreignObject.attr("width", 50).attr("height", 50);
          return `
     
          ${
            node.children
              ? `<div class="btn-rounded-org"><svg xmlns="http://www.w3.org/2000/svg" height="7" width="7" fill="#0c166e" viewBox="0 0 448 512"><path d="M416 208H32c-17.7 0-32 14.3-32 32v32c0 17.7 14.3 32 32 32h384c17.7 0 32-14.3 32-32v-32c0-17.7-14.3-32-32-32z"/></svg></div>`
              : `<div class="btn-rounded-org"><svg xmlns="http://www.w3.org/2000/svg" height="7" width="7" fill="#0c166e" viewBox="0 0 448 512"><path d="M416 208H272V64c0-17.7-14.3-32-32-32h-32c-17.7 0-32 14.3-32 32v144H32c-17.7 0-32 14.3-32 32v32c0 17.7 14.3 32 32 32h144v144c0 17.7 14.3 32 32 32h32c17.7 0 32-14.3 32-32V304h144c17.7 0 32-14.3 32-32v-32c0-17.7-14.3-32-32-32z"/></svg></div>`
          }
          `;
        })
        .nodeContent((node) => {
          return `
           <div class="card-ui position-relative" style="width:${
             node.width
           }px;height:${node.height}px">
    <div class="bg" style="${
      node.data?.To_Be_Hired > 1 ? "border: 3px solid red;" : ""
    }"></div>
    <div
      class="position-absolute px-4 pt-1 pb-3"
      style="top: 0; left: 0; right: 0; bottom: 10; overflow: hidden"
    >
      <div style="display: inline-flex">
        <div class="d-flex flex-column align-center justify-start">
          <div class="d-flex align-center justify-start" style="width: 100%">
          <div class="text-caption text-grey-darken-1 font-weight-bold pl-1 text-truncate-org" title="${
            node.data.name ?? " - "
          }">>
            ${node.data.name ?? " - "}
          </div>
          </div>
             <div class="text-caption font-weight-bold pl-1">
            ${node.data.code ?? " - "}
          </div>
        </div>
      </div>
        <div class="d-flex align-center flex-column justify-start pt-1">
          <div class="straight-line" style="width: 100%; align-items: baseline"><div class="text-body-2 text-grey-darken-1 text-truncate-label-org pb-1" title="Approved Positions">Approved Positions</div><div class="text-primary px-1 d-flex align-center text-truncate-label-org"> : ${
            node.data?.Approved_Position || " - "
          }</div></div>
          <div class="straight-line" style="width: 100%; align-items: baseline"><div class="text-body-2 text-grey-darken-1 text-truncate-label-org pb-1" title="Warm Bodies">Warm Bodies</div><div class="text-primary pl-1 d-flex align-center text-truncate-label-org"> : ${
            node.data?.Warm_Bodies || " - "
          }</div></div>
          <div class="straight-line" style="width: 100%; align-items: baseline"><div class="text-body-2 text-grey-darken-1 text-truncate-label-org pb-1" title="Vacant Positions">Vacant Positions</div><div class="text-primary px-1 d-flex align-center text-truncate-label-org"> : ${
            node.data?.To_Be_Hired || " - "
          }</div></div></div>
        <div class="d-flex child-org-count align-center justify-end py-0">
          <div>
            ${
              node.data._totalSubordinates
                ? `<span><svg
              xmlns="http://www.w3.org/2000/svg"
              height="14"
              width="14"
              fill="#0c166e"
              viewBox="0 0 640 512"
            >
              <path
                d="M368 32h-96c-17.7 0-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32V64c0-17.7-14.3-32-32-32zM208 88h-84.8C113.8 64.6 90.8 48 64 48 28.7 48 0 76.7 0 112s28.7 64 64 64c26.8 0 49.8-16.6 59.3-40h79.7c-55.4 32.5-95.9 87.3-109.5 152h49.4c11.3-41.6 36.8-77.2 71-101.6-3.7-8.1-5.9-17-5.9-26.4V88zm-48 232H64c-17.7 0-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32v-96c0-17.7-14.3-32-32-32zM576 48c-26.8 0-49.8 16.6-59.3 40H432v72c0 9.5-2.2 18.4-5.9 26.4 34.3 24.4 59.7 60 71 101.6h49.4c-13.7-64.7-54.2-119.5-109.5-152h79.7c9.5 23.4 32.4 40 59.3 40 35.3 0 64-28.7 64-64s-28.7-64-64-64zm0 272h-96c-17.7 0-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32v-96c0-17.7-14.3-32-32-32z"
              />
            </svg>
            <div class="d-flex justify-center" style="font-size: 8px">
              ${node.data._totalSubordinates}
            </div>
            </span>`
                : ``
            }
          </div>
        </div>
        </div>
    </div>
       </div>`;
        })
        .render()
        .fit();
      return chart;
      // Chart functions
      function markNode(nodeID) {
        if (chart) {
          // Check if chart is defined
          chart.setHighlighted(nodeID).render();
        }
      }
      function centerNode(nodeId) {
        if (chart) {
          // chart.fit(nodeId).render();
          chart.setCentered(nodeId).render();
        }
      }
    }
    function collapseAll(flag) {
      if (chartInstance.value) {
        this.expandFlag = !flag;
        if (flag) {
          chartInstance.value.collapseAll();
        } else {
          chartInstance.value.expandAll();
        }
      }
    }
    function compactView(flag) {
      if (chartInstance.value) {
        this.compactFlag = !this.compactFlag;
        chartInstance.value.compact(flag).render().fit();
      }
    }
    function centerView() {
      if (chartInstance.value) {
        chartInstance.value
          .setUpToTheRootHighlighted(props.parentOrgId)
          .render()
          .fit();
      }
    }
    function downloadPDF() {
      this.isLoading = true;
      var element = document.querySelector("#org-chart-container");
      var opt = {
        margin: 0,
        filename: "organization-chart.pdf",
        image: { type: "jpeg", quality: 1 },
        html2canvas: {
          scale: 1,
          useCORS: false,
        },
        jsPDF: { unit: "in", format: [15, 30], orientation: "portrait" },
      };
      function inlineStyles(el) {
        const elements = el.querySelectorAll("*");
        elements.forEach((el) => {
          const computedStyle = window.getComputedStyle(el);
          for (const key of computedStyle) {
            el.style[key] = computedStyle.getPropertyValue(key);
          }
        });
      }
      inlineStyles(element);
      html2pdf()
        .set(opt)
        .from(element)
        .save()
        .then(() => {
          const el = element.querySelector(".chart");
          el.style = "";
          this.isLoading = false;
          chartInstance.value.render();
        })
        .catch(() => {
          const el = element.querySelector(".chart");
          el.style = "";
          this.isLoading = false;
          chartInstance.value.render();
        });
    }
    onMounted(async () => {
      try {
        chartInstance.value = renderOrgChart(chartContainer, props.orgList);
      } catch (error) {
        console.error("Error loading data:", error);
      }
    });
    return {
      chartContainer,
      collapseAll,
      expandFlag,
      compactFlag,
      downloadPDF,
      compactView,
      centerView,
      isLoading,
    };
  },
};
</script>
<style>
.text-truncate-org {
  display: -webkit-box;
  -webkit-line-clamp: 1; /* Show only 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text-truncate-label-org {
  font-size: 10px !important;
  text-align: center;
  justify-content: center;
  text-overflow: ellipsis;
}
.child-org-count {
  position: absolute;
  right: 10px;
  bottom: 10px;
}
.chart-container {
  width: 100%;
  height: 90vh;
  border-radius: 10px;
  position: relative;
  overflow: auto;
  display: flex;
  justify-content: start;
  background-color: #e6e6e6;
}
.card-ui {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.bg {
  background: #ffffff;
  border-radius: 10px;
  border-style: solid;
  border-color: transparent;
  border-width: 2px;
  flex-shrink: 0;
  width: 240px;
  height: 110px;
  overflow: hidden;
  position: relative;
  box-shadow: rgba(0, 17, 51, 0.1) 0px 10px 10px;
}
.btn-rounded-org {
  height: 16px !important;
  width: 16px !important;
  border-radius: 50% !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  border: 0.5px solid #cfcfcf !important;
  background-color: #ffffff !important;
}
.btn-rounded-org :hover {
  background-color: #e2e2e2 !important;
}
.straight-line {
  display: inline-flex !important;
  justify-content: start;
  font-weight: bold;
}
</style>
