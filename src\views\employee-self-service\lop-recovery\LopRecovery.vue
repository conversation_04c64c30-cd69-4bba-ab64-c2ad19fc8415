<template>
  <div v-if="isMounted">
    <!-- AppTopBar component is reused here which is already present -->
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row
            justify="center"
            v-if="backupLopRecoveryDetails.length > 0"
            class="mr-2"
          >
            <v-col cols="12" md="9" class="d-flex justify-end mr-8">
              <EmployeeDefaultFilterMenu
                v-if="backupLopRecoveryDetails.length > 0"
                class="justify-end"
                :list-items="backupLopRecoveryDetails"
                :isFilter="false"
                :isApplyFilter="true"
                departmentIdKey="Designation_Id"
                designationIdKey="Department_Id"
                locationIdKey="Location_Id"
                empTypeIdKey="EmpType_Id"
                workScheduleIdKey="Work_Schedule"
                @reset-emp-filter="resetFilter()"
                @applied-filter="applyFilter($event)"
              >
                <template #bottom-filter-menu>
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-select
                      v-model="selectedLopRecoveryProcessingMonth"
                      color="secondary"
                      :items="recoveryProcessingMonthList"
                      label="LOP Recovery Processing Month"
                      multiple
                      closable-chips
                      chips
                      density="compact"
                      single-line
                    >
                    </v-select>
                  </v-col>
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-select
                      v-model="selectedStatus"
                      color="secondary"
                      :items="['Applied', 'Approved', 'Paid', 'Rejected']"
                      label="Status"
                      multiple
                      closable-chips
                      chips
                      density="compact"
                      single-line
                    >
                    </v-select>
                  </v-col>
                </template>
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <!-- Tab body starts from here -->

    <v-container fluid class="lop-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="lopRecoveryData.length == 0 && emptyFilterScreen"
            image-name="common/no-records"
            main-title="There are no LOP recovery for the selected filters/searches."
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center">
                <v-btn
                  color="primary"
                  variant="elevated"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click.stop="resetFilter()"
                >
                  Reset Filter/Search
                </v-btn>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="isLopConfigurationNotExist && isErrorInList"
            icon-name=""
            button-text=""
          >
            <template #contentSlot>
              <div
                :style="isMobileView ? 'max-width: 80%;' : 'max-width: 80%;'"
              >
                <v-row
                  v-if="formId === '252'"
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="Your organization currently does not have LOP recovery configurations enabled. For assistance, please get in touch with your HR administrator."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                      ><v-btn
                        variant="elevated"
                        color="primary"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="refetchList()"
                      >
                        <v-icon size="15" class="pr-1">fas fa-redo-alt</v-icon>
                        Retry
                      </v-btn></v-col
                    >
                  </v-col>
                </v-row>

                <v-row
                  v-else
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="Your organization's LOP recovery configuration is not enabled. To enable and update it, please go to the 'Settings' section."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <v-btn
                        v-if="formAccess.admin === 'admin' && formId === '256'"
                        rounded="lg"
                        color="primary"
                        variant="elevated"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="settingsRedirection()"
                      >
                        <v-icon class="add-icon mr-1"> fas fa-plus </v-icon>
                        Add Configuration
                      </v-btn>

                      <v-btn
                        variant="elevated"
                        color="primary"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="refetchList()"
                      >
                        <v-icon size="15" class="pr-1">fas fa-redo-alt</v-icon>
                        Retry
                      </v-btn>
                    </v-col>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="!isLopConfigurationNotExist && isErrorInList"
            image-name="common/human-error-image"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            button-text="Retry"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="
              backupLopRecoveryDetails.length === 0 && !showAddEditForm
            "
            key="no-results-screen"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  v-if="!isLoading"
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="The LOP (Loss Of Pay) recovery function is an essential tool designed for employees to address any unintended LOP deductions in their payroll. These deductions can often arise from scenarios such as a missed attendance or leave on a particular day, late arrivals, or falling short of the mandated work hours. The functionality empowers employees to submit a recovery request when they believe an LOP deduction has been unjustly applied. By providing valid reasons or evidence to back their claim, employees can seek a reversal of the deduction."
                      backgroundColor="transparent"
                      class="mb-2"
                    ></NotesCard>
                    <NotesCard
                      notes="Once a recovery request is submitted through this function, it undergoes a review process. The concerned department evaluates the reasons and evidence provided by the employee. If deemed valid, the LOP deduction is rectified, ensuring that employees are compensated fairly for their work and that any genuine oversights or errors in attendance tracking are corrected promptly."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>

                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAdd()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Add LOP Recovery
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="fetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <div
                v-if="!isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  prepend-icon="fas fa-plus"
                  color="primary rounded-lg"
                  @click="onAdd()"
                  :size="isMobileView ? 'small' : 'default'"
                  v-if="formAccess.add"
                >
                  <template v-slot:prepend>
                    <v-icon color="white"></v-icon>
                  </template>
                  Add LOP Recovery
                </v-btn>
                <v-btn
                  rounded="lg"
                  color="transparent"
                  variant="flat"
                  class="ml-2 mt-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>

                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n2 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="exportReportFile()"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="lopRecoveryData.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                >
                  <v-data-table
                    v-model="selectedData"
                    :headers="headers"
                    :items="lopRecoveryData"
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        280,
                        lopRecoveryData
                      )
                    "
                    class="elevation-1"
                    style="box-shadow: none !important"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        style="z-index: 200"
                        class="data-table-tr bg-white cursor-pointer"
                        @click="openViewForm(item)"
                        :class="[
                          isMobileView
                            ? ' v-data-table__mobile-table-row ma-0 mt-2'
                            : '',
                        ]"
                      >
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5 font-weight-small'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Employee
                          </div>
                          <section
                            style="height: 3em"
                            class="d-flex align-center"
                          >
                            <div class="d-flex align-center">
                              <div
                                v-if="
                                  isSmallTable &&
                                  !isMobileView &&
                                  selectedItem &&
                                  selectedItem.LOP_Recovery_Id ===
                                    item.LOP_Recovery_Id
                                "
                                class="data-table-side-border lop-border-color d-flex"
                                style="height: 3em"
                              ></div>
                            </div>
                            <span
                              class="text-primary text-body-2 font-weight-medium"
                            >
                              <v-tooltip
                                :text="item.Employee_Name"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-bind="
                                      item.Employee_Name &&
                                      item.Employee_Name.length > 20
                                        ? props
                                        : ''
                                    "
                                    >{{ item.Employee_Name }}</span
                                  >
                                </template>
                              </v-tooltip>
                              <v-tooltip
                                :text="item.userDefinedEmpId"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    v-if="item.userDefinedEmpId"
                                    v-bind="
                                      item.userDefinedEmpId &&
                                      item.userDefinedEmpId.length > 20
                                        ? props
                                        : ''
                                    "
                                    class="text-grey"
                                  >
                                    {{ item.userDefinedEmpId }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </span>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5 font-weight-medium'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            LOP Recovery Processing Month
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{ item.Salary_Month }}
                            </span>
                          </section>
                        </td>

                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Total Recovery Days
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{ item.Total_Lop_Recovery_Days }}
                            </span>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5 font-weight-medium'
                          "
                          v-if="!isSmallTable"
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Total LOP Recovery Amount
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{ item.Recovery_Amount }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Status
                          </div>
                          <section
                            class="d-flex align-center justify-space-between"
                          >
                            <div
                              class="d-flex align-center justify-space-around"
                            >
                              <span
                                id="w-80"
                                class="text-subtitle-1 font-weight-regular d-flex justify-center align-center"
                                :class="getStatusClass(item.Approval_Status)"
                                >{{ item.Approval_Status }}</span
                              >
                            </div>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Action
                          </div>
                          <section
                            v-if="
                              item.Approval_Status == 'Applied' ||
                              item.Approval_Status == 'Approved'
                            "
                            class="d-flex align-center justify-space-between"
                          >
                            <div
                              v-if="formAccess.delete"
                              class="d-flex align-center justify-end"
                            >
                              <v-avatar
                                :class="
                                  formAccess.delete
                                    ? 'cursor-pointer'
                                    : 'cursor-not-allow'
                                "
                                size="30"
                                color="red"
                                style="z-index: 0"
                                @click.stop="
                                  formAccess.delete
                                    ? openWarningPopup(item, 'fas fa-trash')
                                    : {}
                                "
                              >
                                <v-icon size="15" color="white"
                                  >fas fa-trash-alt</v-icon
                                >
                              </v-avatar>
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewLopRecovery
                    :selectedItem="selectedItem"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>

                <v-col
                  :cols="lopRecoveryData.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264 && !listLoading"
                >
                  <AddEditLopRecovery
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :isListEmpty="isListEmpty"
                    :formName="formName"
                    @close-form="closeAllForms()"
                    @edit-updated="refetchList()"
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>

      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      :confirmation-heading="warningText"
      :icon-name="warningIconClass"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="deleteRecord(deleteItem)"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditLopRecovery
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :isListEmpty="isListEmpty"
        :formName="formName"
        @close-form="closeAllForms()"
        @edit-updated="refetchList()"
      />
      <ViewLopRecovery
        v-if="showViewForm"
        :selectedItem="selectedItem"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewLopRecovery = defineAsyncComponent(() =>
  import("./ViewLopRecovery.vue")
);
const AddEditLopRecovery = defineAsyncComponent(() =>
  import("./AddEditLopRecovery.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
// Queries
import {
  RETRIEVE_LOP_RECOVERY_DETAILS,
  DELETE_LOP_REQUEST,
} from "@/graphql/employee-self-service/lopRecoveryQueries.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import moment from "moment";
import { convertUTCToLocal } from "@/helper.js";
export default {
  name: "LopRecovery",
  props: {
    formName: {
      type: String,
      required: true,
    },
    formId: {
      type: String,
      required: true,
    },
  },
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ViewLopRecovery,
    AddEditLopRecovery,
  },
  mixins: [FileExportMixin],
  data: () => ({
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    isLoading: false,
    isEdit: false,
    showViewForm: false,

    showAddEditForm: false,
    selectedData: [],

    currentTabItem: "tab-0",
    isLopConfigurationNotExist: false,
    lopRecoveryData: [],
    selectedItem: null,
    selectedCoverageType: 0,
    validationMessages: [],
    showValidationAlert: false,
    isMounted: false,

    emptyFilterScreen: false,
    backupLopRecoveryDetails: [],
    openMoreMenu: false,
    openWarningModal: false,
    warningText: "Are you sure to delete the LOP recovery ?",
    deleteItem: null,
    selectedStatus: [],
    selectedLopRecoveryProcessingMonth: [],
  }),
  computed: {
    landedFormName() {
      return "LOP Recovery";
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
    isListEmpty() {
      return this.backupLopRecoveryDetails.length == 0;
    },
    formAccess() {
      let lopRecoveryAccess = this.accessRights(this.formId);
      if (
        lopRecoveryAccess &&
        lopRecoveryAccess.accessRights &&
        lopRecoveryAccess.accessRights["view"]
      ) {
        return lopRecoveryAccess.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    approvalsFormAccess() {
      // 184 - approval management form access
      let lopRecoveryAccess = this.accessRights("184");
      if (
        lopRecoveryAccess &&
        lopRecoveryAccess.accessRights &&
        lopRecoveryAccess.accessRights["view"]
      ) {
        return true;
      } else return false;
    },
    mainTabs() {
      if (this.formId == "256") {
        if (this.approvalsFormAccess) {
          return [this.landedFormName, "Approvals"];
        } else {
          return [this.landedFormName];
        }
      } else {
        return [this.landedFormName];
      }
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    headers() {
      if (this.isSmallTable) {
        return [
          {
            title: "Employee Name",
            align: "start",
            key: "Employee_Name",
          },
          {
            title: "LOP Recovery Processing Month",
            key: "Salary_Month",
          },

          {
            title: "Total Recovery Days",
            key: "Total_Lop_Recovery_Days",
          },
        ];
      } else {
        return [
          {
            title: "Employee Name",
            key: "Employee_Name",
          },
          {
            title: "LOP Recovery Processing Month",
            align: "start",
            key: "Salary_Month",
          },
          {
            title: "Total Recovery Days",
            key: "Total_Lop_Recovery_Days",
          },
          {
            title: "Total LOP Recovery Amount",
            key: "Recovery_Amount",
          },
          {
            title: "Status",
            key: "Approval_Status",
          },
          { title: "Action", key: "action" },
        ];
      }
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    formatDate() {
      return (date, withTime = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    recoveryProcessingMonthList() {
      const salaryMonths = this.backupLopRecoveryDetails.map(
        (item) => item.Salary_Month
      );
      const uniqueSalaryMonths = [...new Set(salaryMonths)];
      return uniqueSalaryMonths;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
    this.isMounted = true;
  },
  methods: {
    convertUTCToLocal,
    onTabChange(tabName) {
      if (tabName !== this.landedFormName) {
        this.isLoading = true;
        this.$router.push("/approvals/approval-management?form_id=253");
      }
    },
    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onAdd() {
      this.isEdit = false;
      this.showAddEditForm = true;
      this.showViewForm = false;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    resetFilter() {
      this.selectedStatus = [];
      this.emptyFilterScreen = false;
      this.selectedLopRecoveryProcessingMonth = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.lopRecoveryData = this.backupLopRecoveryDetails;
    },
    applyFilter(filteredArray) {
      let filteredList = filteredArray;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedStatus.includes(item.Status);
        });
      }
      if (this.selectedLopRecoveryProcessingMonth.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedLopRecoveryProcessingMonth.includes(
            item.Salary_Month
          );
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.lopRecoveryData = filteredList;
      if (this.lopRecoveryData.length == 0) {
        this.emptyFilterScreen = true;
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.lopRecoveryData = this.backupLopRecoveryDetails;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupLopRecoveryDetails;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.lopRecoveryData = searchItems;
        if (this.lopRecoveryData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    exportReportFile() {
      let lopRecoveryList = this.lopRecoveryData.flatMap((item) => {
        const lopDaysForRecovery = this.formLopDaysForRecovery(item);

        return lopDaysForRecovery.map((lopDay, index) => ({
          userDefinedEmpId: item.userDefinedEmpId,
          Employee_Name: item.Employee_Name,
          Employee_Type: item.Employee_Type,
          Salary_Month: item.Salary_Month,
          LOP_Days_For_Recovery: lopDay,
          Recovery_Amount: item.All_Recovery_Amount.split(",")[index],
          Location_Name: item.Location_Name,
          Designation_Name: item.Designation_Name,
          Department_Name: item.Department_Name,
          remark: item.remark,
          Approval_Status: item.Approval_Status,
          Added_By: item.Added_By,
          Added_On: this.convertUTCToLocal(item.Added_On),
          Updated_By: item.Updated_By,
          Updated_On: item.Updated_On
            ? this.convertUTCToLocal(item.Updated_On)
            : "",
          Approved_On: item.Approved_On
            ? this.convertUTCToLocal(item.Approved_On)
            : "",
          Approved_By_Name: item.Approved_By_Name,
          Duration: parseFloat(item.Duration.split(",")[index]),
        }));
      });
      let fileName = "LOP Recovery";
      let exportHeaders = [
        { header: "Employee Id", key: "userDefinedEmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        {
          header: "Designation",
          key: "Designation_Name",
        },

        {
          header: "Department",
          key: "Department_Name",
        },
        {
          header: "Employee Type",
          key: "Employee_Type",
        },
        { header: "Location", key: "Location_Name" },
        {
          header: "LOP Recovery Processing Month",
          key: "Salary_Month",
        },
        { header: "LOP Days For Recovery", key: "LOP_Days_For_Recovery" },
        { header: "Duration", key: "Duration" },
        { header: "Recovery Amount", key: "Recovery_Amount" },

        { header: "Remarks", key: "remark" },
        { header: "Status", key: "Approval_Status" },
        { header: "Added By", key: "Added_By" },
        { header: "Added On", key: "Added_On" },
        { header: "Update By", key: "Updated_By" },
        { header: "Update On", key: "Updated_On" },

        { header: "Approved By", key: "Approved_By_Name" },
        { header: "Approved On", key: "Approved_On" },
      ];
      let exportOptions = {
        fileExportData: lopRecoveryList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    formLopDaysForRecovery(newData) {
      // Split the relevant properties into arrays
      const startDateArray = newData.Start_Date.split(",");
      const reasonArray = newData.Reason.split(",");
      const durationArray = newData.Duration.split(",");

      // Create the desired output array
      let lopDaysForRecovery = startDateArray.map((startDate, index) => {
        const duration = parseFloat(durationArray[index]);
        const reason = reasonArray[index];

        let durationLabel = "";

        if (duration === 1) {
          durationLabel = "Full Day";
        } else if (duration === 0.5) {
          durationLabel = "Half a Day";
        }

        return `${this.formatDate(startDate)} - ${reason} (${durationLabel})`;
      });

      return lopDaysForRecovery;
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_LOP_RECOVERY_DETAILS,
          variables: {
            selfService: this.formName == "MyTeam" ? 0 : 1,
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveLopRecoveryDetails) {
            vm.lopRecoveryData =
              response.data.retrieveLopRecoveryDetails.lopRecoveryDetails;
            vm.backupLopRecoveryDetails = vm.lopRecoveryData;
            if (vm.lopRecoveryData.length) {
              vm.applyFilter(vm.lopRecoveryData);
            }

            vm.listLoading = false;
          } else {
            vm.handleListError((err = ""));
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "LOP recovery",
          isListError: true,
        })
        .then((errorMessages) => {
          // ELR0013 - when lop was not configured/enabled
          if (errorMessages.includes("ELR0013")) {
            this.isLopConfigurationNotExist = true;
          }
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    settingsRedirection() {
      this.$router.push("/settings/core-hr/lop-recovery");
    },

    refetchList() {
      this.isErrorInList = false;
      this.isLopConfigurationNotExist = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },
    getStatusClass(status) {
      if (status === "Approved") {
        return "text-green";
      } else if (status === "Paid") {
        return "text-amber";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    // function close the warning modal
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.deleteItem = null;
    },
    openWarningPopup(item, warningIcon) {
      if (item === null) {
        this.warningIconClass = warningIcon;
        this.openWarningModal = false;
        return;
      }
      this.warningIconClass = warningIcon;
      this.openWarningModal = true;
      this.deleteItem = item;
    },
    //function to delete  record
    deleteRecord() {
      this.openWarningModal = false;
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_LOP_REQUEST,
            variables: {
              lopRecoveryID: vm.deleteItem.LOP_Recovery_Id
                ? parseInt(vm.deleteItem.LOP_Recovery_Id)
                : 0,
              selfService: this.formName == "MyTeam" ? 0 : 1,
            },
            client: "apolloClientAD",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "LOP recovery deleted successfully.",
            };
            vm.showAlert(snackbarData);
            vm.refetchList();
          })
          .catch((error) => {
            vm.handleDeleteError(error);
          });
      } catch {
        vm.handleDeleteError();
      }
    },
    handleDeleteError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: "lop recovery",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>

<style scoped>
.lop-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}

.lop-border-color {
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .lop-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
