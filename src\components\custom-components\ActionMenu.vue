<template>
  <v-menu class="ma-2">
    <template v-slot:activator="{ props }">
      <v-icon
        class="ma-2 mt-4"
        :color="iconColor"
        icon="fas fa-ellipsis-v"
        v-bind="props"
        style="width: 3em"
      ></v-icon>
    </template>
    <v-card class="pa-2 rounded-lg" min-width="170">
      <div v-for="action of actions" :key="action + 'action-menu'">
        <v-tooltip
          v-if="disableAction(action)"
          :text="tooltipText(action)"
          open-on-click
        >
          <template v-slot:activator="{ props }">
            <div v-bind="props" class="pa-1 cursor-not-allowed">
              <div class="pa-3 rounded-lg bg-grey-lighten-4">
                {{ action }}
              </div>
            </div>
          </template>
        </v-tooltip>
        <v-tooltip
          v-else-if="tooltipAction(action)"
          :text="tooltipText(action)"
          open-on-click
        >
          <template v-slot:activator="{ props }">
            <div
              v-bind="props"
              :class="
                disableAction(action) ? 'cursor-not-allowed' : 'cursor-pointer'
              "
              class="pa-1"
              @click="disableAction(action) ? {} : onChangeAction(action)"
            >
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <div
                    v-bind="props"
                    class="pa-3 rounded-lg"
                    :class="isHovering ? 'bg-hover' : 'bg-grey-lighten-4'"
                  >
                    {{ action }}
                  </div>
                </template>
              </v-hover>
            </div>
          </template>
        </v-tooltip>
        <div v-else class="pa-1 cursor-pointer" @click="onChangeAction(action)">
          <v-hover>
            <template v-slot:default="{ isHovering, props }">
              <div
                v-bind="props"
                class="pa-3 rounded-lg"
                :class="isHovering ? 'bg-hover' : 'bg-grey-lighten-4'"
              >
                {{ action }}
              </div>
            </template>
          </v-hover>
        </div>
      </div>
    </v-card>
  </v-menu>
</template>

<script>
export default {
  name: "ActionMenu",

  emits: ["selected-action"],

  props: {
    actions: {
      type: Array,
      default: function () {
        return ["Edit" /*"Delete"*/];
      },
    },
    disableActionButtons: {
      type: Array,
      default: function () {
        return [];
      },
    },
    tooltipActionButtons: {
      type: Array,
      default: function () {
        return [];
      },
    },
    iconColor: {
      type: String,
      default: "black",
    },
    accessRights: {
      type: Object,
      required: false,
      default: () => {
        return {};
      },
    },
    tooltipMessage: {
      type: String,
      default: "",
    },
    isPresentTooltip: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    onChangeAction(action) {
      this.$emit("selected-action", action);
    },
    disableAction(action) {
      if (action && this.isPresentTooltip) {
        let accessAction =
          action.toLowerCase() === "edit" ||
          action.toLowerCase() === "edit flow"
            ? "update"
            : action.toLowerCase() === "history"
            ? "view"
            : action.toLowerCase();
        if (this.accessRights && !this.accessRights[accessAction]) {
          return true;
        } else if (this.tooltipMessage) {
          return false;
        } else {
          return false;
        }
      } else if (
        this.disableActionButtons &&
        this.disableActionButtons.length &&
        this.disableActionButtons.filter(
          (actionItem) => actionItem.toLowerCase() === action.toLowerCase()
        ).length
      ) {
        return true;
      } else {
        return false;
      }
    },
    tooltipAction(action) {
      if (
        this.tooltipActionButtons &&
        this.tooltipActionButtons.length &&
        this.tooltipActionButtons.filter((actionItem) => actionItem === action)
          .length
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  computed: {
    tooltipText() {
      return (action) => {
        if (this.isPresentTooltip) {
          let actionInLowerCase = action.toLowerCase();
          if (this.tooltipMessage && !this.disableAction(action)) {
            let updatedText = this.tooltipMessage.replace(
              /changeAction/g,
              actionInLowerCase
            );
            return updatedText;
          } else {
            return `You don't have access to perform this action`;
          }
        } else if (this.tooltipMessage && this.tooltipMessage.length) {
          return this.tooltipMessage;
        }
        return "";
      };
    },
  },
};
</script>

<style></style>
