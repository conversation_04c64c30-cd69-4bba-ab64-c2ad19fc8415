<template>
  <div v-if="isMounted">
    <span
      class="d-flex"
      :class="
        educationFormData.length > 0
          ? 'justify-end mt-n4'
          : 'justify-start ml-n3 mt-2 mb-n2'
      "
    >
      <v-btn color="primary" variant="text" @click="onAddNew()">
        <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add New</v-btn
      >
    </span>
    <v-form ref="addEditEducationForm">
      <div v-for="(certificate, i) in educationFormData" :key="'exp' + i">
        <v-row align="center">
          <v-col cols="11">
            <v-row>
              <v-col
                cols="12"
                md="6"
                v-if="labelList[128].Field_Visiblity == 'Yes'"
              >
                <CustomSelect
                  :items="courseList"
                  :label="labelList[128].Field_Alias"
                  v-if="labelList[128].Predefined === 'No'"
                  :itemSelected="
                    educationFormData[i].Education_Type_Id
                      ? parseInt(educationFormData[i].Education_Type_Id)
                      : null
                  "
                  :rules="[
                    labelList[128].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[128].Field_Alias,
                          educationFormData[i].Education_Type_Id
                        )
                      : true,
                  ]"
                  itemValue="Course_Id"
                  itemTitle="Course_Name"
                  :isRequired="labelList[128].Mandatory_Field === 'Yes'"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Education_Type_Id', i)
                  "
                  :isAutoComplete="true"
                  :isLoading="courseListFetching"
                  :noDataText="
                    courseListFetching ? 'Loading...' : 'No data available'
                  "
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="6"
                v-if="labelList[175].Field_Visiblity == 'Yes'"
              >
                <CustomSelect
                  v-if="labelList[175].Predefined == 'Yes'"
                  :items="instituteList"
                  :label="labelList[175].Field_Alias"
                  :itemSelected="
                    educationFormData[i].Institution_Id
                      ? parseInt(educationFormData[i].Institution_Id)
                      : null
                  "
                  :rules="[
                    labelList[175].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[175].Field_Alias,
                          educationFormData[i].Institution_Id
                        )
                      : true,
                  ]"
                  :isRequired="labelList[175].Mandatory_Field == 'Yes'"
                  itemValue="Institution_Id"
                  itemTitle="Institution"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Institution_Id', i)
                  "
                  :isAutoComplete="true"
                  :isLoading="splInstituteLoading"
                  :noDataText="
                    splInstituteLoading ? 'Loading...' : 'No data available'
                  "
                ></CustomSelect>
                <v-text-field
                  v-else
                  v-model="educationFormData[i].Institute_Name"
                  :rules="[
                    labelList[175].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[175].Field_Alias}`,
                          educationFormData[i].Institute_Name
                        )
                      : true,
                    educationFormData[i].Institute_Name
                      ? validateWithRulesAndReturnMessages(
                          educationFormData[i].Institute_Name,
                          'instituteName',
                          `${labelList[175].Field_Alias}`
                        )
                      : true,
                  ]"
                  variant="solo"
                  @update:model-value="onChangeFields"
                  ><template v-slot:label>
                    {{ labelList[175].Field_Alias
                    }}<span
                      style="color: red"
                      v-if="labelList[175].Mandatory_Field == 'Yes'"
                    >
                      *</span
                    >
                  </template></v-text-field
                >
              </v-col>
              <v-col
                cols="12"
                md="6"
                v-if="labelList[174].Field_Visiblity == 'Yes'"
              >
                <CustomSelect
                  v-if="labelList[174].Predefined === 'Yes'"
                  :items="specializationList"
                  :label="labelList[174].Field_Alias"
                  :itemSelected="
                    educationFormData[i].Specialization_Id
                      ? parseInt(educationFormData[i].Specialization_Id)
                      : null
                  "
                  :rules="[
                    labelList[174].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[174].Field_Alias,
                          educationFormData[i].Specialization_Id
                        )
                      : true,
                  ]"
                  :isRequired="labelList[174].Mandatory_Field === 'Yes'"
                  itemValue="Specialization_Id"
                  itemTitle="Specialization"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Specialization_Id', i)
                  "
                  :isAutoComplete="true"
                  :isLoading="splInstituteLoading"
                  :noDataText="
                    splInstituteLoading ? 'Loading...' : 'No data available'
                  "
                ></CustomSelect>
                <v-text-field
                  v-else
                  v-model="educationFormData[i].Specialisation"
                  :rules="[
                    labelList[174].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[174].Field_Alias}`,
                          educationFormData[i].Specialisation
                        )
                      : true,
                    educationFormData[i].Specialisation
                      ? validateWithRulesAndReturnMessages(
                          educationFormData[i].Specialisation,
                          'specialisation',
                          `${labelList[174].Field_Alias}`
                        )
                      : true,
                  ]"
                  variant="solo"
                  :isRequired="labelList[174].Mandatory_Field === 'Yes'"
                  @update:model-value="onChangeFields"
                  ><template v-slot:label>
                    {{ labelList[174].Field_Alias
                    }}<span
                      style="color: red"
                      v-if="labelList[174].Mandatory_Field == 'Yes'"
                    >
                      *</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <!-- Year of Start -->
              <v-col
                cols="12"
                md="6"
                v-if="labelList[294]?.Field_Visiblity?.toLowerCase() === 'yes'"
              >
                <CustomSelect
                  :items="yearList"
                  :label="labelList[294].Field_Alias"
                  :rules="[
                    labelList[294].Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[294].Field_Alias,
                          educationFormData[i].Year_Of_Start
                        )
                      : true,
                  ]"
                  variant="solo"
                  :isRequired="
                    labelList[294].Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  :itemSelected="educationFormData[i].Year_Of_Start"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Year_Of_Start', i)
                  "
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="6"
                v-if="labelList[179].Field_Visiblity == 'Yes'"
              >
                <CustomSelect
                  :items="yearList"
                  :label="labelList[179].Field_Alias"
                  :itemSelected="educationFormData[i].Year_Of_Passing"
                  :rules="[
                    labelList[179].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[179].Field_Alias,
                          educationFormData[i].Year_Of_Passing
                        )
                      : true,
                  ]"
                  :isRequired="labelList[179].Mandatory_Field === 'Yes'"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Year_Of_Passing', i)
                  "
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                md="6"
                v-if="labelList[177].Field_Visiblity == 'Yes'"
              >
                <v-text-field
                  v-model="educationFormData[i].Percentage"
                  :rules="[
                    labelList[177].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[177].Field_Alias,
                          educationFormData[i].Percentage
                        )
                      : true,
                    educationFormData[i].Percentage
                      ? validateWithRulesAndReturnMessages(
                          educationFormData[i].Percentage,
                          'percentage',
                          labelList[177].Field_Alias
                        )
                      : true,
                  ]"
                  :isRequired="labelList[177].Mandatory_Field == 'Yes'"
                  variant="solo"
                  type="number"
                  @update:model-value="onChangeFields"
                  ><template v-slot:label>
                    {{ labelList[177].Field_Alias
                    }}<span
                      style="color: red"
                      v-if="labelList[177].Mandatory_Field == 'Yes'"
                    >
                      *</span
                    >
                  </template></v-text-field
                >
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="1" class="d-flex justify-center">
            <v-hover>
              <template v-slot:default="{ isHovering, props }">
                <v-icon
                  v-bind="props"
                  :color="isHovering ? 'red' : 'grey'"
                  @click="onDelate(i)"
                  >fas fa-trash</v-icon
                >
              </template>
            </v-hover></v-col
          >
        </v-row>
        <v-divider class="my-4"></v-divider>
      </div>
    </v-form>
  </div>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  LIST_COURSE,
  LIST_SPECIALIZATION_INSTITUTE,
} from "@/graphql/dropDownQueries";

export default {
  name: "AddEditEducationDetails",
  mixins: [validationRules],
  props: {
    educationDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: null,
    },
    jobPostId: {
      type: Number,
      default: 0,
    },
    isUserLogedIn: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    CustomSelect,
  },
  data() {
    return {
      educationFormData: [],
      // list
      courseListFetching: false,
      courseList: [],
      specializationList: [],
      instituteList: [],
      splInstituteLoading: false,
      // edit
      isFormDirty: false,
      isMounted: false,
    };
  },

  mounted() {
    if (
      this.educationDetails &&
      Object.keys(this.educationDetails).length > 0
    ) {
      this.educationFormData = this.educationDetails;
    } else {
      this.educationFormData = [];
    }
    this.isMounted = true;
    this.retrieveEducationCourse();
    this.retrieveSplInstitute();
  },
  watch: {
    educationDetails(val) {
      if (val && Object.keys(val).length > 0) {
        this.educationFormData = val;
      } else {
        this.educationFormData = [];
      }
    },
    educationFormData: {
      deep: true,
      handler(newVal) {
        if (newVal?.length)
          this.$emit("update-education-details", newVal, "education");
      },
    },
  },
  emits: ["update-education-details"],

  // function for getting the available years
  computed: {
    yearList() {
      let empDob = this.selectedCandidateDOB;
      let year = 0;
      if (empDob) {
        year = new Date().getFullYear() - new Date(empDob).getFullYear();
      } else {
        year = 80;
      }
      const now = new Date().getUTCFullYear();
      const years = Array(now - (now - year))
        .fill("")
        .map((v, idx) => now - idx);
      return years;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field, index) {
      this.onChangeFields();
      this.educationFormData[index][field] = value;
      if (field === "Specialization_Id") {
        this.educationFormData[index]["Specialisation"] =
          this.specializationList.find(
            (item) => item.Specialization_Id === value
          ).Specialization;
      }
      if (field === "Education_Type_Id") {
        this.educationFormData[index]["Education_Type"] = this.courseList.find(
          (el) => el.Course_Id === value
        )?.Course_Name;
      }
      const startYear = parseInt(this.educationFormData[index].Year_Of_Start);
      const passingYear = parseInt(
        this.educationFormData[index].Year_Of_Passing
      );
      if (startYear && passingYear && startYear > passingYear) {
        if (field === "Year_Of_Start") {
          this.educationFormData[index].Year_Of_Passing = null;
        } else if (field === "Year_Of_Passing") {
          this.educationFormData[index].Year_Of_Start = null;
        }
      }
    },

    onAddNew() {
      this.educationFormData.push({
        Education_Type: "",
        Education_Type_Id: null,
        Specialisation: "",
        Institute_Name: "",
        Year_Of_Start: null,
        Year_Of_Passing: null,
        Percentage: 0,
        Specialisation_Id: 0,
        Institute_Id: 0,
        Start_Date: "",
        End_Date: "",
        City: "",
        State: "",
        Country: "",
      });
    },

    onDelate(index) {
      this.educationFormData.splice(index, 1);
    },

    async validateEducationDetails() {
      if (this.educationFormData.length > 0) {
        const { valid } = await this.$refs.addEditEducationForm.validate();
        if (valid) {
          let educationDetails = this.educationFormData;
          for (let i = 0; i < this.educationFormData.length; i++) {
            let selectedEduType = this.courseList.filter(
              (el) =>
                el.Course_Id == this.educationFormData[i].Education_Type_Id
            );
            let selectedSpl = this.specializationList.filter(
              (el) =>
                el.Specialization_Id ==
                this.educationFormData[i].Specialization_Id
            );
            let selectedIns = this.instituteList.filter(
              (el) =>
                el.Institution_Id == this.educationFormData[i].Institution_Id
            );
            educationDetails[i]["Education_Type"] =
              selectedEduType.length > 0 ? selectedEduType[0].Course_Name : "";
            educationDetails[i]["Specialisation"] = educationDetails[i][
              "Specialisation"
            ]
              ? educationDetails[i]["Specialisation"]
              : selectedSpl.length > 0
              ? selectedSpl[0].Specialization
              : "";
            educationDetails[i]["Institute_Name"] = educationDetails[i][
              "Institute_Name"
            ]
              ? educationDetails[i]["Institute_Name"]
              : selectedIns.length > 0
              ? selectedIns[0].Institution
              : "";
          }
          return educationDetails;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },

    retrieveEducationCourse() {
      let vm = this;
      vm.courseListFetching = true;
      vm.$apollo
        .query({
          query: LIST_COURSE,
          client:
            vm.jobPostId || vm.isUserLogedIn
              ? "apolloClientAP"
              : "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listCourseDetails &&
            !response.data.listCourseDetails.errorCode
          ) {
            const { courseDetails } = response.data.listCourseDetails;
            vm.courseList =
              courseDetails && courseDetails.length > 0 ? courseDetails : [];
          }
          vm.courseListFetching = false;
        })
        .catch(() => {
          vm.courseListFetching = false;
        });
    },

    retrieveSplInstitute() {
      let vm = this;
      vm.splInstituteLoading = true;
      vm.$apollo
        .query({
          query: LIST_SPECIALIZATION_INSTITUTE,
          client: "apolloClientAS",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveListEduInstitutionAndSpecialization &&
            !response.data.retrieveListEduInstitutionAndSpecialization.errorCode
          ) {
            const { institution, specialization } =
              response.data.retrieveListEduInstitutionAndSpecialization;
            vm.specializationList = specialization;
            vm.instituteList = institution;
            for (let i = 0; i < this.educationFormData.length; i++) {
              if (!this.educationFormData[i].Specialisation_Id) {
                let selectedSpl = this.specializationList.filter(
                  (el) =>
                    el.Specialization.toLowerCase() ==
                    this.educationFormData[i].Specialisation.toLowerCase()
                );
                if (selectedSpl.length > 0) {
                  this.educationFormData[i].Specialisation_Id =
                    selectedSpl[0].Specialization_Id;
                }
              }
              if (!this.educationFormData[i].Institution_Id) {
                let selectedIns = this.instituteList.filter((el) => {
                  return (
                    el.Institution.toLowerCase() ==
                    this.educationFormData[i].Institute_Name.toLowerCase()
                  );
                });
                if (selectedIns.length > 0) {
                  this.educationFormData[i].Institution_Id =
                    selectedIns[0].Institution_Id;
                }
              }
            }
          }
          vm.splInstituteLoading = false;
        })
        .catch(() => {
          vm.splInstituteLoading = false;
        });
    },
  },
};
</script>
