import gql from "graphql-tag";

///////////////////
// Queries ///////
//////////////////

export const GET_MY_PAYSLIP = gql`
  query listSalaryPayslip($year: Int!, $formId: Int) {
    listSalaryPayslip(year: $year, formId: $formId) {
      errorCode
      message
      payslipDetails
      payrollStartDate
      employeeDateOfJoin
    }
  }
`;

export const GENERATE_MY_PAYSLIP = gql`
  mutation generateSyntrumPayslipDetails(
    $formId: Int
    $employeeId: Int
    $month: Int!
    $year: Int!
  ) {
    generateSyntrumPayslipDetails(
      formId: $formId
      employeeId: $employeeId
      month: $month
      year: $year
    ) {
      errorCode
      message
      data
    }
  }
`;
