<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
      ></AppTopBarTab>
    </div>
    <v-container fluid class="job-postings" v-if="formAccess.view">
      <div class="text-subtitle-1 text-grey">Job Portals</div>
      <v-row>
        <v-col cols="12" sm="12" md="4" xl="3">
          <CompanyCard
            :logo="linkedInLogo"
            :description="linkedin"
            CompanyName="LinkedIn"
            :statusArray="statusArray"
            hyperLink="https://www.linkedin.com/help/linkedin/answer/a415420"
            @refetch="fetchIntegrationStatus()"
          >
          </CompanyCard>
        </v-col>
        <v-col cols="12" sm="12" md="4" xl="3">
          <CompanyCard
            :logo="jobStreetLogo"
            :description="jobStreet"
            CompanyName="JobStreet"
            :statusArray="statusArray"
            hyperLink="https://talent.seek.com.au/partners/link-your-software/"
            @refetch="fetchIntegrationStatus()"
          >
          </CompanyCard>
        </v-col>
        <v-col cols="12" sm="12" md="4" xl="3">
          <CompanyCard
            :logo="indeedLogo"
            :description="indeed"
            CompanyName="Indeed"
            :statusArray="statusArray"
            hyperLink="https://docs.indeed.com/api/job/objects/SourcedJobPostingJobSourceInput#fields"
            @refetch="fetchIntegrationStatus()"
          >
          </CompanyCard>
        </v-col>
        <v-col cols="12" sm="12" md="4" xl="3">
          <CompanyCard
            :logo="irukkaLogo"
            :description="irukka"
            CompanyName="Irukka"
            :statusArray="statusArray"
            @refetch="fetchIntegrationStatus()"
          >
          </CompanyCard>
        </v-col>
      </v-row>
      <div class="text-subtitle-1 text-grey mt-7">Calendar Sync</div>
      <v-row>
        <v-col cols="12" sm="12" md="4" xl="3">
          <v-card
            :style="isMobileView ? '' : 'max-width:350px'"
            class="rounded-lg py-3"
          >
            <div
              class="ml-3 mr-7 d-flex justify-space-between"
              style="height: 30px"
            >
              <div class="d-flex align-center" style="width: 200px">
                <img
                  :src="outlookLogo"
                  style="width: 30px; height: auto"
                  class=""
                  :alt="'Outlook'"
                />
                <p class="ml-2 font-weight-bold" style="height: max-content">
                  Outlook 365 Calendar
                </p>
              </div>
              <div class="d-flex justify-end align-center">
                <v-tooltip text="You don't have access to perform this action">
                  <template v-slot:activator="{ props }">
                    <v-switch
                      density="compact"
                      v-bind="formAccess.add && isAdmin ? '' : props"
                      :readonly="formAccess.add && isAdmin ? false : true"
                      color="primary"
                      true-value="Active"
                      false-value="Inactive"
                      hide-details
                      v-model="microsoftStatus.Integration_Status"
                      @update:model-value="fetchAuthToken($event, 'Microsoft')"
                    ></v-switch>
                  </template>
                </v-tooltip>
              </div>
            </div>
            <div style="background-color: rgb(***********)" class="pa-3 my-2">
              <div bg-color="grey-lighten-2" style="font-size: small">
                {{ microsoftDescription }}
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
      <div class="text-subtitle-1 text-grey mt-7">Online Meeting Platforms</div>
      <v-row class="mb-4">
        <v-col cols="12" sm="12" md="4" xl="3">
          <v-card
            :style="isMobileView ? '' : 'max-width:350px'"
            class="rounded-lg py-3"
          >
            <div
              class="ml-3 mr-7 d-flex justify-space-between"
              style="height: 30px"
            >
              <div class="d-flex align-center" style="width: 200px">
                <img
                  :src="teamsLogo"
                  style="width: 30px; height: auto"
                  class=""
                  :alt="'Outlook'"
                />
                <p class="ml-2 font-weight-bold" style="height: max-content">
                  Microsoft Teams
                </p>
              </div>
              <div class="d-flex justify-end align-center">
                <v-tooltip text="You don't have access to perform this action">
                  <template v-slot:activator="{ props }">
                    <v-switch
                      density="compact"
                      v-bind="formAccess.add && isAdmin ? '' : props"
                      :readonly="formAccess.add && isAdmin ? false : true"
                      color="primary"
                      true-value="Active"
                      false-value="Inactive"
                      hide-details
                      v-model="teamsStatus.Integration_Status"
                      @update:model-value="fetchAuthToken($event, 'Teams')"
                    ></v-switch>
                  </template>
                </v-tooltip>
              </div>
            </div>
            <div style="background-color: rgb(***********)" class="pa-3 my-2">
              <div bg-color="grey-lighten-2" style="font-size: small">
                {{ teamsDescription }}
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
    <AppAccessDenied v-else></AppAccessDenied>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import CompanyCard from "./CompanyCard.vue";
import linkedIn from "../../../../assets/images/common/LinkedIn-Logo.png";
import jobStreet from "../../../../assets/images/common/JobStreet-Logo.png";
import Indeed from "../../../../assets/images/common/Indeed-Logo.webp";
import Irruka from "../../../../assets/images/common/Irukka-Logo.png";
import outlookLogo from "@/assets/images/common/outlook-logo.png";
import teamsLogo from "@/assets/images/common/teams-logo.png";

import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
import {
  ADD_UPDATE_RECRUITMENT_STATUS,
  GET_MICROSOFT_CREDENTIALS,
} from "@/graphql/settings/Integration/jobPostIntegrationQueries";
import { UPDATE_MY_INTEGRATION } from "@/graphql/recruitment/myIntegrationQueries.js";
import { PublicClientApplication } from "@azure/msal-browser";
import Config from "../../../../config.js";

export default {
  name: "JobPostIntegration",
  data: () => {
    return {
      tab: null,
      linkedInLogo: linkedIn,
      jobStreetLogo: jobStreet,
      indeedLogo: Indeed,
      irukkaLogo: Irruka,
      outlookLogo: outlookLogo,
      teamsLogo: teamsLogo,
      currentTabItem: "0",
      statusArray: [],
      isLoading: false,
      clientId: "",
      microsoftStatus: {
        Integration_Id: 0,
        Integration_Type: "Microsoft",
        Integration_Status: "Inactive",
      },
      teamsStatus: {
        Integration_Id: 0,
        Integration_Type: "Teams Meeting",
        Integration_Status: "Inactive",
      },
      userEmail: "",
      teamsDescription:
        "Microsoft Teams is a communication platform developed as part of the Microsoft family of products. It is a video conferencing and communication service.",
      microsoftDescription:
        "Outlook 365 allows organizations to maintaining schedules. Let your talent acquisition team and hiring panel members stay in sync with interview schedules. You can check interviewer's availability, schedule or cancel interviews.",
      linkedin:
        "Directly publish the jobs from recruitment module to LinkedIn with this integration. This integration or publishing to LinkedIn is free. This integration requires a company id.",
      indeed:
        "Directly publish the jobs from recruitment module to Indeed with this integration. This integration or publishing to Indeed requires you to have an active account in Indeed.",
      jobStreet:
        "Directly publish the jobs from recruitment module to Seek / JobStreet with this integration. This integration requires a registered account on JobStreet with Hirer ID activated.",
      irukka:
        "Irukka, your employment-centric social platform, simplifies job postings and connects with the right candidates. Ensure your profile's security with OTP verification; click 'Configure' to begin",
    };
  },
  components: {
    CompanyCard,
  },
  computed: {
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    redirectUri() {
      return `${this.baseUrl}v3/recruitment/my-integration`;
    },
    formAccess() {
      let formAccess = this.accessRights("242");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"] &&
        this.isAdmin
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    mainTabs() {
      return ["Recruitment"];
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    this.fetchIntegrationStatus();
    this.getMicrosoftCredentials();
  },
  methods: {
    fetchIntegrationStatus() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 242,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            let { getStatus } = response.data.jobBoardIntegrationStatus;
            vm.statusArray = getStatus;
            for (let iStatus of getStatus) {
              if (iStatus.Integration_Type === "Microsoft") {
                vm.microsoftStatus = iStatus;
              }
              if (iStatus.Integration_Type === "Teams Meeting") {
                vm.teamsStatus = iStatus;
              }
            }
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "integration status",
        isListError: false,
      });
    },
    addUpdateRecruitmentStatus(statusObj) {
      this.isLoading = true;
      let vm = this;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_RECRUITMENT_STATUS,
          variables: {
            Integration_Id: statusObj.Integration_Id,
            Integration_Type: statusObj.Integration_Type,
            Integration_Status: statusObj.Integration_Status,
          },
          client: "apolloClientAH",
        })
        .then((res) => {
          if (res) {
            let snackbarData = {
              isOpen: true,
              message: "Integration status updated successfully.",
              type: "success",
            };
            vm.showAlert(snackbarData);
          }
          vm.isLoading = false;
          vm.overlay = false;
          vm.fetchIntegrationStatus();
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleAddUpdateIntegrationStatusError(err);
        });
    },
    handleAddUpdateIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "integration status",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getMicrosoftCredentials() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_MICROSOFT_CREDENTIALS,
          variables: {
            Type: "microsoft calendar",
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAWSCognitoIdentities &&
            response.data.getAWSCognitoIdentities.data &&
            response.data.getAWSCognitoIdentities.data.workwiselymsapplicationID
          ) {
            let { workwiselymsapplicationID } =
              response.data.getAWSCognitoIdentities.data;
            vm.clientId = workwiselymsapplicationID;
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleGetMicrosoftCredentialsError(err);
        });
    },
    handleGetMicrosoftCredentialsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "microsoft credentials",
        isListError: false,
      });
    },
    updateMicrosoftIntegrationStatus() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_MY_INTEGRATION,
          client: "apolloClientAH",
          variables: {
            action: "add",
            calendarStatus: this.microsoftStatus.Integration_Status,
            microsoftEmail: this.userEmail,
            teamsStatus: this.teamsStatus.Integration_Status,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.addUpdateMicrosoftIntegration &&
            !response.data.addUpdateMicrosoftIntegration.errorCode
          ) {
            let snackbarData = {
              isOpen: true,
              message: "Integration status updated successfully.",
              type: "success",
            };
            vm.showAlert(snackbarData);
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleUpdateIntegrationStatusError(err);
        });
    },
    handleUpdateIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "integration status",
        isListError: false,
      });
    },
    async fetchAuthToken(status, type) {
      if (status?.toLowerCase() === "active") {
        this.isLoading = true;
        const msalConfig = {
          auth: {
            clientId: this.clientId,
            authority: Config.microsoftLogin,
            redirectUri: this.redirectUri,
          },
          cache: {
            cacheLocation: "localStorage",
            storeAuthStateInCookie: true,
          },
        };
        let scopes = [];
        if (type?.toLowerCase() === "microsoft") {
          scopes = ["user.read", "calendars.readwrite"];
        } else if (type?.toLowerCase() === "teams") {
          scopes = ["user.read", "onlineMeetings.readwrite"];
        }
        const msalInstance = new PublicClientApplication(msalConfig);
        try {
          await msalInstance.initialize();
          let response = await msalInstance.loginPopup({
            scopes: scopes,
            prompt: "consent",
          });
          localStorage.setItem("outlookAccess", response.accessToken);
          this.userEmail = response.account.username;
          if (type.toLowerCase() === "microsoft") {
            this.addUpdateRecruitmentStatus(this.microsoftStatus);
          } else if (type.toLowerCase() === "teams") {
            this.addUpdateRecruitmentStatus(this.teamsStatus);
          }
          this.updateMicrosoftIntegrationStatus(status);
        } catch (err) {
          this.isLoading = false;
          if (type?.toLowerCase() === "teams") {
            this.teamsStatus.Integration_Status = "Inactive";
          }
          if (type?.toLowerCase() === "microsoft") {
            this.microsoftStatus.Integration_Status = "Inactive";
          }
          let error = err.message;
          if (!/(AADSTS65004|user_cancelled)/.test(error)) {
            let snackbarData = {
              isOpen: true,
              message: error,
              type: "warning",
            };
            this.showAlert(snackbarData);
          }
        }
      } else {
        if (type.toLowerCase() === "microsoft") {
          this.addUpdateRecruitmentStatus(this.microsoftStatus);
        } else if (type.toLowerCase() === "teams") {
          this.addUpdateRecruitmentStatus(this.teamsStatus);
        }
      }
    },
  },
};
</script>
<style>
.job-postings {
  padding: 5em 2em 0em 3em;
}
</style>
