<template>
  <div class="d-flex">
    <div class="d-flex align-center">
      <v-progress-circular
        model-value="100"
        color="secondary"
        :size="22"
        class="mr-2 m"
      ></v-progress-circular>
      <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold">
        {{ labelList[230]?.Field_Alias || "Accreditations" }} Details
      </span>
    </div>
    <span v-if="enableAdd" class="d-flex justify-end ml-auto">
      <v-btn color="primary" variant="text" @click="openAddForm()">
        <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
      >
    </span>
  </div>
  <v-dialog
    v-if="!isMobileView"
    v-model="showAddEditAccreditationsForm"
    max-width="70%"
  >
    <AddEditAccreditation
      :selectedAccreditationDetails="selectedAccreditationDetails"
      :selectedCandidateId="selectedCandidateId"
      :selectedCandidateDOB="selectedCandidateDOB"
      @close-accreditations-form="closeAddEditForm"
      @refetch-doc-accreditation-details="submitAccreditationForm"
    >
    </AddEditAccreditation>
  </v-dialog>

  <div v-if="!isMobileView" class="d-flex">
    <v-slide-group
      class="pa-4"
      selected-class="bg-secondary"
      prev-icon="fas fa-chevron-circle-left"
      next-icon="fas fa-chevron-circle-right"
      show-arrows
    >
      <v-slide-group-item>
        <ViewAccreditationDetails
          :accreditationDetails="accreditationDetails"
          :formAccess="formAccess"
          @on-open-edit="openEditForm"
          @on-delete="showDeleteConfirmation"
        />
      </v-slide-group-item>
    </v-slide-group>
  </div>
  <div v-else>
    <div class="card-container">
      <ViewAccreditationDetails
        :accreditationDetails="accreditationDetails"
        :formAccess="formAccess"
        @on-open-edit="openEditForm"
        @on-delete="showDeleteConfirmation"
      />
    </div>
  </div>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure you want to delete this record ?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onDeleteAccreditation()"
  >
  </AppWarningModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
// components
const AddEditAccreditation = defineAsyncComponent(() =>
  import("./AddEditAccreditation.vue")
);
const ViewAccreditationDetails = defineAsyncComponent(() =>
  import("./ViewAccreditationDetails.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AccreditationMain",
  components: {
    AddEditAccreditation,
    ViewAccreditationDetails,
  },
  props: {
    accreditationDetailsData: {
      type: Array,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
  },
  emits: ["refetch-doc-accreditation-details"],
  data: () => ({
    // add/edit
    showAddEditAccreditationsForm: false,
    selectedAccreditationDetails: {},
    // view
    accreditationDetails: [],
    // delete
    selectedAccreditationDeleteRecord: null,
    openWarningModal: false,
  }),
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    enableAdd() {
      return this.formAccess && this.formAccess.add;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.accreditationDetailsData &&
      this.accreditationDetailsData.length > 0
    ) {
      this.accreditationDetails = this.accreditationDetailsData;
    }
  },
  methods: {
    submitAccreditationForm() {
      this.closeAddEditForm();
      this.$emit("refetch-doc-accreditation-details");
    },
    closeAddEditForm() {
      this.showAddEditAccreditationsForm = false;
      this.selectedAccreditationDetails = {};
    },
    openAddForm() {
      mixpanel.track("Onboarded-candidate-accreditation-add-opened");
      this.selectedAccreditationDetails = {
        Accreditation_Category: "",
        Accreditation_Type: "",
        Identifier: "",
        File_Name: null,
        Received_Date: null,
        Expiry_Date: null,
      };
      this.showAddEditAccreditationsForm = true;
    },
    openEditForm(selectedItem) {
      mixpanel.track("Onboarded-candidate-accreditation-edit-opened");
      this.selectedAccreditationDetails = selectedItem;
      this.showAddEditAccreditationsForm = true;
    },
    showDeleteConfirmation(selectedItem) {
      this.selectedAccreditationDeleteRecord = selectedItem;
      this.openWarningModal = true;
    },
    onDeleteAccreditation(selectedItem) {
      this.selectedItem = selectedItem;
      this.openWarningModal = false;
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedItem = null;
    },
  },
};
</script>

<style scoped>
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  /* The grid-gap property adds a 10-pixel gap between the cards. */
  grid-gap: 10px;
}
@media (max-width: 600px) {
  .card-container {
    grid-template-columns: 1fr;
  }
}
</style>
