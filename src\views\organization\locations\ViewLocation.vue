<template>
  <v-card class="rounded-lg mt-2">
    <div
      class="d-flex align-center"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center pl-4 py-2">
        <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
          <v-icon class="text-primary" size="20">fas fa-map-marker-alt</v-icon>
        </v-avatar>
        <section style="max-width: 250px" class="text-truncate">
          <div class="text-subtitle-1 font-weight-bold d-flex align-center">
            <!-- Tooltip for Location Code (only if length > 5) -->
            <template
              v-if="
                selectedItem.Location_Code &&
                selectedItem.Location_Code.length > 5
              "
            >
              <v-tooltip location="bottom" :text="selectedItem.Location_Code">
                <template v-slot:activator="{ props }">
                  <v-card variant="flat" v-bind="props">
                    <span class="d-inline-block">
                      {{ selectedItem.Location_Code.slice(0, 5) + "..." }}
                    </span>
                  </v-card>
                </template>
              </v-tooltip>
            </template>
            <template v-else>
              <v-card variant="flat">
                <span class="d-inline-block">{{
                  selectedItem.Location_Code
                }}</span>
              </v-card>
            </template>

            <!-- Dash between Location Code and Location Name -->
            <span
              v-if="selectedItem.Location_Code && selectedItem.Location_Name"
              class="text-bold mx-1"
            >
              -
            </span>

            <!-- Tooltip for Location Name (only if length > 20) -->
            <template
              v-if="
                selectedItem.Location_Name &&
                selectedItem.Location_Name.length > 20
              "
            >
              <v-tooltip location="bottom" :text="selectedItem.Location_Name">
                <template v-slot:activator="{ props }">
                  <v-card variant="flat" v-bind="props">
                    <span class="d-inline-block">
                      {{ selectedItem.Location_Name.slice(0, 20) + "..." }}
                    </span>
                  </v-card>
                </template>
              </v-tooltip>
            </template>
            <template v-else>
              <v-card variant="flat">
                <span class="d-inline-block">{{
                  selectedItem.Location_Name
                }}</span>
              </v-card>
            </template>
          </div>
        </section>
      </div>

      <div class="d-flex align-center">
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <v-btn
              v-if="accessRights.update"
              v-bind="selectedItem.isAssociated ? props : ''"
              @click="$emit('open-edit-form')"
              size="small"
              color="primary"
              rounded="lg"
              >Edit</v-btn
            >
          </template>
        </v-tooltip>
        <v-icon class="mx-1" color="primary" @click="$emit('close-form')">
          fas fa-times
        </v-icon>
      </div>
    </div>
    <div style="height: calc(100vh - 300px); overflow: scroll">
      <v-card-text>
        <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
          <!-- <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Location Name</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Location_Name ? selectedItem.Location_Name : "-"
              }}
            </p>
          </v-col> -->
          <v-col
            v-if="
              entomoIntegrationEnabled ||
              labelList[310]?.Field_Visiblity.toLowerCase() === 'yes'
            "
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[310]?.Field_Alias || "Location Code" }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Location_Code ? selectedItem.Location_Code : "-"
              }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Location Type</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Location_Type ? selectedItem.Location_Type : "-"
              }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Street1</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Street1 ? selectedItem.Street1 : "-" }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Street2</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Street2 ? selectedItem.Street2 : "-" }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">City</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.City_Name ? selectedItem.City_Name : "-" }}
            </p>
          </v-col>

          <v-col
            v-if="labelList[431]?.Field_Visiblity?.toLowerCase() === 'yes'"
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[431]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedItem.Barangay) }}
            </p>
          </v-col>

          <v-col
            v-if="labelList[432]?.Field_Visiblity?.toLowerCase() === 'yes'"
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[432]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedItem.Region) }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">State</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.State_Name ? selectedItem.State_Name : "-" }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Country</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Country_Name ? selectedItem.Country_Name : "-" }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Pin Code</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Pincode ? selectedItem.Pincode : "-" }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Time Zone</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.TimeZone_Name ? selectedItem.TimeZone_Name : "-"
              }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Currency Symbol</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Currency_Symbol
                  ? selectedItem.Currency_Symbol
                  : "-"
              }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Contact Number</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Phone ? selectedItem.Phone : "-" }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Status</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Location_Status
                  ? selectedItem.Location_Status
                  : "-"
              }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Description</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Description ? selectedItem.Description : "-" }}
            </p>
          </v-col>

          <v-col cols="12">
            <MoreDetails
              :more-details-list="prefillMoreDetails()"
            ></MoreDetails>
          </v-col>
        </v-row>
      </v-card-text>
    </div>
  </v-card>
</template>
<script>
import { defineComponent, defineAsyncComponent } from "vue";
import { checkNullValue } from "@/helper.js";
import moment from "moment";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);
export default defineComponent({
  name: "ViewLocation",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    moreDetailsList: [],
  }),
  computed: {
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    checkNullValue,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      const Added_On = this.formatDate(this.selectedItem.Added_On),
        Added_By_Name = this.selectedItem.Added_By_Name,
        Updated_By_Name = this.selectedItem.Updated_By_Name,
        Updated_On = this.formatDate(this.selectedItem.Updated_On);

      if (Added_On && Added_By_Name) {
        this.moreDetailsList.push({
          actionDate: Added_On,
          actionBy: Added_By_Name,
          text: "Added",
        });
      }
      if (Updated_By_Name && Updated_On) {
        this.moreDetailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By_Name,
          text: "Updated",
        });
      }
      return this.moreDetailsList;
    },
  },
});
</script>
