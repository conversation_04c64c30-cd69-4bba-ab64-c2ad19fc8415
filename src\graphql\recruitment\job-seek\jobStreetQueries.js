import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const JOB_CATEGORIES = gql`
  query (
    $schemeId: String!
    $positionProfile: JobCategories_PositionProfileInput!
  ) {
    jobCategories(schemeId: $schemeId, positionProfile: $positionProfile) {
      id {
        value
      }
      name
      children {
        id {
          value
        }
        name
      }
    }
  }
`;

export const LOCATION_SUGGESTIONS = gql`
  query (
    $first: Int!
    $hirerId: String!
    $schemeId: String!
    $text: String!
    $usageTypeCode: String!
  ) {
    locationSuggestions(
      first: $first
      hirerId: $hirerId
      schemeId: $schemeId
      text: $text
      usageTypeCode: $usageTypeCode
    ) {
      location {
        id {
          value
        }
        contextualName
        countryCode

        # Enable your software to pre-select a salary currency
        currencies {
          code
        }
      }
    }
  }
`;

export const ADVERTISEMENT_PRODUCTS = gql`
  query advertisementProducts(
    $positionProfile: AdvertisementProducts_PositionProfileInput!
    $selectedAdvertisementProductId: String
  ) {
    advertisementProducts(
      positionProfile: $positionProfile
      selectedAdvertisementProductId: $selectedAdvertisementProductId
    ) {
      products {
        id {
          value
        }
        label
        description
        sellingPoints {
          text
        }
        price {
          summary
        }
        selected
        features {
          branding {
            coverImageIndicator
            logoIndicator
          }
          searchBulletPoints {
            limit
          }
        }
        payment {
          summaryHtml
        }
      }
      information
    }
  }
`;

export const ADVERTISEMENT_SEEK_BRANDINGS = gql`
  query ($after: String, $first: Int, $hirerId: String!) {
    advertisementBrandings(after: $after, first: $first, hirerId: $hirerId) {
      edges {
        node {
          id {
            value
          }
          name
          images {
            typeCode
            url
          }
        }
      }
      brandManagementUrl {
        url
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;
// ===============
// Mutations
// ===============

export const PREVIEW_LOGO_SEEK = gql`
  query ($after: String, $first: Int, $hirerId: String!) {
    advertisementBrandings(after: $after, first: $first, hirerId: $hirerId) {
      edges {
        node {
          id {
            value
          }
          name
          images {
            typeCode
            url
          }
        }
      }
      brandManagementUrl {
        url
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;
export const GET_HIRER_LIST = gql`
  query getJobStreetHirerList($status: String) {
    getJobStreetHirerList(status: $status) {
      errorCode
      message
      hirerList {
        Hirer_List_Id
        Integration_Type
        Hirer_ID
        Company_Id
        Status
        Company_Name
      }
    }
  }
`;
export const UPDATE_SEEK_STATUS = gql`
  mutation jobStreetCompanyIdStatusUpdate(
    $formId: Int!
    $status: String!
    $seekHirerId: Int!
  ) {
    jobStreetCompanyIdStatusUpdate(
      formId: $formId
      status: $status
      seekHirerId: $seekHirerId
    ) {
      errorCode
      message
    }
  }
`;
export const GET_SEEK_TOKEN = gql`
  query getAuthTokenJobStreet(
    $hirer_Id: String
    $form_Id: Int!
    $isPublish: Int
  ) {
    getAuthTokenJobStreet(
      hirer_Id: $hirer_Id
      form_Id: $form_Id
      isPublish: $isPublish
    ) {
      errorCode
      message
      getData {
        browserToken
        accessToken
      }
    }
  }
`;
export const ADD_SEEK_INTEGRATION = gql`
  mutation addUpdateJobPostJobStreet(
    $jobStreetId: Int!
    $jobId: Int!
    $roleCode: String
    $payDescription: String
    $email: String!
    $recruiterName: String
    $documentId: String
    $videoPositionCode: String
    $videoUrl: String
    $positionLocationId: String!
    $phoneNo: String
    $seekAdvertisementProductId: String!
    $hirerId: String!
    $searchBulletPointsArray: [String]
    $categoryId: String!
    $seekWorkTypeCode: String
    $advertisementBranding: String
    $seekBillingReference: String
    $profileId: String
    $appliedStatus: String!
    $searchSummaryDescription: String!
    $jobTitle: String!
    $jobSummary: String!
    $recruiterNoCountryCode: String
    $minimumAmount: String
    $maximumAmount: String
    $currency: String
  ) {
    addUpdateJobPostJobStreet(
      jobStreetId: $jobStreetId
      jobId: $jobId
      roleCode: $roleCode
      searchBulletPointsArray: $searchBulletPointsArray
      email: $email
      recruiterName: $recruiterName
      documentId: $documentId
      videoPositionCode: $videoPositionCode
      videoUrl: $videoUrl
      positionLocationId: $positionLocationId
      phoneNo: $phoneNo
      hirerId: $hirerId
      seekAdvertisementProductId: $seekAdvertisementProductId
      categoryId: $categoryId
      seekWorkTypeCode: $seekWorkTypeCode
      advertisementBranding: $advertisementBranding
      seekBillingReference: $seekBillingReference
      profileId: $profileId
      appliedStatus: $appliedStatus
      searchSummaryDescription: $searchSummaryDescription
      payDescription: $payDescription
      jobTitle: $jobTitle
      jobSummary: $jobSummary
      recruiterNoCountryCode: $recruiterNoCountryCode
      minimumAmount: $minimumAmount
      maximumAmount: $maximumAmount
      currency: $currency
    ) {
      errorCode
      message
    }
  }
`;
export const VERIFY_JOB_STREET = gql`
  query verifyJobStreetRelation($companyId: Int!, $form_Id: Int!) {
    verifyJobStreetRelation(companyId: $companyId, form_Id: $form_Id) {
      errorCode
      message
    }
  }
`;
export const JOB_STREET_JOB_DETAILS = gql`
  query retrieveJobStreetJobPostDetails($jobPostId: Int!) {
    retrieveJobStreetJobPostDetails(jobPostId: $jobPostId) {
      errorCode
      message
      data {
        jobStreetId
        JobPostId
        roleCode
        recruiterEmailId
        recruiterName
        documentId
        videoPositionCode
        videoUrl
        positionLocationId
        seekAdvertisementProductId
        subCategoryId
        categoryId
        seekWorkTypeCode
        advertisementBranding
        seekBillingReference
        currencyName
        currencyId
        currencyCode
        payDescription
        profileId
        appliedStatus
        searchSummaryDescription
        minimumAmount
        maximumAmount
        jobTitle
        jobSummary
        searchBulletPointsArray
        phoneNo
        hirerId
        recruiterNoCountryCode
      }
    }
  }
`;
export const GET_RECRUITER_LIST = gql`
  query getRoleBasedJobPostMember($jobPostId: Int!) {
    getRoleBasedJobPostMember(jobPostId: $jobPostId) {
      message
      errorCode
      panelMembers {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
      }
      recruiters {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
      }
      hiringManager {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
      }
    }
  }
`;
export const UPDATE_SEEK_INTEGRATION = gql`
  mutation updateJobOpeningDetails(
    $jobStreetId: Int!
    $roleCode: String!
    $email: String!
    $recruiterName: String!
    $phoneNo: String
    $recruiterNoCountryCode: String
  ) {
    updateJobOpeningDetails(
      jobStreetId: $jobStreetId
      roleCode: $roleCode
      email: $email
      recruiterName: $recruiterName
      phoneNo: $phoneNo
      recruiterNoCountryCode: $recruiterNoCountryCode
    ) {
      errorCode
      message
    }
  }
`;
export const CLOSE_SEEK_INTEGRATION = gql`
  mutation closeJobStreetJob($jobStreetId: Int!) {
    closeJobStreetJob(jobStreetId: $jobStreetId) {
      errorCode
      message
    }
  }
`;
export const GET_PREVIEW_LIST = gql`
  query ($positionProfile: PostedPositionProfilePreview_PositionProfileInput!) {
    postedPositionProfilePreview(positionProfile: $positionProfile) {
      previewUri {
        url
      }
    }
  }
`;
export const GET_LOCATION_NAME = gql`
  query ($id: String!) {
    location(id: $id) {
      name
      contextualName
      countryCode

      # Enable your software to pre-select a salary currency
      currencies {
        code
      }
    }
  }
`;
export const GET_CURRENCY_LIST = gql`
  query ($usageTypeCode: String!) {
    currencies(usageTypeCode: $usageTypeCode) {
      code
    }
  }
`;
export const GET_PROFILE_POSITION = gql`
  query ($id: String!) {
    positionProfile(id: $id) {
      profileId {
        value
      }
      positionTitle
      positionUri
      postingInstructions {
        start
        end
      }
    }
  }
`;
