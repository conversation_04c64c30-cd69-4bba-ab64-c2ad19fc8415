<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="text-subtitle-1 font-weight-bold">
            {{ landedFormName }}</span
          >
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              variant="elevated"
              class="mb-2 primary"
              @click="validateDepartmentForm"
              >Save</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Save</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" class="mr-1"> fas fa-times </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="height: calc(100vh - 260px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="departmentHierarchyForm">
            <v-row>
              <!-- Department Code -->
              <v-col
                v-if="entomoIntegrationEnabled"
                cols="6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="selectedCode"
                  variant="solo"
                  :rules="[
                    required(
                      labelList[314]?.Field_Alias || 'Department Code',
                      selectedCode
                    ),
                    selectedCode
                      ? validateWithRulesAndReturnMessages(
                          selectedCode,
                          'departmentCode',
                          labelList[314]?.Field_Alias || 'Department Code'
                        )
                      : true,
                  ]"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    {{ labelList[314]?.Field_Alias || "Department Code" }}
                    <span style="color: red">*</span>
                  </template></v-text-field
                >
              </v-col>
              <v-col
                v-else-if="
                  labelList[314] && labelList[314].Field_Visiblity === 'Yes'
                "
                cols="6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="selectedCode"
                  variant="solo"
                  :rules="[
                    labelList[314].Mandatory_Field === 'Yes'
                      ? required(labelList[314].Field_Alias, selectedCode)
                      : true,
                    selectedCode
                      ? validateWithRulesAndReturnMessages(
                          selectedCode,
                          'departmentCode',
                          labelList[314]?.Field_Alias
                            ? labelList[314].Field_Alias
                            : 'Department Code'
                        )
                      : true,
                  ]"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    {{ labelList["314"].Field_Alias }}
                    <span
                      v-if="labelList['314'].Mandatory_Field === 'Yes'"
                      style="color: red"
                      >*</span
                    >
                  </template></v-text-field
                >
              </v-col>

              <v-col cols="6" class="px-md-6 pb-0 mb-2">
                <v-text-field
                  v-model="selectedTitle"
                  variant="solo"
                  :rules="[
                    required('Department Name', selectedTitle),
                    selectedTitle
                      ? validateWithRulesAndReturnMessages(
                          selectedTitle,
                          'departmentName',
                          'Department Name'
                        )
                      : true,
                  ]"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    <span>Department Name</span>
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>

              <v-col cols="6" class="px-md-6 pb-0 mb-2">
                <CustomSelect
                  v-model="selectedHeaders"
                  :items="listHeaders"
                  label="Department Header"
                  :is-required="true"
                  :isAutoComplete="true"
                  item-title="Structure_Name"
                  item-value="Org_Structure_Id"
                  :clearable="true"
                  :is-auto-complete="true"
                  :is-loading="isDeptHeaderLoding"
                  :itemSelected="selectedHeaders"
                  :rules="[required('Department Header', selectedHeaders)]"
                  @selected-item="
                    onChangeSelectField($event, 'selectedHeaders')
                  "
                  @update:model-value="isFormDirty = true"
                />
              </v-col>

              <v-col cols="6" class="px-md-6 pb-0 mb-2">
                <v-autocomplete
                  v-model="selectedParents"
                  :items="listParents"
                  label="Parent Department"
                  :isAutoComplete="true"
                  :itemSelected="selectedParents"
                  :is-loading="isDeptHeaderLoding"
                  item-title="Department_Name"
                  item-value="Department_Id"
                  variant="solo"
                  color="primary"
                  :disabled="!selectedHeaders"
                  clearable="true"
                  :is-auto-complete="true"
                  @selected-item="
                    onChangeSelectField($event, 'selectedParents')
                  "
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:item="{ props, item }">
                    <div
                      v-bind="props"
                      :class="'pl-' + item?.raw?.level"
                      style="pointer-events: auto"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <div
                            v-bind="props"
                            class="pa-3 rounded-lg cursor-pointer"
                            :class="[
                              isHovering ? 'bg-hover' : 'bg-light-grey',
                              isSelected(item?.title) ? 'bg-primary' : '',
                            ]"
                            @click="selectItem(item?.title)"
                          >
                            {{ item?.title }}
                          </div>
                        </template>
                      </v-hover>
                    </div>
                  </template>
                </v-autocomplete>
              </v-col>

              <v-col cols="6" class="px-md-6 pb-0 mb-2"
                ><CustomSelect
                  v-model="selectedBonusType"
                  :items="listBonusTypes"
                  label="Bonus Type"
                  :isAutoComplete="true"
                  :itemSelected="selectedBonusType"
                  item-value="BonusType_Id"
                  item-title="Bonus_Type"
                  :is-loading="isBonusTypeLoading"
                  :clearable="true"
                  :is-auto-complete="true"
                  @update:model-value="isFormDirty = true"
                  @selected-item="
                    onChangeSelectField($event, 'selectedBonusType')
                  "
                />
              </v-col>

              <v-col
                v-if="isEdit"
                cols="6"
                class="px-md-6 pb-0 mb-2 d-flex align-center"
              >
                <div class="v-label mr-4">
                  <span>Status</span>
                  <span style="color: red">*</span>
                </div>
                <AppToggleButton
                  button-active-text="Active"
                  button-inactive-text="InActive"
                  button-active-color="#7de272"
                  button-inactive-color="red"
                  id-value="gab-analysis-based-on"
                  :current-value="selectedStatus === 'Active' ? true : false"
                  @chosen-value="onChangeStatus($event)"
                  @update:model-value="isFormDirty = true"
                />
              </v-col>

              <v-col cols="12" class="px-md-6 pb-0 mb-2">
                <v-textarea
                  v-model="selectedDescription"
                  variant="solo"
                  auto-grow
                  label="Description"
                  rows="5"
                  :rules="[
                    selectedDescription
                      ? validateWithRulesAndReturnMessages(
                          selectedDescription,
                          'departmentDescription',
                          'Description'
                        )
                      : true,
                  ]"
                  @update:model-value="isFormDirty = true"
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    />
    <AppLoading v-if="isLoading" />
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_UPDATE_DEPARTMENT,
  LIST_DEPARTMENT_HIERARCHY,
  LIST_DEPARTMENT_HEADERS,
  LIST_BONUS_TYPES,
} from "@/graphql/organisation/department-hierarchy/departmentHierarchyQueries.js";
export default {
  name: "AddEditDepartmentHierarchy",
  components: {
    CustomSelect,
  },
  mixins: [validationRules],

  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    originalList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      // add/update
      selectedTitle: "",
      selectedCode: "",
      selectedHeaders: null,
      selectedParents: null,
      selectedBonusType: null,
      selectedStatus: this.isEdit ? "" : "Active",
      selectedDescription: "",
      isFormDirty: false,
      listHeaders: [],
      listParents: [],
      listBonusTypes: [],
      // loading/error/other
      isLoading: false,
      isDeptHeaderLoding: false,
      isBonusTypeLoading: false,
      openConfirmationPopup: false,
      validationMessages: [],
      showValidationAlert: false,
      selectedItem: null,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
  },
  mounted() {
    if (this.editFormData) {
      this.selectedTitle = this.editFormData.Department_Name;
      this.selectedCode = this.editFormData.Department_Code;
      this.selectedHeaders = this.editFormData.Organization_Type_Id;
      this.selectedParents =
        this.editFormData.Parent_Type_Id !== 0
          ? this.editFormData.Parent_Type_Id
          : null;
      this.selectedBonusType =
        this.editFormData.BonusType_Id !== 0
          ? this.editFormData.BonusType_Id
          : null;
      this.selectedStatus = this.editFormData.Department_Status;
      this.selectedDescription = this.editFormData.Description;
    }
    this.fetchListDepartmentHierarchy();
    this.fetchListDeptHeader();
    this.fetchListBonusTypes();
  },
  watch: {
    editFormData() {
      this.selectedTitle = this.editFormData.Department_Name;
      this.selectedCode = this.editFormData.Department_Code;
      this.selectedHeaders = this.editFormData.Organization_Type_Id;
      this.selectedParents = this.editFormData.Parent_Type_Id;
      this.selectedBonusType = this.editFormData.BonusType_Id;
      this.selectedStatus = this.editFormData.Department_Status;
      this.selectedDescription = this.editFormData.Description;
    },
  },
  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    isSelected(item) {
      return this.selectedItem && this.selectedItem === item;
    },
    selectItem(item) {
      this.selectedItem = item;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },

    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },

    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },

    // change the mode of performance management
    onChangeStatus(value) {
      this.selectedStatus = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },
    fetchListDeptHeader() {
      let vm = this;
      vm.isDeptHeaderLoding = true;
      vm.$apollo
        .query({
          query: LIST_DEPARTMENT_HEADERS,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDepartmentHeaders &&
            response.data.listDepartmentHeaders.departmentHeaders &&
            !response.data.listDepartmentHeaders.errorCode
          ) {
            const departHeadersList = JSON.parse(
              response.data.listDepartmentHeaders.departmentHeaders
            );
            vm.listHeaders = departHeadersList;
            vm.isDeptHeaderLoding = false;
          } else {
            vm.isDeptHeaderLoding = false;
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.isDeptHeaderLoding = false;
          vm.handleListError(err);
        });
    },

    fetchListBonusTypes() {
      let vm = this;
      vm.isBonusTypeLoading = true;
      vm.$apollo
        .query({
          query: LIST_BONUS_TYPES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBonusTypes &&
            response.data.listBonusTypes.bonusTypes &&
            !response.data.listBonusTypes.errorCode
          ) {
            const bonusList = JSON.parse(
              response.data.listBonusTypes.bonusTypes
            );
            vm.listBonusTypes = bonusList;
            vm.isBonusTypeLoading = false;
          } else {
            vm.isBonusTypeLoading = false;
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.isBonusTypeLoading = false;
          vm.handleListError(err);
        });
    },

    async validateDepartmentForm() {
      const { valid } = await this.$refs.departmentHierarchyForm.validate();
      if (valid) {
        this.addUpdateDepartmentHierarchy();
      }
    },

    buildHierarchy(departments) {
      const departmentMap = {};
      const hierarchy = [];

      // Map all departments by their Department_Id
      departments.forEach((dept) => {
        departmentMap[dept.Department_Id] = { ...dept, children: [] };
      });

      // Organize departments into a hierarchical structure
      departments.forEach((dept) => {
        if (dept.Parent_Type_Id === 0) {
          // If it's a root department, add it to the hierarchy
          hierarchy.push(departmentMap[dept.Department_Id]);
        } else {
          // Else, add it to its parent's children array
          departmentMap[dept.Parent_Type_Id].children.push(
            departmentMap[dept.Department_Id]
          );
        }
      });

      return hierarchy;
    },
    flattenTree(items, level = 0) {
      let flattened = [];
      items.forEach((item) => {
        // Add indentation based on the level of hierarchy
        flattened.push({
          ...item,
          level: level * 4,
        });

        // Recursively add children with increased indentation
        if (item.children) {
          flattened = flattened.concat(
            this.flattenTree(item.children, level + 1)
          );
        }
      });
      return flattened;
    },
    fetchListDepartmentHierarchy() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: LIST_DEPARTMENT_HIERARCHY,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDepartmentHierarchy &&
            response.data.listDepartmentHierarchy.departmentHierarchy &&
            !response.data.listDepartmentHierarchy.errorCode
          ) {
            const departHierList = JSON.parse(
              response.data.listDepartmentHierarchy.departmentHierarchy
            );
            // Build the hierarchy using the provided function
            const hierarchy = vm.buildHierarchy(departHierList);
            vm.listParents = vm.flattenTree(hierarchy);
            vm.isLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Department Hierarchy",
        isListError: true,
      });
    },

    addUpdateDepartmentHierarchy() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_DEPARTMENT,
          variables: {
            Department_Id: vm.isEdit ? this.editFormData.Department_Id : 0,
            Department_Name: vm.selectedTitle,
            Department_Code: vm.selectedCode ? vm.selectedCode : "",
            Organization_Type_Id: vm.selectedHeaders,
            Parent_Type_Id: vm.selectedParents ? vm.selectedParents : 0,
            BonusType_Id: vm.selectedBonusType ? vm.selectedBonusType : 0,
            Department_Status: vm.isEdit ? vm.selectedStatus : "Active",
            Description: vm.selectedDescription,
          },
          client: "apolloClientBB",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.isEdit
              ? vm.landedFormName + " updated successfully."
              : vm.landedFormName + " added successfully.",
          };
          vm.showAlert(snackbarData);
          vm.$emit("edit-updated");
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;

      // Check if the error contains GraphQL errors and extensions
      if (
        err &&
        err.graphQLErrors &&
        err.graphQLErrors[0] &&
        err.graphQLErrors[0].extensions
      ) {
        const graphQLError = err.graphQLErrors[0];

        // Check for the specific error code "ODM0112" and message
        if (graphQLError.extensions.code === "ODM0112") {
          const errorMessage =
            graphQLError.message ||
            "The Parent Department's header level cannot be lower than that of the current Department.";

          // Show alert with the specific error message
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: errorMessage,
          });
          return; // Exit the function after handling the specific error
        }

        // Handle associated forms error (if any)
        const associatedForms = graphQLError.extensions.associatedForms;
        if (associatedForms && associatedForms.length > 0) {
          let errorMessage =
            "Operation failed as the following forms are associated with this department:\n\n";
          associatedForms.forEach((form, index) => {
            // Add a comma for all but the last item
            if (index === associatedForms.length - 1) {
              errorMessage += ` ${form}.`; // Add a period for the last item
            } else {
              errorMessage += ` ${form},\n`; // Add a comma and newline for other items
            }
          });
          errorMessage +=
            "\nPlease ensure the forms are reassigned or removed before proceeding.";

          // Show alert with the associated forms
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: errorMessage,
          });
          return; // Exit the function after handling associated forms error
        }
      }

      // General error handling using the existing store action
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.landedFormName.toLowerCase(),
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
  },
};
</script>
