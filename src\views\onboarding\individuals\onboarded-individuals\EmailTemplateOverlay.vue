<template>
  <div class="text-center">
    <v-overlay
      v-model="overlay"
      class="d-flex justify-end overlay-content-parent"
      @click:outside="onClickClose()"
      persistent
      style="z-index: 1000"
    >
      <template v-slot:default>
        <div class="overlay-card">
          <div
            class="d-flex align-center text-h6 text-medium-emphasis pa-2 bg-primary"
            style="width: 100%"
          >
            <v-icon
              v-if="displayCustomEmail && !noCustomTemplate"
              @click="
                displayCustomEmail
                  ? (displayCustomEmail = false)
                  : $emit('close-overlay')
              "
              size="17"
              class="mx-2"
              >fas fa-chevron-left</v-icon
            >
            <span class="ml-2">
              {{ actionName ? actionName : "Change Status" }}
            </span>
            <v-spacer></v-spacer>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="onClickClose()"
            ></v-btn>
          </div>
          <div class="overlay-body">
            <v-form v-if="!displayCustomEmail" ref="cancelInterviewForm">
              <div
                class="d-flex justify-space-between mt-5"
                style="max-height: 80px; flex: 1"
                v-if="
                  emailTemplateList?.length ||
                  actionName?.toLowerCase() === 'deployment notification'
                "
              >
                <CustomSelect
                  :items="emailTemplateList"
                  v-model="selectedEmailTemplate"
                  label="Email Template"
                  itemValue="Template_Id"
                  itemTitle="Template_Name"
                  ref="selectedEmailTemplate"
                  :isAutoComplete="true"
                  :isRequired="true"
                  :rules="[required('Email Template', selectedEmailTemplate)]"
                  :itemSelected="selectedEmailTemplate"
                  @selected-item="selectedEmailTemplate = $event"
                  @update:model-value="isFormDirty = true"
                ></CustomSelect>
              </div>
            </v-form>
            <CustomEmail
              v-else
              ref="customEmail"
              :formId="178"
              :typeOfTemplate="typeOfTemplate"
              :notificationTimeNow="notificationTimeNow"
              :emailRecievers="emailRecievers"
              :ccEmailRecievers="ccEmailRecievers"
              :toCCExchange="toCCExchange"
              :typeOfSchedule="typeOfSchedule"
              :template-email="templateEmail"
              :template-data="templateData"
              :selectedCandidateId="candidateId"
              :emailTemplateList="emailTemplateList"
              :selectedEmailTemplate="selectedEmailTemplate"
              :noCustomTemplate="noCustomTemplate"
              @custom-email-sent="customEmailSent"
            />
          </div>
          <v-card class="overlay-footer" elevation="16">
            <v-btn
              class="mr-5"
              variant="outlined"
              @click="
                displayCustomEmail && !noCustomTemplate
                  ? (this.displayCustomEmail = false)
                  : onClickClose()
              "
              rounded="lg"
              >Cancel</v-btn
            >
            <v-btn
              color="primary"
              variant="elevated"
              rounded="lg"
              @click="validateCancelInterviewForm()"
            >
              {{ displayCustomEmail ? "Send Email" : "Preview Email" }}
            </v-btn>
          </v-card>
        </div>
        <AppLoading v-if="isEmailTemplateListLoading"></AppLoading>
      </template>
    </v-overlay>
  </div>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  ></AppWarningModal>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import CustomEmail from "@/views/common/customEmail/CustomEmailComponent.vue";
import { LIST_CUSTOM_EMAIL_TEMPLATES } from "@/graphql/settings/email-template/emailTemplateQueries.js";

export default {
  name: "EmailTemplateOverlay",
  components: {
    CustomSelect,
    CustomEmail,
  },
  props: {
    candidateId: {
      type: Number,
      required: true,
    },
    notificationTimeNow: {
      type: Boolean,
      default: true,
    },
    typeOfTemplate: {
      type: String,
      required: false,
    },
    templateEmail: {
      type: Array,
      default: function () {
        return [];
      },
    },
    templateData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    emailRecievers: {
      type: Array,
      default: function () {
        return [];
      },
    },
    ccEmailRecievers: {
      type: Array,
      default: function () {
        return [];
      },
    },
    toCCExchange: {
      type: Boolean,
      required: false,
      default: () => false,
    },
    typeOfSchedule: {
      type: String,
      required: false,
    },
    actionName: {
      type: String,
      default: "",
      required: false,
    },
  },
  mixins: [validationRules],
  emits: ["close-email-template-window", "close-overlay"],

  data: () => ({
    isFormDirty: false,
    overlay: true,
    selectedEmailTemplate: null,
    displayCustomEmail: false,
    emailTemplateList: [],
    openConfirmationPopup: false,
    isEmailTemplateListLoading: false,
    noCustomTemplate: false,
  }),

  mounted() {
    this.fetchEmailTemplates();
  },
  methods: {
    onClickClose() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.$emit("close-email-template-window", false);
      }
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-email-template-window", false);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async validateCancelInterviewForm() {
      if (!this.displayCustomEmail) {
        let { valid } = await this.$refs.cancelInterviewForm.validate();
        if (valid) {
          this.displayCustomEmail = true;
        }
      } else {
        let customEmailRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        if (customEmailRef) {
          let noPlaceholderFound = customEmailRef.noPlaceholderFound;
          if (noPlaceholderFound) {
            let snackbarData = {
              isOpen: true,
              message:
                "Some placeholders are not replaced, kindly replace or remove them before proceeding.",
              type: "warning",
            };
            this.showAlert(snackbarData);
            customEmailRef.noPlaceholderFound = false;
          } else {
            await customEmailRef.validateCustomEmailForm();
          }
        }
      }
    },
    fetchEmailTemplates() {
      let vm = this;
      const actionCategoryMap = {
        "reject candidate": 10,
        "return candidate": 12,
        "notify candidate": 13,
        "notify hiring manager": 14,
      };

      // Assign categoryId based on actionName
      let categoryId = actionCategoryMap[vm.actionName?.toLowerCase()] || null;
      vm.isEmailTemplateListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CUSTOM_EMAIL_TEMPLATES,
          variables: {
            formId: 178,
            categoryId: categoryId,
            categoryTypeId: null,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listCustomEmailTemplates &&
            response.data.listCustomEmailTemplates.emailTemplates
          ) {
            vm.emailTemplateList =
              response.data.listCustomEmailTemplates.emailTemplates;
            vm.noCustomTemplate =
              vm.emailTemplateList?.length === 0 ? true : false;
            if (
              vm.noCustomTemplate &&
              vm.actionName?.toLowerCase() !== "deployment notification"
            ) {
              vm.displayCustomEmail = true;
            }
          }
          vm.isEmailTemplateListLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.isEmailTemplateListLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "email templates",
        isListError: false,
      });
    },
    customEmailSent() {
      this.$emit("custom-email-sent", this.selectedEmailTemplate);
    },
    getCustomEmailRef() {
      return this.$refs.customEmail;
    },
  },
};
</script>

<style scoped>
.headingColor {
  background-color: rgb(var(--v-theme-primary));
}

.overlay-card {
  height: 100%;
  width: 100%;
  background: white;
}

.overlay-content-parent {
  z-index: 1000 !important;
}

.overlay-content-parent > .v-overlay__content {
  height: 100%;
  width: 700px;
}

@media only screen and (max-width: 600px) {
  .overlay-content-parent > .v-overlay__content {
    width: 100%;
  }
}
:deep(.v-overlay__content) {
  width: 40% !important;
  height: 100% !important;
}

.overlay-body {
  padding: 15px;
  height: calc(100vh - 130px);
  overflow-y: scroll !important;
  overflow: hidden;
}

.overlay-footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
