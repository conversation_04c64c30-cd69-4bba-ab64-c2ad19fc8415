<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col
            v-if="showTeamFilters"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedEmployee"
              density="compact"
              :items="employeesList"
              label="Employee"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedRequestFor"
              density="compact"
              :items="requestForList"
              label="Request For"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedLateArrival"
              density="compact"
              :items="['Yes', 'No']"
              label="Late Arrival"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedEarlyCheckout"
              density="compact"
              :items="['Yes', 'No']"
              label="Early Checkout"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              density="compact"
              :items="statusList"
              variant="solo"
              label="Status"
              single-line
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="showTeamFilters"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedDesignation"
              density="compact"
              :items="designationList"
              item-value="id"
              item-title="name"
              label="Designation"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="showTeamFilters"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedDepartment"
              density="compact"
              :items="departmentList"
              item-value="id"
              item-title="name"
              label="Department"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="showTeamFilters"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLocation"
              density="compact"
              :items="locationList"
              label="Location"
              item-value="id"
              item-title="name"
              :isLoading="dropdownListFetching"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="showTeamFilters"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedWorkSchedule"
              density="compact"
              :items="workScheduleList"
              label="Work Schedule"
              item-value="id"
              item-title="name"
              :isLoading="dropdownListFetching"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="showTeamFilters"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedEmployeeType"
              density="compact"
              :items="employeeTypeList"
              item-value="id"
              item-title="name"
              :isLoading="dropdownListFetching"
              label="Employee Type"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="showTeamFilters"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedBusinessUnit"
              density="compact"
              :items="businessUnitList"
              item-value="id"
              item-title="name"
              label="Business Unit"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="fieldForce && showTeamFilters"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedServiceProvider"
              density="compact"
              variant="solo"
              :items="serviceProviderList"
              item-value="id"
              item-title="name"
              :isLoading="dropdownListFetching"
              :label="labelList[115]?.Field_Alias"
              single-line
              multiple
              chips
              closable-chips
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "ShortTimeOffFilter",

  data: () => ({
    openFormFilter: false,
    dropdownListFetching: false,
    // Filter values
    selectedEmployee: null,
    selectedRequestFor: null,
    selectedLocation: null,
    selectedDepartment: null,
    selectedDesignation: null,
    selectedWorkSchedule: null,
    selectedEmployeeType: null,
    selectedLateArrival: null,
    selectedEarlyCheckout: null,
    selectedServiceProvider: null,
    selectedStatus: null,
    selectedBusinessUnit: null,
    // Lists
    filterItemList: [],
    requestForList: ["Permission", "On Duty"],
    employeesList: [],
    locationList: [],
    departmentList: [],
    designationList: [],
    workScheduleList: [],
    employeeTypeList: [],
    lateArrivalList: ["Yes", "No"],
    earlyCheckoutList: ["Yes", "No"],
    serviceProviderList: [],
    statusList: [
      "Applied",
      "Approved",
      "Rejected",
      "Cancelled",
      "Cancel Applied",
    ],
    businessUnitList: [],
  }),
  props: {
    items: {
      type: Array,
      default: () => [],
    },
    originalList: {
      type: Array,
      default: () => [],
    },
    formId: {
      type: Number,
      default: 352, // Default to Short Time Off form ID
    },
    callingFrom: {
      type: String,
      default: "myTeam",
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    showTeamFilters() {
      return this.callingFrom === "myTeam";
    },
  },
  mounted() {
    this.filterItemList = this.originalList;
    this.formFilterData();
  },

  watch: {
    originalList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.openFormFilter = false;
          this.filterItemList = newVal;
          this.formFilterData();
        }
      },
      deep: true,
    },
  },

  methods: {
    // apply filter
    fnApplyFilter() {
      let filteredArray = this.filterItemList;

      // Filter by employee
      if (this.selectedEmployee && this.selectedEmployee.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          const employeeName = item.User_Defined_EmpId
            ? `${item.Employee_Name} - ${item.User_Defined_EmpId}`
            : item.Employee_Name;
          return this.selectedEmployee.includes(employeeName);
        });
      }

      // Filter by request for
      if (this.selectedRequestFor && this.selectedRequestFor.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedRequestFor.includes(item.Request_For);
        });
      }

      // Filter by location
      if (this.selectedLocation && this.selectedLocation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLocation.includes(item.Location_Id);
        });
      }

      // Filter by department
      if (this.selectedDepartment && this.selectedDepartment.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDepartment.includes(item.Department_Id);
        });
      }

      // Filter by designation
      if (this.selectedDesignation && this.selectedDesignation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDesignation.includes(item.Designation_Id);
        });
      }

      // Filter by work schedule
      if (this.selectedWorkSchedule && this.selectedWorkSchedule.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedWorkSchedule.includes(item.WorkSchedule_Id);
        });
      }

      // Filter by employee type
      if (this.selectedEmployeeType && this.selectedEmployeeType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmployeeType.includes(item.EmpType_Id);
        });
      }

      // Filter by late attendance
      if (this.selectedLateArrival && this.selectedLateArrival.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLateArrival.includes(item.Late_Attendance);
        });
      }

      // Filter by early checkout
      if (this.selectedEarlyCheckout && this.selectedEarlyCheckout.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEarlyCheckout.includes(item.Early_Checkout);
        });
      }

      // Filter by service provider
      if (
        this.selectedServiceProvider &&
        this.selectedServiceProvider.length > 0
      ) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedServiceProvider.includes(
            item.Service_Provider_Id
          );
        });
      }

      // Filter by status
      if (this.selectedStatus && this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Approval_Status);
        });
      }

      // Filter by business unit
      if (this.selectedBusinessUnit && this.selectedBusinessUnit.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedBusinessUnit.includes(item.Business_Unit_Id);
        });
      }

      // Emit the filtered array to the parent component
      this.openFormFilter = false;
      this.$emit("apply-filter", filteredArray);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      // Emit reset event to parent component
      this.$emit("reset-filter");
      // Update dropdown lists with original data
      this.formFilterData();
    },

    resetAllModelValues() {
      this.selectedEmployee = null;
      this.selectedRequestFor = null;
      this.selectedLocation = null;
      this.selectedDepartment = null;
      this.selectedDesignation = null;
      this.selectedWorkSchedule = null;
      this.selectedEmployeeType = null;
      this.selectedLateArrival = null;
      this.selectedEarlyCheckout = null;
      this.selectedServiceProvider = null;
      this.selectedStatus = null;
      this.selectedBusinessUnit = null;
    },
    formFilterData() {
      // Create Sets and Maps to store unique values
      const employeeSet = new Set();
      const employeeValueSet = new Set();
      const requestForSet = new Set();
      const statusSet = new Set();

      // Maps to store unique objects with id and name
      const locationMap = new Map();
      const departmentMap = new Map();
      const designationMap = new Map();
      const workScheduleMap = new Map();
      const employeeTypeMap = new Map();
      const businessUnitMap = new Map();
      const serviceProviderMap = new Map();

      // Ensure we have default values for requestForList and statusList
      if (!this.requestForList || this.requestForList.length === 0) {
        this.requestForList = ["Permission", "On Duty"];
      }

      if (!this.statusList || this.statusList.length === 0) {
        this.statusList = [
          "Applied",
          "Approved",
          "Rejected",
          "Cancelled",
          "Cancel Applied",
        ];
      }

      // Use originalList if available, otherwise use filterItemList
      const sourceList =
        this.originalList && this.originalList.length > 0
          ? this.originalList
          : this.filterItemList;

      for (let item of sourceList) {
        if (item && item.Employee_Name) {
          const employeeName = item.User_Defined_EmpId
            ? `${item.Employee_Name} - ${item.User_Defined_EmpId}`
            : item.Employee_Name;
          employeeSet.add(employeeName);
          employeeValueSet.add(item.Employee_Name);
        }

        if (item && item.Request_For) {
          requestForSet.add(item.Request_For);
        }

        // Add status if it exists
        if (item && item.Approval_Status) {
          statusSet.add(item.Approval_Status);
        }

        // Add location if it exists
        if (item && item.Location_Id && item.Location_Name) {
          const locationNameId = `${item.Location_Name} - ${item.Location_Id}`;
          locationMap.set(item.Location_Id, {
            id: item.Location_Id,
            name: locationNameId,
          });
        }

        // Add department if it exists
        if (item && item.Department_Id && item.Department_Name) {
          const departmentNameId = `${item.Department_Name} - ${item.Department_Id}`;
          departmentMap.set(item.Department_Id, {
            id: item.Department_Id,
            name: departmentNameId,
          });
        }

        // Add designation if it exists
        if (item && item.Designation_Id && item.Designation_Name) {
          const designationNameId = `${item.Designation_Name} - ${item.Designation_Id}`;
          designationMap.set(item.Designation_Id, {
            id: item.Designation_Id,
            name: designationNameId,
          });
        }

        // Add work schedule if it exists
        if (item && item.WorkSchedule_Id && item.Work_Schedule) {
          const workScheduleNameId = `${item.Work_Schedule} - ${item.WorkSchedule_Id}`;
          workScheduleMap.set(item.WorkSchedule_Id, {
            id: item.WorkSchedule_Id,
            name: workScheduleNameId,
          });
        }

        // Add employee type if it exists
        if (item && item.EmpType_Id && item.Employee_Type) {
          const employeeTypeNameId = item.Employee_Type;
          employeeTypeMap.set(item.EmpType_Id, {
            id: item.EmpType_Id,
            name: employeeTypeNameId,
          });
        }

        // Add business unit if it exists
        if (item && item.Business_Unit_Id && item.Business_Unit_Name) {
          const businessUnitNameId = `${item.Business_Unit_Name} - ${item.Business_Unit_Id}`;
          businessUnitMap.set(item.Business_Unit_Id, {
            id: item.Business_Unit_Id,
            name: businessUnitNameId,
          });
        }

        // Add service provider if it exists
        if (item && item.Service_Provider_Id && item.Service_Provider_Name) {
          const serviceProviderNameId = `${item.Service_Provider_Name} - ${item.Service_Provider_Id}`;
          serviceProviderMap.set(item.Service_Provider_Id, {
            id: item.Service_Provider_Id,
            name: serviceProviderNameId,
          });
        }
      }

      // Convert Sets to Arrays using Array.from directly
      this.employeesList = Array.from(employeeSet);
      this.requestForList = Array.from(requestForSet);
      this.statusList = Array.from(statusSet);

      // Convert Maps to Arrays of objects with id and name
      if (!this.locationList || this.locationList.length === 0) {
        this.locationList = Array.from(locationMap.values());
      }

      if (!this.departmentList || this.departmentList.length === 0) {
        this.departmentList = Array.from(departmentMap.values());
      }

      if (!this.designationList || this.designationList.length === 0) {
        this.designationList = Array.from(designationMap.values());
      }

      if (!this.workScheduleList || this.workScheduleList.length === 0) {
        this.workScheduleList = Array.from(workScheduleMap.values());
      }

      if (!this.employeeTypeList || this.employeeTypeList.length === 0) {
        this.employeeTypeList = Array.from(employeeTypeMap.values());
      }

      if (!this.businessUnitList || this.businessUnitList.length === 0) {
        this.businessUnitList = Array.from(businessUnitMap.values());
      }

      if (!this.serviceProviderList || this.serviceProviderList.length === 0) {
        this.serviceProviderList = Array.from(serviceProviderMap.values());
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
