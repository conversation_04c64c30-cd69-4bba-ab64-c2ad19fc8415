<template>
  <div>
    <div v-if="mainTabs?.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :show-bottom-sheet="showBottomSheet"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :class="showFilter ? '' : 'mr-8'"
                :isFilter="false"
              />
              <LeavesFilter
                v-if="showFilter"
                ref="formFilterRef"
                :form-id="333"
                callingFrom="selfService"
                :item-list="originalList"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-container fluid class="leaves-card">
            <ProfileCard>
              <FormTab :model-value="openedSubTab" :hide-slider="true">
                <v-tab
                  v-for="tab in subTabItems"
                  :key="tab.value"
                  :value="tab.value"
                  :disabled="tab.disable"
                  @click="onChangeSubTabs(tab.value)"
                >
                  <div
                    :class="[
                      isActiveSubTab(tab.value)
                        ? 'text-primary font-weight-bold'
                        : 'text-grey-darken-2 font-weight-bold',
                    ]"
                  >
                    {{ tab.label }}
                    <div
                      v-if="isActiveSubTab(tab.value)"
                      class="mt-3 mb-n4"
                      style="border-bottom: 4px solid; width: 150px"
                    ></div>
                  </div>
                </v-tab>
              </FormTab>
            </ProfileCard>
          </v-container>
          <v-container fluid>
            <v-window v-model="openedSubTab">
              <v-window-item value="leaveRequests">
                <LeaveRequestList
                  v-if="openedSubTab === `leaveRequests`"
                  callingFrom="selfService"
                  :form-access="formAccess"
                  :leave-settings="leaveSettings"
                  :form-id="333"
                  :landed-form-name="landedFormName"
                  :filtered-list="itemList"
                  @opened-forms="presentBottomSheet($event)"
                  @send-list-data="updateList($event)"
                />
              </v-window-item>
              <v-window-item value="leaveBalance">
                <LeaveBalance
                  v-if="openedSubTab === `leaveBalance`"
                  :form-id="333"
                  :form-access="formAccess"
                  :filtered-list="itemList"
                  landed-form-name="Leave Balance"
                  @send-list-data="updateList($event)"
                />
              </v-window-item>
            </v-window>
          </v-container>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
</template>
<script>
const { defineAsyncComponent } = require("vue");
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const LeaveRequestList = defineAsyncComponent(() =>
  import("@/views/my-team/time-off/leaves/leave-request/LeaveRequestList.vue")
);
const LeavesFilter = defineAsyncComponent(() =>
  import("@/views/my-team/time-off/leaves/LeavesFilter.vue")
);
const LeaveBalance = defineAsyncComponent(() =>
  import("./leave-balance/LeaveBalance.vue")
);
import { GET_LEAVE_SETTINGS } from "@/graphql/settings/core-hr/leaveSettingsQueries.js";

import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "LeaveRequestListSetup",
  components: {
    EmployeeDefaultFilterMenu,
    LeaveRequestList,
    LeavesFilter,
    LeaveBalance,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      originalList: [],
      itemList: [],
      leaveSettings: null,
      showViewForm: false,
      showAddEditForm: false,
      isErrorInList: false,
      listLoading: false,
      openMoreMenu: false,
      isEdit: false,
      // Leaves
      currentTabItem: "",
      openedSubTab: "Leave Request",
      isFilterApplied: false,
      showBottomSheet: false,
    };
  },
  computed: {
    landedFormName() {
      let form = this.accessRights(333);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Leave Request";
    },
    selfServiceTimeOffFormAccess() {
      return this.$store.getters.selfServiceTimeOffFormAccess;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(333);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.selfServiceTimeOffFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    subTabItems() {
      return [
        {
          label: "Leave Request",
          value: "leaveRequests",
          disable: false,
        },
        {
          label: "Leave Balance",
          value: "leaveBalance",
          disable: false,
        },
      ];
    },
    showFilter() {
      return this.originalList?.length && this.openedSubTab === "leaveRequests";
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf("Leave");
    this.getLeaveSettings();
  },
  methods: {
    checkNullValue,
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    presentBottomSheet(val) {
      this.showBottomSheet = !val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    updateList(obj) {
      this.originalList = obj?.list || [];
      this.itemList = obj?.list || [];
      if (obj?.type === "updated") {
        this.$refs?.formFilterRef?.fnApplyFilter(obj?.list || []);
      } else this.resetFilter();
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
    },

    applyFilter(filter) {
      this.isFilterApplied = true;
      this.itemList = filter;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.selfServiceTimeOffFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/employee-self-service/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/employee-self-service/" + clickedForm.url;
        }
      }
    },
    getLeaveSettings() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_LEAVE_SETTINGS,
          variables: {
            formId: 333,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveLeaveSettings
          ) {
            const { leaveSettings } = response.data.retrieveLeaveSettings;
            vm.leaveSettings = leaveSettings;
          }
          vm.listLoading = false;
        })
        .catch(() => {
          vm.listLoading = false;
        });
    },
  },
};
</script>
<style scoped>
.container {
  padding: 3.7em 0em 0em 0em;
}

.leaves-card {
  padding: 0em 0em 0em 0em;
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
  .leaves-card {
    padding: 0em 0em 0em 0em;
  }
}
</style>
