import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const LIST_LOCATION_DETAILS = gql`
  query listLocationDetails($limit: Int!, $offset: Int!) {
    listLocationDetails(limit: $limit, offset: $offset) {
      errorCode
      message
      location {
        Location_Id
        Location_Name
        Location_Code
        Location_Type
        Location_Status
        Street1
        Street2
        Barangay_Id
        City_Id
        City_Name
        State_Id
        State_Name
        Country_Code
        Country_Name
        Pincode
        Phone
        Description
        Currency_Symbol
        Org_Id
        TimeZone_Id
        TimeZone_Name
        Barangay
        Region
        Added_On
        Added_By
        Added_By_Name
        Updated_On
        Updated_By
        Updated_By_Name
      }
    }
  }
`;

export const GET_TIME_ZONE_LIST = gql`
  query getTimezoneList {
    getTimezoneList {
      errorCode
      message
      timeZoneDetails {
        Zone_Id
        Country_Code
        Country_Name
        TimeZone_Id
        Offset_Time
        Summer_Time
        Start_Time
        End_Time
        Dst_Flag
        Dst_Id
      }
    }
  }
`;

export const ADD_EDIT_LIST_LOCATION_DETAILS = gql`
  mutation addUpdateLocation(
    $locationId: Int!
    $locationName: String!
    $locationCode: String
    $locationType: String!
    $street1: String!
    $street2: String
    $cityId: Int!
    $stateId: Int!
    $countryCode: String!
    $pincode: String
    $phone: String
    $currencySymbol: String!
    $description: String
    $orgId: Int
    $zoneId: Int!
    $locationStatus: String!
    $oldStatus: String
    $barangay: String
    $region: String
    $barangayId: Int
  ) {
    addUpdateLocation(
      locationId: $locationId
      locationName: $locationName
      locationCode: $locationCode
      locationType: $locationType
      street1: $street1
      street2: $street2
      cityId: $cityId
      stateId: $stateId
      countryCode: $countryCode
      pincode: $pincode
      phone: $phone
      currencySymbol: $currencySymbol
      description: $description
      orgId: $orgId
      zoneId: $zoneId
      locationStatus: $locationStatus
      oldStatus: $oldStatus
      barangay: $barangay
      region: $region
      barangayId: $barangayId
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_LOCATION_DETAILS = gql`
  mutation deleteLocation($locationId: Int!) {
    deleteLocation(locationId: $locationId) {
      errorCode
      message
    }
  }
`;
