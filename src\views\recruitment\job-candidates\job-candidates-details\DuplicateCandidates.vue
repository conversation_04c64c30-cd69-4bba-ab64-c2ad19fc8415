<template>
  <div class="text-center">
    <v-overlay
      v-if="overlay"
      v-model="overlay"
      class="d-flex justify-end overlay"
      persistent
      @click:outside="
        {
        }
      "
    >
      <template v-slot:default>
        <v-card
          rounded="lg"
          :style="
            isMobileView
              ? 'width:100vw; height: 100vh;'
              : 'width:45vw; height: 100vh;'
          "
        >
          <v-card-title
            class="d-flex justify-space-between align-center bg-primary"
          >
            <div class="text-h6">Duplicate Candidates - Review</div>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeWindow(false)"
              color="white"
            ></v-btn>
          </v-card-title>
          <v-divider class="mb-2"></v-divider>

          <v-card-text v-if="isLoading" class="d-flex justify-center">
            <AppLoading />
          </v-card-text>

          <v-card-text v-else class="overflow-y-auto" style="max-height: 85vh">
            <div
              v-if="
                sortedDuplicateCandidates && sortedDuplicateCandidates.length
              "
            >
              <v-row
                v-for="candidate in sortedDuplicateCandidates"
                :key="candidate.Candidate_Id"
                class="mb-2"
              >
                <v-col cols="12">
                  <v-card
                    class="pa-3"
                    @click="navigateToCandidateProfile(candidate)"
                    hover
                    elevation="2"
                  >
                    <v-card-title>
                      <v-icon color="primary" class="mb-2" size="x-small"
                        >fas fa-user</v-icon
                      >
                      {{ candidate.Candidate_Name }}
                    </v-card-title>
                    <v-card-subtitle
                      v-if="
                        candidate?.Talent_Pool ||
                        candidate?.Archived?.toLowerCase() === 'yes' ||
                        candidate.Job_Post_Name
                      "
                    >
                      <p>
                        <v-tooltip text="Job post" location="bottom">
                          <template v-slot:activator="{ props }">
                            <v-icon
                              color="primary"
                              class="mb-1 mr-2"
                              size="18"
                              v-bind="props"
                            >
                              fas fa-suitcase
                            </v-icon>
                          </template>
                        </v-tooltip>
                        {{ checkNullValue(candidate.Job_Post_Name) }}
                      </p>
                    </v-card-subtitle>

                    <v-divider></v-divider>

                    <v-card-text>
                      <!-- Check if Candidate_Status is present, show status if true, else show Talent_Pool -->
                      <p
                        v-if="candidate?.Archived?.toLowerCase() === 'yes'"
                        class="status-archived font-weight-bold"
                      >
                        <v-icon color="red" size="10" class="mr-2">
                          fas fa-circle
                        </v-icon>
                        Archived
                      </p>
                      <p
                        v-else-if="candidate?.Talent_Pool"
                        class="font-weight-bold"
                      >
                        <v-tooltip text="Talent Pool" location="bottom">
                          <template v-slot:activator="{ props }">
                            <v-icon
                              color="primary"
                              class="mr-2"
                              size="20"
                              v-bind="props"
                            >
                              far fa-folder
                            </v-icon>
                          </template>
                        </v-tooltip>
                        Talent Pool - {{ candidate.Talent_Pool }}
                      </p>
                      <p
                        v-else
                        :class="stageClass(candidate.Hiring_Stage)"
                        class="status-container font-weight-bold"
                      >
                        {{ candidate.Candidate_Status }}
                      </p>

                      <!-- Add margin below Candidate Status -->
                      <p class="mb-4"></p>

                      <v-row>
                        <v-col cols="6">
                          <p>
                            <strong>
                              <v-icon
                                color="primary"
                                class="mr-1"
                                size="x-small"
                                >fas fa-phone-alt</v-icon
                              >
                            </strong>
                            {{ checkNullValue(candidate.Mobile_No) }}
                          </p>
                        </v-col>
                        <v-col cols="6">
                          <p>
                            <strong>
                              <v-icon
                                color="primary"
                                class="mr-1"
                                size="x-small"
                                >fas fa-envelope</v-icon
                              >
                            </strong>
                            {{ checkNullValue(candidate.Personal_Email) }}
                          </p>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </div>
            <div v-else class="text-center">No duplicate candidates found.</div>
          </v-card-text>
        </v-card>
      </template>
    </v-overlay>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 secondary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
  <JobCandidateTopCard
    :duplicateCandidateDetails="duplicateCandidateDetails"
  ></JobCandidateTopCard>
</template>

<script>
import {
  GET_DUPLICATE_CANDIDATE_LIST,
  LIST_JOB_POSTS,
} from "@/graphql/recruitment/recruitmentQueries";
import { checkNullValue } from "@/helper";
export default {
  name: "DuplicateCandidates",
  props: {
    candidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    candidateId: {
      type: Number,
      default: null,
      required: true,
    },
    candidateName: {
      type: String,
      default: "",
      required: true,
    },
    jobPostId: {
      type: Number,
      default: null,
      required: true,
    },
    statusId: {
      type: Number,
      default: null,
      required: true,
    },
    jobTitle: {
      type: String,
      default: "",
      required: true,
    },
    parentTabName: {
      type: String,
      default: "",
    },
  },

  data: () => ({
    overlay: true,
    openConfirmationModel: false,
    duplicateCandidateDetails: [],
    jobPostList: [],
    isFetching: false,
    isLoading: true,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    sortedDuplicateCandidates() {
      return this.duplicateCandidateDetails
        .slice()
        .sort((a, b) => b.Candidate_Id - a.Candidate_Id);
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccessForTalentPool() {
      let formAccess = this.accessRights("297");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },

  mounted() {
    this.fetchDuplicateCandidates();
  },

  methods: {
    stageClass(Hiring_Stage) {
      switch (Hiring_Stage) {
        case "Screening":
          return "stage-screening";
        case "Sourced":
          return "stage-sourced";
        case "Interview":
          return "stage-interview";
        case "Hired":
          return "stage-hired";
        case "Preboarding":
          return "stage-preboarding";
        case "Archived":
          return "stage-archived";
        default:
          return "stage-default";
      }
    },
    checkNullValue,
    fetchDuplicateCandidates() {
      let vm = this;
      vm.isLoading = true;

      vm.$apollo
        .query({
          query: GET_DUPLICATE_CANDIDATE_LIST,
          client: "apolloClientAN",
          variables: {
            candidateId: vm.candidateId,
            candidateIdSelected: vm.candidateIdSelected,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          const data = res.data.getDuplicateCandidateList;
          vm.duplicateCandidateDetails = data?.duplicateCandidateData || [];
          vm.duplicateCandidateDetails = JSON.parse(
            vm.duplicateCandidateDetails
          );
        })
        .catch((error) => {
          vm.handleDuplicateCandidatesError(error);
        })
        .finally(() => {
          vm.retrieveJobPosts();
        });
    },

    handleDuplicateCandidatesError(err = "") {
      mixpanel.track("Duplicate Candidates error in fetch API");
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Duplicate Candidates",
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    retrieveJobPosts() {
      let vm = this;
      vm.isLoading = true;

      vm.$apollo
        .query({
          query: LIST_JOB_POSTS,
          client: "apolloClientA",
          variables: {
            searchString: "",
            designation: null,
            functionalArea: null,
            jobType: null,
            closingDate: "",
            status: null,
            location: [],
            isDropDownCall: 1,
            skills: [],
            qualification: [],
            action: "add",
            formId: 16,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobPost &&
            !response.data.listJobPost.errorCode.length
          ) {
            vm.jobPostList = response.data.listJobPost.JobpostDetails;

            if (vm.jobPostList && vm.jobPostList.length > 0) {
              vm.selectedJobPostId = vm.jobPostList[0].Job_Post_Id;
            } else {
              vm.selectedJobPostId = 0;
            }

            let allJobPost = [
              {
                Job_Post_Name: "All",
                Job_Post_Id: 0,
              },
            ];
            vm.jobPostList = allJobPost.concat(vm.jobPostList);
          } else {
            vm.handleListError();
          }
        })
        .catch((error) => {
          vm.handleListError(error);
        })

        .finally(() => {
          vm.isLoading = false; // Set loading to false when done
        });
    },
    handleListError() {
      this.errorContent =
        "Failed to retrieve job posts. Please try again later.";

      this.jobPostList = [];

      this.selectedJobPostId = 0;

      // You could also dispatch an action to log errors if needed
      this.$store.dispatch("handleApiErrors", {
        error: "Job posts retrieval error", // You can provide more specific error information here
        action: "retrieving job posts",
        form: "Job Post List",
        isListError: true,
      });
    },

    navigateToCandidateProfile(selectedCandidateRecord) {
      if (selectedCandidateRecord?.Archived?.toLowerCase() === "yes") {
        window.open(
          this.$store.getters.baseUrl +
            `v3/recruitment/job-candidates/archived-candidates?candidateId=${selectedCandidateRecord.Candidate_Id}&jobpostId=${selectedCandidateRecord?.Job_Post_Id}`,
          "_blank"
        );
      } else if (selectedCandidateRecord?.Talent_Pool_Id) {
        if (this.formAccessForTalentPool?.view) {
          window.open(
            this.$store.getters.baseUrl +
              `v3/recruitment/job-candidates/talent-pool?candidateId=${selectedCandidateRecord.Candidate_Id}&talentPoolId=${selectedCandidateRecord.Talent_Pool_Id}`,
            "_blank"
          );
        }
      } else {
        if (this.parentTabName?.toLowerCase() === "job candidates") {
          window.open(
            this.$store.getters.baseUrl +
              `v3/recruitment/job-candidates?candidateId=${selectedCandidateRecord.Candidate_Id}&jobpostId=${selectedCandidateRecord?.Job_Post_Id}`,
            "_blank"
          );
        } else if (
          this.parentTabName?.toLowerCase() === "duplicate candidates"
        ) {
          window.open(
            this.$store.getters.baseUrl +
              `v3/recruitment/job-candidates/duplicate-candidates?candidateId=${selectedCandidateRecord.Candidate_Id}&jobpostId=${selectedCandidateRecord?.Job_Post_Id}`,
            "_blank"
          );
        } else {
          window.open(
            this.$store.getters.baseUrl +
              `v3/recruitment/job-candidates?candidateId=${selectedCandidateRecord.Candidate_Id}&jobpostId=${selectedCandidateRecord?.Job_Post_Id}`,
            "_blank"
          );
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeDuplicateCandidates() {
      this.openConfirmationModel = true;
      this.overlay = false;
    },

    closeWindow(isSuccess) {
      this.$emit("close-duplicate-candidates-window", isSuccess);
    },
  },
};
</script>

<style scoped>
.overlay {
  height: 100% !important;
}

.stage-screening {
  background-color: #faedcb; /* Pastel Yellow */
  color: #856404; /* Dark Goldenrod */
}

.stage-sourced {
  background-color: #c9e4de; /* Pastel Mint */
  color: #2c5f5d; /* Dark Teal */
}

.stage-interview {
  background-color: #c6def1; /* Pastel Sky Blue */
  color: #1e3a56; /* Dark Navy Blue */
}

.stage-hired {
  background-color: #dbcdf0; /* Pastel Lavender */
  color: #4a235a; /* Deep Purple */
}

.stage-archived {
  background-color: #f2c6de; /* Pastel Pink */
  color: #4b2c3e; /* Deep Plum */
}
.status-archived {
  background-color: #f6d5cf;
  color: #a43500;
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.stage-preboarding {
  background-color: #f7d9c4; /* Pastel Peach */
  color: #8c5023; /* Dark Burnt Orange */
}

.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
