<template>
  <AdminAttendance :callingFrom="callingForm"></AdminAttendance>
</template>

<script>
import AdminAttendance from "./AdminAttendance.vue";

export default {
  name: "AttendanceRouting",
  components: { AdminAttendance },
  computed: {
    callingForm() {
      let url = window.location.href;
      if (url.includes("my-team")) {
        return "team";
      } else {
        return "employee";
      }
    },
  },
};
</script>
