.dynamicFormTemplate {
  min-height:450px; 
}

.cf-chat .scrollableInner::-webkit-scrollbar { 
  display: none !important; 
} 

cf-chat .scrollableInner{
         overflow : auto !important
}

.rendered-form .btn, .rendered-form .form-control {
  border-bottom: 1px solid #d2d2d2;
}

.close-preview {
  margin-top: -2em;
}
.gridPanelPreviewFormTemplate {
  padding: 5em;
}
.client-records-not-found {
  color: #888383;
  font-size: 1.3em;
  font-weight: 500;
  text-align: -webkit-center;
  text-align: center;
  margin-top: 2%;
  margin-bottom: 8%;
}

.card-heading {
  font-weight: 900 !important;
}

.workflow-card-heading {
  text-transform: none !important;
}

.card-body-content {
  color: darkslategrey;
}

.card-body-sub-content {
  color: grey;
}

.recruitment-filter-img {
  display: inline-block;
  width: 24px;
  padding-bottom: 2px;
  margin-right: 10px;
}

.template-card-width-cls{
  width: 150px;
  height: 150px;
  cursor: pointer;
}

.conversational-img {
  display: inline-block;
  margin: 0 ;
  position: relative;
  box-shadow: 0 0 9px rgb(17, 40, 107);
}

.view-img {
  width: 100px !important; 
  height: 100px !important;
  display: unset;
}

.client-btns {
  bottom: 5px;
}

.client-search-option {
  width: 100%;
  border-radius: 10px;
  margin: unset !important;
  background: white !important;
  display: inline-flex;
  height: 45px;
}
/* .search-input {
  background-image: none !important;
  border: 1px solid #dfdfdf !important;
  border-radius: 4px;
  margin-top: 5px !important;
  padding-left:10px;
  background-color: #f9f6f6;
  color: black;
  width:83%;
  margin-left:15% !important;
} */

.client-search-box {
  width: 100%;
}

.client-search-box-lg {
  padding-left: 5px;
  padding-right: 5px;
}

.client-search-filter {
  text-align: -webkit-center;
  text-align: center;
}

.workflow-filter {
  margin-top: -5px !important;
  padding-left: 2px !important;

}

.workflow-button-filter {
  margin-top: 5px !important;
}
.clients-data {
  text-align: -webkit-center;
  text-align: center;
}

.client-verticalstyle {
  width: 1px;
  background-color: #dfdfdf;
  position: absolute;
  top: 0;
  bottom: 0;
  /* left: 170px; */
  left: 300px;
  /* right: 180px; */
}

.client-pagination-style {
  padding-left: 13px;
}

.client-search-btn {
  border-radius: 10px;
  margin: unset !important;
  background: white !important;
  display: inline-flex;
}
.client-search-icon {
  padding-top: 14px;
  position: absolute;
  float: left;
  margin-left: -36px;
}


.btn-group,
.btn-group-vertical {
  margin: none !important;
}

.custom-filter-options {
  padding : 0px !important;
  margin-top: 15px;
}

.select2-container .select2-choices {
  border: none !important;
  box-shadow: none !important;
  /* border-bottom: 1px solid #ffffff !important; */
  border-radius: 0px !important;
  background-color: #ffffff !important;
  border-bottom: 1px solid #d0d0d0 !important;
  border-radius: 0px !important;
}

.select2-container .select2-choices .select2-search-field input {
  background-color: #ffffff !important;
  border: none !important;
  /* border-bottom: 1px solid #0096ac !important; */
  border-radius: 0px !important;
}

.select2-dropdown-open {
  border: none !important;
}

.select2-container-active .select2-choice {
  border: none !important;
  box-shadow: none !important;
  border-bottom: 1px solid #0096ac !important;
}

.select2-drop-active {
  border-top: none !important;
  border: 1px solid #dddfe1 !important;
  box-shadow: none !important;
  /* border-bottom: 1px solid #0096ac !important; */
}

.select2-container-multi.select2-container-active .select2-choices {
  border: none !important;
  box-shadow: none !important;
  border-bottom: 1px solid #0096ac !important;
}

.select2-container-multi .select2-choices .select2-search-choice:hover {
  background: #0096ac !important;
  border: #0096ac !important;
}

.select2-container-multi .select2-choices .select2-search-choice {
  background-color: #0096ac !important;
  border: #0096ac !important;
}

.workflow-select-fields {
  margin-left: 10% !important;
}

.client-input-types {
  font-size: 16px;
  font-style: normal;
}

.client-records-not-found {
  color: #888383;
  font-size: 1.3em;
  font-weight: 500;
  text-align: -webkit-center;
  text-align: center;
  margin-top: 2%;
  margin-bottom: 8%;
}
.form-wrap.form-builder .frmb-control li {
font-family: inherit;
display: block;
}

.form-wrap.form-builder [class^=icon-]:before, .form-wrap.form-builder [class*=" icon-"]:before {
font-family: fb-icons;
}
.cf-radio-button.cf-button.animate-in { 
display: block !important; 
max-width: 300px !important;
}
.renderForm{
  background-color: white !important;
  padding: 30px;
}
.renderTemplate{
  margin-top: 30px;
  display: block !important;
  font-family:RobotoDraft,Roboto,Helvetica Neue,Helvetica,Arial,sans-serif !important;
  font-size: larger !important;
  overflow:hidden;
  min-height: 500px !important; 
}

.filterButton {
  margin-top: 5px !important;
  padding-left: 2px !important;
  position: absolute !important;
}

.searchField {
  background-image: none !important;
  border: 1px solid #dfdfdf !important;
  width:150px;
  border-radius: 4px;
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
  padding-left:10px !important;
  background-color: #f9f6f6;
  color: black;
}
.closeButtonDisplay {
  display: block !important;
}

.buttonBottom {
  bottom: 5px;
}

.addButtonAlign{
  text-align:left
}

.cursorPointer{
  cursor:pointer
}
.iconTemplateStyle {
  cursor:pointer;
  float:right; 
  margin-right : 5%
}

.iconViewFont {
  font-size:20px; 
  color: grey
}

.cardValues {
  margin-left:5px
}

.cardBorder{
  margin-top:10px; 
  border-top : 1px solid lightsteelblue;
}

.formPreview{
  cursor:pointer;
  font-size:15px; 
  color:royalblue; 
  margin-right: 5%; 
  float: right; 
  margin-top: 15px;
}

.cardHeaderName{
  height : auto !important; 
  margin-left:20px
}

.panelBorder {
  border-radius: 20px !important;
  box-shadow: 5px 5px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12) !important;
  border-left-width: 10px !important;
  border-left-color: royalblue  !important;
}

.panelBorderLeft {
  border-left-style: solid !important;

}
.closeButton{
  font-size:1em !important;
  color: black !important;
}
.createBtn{
  margin-top: -2em !important;
  margin-bottom:2em !important;
}


@media screen and (min-width: 1025px) and (max-width: 1140px) {
  .clients-filter-btn-group {
      text-align: -webkit-left;
      text-align: left;
          margin-left: -100px;
  }
  .client-add-button {
      text-align: -webkit-center;
      text-align: center;
  }
}

@media screen and (min-width: 1141px) {
  .clients-filter-btn-group {
      text-align: -webkit-center;
      text-align: center;
      }
  .client-add-button {
      text-align: -webkit-center;
      text-align: center;
  }
}


@media screen and (max-width: 767px) {
  .client-pagination-style {
      width: 100%;
  }
  .client-btn {
      width: 100%;
  }
  .client-input-fields-alignment {
      text-align: -webkit-center !important;
      text-align: center !important;
  }
}
/* @media screen and (max-width:535px)
{
  .search-input {       
      width: 73% !important; 
      margin-left: 30% !important;
  }
} */

@media screen and (width:1024px)
{
  .addButtonAlign {       
      margin-top: 2em;
      height: 2.4em;
      margin-left: 30em;
      margin-bottom: 1em;
  }
}

@media screen and (max-width: 991px) 
{
  .clients-filter-btn-group {
      text-align: -webkit-center;
      text-align: center;
          margin-top: 2%;
      margin-left: 0%;
      margin-right: 0%;
  }
  .client-add-button {
      text-align: -webkit-center;
      text-align: center;
      margin-top: 2em;
  }
  .client-search-option {
      padding-right: 15px;
      padding-left: 15px;
  }
  .client-pagination-style {
      min-width: 250px;
      text-align: -webkit-center;
      text-align: center;
  }
}

@media screen and (min-width: 1200px) {
  .client-search-not-found {
      margin-left: -85px;
  }
  .client-records-not-found {
      margin-left: -85px;
  }
}
