<template>
  <v-card class="rounded-lg">
    <div
      class="d-flex align-center"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center pl-4 py-2">
        <v-avatar class="mr-2" size="40" color="hover" variant="elevated">
          <i class="primary hr-workflow-approval-management text-h6"></i>
        </v-avatar>
        <div
          class="text-truncate"
          :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
        >
          <p class="text-subtitle-1 font-weight-bold">Individual Details</p>
        </div>
      </div>
      <div class="pa-3">
        <v-icon color="primary" @click="$emit('close-split-view')">
          fas fa-times
        </v-icon>
      </div>
    </div>
    <v-card>
      <v-row
        class="px-sm-8 px-md-12 mt-3 mb-6"
        :style="
          'overflow: scroll; height: ' + $store.getters.getTableHeight(350)
        "
      >
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Name</p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Candidate_Name) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Email</p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Candidate_Email) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Location</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Location_Name) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[151] && labelList[151].Field_Visiblity == 'Yes'"
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["151"].Field_Alias }}
          </p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Organization_Group) }}
          </p>
        </v-col>
        <v-col
          v-if="fieldForce"
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["115"]?.Field_Alias || "Service Provider" }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Service_Provider_Name) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[384]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[384]?.Field_Alias }}
          </p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Business_Unit) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Department</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Department_Name) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Designation</p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Designation_Name) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[425]?.Field_Visiblity.toLowerCase() === 'yes'"
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[425]?.Field_Alias }}
          </p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobRoleNames) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Work Schedule</p>
          <div class="value-text">
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedItem.WorkSchedule_Name) }}
            </p>
          </div>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Employment Type</p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Employee_Type) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Manager</p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Manager_Name) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          v-if="labelList[274]?.Field_Visiblity == 'Yes'"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[274].Field_Alias }}
          </p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(secondLineManagerDetails) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Job Code</p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Job_Code) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Date of Joining</p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(formatDate(selectedItem.Date_Of_Join, true)) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Probation Date</p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(formatDate(selectedItem.Probation_Date, true)) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">URL Validity</p>

          <p
            class="text-subtitle-1 font-weight-regular"
            :class="urlExpiryColorClass"
          >
            {{
              checkNullValue(convertUTCToLocal(selectedItem.Expire_Time, false))
            }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          v-if="labelList[303]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[303]?.Field_Alias }}
          </p>

          <p class="text-subtitle-1 font-weight-regular">
            {{
              checkNullValue(selectedItem.Group_Names?.split(",")?.join(", "))
            }}
          </p>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          lg="6"
          v-if="labelList[460]?.Field_Visiblity?.toLowerCase() == 'yes'"
          :class="isMobileView ? ' px-md-6 pb-0 ml-4' : 'px-md-6 pb-0'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{
              labelList[460]?.Field_Alias || "Accreditation Enforcement Groups"
            }}
          </p>

          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(selectedItem.Accreditation_Group_Names) }}
          </p>
        </v-col>
      </v-row>
      <v-row
        v-if="moreDetailsList.length > 0"
        class="px-sm-8 px-md-10 mt-2 mb-2"
      >
        <v-col cols="12">
          <MoreDetails
            :more-details-list="moreDetailsList"
            :open-close-card="openMoreDetails"
            @on-open-close="openMoreDetails = $event"
          ></MoreDetails> </v-col
      ></v-row>
    </v-card>
  </v-card>
</template>
<script>
import { checkNullValue, convertUTCToLocal } from "@/helper";
import { RETRIEVE_SECONDLINE_MANAGER } from "@/graphql/onboarding/individualQueries.js";
import moment from "moment";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
export default {
  name: "ViewInvitedIndividuals",
  data() {
    return {
      secondLineManagerDetails: null,
      isErrorInSeconLineManager: false,
      isLoading: false,
      moreDetailsList: [],
      openMoreDetails: true,
    };
  },
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    urlExpiryColorClass: {
      type: String,
      required: true,
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    formatedDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    jobRoleNames() {
      return (
        this.selectedItem?.Job_Role_Details?.map(
          (role) => role.Job_Role_Name
        ).join(", ") || null
      );
    },
  },
  mounted() {
    this.selectedManager = this.selectedItem.Manager_Id;
    if (this.selectedManager && this.labelList[274].Field_Visiblity === "Yes") {
      this.fetchSecondlineManagerDetails(this.selectedManager);
    }
    this.prefillMoreDetails();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    formatDate(date, withoutTime) {
      let orgDateFormat = withoutTime
        ? this.$store.state.orgDetails.orgDateFormat
        : this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
      return date ? moment(date).format(orgDateFormat) : "-";
    },
    fetchSecondlineManagerDetails(employeeId) {
      let vm = this;
      vm.isLoading = true;
      vm.isErrorInSeconLineManager = false;
      vm.$apollo
        .query({
          query: RETRIEVE_SECONDLINE_MANAGER,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: { employeeId: employeeId },
        })
        .then(({ data }) => {
          vm.isLoading = false;
          if (data && data.retrieveManagerDetails) {
            const response = data.retrieveManagerDetails;
            if (response.errorCode === "") {
              vm.secondLineManagerDetails = response.managerName;
            } else {
              vm.handleManagerError(response.message);
            }
          } else {
            vm.handleManagerError();
          }
        })
        .catch((err) => {
          vm.handleManagerError(err);
        });
    },
    handleManagerError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "individuals",
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInSeconLineManager = true;
        });
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatedDate(
          new Date(this.selectedItem.Created_At + ".000Z")
        ),
        addedByName = this.selectedItem.Created_By_Name,
        updatedByName = this.selectedItem.Updated_By_Name,
        updatedOn = this.formatedDate(
          new Date(this.selectedItem.Updated_At + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>
