import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_MY_INTEGRATION = gql`
  query retrieveMicrosoftIntegration($formId: Int!) {
    retrieveMicrosoftIntegration(formId: $formId) {
      message
      errorCode
      microsoftEmail
      calendarStatus
      teamsStatus
    }
  }
`;

// ===============
// Mutations
// ===============

export const UPDATE_MY_INTEGRATION = gql`
  mutation addUpdateMicrosoftIntegration(
    $action: String!
    $microsoftEmail: String!
    $calendarStatus: String
    $teamsStatus: String
  ) {
    addUpdateMicrosoftIntegration(
      action: $action
      microsoftEmail: $microsoftEmail
      calendarStatus: $calendarStatus
      teamsStatus: $teamsStatus
    ) {
      errorCode
      message
      validationError
    }
  }
`;

export const DELETE_MY_INTEGRATION = gql`
  mutation deleteMicrosoftIntegration {
    deleteMicrosoftIntegration {
      errorCode
      message
      validationError
    }
  }
`;
