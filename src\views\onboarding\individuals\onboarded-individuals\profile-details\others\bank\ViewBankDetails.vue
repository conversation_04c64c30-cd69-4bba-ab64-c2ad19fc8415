<template>
  <div
    v-if="bankDetails && bankDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No bank details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in bankDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width: 500px;border-left: 7px solid ${generateRandomColor()}; min-height:350px;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip :text="data.bankName" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="data.bankName ? props : ''"
                    >
                      {{ checkNullValue(data.bankName) }}
                      <span
                        v-if="data.Status"
                        :class="
                          data.Status === 'Active' ? 'text-green' : 'text-red'
                        "
                      >
                        - {{ data.Status }}</span
                      >
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>

      <div class="card-columns w-100 mt-n6">
        <span
          :style="!isMobileView ? 'width:55%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">
                {{ labelList[455]?.Field_Alias || "Account Number" }}
              </b>
              <span class="py-2">
                {{ checkNullValue(data.Bank_Account_Number) }}</span
              >
            </div>
            <div
              v-if="labelList[135].Field_Visiblity === 'Yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start"
                >{{ labelList[135].Field_Alias }}
              </b>
              <span class="py-2"> {{ checkNullValue(data.IFSC_Code) }}</span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Credit Account </b>
              <span class="py-2">
                {{ checkNullValue(data.Credit_Account) }}</span
              >
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">
                {{ labelList[235].Field_Alias }}
              </b>
              <span
                class="py-2"
                :style="isMobileView ? 'max-width: 180px' : 'max-width: 350px'"
              >
                {{ checkNullValue(data.Street) }}</span
              >
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">State </b>
              <span class="py-2"> {{ checkNullValue(data.State) }}</span>
            </div>
            <div
              class="mt-2 mr-2 d-flex flex-column justify-start"
              v-if="labelList[346]?.Field_Visiblity?.toLowerCase() === 'yes'"
            >
              <b class="mr-2 text-grey justify-start">
                {{ labelList[346].Field_Alias }}
              </b>
              <span class="py-2">
                {{ checkNullValue(data.Bank_Account_Name) }}</span
              >
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              class="mt-2 mr-2 d-flex flex-column justify-start"
              v-if="labelList[443]?.Field_Visiblity === 'Yes'"
            >
              <b class="mr-2 text-grey justify-start">
                {{ labelList[443].Field_Alias }}
              </b>
              <span class="py-2"> {{ checkNullValue(data.Account_Type) }}</span>
            </div>
            <div
              class="mt-2 mr-2 d-flex flex-column justify-start"
              v-if="labelList[442]?.Field_Visiblity === 'Yes'"
            >
              <b class="mr-2 text-grey justify-start">
                {{ labelList[442].Field_Alias }}
              </b>
              <span class="py-2"> {{ checkNullValue(data.Branch_Name) }}</span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Beneficiary Id </b>
              <span class="py-2">
                {{ checkNullValue(data.Beneficiary_Id) }}</span
              >
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">City </b>
              <span class="py-2"> {{ checkNullValue(data.City) }}</span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">{{
                labelList[146].Field_Alias
              }}</b>
              <span class="py-2"> {{ checkNullValue(data.Zip) }}</span>
            </div>
            <div
              v-if="
                data.File_Name &&
                labelList[449]?.Field_Visiblity?.toLowerCase() === 'yes'
              "
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span class="text-blue-grey-darken-3 font-weight-bold"></span>
              <span class="text-blue-grey-darken-6">
                <span
                  style="text-decoration: underline"
                  @click="retrieveDocuments(data.File_Name)"
                  class="text-green cursor-pointer"
                >
                  View Document</span
                >
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="formAccess && formAccess.update" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employee Bank Details"
    fileRetrieveType="documents"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { defineAsyncComponent } from "vue";

const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

export default {
  name: "ViewBanks",
  components: { ActionMenu, FilePreviewModal },

  props: {
    bankDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      retrievedFileName: "",
      openModal: false,
      havingAccess: {},
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    //using the generateRandomColor function of helper.js file
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.bankDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    retrieveDocuments(fileName) {
      this.retrievedFileName = fileName;
      this.openModal = true;
    },
  },
};
</script>
