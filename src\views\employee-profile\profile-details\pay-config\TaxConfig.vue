<template>
  <div class="mt-4">
    <div class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="yellow"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold">Tax</span>
      </div>
      <div v-if="!showEditForm && formAccess && formAccess.update && allowEdit">
        <v-btn @click="openEditForm" color="primary" variant="text">
          <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
        </v-btn>
      </div>
    </div>
    <div v-if="showEditForm">
      <v-form ref="taxConfigObserver">
        <v-row class="pa-4 ma-2">
          <v-col
            v-if="
              labelList['109'] && labelList['109'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="4"
            sm="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["109"].Field_Alias }}
            </p>
            <v-tooltip
              text="Since the payslip has already been generated, updates to this field are unavailable. If any changes are required, please delete the Salary Payslip and Full & Final Settlement."
            >
              <template v-slot:activator="{ props }">
                <v-card
                  width="max-content"
                  variant="plain"
                  v-bind="fieldsEditable ? '' : props"
                  :class="fieldsEditable ? '' : 'cursor-not-allowed'"
                >
                  <v-switch
                    color="primary"
                    v-model="editTDSContractorData.Eligible_For_PT"
                    :disabled="
                      validationData.professionalTax || !fieldsEditable
                    "
                    :true-value="1"
                    :false-value="0"
                    @update:model-value="onChangeFields"
                  ></v-switch>
                </v-card>
              </template>
            </v-tooltip>
          </v-col>
          <v-col
            v-if="
              sectionList.length > 0 &&
              labelList['113'] &&
              labelList['113'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="4"
            sm="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["113"].Field_Alias }}
            </p>
            <v-switch
              color="primary"
              v-model="editTDSContractorData.Eligible_For_Contractor_Tds"
              :true-value="1"
              :false-value="0"
              @update:model-value="onChangeFields('tds')"
            ></v-switch>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <CustomSelect
              :items="['Gross', 'Gross Up']"
              :itemSelected="editTDSContractorData.Salary_Calculation_Scheme"
              style="max-width: 250px"
              :label="`Salary Configuration Schema`"
              @selected-item="
                onChangeCustomSelectField($event, 'Salary_Calculation_Scheme')
              "
            ></CustomSelect>
          </v-col>
          <v-col
            v-if="
              sectionList.length > 0 &&
              editTDSContractorData.Eligible_For_Contractor_Tds &&
              labelList['114'] &&
              labelList['114'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="4"
            sm="6"
          >
            <CustomSelect
              :items="tdsStaticData"
              :itemSelected="editTDSContractorData.Tax_Section_Id"
              itemValue="Tax_Section_Id"
              itemTitle="Tax_Section_Name"
              subText="Description"
              style="max-width: 250px"
              :label="labelList['114'].Field_Alias"
              :isRequired="
                labelList['114'].Mandatory_Field === 'Yes' ? true : false
              "
              :rules="[
                labelList['114'].Mandatory_Field === 'Yes'
                  ? required(
                      labelList['114'].Field_Alias,
                      editTDSContractorData.Tax_Section_Id
                    )
                  : true,
              ]"
              @selected-item="
                onChangeCustomSelectField($event, 'Tax_Section_Id')
              "
            ></CustomSelect>
          </v-col>
          <v-col
            v-if="
              sectionList.length > 0 &&
              editTDSContractorData.Eligible_For_Contractor_Tds &&
              editTDSContractorData.Tax_Section_Id
            "
            cols="12"
            md="4"
            sm="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              Threshold limit per annum
            </p>
            <p class="text-subtitle-1 mt-4 font-weight-regular">
              {{
                checkNullValue(editTDSContractorData.Threshold_Limit_Per_Annum)
              }}
            </p>
          </v-col>
          <v-col
            v-if="
              sectionList.length > 0 &&
              editTDSContractorData.Eligible_For_Contractor_Tds &&
              editTDSContractorData.Tax_Section_Id
            "
            cols="12"
            md="4"
            sm="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">TDS Rate</p>
            <p class="text-subtitle-1 mt-4 font-weight-regular">
              {{ checkNullValue(editTDSContractorData.TDS_Rate) }}
            </p>
          </v-col>
          <v-col
            v-if="
              sectionList.length > 0 &&
              editTDSContractorData.Eligible_For_Contractor_Tds &&
              editTDSContractorData.Tax_Section_Id
            "
            cols="12"
            md="4"
            sm="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">TDS Description</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(editTDSContractorData.Description) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <v-text-field
              v-model="editTDSContractorData.Minimum_Wage"
              label="Minimum Wage"
              type="number"
              variant="solo"
              :rules="[
                editTDSContractorData.Minimum_Wage
                  ? minNumberValidation(
                      'Minimum Wage',
                      editTDSContractorData.Minimum_Wage,
                      0
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields('minimum_wage')"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <CustomSelect
              :items="['Employment Start', 'Residency Threshold']"
              :itemSelected="editTDSContractorData.TDS_Effective_From"
              style="max-width: 250px"
              label="TDS to be Deducted"
              @selected-item="
                onChangeCustomSelectField($event, 'TDS_Effective_From')
              "
            ></CustomSelect>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              FRRO Registeration Completed
            </p>
            <v-switch
              color="primary"
              v-model="editTDSContractorData.FRRO_Registration"
              :true-value="'Yes'"
              :false-value="'No'"
              @update:model-value="onChangeFields()"
            ></v-switch>
          </v-col>
        </v-row>
      </v-form>
    </div>
    <div v-else>
      <v-row class="pa-4 ma-2 card-blue-background">
        <v-col
          v-if="labelList['109'] && labelList['109'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["109"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ viewTDSContractorData.Eligible_For_PT ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col
          v-if="
            sectionList.length > 0 &&
            labelList['113'] &&
            labelList['113'].Field_Visiblity === 'Yes'
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["113"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              viewTDSContractorData.Eligible_For_Contractor_Tds ? "Yes" : "No"
            }}
          </p>
        </v-col>
        <v-col
          v-if="
            sectionList.length > 0 &&
            viewTDSContractorData.Eligible_For_Contractor_Tds &&
            labelList['114'] &&
            labelList['114'].Field_Visiblity === 'Yes'
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["114"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewTDSContractorData.Tax_Section_Name) }}
          </p>
        </v-col>
        <v-col
          v-if="
            sectionList.length > 0 &&
            viewTDSContractorData.Eligible_For_Contractor_Tds &&
            viewTDSContractorData.Tax_Section_Name
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            Threshold limit per annum
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ viewTDSContractorData.Threshold_Limit_Per_Annum }}
          </p>
        </v-col>
        <v-col
          v-if="
            sectionList.length > 0 &&
            viewTDSContractorData.Eligible_For_Contractor_Tds &&
            viewTDSContractorData.Tax_Section_Name
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">TDS Rate</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ viewTDSContractorData.TDS_Rate }}
          </p>
        </v-col>
        <v-col
          v-if="
            sectionList.length > 0 &&
            viewTDSContractorData.Eligible_For_Contractor_Tds &&
            viewTDSContractorData.Tax_Section_Name
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">TDS Description</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewTDSContractorData.Description) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Salary Configuration Group
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              checkNullValue(viewTDSContractorData.Salary_Calculation_Scheme)
            }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Minimum Wage</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewTDSContractorData.Minimum_Wage) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">TDS to be deducted</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewTDSContractorData.TDS_Effective_From) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            FRRO Registeration Completed
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewTDSContractorData.FRRO_Registration) }}
          </p>
        </v-col>
      </v-row>
    </div>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateTDSContractor()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import { UPDATE_TDS_CONTRACTOR_CONFIG } from "@/graphql/employee-profile/profileQueries.js";
import { checkNullValue } from "@/helper.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "TaxConfig",
  components: { CustomSelect },
  mixins: [validationRules],
  props: {
    tdsData: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      required: true,
    },
    allowEdit: {
      type: Boolean,
      required: true,
    },
    sectionList: {
      type: Array,
      required: true,
    },
    validationData: {
      type: Object,
      required: true,
    },
    labelList: {
      type: Array,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
  },

  emits: ["update-success", "edit-form-opened"],

  data: () => ({
    viewTDSContractorData: {},
    editTDSContractorData: {},
    showEditForm: false,
    isFormDirty: false,
    isLoading: false,
    openMoreDetails: false,
    validationMessages: [],
    showValidationAlert: false,
    openWarningModal: false,
    openBottomSheet: false,
    tdsStaticData: [],
    fieldsEditable: true,
  }),
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    const { pan, residentType } = this.validationData;
    this.viewTDSContractorData = this.tdsData;
    this.tdsStaticData = this.sectionList;
    this.viewTDSContractorData["Tax_Section_Id"] = this.viewTDSContractorData
      .Tax_Section_Id
      ? this.viewTDSContractorData.Tax_Section_Id
      : null;
    if (
      this.viewTDSContractorData.Tax_Section_Id &&
      this.tdsStaticData.length > 0
    ) {
      let filteredTDSStaticData = this.tdsStaticData.filter(
        (el) => el.Tax_Section_Id == this.viewTDSContractorData.Tax_Section_Id
      );
      if (filteredTDSStaticData && filteredTDSStaticData.length > 0) {
        this.viewTDSContractorData["Tax_Section_Name"] =
          filteredTDSStaticData[0].Tax_Section_Name;
        this.viewTDSContractorData["Threshold_Limit_Per_Annum"] =
          residentType === "Resident"
            ? filteredTDSStaticData[0].Resident_Threshold_Limit
            : filteredTDSStaticData[0].NonResident_Threshold_Limit;
        this.viewTDSContractorData["TDS_Rate"] =
          residentType === "Resident"
            ? !pan
              ? filteredTDSStaticData[0].Resident_NoPan_Tds_Rate
              : filteredTDSStaticData[0].Resident_Tds_Rate
            : !pan
            ? filteredTDSStaticData[0].NonResident_NoPan_Tds_Rate
            : filteredTDSStaticData[0].NonResident_Tds_Rate;
        this.viewTDSContractorData["Description"] =
          filteredTDSStaticData[0].Description;
      }
    }
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    openBottomSheet(val) {
      this.$emit("edit-form-opened", val);
    },
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
    validationData(val) {
      if (val) {
        this.fieldsEditable = this.selectedEmpStatus == "Active" || false;
      }
    },
  },

  methods: {
    checkNullValue,

    onChangeFields(field = "") {
      if (field === "tds") {
        this.editTDSContractorData["Tax_Section_Id"] = null;
      }
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.editTDSContractorData[field] = value;
      let filteredTDSStaticData = this.tdsStaticData.filter(
        (el) => el.Tax_Section_Id == value
      );
      if (filteredTDSStaticData && filteredTDSStaticData.length > 0) {
        const { pan, residentType } = this.validationData;
        this.editTDSContractorData["Threshold_Limit_Per_Annum"] =
          residentType === "Resident"
            ? filteredTDSStaticData[0].Resident_Threshold_Limit
            : filteredTDSStaticData[0].NonResident_Threshold_Limit;
        this.editTDSContractorData["TDS_Rate"] =
          residentType === "Resident"
            ? !pan
              ? filteredTDSStaticData[0].Resident_NoPan_Tds_Rate
              : filteredTDSStaticData[0].Resident_Tds_Rate
            : !pan
            ? filteredTDSStaticData[0].NonResident_NoPan_Tds_Rate
            : filteredTDSStaticData[0].NonResident_Tds_Rate;
        this.editTDSContractorData["Description"] =
          filteredTDSStaticData[0].Description;
      }
      this.onChangeFields();
    },

    openEditForm() {
      mixpanel.track("EmpProfile-payConfig-tax-edit-opened");
      this.editTDSContractorData = JSON.parse(
        JSON.stringify(this.viewTDSContractorData)
      );
      this.showEditForm = true;
      this.openBottomSheet = true;
    },

    closeEditForm() {
      mixpanel.track("EmpProfile-payConfig-tax-edit-closed");
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.showEditForm = false;
      }
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    async validateTDSContractor() {
      const { valid } = await this.$refs.taxConfigObserver.validate();
      mixpanel.track("EmpProfile-payConfig-tax-submit-clicked");
      if (valid) {
        this.updateTDSContractor();
      }
    },

    updateTDSContractor() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_TDS_CONTRACTOR_CONFIG,
          variables: {
            employeeId: vm.selectedEmpId,
            eligibleForContractorTds: vm.editTDSContractorData
              .Eligible_For_Contractor_Tds
              ? vm.editTDSContractorData.Eligible_For_Contractor_Tds
              : 0,
            taxSectionId:
              vm.editTDSContractorData.Eligible_For_Contractor_Tds &&
              vm.editTDSContractorData.Tax_Section_Id
                ? vm.editTDSContractorData.Tax_Section_Id
                : 0,
            eligibleForPt: vm.editTDSContractorData.Eligible_For_PT
              ? vm.editTDSContractorData.Eligible_For_PT
              : 0,
            salaryCalculationScheme: vm.editTDSContractorData
              .Salary_Calculation_Scheme
              ? vm.editTDSContractorData.Salary_Calculation_Scheme
              : null,
            minimumWage: vm.editTDSContractorData.Minimum_Wage
              ? parseFloat(vm.editTDSContractorData.Minimum_Wage)
              : null,
            tdsEffectiveFrom: vm.editTDSContractorData.TDS_Effective_From
              ? vm.editTDSContractorData.TDS_Effective_From
              : null,
            frroRegistration: vm.editTDSContractorData.FRRO_Registration
              ? vm.editTDSContractorData.FRRO_Registration
              : "No",
          },
          client: "apolloClientAD",
        })
        .then(() => {
          mixpanel.track("EmpProfile-payConfig-tax-edit-success");
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Tax configuration updated successfully.",
          };
          vm.showAlert(snackbarData);
          vm.openBottomSheet = false;
          vm.showEditForm = false;
          vm.isLoading = false;
          vm.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
          vm.$store.commit(
            "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
            "0-false"
          );
          vm.$emit("update-success");
        })
        .catch((updateError) => {
          vm.handleUpdateError(updateError);
        });
    },

    handleUpdateError(err) {
      mixpanel.track("EmpProfile-payConfig-tax-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "tax configuration",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
