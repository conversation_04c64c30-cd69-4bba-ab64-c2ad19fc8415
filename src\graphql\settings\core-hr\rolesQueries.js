import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_ROLE_CONFIGURATIONS = gql`
  query listRoles {
    listRoles {
      errorCode
      message
      listRoles {
        Roles_Id
        Roles_Name
        Is_Template
        Type_Of_Admin
        Description
        Role_Status
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;

export const RETRIEVE_ROLE_ACCESS_RIGHTS = gql`
  query retrieveRolesAccessRights(
    $Roles_Id: Int
    $Template_Name: String
    $formId: Int
  ) {
    retrieveRolesAccessRights(
      Roles_Id: $Roles_Id
      Template_Name: $Template_Name
      formId: $formId
    ) {
      errorCode
      message
      rolesData {
        Roles_Id
        Roles_Name
        Description
        Is_Template
        Role_Status
        Modules
        Forms
        AccessRights
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_ROLES = gql`
  mutation addUpdateRoles(
    $roleId: Int
    $roleName: String
    $description: String
    $roleStatus: String
    $accessRights: String
  ) {
    addUpdateRoles(
      roleId: $roleId
      roleName: $roleName
      description: $description
      roleStatus: $roleStatus
      accessRights: $accessRights
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_ROLES = gql`
  mutation deleteRoles($roleId: Int!) {
    deleteRoles(roleId: $roleId) {
      errorCode
      message
    }
  }
`;

export const ASSOCIATE_ROLES_TO_EMPLOYEES = gql`
  mutation associateRolesToEmployees($roleId: Int!, $employeeIds: [Int!]) {
    associateRolesToEmployees(roleId: $roleId, employeeIds: $employeeIds) {
      errorCode
      message
    }
  }
`;
