<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <OrgGroupFilter
                v-if="
                  !dropDownLoading &&
                  !showAddEditForm &&
                  (itemList.length || isFilterApplied)
                "
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilterFromFilterComponent($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="business-unit-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList('Organization unit error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      :notes="`The ${landedFormName} feature enables organizations to establish a hierarchical structure based on distinct functions within the business. This functionality allows for better organization and management of different business areas such as support, delivery, and other broader functions.`"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      color="secondary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddBusinessUnit()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Configure Organization Unit
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      variant="flat"
                      rounded="lg"
                      class="mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <div
                v-if="originalList.length > 0 && !isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  v-if="formAccess.add"
                  prepend-icon="fas fa-plus"
                  color="primary"
                  rounded="lg"
                  class="mx-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="onAddBusinessUnit"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  Add Configuration
                </v-btn>
                <v-btn
                  rounded="lg"
                  class="mt-1"
                  color="transparent"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-tooltip :text="action.message">
                              <template v-slot:activator="{ props }">
                                <div v-bind="action.message ? props : ''">
                                  {{ action.key }}
                                </div>
                              </template>
                            </v-tooltip>
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            {{ landedFormName }}
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.organizationGroupId ===
                                  item.organizationGroupId
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.organizationGroup"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.organizationGroup.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ item.organizationGroup }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            {{ landedFormName }}
                          </div>
                          <v-tooltip
                            :text="item.organizationGroup"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.organizationGroup.length > 100
                                    ? props
                                    : ''
                                "
                                :style="
                                  !isMobileView
                                    ? 'max-width: 500px; '
                                    : 'max-width: 200px; '
                                "
                              >
                                {{ checkNullValue(item.organizationGroupCode) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Description
                          </div>
                          <v-tooltip
                            :text="item.description"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.description.length > 100 ? props : ''
                                "
                                :style="
                                  !isMobileView
                                    ? 'max-width: 500px; '
                                    : 'max-width: 200px; '
                                "
                              >
                                {{ checkNullValue(item.description) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'width: max-content'
                          "
                          :cols="2"
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Status
                          </div>

                          <div
                            @click.stop="
                              {
                              }
                            "
                          >
                            <AppToggleButton
                              button-active-text="Active"
                              button-inactive-text="InActive"
                              button-active-color="#7de272"
                              button-inactive-color="red"
                              id-value="gab-analysis-based-on"
                              :current-value="
                                item.status === 'Active' ? true : false
                              "
                              :isDisableToggle="!formAccess.update"
                              :tooltipContent="
                                formAccess.update
                                  ? ''
                                  : `Sorry, you don't have access rights to update the status`
                              "
                              @chosen-value="updateStatus($event, item)"
                            ></AppToggleButton>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewOrganizationGroup
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                    :access-rights="formAccess"
                    :landedFormName="landedFormName"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>

                <v-col
                  :cols="originalList.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264"
                >
                  <EditOrganizationGroup
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :originalList="originalList"
                    :maxOrgCode="maxOrgCode"
                    :landedFormName="landedFormName"
                    @close-form="closeAllForms()"
                    @edit-updated="refetchList('og updated successfully')"
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <EditOrganizationGroup
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :maxOrgCode="maxOrgCode"
        :landedFormName="landedFormName"
        :originalList="originalList"
        @close-form="closeAllForms()"
        @edit-updated="refetchList('OG updated successfully')"
      />
      <ViewOrganizationGroup
        v-if="showViewForm"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        :access-rights="formAccess"
        :landedFormName="landedFormName"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EditOrganizationGroup = defineAsyncComponent(() =>
  import("./EditOrganisationGroup.vue")
);
import ViewOrganizationGroup from "./ViewOrganizationGroup.vue";
import { checkNullValue } from "@/helper.js";
// Queries
import {
  RETRIEVE_ORGANISATION_GROUP_LIST,
  UPDATE_ORGANIZATION_GROUP_STATUS,
} from "@/graphql/corehr/organisationGroupQueries";
import mixpanel from "mixpanel-browser";
import FileExportMixin from "@/mixins/FileExportMixin";
import Config from "@/config.js";
import OrgGroupFilter from "./OrgGroupFilter.vue";

export default {
  name: "OrganizationGroup",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    EditOrganizationGroup,
    OrgGroupFilter,
    ViewOrganizationGroup,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    isFilterApplied: false,
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    maxOrgCode: null,
    // add/update
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    dropDownLoading: false,
    openMoreMenu: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
  }),
  computed: {
    landedFormName() {
      let projectForm = this.accessRights("269");
      if (projectForm && projectForm.customFormName) {
        return projectForm.customFormName;
      } else return "Organization Group";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    orgStructureFormAccess() {
      return this.$store.getters.orgStructureFormAccess;
    },
    formAccess() {
      let formAccessRights = this.accessRights("269");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.orgStructureFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === "Organization Group"
          ) {
            let fName =
              access === "Organization Group" ? this.landedFormName : access;
            formAccessArray.push(fName);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: this.landedFormName,
            align: "start",
            key: "orgName",
          },
          {
            title: this.landedFormName + " Code",
            key: "orgGroupCode",
          },
          {
            title: "Status",
            key: "status",
          },
        ];
      } else {
        return [
          {
            title: this.landedFormName,
            align: "start",
            key: "orgName",
          },
          {
            title: this.landedFormName + " Code",
            key: "orgGroupCode",
          },
          {
            title: "Description",
            key: "description",
          },
          {
            title: "Status",
            key: "status",
          },
        ];
      }
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    moreActions() {
      return [{ key: "Export" }];
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    onTabChange(tab) {
      mixpanel.track("Organization unit form tab changed");
      if (tab !== this.landedFormName) {
        // this.isLoading = true;
        const { formAccess } = this.orgStructureFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
      let filterObj = {
        orgGroupCode: "",
        orgGroup: "",
        parentCode: "",
        status: null,
      };
      this.applyFilter(filterObj);
    },

    applyFilterFromFilterComponent(filter) {
      this.applyFilter(filter);
    },

    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter.orgGroupCode && filter.orgGroupCode != "") {
        filteredList = filteredList.filter((item) => {
          return item.organizationGroupCode == filter.orgGroupCode;
        });
      }
      if (filter.orgGroup) {
        filteredList = filteredList.filter((item) => {
          return item.organizationGroup == filter.orgGroup;
        });
      }
      if (filter.status) {
        filteredList = filteredList.filter((item) => {
          return item.status == filter.status;
        });
      }
      this.isFilterApplied = true;
      this.filteredList = filteredList;
      this.itemList = filteredList;
    },
    // resetFilter() {
    //   this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    //   this.itemList = this.originalList;
    // },\
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType.key === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: this.landedFormName + " Code",
          key: "organizationGroupCode",
        },
        {
          header: this.landedFormName,
          key: "organizationGroup",
        },
        {
          header: "Description",
          key: "description",
        },
        {
          header: "Status",
          key: "status",
        },
      ];
      if (this.entomoIntegrationEnabled && this.isEntomoSyncTypePush) {
        exportHeaders.unshift({
          header: "Level",
          key: "level",
        });
      }
      let organizationGroup = this.itemList;
      let exportOptions = {
        fileExportData: organizationGroup,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },

    openViewForm(item) {
      mixpanel.track("Organization unit view form opened");
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    openEditForm() {
      mixpanel.track("Business unit edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    onAddBusinessUnit() {
      mixpanel.track("Organization unit add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      mixpanel.track("Organization unit all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_ORGANISATION_GROUP_LIST,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: { formId: 269 },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listOrganizationGroup &&
            !response.data.listOrganizationGroup.errorCode
          ) {
            const { organizationGroupObject } =
              response.data.listOrganizationGroup;
            if (organizationGroupObject) {
              vm.maxOrgCode = organizationGroupObject.maxOrgCode;
              const orgList = organizationGroupObject.organizationGroupList
                ? organizationGroupObject.organizationGroupList
                : [];
              vm.itemList = orgList;
              vm.originalList = orgList;
              vm.onApplySearch();
            }
            vm.listLoading = false;
            mixpanel.track("OG unit list retrieved");
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      mixpanel.track("Organization unit error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Organization unit configuration",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },

    updateStatus(statusVal, item) {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_ORGANIZATION_GROUP_STATUS,
            variables: {
              Status: statusVal[1] ? "Active" : "InActive",
              organizationGroupId: item.organizationGroupId,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: this.landedFormName + " status updated successfully",
            };
            vm.showAlert(snackbarData);
            vm.refetchList("status-updated");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },

    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "status",
        isListError: false,
      });
      this.refetchList("status-update-failed");
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.business-unit-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .business-unit-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
