<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" width="500">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="my-4 mr-4" style="overflow-y: scroll !important">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              label="Candidate name"
              :isAutocomplete="true"
              :itemSelected="candidateName"
              :items="candidateNameList"
              density="compact"
              multiple
              closable-chips
              chips
              @selected-item="onChangeSelectField($event, 'candidateName')"
            >
            </CustomSelect>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              label="Job title"
              :isAutocomplete="true"
              :itemSelected="jobTitle"
              :items="jobTitleList"
              density="compact"
              multiple
              closable-chips
              chips
              @selected-item="onChangeSelectField($event, 'jobTitle')"
            >
            </CustomSelect>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              label="Interview round"
              :isAutocomplete="true"
              :itemSelected="interviewRound"
              :items="interviewRoundList"
              density="compact"
              multiple
              closable-chips
              chips
              @selected-item="onChangeSelectField($event, 'interviewRound')"
            ></CustomSelect>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              label="Panel members"
              :isAutocomplete="true"
              :itemSelected="panelMember"
              :items="panelMemberList"
              density="compact"
              multiple
              closable-chips
              chips
              @selected-item="onChangeSelectField($event, 'panelMember')"
            >
            </CustomSelect>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-menu
              v-model="minStartDateModal"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
              ><template v-slot:activator="{ props }">
                <v-text-field
                  ref="minStartDate"
                  v-model="formattedMinStartDate"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  v-bind="props"
                  variant="solo"
                  density="compact"
                >
                  <template v-slot:label>
                    Minimum start date
                  </template></v-text-field
                >
              </template>
              <v-date-picker
                v-model="minStartDate"
                @update:modelValue="
                  onChangeDate('formattedMinStartDate', minStartDate)
                "
              ></v-date-picker>
            </v-menu>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-menu
              v-model="maxStartDateModal"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
              ><template v-slot:activator="{ props }">
                <v-text-field
                  ref="maxStartDate"
                  v-model="formattedMaxStartDate"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  v-bind="props"
                  variant="solo"
                  density="compact"
                >
                  <template v-slot:label>
                    Maximum start date
                  </template></v-text-field
                >
              </template>
              <v-date-picker
                v-model="maxStartDate"
                @update:modelValue="
                  onChangeDate('formattedMaxStartDate', maxStartDate)
                "
              ></v-date-picker>
            </v-menu>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-menu
              v-model="minEndDateModal"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
              ><template v-slot:activator="{ props }">
                <v-text-field
                  ref="maxStartDate"
                  v-model="formattedMinEndDate"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  v-bind="props"
                  variant="solo"
                  density="compact"
                >
                  <template v-slot:label>
                    Minimum end date
                  </template></v-text-field
                >
              </template>
              <v-date-picker
                v-model="minEndDate"
                @update:modelValue="
                  onChangeDate('formattedMinEndDate', minEndDate)
                "
              ></v-date-picker>
            </v-menu>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-menu
              v-model="maxEndDateModal"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
              ><template v-slot:activator="{ props }">
                <v-text-field
                  ref="maxStartDate"
                  v-model="formattedMaxEndDate"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  v-bind="props"
                  variant="solo"
                  density="compact"
                >
                  <template v-slot:label>
                    Maximum end date
                  </template></v-text-field
                >
              </template>
              <v-date-picker
                v-model="maxEndDate"
                @update:modelValue="
                  onChangeDate('formattedMaxEndDate', maxEndDate)
                "
              ></v-date-picker>
            </v-menu>
          </v-col>

          <v-col class="py-2" :cols="12"> </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import moment from "moment";
export default defineComponent({
  name: "FormFilter",

  emits: ["apply-filter", "reset-filter"],

  components: { CustomSelect },

  data: () => ({
    openFormFilter: false,
    jobTitleList: [],
    interviewRoundList: [],
    panelMemberList: [],
    candidateNameList: [],
    candidateName: [],
    jobTitle: [],
    interviewRound: [],
    panelMember: [],
    minStartDate: new Date(),
    minStartDateModal: false,
    formattedMinStartDate: "",
    maxStartDate: new Date(),
    maxStartDateModal: false,
    formattedMaxStartDate: "",
    minEndDate: new Date(),
    minEndDateModal: false,
    formattedMinEndDate: "",
    maxEndDate: new Date(),
    maxEndDateModal: false,
    formattedMaxEndDate: "",
  }),

  props: {
    dropdown: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    minimumDateAllowed() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 80);
      const minBirthDateISO = minBirthDate;
      return minBirthDateISO;
    },
  },
  watch: {
    formattedMinStartDate(val) {
      if (val) {
        this.minStartDateModal = false;
      }
    },
    formattedMaxStartDate(val) {
      if (val) {
        this.maxStartDateModal = false;
      }
    },
    formattedMinEndDate(val) {
      if (val) {
        this.minEndDateModal = false;
      }
    },
    formattedMaxEndDate(val) {
      if (val) {
        this.maxEndDateModal = false;
      }
    },
  },
  mounted() {
    if (this.dropdown && this.dropdown.length > 0) {
      this.dropdown.map((item) => {
        if (!this.candidateNameList.includes(item.Candidate_Name)) {
          this.candidateNameList.push(item.Candidate_Name);
        }
        if (!this.interviewRoundList.includes(item.Round_Name)) {
          this.interviewRoundList.push(item.Round_Name);
        }
        if (!this.jobTitleList.includes(item.Job_Post_Name)) {
          this.jobTitleList.push(item.Job_Post_Name);
        }
        let members = item.Panel_Member_Name.split(", ");
        members.map((member) => {
          if (!this.panelMemberList.includes(member)) {
            this.panelMemberList.push(member);
          }
        });
      });
    }
  },
  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    onChangeDate(field, value) {
      this[field] = this.formatDate(value);
    },
    formatDate(date) {
      if (date && date !== "0000-00-00" && date != "Invalid Date") {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return date ? moment(date).format(orgDateFormat) : "";
      }
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        jobTitle: this.jobTitle,
        interviewRound: this.interviewRound,
        panelMember: this.panelMember,
        minStartDate: this.formattedMinStartDate ? this.minStartDate : "",
        maxStartDate: this.formattedMaxStartDate ? this.maxStartDate : "",
        minEndDate: this.formattedMinEndDate ? this.minEndDate : "",
        maxEndDate: this.formattedMaxEndDate ? this.maxEndDate : "",
        candidateName: this.candidateName,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.jobTitle = [];
      this.interviewRound = [];
      this.panelMember = [];
      this.formattedMinStartDate = "";
      this.formattedMaxStartDate = "";
      this.formattedMinEndDate = "";
      this.formattedMaxEndDate = "";
      this.candidateName = [];
    },
  },
});
</script>
