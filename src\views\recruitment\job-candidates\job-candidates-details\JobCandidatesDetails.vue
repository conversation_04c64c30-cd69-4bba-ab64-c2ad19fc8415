<template>
  <div>
    <JobCandidateTopCard
      v-if="candidateIdSelected"
      :listLoading="isLoading"
      :selectedItem="selectedItem"
      :candidateDetails="candidateDetails"
      :candidateIdSelected="candidateIdSelected"
      :form-access="formAccess"
      :candidateList="candidateList"
      :candidateJobPostIdSelected="candidateJobPostIdSelected"
      :isRecruiter="isRecruiter"
      @close-view-form="closeViewForm()"
      @edit-updated="retrieveJobCandidateDetails()"
      :parentTabName="parentTabName"
      @status-updated="retrieveJobCandidateDetails('', 'edit')"
      @on-change-candidate="$emit('on-change-candidate', $event)"
    ></JobCandidateTopCard>
    <ProfileCard class="mt-3 mb-2">
      <FormTab :model-value="activeTab" :hide-slider="true">
        <v-tab
          v-for="tab in viewFormTabs"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          @click="onChangeViewFormTabs(tab.value)"
        >
          <div
            :class="[
              isActiveTab(tab)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            {{ tab.label }}
            <div
              v-if="isActiveTab(tab)"
              class="mt-3 mb-n4"
              style="border-bottom: 4px solid; width: 200px"
            ></div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>

    <ProfileCard class="my-5" ref="viewFormContent">
      <div v-if="isLoading" class="pa-4">
        <div v-for="i in 5" :key="i" class="mt-2">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <div v-else>
        <div v-if="activeTab === 'Profile'">
          <div
            v-if="
              !showEditForm &&
              parentTabName &&
              parentTabName.toLowerCase() !== 'archived'
            "
            class="d-flex justify-end mt-2 mb-n6"
          >
            <v-tooltip
              :text="
                candidateDetails?.Hiring_Stage?.toLowerCase() === 'preboarding'
                  ? `Candidate details cannot be modified once preboarding has started`
                  : ''
              "
            >
              <template v-slot:activator="{ props }">
                <v-btn
                  v-if="
                    formAccess &&
                    formAccess.update &&
                    candidateDetails.Status_Id != 3
                  "
                  v-bind="
                    candidateDetails?.Hiring_Stage?.toLowerCase() ===
                    'preboarding'
                      ? props
                      : {}
                  "
                  @click="
                    candidateDetails?.Hiring_Stage?.toLowerCase() ===
                    'preboarding'
                      ? {}
                      : openEditForm()
                  "
                  class="primary"
                  variant="text"
                >
                  <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
                </v-btn>
              </template></v-tooltip
            >
            <v-icon
              size="14"
              class="mt-3 mr-4"
              color="grey"
              @click="retrieveJobCandidateDetails()"
              >fas fa-redo-alt</v-icon
            >
          </div>
          <v-card-text v-if="showEditForm" class="edit-form-section-height">
            <EditJobCandidateDetails
              ref="editJobCandidateDetails"
              :candidateDetails="candidateDetails"
              :candidateIdSelected="candidateIdSelected"
              @close-edit-form="closeEditForm()"
              @edit-updated="retrieveJobCandidateDetails($event, 'edit')"
              :showJobHeader="false"
              @close-candidate-details="closeViewForm()"
            ></EditJobCandidateDetails>
          </v-card-text>
          <v-card-text v-else class="view-form-section-height pa-4">
            <PersonalInfo :candidateDetails="candidateDetails" />
            <JobInfo :candidateDetails="candidateDetails" />
            <ExperienceDetails
              :candidateDetails="candidateDetails"
              :isEdit="false"
            ></ExperienceDetails>
            <ContactInfo :candidateDetails="candidateDetails" />
            <CareerInfo :isEdit="false" :candidateDetails="candidateDetails" />
            <JobCandidatesCustomFields
              :custom-form-name="formName"
              :form-id="jobPostId ? 311 : 16"
              :primary-id="candidateIdSelected ? candidateIdSelected : 0"
              :show-view-form="true"
              :show-edit-form="false"
            />
          </v-card-text>
        </div>
        <v-card-text
          v-if="activeTab === 'Feedback'"
          class="edit-form-section-height pa-4"
        >
          <Feedback
            :candidateIdSelected="candidateIdSelected"
            :candidateJobPostIdSelected="candidateJobPostIdSelected"
            :formAccess="formAccess"
            :candidateDetails="candidateDetails"
            :canScheduleInterview="canScheduleInterview"
            :isRecruiter="isRecruiter"
            :parentTabName="parentTabName"
          />
        </v-card-text>
        <v-card-text
          v-if="activeTab === 'Activity Log'"
          class="edit-form-section-height pa-4"
        >
          <ActivityLog :selectedJobCandidateId="candidateIdSelected" />
        </v-card-text>
      </div>
    </ProfileCard>
  </div>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="changeTab()"
  ></AppWarningModal>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import PersonalInfo from "./PersonalInfo.vue";
import JobInfo from "./JobInfo.vue";
import ExperienceDetails from "./experience-details/ExperienceDetails.vue";
import ContactInfo from "./ContactInfo.vue";
import CareerInfo from "./career/CareerInfo.vue";
import JobCandidateTopCard from "./JobCandidateTopCard.vue";
import Feedback from "./feedback/Feedback.vue";
import ActivityLog from "./activity-log/ActivityLog.vue";
import { RETRIEVE_JOB_CANDIDATE_DETAILS } from "@/graphql/recruitment/recruitmentQueries.js";
const EditJobCandidateDetails = defineAsyncComponent(() =>
  import("./EditJobCandidateDetails.vue")
);
const JobCandidatesCustomFields = defineAsyncComponent(() =>
  import("./custom-fields/JobCandidatesCustomFields.vue")
);

export default defineComponent({
  name: "JobCandidateDetails",
  components: {
    PersonalInfo,
    JobInfo,
    ContactInfo,
    CareerInfo,
    JobCandidatesCustomFields,
    Feedback,
    JobCandidateTopCard,
    ExperienceDetails,
    EditJobCandidateDetails,
    ActivityLog,
  },
  props: {
    selectedJobCandidateId: {
      type: Number,
      default: 0,
    },
    selectedJobPostId: {
      type: Number,
      default: 0,
    },
    selectedItem: {
      type: Object,
      default: function () {
        return {};
      },
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    candidateList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    candidateChangeCount: {
      type: Number,
      default: 0,
    },
    canScheduleInterview: {
      type: Boolean,
      default: true,
    },
    isRecruiter: {
      type: String,
      default: "No",
    },
    parentTabName: {
      type: String,
      default: "",
    },
  },
  emits: ["retrieve-error", "close-view-form", "on-change-candidate"],
  data() {
    return {
      activeTab: "Profile",
      candidateIdSelected: 0,
      candidateJobPostIdSelected: 0,
      changedTab: "",
      candidateDetails: {},
      showScrollButton: false,
      isLoading: false,
      showEditForm: false,
      refetchCount: 0,
      openWarningModal: false,
    };
  },
  computed: {
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formName() {
      let formName = this.jobPostId
        ? this.accessIdRights("311")
        : this.accessIdRights("16");
      if (formName?.customFormName && formName.customFormName !== "") {
        return [formName.customFormName];
      } else return ["Job Candidates"];
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    viewFormTabs() {
      return [
        {
          label: "Profile",
          value: "Profile",
          disable: false,
        },
        {
          label: "Feedback",
          value: "Feedback",
          disable: false,
        },
        {
          label: "Activity Log",
          value: "Activity Log",
          disable: false,
        },
      ];
    },
  },
  watch: {
    candidateChangeCount(count) {
      if (count > 0) {
        this.candidateIdSelected = this.selectedJobCandidateId;
        this.candidateJobPostIdSelected = this.selectedJobPostId;
        if (this.candidateIdSelected) {
          this.retrieveJobCandidateDetails();
          this.enableAllTabs();
        }
      }
    },
  },
  mounted() {
    this.candidateIdSelected = this.selectedJobCandidateId;
    this.candidateJobPostIdSelected = this.selectedJobPostId;
    if (this.candidateIdSelected) {
      this.retrieveJobCandidateDetails();
      this.enableAllTabs();
    } else {
      this.showEditForm = true;
    }
  },
  methods: {
    closeViewForm() {
      if (this.$route.query?.candidateId) {
        this.refetchCount = 1;
      }
      this.$emit("close-view-form", this.refetchCount);
    },
    isActiveTab(tab) {
      return this.activeTab === tab.value;
    },
    enableAllTabs() {
      this.viewFormTabs = this.viewFormTabs.map((item) => {
        item["disable"] = false;
        return item;
      });
    },
    onChangeViewFormTabs(val) {
      if (this.showEditForm) {
        this.changedTab = val;
        this.openWarningModal = true;
      } else {
        this.activeTab = val;
      }
    },
    onCloseWarningModal() {
      this.changedTab = "";
      this.openWarningModal = false;
    },
    changeTab() {
      this.activeTab = this.changedTab;
      this.showEditForm = false;
      this.openWarningModal = false;
    },
    openEditForm() {
      this.showEditForm = true;
    },
    closeEditForm() {
      if (!this.candidateIdSelected) {
        this.$emit("close-view-form", this.refetchCount);
      } else {
        this.showEditForm = false;
      }
    },
    retrieveJobCandidateDetails(candidateId = "", type) {
      if (candidateId) {
        this.candidateIdSelected = candidateId;
        this.enableAllTabs();
      }
      if (type == "edit") {
        this.refetchCount += 1;
      }
      let vm = this;
      vm.isLoading = true;
      vm.showEditForm = false;
      vm.$apollo
        .query({
          query: RETRIEVE_JOB_CANDIDATE_DETAILS,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            employeeId: parseInt(vm.loginEmployeeId),
            candidateId: parseInt(vm.candidateIdSelected),
            action: "view",
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveJobCandidates &&
            !response.data.retrieveJobCandidates.errorCode
          ) {
            vm.candidateDetails =
              response.data.retrieveJobCandidates.jobCandidateDetails;
            if (!vm.candidateIdSelected && vm.candidateDetails) {
              vm.candidateIdSelected = vm.candidateDetails.Candidate_Id
                ? vm.candidateDetails.Candidate_Id
                : 0;
            }
            if (!vm.candidateJobPostIdSelected && vm.candidateDetails) {
              vm.candidateJobPostIdSelected = vm.candidateDetails.Job_Post_Id
                ? vm.candidateDetails.Job_Post_Id
                : 0;
            }
            vm.isLoading = false;
          } else {
            vm.handleRetrieveError();
          }
        })
        .catch((err) => {
          vm.handleRetrieveError(err);
        });
    },

    handleRetrieveError() {
      this.isLoading = false;
      var snackbarData = {
        isOpen: true,
        type: "warning",
        message: "Something went wrong while retrieving job candidate details",
      };
      this.showAlert(snackbarData);
      this.$emit("retrieve-error", this.refetchCount);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style scoped>
.scroll-to-bottom-button {
  position: fixed;
  bottom: 5%;
  right: 15%;
  cursor: pointer;
}
.view-form-section-height {
  height: calc(100vh - 230px);
  overflow: hidden;
  overflow-y: scroll;
}

.edit-form-section-height {
  height: calc(100vh - 210px);
  overflow: hidden;
  overflow-y: scroll;
}
</style>
