<template>
  <v-dialog v-model="dialogVisible" persistent max-width="1000px">
    <v-card class="rounded-lg">
      <div class="d-flex justify-end pa-1">
        <v-icon
          color="secondary"
          class="pr-4 pt-4 font-weight-bold"
          @click="closeModal()"
        >
          fas fa-times
        </v-icon>
      </div>
      <v-card-title class="mt-n6 text-center">
        <v-icon size="15" class="mr-3" style="color: #ff247b"
          >far fa-circle</v-icon
        >
        <span>Download Tax Relief Template</span>
      </v-card-title>
      <v-card-text>
        <v-form ref="forms">
          <v-container>
            <v-row>
              <v-col cols="12">
                <CustomSelect
                  :items="employeeList"
                  label="Employee"
                  :itemSelected="employeeSelected"
                  itemValue="Employee_Id"
                  itemTitle="Employee_Id_Name"
                  :isRequired="true"
                  @selected-item="onChangeCustomSelectField($event, 'Employee')"
                  :rules="[required('Employee', employeeSelected.length)]"
                  :isAutoComplete="true"
                  :noDataText="'No data available'"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                ></CustomSelect>
              </v-col>
              <v-col cols="6">
                <CustomSelect
                  :items="categoryList"
                  label="Tax Relief Category"
                  :itemSelected="categorySelected"
                  itemValue="Tax_Relief_Category_Id"
                  itemTitle="Tax_Relief_Title"
                  :isRequired="true"
                  @selected-item="onChangeCustomSelectField($event, 'Category')"
                  :rules="[
                    required('Tax Relief Category', categorySelected.length),
                  ]"
                  :isAutoComplete="true"
                  :noDataText="'No data available'"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                ></CustomSelect>
              </v-col>
              <v-col cols="3">
                <CustomSelect
                  :items="monthYearListObj"
                  label="Start Period"
                  :itemSelected="startPeriod"
                  :isRequired="true"
                  :rules="[
                    required('Start Period', startPeriod),
                    validatePeriod(startPeriod, endPeriod, 'startPeriod'),
                  ]"
                  itemValue="monthYear"
                  itemTitle="monthYear"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Start Period')
                  "
                ></CustomSelect>
              </v-col>
              <v-col cols="3">
                <CustomSelect
                  :items="monthYearListObj"
                  label="End Period"
                  :itemSelected="endPeriod"
                  :rules="[
                    required('End Period', endPeriod),
                    validatePeriod(startPeriod, endPeriod, 'endPeriod'),
                  ]"
                  :isRequired="true"
                  itemValue="monthYear"
                  itemTitle="monthYear"
                  @selected-item="
                    onChangeCustomSelectField($event, 'End Period')
                  "
                ></CustomSelect>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>
      <v-card-actions class="d-flex justify-center">
        <v-btn
          variant="flat"
          rounded="lg"
          color="secondary"
          class="pa-3"
          @click="validateExportData()"
        >
          <v-icon class="pr-2 pb-1">fas fa-file-export</v-icon>
          Download
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import moment from "moment";

//Mixins
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";

export default {
  name: "ExportTaxRelief",

  mixins: [FileExportMixin, validationRules],

  components: {
    CustomSelect,
  },

  props: {
    employeeList: {
      type: Array,
      required: true,
    },
    categoryList: {
      type: Array,
      required: true,
    },
    openDialog: {
      type: Boolean,
      default: false,
    },
    months: {
      type: Array,
      required: true,
    },
    monthYearListObj: {
      type: Array,
      required: true,
    },
  },

  watch: {
    openDialog(val) {
      this.dialogVisible = val;
    },
  },

  data() {
    return {
      dialogVisible: false,
      employeeSelected: [],
      categorySelected: [],
      startPeriod: "",
      endPeriod: "",
      exportListData: [],
    };
  },

  methods: {
    closeModal() {
      this.employeeSelected = [];
      this.categorySelected = [];
      this.startPeriod = "";
      this.endPeriod = "";
      this.exportListData = [];
      this.$emit("close-modal");
    },
    validatePeriod(startPeriod, endPeriod, type) {
      const format = "MMM - YYYY";

      const parsedStartPeriod = moment(startPeriod, format, true);
      const parsedEndPeriod = moment(endPeriod, format, true);

      if (parsedStartPeriod.isAfter(parsedEndPeriod)) {
        if (type === "startPeriod") {
          return "Start Period cannot be greater than the end period";
        } else {
          return "End Period cannot be less than the start period";
        }
      }
      return true;
    },

    onChangeCustomSelectField(val, type) {
      if (type === "Employee") {
        if (val.includes("All")) {
          this.employeeSelected = ["All"];
        } else {
          this.employeeSelected = val;
        }
      } else if (type === "Start Period") {
        this.startPeriod = val;
      } else if (type === "End Period") {
        this.endPeriod = val;
      } else {
        if (val.includes("All")) {
          this.categorySelected = ["All"];
        } else {
          this.categorySelected = val;
        }
      }
    },
    async validateExportData() {
      const { valid } = await this.$refs.forms.validate();
      if (valid) {
        this.formExportData();
      }
    },
    formExportData() {
      if (this.employeeSelected.includes("All")) {
        this.employeeSelected = this.employeeList.map((el) => el.Employee_Id);
      }
      this.employeeSelected = this.employeeSelected.filter(
        (el) => el !== undefined
      );
      this.exportListData = this.employeeSelected.map((empId) => {
        let employee = this.employeeList.find((d) => d.Employee_Id === empId);
        return {
          Employee_Id: employee.Employee_Id,
          Employee_Name: employee.Employee_Name,
          Start_Period: this.startPeriod,
          End_Period: this.endPeriod,
          Amount: 0,
        };
      });
      this.exportData();
    },
    exportData() {
      let updatedData = [];
      this.exportListData.forEach((record) => {
        const startDate = moment(record.Start_Period, "MMM YYYY");
        const endDate = moment(record.End_Period, "MMM YYYY");

        while (startDate.isSameOrBefore(endDate, "month")) {
          const transformedRecord = {
            Employee_Id: record.Employee_Id,
            Employee_Name: record.Employee_Name,
            Month: startDate.format("MMM - YYYY"),
            Amount: record.Amount,
          };

          updatedData.push(transformedRecord);

          startDate.add(1, "month");
        }
      });

      let employeeWithTaxRelief = [];

      if (this.categorySelected.includes("All")) {
        this.categorySelected = this.categoryList.map(
          (el) => el.Tax_Relief_Category_Id
        );
      }

      this.categorySelected = this.categorySelected.filter(
        (el) => el !== undefined
      );

      for (let emp of updatedData) {
        for (let categoryId of this.categorySelected) {
          let category = this.categoryList.find(
            (cat) => cat.Tax_Relief_Category_Id === categoryId
          );

          if (category) {
            employeeWithTaxRelief.push({
              Employee_Id: emp.Employee_Id,
              Employee_Name: emp.Employee_Name,
              Month: emp.Month,
              Amount: emp.Amount,
              Tax_Relief_Category: category.Tax_Relief_Title,
            });
          }
        }
      }
      let headers = [
        { key: "Employee_Id", header: "Employee Id" },
        { key: "Employee_Name", header: "Employee Name" },
        { key: "Tax_Relief_Category", header: "Tax Relief Category" },
        { key: "Month", header: "Month" },
        { key: "Amount", header: "Amount" },
      ];

      let exportOptions = {
        fileExportData: employeeWithTaxRelief,
        fileName: "Import Tax Relief",
        sheetName: "Import Tax Relief",
        header: headers,
        columnHighlightProps: {
          type: "Tax Relief",
          months: this.monthYearListObj.map((el) => el.monthYear),
          category: this.categoryList
            .map((el) => el.Tax_Relief_Title)
            .filter((category) => category !== undefined),
        },
      };
      this.exportExcelFile(exportOptions);
      this.closeModal();
    },
  },
};
</script>
