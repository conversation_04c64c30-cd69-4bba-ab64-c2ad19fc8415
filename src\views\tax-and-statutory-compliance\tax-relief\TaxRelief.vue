<template>
  <div>
    <AppTopBarTab :tabs-list="mainTabs" :showBottomSheet="!isBottomSheetOpened">
      <template #topBarContent>
        <v-row v-show="!listLoading">
          <v-col
            cols="12"
            class="d-flex"
            :class="taxReliefListBackup.length > 0 ? '' : 'justify-end'"
            :style="isMobileView ? '' : 'margin-left: -108px'"
          >
            <EmployeeDefaultFilterMenu
              v-if="taxReliefListBackup.length > 0"
              class="justify-end"
              :reset-filter-count="resetFilterCount"
              :list-items="taxReliefListBackup"
              :isApplyFilter="true"
              departmentIdKey="Department_Id"
              designationIdKey="Designation_Id"
              locationIdKey="Location_Id"
              empTypeIdKey="EmpType_Id"
              workScheduleIdKey="Work_Schedule"
              @reset-emp-filter="resetFilter('filter')"
              @applied-filter="applyFilter($event)"
              :class="isMobileView ? 'mr-4' : 'ml-1'"
            >
              <template #bottom-filter-menu>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedStatus"
                    color="secondary"
                    :items="statusList"
                    label="Status"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                  >
                  </v-select>
                </v-col>
                <v-col
                  v-if="reliefCategoryList.length > 0"
                  :cols="windowWidth > 600 ? 6 : 12"
                  class="py-2"
                >
                  <v-autocomplete
                    v-model="selectedReliefCategory"
                    color="secondary"
                    :items="reliefCategoryList"
                    label="Relief Category"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                    itemValue="Tax_Relief_Category_Id"
                    itemTitle="Tax_Relief_Title"
                    :loading="reliefCategoryListLoading"
                  >
                  </v-autocomplete>
                </v-col>
              </template>
            </EmployeeDefaultFilterMenu>
            <div class="font-weight-medium text-primary mt-2">
              <AppButtonWithDropdown
                :list-items="assessmentYearList"
                :button-value="assessmentYear"
                :is-override-button="windowWidth < 1264 ? true : false"
                background-color="primary"
                :class="isMobileView ? 'ml-4 mt-n2' : 'ml-1'"
                itemLabel="Assessment Year"
                @selected-value="applyYearFilter($event)"
              ></AppButtonWithDropdown>
            </div>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-container v-else fluid class="tax-relief-container">
      <section>
        <v-window v-model="currentTabItem">
          <v-window-item value="tab-0">
            <div v-if="listLoading" class="mt-3">
              <v-skeleton-loader
                ref="skeleton1"
                type="table-heading"
                class="mx-auto"
              ></v-skeleton-loader>
              <div v-for="i in 3" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item-avatar"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </div>
            <div v-else-if="isErrorInList">
              <AppFetchErrorScreen
                image-name="common/common-error-image"
                :content="errorContent"
                icon-name="fas fa-redo-alt"
                button-text="Retry"
                :isSmallImage="true"
                @button-click="refetchAPIs()"
              >
              </AppFetchErrorScreen>
            </div>
            <v-row v-else>
              <v-col cols="12">
                <ListTaxRelief
                  :consolidated-list="taxReliefList"
                  :originalList="taxReliefListBackup"
                  :selectedStatus="selectedStatus"
                  :assessmentYear="assessmentYear"
                  :statusList="statusList"
                  :display-import="displayImport"
                  :fiscalStartMonth="finStartMonth"
                  @refetch-list="refetchAPIs()"
                  @close-bulk-import="closeBulkImport"
                  @reset-filter="resetFilter('grid')"
                  @add-edit-updated="refetchAPIs()"
                  @is-bottom-sheet-opened="isBottomSheetOpened = $event"
                ></ListTaxRelief>
              </v-col>
            </v-row>
          </v-window-item>
        </v-window>
      </section>
    </v-container>
  </div>
</template>

<script>
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import { defineComponent } from "vue";
import {
  LIST_TAX_RELIEF_DECLARATIONS,
  GET_ASSESSMENT_YEARS,
  LIST_TAX_RELIEF_CATEGORIES,
} from "@/graphql/tax-and-statutory-compliance/taxRelief.js";
import ListTaxRelief from "./ListTaxRelief.vue";
import moment from "moment";

export default defineComponent({
  name: "TaxRelief",

  components: {
    EmployeeDefaultFilterMenu,
    ListTaxRelief,
  },

  data() {
    return {
      currentTabItem: "tab-0",
      isBottomSheetOpened: false,
      // list
      taxReliefList: [],
      taxReliefListBackup: [],
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      isLoading: false,
      // filter
      selectedStatus: [],
      statusList: [
        "Declared",
        "Applied",
        "Approved",
        "Returned",
        "Reopened",
        "Rejected",
      ],
      resetFilterCount: 0,
      assessmentYear: "",
      assessmentYearListWinFinStartMonth: [],
      reliefCategoryListLoading: false,
      reliefCategoryList: [],
      selectedReliefCategory: [],
      displayImport: false,
    };
  },

  computed: {
    mainTabs() {
      return ["Tax Relief"];
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    assessmentYearList() {
      let year = [];
      this.assessmentYearListWinFinStartMonth.forEach((item) => {
        year.push(item.Year);
      });
      return year;
    },
    finStartMonth() {
      let filteredArray = this.assessmentYearListWinFinStartMonth.filter(
        (item) => {
          return item.Year == this.assessmentYear;
        }
      );
      let finStartMon =
        filteredArray && filteredArray.length > 0
          ? filteredArray[0].finStartMonth
          : "January";
      return finStartMon;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("Tax Relief Error:", err, vm, info);
    let msg =
      "Something went wrong while loading the tax-relief form. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    if (vm.name != "bottom-navigation") this.showAlert(snackbarData);
    return false;
  },

  mounted() {
    let status = this.$route.query.status;
    if (status === "bulk") {
      this.displayImport = true;
    }
    status = status && status != "bulk" ? [status] : [];
    var assessMentYear = this.$route.query.assessMentYear;
    let urlData = this.$route.query.data;
    if (urlData && !assessMentYear) {
      status = ["Declared", "Applied", "Reopened", "Returned"];
      let encodedData = atob(urlData);
      assessMentYear = encodedData.split("=");
      assessMentYear = assessMentYear[1];
    }
    this.selectedStatus = status;
    this.assessmentYear = assessMentYear ? assessMentYear : moment().year();
    this.getAssessMentYears();
  },

  methods: {
    closeBulkImport() {
      this.displayImport = false;
    },
    refetchAPIs() {
      this.isErrorInList = false;
      this.retrieveReliefCategories();
      this.getTaxReliefDetails();
    },

    resetFilter(calledFrom) {
      if (calledFrom === "grid") {
        this.resetFilterCount += 1;
      }
      this.selectedStatus = [];
      this.selectedReliefCategory = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.taxReliefList = this.taxReliefListBackup;
    },

    applyYearFilter(year) {
      this.assessmentYear = year;
      this.refetchAPIs();
    },

    applyFilter(filteredArray) {
      let filteredList = filteredArray;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return item.taxReliefDeclarations.some((tax) =>
            this.selectedStatus.includes(tax.Approval_Status)
          );
        });
      }
      if (this.selectedReliefCategory.length > 0) {
        filteredList = filteredList.filter((item) => {
          return item.taxReliefDeclarations.some((tax) =>
            this.selectedReliefCategory.includes(tax.Tax_Relief_Category_Id)
          );
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.taxReliefList = filteredList;
    },

    retrieveReliefCategories() {
      let vm = this;
      vm.reliefCategoryListLoading = true;
      vm.$apollo
        .query({
          query: LIST_TAX_RELIEF_CATEGORIES,
          client: "apolloClientAI",
          variables: {
            assessmentYear: vm.assessmentYear.toString(),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listTaxReliefCategories &&
            !response.data.listTaxReliefCategories.errorCode
          ) {
            const { taxReliefCategories } =
              response.data.listTaxReliefCategories;
            vm.reliefCategoryList = taxReliefCategories
              ? JSON.parse(taxReliefCategories)
              : [];
          }
          vm.reliefCategoryListLoading = false;
        })
        .catch(() => {
          vm.reliefCategoryListLoading = false;
        });
    },

    getAssessMentYears() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_ASSESSMENT_YEARS,
          client: "apolloClientAJ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getHousePropertyStaticData
          ) {
            let { housePropertyStaticData } =
              response.data.getHousePropertyStaticData;
            housePropertyStaticData = JSON.parse(housePropertyStaticData);
            vm.assessmentYearListWinFinStartMonth =
              housePropertyStaticData.minAndMaxAssessmentYear;
            vm.isLoading = false;
            vm.refetchAPIs();
          } else {
            vm.assessmentYearListWinFinStartMonth = [
              { Year: moment().year(), finStartMonth: "January" },
            ];
            vm.isLoading = false;
            vm.refetchAPIs();
          }
        })
        .catch(() => {
          vm.assessmentYearListWinFinStartMonth = [
            { Year: moment().year(), finStartMonth: "January" },
          ];
          vm.isLoading = false;
          vm.refetchAPIs();
        });
    },
    getTaxReliefDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_TAX_RELIEF_DECLARATIONS,
          client: "apolloClientAI",
          variables: {
            assessmentYear: vm.assessmentYear.toString(),
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listTaxReliefDeclarations
          ) {
            let { taxReliefDeclaration } =
              response.data.listTaxReliefDeclarations;
            taxReliefDeclaration = JSON.parse(taxReliefDeclaration);
            // Your original array
            const array = taxReliefDeclaration;
            let resultArray = [];
            // Iterate through the original array to form expanded and consoldiated array
            array.forEach((employee) => {
              // Initialize an object to store the total amounts for each month
              const totalAmounts = {
                Mon_Jan: 0,
                Mon_Feb: 0,
                Mon_Mar: 0,
                Mon_Apr: 0,
                Mon_May: 0,
                Mon_Jun: 0,
                Mon_Jul: 0,
                Mon_Aug: 0,
                Mon_Sep: 0,
                Mon_Oct: 0,
                Mon_Nov: 0,
                Mon_Dec: 0,
              };
              // push total amount for each month for each employee
              let totalAmtObj = {};
              if (employee.taxReliefDeclarations.length > 0) {
                employee.taxReliefDeclarations.forEach((relief) => {
                  for (const month in totalAmounts) {
                    totalAmtObj[`Total_${month}`] = !totalAmtObj[
                      `Total_${month}`
                    ]
                      ? 0
                      : totalAmtObj[`Total_${month}`];
                    if (relief.Approval_Status != "Rejected") {
                      totalAmtObj[`Total_${month}`] += relief[month];
                    }
                  }
                });
              } else {
                for (const month in totalAmounts) {
                  totalAmtObj[`Total_${month}`] = 0;
                }
              }
              resultArray.push({
                ...employee,
                isExpand: false,
                ...totalAmtObj,
              });
            });
            vm.taxReliefList = resultArray;
            vm.taxReliefListBackup = resultArray;
            vm.applyFilter(vm.taxReliefList);
          } else {
            vm.handleListError();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "tax relief",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style>
.tax-relief-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .tax-relief-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
