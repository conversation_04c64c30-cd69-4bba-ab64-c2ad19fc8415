<template>
  <div ref="experienceDetails">
    <div class="d-flex">
      <div class="d-flex align-center">
        <v-progress-circular
          v-if="!isEdit"
          model-value="100"
          color="primary"
          :size="18"
          class="mr-1"
        ></v-progress-circular>
        <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
          >Experience Details</span
        >
      </div>
    </div>
    <div v-if="showAddEditExperienceForm">
      <AddEditExperienceDetails
        ref="experienceAddEdit"
        :experienceDetails="experienceDetails"
        :dateFormat="dateFormat"
        :selectedCandidateDOB="selectedCandidateDOB"
        @update-experience-details="updateExperienceDetails"
      >
      </AddEditExperienceDetails>
    </div>
    <div v-else>
      <div v-if="!isMobileView" class="d-flex mt-4 pb-4">
        <v-slide-group
          selected-class="bg-secondary"
          prev-icon="fas fa-chevron-circle-left"
          next-icon="fas fa-chevron-circle-right"
          show-arrows
        >
          <v-slide-group-item>
            <ViewExperienceDetails :experienceDetails="experienceDetails" />
          </v-slide-group-item>
        </v-slide-group>
      </div>
      <div v-else>
        <div class="card-container">
          <ViewExperienceDetails
            :experienceDetails="experienceDetails"
          ></ViewExperienceDetails>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
// components
const AddEditExperienceDetails = defineAsyncComponent(() =>
  import("./AddEditExperienceDetails.vue")
);
const ViewExperienceDetails = defineAsyncComponent(() =>
  import("./ViewExperienceDetails.vue")
);

export default {
  name: "ExperienceDetails",
  components: {
    AddEditExperienceDetails,
    ViewExperienceDetails,
  },
  emits: ["update-experience-details"],
  props: {
    candidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      required: true,
    },
    dateFormat: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      experienceDetails: [],
      showAddEditExperienceForm: false,
      experienceInfo: {},
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    selectedCandidateDOB() {
      if (this.candidateDetails && this.candidateDetails.DOB) {
        return this.candidateDetails.DOB;
      }
      return null;
    },
  },

  mounted() {
    this.experienceDetails = this.candidateDetails.Candidate_Experience
      ? this.candidateDetails.Candidate_Experience
      : [];
    if (this.isEdit) {
      this.showAddEditExperienceForm = true;
    }
  },

  methods: {
    async validateAddEditForm() {
      if (this.$refs.experienceAddEdit) {
        let addEditRes =
          await this.$refs.experienceAddEdit.validateExperienceDetails();
        return addEditRes;
      } else {
        return true;
      }
    },
    updateExperienceDetails(data, from) {
      let updatedData = { ...this.experienceInfo };
      if (from?.toLowerCase() === "experience") {
        updatedData["Candidate_Experience"] = [...data];
      }
      this.experienceInfo = updatedData;
      this.$nextTick(() => {
        this.$emit(
          "update-experience-details",
          JSON.parse(JSON.stringify(updatedData)),
          "experience"
        );
      });
    },
  },
};
</script>

<style scoped>
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  /* The grid-gap property adds a 10-pixel gap between the cards. */
  grid-gap: 10px;
}
@media (max-width: 600px) {
  .card-container {
    grid-template-columns: 1fr;
  }
}
</style>
