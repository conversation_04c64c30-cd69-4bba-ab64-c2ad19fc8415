<template>
  <v-row>
    <v-col cols="7">
      <div
        :class="
          isMobileView
            ? 'd-flex flex-column justify-center mx-auto ma-2'
            : 'ma-2'
        "
      >
        <div v-if="callingFromRecruitment" class="my-1">
          <span
            class="text-primary text-h6 font-weight-bold"
            style="white-space: normal; word-break: break-word"
            >{{ employeeName }}</span
          >
        </div>
        <div v-else-if="isMobileView && employeeName" class="my-1 ml-3">
          <span class="text-primary text-h6 font-weight-bold">{{
            employeeName
          }}</span>
        </div>
        <div
          class="d-flex align-center my-2"
          :class="isMobileView ? 'ml-3' : ''"
        >
          <v-icon color="blue" size="17">fas fa-envelope</v-icon>
          <span class="ml-2 text-wrap">{{
            empEmailAddress ? empEmailAddress : "-"
          }}</span>
        </div>

        <div
          class="d-flex align-center my-2"
          :class="isMobileView ? 'ml-3' : ''"
          :style="isMobileView ? 'margin-bottom:65px !important;' : ''"
        >
          <v-icon color="green" size="17">fas fa-phone</v-icon>
          <div class="ml-2 d-flex">
            <span v-if="empMobileNoCode" class="pr-1">{{
              empMobileNoCode
            }}</span>
            {{ empMobileNo ? empMobileNo : "-" }}
          </div>
        </div>

        <div v-if="!isMobileView" class="my-2">
          <v-icon color="red" size="17">fas fa-map-marker-alt</v-icon>
          <span class="ml-2 text-wrap">{{ address ? address : "-" }}</span>
        </div>

        <div v-if="!isMobileView && invitationStatus" class="my-2">
          <v-icon color="purple" size="17">fas fa-paper-plane</v-icon>
          <span class="ml-2"
            ><b>Invitation Status:</b>
            {{ invitationStatus ? invitationStatus : "-" }}</span
          >
        </div>
      </div>
    </v-col>
    <v-col cols="4">
      <v-btn
        v-if="displayChangeButton"
        class="mt-3"
        color="primary"
        rounded="lg"
        variant="outlined"
        size="small"
        @click="$emit('on-display-change-button')"
      >
        <v-icon color="primary" class="mr-1">fas fa-exchange-alt</v-icon>
        {{ displayChangeButtonName }}
      </v-btn>
    </v-col>
  </v-row>
</template>

<script>
export default {
  name: "ContactDetails",
  props: {
    displayChangeButtonName: {
      type: String,
      default: "Change",
    },
    displayChangeButton: {
      type: Boolean,
      default: false,
    },
    employeeName: {
      type: String,
      default: "",
    },
    empEmailAddress: {
      type: String,
      default: "",
    },
    empMobileNo: {
      type: String,
      default: "",
    },
    empMobileNoCode: {
      type: String,
      default: "",
    },
    address: {
      type: String,
      default: "",
    },
    invitationStatus: {
      type: String,
      default: "",
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
    callingFromRecruitment: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
};
</script>
