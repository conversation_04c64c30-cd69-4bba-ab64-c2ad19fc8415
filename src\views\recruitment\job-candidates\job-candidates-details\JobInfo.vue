<template>
  <div class="d-flex align-center justify-space-between">
    <div class="d-flex align-center">
      <v-progress-circular
        model-value="100"
        color="green"
        :size="18"
        class="mr-1"
      ></v-progress-circular>
      <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
        >Job Details</span
      >
      <span
        class="text-subtitle-1 text-orange-darken-1 font-weight-bold ml-2"
        v-if="candidateDetails?.Sponsored === 1"
        >(Sponsored)</span
      >
    </div>
  </div>
  <v-row class="pa-4 ma-2 card-blue-background">
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Job Title</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Job_Post_Name) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[290].Field_Visiblity == 'No'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[290].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{
          candidateDetails.Preferred_Location &&
          candidateDetails.Preferred_Location.length > 0
            ? formPreferredLocation(candidateDetails.Preferred_Location)
            : "-"
        }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Skill Set</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ splitSkills(candidateDetails.Skill_Set) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Current Employer</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Current_Employer) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">
        Availability to join (In days)
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{
          candidateDetails.Notice_Period ? candidateDetails.Notice_Period : 0
        }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Total Experience</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{
          candidateDetails.Total_Experience_In_Years
            ? candidateDetails.Total_Experience_In_Years
            : 0
        }}
        Years
        {{
          candidateDetails.Total_Experience_In_Months
            ? candidateDetails.Total_Experience_In_Months
            : 0
        }}
        Months
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Current Basic Salary</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ candidateDetails.Current_CTC ? candidateDetails.Current_CTC : 0 }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Expected Basic Salary</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ candidateDetails.Expected_CTC ? candidateDetails.Expected_CTC : 0 }}
      </p>
    </v-col>
    <v-col
      cols="12"
      md="4"
      sm="6"
      v-if="candidateDetails?.Source?.toLowerCase() === 'seek'"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        Expected Salary (Captured from Seek)
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails?.Expected_CTC_Sourced_From_Seek) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[269].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[269].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Passport_No) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[289].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[289].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Currency_Name) }}
      </p>
    </v-col>
    <v-col
      cols="12"
      md="4"
      sm="6"
      v-if="labelList[322].Field_Visiblity == 'Yes'"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[322].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Verifier_Name) }}
      </p>
    </v-col>
    <v-col
      cols="12"
      md="4"
      sm="6"
      v-if="labelList[323].Field_Visiblity == 'Yes'"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[322].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Verifier_Phone_Number) }}
      </p>
    </v-col>
    <v-col
      cols="12"
      md="4"
      sm="6"
      v-if="labelList[324].Field_Visiblity == 'Yes'"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[324].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Verifier_Email_Id) }}
      </p>
    </v-col>
  </v-row>
</template>

<script>
import { checkNullValue } from "@/helper";

export default {
  name: "JobDetails",

  props: {
    candidateDetails: {
      type: [Object, Array],
      required: true,
    },
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  methods: {
    checkNullValue,
    splitSkills(skills) {
      if (skills?.length) return skills.join(", ");
      else return "-";
    },
    formPreferredLocation(locations) {
      let locationNames = [];
      for (var i = 0; i < locations.length; i++) {
        locationNames.push(locations[i].Location_Name);
      }
      return locationNames.join(", ");
    },
  },
};
</script>
