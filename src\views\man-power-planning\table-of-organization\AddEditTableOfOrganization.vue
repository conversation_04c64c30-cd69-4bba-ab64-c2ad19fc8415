<template>
  <v-overlay
    :model-value="showAddForm"
    @click:outside="onCloseEditForm()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card"
        :style="windowWidth >= 1264 ? 'width: 50vw' : 'width: 100vw'"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">
            {{ isEdit ? "Edit" : "Add" }}
            Table of Organization
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseEditForm()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <div
          :class="isMobileView ? 'pa-2' : 'pa-5'"
          style="height: calc(100vh - 100px); overflow: scroll"
        >
          <v-card-text>
            <v-form ref="orgForm">
              <v-row>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="position"
                    :label="'Position'"
                    variant="solo"
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="positionCode"
                    :label="'Position Code'"
                    variant="solo"
                    :disabled="
                      editFormData && editFormData.Pos_Code ? true : false
                    "
                    :rules="[required('Position Code', positionCode)]"
                  >
                    <template v-slot:label>
                      Position Code
                      <span
                        v-if="
                          editFormData && editFormData.Pos_Code ? false : true
                        "
                        class="text-red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="jobTitleCode"
                    :label="'Job Title Code'"
                    variant="solo"
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="costCenter"
                    :label="'Cost Center'"
                    variant="solo"
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="noOfPositions"
                    :rules="[
                      required('No. of Approved Positions', noOfPositions),
                      minMaxNumberValidation(
                        'No. of Approved Positions',
                        noOfPositions,
                        1,
                        500
                      ),
                    ]"
                    variant="solo"
                    type="number"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      No. of Approved Positions <span class="text-red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="warmBodies"
                    :label="'Warm Bodies'"
                    :rules="[
                      required('Warm Bodies', warmBodies),
                      minMaxNumberValidation(
                        'Warm Bodies',
                        warmBodies,
                        0,
                        noOfPositions,
                        '',
                        'No. of Approved Positions'
                      ),
                    ]"
                    variant="solo"
                    type="number"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      Warm Bodies <span class="text-red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="noToBeHired"
                    :label="'Approved Vacant Positions'"
                    variant="solo"
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="careerBand"
                    :label="'Career Band'"
                    variant="solo"
                    disabled
                  ></v-text-field>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <v-textarea
                    v-model="jobSummary"
                    rows="1"
                    auto-grow
                    :label="'Job Summary'"
                    variant="solo"
                    disabled
                  ></v-textarea>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <v-textarea
                    v-model="skills"
                    rows="1"
                    auto-grow
                    :label="'Skills and Competencies'"
                    variant="solo"
                    disabled
                  ></v-textarea>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <v-textarea
                    v-model="education"
                    rows="1"
                    auto-grow
                    :label="'education'"
                    variant="solo"
                    disabled
                  ></v-textarea>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <v-textarea
                    v-model="experience"
                    rows="1"
                    auto-grow
                    :label="'experience'"
                    variant="solo"
                    disabled
                  ></v-textarea>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <v-textarea
                    v-model="conditions"
                    rows="1"
                    auto-grow
                    :label="'Work Conditions'"
                    variant="solo"
                    disabled
                  ></v-textarea>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <v-textarea
                    v-model="internalRelation"
                    rows="1"
                    auto-grow
                    :label="'Operating Network (Internal)'"
                    variant="solo"
                    disabled
                  ></v-textarea>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <v-textarea
                    v-model="externalRelation"
                    rows="1"
                    auto-grow
                    :label="'Operating Network (External)'"
                    variant="solo"
                    disabled
                  ></v-textarea>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <v-textarea
                    v-model="requirements"
                    rows="1"
                    auto-grow
                    :label="'Minimum Requirements'"
                    variant="solo"
                    disabled
                  ></v-textarea>
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Job Description
                  </p>
                  <div id="add-editor">
                    <div
                      ref="editor"
                      class="quill-editor"
                      @keydown="isFormDirty = true"
                    ></div>
                    <p class="text-red">{{ this.instructionError }}</p>
                  </div>
                </v-col>
                <v-col
                  v-if="moreDetailsList && moreDetailsList.length > 0"
                  cols="12"
                >
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </div>
        <div class="card-actions-div">
          <v-card-actions class="d-flex align-end">
            <v-sheet class="align-center text-center" style="width: 100%">
              <v-row justify="center">
                <v-col cols="12" class="d-flex justify-end pr-6">
                  <v-btn
                    rounded="lg"
                    class="mr-6 primary"
                    @click="onCloseEditForm()"
                    variant="outlined"
                  >
                    Cancel
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    class="mr-1 primary"
                    @click="validateForm()"
                    variant="elevated"
                    :disabled="!isFormDirty"
                  >
                    Submit
                  </v-btn>
                </v-col>
              </v-row>
            </v-sheet>
          </v-card-actions>
        </div>
      </v-card>
    </template></v-overlay
  >
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  ></AppWarningModal>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
          >Close</v-btn
        >
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
import { UPDATE_TABLE_OF_ORGANIZATIONS } from "@/graphql/mpp/manPowerPlanningQueries";
import { RETRIEVE_POSITION_JOB_SUMMARY } from "@/graphql/settings/irukka-integration/jobPostFormQueries";

export default {
  name: "AddEditTableOfOrganization",
  components: { MoreDetails },
  mixins: [validationRules],
  emits: ["close-form", "edit-updated"],
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    showAddForm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      position: "",
      positionCode: "",
      jobTitleCode: "",
      costCenter: "",
      noOfPositions: 0,
      warmBodies: 0,
      openConfirmationPopup: false,
      isFormDirty: false,
      moreDetailsList: [],
      showValidationAlert: false,
      validationMessages: [],
      isLoading: false,
      instructionError: "",
      hasContent: false,
      showEditForm: false,
      careerBand: "",
      jobSummary: "",
      skills: "",
      education: "",
      experience: "",
      conditions: "",
      internalRelation: [],
      externalRelation: [],
      requirements: "",
    };
  },
  watch: {
    showAddForm(val) {
      this.showEditForm = val;
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    noToBeHired() {
      return this.noOfPositions - this.warmBodies > 0
        ? this.noOfPositions - this.warmBodies
        : 0;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.initQuillEditor();
    if (this.editFormData) {
      this.position = this.editFormData.Pos_Name;
      this.positionCode = this.editFormData.Pos_Code;
      this.jobTitleCode = this.editFormData.Job_Title_Code;
      this.costCenter = this.editFormData.Cost_Code;
      this.noOfPositions = this.editFormData.Approved_Position;
      this.warmBodies = this.editFormData.Warm_Bodies;
      this.jobDescription = this.editFormData.Job_Description;
      this.retrieveJobDescription(this.editFormData.Organization_Structure_Id);
    }
  },
  methods: {
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editor, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
        this.contentLength = this.quill.getLength();
        if (this.contentLength <= 1) {
          this.instructionError = "Description is required";
        } else if (this.contentLength <= 100) {
          this.instructionError = "Description minimum length 100";
        } else if (this.contentLength > 15000) {
          this.instructionError = "Description maximum length 15000";
        } else {
          this.instructionError = "";
        }
      });
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editor.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveJobDescription(positionId) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_POSITION_JOB_SUMMARY,
          variables: {
            originalPositionId: "" + positionId,
            formId: 288,
          },
          fetchPolicy: "no-cache",
          client: "apolloClientAG",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.retrievePositionJobSummary &&
            data.retrievePositionJobSummary.summary
          ) {
            let { summary } = data.retrievePositionJobSummary;
            this.quill.root.innerHTML = summary.Job_Description;
            vm.careerBand = summary.Career_Band;
            vm.jobSummary = summary.Job_Summary;
            vm.skills = summary.Skills_Competencies;
            vm.education = summary.Education;
            vm.experience = summary.Experience;
            vm.conditions = summary.Working_Conditions;
            vm.internalRelation = summary.Working_Relationship_Internal?.filter(
              (item) => item
            ).join(", ");
            vm.externalRelation = summary.Working_Relationship_External?.filter(
              (item) => item
            ).join(", ");
            vm.requirements = summary.Minimum_Requirements;
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },

    async validateForm() {
      let { valid } = await this.$refs.orgForm.validate();
      if (valid && this.hasContent) {
        if (this.contentLength <= 1) {
          this.descriptionErrorMsg = "Description is required";
        } else if (this.contentLength <= 15000 && this.contentLength >= 100) {
          this.descriptionErrorMsg = "";
          this.fillForm = false;
          this.updateTableOfOrganization();
        } else {
          this.hasContent = false;
        }
      }
    },

    updateTableOfOrganization() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_TABLE_OF_ORGANIZATIONS,
          variables: {
            formId: 288,
            jobDescription: this.quill.root.innerHTML,
            approvedPosition: parseInt(this.noOfPositions),
            warmBodies: parseInt(this.warmBodies),
            originalPositionId:
              "" + this.editFormData.Organization_Structure_Id,
            positionCode: this.positionCode,
          },
          client: "apolloClientAH",
        })
        .then((response) => {
          if (response && response.data && !response.data.errorCode) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Position updated successfully.",
            };
            vm.isLoading = false;
            vm.showAlert(snackbarData);
            vm.$emit("edit-updated");
          }
        })
        .catch((error) => {
          this.handleUpdateError(error);
        });
    },
    handleUpdateError(error) {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: error,
          action: "updating",
          form: "Leave Override",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
  },
};
</script>
<style>
.quill-editor {
  height: 200px;
}
.overlay-card {
  height: 100vh;
  width: 50vw;
  overflow-y: auto;
}
#add-editor > .ql-toolbar {
  display: block !important;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
