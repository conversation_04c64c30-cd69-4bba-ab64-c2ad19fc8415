<template>
  <v-card v-if="!isListError" class="ma-5 pa-4">
    <div class="d-flex align-center">
      <v-progress-circular
        v-if="!isEdit"
        model-value="100"
        color="blue-grey"
        :size="18"
        class="mr-1"
      ></v-progress-circular>
      <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold">
        Job Post Details
      </span>
    </div>

    <v-card-text>
      <v-row class="mt-1">
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Job Title</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.jobTitle) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Job Requisition Id</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ orgCode ? orgCode + "-" : ""
            }}{{ checkNullValue(parseInt(this.jobPostIdQueryParam)) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Work Schedule</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.paymentType) }}
          </p>
        </v-col>
        <v-col v-if="fieldForce" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Service Provider</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.serviceProviderName) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Functional Area</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.functionalArea) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Currency</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.currency) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Designation</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.designation) }}
          </p>
        </v-col>
        <v-col
          v-if="jobPostDetails.minSalary && jobPostDetails.maxSalary"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Salary Range</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ jobPostDetails.minSalary }} -
            {{ checkNullValue(jobPostDetails.maxSalary) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Job Type</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.jobType) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Expected Qualification
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            <span class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(jobPostDetails.expectedQualification) }}
            </span>
          </p>
        </v-col>
        <v-col v-if="jobPostDetails.jobDuration" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Job Duration</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.jobDuration) }}
            months
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Key Skills</p>
          <span class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.keySkillSet) }}
          </span>
        </v-col>
        <v-col
          v-if="
            jobPostDetails.minExperienceRange && jobPostDetails.maxExperience
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">Experience Range</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ jobPostDetails.minExperienceRange }} -
            {{ checkNullValue(jobPostDetails.maxExperience) }} years(s)
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Rounds</p>
          <span class="text-subtitle-1 font-weight-regular">
            {{ jobPostDetails.rounds }}
          </span>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Posting Date</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(formatDate(jobPostDetails.postingDate)) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Closing Date</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(formatDate(jobPostDetails.closingDate)) }}
          </p>
        </v-col>
        <v-col
          v-if="jobPostDetails.expectedJoiningDate != '1970/01/01'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            Expected Joining Date
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(formatDate(jobPostDetails.expectedJoiningDate)) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Job Location</p>
          <span class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.locations) }}
          </span>
        </v-col>
        <v-col v-if="jobPostDetails.address" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Company Address</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.address) }}
          </p>
        </v-col>
        <v-col v-if="jobPostDetails.coolingPeriod" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Cooling Period</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.coolingPeriod) }}
            month(s)
          </p>
        </v-col>

        <v-col v-if="jobPostDetails.isTravelRequired" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Travel Required</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              jobPostDetails.isTravelRequired
                ? jobPostDetails.isTravelRequired == 1
                  ? "Yes"
                  : "No"
                : "-"
            }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">No of Vacancies</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.noOfVacancies) }}
          </p>
        </v-col>
        <v-col v-if="jobPostDetails.noOfMaleVacancies" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">No of Male Vacancies</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.noOfMaleVacancies) }}
          </p>
        </v-col>
        <v-col
          v-if="jobPostDetails.noOfFemaleVacancies"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            No of Female Vacancies
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.noOfFemaleVacancies) }}
          </p>
        </v-col>

        <v-col v-if="jobPostDetails.priority" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Priority</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.priority) }}
          </p>
        </v-col>
        <v-col v-if="jobPostDetails.agencyInvolved" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Agency Involved</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.agencyInvolved) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Expected Work Permits
          </p>
          <span class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.workPermits) }}
          </span>
        </v-col>
        <v-col v-if="jobPostDetails.otherWorkPermits" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Other Work Permits</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.otherWorkPermits) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Status</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.jobpostStatus) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Added On</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.addedOn) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Added By</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.addedBy) }}
          </p>
        </v-col>
        <v-col v-if="jobPostDetails.industry" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Industry</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.industry) }}
          </p>
        </v-col>
        <v-col v-if="jobPostDetails.category" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Category</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.category) }}
          </p>
        </v-col>
        <v-col v-if="jobPostDetails.subcategory" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Subcategory</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.subcategory) }}
          </p>
        </v-col>
        <v-col v-if="jobPostDetails.client" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Client</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.client) }}
          </p>
        </v-col>
      </v-row>
      <v-row>
        <v-col v-if="jobPostDetails.reasonForOpening" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Reason for Opening</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.reasonForOpening) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Workflow</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.workflow) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Replacement for</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobPostDetails.replacementFor) }}
          </p>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" md="12" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1 pb-2">Job Description</p>
          <div
            class="job-description"
            :innerHTML="sanitizedJobDescription"
          ></div>
        </v-col>
      </v-row>
    </v-card-text>
    <AppLoading v-if="isGetJobpostDetailsLoading"></AppLoading>
  </v-card>
  <AppFetchErrorScreen
    v-else
    key="no-results-screen"
    main-title="No matching search results found"
    image-name="common/no-records"
  ></AppFetchErrorScreen>
</template>

<script>
//components
import moment from "moment";
import { checkNullValue } from "@/helper";
import DOMPurify from "dompurify";
import { GET_JOB_POST_DETAILS } from "@/graphql/workflow/approvalManagementQueries.js";
export default {
  name: "JobPostDetails",
  data() {
    return {
      givenJobPostIndex: null,
      wholeJobPostData: null,
      mytoken: "",
      showValidationAlert: false,
      isGetJobpostDetailsLoading: false,
      isListError: false,
      jobPostDetails: {
        jobTitle: "",
        paymentType: "",
        serviceProviderName: "",
        functionalArea: "",
        currency: "",
        designation: "",
        minSalary: "",
        maxSalary: "",
        jobType: "",
        expectedQualification: null,
        jobDuration: "",
        keySkillSet: "",
        minExperienceRange: "",
        maxExperience: "",
        rounds: "",
        expectedJoiningDate: null,
        locations: "",
        address: "",
        coolingPeriod: "",
        isTravelRequired: null,
        noOfVacancies: null,
        noOfMaleVacancies: null,
        noOfFemaleVacancies: null,
        priority: "",
        agencyInvolved: "",
        workPermits: "",
        postingDate: null,
        jobpostStatus: "",
        otherWorkPermits: "",
        closingDate: null,
        jobDescription: "",
        category: "",
        subcategory: "",
        industry: "",
        reasonForOpening: "",
        workflow: "",
        client: "",
        replacementFor: null,
        addedBy: "",
        addedOn: "",
        updatedBy: "",
      },
      tab: null,
    };
  },
  computed: {
    sanitizedJobDescription() {
      return DOMPurify.sanitize(this.jobPostDetails.jobDescription);
    },
    fieldForce() {
      const { fieldForce } = this.$store.state.orgDetails;
      return fieldForce;
    },
    formatDate() {
      return (date) => {
        if (date) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },

  mounted() {
    this.jobPostIdQueryParam = this.$route.query.jobPostId;
    this.getEmployeeJobPostDetails();
  },

  methods: {
    checkNullValue,
    formLanguagesNames(languages) {
      let langNames = [];
      for (var i = 0; i < languages.length; i++) {
        langNames.push(languages[i].Language_Name);
      }
      return langNames.join(", ");
    },
    getDateAndTime(dateTimeData) {
      // Create a Date object from the date-time string
      const dateTime = new Date(dateTimeData);

      // Extract the date components
      const year = dateTime.getFullYear();
      const month = dateTime.getMonth() + 1; // Month is zero-indexed, so add 1
      const day = dateTime.getDate();

      // Extract the time components
      const hours = dateTime.getUTCHours();
      const minutes = dateTime.getUTCMinutes();
      const seconds = dateTime.getUTCSeconds();

      let dateAndTime =
        day +
        "/" +
        month +
        "/" +
        year +
        " at " +
        hours +
        ":" +
        minutes +
        ":" +
        seconds;
      return dateAndTime;
    },
    async getEmployeeJobPostDetails() {
      let vm = this;
      vm.isGetJobpostDetailsLoading = true;
      await vm.$apollo
        .query({
          query: GET_JOB_POST_DETAILS,
          variables: {
            jobPostId: parseInt(this.jobPostIdQueryParam),
            employeeId: 1,
            action: "view",
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveJobPost &&
            response.data.retrieveJobPost.jobPostData
          ) {
            this.wholeJobPostData = response.data.retrieveJobPost.jobPostData;
          }
          this.getJobPostDetails();
          vm.isGetJobpostDetailsLoading = false;
        })
        .catch((err) => {
          vm.isGetJobpostDetailsLoading = false;
          this.handleGetEmployeeJobPostDetailsError(err);
        });
    },
    handleGetEmployeeJobPostDetailsError() {
      this.isListError = true;
    },
    getJobPostDetails() {
      this.jobPostDetails.jobTitle = this.wholeJobPostData.Job_Post_Name;
      this.jobPostDetails.jobTitle = this.wholeJobPostData.Job_Post_Name;
      this.jobPostDetails.paymentType = this.wholeJobPostData.Payment_Type;
      this.jobPostDetails.functionalArea =
        this.wholeJobPostData.Functional_Area;
      this.jobPostDetails.serviceProviderName =
        this.wholeJobPostData.Service_Provider_Name;
      this.jobPostDetails.currency = this.wholeJobPostData.Currency;
      this.jobPostDetails.designation = this.wholeJobPostData.Designation;
      this.jobPostDetails.minSalary =
        this.wholeJobPostData.Min_Payment_Frequency;
      this.jobPostDetails.maxSalary =
        this.wholeJobPostData.Max_Payment_Frequency;
      this.jobPostDetails.jobType = this.wholeJobPostData.Job_Type;
      this.jobPostDetails.expectedQualification =
        this.wholeJobPostData.Qualification.map((item) => {
          return item.Qualification;
        });
      this.jobPostDetails.expectedQualification =
        this.jobPostDetails.expectedQualification.join(", ");
      this.jobPostDetails.jobDuration = this.wholeJobPostData.Job_Duration;
      this.jobPostDetails.keySkillSet = this.wholeJobPostData.Skill_Set;
      this.jobPostDetails.minExperienceRange =
        this.wholeJobPostData.Min_Work_Experience;
      this.jobPostDetails.maxExperience =
        this.wholeJobPostData.Max_Work_Experience;
      this.jobPostDetails.rounds = this.wholeJobPostData.Rounds.map((item) => {
        return item.Round_Name;
      });
      this.jobPostDetails.rounds = this.jobPostDetails.rounds.join(", ");
      this.jobPostDetails.expectedJoiningDate =
        this.wholeJobPostData.Expected_Joining_Date;
      this.jobPostDetails.locations = this.wholeJobPostData.JobLocations.map(
        (item) => {
          return item.Location_Name;
        }
      );
      this.jobPostDetails.locations = this.jobPostDetails.locations.join(", ");
      this.jobPostDetails.address = this.wholeJobPostData.Address;
      this.jobPostDetails.coolingPeriod = this.wholeJobPostData.Cooling_Period;
      this.jobPostDetails.isTravelRequired =
        this.wholeJobPostData.Travel_Required;
      this.jobPostDetails.noOfVacancies = this.wholeJobPostData.No_Of_Vacancies;
      this.jobPostDetails.noOfMaleVacancies =
        this.wholeJobPostData.No_Of_Male_Vacancies;
      this.jobPostDetails.noOfFemaleVacancies =
        this.wholeJobPostData.No_Of_Female_Vacancies;
      this.jobPostDetails.priority = this.wholeJobPostData.Priority;
      this.jobPostDetails.agencyInvolved =
        this.wholeJobPostData.Agency_Involved;
      this.jobPostDetails.workPermits =
        this.wholeJobPostData.WorkAuthorization.map((item) => {
          return item.Work_Authorization_Name;
        });
      this.jobPostDetails.workPermits =
        this.jobPostDetails.workPermits.join(", ");
      this.jobPostDetails.postingDate = this.wholeJobPostData.Posting_Date;
      this.jobPostDetails.jobpostStatus = this.wholeJobPostData.Job_Post_Status;
      this.jobPostDetails.otherWorkPermits =
        this.wholeJobPostData.OtherWorkAuthorization;
      this.jobPostDetails.closingDate = this.wholeJobPostData.Closing_Date;
      this.jobPostDetails.jobDescription =
        this.wholeJobPostData.Job_Description;
      this.jobPostDetails.category = this.wholeJobPostData.Category_Name;
      this.jobPostDetails.subcategory = this.wholeJobPostData.Subcategory_Name;
      this.jobPostDetails.industry = this.wholeJobPostData.Industry_Name;
      this.jobPostDetails.reasonForOpening =
        this.wholeJobPostData.Reason_For_Opening;
      this.jobPostDetails.workflow = this.wholeJobPostData.Workflow_Name;
      this.jobPostDetails.client = this.wholeJobPostData.Client_Name;
      this.jobPostDetails.addedOn = this.wholeJobPostData.Added_On;
      this.jobPostDetails.addedOn = this.getDateAndTime(
        this.jobPostDetails.addedOn
      );
      this.jobPostDetails.addedBy = this.wholeJobPostData.addedByName;
      this.jobPostDetails.updatedBy = this.wholeJobPostData.updatedByName;
      this.jobPostDetails.replacementFor =
        this.wholeJobPostData.ReplacementFor.map((item) => {
          return item.Employee_Name;
        });
      this.jobPostDetails.replacementFor =
        this.jobPostDetails.replacementFor.join(", ");
    },
  },
};
</script>
<style scoped>
.job-description-content {
  font-size: 16px; /* Adjust the font size as needed */
}
/* Ensure list styles are visible */
.job-description-content ol,
.job-description-content ul {
  list-style: initial !important ; /* Reset list styles to default */
}

/* Ensure list items are properly displayed */
.job-description-content li {
  margin-bottom: 5px; /* Add some spacing between list items */
}
</style>
