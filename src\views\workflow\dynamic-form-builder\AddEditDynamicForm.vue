<template>
  <v-card v-if="!isLoading" class="rounded-lg mt-2">
    <div
      class="d-flex align-center py-2"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center pl-4 py-2">
        <v-avatar class="mr-2" size="28" color="hover" variant="elevated">
          <v-icon class="primary" size="18">fas fa-file-alt</v-icon>
        </v-avatar>
        <div v-if="!isEdit" class="text-subtitle-1 font-weight-bold">
          {{ templateName + " - " + formFor }}
        </div>
        <div v-else class="text-subtitle-1 font-weight-bold">
          {{ editFormData.templateName + " - " + editFormData.formFor }}
        </div>
      </div>
      <div class="d-flex align-center">
        <AppToggleButton
          v-if="isEdit"
          class="mr-2"
          button-active-text="Active"
          button-inactive-text="Inactive"
          button-active-color="#7de272"
          button-inactive-color="red"
          :current-value="status === 'Active' ? true : false"
          @chosen-value="onChangeStatus($event)"
        ></AppToggleButton>
        <!-- <v-btn
          class="mr-2 mt-2"
          color="secondary"
          size="small"
          rounded="lg"
          variant="outlined"
          @click="resetForm()"
        >
          Reset
        </v-btn>
        <v-btn
          v-if="accessRights.update"
          class="mr-2 mt-2"
          @click="validateAddUpdateDynamicForm()"
          size="small"
          color="secondary"
          rounded="lg"
          >Save</v-btn
        > -->
        <v-icon class="mx-1" color="primary" @click="closeAddEditForm">
          fas fa-times
        </v-icon>
      </div>
    </div>
    <div
      style="height: calc(100vh - 250px); overflow: scroll"
      class="mb-4 d-flex justify-center"
    >
      <div
        id="fb-render"
        ref="fbRender"
        class="form-horizontal form-validation pa-10 w-100"
        cf-context
      ></div>
    </div>
  </v-card>
  <v-bottom-navigation v-if="!isLoading && openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            class="ml-2 primary"
            rounded="lg"
            variant="outlined"
            size="small"
            style="height: 40px"
            @click="closeAddEditForm"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            class="mr-2 primary"
            rounded="lg"
            variant="outlined"
            size="small"
            style="height: 40px"
            @click="resetForm()"
            ><span class="primary">Reset</span></v-btn
          >
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            style="height: 40px"
            @click="validateAddUpdateDynamicForm"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import "formBuilder";
import "jquery-ui-dist/jquery-ui.min.js";
import "jquery-validation/dist/jquery.validate.min.js";
import { defineComponent } from "vue";
import { ADD_UPDATE_DYNAMIC_FORM } from "@/graphql/workflow/dynamicFormBuilderQueries";

export default defineComponent({
  name: "AddEditDynamicForm",
  data: () => ({
    isLoading: false,
    formBuilder: null,
    openBottomSheet: true,
    status: "Active",
  }),
  emits: ["close-form", "form-updated"],
  props: {
    templateName: {
      type: String,
      required: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    formFor: {
      type: String,
      required: false,
    },
    editFormData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    accessRights: {
      type: Object,
      required: true,
    },
    templateRequired: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    let options = {
      disabledActionButtons: ["data", "clear", "save"],
      disableFields: ["autocomplete", "button", "file"],
      inputSets: [
        {
          label: "Assets",
          icon: "☑",
          showHeader: true,
          fields: [
            {
              type: "radio-group",
              label: "Discussion Preview",
              inline: true,
              required: true,
              values: [
                {
                  label: "Returned",
                  value: "Returned",
                  selected: false,
                  name: "discussion-preview",
                },
                {
                  label: "Not Returned",
                  value: "Not Returned",
                  selected: false,
                  name: "discussion-preview",
                },
                {
                  label: "Damaged",
                  value: "Damaged",
                  selected: false,
                  name: "discussion-preview",
                },
              ],
            },
            {
              type: "text",
              required: true,
              class: "form-control",
              label: "Deduction Applicable",
            },
          ],
          className: "assetValue",
        },
      ],
    };
    if (this.editFormData) {
      //Prefill the status
      this.status = this.editFormData.status;
      let formData = this.editFormData.formTemplate;
      options.formData = formData;
      this.formBuilder = $("#fb-render").formBuilder(options);
    } else {
      this.formBuilder = $("#fb-render").formBuilder(options);
    }
  },
  methods: {
    resetForm() {
      this.formBuilder.actions.clearFields();
    },
    closeAddEditForm() {
      this.openBottomSheet = false;
      this.$emit("close-form");
    },
    onChangeStatus(status) {
      if (status && status.length) {
        this.status = status[1] === true ? "Active" : "Inactive";
      }
    },
    validateAddUpdateDynamicForm() {
      let data = JSON.parse(this.formBuilder.actions.getData("json"));
      if (data && data.length) {
        this.addUpdateDynamicForm(data);
      } else {
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message: "Please add at least one field to save the form.",
        };
        this.showAlert(snackbarData);
      }
    },
    addUpdateDynamicForm(templateData) {
      let vm = this;
      vm.isLoading = true;
      try {
        let dynamicForm = {
          templateName: vm.isEdit
            ? vm.editFormData.templateName
            : vm.templateName,
          conversational: 0,
          status: vm.status,
          formFor: vm.isEdit ? vm.editFormData.formFor : vm.formFor,
          formTemplate: JSON.stringify(templateData),
          required: vm.templateRequired ? 1 : 0,
        };
        //Change formFor
        dynamicForm.formFor =
          dynamicForm.formFor.toLowerCase() === "vendor" ? "VEND" : "IND";
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_DYNAMIC_FORM,
            variables: {
              templateId:
                vm.editFormData && vm.editFormData.templateId
                  ? parseInt(vm.editFormData.templateId)
                  : 0,
              dynamicForm,
            },
            client: "apolloClientAL",
          })
          .then(() => {
            vm.isLoading = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: `Dynamic form template ${
                vm.isEdit ? "updated" : "added"
              } successfully.`,
            };
            vm.showAlert(snackbarData);
            vm.$emit("form-updated");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },

    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEdit ? "updating" : "addding",
        form: "dynamic form",
        isListError: false,
      });
      this.$emit("form-updated");
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
<style>
@import url("../../../assets/css/dynamic-form-builder.css");
.error {
  color: red;
}
h1,
h2,
p {
  margin: 15px 0px;
}
.formbuilder-radio-group-label {
  margin: 15px 0px;
}
.radio-group,
.checkbox-group {
  margin: 10px 0px;
}
.formbuilder-checkbox {
  margin: 5px 0px;
}
.rendered-form .form-group {
  margin-bottom: 25px !important;
}
</style>
