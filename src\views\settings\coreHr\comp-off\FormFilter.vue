<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="secondary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedSalaryType"
              color="secondary"
              :items="salaryType"
              label="Salary Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedWorkDayType"
              color="secondary"
              :items="workDayType"
              label="Work Day Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedCompOffThreshold"
              color="secondary"
              :items="compOffThresholdList"
              label="Comp Off Threshold"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedCompOffEncashment"
              color="secondary"
              :items="compOffEncashmentList"
              label="Comp Off Encashment"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedCompOffApplicability"
              color="primary"
              :items="compOffApplicabilityList"
              label="Comp Off Applicability For Overtime Hours"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
export default defineComponent({
  name: "FormFilter",

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },

  data: () => ({
    openFormFilter: false,
    compOffThresholdList: ["Work Schedule Hours", "Fixed Hours"],
    selectedCompOffThreshold: [],
    compOffApplicabilityList: [
      "Full Day",
      "Half Day",
      "Both Full Day & Half Day",
      "Not Applicable",
    ],
    selectedCompOffApplicability: [],
    compOffEncashmentList: ["Yes", "No"],
    selectedCompOffEncashment: [],
    workDayType: [
      "Extra Work Hours(Weekday)",
      "Holiday",
      "Mandatory",
      "Work Schedule Holiday(Week Off)",
    ],
    selectedWorkDayType: [],
    salaryType: ["Monthly", "Hourly"],
    selectedSalaryType: [],
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.fnApplyFilter();
  },
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.items;

      if (this.selectedCompOffThreshold.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCompOffThreshold.includes(
            item.Comp_Off_Threshold
          );
        });
      }
      if (this.selectedCompOffApplicability.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCompOffApplicability.includes(
            item.Comp_Off_Applicability_For_Overtime_Hours
          );
        });
      }
      if (this.selectedCompOffEncashment.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCompOffEncashment.includes(
            item.Comp_Off_Encashment
          );
        });
      }
      if (this.selectedWorkDayType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedWorkDayType.includes(item.Work_Day_Type);
        });
      }
      if (this.selectedSalaryType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedSalaryType.includes(item.Salary_Type);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedCompOffThreshold = [];
      this.selectedCompOffApplicability = [];
      this.selectedCompOffEncashment = [];
      this.selectedWorkDayType = [];
      this.selectedSalaryType = [];
      this.openFormFilter = false;
    },
  },
});
</script>
