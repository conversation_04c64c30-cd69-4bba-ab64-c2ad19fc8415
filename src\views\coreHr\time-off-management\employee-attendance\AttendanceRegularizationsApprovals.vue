<template>
  <div class="mx-7 mt-1 overflow-y-scroll" style="height: calc(100vh - 180px)">
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>

    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      :button-text="showRetryBtn ? 'Retry' : ''"
      @button-click="refetchList()"
    >
    </AppFetchErrorScreen>

    <AppFetchErrorScreen
      v-else-if="originalList.length === 0"
      key="no-results-screen"
      :main-title="emptyScenarioMsg[0]"
      :topMessage="originalList?.length === 0 ? emptyScenarioMsg[1] : ''"
      :image-name="
        originalList?.length === 0
          ? 'workflow/empty-approval'
          : 'common/no-records'
      "
      :isSmallImage="originalList.length === 0"
    >
      <template #contentSlot>
        <div class="d-flex mb-2 flex-wrap justify-center align-center">
          <div class="d-flex align-center">
            <!-- Datepicker -->
            <datepicker
              :format="'MMMM, yyyy'"
              v-model="selectedMonthYear"
              maximum-view="year"
              minimum-view="month"
              class="approval-center_datepicker"
              :disabled-dates="getDisabledDates"
            ></datepicker>
          </div>
          <v-btn
            v-if="originalList.length === 0"
            color="transparent"
            class="mt-1"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <div
        v-if="originalList.length > 0"
        class="d-flex flex-wrap align-center my-4"
        :class="isMobileView ? 'flex-column' : 'flex-row'"
        style="justify-content: space-between"
      >
        <!-- Left Side (Back button, Dropdown, and Datepicker) -->
        <div class="d-flex align-center">
          <!-- Datepicker -->
          <datepicker
            :format="'MMMM, yyyy'"
            v-model="selectedMonthYear"
            maximum-view="year"
            minimum-view="month"
            style="width: 220px"
            :disabled-dates="getDisabledDates"
          ></datepicker>
        </div>

        <!-- Right Side (Buttons and Menu) -->
        <div class="d-flex align-center">
          <v-btn
            v-if="formAccess.add"
            color="white"
            rounded="lg"
            class="ml-2"
            @click="onClickApprove()"
            :size="isMobileView ? 'small' : 'default'"
          >
            <i
              class="hr-workflow-task-management-approve text-green pr-1 text-body-1"
            ></i
            >Approve
          </v-btn>
          <v-btn
            color="transparent"
            class="mt-1"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
          <v-menu v-model="openMoreMenu" transition="scale-transition">
            <template v-slot:activator="{ props }">
              <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
                <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                <v-icon v-else>fas fa-caret-up</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="action in moreActions"
                :key="action.key"
                @click="onMoreAction(action.key)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <v-list-item-title
                      v-bind="props"
                      class="pa-3"
                      :class="{
                        'bg-hover': isHovering,
                      }"
                    >
                      {{ action.key }}
                    </v-list-item-title>
                  </template>
                </v-hover>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>

      <v-row>
        <v-col v-if="originalList.length > 0" :cols="12">
          <v-progress-linear
            v-if="loading"
            indeterminate
            color="primary"
          ></v-progress-linear>
          <v-data-table
            v-model="selectedApprovalRecords"
            :headers="tableHeaders"
            :items="itemList"
            item-value="Attendance_Id"
            fixed-header
            :show-select="!isMobileView"
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(350, itemList)
            "
            :items-per-page="50"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              {
                value: -1,
                title: '$vuetify.dataFooter.itemsPerPageAll',
              },
            ]"
          >
            <template v-slot:[`header.data-table-select`]="{ selectAll }">
              <v-checkbox-btn
                v-model="selectAllBox"
                color="primary"
                false-icon="far fa-circle"
                true-icon="fas fa-check-circle"
                indeterminate-icon="fas fa-minus-circle"
                class="mt-1"
                @change="selectAll(selectAllBox)"
              ></v-checkbox-btn>
            </template>
            <template v-slot:item="{ item }">
              <tr
                @click="openViewForm(item)"
                class="data-table-tr bg-white cursor-pointer"
                :class="isMobileView ? 'v-data-table__mobile-table-row' : ''"
              >
                <td
                  :class="isMobileView ? 'mt-3 mb-n5' : ''"
                  class="d-flex align-center"
                >
                  <div
                    v-if="
                      !isMobileView &&
                      selectedItem &&
                      selectedItem.Attendance_Id === item.Attendance_Id
                    "
                    class="data-table-side-border d-flex"
                  ></div>
                  <v-checkbox-btn
                    v-model="item.isSelected"
                    v-if="!isMobileView"
                    color="primary"
                    false-icon="far fa-circle"
                    true-icon="fas fa-check-circle"
                    class="mt-n2 ml-n2"
                    @click.stop="
                      {
                      }
                    "
                    @change="checkAllSelected()"
                  ></v-checkbox-btn>
                </td>
                <td id="mobile-view-td">
                  <div id="mobile-header" class="font-weight-bold mt-2">
                    Employee
                  </div>
                  <section class="d-flex align-center">
                    <div style="max-width: 200px" class="text-truncate">
                      <span class="text-primary text-body-2 font-weight-medium">
                        <v-tooltip
                          :text="item.Employee_Name + ''"
                          location="bottom"
                        >
                          <template v-slot:activator="{ props }">
                            <span
                              v-bind="
                                item.Employee_Name &&
                                item.Employee_Name.length > 20
                                  ? props
                                  : ''
                              "
                              >{{ item.Employee_Name }}</span
                            >
                          </template>
                        </v-tooltip>
                        <v-tooltip
                          :text="item.User_Defined_EmpId + ''"
                          location="bottom"
                        >
                          <template v-slot:activator="{ props }">
                            <div
                              v-if="item.User_Defined_EmpId"
                              v-bind="
                                item.User_Defined_EmpId &&
                                item.User_Defined_EmpId.length > 20
                                  ? props
                                  : ''
                              "
                              class="text-grey"
                            >
                              {{ checkNullValue(item.User_Defined_EmpId) }}
                            </div>
                          </template>
                        </v-tooltip>
                      </span>
                    </div>
                  </section>
                </td>
                <td id="mobile-view-td" width="300px">
                  <div id="mobile-header" class="font-weight-bold">Date</div>
                  <section class="text-body-2 text-primary">
                    {{ checkNullValue(formatDate(item.AttendanceDate)) }}
                  </section>
                </td>
                <td id="mobile-view-td" width="300px">
                  <div id="mobile-header" class="font-weight-bold">
                    Check In
                  </div>
                  <section class="text-body-2 text-primary">
                    {{
                      checkNullValue(
                        formatDateTime(item.Attendance_PunchIn_Date)
                      )
                    }}
                  </section>
                </td>
                <td id="mobile-view-td" width="300px">
                  <div id="mobile-header" class="font-weight-bold">
                    Check Out
                  </div>
                  <section class="text-body-2 text-primary">
                    {{
                      checkNullValue(
                        formatDateTime(item.Attendance_PunchOut_Date)
                      )
                    }}
                  </section>
                </td>
                <td id="mobile-view-td" width="300px">
                  <div id="mobile-header" class="font-weight-bold">
                    Effective Hours
                  </div>
                  <section class="text-body-2 text-primary">
                    {{ checkNullValue(formatTime(item.Actual_Total_Hours)) }}
                  </section>
                </td>
                <td>
                  <div id="mobile-header" class="font-weight-bold">
                    Late Arrival
                  </div>
                  <section class="d-flex align-center">
                    {{ checkNullValue(item.Arrival) }}
                  </section>
                </td>
                <td>
                  <div>
                    <section
                      class="d-flex justify-center align-center"
                      style="width: 100%"
                    >
                      <div class="d-flex align-center">
                        <v-tooltip
                          text="You don't have access to perform this action."
                          location="top"
                        >
                          <template v-slot:activator="{ props }">
                            <i
                              class="hr-workflow-task-management-approve text-green text-h5 pl-2 cursor-pointer"
                              aria-hidden="true"
                              title="Approve"
                              id="idApproveButton"
                              v-bind="
                                formAccess && formAccess.update ? {} : props
                              "
                              @click.stop="
                                formAccess && formAccess.update
                                  ? onClickItemAction(item, 'approve')
                                  : {}
                              "
                            />
                            <i
                              class="hr-workflow-task-management-reject text-red text-h5 pl-2 cursor-pointer"
                              aria-hidden="true"
                              title="Reject"
                              id="idRejectButton"
                              v-bind="
                                formAccess && formAccess.update ? {} : props
                              "
                              @click.stop="
                                formAccess && formAccess.update
                                  ? onClickItemAction(item, 'reject')
                                  : {}
                              "
                            />
                          </template>
                        </v-tooltip>
                      </div>
                    </section>
                  </div>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </div>
    <AppLoading v-if="listLoading" />
    <AppWarningModal
      v-if="conformationModel"
      :open-modal="conformationModel"
      confirmation-heading="Are you sure you want to approve the records?"
      icon-name="fas fa-check-circle"
      icon-color="success"
      icon-Size="75"
      @close-warning-modal="closeConfirmationModal"
      @accept-modal="
        onMultiApproval(
          'approve',
          'hr-workflow-task-management-approve text-green'
        )
      "
    />
    <v-dialog
      :model-value="openFormInModal"
      class="rounded-lg"
      max-width="1000"
      @click:outside="closeAllForms()"
    >
      <v-card v-if="showEmpListModal" class="px-8 rounded-lg" min-width="100%">
        <v-card-title>
          <div class="text-primary text-center font-weight-medium ma-4">
            Select an employee for the Attendance Regularization
          </div>
        </v-card-title>
        <div v-if="listLoading" class="mt-3">
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            />
          </div>
        </div>
      </v-card>
    </v-dialog>
    <AppLoading v-if="isLoading"></AppLoading>
    <ViewAttendanceApproval
      v-if="openViewOverlayForm"
      :selectedEmployee="selectedEmployee"
      :access-rights="formAccess"
      :landedFormName="landedFormName"
      :selectedItem="selectedItem"
      @close-view-attendance-window="openViewOverlayForm = false"
      @refetch-data="refetchList()"
      @close-form="closeAllForms()"
      @open-edit-form="openEditForm()"
      @on-delete-entry="onDeleteEntry($event)"
      @on-edit-entry="onEditEntry($event)"
    ></ViewAttendanceApproval>
    <AppWarningModal
      v-if="conformationModel || rejectionModel"
      :open-modal="conformationModel || rejectionModel"
      :confirmation-heading="
        conformationModel
          ? 'Are you sure you want to approve the records?'
          : 'Are you sure you want to reject the records?'
      "
      :icon-name="
        conformationModel ? 'fas fa-check-circle' : 'fas fa-times-circle'
      "
      :icon-color="conformationModel ? 'success' : 'red'"
      :icon-Size="75"
      @close-warning-modal="
        conformationModel ? closeConfirmationModal() : closeRejectionModal()
      "
      @accept-modal="
        conformationModel
          ? onMultiApproval(
              'approve',
              'hr-workflow-task-management-approve text-green'
            )
          : onMultiApproval(
              'reject',
              'hr-workflow-task-management-reject text-red'
            )
      "
    />
    <AppWarningModal
      v-if="singleRejectionModel"
      :open-modal="singleRejectionModel"
      :confirmation-heading="'Are you sure to reject this record?'"
      :iconName="'hr-workflow-task-management-reject text-red'"
      iconColor="red"
      :iconSize="75"
      @close-warning-modal="singleRejectionModel = false"
      @accept-modal="validateRejectForm()"
    >
      <template v-slot:warningModalContent>
        <v-form
          ref="rejectForm"
          style="width: 70%"
          class="d-flex justify-center"
        >
          <v-textarea
            v-model="comment"
            variant="solo"
            rows="2"
            :rules="[
              required('Reason', comment),
              maxLengthValidation('Reason', comment, 100),
              minLengthValidation('Reason', comment, 3),
              multilingualNameNumericValidation('Reason', comment),
            ]"
          >
            <template v-slot:label>
              Reason <span class="text-red">*</span>
            </template>
          </v-textarea>
        </v-form>
      </template>
    </AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";

import { checkNullValue, convertUTCToLocal } from "@/helper.js";
// Queries
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
import Datepicker from "vuejs3-datepicker";
const ViewAttendanceApproval = defineAsyncComponent(() =>
  import("./ViewAttendanceApproval.vue")
);

export default {
  name: "AttendanceRegularizationsApprovals",
  components: {
    Datepicker,
    ViewAttendanceApproval,
  },
  mixins: [FileExportMixin, validationRules],
  emits: ["change-count", "refetch-list"],
  data: () => ({
    selectedEmpRecords: [],
    popupEmployeeList: [],
    selectStrategy: "single",
    selectedRowIndex: null,
    // list
    listLoading: false,
    isLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    showEmpListModal: false,
    otherShiftEmployees: [],
    conformationModel: false,
    // view
    selectedItem: null,
    openViewOverlayForm: false,
    // tab
    isFilterApplied: false,
    openMoreMenu: false,
    openedSubTab: "Regularizations",
    selectAllBox: false,
    selectedApprovalRecords: [],
    selectedMonthYear: new Date(),
    selectedItems: [],
    rejectionModel: false,
    singleRejectionModel: false,
    comment: "",
    loading: false,
  }),
  props: {
    itemsList: { type: Array, default: () => [] },
  },
  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    subTabItems() {
      let initialTabs = [
        {
          label: "General Approvals",
          value: "generalApprovals",
          disable: false,
        },
        { label: "Week Off/Holiday", value: "weekOffHoliday", disable: false },
        { label: "Regularizations", value: "regularizations", disable: false },
      ];
      return initialTabs;
    },
    landedFormName() {
      let formName = this.accessIdRights("307");
      return formName?.formName;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formatDateTime() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    getDisabledDates() {
      const currentYear = new Date().getFullYear();
      const startOfPreviousYear = new Date(currentYear - 1, 0, 1);
      const endOfNextYear = new Date(currentYear + 1, 11, 31);

      return {
        to: startOfPreviousYear,
        from: endOfNextYear,
        preventDisableDateSelection: true,
      };
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessIdRights("307");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      return [
        {
          title: "Employee",
          align: "start",
          key: "Employee_Name",
        },
        {
          title: "Date",
          key: "AttendanceDate",
        },
        {
          title: "Check-In",
          key: "Attendance_PunchIn_Date",
        },
        {
          title: "Check Out",
          key: "Attendance_PunchOut_Date",
        },
        {
          title: "Effective Hours",
          key: "Actual_Total_Hours",
        },
        {
          title: "Late Arrival",
          key: "Arrival",
        },
        {
          title: "Actions",
          key: "action",
          align: "center",
          sortable: false,
        },
      ];
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList?.length === 0) {
        msgText = [
          "We'll let you know when we've got something new for you.",
          "You have caught up with all approval requests.",
        ];
      }
      return msgText;
    },
    openFormInModal() {
      if (this.openViewOverlayForm && this.windowWidth < 1264) {
        return true;
      } else if (this.showEmpListModal) {
        return true;
      }
      return false;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
  },

  mounted() {
    this.openedSubTab = "regularizations";
    this.selectedEmployee = this.loginEmployeeId;
    this.itemList = this.itemsList;
    this.originalList = this.itemList;
  },

  watch: {
    itemsList() {
      this.itemList = this.itemsList;
      this.originalList = this.itemList;
    },
    itemList(val) {
      if (val) this.$emit("change-count", val.length);
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
    selectedMonthYear() {
      this.fetchList(this.selectedEmployee);
    },
    selectedApprovalRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemLogList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.Employee_Id)) {
            // Set to true if there's a match
            item.isSelected = true;
            this.selectedItems.push(item);
          }
        }
      } else {
        // Iterate through itemLogList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
        this.selectedItems = [];
      }
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    formatTime(timeString) {
      if (!timeString) return "";

      const [hours, minutes] = timeString.split(":").map(Number);
      let formattedTime = "";
      if (hours && minutes) {
        formattedTime = `${hours} Hrs ${minutes} Mins`;
      } else if (hours) {
        formattedTime = `${hours} Hrs`;
      } else if (minutes) {
        formattedTime = `0 Hrs ${minutes} Mins`;
      }

      return formattedTime;
    },
    checkAllSelected() {
      let selectedItems = this.originalList.filter((el) => el.isSelected);
      this.selectedItems = selectedItems;
      this.selectAllBox = selectedItems.length === this.originalList.length;
    },
    onClickApprove() {
      if (this.selectedItems.length > 0) {
        if (this.formAccess.update) {
          this.conformationModel = true;
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: "You don't have access to perform this action.",
          };
          this.showAlert(snackbarData);
        }
      } else {
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message: "Please select at least one record to proceed.",
        };
        this.showAlert(snackbarData);
      }
    },
    onClickItemAction(item, action) {
      const newLocal = this;
      newLocal.selectedItem = item;
      this.selectedAction = action;
      if (action?.toLowerCase() === "approve") {
        this.onSingleApproval(item, action);
      } else if (action?.toLowerCase() === "reject") {
        this.singleRejectionModel = true;
      }
    },
    async validateRejectForm() {
      let { valid } = await this.$refs.rejectForm.validate();
      if (valid) {
        this.onSingleApproval(this.selectedItem, "reject");
      }
    },
    async onSingleApproval(item, selectedAction) {
      this.singleRejectionModel = false;
      if (this.formAccess.update) {
        let vm = this;
        vm.isLoading = true;
        let attendanceIds = [];
        if (item) {
          attendanceIds = [item.Attendance_Id];
        }
        const newStatus =
          selectedAction.toLowerCase() === "approve" ? "Approved" : "Rejected";

        const formData = {
          attendanceIdArr: attendanceIds,
          status: newStatus,
          comments:
            selectedAction.toLowerCase() === "approve"
              ? "Approved successfully."
              : this.comment,
          isAction: "StatusUpdate",
        };

        this.isLoading = true;
        const apiObj = {
          url: vm.baseUrl + "employees/attendance/status-multi-approval/",
          type: "POST",
          dataType: "json",
          data: formData,
        };

        try {
          const response = await this.$store.dispatch(
            "triggerControllerFunction",
            apiObj
          );

          if (response?.success) {
            this.showAlert({
              isOpen: true,
              type: "success",
              message: "Attendance status updated successfully.",
            });

            this.conformationModel = false;
            this.rejectionModel = false;
            this.singleRejectionModel = false;
            this.selectedItem = null;
            this.refetchList("Attendance status updated");
          } else {
            this.showAlert({
              isOpen: true,
              type: "warning",
              message: response?.msg
                ? response.msg
                : "Something went wrong. Please try after some time.",
            });
          }
        } catch (err) {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: err?.response?.data?.msg
              ? err.response.data.msg
              : "An error occurred while updating the attendance status. Please try again later.",
          });
        } finally {
          this.isLoading = false;
        }
      } else {
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: "You don't have access to perform this action.",
        });
      }
    },
    async onMultiApproval(selectedStatusAction) {
      let vm = this;
      vm.isLoading = true;
      let attendanceIds = [];

      if (Array.isArray(vm.selectedItems)) {
        attendanceIds = vm.selectedItems.map((item) => {
          return item.Attendance_Id;
        });
      } else if (vm.selectedItems) {
        attendanceIds = [vm.selectedItems.Attendance_Id];
      }

      const newStatus =
        selectedStatusAction.toLowerCase() === "approve"
          ? "Approved"
          : "Rejected";

      const formData = {
        attendanceIdArr: attendanceIds,
        status: newStatus,
        comments:
          selectedStatusAction.toLowerCase() === "approve"
            ? "Approved successfully."
            : "Rejected due to issues.",
        isAction: "StatusUpdate",
      };

      this.isLoading = true;
      const apiObj = {
        url: vm.baseUrl + "employees/attendance/status-multi-approval/",
        type: "POST",
        dataType: "json",
        data: formData,
      };

      try {
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );

        if (response?.success) {
          this.showAlert({
            isOpen: true,
            type: "success",
            message: "Attendance status updated successfully.",
          });

          this.conformationModel = false;
          this.rejectionModel = false;
          this.singleRejectionModel = false;
          this.refetchList("Attendance status updated");
          this.selectedItems = [];
          this.selectAllBox = false;
        } else {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please try after some time.",
          });
        }
      } catch (err) {
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: err?.response?.data?.msg
            ? err.response.data.msg
            : "An error occurred while updating the attendance status. Please try again later.",
        });
      } finally {
        this.isLoading = false;
      }
    },
    closeConfirmationModal() {
      this.conformationModel = false;
      this.singleRejectionModel = false;
      this.selectedLogItem = null;
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    onMoreAction(actionType) {
      if (actionType?.toLowerCase() === "export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    changeStatus(item, type) {
      this.selectedItem = item;
      this.selectedItem.statusRequested = type;
      this.conformationModel = true;
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee", key: "Employee_Name" },
        { header: "Date", key: "AttendanceDate" },
        { header: "Check-In", key: "Attendance_PunchIn_Date" },
        { header: "Check-Out", key: "Attendance_PunchOut_Date" },
        { header: "Effective Hours", key: "Actual_Total_Hours" },
        { header: "Late Arrival", key: "Arrival" },
        { header: "Added On", key: "Added_On" },
        { header: "Added By", key: "Added_By_Name" },
        { header: "Updated On", key: "Updated_On" },
        { header: "Updated By", key: "Updated_By_Name" },
      ];

      const exportList = this.itemList.map((item) => ({
        Employee_Name: item.Employee_Name,
        User_Defined_EmpId: item.User_Defined_EmpId,
        AttendanceDate: item.AttendanceDate,
        Attendance_PunchIn_Date: item.Attendance_PunchIn_Date
          ? this.formatDateTime(item.Attendance_PunchIn_Date)
          : "",
        Attendance_PunchOut_Date: item.Attendance_PunchOut_Date
          ? this.formatDateTime(item.Attendance_PunchOut_Date)
          : "",
        Actual_Total_Hours: this.formatTime(item.Actual_Total_Hours),
        Arrival: item.Arrival,
        Added_On: item.Added_On ? this.formatDateTime(item.Added_On) : "",
        Added_By_Name: item.Added_By_Name,
        Updated_By_Name: item.Updated_By_Name,
        Updated_On: item.Updated_On ? this.formatDateTime(item.Updated_On) : "",
      }));

      const exportOptions = {
        fileExportData: exportList,
        fileName: "Attendance Regularization",
        sheetName: "Attendance Regularization",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.openViewOverlayForm = true;
    },

    closeAllForms() {
      this.openViewOverlayForm = false;
      this.selectedItem = null;
      this.showEmpListModal = false;
      this.conformationModel = false;
      this.selectedEmpRecords = [];
      this.selectedRowIndex = null;
      this.conformationModel = false;
    },
    fetchList() {
      this.$emit("refetch-list", this.selectedMonthYear);
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: " Approvals",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.attendance-approval {
  padding: 0em 0em 0em 0em;
}
.attendance-data {
  padding-left: 2em;
  padding-right: 2em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

.selected-row {
  background-color: rgb(var(--v-theme-hover));
}
@media screen and (max-width: 805px) {
  .attendance-approval {
    padding: 10em 1em 0em 1em;
  }
  .attendance-data {
    padding: 0em 2em 0em 3em;
  }
}
:deep(.approval-center_datepicker .vuejs3-datepicker__calendar) {
  top: -270px;
}
:deep(.vuejs3-datepicker__value) {
  min-width: 160px;
}
</style>
