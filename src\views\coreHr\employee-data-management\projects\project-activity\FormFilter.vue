<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              :items="activityNameList"
              label="Activity"
              :isAutoComplete="true"
              :itemSelected="selectedActivityName"
              @selected-item="
                onChangeIsFormDirty($event, 'selectedActivityName')
              "
              :is-auto-complete="true"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              listWidth="max-width: 300px !important"
              density="compact"
            ></CustomSelect>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              :items="billableList"
              label="Billable"
              :isAutoComplete="true"
              :itemSelected="selectedBillable"
              @selected-item="onChangeIsFormDirty($event, 'selectedBillable')"
              :is-auto-complete="true"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              listWidth="max-width: 300px !important"
              density="compact"
            ></CustomSelect>
          </v-col>
          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { defineComponent } from "vue";
export default defineComponent({
  name: "FormFilter",

  props: {
    backupMainList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  components: {
    CustomSelect,
  },
  data: () => ({
    openFormFilter: false,
    billableList: ["Yes", "No"],
    selectedBillable: [],
    selectedActivityName: [],
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isModelValue() {
      return (
        this.selectedBillable.length > 0 || this.selectedActivityName.length > 0
      );
    },
    activityNameList() {
      // Use Set to filter out duplicate activityName values
      const uniqueNamesSet = new Set(
        this.backupMainList.map((item) => item.activityName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
  },
  mounted() {
    this.fnApplyFilter();
  },
  methods: {
    onChangeIsFormDirty(val, type) {
      if (type == "selectedActivityName") {
        this.selectedActivityName = val;
      } else if (type == "selectedBillable") {
        this.selectedBillable = val;
      }
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.backupMainList;

      if (this.selectedActivityName.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedActivityName.includes(item.activityName);
        });
      }
      if (this.selectedBillable.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedBillable.includes(item.isBillable);
        });
      }
      this.$emit("apply-filter", [filteredArray, this.isModelValue]);
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedActivityName = [];
      this.selectedBillable = [];
      this.openFormFilter = false;
    },
  },
});
</script>
