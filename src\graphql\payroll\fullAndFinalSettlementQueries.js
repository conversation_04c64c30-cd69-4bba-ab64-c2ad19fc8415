import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_CURRENT_PAYROLL_MONTH = gql`
  query getCurrentSalaryMonth {
    getCurrentSalaryMonth {
      errorCode
      message
      displaySalaryMonth
      displaySalaryYear
      restrictFinancialAccessForManager
    }
  }
`;
export const LIST_RESIGNED_EMPLOYEES = gql`
  query listResignedEmployees($salaryMonth: Int!, $salaryYear: Int!) {
    listResignedEmployees(salaryMonth: $salaryMonth, salaryYear: $salaryYear) {
      errorCode
      message
      employeeDetails {
        employee_id
        employee_name
        designation_name
        department_name
        user_defined_empid
        Designation_Id
        Department_Id
        Location_Id
        EmpType_Id
        Work_Schedule
        notice_date
        exit_date
      }
    }
  }
`;
export const GET_SETTLEMENT_LIST = gql`
  query listFullAndFinalSettlement($salaryMonth: Int!, $salaryYear: Int!) {
    listFullAndFinalSettlement(
      salaryMonth: $salaryMonth
      salaryYear: $salaryYear
    ) {
      errorCode
      message
      employeeDetails {
        employeeId
        departmentId
        designationId
        locationId
        empTypeId
        workSchedule
        employeeName
        settlementStatus
        settlementDate
        netPay
        userDefinedEmpId
        addedBy
        addedOn
        updatedBy
        updatedOn
      }
    }
  }
`;
export const VIEW_SETTLEMENT = gql`
  query viewFullAndFinalSettlement($employeeId: Int!) {
    viewFullAndFinalSettlement(employeeId: $employeeId) {
      errorCode
      message
      employeeDetails {
        resignationDate
        noticeDate
        relievingReason
        earningsDeductions {
          Employee_Id
          Settlement_Type
          Mode
          Calculate_By
          Periodical_Amount
          Multiply_By
          Amount
        }
        summaryDeductions
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const DELETE_SETTLEMENT = gql`
  mutation deleteFullAndFinalSettlement(
    $employeeId: [Int]!
    $salaryMonth: Int!
    $salaryYear: Int!
  ) {
    deleteFullAndFinalSettlement(
      employeeId: $employeeId
      salaryMonth: $salaryMonth
      salaryYear: $salaryYear
    ) {
      errorCode
      message
    }
  }
`;
export const APPROVE_SETTLEMENT = gql`
  mutation approveFullAndFinalSettlement(
    $employeeId: Int!
    $earningsAndDeductions: [earningsAndDeductions]
    $isEdit: Int
  ) {
    approveFullAndFinalSettlement(
      employeeId: $employeeId
      earningsAndDeductions: $earningsAndDeductions
      isEdit: $isEdit
    ) {
      errorCode
      message
    }
  }
`;
export const REOPEN_SETTLEMENT = gql`
  mutation reopenFullAndFinalSettlement($employeeId: [Int]!) {
    reopenFullAndFinalSettlement(employeeId: $employeeId) {
      errorCode
      message
    }
  }
`;
export const ADD_SETTLEMENT = gql`
  mutation addFullAndFinalSettlement(
    $employeeDetails: [employeeDetailsInput]
    $salaryMonth: Int!
    $salaryYear: Int!
  ) {
    addFullAndFinalSettlement(
      employeeDetails: $employeeDetails
      salaryMonth: $salaryMonth
      salaryYear: $salaryYear
    ) {
      errorCode
      message
    }
  }
`;
