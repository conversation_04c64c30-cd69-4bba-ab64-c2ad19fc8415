<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row v-if="originalList.length > 0" justify="center">
            <v-col
              cols="12"
              md="9"
              class="d-flex justify-end"
              v-if="!showAddEditForm && !showViewForm"
            >
              <EmployeeDefaultFilterMenu
                class="d-flex justify-end mr-8"
                :isFilter="false"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="originalList.length === 0"
            key="no-data-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="The Job Roles module in our HRMS platform provides a flexible and structured framework for defining and managing workforce responsibilities. Job Roles can be mapped to designations or positions, allowing organizations to create a clear alignment between job expectations and employee contributions. This structured approach ensures that every individual’s role within the organization is well-defined, fostering clarity in responsibilities, career progression, and competency mapping."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="Recognizing the dynamic nature of modern workplaces, our platform allows employees, contractors, and contingent workers to be assigned multiple roles. This enables organizations to maximize workforce efficiency, ensuring that individuals can take on cross-functional responsibilities as needed. Whether for project-based assignments, temporary tasks, or specialized roles, the Job Roles module provides the agility needed to optimize talent utilization and enhance overall productivity."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess?.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Add New
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      rounded="lg"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="itemList.length == 0"
            key="no-results-screen"
            main-title="There are no job roles matched for the selected filters/searches."
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div>
              <div
                v-if="originalList.length > 0"
                class="d-flex flex-wrap align-center my-3"
                :class="isMobileView ? 'flex-column' : ''"
                style="justify-content: space-between"
              >
                <div
                  class="d-flex align-center flex-wrap"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <v-btn
                    rounded="lg"
                    style="pointer-events: none"
                    variant="flat"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    >Active:
                    <span class="text-green font-weight-bold">{{
                      activeRecords
                    }}</span>
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    style="pointer-events: none"
                    variant="flat"
                    class="ml-2"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    >Inactive:
                    <span class="text-red font-weight-bold">{{
                      inActiveRecords
                    }}</span></v-btn
                  >
                </div>

                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : 'justify-end'"
                >
                  <v-btn
                    v-if="formAccess.add"
                    prepend-icon="fas fa-plus"
                    color="primary"
                    variant="elevated"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="openAddForm"
                  >
                    <template v-slot:prepend>
                      <v-icon></v-icon>
                    </template>
                    Add New
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    color="transparent"
                    variant="flat"
                    class="mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList('Refetch List')"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                  <v-menu v-model="openMoreMenu" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="plain"
                        class="mt-1 ml-n5 mr-n5"
                        v-bind="props"
                      >
                        <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                        <v-icon v-else>fas fa-caret-up</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action"
                        @click="onMoreAction(action)"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="15" class="pr-2">{{
                                action.icon
                              }}</v-icon
                              >{{ action.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>

              <v-row>
                <v-col v-if="originalList.length > 0" cols="12" class="mb-12">
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Job Role
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Job_Role === item.Job_Role
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Job_Role"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Job_Role.length > 50 ? props : ''
                                  "
                                >
                                  {{ checkNullValue(item.Job_Role) }}
                                  <div
                                    v-if="
                                      item?.Job_Role_Code &&
                                      labelList?.[426]?.Field_Visiblity?.toLowerCase() ===
                                        'yes'
                                    "
                                    class="text-grey"
                                  >
                                    {{ checkNullValue(item.Job_Role_Code) }}
                                  </div>
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Associate Training/Accreditation
                          </div>
                          <div class="d-flex align-center">
                            <section
                              class="text-subtitle-1 font-weight-regular text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 500px;'
                                  : 'max-width: 200px;'
                              "
                            >
                              <v-tooltip
                                :text="
                                  item.accreditations[0]?.Accreditation_Type
                                "
                                location="bottom"
                                max-width="400"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                    :style="
                                      !isMobileView
                                        ? 'max-width: 300px; '
                                        : 'max-width: 200px; '
                                    "
                                    v-bind="
                                      item.accreditations[0]
                                        ?.Accreditation_Type > 20
                                        ? props
                                        : ''
                                    "
                                  >
                                    {{
                                      checkNullValue(
                                        item.accreditations[0]
                                          ?.Accreditation_Type
                                      )
                                    }}
                                    <div
                                      v-if="
                                        item.accreditations[0]
                                          ?.Accreditation_Category
                                      "
                                      class="text-grey"
                                    >
                                      {{
                                        checkNullValue(
                                          item.accreditations[0]
                                            ?.Accreditation_Category
                                        )
                                      }}
                                    </div>
                                  </div>
                                </template>
                              </v-tooltip>
                            </section>

                            <div
                              v-if="item.accreditations?.length > 1"
                              class="ml-n2"
                            >
                              <v-tooltip
                                :text="
                                  constructAccreditation(item.accreditations)
                                "
                                location="top"
                                max-width="300"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-if="item.accreditations?.length"
                                    variant="text"
                                    small
                                    v-bind="props"
                                    class="text-primary ml-4"
                                  >
                                    +{{ item.accreditations?.length - 1 }}
                                  </span>
                                </template>
                              </v-tooltip>
                            </div>
                          </div>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Designation
                          </div>
                          <div class="d-flex align-center">
                            <section
                              class="text-subtitle-1 font-weight-regular text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 500px;'
                                  : 'max-width: 200px;'
                              "
                            >
                              {{
                                checkNullValue(
                                  item.designations[0]?.Designation_Name
                                )
                              }}
                            </section>

                            <div
                              v-if="item.designations?.length > 1"
                              class="ml-n2"
                            >
                              <v-tooltip
                                :text="constructDesignations(item.designations)"
                                location="top"
                                max-width="300"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-if="item.designations?.length"
                                    variant="text"
                                    small
                                    v-bind="props"
                                    class="text-primary ml-4"
                                  >
                                    +{{ item.designations?.length - 1 }}
                                  </span>
                                </template>
                              </v-tooltip>
                            </div>
                          </div>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'width: max-content'
                          "
                          :cols="2"
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Status
                          </div>

                          <div
                            @click.stop="
                              {
                              }
                            "
                          >
                            <AppToggleButton
                              button-active-text="Active"
                              button-inactive-text="InActive"
                              button-active-color="#7de272"
                              button-inactive-color="red"
                              id-value="gab-analysis-based-on"
                              :current-value="
                                item.Status === 'Active' ? true : false
                              "
                              :isDisableToggle="!formAccess.update"
                              :tooltipContent="
                                formAccess.update
                                  ? ''
                                  : `Sorry, you don't have access rights to update the status`
                              "
                              @chosen-value="updateStatus($event, item)"
                            ></AppToggleButton>
                          </div>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-center align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex justify-center align-center"
                            style="width: 100%"
                          >
                            Actions
                          </div>
                          <section
                            class="d-flex justify-start align-center"
                            style="width: 100%"
                          >
                            <ActionMenu
                              v-if="itemActions(item)?.length"
                              :accessRights="checkAccess()"
                              @selected-action="onActions($event, item)"
                              :actions="itemActions(item)"
                              iconColor="grey"
                            ></ActionMenu>
                            <div v-else>
                              <p>-</p>
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <AppLoading v-if="listLoading" />
    <AddEditJobRoles
      v-if="showAddEditForm"
      :isEdit="isEdit"
      :selected-item="selectedItem"
      :landedFormName="landedFormName"
      @close-form="closeAllForms()"
      @edit-updated="refetchList()"
    />
    <ViewJobRoles
      v-if="showViewForm"
      :selectedItem="selectedItem"
      :form-access="formAccess"
      :landedFormName="landedFormName"
      @close-form="closeAllForms()"
      @open-edit-form="openEditForm()"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const AddEditJobRoles = defineAsyncComponent(() =>
  import("./AddEditJobRoles.vue")
);
const ViewJobRoles = defineAsyncComponent(() => import("./ViewJobRoles.vue"));
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
// Queries
import {
  LIST_JOB_ROLES,
  ADD_UPDATE_JOB_ROLE,
} from "@/graphql/corehr/jobRolesQueries.js";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "JobRoles",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ActionMenu,
    AddEditJobRoles,
    ViewJobRoles,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    isFilterApplied: false,
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    // add/update
    isEdit: false,
    showAddEditForm: false,
    openMoreMenu: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
  }),
  computed: {
    landedFormName() {
      let departmentForm = this.accessRights("329");
      if (departmentForm && departmentForm.customFormName) {
        return departmentForm.customFormName;
      } else return "Job Roles";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights("329");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    orgStructureFormAccess() {
      return this.$store.getters.orgStructureFormAccess;
    },
    organizationGroupFormName() {
      let projectForm = this.accessRights("269");
      if (
        projectForm &&
        projectForm.customFormName &&
        projectForm.customFormName !== ""
      ) {
        return projectForm.customFormName;
      } else return "Organization Group";
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.orgStructureFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (formAccessArray && formAccessArray.includes("Organization Group")) {
          const index = formAccessArray.indexOf("Organization Group");
          formAccessArray[index] = this.organizationGroupFormName;
        }
        return formAccessArray;
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      return [
        {
          title: "Job Role",
          align: "start",
          key: "Job_Role",
        },
        {
          title: "Associate Training/Accreditation",
          key: "accreditations",
        },
        {
          title: "Designation",
          key: "designations",
        },
        {
          title: "Status",
          key: "Status",
        },
        {
          title: "Actions",
          key: "actions",
          sortable: false,
        },
      ];
    },
    activeRecords() {
      let empList = this.originalList.filter((el) => el.Status === "Active");
      return empList.length;
    },
    inActiveRecords() {
      let empList = this.originalList.filter((el) => el.Status === "InActive");
      return empList.length;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    itemActions() {
      if (this.formAccess?.update) return ["Edit"];
      else return [];
    },
    checkAccess() {
      let havingAccess = {};
      havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      return havingAccess;
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action === "Edit") {
        this.isEdit = true;
        this.showAddEditForm = true;
      }
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        if (tab === this.organizationGroupFormName) {
          tab = "Organization Group";
        }
        this.listLoading = true;
        const { formAccess } = this.orgStructureFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();

        this.itemList = this.originalList.filter((item) => {
          // Check top-level properties
          let matchesTopLevel = Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );

          // Check `designations` array
          let matchesDesignations = item.designations?.some((d) =>
            d.Designation_Name.toLowerCase().includes(searchValue)
          );

          // Check `accreditations` array
          let matchesAccreditations = item.accreditations?.some(
            (a) =>
              a.Accreditation_Type.toLowerCase().includes(searchValue) ||
              a.Accreditation_Category.toLowerCase().includes(searchValue)
          );

          return (
            matchesTopLevel || matchesDesignations || matchesAccreditations
          );
        });
      }
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
    },
    constructDesignations(designations) {
      if (designations?.length) {
        return designations.map((el) => `${el.Designation_Name}\n`).join(", ");
      } else {
        return "-";
      }
    },
    constructAccreditation(accreditations) {
      if (accreditations?.length) {
        return accreditations
          .map(
            (el) =>
              `Accreditation Type: ${el.Accreditation_Type}\nAccreditation Category: ${el.Accreditation_Category}\n\n`
          )
          .join(", "); // Adds extra space between entries
      } else {
        return "-";
      }
    },
    onMoreAction(actionType) {
      if (actionType.key === "Export") {
        this.exportReportFile(actionType);
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        accreditations: el.accreditations
          ?.map(
            (el) => `${el.Accreditation_Type} - ${el.Accreditation_Category}`
          )
          ?.join(", "),
        designations: el.designations
          ?.map((el) => el.Designation_Name)
          ?.join(", "),
        Added_On: el.Added_On ? this.convertUTCToLocal(el.Added_On) : "",
        Updated_On: el.Updated_On ? this.convertUTCToLocal(el.Updated_On) : "",
      }));
      let headers = [];
      if (this.labelList[426]?.Field_Visiblity?.toLowerCase() === "yes")
        headers.push({
          header: `${this.labelList[426]?.Field_Alias || "Job Role Code"}`,
          key: "Job_Role_Code",
        });

      headers.push(
        { header: "Job Role", key: "Job_Role" },
        { header: "Associate Training/Accreditation", key: "accreditations" },
        { header: "Designation", key: "designations" },
        { header: "Status", key: "Status" },
        { header: "Description", key: "Description" },
        { header: "Created On", key: "Added_On" },
        { header: "Created By", key: "Added_By_Name" },
        { header: "Updated On", key: "Updated_On" },
        { header: "Updated By", key: "Updated_By_Name" }
      );
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: headers,
      };
      this.exportExcelFile(exportOptions);
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openAddForm() {
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_JOB_ROLES,
          client: "apolloClientAZ",
          variables: { formId: 329 },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobRoles &&
            response.data.listJobRoles.jobRoles &&
            !response.data.listJobRoles.errorCode
          ) {
            let tempData = JSON.parse(response.data.listJobRoles.jobRoles);
            // Ensure tempData is an array before mapping
            if (Array.isArray(tempData) && tempData?.length) {
              tempData = tempData.map((jobRole) => ({
                ...jobRole,
                Designation_Ids: JSON.parse(jobRole?.Designation_Ids) || [],
                Accreditation_Category_And_Type_Ids:
                  JSON.parse(jobRole?.Accreditation_Category_And_Type_Ids) ||
                  [],
              }));
            }
            vm.itemList = tempData;
            vm.originalList = tempData;
            vm.onApplySearch();
            vm.listLoading = false;
          } else {
            vm.handleListError(response.data.listJobRoles?.errorCode || "");
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },

    updateStatus(statusVal, item) {
      let vm = this;
      vm.listLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_JOB_ROLE,
            variables: {
              Form_Id: 329,
              Job_Role_Id: item?.Job_Role_Id,
              Job_Role: item?.Job_Role,
              Job_Role_Code: item?.Job_Role_Code || null,
              Accreditation_Category_And_Type_Ids:
                item.Accreditation_Category_And_Type_Ids,
              Designation_Ids: item.Designation_Ids,
              Status: statusVal[1] ? "Active" : "InActive",
              Description: item?.Description,
            },
            client: "apolloClientBB",
          })
          .then(() => {
            vm.listLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.landedFormName + " status updated successfully",
            };
            vm.showAlert(snackbarData);
            vm.refetchList("status-updated");
          })
          .catch((err) => {
            vm.handleAddUpdateError(err);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.listLoading = false;
      // Check if the error contains GraphQL errors and extensions
      if (err?.graphQLErrors && err.graphQLErrors[0]?.extensions) {
        const graphQLError = err.graphQLErrors[0];

        // Handle associated forms error (if any)
        const associatedForms = graphQLError.extensions.associatedForms;
        if (associatedForms && associatedForms.length > 0) {
          let errorMessage =
            "Operation failed as the following forms are associated with this job roles:\n\n";
          associatedForms.forEach((form, index) => {
            // Add a comma for all but the last item
            if (index === associatedForms.length - 1) {
              errorMessage += ` ${form}.`; // Add a period for the last item
            } else {
              errorMessage += ` ${form},\n`; // Add a comma and newline for other items
            }
          });
          errorMessage +=
            "\nPlease ensure the forms are reassigned or removed before proceeding.";

          // Show alert with the associated forms
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: errorMessage,
          });
          return; // Exit the function after handling associated forms error
        }
      }
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: this.landedFormName,
        isListError: false,
      });
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
