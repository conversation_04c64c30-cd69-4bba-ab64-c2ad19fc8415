<template>
  <div class="mt-3 mx-5" style="height: calc(100vh - 180px)">
    <div v-if="listLoading" class="mt-3">
      <!-- Skeleton loaders -->
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList && !listLoading"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      :button-text="'Retry'"
      @button-click="refetchList()"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="itemList.length === 0"
      key="no-results-screen"
      :main-title="'There are no records for the selected filters/searches.'"
      :image-name="
        originalList?.length === 0
          ? 'workflow/empty-approval'
          : 'common/no-records'
      "
      :isSmallImage="originalList.length === 0"
    >
      <template #contentSlot>
        <div class="d-flex mb-2 flex-wrap justify-center align-center">
          <v-btn
            class="bg-white my-2 ml-2"
            :style="'width: max-content'"
            :size="isMobileView ? 'small' : 'default'"
            rounded="lg"
            @click="$refs.datePicker.fp.open()"
          >
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            <span class="text-caption px-1">Date:</span>
            <flat-pickr
              ref="datePicker"
              v-model="appliedDateRange"
              :config="flatPickerOptions"
              placeholder="Select Date Range"
              class="ml-2 mt-1 date-range-picker-custom-bg"
              style="outline: 0px; color: var(--v-primary-base); width: 170px"
              @onChange="onChangeDateRange"
            ></flat-pickr>
          </v-btn>
          <v-btn
            v-if="originalList.length === 0"
            color="transparent"
            class="mt-1"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
          <v-btn
            v-if="originalList.length > 0"
            color="primary"
            variant="elevated"
            class="ml-4 mt-1"
            rounded="lg"
            :size="isMobileView ? 'small' : 'default'"
            @click="$emit('reset-filter')"
          >
            Reset Filter/Search
          </v-btn>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <div
        v-if="originalList.length"
        :class="{
          'mb-3': !isMobileView,
          'd-flex': true,
          'justify-space-between': !isMobileView,
          'align-center': true,
          'flex-column': isMobileView,
        }"
      >
        <v-btn
          class="bg-white mr-2"
          :class="isMobileView ? 'mb-2' : ''"
          :style="'width: max-content'"
          :size="isMobileView ? 'small' : 'default'"
          rounded="lg"
          @click="$refs.datePicker.fp.open()"
        >
          <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
          <span class="text-caption px-1">Date:</span>
          <flat-pickr
            ref="datePicker"
            v-model="appliedDateRange"
            :config="flatPickerOptions"
            placeholder="Select Date Range"
            class="ml-2 mt-1 date-range-picker-custom-bg"
            style="outline: 0px; color: var(--v-primary-base); width: 170px"
            @onChange="onChangeDateRange"
          ></flat-pickr>
        </v-btn>
        <div :class="isMobileView ? 'd-flex flex-column' : 'd-flex'">
          <v-btn
            color="primary"
            :class="isMobileView ? 'mb-2' : ''"
            class="mr-2"
            rounded="lg"
            @click="handleInitiateAction()"
          >
            Initiate Leave as per policy
          </v-btn>
          <v-btn
            color="primary"
            :class="isMobileView ? 'mb-2' : ''"
            class="mr-2"
            rounded="lg"
            @click="handleClickIgnore()"
          >
            Ignore
          </v-btn>
          <div class="d-flex align-center justify-center">
            <v-btn
              color="transparent"
              class="mt-1"
              variant="flat"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu v-model="openMoreMenu" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
                  <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                  <v-icon v-else>fas fa-caret-up</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in [
                    { key: 'Export', icon: 'fas fa-file-export' },
                  ]"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                      >
                        {{ action.key }}
                      </v-list-item-title>
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
      </div>
      <v-data-table
        v-model="selectedRecords"
        :headers="tableHeaders"
        :items="itemList"
        item-value="unique_id"
        fixed-header
        :show-select="!isMobileView"
        :height="$store.getters.getTableHeightBasedOnScreenSize(290, itemList)"
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          {
            value: -1,
            title: '$vuetify.dataFooter.itemsPerPageAll',
          },
        ]"
      >
        <template v-slot:[`header.data-table-select`]="{ selectAll }">
          <div class="d-flex justify-center align-center">
            <v-checkbox-btn
              v-model="selectAllBox"
              color="primary"
              false-icon="far fa-circle"
              true-icon="fas fa-check-circle"
              :indeterminate="
                selectedItems.length !== 0 &&
                selectedItems.length !== itemList.length
              "
              indeterminate-icon="fas fa-minus-circle"
              @change="selectAll(selectAllBox)"
            ></v-checkbox-btn>
          </div>
        </template>
        <template v-slot:item="{ item }">
          <tr
            class="data-table-tr bg-white"
            :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
          >
            <td :class="isMobileView ? 'mt-3 mb-n5' : ''">
              <v-checkbox-btn
                v-model="item.isSelected"
                color="primary"
                false-icon="far fa-circle"
                true-icon="fas fa-check-circle"
                class="mt-n2 ml-n2"
                @click.stop="
                  {
                  }
                "
                @change="checkAllSelected()"
              ></v-checkbox-btn>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Employee</div>
              <section>
                <div style="max-width: 200px" class="text-truncate">
                  <span class="text-primary text-body-2 font-weight-medium">
                    <v-tooltip :text="item.employee_name" location="bottom">
                      <template v-slot:activator="{ props }">
                        <span
                          v-bind="
                            item.employee_name && item.employee_name.length > 20
                              ? props
                              : ''
                          "
                          >{{ checkNullValue(item.employee_name) }}</span
                        >
                      </template>
                    </v-tooltip>
                    <v-tooltip
                      :text="item.user_defined_empid?.toString()"
                      location="bottom"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          v-if="item.user_defined_empid"
                          v-bind="
                            item.user_defined_empid &&
                            item.user_defined_empid.length > 20
                              ? props
                              : ''
                          "
                          class="text-grey"
                        >
                          {{ checkNullValue(item.user_defined_empid) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </span>
                </div>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Date</div>
              <section>
                {{ checkNullValue(item.d_absent_date) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Duration</div>
              <section>
                {{ checkNullValue(item.leave_duration) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Leave Period
              </div>
              <section>
                {{ checkNullValue(item.leave_period) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Attendance Hours
              </div>
              <section>
                {{ checkNullValue(item.attendance_hours) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Shortage Hours
              </div>
              <section>
                {{ checkNullValue(item.deviation_hours) }}
              </section>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="initiateModel"
    :open-modal="initiateModel"
    :confirmation-heading="'Are you sure you want to proceed with initiating leave for shortage on the selected records as per the policy?'"
    :icon-name="''"
    :icon-color="'red'"
    :icon-Size="'75'"
    @close-warning-modal="initiateModel = false"
    @accept-modal="initiateShortagePolicy()"
  />
  <AppWarningModal
    v-if="ignoreModel"
    :open-modal="ignoreModel"
    :confirmation-heading="''"
    :icon-name="'fas fa-ban'"
    :icon-color="'red'"
    closeButtonText="No"
    acceptButtonText="Yes"
    icon-Size="75"
    @close-warning-modal="ignoreModel = false"
    @accept-modal="initiateIgnoreShortage()"
  >
    <template v-slot:warningModalContent>
      <div
        class="text-h6 justify-center font-weight-bold text-primary text-center px-6 pt-2"
      >
        Are you sure to proceed with this action?
      </div>
      <div class="text-center mt-3 text-grey">
        Attendance shortage is applicable as per the policy, but clicking the
        ignore option will exclude the selected record(s) from leave deduction.
      </div>
    </template>
  </AppWarningModal>
</template>
<script>
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import moment from "moment";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "AttendanceShortage",
  components: { flatPickr },
  mixins: [FileExportMixin],
  emits: ["reset-filter", "list-count", "show-shortage-tab"],
  props: {
    salaryStartDate: { type: String, required: true },
    salaryEndDate: { type: String, required: true },
    filterObj: { type: Object, default: () => ({}) },
    filterAppliedCount: { type: Number, default: 0 },
    preReq: { type: Number, default: 0 },
    payslipEmployeeIds: { type: Array, default: () => [] },
  },
  data() {
    return {
      listLoading: false,
      originalList: [],
      itemList: [],
      isErrorInList: false,
      errorContent: "Something went wrong. Please try after some time.",
      appliedDateRange: null,
      startDate: "",
      endDate: "",
      openMoreMenu: false,
      selectAllBox: false,
      selectedItems: [],
      selectedRecords: [],
      isLoading: false,
      ignoreModel: false,
      initiateModel: false,
    };
  },
  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment().format(this.$store.state.orgDetails.orgDateFormat),
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    tableHeaders() {
      return [
        {
          title: "Employee",
          align: "start",
          key: "employee_name",
        },
        {
          title: "Date",
          key: "d_absent_date",
        },
        {
          title: "Duration",
          key: "leave_duration",
        },
        {
          title: "Leave Period",
          key: "leave_period",
        },
        {
          title: "Attendance Hours",
          key: "attendance_hours",
        },
        {
          title: "Shortage Hours",
          key: "deviation_hours",
        },
      ];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(357);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
  },
  watch: {
    salaryStartDate(val) {
      if (val) {
        this.startDate = val;
        this.setDateRange();
      }
    },
    salaryEndDate(val) {
      if (val) {
        this.endDate = val;
        this.setDateRange();
      }
    },
    selectedRecords(selRecords) {
      if (this.selectAllBox) {
        const selSet = new Set(selRecords);

        this.itemList.forEach((item) => {
          item.isSelected = selSet.has(item.unique_id);
        });
        this.selectedItems = this.itemList.filter((item) =>
          selSet.has(item.unique_id)
        );
      } else {
        // Iterate through itemLogList
        this.itemList.forEach((item) => {
          item.isSelected = false;
        });
        this.selectedItems = [];
      }
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
    filterAppliedCount(val) {
      if (val) {
        this.applyFilter();
      } else {
        this.itemList = this.originalList;
      }
    },
    itemList: {
      handler(newList) {
        this.$emit("list-count", newList.length);
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.setDateRange();
  },
  // Reset the itemList to the original list when the count is zero
  methods: {
    checkNullValue,
    setDateRange() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      this.appliedDateRange =
        moment(this.salaryStartDate).format(orgDateFormat) +
        " to " +
        moment(this.salaryEndDate).format(orgDateFormat);
      this.fetchNoAttendanceList();
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
      }
    },
    applyFilter() {
      if (this.filterAppliedCount) {
        let filteredArray = this.originalList;
        if (this.filterObj.selectedDepartment.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedDepartment.includes(
              item.department_name
            );
          });
        }
        if (this.filterObj.selectedLocation.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedLocation.includes(item.location_name);
          });
        }
        if (this.filterObj.selectedServiceProvider.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedServiceProvider.includes(
              item.approval_status
            );
          });
        }
        if (this.filterObj.selectedEmpType.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedEmpType.includes(item.location_name);
          });
        }
        this.itemList = filteredArray;
      }
    },
    convertHMToSecs(hmTime) {
      if (!hmTime) return 0; // Handle null, undefined, or 0

      if (typeof hmTime === "number") return hmTime; // Already in seconds

      if (
        typeof hmTime === "string" &&
        (hmTime.includes(":") || hmTime.includes("."))
      ) {
        let delimiter = hmTime.includes(":") ? ":" : ".";
        let timeSplit = hmTime.split(delimiter).map(Number);

        if (
          timeSplit.length === 2 &&
          !isNaN(timeSplit[0]) &&
          !isNaN(timeSplit[1])
        ) {
          return timeSplit[0] * 3600 + timeSplit[1] * 60; // Convert to seconds
        }
      }

      let parsedTime = parseInt(hmTime, 10);
      return isNaN(parsedTime) ? 0 : parsedTime; // Return integer if valid, otherwise 0
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.selectedItems = [];
      this.selectAllBox = false;
      this.fetchNoAttendanceList();
    },
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectedItems = selectedItems;
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    async fetchNoAttendanceList() {
      if (!this.startDate || !this.endDate) return;
      let vm = this;
      vm.listLoading = true;
      try {
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/attendance-finalization/list-attendance-finalization",
          type: "POST",
          dataType: "json",
          data: {
            actualSubTab: "shortageTab",
            department: [],
            employeeId: this.preReq ? this.payslipEmployeeIds : [],
            employeeType: [],
            endDate: this.endDate,
            finalizationMethod: "noAttendance",
            location: [],
            startDate: this.startDate,
            status: [],
          },
        };
        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          if (response.noAttendanceData?.aaData?.length) {
            let shortageEmployees = response.noAttendanceData.aaData.filter(
              (el) => vm.convertHMToSecs(el.attendance_hours) > 0
            );
            shortageEmployees = shortageEmployees.map((item, index) => {
              return {
                ...item,
                unique_id: index + 1,
              };
            });
            vm.originalList = shortageEmployees;
            vm.itemList = shortageEmployees;
          } else {
            vm.originalList = [];
            vm.itemList = [];
          }
        } else {
          vm.originalList = [];
          vm.itemList = [];
          vm.handleFetchAttendanceError();
        }
      } catch (error) {
        vm.originalList = [];
        vm.itemList = [];
        vm.handleFetchAttendanceError(error);
      } finally {
        vm.listLoading = false;
      }
    },
    handleFetchAttendanceError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Shortage",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    onChangeDateRange(selectedDates) {
      if (selectedDates.length > 1) {
        // Parse the dates from the given format
        let parsedStartDate = moment(selectedDates[0], "DD/MM/YYYY");
        let parsedEndDate = moment(
          selectedDates.length > 1 ? selectedDates[1] : selectedDates[0],
          "DD/MM/YYYY"
        );

        // Format the dates into "YYYY-MM-DD" format
        this.startDate = parsedStartDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.fetchNoAttendanceList();
      }
    },
    handleClickIgnore() {
      if (this.selectedItems.length) {
        if (this.formAccess.optionalChoice) {
          this.ignoreModel = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please select at least one record.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    async initiateIgnoreShortage() {
      let vm = this;
      vm.isLoading = true;
      vm.ignoreModel = false;
      let shortageList = vm.selectedItems.map((attShortageRec) => ({
        Employee_Id: attShortageRec.employee_id,
        Attendance_Date: attShortageRec.absent_date,
        Shortage_Hours: attShortageRec.actual_deviation_hours,
      }));
      try {
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/attendance-finalization/initiate-ignore-attendance-shortage/",
          type: "POST",
          dataType: "json",
          data: {
            ignoreAttShortageEmp: shortageList,
          },
        };
        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          let snackbarData = {
            isOpen: true,
            message: "Attendance record(s) ignored successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.selectedItems = [];
          vm.fetchNoAttendanceList();
        } else {
          if (response?.msg) {
            let snackbarData = {
              isOpen: true,
              message: response.msg,
              type: "warning",
            };
            vm.showAlert(snackbarData);
          } else {
            vm.handleIgnoreActionError();
          }
        }
      } catch (error) {
        vm.handleIgnoreActionError(error);
      } finally {
        vm.isLoading = false;
      }
    },
    handleIgnoreActionError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "initiating",
        form: "Attendance (Incomplete)",
        isListError: false,
      });
    },
    handleInitiateAction() {
      if (this.selectedItems.length) {
        if (this.formAccess.optionalChoice) {
          this.initiateModel = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please select at least one record.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    async initiateShortagePolicy() {
      let vm = this;
      vm.isLoading = true;
      vm.initiateModel = false;
      let shortageList = this.selectedItems.map((noAttendanceRec) => ({
        Absent_Date: noAttendanceRec.absent_date,
        Contact_Details: noAttendanceRec.contact_details,
        Employee_Id: noAttendanceRec.employee_id,
        Hours: noAttendanceRec.hours,
        Leave_Duration: noAttendanceRec.leave_duration,
        Leave_Period: noAttendanceRec.leave_period,
      }));
      try {
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/attendance-finalization/initiate-attendance-shortage-leave/",
          type: "POST",
          dataType: "json",
          data: {
            noAttendanceDetails: shortageList,
          },
        };
        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          let snackbarData = {
            isOpen: true,
            message:
              "Attendance record(s) are initiated as per the attendance shortage configuration successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.selectedItems = [];
          vm.fetchNoAttendanceList();
        } else {
          if (response?.msg) {
            let snackbarData = {
              isOpen: true,
              message: response.msg,
              type: "warning",
            };
            vm.showAlert(snackbarData);
          } else {
            vm.handleInitiateActionError();
          }
        }
      } catch (error) {
        vm.handleInitiateActionError(error);
      } finally {
        vm.isLoading = false;
      }
    },
    handleInitiateActionError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "initiating",
        form: "shortage policy",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onMoreAction(actionType) {
      if (actionType?.toLowerCase() === "export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Employee Id", key: "user_defined_empid" },
        { header: "Employee", key: "employee_name" },
        { header: "Date", key: "d_absent_date" },
        { header: "Duration", key: "leave_duration" },
        { header: "Leave Period", key: "leave_period" },
        { header: "Attendance Hours", key: "attendance_hours" },
        { header: "Shortage Hours", key: "deviation_hours" },
      ];

      const exportOptions = {
        fileExportData: this.itemList,
        fileName: "Attendance Shortage",
        sheetName: "Attendance Shortage",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
