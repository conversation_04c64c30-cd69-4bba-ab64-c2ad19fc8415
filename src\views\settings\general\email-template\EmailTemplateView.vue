<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="onCloseView()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:50vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">
              View Email Template Details
            </div>
            <div class="d-flex align-center">
              <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
                <v-icon>fas fa-times</v-icon>
              </v-btn>
            </div>
          </v-card-title>
          <div
            v-if="formAccess?.update"
            class="d-flex justify-end align-center"
          >
            <v-btn
              @click="onEditPosition()"
              class="mr-3 mt-3 bg-white text-primary"
              variant="text"
              rounded="lg"
            >
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <div class="px-6">
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Template Name
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{
                      checkNullValue(selectedEmailTemplateData.Template_Name)
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Form</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedEmailTemplateData.Form_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Category</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{
                      checkNullValue(selectedEmailTemplateData.Category_Name)
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Category Type
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{
                      checkNullValue(
                        selectedEmailTemplateData.Category_Type_Name
                      )
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Default Email Template
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{
                      checkNullValue(selectedEmailTemplateData.Default_Template)
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Visibility</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedEmailTemplateData.Visibility) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">To</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(formattedToEmails) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Cc</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(formattedCcEmails) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Bcc</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(formattedBccEmails) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Additional Emails
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(formattedAdditionalEmails) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  External Emails
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(formattedExternalEmails) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="12" md="12" class="px-md-6">
                <div class="border-md pa-4">
                  <span class="font-weight-bold">Sender Name : </span>
                  <span class="text-body-2">
                    {{ checkNullValue(selectedEmailTemplateData.Sender_Name) }}
                  </span>
                  <div>
                    <span class="font-weight-bold">Subject : </span>
                    <span class="text-body-2">
                      {{
                        checkNullValue(
                          selectedEmailTemplateData.Subject_Content
                        )
                      }}
                    </span>
                  </div>
                  <div ref="editorView" class="quill-editorView"></div>
                </div>
              </v-col>
              <v-col
                cols="12"
                v-if="emailTemplateAttachments.length > 0"
                class="px-md-6 pb-0"
              >
                <p class="text-subtitle-1 font-weight-bold">Attachments(s)</p>
                <p
                  v-for="(doc, index) in emailTemplateAttachments"
                  :key="index"
                  class="text-subtitle-1 font-weight-regular text-blue cursor-pointer mb-2"
                  style="text-decoration: underline"
                  @click="viewEmailAttachments(index)"
                >
                  {{ formattedFileName(doc.filePath) }}
                </p>
              </v-col>
              <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails> </v-col
              ></v-row>
            </v-row></div></v-card></template
    ></v-overlay>
    <FilePreviewModal
      v-if="showEmailAttachments"
      :fileName="attachmentFileName"
      :folderName="'Email Template Document Upload'"
      :current="selectedDocumentIndex"
      :length="emailTemplateAttachments.length"
      @prev-document="changeSelectedDocument(-1)"
      @next-document="changeSelectedDocument(1)"
      @close-preview-modal="showEmailAttachments = false"
    ></FilePreviewModal>
  </div>
</template>

<script>
import { checkNullValue, convertUTCToLocal } from "@/helper";
import { defineAsyncComponent } from "vue";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
import moment from "moment";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
export default {
  name: "EmailTemplateView",
  emits: ["close-view-details", "edit-email-template"],
  props: {
    selectedEmailTemplateData: {
      type: Object,
      required: true,
    },
    enableView: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      showViewForm: false,
      moreDetailsList: [],
      openMoreDetails: true,
      showEmailAttachments: false,
      attachmentFileName: "",
      emailTemplateAttachments: [],
      selectedDocumentIndex: null,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formattedToEmails() {
      const emailsArray = JSON.parse(this.selectedEmailTemplateData?.To_Emails);
      return emailsArray.join(", ");
    },
    formattedCcEmails() {
      const emailsArray = JSON.parse(this.selectedEmailTemplateData?.CC_Emails);
      return emailsArray.join(", ");
    },
    formattedBccEmails() {
      const emailsArray = JSON.parse(
        this.selectedEmailTemplateData?.Bcc_Emails
      );
      return emailsArray.join(", ");
    },
    formattedAdditionalEmails() {
      const emailsArray = JSON.parse(
        this.selectedEmailTemplateData?.Additional_Emails
      ).map((item) => item.email);
      return emailsArray.join(", ");
    },
    formattedExternalEmails() {
      const emailsArray = this.selectedEmailTemplateData?.External_Emails
        ?.length
        ? JSON.parse(this.selectedEmailTemplateData?.External_Emails).map(
            (item) => item
          )
        : [];
      return emailsArray.join(", ");
    },
    formAccess() {
      let formAccess = this.accessRights("310");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formattedFileName() {
      return (fileName) => {
        if (fileName) {
          return fileName.split("?")[3];
        }
      };
    },
  },
  mounted() {},
  watch: {
    enableView(val) {
      this.showViewForm = val;
      let parsedEmailTemplateAttachments = this.selectedEmailTemplateData
        .Attachments
        ? JSON.parse(this.selectedEmailTemplateData.Attachments)
        : null;
      this.emailTemplateAttachments = parsedEmailTemplateAttachments
        ? parsedEmailTemplateAttachments
        : [];
      this.emailTemplateAttachments = this.emailTemplateAttachments.map(
        (file) => ({
          filePath: file,
        })
      );
      this.prefillMoreDetails();
    },
  },
  components: {
    MoreDetails,
    FilePreviewModal,
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    onCloseView() {
      this.showViewForm = false;
      this.$emit("close-view-details");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(
          this.selectedEmailTemplateData?.Added_On
        ),
        addedByName = this.selectedEmailTemplateData.Added_By_Name,
        updatedByName = this.selectedEmailTemplateData.Updated_By_Name,
        updatedOn = this.convertUTCToLocal(
          this.selectedEmailTemplateData.Updated_On
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
      setTimeout(() => {
        this.initQuillEditor();
      }, 0);
    },
    onEditPosition() {
      this.showViewForm = false;
      this.$emit("edit-email-template");
    },
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editorView, {
        theme: "snow",
      });
      this.setEditorFontSize("14px");
      this.quill.root.innerHTML =
        this.selectedEmailTemplateData?.Template_Content;
      this.quill.enable(false);
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editorView.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    viewEmailAttachments(index) {
      this.selectedDocumentIndex = index;
      this.attachmentFileName = this.emailTemplateAttachments[index].filePath;
      this.showEmailAttachments = true;
    },
    changeSelectedDocument(step) {
      this.selectedDocumentIndex += step;
      if (
        this.selectedDocumentIndex >= 0 &&
        this.selectedDocumentIndex < this.emailTemplateAttachments.length
      ) {
        this.attachmentFileName =
          this.emailTemplateAttachments[this.selectedDocumentIndex].filePath;
      }
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
:deep(.ql-toolbar.ql-snow) {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
:deep(.ql-editor) {
  font-family: Roboto, sans-serif !important;
}
</style>
