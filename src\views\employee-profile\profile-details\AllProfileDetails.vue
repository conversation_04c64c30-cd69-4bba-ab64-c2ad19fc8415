<template>
  <div>
    <ProfileCard class="my-5">
      <FormTab :model-value="activeTab" grow :hide-slider="true">
        <v-tab
          v-for="tab in profileTabs"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          @click="onChangeProfileTabs(tab.value)"
        >
          <div
            :class="[
              isActiveTab(tab)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            {{ tab.label }}
            <div
              v-if="isActiveTab(tab)"
              class="mt-3 mb-n4"
              style="border-bottom: 4px solid; width: 10rem; margin: 0 auto"
            ></div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>

    <ProfileCard
      v-if="isMounted"
      class="my-5"
      v-on:scroll="handleScroll"
      ref="profileContent"
    >
      <div
        v-if="!personalOpenedEditForm && activeTab === 'Personal_Info'"
        class="d-flex pl-4 pr-4 mb-n2"
        style="justify-content: end"
      >
        <!-- <v-btn variant="text" color="secondary" @click="goBackToList()">
          <v-icon class="pr-3">fas fa-angle-left fa-lg</v-icon>
          Back</v-btn
        > -->
        <v-icon @click="refetchCount += 1" size="17" color="grey" class="mt-2"
          >fas fa-redo-alt</v-icon
        >
      </div>
      <div class="profile-section-height">
        <v-card-text class="fill-height">
          <PersonalInfo
            v-if="activeTab === 'Personal_Info'"
            :selectedEmpId="empIdSelected"
            :formAccess="formAccess"
            :actionType="actionType"
            :callingFrom="callingFrom"
            :selectedEmpStatus="selectedEmpStatus"
            :empFormUpdateAccess="empFormUpdateAccess"
            :refetchCount="refetchCount"
            @selected-emp-dob="selectedEmployeeDob = $event"
            @close-add-form="$emit('close-add-form')"
            @details-retrieved="enableTabBasedOnData(1, $event)"
            @details-updated="$emit('details-updated')"
            @selected-emp-id="onNewEmpAdd($event)"
            @opened-edit-form="personalOpenedEditForm = $event"
          />
          <JobInfo
            v-else-if="activeTab === 'Job_Info'"
            :selectedEmpId="empIdSelected"
            :formAccess="formAccess"
            :selectedEmployeeDob="selectedEmployeeDob"
            :actionType="actionType"
            :callingFrom="callingFrom"
            :selectedEmployeeDetails="employeeSelected"
            :selectedEmpStatus="selectedEmpStatus"
            :myTeamList="myTeamList"
            :empFormUpdateAccess="empFormUpdateAccess"
            @details-retrieved="enableTabBasedOnData(2, $event)"
            @details-updated="$emit('details-updated')"
          />
          <ContactInfo
            v-else-if="activeTab === 'Contact_Info'"
            :selectedEmpId="empIdSelected"
            :formAccess="formAccess"
            :selectedEmpStatus="selectedEmpStatus"
            :actionType="actionType"
            :callingFrom="callingFrom"
            :empFormUpdateAccess="empFormUpdateAccess"
            @details-retrieved="enableTabBasedOnData(3, $event)"
            @details-updated="$emit('details-updated')"
          />
          <CareerInfo
            v-else-if="activeTab === 'Career_Info'"
            :selectedEmpId="empIdSelected"
            :formAccess="selectedEmpStatus === 'Active' ? formAccess : false"
            :selectedEmployeeDob="selectedEmployeeDob"
            :callingFrom="callingFrom"
            :empFormUpdateAccess="empFormUpdateAccess"
            @details-retrieved="enableTabBasedOnData(4, [$event])"
          />
          <DocumentAndAccreditation
            v-else-if="activeTab === 'Document/Accreditations'"
            :selectedEmpId="empIdSelected"
            :formAccess="selectedEmpStatus === 'Active' ? formAccess : false"
            :callingFrom="callingFrom"
            :empFormUpdateAccess="empFormUpdateAccess"
            :selectedEmployeeDob="selectedEmployeeDob"
            @details-retrieved="enableTabBasedOnData(5, [$event])"
          />
          <Others
            v-else-if="activeTab === 'Other'"
            :selectedEmpId="empIdSelected"
            :selectedEmpStatus="selectedEmpStatus"
            :formAccess="formAccess"
            :actionType="actionType"
            :callingFrom="callingFrom"
            @details-retrieved="enableTabBasedOnData(5, [$event])"
          />
          <PayConfig
            v-else-if="activeTab === 'Pay_Config'"
            :selectedEmpId="empIdSelected"
            :selectedEmpStatus="selectedEmpStatus"
            :formAccess="payConfigAccess"
            :employeeData="employeeSelected"
            :actionType="actionType"
            :callingFrom="callingFrom"
          ></PayConfig>
          <EmployeeResignation
            v-else-if="activeTab === 'Separation'"
            :selectedEmpId="empIdSelected"
            :selectedEmpStatus="selectedEmpStatus"
            :formAccess="resignationAccess"
            @details-retrieved="enableTabBasedOnData(5, [$event])"
          />
          <AirTicket
            v-else-if="activeTab === 'aitTicketPolicy'"
            :selectedEmpId="empIdSelected"
            :formAccess="
              selectedEmpStatus?.toLowerCase() === 'active'
                ? airTicketingAccess
                : false
            "
            :selectedEmpStatus="selectedEmpStatus"
            :actionType="actionType"
            :callingFrom="callingFrom"
            @details-retrieved="enableTabBasedOnData(5, [$event])"
          />
        </v-card-text>
      </div>
    </ProfileCard>
    <!-- Scroll to bottom button -->
    <v-btn
      v-if="showScrollButton"
      @click="scrollToBottom"
      class="scroll-to-bottom-button"
      variant="outlined"
      icon="fas fa-chevron-down"
      size="x-small"
    >
    </v-btn>
  </div>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="changeTab()"
  ></AppWarningModal>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import PersonalInfo from "./personal/PersonalInfo.vue";
const JobInfo = defineAsyncComponent(() => import("./job/JobInfo.vue"));
const ContactInfo = defineAsyncComponent(() =>
  import("./contact/ContactInfo.vue")
);
const CareerInfo = defineAsyncComponent(() =>
  import("./career/CareerInfo.vue")
);
const DocumentAndAccreditation = defineAsyncComponent(() =>
  import("./document-accreditations/DocumentAndAccreditation.vue")
);
const Others = defineAsyncComponent(() => import("./others/Others.vue"));
const PayConfig = defineAsyncComponent(() =>
  import("./pay-config/PayConfig.vue")
);
const EmployeeResignation = defineAsyncComponent(() =>
  import("./exit-management/EmployeeResignation.vue")
);
const AirTicket = defineAsyncComponent(() =>
  import("./air-ticket-policy/AirTicket.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default defineComponent({
  name: "AllProfileDetails",
  components: {
    PersonalInfo,
    JobInfo,
    ContactInfo,
    CareerInfo,
    DocumentAndAccreditation,
    Others,
    PayConfig,
    EmployeeResignation,
    AirTicket,
  },
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    selectedEmpDateOfBirth: {
      type: String,
      default: "",
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    selectedEmployeeDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    myTeamList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    empChangeCount: {
      type: Number,
      default: 0,
    },
    showTab: {
      type: String,
      default: "Personal_Info",
    },
  },
  emits: [
    "close-add-form",
    "employee-id-retrieved",
    "details-updated",
    "employee-details-updated",
    "close-profile",
  ],
  data() {
    return {
      activeTab: "Personal_Info",
      profileTabs: [],
      selectedEmployeeDob: "",
      empIdSelected: 0,
      isMounted: false,
      openWarningModal: false,
      changedTab: "",
      employeeSelected: {},
      showScrollButton: false,
      personalOpenedEditForm: "",
      refetchCount: 0,
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    myTeamAccess() {
      let formAccess = this.accessRights("243");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    payrollAdminAccess() {
      let formAccess = this.accessRights("149");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["update"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    payConfigAccess() {
      let formAccess = this.accessRights("263");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    resignationAccess() {
      let formAccess = this.accessRights("292");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    airTicketingAccess() {
      let formAccess =
        this.callingFrom === "profile"
          ? this.accessRights("323")
          : this.accessRights("322");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    myTeamTabs() {
      let tabs = [
        {
          label: "Personal Info",
          value: "Personal_Info",
          disable: false,
        },
        {
          label: "Job Info",
          value: "Job_Info",
          disable: true,
        },
        {
          label: "Contact Info",
          value: "Contact_Info",
          disable: true,
        },
        {
          label: "Career Info",
          value: "Career_Info",
          disable: true,
        },
        {
          label:
            "Document/" +
            (this.labelList[450]?.Field_Alias || "Accreditations"),
          value: "Document/Accreditations",
          disable: true,
        },
        {
          label: "Bank & Other Info",
          value: "Other",
          disable: true,
        },
      ];
      if (this.airTicketingAccess) {
        tabs.push({
          label: "Air Ticket Policy",
          value: "aitTicketPolicy",
          disable: true,
        });
      }
      if (this.resignationAccess && this.callingFrom === "profile") {
        tabs.push({
          label: "Separation",
          value: "Separation",
          disable: true,
        });
      }
      return tabs;
    },
    payConfigTab() {
      return [
        {
          label: "Pay Configuration",
          value: "Pay_Config",
          disable: true,
        },
      ];
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  watch: {
    employeeSelected(val) {
      this.$emit("employee-details-updated", val);
    },
    empChangeCount(count) {
      if (count > 0) {
        this.assignValues(true);
      }
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.assignValues();
    if (this.showTab && this.showTab !== "Personal_Info") {
      this.activeTab = this.showTab;
    }
    this.isMounted = true;
  },
  methods: {
    assignValues(isEmpChanged = false) {
      this.empIdSelected = this.selectedEmpId;
      this.selectedEmployeeDob = this.selectedEmpDateOfBirth;
      this.employeeSelected = this.selectedEmployeeDetails;
      if (this.callingFrom === "profile") {
        this.profileTabs = this.myTeamTabs;
        this.enableAllTabs();
      } else {
        let firstTabArray = [],
          secondTabArray = [];
        // for payroll admin we should present pay config tab alone only we have access to pay config
        if (this.payConfigAccess && this.payrollAdminAccess) {
          secondTabArray = this.payConfigTab;
          this.activeTab = "Pay_Config";
        } else {
          // for other admins we can present pay config if we have access
          if (
            this.payConfigAccess &&
            this.myTeamAccess &&
            this.myTeamAccess.admin === "admin"
          ) {
            secondTabArray = this.payConfigTab;
            this.activeTab = "Pay_Config";
          }
          // present the remaining 5 tabs when they have team-summary access
          if (this.myTeamAccess) {
            firstTabArray = this.myTeamTabs;
            this.activeTab = "Personal_Info";
          }
        }
        this.profileTabs = firstTabArray.concat(secondTabArray);
        // if contact details exist, then enable all the tabs
        if (this.employeeSelected.Mobile_No || this.actionType === "edit") {
          this.enableAllTabs();
        } else {
          for (let i in this.profileTabs) {
            // if employeeId exist, then enable job details tab
            if (i == 1 && this.employeeSelected.employeeId) {
              this.profileTabs[i]["disable"] = false;
            }
            // if designation exist, enable contact details tab
            if (
              i == 2 &&
              this.employeeSelected.dateOfJoin &&
              this.employeeSelected.dateOfJoin !== "-"
            ) {
              this.profileTabs[i]["disable"] = false;
            }
          }
        }
      }
      if (this.empIdSelected) {
        this.$nextTick(() => {
          this.scrollToBottom(); // Optional: Scroll to bottom on component mount
        });
      }
      if (isEmpChanged) this.refetchCount += 1;
    },
    goBackToList() {
      mixpanel.track("EmpProfile-profileCard-backTo-team-list");
      this.$emit("close-profile");
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        "0-false"
      );
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
    },
    handleScroll() {
      const profileContentEl = this.$refs.profileContent;
      if (profileContentEl) {
        // Use the inner container for Vuetify components
        const innerContainer = profileContentEl.$el || profileContentEl;
        // Check if the scroll position is close to the bottom
        this.showScrollButton =
          innerContainer.scrollTop + innerContainer.clientHeight <
          innerContainer.scrollHeight;
      }
    },
    scrollToBottom() {
      const profileContentEl = this.$refs.profileContent;
      if (profileContentEl) {
        const innerContainer = profileContentEl.$el || profileContentEl;
        innerContainer.scrollTop = innerContainer.scrollHeight;
        this.handleScroll();
      }
    },
    isActiveTab(tab) {
      return this.activeTab === tab.value;
    },
    enableTabBasedOnData(index, type) {
      if (type && type.length === 2) {
        const combinedObj = { ...this.employeeSelected, ...type[1] };
        this.employeeSelected = combinedObj;
      }
      if (type[0] === "update") {
        this.$emit("details-updated");
        if (index >= 3) {
          this.enableAllTabs();
        } else {
          this.profileTabs[index]["disable"] = false;
        }
      }
    },
    onNewEmpAdd(empId) {
      this.empIdSelected = empId;
      this.scrollToBottom();
      this.$emit("employee-id-retrieved", empId);
    },
    enableAllTabs() {
      this.profileTabs = this.profileTabs.map((item) => {
        item["disable"] = false;
        return item;
      });
    },

    onChangeProfileTabs(val) {
      let isEditFormChanged =
        this.$store.state.employeeProfile.isEditFormChanged;
      if (isEditFormChanged) {
        this.changedTab = val;
        this.openWarningModal = true;
      } else {
        this.activeTab = val;
      }
      mixpanel.track("EmpProfile-tab-changed");
    },

    onCloseWarningModal() {
      this.changedTab = "";
      this.openWarningModal = false;
    },

    changeTab() {
      this.activeTab = this.changedTab;
      this.openWarningModal = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
    },
  },
});
</script>

<style scoped>
.scroll-to-bottom-button {
  position: fixed;
  bottom: 5%;
  right: 15%;
  cursor: pointer;
}
.profile-section-height {
  height: calc(100vh - 240px);
  overflow: hidden;
  overflow-y: scroll;
}

.profile-section-height :deep() .v-tabs--density-default {
  --v-tabs-height: 55px !important;
}
</style>
