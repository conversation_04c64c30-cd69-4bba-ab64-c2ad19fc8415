import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const GET_MICROSOFT_CREDENTIALS = gql`
  query getAWSCognitoIdentitie($Type: String!) {
    getAWSCognitoIdentities(Type: $Type) {
      errorCode
      message
      data {
        userPoolId
        clientSecret
        secretPassword
        workwiselymsapplicationID
        workwiselymstenantID
      }
    }
  }
`;

// ===============
// Mutations
// ===============

export const ADD_UPDATE_RECRUITMENT_STATUS = gql`
  mutation addUpdateRecruitmentStatus(
    $Integration_Id: Int!
    $Integration_Type: String!
    $Integration_Status: String!
    $Indeed_Client_Id: String
    $Indeed_Secret_Key: String
    $Indeed_Source_Name: String
    $Company_Id: String
    $Hirer_ID: String
  ) {
    addUpdateRecruitmentStatus(
      Integration_Id: $Integration_Id
      Integration_Type: $Integration_Type
      Integration_Status: $Integration_Status
      Indeed_Client_Id: $Indeed_Client_Id
      Indeed_Secret_Key: $Indeed_Secret_Key
      Indeed_Source_Name: $Indeed_Source_Name
      Company_Id: $Company_Id
      Hirer_ID: $Hirer_ID
    ) {
      errorCode
      message
    }
  }
`;
