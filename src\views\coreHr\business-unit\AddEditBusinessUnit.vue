<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">
            Business Unit / Cost Center</span
          >
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              class="mb-2 primary"
              variant="elevated"
              @click="validateBusinessUnitForm"
              >Save</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Save</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" color="primary" class="mr-1">
            fas fa-times
          </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="height: calc(100vh - 260px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="businessUnitForm">
            <v-row>
              <!-- Business Unit Level -->
              <v-col
                v-if="entomoIntegrationEnabled && isEntomoSyncTypePush"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="selectedLevel"
                  variant="solo"
                  :rules="[
                    required(`Level`, selectedLevel),
                    selectedLevel
                      ? validateWithRulesAndReturnMessages(
                          selectedLevel,
                          'level',
                          'Level',
                          true
                        )
                      : true,
                  ]"
                  type="number"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    Level
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>

              <!-- Business Unit Code -->
              <v-col
                v-if="entomoIntegrationEnabled"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="businessUnitCode"
                  variant="solo"
                  :rules="[
                    required(
                      labelList[318]?.Field_Alias || `Business Unit Code`,
                      businessUnitCode
                    ),
                    businessUnitCode
                      ? validateWithRulesAndReturnMessages(
                          businessUnitCode,
                          'businessUnitCode',
                          labelList[318]?.Field_Alias || `Business Unit Code`
                        )
                      : true,
                  ]"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    {{ labelList[318]?.Field_Alias || "Business Unit Code" }}
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-else-if="
                  labelList[318] &&
                  labelList[318].Field_Visiblity.toLowerCase() === 'yes'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="businessUnitCode"
                  variant="solo"
                  :rules="[
                    labelList[318].Mandatory_Field.toLowerCase() === 'yes'
                      ? required(
                          `${labelList[318].Field_Alias}`,
                          businessUnitCode
                        )
                      : true,
                    businessUnitCode
                      ? validateWithRulesAndReturnMessages(
                          businessUnitCode,
                          'businessUnitCode',
                          labelList[318].Field_Alias
                        )
                      : true,
                  ]"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    {{ labelList[318].Field_Alias }}
                    <span
                      v-if="
                        labelList[318].Mandatory_Field.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <CustomSelect
                  label="Parent Unit Code"
                  :items="businessList"
                  :itemSelected="parentUnitCode"
                  itemTitle="businessUnit"
                  itemValue="businessUnitId"
                  subText="businessUnitId"
                  subTextTitle="Id"
                  @selected-item="changeField('parentUnitCode', $event)"
                  variant="solo"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                </CustomSelect>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-text-field
                  v-model="businessUnit"
                  variant="solo"
                  :rules="[
                    required('Business Unit / Cost Center', businessUnit),
                    validateWithRulesAndReturnMessages(
                      businessUnit,
                      'businessUnit',
                      'Business Unit / Cost Center'
                    ),
                  ]"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    Business Unit / Cost Center
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2 d-flex align-center"
              >
                <div class="v-label mr-4">Status</div>
                <AppToggleButton
                  button-active-text="Active"
                  button-inactive-text="InActive"
                  button-active-color="#7de272"
                  button-inactive-color="red"
                  id-value="gab-analysis-based-on"
                  :current-value="status === 'Active' ? true : false"
                  :isDisableToggle="!isEdit"
                  @chosen-value="onChangeStatus($event)"
                ></AppToggleButton>
              </v-col>

              <v-col cols="12" class="px-md-6 pb-0 mb-2">
                <div class="v-label mb-4">Description</div>
                <v-textarea
                  v-model="description"
                  variant="solo"
                  auto-grow
                  rows="1"
                  :rules="[
                    validateWithRulesAndReturnMessages(
                      description,
                      'description',
                      'Description'
                    ),
                  ]"
                  @update:model-value="isFormDirty = true"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 primary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import { ADD_UPDATE_BUSINESS_UNIT_SETTINGS } from "@/graphql/settings/core-hr/businessUnitQueries.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "AddEditBusinessUnit",
  mixins: [validationRules],
  components: { CustomSelect },
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    businessList: {
      type: [Object, Array],
      required: true,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      // add/update
      selectedLevel: null,
      businessUnit: "",
      businessUnitCode: null,
      parentUnitCode: null,
      status: "Active",
      description: "",
      isFormDirty: false,
      // loading/error/other
      isLoading: false,
      openConfirmationPopup: false,
      validationMessages: [],
      showValidationAlert: false,
    };
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    if (this.isEdit) {
      const {
        businessUnitId,
        businessUnit,
        description,
        status,
        businessUnitCode,
        businessUnitParentId,
        level,
      } = this.editFormData;
      this.selectedLevel = level;
      this.businessUnitCode = businessUnitCode;
      this.parentUnitCode = businessUnitParentId;
      this.businessUnitId = businessUnitId ? businessUnitId : 0;
      this.businessUnit = businessUnit ? businessUnit : "";
      this.description = description ? description : "";
      this.status = status ? status : "Active";
    }
  },
  methods: {
    checkIfAlreadyPresent() {
      if (this.isEdit) {
        let newlist = this.businessList.filter(
          (ele) => ele.businessUnitId != this.editFormData.businessUnitId
        );
        for (let org of newlist) {
          if (org.businessUnitCode == this.businessUnitCode) {
            return false;
          }
        }
        return true;
      } else {
        for (let org of this.businessList) {
          if (org.businessUnitCode == this.businessUnitCode) {
            return false;
          }
        }
        return true;
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },

    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },

    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },

    // change the mode of performance management
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },

    async validateBusinessUnitForm() {
      const { valid } = await this.$refs.businessUnitForm.validate();
      if (valid) {
        if (this.checkIfAlreadyPresent()) {
          this.addUpdateBusinessUnit();
        } else {
          var snackbarData = {
            isOpen: true,
            type: "error",
            message: "Business Unit Code should be unique",
          };
          this.showAlert(snackbarData);
        }
      }
    },

    changeField(field, value) {
      this[field] = value;
    },

    addUpdateBusinessUnit() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_BUSINESS_UNIT_SETTINGS,
            variables: {
              businessUnitId: vm.businessUnitId
                ? parseInt(vm.businessUnitId)
                : 0,
              businessUnit: vm.businessUnit ? vm.businessUnit.toString() : "",
              description: vm.description ? vm.description : "",
              status: vm.status,
              isAdd: vm.isEdit ? 0 : 1,
              oldStatus: vm.editFormData ? vm.editFormData.status : "",
              businessUnitCode: vm.businessUnitCode
                ? vm.businessUnitCode.toString()
                : null,
              businessUnitParentId: vm.parentUnitCode,
              level: vm.selectedLevel ? parseInt(vm.selectedLevel) : null,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.isEdit
                ? "Business Unit / Cost Center configuration updated successfully."
                : "Business Unit / Cost Center configuration added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("edit-updated");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },

    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: "business unit / cost center configuration",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
