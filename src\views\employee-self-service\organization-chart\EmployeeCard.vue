<script>
export default {
  name: "EmployeeCard",
  props: {
    customData: {
      type: Object,
    },
  },
  data() {
    return {
      tempData: "",
    };
  },
};
</script>

<template>
  <div class="card-ui position-relative">
    <div class="bg"></div>
    <div
      class="position-absolute px-4 pt-4 pb-1"
      style="top: 0; left: 0; right: 0"
    >
      <div style="display: inline-flex">
        <svg
          height="30"
          width="30"
          fill="#4287f5"
          v-if="customData.org"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 448 512"
        >
          <path
            d="M128 148v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12zm140 12h40c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12zm-128 96h40c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12zm128 0h40c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12zm-76 84v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm76 12h40c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12zm180 124v36H0v-36c0-6.6 5.4-12 12-12h19.5V24c0-13.3 10.7-24 24-24h337c13.3 0 24 10.7 24 24v440H436c6.6 0 12 5.4 12 12zM79.5 463H192v-67c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v67h112.5V49L80 48l-.5 415z"
          />
        </svg>
        <img
          v-else
          style="border-radius: 50%; width: 40px; height: 40px"
          :src="
            customData.gender === 'Male'
              ? 'https://capricetest.hrapp.co.in/images/male-emp.png'
              : 'https://capricetest.hrapp.co.in/images/female-emp.png'
          "
          alt="form exit"
        />
        <div class="px-3">
          <div
            class="rounded-lg"
            :class="
              customData.org
                ? 'bg-blue'
                : customData.isManager
                ? 'bg-orange'
                : 'bg-green'
            "
            style="height: 4px; width: 100px"
          ></div>
          <div class="text-caption font-weight-bold pt-3">
            {{ customData.name }}
          </div>
        </div>
      </div>
      <div class="d-flex justify-space-between align-center">
        <div class="d-flex justify-center text-caption">
          {{ customData.title ? customData.title : "Organization" }}
        </div>
        <div class="d-flex justify-end">
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="14"
              width="14"
              fill="#0c166e"
              viewBox="0 0 640 512"
            >
              <path
                d="M368 32h-96c-17.7 0-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32V64c0-17.7-14.3-32-32-32zM208 88h-84.8C113.8 64.6 90.8 48 64 48 28.7 48 0 76.7 0 112s28.7 64 64 64c26.8 0 49.8-16.6 59.3-40h79.7c-55.4 32.5-95.9 87.3-109.5 152h49.4c11.3-41.6 36.8-77.2 71-101.6-3.7-8.1-5.9-17-5.9-26.4V88zm-48 232H64c-17.7 0-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32v-96c0-17.7-14.3-32-32-32zM576 48c-26.8 0-49.8 16.6-59.3 40H432v72c0 9.5-2.2 18.4-5.9 26.4 34.3 24.4 59.7 60 71 101.6h49.4c-13.7-64.7-54.2-119.5-109.5-152h79.7c9.5 23.4 32.4 40 59.3 40 35.3 0 64-28.7 64-64s-28.7-64-64-64zm0 272h-96c-17.7 0-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32v-96c0-17.7-14.3-32-32-32z"
              />
            </svg>
            <div class="d-flex justify-center" style="font-size: 8px">
              {{ customData._totalSubordinates }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.card-ui {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.bg {
  background: #ffffff;
  border-radius: 10px;
  border-style: solid;
  border-color: transparent;
  border-width: 2px;
  flex-shrink: 0;
  width: 240px;
  height: 100px;
  position: relative;
  border: 1px solid #7c7c7c;
}
.btn-rounded-org {
  height: 16px !important;
  width: 16px !important;
  border-radius: 50% !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  border: 0.5px solid #cfcfcf !important;
  background-color: #ffffff !important;
}
.btn-rounded-org :hover {
  background-color: #e2e2e2 !important;
}
</style>
