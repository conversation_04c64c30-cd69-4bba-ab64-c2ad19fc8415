<template>
  <div
    v-if="certificationDetails && certificationDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No certifications have been uploaded
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in certificationDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:400px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip :text="data.Certification_Name" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="data.Certification_Name ? props : ''"
                    >
                      {{ checkNullValue(data.Certification_Name) }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n5">
        <span
          class="d-flex align-start flex-column"
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Received on </b>
              <span class="mb-3">{{ formatDate(data.Received_Date) }}</span>
            </div>
            <div
              v-if="labelList[381]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[381]?.Field_Alias }}
              </b>
              <v-tooltip :text="data.Ranking" location="bottom" max-width="400">
                <template v-slot:activator="{ props }">
                  <span class="pb-1 pt-1" v-bind="props">
                    <div
                      :style="
                        isMobileView ? 'max-width: 200px' : 'max-width:140px'
                      "
                      class="text-truncate"
                    >
                      {{ checkNullValue(data.Ranking) }}
                    </div></span
                  >
                </template>
              </v-tooltip>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">From </b>
              <span class="mb-3">{{
                checkNullValue(data.Certificate_Received_From)
              }}</span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewCertificationDetails",
  components: { ActionMenu },
  props: {
    certificationDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return { havingAccess: {} };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.certificationDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", [selectedActionItem, "certification"]);
      } else {
        this.$emit("on-open-edit", [selectedActionItem, "certification"]);
      }
    },
  },
};
</script>
