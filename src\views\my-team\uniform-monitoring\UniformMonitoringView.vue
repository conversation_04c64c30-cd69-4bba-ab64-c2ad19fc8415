<template>
  <v-card class="rounded-lg mb-5">
    <v-card-title class="pa-0">
      <div
        class="d-flex align-center pa-0"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="40" color="hover" variant="elevated">
            <i class="text-primary hr-workflow-approval-management text-h6"></i>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              Uniform Monitoring Details
            </p>
          </div>
        </div>
        <div class="pa-3 d-flex">
          <v-icon color="primary" @click="$emit('close-split-view')">
            fas fa-times
          </v-icon>
        </div>
      </div>
    </v-card-title>
    <div class="py-3">
      <v-form ref="leaveOverrideForm">
        <v-row
          class="px-5 pt-3 gap-5"
          style="height: calc(100vh - 300px); overflow: scroll"
        >
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Employee Id</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.userDefinedEmpId) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Employee Name</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.employeeName) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Uniform Type</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.uniformType) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Gender</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.gender) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
            v-if="!isEdit"
          >
            <p class="text-subtitle-1 text-grey-darken-1">Uniform Size</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.uniformSize) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Status</p>
            <p
              class="text-subtitle-1 font-weight-regular font-weight-bold"
              :class="
                employeeDetails.uniformStatus == 'Returned'
                  ? 'text-purple'
                  : employeeDetails.uniformStatus === 'Delivered'
                  ? 'text-green'
                  : 'text-red'
              "
            >
              {{ checkNullValue(employeeDetails.uniformStatus) }}
            </p>
          </v-col>
        </v-row>
      </v-form>
    </div>
  </v-card>
</template>
<script>
import { checkNullValue } from "@/helper";
export default {
  name: "UniformMonitoringView",
  emits: [
    "close-split-view",
    "refetch-list",
    "enable-loader",
    "disable-loader",
  ],
  props: {
    employeeDetails: {
      type: Object,
      required: true,
    },
    isSmallTable: {
      type: Boolean,
      required: false,
    },
  },
  data() {
    return {
      viewFormTab: 1,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  watch: {
    employeeDetails: {
      immediate: true,
      handler(newData) {
        this.currentYearLeaveEntitlement = newData.currentYearTotalEligibleDays;
        this.carryOverBalance = newData.lastCOBalance;
        this.carryOver = newData.carryOver;
      },
    },
  },
  methods: {
    checkNullValue,
  },
};
</script>
