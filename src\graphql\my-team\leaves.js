import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_LIST_LEAVES = gql`
  query listLeaves(
    $formId: Int!
    $leaveStartDate: Date!
    $leaveEndDate: Date!
    $offset: Int
    $limit: Int
  ) {
    listLeaves(
      formId: $formId
      leaveStartDate: $leaveStartDate
      leaveEndDate: $leaveEndDate
      offset: $offset
      limit: $limit
    ) {
      errorCode
      message
      totalCount
      leaveDetails
    }
  }
`;
export const LIST_LEAVE_TYPES = gql`
  query listLeaveTypes($formId: Int, $leaveTypeId: Int) {
    listLeaveTypes(formId: $formId, leaveTypeId: $leaveTypeId) {
      errorCode
      message
      leaveTypeDetails
    }
  }
`;
// ===============
// Mutations
// ===============
