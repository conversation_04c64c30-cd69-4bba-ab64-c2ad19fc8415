<template>
  <div v-if="!enableWorkflowBuilder">
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        :showBottomSheet="workflowListBackup.length > 0"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="workflowListBackup.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isDefaultFilter="false"
                @reset-emp-filter="resetFilter()"
                @apply-emp-filter="applyFilter()"
              >
                <template #new-filter>
                  <v-row class="mt-5">
                    <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                      <v-select
                        v-model="selectedModuleFilter"
                        color="primary"
                        :items="workflowModuleList"
                        label="Modules"
                        multiple
                        :item-title="('Module_Name', 'Form_Name')"
                        item-value="Workflow_Module_Id"
                        closable-chips
                        chips
                        density="compact"
                        single-line
                      >
                      </v-select>
                    </v-col>
                  </v-row>
                </template>
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="roles-configuration-container">
      <v-window v-model="currentTabItem" v-if="isSuperAdmin">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-12">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <v-row class="d-flex">
              <v-col
                xlg="3"
                lg="4"
                md="6"
                sm="6"
                v-for="i in 3"
                :key="i"
                class="mt-4"
              >
                <v-skeleton-loader
                  ref="skeleton2"
                  type="article"
                  class="mx-auto"
                ></v-skeleton-loader>
              </v-col>
            </v-row>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="fetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="workflowListBackup.length === 0"
            key="no-results-screen"
            :isSmallImage="true"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col v-if="workflowListBackup.length === 0" cols="12">
                    <NotesCard
                      notes="There are no workflows in the list, so let's create a new one."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                    v-if="isSuperAdmin"
                  >
                    <v-btn
                      v-if="workflowListBackup.length === 0 && isSuperAdmin.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAdd()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Add
                    </v-btn>
                    <v-btn
                      v-if="workflowListBackup.length === 0"
                      variant="elevated"
                      rounded="lg"
                      class="ml-2 mt-1 primary"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="fetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else class="mt-12">
            <div v-if="workflowLists.length > 0 && isSuperAdmin">
              <div
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'd-flex flex-wrap align-center my-6 justify-center flex-column'
                    : 'd-flex flex-wrap align-center my-4 justify-end'
                "
              >
                <v-btn
                  v-if="isSuperAdmin.add"
                  prepend-icon="fas fa-plus"
                  color="primary rounded-lg"
                  class="mx-1 mt-2"
                  @click="openAdd()"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  Add
                </v-btn>
                <v-btn
                  color="transparent"
                  class="ml-1 mt-2"
                  variant="flat"
                  size="small"
                  @click="fetchList()"
                  ><v-icon color="grey">fas fa-redo-alt</v-icon></v-btn
                >
              </div>

              <v-row
                class="mb-12"
                :style="
                  'overflow: scroll; height: ' +
                  $store.getters.getTableHeight(230)
                "
              >
                <v-col
                  v-for="(data, index) in workflowLists"
                  :key="index"
                  xlg="3"
                  lg="4"
                  md="6"
                  sm="6"
                  cols="12"
                >
                  <WorkflowCard
                    :accessRights="isSuperAdmin"
                    :workflowDetails="data"
                    @refetch-list="fetchList()"
                    @on-open-edit="openEditForm(data)"
                    @edit-flow="openEditFlow(data)"
                  ></WorkflowCard>
                </v-col>
              </v-row>
            </div>
            <AppFetchErrorScreen
              v-else
              key="no-results-screen"
              main-title="There is no workflow matched for the selected filters/searches."
              image-name="common/no-records"
            >
              <template #contentSlot>
                <div style="max-width: 80%">
                  <v-row class="rounded-lg pa-5 mb-4">
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <v-btn
                        variant="elevated"
                        color="primary"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="resetFetchList()"
                      >
                        Reset Filter/Search
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <v-dialog v-model="openWorkflowModal" width="800">
      <v-form ref="workflowForm">
        <v-card class="px-8 pt-10 pb-5">
          <v-icon
            color="primary"
            class="pr-4 pt-4 font-weight-bold position-absolute top-0 right-0"
            @click="closeWarningModal = true"
            >fas fa-times</v-icon
          >
          <v-row class="mr-2">
            <v-col class="py-2 d-flex" :cols="12">
              <b
                class="mb-3 text-black font-weight-regular text-center justify-center w-100"
              >
                Create a Workflow
              </b>
            </v-col>
          </v-row>
          <v-row class="mr-2 mt-2">
            <v-col class="py-2" :cols="12">
              <v-text-field
                v-model="workflowName"
                label="Name"
                density="compact"
                variant="solo"
                maxlength="100"
              >
              </v-text-field
            ></v-col>
          </v-row>
          <v-row class="mr-2 mt-2">
            <v-col class="py-2" :cols="12">
              <CustomSelect
                label="Module"
                v-model="selectedWorkflowModule"
                :itemSelected="selectedWorkflowModule"
                :items="workflowModuleList"
                item-value="Workflow_Module_Id"
                :item-title="('Module_Name', 'Form_Name')"
                :isLoading="workflowModuleLoader"
                :isAutoComplete="true"
                @selected-item="
                  (e) => {
                    selectedForm(e);
                  }
                "
                variant="solo"
              >
              </CustomSelect>
            </v-col>
          </v-row>
          <v-row class="mr-2 mt-2" v-if="showDesignation">
            <v-col class="py-2" :cols="12">
              <CustomSelect
                label="Designation"
                v-model="selectedDesignation"
                :itemSelected="selectedDesignation"
                :items="designationList"
                item-value="Designation_Id"
                item-title="Designation_Name"
                :isLoading="designationLoader"
                :selectProperties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                  clearable: true,
                }"
                :no-data-text="noDataText"
                placeholder="Type minimum 3 characters to list"
                :isAutoComplete="true"
                @selected-item="(e) => {}"
                variant="solo"
                @update-search-value="callDesignationList($event)"
              >
              </CustomSelect>
            </v-col>
          </v-row>
          <v-row class="mr-2">
            <v-col
              class="py-2 d-flex justify-start align-center item-center"
              :cols="12"
            >
              <span class="mb-1 text-gray justify-start">
                Default Workflow for this module
              </span>
              <v-switch
                color="primary"
                class="mt-2 ml-4"
                v-model="isDefault"
                :true-value="'Yes'"
                :false-value="'No'"
              ></v-switch>
            </v-col>
          </v-row>
          <v-row class="mr-2 my-2">
            <v-col class="py-2" :cols="12">
              <v-textarea
                v-model="workflowDescription"
                rows="2"
                row-height="8"
                color="primary"
                maxlength="600"
                :rules="[
                  workflowDescription
                    ? validateWithRulesAndReturnMessages(
                        workflowDescription,
                        'departmentDescription',
                        'Description'
                      )
                    : true,
                ]"
                hide-details="auto"
                variant="solo"
                label="Description"
              ></v-textarea>
            </v-col>
          </v-row>
          <v-divider></v-divider>
          <v-row class="my-3">
            <v-col cols="12" class="d-flex justify-end pt-3 pr-5">
              <v-btn
                class="text-capitalize mr-5 primary"
                elevation="1"
                rounded="md"
                variant="outlined"
                @click="closeWarningModal = true"
                >Cancel</v-btn
              >
              <v-btn
                class="text-capitalize"
                color="primary"
                elevation="1"
                rounded="md"
                variant="elevated"
                @click="validateWorkflowForm()"
                >Save</v-btn
              >
            </v-col>
          </v-row>
        </v-card>
      </v-form>
    </v-dialog>
  </div>
  <WorkflowBuilder
    v-else
    :editWorkflow="editWorkflow"
    :editFlowData="editFlowData"
    :workflowName="workflowName"
    @submitWorkflow="(e) => createWorkflow(e)"
    @onCloseWorkflow="enableWorkflowBuilder = false"
    :workflowModule="workflowModule"
    :eventId="eventId"
    :selectedFormId="selectedFormId"
  />
  <AppWarningModal
    v-if="closeWarningModal"
    :open-modal="closeWarningModal"
    confirmation-heading="Are you sure to exit the form?"
    imgUrl="common/exit_form"
    @close-warning-modal="closeWarningModal = false"
    @accept-modal="onCloseModule()"
  ></AppWarningModal>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { LIST_WORKFLOW } from "@/graphql/settings/irukka-integration/jobPostFormQueries";
import WorkflowCard from "./components/WorkflowCard.vue";
import {
  CREATE_WORKFLOW_MODULE,
  GET_WORKFLOW_MODULE,
  EDIT_WORKFLOW_MODULE,
} from "@/graphql/commonQueries";
import { defineAsyncComponent } from "vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
import validationRules from "@/mixins/validationRules";

import WorkflowBuilder from "../WorkflowBuilder.vue";
import Config from "@/config.js";
import axios from "axios";
// import { getOrigin } from "@/helper";

export default {
  name: "WorkflowList",
  components: {
    WorkflowCard,
    NotesCard,
    EmployeeDefaultFilterMenu,
    CustomSelect,
    WorkflowBuilder,
  },
  mixins: [validationRules],
  data() {
    return {
      isLoading: false,
      listLoading: false,
      workflowLists: [],
      workflowListBackup: [],
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      mainTabs: ["Workflow Builder"],
      currentTabItem: "0",
      workflowModuleList: [],
      workflowModuleLoader: false,
      selectedWorkflowModule: "",
      workflowModule: "",
      openWorkflowModal: false,
      isDefault: "No",
      workflowName: "",
      workflowDescription: "",
      workModuleEndpoint: "",
      enableWorkflowBuilder: false,
      enableEditModule: false,
      editWorkflow: false,
      editFlowData: [],
      eventId: "",
      workflowId: "",
      selectedModuleFilter: [],
      designationList: [],
      showDesignation: false,
      selectedDesignation: [],
      designationLoader: false,
      chartData: "",
      selectedFormId: "",
      searchString: "",
      closeWarningModal: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("173");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    noDataText() {
      if (this.designationLoader) {
        return "Loading...";
      } else if (
        !this.designationLoader &&
        this.designationList.length == 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.fetchList();
    this.getWorkflowModule();
  },
  methods: {
    onApplySearch(query) {
      if (!query) {
        this.workflowLists = this.workflowListBackup;
      } else {
        let searchValue = query.toString().toLowerCase();
        let searchItems = this.workflowListBackup;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.workflowLists = searchItems;
      }
    },
    resetFilter() {
      if (this.selectedModuleFilter && this.selectedModuleFilter.length) {
        this.selectedModuleFilter = [];
        this.fetchList();
      }
      this.selectedModuleFilter = [];
    },
    resetFetchList() {
      this.selectedModuleFilter = [];
      this.fetchList();
    },
    applyFilter() {
      this.fetchList();
    },
    openEditForm(formData) {
      this.enableEditModule = true;
      this.eventId = formData.Event_Id;
      this.workflowId = formData.Workflow_Id;
      this.selectedWorkflowModule = formData.Workflow_Module_Id;
      this.selectedFormId = formData.Form_Id;
      this.workflowModule = formData.Form_Name;
      this.openWorkflowModal = true;
      this.isDefault = formData.Default_Workflow === 1 ? "Yes" : "No";
      this.workflowName = formData.Workflow_Name;
      this.workflowDescription = formData.Description;
      this.showDesignation = false;
      this.selectedDesignation = [];
      if (formData.Workflow_Module_Id === 2) {
        this.selectedDesignation = formData.Designations.map((list) => {
          return list.Designation_Id;
        });
        this.showDesignation = true;
        this.designationList = formData.Designations;
      }
      // this.editLockModule(formData, true);
    },
    openAdd() {
      this.openWorkflowModal = true;
      this.editWorkflow = false;
      this.editFlowData = [];
      this.selectedWorkflowModule = "";
      this.selectedFormId = "";
      this.workflowModule = "";
      this.isDefault = "No";
      this.workflowName = "";
      this.workflowDescription = "";
      this.showDesignation = false;
      this.selectedDesignation = [];
      this.designationList = [];
    },
    onCloseModule() {
      this.closeWarningModal = false;
      this.openWorkflowModal = false;
      if (this.enableEditModule) {
        // this.clearWorkflowGQL();
      }
    },
    openEditFlow(eventData) {
      this.workflowId = eventData.Workflow_Id;
      this.workflowName = eventData.Workflow_Name;
      this.eventId = eventData.Event_Id;
      this.selectedWorkflowModule = eventData.Workflow_Module_Id;
      this.selectedFormId = eventData.Form_Id;
      this.workflowModule = eventData.Form_Name;
      this.isDefault = eventData.Default_Workflow === 1 ? "Yes" : "No";
      this.selectedDesignation = [];
      this.designationList = [];
      if (eventData.Workflow_Module_Id === 2) {
        this.selectedDesignation = eventData.Designations.map((list) => {
          return list.Designation_Id;
        });
      }
      this.isLoading = true;
      let vm = this;
      const payload = {
        query_key: "query.workflow",
        limit: 100,
        offset: 0,
        filter: [
          { key: "status_id", value: "1001", op: "=" },
          {
            key: "event_id",
            value: eventData.Event_Id,
            op: "=",
          },
        ],
        sort: "created_date",
        order: "desc",
      };
      axios
        .post(Config.workflowUrl + "/master/query", JSON.stringify(payload), {
          headers: {
            org_code: vm.orgCode,
            employee_id: vm.loginEmployeeId,
            // Origin: getOrigin(),
            db_prefix: vm.domainName,
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            Authorization: window.$cookies.get("accessToken")
              ? window.$cookies.get("accessToken")
              : "",
          },
        })
        .then((res) => {
          this.isLoading = false;
          if (res && res.data && res.data.results) {
            this.editFlowData = res.data.results;
            this.editWorkflow = true;
            this.enableWorkflowBuilder = true;
          }
        })
        .catch((e) => {
          this.isLoading = false;
          this.handleWorkflowTaskError(e);
        });
    },
    handleRetrieveWorkflowError(err = "") {
      let snackbarData = {
        isOpen: true,
        message:
          "Something went wrong while submitting the workflow. Please try after time",
        type: "warning",
      };
      if (err?.graphQLErrors[0]?.message) {
        const errorType = JSON.parse(err?.graphQLErrors[0]?.message);
        switch (Object.keys(errorType?.validationError)[0]) {
          case "ERR-683":
            snackbarData.message = "Workflow Name already exists";
            break;
          case "ERR-642":
            snackbarData.message = "Workflow Name is required";
            break;
          default:
            snackbarData.message =
              "Something went wrong while submitting the workflow. Please try after time";
            break;
        }
      } else {
        snackbarData.message = "Please fill the all required fields";
      }
      this.showAlert(snackbarData);
    },
    async validateWorkflowForm() {
      const { valid } = await this.$refs.workflowForm.validate();
      if (valid) {
        this.onSubmitFormData();
      }
    },
    onSubmitFormData() {
      if (this.enableEditModule) {
        this.createWorkflowGQL(true);
      } else {
        this.isLoading = true;
        let vm = this;
        const payload = {
          query_key: "query.event.upsert",
          data: {
            name: this.workflowName,
            description: this.workflowDescription,
            status_id: "1004",
            endpoint_key: this.workModuleEndpoint,
            parameters_schema: `{ "definitions": {}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://example.com/root.json", "type": "object", "title": "The Root Schema", "required": [ "formId"], "properties": { "formId": { "$id": "#/properties/formId", "type": "integer", "title": "The Formid Schema", "default": ${
              this.isDefault === "Yes" ? 1 : 0
            }, "examples": [ 268 ] }}}`,
          },
        };
        axios
          .post(Config.workflowUrl + "/master", JSON.stringify(payload), {
            headers: {
              org_code: vm.orgCode,
              employee_id: vm.loginEmployeeId,
              // Origin: getOrigin(),
              Db_prefix: vm.domainName,
              "Content-Type":
                "application/x-www-form-urlencoded; charset=UTF-8",
              Authorization: window.$cookies.get("accessToken")
                ? window.$cookies.get("accessToken")
                : "",
            },
          })
          .then((res) => {
            this.isLoading = false;
            if (res && res.data && res.data._id) {
              this.createModule(res.data._id);
            }
          })
          .catch((e) => {
            this.handleWorkflowTaskError(e);
            this.isLoading = false;
          });
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    createModule(eventId) {
      this.eventId = eventId;
      let vm = this;
      this.isLoading = true;
      vm.$apollo
        .query({
          query: CREATE_WORKFLOW_MODULE,
          variables: {
            addedBy: vm.loginEmployeeId,
            workflowName: this.workflowName,
            eventId: eventId,
            workflowModuleId: this.selectedWorkflowModule,
            workflowImage: "",
            isDefault: this.isDefault === "Yes" ? 1 : 0,
            description: this.workflowDescription,
            designationIds: this.selectedDesignation,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.isLoading = false;
          if (
            res &&
            res.data &&
            res.data.insertWorkflow &&
            res.data.insertWorkflow.message
          ) {
            this.workflowId = res.data.insertWorkflow.data;
            let snackbarData = {
              isOpen: true,
              message: res.data.insertWorkflow.message,
              type: "success",
            };
            this.enableWorkflowBuilder = true;
            this.openWorkflowModal = false;
            vm.showAlert(snackbarData);
            this.workflowInitiate();
          }
        })
        .catch((e) => {
          this.isLoading = false;
          vm.handleRetrieveWorkflowError(e, "creating");
        });
    },
    selectedForm(id) {
      const responseData = this.workflowModuleList.find(
        (list) => list.Workflow_Module_Id === id
      );
      this.workModuleEndpoint = responseData?.Workflow_End_Point_Key;
      this.selectedFormId = responseData?.Form_Id;
      this.showDesignation = false;
      if (id === 2) {
        this.showDesignation = true;
      }
    },
    workflowInitiate() {
      let vm = this;
      const payload = {
        query_key: "query.workflow",
        limit: 100,
        offset: 0,
        filter: [
          { key: "status_id", value: "1001", op: "=" },
          {
            key: "event_id",
            value: this.eventId,
            op: "=",
          },
        ],
        sort: "created_date",
        order: "desc",
      };
      axios
        .post(Config.workflowUrl + "/master/query", JSON.stringify(payload), {
          headers: {
            org_code: vm.orgCode,
            employee_id: vm.loginEmployeeId,
            Db_prefix: vm.domainName,
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            Authorization: window.$cookies.get("accessToken")
              ? window.$cookies.get("accessToken")
              : "",
          },
        })
        .then(() => {
          this.isLoading = false;
        })
        .catch((e) => {
          this.isLoading = false;
          this.handleWorkflowTaskError(e);
        });
    },
    createWorkflow(workflowData) {
      this.isLoading = true;
      let vm = this;
      const editUrl = this.editFlowData[0]?.workflow_id
        ? Config.workflowUrl +
          "/workflow/definition/" +
          this.editFlowData[0]?.workflow_id
        : Config.workflowUrl + "/workflow/definition";
      const payload = {
        name: "",
        event_id: this.eventId,
        description: "",
        status_id: "1001",
        definition: "<>",
        ui_config: workflowData,
      };
      axios
        .post(
          this.editWorkflow
            ? editUrl
            : Config.workflowUrl + "/workflow/definition",
          JSON.stringify(payload),
          {
            headers: {
              org_code: vm.orgCode,
              employee_id: vm.loginEmployeeId,
              // Origin: getOrigin(),
              Db_prefix: vm.domainName,
              "Content-Type":
                "application/x-www-form-urlencoded; charset=UTF-8",
              Authorization: window.$cookies.get("accessToken")
                ? window.$cookies.get("accessToken")
                : "",
            },
          }
        )
        .then((res) => {
          if (res && res.data && res.data._id) {
            this.createWorkflowGQL(this.editWorkflow);
            // this.createWorkflowGQL(res.data._id, false);
          } else {
            this.isLoading = false;
          }
        })
        .catch((e) => {
          this.isLoading = false;
          this.handleWorkflowTaskError(e);
        });
    },
    createWorkflowGQL(editFlag) {
      this.isLoading = true;
      let vm = this;
      vm.$apollo
        .query({
          query: EDIT_WORKFLOW_MODULE, //this query also for the same in createWorkflow
          variables: {
            workflowId: this.workflowId,
            updatedBy: vm.loginEmployeeId,
            workflowName: this.workflowName,
            eventId: this.eventId,
            workflowModuleId: this.selectedWorkflowModule,
            workflowImage: "",
            isDefault: this.isDefault === "Yes" ? 1 : 0,
            description: this.workflowDescription,
            designationIds: this.selectedDesignation,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.updateWorkflow &&
            res.data.updateWorkflow.message
          ) {
            let snackbarData = {
              isOpen: true,
              message: res.data.updateWorkflow.message,
              type: "success",
            };
            this.editWorkflow = false;
            if (editFlag) {
              this.enableEditModule = false;
              this.openWorkflowModal = false;
              // this.selectedWorkflowModule = "";
              // this.isDefault = "No";
              this.workflowName = "";
              this.workflowDescription = "";
            }
            this.enableWorkflowBuilder = false;
            this.fetchList();
            // this.clearWorkflowGQL();
            vm.showAlert(snackbarData);
            this.isLoading = false;
          } else {
            this.isLoading = false;
          }
        })
        .catch((e) => {
          this.isLoading = false;
          this.handleRetrieveWorkflowError(e);
        });
    },
    clearWorkflowGQL() {
      let vm = this;
      this.isLoading = true;
      vm.$apollo
        .query({
          // query: CLEAR_LOCK_WORKFLOW,
          variables: {
            uniqueId: this.workflowId,
            Employee_Id: vm.loginEmployeeId,
            formName: "Workflow Builder",
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.isLoading = false;
          if (res && res.data) {
            this.enableWorkflowBuilder = false;
            // this.fetchList();
          }
        })
        .catch(() => {
          this.isLoading = false;
        });
    },
    fetchList() {
      this.listLoading = true;
      let vm = this;
      vm.workflowLists = [];
      vm.workflowListBackup = [];
      let filterData = [];
      this.selectedModuleFilter?.forEach((filter) => {
        filterData?.push(filter.toString());
      });
      vm.$apollo
        .query({
          query: LIST_WORKFLOW,
          variables: {
            employeeId: vm.loginEmployeeId,
            searchString: "",
            moduleId: filterData,
            isDropDownCall: 1,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((workflowList) => {
          this.listLoading = false;
          if (
            workflowList &&
            workflowList.data &&
            workflowList.data.listWorkflow &&
            workflowList.data.listWorkflow.Workflows &&
            workflowList.data.listWorkflow.Workflows.length > 0
          ) {
            this.workflowLists = workflowList.data.listWorkflow.Workflows;
            this.workflowListBackup = workflowList.data.listWorkflow.Workflows;
          }
        })
        .catch((err) => {
          vm.handleRetrieveWorkflowError(err, "retrieving");
          this.isLoading = false;
        });
    },
    getWorkflowModule() {
      let vm = this;
      vm.workflowModuleLoader = true;
      vm.workflowModuleList = [];
      vm.$apollo
        .query({
          query: GET_WORKFLOW_MODULE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getWorkflowModules &&
            response.data.getWorkflowModules.Modules
          ) {
            this.workflowModuleList = response.data.getWorkflowModules.Modules;
            this.workflowModuleLoader = false;
          } else {
            this.workflowModuleList = [];
            this.workflowModuleLoader = false;
          }
        })
        .catch(() => {
          this.workflowModuleList = [];
          this.workflowModuleLoader = false;
        });
    },
    editLockModule(formData, flag) {
      let vm = this;
      this.isLoading = true;
      vm.$apollo
        .query({
          query: EDIT_WORKFLOW_MODULE,
          variables: {
            uniqueId: this.workflowId,
            Employee_Id: vm.loginEmployeeId,
            formName: "Workflow Builder",
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.isLoading = false;
          if (res && res.data) {
            if (flag) {
              this.selectedWorkflowModule = formData.Workflow_Module_Id;
              this.openWorkflowModal = true;
              this.isDefault = formData.Default_Workflow === 1 ? "Yes" : "No";
              this.workflowName = formData.Workflow_Name;
              this.workflowDescription = formData.Description;
            }
          }
        })
        .catch(() => {
          this.isLoading = false;
        });
    },
    handleWorkflowTaskError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message:
          "Something went wrong while submitting the workflow. Please try after time.",
        type: "warning",
      };
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data;
        if (errorCode.message.includes("type")) {
          snackbarData.message = "Please fill the all nodes data";
        } else if (errorCode.message.includes("modalTaskData")) {
          snackbarData.message = "Please fill the user task required fields";
        } else {
          snackbarData.message =
            "Something went wrong while connecting the node";
        }
      }
      this.showAlert(snackbarData);
    },

    callDesignationList(searchString) {
      this.searchString = searchString;
      if (searchString.length >= 3) {
        this.fetchDesignationList(searchString);
      }
    },

    async fetchDesignationList(searchString) {
      this.designationLoader = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            // Get currently selected designations
            const selected =
              this.designationList?.filter((item) =>
                this.selectedDesignation?.includes(item.Designation_Id)
              ) || [];

            const { designationResult } = res.data.getDesignationDetails;
            const existingIds = new Set(
              selected.map((item) => item.Designation_Id)
            );

            // Filter out duplicates from new designations
            const newDesignations =
              designationResult?.filter(
                (item) => !existingIds.has(item.Designation_Id)
              ) || [];
            this.designationList = [...selected, ...newDesignations];
          }
          this.designationLoader = false;
        })
        .catch(() => {
          this.designationLoader = false;
          this.designationList = [];
        });
    },
  },
};
</script>
<style>
.v-btn--variant-elevated {
  background: rgb(var(--v-theme-primary));
  color: white;
  box-shadow: 0.3px 3px 3px grey;
}
.v-btn--variant-outlined {
  color: rgb(var(--v-theme-primary));
  border: none;
  box-shadow: 0.3px 3px 3px grey;
}
</style>
