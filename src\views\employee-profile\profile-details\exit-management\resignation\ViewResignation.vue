<template>
  <div class="d-flex justify-space-between align-center">
    <v-row>
      <v-col cols="11" class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="green"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
          >Initiate Separation</span
        >
      </v-col>
      <v-col cols="1">
        <v-btn
          v-if="enableAdd()"
          color="primary"
          variant="text"
          @click="openAddForm()"
        >
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Apply</v-btn
        >
      </v-col>
    </v-row>
  </div>
  <div
    v-if="resignationDetails && resignationDetails.length === 0"
    class="d-flex flex-column align-center justify-center fill-height text-h6 text-grey emptyDiv"
  >
    <v-row class="rounded-lg pa-5" :style="'background: white'">
      <v-col cols="12">
        <NotesCard
          notes="The portal allows employees to initiate and manage their exit from the organization in a structured and transparent manner. This feature streamlines resignation, clearance, and documentation processes, providing a smooth experience for both the employee and the HR team."
          backgroundColor="transparent"
          class="mb-4"
        />
        <NotesCard
          notes="Employees can track the progress of their separation process at each stage, from resignation approval to final clearance. This gives them real-time visibility and reduces the need for back-and-forth communication with HR."
          backgroundColor="transparent"
          class="mb-4"
        />
      </v-col>
      <v-col cols="12" class="d-flex align-center justify-center"
        ><v-btn
          v-if="formAccess?.add && resignationDetails?.length === 0"
          color="primary"
          class="d-flex justify-between"
          variant="elevated"
          @click="openAddForm()"
        >
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Apply</v-btn
        ></v-col
      ></v-row
    >
  </div>
  <div class="d-flex justify center" v-if="!isMobileView">
    <v-slide-group
      class="pa-4"
      selected-class="bg-secondary"
      prev-icon="fas fa-chevron-circle-left"
      next-icon="fas fa-chevron-circle-right"
      show-arrows
    >
      <v-slide-group-item>
        <v-card
          elevation="3"
          v-for="(data, index) in resignationDetails"
          :key="index"
          class="card-item d-flex pa-4 rounded-lg ma-2"
          color="grey-lighten-5"
          @click="openViewForm(data)"
          :style="
            !isMobileView
              ? `min-width: 400px; border-left: 7px solid ${generateRandomColor()}; height:auto; cursor: ${
                  data.resignationStatus !== 'Canceled'
                    ? 'pointer'
                    : 'not-allowed'
                }`
              : `border-left: 7px solid ${generateRandomColor()}; cursor: ${
                  data.resignationStatus !== 'Canceled'
                    ? 'pointer'
                    : 'not-allowed'
                }`
          "
        >
          <div class="d-flex flex-column" style="width: 100%">
            <div class="w-100 mt-n7">
              <span>
                <v-card-text class="text-body-1 font-weight-regular">
                  <v-row>
                    <v-col cols="8">
                      <v-tooltip
                        :text="`${data.resignationStatus}`"
                        location="bottom"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="font-weight-bold text-h6 text-truncate"
                            :class="statusColor(data.resignationStatus)"
                            :style="
                              isMobileView
                                ? 'max-width: 180px'
                                : 'max-width: 350px'
                            "
                            v-bind="data.employeeName ? props : ''"
                          >
                            {{ checkNullValue(data.resignationStatus) }}
                          </div>
                        </template>
                      </v-tooltip>
                    </v-col>
                    <v-col cols="4">
                      <v-btn
                        v-if="
                          formAccess?.update &&
                          (data.resignationStatus === 'Applied' ||
                            data.resignationStatus === 'Incomplete')
                        "
                        size="sm"
                        class="pa-1 mt-1"
                        rounded="lg"
                        variant="outlined"
                        @click.stop="openCancelModal"
                        >Withdraw</v-btn
                      >
                    </v-col>
                  </v-row>
                </v-card-text>
              </span>
            </div>
            <div class="card-columns w-100 mt-n3">
              <span
                :style="!isMobileView ? 'width:60%' : 'width:100%'"
                class="d-flex align-start flex-column"
              >
                <v-card-text class="text-body-1 font-weight-regular">
                  <div class="mt-2 mr-2 d-flex flex-column justify-start">
                    <b class="mb-1 text-grey justify-start"
                      >Date of Resignation</b
                    >
                    <span class="py-2">{{
                      checkNullValue(formatDate(data.appliedDate))
                    }}</span>
                  </div>
                </v-card-text>
              </span>
              <span
                :style="
                  !isMobileView
                    ? 'width:40%'
                    : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
                "
                class="d-flex align-start flex-column"
              >
                <v-card-text class="text-body-1 font-weight-regular">
                  <div class="mt-2 mr-2 d-flex flex-column justify-start">
                    <b class="mb-1 text-grey justify-start">Date of Exit</b>
                    <span class="py-2">{{
                      checkNullValue(formatDate(data.resignationDate))
                    }}</span>
                  </div>
                </v-card-text>
              </span>
            </div>
            <div class="card-columns w-100 mt-n3">
              <v-card-text class="text-body-1 font-weight-regular">
                <div
                  v-if="
                    data.resignationStatus === 'Canceled' ||
                    data.resignationStatus === 'Withdrawn' ||
                    data.resignationStatus === 'Rejected'
                  "
                  class="mt-2 mr-2 d-flex flex-column justify-start"
                >
                  <b class="mb-1 text-grey justify-start">Comment</b>
                  <span class="py-2">{{
                    checkNullValue(data.withdrawnCancellationComment)
                  }}</span>
                </div>
                <div
                  v-if="
                    data.resignationStatus === 'Applied' ||
                    data.resignationStatus === 'Incomplete' ||
                    data.resignationStatus === 'Approved'
                  "
                  class="mt-2 mr-2 d-flex flex-column justify-start"
                >
                  <b class="mb-1 text-grey justify-start"
                    >Reason For Resignation</b
                  >
                  <span class="py-2">{{
                    checkNullValue(data.relievingReasonComment)
                  }}</span>
                </div>
              </v-card-text>
            </div>
          </div>
        </v-card>
      </v-slide-group-item></v-slide-group
    >
  </div>

  <v-dialog
    transition="dialog-bottom-transition"
    v-model="showResignationForm"
    :width="isMobileView ? '70%' : '55%'"
  >
    <AddEditResignationDetails
      :selectedEmpId="selectedEmpId"
      @close-resignation-form="showResignationForm = false"
      @refetch-resignation-detail="closeAddEditForm"
      @added-new-record="openWorkflowModel"
    />
  </v-dialog>
  <v-dialog
    transition="dialog-bottom-transition"
    v-model="showWorkFlowModel"
    width="70%"
    @click:outside="closeFormRenderForm()"
  >
    <FormRender
      v-if="showWorkFlowModel"
      :form-data="formJsonData"
      :resignationId="selectedItem.resignationId"
      :taskId="
        dynamicFormViewNodeData?.taskId
          ? dynamicFormViewNodeData.taskId
          : createTaskId
      "
      :formResponseId="formResponseId"
      :conversationalId="conversationalId"
      :processInstanceId="processInstanceId"
      :show-approval="showApproval"
      @form-update-success="callUpdateTask()"
      @close-form-render="closeFormRenderForm()"
    />
  </v-dialog>
  <v-dialog
    :model-value="openFormInModal"
    class="pl-4"
    width="900"
    @click:outside="closeAllForms()"
  >
    <ViewExitManagement
      v-if="showViewForm"
      :selectedItem="selectedItem"
      :isEmployee="isEmployee"
      :access-rights="formAccess"
      @close-form="closeAllForms()"
      @open-dynamic-form="openWorkFlowFromViewForm"
    />
  </v-dialog>
  <AppWarningModal
    v-if="cancelModel"
    :open-modal="cancelModel"
    confirmation-heading="Are you sure to withdraw the resignation?"
    icon-name="far fa-times-circle"
    icon-Size="50"
    :acceptButtonDisable="checkCancelButton"
    @close-warning-modal="cancelModel = false"
    @accept-modal="validateCancelResignation()"
    ><template v-slot:warningModalContent>
      <div class="mt-2">
        <v-textarea
          ref="selectedReasonForCancel"
          v-model="selectedReasonForCancel"
          :rules="[
            required('Reason for Withdraw', selectedReasonForCancel),
            firstCharacterValidation(selectedReasonForCancel),
            minLengthValidation(
              'Reason for Withdraw',
              selectedReasonForCancel,
              5
            ),
            validateWithRulesAndReturnMessages(
              selectedReasonForCancel,
              'description',
              'Reason for Withdraw'
            ),
          ]"
          v-bind="props"
          rows="3"
          :clearable="true"
          auto-grow
          variant="solo"
          min-width="350px"
          ><template v-slot:label>
            Reason for Withdraw
            <span style="color: red">*</span>
          </template></v-textarea
        >
      </div>
    </template></AppWarningModal
  >
</template>

<script>
import moment from "moment";
import Config from "@/config.js";
import axios from "axios";

import { generateRandomColor, checkNullValue } from "@/helper";
import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
const ViewExitManagement = defineAsyncComponent(() =>
  import("@/views/my-team/exit-management/ViewExitManagement.vue")
);
const FormRender = defineAsyncComponent(() =>
  import("@/views/workflow/approval-management/FormRender.vue")
);
const AddEditResignationDetails = defineAsyncComponent(() =>
  import("./AddEditResignationDetails.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import { GET_DYNAMIC_FORM_DETAILS } from "@/graphql/workflow/approvalManagementQueries.js";
import { WITHDRAWN_CANCEL_RESIGNATION } from "@/graphql/my-team/exitManagement.js";
export default {
  name: "ViewResignation",
  components: {
    ViewExitManagement,
    AddEditResignationDetails,
    FormRender,
    NotesCard,
  },
  mixins: [validationRules],
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    resignationDetails: {
      type: Array,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
  },
  emits: [],
  data() {
    return {
      showViewForm: false,
      selectedItem: null,
      isEmployee: false,
      //cancel
      showResignationForm: false,
      resignationIdWithInvalidStatus: null,
      createResignationResponse: null,
      selectedReasonForCancel: null,
      cancelModel: false,
      //workflow
      isUpdatingForm: false,
      isDynamicForm: false,
      showWorkFlowModel: false,
      formResponseId: null,
      dynamicFormViewNodeData: null,
      dynamicViewResignationId: null,
      showApproval: false,
      conversationalId: 0,
      formJsonData: {},
      processInstanceId: "",
      createTaskId: null,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    checkCancelButton() {
      if (
        this.selectedReasonForCancel &&
        this.selectedReasonForCancel.trim().length >= 5
      ) {
        return false;
      } else {
        return true;
      }
    },
    domainName() {
      return this.$store.getters.domain;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    openFormInModal() {
      if (this.showViewForm) {
        return true;
      }
      return false;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    this.findInvalidResignationId();
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    firstCharacterValidation(value) {
      if (value && value.trimStart() === "") {
        return "The first character cannot be a space.";
      }
      return true;
    },
    openCancelModal() {
      this.cancelModel = true;
    },
    openAddForm() {
      this.showResignationForm = true;
    },
    enableAdd() {
      const isEnable =
        this.formAccess &&
        this.resignationDetails?.length !== 0 &&
        this.formAccess.add &&
        !this.resignationDetails.some((details) => {
          return (
            details.resignationStatus === "Applied" ||
            details.resignationStatus === "Incomplete"
          );
        });
      return isEnable;
    },
    closeAddEditForm() {
      this.showResignationForm = false;
    },
    statusColor(status) {
      if (status === "Approved" || status === "Applied") {
        return "text-green";
      } else if (status === "Canceled" || status === "Withdrawn") {
        return "text-amber";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-primary";
      }
    },

    openWorkflowModel(createResponse) {
      this.createResignationResponse = createResponse;
      this.showResignationForm = false;
      if (createResponse.dynamicFormTemplate) {
        this.formJsonData = JSON.parse(
          createResponse.dynamicFormTemplate?.template
        );
        this.createTaskId = createResponse?.workflow?.workflowTaskId;
        this.conversationalId =
          createResponse.dynamicFormTemplates?.conversational;
        this.showApproval = true;
        this.selectedItem = createResponse.resignation;
        this.showWorkFlowModel = true;
      } else {
        this.$emit("refetch-resignation-details");
      }
    },
    async validateCancelResignation() {
      this.cancelModel = false;
      this.findInvalidResignationId();
      if (this.selectedReasonForCancel && this.resignationIdWithInvalidStatus) {
        this.withdrawnResignation();
      }
    },
    findInvalidResignationId() {
      // Iterate through the resignationDetails to find a record with invalid status
      const invalidRecord = this.resignationDetails.find((detail) => {
        const status = detail.resignationStatus.toLowerCase();
        return status === "incomplete" || status === "applied";
      });
      this.resignationIdWithInvalidStatus = invalidRecord
        ? invalidRecord.resignationId
        : null;
    },
    async withdrawnResignation() {
      let vm = this;
      vm.isLoading = true;
      await vm.$apollo
        .mutate({
          mutation: WITHDRAWN_CANCEL_RESIGNATION,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: this.loginEmployeeId,
              orgCode: this.orgCode,
              formId: 292,
            },
            approvalStatus: "Withdrawn",
            comment: this.selectedReasonForCancel,
            resignationId: parseInt(this.resignationIdWithInvalidStatus),
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.withdrawnCancelResignation &&
            !response.data.withdrawnCancelResignation.error
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Resignation Withdrawn successfully!",
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-resignation-details");
            vm.isLoading = false;
            vm.cancelModel = false;
          } else {
            let errMsg =
              response?.data?.withdrawnCancelResignation?.error ?? "";
            vm.handleCancelErrors(errMsg);
          }
        })
        .catch((error) => {
          vm.handleCancelErrors(error);
        });
    },
    handleCancelErrors(err = "") {
      this.isLoading = false;
      // Check if the error code is "ERE0130"
      if (err?.code === "ERE0130") {
        var snackbarData = {
          isOpen: true,
          type: "warning",
          message: err.message,
        };
        this.showAlert(snackbarData);
      } else {
        // Handle other errors as usual
        this.$store.dispatch("handleApiErrors", {
          error: err,
          action: "withdrawn",
          form: "Resignation",
          isListError: false,
        });
      }
    },
    openViewForm(item) {
      if (item.resignationStatus !== "Canceled") {
        this.selectedItem = item;
        this.isEmployee = false;
        this.showViewForm = true;
      }
    },
    closeAllForms() {
      this.showViewForm = false;
    },
    openWorkFlowFromViewForm(clickResponse, resignationId, taskAction) {
      this.dynamicViewResignationId = parseInt(resignationId);
      this.closeAllForms();
      this.dynamicFormViewNodeData = clickResponse;
      if (this.dynamicFormViewNodeData?.taskId) {
        this.getDynamicFormDetails(
          this.dynamicFormViewNodeData?.taskId,
          this.dynamicFormViewNodeData?.formIdentifier,
          this.dynamicFormViewNodeData?.processInstanceId,
          taskAction?.task_action
        );
      }
    },
    getDynamicFormDetails(
      workflowTaskId,
      dynamicFormId,
      processInstanceId,
      taskAction
    ) {
      let vm = this;
      vm.isDynamicForm = true;
      vm.processInstanceId = processInstanceId;
      vm.$apollo
        .query({
          query: GET_DYNAMIC_FORM_DETAILS,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
              formId: 292,
            },
            dynamicFormId: parseInt(dynamicFormId),
            workflowTaskId: workflowTaskId,
          },
          client: "apolloClientZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getWorkflowTaskDynamicFormDetails &&
            response.data.getWorkflowTaskDynamicFormDetails.result
          ) {
            const { dynamicFormTemplates, dynamicFormResponse } =
              response.data.getWorkflowTaskDynamicFormDetails.result;
            let formJson = "";
            if (dynamicFormResponse) {
              // response of the form -- empty if it is not submitted before, or the previous response is returned
              formJson = dynamicFormResponse.formResponse;
            } else {
              // form template for new data
              formJson = dynamicFormTemplates.template;
            }
            vm.formJsonData = JSON.parse(formJson);
            vm.conversationalId = dynamicFormTemplates?.conversational;
            vm.formResponseId = dynamicFormResponse
              ? parseInt(dynamicFormResponse?.formResponseId)
              : null;
            vm.showApproval = taskAction ? true : false;
            vm.showWorkFlowModel = true;
            vm.isDynamicForm = false;
          } else {
            vm.handleDynamicFormRetrieveError();
          }
        })
        .catch(() => {
          vm.handleDynamicFormRetrieveError();
        });
    },
    handleDynamicFormRetrieveError() {
      this.isDynamicForm = false;
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message:
          "Something went wrong while retrieving the form details. Please try after some time.",
      };
      this.showAlert(snackbarData);
    },
    closeFormRenderForm() {
      this.showWorkFlowModel = false;
      this.$emit("refetch-resignation-details");
    },
    callUpdateTask() {
      let inputVariables = {
        task_id: this.dynamicFormViewNodeData?.taskId
          ? this.dynamicFormViewNodeData?.taskId
          : this.createResignationResponse?.workflow?.workflowTaskId,
        status: "approve",
        completed_by: this.loginEmployeeId,
        form_data: {},
        status_id: "1002",
        remarks: "",
      };
      let type = "formSubmission";
      this.updateTask(inputVariables, type);
    },

    updateTask(inputParams, type = "") {
      let vm = this;
      vm.showAddEditForm = false;
      try {
        vm.isLoading = true;
        axios
          .post(Config.workflowUrl + "/task/update", inputParams, {
            headers: {
              org_code: vm.orgCode,
              employee_id: vm.loginEmployeeId,
              db_prefix: vm.domainName,
              irukka_id_token: this.irukkaIdToken,
              Authorization: window.$cookies.get("accessToken")
                ? window.$cookies.get("accessToken")
                : "",
              refresh_token: window.$cookies.get("refreshToken")
                ? window.$cookies.get("refreshToken")
                : null,
              partnerid: window.$cookies.get("partnerid")
                ? window.$cookies.get("partnerid")
                : "-",
              additional_headers: JSON.stringify({
                d_code: window.$cookies.get("d_code"),
                b_code: window.$cookies.get("b_code"),
                org_code: vm.orgCode,
                user_ip: this.$store.state.userIpAddress,
                Authorization: window.$cookies.get("accessToken")
                  ? window.$cookies.get("accessToken")
                  : null,
                refresh_token: window.$cookies.get("refreshToken")
                  ? window.$cookies.get("refreshToken")
                  : null,
                partnerid: window.$cookies.get("partnerid")
                  ? window.$cookies.get("partnerid")
                  : "-",
              }),
            },
          })
          .then(() => {
            vm.isLoading = false;
            let snackbarData;
            if (type === "formSubmission") {
              snackbarData = {
                isOpen: true,
                message: "Form submission completed and approved successfully",
                type: "success",
              };
            } else {
              snackbarData = {
                isOpen: true,
                message: "Approved Successfully",
                type: "success",
              };
            }
            vm.showAlert(snackbarData);
            vm.$emit("refetch-resignation-details");
          })
          .catch(function (e) {
            vm.handleUpdateTaskError(e);
          });
      } catch {
        vm.handleUpdateTaskError();
      }
    },
    handleUpdateTaskError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message:
          this.actionType === "approve"
            ? "Something went wrong while approving the record. Please try after some time."
            : "Something went wrong while rejecting the record. Please try after some time.",
        type: "warning",
      };
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "ERR-756": // for multiple scenarios
              if (err.response.data.message === "Task id not found") {
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as it was already deleted/approved/rejected in the same or some other user session."
                    : "Unable to reject the record as it was already deleted/approved/rejected in the same or some other user session.";
              } else if (
                err.response.data.message ===
                "Unable to update status as the employee's available leave balance is less than applied leave"
              ) {
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as the employee's available leave balance is less than applied leave."
                    : "Unable to reject the record as the employee's available leave balance is less than applied leave.";
              } else if (
                err.response.data.message ===
                "Error while processing the request to update the leave status. Please contact the system admin."
              ) {
                snackbarData.message =
                  "Something went wrong while processing the request to update the leave status. Please contact the platform administrator.";
              } else if (
                err.response.data.message ===
                "Error while updating the workflow status. Please contact the system admin."
              ) {
                snackbarData.message =
                  "Something went wrong while updating the workflow status. Please contact the platform administrator.";
              } else if (
                err.response.data.message ===
                "Error while processing the request to update the leave workflow status."
              ) {
                snackbarData.message =
                  "Something went wrong while processing the request to update the leave workflow status. Please contact the platform administrator.";
              } else if (
                err.response.data.message ===
                  "Leave cannot be updated as the full and final settlement is initiated or settled for the employee" ||
                err.response.data.message ===
                  "Leave cannot be added or updated as the full and final settlement is initiated or settled for the employee.Kindly delete the F & F settlement in order to make the necessary modifications."
              ) {
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as the employee's full and final settlement is initiated or settled"
                    : "Unable to reject the record as the employee's full and final settlement is initiated or settled";
              } else if (
                err.response.data.message ===
                "Error while processing the request to update the lop recovery workflow status."
              ) {
                snackbarData.message =
                  "Something went wrong while processing the request to update the lop recovery status. Please contact the platform administrator.";
              } else if (
                err.response.data.message === "The leave record does not exist."
              ) {
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as the leave record does not exits"
                    : "Unable to reject the record as the leave record does not exits";
              } else {
                snackbarData.message =
                  "Something went wrong while processing the request to update the status. Please contact the platform administrator.";
              }
              this.$emit("on-status-update");
              break;
            case "ERR-753": // task id not found
              snackbarData.message =
                this.actionType === "approve"
                  ? "Unable to approve the record as it was already deleted/approved/rejected in the same or some other user session."
                  : "Unable to reject the record as it was already deleted/approved/rejected in the same or some other user session.";
              this.$emit("on-status-update");
              break;
            case "ERR-799": // Status id not found
              snackbarData.message =
                this.actionType === "approve"
                  ? "Unable to approve the record as the status is invalid. If you continue to see this issue please contact the platform administrator."
                  : "Unable to reject the record as the status is invalid. If you continue to see this issue please contact the platform administrator.";
              this.$emit("on-status-update");
              break;
            case "ERR-754": // task already approved or rejected
              snackbarData.message =
                this.actionType === "approve"
                  ? "Unable to approve the record as it was already approved in the same or some other user session."
                  : "Unable to reject the record as it was already rejected in the same or some other user session.";
              this.$emit("on-status-update");
              break;
            case "ERR-798": // Error while executing the query
            default:
              this.actionType === "approve"
                ? "Something went wrong while approving the record. If you continue to see this issue please contact the platform administrator."
                : "Something went wrong while rejecting the record. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        }
      }
      this.showAlert(snackbarData);
      this.showAddEditForm = false;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.emptyDiv {
  margin: 30px 100px 0px 100px !important;
  box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px,
    rgba(17, 17, 26, 0.1) 0px 8px 24px, rgba(17, 17, 26, 0.1) 0px 16px 56px;
}
</style>
