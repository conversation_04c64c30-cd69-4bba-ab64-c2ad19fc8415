<template>
  <v-container class="import-container" fluid>
    <v-row justify="center">
      <v-col cols="12" lg="11" md="12" sm="12">
        <v-card min-height="560" class="rounded-lg">
          <v-card-text>
            <div class="text-center mb-6">
              <span v-for="i in 3" :key="i">
                <v-icon color="primary" size="18" class="ml-1">{{
                  currentStep >= i ? "fas fa-circle" : "far fa-circle"
                }}</v-icon>
              </span>
            </div>
            <v-card-text v-show="currentStep === 1" class="mb-5">
              <div>
                <v-form ref="forms">
                  <v-container>
                    <v-row>
                      <v-col cols="12">
                        <div class="font-weight-bold text-body-1">
                          Select the import type
                        </div>
                      </v-col>
                      <v-col cols="6">
                        <v-autocomplete
                          v-model="employeeDetails"
                          :items="employeeDetailsOptions"
                          variant="underlined"
                          :rules="[
                            (v) => !!v || 'Employee details type is required',
                          ]"
                          required
                        >
                          <template v-slot:label>
                            <span class="text-subtitle-1"
                              >Select the employee details to export</span
                            ><strong class="pl-1 text-red-darken-1">*</strong>
                          </template>
                        </v-autocomplete>
                      </v-col>
                      <v-col cols="6">
                        <v-autocomplete
                          v-model="fields"
                          :items="fieldOptionsArray"
                          item-value="value"
                          item-title="text"
                          multiple
                          clearable
                          chips
                          closable-chips
                          variant="underlined"
                          :rules="[
                            (value) =>
                              !!employeeDetails ||
                              'Employee details type is required to display the fields',
                            (value) =>
                              (value && value.length > 0) ||
                              'Please select atleast one field',
                          ]"
                          required
                        >
                          <template v-slot:label>
                            <span class="text-subtitle-1">Select Fields</span>
                            <strong class="pl-1 text-red-darken-1">*</strong>
                          </template>
                        </v-autocomplete>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-form>
              </div>
            </v-card-text>
            <BulkImportStep1
              class="mb-10"
              v-show="currentStep === 1"
              ref="bulkStep1"
              :step1-text="step1Text"
              :showDownload="true"
              @generate-excel="openExportMenu = true"
              @file-upload-success="uploadFile($event)"
              @file-upload-error="fileRemoveOrError()"
            >
            </BulkImportStep1>
            <BulkImportStep2
              class="mb-10 pb-5"
              v-if="
                fileContent.length > 0 && fields.length && currentStep === 2
              "
              ref="bulkStep2"
              :file-params="fileContent"
              :headers-selected="selectedHeaders"
              @column-mapped="
                matchedCount = $event[0];
                mappedFileHeader = $event[1];
              "
            ></BulkImportStep2>
            <BulkImportStep3
              class="mb-10"
              ref="bulkImportStep3"
              v-if="checkMatchedFields && currentStep === 3"
              :fields="generateFields"
              :json-data="excelEditorData"
              :extend-validation="effectiveDates"
              type-of-import="employee"
            ></BulkImportStep3>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-bottom-navigation v-model="openBottomSheet">
      <v-sheet
        class="align-center text-center"
        :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      >
        <v-row justify="center">
          <v-col
            v-if="!isLoadUploadButton"
            cols="6"
            class="pa-0 d-flex justify-start align-center pl-2"
          >
            <v-btn
              v-if="currentStep > 1"
              id="back_to_step"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              :dense="isMobileView"
              color="secondary"
              @click="backToStep()"
            >
              <span><i class="fa fa-chevron-left pr-2"></i> Back</span></v-btn
            >
            <v-btn
              id="cancel_step"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              :dense="isMobileView"
              class="ml-2"
              color="secondary"
              @click="closeForm()"
              >Cancel</v-btn
            >
          </v-col>
          <v-col
            :cols="isLoadUploadButton ? '12' : '6'"
            class="pa-0 d-flex justify-center align-center pr-4"
            :style="windowWidth >= 1264 ? 'margin-left: -106px' : ''"
          >
            <div v-if="windowWidth > 768" class="text-end mr-2">
              <div class="mr-1 text-grey text-caption" style="width: 400px">
                {{ nextBtnHelpContent }}
              </div>
            </div>
            <v-btn
              id="next_step"
              rounded="lg"
              color="primary"
              class="mr-10"
              :disabled="!enableNextButton"
              :loading="isLoadUploadButton"
              :size="isMobileView ? 'small' : 'default'"
              :dense="isMobileView"
              @click="nextStep()"
            >
              <v-span>
                {{ currentStep === 3 ? "Submit" : "Next" }}
                <v-icon v-if="currentStep !== 3" class="pl-1" size="15"
                  >fa fa-chevron-right</v-icon
                >
              </v-span>
            </v-btn>
          </v-col>
          <v-col
            v-if="windowWidth <= 768 && nextBtnHelpContent"
            class="pa-1 d-flex align-center pr-4 justify-end"
            cols="12"
          >
            <div class="mr-1 text-grey mb-0" style="font-size: 10px">
              {{ nextBtnHelpContent }}
            </div>
          </v-col>
        </v-row>
      </v-sheet>
    </v-bottom-navigation>
    <v-dialog v-model="importConfirmation" width="50%">
      <v-card>
        <v-row>
          <v-col v-if="invalidData && invalidData.length" cols="12">
            <v-alert prominent type="warning">
              <v-row align="center">
                <v-col v-if="invalidData" class="grow"
                  ><span>{{ invalidEmployees.length }}</span>
                  out of
                  {{ excelEditorData.length }} employee do not have valid
                  records.This may result in omittion of update for those
                  records
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertUpdateEmployeeData(finalUpdateData)"
                    >Update anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
          <v-col v-else cols="12" class="pa-3">
            <v-alert prominent type="success">
              <v-row align="center">
                <v-col class="grow">
                  Everything looks <strong>good</strong>.
                  <div class="pt-1">
                    Are you
                    <strong>sure</strong> you want to import the employee
                    details?
                  </div>
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertUpdateEmployeeData(finalUpdateData)"
                    >Update anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-overlay
          class="align-center justify-center"
          contained
          :model-value="isLoading"
          scrim="#fff"
        >
          <v-progress-circular color="primary" indeterminate size="64">
          </v-progress-circular>
        </v-overlay>
      </v-card>
    </v-dialog>
    <ExportEmployeeDataDialog
      screen="import"
      :open-dialog="openExportMenu"
      :formId="243"
      @close-modal="openExportMenu = false"
    >
    </ExportEmployeeDataDialog>
  </v-container>
</template>
<script>
import isEqual from "lodash/isEqual";
import BulkImportStep1 from "@/views/common/bulkImport/BulkImportStep1.vue";
import BulkImportStep2 from "@/views/common/bulkImport/BulkImportStep2.vue";
import BulkImportStep3 from "@/views/common/bulkImport/BulkImportStep3.vue";
//Queries
import {
  IMPORT_EMPLOYEE_DATA,
  GET_EFFECTIVE_DATES,
} from "@/graphql/corehr/employeeDataQueries.js";
import ExportEmployeeDataDialog from "./ExportEmployeeDataDialog.vue";
import moment from "moment";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
export default {
  name: "ImportEmployeeData",
  components: {
    BulkImportStep1,
    BulkImportStep2,
    BulkImportStep3,
    ExportEmployeeDataDialog,
  },
  data: () => ({
    openExportMenu: false,
    showUpdateForm: true,
    showAddForm: false,
    currentStep: 1,
    fileContent: [],
    errorsCountInExcel: 0,
    matchedCount: 0,
    openBottomSheet: true,
    isLoadUploadButton: false,
    mappedFileHeader: [],
    step1Text: {
      typeofData: "employee",
      text: "You have the option of using our predefined template or bring in your own employee data sheet with the headers for import",
      heading: "Upload the excel file",
    },
    selectedImportType: 0,
    isImportAddEmployeeDataOpen: false,
    employeeDetailsOptions: ["Employee Job Details"],
    employeeDetails: "Employee Job Details",
    fieldOptions: {
      Email: "Email",
      WorkSchedule: "WorkSchedule",
      Designation: "Designation",
      Department: "Department",
      Location: "Location",
      EmployeeType: "EmployeeType",
      Manager: "Manager",
    },
    fields: [],
    optionValues: {},
    excelEditorData: [],
    importConfirmation: false,
    finalFormData: {
      Email: "Emp_Email",
      WorkSchedule: "Work_Schedule",
      Designation: "Designation_Id",
      Department: "Department_Id",
      Location: "Location_Id",
      EmployeeType: "EmpType_Id",
      Manager: "Manager_Id",
    },
    finalExcelData: [],
    finalUpdateData: [],
    isLoading: false,
    step2HeadersData: [],
    effectiveDates: [],
  }),

  computed: {
    fieldOptionsArray() {
      return Object.values(this.fieldOptions);
    },
    selectedHeaders() {
      mixpanel.track("MyTeam-import-headers-selected");
      //Form the selectedHeaders with fields and fieldOptions
      let output = [];
      this.fields.forEach((field) => {
        const internalName = Object.keys(this.fieldOptions).find((key) =>
          key.toLowerCase().includes(field.toLowerCase())
        );
        const displayName = this.fieldOptions[internalName];

        if (internalName) {
          output.push({
            title: displayName,
            value: internalName,
            props: {
              disabled: false,
            },
          });
        }
      });
      //Default inclusion of Employee_Id
      output.unshift(
        {
          title: "EmployeeId",
          value: "EmployeeId",
          props: {
            disabled: false,
          },
        },
        {
          title: "Employee Name",
          value: "EmployeeName",
          props: {
            disabled: false,
          },
        }
      );
      return output;
    },
    invalidData() {
      return this.$refs.bulkImportStep3.invalidData;
    },
    invalidEmployees() {
      let invalidData = this.$refs.bulkImportStep3.invalidData;
      let employeeFail = Array.from(new Set(invalidData));
      return employeeFail;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    // enable next button based on current step and scenarios
    enableNextButton() {
      if (
        this.currentStep === 1 &&
        this.fileContent.length > 0 &&
        this.fields.length
      ) {
        this.getEffectiveDates();
        return true;
      } else if (this.currentStep === 2 && this.checkMatchedFields) {
        return true;
      } else if (this.currentStep === 3) {
        this.formattedFileContent();
        return true;
      } else {
        return false;
      }
    },

    // next button help content based on current step and scenarios
    nextBtnHelpContent() {
      if (this.currentStep === 1) {
        if (this.fileContent.length === 0)
          return "Please import the data with supported file types (CSV, XLSX and XLS) to continue with the next step.";
        else return "";
      } else if (this.currentStep === 2) {
        return "The unmatched optional column(s) will not be processed in the next step.";
      } else if (this.currentStep === 3) {
        if (this.formattedFileContent.length === 0) {
          return "";
        } else if (this.errorsCountInExcel !== 0) {
          return "There seems to be some validation error(s) in your file. Please amend it before uploading.";
        } else {
          return "By clicking the 'Submit' button, you can import employee data.";
        }
      } else {
        return "";
      }
    },

    // check mandatory fields all are matched
    checkMatchedFields() {
      let mandatoryHeader = this.fields;
      if (this.matchedCount >= this.fields.length + 2) {
        let mandatoryMatchedCount = 0;
        for (var i in this.mappedFileHeader) {
          if (mandatoryHeader.includes(this.mappedFileHeader[i].hrapp_header))
            mandatoryMatchedCount++;
        }
        //Get the headers assigned in bulk import step 2
        this.addHeaders();

        //  if all the mandatory field are matched then return true else false
        return mandatoryMatchedCount === this.fields.length ? true : false;
      } else return false;
    },

    // get the data from excel file without empty values
    excelFileData() {
      return this.fileContent.filter(
        (content) => content.filter(Boolean).length > 1
      );
    },
    generateFields() {
      const formOutput = [];
      let fieldOptions = JSON.parse(JSON.stringify(this.fieldOptions));
      // Loop through the fields array and create an object for each field
      this.fields.forEach((fieldName) => {
        const options = this.optionValues[fieldName];
        const fieldType = Object.keys(fieldOptions).filter(function (key) {
          return fieldOptions[key] === fieldName;
        })[0];

        // Create the object for this field
        const fieldObject = {
          field: fieldType,
          label: fieldName,
          type: "string",
          width: "200px",
          validate: true,
        };

        // Add the field object to the form output array
        formOutput.push(fieldObject);

        // Add options to the object if they exist
        if (options) {
          fieldObject.type = "select";
          if (fieldType === "WorkSchedule") {
            fieldObject.options = options.map((el) => el.Title);
            formOutput.push({
              field: "Work_Schedule_End_Date",
              label: "WorkSchedule Effective Date",
              type: "date",
              width: "200px",
            });
          } else if (fieldType === "Designation") {
            fieldObject.options = options.map((el) => el.Designation_Name);
            formOutput.push({
              field: "Designation_Id_End_Date",
              label: "Designation Effective Date",
              type: "date",
              width: "200px",
            });
          } else if (fieldType === "Department") {
            fieldObject.options = options.map((el) => el.Department_Name);
            formOutput.push({
              field: "Department_Id_End_Date",
              label: "Department Effective Date",
              type: "date",
              width: "200px",
            });
          } else if (fieldType === "Location") {
            fieldObject.options = options.map((el) => el.Location_Name);
            formOutput.push({
              field: "Location_Id_End_Date",
              label: "Location Effective Date",
              type: "date",
              width: "200px",
            });
          } else if (fieldType === "EmployeeType") {
            fieldObject.options = options.map((el) => el.Employee_Type);
            formOutput.push({
              field: "EmpType_Id_End_Date",
              label: "EmployeeType Effective Date",
              type: "date",
              width: "200px",
            });
          } else if (fieldType === "Manager") {
            fieldObject.options = options.map((el) => el.Manager_Name);
            formOutput.push({
              field: "Manager_Id_End_Date",
              label: "Manager Effective Date",
              type: "date",
              width: "200px",
            });
          }
        }
      });

      // Default Inclusion
      formOutput.unshift(
        {
          field: "EmployeeId",
          label: "Employee Id",
          type: "string",
          readonly: true,
          width: "200px",
        },
        {
          field: "Employee Name",
          label: "Employee Name",
          type: "string",
          readonly: true,
          width: "200px",
        }
      );

      return formOutput;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.getDropDownBoxDetails();
  },
  methods: {
    //Get effective Dates
    getEffectiveDates() {
      let vm = this;
      //Get all employee ids from fileContent
      let allEmployeeIds = vm.fileContent.map((el) => {
        return el[0];
      });
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_EFFECTIVE_DATES,
          client: "apolloClientI",
          variables: {
            employeeIds: allEmployeeIds,
          },
          fetchPolicy: "no-cache",
        })
        .then(async (response) => {
          mixpanel.track("MyTeam-import-effectiveDate-fetch-success");
          if (response && response.data && response.data.getEffectiveDate) {
            vm.effectiveDates = response.data.getEffectiveDate.getEffectiveDate
              ? response.data.getEffectiveDate.getEffectiveDate
              : [];
            vm.isLoading = false;
          } else {
            vm.handleEffectiveDate();
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.handleEffectiveDate(err);
          vm.isLoading = false;
        });
    },
    handleEffectiveDate(err = "") {
      mixpanel.track("MyTeam-import-effectiveDate-fetch-error");
      let snackbarData = {
        isOpen: true,
        type: "warning",
      };
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "_DB0000": // technical errors
              snackbarData.message =
                "It’s us! There seem to be some technical difficulties. Please try after some time.";
              break;
            case "_UH0001": // unhandled error
            case "_DB0001": // Error while retrieving the employee access rights
            case "_DB0002": // Error while checking the employee access rights
            case "_DB0104": // While check access rights form not found
            case "EDM0104": // Error while getting effective dates
            case "EDM0004": // Error while processing the request to retrieve effective dates
            default:
              snackbarData.message =
                "Something went wrong while retrieving the effective date details. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        } else {
          snackbarData.message =
            "Something went wrong while retrieving the effective date details. Please try after some time.";
        }
      } else {
        snackbarData.message =
          "Something went wrong while retrieving the effective date details. Please try after some time.";
      }
    },
    getDropDownBoxDetails() {
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              designations,
              locations,
              employeeType,
              workSchedules,
              managers,
            } = res.data.getDropDownBoxDetails;

            this.optionValues.Designation = designations;
            this.optionValues.Department = departments;
            this.optionValues.Location = locations;
            this.optionValues.EmployeeType = employeeType;
            this.optionValues.WorkSchedule = workSchedules;
            this.optionValues.Manager = managers;
            this.isLoading = false;
          } else {
            this.handleImportEmployeeError();
          }
        })
        .catch((err) => {
          this.handleImportEmployeeError(err);
        });
    },
    formattedFileContent() {
      //With Fields form the headers
      let generatedData = this.formExcelData();
      this.excelEditorData = generatedData;
    },
    formExcelData() {
      let fields = this.generateFields;
      let data = JSON.parse(JSON.stringify(this.excelFileData));
      let headersAssigned = this.step2HeadersData;

      //Getting the field of the array of objects
      let excelData = [];
      let idCounter = 1;
      // Iterate through each row of data
      for (let i = 1; i < data.length; i++) {
        let rowData = data[i];
        let rowObj = { $id: "000000" + idCounter++ };

        // Iterate through each field definition and populate the row object
        for (let j = 0; j < fields.length; j++) {
          let fieldDef = fields[j];
          let fieldName = fieldDef.field;

          // Find the index of the field in the header mappings array
          let headerIndex = headersAssigned.findIndex(
            (header) => header.hrapp_header === fieldName
          );

          // If the field is present in the header mappings array, use the corresponding value from the input data
          if (headerIndex >= 0) {
            let dataValue = rowData[headerIndex];
            if (dataValue !== null && dataValue !== undefined) {
              rowObj[fieldName] = dataValue;
            } else {
              rowObj[fieldName] = null;
            }
          } else {
            // If the field is not present in the header mappings array, use the default value for the field type
            switch (fieldDef.type) {
              case "string":
                rowObj[fieldName] = "";
                break;
              case "number":
                rowObj[fieldName] = 0;
                break;
              case "boolean":
                rowObj[fieldName] = false;
                break;
              default:
                rowObj[fieldName] = null;
                break;
            }
          }
        }
        excelData.push(rowObj);
      }
      //Include Effective dates
      excelData = excelData.map((data) => {
        const matchingDate = this.effectiveDates.find(
          (date) => date.Employee_Id === data.EmployeeId
        );
        if (matchingDate) {
          for (const key in data) {
            if (key.endsWith("_End_Date")) {
              if (!matchingDate[key] || matchingDate[key] === "0000-00-00") {
                matchingDate[key] = moment(matchingDate.Date_Of_Join).subtract(
                  1,
                  "days"
                );
              }
              //Form the date
              let maxPayslipDate = matchingDate.Effective_Date;
              if (maxPayslipDate) {
                let effectiveDate = moment(matchingDate[key]).add(1, "days");

                let minDate = moment
                  .max(moment(maxPayslipDate), effectiveDate)
                  .format("YYYY-MM-DD");
                data[key] = minDate;
              } else {
                let maxPayslipDate = matchingDate.Date_Of_Join;
                let effectiveDate = moment(matchingDate[key]).add(1, "days");

                let minDate = moment
                  .max(moment(maxPayslipDate), effectiveDate)
                  .format("YYYY-MM-DD");
                data[key] = minDate;
              }
            }
          }
        }

        return data;
      });
      return excelData;
    },
    // called cancel is clicked to close form
    closeForm() {
      mixpanel.track("MyTeam-import-form-closed");
      this.$emit("close-modal");
    },
    // back button clicks, to subtract 1 from current step
    backToStep() {
      mixpanel.track("MyTeam-import-backTo-prev-step");
      this.currentStep = this.currentStep - 1;
      this.allRecordsFail = false;
    },

    // next button clicks, to add 1 from current step
    nextStep() {
      mixpanel.track("MyTeam-import-to-next-step");
      if (this.currentStep === 3) {
        this.formBulkData(this.$refs.bulkImportStep3.filteredData);
      } else {
        this.currentStep += 1;
      }
    },

    addHeaders() {
      if (this.$refs.bulkStep2 && this.$refs.bulkStep2.tableItems) {
        this.step2HeadersData = this.$refs.bulkStep2.tableItems;
      }
    },

    formBulkData(data) {
      this.finalExcelData = data;
      // const filteredData = data.filter((obj) => {
      //   return Object.values(obj).every((value) => value !== null);
      // });
      const filteredData = data;
      this.finalUpdateData = filteredData.map((employee) => {
        let employeeData = {};
        for (let field of this.fields) {
          employeeData.Employee_Id = employee["EmployeeId"];
          if (field === "WorkSchedule") {
            //Get the WorkSchedule Id
            let workschedule = this.optionValues.WorkSchedule.find(
              (el) => el.Title === employee.WorkSchedule
            );
            if (workschedule) {
              employeeData.Work_Schedule = workschedule.WorkSchedule_Id;
              employeeData.Work_Schedule_End_Date =
                employee.Work_Schedule_End_Date;
            }
          }
          if (field === "Designation") {
            //Get the Designation Id
            let designation = this.optionValues.Designation.find(
              (el) => el.Designation_Name === employee.Designation
            );
            if (designation) {
              employeeData.Designation_Id = designation.Designation_Id;
              employeeData.Designation_Id_End_Date =
                employee.Designation_Id_End_Date;
            }
          }
          if (field === "Department") {
            //Get the Department Id
            let department = this.optionValues.Department.find(
              (el) => el.Department_Name === employee.Department
            );
            if (department) {
              employeeData.Department_Id = department.Department_Id;
              employeeData.Department_Id_End_Date =
                employee.Department_Id_End_Date;
            }
          }
          if (field === "Location") {
            //Get the Location Id
            let location = this.optionValues.Location.find(
              (el) => el.Location_Name === employee.Location
            );
            if (location) {
              employeeData.Location_Id = location.Location_Id;
              employeeData.Location_Id_End_Date = employee.Location_Id_End_Date;
            }
          }
          if (field === "EmployeeType") {
            //Get the Designation Id
            let employeeType = this.optionValues.EmployeeType.find(
              (el) => el.Employee_Type === employee.EmployeeType
            );
            if (employeeType) {
              employeeData.EmpType_Id = employeeType.EmpType_Id;
              employeeData.EmpType_Id_End_Date = employee.EmpType_Id_End_Date;
            }
          }
          if (field === "Manager") {
            //Get the Designation Id
            let manager = this.optionValues.Manager.find(
              (el) => el.Manager_Name === employee.Manager
            );
            if (manager) {
              employeeData.Manager_Id = manager.Manager_Id;
              employeeData.Manager_Id_End_Date = employee.Manager_Id_End_Date;
            } else {
              employeeData.Manager_Id = null;
            }
          }
          if (field === "Email") {
            employeeData.Emp_Email = employee["Email"] ? employee["Email"] : "";
          }
        }
        return employeeData;
      });
      this.importConfirmation = true;
    },

    insertUpdateEmployeeData(data) {
      mixpanel.track("MyTeam-import-start");
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: IMPORT_EMPLOYEE_DATA,
          client: "apolloClientJ",
          variables: {
            typeOfEmployeeDetails: vm.employeeDetails,
            employeeData: JSON.stringify(data),
            typeOfImport: "update",
            isAsync: 0,
          },
        })
        .then(async (response) => {
          mixpanel.track("MyTeam-import-success");
          if (response && response.data && response.data.importEmployeeData) {
            const { validationFails } = response.data.importEmployeeData;
            if (!validationFails.length) {
              //Check if frontend validation
              //Getting remaining data which failed from frontend
              let excelInvalidData = this.$refs.bulkImportStep3.invalidData;
              if (excelInvalidData && excelInvalidData.length) {
                //Get the excel data to compare
                let inputData = this.$refs.bulkImportStep3.editorData;
                let remainingData = [];
                for (let i = 0; i < inputData.length; i++) {
                  if (excelInvalidData.includes(inputData[i].$id)) {
                    //Check if data is not already there in remaining data
                    if (!remainingData.includes(inputData[i])) {
                      remainingData.push(inputData[i]);
                    }
                  }
                }
                //Swap remainingData for excelData
                this.$refs.bulkImportStep3.editorData = remainingData;
                for (let i = 0; i < validationFails.length; i++) {
                  for (let j = 0; j < remainingData.length; j++) {
                    if (
                      validationFails[i].field ===
                      remainingData[j][validationFails[i].fieldName]
                    ) {
                      let validationFailReason = validationFails[i].reason;
                      let field = {};
                      field.name = validationFails[i].fieldName;
                      this.$refs.bulkImportStep3.setFieldError(
                        validationFailReason,
                        remainingData[j],
                        field
                      );
                    }
                  }
                }
                this.isLoading = false;
                this.importConfirmation = false;
                this.excelEditorData = remainingData;
              } else {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Employee data was updated successfully!",
                };
                vm.showAlert(snackbarData);
                vm.importConfirmation = false;
                vm.closeForm();
              }
            } else {
              let failedDataFromBackend = [];
              for (let i = 0; i < validationFails.length; i++) {
                for (let j = 0; j < this.finalExcelData.length; j++) {
                  if (
                    validationFails[i].field ===
                    this.finalExcelData[j][validationFails[i].fieldName]
                  ) {
                    failedDataFromBackend.push(this.finalExcelData[j]);
                  }
                }
              }
              //Get the excel data to compare
              let inputData = this.$refs.bulkImportStep3.editorData;
              let remainingData = [];
              //Getting remaining data from backend
              for (let i = 0; i < inputData.length; i++) {
                const currentData = inputData[i];
                const match = failedDataFromBackend.find((d) =>
                  isEqual(d, currentData)
                );
                if (match) {
                  remainingData.push(currentData);
                }
              }
              //Getting remaining data which failed from frontend
              let excelInvalidData = this.$refs.bulkImportStep3.invalidData;
              for (let i = 0; i < inputData.length; i++) {
                if (excelInvalidData.includes(inputData[i].$id)) {
                  //Check if data is not already there in remaining data
                  if (!remainingData.includes(inputData[i])) {
                    remainingData.push(inputData[i]);
                  }
                }
              }
              //Swap remainingData for excelData
              this.$refs.bulkImportStep3.editorData = remainingData;
              for (let i = 0; i < validationFails.length; i++) {
                for (let j = 0; j < remainingData.length; j++) {
                  if (
                    validationFails[i].field ===
                    remainingData[j][validationFails[i].fieldName]
                  ) {
                    let validationFailReason = validationFails[i].reason;
                    let field = {};
                    field.name = validationFails[i].fieldName;
                    this.$refs.bulkImportStep3.setFieldError(
                      validationFailReason,
                      remainingData[j],
                      field
                    );
                  }
                }
              }
              this.isLoading = false;
              this.importConfirmation = false;
              this.excelEditorData = remainingData;
            }
          } else {
            vm.handleImportEmployeeError();
            vm.importConfirmation = false;
            vm.isLoading = false;
            vm.closeForm();
          }
        })
        .catch((err) => {
          vm.handleImportEmployeeError(err);
          vm.importConfirmation = false;
          vm.isLoading = false;
          vm.closeForm();
        });
    },
    handleImportEmployeeError(err = "") {
      mixpanel.track("MyTeam-import-error");
      let snackbarData = {
        isOpen: true,
        type: "warning",
      };
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "_DB0000": // technical errors
              snackbarData.message =
                "It’s us! There seem to be some technical difficulties. Please try after some time.";
              break;
            case "_UH0001": // unhandled error
            case "_DB0001": // Error while retrieving the employee access rights
            case "_DB0002": // Error while checking the employee access rights
            case "_DB0104": // While check access rights form not found
            case "EDM0102": // Error while importing employee details
            case "EDM0002": // Error while processing the request to import employees
            default:
              snackbarData.message =
                "Something went wrong while importing the employee details. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        } else {
          snackbarData.message =
            "Something went wrong while importing the employee details. Please try after some time.";
        }
      } else {
        snackbarData.message =
          "Something went wrong while importing the employee details. Please try after some time.";
      }
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    // called when file uploaded in step 1
    uploadFile(event) {
      mixpanel.track("MyTeam-import-file-uploaded");
      this.fileContent = event;
    },
    // called from step 1 when error while uploading or removing the file
    fileRemoveOrError() {
      mixpanel.track("MyTeam-import-file-removed");
      this.fileContent = [];
      this.matchedCount = 0;
      this.errorsCountInExcel = 0;
    },
  },
};
</script>
<style>
.v-bottom-navigation__content {
  justify-content: space-around;
  flex-direction: column;
}
.dp__button_bottom {
  display: none;
}
</style>
