<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12" sm="6">
            <v-autocomplete
              v-model="documentCategory"
              label="Document category"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              color="primary"
              :items="documentCategoryList"
              item-title="Category_Fields"
              item-value="Category_Id"
              :loading="documentCategoryListLoading"
            ></v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="12" sm="6">
            <v-autocomplete
              v-model="documentType"
              label="Document Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              color="primary"
              :items="documentTypeList"
              item-title="Document_Type"
              item-value="Document_Type_Id"
              :loading="documentTypeListLoading"
            ></v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="12" sm="6">
            <v-autocomplete
              v-model="emailTemplate"
              label="Email Template"
              item-title="TemplateName"
              item-value="TemplateName"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              color="primary"
              :items="emailTemplateList"
              :loading="emailTemplateListLoading"
            ></v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="12" sm="6">
            <v-autocomplete
              v-model="tags"
              label="Document Enforcement Group"
              multiple
              closable-chips
              chips
              density="compact"
              itemTitle="Group_Name"
              itemValue="Group_Id"
              single-line
              color="primary"
              :items="tagsList"
              :loading="tagsListLoading"
            ></v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="12" sm="6">
            <v-radio-group inline color="primary" v-model="mandatory">
              <template v-slot:label>
                Enforced during Self Onboarding
              </template>
              <v-radio label="Yes" value="Yes"></v-radio>
              <v-radio label="No" value="No"></v-radio>
            </v-radio-group>
          </v-col>

          <v-col class="py-2" :cols="12" sm="6">
            <v-radio-group inline color="primary" v-model="onlyForEmail">
              <template v-slot:label>
                Allowed for Email communication
              </template>
              <v-radio label="Yes" value="Yes"></v-radio>
              <v-radio label="No" value="No"></v-radio>
            </v-radio-group>
          </v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { LIST_DOC_CATEGORY, LIST_DOC_TYPE } from "@/graphql/dropDownQueries";
import { RETRIEVE_DOCUMENTS_TAG } from "@/graphql/onboarding/individualQueries.js";
import { LIST_EMAIL_TEMPLATE } from "@/graphql/corehr/documentSubtypeQueries";
export default {
  name: "FormFilter",
  data: () => ({
    openFormFilter: false,
    documentCategory: [],
    documentType: [],
    emailTemplate: [],
    tags: [],
    mandatory: "",
    onlyForEmail: "",
    documentTypeListLoading: false,
    documentCategoryListLoading: false,
    tagsListLoading: false,
    emailTemplateListLoading: false,
    documentTypeList: [],
    documentCategoryList: [],
    tagsList: [],
    emailTemplateList: [],
  }),

  props: {},

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.retrieveDocumentCategory();
    this.retrieveDocumentType();
    this.retrieveDocumentTagList();
    this.retrieveEmailTemplate();
  },
  methods: {
    // apply filter
    onChange(value, field) {
      this.isFormDirty = true;
      this[field] = value;
      if (field == "documentCategory") {
        this.filterDocumentTypes(value);
      }
    },
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        documentCategory: this.documentCategory,
        documentType: this.documentType,
        mandatory: this.mandatory,
        tags: this.tags,
        emailTemplate: this.emailTemplate,
        onlyForEmail: this.onlyForEmail,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.documentCategory = [];
      this.documentType = [];
      this.emailTemplate = [];
      this.mandatory = "";
      this.tags = [];
      this.onlyForEmail = "";
      this.retrieveDocumentType();
    },
    retrieveDocumentCategory() {
      let vm = this;
      vm.documentCategoryListLoading = true;
      vm.$apollo
        .query({
          query: LIST_DOC_CATEGORY,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentCategory &&
            !response.data.listDocumentCategory.errorCode
          ) {
            const { documentCategory } = response.data.listDocumentCategory;
            vm.documentCategoryList =
              documentCategory && documentCategory.length > 0
                ? documentCategory
                : [];
          }
          vm.documentCategoryListLoading = false;
        })
        .catch(() => {
          vm.documentCategoryListLoading = false;
        });
    },
    retrieveDocumentType() {
      let vm = this;
      vm.documentTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_DOC_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentType &&
            !response.data.listDocumentType.errorCode
          ) {
            const { documentType } = response.data.listDocumentType;
            vm.documentTypeList =
              documentType && documentType.length > 0 ? documentType : [];
          }
          vm.documentTypeListLoading = false;
        })
        .catch(() => {
          vm.documentTypeListLoading = false;
        });
    },
    filterDocumentTypes(categoryId) {
      this.documentType = null;
      this.documentTypeList = this.documentTypeList.filter((item) => {
        return item.Category_Id == categoryId;
      });
    },

    retrieveDocumentTagList() {
      let vm = this;
      vm.tagsListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_DOCUMENTS_TAG,
          client: "apolloClientV",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.listdocumentEnforcementGroup &&
            data.listdocumentEnforcementGroup.groupIds &&
            data.listdocumentEnforcementGroup.groupIds.length > 0
          ) {
            vm.tagsList = data.listdocumentEnforcementGroup.groupIds;
          }
          vm.tagsListLoading = false;
        })
        .catch(() => {
          vm.tagsListLoading = false;
        });
    },
    retrieveEmailTemplate() {
      let vm = this;
      vm.emailTemplateListLoading = true;
      vm.$apollo
        .query({
          query: LIST_EMAIL_TEMPLATE,
          client: "apolloClientA",
          variables: {
            type: "Email",
            formIds: [178],
          },
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.getNotificationTemplates &&
            data.getNotificationTemplates.notificationTemplates &&
            data.getNotificationTemplates.notificationTemplates.length > 0
          ) {
            vm.emailTemplateList =
              data.getNotificationTemplates.notificationTemplates;
          } else {
            vm.emailTemplateList = [];
          }
          vm.emailTemplateListLoading = false;
        })
        .catch((err) => {
          this.handleListTemplateError(err);
        });
    },
    handleListTemplateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "email templates",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
  },
};
</script>
