<template>
  <div class="text-center">
    <v-overlay
      v-model="overlay"
      class="d-flex justify-end overlay-content-parent"
      @click:outside="onClickClose()"
      persistent
      style="z-index: 1000"
    >
      <template v-slot:default>
        <div class="overlay-card">
          <div
            class="d-flex align-center text-h6 text-medium-emphasis pa-2 bg-primary"
            style="width: 100%"
          >
            <v-icon
              v-if="
                displayCustomEmail ||
                (interviewType != 'Assessment Link' && !isEdit)
              "
              @click="
                displayCustomEmail
                  ? (displayCustomEmail = false)
                  : $emit('close-overlay')
              "
              size="17"
              class="mx-2"
              >fas fa-chevron-left</v-icon
            >
            <span class="ml-2">
              {{ interviewType == "Assessment Link" ? "Send" : "Schedule" }}
              {{ interviewType?.toLowerCase() }}
              {{ interviewType == "Assessment Link" ? "to" : "interview with" }}
              {{ candidateName ? candidateName : "selected candidate(s)" }} ({{
                jobTitle
              }})
            </span>
            <v-spacer></v-spacer>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="onClickClose()"
            ></v-btn>
          </div>
          <div class="overlay-body">
            <v-form v-if="!displayCustomEmail" ref="scheduleInterviewForm">
              <div
                class="d-flex align-center flex-wrap"
                v-if="
                  !candidateWillSelect || interviewType == 'Assessment Link'
                "
              >
                <v-tabs v-model="interviewDate" class="mr-2 mt-n2">
                  <v-tab :value="calculateInterviewDate('0')">
                    Today
                    <v-tooltip activator="parent" location="top">{{
                      calculateInterviewDate("0")
                    }}</v-tooltip>
                  </v-tab>
                  <v-tab :value="calculateInterviewDate('1')">
                    Tomorrow
                    <v-tooltip activator="parent" location="top">{{
                      calculateInterviewDate("1")
                    }}</v-tooltip>
                  </v-tab>
                </v-tabs>
                <datepicker
                  :format="orgDateFormat"
                  v-model="interviewDate"
                  class="custom-date-picker"
                  placeholder="Select Date"
                  :disabled-dates="{
                    to: new Date(previousDate),
                  }"
                ></datepicker>
              </div>
              <div
                class="d-flex justify-space-between mt-5"
                style="max-height: 80px; flex: 1"
              >
                <!-- <CustomSelect
                  v-if="interviewType == 'Face To Face'"
                  :items="locationList"
                  label="Location"
                  :isAutoComplete="true"
                  :itemSelected="jobPostLocation"
                  :isLoading="jobPostLocationLoading"
                  :noDataText="
                    jobPostLocationLoading ? 'Loading...' : 'No data available'
                  "
                  itemValue="Location_Name"
                  itemTitle="Location_Name"
                  :isRequired="true"
                  :rules="[required('Location', jobPostLocation)]"
                  @selected-item="changeFieldValue($event, 'jobPostLocation')"
                  class="mr-4"
                ></CustomSelect>-->
                <CustomSelect
                  label="Rounds"
                  :isRequired="true"
                  :rules="[required('Rounds', roundSelected)]"
                  :items="roundsOptionList"
                  :itemSelected="roundSelected"
                  :isAutoComplete="true"
                  @selected-item="changeFieldValue($event, 'roundSelected')"
                  item-title="Round_Name"
                  item-value="Round_Id"
                  :isLoading="roundListLoading"
                  :noDataText="
                    roundListLoading ? 'Loading...' : 'No data available'
                  "
                ></CustomSelect>
              </div>
              <v-row v-if="!candidateWillSelect">
                <v-col cols="6" v-if="calendarItems.length > 0">
                  <custom-select
                    label="Calendars"
                    :items="calendarItems"
                    :item-selected="selectedCalendar"
                    :show-configure="showConfigArray"
                    :select-properties="{
                      clearable: true,
                    }"
                    @selected-item="
                      (selectedCalendar = $event), callCalendarApi()
                    "
                    @configure-clicked="configureCalendars($event)"
                  ></custom-select>
                </v-col>
                <v-col
                  cols="6"
                  v-if="meetingItems.length > 0 && interviewType == 'Online'"
                >
                  <custom-select
                    label="Meetings"
                    :items="meetingItems"
                    :item-selected="selectedMeeting"
                    :show-configure="showConfigArray"
                    :select-properties="{
                      clearable: true,
                    }"
                    @selected-item="selectedMeeting = $event"
                    @configure-clicked="configureCalendars($event)"
                  ></custom-select>
                </v-col>
              </v-row>
              <v-row class="mt-2" v-if="interviewType == 'Face To Face'">
                <v-col v-if="!isManualEnterForPermanentAddress" cols="12">
                  <VCard>
                    <vue-google-autocomplete
                      id="pMap"
                      ref="pGoogleVal"
                      class="form-control pa-5 google-auto-complete-address-field"
                      placeholder="Please type interview venue"
                      v-on:placechanged="setPermanentAddress"
                      :enable-geolocation="true"
                      :fields="['address_components', 'geometry']"
                    ></vue-google-autocomplete>
                  </VCard>
                  <p
                    v-if="showMessage"
                    class="text-red-darken-1 text-subtitle-2 ml-5"
                  >
                    Please enter a valid address for google map link
                  </p>
                </v-col>
                <v-col cols="12">
                  <div>
                    <v-checkbox-btn
                      v-model="isManualEnterForPermanentAddress"
                      color="primary"
                      false-icon="far fa-square"
                      true-icon="fas fa-check-square"
                      indeterminate-icon="fas fa-minus-circle"
                      class="ml-n2"
                    >
                      <template v-slot:label>
                        <div class="text-grey-darken-1" style="font-size: 16px">
                          No autocomplete address found
                        </div>
                      </template>
                    </v-checkbox-btn>
                  </div>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    variant="solo"
                    v-model="venue"
                    v-if="isManualEnterForPermanentAddress"
                    :rules="[required('Venue', venue)]"
                    @input="resetPermanentAddress()"
                  >
                    <template v-slot:label>
                      Venue
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
              </v-row>
              <!-- <v-text-field
                variant="solo"
                class="mt-2"
                :rules="[required('Venue', venue)]"
                v-model="venue"
              >
                <template v-slot:label>
                  Venue
                  <span style="color: red">*</span>
                </template>
              </v-text-field>-->
              <v-card
                v-for="(item, index) in rounds"
                :key="index"
                class="w-100 mt-5 rounded-lg pa-5 card-blue-background"
                style="height: max-content"
                elevation="2"
              >
                <div class="d-flex justify-space-between mb-5">
                  <p class="text-h6 text-medium-emphasis">
                    {{ item.Round_Name }}
                  </p>
                  <i
                    class="fas fa-trash-alt fa-sm"
                    style="color: lightgrey"
                    @click="deleteRound(index)"
                  ></i>
                </div>
                <CustomSelect
                  label="Panel Members"
                  :items="panelMemberOptions"
                  :item-selected="item.Panel_Members"
                  :isAutoComplete="true"
                  @selected-item="
                    changeFieldValue($event, 'Panel_Members', index)
                  "
                  item-title="Emp_Name"
                  item-value="Employee_Id"
                  subText="Employee_Id"
                  :select-properties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                  :rules="[
                    listLengthValidation(
                      'Panel Members',
                      item.Panel_Members,
                      item.Minimum_Panel_Members,
                      item.Maximum_Panel_members
                    ),
                  ]"
                  :isLoading="panelMemberLoading"
                  :noDataText="
                    panelMemberLoading ? 'Loading...' : 'No data available'
                  "
                ></CustomSelect>
                <div
                  v-if="item.busyPanelMembers?.length > 0"
                  class="bg-amber-lighten-3 pa-2 rounded d-flex align-center"
                >
                  <v-icon size="small" color="amber-darken-3" class="mr-2">
                    fas fa-info-circle
                  </v-icon>
                  <div>
                    {{ item.busyPanelMembers.join(", ") }} is busy during the
                    selected duration. Please select another time slot.
                  </div>
                </div>
                <div
                  v-if="
                    this.selectedMemebers.length > 0 && !candidateWillSelect
                  "
                >
                  <div v-if="!item.showSuggestions" class="d-flex justify-end">
                    <p
                      @click="suggestSlots(index)"
                      class="text-blue-lighten-2 cursor-pointer"
                    >
                      Suggest Slots
                    </p>
                  </div>
                  <div v-else class="bg-hover pa-2 elevation-1 mt-1 rounded">
                    <div class="d-flex justify-space-between">
                      <p class="custom-label">Suggested Start Time</p>
                      <p
                        class="text-blue-lighten-2 cursor-pointer"
                        style="font-size: 0.85em"
                        @click="onShowCalendar(index)"
                      >
                        choose from Calendar
                      </p>
                    </div>
                    <v-slide-group show-arrows class="mt-1">
                      <v-slide-group-item
                        v-for="startTime in item.suggestedStartTimes"
                        :key="startTime"
                      >
                        <v-chip
                          class="mr-2"
                          @click="selectStartTime(startTime, index)"
                          >{{ formatStartTime(startTime.start) }}</v-chip
                        >
                      </v-slide-group-item>
                    </v-slide-group>
                  </div>
                </div>
                <v-text-field
                  clearable
                  :label="
                    interviewType == 'Assessment Link'
                      ? 'Assessment Link'
                      : 'CalenderLink'
                  "
                  variant="solo"
                  class="w-100"
                  v-model="calenderLink"
                  v-if="
                    candidateWillSelect == true ||
                    interviewType == 'Assessment Link'
                  "
                  :rules="[urlValidation(calenderLink)]"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:append-inner>
                    <v-icon>fas fa-link</v-icon>
                  </template>
                </v-text-field>
                <div
                  v-if="candidateWillSelect == false"
                  class="d-flex justify-start mt-3"
                >
                  <v-text-field
                    ref="startTime"
                    label="Start Time"
                    variant="solo"
                    type="time"
                    class="mr-2 w-25"
                    v-model="item.Start_Time"
                    :rules="[validateStartTime(index)]"
                    @update:model-value="onChangeStartEndTime(index)"
                  ></v-text-field>
                  <v-text-field
                    label="End Time"
                    variant="solo"
                    type="time"
                    class="mr-2 w-25"
                    :rules="[validateStartEndTime(index)]"
                    v-model="item.End_Time"
                    @update:model-value="onChangeStartEndTime(index)"
                  ></v-text-field>
                  <v-text-field
                    label="Duration"
                    variant="solo"
                    readonly
                    v-model="item.Duration"
                  ></v-text-field>
                </div>
              </v-card>
              <div
                class="d-flex justify-space-between mt-5"
                style="max-height: 80px; flex: 1"
                v-if="emailTemplateList?.length"
              >
                <CustomSelect
                  :items="emailTemplateList"
                  v-model="selectedEmailTemplate"
                  label="Email Template"
                  itemValue="Template_Id"
                  itemTitle="Template_Name"
                  ref="selectedEmailTemplate"
                  :isAutoComplete="true"
                  :isRequired="true"
                  :rules="[required('Email Template', selectedEmailTemplate)]"
                  :itemSelected="selectedEmailTemplate"
                  @selected-item="selectedEmailTemplate = $event"
                ></CustomSelect>
              </div>
              <AppLoading v-if="isEmailTemplateListLoading"></AppLoading>
            </v-form>
            <CustomEmail
              v-else
              ref="customEmail"
              :formId="16"
              :typeOfTemplate="typeOfTemplate"
              :notificationTimeNow="notificationTimeNow"
              :typeOfSchedule="candidateWillSelect ? 'noncalendar' : 'calendar'"
              :template-email="templateEmail"
              :template-data="templateData"
              :selectedCandidateId="candidateId"
              :emailTemplateList="emailTemplateList"
              :selectedEmailTemplate="selectedEmailTemplate"
              :noCustomTemplate="noCustomTemplate"
              :sendDateAlone="interviewType === 'Assessment Link'"
            />
          </div>
          <v-card class="overlay-footer" elevation="16">
            <v-btn
              class="mr-5"
              variant="outlined"
              @click="
                displayCustomEmail
                  ? (this.displayCustomEmail = false)
                  : onClickClose()
              "
              rounded="lg"
              >Cancel</v-btn
            >
            <v-btn
              color="primary"
              variant="elevated"
              rounded="lg"
              @click="validateInterviewScheduleForm()"
            >
              {{
                displayCustomEmail || isEdit
                  ? interviewType == "Assessment Link"
                    ? "Send Assessment"
                    : "Schedule Interview"
                  : "Preview Email"
              }}
            </v-btn>
          </v-card>
        </div>
        <AppLoading v-if="isLoading"></AppLoading>
      </template>
    </v-overlay>
  </div>
  <panel-members-schedule
    v-if="showCalendar"
    :selected-memebers="selectedMemebers"
    :selected-date="interviewDate"
    @close="showCalendar = false"
  ></panel-members-schedule>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  ></AppWarningModal>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import CustomEmail from "../../common/customEmail/CustomEmailComponent.vue";
import Datepicker from "vuejs3-datepicker";
import Config from "../../../config.js";
import moment from "moment";
import {
  GET_PANEL_MEMBERS_LIST,
  ADD_INTERVIEW_DETAILS,
  RETRIEVE_INTERVIEW_ROUNDS_LIST,
  UPDATE_INTERVIEW_DETAILS,
  GET_PANEL_MEMBERS_SCHEDULE,
  GENERATE_MEETING_URL,
  GET_PANEL_INTERNAL_SCHEDULE,
} from "@/graphql/recruitment/interviewScheduleQueries";
import {
  SEND_CUSTOM_EMAIL,
  LIST_JOB_LOCATIONS,
} from "@/graphql/recruitment/recruitmentQueries.js";
import {
  recruitmentEmailTemplates,
  replacementTags,
} from "../../common/customEmail/recruitmentEmailTemplates";
import { GET_MICROSOFT_CREDENTIALS } from "@/graphql/settings/Integration/jobPostIntegrationQueries";
import VueGoogleAutocomplete from "vue-google-autocomplete";
import panelMembersSchedule from "./panelMembersSchedule.vue";
import { PublicClientApplication } from "@azure/msal-browser";
import { LIST_CUSTOM_EMAIL_TEMPLATES } from "@/graphql/settings/email-template/emailTemplateQueries.js";

export default {
  name: "InterviewSchedules",
  mixins: [validationRules],
  components: {
    CustomSelect,
    CustomEmail,
    Datepicker,
    VueGoogleAutocomplete,
    panelMembersSchedule,
  },
  props: {
    candidateDetails: {
      type: Object,
      default: () => {},
    },
    candidateEmail: {
      type: Array,
      required: true,
    },
    isBulk: {
      type: Boolean,
      default: false,
    },
    candidateName: {
      default: "",
      type: String,
      required: true,
    },
    interviewType: {
      default: "",
      type: String,
      required: true,
    },
    candidateWillSelect: {
      default: false,
      type: Boolean,
      required: true,
    },
    statusId: {
      default: null,
      type: Number,
    },
    jobTitle: {
      default: "",
      type: String,
    },
    jobPostId: {
      default: null,
      type: Number,
    },
    candidateId: {
      default: [],
      required: true,
    },
    editedInterviewDetails: {
      type: Object,
      default: () => {},
    },
    interviewVenue: {
      type: String,
      default: "",
    },
    editForm: {
      type: Boolean,
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    calendarItems: {
      type: Array,
      default: () => [],
    },
    meetingItems: {
      type: Array,
      default: () => [],
    },
    showConfigArray: {
      type: Array,
      default: () => [],
    },
  },
  emits: ["close-interview-schedule-window", "close-overlay"],
  data: () => {
    return {
      overlay: true,
      showError: false,
      isLoading: false,
      tab: null,
      jobPostLocation: "",
      interviewDate: null,
      selectedJoiningDate: null,
      calenderLink: null,
      joiningdateError: "",
      clientId: "",
      rounds: [],
      roundSelected: null,
      roundsOptionList: [],
      locationList: [],
      panelMemberOptions: [],
      jobPostLocationLoading: false,
      roundListLoading: false,
      panelMemberLoading: false,
      displayCustomEmail: false,
      typeOfTemplate: "",
      templateData: {},
      isEdit: false,
      removedPanelMemberEmails: [],
      addedPanelMemberEmails: [],
      unChangedPanelMemberEmails: [],
      isEditContentChanged: false,
      openConfirmationPopup: false,
      isFormDirty: false,
      venue: "",
      isManualEnterForPermanentAddress: false,
      editedDetails: {},
      mapLink: "",
      showMessage: false,
      latitude: "",
      longitude: "",
      selectedCalendar: null,
      showCalendar: false,
      selectedMemebers: [],
      candidateEmailContent: "",
      selectedMeeting: null,
      onlineMeetingId: "",
      meetingUrl: "",
      panelMemberData: null,
      noCustomTemplate: false,
      emailTemplateList: [],
      selectedEmailTemplate: null,
      isEmailTemplateListLoading: false,
    };
  },
  computed: {
    redirectUri() {
      return `${this.baseUrl}v3/recruitment/my-integration`;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    notificationTimeNow() {
      if (this.selectedCalendar || this.selectedMeeting) {
        return false;
      } else return true;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    timeZone() {
      const now = new Date();
      const options = { timeZoneName: "long" };
      const timeZoneName = new Intl.DateTimeFormat("en", options)
        .formatToParts(now)
        .find((part) => part.type === "timeZoneName").value;
      return timeZoneName;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    addressLine1() {
      let organization = {};
      if (this.fieldForce) {
        organization = this.$store.state.orgDetails.serviceProvider;
      } else {
        organization = this.$store.state.orgDetails.organization;
      }
      let line1 = [];
      if (organization.street1) {
        line1.push(organization.street1);
      }
      if (organization.street2) {
        line1.push(organization.street2);
      }
      return line1.length > 0 ? line1.join(",") : "";
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    addressLine2() {
      const { organization } = this.$store.state.orgDetails;
      let line2 = [];
      if (organization.city) {
        line2.push(organization.city);
      }
      if (organization.state) {
        line2.push(organization.state);
      }
      if (organization.country) {
        line2.push(organization.country);
      }
      if (organization.pincode) {
        line2.push(organization.pincode);
      }
      return line2.length > 0 ? line2.join(",") : "";
    },
    loginEmployeeName() {
      return (
        this.$store.state.userDetails.employeeFirstName +
        " " +
        this.$store.state.userDetails.employeeLastName
      );
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    panelMemberEmail() {
      if (this.rounds && this.rounds.length) {
        let panelMembers = this.rounds[0].Panel_Members;
        if (panelMembers && panelMembers.length) {
          let panelMemberEmail = this.panelMemberOptions.filter((el) =>
            panelMembers.includes(el.Employee_Id)
          );
          panelMemberEmail = panelMemberEmail
            .map((el) =>
              el.Emp_Email && el.Emp_Email.length ? el.Emp_Email : null
            )
            .filter((email) => email !== null);
          return panelMemberEmail;
        }
      }
      return [];
    },
    templateEmail() {
      return this.candidateEmail;
    },
    previousDate() {
      let prevDay = moment().subtract(1, "days");
      return prevDay._d;
    },
    validateStartTime() {
      return (index) => {
        if (this.rounds[index].Start_Time) {
          const currentTime = moment();

          // Define the time you want to check
          let date = "";
          if (this.interviewDate instanceof Date) {
            date = moment(this.interviewDate).format("YYYY-MM-DD");
          } else date = this.interviewDate;
          const targetTime = moment(date + "T" + this.rounds[index].Start_Time);

          // Check if the target time is before the current time
          if (targetTime.isBefore(currentTime)) {
            return "Start time should be greater than current time";
          } else {
            return true;
          }
        } else {
          return "Start time is required";
        }
      };
    },
    validateStartEndTime() {
      return (index) => {
        if (this.rounds[index].End_Time) {
          const startTime = moment(this.rounds[index].Start_Time, "HH:mm");
          const endTime = moment(this.rounds[index].End_Time, "HH:mm");
          const duration = endTime.diff(startTime, "minutes");
          if (!endTime.isAfter(startTime)) {
            return "End time should be greater than start time";
          } else if (duration < 5) {
            return "Duration should be greater than 5 minutes";
          } else {
            return true;
          }
        } else {
          return "End time is required";
        }
      };
    },
  },
  watch: {
    roundSelected(val) {
      if (Array.isArray(val)) {
        if (val.length > this.rounds.length) {
          for (let i = 0; i < this.roundsOptionList.length; i++) {
            if (this.roundsOptionList[i]["Round_Id"] == val[val.length - 1]) {
              let newObj = {
                Panel_Members: [],
                Start_Time: "",
                End_Time: "",
                Duration: "",
                suggestedStartTimes: [],
                showSuggestions: false,
                busyPanelMembers: [],
                eventId: "",
              };
              let pushObj = { ...this.roundsOptionList[i], ...newObj };
              this.rounds.push(pushObj);
              break;
            }
          }
        } else if (val.length < this.rounds.length) {
          for (let i = 0; i < this.rounds.length; i++) {
            if (!val.includes(this.rounds[i]["Round_Id"])) {
              this.rounds.splice(i, 1);
              break;
            }
          }
        }
      } else {
        for (let i = 0; i < this.roundsOptionList.length; i++) {
          if (this.roundsOptionList[i]["Round_Id"] == val) {
            let newObj = {
              Panel_Members: [],
              Start_Time: "",
              End_Time: "",
              Duration: "",
              suggestedStartTimes: [],
              showSuggestions: false,
              busyPanelMembers: [],
              eventId: "",
            };
            this.rounds[0] = { ...this.roundsOptionList[i], ...newObj };
            break;
          }
        }
      }
    },
    interviewDate() {
      this.isFormDirty = true;
      if (this.rounds.length > 0) {
        this.callCalendarApi();
        this.$refs.scheduleInterviewForm.validate();
      }
      if (this.isEdit) {
        this.$refs.scheduleInterviewForm.validate();
      }
    },
  },
  mounted() {
    if (this.editForm) {
      this.venue = this.interviewVenue;
      this.isManualEnterForPermanentAddress = true;
    }
    this.retrieveInterviewRoundsList();
    this.retrieveJobPostLocation();
    this.getMicrosoftCredentials();
    this.fetchPanelMemberList();

    if (this.editedInterviewDetails) {
      this.isEdit = true;
      const {
        Start_Date,
        rounds,
        Calendar_Link,
        Venue,
        Calendar_Type,
        Meeting_Type,
        Online_Meeting_Id,
      } = this.editedInterviewDetails;
      this.interviewDate = moment(Start_Date, "YYYY/MM/DD").format(
        "YYYY-MM-DD"
      );
      this.jobPostLocation = Venue;
      this.calenderLink = Calendar_Link;
      this.selectedCalendar = Calendar_Type;
      this.selectedMeeting = Meeting_Type;
      this.onlineMeetingId = Online_Meeting_Id;
      if (rounds && rounds.length > 0) {
        this.roundSelected = rounds[0].Round_Id;
        this.rounds = [
          {
            Round_Name: rounds[0].Round_Name,
            Panel_Members: rounds[0].panelMembers.map(
              (member) => member.Panel_Member_Id
            ),
            Start_Time: moment(
              rounds[0].Round_Start_Date_Time + ".000Z"
            ).format("HH:mm"),
            End_Time: moment(rounds[0].Round_End_Date_Time + ".000Z").format(
              "HH:mm"
            ),
            Duration: "",
            suggestedStartTimes: [],
            showSuggestions: false,
            busyPanelMembers: [],
          },
        ];
        this.onChangeStartEndTime(0);
      }
      this.latitude = this.editedInterviewDetails.Latitude;
      this.longitude = this.editedInterviewDetails.Longitude;
      if (this.latitude && this.longitude) {
        let lat_long = this.latitude + "," + this.longitude;
        this.mapLink = `https://www.google.com/maps?q=${lat_long}`;
      }
    } else {
      this.interviewDate = this.calculateInterviewDate(0);
    }
    this.fetchEmailTemplates();
  },
  methods: {
    callCalendarApi() {
      for (let i = 0; i < this.rounds.length; i++) {
        if (
          this.rounds[i].Panel_Members.length > 0 &&
          !this.candidateWillSelect
        ) {
          this.getInternalPanelSchedule("00:00:00", "23:59:59", i);
        }
      }
    },
    resetPermanentAddress() {
      this.editedDetails.pStreet_Name = "";
      this.editedDetails.pCity = "";
      this.editedDetails.pState = "";
      this.editedDetails.pPincode = "";
      this.editedDetails.pCountry = "";
      this.editedDetails.pApartment_Name = "";
      this.mapLink = "";
      this.latitude = "";
      this.longitude = "";
    },
    setPermanentAddress(addressData, fullData) {
      this.resetPermanentAddress();
      this.latitude = addressData.latitude + "";
      this.longitude = addressData.longitude + "";
      for (const component of fullData.address_components) {
        const componentType = component.types[0];

        switch (componentType) {
          case "street_number":
          case "street_address":
          case "premise":
          case "establishment":
          case "route": {
            this.editedDetails.pApartment_Name = component.long_name;
            break;
          }

          case "neighborhood": {
            this.editedDetails.pStreet_Name = component.long_name;
            break;
          }

          case "administrative_area_level_2":
          case "sublocality_level_2":
          case "sublocality_level_1": {
            this.editedDetails.pStreet_Name =
              this.editedDetails.pStreet_Name &&
              this.editedDetails.pStreet_Name.length
                ? (this.editedDetails.pStreet_Name =
                    this.editedDetails.pStreet_Name +
                    ", " +
                    component.long_name)
                : (this.editedDetails.pStreet_Name = component.long_name);
            break;
          }

          case "locality": {
            this.editedDetails.pCity && this.editedDetails.pCity.length
              ? ""
              : (this.editedDetails.pCity = component.long_name);
            break;
          }

          case "administrative_area_level_3": {
            this.editedDetails.pCity && this.editedDetails.pCity.length
              ? ""
              : (this.editedDetails.pCity = component.long_name);
            break;
          }

          case "administrative_area_level_1": {
            this.editedDetails.pState = component.long_name;
            break;
          }

          case "country": {
            this.editedDetails.pCountry = component.long_name;
            break;
          }

          case "postal_code": {
            this.editedDetails.pPincode = component.long_name;
            break;
          }

          case "postal_code_suffix": {
            this.editedDetails.pPincode = `${this.editedDetails.pPincode}-${component.long_name}`;
            break;
          }
        }
      }
      this.venue =
        this.editedDetails.pApartment_Name +
        ", " +
        this.editedDetails.pStreet_Name +
        ", " +
        this.editedDetails.pCity +
        ", " +
        this.editedDetails.pState +
        ", " +
        this.editedDetails.pCountry +
        ", " +
        this.editedDetails.pPincode +
        ", ";
      let lat_long = this.latitude + "," + this.longitude;
      this.mapLink = `https://www.google.com/maps?q=${lat_long}`;
      this.isManualEnterForPermanentAddress = true;
    },
    onChangeStartEndTime(index) {
      this.isFormDirty = true;
      if (this.rounds[index].Start_Time && this.rounds[index].End_Time) {
        const startTime = moment(this.rounds[index].Start_Time, "HH:mm");
        const endTime = moment(this.rounds[index].End_Time, "HH:mm");
        const duration = moment.duration(endTime.diff(startTime));
        const hours = duration.hours();
        const minutes = duration.minutes();
        this.rounds[index].Duration = `${hours}h ${minutes}m`;
        if (duration.hours() > 0 || duration.minutes() > 5) {
          this.getBusyMembers(index);
        }
        this.$refs.scheduleInterviewForm.validate();
      }
    },
    onClickClose() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.$emit("close-interview-schedule-window", false);
      }
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-interview-schedule-window", false);
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    calculateInterviewDate(val) {
      const tomorrow = moment().add(val, "day");
      return tomorrow.format("YYYY-MM-DD");
    },
    changeFieldValue(value, field, index = null) {
      this.isFormDirty = true;
      if (index != null) {
        if (field == "Panel_Members" && !this.candidateWillSelect) {
          if (value.length > this.rounds[index][field].length) {
            let member = value.filter(
              (el) => !this.rounds[index][field].includes(el)
            );
            this.getInternalPanelSchedule(
              "00:00:00",
              "23:59:59",
              index,
              member
            );
          } else {
            let member = this.rounds[index][field].filter(
              (el) => !value.includes(el)
            );
            this.selectedMemebers = this.selectedMemebers.filter(
              (el) => !member.includes(el.id)
            );
            this.getBusyMembers(index);
            if (this.selectedMemebers.length > 0) {
              this.suggestSlots(index);
            }
          }
        }
        this.rounds[index][field] = value;
      } else {
        if (field?.toLowerCase() === "roundselected") {
          this.rounds = [];
          this.calenderLink = null;
        }
        this[field] = value;
      }
    },
    deleteRound(index) {
      this.rounds.splice(index, 1);
      if (Array.isArray(this.roundSelected)) {
        this.roundSelected.splice(index, 1);
      } else {
        this.roundSelected = null;
      }
    },
    fetchPanelMemberList() {
      this.panelMemberLoading = true;
      this.$apollo
        .query({
          query: GET_PANEL_MEMBERS_LIST,
          variables: {
            jobPostId: this.jobPostId,
            formId: 16,
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.getRoleBasedJobPostMember) {
            if (
              res.data.getRoleBasedJobPostMember.panelMembers &&
              res.data.getRoleBasedJobPostMember.panelMembers.length > 0
            ) {
              this.panelMemberOptions = [
                ...res.data.getRoleBasedJobPostMember.panelMembers,
              ];
            }
            if (
              res.data.getRoleBasedJobPostMember.hiringManager &&
              res.data.getRoleBasedJobPostMember.hiringManager.length > 0
            ) {
              this.panelMemberOptions = [
                ...this.panelMemberOptions,
                ...res.data.getRoleBasedJobPostMember.hiringManager,
              ];
            }
            if (
              res.data.getRoleBasedJobPostMember.recruiters &&
              res.data.getRoleBasedJobPostMember.recruiters.length > 0
            ) {
              this.panelMemberOptions = [
                ...this.panelMemberOptions,
                ...res.data.getRoleBasedJobPostMember.recruiters,
              ];
            }
            let uniqueObjects1 = Array.from(
              new Set(this.panelMemberOptions.map((obj) => obj.Employee_Id))
            ).map((name) => {
              return this.panelMemberOptions.find(
                (obj) => obj.Employee_Id === name
              );
            });
            this.panelMemberOptions = [...uniqueObjects1];
            if (this.editForm) this.getInternalPanelSchedule();
          }
          this.panelMemberLoading = false;
        })
        .catch(() => {
          this.panelMemberOptions = [];
          this.panelMemberLoading = false;
        });
    },
    retrieveInterviewRoundsList() {
      this.roundListLoading = true;
      this.$apollo
        .query({
          query: RETRIEVE_INTERVIEW_ROUNDS_LIST,
          variables: {
            jobpostId: this.jobPostId,
            searchString: "",
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listJobPostRounds &&
            res.data.listJobPostRounds.jobpostRounds &&
            res.data.listJobPostRounds.jobpostRounds.length > 0
          ) {
            this.roundsOptionList = Array.from(
              res.data.listJobPostRounds.jobpostRounds
            );
          }
          this.roundListLoading = false;
        })
        .catch(() => {
          this.roundsOptionList = [];
          this.roundListLoading = false;
        });
    },
    retrieveJobPostLocation() {
      let vm = this;
      vm.jobPostLocationLoading = true;
      vm.$apollo
        .query({
          query: LIST_JOB_LOCATIONS,
          client: "apolloClientA",
          variables: {
            jobpostId: vm.jobPostId,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobPostLocations &&
            !response.data.listJobPostLocations.errorCode.length
          ) {
            vm.locationList =
              response.data.listJobPostLocations.jobpostLocations;
          }
          vm.jobPostLocationLoading = false;
        })
        .catch(() => {
          vm.jobPostLocationLoading = false;
        });
    },
    scheduleInterviews() {
      let htmlContent = null,
        candidateSubject = null,
        panelMemberData = null;
      if (this.selectedCalendar !== null) {
        let customEmailRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        htmlContent = customEmailRef ? customEmailRef.htmlContent : null;
        htmlContent = htmlContent
          ? htmlContent.replace("[Meeting Link]", this.meetingUrl)
          : null;
        candidateSubject = customEmailRef ? customEmailRef.subject : null;
        let templateType = this.formPanelMemberTemplate();
        panelMemberData = this.formPanelMemberEmail(
          this.panelMemberEmail,
          templateType,
          false,
          "panel"
        );
        if (panelMemberData) {
          if (recruitmentEmailTemplates[panelMemberData.typeOfTemplate]) {
            panelMemberData.subject = this.replaceTags(
              recruitmentEmailTemplates[panelMemberData.typeOfTemplate].subject,
              replacementTags,
              panelMemberData.templateData
            );
            panelMemberData.htmlContent = this.replaceTags(
              recruitmentEmailTemplates[panelMemberData.typeOfTemplate].body,
              replacementTags,
              panelMemberData.templateData
            );
            panelMemberData.htmlContent = panelMemberData.htmlContent
              ? panelMemberData.htmlContent.replace(
                  "[Meeting Link]",
                  this.meetingUrl
                )
              : null;
          }
        }
      }
      let vm = this;
      vm.isLoading = true;
      let interviewRounds = [];
      for (let i = 0; i < this.rounds.length; i++) {
        if (vm.rounds[i].Start_Time == "") {
          vm.rounds[i].Start_Time = moment().format("HH:mm");
        }
        if (vm.rounds[i].End_Time == "") {
          vm.rounds[i].End_Time = "23:59";
        }
        let startTime =
          moment(vm.interviewDate).format("YYYY-MM-DD") +
          " " +
          vm.rounds[i].Start_Time;
        startTime = moment(startTime).utc();
        let endTime =
          moment(vm.interviewDate).format("YYYY-MM-DD") +
          " " +
          vm.rounds[i].End_Time;
        endTime = moment(endTime).utc();
        interviewRounds.push({
          roundId: this.rounds[i].Round_Id,
          panelMembers: this.rounds[i].Panel_Members,
          startDateTime: moment(startTime).format("YYYY-MM-DD HH:mm"),
          endDateTime: moment(endTime).format("YYYY-MM-DD HH:mm"),
          roundSequence: i,
        });
      }
      vm.$apollo
        .query({
          query: ADD_INTERVIEW_DETAILS,
          variables: {
            interviewName: vm.interviewType,
            jobPostId: vm.jobPostId,
            formId: vm.interviewType == "Assessment Link" ? 275 : 272,
            venue: vm.isEdit && vm.venue == "" ? vm.interviewVenue : vm.venue,
            calendarLink: vm.calenderLink,
            assessmentLink: vm.assessmentLink,
            additionalNotes: "",
            startDate: moment(vm.interviewDate).format("YYYY-MM-DD"),
            endDate: moment(vm.interviewDate).format("YYYY-MM-DD"),
            status: 13,
            totalVacancy: 8,
            candidates: vm.candidateId,
            rounds: interviewRounds,
            interviewImage: "",
            employeeId: parseInt(vm.$store.state.orgDetails.employeeId, 10),
            formName: "Job Candidates",
            latitude: vm.latitude,
            longitude: vm.longitude,
            subject: panelMemberData ? panelMemberData.subject : "",
            emailBody: panelMemberData ? panelMemberData.htmlContent : "",
            calendarType: vm.selectedCalendar,
            meetingType: vm.selectedMeeting,
            onlineMeetingId: vm.onlineMeetingId,
            candidateEmailBody: htmlContent,
            candidateSubject: candidateSubject,
          },
          client: "apolloClientBC",
          fetchPolicy: "no-cache",
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            message:
              vm.interviewType == "Assessment Link"
                ? "Assessment link sent successfully"
                : "Interview scheduled successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.$emit("close-interview-schedule-window", true);
        })
        .catch((err) => {
          if (
            err &&
            err.graphQLErrors &&
            err.graphQLErrors[0] &&
            err.graphQLErrors[0].message
          ) {
            let message = JSON.parse(err.graphQLErrors[0].message);
            if (message.errorCode == "INVALID_MIS_TOKEN") {
              this.getAccessTokenFromRefreshToken("scheduleInterviews");
            } else {
              vm.$store.dispatch("handleApiErrors", {
                error: err,
                action: "adding",
                form: "interview details",
                isListError: false,
              });
              vm.isLoading = false;
            }
          } else {
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "adding",
              form: "interview details",
              isListError: false,
            });
          }
          vm.isLoading = false;
        });
    },
    reScheduleInterviews() {
      let vm = this;
      vm.isLoading = true;
      let interviewRounds = [];
      const { rounds, CandidatesData, Total_Vacancy, Interview_Id } =
        this.editedInterviewDetails;
      for (let i = 0; i < this.rounds.length; i++) {
        let editedPanelMembers = [];
        for (let panelMember of this.rounds[i].Panel_Members) {
          editedPanelMembers.push({
            panelMemberId: parseInt(panelMember),
            inviteStatus: 0,
            isInviteSent: 0,
          });
        }
        let startTime =
          moment(vm.interviewDate).format("YYYY-MM-DD") +
          " " +
          vm.rounds[i].Start_Time;
        startTime = moment(startTime).utc();
        let endTime;
        if (!vm.rounds[i].Start_Time) {
          endTime = startTime.add(23, "hours");
        } else {
          endTime =
            moment(vm.interviewDate).format("YYYY-MM-DD") +
            " " +
            vm.rounds[i].End_Time;
        }
        endTime = moment(endTime).utc();
        interviewRounds.push({
          roundId: parseInt(vm.roundSelected),
          startDateTime: moment(startTime).format("YYYY-MM-DD hh:mm:ss"),
          endDateTime: moment(endTime).format("YYYY-MM-DD hh:mm:ss"),
          roundSequence: i,
          roundStatus: rounds && rounds[i] ? rounds[i].Round_Status : "",
          panelAcceptedInviteCount:
            rounds && rounds[i] ? rounds[i].Panel_Accepted_Invite_Count : 0,
          isReschedule: 0,
          isNewPanelAdded: 0,
          panelMembers: editedPanelMembers,
        });
      }
      let interviewCandidate = [];
      for (let candidate of CandidatesData) {
        interviewCandidate.push({
          candidateId: candidate.Candidate_Id,
          roundStatus: candidate.Round_Status,
          panelScore: candidate.Panel_Score,
          isInviteSent: candidate.Is_Invite_Sent,
        });
      }
      let htmlContent = null,
        candidateSubject = null,
        panelMemberData = null;
      if (this.selectedCalendar !== null) {
        let customEmailRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        htmlContent = customEmailRef ? customEmailRef.htmlContent : null;
        htmlContent = htmlContent
          ? htmlContent.replace("[Meeting Link]", this.meetingUrl)
          : null;
        candidateSubject = customEmailRef ? customEmailRef.subject : null;
        let templateType = this.formPanelMemberTemplate();
        panelMemberData = this.formPanelMemberEmail(
          this.panelMemberEmail,
          templateType,
          false,
          "panel"
        );
        if (panelMemberData) {
          if (recruitmentEmailTemplates[panelMemberData.typeOfTemplate]) {
            panelMemberData.subject = this.replaceTags(
              recruitmentEmailTemplates[panelMemberData.typeOfTemplate].subject,
              replacementTags,
              panelMemberData.templateData
            );
            panelMemberData.htmlContent = this.replaceTags(
              recruitmentEmailTemplates[panelMemberData.typeOfTemplate].body,
              replacementTags,
              panelMemberData.templateData
            );
            panelMemberData.htmlContent = panelMemberData.htmlContent
              ? panelMemberData.htmlContent.replace(
                  "[Meeting Link]",
                  this.meetingUrl
                )
              : null;
          }
        }
      }
      vm.$apollo
        .query({
          query: UPDATE_INTERVIEW_DETAILS,
          variables: {
            interviewName: vm.interviewType,
            jobPostId: vm.jobPostId,
            venue: vm.venue,
            calendarLink: vm.calenderLink,
            assessmentLink: vm.assessmentLink,
            additionalNotes: "",
            startDate: moment(vm.interviewDate).format("YYYY-MM-DD"),
            endDate: moment(vm.interviewDate).format("YYYY-MM-DD"),
            status: 13,
            totalVacancy: Total_Vacancy,
            candidates: interviewCandidate,
            rounds: interviewRounds,
            interviewImage: "",
            employeeId: parseInt(vm.$store.state.orgDetails.employeeId, 10),
            formName: "Job Candidates",
            interviewId: Interview_Id,
            latitude: vm.latitude,
            longitude: vm.longitude,
            subject: panelMemberData ? panelMemberData.subject : "",
            emailBody: panelMemberData ? panelMemberData.htmlContent : "",
            calendarType: vm.selectedCalendar,
            meetingType: vm.selectedMeeting,
            onlineMeetingId: vm.onlineMeetingId,
            candidateEmailBody: htmlContent,
            candidateSubject: candidateSubject,
          },
          client: "apolloClientBC",
          fetchPolicy: "no-cache",
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            message: "Interview rescheduled successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.$emit("close-interview-schedule-window", true);
          if (vm.selectedCalendar == null) {
            vm.triggerEmails();
          }
        })
        .catch((err) => {
          if (
            err &&
            err.graphQLErrors &&
            err.graphQLErrors[0] &&
            err.graphQLErrors[0].message
          ) {
            let message = JSON.parse(err.graphQLErrors[0].message);
            if (message.errorCode == "INVALID_MIS_TOKEN") {
              this.getAccessTokenFromRefreshToken("reScheduleInterviews");
            } else {
              vm.$store.dispatch("handleApiErrors", {
                error: err,
                action: "rescheduling",
                form: "interview details",
                isListError: false,
              });
              vm.isLoading = false;
            }
          } else {
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "rescheduling",
              form: "interview details",
              isListError: false,
            });
          }
          vm.isLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    formTemplateData(useOldData = false) {
      let templateData = this.candidateDetails;
      //Include other data
      templateData.Calendar_Link = this.calenderLink;
      let date = moment(this.interviewDate);
      templateData.Date = date.format("MMMM D, YYYY");
      templateData.Location = this.jobPostLocation;
      templateData.Venue = this.venue;
      templateData.Venue_Link = this.mapLink ? this.mapLink : "";
      templateData.Time_Zone = this.timeZone;
      templateData.Company_Address_1 = this.addressLine1;
      templateData.Company_Address_2 = this.addressLine2;
      let templateRounds = this.rounds;
      if (useOldData) {
        const { Start_Date, rounds, Calendar_Link, Venue } =
          this.editedInterviewDetails;
        templateData.Calendar_Link = Calendar_Link;
        templateData.Date = moment(Start_Date, "YYYY/MM/DD").format(
          "YYYY-MM-DD"
        );
        templateData.Location = Venue;
        templateRounds = rounds;
      }
      templateData.Candidate_Name = this.candidateName;
      templateData.Company_Name = this.companyName;
      templateData.Recruiter_Name = this.loginEmployeeName;
      templateData.Job_Post_Name = this.jobTitle;
      templateData.Panel_Members_Emails = this.panelMemberEmail;
      //Rounds
      if (templateRounds && templateRounds.length > 0) {
        templateData.Round_Name = templateRounds[0].Round_Name;
        let round = useOldData
          ? {
              Round_Name: templateRounds[0].Round_Name,
              Panel_Members: templateRounds[0].panelMembers.map(
                (member) => member.Panel_Member_Id
              ),
              Start_Time: moment(
                templateRounds[0].Round_Start_Date_Time
              ).format("HH:mm"),
              End_Time: moment(templateRounds[0].Round_End_Date_Time).format(
                "HH:mm"
              ),
            }
          : templateRounds[0];
        templateData = { ...templateData, ...round };
      }
      return templateData;
    },
    async triggerEmails() {
      // if any(round,location, calendar link, date, time) is changed have to send cancel & invite email for candidate and panel members
      if (this.isEditContentChanged) {
        // candidate cancel & invite
        if (this.candidateEmail.length > 0 && this.candidateEmail[0]) {
          // candidate cancel
          let candidateData = this.formPanelMemberEmail(
            this.candidateEmail,
            "roundCancelledCandidate",
            true
          );
          if (candidateData) {
            this.sendCustomEmail(candidateData);
          }
          // candidate invite
          let customTemplate = this.formCandidateTemplate();
          let candidateInviteData = this.formPanelMemberEmail(
            this.candidateEmail,
            customTemplate
          );
          if (candidateInviteData) {
            //Send Custom Email for Candidates
            this.sendCustomEmail(candidateInviteData);
          }
        }
        // panel cancel & invite
        if (this.unChangedPanelMemberEmails.length > 0) {
          let panelMemberCancelData = this.formPanelMemberEmail(
            this.unChangedPanelMemberEmails,
            "roundCancelledPanelMember",
            true
          );
          // panel cancel
          if (panelMemberCancelData) {
            //Send Custom Email for Panel Members
            this.sendCustomEmail(panelMemberCancelData);
          }
          let templateType = this.formPanelMemberTemplate();
          let panelMemberInviteData = this.formPanelMemberEmail(
            this.unChangedPanelMemberEmails,
            templateType,
            false,
            "panel"
          );
          // panel invite
          if (panelMemberInviteData) {
            //Send Custom Email for Panel Members
            this.sendCustomEmail(panelMemberInviteData);
          }
        }
      }
      // if any panel members removed send cancel email to panel members
      if (this.removedPanelMemberEmails.length > 0) {
        // remove panel members
        let panelMemberData = this.formPanelMemberEmail(
          this.removedPanelMemberEmails,
          "panelMemberRemove",
          true
        );
        if (panelMemberData) {
          //Send Custom Email for Panel Members
          this.sendCustomEmail(panelMemberData);
        }
      }
      // if any panel members newly added send invite email to panel members
      if (this.addedPanelMemberEmails.length > 0) {
        let templateType = this.formPanelMemberTemplate();
        let panelMemberData = this.formPanelMemberEmail(
          this.addedPanelMemberEmails,
          templateType,
          false,
          "panel"
        );
        if (panelMemberData) {
          //Send Custom Email for Panel Members
          this.sendCustomEmail(panelMemberData);
        }
      }
    },
    sendCustomEmail(customData = null) {
      let vm = this;
      vm.isLoading = true;
      if (recruitmentEmailTemplates[customData.typeOfTemplate]) {
        customData.subject = this.replaceTags(
          recruitmentEmailTemplates[customData.typeOfTemplate].subject,
          replacementTags,
          customData.templateData
        );
        customData.htmlContent = this.replaceTags(
          recruitmentEmailTemplates[customData.typeOfTemplate].body,
          replacementTags,
          customData.templateData
        );
      }
      if (customData.templateData.Date) {
        if (
          this.interviewType === "Assessment Link" ||
          !customData.templateData.Start_Time
        ) {
          customData.startDateTime = moment(
            customData.templateData.Date
          ).format("YYYY-MM-DD");
        } else if (customData.templateData.Start_Time) {
          customData.startDateTime = moment(
            customData.templateData.Date +
              " " +
              customData.templateData.Start_Time
          ).format("YYYY-MM-DD HH:mm");
        }
      }
      if (customData.templateData.Date) {
        if (
          this.interviewType === "Assessment Link" ||
          !customData.templateData.End_Time
        ) {
          customData.endDateTime = moment(customData.templateData.Date).format(
            "YYYY-MM-DD"
          );
        } else if (customData.templateData.End_Time) {
          customData.endDateTime = moment(
            customData.templateData.Date +
              " " +
              customData.templateData.End_Time
          ).format("YYYY-MM-DD HH:mm");
        }
      }
      vm.$apollo
        .mutate({
          mutation: SEND_CUSTOM_EMAIL,
          variables: customData,
          client: "apolloClientAQ",
        })
        .then(() => {
          vm.isLoading = false;
          vm.$emit("custom-email-sent");
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
    // Function to replace tags with corresponding values
    replaceTags(template, replacementTags, templateData) {
      for (const [tag, replacement] of Object.entries(replacementTags)) {
        if (
          template.includes(tag) &&
          templateData.hasOwnProperty(replacement)
        ) {
          // Check if tag exists in template and replacement data exists
          template = template.replace(
            new RegExp("\\" + tag, "g"),
            templateData[replacement]
          );
        }
      }
      return template;
    },
    checkPanelMemberNewlyAddedOrRemoved() {
      if (this.editedInterviewDetails) {
        const { Start_Date, Calendar_Link, Venue, rounds } =
          this.editedInterviewDetails;
        let retrievedPanelMembers =
          rounds && rounds[0] ? rounds[0].panelMembers : [];
        let editedPanelMembers = this.rounds[0].Panel_Members;
        this.isEditContentChanged = false;
        // Extract the time parts from retrieved round
        const retrievedStartTime =
          rounds && rounds[0]
            ? moment(rounds[0].Round_Start_Date_Time).format("HH:mm")
            : "";
        const retrievedEndTime =
          rounds && rounds[0]
            ? moment(rounds[0].Round_End_Date_Time).format("HH:mm")
            : "";
        // Extract the time parts from edited round
        const editedStartTime = this.rounds[0].Start_Time;
        const editedEndTime = this.rounds[0].End_Time;
        if (
          this.interviewDate !==
            moment(Start_Date, "YYYY/MM/DD").format("YYYY-MM-DD") ||
          this.calenderLink !== Calendar_Link ||
          this.jobPostLocation !== Venue ||
          retrievedStartTime !== editedStartTime ||
          retrievedEndTime !== editedEndTime
        ) {
          this.isEditContentChanged = true;
        }
        // Initialize the result array
        let removedPanelMembers = [],
          addedPanelMembers = [],
          unChangedPanelMembers = [];
        // Create a set of Panel_Member_Id values from editedPanelMembers for quick lookup
        let editedDataSet = new Set(editedPanelMembers);
        // Iterate over each item in retrievedPanelMembers
        retrievedPanelMembers.forEach((panelMember) => {
          let panelMemberId = panelMember.Panel_Member_Id;
          let isRemoved = !editedDataSet.has(panelMemberId); // Check if Panel_Member_Id is not in editedPanelMembers
          if (isRemoved) {
            removedPanelMembers.push(panelMemberId);
          } else {
            unChangedPanelMembers.push(panelMemberId);
          }
        });
        // Check for Panel_Member_Id values in editedPanelMembers that are not in retrievedPanelMembers
        editedPanelMembers.forEach((panelMemberId) => {
          if (
            !retrievedPanelMembers.find(
              (m) => m.Panel_Member_Id === panelMemberId
            )
          ) {
            addedPanelMembers.push(panelMemberId);
          }
        });
        let removedPanelMemberEmail = this.panelMemberOptions.filter((el) =>
          removedPanelMembers.includes(el.Employee_Id)
        );
        removedPanelMemberEmail = removedPanelMemberEmail
          ?.map((el) => (el.Emp_Email?.length ? el.Emp_Email : null))
          .filter((email) => email !== null);
        let addedPanelMemberEmail = this.panelMemberOptions.filter((el) =>
          addedPanelMembers.includes(el.Employee_Id)
        );
        addedPanelMemberEmail = addedPanelMemberEmail
          ?.map((el) => (el.Emp_Email?.length ? el.Emp_Email : null))
          .filter((email) => email !== null);
        let unChangedPanelMemberEmail = this.panelMemberOptions.filter((el) =>
          unChangedPanelMembers.includes(el.Employee_Id)
        );
        unChangedPanelMemberEmail = unChangedPanelMemberEmail
          ?.map((el) => (el.Emp_Email?.length ? el.Emp_Email : null))
          .filter((email) => email !== null);
        this.unChangedPanelMemberEmails = unChangedPanelMemberEmail;
        this.removedPanelMemberEmails = removedPanelMemberEmail;
        this.addedPanelMemberEmails = addedPanelMemberEmail;
      }
    },
    formPanelMemberEmail(
      panelMemberEmails = this.panelMemberEmail,
      templateType = this.mapLink && this.mapLink.length > 0
        ? "panelMemberFaceToFaceInterview"
        : "panelMemberFaceToFaceInterviewWithVenue",
      useOldData = false,
      receiverType = ""
    ) {
      let panelMemberEmail = panelMemberEmails;
      if (!panelMemberEmail.length) return null;
      let candidateResumeDetails = [];
      let coverLetterDetails = [];
      let templateData = this.formTemplateData(useOldData);
      if (templateData.Resume) {
        candidateResumeDetails = [
          {
            filePath:
              this.domainName +
              "_" +
              "/" +
              this.orgCode +
              "/" +
              "resume" +
              "/" +
              templateData.Resume,
            type: "documents",
          },
        ];
      }
      if (templateData?.Other_Attachments?.length) {
        coverLetterDetails = templateData.Other_Attachments.map((fileName) => ({
          filePath:
            this.domainName +
            "_" +
            "/" +
            this.orgCode +
            "/" +
            "otherAttachment" +
            "/" +
            fileName.File_Name,
          type: "documents",
        }));
      }
      let documentList = [...candidateResumeDetails, ...coverLetterDetails];
      templateData.Full_Time =
        templateData.Start_Time &&
        templateData.Start_Time.length &&
        templateData.End_Time &&
        templateData.End_Time.length
          ? templateData.Start_Time +
            " to " +
            templateData.End_Time +
            " (24 hr Format)"
          : "Yet to be decided";
      templateData.Location =
        templateData.Location && templateData.Location.length
          ? templateData.Location
          : "Online Interview";
      let panelMemberData = {
        formId: 16,
        typeOfInterview: "onlineinterview",
        typeOfTemplate: templateType,
        typeOfSchedule: this.candidateWillSelect ? "noncalendar" : "calendar",
        bccEmails: panelMemberEmails,
        templateData: templateData,
        attchmentList: receiverType === "panel" ? documentList : [],
        location: "",
        description: null,
      };
      return panelMemberData;
    },
    async validateInterviewScheduleForm() {
      this.checkPanelMemberNewlyAddedOrRemoved();
      if (!this.displayCustomEmail || this.isEdit) {
        if (this.displayCustomEmail) {
          let customEmailRef = this.$refs.customEmail
            ? this.$refs.customEmail
            : null;
          if (customEmailRef) {
            let noPlaceholderFound = customEmailRef.noPlaceholderFound;
            if (noPlaceholderFound) {
              let snackbarData = {
                isOpen: true,
                message:
                  "Some placeholders are not replaced, kindly replace or remove them before proceeding.",
                type: "warning",
              };
              this.showAlert(snackbarData);
              customEmailRef.noPlaceholderFound = false;
              return;
            }
            const { valid } =
              await customEmailRef.$refs.customEmailForm.validate();
            if (valid && customEmailRef.isContentPresent) {
              if (this.selectedMeeting) {
                this.generateMeetingUrl("reScheduleInterviews");
              } else this.reScheduleInterviews();
            }
          }
        } else {
          let { valid } = await this.$refs.scheduleInterviewForm.validate();
          if (this.interviewType == "Face To Face") {
            if (this.venue == "") {
              valid = false;
              this.showMessage = true;
            } else {
              this.showMessage = false;
            }
          }
          this.typeOfTemplate = this.formCandidateTemplate();
          this.templateData = this.formTemplateData();
          if (valid) {
            if (this.displayCustomEmail) {
              let customEmailRef = this.$refs.customEmail
                ? this.$refs.customEmail
                : null;
              if (customEmailRef) {
                const { valid } =
                  await customEmailRef.$refs.customEmailForm.validate();
                if (valid && customEmailRef.isContentPresent) {
                  if (this.selectedMeeting) {
                    this.generateMeetingUrl("reScheduleInterviews");
                  } else this.reScheduleInterviews();
                }
              }
            } else {
              this.displayCustomEmail = true;
            }
          }
        }
      } else {
        let customEmailRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        if (customEmailRef) {
          let noPlaceholderFound = customEmailRef.noPlaceholderFound;
          if (noPlaceholderFound) {
            let snackbarData = {
              isOpen: true,
              message:
                "Some placeholders are not replaced, kindly replace or remove them before proceeding.",
              type: "warning",
            };
            this.showAlert(snackbarData);
            customEmailRef.noPlaceholderFound = false;
            return;
          }
          let valid = await customEmailRef.validateCustomEmailForm();
          if (valid && customEmailRef.isContentPresent) {
            let templateType = this.formPanelMemberTemplate();
            let panelMemberData = this.formPanelMemberEmail(
              this.panelMemberEmail,
              templateType,
              false,
              "panel"
            );
            this.panelMemberData = panelMemberData;
            if (
              panelMemberData &&
              this.selectedCalendar === null &&
              this.notificationTimeNow
            ) {
              //Send Custom Email for Panel Members
              await customEmailRef.sendCustomEmail(panelMemberData);
            }
            //Schedule Interview
            if (this.selectedMeeting) {
              this.generateMeetingUrl("scheduleInterviews");
            } else this.scheduleInterviews();
          }
        }
      }
    },
    formCandidateTemplate() {
      if (this.interviewType === "Assessment Link") {
        return this.isBulk ? "candidateAssessmentBulk" : "candidateAssessment";
      } else {
        if (this.candidateWillSelect) {
          if (this.interviewType === "Face To Face") {
            return this.isBulk
              ? this.mapLink.length > 0
                ? "candidateFaceToFaceBulk"
                : "candidateFaceToFaceBulkWithVenue"
              : this.mapLink.length > 0
              ? "candidateFaceToFaceCalendarLink"
              : "candidateFaceToFaceCalendarLinkWithVenue";
          } else {
            if (this.isBulk) {
              if (this.candidateDetails.Source)
                return "candidateCalendarLinkInterviewBulk";
              else return "candidateCalendarLinkInterviewBulkNoSource";
            } else {
              if (this.candidateDetails.Source)
                return "candidateCalendarLinkInterview";
              else return "candidateCalendarLinkInterviewNoSource";
            }
          }
        } else {
          if (this.interviewType === "Online") {
            if (this.isBulk) {
              if (this.selectedMeeting)
                if (this.candidateDetails.Source)
                  return "candidateOnlineInterviewBulkWithMeeting";
                else return "candidateOnlineInterviewBulkWithMeetingNoSource";
              else {
                if (this.candidateDetails.Source)
                  return "candidateOnlineInterviewBulk";
                else return "candidateOnlineInterviewBulkNoSource";
              }
            } else {
              if (this.selectedMeeting) {
                if (this.candidateDetails.Source)
                  return "candidateOnlineInterviewWithMeeting";
                else return "candidateOnlineInterviewWithMeetingNoSource";
              } else {
                if (this.candidateDetails.Source)
                  return "candidateOnlineInterview";
                else return "candidateOnlineInterviewNoSource";
              }
            }
          } else if (this.interviewType === "Face To Face") {
            return this.isBulk
              ? this.mapLink.length > 0
                ? "candidateFaceToFaceBulk"
                : "candidateFaceToFaceBulkWithVenue"
              : this.mapLink.length > 0
              ? "candidateFaceToFace"
              : "candidateFaceToFaceWithVenue";
          }
        }
      }
    },
    formPanelMemberTemplate() {
      let templateType = "";
      if (this.interviewType === "Assessment Link") {
        templateType = "panelMemberAssessment";
      } else {
        if (this.candidateWillSelect) {
          if (this.interviewType === "Face To Face") {
            templateType =
              this.mapLink.length > 0
                ? "panelMemberFaceToFaceInterview"
                : "panelMemberFaceToFaceInterviewWithVenue";
          } else {
            templateType = "panelCalendarLinkInterview";
          }
        } else {
          if (this.interviewType === "Online") {
            if (this.selectedMeeting) return "panelOnlineInterviewWithMeeting";
            else templateType = "panelOnlineInterview";
          } else if (this.interviewType === "Face To Face") {
            templateType =
              this.mapLink.length > 0
                ? "panelMemberFaceToFaceInterview"
                : "panelMemberFaceToFaceInterviewWithVenue";
          }
        }
      }
      return templateType;
    },
    selectStartTime(startTime, index) {
      this.rounds[index].Start_Time = this.formatStartTime(startTime.start);
      this.rounds[index].End_Time = this.formatStartTime(startTime.end);
      const start = moment(this.rounds[index].Start_Time, "HH:mm");
      const end = moment(this.rounds[index].End_Time, "HH:mm");
      const duration = moment.duration(end.diff(start));
      const hours = duration.hours();
      const minutes = duration.minutes();
      this.rounds[index].Duration = `${hours}h ${minutes}m`;
      this.getBusyMembers(index);
    },
    onShowCalendar() {
      this.showCalendar = true;
    },
    convertToLocal(date) {
      const stillUTC = moment.utc(date).toDate();
      return new Date(
        moment(stillUTC, "YYYY-MM-DD HH:mm:ss")
          .local()
          .format("YYYY-MM-DDTHH:mm:ss")
      );
    },
    fetchPanelMemberSchedule(
      startTime = "00:00:00",
      endTime = "23:59:59",
      index = 0,
      panelMember = []
    ) {
      const startObj = moment(startTime, "HH:mm");
      const endObj = moment(endTime, "HH:mm");
      if (!endObj.isAfter(startObj)) {
        return;
      }
      let vm = this;
      vm.isLoading = true;
      let date = "";
      if (this.interviewDate instanceof Date) {
        date = moment(this.interviewDate).format("YYYY-MM-DD");
      } else date = this.interviewDate;
      let start = moment(date + "T" + startTime)
        .utc()
        .format("YYYY-MM-DDTHH:mm:ss");
      let end = moment(date + "T" + endTime)
        .utc()
        .format("YYYY-MM-DDTHH:mm:ss");
      vm.$apollo
        .query({
          query: GET_PANEL_MEMBERS_SCHEDULE,
          variables: {
            panelMembers: panelMember.length
              ? panelMember
              : vm.rounds[index].Panel_Members,
            startDateTime: start,
            endDateTime: end,
            formId: 16,
          },
          client: "apolloClientBD",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.getMicrosoftCalendarEvent &&
            data.getMicrosoftCalendarEvent.data
          ) {
            let schedule = JSON.parse(data.getMicrosoftCalendarEvent.data);
            if (schedule && schedule.value && schedule.value.length > 0) {
              for (let value of schedule.value) {
                let memberIndex = vm.selectedMemebers.findIndex(
                  (obj) =>
                    obj.email === value.scheduleId ||
                    obj.Microsoft_Email === value.scheduleId
                );
                if (!value.error) {
                  if (value.scheduleItems?.length > 0) {
                    let events = value.scheduleItems
                      .filter(
                        (event) =>
                          !event.subject?.toLowerCase().includes("canceled")
                      )
                      .map((event) => {
                        return {
                          title: event.subject,
                          start: this.convertToLocal(event.start.dateTime),
                          end: this.convertToLocal(event.end.dateTime),
                          class: "bg-primary",
                        };
                      });
                    if (memberIndex !== -1) {
                      let mergedList = vm.removeDuplicateEvents(
                        events,
                        vm.selectedMemebers[memberIndex].events
                      );
                      vm.selectedMemebers[memberIndex].events = mergedList;
                    } else {
                      this.selectedMemebers.push({
                        panelMemberName: memberObj.Emp_Name,
                        id: memberObj.Employee_Id,
                        email: memberObj.Emp_Email,
                        events: events,
                      });
                    }
                  }
                } else {
                  if (value.error.responseCode != 5009) {
                    let msg = value.error.message;
                    let snackbarData = {
                      isOpen: true,
                      message: msg,
                      type: "warning",
                    };
                    this.showAlert(snackbarData);
                  }
                }
              }
              this.suggestSlots(index);
              this.getBusyMembers(index);
            }
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          if (
            err &&
            err.graphQLErrors &&
            err.graphQLErrors[0] &&
            err.graphQLErrors[0].message
          ) {
            let { extensions } = err.graphQLErrors[0];
            if (extensions && extensions.code == "INVALID_MIS_TOKEN") {
              this.getAccessTokenFromRefreshToken("fetchPanelMemberSchedule");
            } else if (extensions && extensions.code == "MIS0103")
              this.fetchAuthToken("fetchPanelMemberSchedule");
            else {
              vm.$store.dispatch("handleApiErrors", {
                error: err,
                action: "retrieving",
                form: "schedule",
                isListError: false,
              });
              vm.isLoading = false;
            }
          } else {
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: "schedule",
              isListError: false,
            });
          }
          vm.isLoading = false;
        });
    },
    removeDuplicateEvents(newEvents, oldEvents) {
      const merged = [...newEvents, ...oldEvents];
      const uniqueEvents = new Map();

      merged.forEach((event) => {
        const key = `${event.title}-${event.start}-${event.end}`;
        if (!uniqueEvents.has(key)) {
          uniqueEvents.set(key, event);
        }
      });

      return Array.from(uniqueEvents.values());
    },
    async getAccessTokenFromRefreshToken(functionName) {
      const msalConfig = {
        auth: {
          clientId: this.clientId,
          authority: Config.microsoftLogin,
          redirectUri: this.redirectUri,
        },
        cache: {
          cacheLocation: "localStorage",
          storeAuthStateInCookie: true,
        },
      };
      const msalInstance = new PublicClientApplication(msalConfig);
      try {
        await msalInstance.initialize();
        const tokenRequest = {
          scopes: [
            "User.Read",
            "Calendars.ReadWrite",
            "OnlineMeetings.ReadWrite",
          ],
          account: msalInstance.getAllAccounts()[0],
        };
        const response = await msalInstance.acquireTokenSilent(tokenRequest);
        localStorage.setItem("outlookAccess", response.accessToken);
        this[functionName]();
      } catch (error) {
        let msg = error.response?.data?.error_description?.split(".")[0];
        let snackbarData = {
          isOpen: true,
          message: msg,
          type: "warning",
        };
        this.showAlert(snackbarData);
        this.fetchAuthToken(functionName);
      }
    },
    async fetchAuthToken(functionName) {
      const msalConfig = {
        auth: {
          clientId: this.clientId,
          authority: Config.microsoftLogin,
          redirectUri: this.redirectUri,
        },
        cache: {
          cacheLocation: "localStorage",
          storeAuthStateInCookie: true,
        },
      };
      let scopes = [
        "User.Read",
        "Calendars.ReadWrite",
        "OnlineMeetings.ReadWrite",
      ];
      const msalInstance = new PublicClientApplication(msalConfig);
      try {
        await msalInstance.initialize();
        let response = await msalInstance.loginPopup({
          scopes: scopes,
          prompt: "consent",
        });
        localStorage.setItem("outlookAccess", response.accessToken);
        this[functionName]();
      } catch (err) {
        let error = err.message;
        if (error.includes("user_cancelled")) {
          error =
            "The login process was canceled, preventing access to the panel members' calendars.";
          this.selectedCalendar = null;
        } else if (error.includes("interaction_in_progress")) {
          error =
            "The login process is already in progress, either complete the process or close the window and try again to access the panel members' calendars.";
          this.selectedCalendar = null;
        }
        let snackbarData = {
          isOpen: true,
          message: error,
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    getMicrosoftCredentials() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_MICROSOFT_CREDENTIALS,
          variables: {
            Type: "microsoft calendar",
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAWSCognitoIdentities &&
            response.data.getAWSCognitoIdentities.data &&
            response.data.getAWSCognitoIdentities.data.workwiselymsapplicationID
          ) {
            let { workwiselymsapplicationID } =
              response.data.getAWSCognitoIdentities.data;
            vm.clientId = workwiselymsapplicationID;
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleGetMicrosoftCredentialsError(err);
        });
    },
    handleGetMicrosoftCredentialsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "microsoft credentials",
        isListError: false,
      });
    },
    suggestSlots(index) {
      let duration = 30;
      if (this.rounds[index].Duration) {
        duration = this.convertToMinutes(this.rounds[index].Duration);
      }
      if (this.selectedMemebers.length == 0) return;
      let slots = this.findAvailableSlots(
        this.selectedMemebers,
        this.interviewDate,
        duration
      );
      this.rounds[index].suggestedStartTimes = slots;
      this.rounds[index].showSuggestions = true;
    },
    convertToMinutes(duration) {
      const regex = /(\d+)h\s*(\d+)m/; // Regex to match the pattern 'Xh Ym'
      const match = duration.match(regex);
      if (match) {
        const hours = parseInt(match[1], 10); // Extract hours
        const minutes = parseInt(match[2], 10); // Extract minutes
        return hours * 60 + minutes; // Convert to minutes
      }
      return 0;
    },
    findAvailableSlots(panelMembers, selectedDate, minSlotDuration = 30) {
      // Helper function to find available gaps in a panel member's events
      function getAvailableSlots(events, selectedDate) {
        // Convert selected date to a Date object (start of the day)
        const startOfDay = new Date(selectedDate);
        startOfDay.setHours(0, 0, 0, 0); // Start of the selected date

        const endOfDay = new Date(startOfDay);
        endOfDay.setHours(23, 59, 59, 999); // End of the selected date

        // Filter events that are within the selected date
        const filteredEvents = events.filter((event) => {
          const eventStart = new Date(event.start);
          return eventStart >= startOfDay && eventStart <= endOfDay;
        });

        // Sort events by start time
        filteredEvents.sort((a, b) => new Date(a.start) - new Date(b.start));

        const availableSlots = [];
        let currentEnd = new Date(startOfDay); // Start from the beginning of the day

        // Loop through events to find gaps
        for (const event of filteredEvents) {
          const eventStart = new Date(event.start);
          if (eventStart > currentEnd) {
            // If there's a gap, check if it's long enough to be a valid slot
            const gapDuration = (eventStart - currentEnd) / 60000; // Convert from ms to minutes
            if (gapDuration >= minSlotDuration) {
              // Add multiple slots within the gap, if large enough
              let slotStart = currentEnd;
              while (
                slotStart.getTime() + minSlotDuration * 60000 <=
                eventStart.getTime()
              ) {
                availableSlots.push({
                  start: new Date(slotStart),
                  end: new Date(slotStart.getTime() + minSlotDuration * 60000),
                  duration: minSlotDuration,
                });
                slotStart = new Date(
                  slotStart.getTime() + minSlotDuration * 60000
                ); // Move to the next slot
              }
            }
          }
          currentEnd = new Date(Math.max(currentEnd, new Date(event.end)));
        }

        // Add final gap after the last event
        let slotStart = currentEnd;
        while (
          slotStart.getTime() + minSlotDuration * 60000 <=
          endOfDay.getTime()
        ) {
          availableSlots.push({
            start: new Date(slotStart),
            end: new Date(slotStart.getTime() + minSlotDuration * 60000),
            duration: minSlotDuration,
          });
          slotStart = new Date(slotStart.getTime() + minSlotDuration * 60000); // Move to the next slot
        }

        return availableSlots;
      }

      // If there is only one panel member, return all available slots for that member
      if (panelMembers.length === 1) {
        return getAvailableSlots(panelMembers[0].events, selectedDate);
      }

      // Get available slots for each panel member
      const allAvailableSlots = panelMembers.map((member) => {
        return getAvailableSlots(member.events, selectedDate);
      });

      // Find the intersection of all available slots across all panel members
      const availableTimeSlots = [];

      // Loop through the slots of the first panel member
      for (let slot of allAvailableSlots[0]) {
        const isCommonSlot = allAvailableSlots.every((memberSlots) => {
          return memberSlots.some(
            (memberSlot) =>
              memberSlot.start <= slot.start && memberSlot.end >= slot.end
          );
        });

        if (isCommonSlot) {
          availableTimeSlots.push(slot);
        }
      }

      return availableTimeSlots;
    },

    findOverlappingSlots(freeSlotsList) {
      const overlaps = [];

      for (let i = 0; i < freeSlotsList[0].length; i++) {
        let currentSlot = freeSlotsList[0][i];

        for (let j = 1; j < freeSlotsList.length; j++) {
          currentSlot = this.getSlotOverlap(currentSlot, freeSlotsList[j]);
          if (!currentSlot) break;
        }

        if (currentSlot) {
          overlaps.push(currentSlot);
        }
      }

      return overlaps;
    },
    getSlotOverlap(slotA, slotB) {
      const start = new Date(Math.max(slotA.start, slotB.start));
      const end = new Date(Math.min(slotA.end, slotB.end));
      return start < end ? { start, end } : null;
    },
    parseTime(time) {
      if (time instanceof Date) {
        time = time.getHours() + ":" + time.getMinutes();
      }
      const [hours, minutes] = time.split(":").map(Number);
      const now = new Date();
      return new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        hours,
        minutes
      );
    },
    formatTime(date) {
      return date.toTimeString().slice(0, 5); // Return HH:mm format
    },
    formatStartTime(date) {
      const hours =
        date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
      const minutes =
        date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
      return `${hours}:${minutes}`;
    },
    configureCalendars() {
      if (window.location.href.includes("localhost"))
        window.location.href = `${this.baseUrl}recruitment/my-integration`;
      else
        window.location.href = `${this.baseUrl}v3/recruitment/my-integration`;
    },
    generateMeetingUrl(functionName) {
      if (this.selectedMeeting == "Teams Meeting") {
        this.generateTeamsUrl(functionName);
      }
    },
    generateTeamsUrl(functionName) {
      let vm = this;
      vm.isLoading = true;
      let date = "";
      if (this.interviewDate instanceof Date) {
        date = moment(this.interviewDate).format("YYYY-MM-DD");
      } else date = this.interviewDate;
      let start = moment(date + "T" + this.rounds[0].Start_Time)
        .utc()
        .format("YYYY-MM-DDTHH:mm:ss");
      let end = moment(date + "T" + this.rounds[0].End_Time)
        .utc()
        .format("YYYY-MM-DDTHH:mm:ss");
      vm.$apollo
        .query({
          query: GENERATE_MEETING_URL,
          variables: {
            action: vm.onlineMeetingId ? "update" : "add",
            panelMembers: vm.rounds[0].Panel_Members,
            startDateTime: start,
            endDateTime: end,
            formId: 16,
            onlineMeetingId: this.onlineMeetingId,
          },
          client: "apolloClientBD",
          fetchPolicy: "no-cache",
        })
        .then(async (response) => {
          if (
            response &&
            response.data &&
            response.data.getMicrosoftOnlineMeeting &&
            response.data.getMicrosoftOnlineMeeting.data
          ) {
            let { data } = response.data.getMicrosoftOnlineMeeting;
            data = JSON.parse(data);
            this.onlineMeetingId = data.onlineMeetingId;
            this.meetingUrl = data.onlineMeetingUrl;
            let customEmailRef = this.$refs.customEmail
              ? this.$refs.customEmail
              : null;
            if (
              customEmailRef &&
              !this.notificationTimeNow &&
              !this.selectedCalendar
            ) {
              await customEmailRef.sendCustomEmail(null, this.meetingUrl);
              await customEmailRef.sendCustomEmail(
                this.panelMemberData,
                this.meetingUrl
              );
            }
            if (functionName) vm[functionName]();
            else {
              if (this.isEdit) vm.reScheduleInterviews();
              else vm.scheduleInterviews();
            }
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          if (
            err &&
            err.graphQLErrors &&
            err.graphQLErrors[0] &&
            err.graphQLErrors[0].message
          ) {
            let { extensions } = err.graphQLErrors[0];
            if (extensions && extensions.code == "INVALID_MIS_TOKEN") {
              this.getAccessTokenFromRefreshToken("generateTeamsUrl");
            } else {
              vm.$store.dispatch("handleApiErrors", {
                error: err,
                action: "generating",
                form: "meeting link",
                isListError: false,
              });
              vm.isLoading = false;
            }
          } else {
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "generating",
              form: "meeting",
              isListError: false,
            });
          }
          vm.isLoading = false;
        });
    },
    getBusyMembers(index = 0) {
      let startTime = this.rounds[index].Start_Time;
      let endTime = this.rounds[index].End_Time;
      let busyMembers = [];
      this.selectedMemebers.forEach((member) => {
        let isBusy = member.events.some((event) => {
          const eventStartTime = event.start.toTimeString().slice(0, 5);
          const eventEndTime = event.end.toTimeString().slice(0, 5);
          return startTime < eventEndTime && endTime > eventStartTime;
        });
        if (isBusy) {
          busyMembers.push(member.name);
        }
      });
      this.rounds[index].busyPanelMembers = busyMembers;
    },
    getInternalPanelSchedule(
      startTime = "00:00:00",
      endTime = "23:59:59",
      index = 0,
      panelMember = []
    ) {
      const startObj = moment(startTime, "HH:mm");
      const endObj = moment(endTime, "HH:mm");
      const duration = endObj.diff(startObj, "minutes");
      if (duration < 5) {
        return;
      }
      let vm = this;
      vm.isLoading = true;
      let date = "";
      if (this.interviewDate instanceof Date) {
        date = moment(this.interviewDate).format("YYYY-MM-DD");
      } else date = this.interviewDate;
      let start = moment(date + "T" + startTime)
        .utc()
        .format("YYYY-MM-DDTHH:mm:ss");
      let end = moment(date + "T" + endTime)
        .utc()
        .format("YYYY-MM-DDTHH:mm:ss");
      vm.$apollo
        .query({
          query: GET_PANEL_INTERNAL_SCHEDULE,
          variables: {
            panelMembers: panelMember.length
              ? panelMember
              : this.rounds[index].Panel_Members,
            startDateTime: start,
            endDateTime: end,
            formId: 16,
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (data && data.getPanelMemberScheduledInterview) {
            let { data } = response.data.getPanelMemberScheduledInterview;
            data = JSON.parse(data);
            if (date && data.length) {
              for (let schedule of data) {
                let events = [];
                if (
                  this.isEdit &&
                  this.editedInterviewDetails &&
                  this.editedInterviewDetails.rounds &&
                  this.editedInterviewDetails.rounds[index]
                ) {
                  if (
                    moment(date + "T" + this.rounds[index].Start_Time).format(
                      "YYYY-MM-DDTHH:mm:ss"
                    ) ==
                      moment(
                        this.editedInterviewDetails.rounds[index]
                          .Round_Start_Date_Time
                      ).format("YYYY-MM-DDTHH:mm:ss") &&
                    moment(date + "T" + this.rounds[index].End_Time).format(
                      "YYYY-MM-DDTHH:mm:ss"
                    ) ==
                      moment(
                        this.editedInterviewDetails.rounds[index]
                          .Round_End_Date_Time
                      ).format("YYYY-MM-DDTHH:mm:ss")
                  ) {
                    for (let event of schedule.scheduleItems) {
                      const startUTC = moment
                        .utc(event.start.dateTime)
                        .toDate();
                      const endUTC = moment.utc(event.end.dateTime).toDate();
                      let startLocal = moment(startUTC, "YYYY-MM-DD HH:mm:ss")
                        .local()
                        .format("YYYY-MM-DDTHH:mm:ss");
                      let endLocal = moment(endUTC, "YYYY-MM-DD HH:mm:ss")
                        .local()
                        .format("YYYY-MM-DDTHH:mm:ss");
                      if (
                        startLocal !==
                          moment(
                            date + "T" + this.rounds[index].Start_Time
                          ).format("YYYY-MM-DDTHH:mm:ss") &&
                        endLocal !==
                          moment(
                            date + "T" + this.rounds[index].End_Time
                          ).format("YYYY-MM-DDTHH:mm:ss")
                      ) {
                        events.push({
                          title: event.subject,
                          start: vm.convertToLocal(event.start.dateTime),
                          end: vm.convertToLocal(event.end.dateTime),
                          class: "bg-hover",
                        });
                      }
                    }
                  } else {
                    events = schedule.scheduleItems.map((event) => {
                      return {
                        title: event.subject,
                        start: vm.convertToLocal(event.start.dateTime),
                        end: vm.convertToLocal(event.end.dateTime),
                        class: "bg-hover",
                      };
                    });
                  }
                } else {
                  events = schedule.scheduleItems.map((event) => {
                    return {
                      title: event.subject,
                      start: vm.convertToLocal(event.start.dateTime),
                      end: vm.convertToLocal(event.end.dateTime),
                      class: "bg-hover",
                    };
                  });
                }

                let memberIndex = vm.selectedMemebers.findIndex(
                  (member) => member.id == schedule.panelMemberId
                );
                if (memberIndex != -1) {
                  let memberObj = vm.panelMemberOptions.find(
                    (member) => member.Employee_Id == schedule.panelMemberId
                  );
                  let scheduleDetails = {
                    id: schedule.panelMemberId,
                    name: memberObj?.Emp_Name,
                    email: memberObj?.Emp_Email,
                    Microsoft_Email: memberObj?.Microsoft_Email,
                    events: events,
                  };
                  vm.selectedMemebers[memberIndex] = scheduleDetails;
                } else {
                  let memberObj = vm.panelMemberOptions.find(
                    (member) => member.Employee_Id == schedule.panelMemberId
                  );
                  let scheduleDetails = {
                    id: schedule.panelMemberId,
                    name: memberObj?.Emp_Name,
                    email: memberObj?.Emp_Email,
                    Microsoft_Email: memberObj?.Microsoft_Email,
                    events: events,
                  };
                  vm.selectedMemebers.push(scheduleDetails);
                }
              }
            } else {
              let panelMembers = panelMember.length
                ? panelMember
                : this.rounds[index].Panel_Members;
              panelMembers.forEach((item) => {
                let index = vm.selectedMemebers.findIndex(
                  (member) => member.id == item
                );
                if (index != -1) {
                  vm.selectedMemebers[index].events = [];
                } else {
                  let memberObj = this.panelMemberOptions.find(
                    (member) => member.Employee_Id == item
                  );
                  let scheduleDetails = {
                    id: item,
                    name: memberObj.Emp_Name,
                    email: memberObj.Emp_Email,
                    Microsoft_Email: memberObj.Microsoft_Email,
                    events: [],
                  };
                  vm.selectedMemebers.push(scheduleDetails);
                }
              });
            }
          }
          if (this.selectedCalendar?.toLowerCase() == "microsoft") {
            this.fetchPanelMemberSchedule(
              startTime,
              endTime,
              index,
              panelMember
            );
          } else if (this.selectedCalendar == null) {
            this.getBusyMembers(index);
          }
          this.suggestSlots(index);
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "getting",
            form: "panel member schedule",
            isListError: false,
          });
        });
    },
    fetchEmailTemplates() {
      let vm = this;
      let interviewType = vm.interviewType?.toLowerCase();
      let categoryId =
        interviewType && interviewType === "assessment link" ? 4 : 5;

      // Default value for categoryTypeId
      let categoryTypeId = null;

      // Check conditions when categoryId is 5
      if (categoryId === 5) {
        if (vm.actionType?.toLowerCase() === "edit/reschedule") {
          categoryTypeId = 1;
        } else {
          const categoryMapping = {
            online: { true: 10, false: 8 },
            "face to face": { true: 11, false: 9 },
          };

          if (
            categoryMapping[interviewType] &&
            vm.candidateWillSelect !== undefined
          ) {
            categoryTypeId =
              categoryMapping[interviewType][vm.candidateWillSelect];
          }
        }
      }
      vm.isEmailTemplateListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CUSTOM_EMAIL_TEMPLATES,
          variables: {
            formId: 16,
            categoryId: categoryId,
            categoryTypeId: categoryTypeId,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listCustomEmailTemplates &&
            response.data.listCustomEmailTemplates.emailTemplates
          ) {
            vm.emailTemplateList =
              response.data.listCustomEmailTemplates.emailTemplates;
            vm.noCustomTemplate =
              vm.emailTemplateList?.length === 0 ? true : false;
          }
          vm.isEmailTemplateListLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.isEmailTemplateListLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "email templates",
        isListError: false,
      });
    },
  },
};
</script>
<style>
.headingColor {
  background-color: rgb(var(--v-theme-primary));
}

.overlay-card {
  height: 100%;
  width: 100%;
  background: white;
}

.overlay-content-parent {
  z-index: 1000 !important;
}

.overlay-content-parent > .v-overlay__content {
  height: 100%;
  width: 700px;
}

@media only screen and (max-width: 600px) {
  .overlay-content-parent > .v-overlay__content {
    width: 100%;
  }
}

.overlay-body {
  padding: 15px;
  height: calc(100vh - 130px);
  overflow-y: scroll !important;
  overflow: hidden;
}

#job-options-card {
  min-height: 30%;
}

.overlay-footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}

.google-auto-complete-address-field {
  width: 100% !important;
  border: none !important;
  z-index: 3000 !important;
  position: relative;
  top: 0;
}

.google-auto-complete-address-field:focus {
  outline: none !important;
}

.google-auto-complete-address-field .pac-container {
  z-index: 3000 !important;
  /* Ensure this is higher than the z-index of the overlay */
}
</style>
