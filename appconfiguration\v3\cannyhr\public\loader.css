.custom-loading-cls {
  background: white;
  position: fixed;
  height: 100%;
  width: 100%;
  z-index: 5000;
  top: 0;
  left: 0;
  float: left;
  text-align: center;
  background-repeat: no-repeat;
  background-size: cover;
  /* background-image: url('./app-loader-bg.png'), linear-gradient(white,white); */
  background-blend-mode: darken;
  opacity: 0.85;
}

@media screen and (max-width: 960px){
  .custom-loading-cls {
    background-image: none !important;
  }
}

.loader {
  height: 20px;
  width: 450px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
.loader--dot {
  animation-name: loader;
  animation-timing-function: ease-in-out;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  height: 20px;
  width: 20px;
  border-radius: 100%;
  background-color: black;
  position: absolute;
  border: 2px solid white;
}
.loader--dot:first-child {
  background-color: #84db39;
  animation-delay: 0.5s;
}
.loader--dot:nth-child(2) {
  background-color: #804bbc;
  animation-delay: 0.4s;
}
.loader--dot:nth-child(3) {
  background-color: #fa3c5a;
  animation-delay: 0.3s;
}
.loader--dot:nth-child(4) {
  background-color: #e58315;
  animation-delay: 0.2s;
}
.loader--dot:nth-child(5) {
  background-color: #3bade3;
  animation-delay: 0.1s;
}
.loader--dot:nth-child(6) {
  background-color: #f6e61a;
  animation-delay: 0s;
}

@keyframes loader {
  15% {
    transform: translateX(0);
  }
  45% {
    transform: translateX(230px);
  }
  65% {
    transform: translateX(230px);
  }
  95% {
    transform: translateX(0);
  }
}