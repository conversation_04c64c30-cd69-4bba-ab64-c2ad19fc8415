<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template
        v-if="!fillForm && !isErrorInList && !showPostedJobDetails"
        #topBarContent
      >
        <v-row
          v-if="backUpJobPostData && backUpJobPostData.length > 0"
          justify="center"
        >
          <v-col cols="12" md="9" class="d-flex justify-end">
            <!-- this the component which has search component and based on prop isFilter filter component is also rendered -->
            <EmployeeDefaultFilterMenu class="justify-end" :isFilter="false" />
            <FormFilter
              ref="formFilterRef"
              :items="backUpJobPostData"
              @reset-filter="resetFilter()"
              @apply-filter="applyFilter($event)"
            ></FormFilter>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
  </div>
  <v-container fluid class="job-post-container">
    <v-window v-model="currentTabItem" v-if="formAccess">
      <v-window-item :value="currentTabItem">
        <div v-if="!fillForm && !showPostedJobDetails && !showCloneForm">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else-if="!isErrorInList">
            <div
              v-if="jobPostData.length > 0"
              class="d-flex align-center my-3"
              :class="isMobileView ? 'justify-center ' : 'justify-end'"
            >
              <div v-if="formAccess && formAccess.add">
                <v-tooltip
                  :text="
                    !isRecruiter
                      ? 'Only recruiters are allowed to add/clone the job post.'
                      : ''
                  "
                >
                  <template v-slot:activator="{ props }">
                    <v-btn
                      @click="addButtonClicked()"
                      class="px-6 mr-2 primary"
                      variant="elevated"
                      :size="isMobileView ? 'small' : 'default'"
                      v-bind="!isRecruiter ? props : {}"
                    >
                      <v-icon size="15" class="pr-1 primary"
                        >fas fa-plus</v-icon
                      >
                      <span class="primary">Add</span></v-btn
                    >
                  </template>
                </v-tooltip>
              </div>
              <v-btn
                color="transparent"
                variant="flat"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchJObPostedList()"
              >
                <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu class="mb-1" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                    <v-icon>fas fa-ellipsis-v</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action.key"
                    @click="onMoreAction(action.key)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                          ><v-icon size="15" class="pr-2">{{
                            action.icon
                          }}</v-icon
                          >{{ action.key }}</v-list-item-title
                        >
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
            <!-- <JobPostTable /> -->
            <v-data-table
              v-if="jobPostData.length > 0"
              :headers="headers"
              :items="jobPostData"
              :items-per-page="50"
              :items-per-page-options="[
                { value: 50, title: '50' },
                { value: 100, title: '100' },
                { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
              ]"
              fixed-header
              :height="
                $store.getters.getTableHeightBasedOnScreenSize(290, jobPostData)
              "
              :sort-by="[{ key: 'Posting_Date', order: 'desc' }]"
              class="elevation-1"
              style="box-shadow: none !important"
            >
              <template v-slot:item="{ item, index }">
                <tr
                  style="z-index: 200; cursor: pointer"
                  class="data-table-tr bg-white cursor-pointer"
                  @click="viewEmployeeJobPostDetails(item, index)"
                  :class="[
                    isMobileView
                      ? ' v-data-table__mobile-table-row ma-0 mt-2'
                      : '',
                  ]"
                >
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-small'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Job Title
                    </div>
                    <section>
                      <span
                        class="text-primary text-body-2 font-weight-regular"
                      >
                        {{ item.Job_Post_Name }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Vacancies
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ item.No_Of_Vacancies }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-small'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Applicants
                    </div>
                    <section>
                      <span
                        class="text-primary text-body-2 font-weight-regular"
                      >
                        {{ item.Number_Of_Applicants }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Posting Date
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ formatDate(item.Posting_Date) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Posted By
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ item.Added_By }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Ageing
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ item.Ageing }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Status
                    </div>
                    <section class="d-flex align-center justify-space-between">
                      <div class="d-flex align-center justify-space-around">
                        <span
                          id="w-80"
                          :class="getStatusClass(item.Job_Post_Status)"
                          class="text-body-2 font-weight-regular d-flex justify-center align-center"
                          >{{ item.Job_Post_Status }}</span
                        >
                      </div>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-medium'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Closing Date
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ formatDate(item.Closing_Date) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Actions
                    </div>
                    <section>
                      <div class="d-flex justify-center">
                        <ActionMenu
                          v-if="getActions(item).length"
                          @selected-action="onActions($event, item)"
                          :actions="getActions(item)"
                          :access-rights="checkAccess()"
                          :isPresentTooltip="true"
                          iconColor="grey"
                        ></ActionMenu>
                        <section class="text-body-2 font-weight-medium" v-else>
                          -
                        </section>
                      </div>
                    </section>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </div>
          <div v-if="!listLoading && backUpJobPostData.length == 0">
            <AppFetchErrorScreen key="no-results-screen">
              <template #contentSlot>
                <div v-if="!isSmallTable" style="max-width: 80%">
                  <v-row
                    v-if="!isLoading"
                    style="background: white"
                    class="rounded-lg pa-5 mb-4"
                  >
                    <v-col cols="12">
                      <NotesCard
                        notes="To maintain the highest standards in your recruitment process and ensure alignment with your organizational values, we have implemented a structured approval workflow for all job postings. Start by crafting your job description using our designated template, ensuring it's both comprehensive and unbiased."
                        backgroundColor="transparent"
                        class="mb-2"
                      ></NotesCard>
                      <NotesCard
                        notes="After incorporating any preliminary internal feedback, proceed to submit the job description for the formal approval process. This process will usually include review by your immediate supervisor, a representative from HR, and, depending on the workflow settings, potentially a senior leader. Make use of our portal's real-time tracking feature to keep an eye on the approval status. In the event of a rejection, promptly address the feedback specified in the portal."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                      <NotesCard
                        notes="Once all approvals are in place, the job post will be auto-published. Should you wish, you can also opt to broadcast the job listing on external job portals, provided the necessary integration has been activated."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                    </v-col>

                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <div v-if="formAccess && formAccess.add">
                        <v-tooltip
                          :text="
                            !isRecruiter
                              ? 'Only recruiters are allowed to add/clone the job post.'
                              : ''
                          "
                        >
                          <template v-slot:activator="{ props }">
                            <v-btn
                              @click="addButtonClicked()"
                              class="px-6 seconary mr-2"
                              v-bind="!isRecruiter ? props : {}"
                              variant="elevated"
                            >
                              <v-icon size="15" class="pr-1">fas fa-plus</v-icon
                              ><span class="primary">Add Job Post</span>
                            </v-btn></template
                          ></v-tooltip
                        >
                      </div>
                      <v-btn
                        color="transparent"
                        variant="flat"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="refetchJObPostedList()"
                      >
                        <v-icon class="pr-1" color="grey"
                          >fas fa-redo-alt</v-icon
                        >
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>
          </div>
          <div v-if="jobPostData.length == 0 && emptyFilterScreen">
            <AppFetchErrorScreen
              image-name="common/no-records"
              main-title="There are no job-posts for the selected filters/searches."
            >
              <template #contentSlot>
                <div class="d-flex mb-2 flex-wrap justify-center">
                  <v-btn
                    color="primary"
                    variant="elevated"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click.stop="resetFilter()"
                  >
                    Reset Filter/Search
                  </v-btn>
                </div>
              </template>
            </AppFetchErrorScreen>
          </div>
          <div v-else-if="isErrorInList && backUpJobPostData.length != 0">
            <AppFetchErrorScreen
              :content="errorContent"
              icon-name="fas fa-redo-alt"
              :button-text="showRetryBtn ? 'Retry' : ''"
              @button-click="refetchJObPostedList()"
            >
            </AppFetchErrorScreen>
          </div>
        </div>
        <AppWarningModal
          v-if="openWarningModal"
          :open-modal="openWarningModal"
          :confirmation-heading="warningText"
          :icon-name="warningIconClass"
          @close-warning-modal="onCloseWarningModal()"
          @accept-modal="deleteThisJob(deleteItem)"
        >
          <template>
            <v-form>
              <v-textarea
                variant="outlined"
                auto-grow
                rows="1"
                class="mt-4"
                :style="isMobileView ? '' : 'min-width: 300px'"
              ></v-textarea>
            </v-form>
          </template>
        </AppWarningModal>
      </v-window-item>
    </v-window>
    <AppAccessDenied v-else></AppAccessDenied>
  </v-container>
  <v-dialog v-model="showOptionModal" class="rounded-lg" max-width="1000">
    <v-card class="px-8 rounded-lg" min-width="100%">
      <v-icon
        color="primary"
        class="font-weight-bold mt-4 ml-auto"
        size="20"
        @click="showOptionModal = false"
        >far fa-times
      </v-icon>
      <v-card-title class="mt-n4">
        <div class="text-primary text-center font-weight-medium">
          Add Job Post
        </div>
      </v-card-title>
      <div style="overflow: scroll">
        <v-radio-group inline v-model="showWorkFlow">
          <template v-slot:label>
            <p class="text-wrap">
              Would you like to associate workflow for jobpost approval?
            </p>
          </template>
          <v-radio label="Yes" value="Yes"></v-radio>
          <v-radio label="No" value="No"></v-radio>
        </v-radio-group>
        <CustomSelect
          v-if="showWorkFlow.toLowerCase() === 'yes'"
          v-model="selectedWorkflow"
          class="ml-4"
          :items="dropdownWorkflow"
          item-title="Workflow_Name"
          item-value="Workflow_Id"
          :isAutoComplete="true"
          label="Choose the approval workflow"
          variant="underlined"
          :isLoading="workflowApprovalLoading"
          clearable
          is-required
          :itemSelected="selectedWorkflow"
          :rules="[required('Workflow', selectedWorkflow)]"
          @selected-item="selectedWorkflow = $event"
        ></CustomSelect>
        <div v-if="isMppIntegration.toLowerCase() === 'yes'">
          <v-radio-group inline v-model="requestType" class="mt-2 ml-2">
            <v-radio
              label="New Position & Additional Headcount"
              value="New Position"
              @click="retrieveNewPositionList()"
            ></v-radio>
            <v-radio
              label="Approved & Forecasted Positions"
              value="Recruitment Request"
              @click="retrieveRecruitmentList()"
            ></v-radio>
          </v-radio-group>
          <v-row>
            <v-col cols="12" md="6" sm="12">
              <CustomSelect
                v-model="selectedPosition"
                color="primary"
                class="ml-4"
                :isLoading="positionListLoading"
                :itemSelected="selectedPosition"
                :items="positionGroup"
                label="Position Group"
                variant="underlined"
                item-title="Pos_Name"
                item-value="Originalpos_Id"
                :disabled="newPositionListLoading"
                clearable
                :isAutoComplete="true"
                @update:model-value="updateGroup()"
              />
            </v-col>
            <v-col cols="12" md="6" sm="12">
              <CustomSelect
                v-model="selectedDivision"
                color="primary"
                class="ml-4"
                :items="divisionList"
                clearable
                :isAutoComplete="true"
                label="Division"
                item-title="Pos_full_Name"
                :itemSelected="selectedDivision"
                :is-loading="divisionListLoading"
                variant="underlined"
                item-value="Pos_Code"
                :disabled="!selectedPosition"
                @update:model-value="updateDivision()"
              />
            </v-col>

            <v-col cols="12" md="6" sm="12">
              <CustomSelect
                v-model="selectedDepartment"
                color="primary"
                :items="departmentList"
                :loading="divisionListLoading"
                class="ml-4"
                clearable
                variant="underlined"
                :isAutoComplete="true"
                label="Department"
                placeholder="Department"
                item-title="Pos_full_Name"
                :itemSelected="selectedDepartment"
                item-value="Pos_Code"
                :disabled="!selectedDivision"
                @update:model-value="updateDepartment()"
              />
            </v-col>
            <v-col cols="12" md="6" sm="12">
              <CustomSelect
                v-model="selectedSection"
                color="primary"
                :items="sectionList"
                :loading="divisionListLoading"
                class="ml-4"
                clearable
                variant="underlined"
                :isAutoComplete="true"
                label="Section"
                placeholder="Section"
                item-title="Pos_full_Name"
                :itemSelected="selectedSection"
                :disabled="!selectedDepartment"
                item-value="Pos_Code"
                @update:model-value="updateSection()"
              />
            </v-col>
            <v-col cols="12" md="6" sm="12"></v-col>
            <v-col cols="12" md="6" sm="12">
              <v-text-field
                v-model="mppSearchList"
                label="Search"
                density="compact"
                prepend-inner-icon="fas fa-search"
                variant="underlined"
                clearable
                class="ml-4"
              />
            </v-col>
          </v-row>
        </div>
        <v-divider class="mb-4"></v-divider>
        <div v-if="isMppIntegration.toLowerCase() === 'yes'">
          <v-row no-gutters>
            <v-col cols="12" class="pa-0 mb-4 flex align-center">
              <v-data-table
                v-if="listData.length > 0"
                v-model="selectedEmpRecords"
                id="gridView"
                :headers="tableHeaders"
                :show-select="selectStrategy !== 'single' && !isMobileView"
                :select-strategy="selectStrategy"
                :items="listData"
                :items-per-page="50"
                fixed-header
                :height="
                  $store.getters.getTableHeightBasedOnScreenSize(200, listData)
                "
                :items-per-page-options="[
                  { value: 50, title: '50' },
                  { value: 100, title: '100' },
                  { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
                ]"
              >
                <template #item="{ item }">
                  <tr
                    class="data-table-tr bg-white cursor-pointer"
                    :class="
                      isMobileView
                        ? 'v-data-table__mobile-table-row'
                        : selectStrategy === 'single'
                        ? 'cursor-pointer'
                        : ''
                    "
                    @click="selectStrategy === 'single' ? selectItem(item) : {}"
                  >
                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Position
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section
                          v-if="requestType?.toLowerCase() === 'new position'"
                          class="text-primary text-body-2"
                        >
                          {{ checkNullValue(item.Position_Title) }}
                        </section>
                        <section v-else class="text-primary text-body-2">
                          {{ checkNullValue(item.Pos_Name) }}
                        </section>
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2 text-grey">
                          {{ checkNullValue(item.Pos_Code) }}
                        </section>
                      </div>
                    </td>
                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Status
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2">
                          {{ checkNullValue(item.Status) }}
                        </section>
                      </div>
                    </td>

                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Group
                      </div>
                      <div>
                        {{ checkNullValue(item.Group_Name) }}
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2 text-grey">
                          {{ checkNullValue(item.Group_Code) }}
                        </section>
                      </div>
                    </td>

                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Division
                      </div>
                      <div>
                        {{ checkNullValue(item.Division_Name) }}
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2 text-grey">
                          {{ checkNullValue(item.Division_Code) }}
                        </section>
                      </div>
                    </td>

                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Owner of the Request
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2">
                          {{ checkNullValue(item.Added_By) }}
                        </section>
                      </div>
                    </td>

                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        No of Position
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2">
                          {{ checkNullValue(item.No_Of_Position) }}
                        </section>
                      </div>
                    </td>
                  </tr>
                </template>
              </v-data-table>
              <div v-else-if="newPositionListLoading" class="mt-3">
                <div v-for="i in 3" :key="i" class="mt-4">
                  <v-skeleton-loader
                    ref="skeleton2"
                    type="list-item-avatar"
                    class="mx-auto"
                  ></v-skeleton-loader>
                </div>
              </div>

              <AppFetchErrorScreen
                v-else
                key="no-results-screen"
                main-title="No matching search results found"
                image-name="common/no-records"
              ></AppFetchErrorScreen>
            </v-col>
          </v-row>
        </div>
      </div>
      <div class="text-center">
        <div class="text-center pb-4">
          <v-btn
            rounded="lg"
            class="primary"
            :disabled="
              isMppIntegration.toLowerCase() === 'yes' && !selectedRowIndex
            "
            @click="validateWorkflowForm()"
          >
            <span class="primary">Next</span>
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
  <JobPostForm
    ref="addJobPostForm"
    v-if="fillForm"
    @submit-job-post-form="closeForm"
    :selectedWorkflow="selectedWorkflow"
    :selectedEventId="selectedEventId"
    :selectedPositionRecord="selectedPositionRecord"
    :jobDescriptionData="jobDescriptionData"
    :orgGroupCode="orgGroupCode"
    :availableVacancies="availableVacancies"
    :requestType="requestType"
    :formAccess="formAccess"
  >
  </JobPostForm>
  <AppLoading v-if="isGetJobpostDetailsLoading"></AppLoading>
  <ViewJObPostDetails
    v-if="showPostedJobDetails && !isGetJobpostDetailsLoading"
    @close-view-job-post-details="showPostedJobDetails = false"
    :jobPostId="
      jobPostIdQueryParam ? jobPostIdQueryParam : jobPostId.Job_Post_Id
    "
    :jobPostData="wholeJobPostData"
    :jobPostIdQueryParam="jobPostIdQueryParam"
    :showJobRoundsTab="showJobRoundsTab"
  ></ViewJObPostDetails>
  <AddJobPost
    ref="addJobPostForm"
    v-if="showCloneForm"
    :wholeJobPostData="wholeJobPostData"
    :selectedWorkflow="selectedWorkflow"
    :jobPostId="jobPostId"
    :selectedEventId="selectedEventId"
    :selectedPositionRecord="selectedPositionRecord"
    :jobDescriptionData="jobDescriptionData"
    :orgGroupCode="orgGroupCode"
    :availableVacancies="availableVacancies"
    :requestType="requestType"
    :formAccess="formAccess"
    @submit-job-post-form="closeCloneForm"
  >
  </AddJobPost>
  <AppWarningModal
    v-if="tabChangeWarning"
    :open-modal="tabChangeWarning"
    confirmation-heading="You have filled the job post form. Are you sure to change tab"
    icon-name="far fa-times-circle"
    @close-warning-modal="onDeclineTabChange()"
    @accept-modal="onAcceptTabChange()"
  ></AppWarningModal>
  <AppWarningModal
    v-if="openCloseJobWarningModal"
    :open-modal="openCloseJobWarningModal"
    confirmation-heading="Are you sure to close the Job post?"
    icon-name="far fa-times-circle"
    @close-warning-modal="openCloseJobWarningModal = false"
    @accept-modal="validateCloseJob()"
    ><template v-slot:warningModalContent>
      <v-row>
        <v-col cols="12" class="text-left">
          <div style="background-color: white" class="rounded-lg pa-4 mb-2">
            <NotesCard
              :notes="`Closing the job post will set the current date: ${formatDate(
                currentDate
              )} as the closing date on the job post. Additionally, based on your selection below, the job post will be removed from the job board.`"
              backgroundColor="transparent"
            ></NotesCard>
          </div>
          <div v-if="isStatusIsPublished || seekTokenLoading" class="mt-3">
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else>
            <div v-if="isLinkedinPublished" class="d-flex mb-n6">
              <span class="v-label pr-6 pb-5">LinkedIn</span>
              <v-switch color="primary" v-model="closeOnLinkedIn"></v-switch>
            </div>
            <div v-if="isIndeedPublished" class="d-flex mb-n6">
              <span class="v-label pr-9 pb-5">Indeed</span>
              <v-switch color="primary" v-model="closeOnIndeed"></v-switch>
            </div>
            <div
              v-if="isJobStreetPublished && isTokenPassed"
              class="d-flex mb-n6"
            >
              <span class="v-label pr-3 pb-5">Job Street</span>
              <v-switch color="primary" v-model="closeOnJobStreet"></v-switch>
            </div>
          </div>
        </v-col>
      </v-row> </template
  ></AppWarningModal>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="mt-n5 primary"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <div class="d-flex justify-center">
    <v-dialog v-model="showSignInForm" max-width="70%">
      <irukkaSignInForm
        :formType="formType"
        @change-status="submitFunctionality"
        @close="showSignInForm = false"
      >
      </irukkaSignInForm>
    </v-dialog>
  </div>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "../../../mixins/validationRules.js";
import moment from "moment";
import { defineAsyncComponent } from "vue";
import FileExportMixin from "@/mixins/FileExportMixin";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import { GET_JOB_POST_DETAILS } from "@/graphql/workflow/approvalManagementQueries.js";
const FormFilter = defineAsyncComponent(() =>
  import("./JobPostFormFilter.vue")
);
const irukkaSignInForm = defineAsyncComponent(() => import("./SignInForm.vue"));
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
const AddJobPost = defineAsyncComponent(() => import("./JobPostForm.vue"));
import {
  POSTED_JOBS,
  DELETE_JOB_POST,
  LIST_WORKFLOW,
  CLOSE_JOB_POST,
  RECRUITMENT_SETTINGS,
  RETRIEVE_POSITION_JOB_SUMMARY,
  GET_CUSTOM_GROUP_COVERAGE,
} from "@/graphql/settings/irukka-integration/jobPostFormQueries.js";
import {
  LIST_RECRUITMENT_REQUEST,
  NEW_POSITION_LIST,
} from "@/graphql/mpp/manPowerPlanningQueries";
import {
  RETRIEVE_INDEED_JOBPOST_DETAILS,
  CLOSE_JOBPOST_TO_INDEED,
  GET_INDEED_AUTH_TOKEN,
  GET_ENCRYPTION_KEY,
} from "@/graphql/recruitment/job-post/indeedIntegrationQueries.js";
import { LIST_OF_POSITION_LIST } from "@/graphql/mpp/manPowerPlanningQueries";
import {
  CLOSE_SEEK_INTEGRATION,
  JOB_STREET_JOB_DETAILS,
} from "@/graphql/recruitment/job-seek/jobStreetQueries.js";
import {
  RETRIVE_LINKEDIN_JOB_DETAILS,
  DELETE_LINKEDIN_JOB_POST,
} from "@/graphql/settings/Integration/publishJobToLinkedin";
import { checkNullValue } from "@/helper";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const JobPostForm = defineAsyncComponent(() => import("./JobPostForm.vue"));
const ViewJObPostDetails = defineAsyncComponent(() =>
  import("./JobPostViewPage.vue")
);
import { getErrorCodes, convertUTCToLocal } from "@/helper.js";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { GET_SEEK_TOKEN } from "@/graphql/recruitment/job-seek/jobStreetQueries.js";
import { ORG_STRUCTURE_BASED_ON_GROUP } from "@/graphql/mpp/newPositionQueries";
export default {
  name: "JobPostPage",
  components: {
    CustomSelect,
    JobPostForm,
    ViewJObPostDetails,
    EmployeeDefaultFilterMenu,
    FormFilter,
    irukkaSignInForm,
    AddJobPost,
    NotesCard,
    ActionMenu,
  },
  mixins: [validationRules, FileExportMixin],
  data() {
    return {
      currentTabItem: "tab-0",
      selectedWorkflow: null,
      dropdownWorkflow: [],
      fillForm: false,
      workflowApprovalLoading: false,
      positionListLoading: false,
      showSignInForm: false,
      irukkaAuthToken: "",
      openCloseJobWarningModal: false,
      orgCode: "",
      headers: [
        {
          title: "Job Title",
          align: "start",
          key: "Job_Post_Name",
        },
        { title: "Vacancies", key: "No_Of_Vacancies" },
        {
          title: "Applicants",
          key: "Number_Of_Applicants",
        },
        { title: "Posting Date", key: "Posting_Date" },
        { title: "Posted By", key: "Added_By" },
        { title: "Ageing (in days)", key: "Ageing" },
        { title: "Status", key: "Job_Post_Status" },
        { title: "Closing Date", key: "Closing_Date" },
        { title: "Actions", key: "actions", sortable: false },
      ],
      jobPostData: [],
      listLoading: true,
      selectedData: [],
      backUpJobPostData: [],
      showSelect: false,
      valuepresent: false,
      selectedItem: null,
      showPostedJobDetails: false,
      showOptionModal: false,
      //WARNING MODEL
      openWarningModal: false,
      warningText: "Are you sure you want to delete this job post",
      deleteItem: null,
      warningIconClass: "",
      wholeJobPostData: null,
      jobPostId: null,
      totalJobPostItems: [],
      deleteThisJobpost: [],
      validationMessages: [],
      showValidationAlert: false,
      numberOfPages: null,
      showCloneForm: false,
      formType: "JobPostPage",
      irukkaStatus: null,
      isGetJobpostDetailsLoading: false,
      selectedEventId: null,
      emptyFilterScreen: false,
      errorContent: "",
      showRetryBtn: true,
      isErrorInList: false,
      getIntegrationStatus: [],
      jobPostIdQueryParam: null,
      backupFilterData: [],
      isCloneForm: false,
      openMoreMenu: false,
      havingAccess: {},
      encryptionKey: null,
      intergrationId: 0,
      indeedSourceId: "",
      indeedIntegraionId: 0,
      jobStreetId: 0,
      closeOnLinkedIn: false,
      closeOnJobStreet: false,
      closeOnIndeed: false,
      isLinkedinPublished: false,
      isJobStreetPublished: false,
      isIndeedPublished: false,
      isStatusIsPublished: false,
      linkedinError: "",
      indeedError: "",
      jobStreetError: "",
      seekTokenLoading: false,
      isTokenPassed: false,
      isMppIntegration: "",
      positionGroup: [],
      selectedPosition: null,
      requestType: "New Position",
      showWorkFlow: "No",
      recruitmentRequestList: [],
      newPositionListLoading: false,
      newPositionList: [],
      selectedRowIndex: null,
      selectedPositionRecord: {},
      positionCode: null,
      selectStrategy: "single",
      selectedEmpRecords: [],
      listData: [],
      availableVacancies: null,
      jobDescriptionData: "",
      orgGroupCode: null,
      divisionList: [],
      divisionListLoading: false,
      selectedDivision: null,
      departmentList: [],
      selectedDepartment: null,
      sectionList: [],
      selectedSection: null,
      jobRequisitionLimitToCallAPI: 10000,
      apiCallCount: 0,
      totalApiCount: 0,
      tabChangeWarning: false,
      tabName: "",
      showJobRoundsTab: "No",
      mppSearchList: "",
    };
  },
  computed: {
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        if (date && date != "0000-00-00") {
          let formattedDate = this.convertUTCToLocal(date);
          return formattedDate.split(" ")[0];
        } else return "-";
      };
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    landedFormName() {
      return "Job Posts";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("15");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    approvalFormAccess() {
      let formAccess = this.accessRights("184");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    isRecruiter() {
      return this.$store.state.isRecruiter;
    },
    mainTabList() {
      return this.approvalFormAccess && this.approvalFormAccess.view
        ? ["Job Posts", "Approvals"]
        : ["Job Posts"];
    },
    tableHeaders() {
      return [
        {
          title: "Position",
          align: "start",
          key:
            this.requestType?.toLowerCase() === "new position"
              ? "Position_Title"
              : "Pos_Name",
        },
        { title: "Status", key: "Status" },
        { title: "Group", key: "Group_Code" },
        {
          title: "Division",
          key: "Division_Code",
        },
        { title: "Owner of the Request", key: "Added_By" },
        { title: "No of Position", key: "No_Of_Position" },
      ];
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    mppSearchList(val) {
      this.onMPPSearch(val);
    },
    selectedWorkflow(val) {
      let selectedWorkflow = this.dropdownWorkflow.find(
        (cat) => cat.Workflow_Id === val
      );
      if (selectedWorkflow) {
        this.selectedEventId = selectedWorkflow.Event_Id;
      }
    },
    selectedPosition(val) {
      const result = this.positionGroup.find(
        (item) => item.Originalpos_Id === val
      );
      this.positionCode = result ? result.Pos_Code : null;
      if (val && this.positionCode) {
        this.refetchList();
      } else {
        this.listData = [];
      }
    },
    selectedDivision(val) {
      if (val) {
        this.refetchList();
      } else {
        this.listData = [];
      }
    },
  },
  mounted() {
    this.jobPostIdQueryParam = this.$route.query.state;
    if (this.jobPostIdQueryParam) {
      this.showPostedJobDetails = true;
      this.getEmployeeJobPostDetails(parseInt(this.jobPostIdQueryParam));
    }
    this.fetchPostedJobs();
    this.loadAuthToken();
    this.getMppIntegrationStatus();
    this.retrievePositionList();
    this.orgCode = this.$store.getters.orgCode;
    this.getCustomGroupCoverage();
  },
  methods: {
    convertUTCToLocal,
    checkNullValue,
    onMPPSearch(val) {
      const listData =
        this.requestType?.toLowerCase() === "new position"
          ? this.newPositionList
          : this.recruitmentRequestList;
      if (!val) {
        this.listData = listData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = listData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.listData = searchItems;
      }
    },
    checkAccess() {
      this.havingAccess["clone"] =
        this.formAccess && this.formAccess.add && this.isRecruiter ? 1 : 0;
      this.havingAccess["close"] =
        this.formAccess && this.formAccess.update && this.isRecruiter ? 1 : 0;
      this.havingAccess["apply"] =
        this.formAccess && this.formAccess.view ? 1 : 0;
      this.havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      return this.havingAccess;
    },
    getActions(item) {
      let list = [];
      if (this.formAccess.add) {
        list.push("Clone");
      }
      if (item.Status_Id != 6) {
        if (item.Status_Id != 1) {
          list.push("Close");
        }
        if (item.Status_Id == 1 || item.Status_Id == 3) {
          list.push("Delete");
        } else if (item.Status_Id == 5) {
          list.push("Apply");
        }
      }
      return list;
      // return item.Status_Id != 6
      //   ? item.Status_Id == 1 || item.Status_Id == 3
      //     ? this.formAccess.add
      //       ? ["Clone", "Close", "Delete"]
      //       : ["Close", "Delete"]
      //     : item.Status_Id == 5

      //     ? this.formAccess.add
      //       ? ["Clone", "Close", "Apply"]
      //       : ["Close", "Apply"]
      //     : this.formAccess.add
      //     ? ["Clone", "Close"]
      //     : ["Close"]
      //   : this.formAccess.add
      //   ? ["Clone"]
      //   : null;
    },
    addButtonClicked() {
      if (this.isRecruiter) {
        this.showOptionModal = true;
        this.showWorkFlow = "No";
        this.requestType = "New Position";
        this.positionCode = null;
        this.selectedPosition = null;
        this.isCloneForm = false;
        this.selectedDivision = null;
        this.refetchList();
        this.retrieveWorkflowDetails();
        if (this.isMppIntegration?.toLowerCase() === "yes")
          this.retrieveCountGroupPosition("group");
      }
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    loadAuthToken() {
      this.irukkaAuthToken = window.$cookies.get("irukkaAuthToken");
    },
    onAcceptTabChange() {
      this.tabChangeWarning = false;
      this.$refs.addJobPostForm.isFormDirty = false;
      this.onTabChange(this.tabName);
    },
    onDeclineTabChange() {
      this.tabChangeWarning = false;
      this.currentTabItem = "tab-0";
    },
    onTabChange(tabName) {
      this.tabName = tabName;
      this.currentTabItem = "tab-" + this.mainTabList.indexOf(tabName);
      if (this.$refs.addJobPostForm?.isFormDirty) {
        this.tabChangeWarning = true;
      } else {
        if (tabName.toLowerCase() === "job posts") {
          this.currentTabItem = "tab-0";
        } else if (tabName == "Approvals") {
          this.isGetJobpostDetailsLoading = true;
          this.$router.push("/approvals/approval-management?form_id=15");
        }
        this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      }
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      const convertToScript = (html) => {
        const tmp = document.createElement("div");
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || "";
      };
      let exportData = JSON.parse(JSON.stringify(this.jobPostData));
      exportData = exportData.map((el) => ({
        ...el,
        Posting_Date: el.Posting_Date ? this.formatDate(el.Posting_Date) : "",
        Closing_Date: el.Closing_Date ? this.formatDate(el.Closing_Date) : "",
        Job_Description: el.Job_Description
          ? convertToScript(el.Job_Description)
          : "",
      }));

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Job Posts",
        sheetName: "Job Posts",
        header: [
          { key: "Job_Post_Name", header: "Job Post Name" },
          { key: "Job_Type", header: "Job Type" },
          { key: "Client_Name", header: "Client Name" },
          { key: "Service_Provider_Name", header: "Service Provider" },
          { key: "Job_Post_Status", header: "Status" },
          { key: "Department_Name", header: "Department" },
          { key: "Experience_Level", header: "Experience Level" },
          { key: "Added_By", header: "Added By" },
          { key: "Posting_Date", header: "Posting Date" },
          { key: "Closing_Date", header: "Closing Date" },
          { key: "No_Of_Vacancies", header: "No Of Vacancies" },
          { key: "Number_Of_Applicants", header: "Number Of Applicants" },
          { key: "City_Name", header: "City" },
          { key: "State_Name", header: "State" },
          { key: "Country_Name", header: "Country" },
          { key: "Job_Description", header: "Job Description" },
        ],
      };

      this.exportExcelFile(exportOptions);
    },
    onApplySearch(val) {
      if (!val) {
        this.jobPostData = this.backupFilterData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupFilterData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.jobPostData = searchItems;
        if (this.jobPostData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    resetFilter() {
      this.jobPostData = this.backUpJobPostData;
      this.backupFilterData = this.backUpJobPostData;
      this.emptyFilterScreen = false;
      this.$refs.formFilterRef.resetAllModelValues();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    applyFilter(filterParams) {
      this.jobPostData = filterParams;
      this.backupFilterData = filterParams;
      this.onApplySearch(this.searchValue);
      if (this.jobPostData.length == 0) {
        this.emptyFilterScreen = true;
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    validateUser(item) {
      this.jobPostId = item.Job_Post_Id;
      this.openCloseJobWarningModal = true;
      this.closeOnLinkedIn = false;
      this.closeOnJobStreet = false;
      this.closeOnIndeed = false;
      this.retrieveLinkedInJobPostPublishDetails();
      this.retrieveIndeedJobPostDetails();
      this.retrieveJobStreetJobPostDetails();
      this.getEncryptionKey();
    },
    async getSeekToken(hirerId) {
      let vm = this;
      vm.seekTokenLoading = true;
      await vm.$apollo
        .query({
          query: GET_SEEK_TOKEN,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            hirer_Id: hirerId,
            form_Id: 15,
            isPublish: 1,
          },
        })
        .then(async (response) => {
          vm.seekTokenLoading = false;
          if (
            response &&
            response.data &&
            response.data.getAuthTokenJobStreet &&
            response.data.getAuthTokenJobStreet.getData &&
            response.data.getAuthTokenJobStreet.getData.accessToken
          ) {
            const token = response.data.getAuthTokenJobStreet.getData;
            if (token && token.accessToken && token.browserToken) {
              const browserToken = JSON.parse(token.browserToken);
              const accessToken = JSON.parse(token.accessToken);
              await window.$cookies.set(
                "jobStreet_bowser_token",
                browserToken.access_token,
                browserToken.expires_in
              );
              await window.$cookies.set(
                "jobStreet_access_token",
                accessToken.access_token,
                accessToken.expires_in
              );
              vm.isTokenPassed = true;
            }
          }
        })
        .catch((err) => {
          vm.seekTokenLoading = false;
          vm.handleRetrieveSeekBranding(err);
        });
    },
    handleRetrieveSeekBranding(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job post",
        isListError: false,
      });
    },

    async validateCloseJob() {
      this.openCloseJobWarningModal = false;
      await this.fetchIntegrationStatus();
      if (
        this.irukkaStatus &&
        this.irukkaStatus == "Active" &&
        !this.irukkaAuthToken
      ) {
        this.showSignInForm = true;
      } else {
        // Close job post on selected integrations
        if (this.closeOnLinkedIn) {
          this.closeLinkedInJobPost();
        }
        if (this.closeOnIndeed) {
          await this.getIndeedAuthTokens();
          this.validateCloseJobToIndeed();
        }
        if (this.closeOnJobStreet) {
          this.closeJobPostJobStreet();
        }
        if (!this.linkedinError && !this.indeedError && !this.jobStreetError) {
          this.closeJobpost();
        }
      }
    },

    retrieveLinkedInJobPostPublishDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.isStatusIsPublished = true;
      vm.$apollo
        .query({
          query: RETRIVE_LINKEDIN_JOB_DETAILS,
          variables: {
            jobPostId: parseInt(this.jobPostId),
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveLinkedInJobPostPublishDetails
          ) {
            const status =
              response.data.retrieveLinkedInJobPostPublishDetails.Status;
            this.isLinkedinPublished = status
              ? ["published", "yet to publish"].includes(
                  status.trim().toLowerCase()
                )
              : false;
            this.intergrationId =
              response.data.retrieveLinkedInJobPostPublishDetails?.integrationId;
          } else {
            this.isLinkedinPublished = false;
          }
          vm.isLoading = false;
          vm.isStatusIsPublished = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.isStatusIsPublished = false;
          vm.handleRetrieveLinkedInError(err);
        });
    },
    handleRetrieveLinkedInError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job details",
        isListError: false,
      });
    },

    retrieveIndeedJobPostDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.isStatusIsPublished = true;
      vm.$apollo
        .query({
          query: RETRIEVE_INDEED_JOBPOST_DETAILS,
          variables: {
            jobPostId: parseInt(this.jobPostId),
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveIndeedJobPostDetails &&
            response.data.retrieveIndeedJobPostDetails.data &&
            response.data.retrieveIndeedJobPostDetails.data.length
          ) {
            let indeedData = response.data.retrieveIndeedJobPostDetails.data[0];
            this.isIndeedPublished =
              indeedData?.status === "Published" ? true : false;
            this.indeedIntegraionId = indeedData?.integrationId;
            this.integrationId = indeedData?.integrationId;
            this.indeedSourceId = indeedData?.employerJobId;
          } else {
            this.isIndeedPublished = false;
          }
          vm.isLoading = false;
          vm.isStatusIsPublished = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.isStatusIsPublished = false;
          vm.handleRetrieveIndeedInError(err);
        });
    },
    handleRetrieveIndeedInError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job details",
        isListError: false,
      });
    },

    retrieveJobStreetJobPostDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.isStatusIsPublished = true;
      vm.$apollo
        .query({
          query: JOB_STREET_JOB_DETAILS,
          variables: {
            jobPostId: parseInt(this.jobPostId),
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveJobStreetJobPostDetails &&
            response.data.retrieveJobStreetJobPostDetails.data &&
            response.data.retrieveJobStreetJobPostDetails.data.length
          ) {
            let jobstreetData =
              response.data.retrieveJobStreetJobPostDetails.data[0];
            vm.isJobStreetPublished =
              jobstreetData?.appliedStatus === "Applied" ? true : false;
            vm.jobStreetId = jobstreetData?.jobStreetId;
            if (vm.isJobStreetPublished) {
              vm.getSeekToken(jobstreetData?.hirerId);
            }
          } else {
            vm.isJobStreetPublished = false;
          }
          vm.isLoading = false;
          vm.isStatusIsPublished = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.isStatusIsPublished = false;
          vm.handleRetrieveJobStreetIsError(err);
        });
    },
    handleRetrieveJobStreetIsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job details",
        isListError: false,
      });
    },
    closeForm(data, action) {
      if (action == "close") {
        this.fillForm = false;
      } else {
        this.fillForm = data;
        let snackbarData = {
          isOpen: true,
          message: "Job posted successfully.",
          type: "success",
        };
        this.refetchJObPostedList();
        this.showAlert(snackbarData);
      }
    },
    closeCloneForm(data, action) {
      if (action == "close") {
        this.showCloneForm = false;
      } else {
        this.showCloneForm = false;
        let snackbarData = {
          isOpen: true,
          message: "Job posted successfully.",
          type: "success",
        };
        this.refetchJObPostedList();
        this.showAlert(snackbarData);
      }
    },
    async validateWorkflowForm() {
      const valid =
        this.showWorkFlow.toLowerCase() === "yes"
          ? this.selectedWorkflow
            ? true
            : false
          : true;

      if (this.showWorkFlow.toLowerCase() === "no") {
        this.selectedEventId = null;
        this.selectedWorkflow = null;
      }
      let validVacancies = true;
      if (
        this.selectedPositionRecord &&
        Object.keys(this.selectedPositionRecord).length
      ) {
        if (this.availableVacancies == null || this.availableVacancies <= 0) {
          validVacancies = false;
          let snackbarData = {
            isOpen: true,
            message:
              "You are not allowed to move forward with current chosen record, as this record has no availabe vacancies.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      }
      // submit the form only if all the fields are filled
      if (valid && validVacancies) {
        if (this.isCloneForm) {
          this.showCloneForm = true;
        } else {
          this.fillForm = true;
        }
        this.showOptionModal = false;
      }
    },
    retrieveWorkflowDetails() {
      let vm = this;
      vm.selectedWorkflow = null;
      vm.workflowApprovalLoading = true;
      vm.dropdownWorkflow = [];
      vm.$apollo
        .query({
          query: LIST_WORKFLOW,
          variables: {
            employeeId: vm.loginEmployeeId,
            searchString: "",
            moduleId: ["1"],
            isDropDownCall: 1,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((workflowList) => {
          if (
            workflowList &&
            workflowList.data &&
            workflowList.data.listWorkflow &&
            workflowList.data.listWorkflow.Workflows &&
            workflowList.data.listWorkflow.Workflows.length > 0
          ) {
            this.dropdownWorkflow = workflowList.data.listWorkflow.Workflows;
            this.selectedWorkflow = this.dropdownWorkflow[0].Workflow_Id;
            this.workflowApprovalLoading = false;
          } else {
            this.workflowApprovalLoading = false;
          }
        })
        .catch((err) => {
          this.workflowApprovalLoading = false;
          this.handleRetrieveWorkflowDetailsErrors(err);
        });
    },
    handleRetrieveWorkflowDetailsErrors(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case CHR0037: // Technical error
            snackbarData.message =
              "Oops! error in retrieval of workflow details. Please contact the  platform administrator.";
            break;
          case _DB0100:
            snackbarData.message =
              "Sorry you do not have the access rights to view the workflow details. Please contact HR administrator.";
            break;
          default:
            snackbarData.message =
              "Something went wrong while retrieving the workflow details. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while fetching workflow details. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    getEncryptionKey() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_ENCRYPTION_KEY,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getEncryptionKey &&
            response.data.getEncryptionKey.indeedEncryptionKeyData
          ) {
            this.encryptionKey =
              response.data.getEncryptionKey.indeedEncryptionKeyData;
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleGetEncryptionKeyError(err);
        });
    },
    handleGetEncryptionKeyError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job posts",
        isListError: false,
      });
    },
    async getIndeedAuthTokens() {
      let vm = this;
      vm.isLoading = true;
      await vm.$apollo
        .query({
          query: GET_INDEED_AUTH_TOKEN,
          variables: {
            scope: "employer_access",
            grantType: "client_credentials",
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getIndeedAuthToken &&
            response.data.getIndeedAuthToken.data
          ) {
            let authData = JSON.parse(response.data.getIndeedAuthToken.data);
            let accessToken = authData.access_token;
            let sourceName = response.data.getIndeedAuthToken.sourceName;
            window.$cookies.set("sourceName", sourceName, "60MIN");
            window.$cookies.set("indeedAccessToken", accessToken, "60MIN");
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleGetIndeedAuthTokensError(err);
        });
    },
    handleGetIndeedAuthTokensError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job post",
        isListError: false,
      });
    },
    fetchPostedJobs() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: POSTED_JOBS,
          variables: {
            employeeId: vm.loginEmployeeId,
            isDropDownCall: 0,
            searchString: "",
            formId: 15,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listJobPost) {
            this.jobPostData = [];
            this.backUpJobPostData = response.data.listJobPost.JobpostDetails;
            this.jobPostData = this.backUpJobPostData;
            this.backupFilterData = this.backUpJobPostData;
            this.jobPostData.forEach((item) => {
              if (item.Closing_Date != "0000-00-00") {
                const closedDate = moment(item.Closing_Date);
                const currentDate = moment();
                const differenceInDaysForClosedJobs = currentDate.diff(
                  closedDate,
                  "days"
                );
                item.Closed_Before = differenceInDaysForClosedJobs;
              }
              if (item.Posting_Date != "0000-00-00") {
                const postingDate = moment(item.Posting_Date);
                const currentDate = moment();
                // Calculate the difference in days
                const differenceInDaysForPostedJobs = currentDate.diff(
                  postingDate,
                  "days"
                );
                // Add the data to the object
                item.Ageing = differenceInDaysForPostedJobs;
              } else {
                item.Ageing = "-";
              }
            });
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job posts",
        isListError: false,
      });
    },
    getStatusClass(status) {
      if (status === "Open") {
        return "text-amber-darken-4";
      } else if (status === "Closed") {
        return "text-amber";
      } else if (status === "Shortlisted") {
        return "text-purple-darken-4";
      } else if (status === "Scheduled For Interview") {
        return "text-green";
      } else if (status === "Approved") {
        return "text-brown-darken-4";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    validateDeletionOfJobpost(item, warningicon) {
      this.deleteThisJobpost = item;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      let interviewStatus = this.deleteThisJobpost.Interview_Status;
      let status = this.deleteThisJobpost.Status_Id;
      if (status === 1 || status === 3) {
        this.openWarningPopUp(item, warningicon);
      } else if (interviewStatus === 1) {
        snackbarData.message =
          "Sorry deleting job posts in 'In-Progress' status is not permitted.";
      } else {
        snackbarData.message =
          "Sorry deleting job posts in other than 'Rejected or Waiting for Approval' status is not permitted.";
        this.showAlert(snackbarData);
      }
    },
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.deleteItem = null;
    },
    openWarningPopUp(item, warningicon) {
      if (item === null) {
        this.warningIconClass = warningicon;
        this.openWarningModal = true;
        return;
      } else {
        this.warningIconClass = warningicon;
        this.openWarningModal = true;
      }
    },
    deleteThisJob() {
      let vm = this;
      if (vm.deleteThisJobpost.Job_Post_Id) {
        vm.openWarningModal = false;
        vm.$apollo
          .mutate({
            mutation: DELETE_JOB_POST,
            variables: {
              jobPostId: vm.deleteThisJobpost.Job_Post_Id,
              employeeId: vm.loginEmployeeId,
            },
            client: "apolloClientA",
          })
          .then(() => {
            let snackbarData = {
              isOpen: true,
              message: "Job deleted successfully.",
              type: "success",
            };
            this.showAlert(snackbarData);
            this.refetchJObPostedList();
          })
          .catch((err) => {
            vm.handleDeleteJobPostError(err);
          });
      }
    },
    handleDeleteJobPostError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: " job post",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    refetchJObPostedList() {
      this.isErrorInList = false;
      this.fetchPostedJobs();
    },
    viewEmployeeJobPostDetails(index) {
      this.showPostedJobDetails = true;
      this.jobPostId = index;
      this.jobPostIdQueryParam = null;
      this.getEmployeeJobPostDetails(this.jobPostId.Job_Post_Id);
    },
    selectItem(item) {
      const itemId = item[Object.keys(item)[0]];
      if (this.selectedRowIndex === itemId) {
        // Unselect if already selected
        this.selectedRowIndex = null;
        this.selectedPositionRecord = null;
      } else {
        // Select the clicked row
        this.selectedRowIndex = itemId;
        this.selectedPositionRecord = item;
        this.retrieveJobDescription();
      }
    },
    submitFunctionality() {
      this.showSignInForm = false;
      this.loadAuthToken();
      if (this.irukkaAuthToken) {
        this.closeJobpost();
      }
    },
    async closeJobpost() {
      let vm = this;
      vm.isGetJobpostDetailsLoading = true;
      await vm.$apollo
        .mutate({
          mutation: CLOSE_JOB_POST,
          variables: {
            Job_Post_Id: vm.jobPostId,
            Status: 6,
          },
          client: "apolloClientAV",
        })
        .then(() => {
          vm.isGetJobpostDetailsLoading = false;
          // add functions to close the jobpost to all the plateforms
          let snackbarData = {
            isOpen: true,
            message: "Job closed successfully",
            type: "success",
          };
          vm.refetchJObPostedList();
          vm.showAlert(snackbarData);
        })
        .catch((err) => {
          vm.isGetJobpostDetailsLoading = false;
          vm.handleCloseJobPostError(err);
        });
    },
    handleCloseJobPostError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "closing",
          form: " job post",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeLinkedInJobPost() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_LINKEDIN_JOB_POST,
          client: "apolloClientAM",
          variables: {
            integrationId: this.intergrationId,
            jobPostId: parseInt(this.jobPostId),
          },
        })
        .then((res) => {
          if (res && res.data && res.data.deleteLinkedInJobPost) {
            vm.intergrationId = 0;
            vm.isLoading = false;
            let response = res?.data?.deleteLinkedInJobPost;
            this.linkedinError = response?.errorCode;
            if (response && !response.errorCode) {
              let snackbarData = {
                isOpen: true,
                message: `Linkedin Job Details deleted successfully.`,
                type: "success",
              };
              vm.showAlert(snackbarData);
            }
          } else {
            let snackbarData = {
              isOpen: true,
              message:
                "An error occurred while attempting to close the jobpost to linkedin. Please contact the platform administrator for assistance.",
              type: "warning",
            };
            this.showAlert(snackbarData);
          }
        })
        .catch((err) => {
          vm.handleDeleteJobDetailsError(err);
          vm.isLoading = false;
        });
    },
    handleDeleteJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "closing",
        form: "job details",
        isListError: false,
      });
    },
    validateCloseJobToIndeed() {
      this.accessToken = window.$cookies.get("indeedAccessToken");
      if (this.accessToken) {
        this.closeJobpostToIndeed(this.indeedSourceId, this.indeedIntegraionId);
      } else {
        this.getIndeedAuthTokens();
        this.closeJobpostToIndeed(this.indeedSourceId, this.indeedIntegraionId);
      }
    },
    closeJobpostToIndeed(indeedJobId, indeedIntegraionId) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: CLOSE_JOBPOST_TO_INDEED,
          variables: {
            sourcedPostingId: indeedJobId,
            integrationId: indeedIntegraionId,
          },
          client: "apolloClientAV",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.closeJobpostToIndeed &&
            res.data.closeJobpostToIndeed.results
          ) {
            let response = JSON.parse(res.data.closeJobpostToIndeed.results);
            this.indeedError = response?.errors;
            if (!response.errors) {
              let snackbarData = {
                isOpen: true,
                message: "Indeed job post has been closed successfully.",
                type: "success",
              };
              this.showAlert(snackbarData);
            } else {
              let snackbarData = {
                isOpen: true,
                message:
                  "An error occurred while attempting to close the jobpost to indeed. Please contact the platform administrator for assistance.",
                type: "warning",
              };
              this.showAlert(snackbarData);
            }
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleCloseJobDetailsError(err);
        });
    },
    handleCloseJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "closing",
        form: "job details",
        isListError: false,
      });
    },
    closeJobPostJobStreet() {
      let vm = this;
      vm.isGetJobpostDetailsLoading = true;
      vm.$apollo
        .query({
          query: CLOSE_SEEK_INTEGRATION,
          variables: {
            jobStreetId: this.jobStreetId,
          },
          client: "apolloClientAX",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.closeJobStreetJob) {
            let response = JSON.parse(
              JSON.stringify(res.data.closeJobStreetJob)
            );
            this.jobStreetError = response?.errorCode;
            if (!response.errorCode) {
              let snackbarData = {
                isOpen: true,
                message: "Job Street has been closed successfully.",
                type: "success",
              };
              this.showAlert(snackbarData);
            } else {
              let snackbarData = {
                isOpen: true,
                message:
                  "An error occurred while attempting to close the job street. Please contact the platform administrator for assistance.",
                type: "warning",
              };
              this.isTokenPassed = false;
              this.showAlert(snackbarData);
            }
            vm.isGetJobpostDetailsLoading = false;
          }
        })
        .catch((err) => {
          vm.isGetJobpostDetailsLoading = false;
          vm.handleCloseJobStreetError(err);
        });
    },
    handleCloseJobStreetError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "closing",
        form: "job details",
        isListError: false,
      });
    },
    async fetchIntegrationStatus() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 15,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length
          ) {
            this.getIntegrationStatus =
              response.data.jobBoardIntegrationStatus.getStatus;
            this.irukkaStatus = this.getStatusByType("irukka");
          }
        })
        .catch((err) => {
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "integration status",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    async cloneData(item) {
      await this.getEmployeeJobPostDetails(item.Job_Post_Id);
      this.isCloneForm = true;
      this.showOptionModal = true;
      this.showWorkFlow = "No";
      this.requestType = "New Position";
      this.positionCode = null;
      this.selectedPosition = null;
      this.retrieveWorkflowDetails();
      this.refetchList();
      this.jobPostId = item;
    },
    async getEmployeeJobPostDetails(jobPostId) {
      let vm = this;
      vm.isGetJobpostDetailsLoading = true;
      await vm.$apollo
        .query({
          query: GET_JOB_POST_DETAILS,
          variables: {
            jobPostId: jobPostId,
            employeeId: vm.loginEmployeeId,
            action: "view",
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveJobPost &&
            response.data.retrieveJobPost.jobPostData
          ) {
            this.wholeJobPostData = response.data.retrieveJobPost.jobPostData;
          }
          vm.isGetJobpostDetailsLoading = false;
        })
        .catch((err) => {
          vm.isGetJobpostDetailsLoading = false;
          this.handleGetEmployeeJobPostDetailsError(err);
        });
    },
    handleGetEmployeeJobPostDetailsError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "view",
          form: " job post",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    getStatusByType(integrationType) {
      // Find the object with the specified Integration_Type
      const integration = this.getIntegrationStatus.find(
        (item) =>
          item.Integration_Type.toLowerCase() === integrationType.toLowerCase()
      );

      // If the integration is found, return its Integration_Status
      if (integration) {
        return integration.Integration_Status;
      } else {
        return null;
      }
    },
    onActions(type, item) {
      if (type && type.toLowerCase() === "clone") {
        this.cloneData(item);
      } else if (type && type.toLowerCase() === "close") {
        this.validateUser(item);
      } else if (type && type.toLowerCase() === "delete") {
        this.validateDeletionOfJobpost(item, "fas fa-trash");
      } else if (type && type.toLowerCase() === "apply") {
        window.open(
          this.$store.getters.baseUrl +
            `v3/job-candidates?jobPostId=${item.Job_Post_Id}`,
          "_blank"
        );
      }
    },
    getMppIntegrationStatus() {
      let vm = this;
      vm.isGetJobpostDetailsLoading = true;
      vm.$apollo
        .mutate({
          mutation: RECRUITMENT_SETTINGS,
          client: "apolloClientA",
        })
        .then((response) => {
          vm.isGetJobpostDetailsLoading = false;
          const { recruitmentSetting } = response.data || {};
          if (recruitmentSetting && !recruitmentSetting.errorCode) {
            this.isMppIntegration =
              recruitmentSetting?.settingResult[0]?.MPP_Integration;
          } else {
            vm.handleGetMppIntegrationStatusError();
          }
        })
        .catch((err) => {
          vm.isGetJobpostDetailsLoading = false;
          vm.handleGetMppIntegrationStatusError(err);
        });
    },
    handleGetMppIntegrationStatusError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "MPP integration status",
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    retrievePositionList() {
      this.positionListLoading = true;
      this.$apollo
        .query({
          query: LIST_OF_POSITION_LIST,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            Form_Id: 15,
            conditions: [
              {
                key: "Org_Level",
                operator: "=",
                value: "GRP",
              },
            ],
          },
        })
        .then((response) => {
          const { jobTitleList } = response.data || {};
          if (jobTitleList && !jobTitleList.errorCode) {
            this.positionGroup = [
              {
                Pos_Name: "No Group",
                Pos_full_Name: "No Group",
                Originalpos_Id: "nogroup",
              },
            ].concat(jobTitleList.jobTitleResult);
          } else {
            vm.handleRetrieveError();
          }
          this.positionListLoading = false;
        })
        .catch((err) => {
          this.positionListLoading = false;
          this.positionGroup = [];
          this.handleRetrieveError(err);
        });
    },
    handleRetrieveError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "Job Post",
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    retrieveRecruitmentList() {
      this.newPositionListLoading = true;
      this.listData = [];
      this.mppSearchList = "";
      this.selectedRowIndex = null;
      this.selectedPositionRecord = null;
      let code = "";
      if (this.selectedSection) code = this.selectedSection.toString();
      else if (
        this.selectedDepartment &&
        this.selectedDepartment.toString().toLowerCase() !== "nodepartment"
      )
        code = this.selectedDepartment;
      else if (
        this.selectedDivision &&
        this.selectedDivision.toString().toLowerCase() !== "nodivision"
      )
        code = this.selectedDivision;
      else if (
        this.selectedPosition &&
        this.selectedPosition.toString().toLowerCase() !== "nogroup"
      )
        code = this.positionCode;
      else if (!this.selectedPosition) code = "";

      let apiVariables = {
        formId: 15,
        postionParentCode: String(code),
      };
      if (this.selectedPosition) {
        const group = this.positionGroup.find(
          (item) => item.Originalpos_Id == this.selectedPosition
        );
        apiVariables.groupFilter = {
          Org_Level: "GRP",
          code: String(
            this.selectedPosition == "nogroup" ? "0" : group?.Pos_Code
          ),
        };
      }
      if (this.selectedDivision)
        apiVariables.divisionFilter = {
          Org_Level: "DIV",
          code: String(
            this.selectedDivision == "nodivision" ? "0" : this.selectedDivision
          ),
        };
      if (this.selectedDepartment)
        apiVariables.departmentFilter = {
          Org_Level: "DEPT",
          code: String(
            this.selectedDepartment == "nodepartment"
              ? "0"
              : this.selectedDepartment
          ),
        };

      if (this.selectedSection)
        apiVariables.sectionFilter = {
          Org_Level: "SEC",
          code: String(
            this.selectedSection == "nosection" ? "0" : this.selectedSection
          ),
        };

      this.$apollo
        .query({
          query: LIST_RECRUITMENT_REQUEST,
          variables: apiVariables,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listReqruitmentRequest &&
            res.data.listReqruitmentRequest.reqruitmentRequestDetails &&
            res.data.listReqruitmentRequest.reqruitmentRequestDetails.length
          ) {
            this.recruitmentRequestList =
              res.data.listReqruitmentRequest.reqruitmentRequestDetails;
            if (this.requestType.toLowerCase() === "recruitment request") {
              if (
                this.selectedDivision &&
                this.selectedPosition &&
                this.selectedPosition === "nogroup"
              ) {
                this.listData = this.recruitmentRequestList;
              } else if (
                this.selectedPosition &&
                this.selectedPosition !== "nogroup"
              ) {
                this.listData = this.recruitmentRequestList;
              } else {
                this.listData = [];
              }
            }
            let posCode = res.data.listReqruitmentRequest.positionParentId;
            if (!this.selectedPosition) {
              const res = this.matchFound(parseInt(posCode));
              if (res) {
                this.selectedPosition = parseInt(posCode);
              } else {
                this.selectedPosition = "nogroup";
                this.selectedDivision = this.getPosCodeByOriginalposId(posCode);
              }
            }
          } else {
            this.recruitmentRequestList = [];
            this.originalList = [];
          }
        })
        .catch(() => {
          this.recruitmentRequestList = [];
          this.originalList = [];
        })
        .finally(() => {
          this.newPositionListLoading = false;
        });
    },
    retrieveNewPositionList() {
      this.newPositionListLoading = true;
      this.listData = [];
      this.mppSearchList = "";
      this.selectedRowIndex = null;
      this.selectedPositionRecord = null;
      let code = "";
      if (this.selectedSection) {
        code = this.selectedSection;
      } else if (
        this.selectedDepartment &&
        this.selectedDepartment.toString().toLowerCase() !== "nodepartment"
      ) {
        code = this.selectedDepartment;
      } else if (
        this.selectedDivision &&
        this.selectedDivision.toString().toLowerCase() !== "nodivision"
      ) {
        code = this.selectedDivision;
      } else if (
        this.selectedPosition &&
        this.selectedPosition.toString().toLowerCase() !== "nogroup"
      ) {
        code = this.positionCode;
      } else if (!this.selectedPosition) {
        code = "";
      }

      let apiVariables = {
        formId: 15,
        postionParentCode: String(code),
      };
      if (this.selectedPosition) {
        const group = this.positionGroup.find(
          (item) => item.Originalpos_Id == this.selectedPosition
        );
        apiVariables.groupFilter = {
          Org_Level: "GRP",
          code: String(
            this.selectedPosition == "nogroup" ? "0" : group?.Pos_Code
          ),
        };
      }
      if (this.selectedDivision)
        apiVariables.divisionFilter = {
          Org_Level: "DIV",
          code: String(
            this.selectedDivision == "nodivision" ? "0" : this.selectedDivision
          ),
        };

      if (this.selectedDepartment)
        apiVariables.departmentFilter = {
          Org_Level: "DEPT",
          code: String(
            this.selectedDepartment == "nodepartment"
              ? "0"
              : this.selectedDepartment
          ),
        };

      if (this.selectedSection)
        apiVariables.sectionFilter = {
          Org_Level: "SEC",
          code: String(
            this.selectedSection == "nosection" ? "0" : this.selectedSection
          ),
        };

      this.$apollo
        .query({
          query: NEW_POSITION_LIST,
          variables: apiVariables,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.newPositionListLoading = false;
          if (
            res &&
            res.data &&
            res.data.listNewPositionRequest &&
            res.data.listNewPositionRequest.openPositiontRequestDetails &&
            res.data.listNewPositionRequest.openPositiontRequestDetails.length
          ) {
            this.newPositionList =
              res.data.listNewPositionRequest.openPositiontRequestDetails;
            if (this.requestType.toLowerCase() === "new position") {
              if (
                this.selectedDivision &&
                this.selectedPosition &&
                this.selectedPosition === "nogroup"
              ) {
                this.listData = this.newPositionList;
              } else if (
                this.selectedPosition &&
                this.selectedPosition !== "nogroup"
              ) {
                this.listData = this.newPositionList;
              } else {
                this.listData = [];
              }
            }
            let posCode = res.data.listNewPositionRequest.positionParentId;
            if (!this.selectedPosition) {
              const res = this.matchFound(parseInt(posCode));
              if (res) {
                this.selectedPosition = parseInt(posCode);
              } else {
                this.selectedPosition = "nogroup";
                this.selectedDivision = this.getPosCodeByOriginalposId(posCode);
              }
            }
          } else {
            this.newPositionList = [];
            this.originalList = [];
          }
        })
        .catch(() => {
          this.newPositionListLoading = false;
          this.newPositionList = [];
          this.originalList = [];
        });
    },
    getPosCodeByOriginalposId(givenOriginalposId) {
      const matchingPosition = this.divisionList.find(
        (item) => item.Originalpos_Id === givenOriginalposId
      );
      return matchingPosition ? matchingPosition.Pos_Code : null;
    },
    refetchList() {
      if (this.requestType?.toLowerCase() === "recruitment request") {
        this.retrieveRecruitmentList();
      } else {
        this.retrieveNewPositionList();
      }
    },
    retrieveJobDescription() {
      let vm = this;
      vm.$apollo
        .query({
          query: RETRIEVE_POSITION_JOB_SUMMARY,
          client: "apolloClientAG",
          variables: {
            originalPositionId: String(
              vm.selectedPositionRecord?.Organization_Structure_Id
            ),
            formId: 15,
            source: "recruitment",
            mppPositionType: vm.requestType,
            positionRequestId: vm.selectedPositionRecord
              ? vm.requestType?.toLowerCase() === "recruitment request"
                ? vm.selectedPositionRecord.Recruitment_Id
                : vm.selectedPositionRecord.Position_Request_Id
              : null,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrievePositionJobSummary &&
            !response.data.retrievePositionJobSummary?.errorCode?.length
          ) {
            vm.jobDescriptionData = response.data.retrievePositionJobSummary
              .summary
              ? response.data.retrievePositionJobSummary.summary.Job_Description
              : "";
            vm.orgGroupCode = response.data.retrievePositionJobSummary.summary
              ? response.data.retrievePositionJobSummary.summary.Company_Id
              : null;
            vm.availableVacancies =
              response.data.retrievePositionJobSummary?.vacancyAvailable;
          } else {
            vm.handleRetrieveJobDescriptionError();
          }
        })
        .catch((error) => {
          vm.handleRetrieveJobDescriptionError(error);
        });
    },
    handleRetrieveJobDescriptionError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "job description details",
        isListError: false,
      });
    },
    updateGroup() {
      this.selectedDivision = null;
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedPosition) {
        if (this.selectedPosition.toString().toLowerCase() === "nogroup") {
          this.selectedPositionId = "";
        } else {
          this.selectedPositionId = this.selectedPosition;
        }
        this.retrieveCountGroupPosition("group");
      }
    },
    updateDivision() {
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedDivision) {
        this.retrieveCountGroupPosition("division");
      }
    },
    updateDepartment() {
      this.selectedSection = null;
      if (this.selectedDepartment) {
        this.retrieveCountGroupPosition("department");
      }
    },
    updateSection() {
      if (this.selectedSection) {
        this.retrieveCountGroupPosition();
      }
    },
    retrieveCountGroupPosition(type = "") {
      let groupId = "0";
      if (this.selectedSection) {
        groupId =
          this.sectionList.find((item) => item.Pos_Code == this.selectedSection)
            ?.Originalpos_Id || "";
      } else if (
        this.selectedDepartment &&
        this.selectedDepartment.toString().toLowerCase() != "nodepartment"
      ) {
        groupId =
          this.departmentList.find(
            (item) => item.Pos_Code == this.selectedDepartment
          )?.Originalpos_Id || "";
      } else if (
        this.selectedDivision &&
        this.selectedDivision.toString().toLowerCase() != "nodivision"
      ) {
        groupId =
          this.divisionList.find(
            (item) => item.Pos_Code == this.selectedDivision
          )?.Originalpos_Id || "";
      } else if (
        this.selectedPosition &&
        this.selectedPosition.toString().toLowerCase() !== "nogroup"
      ) {
        groupId = this.selectedPosition;
      } else if (!this.selectedPosition) {
        groupId = "";
      }
      this.divisionListLoading = true;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 15,
            postionParentId: String(groupId),
            limit: this.jobRequisitionLimitToCallAPI,
            offset: 0,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listDetailsBasedOnGroupCode) {
            if (res.data.listDetailsBasedOnGroupCode.positionDetails) {
              const tempData =
                res.data.listDetailsBasedOnGroupCode.positionDetails;
              if (type === "group")
                this.divisionList = tempData.divisionList || [];
              if (type === "division")
                this.departmentList = tempData.deptList || [];
              if (type === "department")
                this.sectionList = tempData.sectionList || [];
              if (
                !this.divisionList.some(
                  (item) => item.Pos_Code === "nodivision"
                )
              )
                this.divisionList.unshift({
                  Pos_Name: "No Division",
                  Pos_Code: "nodivision",
                  Pos_full_Name: "No Division",
                  Originalpos_Id: "",
                });

              if (
                !this.departmentList.some(
                  (item) => item.Pos_Code === "nodepartment"
                )
              )
                this.departmentList.unshift({
                  Pos_Name: "No Department",
                  Pos_Code: "nodepartment",
                  Pos_full_Name: "No Department",
                  Originalpos_Id: "",
                });
            } else {
              this.resetTempList();
            }
            let { totalRecords } = res.data.listDetailsBasedOnGroupCode;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalRecords / this.jobRequisitionLimitToCallAPI
              );
              for (let i = 1; i < this.totalApiCount; i++) {
                this.updateGroupPosition(i, groupId, type);
              }
              if (this.requestType?.toLowerCase() === "recruitment request") {
                this.retrieveRecruitmentList();
              } else {
                this.retrieveNewPositionList();
              }
            }
            this.divisionListLoading = false;
          } else {
            this.divisionListLoading = false;
          }
        })
        .catch((err) => {
          this.handleRetrieveError(err);
          this.resetTempList();
          this.divisionListLoading = false;
        });
    },
    updateGroupPosition(index = 1, groupId = "", type = "") {
      this.divisionListLoading = true;
      let apiOffset = parseInt(index) * this.jobRequisitionLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.isFormDirty = true;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 15,
            postionParentId: String(groupId),
            limit: this.jobRequisitionLimitToCallAPI,
            offset: apiOffset,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listDetailsBasedOnGroupCode &&
            res.data.listDetailsBasedOnGroupCode.positionDetails
          ) {
            const tempData =
              res.data.listDetailsBasedOnGroupCode.positionDetails;
            if (
              tempData.divisionList &&
              tempData.divisionList.length > 0 &&
              type === "group"
            ) {
              this.divisionList = [
                ...this.divisionList,
                ...tempData.divisionList,
              ];
            }
            if (
              tempData.deptList &&
              tempData.deptList.length > 0 &&
              type === "division"
            ) {
              this.departmentList = [
                ...this.departmentList,
                ...tempData.deptList,
              ];
            }
            if (
              tempData.sectionList &&
              tempData.sectionList.length > 0 &&
              type === "department"
            ) {
              this.sectionList = [...this.sectionList, ...tempData.sectionList];
            }

            this.apiCallCount = this.apiCallCount + 1;
            if (this.totalApiCount === this.apiCallCount) {
              this.divisionListLoading = false;
            }
          } else {
            this.divisionListLoading = false;
            this.resetTempList();
          }
        })
        .catch((err) => {
          this.handleRetrieveError(err);
          this.resetTempList();
          this.divisionListLoading = false;
        });
    },
    resetTempList() {
      this.divisionList = [];
      this.departmentList = [];
      this.sectionList = [];
    },
    matchFound(posCode) {
      const matchFound = this.positionGroup.some(
        (item) => item.Originalpos_Id === posCode
      );
      return matchFound;
    },
    async getCustomGroupCoverage() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_CUSTOM_GROUP_COVERAGE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.recruitmentSetting &&
            response.data.recruitmentSetting.settingResult &&
            response.data.recruitmentSetting.settingResult.length
          ) {
            this.showJobRoundsTab =
              response.data.recruitmentSetting.settingResult[0].Show_Job_Rounds_Tab;
          }
        })
        .catch((err) => {
          this.handleGetCustomGroupCoverageError(err);
        });
    },
  },
};
</script>
<style scoped>
.job-post-container {
  padding: 5em 2em 0em 3em;
}

table {
  border-collapse: collapse;
  width: 100%;
}

th {
  text-align: left;
  padding: 8px;
}

td {
  text-align: left;
  padding: 8px;
  background-color: #ffffff;
}

th:first-child {
  position: sticky;
  left: 0;
  border: 0px;
}

th:last-child {
  border: 0px;
}

thead th {
  position: sticky;
  top: 0;
  z-index: 2000;
}

@media screen and (max-width: 600px) {
  thead {
    display: contents !important;
  }
  thead th {
    position: relative;
  }
  th:first-child {
    position: relative;
  }
}

@media screen and (max-width: 805px) {
  .job-post-container {
    padding: 4em 1em 0em 1em;
  }
}

.selected-row {
  background-color: rgb(var(--v-theme-hover));
}

::v-deep.v-dialog > .v-overlay__content > .v-card {
  align-self: center !important;
}
</style>
