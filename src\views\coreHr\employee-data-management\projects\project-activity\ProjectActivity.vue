<template>
  <div>
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      :button-text="showRetryBtn ? 'Retry' : ''"
      @button-click="refetchData()"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="backupMainList.length === 0 && !showEditForm"
      key="no-results-screen"
    >
      <template #contentSlot>
        <div style="max-width: 80%" class="mx-auto">
          <v-row
            v-if="!isLoading"
            style="background: white"
            class="rounded-lg pa-5 mb-4"
            :class="isMobileView ? 'mt-n16' : ''"
          >
            <v-col cols="12">
              <NotesCard
                :notes="`The activities feature enables users to create and manage activities within a ${projectLabelSmallCase}. Users have the flexibility to assign a name to each activity, providing a clear and descriptive label for easy identification. Additionally, users can specify whether the activity is billable or non-billable, allowing for accurate tracking of ${projectLabelSmallCase}-related costs.`"
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
              <NotesCard
                :notes="`This functionality streamlines ${projectLabelSmallCase} workflows, enhances transparency in activity management, and ensures accurate financial reporting by capturing billable status for each ${projectLabelSmallCase} activity.`"
                backgroundColor="transparent"
                class="mb-2"
              ></NotesCard>
            </v-col>
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                v-if="formAccess.add"
                variant="elevated"
                class="ml-4 mt-1 primary"
                rounded="lg"
                :size="this.isMobileView ? 'small' : 'default'"
                @click="onAdd()"
              >
                <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                Add
              </v-btn>
              <v-btn
                rounded="lg"
                color="transparent"
                variant="flat"
                class="mt-1"
                :size="this.isMobileView ? 'small' : 'default'"
                @click="refetchData()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else-if="isImportModel">
      <ActivitiesImport
        @close-import-model="closeImportModel()"
        @refetch-data="refetchData()"
        :backupMainList="backupMainList"
      ></ActivitiesImport>
    </div>
    <div v-else class="pt-4">
      <v-row>
        <v-col :cols="isAddViewEditFormOpened && windowWidth >= 1264 ? 5 : 12">
          <ListProjectActivity
            :items="mainList"
            :backupMainList="backupMainList"
            :is-small-table="isAddViewEditFormOpened"
            :formAccess="formAccess"
            @open-view-form="onOpenViewForm($event)"
            @refetch-data="refetchData()"
            @open-add-form="onAdd()"
            @delete="deleteRecord($event)"
            @reset-search-filter="resetSearchFilter()"
            @open-import-model="openImportModel()"
          ></ListProjectActivity>
        </v-col>
        <v-col
          v-if="isAddViewEditFormOpened && windowWidth >= 1264"
          :cols="backupMainList.length === 0 ? 12 : 7"
        >
          <ViewProjectActivity
            v-if="showViewForm"
            @open-edit-form="openEditForm()"
            @close-form="closeAllForms"
            :selectedItem="selectedItem"
            :formAccess="formAccess"
          ></ViewProjectActivity>
          <AddEditProjectActivity
            v-else
            :selectedItem="selectedItem"
            :isEdit="isEdit"
            @close-form="closeAllForms"
            @refetch-data="refetchData()"
          >
          </AddEditProjectActivity>
        </v-col>
      </v-row>
    </div>

    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 primary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <ViewProjectActivity
        v-if="showViewForm"
        @open-edit-form="openEditForm()"
        @close-form="closeAllForms()"
        :selectedItem="selectedItem"
        :formAccess="formAccess"
      />
      <AddEditProjectActivity
        v-if="showEditForm"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        @close-form="closeAllForms()"
        @refetch-data="refetchData()"
      />
    </v-dialog>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const AddEditProjectActivity = defineAsyncComponent(() =>
  import("./AddEditProjectActivity.vue")
);
const ActivitiesImport = defineAsyncComponent(() =>
  import("./activity-import/ActivitiesImport.vue")
);
// components
import ViewProjectActivity from "./ViewProjectActivity.vue";
import ListProjectActivity from "./ListProjectActivity.vue";
// Queries
import {
  LIST_PROJECT_ACTIVITIES,
  DELETE_ACTIVITY,
} from "@/graphql/corehr/projectActivityQueries.js";
export default {
  name: "ProjectActivity",
  components: {
    ViewProjectActivity,
    NotesCard,
    ListProjectActivity,
    AddEditProjectActivity,
    ActivitiesImport,
  },
  props: {
    formAccess: {
      type: Object,
      required: true,
    },
    mainList: {
      type: Array,
      default: () => [],
      required: true,
    },
    backupMainList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  emits: ["reset-search-filter", "assign-data"],
  data() {
    return {
      isLoading: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      isEdit: false,
      showViewForm: false,
      selectedItem: {},
      showEditForm: false,
      validationMessages: [],
      showValidationAlert: false,
      isImportModel: false,
      mainData: [],
      backupMainData: [],
    };
  },
  computed: {
    projectLabelSmallCase() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel ? pLabel.toLowerCase() : pLabel;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isAddViewEditFormOpened() {
      return this.showViewForm || this.showEditForm;
    },
    openFormInModal() {
      if (this.isAddViewEditFormOpened && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
  },

  mounted() {
    this.fetchData();
  },
  methods: {
    openImportModel() {
      this.isImportModel = true;
    },
    closeImportModel() {
      this.isImportModel = false;
    },
    resetSearchFilter() {
      this.$emit("reset-search-filter");
    },
    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showEditForm = true;
    },
    closeAllForms() {
      this.isEdit = false;
      this.showEditForm = false;
      this.showViewForm = false;
    },
    onAdd() {
      this.selectedItem = null;
      this.showEditForm = true;
      this.isEdit = false;
    },
    refetchData() {
      this.errorContent = "";
      this.isErrorInList = false;
      this.resetSearchFilter();
      this.closeAllForms();
      this.fetchData();
    },
    onOpenViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    fetchData() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_PROJECT_ACTIVITIES,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listProjectActivities) {
            let mainData = response.data.listProjectActivities.activities;
            vm.mainData = mainData;
            vm.backupMainData = mainData;
            this.$emit("assign-data", [vm.mainData, vm.backupMainData]);
            vm.listLoading = false;
          } else {
            vm.handleListError((err = ""), "activities");
          }
        })
        .catch((err) => {
          vm.handleListError(err, "activities");
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    //function to delete  record
    deleteRecord(deleteItem) {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_ACTIVITY,
            variables: {
              activityId: deleteItem.activityId
                ? parseInt(deleteItem.activityId)
                : 0,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Activity deleted successfully.",
            };
            vm.showAlert(snackbarData);
            vm.refetchData();
          })
          .catch((error) => {
            vm.handleDeleteError(error);
          });
      } catch {
        vm.handleDeleteError();
      }
    },
    handleDeleteError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: "activity",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
