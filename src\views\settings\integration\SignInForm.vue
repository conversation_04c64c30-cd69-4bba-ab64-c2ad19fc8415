<template>
  <v-card :style="isMobileView ? '' : 'width:500px'" class="rounded-lg h-100">
    <div class="pa-4 bg-primary d-flex">
      <p class="font-weight-bold">Irukka Integration</p>
      <v-icon class="pr-4 font-weight-bold ml-auto" @click="this.$emit('close')"
        >fas fa-times
      </v-icon>
    </div>
    <v-form class="ma-4" @submit.prevent="onVerifyOtp">
      <VueTelInput
        class="pa-2"
        v-model="mobileNumber"
        :preferred-countries="['IN', 'US', 'AU']"
        :error="!mobileNumberValidation"
        error-color="#E53935"
        valid-color="#9E9E9E"
        :defaultCountry="mobileNoCountryCode"
        :autoDefaultCountry="false"
        @on-input="updateMobileNumber"
        @country-changed="getCountryCode($event)"
        :valid-characters-only="true"
      ></VueTelInput>

      <span
        style="
          color: rgb(var(--v-theme-error));
          font-size: 12px;
          padding-left: 14px;
        "
        >{{ mobileNumberValidation }}</span
      >
      <v-text-field
        class="my-4"
        v-model="otp"
        :append-inner-icon="show ? 'fas fa-eye' : 'fas fa-eye-slash'"
        :type="show ? 'text' : 'password'"
        name="input-10-1"
        label="OTP"
        :error-messages="otpErrorMsg ? otpErrorMsg : ''"
        counter
        @click:append-inner="show = !show"
        @update:model-value="otpErrorMsg = ''"
        :disabled="isClickCheck"
        variant="outlined"
      ></v-text-field>
      <span
        v-if="!isClicked || counter == '0:00'"
        @click="wrapperMethod"
        class="hand-cursor mt-4 mr-4 text-subtitle-1 text-primary"
        >Generate OTP</span
      >
      <span v-if="isClicked && counter != '0:00'">
        <v-icon size="x-large" class="mr-3 primary">fas fa-stopwatch</v-icon>
        OTP will expire in {{ counter }}
      </span>

      <div class="my-4 d-flex justify-center">
        <v-btn variant="elevated" class="px-7 primary" type="submit"
          >Verify</v-btn
        >
      </div>
    </v-form>
  </v-card>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
import { VueTelInput } from "vue-tel-input";
import AWSCognitoIdentity from "../../../../src/irukka-cognito-verification/awsConfig/AWSCognitoIdentity";
const awsCognito = new AWSCognitoIdentity();
import axios from "axios";
//importing config file
import Config from "../../../config.js";
import { GET_COMPANY_AND_USER_DETAILS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
export default {
  emits: ["close", "change-status"],
  components: { VueTelInput },
  data() {
    return {
      showSignUp: false,
      otp: "",
      mobileNumber: null,
      mobileNoCountryCode: 0,
      originalMobileNoCountryCode: "+91",
      mobileNumberCountrycode: null,
      isClicked: false,
      counter: "3:00",
      validationMessages: [],
      showValidationAlert: false,
      show: false,
      otpErrorMsg: "",
      irukkaToken: "",
      parentComponent: null,
    };
  },
  props: {
    formType: {
      type: String,
      required: true,
    },
  },
  computed: {
    mobileNumberValidation() {
      if (!this.mobileNumber || !this.mobileNoCountryCode) {
        return "Mobile number is required";
      } else if (this.mobileNumber && this.mobileNumberProps) {
        return this.mobileNumberProps.valid ||
          this.mobileNumberProps.valid === undefined
          ? ""
          : "Please provide a valid mobile number";
      }
      return "";
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isClickCheck() {
      if (this.isClicked) {
        return false;
      } else {
        return true;
      }
    },
  },
  mounted() {
    this.parentComponent = this.formType;
    this.getUserMobileNumber();
    if (this.mobileNumberCountrycode) {
      let cCode = this.mobileNumberCountrycode;
      this.mobileNoCountryCode =
        cCode && cCode.length() > 0 ? parseInt(cCode) : "";
    }
  },
  methods: {
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getCountryCode(mobileNoCountryCode) {
      if (mobileNoCountryCode) {
        this.mobileNoCountryCode = parseInt(mobileNoCountryCode.dialCode);
        return this.mobileNoCountryCode;
      } else {
        return "";
      }
    },
    wrapperMethod() {
      this.sendOTP();
      this.handleSendOtpButtonClick();
    },
    sendOTP() {
      this.startOtpTimer();
      this.isClicked = true;
    },
    startOtpTimer() {
      this.otpExpiryTime = Math.floor(Date.now() / 1000) + 180; // Set OTP expiry time to current time + 180 seconds (3 minutes)

      const timerInterval = setInterval(() => {
        const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
        const remainingSeconds = this.otpExpiryTime - currentTime;
        this.remainingTime = Math.max(0, remainingSeconds);

        // Calculate minutes and seconds
        const minutes = Math.floor(this.remainingTime / 60);
        const seconds = this.remainingTime % 60;

        // Update counter in the format MM:SS
        this.counter = `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;

        if (this.remainingTime === 0) {
          this.counter = "0:00";
          clearInterval(timerInterval); // Stop the timer when the remaining time reaches 0
        }
      }, 1000); // Update the remaining time every second
    },

    async getUserMobileNumber() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_COMPANY_AND_USER_DETAILS,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getCompanySignUpDetails) {
            this.mobileNumber =
              response.data.getCompanySignUpDetails.getPersonalDetails[0].Mobile_Number;
            // Split the string by spaces and take the first part
            this.mobileNumberCountrycode = this.mobileNumber.split(" ")[0];
          }
        })
        .catch((err) => {
          vm.handleGetUserMobileNumberDetailError(err);
        });
    },
    handleGetUserMobileNumberDetailError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "recruitment",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    updateMobileNumber(payload, payload2) {
      this.mobileNumberProps = payload2;
      if (payload2 && Object.keys(payload2).length > 0) {
        this.mobileNumber =
          payload.length >= 3 ? payload2.nationalNumber : payload;
        this.mobileNoCountryCode = "+" + payload2.countryCallingCode;
      }
    },
    async verifyCompany(refreshToken) {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "",
      };
      try {
        const url = Config.irukkaUrl + "/verify/company";
        const headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.irukkaToken,
        };

        const response = await axios.get(url, { headers });
        this.errorMessage = response.data;
        if (this.errorMessage.payload.errorHandlerId == 5001) {
          this.$router.push("/settings/integration/recruitment");
        } else {
          (snackbarData.message = "Company Verification Done Successfully."),
            (snackbarData.type = "success"),
            this.showAlert(snackbarData);
          window.$cookies.set("irukkaAuthToken", this.irukkaToken, "59MIN");
          window.$cookies.set("irukkaRefreshToken", refreshToken, "59MIN");
          this.$emit("change-status", this.irukkaToken);
          this.showSignUp = false;
          this.status = true;
        }
      } catch (error) {
        if (error.response.status && error.response.status == 417) {
          // saving these details so that user can fill the company signUp form
          window.$cookies.set("irukkaAuthToken", this.irukkaToken, "59MIN");
          window.$cookies.set("irukkaRefreshToken", refreshToken, "59MIN");
          this.$router.push("/settings/integration/recruitment");
        } else {
          snackbarData.message =
            "Oops! something went wrong. Please try after some time.";
          snackbarData.type = "error";
          this.showAlert(snackbarData);
          this.otp = "";
        }
      }
    },
    // handeling OTP generation logic
    handleSendOtpButtonClick() {
      this.isSendOtpPressed = !this.isSendOtpPressed;
      this.handleSendOtpButton();
    },
    handleSendOtpButton() {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "",
      };
      awsCognito.signIn(
        `+91${this.mobileNumber}`,
        (res) => {
          snackbarData.type = "warning";
          snackbarData.message = "SignIn Called" + res;
          this.showAlert(snackbarData);
        },
        (error) => {
          snackbarData.type = "error";
          snackbarData.message = "You are not registered with Irukka !";
          this.showAlert(snackbarData);
          if (
            error.message &&
            error.message.includes("Incorrect username or password")
          ) {
            awsCognito.signUp(
              `+91${this.mobileNumber}`,
              () => {
                //setIsNewUser(true);
                setTimeout(() => {
                  snackbarData.type = "success";
                  snackbarData.message =
                    "Registering new user, Please fill up the OTP";
                  this.showAlert(snackbarData);
                }, 2000);
                setTimeout(() => {
                  awsCognito.signIn(
                    `+91${this.mobileNumber}`,
                    (signInResponse) => {
                      let response = signInResponse;
                      alert(response);
                    },
                    (signInError) => {
                      snackbarData.type = "error";
                      snackbarData.message =
                        "Sign In Error" + " " + JSON.stringify(signInError);
                      this.showAlert(snackbarData);
                    }
                  );
                }, 700);
              },
              (error) => {
                snackbarData.type = "error";
                snackbarData.message = "Post Signup Error" + " " + error;
                this.showAlert(snackbarData);
              }
            );
          }
        }
      );
    },
    onVerifyOtp() {
      // this.otp = otp;
      if (this.otp) {
        awsCognito.verify(
          this.otp,
          () => {
            let jwtToken = awsCognito.getAccessJwtToken();
            let refreshToken = awsCognito.refreshToken();
            // dispatch(setLoginType(Constants.loginType.cognito));
            this.irukkaToken = jwtToken;
            if (!this.parentComponent) {
              this.verifyCompany(refreshToken.token);
            } else {
              window.$cookies.set("irukkaAuthToken", this.irukkaToken, "59MIN");
              window.$cookies.set(
                "irukkaRefreshToken",
                refreshToken.token,
                "59MIN"
              );
              this.$emit("change-status", this.irukkaToken);
            }
          },
          //OTP mismatch error
          () => {
            this.otpErrorMsg = "Wrong OTP entered.";
          }
        );
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please enter the OTP first",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
  },
};
</script>
<style>
.hand-cursor {
  cursor: pointer;
}
</style>
