<template>
  <div>
    <v-card color="primary" elevation="0" class="pa-4 text-center">
      <v-row class="d-flex justify-center align-center">
        <v-col
          cols="12"
          sm="4"
          xs="12"
          class="text-h5"
          :class="windowWidth > 600 ? 'text-left' : 'text-center'"
        >
          <div>{{ orgName }}</div>
        </v-col>
        <v-col cols="12" sm="4" xs="12" class="text-center text-h5"></v-col>
        <v-col cols="12" sm="4" xs="12" class="d-flex justify-end">
          <div class="d-flex justify-end align-center">
            <v-menu
              :open-on-hover="false"
              :close-on-content-click="false"
              :open-on-click="true"
              v-model="menuOpen"
            >
              <template v-slot:activator="{ props }">
                <v-icon v-bind="props" size="20" class="fas fa-user" />
              </template>
              <v-list>
                <v-list-item
                  v-for="(item, index) in menuItems"
                  :key="index"
                  @click="handleMenuClick(item.action)"
                >
                  <v-list-item-title
                    ><span class="mr-2"
                      ><v-icon
                        :class="item.icon"
                        color="primary"
                        size="15" /></span
                    >{{ item.title }}</v-list-item-title
                  >
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </v-col>
      </v-row>
    </v-card>
    <div
      :class="windowWidth <= 600 ? 'pa-4' : 'pa-12'"
      :style="windowWidth <= 600 ? 'padding-bottom: 70px' : ''"
    >
      <div v-if="listLoading || isApplicationLoading">
        <v-skeleton-loader
          ref="skeleton1"
          type="table-heading"
          class="mx-auto"
        ></v-skeleton-loader>
        <div v-for="i in 4" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <div v-else>
        <!-- Render dynamic content -->
        <div v-if="selectedMenu === 'profile'">
          <EditJobCandidateDetails
            :candidateDetails="editedCandidateDetails"
            :candidateIdSelected="candidateId"
            :isPortalLogedIn="true"
            :presentSimpleForm="profileisSimpleForm"
            @edit-updated="retrieveJobCandidateDetails(candidateId)"
            :showJobHeader="false"
          />
        </div>
        <!-- Application Status -->
        <div v-else-if="selectedMenu?.toLowerCase() === 'applicationstatus'">
          <h2 class="text-primary ml-2 mb-4">Application Status</h2>
          <div v-if="applicationStatusList?.length">
            <v-row>
              <v-col
                cols="12"
                sm="6"
                md="4"
                v-for="(data, index) in applicationStatusList"
                :key="index"
              >
                <v-card
                  elevation="3"
                  class="card-item d-flex pa-4 rounded-lg"
                  color="grey-lighten-5"
                >
                  <v-row>
                    <v-col cols="12" class="pa-0 pl-4 d-flex">
                      <v-tooltip :text="data.jobPostName" location="bottom">
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-primary font-weight-bold text-subtitle-1 text-truncate"
                            :style="
                              isMobileView
                                ? 'max-width: 180px'
                                : 'max-width: 350px'
                            "
                            v-bind="data.jobPostName ? props : ''"
                          >
                            <span class="text-wrap">
                              {{ checkNullValue(data.jobPostName) }}</span
                            >
                          </div>
                        </template>
                      </v-tooltip>
                      <span
                        class="ml-4 font-weight-bold text-subtitle-1"
                        style="margin-left: auto !important"
                        :class="
                          data.jobPostStatus?.toLowerCase() === 'open'
                            ? 'text-green'
                            : 'text-red'
                        "
                        >{{ data.jobPostStatus }}</span
                      >
                    </v-col>
                    <v-col cols="12" sm="6" class="pa-0 pl-4">
                      <div
                        class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
                      >
                        <b class="mr-2 text-grey justify-start"> Status </b>
                        {{ checkNullValue(data.status) }}
                      </div>
                    </v-col>
                    <v-col cols="12" sm="6" class="pa-0 pl-4">
                      <div
                        class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
                      >
                        <b class="mr-2 text-grey justify-start">
                          Last Updated
                        </b>
                        {{ formatDate(data.lastUpdated) }}
                      </div></v-col
                    >
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </div>
          <p v-else>Currently you have not applied for any jobs!</p>
        </div>
        <!-- View Jobs -->
        <div v-else-if="selectedMenu?.toLowerCase() === 'viewjobs'">
          <v-row v-if="!jobPostList?.length">
            <div class="pa-5 d-flex justify-center align-center">
              Currently there are no open jobs available from the organization!
            </div>
          </v-row>
          <v-row v-else>
            <v-col cols="12">
              <!-- Listing the Job Posts -->
              <div v-if="showCareerPageDetails">
                <div>
                  <v-card
                    color="primary"
                    elevation="0"
                    class="pa-4 text-center"
                  >
                    <v-row class="d-flex justify-center align-center">
                      <v-col
                        cols="12"
                        sm="4"
                        xs="12"
                        class="text-h5"
                        :class="windowWidth > 600 ? 'text-left' : 'text-center'"
                      >
                        <div>
                          <v-icon
                            color="white"
                            class="fas fa-angle-left cursor-pointer"
                            @click="showCareerPageDetails = false"
                          />
                        </div>
                      </v-col>
                      <v-col
                        cols="12"
                        sm="4"
                        xs="12"
                        class="text-center text-h5"
                      >
                        <div class="text-h5 text-center">
                          {{ checkNullValue(wholeJobPostData.Job_Post_Name) }}
                        </div>
                        <div
                          class="d-flex mt-2 justify-center align-center text-white"
                        >
                          <span v-if="wholeJobPostData.City_Name">{{
                            wholeJobPostData.City_Name
                          }}</span>
                          <div
                            v-if="
                              wholeJobPostData.City_Name &&
                              wholeJobPostData.Experience_Level
                            "
                            style="
                              background: white;
                              border-radius: 50%;
                              margin: 0px 5px;
                              height: 10px;
                              width: 10px;
                            "
                          ></div>
                          <span v-if="wholeJobPostData.Experience_Level">{{
                            wholeJobPostData.Experience_Level
                          }}</span>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="4" xs="12"></v-col>
                    </v-row>
                  </v-card>
                </div>
                <!-- Job Description -->
                <v-row>
                  <v-col cols="12" class="d-flex justify-end">
                    <div
                      class="text-green d-flex justify-center align-center already-applied mt-2"
                      v-if="disabledCheckbox(this.wholeJobPostData.Job_Post_Id)"
                      style=""
                    >
                      Already Applied
                    </div>
                    <v-btn
                      v-if="
                        enableMultipleJobApply === 'No' &&
                        !disabledCheckbox(this.wholeJobPostData.Job_Post_Id)
                      "
                      class="align-right mt-2"
                      rounded="lg"
                      color="primary"
                      size="large"
                      @click="applyBulkJobPosts('single')"
                      >Apply for this job</v-btn
                    >
                  </v-col>
                  <v-col
                    cols="12"
                    :style="
                      windowWidth <= 768
                        ? 'padding: 5px 5% 5px 5%'
                        : 'padding: 5px 20% 5px 20%'
                    "
                  >
                    <div style="font-size: 20px" class="mt-10 text-body-1">
                      <div
                        v-if="!sanitizedJobDescription"
                        class="pa-5 d-flex bg-hover justify-center align-center"
                        style="margin: 2% 0 10px"
                      >
                        No job description for this job post
                      </div>
                      <div
                        v-else
                        ref="editorView"
                        class="quill-editorView"
                      ></div>
                    </div>
                  </v-col>
                </v-row>
              </div>
              <!-- Job Listings -->
              <div v-else class="pa-4">
                <!-- Search and Filter Section -->
                <v-row class="mb-4">
                  <v-col cols="12" md="8">
                    <v-text-field
                      v-model="searchInput"
                      label="Search jobs"
                      prepend-inner-icon="fas fa-search"
                      variant="outlined"
                      density="compact"
                      hide-details
                      class="rounded-lg"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="4" class="d-flex align-center">
                    <v-btn
                      variant="text"
                      prepend-icon="fas fa-filter"
                      @click="showFilters = !showFilters"
                      class="ml-auto"
                    >
                      Filters
                    </v-btn>
                  </v-col>
                </v-row>

                <!-- Filters Section (Collapsible) -->
                <v-expand-transition>
                  <div v-if="showFilters" class="mb-4">
                    <v-card class="pa-4">
                      <v-row>
                        <v-col cols="12" sm="6" md="4">
                          <v-select
                            v-model="selectedDepartment"
                            :items="departmentList"
                            label="Department"
                            multiple
                            chips
                            variant="outlined"
                            density="compact"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" sm="6" md="4">
                          <v-select
                            v-model="selectedLocation"
                            :items="locationList"
                            label="Location"
                            multiple
                            chips
                            variant="outlined"
                            density="compact"
                          ></v-select>
                        </v-col>
                        <v-col cols="12" sm="6" md="4">
                          <v-select
                            v-model="selectedServiceProvider"
                            :items="serviceProviderList"
                            label="Service Provider"
                            multiple
                            chips
                            variant="outlined"
                            density="compact"
                          ></v-select>
                        </v-col>
                      </v-row>
                    </v-card>
                  </div>
                </v-expand-transition>

                <v-row>
                  <v-col v-if="filteredDepartments?.length" cols="12">
                    <div
                      v-for="(department, index) in filteredDepartments"
                      :key="index"
                    >
                      <div
                        v-if="filtersVisible?.department"
                        class="ma-4 d-flex align-center"
                      >
                        <span
                          class="text-primary pr-2 text-body-1 font-weight-bold"
                          >{{ department.departmentName }}</span
                        >
                        -
                        <span class="bg-hover pa-1 text-body-2 rounded-lg ml-2"
                          >{{ department.jobs.length }} jobs</span
                        >
                      </div>
                      <v-row>
                        <v-col
                          v-for="job in department.jobs"
                          :key="job.Job_Post_Id"
                          cols="12"
                          sm="6"
                          md="4"
                          class="job-card-container"
                        >
                          <v-card
                            class="cursor-pointer job-card h-100"
                            @click="navigateToCareersPageDetails(job)"
                          >
                            <div class="pa-4">
                              <div
                                class="d-flex align-center justify-space-between mb-2"
                              >
                                <v-checkbox
                                  v-if="enableMultipleJobApply === 'Yes'"
                                  v-model="job.isSelected"
                                  density="compact"
                                  color="primary"
                                  :disabled="disabledCheckbox(job.Job_Post_Id)"
                                  false-icon="far fa-circle"
                                  true-icon="fas fa-check-circle"
                                  hide-details
                                  @click.stop
                                ></v-checkbox>
                                <div
                                  class="text-subtitle-1 font-weight-bold text-primary"
                                >
                                  {{ job.Job_Post_Name }}
                                </div>
                              </div>

                              <div
                                class="text-grey text-body-2 d-flex align-center mb-2"
                              >
                                <v-icon size="small" class="mr-1"
                                  >fas fa-map-marker-alt</v-icon
                                >
                                {{ job.City_Name || "Location not specified" }}
                              </div>

                              <div
                                class="text-grey text-body-2 d-flex align-center mb-2"
                              >
                                <v-icon size="small" class="mr-1"
                                  >fas fa-briefcase</v-icon
                                >
                                {{
                                  job.Experience_Level ||
                                  "Experience not specified"
                                }}
                              </div>

                              <div
                                class="text-grey text-caption d-flex align-center"
                              >
                                <v-icon size="x-small" class="mr-1"
                                  >fas fa-clock</v-icon
                                >
                                Posted
                                {{ getPostingDateDifference(job.Posting_Date) }}
                                days ago
                              </div>

                              <div
                                v-if="disabledCheckbox(job.Job_Post_Id)"
                                class="mt-2 text-green text-caption font-weight-medium"
                              >
                                Already Applied
                              </div>
                            </div>
                          </v-card>
                        </v-col>
                      </v-row>
                    </div>
                  </v-col>
                </v-row>
              </div>
            </v-col>
          </v-row>
        </div>
      </div>
    </div>

    <v-card
      v-if="
        selectedMenu?.toLowerCase() === 'viewjobs' &&
        !showCareerPageDetails &&
        enableMultipleJobApply === 'Yes'
      "
      class="position-fixed bottom-0 w-100"
      style="z-index: 100"
    >
      <v-sheet
        class="align-center text-center pa-2"
        :class="windowWidth > 768 ? 'd-flex' : ''"
        style="min-width: 100%"
      >
        <v-row justify="center">
          <v-col cols="12" class="d-flex justify-end pr-4">
            <v-btn
              rounded="lg"
              size="small"
              color="primary"
              variant="elevated"
              class="mr-1"
              :disabled="!jobPostList.some((job) => job.isSelected === true)"
              style="height: 40px; margin-top: 10px"
              @click="applyBulkJobPosts()"
            >
              <span>Apply</span>
            </v-btn>
          </v-col>
        </v-row>
      </v-sheet>
    </v-card>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import DOMPurify from "dompurify";
import Cookies from "js-cookie";
import moment from "moment";
import { checkNullValue } from "@/helper";
import {
  RETRIEVE_JOB_CANDIDATE_DETAILS,
  RETRIVE_CANDIDATE_APPLICATION_STATUS,
  APPLY_CANDIDATE_MULTIPLE_JOBS,
} from "@/graphql/recruitment/recruitmentQueries.js";
import { CUSTOM_COLOR_PICKER } from "@/graphql/commonQueries.js";
import { GET_JOB_HEADER } from "@/graphql/recruitment/recruitmentQueries.js";
import { POSTED_JOBS } from "@/graphql/settings/irukka-integration/jobPostFormQueries.js";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
const EditJobCandidateDetails = defineAsyncComponent(() =>
  import(
    "../../recruitment/job-candidates/job-candidates-details/EditJobCandidateDetails.vue"
  )
);
export default {
  name: "CandidatePortal",
  components: { EditJobCandidateDetails },
  data() {
    return {
      listLoading: false,
      isApplicationLoading: false,
      headingName: "",
      menuOpen: true,
      menuItems: [
        { title: "My Profile", icon: "fas fa-user", action: "profile" },
        {
          title: "Application Status",
          icon: "fas fa-info-circle",
          action: "applicationStatus",
        },
        { title: "View Jobs", icon: "fas fa-list-ul", action: "viewJobs" },
        { title: "Sign out", icon: "fas fa-sign-out-alt", action: "signout" },
      ],
      selectedMenu: "applicationStatus",
      jobPostList: [],
      applicationStatusList: [],
      showCareerPageDetails: false,
      wholeJobPostData: null,
      editedCandidateDetails: null,
      candidateId: null,
      isLoading: false,
      enableMultipleJobApply: "Yes",
      searchInput: "",
      departmentList: [],
      locationList: [],
      serviceProviderList: [],
      selectedDepartment: [],
      selectedLocation: [],
      selectedServiceProvider: [],
      fieldForce: 0,
      orgName: "",
      showFilters: false, // Added for responsive filter toggle
    };
  },
  emits: ["signout-success"],
  props: {
    settingResult: {
      type: Object,
      default: null,
    },
  },
  computed: {
    filtersVisible() {
      let filters = this.settingResult?.Career_Portal_Filters
        ? JSON.parse(this.settingResult.Career_Portal_Filters)
        : {};
      return filters;
    },
    profileisSimpleForm() {
      return (
        this.settingResult?.Candidate_Application_Form_Type?.toLowerCase() ===
        "simple"
      );
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    headerName() {
      if (this.selectedMenu === "profile") return "My Profile";
      else if (this.selectedMenu?.toLowerCase() === "applicationstatus")
        return "Application Status";
      else return "View Jobs";
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    sanitizedJobDescription() {
      let jobDescription = this.wholeJobPostData?.Job_Description
        ? this.convertEmojiCodepointsToEmojis(
            this.wholeJobPostData.Job_Description
          )
        : "";
      return DOMPurify.sanitize(jobDescription);
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    filteredDepartments() {
      let filteredPosts = this.jobPostList;
      const departments = {};
      if (this.searchInput) {
        filteredPosts = filteredPosts.filter((job) =>
          job.Job_Post_Name.toLowerCase().includes(
            this.searchInput.toLowerCase()
          )
        );
      }
      if (this.selectedDepartment.length > 0) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedDepartment.includes(job.Department_Name)
        );
      }
      if (this.selectedLocation.length > 0) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedLocation.includes(job.Location)
        );
      }
      if (
        this.selectedServiceProvider &&
        this.selectedServiceProvider.length > 0
      ) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedServiceProvider.includes(job.Service_Provider_Name)
        );
      }
      filteredPosts.forEach((job) => {
        if (!departments[job.Department_Name]) {
          departments[job.Department_Name] = {
            departmentName: job.Department_Name,
            jobs: [],
          };
        }
        job.isSelected = false;
        departments[job.Department_Name].jobs.push(job);
      });

      return Object.values(departments);
    },
  },

  mounted() {
    const candId = Cookies.get("portalCandidateId");
    if (candId) {
      this.candidateId = parseInt(candId);
      this.retrieveJobCandidateDetails(candId);
      this.fetchApplicationStatus(candId);
    }
    this.checkSessionValidity();
    this.retrieveRecruitmentSetting();
    this.customColorPicker();
    const params = new URLSearchParams(window.location.search);
    const action = params.get("action");
    if (action) {
      this.handleMenuClick(action);
    }
  },
  methods: {
    checkNullValue,
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editorView, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.quill.root.innerHTML = this.wholeJobPostData?.Job_Description
        ? this.convertEmojiCodepointsToEmojis(
            this.wholeJobPostData.Job_Description
          )
        : "";
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
      });
      this.quill.enable(false);
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editorView.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    handleMenuClick(action) {
      this.selectedMenu = action;
      this.menuOpen = false;
      this.checkSessionValidity();
      if (this.selectedMenu?.toLowerCase() === "profile")
        this.retrieveJobCandidateDetails(this.candidateId);
      else if (this.selectedMenu?.toLowerCase() === "applicationstatus")
        this.fetchApplicationStatus(this.candidateId);
      else if (this.selectedMenu?.toLowerCase() === "viewjobs")
        this.fetchPostedJobs();
      else if (this.selectedMenu?.toLowerCase() === "signout")
        this.signoutCandidate();
    },
    checkSessionValidity() {
      setInterval(() => {
        const expiryTime = Cookies.get("portalCandidateExpiry");

        if (!expiryTime) {
          this.signoutCandidate();
        }
      }, 5000); // Check every 5 seconds
    },
    customColorPicker() {
      let vm = this;
      vm.$apollo
        .query({
          query: CUSTOM_COLOR_PICKER,
          client: "apolloClientAO",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.customColorPicker &&
            response.data.customColorPicker.colorResult &&
            response.data.customColorPicker.colorResult.length > 0
          ) {
            this.fieldForce =
              response.data.customColorPicker.colorResult[0].Field_Force;
            this.orgName =
              response.data.customColorPicker.colorResult[0]?.Org_Name || "";
          }
        })
        .catch(() => {
          this.fieldForce = 0;
        });
    },
    signoutCandidate() {
      Cookies.remove("portalCandidateId");
      this.candidateId = null;
      this.$emit("signout-success");
    },
    fetchApplicationStatus(candId = 0) {
      let vm = this;
      vm.isApplicationLoading = true;
      vm.$apollo
        .query({
          query: RETRIVE_CANDIDATE_APPLICATION_STATUS,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: parseInt(candId),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveCandidateApplicationStatus &&
            !response.data.retrieveCandidateApplicationStatus.errorCode
          ) {
            vm.applicationStatusList =
              response.data.retrieveCandidateApplicationStatus.applicationStatus;
          } else {
            vm.handleRetrieveError(
              response.data.retrieveCandidateApplicationStatus.errorCode
            );
          }
          vm.isApplicationLoading = false;
        })
        .catch((err) => {
          vm.isApplicationLoading = false;
          vm.handleRetrieveError(err);
        });
    },
    retrieveJobCandidateDetails(candId = 0) {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_JOB_CANDIDATE_DETAILS,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: parseInt(candId),
            action: "view",
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveJobCandidates &&
            !response.data.retrieveJobCandidates.errorCode
          ) {
            vm.editedCandidateDetails =
              response.data.retrieveJobCandidates.jobCandidateDetails;
          } else {
            vm.handleRetrieveError(
              response.data.retrieveJobCandidates.errorCode
            );
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleRetrieveError(err);
        });
    },
    handleRetrieveError(err = "") {
      this.listLoading = false;
      var snackbarData = {
        isOpen: true,
        type: "warning",
        message:
          err || "Something went wrong while retrieving job candidate details",
      };
      this.showAlert(snackbarData);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    disabledCheckbox(job_Post_Id) {
      return this.applicationStatusList.some(
        (app) => app.jobPostId === job_Post_Id
      );
    },
    navigateToCareersPageDetails(job) {
      this.wholeJobPostData = job;
      this.showCareerPageDetails = true;
      this.$nextTick(() => {
        this.initQuillEditor();
      });
    },
    closeAllForms(refetchCount) {
      this.showCareerPageDetails = false;
      if (refetchCount > 0) {
        this.fetchPostedJobs();
      }
    },
    applyBulkJobPosts(type) {
      let vm = this;
      vm.listLoading = true;
      let selectedJobPostIds = [];
      if (type === "single") {
        selectedJobPostIds = [vm.wholeJobPostData.Job_Post_Id];
      } else {
        selectedJobPostIds = vm.jobPostList
          .filter((job) => job.isSelected)
          .map((job) => job.Job_Post_Id);
      }
      vm.$apollo
        .mutate({
          mutation: APPLY_CANDIDATE_MULTIPLE_JOBS,
          client: "apolloClientAO",
          variables: {
            candidateId: parseInt(vm.candidateId),
            jobPostIds: selectedJobPostIds,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.applyCandidateMultipleJobs &&
            !response.data.applyCandidateMultipleJobs.errorCode
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: response.data.applyCandidateMultipleJobs.message,
            };
            vm.jobPostList.forEach((job) => (job.isSelected = false));
            vm.showAlert(snackbarData);
            vm.fetchApplicationStatus(vm.candidateId);
            vm.showCareerPageDetails = false;
          } else {
            vm.handleBulkApply(
              response.data.applyCandidateMultipleJobs.errorCode
            );
          }
          vm.listLoading = false;
        })
        .catch((error) => {
          vm.listLoading = false;
          vm.handleBulkApply(error);
        });
    },
    handleBulkApply(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "applyBulkJobPosts",
        form: "JobCandidatePortal",
        isListError: false,
      });
    },
    fetchPostedJobs() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: POSTED_JOBS,
          variables: {
            employeeId: vm.loginEmployeeId,
            isDropDownCall: 0,
            searchString: "",
          },
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobPost &&
            response.data.listJobPost.JobpostDetails
          ) {
            this.jobPostList = response.data.listJobPost.JobpostDetails.filter(
              (job) => job.Job_Post_Status === "Open"
            ).map((job) => ({
              ...job,
              Department_Name: job.Department_Name,
              Location: job.City_Name,
              Job_Type: job.Job_Type,
              Service_Provider_Name: job.Service_Provider_Name,
            }));
            this.departmentList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.Department_Name)
                  .filter((department) => department !== null)
              ),
            ];

            this.locationList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.City_Name)
                  .filter((location) => location !== null)
              ),
            ];

            this.serviceProviderList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.Service_Provider_Name)
                  .filter((provider) => provider !== null && provider !== 0)
              ),
            ];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "candidate portal",
        isListError: false,
      });
    },
    getPostingDateDifference(postingDate) {
      if (postingDate && moment(postingDate).isValid()) {
        const postingDateObj = new Date(postingDate);
        const currentDate = new Date();
        const differenceInTime =
          currentDate.getTime() - postingDateObj.getTime();
        const differenceInDays = differenceInTime / (1000 * 3600 * 24);
        return Math.floor(differenceInDays);
      } else {
        return "";
      }
    },
    retrieveRecruitmentSetting() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_JOB_HEADER,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.recruitmentSetting &&
            res.data.recruitmentSetting.settingResult &&
            res.data.recruitmentSetting.settingResult[0]
          ) {
            this.enableMultipleJobApply =
              res.data.recruitmentSetting.settingResult[0].Enable_Multiple_Application;
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
    convertEmojiCodepointsToEmojis(text) {
      return text.replace(/\[EMOJI:([0-9a-f-]+)\]/gi, (match, codePoints) => {
        // Split by dash if there are multiple code points
        const codePointArray = codePoints.split("-");

        // Convert each hex code point back to a character and join them
        const emoji = codePointArray
          .map((hex) => String.fromCodePoint(parseInt(hex, 16)))
          .join("");

        return emoji;
      });
    },
  },
};
</script>

<style scoped>
.job-card {
  border-radius: 8px;
  transition: border-color 0.3s, color 0.3s;
  border: 1px solid rgb(var(--v-theme-hover));
  height: 100%;
}

.job-card:hover,
.job-post-title:hover {
  border-color: rgb(var(--v-theme-primary));
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.job-card-container {
  margin-bottom: 10px;
}

.hover-icon {
  opacity: 0;
  transition: opacity 0.3s;
}

.job-card:hover .hover-icon {
  opacity: 1;
}
.already-applied {
  height: 50px;
  width: 150px;
  border: 1px solid green;
  border-radius: 5px;
}
.sticky-column {
  position: sticky;
  top: 10px;
  height: 100vh;
  overflow-y: auto;
}
.filterCard {
  width: 100%;
}
@media screen and (max-width: 960px) {
  .sticky-column {
    position: relative !important;
    top: auto;
    height: auto;
    overflow-y: visible;
    width: 100%;
  }
}
.quill-editorView {
  height: auto;
}
::v-deep .ql-toolbar.ql-snow {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
::v-deep .ql-editor {
  padding: 0px;
}
</style>
