<template>
  <div>
    <div v-if="roundsLoading">
      <div v-for="i in 5" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else-if="isErrorInList">
      <AppFetchErrorScreen
        image-name="common/common-error-image"
        :content="errorContent"
        icon-name="fas fa-redo-alt"
        button-text="Retry"
        :isSmallImage="true"
        @button-click="fetchRoundsList"
      >
      </AppFetchErrorScreen>
    </div>
    <div v-else class="content">
      <!-- Rounds section -->
      <v-container fluid>
        <h3>Rounds</h3>
        <v-row style="padding-top: 25px">
          <v-col cols="12" md="4" sm="12">
            <div class="dropDown">
              <CustomSelect
                :itemSelected="selectedValues"
                :items="availableRounds"
                label="Select Rounds"
                itemValue="Round_Id"
                itemTitle="Round_Name"
                :isAutoComplete="true"
                @selected-item="updateSelectValue($event, true)"
                :multiple="true"
                clearable
                :isLoading="roundsLoading"
                :disabled="!hasUpdateAccess"
                style="max-width: 100%"
              ></CustomSelect>
            </div>
          </v-col>
          <v-col cols="12" md="8" sm="12">
            <div
              v-for="round in selectedRoundsList"
              :key="round.Round_Id"
              class="cardDiv"
            >
              <v-card class="rounded-lg" elevation="2" style="padding: 3%">
                <v-row>
                  <v-col
                    cols="3"
                    style="padding-left: 10%"
                    class="d-flex align-center justify-center"
                  >
                    <v-avatar :color="roundColorMap[round.Round_Id]" size="30">
                      {{ getInitials(round.Round_Name) }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="7">
                    <h4>{{ round.Round_Name }}</h4>
                  </v-col>
                  <v-col
                    cols="2"
                    style="padding-right: 10%"
                    class="d-flex align-center justify-center"
                  >
                    <i
                      class="fas fa-trash fa-sm deleteIcon"
                      style="color: lightgrey"
                      @click="removeRound(round.Round_Id)"
                      v-if="hasUpdateAccess"
                    ></i>
                  </v-col>
                </v-row>
              </v-card>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <div>
      <v-bottom-navigation
        :disabled="!hasChanges || !hasUpdateAccess"
        class="bottom-nav"
      >
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
          style="width: 100%"
        >
          <v-row justify="center">
            <v-col cols="6" class="d-flex justify-start pl-2">
              <v-btn
                rounded="lg"
                variant="outlined"
                size="small"
                class="primary"
                density="compact"
                style="height: 40px; margin-top: 10px"
                @click="closeEditForm()"
                :disabled="!hasUpdateAccess"
                ><span class="primary">Cancel</span></v-btn
              >
            </v-col>
            <v-col cols="6" class="d-flex justify-end pr-4">
              <v-btn
                rounded="lg"
                size="small"
                density="compact"
                class="mr-1 secondary"
                variant="elevated"
                :dense="isMobileView"
                style="height: 40px; margin-top: 10px"
                @click="updateJobPostRounds()"
                :isLoading="isLoading"
                :disabled="!hasUpdateAccess"
              >
                <span class="primary">Save</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
      <AppSnackBar
        v-if="showValidationAlert"
        :show-snack-bar="showValidationAlert"
        snack-bar-type="warning"
        timeOut="-1"
        @close-snack-bar="closeValidationAlert"
      >
        <template #custom-alert>
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </template>
      </AppSnackBar>
    </div>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { ADD_UPDATE_JOB_POST_ROUNDS } from "@/graphql/settings/irukka-integration/jobPostFormQueries";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "ViewRounds",
  components: {
    CustomSelect,
  },
  props: {
    jobPostId: {
      type: Number,
      required: true,
    },
    jobPostData: {
      type: Object,
      required: true,
    },
    hasUpdateAccess: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      roundsLoading: false,
      roundsList: [],
      filteredRoundsList: [],
      searchQuery: "",
      selectedRounds: [],
      selectedValues: [],
      hasChanges: false,
      initialRounds: [],
      roundColorMap: {},
      isErrorInList: false,
      errorContent: "",
      validationMessages: [],
      showValidationAlert: false,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    selectedRoundsList() {
      return (this.selectedRounds || [])
        .map((selectedId) => {
          const round = this.roundsList?.find(
            (round) => round.Round_Id === selectedId
          );
          return round
            ? { Round_Id: round.Round_Id, Round_Name: round.Round_Name }
            : null;
        })
        .filter(Boolean);
    },
    availableRounds() {
      return this.roundsList.filter(
        (round) => !this.selectedRounds.includes(round.Round_Id)
      );
    },
  },
  mounted() {
    this.fetchRoundsList();
  },
  methods: {
    fetchRoundsList() {
      this.isErrorInList = false;
      this.roundsLoading = true;
      this.isLoading = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { rounds } = res.data.getDropDownBoxDetails;
            // Check if rounds exist and is an array
            if (rounds && Array.isArray(rounds)) {
              this.roundsList = this.sortByField(rounds, "Round_Name");
              this.filteredRoundsList = [...this.roundsList]; // Initialize filtered list
            } else {
              // Handle case where no rounds are returned
              this.roundsList = [];
              this.filteredRoundsList = [];
            }
            // Pre-select rounds if they exist in jobPostData
            if (this.jobPostData.Rounds && this.jobPostData.Rounds.length > 0) {
              // Get the Round_Id values from jobPostData
              const roundIds = this.jobPostData.Rounds.map(
                (round) => round.Round_Id
              );

              // Set selectedRounds to the Round_Id values
              this.selectedRounds = roundIds;
              this.initialRounds = [...roundIds];
            }
          }
          this.roundsLoading = false;
          this.isLoading = false;
        })
        .catch((err) => {
          this.roundsLoading = false;
          this.isLoading = false;
          this.handleListError(err);
        });
    },

    removeRound(roundId) {
      this.selectedRounds = this.selectedRounds.filter((id) => id !== roundId);
      this.checkForChanges();
    },

    updateSelectValue(value = "") {
      if (value && value.length > 0) {
        // Add selected values to the existing selection
        let mergedArray = [...this.selectedRounds, ...value];
        this.selectedRounds = mergedArray;
        this.checkForChanges();
        this.selectedValues = [];
      }
    },

    getInitials(name) {
      if (!name) return "";
      const names = name.split(" ");
      const firstLetter = names[0].charAt(0);
      const lastLetter =
        names.length > 1 ? names[names.length - 1].charAt(0) : "";
      return (firstLetter + lastLetter).toUpperCase();
    },

    checkForChanges() {
      // Compare arrays to check if there are changes
      this.hasChanges =
        JSON.stringify(this.selectedRounds) !==
        JSON.stringify(this.initialRounds);
    },

    sortByField(data, fieldName) {
      // Check if data exists and is an array
      if (!data || !Array.isArray(data) || data.length === 0) {
        return []; // Return empty array if data is invalid
      }

      return data.sort((a, b) => {
        const nameA = a[fieldName].toLowerCase();
        const nameB = b[fieldName].toLowerCase();

        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
        return 0;
      });
    },
    handleListError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "job post rounds",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    closeEditForm() {
      this.selectedRounds = [...this.initialRounds];
      this.hasChanges = false;
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "job post rounds",
          isListError: false,
        })
        .then((validationErrors) => {
          if (validationErrors && validationErrors.length > 0) {
            let validationMessages = [];
            for (var eCode in validationErrors) {
              validationMessages.push(validationErrors[eCode]);
            }
            this.validationMessages = validationMessages;
            this.showValidationAlert = true;
          }
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    updateJobPostRounds() {
      this.isLoading = true;

      // Extract just the Round_Id values for the API call
      const roundIds = this.selectedRounds;

      this.$apollo
        .mutate({
          mutation: ADD_UPDATE_JOB_POST_ROUNDS,
          variables: {
            jobPostId: this.jobPostId,
            roundIds: roundIds,
          },
          client: "apolloClientAM",
        })
        .then((res) => {
          if (res.data && res.data.addUpdateJobPostRounds) {
            if (res.data.addUpdateJobPostRounds.errorCode === "") {
              this.hasChanges = false;
              this.initialRounds = [...this.selectedRounds];
              let snackbarData = {
                isOpen: true,
                message: "Rounds updated successfully",
                type: "success",
              };
              this.showAlert(snackbarData);
            } else {
              this.handleAddUpdateError(res.data.addUpdateJobPostRounds);
            }
          } else {
            this.handleAddUpdateError(err);
          }
          this.isLoading = false;
        })
        .catch((err) => {
          this.handleAddUpdateError(err);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
  watch: {
    selectedRounds(newValue) {
      newValue.forEach((roundId) => {
        if (!this.roundColorMap[roundId]) {
          // Generating each RGB component with a minimum value to ensure lightness
          const r = Math.floor(Math.random() * (255 - 128) + 128);
          const g = Math.floor(Math.random() * (255 - 128) + 128);
          const b = Math.floor(Math.random() * (255 - 128) + 128);
          // Converting the RGB components to a hex color string
          const color = `#${r.toString(16)}${g.toString(16)}${b.toString(16)}`;
          this.roundColorMap[roundId] = color;
        }
      });
      this.checkForChanges();
    },
  },
};
</script>

<style scoped>
.content {
  margin-bottom: 5%;
  padding-bottom: 80px; /* Add space for the bottom navigation */
}

.elevation-2 {
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.rounded-lg {
  border-radius: 8px;
}

.dropDown {
  min-width: 100%;
  display: inline-block;
}

.cardDiv {
  margin: 0px 10px 15px;
  min-width: 30%;
  display: inline-block;
}

.cardDiv .deleteIcon {
  display: none;
}

.cardDiv:hover .deleteIcon {
  display: inline-block;
}

.deleteIcon:hover {
  color: red !important;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

@media screen and (width <= 728px) {
  .cardDiv {
    min-width: 100%;
    margin: 0 0 15px 0;
  }

  .content {
    padding-bottom: 100px; /* More space for bottom nav on mobile */
  }
}
</style>
