<template>
  <v-container class="grade-container" fluid>
    <div>
      <v-row justify="center">
        <v-col cols="12" lg="11" md="12" sm="12">
          <v-card min-height="560" class="rounded-lg">
            <v-card-text>
              <div class="text-center mb-6">
                <span v-for="i in 3" :key="i">
                  <v-icon color="primary" size="18" class="ml-1">
                    {{ currentStep >= i ? "fas fa-circle" : "far fa-circle" }}
                  </v-icon>
                </span>
              </div>
              <BulkImportStep1
                class="mb-10"
                v-show="currentStep === 1"
                ref="bulkStep1"
                :step1-text="step1Text"
                @file-upload-success="uploadFile($event)"
                @file-upload-error="fileRemoveOrError()"
                @generate-excel="onGenerateExcel()"
                :showDownload="true"
              >
              </BulkImportStep1>
              <BulkImportStep2
                class="mb-10 pb-5"
                v-if="fileContent.length > 0 && currentStep === 2"
                ref="bulkStep2"
                :file-params="fileContent"
                :headers-selected="selectedHeaders"
                @column-mapped="
                  matchedCount = $event[0];
                  mappedFileHeader = $event[1];
                "
              ></BulkImportStep2>
              <BulkImportStep3
                class="mb-10"
                ref="bulkImportStep3"
                v-if="checkMatchedFields && currentStep === 3"
                :fields="generateFields"
                :json-data="excelEditorData"
                type-of-import="grades"
                :extend-validation="excelValidation"
              ></BulkImportStep3>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-bottom-navigation v-model="openBottomSheet">
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
        >
          <v-row class="pa-3" justify="center">
            <v-col
              v-if="!isLoadUploadButton"
              cols="6"
              class="pa-0 d-flex justify-start align-center pl-2"
            >
              <v-btn
                v-if="currentStep > 1"
                id="back_to_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                color="primary"
                @click="backToStep()"
              >
                <span><i class="fa fa-chevron-left pr-2"></i> Back</span></v-btn
              >
              <v-btn
                id="cancel_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                class="ml-2"
                color="primary"
                @click="closeForm()"
                >Cancel</v-btn
              >
            </v-col>
            <v-col
              :cols="isLoadUploadButton ? '12' : '6'"
              class="pa-0 d-flex justify-center align-center pr-4"
              :style="windowWidth >= 1264 ? 'margin-left: -106px' : ''"
            >
              <div v-if="windowWidth > 768" class="text-end mr-2">
                <div class="mr-1 text-grey text-caption" style="width: 400px">
                  {{ nextBtnHelpContent }}
                </div>
              </div>
              <v-btn
                id="next_step"
                rounded="lg"
                color="primary"
                class="mr-10"
                :disabled="!enableNextButton"
                :loading="isLoadUploadButton"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                @click="nextStep()"
              >
                <span>
                  {{ currentStep === 3 ? "Submit" : "Next" }}
                  <v-icon v-if="currentStep !== 3" class="pl-1" size="15"
                    >fa fa-chevron-right</v-icon
                  >
                </span>
              </v-btn>
            </v-col>
            <v-col
              cols="12"
              v-if="windowWidth <= 768 && nextBtnHelpContent"
              class="pa-1 pr-4 d-flex align-center justify-end"
            >
              <div class="mr-1 text-grey mb-0" style="font-size: 10px">
                {{ nextBtnHelpContent }}
              </div>
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
    </div>
    <v-dialog v-model="importConfirmation" width="50%">
      <v-card>
        <v-row>
          <v-col v-if="invalidData && invalidData.length" cols="12">
            <v-alert prominent type="warning">
              <v-row align="center">
                <v-col v-if="invalidData" class="grow"
                  ><span>{{ invalidGrades.length }}</span>
                  out of
                  {{ excelEditorData.length }} do not have valid records. This
                  may result in omission of those records.
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertGradeData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
          <v-col v-else cols="12" class="pa-3">
            <v-alert prominent type="success">
              <v-row align="center">
                <v-col class="grow">
                  Everything looks <strong>good</strong>.
                  <div class="pt-1">
                    Are you
                    <strong>sure</strong> you want to import the grade details?
                  </div>
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertGradeData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-overlay
          class="align-center justify-center"
          contained
          :model-value="isLoading"
          scrim="#fff"
        >
          <v-progress-circular color="primary" indeterminate size="64">
          </v-progress-circular>
        </v-overlay>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import BulkImportStep1 from "@/views/common/bulkImport/BulkImportStep1.vue";
import BulkImportStep2 from "@/views/common/bulkImport/BulkImportStep2.vue";
import BulkImportStep3 from "@/views/common/bulkImport/BulkImportStep3.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
// import { IMPORT_GRADES } from "@/graphql/corehr/employeegrades.js";

export default {
  name: "GradesImport",
  components: {
    BulkImportStep1,
    BulkImportStep2,
    BulkImportStep3,
  },
  mixins: [FileExportMixin],
  props: {
    backupMainList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data: () => ({
    currentStep: 1,
    fileContent: [],
    errorsCountInExcel: 0,
    matchedCount: 0,
    openBottomSheet: true,
    isLoadUploadButton: false,
    mappedFileHeader: [],
    allowanceRestrictBonus: "No",
    step1Text: {
      typeofData: "grade sheet",
      text: "You have the option of using our predefined template or bring in your own grade sheet with the headers for import",
      heading: "Download the excel template with predefined headers",
    },
    selectedImportType: 1,
    fields: [],
    optionValues: {},
    excelEditorData: [],
    importConfirmation: false,
    finalExcelData: [],
    finalUpdateData: [],
    isLoading: false,
    step2HeadersData: [],
    // New fields for employee grades
    // parentGradeList: [
    //   { value: "A", text: "A" },
    //   { value: "B", text: "B" },
    //   { value: "C", text: "C" },
    //   { value: "D", text: "D" },
    //   { value: "E", text: "E" },
    //   { value: "F", text: "F" },
    //   { value: "G", text: "G" },
    //   { value: "H", text: "H" },
    //   { value: "I", text: "I" },
    //   { value: "J", text: "J" },
    // ],
    // overtimeAllocationList: [
    //   { value: "Wage Index", text: "Wage Index" },
    //   { value: "Fixed Amount", text: "Fixed Amount" },
    // ],
    showConfirmation: false,
    isFormDirty: false,
    // employeeGrade: "",
    // gradeCode: "",
    // minimumHourlyWages: null,
    // maximumHourlyWages: null,
    // minimumOvertimeHourlyWages: null,
    // maximumOvertimeHourlyWages: null,
    // minimumGrossAnnualSalary: null,
    // maximumGrossAnnualSalary: null,
    // overtimeEligibility: "",
    // overtimeAllocation: "",
    // overtimeFixedAmount: null,
    // wageIndex: null,
    // description: "",
  }),

  computed: {
    gradeList() {
      const uniqueGradesSet = new Set(
        this.backupMainList.map((item) => item.Employee_Grade)
      );
      return Array.from(uniqueGradesSet);
    },
    excelValidation() {
      return {
        Employee_Grade: this.gradeList,
        parentGrade: this.optionValues.parentGrade || [],
        Overtime_Eligibility: ["Yes", "No"],
        Overtime_Allocation: ["Wage Index", "Fixed Amount"],
      };
    },
    selectedHeaders() {
      let output = [
        {
          title: "Employee Grade",
          value: "Employee_Grade",
          props: { disabled: false },
        },
        {
          title: "Grade Code",
          value: "gradeCode",
          props: { disabled: false },
        },
        {
          title: "Parent Grade",
          value: "parentGrade",
          props: { disabled: false },
        },
        {
          title: "Minimum Hourly Wage",
          value: "Minimum_Hourly_Wage",
          props: { disabled: false },
        },
        {
          title: "Maximum Hourly Wage",
          value: "Maximum_Hourly_Wage",
          props: { disabled: false },
        },
        {
          title: "Minimum Overtime Hourly Wages",
          value: "Minimum_Overtime_Hourly_Wages",
          props: { disabled: false },
        },
        {
          title: "Maximum Overtime Hourly Wages",
          value: "Maximum_Overtime_Hourly_Wages",
          props: { disabled: false },
        },
        {
          title: "Minimum Gross Annual Salary",
          value: "minAnnualSalary",
          props: { disabled: false },
        },
        {
          title: "Maximum Gross Annual Salary",
          value: "maxAnnualSalary",
          props: { disabled: false },
        },
        {
          title: "Overtime Eligibility",
          value: "Overtime_Eligibility",
          props: { disabled: false },
        },
        {
          title: "Overtime Allocation",
          value: "Overtime_Allocation",
          props: { disabled: false },
        },
        {
          title: "Overtime Fixed Amount",
          value: "Overtime_Fixed_Amount",
          props: { disabled: false },
        },
        {
          title: "Wage Index",
          value: "Wage_Index",
          props: { disabled: false },
        },
        {
          title: "description",
          value: "description",
          props: { disabled: false },
        },
      ];
      return output;
    },

    invalidData() {
      return this.$refs.bulkImportStep3.invalidData;
    },
    invalidGrades() {
      let invalidData = this.$refs.bulkImportStep3.invalidData;
      let employeeGradeFail = Array.from(new Set(invalidData));
      return employeeGradeFail;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    enableNextButton() {
      if (this.currentStep === 1 && this.fileContent.length > 0) {
        return true;
      } else if (this.currentStep === 2 && this.checkMatchedFields) {
        return true;
      } else if (this.currentStep === 3) {
        this.formattedFileContent();
        return true;
      } else {
        return false;
      }
    },

    nextBtnHelpContent() {
      if (this.currentStep === 1) {
        if (this.fileContent.length === 0)
          return "Please import the data with supported file types (CSV, XLSX, and XLS) to continue with the next step.";
        else return "";
      } else if (this.currentStep === 2) {
        return "The unmatched optional column(s) will not be processed in the next step.";
      } else if (this.currentStep === 3) {
        if (this.formattedFileContent.length === 0) {
          return "";
        } else if (this.errorsCountInExcel !== 0) {
          return "There seems to be some validation error(s) in your file. Please amend it before uploading.";
        } else {
          return "By clicking the 'Submit' button, you can import employee grade data.";
        }
      } else {
        return "";
      }
    },
    mandatoryHeader() {
      let fields = ["Grade Code", "Employee Grade"];
      return fields;
    },
    checkMatchedFields() {
      let mandatoryHeader = this.mandatoryHeader;
      if (this.matchedCount === this.mandatoryHeader.length) {
        let mandatoryMatchedCount = 0;
        for (var i in this.mappedFileHeader) {
          if (mandatoryHeader.includes(this.mappedFileHeader[i].hrapp_header))
            mandatoryMatchedCount++;
        }
        this.addHeaders();
        return mandatoryMatchedCount === this.mandatoryHeader.length
          ? true
          : false;
      } else return false;
    },
    excelFileData() {
      return this.fileContent;
    },
    generateFields() {
      let formOutput = [
        {
          field: "Employee Grade",
          label: "Employee Grade",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Grade Code",
          label: "Grade Code",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Parent Grade",
          label: "Parent Grade",
          type: "select",
          readonly: false,
          width: "200px",
          options: this.optionValues.parentGrade || [], // This should be populated with options fetched from an API.
        },
        {
          field: "Minimum Hourly Wage",
          label: "Minimum Hourly Wage",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "Maximum Hourly Wage",
          label: "Maximum Hourly Wage",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "Minimum Overtime Hourly Wages",
          label: "Minimum Overtime Hourly Wages",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "Maximum Overtime Hourly Wages",
          label: "Maximum Overtime Hourly Wages",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "Minimum Gross Annual Salary",
          label: "Minimum Gross Annual Salary",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "Maximum Gross Annual Salary",
          label: "Maximum Gross Annual Salary",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "Overtime Eligibility",
          label: "Overtime Eligibility",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Yes", "No"],
        },
        {
          field: "Overtime Allocation",
          label: "Overtime Allocation",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Wage Index", "Overtime Fixed Amount"],
        },
        {
          field: "Overtime Fixed Amount",
          label: "Overtime Fixed Amount",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "Wage Index",
          label: "Wage Index",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "description",
          label: "description",
          type: "string",
          readonly: false,
          width: "200px",
        },
      ];
      return formOutput;
    },
  },
  methods: {
    onGenerateExcel() {
      const gradeObject = {
        employeeGrade: null,
        gradeCode: null,
        parentGrade: null,
        minimumHourlyWage: null,
        maximumHourlyWage: null,
        minimumOvertimeHourlyWages: null,
        maximumOvertimeHourlyWages: null,
        minimumGrossAnnualSalary: null,
        maximumGrossAnnualSalary: null,
        overtimeEligibility: null,
        overtimeAllocation: null,
        overtimeFixedAmount: null,
        wageIndex: null,
        description: null,
      };

      const exportData = Array.from({ length: 100 }, () => ({
        ...gradeObject,
      }));

      let headers = [
        { key: "employeeGrade", header: "Employee Grade" },
        { key: "gradeCode", header: "Grade Code" },
        { key: "parentGrade", header: "Parent Grade" },
        { key: "minimumHourlyWage", header: "Minimum Hourly Wage" },
        { key: "maximumHourlyWage", header: "Maximum Hourly Wage" },
        {
          key: "minimumOvertimeHourlyWages",
          header: "Minimum Overtime Hourly Wages",
        },
        {
          key: "maximumOvertimeHourlyWages",
          header: "Maximum Overtime Hourly Wages",
        },
        {
          key: "minimumGrossAnnualSalary",
          header: "Minimum Gross Annual Salary",
        },
        {
          key: "maximumGrossAnnualSalary",
          header: "Maximum Gross Annual Salary",
        },
        { key: "overtimeEligibility", header: "Overtime Eligibility" },
        { key: "overtimeAllocation", header: "Overtime Allocation" },
        { key: "overtimeFixedAmount", header: "Overtime Fixed Amount" },
        { key: "wageIndex", header: "Wage Index" },
        { key: "description", header: "description" },
      ];

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Employee Grades Template.xlsx",
        sheetName: "Employee Grades Sheet",
        header: headers,
        requiredHeaders: [
          "Employee Grade",
          "Grade Code",
          "Parent Grade",
          "Minimum Hourly Wage",
          "Maximum Hourly Wage",
          "Minimum Overtime Hourly Wages",
          "Maximum Overtime Hourly Wages",
          "Minimum Gross Annual Salary",
          "Maximum Gross Annual Salary",
          "Overtime Eligibility",
          "Overtime Allocation",
          "Overtime Fixed Amount",
          "Wage Index",
        ],
        columnHighlightProps: {
          type: "Employee Grades Import",
          parentGrade: [], // Populate with options from API if needed
          Overtime_Allocation: ["Wage Index", "Overtime Fixed Amount"],
          Overtime_Eligibility: ["Yes", "No"],
          // Add other fields with options if applicable
        },
      };

      this.exportExcelFile(exportOptions);
    },

    formattedFileContent() {
      //With Fields form the headers
      let generatedData = this.formExcelData();
      this.excelEditorData = generatedData;
    },
    formExcelData() {
      let fields = this.generateFields;
      let data = JSON.parse(JSON.stringify(this.excelFileData));
      let headersAssigned = this.step2HeadersData;
      //Getting the field of the array of objects
      let excelData = [];
      let idCounter = 1;
      // Iterate through each row of data
      for (let i = 1; i < data.length; i++) {
        let rowData = data[i];
        let rowObj = { $id: "000000" + idCounter++ };

        // Iterate through each field definition and populate the row object
        for (let j = 0; j < fields.length; j++) {
          let fieldDef = fields[j];
          let fieldName = fieldDef.field;
          // Find the index of the field in the header mappings array
          let headerIndex = headersAssigned.findIndex(
            (header) => header.hrapp_header === fieldName
          );

          // If the field is present in the header mappings array, use the corresponding value from the input data
          if (headerIndex >= 0) {
            let dataValue = rowData[headerIndex];
            if (dataValue !== null && dataValue !== undefined) {
              rowObj[fieldName] = dataValue;
            } else {
              rowObj[fieldName] = null;
            }
          } else {
            // If the field is not present in the header mappings array, use the default value for the field type
            switch (fieldDef.type) {
              case "string":
                rowObj[fieldName] = "";
                break;
              case "number":
                rowObj[fieldName] = 0;
                break;
              case "boolean":
                rowObj[fieldName] = false;
                break;
              default:
                rowObj[fieldName] = null;
                break;
            }
          }
        }
        excelData.push(rowObj);
      }
      return excelData;
    },
    // called cancel is clicked to close form
    closeForm() {
      this.$emit("close-import-model");
    },
    // back button clicks, to subtract 1 from current step
    backToStep() {
      this.currentStep = this.currentStep - 1;
      this.allRecordsFail = false;
    },

    // next button clicks, to add 1 from current step
    nextStep() {
      if (this.currentStep === 3) {
        this.formBulkData(this.$refs.bulkImportStep3.filteredData);
      } else {
        this.currentStep += 1;
      }
    },

    addHeaders() {
      if (this.$refs.bulkStep2 && this.$refs.bulkStep2.tableItems) {
        this.step2HeadersData = this.$refs.bulkStep2.tableItems;
      }
    },
    formBulkData(data) {
      const filteredData = data;
      this.finalExcelData = data;

      this.finalUpdateData = filteredData.map((item) => {
        const newItem = {
          Employee_Grade: item["Employee_Grade"],
          gradeCode: item["gradeCode"],
          parentGrade: item["parentGrade"],
          Minimum_Hourly_Wage: item["Minimum_Hourly_Wage"],
          Maximum_Hourly_Wage: item["Maximum_Hourly_Wage"],
          Minimum_Overtime_Hourly_Wages: item["Minimum_Overtime_Hourly_Wages"],
          Maximum_Overtime_Hourly_Wages: item["Maximum_Overtime_Hourly_Wages"],
          minAnnualSalary: item["minAnnualSalary"],
          maxAnnualSalary: item["maxAnnualSalary"],
          Overtime_Eligibility: item["Overtime_Eligibility"],
          Overtime_Allocation: item["Overtime_Allocation"],
          Overtime_Fixed_Amount: item["Overtime_Fixed_Amount"],
          Wage_Index: item["Wage_Index"],
          description: item["description"],
        };
        return newItem;
      });

      this.importConfirmation = true;
    },
    // called when file uploaded in step 1
    uploadFile(event) {
      this.fileContent = event;
    },
    // called from step 1 when error while uploading or removing the file
    fileRemoveOrError() {
      this.fileContent = [];
      this.matchedCount = 0;
      this.errorsCountInExcel = 0;
    },
    insertEmployeeGradeData(data) {
      if (data.length) {
        let vm = this;
        vm.isLoading = true;
        vm.$apollo
          .mutate({
            mutation: ADD_EDIT_LIST_EMPLOYEE_GRADE_DETAILS, // Use the appropriate mutation for employee grades
            client: "apolloClientJ",
            variables: {
              employeeGradeData: data, // Adjust the variable name as needed
            },
          })
          .then(async (response) => {
            if (
              response &&
              response.data &&
              response.data.importEmployeeGrade
            ) {
              let { validationError } = response.data.importEmployeeGrade;
              validationError = JSON.parse(validationError);
              let excelInvalidData = this.$refs.bulkImportStep3.invalidData;
              let remainingData = [];
              let inputData = this.$refs.bulkImportStep3.editorData;

              for (let i = 0; i < inputData.length; i++) {
                if (excelInvalidData.includes(inputData[i].$id)) {
                  if (!remainingData.includes(inputData[i])) {
                    remainingData.push(inputData[i]);
                  }
                }
              }

              let backendErrorsWithMessages = [];
              // Validation Backend Error Exists
              for (let i = 0; i < validationError.length; i++) {
                for (
                  let j = 0;
                  j < validationError[i].failedArrays.length;
                  j++
                ) {
                  for (let k = 0; k < inputData.length; k++) {
                    if (!remainingData.includes(inputData[k])) {
                      remainingData.push(inputData[k]);
                    }
                    let error = JSON.parse(JSON.stringify(inputData[k]));
                    error.Message = validationError[i].Message;
                    backendErrorsWithMessages.push(error);
                  }
                }
              }

              this.excelEditorData = remainingData;
              this.$refs.bulkImportStep3.editorData = remainingData;

              // Set Field Error
              for (let i = 0; i < backendErrorsWithMessages.length; i++) {
                let message = backendErrorsWithMessages[i].Message;
                let data = backendErrorsWithMessages[i];
                let field = {};
                if (field && message && message !== undefined) {
                  field.name = message.includes("Grade Code")
                    ? "Grade Code"
                    : message.includes("Employee Grade")
                    ? "Employee Grade"
                    : message.includes("Parent Grade")
                    ? "Parent Grade"
                    : message.includes("Minimum Hourly Wage")
                    ? "Minimum Hourly Wage"
                    : message.includes("Maximum Hourly Wage")
                    ? "Maximum Hourly Wage"
                    : message.includes("Minimum Overtime Hourly Wages")
                    ? "Minimum Overtime Hourly Wages"
                    : message.includes("Maximum Overtime Hourly Wages")
                    ? "Maximum Overtime Hourly Wages"
                    : message.includes("Minimum Gross Annual Salary")
                    ? "Minimum Gross Annual Salary"
                    : message.includes("Maximum Gross Annual Salary")
                    ? "Maximum Gross Annual Salary"
                    : message.includes("Overtime Eligibility")
                    ? "Overtime Eligibility"
                    : message.includes("Overtime Allocation")
                    ? "Overtime Allocation"
                    : message.includes("Overtime Fixed Amount")
                    ? "Overtime Fixed Amount"
                    : message.includes("Wage Index")
                    ? "Wage Index"
                    : "Employee Grade";
                }
                this.$refs.bulkImportStep3.setFieldError(message, data, field);
              }

              vm.importConfirmation = false;
              vm.isLoading = false;
              if (
                !excelInvalidData.length &&
                !backendErrorsWithMessages.length
              ) {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Employee grades imported successfully.",
                };
                vm.showAlert(snackbarData);
                vm.closeForm();
                this.$emit("refetch-data");
              }
            } else {
              vm.handleImportError();
              vm.importConfirmation = false;
              vm.closeForm();
            }
          })
          .catch((err) => {
            vm.handleImportError(err);
            vm.importConfirmation = false;
            vm.closeForm();
          });
      } else {
        this.importConfirmation = false;
        this.closeForm();
      }
    },
    handleImportError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "importing",
        form: "grade",
        isListError: false,
      });
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.grade-container {
  padding: 5em 2em 0em 3em;
}
.v-bottom-navigation__content {
  justify-content: space-around;
  flex-direction: column;
}
.dp__button_bottom {
  display: none;
}
</style>
