<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      :show-bottom-sheet="!listLoading"
    >
      <template #topBarContent>
        <v-row justify="center" v-if="originalList.length > 0">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isDefaultFilter="false"
              :isFilter="false"
              @reset-emp-filter="resetFilter()"
              @apply-emp-filter="applyFilter()"
            />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container fluid class="table-organization-container">
      <div v-if="formAccess">
        <div v-if="listLoading">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          icon-name="fas fa-redo-alt"
          :button-text="'Retry'"
          @button-click="refetchData()"
        >
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="originalList.length == 0"
          key="no-data-screen"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4" style="background: white">
                <v-col cols="12">
                  <NotesCard
                    notes="The Table of Organization (Position Master) serves as a detailed blueprint of the company’s workforce structure. It outlines the hierarchy, defines roles, and provides a clear overview of all approved positions within the organization."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="This structured approach aids in strategic manpower planning, ensuring that all necessary roles are filled to meet business objectives effectively."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="Currently, there is no data available for the Table of Organization (Position Master). Please import the Position Master data, making sure to include all approved plantilla positions."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                </v-col>
                <v-col cols="12">
                  <div
                    class="d-flex flex-row align-center flex-wrap justify-center"
                  >
                    <CustomSelect
                      v-model="selectedPosition"
                      :itemSelected="selectedPosition"
                      color="primary"
                      :items="originalGroupList"
                      label="Position Group"
                      item-title="Pos_full_Name"
                      item-value="Originalpos_Id"
                      density="compact"
                      :disabled="disableGroup"
                      :isAutoComplete="true"
                      clearable
                      class="mr-3"
                      @update:model-value="updateGroup()"
                      style="
                        max-width: 220px !important;
                        width: 150px !important;
                      "
                    />
                    <CustomSelect
                      class="mr-3"
                      v-model="selectedDivision"
                      color="primary"
                      :items="divisionList"
                      clearable
                      :isAutoComplete="true"
                      label="Division"
                      placeholder="Division"
                      item-title="Pos_full_Name"
                      :itemSelected="selectedDivision"
                      :is-loading="divisionListLoading"
                      item-value="Originalpos_Id"
                      density="compact"
                      :disabled="disableDivision"
                      style="
                        max-width: 220px !important;
                        width: 150px !important;
                      "
                      @update:model-value="updateDivision($event)"
                    />
                    <CustomSelect
                      v-model="selectedDepartment"
                      color="primary"
                      :items="departmentList"
                      class="mr-3"
                      clearable
                      :is-loading="divisionListLoading"
                      :isAutoComplete="true"
                      label="Department"
                      placeholder="Department"
                      item-title="Pos_full_Name"
                      :itemSelected="selectedDepartment"
                      :disabled="disableDepartment"
                      item-value="Originalpos_Id"
                      density="compact"
                      style="
                        max-width: 220px !important;
                        width: 150px !important;
                      "
                      @update:model-value="updateDepartment($event)"
                    />
                    <CustomSelect
                      v-model="selectedSection"
                      color="primary"
                      :items="sectionList"
                      class="mr-3"
                      clearable
                      :is-loading="divisionListLoading"
                      :isAutoComplete="true"
                      label="Section"
                      placeholder="Section"
                      item-title="Pos_full_Name"
                      :itemSelected="selectedSection"
                      :disabled="disableSection"
                      item-value="Originalpos_Id"
                      density="compact"
                      style="
                        max-width: 220px !important;
                        width: 150px !important;
                      "
                    />
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="mt-n6"
                      rounded="lg"
                      @click="applyFilter()"
                    >
                      Apply
                    </v-btn>
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  :class="
                    windowWidth >= 1264
                      ? 'd-flex align-center justify-center'
                      : 'flex-wrap d-flex align-center justify-center'
                  "
                  class="mb-4"
                >
                  <v-btn
                    color="transparent"
                    variant="flat"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="retrieveTotalTableOrganization()"
                  >
                    <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="forecastList.length == 0"
          key="no-results-screen"
          main-title="There are no group matched for the selected filters/searches."
          image-name="common/no-records"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4">
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    color="primary"
                    variant="elevated"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    @click="resetFilter()"
                  >
                    Reset Filter/Search
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div v-else>
          <div>
            <div
              class="d-flex flex-wrap align-center my-3"
              :class="isMobileView ? 'flex-column' : ''"
              style="justify-content: space-between"
            >
              <div
                class="d-flex align-center flex-wrap"
                :class="isMobileView ? 'justify-center' : ''"
              >
                <v-btn
                  v-if="showOrgView"
                  color="primary"
                  variant="elevated"
                  class="mr-3"
                  rounded="lg"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  @click="showOrgView = false"
                >
                  Back
                </v-btn>
                <div v-else class="d-flex flex-row align-center flex-wrap">
                  <CustomSelect
                    v-model="selectedPosition"
                    color="primary"
                    :items="originalGroupList"
                    class="mr-3"
                    clearable
                    :isAutoComplete="true"
                    label="Position Group"
                    placeholder="Position Group"
                    item-title="Pos_full_Name"
                    :itemSelected="selectedPosition"
                    item-value="Originalpos_Id"
                    density="compact"
                    min-width="150px"
                    max-width="200px"
                    :disabled="disableGroup"
                    @update:model-value="updateGroup()"
                  />
                  <CustomSelect
                    v-model="selectedDivision"
                    color="primary"
                    :items="divisionList"
                    class="mr-3"
                    clearable
                    :isAutoComplete="true"
                    label="Division"
                    :is-loading="divisionListLoading"
                    placeholder="Division"
                    item-title="Pos_full_Name"
                    :itemSelected="selectedDivision"
                    item-value="Originalpos_Id"
                    density="compact"
                    min-width="150px"
                    max-width="200px"
                    :disabled="disableDivision"
                    @update:model-value="updateDivision($event)"
                  />
                  <CustomSelect
                    v-model="selectedDepartment"
                    color="primary"
                    :items="departmentList"
                    class="mr-3"
                    clearable
                    :is-loading="divisionListLoading"
                    :isAutoComplete="true"
                    label="Department"
                    placeholder="Department"
                    item-title="Pos_full_Name"
                    :itemSelected="selectedDepartment"
                    item-value="Originalpos_Id"
                    density="compact"
                    min-width="150px"
                    max-width="200px"
                    :disabled="disableDepartment"
                    @update:model-value="updateDepartment($event)"
                  />
                  <CustomSelect
                    v-model="selectedSection"
                    color="primary"
                    :items="sectionList"
                    class="mr-3"
                    clearable
                    :is-loading="divisionListLoading"
                    :isAutoComplete="true"
                    label="Section"
                    placeholder="Section"
                    item-title="Pos_full_Name"
                    :itemSelected="selectedSection"
                    item-value="Originalpos_Id"
                    density="compact"
                    min-width="150px"
                    max-width="200px"
                    :disabled="disableSection"
                  />
                  <v-btn
                    variant="elevated"
                    color="primary"
                    class="mt-n6"
                    rounded="lg"
                    @click="applyFilter()"
                  >
                    Apply
                  </v-btn>
                </div>
              </div>
              <div>
                <div>
                  <v-btn
                    v-if="organizationList && organizationList.length > 0"
                    :color="showOrgView ? 'primary' : 'transparent'"
                    variant="flat"
                    rounded="lg"
                    class="mr-3 org-chart-view-btn"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="showOrgView = true"
                  >
                    <v-icon class="grey">fas fa-sitemap</v-icon>
                  </v-btn>
                  <v-btn
                    color="transparent"
                    variant="flat"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="retrieveTotalTableOrganization()"
                  >
                    <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
                  </v-btn>

                  <v-menu class="mb-1" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                        <v-icon>fas fa-ellipsis-v</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action.key"
                        @click="onMoreAction(action.key)"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="15" class="pr-2">{{
                                action.icon
                              }}</v-icon
                              >{{ action.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>
            </div>
            <v-row>
              <v-col :cols="12">
                <v-data-table
                  v-if="!showOrgView"
                  :headers="tableHeaders"
                  :items="forecastList"
                  fixed-header
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
                  ]"
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      forecastList,
                      true
                    )
                  "
                  style="box-shadow: none !important"
                  class="elevation-1"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView
                          ? ' v-data-table__mobile-table-row mt-2'
                          : ''
                      "
                      @click="openViewForm(item)"
                    >
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                        >
                          Position
                        </div>
                        <section class="d-flex justify-start align-center">
                          <v-tooltip
                            :text="item?.Pos_Name"
                            location="bottom"
                            max-width="300"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                v-bind="props"
                                class="text-body-2 text-truncate text-start text-primary"
                                :style="'max-width: 150px; '"
                              >
                                {{ checkNullValue(item.Pos_Name) }}
                              </div>
                            </template></v-tooltip
                          >
                        </section>
                        <div
                          class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                          :style="'max-width: 200px; '"
                        >
                          {{ checkNullValue(item.Pos_Code) }}
                        </div>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                        >
                          Job Title Code
                        </div>
                        <section>
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="'max-width: 200px; '"
                          >
                            {{ checkNullValue(item.Job_Title_Code) }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                        >
                          Cost Code
                        </div>
                        <section>
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="'max-width: 200px; '"
                          >
                            {{ checkNullValue(item.Cost_Code) }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                        >
                          No Of Approved Positions
                        </div>
                        <section>
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="'max-width: 200px; '"
                          >
                            {{ checkNullValue(item.Approved_Position) }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                        >
                          Warm Bodies
                        </div>
                        <section>
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="'max-width: 200px; '"
                          >
                            {{ checkNullValue(item.Warm_Bodies) }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                        >
                          Approved Vacant Positions
                        </div>
                        <section>
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="'max-width: 200px; '"
                          >
                            {{ checkNullValue(item.To_Be_Hired) }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                        >
                          Status
                        </div>
                        <section>
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="'max-width: 200px; '"
                            :class="getStatusClass(item.Status)"
                          >
                            {{ checkNullValue(item.Status) }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                        >
                          Actions
                        </div>
                        <section>
                          <ActionMenu
                            v-if="listActions(item).length > 0"
                            :actions="listActions(item)"
                            :access-rights="havingAccess"
                            @selected-action="onActions($event, item)"
                            :isPresentTooltip="true"
                            icon-color="grey"
                          ></ActionMenu>
                          <span v-else>-</span>
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
          <div v-if="showOrgView && organizationList.length > 0">
            <OrganizationView
              :orgList="organizationList"
              :parentOrgId="parentOrgId"
            />
          </div>
        </div>
      </div>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <view-table-of-organization
    v-if="showViewForm"
    :enableView="showViewForm"
    :selected-item="selectedItem"
    @open-edit-form="openEditForm()"
    @close-form="onCloseForm()"
  ></view-table-of-organization>
  <add-edit-table-of-organization
    v-if="showAddEditForm"
    :showAddForm="showAddEditForm"
    :isEdit="isEdit"
    :edit-form-data="selectedItem"
    @close-form="onCloseForm()"
    @edit-updated="refetchList()"
  ></add-edit-table-of-organization>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu.vue";
import NotesCard from "@/components/helper-components/NotesCard.vue";
import {
  LIST_OF_POSITION_LIST,
  TABLE_OF_ORGANIZATION_LIST,
} from "@/graphql/mpp/manPowerPlanningQueries";
import moment from "moment";
import OrganizationView from "./OrganizationView.vue";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import AddEditTableOfOrganization from "./AddEditTableOfOrganization.vue";
import ViewTableOfOrganization from "./ViewTableOfOrganization.vue";
import { ORG_STRUCTURE_BASED_ON_GROUP } from "@/graphql/mpp/newPositionQueries";

export default {
  name: "TableOfOrganization",
  data() {
    return {
      originalList: [],
      forecastList: [],
      selectedPosition: null,
      isErrorInList: false,
      errorContent: "",
      mainTabList: ["Table Of Organization"],
      currentTabItem: "tab-0",
      listLoading: false,
      openMoreMenu: false,
      forecastYearList: [],
      selectedForecastYear: moment().year(),
      overlay: false,
      showOrgView: false,
      organizationList: [],
      isLoading: false,
      tableOrgLimitToCallAPI: 10000,
      totalApiCount: 0,
      apiCallCount: 0,
      showAddEditForm: false,
      showViewForm: false,
      selectedItem: {},
      isEdit: false,
      parentOrgId: "",
      originalGroupList: [],
      divisionList: [],
      selectedDivision: null,
      divisionCode: null,
      departmentList: [],
      selectedDepartment: null,
      sectionList: [],
      selectedSection: null,
      divisionListLoading: false,
      allExport: false,
      parentGroup: "",
      orgLevel: "",
    };
  },
  mixins: [FileExportMixin],
  mounted() {
    if (this.formAccess && this.formAccess.view) {
      this.retrieveTotalTableOrganization("mounted");
      this.retrievePositionList();
    }
  },
  computed: {
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isRecruiter() {
      return this.$store.state.isRecruiter;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      if (this.isAdmin || this.isRecruiter) {
        actions.push({
          key: "Export All",
          icon: "fas fa-file-export",
        });
      }
      return actions;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    formAccess() {
      let formAccess = this.accessRights("288");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    listActions() {
      return (item) => {
        if (item.Status && item.Status.toLowerCase() == "draft") {
          return [];
        } else {
          return ["Edit"];
        }
      };
    },
    tableHeaders() {
      let headers = [];
      headers.push(
        {
          title: "Position",
          align: "start",
          key: "Pos_Code",
        },
        {
          title: "Job Title Code",
          align: "start",
          key: "Job_Title_Code",
        },
        {
          title: "Cost Code",
          align: "start",
          key: "Cost_Code",
        },
        {
          title: "No of Approved Positions",
          align: "start",
          key: "Approved_Position",
        },
        {
          title: "Warm Bodies",
          align: "start",
          key: "Warm_Bodies",
        },
        {
          title: "Approved Vacant Positions",
          align: "start",
          key: "To_Be_Hired",
        },
        {
          title: "Status",
          align: "start",
          key: "Status",
        },
        {
          title: "Actions",
          sortable: false,
        }
      );
      return headers;
    },
    havingAccess() {
      let havingAccess = {};
      havingAccess["update"] = this.formAccess && this.formAccess.update;
      return havingAccess;
    },
    disableSection() {
      if (!this.selectedDepartment) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (this.orgLevel?.toLowerCase() == "sec") {
        return true;
      }
      return false;
    },
    disableDepartment() {
      if (!this.selectedDivision) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
    disableDivision() {
      if (!this.selectedPosition) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "div" ||
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
    disableGroup() {
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "grp" ||
        this.orgLevel?.toLowerCase() == "div" ||
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  components: {
    EmployeeDefaultFilterMenu,
    CustomSelect,
    NotesCard,
    OrganizationView,
    ActionMenu,
    AddEditTableOfOrganization,
    ViewTableOfOrganization,
  },
  methods: {
    checkNullValue,
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      } else if (actionType.toLowerCase() === "export all") {
        this.allExport = true;
        this.retrieveTotalTableOrganization();
      }
      this.openMoreMenu = false;
    },
    getStatusClass(status) {
      if (status === "Open") {
        return "text-amber-darken-4";
      } else if (status === "Closed") {
        return "text-amber";
      } else if (status === "Draft") {
        return "text-purple-darken-4";
      } else if (status === "Scheduled For Interview") {
        return "text-green";
      } else if (status === "Approved") {
        return "text-brown-darken-4";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    addForecast() {
      this.overlay = true;
    },
    onCloseOverlay() {
      this.overlay = false;
    },
    applyFilter() {
      this.retrieveTotalTableOrganization();
    },
    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.selectedPosition = null;
      this.selectedDivision = null;
      this.selectedDepartment = null;
      this.selectedSection = null;
      this.divisionCode = null;
      this.retrieveTotalTableOrganization();
    },
    refetchData() {
      this.errorContent = "";
      this.isErrorInList = false;
      this.selectedPosition = null;
      this.selectedDivision = null;
      this.selectedDepartment = null;
      this.selectedSection = null;
      this.divisionCode = null;
      this.retrieveTotalTableOrganization();
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action == "Edit") {
        this.showAddEditForm = true;
        this.isEdit = true;
      }
    },
    onCloseForm() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = {};
      this.isEdit = false;
    },
    openEditForm() {
      this.showViewForm = false;
      this.showAddEditForm = true;
      this.isEdit = true;
    },
    openViewForm(item) {
      this.showViewForm = true;
      this.showAddEditForm = false;
      this.isEdit = false;
      this.selectedItem = item;
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.originalList = [];
      this.onCloseForm();
      this.retrieveTotalTableOrganization();
    },
    onApplySearch(val) {
      if (!val) {
        this.forecastList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.forecastList = searchItems;
      }
    },
    retrievePositionList() {
      this.isLoading = true;
      this.$apollo
        .query({
          query: LIST_OF_POSITION_LIST,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            Form_Id: 288,
            conditions: [
              {
                key: "Org_Level",
                operator: "=",
                value: "GRP",
              },
            ],
          },
        })
        .then((res) => {
          this.isLoading = false;
          if (
            res &&
            res.data &&
            res.data.jobTitleList &&
            res.data.jobTitleList.jobTitleResult
          ) {
            const tempGroupList = res.data.jobTitleList.jobTitleResult;
            tempGroupList.forEach((element) => {
              element.Originalpos_Id = element.Originalpos_Id?.toString();
            });
            this.originalGroupList = [
              {
                Pos_Name: "No Group",
                Pos_Code: "nogroup",
                Pos_full_Name: "No Group",
                Originalpos_Id: "nogroup",
              },
            ].concat(tempGroupList);
          } else {
            this.originalGroupList = [];
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.originalGroupList = [];
          this.handleListError(err, "Position Title");
        });
    },
    selectPositionIdAndRetrieveList() {
      let code = "";
      if (
        this.selectedSection &&
        this.selectedSection.toString().toLowerCase() != "nosection"
      ) {
        code = this.selectedSection;
      } else if (
        this.selectedDepartment &&
        this.selectedDepartment.toString().toLowerCase() != "nodepartment"
      ) {
        code = this.selectedDepartment;
      } else if (
        this.selectedDivision &&
        this.selectedDivision.toString().toLowerCase() != "nodivision"
      ) {
        code = this.selectedDivision;
      } else if (
        this.selectedPosition &&
        String(this.selectedPosition || "")
          .toString()
          .toLowerCase() != "nogroup"
      ) {
        code = this.selectedPosition;
      }
      return code;
    },
    async retrieveTotalTableOrganization(type = "") {
      try {
        const code = this.selectPositionIdAndRetrieveList();
        if (!this.allExport) {
          this.originalList = [];
          this.forecastList = [];
          this.listLoading = true;
        } else {
          this.isLoading = true;
        }
        const res = await this.$apollo.query({
          query: TABLE_OF_ORGANIZATION_LIST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            formId: 288,
            postionParentId: String(code),
            limit: 10000,
            offset: 0,
            alexport: this.allExport,
          },
        });
        this.listLoading = false;
        if (res && res.data && res.data.listForecastPosition) {
          if (!this.allExport) {
            const { totalCountResult, parentGroup, orgLevel, positionList } =
              res.data.listForecastPosition;
            if (type === "mounted") {
              this.parentGroup = parentGroup;
              this.orgLevel = orgLevel;
              this.selectDefaultPosition();
            }
            if (positionList && positionList.length > 0) {
              this.originalList = positionList;
              this.forecastList = positionList;
            }
            if (totalCountResult > 0) {
              const totalCount = parseInt(totalCountResult);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalCount / this.tableOrgLimitToCallAPI
              );
              for (let i = 1; i < this.totalApiCount; i++) {
                this.retrieveTableOrganization(i);
              }
            } else {
              this.listLoading = false;
            }
          } else {
            let sheetUrl = res.data.listForecastPosition.s3Url;
            if (sheetUrl) {
              window.open(sheetUrl, "_blank");
            }
            this.isLoading = false;
          }
          this.allExport = false;
        }
      } catch (err) {
        this.allExport = false;
        this.listLoading = false;
        this.isLoading = true;
        this.handleListError(err, "Position Title");
      }
    },
    selectDefaultPosition() {
      if (this.orgLevel) {
        if (this.orgLevel === "GRP") {
          if (this.parentGroup) {
            this.selectedPosition = this.parentGroup;
          } else {
            this.selectedPosition = "nogroup";
          }
        } else if (this.orgLevel === "DIV") {
          this.selectedPosition = "nogroup";
          if (this.parentGroup) {
            this.selectedDivision = this.parentGroup;
          } else {
            this.selectedDivision = "nodivision";
          }
        } else if (this.orgLevel === "DEPT") {
          this.selectedPosition = "nogroup";
          this.selectedDivision = "nodivision";
          if (this.parentGroup) {
            this.selectedDepartment = this.parentGroup;
          } else {
            this.selectedDepartment = "nodepartment";
          }
        } else if (this.orgLevel === "SEC") {
          this.selectedPosition = "nogroup";
          this.selectedDivision = "nodivision";
          this.selectedDepartment = "nodepartment";
          if (this.parentGroup) {
            this.selectedSection = this.parentGroup;
          } else {
            this.selectedSection = "nosection";
          }
        }
        this.retrieveCountGroupPosition("mounted");
      } else {
        this.divisionList = [];
        this.departmentList = [];
        this.sectionList = [];
        this.selectedPosition = "nogroup";
        this.setListWithNoFilters(true, true, true);
      }
    },
    retrieveTableOrganization(index = 1) {
      this.listLoading = true;
      let apiOffset = parseInt(index) * this.tableOrgLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      const code = this.selectPositionIdAndRetrieveList();
      this.$apollo
        .query({
          query: TABLE_OF_ORGANIZATION_LIST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            formId: 288,
            postionParentId: String(code),
            limit: this.tableOrgLimitToCallAPI,
            offset: apiOffset,
          },
        })
        .then((res) => {
          this.listLoading = false;
          if (res && res.data && res.data.listForecastPosition) {
            const tempData = res.data.listForecastPosition;
            if (tempData.positionList && tempData.positionList.length > 0) {
              this.originalList = [
                ...this.originalList,
                ...tempData.positionList,
              ];
              this.forecastList = [
                ...this.forecastList,
                ...tempData.positionList,
              ];
              this.apiCallCount = this.apiCallCount + 1;
              if (this.totalApiCount === this.apiCallCount) {
                this.listLoading = false;
                this.prepareOrgView();
              }
            }
          }
        })
        .catch((err) => {
          this.originalList = [];
          this.forecastList = [];
          this.listLoading = false;
          this.handleListError(err, "Table of Organization");
        });
    },
    setListWithNoFilters(
      division = false,
      department = false,
      section = false
    ) {
      if (division)
        this.divisionList.unshift({
          Pos_Name: "No Division",
          Pos_full_Name: "No Division",
          Originalpos_Id: "nodivision",
        });

      if (department)
        this.departmentList.unshift({
          Pos_Name: "No Department",
          Pos_full_Name: "No Department",
          Originalpos_Id: "nodepartment",
        });
      if (section)
        this.sectionList.unshift({
          Pos_Name: "No Section",
          Pos_full_Name: "No Section",
          Originalpos_Id: "nosection",
        });
    },
    prepareOrgView() {
      const orgData = this.originalList;
      let parentGroupData = [];
      let childGroupData = [];
      orgData.forEach((item) => {
        if (
          item.Originalpos_Id === parseInt(this.selectedPosition) ||
          (this.selectedPosition === "nogroup" &&
            item.Originalpos_Id === parseInt(this.divisionCode))
        ) {
          this.parentOrgId = item.Originalpos_Id.toString();
          parentGroupData.push({
            name: item.Pos_Name,
            code: item.Pos_Code,
            id: item.Originalpos_Id,
            parentId: "",
            To_Be_Hired: item.To_Be_Hired,
            Warm_Bodies: item.Warm_Bodies,
            Approved_Position: item.Approved_Position,
          });
        } else if (
          item.Originalpos_Id &&
          item.Parent_Id &&
          item.parentId !== 0
        ) {
          childGroupData.push({
            name: item.Pos_Name,
            code: item.Pos_Code,
            id: item.Originalpos_Id,
            parentId: item.Parent_Id,
            To_Be_Hired: item.To_Be_Hired,
            Parent_Path: item.Parent_Path,
            Warm_Bodies: item.Warm_Bodies,
            Approved_Position: item.Approved_Position,
          });
        }
      });
      const filteredData = childGroupData.filter((item) => {
        const parentPathIds = item.Parent_Path.split(",").filter(
          (id) => id !== "0"
        );
        const allIdsMatch = parentPathIds.every((id) => {
          const idExists = childGroupData.some(
            (obj) => parseInt(obj.parentId) === parseInt(id)
          );
          return idExists;
        });

        return allIdsMatch;
      });
      this.organizationList = [...filteredData, ...parentGroupData];
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    exportReportFile() {
      let itemList = this.forecastList.map((item) => ({
        Pos_Name: item.Pos_Name,
        Pos_Code: item.Pos_Code,
        Job_Title_Code: item.Job_Title_Code,
        Cost_Code: item.Cost_Code,
        Approved_Position: item.Approved_Position,
        Warm_Bodies: item.Warm_Bodies,
        To_Be_Hired: item.To_Be_Hired,
      }));
      let fileName = "Table of Organization";
      let exportHeaders = [
        {
          header: "Position Title",
          key: "Pos_Name",
        },
        {
          header: "Position Code",
          key: "Pos_Code",
        },
        {
          header: "Job Title Code",
          key: "Job_Title_Code",
        },
        { header: "Cost Code", key: "Cost_Code" },
        {
          header: "Approved Position",
          key: "Approved_Position",
        },
        { header: "Warm Bodies", key: "Warm_Bodies" },
        { header: "Approved Vacant Positions", key: "To_Be_Hired" },
      ];
      let exportOptions = {
        fileExportData: itemList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    updateDivision(val) {
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedDivision) {
        this.divisionCode = val;
        this.retrieveCountGroupPosition("division");
      }
    },
    updateDepartment() {
      this.selectedSection = null;
      if (this.selectedDepartment) {
        this.retrieveCountGroupPosition("department");
      }
    },
    updateGroup() {
      this.selectedDivision = null;
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedPosition) {
        if (this.selectedPosition === "nogroup") {
          this.selectedDivision = null;
        } else {
          this.divisionCode = null;
        }
        this.retrieveCountGroupPosition("group");
      }
    },
    retrieveCountGroupPosition(type = "") {
      this.divisionListLoading = true;
      const groupId = this.selectPositionIdAndRetrieveList();
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: this.currentTabItem === "tab-0" ? 291 : 290,
            postionParentId: String(groupId),
            limit: this.jobRequisitionLimitToCallAPI,
            offset: 0,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listDetailsBasedOnGroupCode) {
            if (res.data.listDetailsBasedOnGroupCode.positionDetails) {
              const tempData =
                res.data.listDetailsBasedOnGroupCode.positionDetails;
              if (type === "mounted") {
                this.divisionList = tempData.divisionList || [];
                this.departmentList = tempData.deptList || [];
                this.sectionList = tempData.sectionList || [];
              }
              if (type === "group")
                this.divisionList = tempData.divisionList || [];
              if (type === "division")
                this.departmentList = tempData.deptList || [];
              if (type === "department")
                this.sectionList = tempData.sectionList || [];
              if (
                !this.divisionList.some(
                  (item) => item.Originalpos_Id === "nodivision"
                )
              )
                this.divisionList.unshift({
                  Pos_Name: "No Division",
                  Pos_full_Name: "No Division",
                  Originalpos_Id: "nodivision",
                });

              if (
                !this.departmentList.some(
                  (item) => item.Originalpos_Id === "nodepartment"
                )
              )
                this.departmentList.unshift({
                  Pos_Name: "No Department",
                  Pos_full_Name: "No Department",
                  Originalpos_Id: "nodepartment",
                });
              if (
                !this.sectionList.some(
                  (item) => item.Originalpos_Id === "nosection"
                )
              )
                this.sectionList.unshift({
                  Pos_Name: "No Section",
                  Pos_full_Name: "No Section",
                  Originalpos_Id: "nosection",
                });
            } else {
              this.divisionList = [];
              this.departmentList = [];
              this.sectionList = [];
            }
            let { totalRecords } = res.data.listDetailsBasedOnGroupCode;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalRecords / this.jobRequisitionLimitToCallAPI
              );
              if (this.totalApiCount > 1) {
                for (let i = 1; i < this.totalApiCount; i++) {
                  this.updateGroupPosition(i, String(groupId), type);
                }
              }
            }
            this.divisionListLoading = false;
          } else {
            this.divisionListLoading = false;
          }
        })
        .catch((err) => {
          this.handleRetrieveError(err);
          this.divisionList = [];
          this.departmentList = [];
          this.sectionList = [];
          this.divisionListLoading = false;
        });
    },
    updateGroupPosition(index = 1, groupId = "", type = "") {
      this.divisionListLoading = true;
      let apiOffset = parseInt(index) * this.jobRequisitionLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.isFormDirty = true;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: this.currentTabItem === "tab-0" ? 291 : 290,
            postionParentId: String(groupId),
            limit: this.jobRequisitionLimitToCallAPI,
            offset: apiOffset,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listDetailsBasedOnGroupCode &&
            res.data.listDetailsBasedOnGroupCode.positionDetails
          ) {
            const tempData =
              res.data.listDetailsBasedOnGroupCode.positionDetails;
            if (
              tempData.divisionList &&
              tempData.divisionList.length > 0 &&
              (type === "group" || type === "mounted")
            ) {
              this.divisionList = [
                ...this.divisionList,
                ...tempData.divisionList,
              ];
            }

            if (
              tempData.deptList &&
              tempData.deptList.length > 0 &&
              (type === "division" || type === "mounted")
            ) {
              this.departmentList = [
                ...this.departmentList,
                ...tempData.deptList,
              ];
            }

            if (
              tempData.sectionList &&
              tempData.sectionList.length > 0 &&
              (type === "department" || type === "mounted")
            ) {
              this.sectionList = [...this.sectionList, ...tempData.sectionList];
            }
            this.apiCallCount = this.apiCallCount + 1;
            if (this.totalApiCount === this.apiCallCount) {
              this.divisionListLoading = false;
            }
          } else {
            this.divisionListLoading = false;
          }
        })
        .catch((err) => {
          this.handleRetrieveError(err);
          this.divisionList = [];
          this.departmentList = [];
          this.sectionList = [];
          this.divisionListLoading = false;
        });
    },
    handleRetrieveError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "man power planning",
        isListError: false,
      });
    },
  },
};
</script>
<style scoped>
.table-organization-container {
  padding: 5em 2em 0em 3em;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .table-organization-container {
    padding: 4em 1em 0em 1em;
  }
}

@media screen and (max-width: 960px) {
  .org-chart-view-btn {
    display: none !important;
  }
}
</style>
