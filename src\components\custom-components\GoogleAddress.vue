<template>
  <v-container class="pa-0 ma-0">
    <v-row justify="center">
      <v-col cols="12">
        <v-autocomplete
          :model-value="address"
          variant="solo"
          :items="predictions"
          item-title="description"
          item-value="description"
          :rules="rules"
          :loading="loading"
          clearable
          no-filter
          hide-no-data
          @update:search="debouncedGetPredictions"
          @update:modelValue="updateValue"
          @blur="menu = false"
          @change="handleSelection"
        >
          <template v-slot:item="{ item, props }">
            <div class="py-1 px-2" v-bind="props">
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <div
                    v-bind="props"
                    class="pa-3 rounded-lg text-truncate cursor-pointer"
                    :class="
                      isHovering
                        ? 'bg-hover'
                        : modelValue == item.value
                        ? 'bg-primary'
                        : 'bg-grey-lighten-4'
                    "
                  >
                    <v-list-item>
                      {{ item.title }}
                    </v-list-item>
                  </div>
                </template>
              </v-hover>
            </div>
          </template>
          <template v-slot:label>
            <span>{{ label }}</span>
            <span v-if="isRequired" class="ml-1" style="color: red">*</span>
          </template>
        </v-autocomplete>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: "Select Address",
    },
    isRequired: {
      type: Boolean,
      default: false,
    },
    rules: {
      type: Array,
      default: () => [],
    },
    modelValue: {
      type: String,
      default: "",
    },
    types: {
      type: Array,
      default: () => ["(cities)"],
    },
  },
  data() {
    return {
      address: this.modelValue, // Use prop value initially
      predictions: [],
      loading: false,
      selectedAddress: "",
      autocompleteService: null,
      placesService: null,
      sessionToken: null,
    };
  },
  watch: {
    modelValue(newValue) {
      this.address = newValue; // Sync changes from parent
    },
  },
  methods: {
    updateValue(value) {
      this.address = value;
      this.$emit("update:modelValue", value); // Emit change to parent
    },
    initGoogleMapsServices() {
      if (!window.google || !window.google.maps) return;
      this.autocompleteService =
        new window.google.maps.places.AutocompleteService();
      this.placesService = new window.google.maps.places.PlacesService(
        document.createElement("div")
      );
      this.createSessionToken();
    },
    createSessionToken() {
      if (window.google && window.google.maps) {
        this.sessionToken =
          new window.google.maps.places.AutocompleteSessionToken();
      }
    },
    getPredictions() {
      if (!this.autocompleteService || !this.address?.trim()) {
        this.predictions = [];
        return;
      }
      this.loading = true;
      const request = {
        input: this.address,
        sessionToken: this.sessionToken,
        types: this.types,
      };
      this.autocompleteService.getPlacePredictions(
        request,
        (results, status) => {
          this.loading = false;
          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            results
          ) {
            this.predictions = results;
          } else {
            this.predictions = [];
          }
        }
      );
    },
    debouncedGetPredictions(value) {
      this.address = value;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.getPredictions();
      }, 300);
    },
    handleSelection(value) {
      const selectedPrediction = this.predictions.find(
        (p) => p.description === value
      );
      if (!selectedPrediction || !this.placesService) return;

      this.loading = true;
      this.placesService.getDetails(
        {
          placeId: selectedPrediction.place_id,
          fields: ["formatted_address"],
          sessionToken: this.sessionToken,
        },
        (place, status) => {
          this.loading = false;
          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            place
          ) {
            this.address = place.formatted_address;
            this.$emit("update:modelValue", place.formatted_address); // Emit to parent
            this.selectedAddress = place.formatted_address;
            this.predictions = [];
            this.createSessionToken();
          }
        }
      );
    },
  },
  mounted() {
    this.initGoogleMapsServices();
  },
};
</script>
