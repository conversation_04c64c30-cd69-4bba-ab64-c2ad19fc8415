import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const GET_LEAVE_SETTINGS = gql`
  query retrieveLeaveSettings($formName: String, $formId: Int) {
    retrieveLeaveSettings(formName: $formName, formId: $formId) {
      errorCode
      message
      leaveSettings {
        Allow_Upline_Managers_Approval
        Enable_CAMU_Scheduler
        Enable_Workflow
        Enforce_Comment_For_Approval
        Enforce_Comment_For_Leave
        Enforce_Alternate_Person_For_Leave
        Coverage_For_Alternate_Person
        Updated_On
        updatedByName
      }
    }
  }
`;
