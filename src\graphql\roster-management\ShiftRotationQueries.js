import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_SHIFT_ROTATION = gql`
  query listShiftRotation {
    listShiftRotation {
      errorCode
      message
      shiftRotation
    }
  }
`;

export const LIST_SHIFT_LEAVE_TYPES = gql`
  query listShiftLeaveTypes {
    listShiftLeaveTypes {
      errorCode
      message
      shiftLeaveTypes
    }
  }
`;

export const LIST_SHIFT_TYPE = gql`
  query shiftQuery(
    $sortField: Int
    $sortOrder: String
    $searchString: String
    $shiftName: String
    $minCount: Int
    $maxCount: Int
    $isDropDown: Int
  ) {
    listShiftType(
      sortField: $sortField
      sortOrder: $sortOrder
      searchString: $searchString
      shiftName: $shiftName
      minCount: $minCount
      maxCount: $maxCount
      isDropDown: $isDropDown
    ) {
      errorCode
      success
      message
      shiftType {
        Shift_Id
        Shift_Name
        WorkSchedule_Id
        Minimum_Employee_Count
        Maximum_Employee_Count
        Holiday_Override
        Comments
        Colour_Code
        Added_By
        Added_On
        Status
        Updated_By
        Updated_On
      }
      rosterSettings {
        Dynamic_Week_Off
        Overlap_Shift_Schedule
      }
    }
  }
`;

// ===============
// Mutations
// ===============

export const ADD_UPDATE_SHIFT_ROTATION = gql`
  mutation addUpdateShiftRotation(
    $Rotation_Id: Int
    $Scheduler_Name: String!
    $Repeat_Schedule: YesNoOption!
    $Repeat_Count: Int!
    $Enable_Roster_Leave: YesNoOption!
    $Leave_Entitlement_Per_Roster_Day: Float
    $Leave_Replenishment_Period: Leave_Replenishment_Period
    $LeaveType_Id: Int
    $Shift_Rotation_Schedule: [Shift_Rotation_Schedule]
  ) {
    addUpdateShiftRotation(
      Rotation_Id: $Rotation_Id
      Scheduler_Name: $Scheduler_Name
      Repeat_Schedule: $Repeat_Schedule
      Repeat_Count: $Repeat_Count
      Enable_Roster_Leave: $Enable_Roster_Leave
      Leave_Entitlement_Per_Roster_Day: $Leave_Entitlement_Per_Roster_Day
      Leave_Replenishment_Period: $Leave_Replenishment_Period
      LeaveType_Id: $LeaveType_Id
      Shift_Rotation_Schedule: $Shift_Rotation_Schedule
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_SHIFT_ROTATION = gql`
  mutation deleteShiftRotation($Rotation_Id: Int!) {
    deleteShiftRotation(Rotation_Id: $Rotation_Id) {
      errorCode
      message
    }
  }
`;
