<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs('error')"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else>
    <div class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs('refresh')" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <BankDetails
      :formAccess="formAccess"
      :bankDetailsData="bankDetails"
      :selectedCandidateId="selectedCandidateId"
      @refetch-other-details="refetchAPIs('update')"
    />
    <Insurance
      :insuranceDetailsData="insuranceDetails"
      :formAccess="formAccess"
      :selectedCandidateId="selectedCandidateId"
      @refetch-other-details="refetchAPIs('update')"
    />
    <DynamicCustomFields
      :custom-form-name="formName"
      :form-id="178"
      :primary-id="selectedCandidateId"
      :form-access="formAccess"
    />
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import Insurance from "./insurance/Insurance.vue";
import BankDetails from "./bank/BankDetails.vue";
import { RETRIEVE_EMP_OTHER_INFO } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import DynamicCustomFields from "@/components/custom-components/DynamicCustomFields.vue";
export default {
  name: "DocumentAndAccreditation",
  components: {
    BankDetails,
    Insurance,
    DynamicCustomFields,
  },
  props: {
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  emits: ["details-retrieved"],
  data: () => ({
    bankDetails: [],
    insuranceDetails: [],
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    bankInsuranceStep: 0,
    isLoading: false,
  }),
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    formName() {
      let formName = this.accessIdRights("178");
      if (formName?.customFormName && formName.customFormName !== "") {
        return [formName.customFormName];
      } else return ["Invited Individuals"];
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.getOtherDetails();
  },
  methods: {
    refetchAPIs(type) {
      this.isErrorInList = false;
      mixpanel.track("Onboarded-candidate-other-refetch");
      this.getOtherDetails(type);
    },
    getOtherDetails(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_OTHER_INFO,
          client: "apolloClientV",
          variables: {
            candidateId: vm.selectedCandidateId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("Onboarded-candidate-other-fetch-success");
          if (response && response.data && response.data.retrieveOtherInfo) {
            const { bankDetails, insuranceDetails } =
              response.data.retrieveOtherInfo;
            vm.bankDetails = bankDetails ? JSON.parse(bankDetails) : [];
            vm.insuranceDetails = insuranceDetails
              ? JSON.parse(insuranceDetails)
              : [];
            vm.$emit("details-retrieved", type);
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("Onboarded-candidate-other-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "other details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
