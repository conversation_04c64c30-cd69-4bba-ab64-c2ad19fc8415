<template>
  <div v-if="isMounted" class="pb-6">
    <v-form ref="contactInfoEditForm">
      <div>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Permanent Address</span
        >
        <v-row class="pa-2">
          <v-col v-if="!isManualEnterForPermanentAddress" cols="12">
            <VCard>
              <vue-google-autocomplete
                id="pMap"
                ref="pGoogleVal"
                classname="form-control pa-5 google-auto-complete-address-field"
                placeholder="Please type your address"
                v-on:placechanged="setPermanentAddress"
                :enable-geolocation="true"
                :fields="['address_components', 'geometry']"
              >
              </vue-google-autocomplete>
            </VCard>
          </v-col>
          <v-col cols="12">
            <div>
              <v-checkbox-btn
                v-model="isManualEnterForPermanentAddress"
                color="primary"
                false-icon="far fa-square"
                true-icon="fas fa-check-square"
                indeterminate-icon="fas fa-minus-circle"
                class="mt-1"
                @change="onChangeFields"
              >
                <template v-slot:label>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    No autocomplete address found
                  </div>
                </template>
              </v-checkbox-btn>
            </div>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <formTextFeild
              :ref="'pApartment_Name'"
              v-model="editedContactDetails.pApartment_Name"
              :rules="[
                required(
                  labelList[233].Field_Alias,
                  editedContactDetails.pApartment_Name
                ),
                validateWithRulesAndReturnMessages(
                  editedContactDetails.pApartment_Name,
                  'street1',
                  labelList[233].Field_Alias
                ),
              ]"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[233].Field_Alias }}
                <span style="color: red">*</span>
              </template>
            </formTextFeild>
          </v-col>

          <v-col cols="12" md="4" sm="6">
            <formTextFeild
              :ref="'pStreet_Name'"
              v-model="editedContactDetails.pStreet_Name"
              :rules="
                checkFieldAvailability(editedContactDetails.pStreet_Name)
                  ? [
                      validateWithRulesAndReturnMessages(
                        editedContactDetails.pStreet_Name,
                        'street',
                        'Street 2'
                      ),
                    ]
                  : [true]
              "
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              label="Street 2"
              @input="fillCurrentAddressBasedOnPermanentAddress"
            ></formTextFeild>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[340]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <CustomSelect
              v-if="labelList[340].Predefined.toLowerCase() === 'yes'"
              :items="pBarangayList"
              v-model="editedContactDetails.pBarangay_Id"
              :label="labelList[340].Field_Alias"
              item-title="barangayDetails"
              item-value="Barangay_Id"
              :isLoading="isPBarangayLoading"
              :isAutoComplete="true"
              :isRequired="labelList[340].Mandatory_Field == 'Yes'"
              placeholder="Type minimum 3 characters to list"
              :noDataText="noDataTextForPBarangay"
              :rules="[
                labelList[340].Mandatory_Field == 'Yes'
                  ? required(
                      `${labelList[340].Field_Alias}`,
                      editedContactDetails.pBarangay_Id
                    )
                  : true,
              ]"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              @input="onChangeFields"
              :itemSelected="
                editedContactDetails.pBarangay_Id
                  ? editedContactDetails.pBarangay_Id
                  : ''
              "
              @selected-item="onChangeCustomSelectField($event, 'pBarangay_Id')"
              @update-search-value="callPBarangayList($event)"
              @update:model-value="onUpdatePBarangay($event)"
            ></CustomSelect>
            <formTextFeild
              v-else
              :ref="'pBarangay'"
              v-model="editedContactDetails.pBarangay"
              :rules="[
                labelList[340]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[340].Field_Alias,
                      editedContactDetails.pBarangay
                    )
                  : [true],
              ]"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[340].Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[340]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <CustomSelect
              v-if="labelList[137]?.Predefined.toLowerCase() === 'yes'"
              :items="cityList"
              label="City"
              :isAutoComplete="true"
              :isLoading="cityListLoading"
              :noDataText="cityListLoading ? 'Loading...' : 'No data available'"
              :itemSelected="
                editedContactDetails.pCity_Id
                  ? editedContactDetails.pCity_Id
                  : ''
              "
              itemValue="City_Id"
              itemTitle="cityStateDetails"
              :isRequired="true"
              :rules="[required('City', editedContactDetails.pCity_Id)]"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              ref="pCity"
              @selected-item="onChangeCustomSelectField($event, 'pCity_Id')"
            ></CustomSelect>
            <v-text-field
              v-else
              v-model="editedContactDetails.pCity"
              :rules="[
                required('City', editedContactDetails.pCity),
                editedContactDetails.pCity
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.pCity,
                      'city',
                      'City'
                    )
                  : true,
              ]"
              variant="solo"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              :ref="'pCityText'"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                <span> City </span>
                <span class="ml-1" style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <formTextFeild
              :ref="'pState'"
              v-model="editedContactDetails.pState"
              :rules="[
                required(
                  labelList[402]?.Field_Alias,
                  editedContactDetails.pState
                ),
                validateWithRulesAndReturnMessages(
                  editedContactDetails.pState,
                  'state',
                  labelList[402]?.Field_Alias
                ),
              ]"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[402]?.Field_Alias
                }}<span style="color: red">*</span>
              </template></formTextFeild
            >
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[341]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <formTextFeild
              :ref="'pRegion'"
              v-model="editedContactDetails.pRegion"
              :rules="[
                labelList[341]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[341].Field_Alias,
                      editedContactDetails.pRegion
                    )
                  : [true],
              ]"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[341].Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[341]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <CustomSelect
              :items="countryList"
              label="Country"
              :isAutoComplete="true"
              :isLoading="countryListLoading"
              :noDataText="
                countryListLoading ? 'Loading...' : 'No data available'
              "
              :itemSelected="
                editedContactDetails.pCountry
                  ? editedContactDetails.pCountry
                  : ''
              "
              itemValue="Country_Code"
              itemTitle="Country_Name"
              :isRequired="true"
              :rules="[required('Country', editedContactDetails.pCountry)]"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              ref="pCountry"
              @selected-item="onChangeCustomSelectField($event, 'pCountry')"
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[144].Field_Visiblity === 'Yes'"
          >
            <formTextFeild
              :ref="'pZipCode'"
              v-model="editedContactDetails.pPincode"
              :rules="[
                labelList[144].Mandatory_Field === 'Yes'
                  ? required(
                      labelList[144].Field_Alias,
                      editedContactDetails.pPincode
                    )
                  : true,
                editedContactDetails.pPincode
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.pPincode,
                      'pinCode',
                      labelList[144].Field_Alias
                    )
                  : true,
              ]"
              :disabled="
                !isManualEnterForPermanentAddress &&
                !enableEditAutoPermanentAddress
              "
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[144].Field_Alias
                }}<span
                  style="color: red"
                  v-if="labelList[144].Mandatory_Field === 'Yes'"
                  >*</span
                >
              </template></formTextFeild
            >
          </v-col>
        </v-row>
      </div>
      <v-divider></v-divider>
      <div class="mt-4">
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Current Address</span
        >
        <v-row class="pa-2">
          <v-col cols="12">
            <div class="d-flex gap align-center">
              <div class="text-grey-darken-1 pr-3" style="font-size: 16px">
                Same as above address?
              </div>
              <switchButton
                v-model="sameAsPermanent"
                class="d-flex gap align-center"
                color="primary"
                @change="onChangeFields"
              ></switchButton></div
          ></v-col>
          <v-col
            v-if="!sameAsPermanent && !isManualEnterForCurrentAddress"
            cols="12"
          >
            <VCard>
              <vue-google-autocomplete
                ref="cGoogleVal"
                id="cMap"
                classname="form-control pa-5 google-auto-complete-address-field"
                style="width: 100%; border: 1px solid #e0e0e0"
                placeholder="Please type your address"
                v-on:placechanged="setCurrentAddress"
                :enable-geolocation="true"
                :fields="['address_components', 'geometry']"
              >
              </vue-google-autocomplete>
            </VCard>
            <v-col v-if="!sameAsPermanent" cols="12">
              <v-checkbox-btn
                v-model="isManualEnterForCurrentAddress"
                color="primary"
                false-icon="far fa-square"
                true-icon="fas fa-check-square"
                indeterminate-icon="fas fa-minus-circle"
                class="mt-1"
                @change="onChangeFields"
              >
                <template v-slot:label>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    No autocomplete address found
                  </div>
                </template>
              </v-checkbox-btn>
            </v-col>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[233].Field_Visiblity == 'Yes'"
          >
            <formTextFeild
              :ref="'cApartmentName'"
              v-model="editedContactDetails.cApartment_Name"
              :rules="[
                labelList[233].Mandatory_Field == 'Yes'
                  ? required(
                      labelList[233].Field_Alias,
                      editedContactDetails.cApartment_Name
                    )
                  : true,
                validateWithRulesAndReturnMessages(
                  editedContactDetails.cApartment_Name,
                  'street1',
                  labelList[233].Field_Alias
                ),
              ]"
              :disabled="isDisableCurrentAddressFields"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[233].Field_Alias
                }}<span style="color: red">*</span>
              </template></formTextFeild
            >
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <formTextFeild
              :ref="'cStreet_Name'"
              v-model="editedContactDetails.cStreet_Name"
              :rules="
                checkFieldAvailability(editedContactDetails.cStreet_Name)
                  ? [
                      validateWithRulesAndReturnMessages(
                        editedContactDetails.cStreet_Name,
                        'street',
                        'Street 2'
                      ),
                    ]
                  : [true]
              "
              :disabled="isDisableCurrentAddressFields"
              label="Street 2"
              @input="onChangeFields"
            ></formTextFeild>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[342]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <CustomSelect
              v-if="labelList[342].Predefined.toLowerCase() === 'yes'"
              :items="cBarangayList"
              v-model="editedContactDetails.cBarangay_Id"
              :label="labelList[342].Field_Alias"
              item-title="barangayDetails"
              item-value="Barangay_Id"
              :isLoading="isCBarangayLoading"
              :isAutoComplete="true"
              :isRequired="labelList[342].Mandatory_Field == 'Yes'"
              placeholder="Type minimum 3 characters to list"
              :noDataText="noDataTextForCBarangay"
              :rules="[
                labelList[342].Mandatory_Field == 'Yes'
                  ? required(
                      `${labelList[342].Field_Alias}`,
                      editedContactDetails.cBarangay_Id
                    )
                  : true,
              ]"
              :disabled="isDisableCurrentAddressFields"
              @input="onChangeFields"
              :itemSelected="
                editedContactDetails.cBarangay_Id
                  ? editedContactDetails.cBarangay_Id
                  : ''
              "
              @selected-item="onChangeCustomSelectField($event, 'cBarangay_Id')"
              @update-search-value="callCBarangayList($event)"
              @update:model-value="onUpdateCBarangay($event)"
            ></CustomSelect>
            <formTextFeild
              v-else
              :ref="'cBarangay'"
              v-model="editedContactDetails.cBarangay"
              :rules="[
                labelList[342]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[342].Field_Alias,
                      editedContactDetails.cBarangay
                    )
                  : [true],
              ]"
              :disabled="isDisableCurrentAddressFields"
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[342].Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[342]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <CustomSelect
              v-if="labelList[137]?.Predefined.toLowerCase() === 'yes'"
              :items="cityList"
              label="City"
              :isAutoComplete="true"
              :isLoading="cityListLoading"
              :noDataText="cityListLoading ? 'Loading...' : 'No data available'"
              :itemSelected="
                editedContactDetails.cCity_Id
                  ? editedContactDetails.cCity_Id
                  : ''
              "
              itemValue="City_Id"
              itemTitle="cityStateDetails"
              :isRequired="true"
              :rules="[required('City', editedContactDetails.cCity_Id)]"
              :disabled="isDisableCurrentAddressFields"
              ref="cCity"
              @selected-item="onChangeCustomSelectField($event, 'cCity_Id')"
            ></CustomSelect>
            <v-text-field
              v-else
              v-model="editedContactDetails.cCity"
              :rules="[
                required('City', editedContactDetails.cCity),
                editedContactDetails.cCity
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.cCity,
                      'city',
                      'City'
                    )
                  : true,
              ]"
              variant="solo"
              :disabled="isDisableCurrentAddressFields"
              :ref="'cCityText'"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                <span> City </span>
                <span class="ml-1" style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <formTextFeild
              :ref="'cState'"
              v-model="editedContactDetails.cState"
              :rules="[
                required(
                  labelList[402]?.Field_Alias,
                  editedContactDetails.cState
                ),
                validateWithRulesAndReturnMessages(
                  editedContactDetails.cState,
                  'state',
                  labelList[402]?.Field_Alias
                ),
              ]"
              :disabled="isDisableCurrentAddressFields"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[402]?.Field_Alias
                }}<span style="color: red">*</span>
              </template></formTextFeild
            >
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[343]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <formTextFeild
              :ref="'cRegion'"
              v-model="editedContactDetails.cRegion"
              :rules="[
                labelList[343]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[343].Field_Alias,
                      editedContactDetails.cRegion
                    )
                  : [true],
              ]"
              :disabled="isDisableCurrentAddressFields"
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[343].Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[343]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <CustomSelect
              :items="countryList"
              label="Country"
              :isAutoComplete="true"
              :isLoading="countryListLoading"
              :noDataText="
                countryListLoading ? 'Loading...' : 'No data available'
              "
              :itemSelected="
                editedContactDetails.cCountry
                  ? editedContactDetails.cCountry
                  : ''
              "
              itemValue="Country_Code"
              itemTitle="Country_Name"
              :isRequired="true"
              :disabled="isDisableCurrentAddressFields"
              :rules="[required('Country', editedContactDetails.cCountry)]"
              ref="cCountry"
              @selected-item="onChangeCustomSelectField($event, 'cCountry')"
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[144].Field_Visiblity === 'Yes'"
          >
            <formTextFeild
              :ref="'cPincode'"
              v-model="editedContactDetails.cPincode"
              :disabled="isDisableCurrentAddressFields"
              :rules="[
                labelList[144].Mandatory_Field === 'Yes'
                  ? required(
                      labelList[144].Field_Alias,
                      editedContactDetails.cPincode
                    )
                  : true,
                editedContactDetails.cPincode
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.cPincode,
                      'pinCode',
                      labelList[144].Field_Alias
                    )
                  : true,
              ]"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[144].Field_Alias
                }}<span
                  style="color: red"
                  v-if="labelList[144].Mandatory_Field === 'Yes'"
                  >*</span
                >
              </template></formTextFeild
            >
          </v-col>
        </v-row>
      </div>
      <v-divider></v-divider>
      <div class="mt-4">
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Office Address</span
        >
        <v-row class="pa-2">
          <v-col cols="12">
            <div class="d-flex gap align-center">
              <div class="text-grey-darken-1 pr-3" style="font-size: 16px">
                Same as location address?
              </div>
              <switchButton
                v-model="editedContactDetails.Use_Location_Address"
                class="d-flex gap align-center"
                color="primary"
                :true-value="1"
                :false-value="0"
                @change="onChangeFields"
              ></switchButton></div
          ></v-col>
          <v-col
            v-if="
              !editedContactDetails.Use_Location_Address &&
              !isManualEnterForOfficeAddress
            "
            cols="12"
          >
            <VCard>
              <vue-google-autocomplete
                id="oMap"
                ref="oGoogleVal"
                classname="form-control pa-5 google-auto-complete-address-field"
                placeholder="Please type your address"
                v-on:placechanged="setOfficeAddress"
                :enable-geolocation="true"
                :fields="['address_components', 'geometry']"
              >
              </vue-google-autocomplete>
            </VCard>
          </v-col>
          <v-col v-if="!editedContactDetails.Use_Location_Address" cols="12">
            <div>
              <v-checkbox-btn
                v-model="isManualEnterForOfficeAddress"
                color="primary"
                false-icon="far fa-square"
                true-icon="fas fa-check-square"
                indeterminate-icon="fas fa-minus-circle"
                class="mt-1"
                @change="onChangeFields"
              >
                <template v-slot:label>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    No autocomplete address found
                  </div>
                </template>
              </v-checkbox-btn>
            </div>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[233].Field_Visiblity == 'Yes'"
          >
            <formTextFeild
              :ref="'oApartmentName'"
              v-model="editedContactDetails.oApartment_Name"
              :rules="[
                labelList[233].Mandatory_Field == 'Yes'
                  ? required(
                      labelList[233].Field_Alias,
                      editedContactDetails.oApartment_Name
                    )
                  : true,
                validateWithRulesAndReturnMessages(
                  editedContactDetails.oApartment_Name,
                  'street1',
                  labelList[233].Field_Alias
                ),
              ]"
              :disabled="isDisableOfficeAddressFields"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[233].Field_Alias
                }}<span style="color: red">*</span>
              </template></formTextFeild
            >
          </v-col>

          <v-col cols="12" md="4" sm="6">
            <formTextFeild
              :ref="'oStreet_Name'"
              v-model="editedContactDetails.oStreet_Name"
              label="Street 2"
              :rules="[
                checkFieldAvailability(editedContactDetails.oStreet_Name)
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.oStreet_Name,
                      'street',
                      'Street 2'
                    )
                  : true,
              ]"
              :disabled="isDisableOfficeAddressFields"
            ></formTextFeild>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[344]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <CustomSelect
              v-if="labelList[344].Predefined.toLowerCase() === 'yes'"
              :items="oBarangayList"
              v-model="editedContactDetails.oBarangay_Id"
              :label="labelList[344].Field_Alias"
              item-title="barangayDetails"
              item-value="Barangay_Id"
              :isLoading="isOBarangayLoading"
              :isAutoComplete="true"
              :isRequired="labelList[344].Mandatory_Field == 'Yes'"
              :rules="[
                labelList[344].Mandatory_Field == 'Yes'
                  ? required(
                      `${labelList[344].Field_Alias}`,
                      editedContactDetails.oBarangay_Id
                    )
                  : true,
              ]"
              :disabled="isDisableOfficeAddressFields"
              @input="onChangeFields"
              placeholder="Type minimum 3 characters to list"
              :noDataText="noDataTextForOBarangay"
              :itemSelected="
                editedContactDetails.oBarangay_Id
                  ? editedContactDetails.oBarangay_Id
                  : ''
              "
              @selected-item="onChangeCustomSelectField($event, 'oBarangay_Id')"
              @update-search-value="callOBarangayList($event)"
              @update:model-value="onUpdateOBarangay($event)"
            ></CustomSelect>
            <formTextFeild
              v-else
              :ref="'oBarangay'"
              v-model="editedContactDetails.oBarangay"
              :rules="[
                labelList[344]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[344].Field_Alias,
                      editedContactDetails.oBarangay
                    )
                  : true,
              ]"
              :disabled="isDisableOfficeAddressFields"
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[344].Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[344]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <CustomSelect
              v-if="labelList[138]?.Predefined.toLowerCase() === 'yes'"
              :items="cityList"
              label="City"
              :isAutoComplete="true"
              :isLoading="cityListLoading"
              :noDataText="cityListLoading ? 'Loading...' : 'No data available'"
              :itemSelected="
                editedContactDetails.oCity_Id
                  ? editedContactDetails.oCity_Id
                  : ''
              "
              itemValue="City_Id"
              itemTitle="cityStateDetails"
              :isRequired="true"
              :rules="[required('City', editedContactDetails.oCity_Id)]"
              :disabled="isDisableOfficeAddressFields"
              ref="oCity"
              @selected-item="onChangeCustomSelectField($event, 'oCity_Id')"
            ></CustomSelect>
            <v-text-field
              v-else
              v-model="editedContactDetails.oCity"
              :rules="[
                required('City', editedContactDetails.oCity),
                editedContactDetails.oCity
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.oCity,
                      'city',
                      'City'
                    )
                  : true,
              ]"
              variant="solo"
              :disabled="isDisableOfficeAddressFields"
              :ref="'oCityText'"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                <span> City </span>
                <span class="ml-1" style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <formTextFeild
              :ref="'oState'"
              v-model="editedContactDetails.oState"
              :rules="[
                required(
                  labelList[402]?.Field_Alias,
                  editedContactDetails.oState
                ),
                validateWithRulesAndReturnMessages(
                  editedContactDetails.oState,
                  'state',
                  labelList[402]?.Field_Alias
                ),
              ]"
              :disabled="isDisableOfficeAddressFields"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[402]?.Field_Alias
                }}<span style="color: red">*</span>
              </template></formTextFeild
            >
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[345]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <formTextFeild
              :ref="'oRegion'"
              v-model="editedContactDetails.oRegion"
              :rules="[
                labelList[345]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[345].Field_Alias,
                      editedContactDetails.oRegion
                    )
                  : [true],
              ]"
              :disabled="isDisableOfficeAddressFields"
              @input="fillCurrentAddressBasedOnPermanentAddress"
            >
              <template v-slot:label>
                {{ labelList[345].Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[345]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <CustomSelect
              :items="countryList"
              label="Country"
              :isAutoComplete="true"
              :isLoading="countryListLoading"
              :noDataText="
                countryListLoading ? 'Loading...' : 'No data available'
              "
              :itemSelected="
                editedContactDetails.oCountry
                  ? editedContactDetails.oCountry
                  : ''
              "
              itemValue="Country_Code"
              itemTitle="Country_Name"
              :isRequired="true"
              :disabled="isDisableOfficeAddressFields"
              :rules="[required('Country', editedContactDetails.oCountry)]"
              ref="oCountry"
              @selected-item="onChangeCustomSelectField($event, 'oCountry')"
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[145].Field_Visiblity === 'Yes'"
          >
            <formTextFeild
              :ref="'oPincode'"
              v-model="editedContactDetails.oPincode"
              :rules="[
                labelList[145].Mandatory_Field === 'Yes'
                  ? required(
                      labelList[145].Field_Alias,
                      editedContactDetails.oPincode
                    )
                  : true,
                editedContactDetails.oPincode
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.oPincode,
                      'pinCode',
                      labelList[145].Field_Alias
                    )
                  : true,
              ]"
              :disabled="isDisableOfficeAddressFields"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[145].Field_Alias
                }}<span
                  style="color: red"
                  v-if="labelList[145].Mandatory_Field === 'Yes'"
                  >*</span
                >
              </template></formTextFeild
            >
          </v-col>
        </v-row>
      </div>
      <v-divider></v-divider>
      <div class="mt-4 mb-6">
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Contact Information</span
        >
        <v-row class="pa-2 mt-2">
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <div class="custom-label mt-n4">
              Mobile Number<span style="color: red">*</span>
            </div>
            <VueTelInput
              class="pa-2"
              v-model="editedContactDetails.Mobile_No"
              :preferred-countries="['IN', 'US', 'AU']"
              :error="!mobileNumberValidation"
              error-color="#E53935"
              valid-color="#9E9E9E"
              :defaultCountry="mobileNoCountryCode"
              :autoDefaultCountry="false"
              mode="national"
              @country-changed="getCountryCode($event)"
              @validate="validateMobileNumber"
              :valid-characters-only="true"
              ref="mobileNo"
            ></VueTelInput>
            <span
              :class="
                mobileNumberValidation
                  ? 'text-red caption mt-1'
                  : 'text-green caption mt-1'
              "
              >{{ mobileNumberValidation }}</span
            >
          </v-col>
          <v-col cols="12" sm="12" lg="4" md="4" xl="4">
            <formTextFeild
              variant="solo"
              :ref="'telephoneNo'"
              v-model="editedContactDetails.Land_Line_No"
              :rules="
                checkFieldAvailability(editedContactDetails.Land_Line_No)
                  ? [
                      minLengthValidation(
                        'Telephone Number',
                        editedContactDetails.Land_Line_No,
                        6
                      ),
                    ]
                  : []
              "
              :counter="15"
              :maxlength="15"
              label="Telephone Number"
              @input="onChangeFields"
            ></formTextFeild>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[436]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <formTextFeild
              variant="solo"
              :ref="'EmergencyContactNo'"
              v-model="editedContactDetails.Fax_No"
              :rules="[
                checkFieldAvailability(editedContactDetails.Fax_No)
                  ? minLengthValidation(
                      labelList[436]?.Field_Alias,
                      editedContactDetails.Fax_No,
                      6
                    )
                  : true,
                labelList[436]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[436]?.Field_Alias,
                      editedContactDetails.Fax_No
                    )
                  : true,
              ]"
              :counter="15"
              :maxlength="15"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[436]?.Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[436]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[435]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <formTextFeild
              variant="solo"
              :ref="'emergencyContactName'"
              v-model="editedContactDetails.Emergency_Contact_Name"
              :rules="[
                checkFieldAvailability(
                  editedContactDetails.Emergency_Contact_Name
                )
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.Emergency_Contact_Name,
                      'empFirstName',
                      labelList[435]?.Field_Alias
                    )
                  : true,
                labelList[435]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[435]?.Field_Alias,
                      editedContactDetails.Emergency_Contact_Name
                    )
                  : true,
              ]"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[435]?.Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[435]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            lg="4"
            md="4"
            xl="4"
            v-if="labelList[437]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <formTextFeild
              variant="solo"
              :ref="'emergencyContactRelation'"
              v-model="editedContactDetails.Emergency_Contact_Relation"
              :rules="[
                checkFieldAvailability(
                  editedContactDetails.Emergency_Contact_Relation
                )
                  ? validateWithRulesAndReturnMessages(
                      editedContactDetails.Emergency_Contact_Relation,
                      'empFirstName',
                      labelList[437]?.Field_Alias
                    )
                  : true,
                labelList[437]?.Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[437]?.Field_Alias,
                      editedContactDetails.Emergency_Contact_Relation
                    )
                  : true,
              ]"
              @input="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList[437]?.Field_Alias }}
                <span
                  style="color: red"
                  v-if="
                    labelList[437]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  >*</span
                >
              </template>
            </formTextFeild>
          </v-col>
        </v-row>
      </div>
    </v-form>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>
<script>
import VueGoogleAutocomplete from "vue-google-autocomplete";
import validationRules from "@/mixins/validationRules";
import { VueTelInput } from "vue-tel-input";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_UPDATE_CONTACT_DETAILS,
  VALIDATE_MOBILE_NUMBER,
} from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import { LIST_CITIES, LIST_BARANGAY } from "@/graphql/dropDownQueries.js";

export default {
  name: "EditContactInfo",
  mixins: [validationRules],
  props: {
    contactDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
  },
  components: { CustomSelect, VueGoogleAutocomplete, VueTelInput },
  emits: ["close-edit-form", "edit-updated"],
  data() {
    return {
      isManualEnterForPermanentAddress: true,
      enableEditAutoPermanentAddress: false,
      isManualEnterForCurrentAddress: true,
      enableEditAutoCurrentAddress: false,
      isManualEnterForOfficeAddress: true,
      enableEditAutoOfficeAddress: false,
      editedContactDetails: {},
      isFormDirty: false,
      sameAsPermanent: false,
      isMounted: false,
      openWarningModal: false,
      // mobile
      isValidMobileNumber: true,
      mobileNoCountryCode: 0,
      mobileNoAlreadyExist: false,
      // list
      countryList: [],
      countryListLoading: false,
      cityList: [],
      cityListLoading: false,
      // edit
      openBottomSheet: true,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      pBarangayList: [],
      cBarangayList: [],
      oBarangayList: [],
      isPBarangayLoading: false,
      isCBarangayLoading: false,
      isOBarangayLoading: false,
      pSearchString: "",
      cSearchString: "",
      oSearchString: "",
    };
  },

  computed: {
    noDataTextForPBarangay() {
      if (this.isPBarangayLoading) {
        return "Loading...";
      } else if (
        !this.isPBarangayLoading &&
        this.pBarangayList?.length == 0 &&
        this.pSearchString?.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    noDataTextForCBarangay() {
      if (this.isCBarangayLoading) {
        return "Loading...";
      } else if (
        !this.isCBarangayLoading &&
        this.cBarangayList?.length == 0 &&
        this.cSearchString?.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    noDataTextForOBarangay() {
      if (this.isOBarangayLoading) {
        return "Loading...";
      } else if (
        !this.isOBarangayLoading &&
        this.oBarangayList?.length == 0 &&
        this.oSearchString?.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    mobileNumberValidation() {
      if (!this.editedContactDetails.Mobile_No) {
        return "Mobile number is required";
      } else if (!this.isValidMobileNumber) {
        return "Please provide a valid mobile number";
      } else if (this.mobileNoAlreadyExist) {
        return "Mobile number already exist";
      } else {
        return "";
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isDisableOfficeAddressFields() {
      if (
        this.editedContactDetails.Use_Location_Address ||
        (!this.isManualEnterForOfficeAddress &&
          !this.enableEditAutoOfficeAddress)
      ) {
        return true;
      } else return false;
    },
    isDisableCurrentAddressFields() {
      if (
        this.sameAsPermanent ||
        (!this.isManualEnterForCurrentAddress &&
          !this.enableEditAutoCurrentAddress)
      ) {
        return true;
      } else return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  watch: {
    isManualEnterForPermanentAddress() {
      if (this.$refs.pGoogleVal) this.$refs.pGoogleVal.clear();
      this.onChangeFields();
    },
    isManualEnterForCurrentAddress() {
      if (this.$refs.cGoogleVal) this.$refs.cGoogleVal.clear();
      this.onChangeFields();
    },
    isManualEnterForOfficeAddress() {
      if (this.$refs.oGoogleVal) this.$refs.oGoogleVal.clear();
      this.onChangeFields();
    },
    sameAsPermanent(val) {
      if (!val) {
        this.editedContactDetails.cStreet_Name = "";
        this.editedContactDetails.cCity_Id = null;
        this.editedContactDetails.cCity = "";
        this.editedContactDetails.cBarangay_Id = null;
        this.editedContactDetails.cBarangay = "";
        this.editedContactDetails.cRegion = "";
        this.editedContactDetails.cState = "";
        this.editedContactDetails.cPincode = "";
        this.editedContactDetails.cCountry = "";
        this.editedContactDetails.cApartment_Name = "";
      } else {
        this.fillCurrentAddressBasedOnPermanentAddress();
      }
    },
    "editedContactDetails.Use_Location_Address": function (value) {
      if (value) {
        this.editedContactDetails["oApartment_Name"] =
          this.contactDetails["Street1"];
        this.editedContactDetails["oStreet_Name"] =
          this.contactDetails["Street2"];
        this.editedContactDetails["oRegion"] = this.contactDetails["Region"];
        this.editedContactDetails["oBarangay_Id"] =
          this.contactDetails?.Barangay_Id;
        this.editedContactDetails["oBarangay"] =
          this.contactDetails["Barangay"];
        this.callOBarangayList(this.contactDetails?.Barangay);
        this.editedContactDetails["oCity_Id"] = this.contactDetails?.City_Id;
        this.editedContactDetails["oCity"] = this.contactDetails["City_Name"];
        this.editedContactDetails["oState"] = this.contactDetails["State_Name"];
        this.editedContactDetails["oCountry"] =
          this.contactDetails["Country_Code"];
        this.editedContactDetails["oPincode"] = this.contactDetails["Pincode"];
        this.editedContactDetails["oRegion"] = this.contactDetails?.Region;
      } else {
        this.editedContactDetails["oApartment_Name"] =
          this.contactDetails["oApartment_Name"];
        this.editedContactDetails["oStreet_Name"] =
          this.contactDetails["oStreet_Name"];
        this.editedContactDetails["oCity_Id"] = this.contactDetails?.oCity_Id;
        this.editedContactDetails["oCity"] = this.contactDetails["oCity"];
        this.editedContactDetails["oRegion"] = this.contactDetails["oRegion"];
        this.editedContactDetails["oBarangay_Id"] =
          this.contactDetails?.oBarangay_Id;
        this.editedContactDetails["oBarangay"] =
          this.contactDetails["oBarangay"];
        this.callOBarangayList(this.contactDetails?.oBarangay);
        this.editedContactDetails["oState"] = this.contactDetails["oState"];
        this.editedContactDetails["oCountry"] = this.contactDetails["oCountry"];
        this.editedContactDetails["oPincode"] = this.contactDetails["oPincode"];
      }
    },
    isFormDirty(val) {
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", val);
    },
    "editedContactDetails.Mobile_No": function (value) {
      if (this.isValidMobileNumber) {
        this.validateMobileAlreadyExist(value);
      }
    },
    "editedContactDetails.pCity_Id": function (val) {
      if (this.labelList[137]?.Predefined.toLowerCase() === "yes") {
        let filterCity = this.cityList.filter((el) => el.City_Id == val);
        if (filterCity && filterCity[0]) {
          let details = filterCity[0].cityStateDetails
            ? filterCity[0].cityStateDetails.split(", ")
            : ["", ""];
          this.editedContactDetails["pCity"] = filterCity[0].City_Name;
          this.editedContactDetails["pRegion"] = null;
          this.editedContactDetails["pState"] = details[1];
          this.editedContactDetails["pCountry"] = filterCity[0].Country_Code;
          this.fillCurrentAddressBasedOnPermanentAddress();
        }
      }
    },
    "editedContactDetails.cCity_Id": function (val) {
      if (this.labelList[137]?.Predefined.toLowerCase() === "yes") {
        let filterCity = this.cityList.filter((el) => el.City_Id == val);
        if (filterCity && filterCity[0]) {
          let details = filterCity[0].cityStateDetails
            ? filterCity[0].cityStateDetails.split(", ")
            : ["", ""];
          this.editedContactDetails["cCity"] = filterCity[0].City_Name;
          this.editedContactDetails["cRegion"] = null;
          this.editedContactDetails["cState"] = details[1];
          this.editedContactDetails["cCountry"] = filterCity[0].Country_Code;
          this.fillCurrentAddressBasedOnPermanentAddress();
        }
      }
    },
    "editedContactDetails.oCity_Id": function (val) {
      if (this.labelList[138]?.Predefined.toLowerCase() === "yes") {
        let filterCity = this.cityList.filter((el) => el.City_Id == val);
        if (filterCity && filterCity[0]) {
          let details = filterCity[0].cityStateDetails
            ? filterCity[0].cityStateDetails.split(", ")
            : ["", ""];
          this.editedContactDetails["oCity"] = filterCity[0].City_Name;
          this.editedContactDetails["oRegion"] = null;
          this.editedContactDetails["oState"] = details[1];
          this.editedContactDetails["oCountry"] = filterCity[0].Country_Code;
        }
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.retrieveCountries();
    this.retrieveCities();
    this.editedContactDetails = JSON.parse(JSON.stringify(this.contactDetails));
    this.callPBarangayList(this.editedContactDetails?.pBarangay);
    this.callCBarangayList(this.editedContactDetails?.cBarangay);
    if (this.editedContactDetails.Mobile_No_Country_Code) {
      let cCode = this.editedContactDetails.Mobile_No_Country_Code.split("+");
      this.mobileNoCountryCode =
        cCode && cCode.length > 0 ? parseInt(cCode[1]) : "";
    }
    if (
      this.editedContactDetails.cStreet_Name ===
        this.editedContactDetails.pStreet_Name &&
      this.editedContactDetails.cCity === this.editedContactDetails.pCity &&
      this.editedContactDetails.cState === this.editedContactDetails.pState &&
      this.editedContactDetails.cPincode ===
        this.editedContactDetails.pPincode &&
      this.editedContactDetails.cCountry ===
        this.editedContactDetails.pCountry &&
      this.editedContactDetails.cApartment_Name ===
        this.editedContactDetails.pApartment_Name
    ) {
      this.sameAsPermanent = true;
    }
    this.isMounted = true;
  },

  methods: {
    checkFieldAvailability(value) {
      if (value) {
        let strValue = value.toString();
        return strValue.trim().length > 0;
      } else return false;
    },
    callPBarangayList(searchString) {
      this.pSearchString = searchString;
      if (searchString?.length >= 3) {
        this.retrieveBarangay(searchString, "pBarangay");
      }
    },
    callCBarangayList(searchString) {
      this.cSearchString = searchString;
      if (searchString?.length >= 3) {
        this.retrieveBarangay(searchString, "cBarangay");
      }
    },
    callOBarangayList(searchString) {
      this.oSearchString = searchString;
      if (searchString?.length >= 3) {
        this.retrieveBarangay(searchString, "oBarangay");
      }
    },
    retrieveBarangay(searchString, type) {
      const vm = this;
      const typeMap = {
        pBarangay: {
          loadingKey: "isPBarangayLoading",
          listKey: "pBarangayList",
        },
        cBarangay: {
          loadingKey: "isCBarangayLoading",
          listKey: "cBarangayList",
        },
        oBarangay: {
          loadingKey: "isOBarangayLoading",
          listKey: "oBarangayList",
        },
      };

      const config = typeMap[type];
      if (!config) return;

      vm[config.loadingKey] = true;

      vm.$apollo
        .query({
          query: LIST_BARANGAY,
          client: "apolloClientAS",
          fetchPolicy: "no-cache",
          variables: { searchString },
        })
        .then((response) => {
          const data = response?.data?.getBarangayListWithCity;
          if (data && !data.errorCode && data.barangayDetails?.length) {
            vm[config.listKey] = data.barangayDetails;
          }
        })
        .catch(() => {
          vm[config.listKey] = [];
        })
        .finally(() => {
          vm[config.loadingKey] = false;
        });
    },
    onChangePBarangay(val) {
      if (
        this.labelList[340] &&
        this.labelList[340].Predefined.toLowerCase() === "yes"
      ) {
        let filterCity = this.pBarangayList.filter(
          (el) => el.Barangay_Id == val
        );
        if (filterCity && filterCity[0]) {
          this.editedContactDetails.pBarangay = filterCity[0].Barangay_Name;
          this.editedContactDetails.pRegion = filterCity[0].Region_Name;
          this.editedContactDetails.pCity_Id = filterCity[0]?.City_Id;
          this.editedContactDetails.pCity = filterCity[0].City_Name;
          this.editedContactDetails.pState = filterCity[0].State_Name;
          this.editedContactDetails.pCountry = filterCity[0].Country_Code;
        }
      }
    },
    onChangeCBarangay(val) {
      if (
        this.labelList[342] &&
        this.labelList[342].Predefined.toLowerCase() === "yes"
      ) {
        let filterCity = this.cBarangayList.filter(
          (el) => el.Barangay_Id == val
        );
        if (filterCity && filterCity[0]) {
          this.editedContactDetails.cBarangay = filterCity[0].Barangay_Name;
          this.editedContactDetails.cRegion = filterCity[0].Region_Name;
          this.editedContactDetails.cCity_Id = filterCity[0]?.City_Id;
          this.editedContactDetails.cCity = filterCity[0].City_Name;
          this.editedContactDetails.cState = filterCity[0].State_Name;
          this.editedContactDetails.cCountry = filterCity[0].Country_Code;
        }
      }
    },
    onChangeOBarangay(val) {
      if (
        this.labelList[344] &&
        this.labelList[344].Predefined.toLowerCase() === "yes"
      ) {
        let filterCity = this.oBarangayList.filter(
          (el) => el.Barangay_Id == val
        );
        if (filterCity && filterCity[0]) {
          this.editedContactDetails.oBarangay = filterCity[0].Barangay_Name;
          this.editedContactDetails.oRegion = filterCity[0].Region_Name;
          this.editedContactDetails.oCity_Id = filterCity[0]?.City_Id;
          this.editedContactDetails.oCity = filterCity[0].City_Name;
          this.editedContactDetails.oState = filterCity[0].State_Name;
          this.editedContactDetails.oCountry = filterCity[0].Country_Code;
        }
      }
    },
    onUpdatePBarangay(value) {
      this.onChangePBarangay(value);
    },
    onUpdateCBarangay(value) {
      this.onChangeCBarangay(value);
    },
    onUpdateOBarangay(value) {
      this.onChangeOBarangay(value);
    },
    fillCurrentAddressBasedOnPermanentAddress() {
      if (this.sameAsPermanent) {
        this.editedContactDetails.cStreet_Name =
          this.editedContactDetails.pStreet_Name;
        this.editedContactDetails.cCity_Id = this.editedContactDetails.pCity_Id;
        this.editedContactDetails.cCity = this.editedContactDetails.pCity;
        this.editedContactDetails.cBarangay_Id =
          this.editedContactDetails.pBarangay_Id;
        this.cBarangayList = this.pBarangayList;
        this.editedContactDetails.cBarangay =
          this.editedContactDetails.pBarangay;
        this.editedContactDetails.cRegion = this.editedContactDetails.pRegion;
        this.editedContactDetails.cState = this.editedContactDetails.pState;
        this.editedContactDetails.cPincode = this.editedContactDetails.pPincode;
        this.editedContactDetails.cCountry = this.editedContactDetails.pCountry;
        this.editedContactDetails.cApartment_Name =
          this.editedContactDetails.pApartment_Name;
      }
      this.onChangeFields();
    },
    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.editedContactDetails[field] = value;
    },

    resetPermanentAddress() {
      this.editedContactDetails.pStreet_Name = "";
      this.editedContactDetails.pCity_Id = null;
      this.editedContactDetails.pCity = "";
      this.editedContactDetails.pRegion = "";
      this.editedContactDetails.pBarangay_Id = null;
      this.editedContactDetails.pBarangay = "";
      this.editedContactDetails.pState = "";
      this.editedContactDetails.pPincode = "";
      this.editedContactDetails.pCountry = "";
      this.editedContactDetails.pApartment_Name = "";
    },

    setPermanentAddress(addressData, fullData) {
      this.resetPermanentAddress();
      for (const component of fullData.address_components) {
        const componentType = component.types[0];

        switch (componentType) {
          case "street_number":
          case "street_address":
          case "premise":
          case "establishment":
          case "route": {
            this.editedContactDetails.pApartment_Name &&
            this.editedContactDetails.pApartment_Name.length
              ? (this.editedContactDetails.pApartment_Name =
                  this.editedContactDetails.pApartment_Name +
                  " " +
                  component.long_name)
              : (this.editedContactDetails.pApartment_Name =
                  component.long_name);
            break;
          }

          case "neighborhood": {
            this.editedContactDetails.pStreet_Name = component.long_name;
            break;
          }

          case "administrative_area_level_2":
          case "sublocality_level_2":
          case "sublocality_level_1": {
            this.editedContactDetails.pStreet_Name =
              this.editedContactDetails.pStreet_Name &&
              this.editedContactDetails.pStreet_Name.length
                ? (this.editedContactDetails.pStreet_Name =
                    this.editedContactDetails.pStreet_Name +
                    ", " +
                    component.long_name)
                : (this.editedContactDetails.pStreet_Name =
                    component.long_name);
            break;
          }

          case "locality": {
            this.editedContactDetails.pCity &&
            this.editedContactDetails.pCity.length
              ? ""
              : (this.editedContactDetails.pCity = component.long_name);
            break;
          }

          case "administrative_area_level_3": {
            this.editedContactDetails.pCity &&
            this.editedContactDetails.pCity.length
              ? this.editedContactDetails.pStreet_Name &&
                this.editedContactDetails.pStreet_Name.length
                ? (this.editedContactDetails.pStreet_Name =
                    this.editedContactDetails.pStreet_Name +
                    " " +
                    component.long_name)
                : (this.editedContactDetails.pStreet_Name = component.long_name)
              : (this.editedContactDetails.pCity = component.long_name);
            break;
          }

          case "administrative_area_level_1": {
            this.editedContactDetails.pState = component.long_name;
            break;
          }

          case "country": {
            const selectedCountry = this.countryList.filter((item) => {
              return item.Country_Name === component.long_name;
            });
            if (selectedCountry && selectedCountry.length > 0) {
              this.editedContactDetails.pCountry =
                selectedCountry[0].Country_Code;
            }
            break;
          }

          case "postal_code": {
            this.editedContactDetails.pPincode = component.long_name;
            break;
          }

          case "postal_code_suffix": {
            this.editedContactDetails.pPincode = `${this.editedContactDetails.pPincode}-${component.long_name}`;
            break;
          }
        }
      }
      if (this.editedContactDetails.pCity?.length && this.cityList) {
        let filterCity = this.cityList.filter(
          (el) =>
            el.City_Name.toLowerCase() ==
            this.editedContactDetails.pCity.toLowerCase()
        );
        if (filterCity && filterCity[0]) {
          this.editedContactDetails.pCity_Id = filterCity[0].City_Id;
        }
      }
      let allFieldHasValue = false;
      if (
        this.editedContactDetails.pStreet_Name &&
        (this.editedContactDetails.pBarangay_Id ||
          this.editedContactDetails.pBarangay) &&
        (this.editedContactDetails.pCity_Id ||
          this.editedContactDetails.pCity) &&
        this.editedContactDetails.pState &&
        this.editedContactDetails.pPincode &&
        this.editedContactDetails.pRegion &&
        this.editedContactDetails.pCountry &&
        this.editedContactDetails.pApartment_Name
      ) {
        allFieldHasValue = true;
      }
      this.enableEditAutoPermanentAddress = !allFieldHasValue;
      this.fillCurrentAddressBasedOnPermanentAddress();
      this.onChangeFields();
    },

    resetCurrentAddress() {
      this.editedContactDetails.cStreet_Name = "";
      this.editedContactDetails.cCity_Id = null;
      this.editedContactDetails.cCity = "";
      this.editedContactDetails.cRegion = "";
      this.editedContactDetails.cBarangay_Id = null;
      this.editedContactDetails.cBarangay = "";
      this.editedContactDetails.cState = "";
      this.editedContactDetails.cPincode = "";
      this.editedContactDetails.cCountry = "";
      this.editedContactDetails.cApartment_Name = "";
    },

    setCurrentAddress(addressData, fullData) {
      this.resetCurrentAddress();
      for (const component of fullData.address_components) {
        const componentType = component.types[0];

        switch (componentType) {
          case "street_number":
          case "street_address":
          case "premise":
          case "establishment":
          case "route": {
            this.editedContactDetails.cApartment_Name &&
            this.editedContactDetails.cApartment_Name.length
              ? (this.editedContactDetails.cApartment_Name =
                  this.editedContactDetails.cApartment_Name +
                  " " +
                  component.long_name)
              : (this.editedContactDetails.cApartment_Name =
                  component.long_name);
            break;
          }

          case "neighborhood": {
            this.editedContactDetails.cStreet_Name = component.long_name;
            break;
          }

          case "sublocality_level_2":
          case "sublocality_level_1": {
            this.editedContactDetails.cStreet_Name =
              this.editedContactDetails.cStreet_Name &&
              this.editedContactDetails.cStreet_Name.length
                ? (this.editedContactDetails.cStreet_Name =
                    this.editedContactDetails.cStreet_Name +
                    ", " +
                    component.long_name)
                : (this.editedContactDetails.cStreet_Name =
                    component.long_name);
            break;
          }

          case "locality": {
            this.editedContactDetails.cCity &&
            this.editedContactDetails.cCity.length
              ? ""
              : (this.editedContactDetails.cCity = component.long_name);
            break;
          }

          case "administrative_area_level_3": {
            this.editedContactDetails.cCity &&
            this.editedContactDetails.cCity.length
              ? this.editedContactDetails.cStreet_Name &&
                this.editedContactDetails.cStreet_Name.length
                ? (this.editedContactDetails.cStreet_Name =
                    this.editedContactDetails.cStreet_Name +
                    " " +
                    component.long_name)
                : (this.editedContactDetails.cStreet_Name = component.long_name)
              : (this.editedContactDetails.cCity = component.long_name);
            break;
          }

          case "administrative_area_level_1": {
            this.editedContactDetails.cState = component.long_name;
            break;
          }

          case "country": {
            const selectedCountry = this.countryList.filter((item) => {
              return item.Country_Name === component.long_name;
            });
            if (selectedCountry && selectedCountry.length > 0) {
              this.editedContactDetails.cCountry =
                selectedCountry[0].Country_Code;
            }
            break;
          }

          case "postal_code": {
            this.editedContactDetails.cPincode = component.long_name;
            break;
          }

          case "postal_code_suffix": {
            this.editedContactDetails.cPincode = `${this.editedContactDetails.cPincode}-${component.long_name}`;
            break;
          }
        }
      }
      if (this.editedContactDetails.cCity?.length && this.cityList) {
        let filterCity = this.cityList.filter(
          (el) =>
            el.City_Name.toLowerCase() ==
            this.editedContactDetails.cCity.toLowerCase()
        );
        if (filterCity && filterCity[0]) {
          this.editedContactDetails.cCity_Id = filterCity[0].City_Id;
        }
      }
      let allFieldHasValue = false;
      if (
        this.editedContactDetails.cStreet_Name &&
        (this.editedContactDetails.cBarangay_Id ||
          this.editedContactDetails.cBarangay) &&
        (this.editedContactDetails.cCity_Id ||
          this.editedContactDetails.cCity) &&
        this.editedContactDetails.cState &&
        this.editedContactDetails.cPincode &&
        this.editedContactDetails.cRegion &&
        this.editedContactDetails.cCountry &&
        this.editedContactDetails.cApartment_Name
      ) {
        allFieldHasValue = true;
      }
      this.enableEditAutoCurrentAddress = !allFieldHasValue;
      this.onChangeFields();
    },

    resetOfficeAddress() {
      this.editedContactDetails.oStreet_Name = "";
      this.editedContactDetails.oCity_Id = null;
      this.editedContactDetails.oCity = "";
      this.editedContactDetails.oRegion = "";
      this.editedContactDetails.oBarangay_Id = "";
      this.editedContactDetails.oBarangay = "";
      this.editedContactDetails.oState = "";
      this.editedContactDetails.oPincode = "";
      this.editedContactDetails.oCountry = "";
      this.editedContactDetails.oApartment_Name = "";
    },

    setOfficeAddress(addressData, fullData) {
      this.resetOfficeAddress();
      for (const component of fullData.address_components) {
        const componentType = component.types[0];

        switch (componentType) {
          case "street_number":
          case "street_address":
          case "premise":
          case "establishment":
          case "route": {
            this.editedContactDetails.oApartment_Name &&
            this.editedContactDetails.oApartment_Name.length
              ? (this.editedContactDetails.oApartment_Name =
                  this.editedContactDetails.oApartment_Name +
                  " " +
                  component.long_name)
              : (this.editedContactDetails.oApartment_Name =
                  component.long_name);
            break;
          }

          case "neighborhood": {
            this.editedContactDetails.oStreet_Name = component.long_name;
            break;
          }

          case "administrative_area_level_2":
          case "sublocality_level_2":
          case "sublocality_level_1": {
            this.editedContactDetails.oStreet_Name =
              this.editedContactDetails.oStreet_Name &&
              this.editedContactDetails.oStreet_Name.length
                ? (this.editedContactDetails.oStreet_Name =
                    this.editedContactDetails.oStreet_Name +
                    ", " +
                    component.long_name)
                : (this.editedContactDetails.oStreet_Name =
                    component.long_name);
            break;
          }

          case "locality": {
            this.editedContactDetails.oCity &&
            this.editedContactDetails.oCity.length
              ? ""
              : (this.editedContactDetails.oCity = component.long_name);
            break;
          }

          case "administrative_area_level_3": {
            this.editedContactDetails.oCity &&
            this.editedContactDetails.oCity.length
              ? this.editedContactDetails.oStreet_Name &&
                this.editedContactDetails.oStreet_Name.length
                ? (this.editedContactDetails.oStreet_Name =
                    this.editedContactDetails.oStreet_Name +
                    " " +
                    component.long_name)
                : (this.editedContactDetails.oStreet_Name = component.long_name)
              : (this.editedContactDetails.oCity = component.long_name);
            break;
          }

          case "administrative_area_level_1": {
            this.editedContactDetails.oState = component.long_name;
            break;
          }

          case "country": {
            const selectedCountry = this.countryList.filter((item) => {
              return item.Country_Name === component.long_name;
            });
            if (selectedCountry && selectedCountry.length > 0) {
              this.editedContactDetails.oCountry =
                selectedCountry[0].Country_Code;
            }
            break;
          }

          case "postal_code": {
            this.editedContactDetails.oPincode = component.long_name;
            break;
          }

          case "postal_code_suffix": {
            this.editedContactDetails.oPincode = `${this.editedContactDetails.oPincode}-${component.long_name}`;
            break;
          }
        }
      }
      if (this.editedContactDetails.oCity?.length && this.cityList) {
        let filterCity = this.cityList.filter(
          (el) =>
            el.City_Name.toLowerCase() ==
            this.editedContactDetails.oCity.toLowerCase()
        );
        if (filterCity && filterCity[0]) {
          this.editedContactDetails.oCity_Id = filterCity[0].City_Id;
        }
      }
      let allFieldHasValue = false;
      if (
        this.editedContactDetails.oStreet_Name &&
        (this.editedContactDetails.oBarangay_Id ||
          this.editedContactDetails.oBarangay) &&
        (this.editedContactDetails.oCity_Id ||
          this.editedContactDetails.oCity) &&
        this.editedContactDetails.oState &&
        this.editedContactDetails.oPincode &&
        this.editedContactDetails.oRegion &&
        this.editedContactDetails.oCountry &&
        this.editedContactDetails.oApartment_Name
      ) {
        allFieldHasValue = true;
      }
      this.enableEditAutoOfficeAddress = !allFieldHasValue;
      this.onChangeFields();
    },

    getCountryCode(countryCode) {
      if (countryCode) {
        this.mobileNoCountryCode = countryCode.dialCode;
        this.editedContactDetails.Mobile_No_Country_Code =
          "+" + countryCode.dialCode;
        this.validateMobileAlreadyExist();
      } else {
        this.editedContactDetails.Mobile_No_Country_Code = "";
      }
    },

    validateMobileNumber(param) {
      this.isValidMobileNumber = param.valid;
      this.onChangeFields();
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;

        mixpanel.track("Onboarded-candidate-contact-edit-closed");
        this.$emit("close-edit-form");
      }
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.contactInfoEditForm.validate();
      mixpanel.track("Onboarded-candidate-contact-submit-clicked");
      if (isFormValid && isFormValid.valid && !this.mobileNumberValidation) {
        this.updateContactDetails();
      } else {
        // Check the validity of each field
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "pCountry",
                "cCountry",
                "oCountry",
                "pCity",
                "cCity",
                "cCity",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 2, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },

    updateContactDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_CONTACT_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            permanent_appartmentName:
              vm.editedContactDetails.pApartment_Name.toString(),
            permanent_streetName: vm.editedContactDetails.pStreet_Name
              ? vm.editedContactDetails.pStreet_Name.toString()
              : "",
            permanent_city_id: vm.editedContactDetails.pCity_Id
              ? vm.editedContactDetails.pCity_Id
              : null,
            permanent_city: vm.editedContactDetails.pCity,
            permanent_state: vm.editedContactDetails.pState,
            permanent_country: vm.editedContactDetails.pCountry,
            permanent_pinCode: vm.editedContactDetails.pPincode,
            current_appartmentName:
              vm.editedContactDetails.cApartment_Name.toString(),
            current_streetName: vm.editedContactDetails.cStreet_Name
              ? vm.editedContactDetails.cStreet_Name.toString()
              : "",
            current_city_id: vm.editedContactDetails.cCity_Id
              ? vm.editedContactDetails.cCity_Id
              : null,
            current_city: vm.editedContactDetails.cCity,
            current_state: vm.editedContactDetails.cState,
            current_country: vm.editedContactDetails.cCountry,
            current_pinCode: vm.editedContactDetails.cPincode,
            office_appartmentName:
              vm.editedContactDetails.oApartment_Name.toString(),
            office_streetName: vm.editedContactDetails.oStreet_Name
              ? vm.editedContactDetails.oStreet_Name.toString()
              : "",
            office_city_id: vm.editedContactDetails.oCity_Id
              ? vm.editedContactDetails.oCity_Id
              : null,
            office_city: vm.editedContactDetails.oCity,
            office_state: vm.editedContactDetails.oState,
            office_country: vm.editedContactDetails.oCountry,
            office_pinCode: vm.editedContactDetails.oPincode,
            landlineNo: vm.editedContactDetails.Land_Line_No,
            mobileNo: vm.editedContactDetails.Mobile_No.replace(/^0| /g, ""),
            mobileNoCountryCode: vm.editedContactDetails.Mobile_No_Country_Code,
            useLocationAddress: vm.editedContactDetails.Use_Location_Address
              ? vm.editedContactDetails.Use_Location_Address
              : 0,
            faxNo: vm.editedContactDetails.Fax_No,
            emergencyContactName:
              vm.editedContactDetails.Emergency_Contact_Name,
            emergencyContactRelation:
              vm.editedContactDetails.Emergency_Contact_Relation,
            permanent_barangay_id: vm.editedContactDetails.pBarangay_Id
              ? vm.editedContactDetails.pBarangay_Id
              : null,
            current_barangay_id: vm.editedContactDetails.cBarangay_Id
              ? vm.editedContactDetails?.cBarangay_Id
              : null,
            office_barangay_id: vm.editedContactDetails.oBarangay_Id
              ? vm.editedContactDetails.oBarangay_Id
              : null,
            permanent_barangay: vm.editedContactDetails?.pBarangay,
            permanent_region: vm.editedContactDetails?.pRegion,
            current_barangay: vm.editedContactDetails?.cBarangay,
            current_region: vm.editedContactDetails?.cRegion,
            office_barangay: vm.editedContactDetails?.oBarangay,
            office_region: vm.editedContactDetails?.oRegion,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-contact-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Contact details updated successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
          vm.$emit("edit-updated");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-contact-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "Contact details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    async retrieveCountries() {
      this.countryListLoading = true;
      this.countryList = [];
      await this.$store
        .dispatch("listCountries")
        .then((langList) => {
          this.countryList = langList;
          this.countryListLoading = false;
        })
        .catch(() => {
          this.countryListLoading = false;
        });
    },

    retrieveCities() {
      let vm = this;
      vm.cityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES,
          client: "apolloClientAC",
          variables: {
            Form_Id: 178,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            !response.data.getCityListWithState.errorCode
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.cityList = cityDetails;
            if (
              !this.editedContactDetails.pCity_Id &&
              this.editedContactDetails.pCity &&
              this.editedContactDetails.pState
            ) {
              let filterCity = cityDetails.filter(
                (el) =>
                  el.cityStateDetails?.includes(
                    this.editedContactDetails.pCity
                  ) &&
                  el.cityStateDetails?.includes(
                    this.editedContactDetails.pState
                  )
              );
              if (filterCity && filterCity[0]) {
                this.editedContactDetails.pCity_Id = filterCity[0].City_Id;
              }
            }
            if (
              !this.editedContactDetails.cCity_Id &&
              this.editedContactDetails.cCity &&
              this.editedContactDetails.cState
            ) {
              let filterCity = cityDetails.filter(
                (el) =>
                  el.cityStateDetails?.includes(
                    this.editedContactDetails.cCity
                  ) &&
                  el.cityStateDetails?.includes(
                    this.editedContactDetails.cState
                  )
              );
              if (filterCity && filterCity[0]) {
                this.editedContactDetails.cCity_Id = filterCity[0].City_Id;
              }
            }
            if (
              !this.editedContactDetails.oCity_Id &&
              this.editedContactDetails.oCity &&
              this.editedContactDetails.oState
            ) {
              let filterCity = cityDetails.filter(
                (el) =>
                  el.cityStateDetails?.includes(
                    this.editedContactDetails.oCity
                  ) &&
                  el.cityStateDetails?.includes(
                    this.editedContactDetails.oState
                  )
              );
              if (filterCity && filterCity[0]) {
                this.editedContactDetails.oCity_Id = filterCity[0].City_Id;
              }
            }
          }
          vm.cityListLoading = false;
        })
        .catch(() => {
          vm.cityListLoading = false;
        });
    },

    validateMobileAlreadyExist(val) {
      let vm = this;
      let mNo = val ? val : vm.editedContactDetails.Mobile_No;
      vm.mobileNoAlreadyExist = false;
      if (
        mNo &&
        mNo !== vm.contactDetails.Mobile_No &&
        vm.contactDetails.Mobile_No_Country_Code
      ) {
        vm.$apollo
          .query({
            query: VALIDATE_MOBILE_NUMBER,
            client: "apolloClientAC",
            variables: {
              employeeId: 0,
              mobileNo: mNo,
              mobileNoCountryCode:
                vm.editedContactDetails.Mobile_No_Country_Code,
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateMobileNumber &&
              !response.data.validateMobileNumber.errorCode
            ) {
              const { valid } = response.data.validateMobileNumber;
              vm.mobileNoAlreadyExist = !valid;
            }
          })
          .catch((err) => {
            vm.mobileNoAlreadyExist = false;
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: "mobile number",
              isListError: false,
            });
          });
      }
    },
  },
};
</script>
<style>
.google-auto-complete-address-field {
  width: 100% !important;
  border: none !important;
}
.google-auto-complete-address-field:focus {
  outline: none !important;
}
</style>
