<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="900" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary darken-1"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times
          </v-icon>
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <!-- Updated Filter fields here -->
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedEmployeeName"
              color="primary"
              :items="employeeNameList"
              label="Employee Name"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col
            v-if="!hideDate"
            :cols="windowWidth > 900 ? 4 : 12"
            class="py-2"
          >
            <v-autocomplete
              v-model="selectedAttendanceDate"
              color="primary"
              :items="attendanceDateList"
              label="Date"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>

          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedLateAttendance"
              color="primary"
              :items="lateAttendanceList"
              label="Late Arrival"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>

          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedAutoShortTimeOff"
              color="primary"
              :items="autoShortTimeOffList"
              label="Auto Short Time Off"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedReportingManager"
              color="primary"
              :items="reportingManagerList"
              label="Reporting Manager"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>

          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedApprovalManager"
              color="primary"
              :items="approvalManagerList"
              label="Approval Manager"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>

          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedBusinessUnit"
              color="primary"
              :items="businessUnitList"
              label="Business Unit / Cost Center"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>

          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedDesignation"
              color="primary"
              :items="designationList"
              label="Designation"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedDepartment"
              color="primary"
              :items="departmentList"
              label="Department"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedLocation"
              color="primary"
              :items="locationList"
              label="Location"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedEmployeeType"
              color="primary"
              :items="employeeTypeList"
              label="Employee Type"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckInWorkPlace"
              color="primary"
              :items="checkInWorkPlaceList"
              label="Check In Work Place"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckOutWorkPlace"
              color="primary"
              :items="checkOutWorkPlaceList"
              label="Check Out Work Place"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckInFormSource"
              color="primary"
              :items="checkInFormSourceList"
              label="Check In Form Source"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckOutFormSource"
              color="primary"
              :items="checkOutFormSourceList"
              label="Check Out Form Source"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckInDataSource"
              color="primary"
              :items="checkInDataSourceList"
              label="Check In Data Source"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckOutDataSource"
              color="primary"
              :items="checkOutDataSourceList"
              label="Check Out Data Source"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col
            v-if="
              fieldForce &&
              filterItemList.some(
                (item) =>
                  item.Organization_Unit_Name &&
                  item.Organization_Unit_Name.trim() !== ''
              )
            "
            :cols="windowWidth > 900 ? 4 : 12"
            sm="4"
            class="py-2"
          >
            <v-autocomplete
              v-model="selectedServiceProvider"
              color="primary"
              :items="serviceProviderList"
              :label="getCustomFieldName(115, 'Service Provider')"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
              item-title="Service_Provider_Name"
              item-value="Service_Provider_Id"
            />
          </v-col>

          <v-col :cols="windowWidth > 900 ? 4 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedApprovalStatus"
              color="primary"
              :items="approvalStatusList"
              label="Status"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click="fnApplyFilter"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { getCustomFieldName } from "@/helper";

export default defineComponent({
  name: "AttendanceApprovalFilter",
  props: {
    originalList: {
      type: Object,
      required: true,
    },
    hideDate: {
      type: Boolean,
      required: false,
    },
  },
  data() {
    return {
      openFormFilter: false,
      selectedEmployeeName: null,
      selectedAttendanceDate: null,
      selectedEmployeeType: null,
      selectedLateAttendance: null,
      selectedCheckInWorkPlace: null,
      selectedCheckOutWorkPlace: null,
      selectedCheckInFormSource: null,
      selectedCheckOutFormSource: null,
      selectedCheckInDataSource: null,
      selectedCheckOutDataSource: null,
      selectedApprovalStatus: null,
      selectedServiceProvider: null,
      selectedLocation: null,
      selectedDepartment: null,
      selectedDesignation: null,
      selectedBusinessUnit: null,
      selectedApprovalManager: null,
      selectedReportingManager: null,
      selectedAutoShortTimeOff: null,
      filterItemList: [],
      employeeNameList: [],
      attendanceDateList: [],
      checkInWorkPlaceList: [],
      lateAttendanceList: [],
      checkOutWorkPlaceList: [],
      checkInFormSourceList: [],
      originalCheckInFormSourceList: [],
      checkOutFormSourceList: [],
      checkInDataSourceList: [],
      checkOutDataSourceList: [],
      approvalStatusList: ["Applied", "Approved", "Draft"],
      reportingManagerList: [],
      approvalManagerList: [],
      autoShortTimeOffList: [],
      businessUnitList: [],
      designationList: [],
      departmentList: [],
      locationList: [],
      serviceProviderList: [],
      fieldForce: 0,
    };
  },

  mounted() {
    this.filterItemList = this.originalList;
    this.formFilterData();
    this.fetchDropdownData();
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  watch: {
    originalList() {
      this.filterItemList = this.originalList;
      this.formFilterData();
    },
  },
  methods: {
    getCustomFieldName,
    fetchDropdownData() {
      if (!this.isDropdownDataRetrieved) {
        this.$store
          .dispatch("getDefaultDropdownList", { formId: 15 })
          .then((res) => {
            if (
              res.data &&
              res.data.getDropDownBoxDetails &&
              !res.data.getDropDownBoxDetails.errorCode
            ) {
              const { fieldForce, serviceProvider } =
                res.data.getDropDownBoxDetails;
              this.isDropdownDataRetrieved = true;
              this.serviceProviderList = serviceProvider;
              this.fieldForce = fieldForce;
              this.loadingData = false;
            } else {
              this.handleDropdownDataError();
            }
          })
          .catch(() => {
            this.handleDropdownDataError();
          });
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    fnApplyFilter() {
      let filteredArray = this.filterItemList;

      if (this.selectedEmployeeName?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmployeeName.includes(item.Presentational_Name);
        });
      }

      if (this.selectedAttendanceDate?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedAttendanceDate?.includes(item.AttendanceDate);
        });
      }
      if (this.selectedReportingManager?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedReportingManager.includes(item.Manager_Name);
        });
      }

      if (this.selectedApprovalManager?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedApprovalManager.includes(item.Approver_Name);
        });
      }

      if (this.selectedAutoShortTimeOff?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedAutoShortTimeOff.includes(
            item.Auto_Short_Time_Off
          );
        });
      }

      if (this.selectedBusinessUnit?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedBusinessUnit.includes(item.Business_Unit);
        });
      }

      if (this.selectedDesignation?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDesignation.includes(item.Designation_Name);
        });
      }
      if (this.selectedDepartment?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDepartment.includes(item.Department_Name);
        });
      }
      if (this.selectedLocation?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLocation.includes(item.Location_Name);
        });
      }

      if (this.selectedEmployeeType?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmployeeType.includes(item.Employee_Type);
        });
      }

      if (this.selectedLateAttendance?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLateAttendance.includes(
            item.Display_Late_Attendance
          );
        });
      }
      if (this.selectedCheckInWorkPlace?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCheckInWorkPlace.includes(
            item.Checkin_Work_Place
          );
        });
      }
      if (this.selectedCheckOutWorkPlace?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCheckOutWorkPlace.includes(
            item.Checkout_Work_Place
          );
        });
      }
      if (this.selectedCheckInFormSource?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCheckInFormSource.includes(
            item.Checkin_Form_Source
          );
        });
      }
      if (this.selectedCheckOutFormSource?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCheckOutFormSource.includes(
            item.Checkout_Form_Source
          );
        });
      }
      if (this.selectedCheckInDataSource?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCheckInDataSource.includes(
            item.Checkin_Data_Source
          );
        });
      }
      if (this.selectedCheckOutDataSource?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCheckOutDataSource.includes(
            item.Checkout_Data_Source
          );
        });
      }
      if (this.selectedServiceProvider?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedServiceProvider.includes(
            item.Organization_Unit_Id
          );
        });
      }
      if (this.selectedApprovalStatus?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedApprovalStatus.includes(item.Approval_Status);
        });
      }

      this.openFormFilter = false;
      this.$emit("apply-filter", filteredArray);
    },
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedEmployeeName = null;
      this.selectedAttendanceDate = null;
      this.selectedDesignation = null;
      this.selectedDepartment = null;
      this.selectedLocation = null;
      this.selectedEmployeeType = null;
      this.selectedLateAttendance = null;
      this.selectedCheckInWorkPlace = null;
      this.selectedCheckOutWorkPlace = null;
      this.selectedCheckInFormSource = null;
      this.selectedCheckOutFormSource = null;
      this.selectedCheckInDataSource = null;
      this.selectedCheckOutDataSource = null;
      this.selectedServiceProvider = null;
      this.selectedApprovalStatus = null;
      this.selectedReportingManager = null;
      this.selectedApprovalManager = null;
      this.selectedAutoShortTimeOff = null;
      this.selectedBusinessUnit = null;
    },

    formFilterData() {
      const employeeNameSet = new Set();
      const attendanceDateSet = new Set();
      const lateAttendanceSet = new Set();
      const checkInWorkPlaceSet = new Set();
      const checkOutWorkPlaceSet = new Set();
      const checkInFormSourceSet = new Set();
      const checkOutFormSourceSet = new Set();
      const checkInDataSourceSet = new Set();
      const checkOutDataSourceSet = new Set();
      const approvalStatusSet = new Set();
      const reportingManagerSet = new Set();
      const approvalManagerSet = new Set();
      const autoShortTimeOffSet = new Set();
      const businessUnitSet = new Set();
      const designationSet = new Set();
      const departmentSet = new Set();
      const locationSet = new Set();
      const employeeTypeSet = new Set();

      for (let item of this.originalList) {
        if (item && item.Presentational_Name)
          employeeNameSet.add(item.Presentational_Name);
        if (item && item.AttendanceDate)
          attendanceDateSet.add(item.AttendanceDate);
        if (item && item.Display_Late_Attendance)
          lateAttendanceSet.add(item.Display_Late_Attendance);
        if (item && item.Checkin_Work_Place)
          checkInWorkPlaceSet.add(item.Checkin_Work_Place);
        if (item && item.Checkout_Work_Place)
          checkOutWorkPlaceSet.add(item.Checkout_Work_Place);
        if (item && item.Checkin_Form_Source)
          checkInFormSourceSet.add(item.Checkin_Form_Source);
        if (item && item.Checkout_Form_Source)
          checkOutFormSourceSet.add(item.Checkout_Form_Source);
        if (item && item.Checkin_Data_Source)
          checkInDataSourceSet.add(item.Checkin_Data_Source);
        if (item && item.Checkout_Data_Source)
          checkOutDataSourceSet.add(item.Checkout_Data_Source);
        if (item && item.Approval_Status)
          approvalStatusSet.add(item.Approval_Status);
        if (item && item.Manager_Name)
          reportingManagerSet.add(item.Manager_Name);
        if (item && item.Approver_Name)
          approvalManagerSet.add(item.Approver_Name);
        if (item && item.Auto_Short_Time_Off)
          autoShortTimeOffSet.add(item.Auto_Short_Time_Off);
        if (item && item.Business_Unit) businessUnitSet.add(item.Business_Unit);
        if (item && item.Designation_Name)
          designationSet.add(item.Designation_Name);
        if (item && item.Department_Name)
          departmentSet.add(item.Department_Name);
        if (item && item.Location_Name) locationSet.add(item.Location_Name);
        if (item && item.Employee_Type) employeeTypeSet.add(item.Employee_Type);
      }

      this.employeeNameList = Array.from(employeeNameSet);
      this.lateAttendanceList = Array.from(lateAttendanceSet);
      this.attendanceDateList = Array.from(attendanceDateSet);
      this.checkInWorkPlaceList = Array.from(checkInWorkPlaceSet);
      this.checkOutWorkPlaceList = Array.from(checkOutWorkPlaceSet);
      this.checkInFormSourceList = Array.from(checkInFormSourceSet);
      this.checkOutFormSourceList = Array.from(checkOutFormSourceSet);
      this.checkInDataSourceList = Array.from(checkInDataSourceSet);
      this.checkOutDataSourceList = Array.from(checkOutDataSourceSet);
      this.statusList = Array.from(approvalStatusSet);
      this.reportingManagerList = Array.from(reportingManagerSet);
      this.approvalManagerList = Array.from(approvalManagerSet);
      this.autoShortTimeOffList = Array.from(autoShortTimeOffSet);
      this.businessUnitList = Array.from(businessUnitSet);
      this.designationList = Array.from(designationSet);
      this.departmentList = Array.from(departmentSet);
      this.locationList = Array.from(locationSet);
      this.employeeTypeList = Array.from(employeeTypeSet);
    },
  },
});
</script>
