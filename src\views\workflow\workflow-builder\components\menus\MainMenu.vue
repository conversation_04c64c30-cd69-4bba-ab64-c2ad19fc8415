<template>
  <v-container>
    <div class="menu_item">
      <div>
        <div class="menu_1">
          <div
            class="ta_event_btn ta-tooltip-top ta-tooltip-top-right"
            @click="addUserTask"
            data-title=""
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 448 512"
                height="10"
                width="10"
                fill="#ffffff"
              >
                <path
                  d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"
                />
              </svg>
            </div>
            <span class="ta-tooltip"
              >User Task is can manually Approve / Reject / Resend
            </span>
          </div>
        </div>
        <!-- <div class="menu_2">
          <div
            class="ta_event_btn ta-tooltip-top ta-tooltip-top-right"
            @click="addCallActivity"
            data-placement="top"
            data-title="callActivity"
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 448 512"
                height="10"
                width="10"
                fill="#ffffff"
              >
                <path
                  d="M429.6 92.1c4.9-11.9 2.1-25.6-7-34.7s-22.8-11.9-34.7-7l-352 144c-14.2 5.8-22.2 20.8-19.3 35.8s16.1 25.8 31.4 25.8H224V432c0 15.3 10.8 28.4 25.8 31.4s30-5.1 35.8-19.3l144-352z"
                />
              </svg>
            </div>
            <span class="ta-tooltip">Call Activity</span>
          </div>
        </div> -->
        <div class="menu_3">
          <div
            class="ta_event_btn ta-tooltip-right ta-tooltip-right-top"
            @click="addServiceTask"
            data-title="Service Task"
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 640 512"
                height="15"
                width="14"
                fill="#ffffff"
              >
                <path
                  d="M308.5 135.3c7.1-6.3 9.9-16.2 6.2-25c-2.3-5.3-4.8-10.5-7.6-15.5L304 89.4c-3-5-6.3-9.9-9.8-14.6c-5.7-7.6-15.7-10.1-24.7-7.1l-28.2 9.3c-10.7-8.8-23-16-36.2-20.9L199 27.1c-1.9-9.3-9.1-16.7-18.5-17.8C173.9 8.4 167.2 8 160.4 8h-.7c-6.8 0-13.5 .4-20.1 1.2c-9.4 1.1-16.6 8.6-18.5 17.8L115 56.1c-13.3 5-25.5 12.1-36.2 20.9L50.5 67.8c-9-3-19-.5-24.7 7.1c-3.5 4.7-6.8 9.6-9.9 14.6l-3 5.3c-2.8 5-5.3 10.2-7.6 15.6c-3.7 8.7-.9 18.6 6.2 25l22.2 19.8C32.6 161.9 32 168.9 32 176s.6 14.1 1.7 20.9L11.5 216.7c-7.1 6.3-9.9 16.2-6.2 25c2.3 5.3 4.8 10.5 7.6 15.6l3 5.2c3 5.1 6.3 9.9 9.9 14.6c5.7 7.6 15.7 10.1 24.7 7.1l28.2-9.3c10.7 8.8 23 16 36.2 20.9l6.1 29.1c1.9 9.3 9.1 16.7 18.5 17.8c6.7 .8 13.5 1.2 20.4 1.2s13.7-.4 20.4-1.2c9.4-1.1 16.6-8.6 18.5-17.8l6.1-29.1c13.3-5 25.5-12.1 36.2-20.9l28.2 9.3c9 3 19 .5 24.7-7.1c3.5-4.7 6.8-9.5 9.8-14.6l3.1-5.4c2.8-5 5.3-10.2 7.6-15.5c3.7-8.7 .9-18.6-6.2-25l-22.2-19.8c1.1-6.8 1.7-13.8 1.7-20.9s-.6-14.1-1.7-20.9l22.2-19.8zM112 176a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zM504.7 500.5c6.3 7.1 16.2 9.9 25 6.2c5.3-2.3 10.5-4.8 15.5-7.6l5.4-3.1c5-3 9.9-6.3 14.6-9.8c7.6-5.7 10.1-15.7 7.1-24.7l-9.3-28.2c8.8-10.7 16-23 20.9-36.2l29.1-6.1c9.3-1.9 16.7-9.1 17.8-18.5c.8-6.7 1.2-13.5 1.2-20.4s-.4-13.7-1.2-20.4c-1.1-9.4-8.6-16.6-17.8-18.5L583.9 307c-5-13.3-12.1-25.5-20.9-36.2l9.3-28.2c3-9 .5-19-7.1-24.7c-4.7-3.5-9.6-6.8-14.6-9.9l-5.3-3c-5-2.8-10.2-5.3-15.6-7.6c-8.7-3.7-18.6-.9-25 6.2l-19.8 22.2c-6.8-1.1-13.8-1.7-20.9-1.7s-14.1 .6-20.9 1.7l-19.8-22.2c-6.3-7.1-16.2-9.9-25-6.2c-5.3 2.3-10.5 4.8-15.6 7.6l-5.2 3c-5.1 3-9.9 6.3-14.6 9.9c-7.6 5.7-10.1 15.7-7.1 24.7l9.3 28.2c-8.8 10.7-16 23-20.9 36.2L315.1 313c-9.3 1.9-16.7 9.1-17.8 18.5c-.8 6.7-1.2 13.5-1.2 20.4s.4 13.7 1.2 20.4c1.1 9.4 8.6 16.6 17.8 18.5l29.1 6.1c5 13.3 12.1 25.5 20.9 36.2l-9.3 28.2c-3 9-.5 19 7.1 24.7c4.7 3.5 9.5 6.8 14.6 9.8l5.4 3.1c5 2.8 10.2 5.3 15.5 7.6c8.7 3.7 18.6 .9 25-6.2l19.8-22.2c6.8 1.1 13.8 1.7 20.9 1.7s14.1-.6 20.9-1.7l19.8 22.2zM464 304a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"
                />
              </svg>
            </div>
            <span class="ta-tooltip"
              >Service Task, workflow will complete the service task by calling
              API</span
            >
          </div>
        </div>
        <div class="menu_4">
          <div
            class="ta_event_btn ta-tooltip-right ta-tooltip-right-bottom"
            @click="addParallelGateway"
            data-title="Parallel Gateway"
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 576 512"
                height="10"
                width="10"
                fill="#ffffff"
              >
                <path
                  d="M208 80c0-26.5 21.5-48 48-48h64c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48h-8v40H464c30.9 0 56 25.1 56 56v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H464c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-4.4-3.6-8-8-8H312v40h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H256c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V280H112c-4.4 0-8 3.6-8 8v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-30.9 25.1-56 56-56H264V192h-8c-26.5 0-48-21.5-48-48V80z"
                />
              </svg>
            </div>
            <span class="ta-tooltip"
              >Parallel Gateway will create parallelly two or more tasks</span
            >
          </div>
        </div>
        <div class="menu_5">
          <div
            class="ta_event_btn ta-tooltip-bottom ta-tooltip-bottom-left"
            @click="addExclusiveGateway"
            data-title="Exclusive Gateway"
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
                height="10"
                width="10"
                fill="#ffffff"
              >
                <path
                  d="M403.8 34.4c12-5 25.7-2.2 34.9 6.9l64 64c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-64 64c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6V160H352c-10.1 0-19.6 4.7-25.6 12.8L284 229.3 244 176l31.2-41.6C293.3 110.2 321.8 96 352 96h32V64c0-12.9 7.8-24.6 19.8-29.6zM164 282.7L204 336l-31.2 41.6C154.7 401.8 126.2 416 96 416H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H96c10.1 0 19.6-4.7 25.6-12.8L164 282.7zm274.6 188c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6V416H352c-30.2 0-58.7-14.2-76.8-38.4L121.6 172.8c-6-8.1-15.5-12.8-25.6-12.8H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H96c30.2 0 58.7 14.2 76.8 38.4L326.4 339.2c6 8.1 15.5 12.8 25.6 12.8h32V320c0-12.9 7.8-24.6 19.8-29.6s25.7-2.2 34.9 6.9l64 64c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-64 64z"
                />
              </svg>
            </div>
            <span class="ta-tooltip"
              >Exclusive Gateway will create tasks based on defined
              conditions</span
            >
          </div>
        </div>
        <div class="menu_6">
          <div
            class="ta_event_btn ta-tooltip-left ta-tooltip-left-top"
            data-placement="bottom"
            data-title="GOTO"
            @click="addGotoNode"
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
                height="10"
                width="10"
                fill="#ffffff"
              >
                <path
                  d="M125.7 160H176c17.7 0 32 14.3 32 32s-14.3 32-32 32H48c-17.7 0-32-14.3-32-32V64c0-17.7 14.3-32 32-32s32 14.3 32 32v51.2L97.6 97.6c87.5-87.5 229.3-87.5 316.8 0s87.5 229.3 0 316.8s-229.3 87.5-316.8 0c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0c62.5 62.5 163.8 62.5 226.3 0s62.5-163.8 0-226.3s-163.8-62.5-226.3 0L125.7 160z"
                />
              </svg>
            </div>
            <span class="ta-tooltip"
              >Once GOTO defined, the workflow will go to given step</span
            >
          </div>
        </div>
        <!-- <div class="menu_7">
          <div
            class="ta_event_btn ta-tooltip-left ta-tooltip-left-bottom"
            data-placement="top"
            data-title="End task"
            @click="addEndNode"
          >
            <div class="menus_icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
                height="10"
                width="10"
                fill="#ffffff"
              >
                <path
                  d="M464 256A208 208 0 1 0 48 256a208 208 0 1 0 416 0zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z"
                />
              </svg>
            </div>
            <span class="ta-tooltip">End Task</span>
          </div>
        </div> -->
      </div>
    </div>
  </v-container>
</template>
<script>
// import "../../../../../assets/css/tooltip.css";
export default {
  emits: ["handleProcessNode"],
  name: "MenuItems",
  data() {
    return {};
  },
  methods: {
    addUserTask() {
      this.$emit("handleProcessNode", "addUserTask");
    },
    addCallActivity() {
      this.$emit("handleProcessNode", "addCallActivity");
    },
    addServiceTask() {
      this.$emit("handleProcessNode", "addServiceTask");
    },
    addParallelGateway() {
      this.$emit("handleProcessNode", "addParallelGateway");
    },
    addExclusiveGateway() {
      this.$emit("handleProcessNode", "addExclusiveGateway");
    },
    addGotoNode() {
      this.$emit("handleProcessNode", "addGotoNode");
    },
    addEndNode() {
      this.$emit("handleProcessNode", "addEndNode");
    },
  },
  mounted() {},
};
</script>
<style>
.menu_item {
  position: relative;
}

.ta_event_btn {
  /* position:  relative !important; */
}
.menus_icon {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 1.3rem;
  background-color: #ec4079c2;
  display: flex;
  justify-content: center;
  align-items: center;
}

.menu_1 {
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translate(-50%);
  transition-delay: 0.5s;
}

.menu_2 {
  position: absolute;
  top: 2.4rem;
  left: 2.8rem;
  transition-delay: 1s;
}
.menu_3 {
  position: absolute;
  top: 2.2rem;
  left: 2.8rem;
  transition-delay: 1.5s;
}
.menu_4 {
  position: absolute;
  top: 5.8rem;
  left: 2.5rem;
  transition-delay: 2s;
}
.menu_5 {
  position: absolute;
  top: 6.2rem;
  left: -2.6rem;
  transition-delay: 2.5s;
}
.menu_6 {
  position: absolute;
  top: 2.8rem;
  left: -3.8rem;
  transition-delay: 3s;
}

.menu_7 {
  position: absolute;
  top: 2.4rem;
  left: -3.8rem;
  transition-delay: 3.5s;
}

.ta-tooltip {
  font-size: 8px !important;
}
</style>
