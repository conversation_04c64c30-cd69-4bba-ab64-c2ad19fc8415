/* override css */
.row {
    margin: 0px !important;
}
.v-btn__content{
    text-transform: none !important;
}
.v-tab__slider{
    height: 3px !important;
}
.v-slide-group-item--active {
    color: rgb(var(--v-theme-primary)) !important;
}
.v-field__prepend-inner > .v-icon, .v-field__append-inner > .v-icon, .v-field__clearable > .v-icon {
    font-size: 15px !important;
}
.v-picker-title, .v-picker__header {
    display: none !important;
}
.vuejs3-datepicker__calendar-topbar {
  display: none;
}
.vuejs3-datepicker__calendar .cell.selected {
    background: rgb(var(--v-theme-primary)) !important;
}
.vuejs3-datepicker__icon {
    margin-top: 2px;
    margin-right: -5px;
}
.vuejs3-datepicker__value {
    border-radius: 0px !important;
    border: 0px !important;
    width: 100%;
    box-shadow: 0 3px 1px -2px var(--v-shadow-key-umbra-opacity, rgba(0, 0, 0, 0.2)),
      0 2px 2px 0 var(--v-shadow-key-penumbra-opacity, rgba(0, 0, 0, 0.14)),
      0 1px 5px 0 var(--v-shadow-key-penumbra-opacity, rgba(0, 0, 0, 0.12));
}
.vuejs3-datepicker #calendar-div {
    border-radius: 0px !important;
}
/* own css */
.custom-label {
    font-size: 0.85em;
    color: #7a7a7a;
}
.card-blue-background {
    background: #FDFEFF !important;
}
.card-item {
    flex: 1 0 auto;
}
.card-columns {
    display: flex;
    flex-direction: row;
}
/*col of education details card to be stacked vertically */
@media (max-width: 600px) {
    .card-columns {
      display: flex;
      flex-direction: column;
    }
}
.cursor-pointer{
    cursor: pointer !important;
}
.cursor-not-allow{
    cursor: not-allowed !important;
}
.fill-height {
    height: 100% !important;
}
.footer-notes {
    background: linear-gradient(to left, #f7f793 0%, #fff9d1 100%);
    min-height: 50px;
    align-items: center;
    width: 100%;
    margin: 0px;
    flex-wrap: nowrap;
    padding: 0.5em 1em 0.5em 0em;
    border-radius: 0px 0px 20px 20px;
}
.common-box-shadow {
    box-shadow: 0px 1px 15px rgba(15, 84, 136, 0.14) !important;
}
.no-search-results {
    height: 30em;
}
.main-container {
    padding: 6em 2em 3em 3em;
}
.value-text {
    color: #565656 !important;
    font-size: 15px;
    margin: 12px 0px;
    overflow-wrap: break-word;
    max-width: 360px;
}
/* media classes */
@media screen and (max-width:500px) {
    .main-container {
        padding: 5em 0.5em !important;
    }
}
/* avatar class */
.avatarBCColor0 {
    background: #F8BBD0;
    color: #AD1457 !important;
}
.avatarBCColor1 {
    background: #32fff8;
    color:  #015555 !important;
}
.avatarBCColor2 {
    background: #8fb4f5;
    color:  #031577 !important;
}
.avatarBCColor3 {
    background: #FFCDD2;
    color: #B71C1C !important;
}
.avatarBCColor4 {
    background: #C8E6C9;
    color: #1B5E20 !important;
}
.list-delete-enabled {
    cursor: pointer;
}
.list-delete-disabled {
    color: #d7d7d7 !important;
    cursor: not-allowed !important;
}
.qlbt-operation-menu {
    top: 30em !important;
    z-index: 3000 !important;
} 
