<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="destinationCity"
              :items="listCity"
              item-title="destinationCity"
              item-value="destinationCity"
              label="Destination City"
              color="primary"
              variant="solo"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
              @selected-item="onChangeSelectField($event, 'destinationCity')"
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="destinationCountry"
              :items="listCountry"
              label="Destination Country"
              item-value="destinationCountry"
              item-title="destinationCountry"
              @selected-item="onChangeSelectField($event, 'destinationCountry')"
              color="primary"
              variant="solo"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="ticketCategory"
              :items="listCategory"
              item-value="ticketCategory"
              item-title="ticketCategory"
              label="Air Ticket Category"
              @selected-item="onChangeSelectField($event, 'ticketCategory')"
              color="primary"
              variant="solo"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              :items="listStatus"
              label="Status"
              itemValue="status"
              itemTitle="status"
              variant="solo"
              color="primary"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
              @selected-item="onChangeSelectField($event, 'selectedStatus')"
            />
          </v-col>
          <!-- Air Fare Entitlement for Infant -->
          <v-col class="py-2" cols="12">
            <div style="font-size: 1em; color: grey">
              Air Fare Entitlement for Infant<span
                v-if="payrollCurrency"
                class="mr-1"
                >(in {{ payrollCurrency }})</span
              >
            </div>
            <v-range-slider
              v-model="infantAmountRange"
              :min="0"
              :max="2000000000"
              step="1"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="infantAmountRange[0]"
                  :min="0"
                  :max="2000000000"
                  density="compact"
                  style="width: 125px"
                  type="number"
                  variant="solo"
                  hide-details
                />
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="infantAmountRange[1]"
                  :min="0"
                  :max="2000000000"
                  density="compact"
                  style="width: 150px"
                  type="number"
                  variant="solo"
                  hide-details
                />
              </template>
            </v-range-slider>
          </v-col>
          <!-- Air Fare Entitlement for Child -->
          <v-col class="py-2" cols="12">
            <div style="font-size: 1em; color: grey">
              Air Fare Entitlement for Child<span
                v-if="payrollCurrency"
                class="mr-1"
                >(in {{ payrollCurrency }})</span
              >
            </div>
            <v-range-slider
              v-model="childAmountRange"
              :min="0"
              :max="2000000000"
              step="1"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="childAmountRange[0]"
                  :min="0"
                  :max="2000000000"
                  density="compact"
                  style="width: 125px"
                  type="number"
                  variant="solo"
                  hide-details
                />
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="childAmountRange[1]"
                  :min="0"
                  :max="2000000000"
                  density="compact"
                  style="width: 150px"
                  type="number"
                  variant="solo"
                  hide-details
                />
              </template>
            </v-range-slider>
          </v-col>
          <!-- Air Fare Entitlement for Adult -->
          <v-col class="py-2" cols="12">
            <div style="font-size: 1em; color: grey">
              Air Fare Entitlement for Adult<span
                v-if="payrollCurrency"
                class="mr-1"
                >(in {{ payrollCurrency }})</span
              >
            </div>
            <v-range-slider
              v-model="adultAmountRange"
              :min="0"
              :max="2000000000"
              step="1"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="adultAmountRange[0]"
                  :min="0"
                  :max="2000000000"
                  density="compact"
                  style="width: 125px"
                  type="number"
                  variant="solo"
                  hide-details
                />
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="adultAmountRange[1]"
                  :min="0"
                  :max="2000000000"
                  density="compact"
                  style="width: 150px"
                  type="number"
                  variant="solo"
                  hide-details
                />
              </template>
            </v-range-slider>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn rounded="lg" variant="outlined" @click="resetFilterValues()">
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "AirTicketFilter",
  data: () => ({
    openFormFilter: false,
    //Dropdown values
    listCity: [],
    listCountry: [],
    listCategory: [],
    listStatus: [],
    // selected Values
    destinationCity: [],
    destinationCountry: [],
    ticketCategory: [],
    infantAmountRange: [0, 2000000000],
    childAmountRange: [0, 2000000000],
    adultAmountRange: [0, 2000000000],
    selectedStatus: ["Active"],
  }),

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
  },
  mounted() {
    this.fnApplyFilter();
    this.formFilterData();
  },
  methods: {
    formFilterData() {
      for (let item of this.items) {
        if (item && item.Destination_City) {
          this.listCity.push({
            destinationCity: item.Destination_City,
          });
        }
        if (item && item.Destination_Country) {
          this.listCountry.push({
            destinationCountry: item.Destination_Country,
          });
        }
        if (item && item.Air_Ticketing_Category) {
          this.listCategory.push({
            ticketCategory: item.Air_Ticketing_Category,
          });
        }
        if (item && item.Status) {
          this.listStatus.push({
            status: item.Status,
          });
        }
      }
      this.listCity = this.removeDuplicatesFromArrayOfObject(
        this.listCity,
        "destinationCity"
      );
      this.listCountry = this.removeDuplicatesFromArrayOfObject(
        this.listCountry,
        "destinationCountry"
      );
      this.listCategory = this.removeDuplicatesFromArrayOfObject(
        this.listCategory,
        "ticketCategory"
      );
      this.listStatus = this.removeDuplicatesFromArrayOfObject(
        this.listStatus,
        "status"
      );
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        DestinationCity: this.destinationCity,
        DestinationCountry: this.destinationCountry,
        TicketCategory: this.ticketCategory,
        Status: this.selectedStatus,
        InfantAmount: this.infantAmountRange,
        ChildAmount: this.childAmountRange,
        AdultAmount: this.adultAmountRange,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues;
      this.openFormFilter = false;
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.destinationCity = [];
      this.destinationCountry = [];
      this.ticketCategory = [];
      this.selectedStatus = ["Active"];
      this.infantAmountRange = [0, 2000000000];
      this.childAmountRange = [0, 2000000000];
      this.adultAmountRange = [0, 2000000000];
    },
  },
};
</script>
