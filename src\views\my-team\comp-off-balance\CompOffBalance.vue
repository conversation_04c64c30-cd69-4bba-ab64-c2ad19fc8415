<template>
  <div>
    <div
      v-if="windowWidth >= 1280 && backupMainList.length > 0"
      class="filter-menu-overlay"
    >
      <EmployeeDefaultFilterMenu
        v-if="backupMainList.length > 0"
        ref="employeeDefaultFilter"
        class="justify-end"
        :list-items="backupMainList"
        :isApplyFilter="true"
        :parentComponent="'compOffBalance'"
        departmentIdKey="Department_Id"
        designationIdKey="Designation_Id"
        locationIdKey="Location_Id"
        empTypeIdKey="EmpType_Id"
        workScheduleIdKey="WorkSchedule_Id"
        :showServiceProvider="true"
        @reset-emp-filter="resetFilter()"
        @applied-filter="applyFilter($event)"
      >
        <template #bottom-filter-menu>
          <v-row class="ml-1">
            <v-col cols="12" class="mb-n2">
              <span class="text-subtitle-1 text-grey-darken-1">
                Expiry Date
              </span></v-col
            >
            <v-col class="mb-2 py-2" :cols="!isMobileView ? 6 : 12">
              <datepicker
                :format="orgDateFormat"
                class="custom-date-picker"
                v-model="expiryFromDate"
                :disabled-dates="{
                  from: expiryMaxDate ? new Date(expiryMaxDate) : '',
                }"
                :placeholder="'From Date'"
                style="width: 100%"
              ></datepicker
            ></v-col>
            <v-col class="mb-2 py-2" :cols="!isMobileView ? 6 : 12">
              <datepicker
                :format="orgDateFormat"
                class="custom-date-picker"
                v-model="expiryToDate"
                :disabled-dates="{
                  to: expiryMinDate ? new Date(expiryMinDate) : '',
                }"
                :placeholder="'To Date'"
                style="width: 100%"
              ></datepicker>
            </v-col>
          </v-row>
          <v-row class="ml-1 mb-4">
            <v-col cols="12" class="mb-n2">
              <span class="text-subtitle-1 text-grey-darken-1">
                Worked Date
              </span></v-col
            >
            <v-col class="py-2 mb-2" :cols="!isMobileView ? 6 : 12">
              <datepicker
                :format="orgDateFormat"
                class="custom-date-picker"
                v-model="workedFromDate"
                :placeholder="'From Date'"
                :disabled-dates="{
                  from: workedMaxDate ? new Date(workedMaxDate) : '',
                }"
                style="width: 100%"
              ></datepicker
            ></v-col>
            <v-col class="py-2 mb-2" :cols="!isMobileView ? 6 : 12">
              <datepicker
                :format="orgDateFormat"
                class="custom-date-picker"
                v-model="workedToDate"
                :disabled-dates="{
                  to: new Date(workedMinDate),
                }"
                :placeholder="'To Date'"
                style="width: 100%"
              ></datepicker>
            </v-col>
          </v-row>
        </template>
      </EmployeeDefaultFilterMenu>
    </div>
    <v-bottom-navigation
      v-else-if="windowWidth < 1280 && backupMainList.length > 0"
      color="teal"
      elevation="15"
      bg-color="white"
      class="align-center"
    >
      <v-row justify="center" class="mr-2">
        <v-col cols="12" md="9" class="d-flex justify-end mr-8">
          <EmployeeDefaultFilterMenu
            v-if="backupMainList.length > 0"
            ref="employeeDefaultFilter"
            class="justify-end"
            :list-items="backupMainList"
            :isApplyFilter="true"
            departmentIdKey="Department_Id"
            designationIdKey="Designation_Id"
            locationIdKey="Location_Id"
            empTypeIdKey="EmpType_Id"
            workScheduleIdKey="WorkSchedule_Id"
            :showServiceProvider="true"
            @reset-emp-filter="resetFilter()"
            @applied-filter="applyFilter($event)"
          >
            <template #bottom-filter-menu>
              <v-row class="ml-1">
                <v-col cols="12" class="mb-n2">
                  <span class="text-subtitle-1 text-grey-darken-1">
                    Expiry Date
                  </span></v-col
                >
                <v-col class="mb-2 py-2" :cols="!isMobileView ? 6 : 12">
                  <datepicker
                    :format="orgDateFormat"
                    class="custom-date-picker"
                    v-model="expiryFromDate"
                    :disabled-dates="{
                      from: expiryMaxDate ? new Date(expiryMaxDate) : '',
                    }"
                    :placeholder="'From Date'"
                    style="width: 100%"
                  ></datepicker
                ></v-col>
                <v-col class="mb-2 py-2" :cols="!isMobileView ? 6 : 12">
                  <datepicker
                    :format="orgDateFormat"
                    class="custom-date-picker"
                    v-model="expiryToDate"
                    :disabled-dates="{
                      to: expiryMinDate ? new Date(expiryMinDate) : '',
                    }"
                    :placeholder="'To Date'"
                    style="width: 100%"
                  ></datepicker>
                </v-col>
              </v-row>
              <v-row class="ml-1 mb-4">
                <v-col cols="12" class="mb-n2">
                  <span class="text-subtitle-1 text-grey-darken-1">
                    Worked Date
                  </span></v-col
                >
                <v-col class="py-2 mb-2" :cols="!isMobileView ? 6 : 12">
                  <datepicker
                    :format="orgDateFormat"
                    class="custom-date-picker"
                    v-model="workedFromDate"
                    :placeholder="'From Date'"
                    :disabled-dates="{
                      from: workedMaxDate ? new Date(workedMaxDate) : '',
                    }"
                    style="width: 100%"
                  ></datepicker
                ></v-col>
                <v-col class="py-2 mb-2" :cols="!isMobileView ? 6 : 12">
                  <datepicker
                    :format="orgDateFormat"
                    class="custom-date-picker"
                    v-model="workedToDate"
                    :disabled-dates="{
                      to: new Date(workedMinDate),
                    }"
                    :placeholder="'To Date'"
                    style="width: 100%"
                  ></datepicker>
                </v-col>
              </v-row>
            </template>
          </EmployeeDefaultFilterMenu>
        </v-col>
      </v-row>
    </v-bottom-navigation>
    <section v-if="formAccess">
      <div v-if="listLoading" class="mt-3">
        <v-skeleton-loader
          ref="skeleton1"
          type="table-heading"
          class="mx-auto"
        ></v-skeleton-loader>
        <div v-for="i in 3" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <AppFetchErrorScreen
        v-else-if="isErrorInList"
        :content="errorContent"
        icon-name="fas fa-redo-alt"
        :button-text="showRetryBtn ? 'Retry' : ''"
        @button-click="refetchData()"
      >
      </AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="
          backupMainList && backupMainList.length === 0 && !showEditForm
        "
        key="no-results-screen"
      >
        <template #contentSlot>
          <div style="max-width: 80%" class="mx-auto">
            <v-row
              v-if="!isLoading"
              style="background: white"
              class="rounded-lg pa-5 mb-4"
              :class="isMobileView ? 'mt-n16' : ''"
            >
              <v-col cols="12">
                <NotesCard
                  :heading="
                    accessFormName ? accessFormName : 'Comp Off Balance'
                  "
                  imageName=""
                  backgroundColor="transparent"
                  class="mb-4"
                ></NotesCard>
                <NotesCard
                  notes="Earn extra time off effortlessly! Your compensatory off balance grows with every additional hour worked on weekdays, week-offs, holidays, or through submitted wage claims. It's our way of recognizing your dedication beyond the regular hours."
                  backgroundColor="transparent"
                  class="mb-4"
                ></NotesCard>
                <NotesCard
                  notes="Compensatory off balance comes with an expiry, but admins can override it based on your unique needs."
                  backgroundColor="transparent"
                  class="mb-2"
                ></NotesCard>
              </v-col>
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <v-btn
                  v-if="formName === 'MyTeam'"
                  class="primary my-2 ml-2"
                  :size="isMobileView ? 'small' : 'default'"
                  rounded="lg"
                >
                  <v-icon size="14">fas fa-calendar-alt</v-icon>
                  <span class="text-caption px-1">Worked Date:</span>
                  <flat-pickr
                    v-model="emptyFilterDateRange"
                    :config="flatPickerOptions"
                    placeholder="Select Date"
                    class="ml-2 date-range-picker-custom-bg"
                    style="outline: 0px; color: var(--v-primary-base)"
                    :style="
                      isMobileView
                        ? 'width: 70px;  text-overflow: ellipsis;'
                        : 'width: 180px'
                    "
                    @onClose="onChangeApprovalData"
                  ></flat-pickr>
                  <v-icon class="mx-1" size="15" @click="resetDateRange()"
                    >fas fa-redo-alt</v-icon
                  >
                </v-btn>
                <v-btn
                  rounded="lg"
                  color="transparent"
                  variant="flat"
                  class="ml-2 mt-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchData()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <div v-else>
        <v-row>
          <v-col
            :cols="isAddViewEditFormOpened && windowWidth >= 1264 ? 5 : 12"
          >
            <ListCompOffBalance
              :items="mainList"
              :backupMainList="backupMainList"
              :formName="formName"
              :accessFormName="accessFormName"
              :is-small-table="isAddViewEditFormOpened"
              :filteredDateRange="filteredDateRange"
              @on-change-date-range="filteredDateRange = $event"
              @open-view-form="onOpenViewForm($event)"
              @refetch-data="refetchData()"
              @reset-search-filter="resetFilter()"
              @open-dialog-form="openDialogForm()"
            ></ListCompOffBalance>
          </v-col>
          <v-col
            v-if="isAddViewEditFormOpened && windowWidth >= 1264"
            :cols="backupMainList && backupMainList.length === 0 ? 12 : 7"
          >
            <ViewCompOffBalance
              v-if="showViewForm"
              @open-edit-form="openEditForm()"
              :formName="formName"
              @close-form="closeAllForms"
              :selectedItem="selectedItem"
              :formAccess="formAccess"
              :accessFormName="accessFormName"
            ></ViewCompOffBalance>
            <EditCompOffBalance
              v-else
              :selectedItem="selectedItem"
              :isEdit="isEdit"
              @close-form="closeAllForms"
              @refetch-data="refetchData()"
              :accessFormName="accessFormName"
            >
            </EditCompOffBalance>
          </v-col>
        </v-row>
      </div>
    </section>
    <AppAccessDenied v-else></AppAccessDenied>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <ViewCompOffBalance
        v-if="showViewForm"
        @open-edit-form="openEditForm()"
        @close-form="closeAllForms()"
        :formName="formName"
        :selectedItem="selectedItem"
        :formAccess="formAccess"
        :accessFormName="accessFormName"
      />
      <EditCompOffBalance
        v-if="showEditForm"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        @close-form="closeAllForms()"
        @refetch-data="refetchData()"
        :accessFormName="accessFormName"
      />
    </v-dialog>
    <v-dialog v-model="openHistoryExportTemplate" persistent width="900">
      <ExportHistory
        :accessFormName="accessFormName"
        @close-dialog-form="closeDialogForm()"
      />
    </v-dialog>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EditCompOffBalance = defineAsyncComponent(() =>
  import("./EditCompOffBalance.vue")
);
const ExportHistory = defineAsyncComponent(() => import("./ExportHistory.vue"));
import Datepicker from "vuejs3-datepicker";

// components
import moment from "moment";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import ViewCompOffBalance from "./ViewCompOffBalance.vue";
import ListCompOffBalance from "./ListCompOffBalance.vue";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
// Queries
import { LIST_COMPENSATORY_OFF_BALANCE } from "@/graphql/my-team/comp-off-balance/compOffBalanceQueries.js";

export default {
  name: "CompOffBalance",
  components: {
    ViewCompOffBalance,
    NotesCard,
    ListCompOffBalance,
    EditCompOffBalance,
    ExportHistory,
    EmployeeDefaultFilterMenu,
    Datepicker,
    flatPickr,
  },
  props: {
    formName: {
      type: String,
      required: true,
    },
    formId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      isEdit: false,
      showViewForm: false,
      selectedItem: {},
      showEditForm: false,
      mainList: [],
      backupMainList: [],
      openHistoryExportTemplate: false,
      currentTabItem: "tab-1",
      expiryFromDate: null,
      expiryToDate: null,
      workedToDate: null,
      workedFromDate: null,
      filteredDateRange: null,
      isExceed61Days: false,
      emptyFilterDateRange: "",
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights(this.formId);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    accessFormName() {
      let fAccess = this.accessRights(this.formId);
      if (fAccess && fAccess.customFormName) {
        return fAccess.customFormName;
      } else return "Compensatory Off Balance";
    },
    compensatoryOffFormAccess() {
      let fAccess =
        this.$route.query?.from?.toLowerCase() === "myteam"
          ? this.accessRights("334")
          : this.accessRights("335");
      if (fAccess && fAccess.accessRights && fAccess.accessRights["view"]) {
        return fAccess.accessRights;
      } else return false;
    },
    compensatoryOffFormName() {
      let formName =
        this.$route.query?.from?.toLowerCase() === "myteam"
          ? this.accessRights("334")
          : this.accessRights("335");
      if (formName?.customFormName && formName.customFormName !== "") {
        return formName.customFormName;
      } else return "Compensatory Off";
    },
    mainTabs() {
      if (this.compensatoryOffFormAccess && this.formAccess) {
        return [this.compensatoryOffFormName, this.accessFormName];
      } else if (this.formAccess) {
        return [this.accessFormName];
      } else if (this.compensatoryOffFormAccess) {
        return [this.compensatoryOffFormName];
      } else {
        return [];
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isAddViewEditFormOpened() {
      return this.showViewForm || this.showEditForm;
    },
    openFormInModal() {
      if (this.isAddViewEditFormOpened && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    expiryMinDate() {
      if (this.expiryFromDate) {
        const issueDateMs = new Date(this.expiryFromDate)
          .toISOString()
          .substring(0, 10);
        return issueDateMs;
      }
      return null;
    },
    expiryMaxDate() {
      if (this.expiryToDate) {
        const issueDateMs = new Date(this.expiryToDate)
          .toISOString()
          .substring(0, 10);
        return issueDateMs;
      }
      return null;
    },
    workedMinDate() {
      if (this.workedFromDate) {
        const issueDateMs = new Date(this.workedFromDate)
          .toISOString()
          .substring(0, 10);
        return issueDateMs;
      }
      return null;
    },
    workedMaxDate() {
      if (this.workedToDate) {
        const issueDateMs = new Date(this.workedToDate)
          .toISOString()
          .substring(0, 10);
        return issueDateMs;
      }
      return null;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment().format(this.$store.state.orgDetails.orgDateFormat),
      };
    },
    filteredApprovalStartAndEndDate() {
      if (this.filteredDateRange) {
        if (this.filteredDateRange.includes("to")) {
          let splittedDate = this.filteredDateRange.split(" to ");
          return {
            startDate: splittedDate[0],
            endDate: splittedDate[1],
          };
        } else {
          return {
            startDate: this.filteredDateRange,
            endDate: this.filteredDateRange,
          };
        }
      } else {
        return {
          startDate: "",
          endDate: "",
        };
      }
    },
    compOffFormAccess() {
      let formAccess = this.accessRights("334");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    employeeSelfServiceFormAccess() {
      let formAccess = this.accessRights("335");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("Compensatory Off Balance Error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },
  watch: {
    filteredDateRange() {
      this.refetchData();
    },
    emptyFilterDateRange() {
      if (this.isExceed61Days) {
        this.emptyFilterDateRange = this.filteredDateRange;
        this.isExceed61Days = false;
      }
    },
  },
  mounted() {
    // form the filter date as form prev month start date to current date
    let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
    let currentDate = moment().format(orgDateFormat);
    let prevMonthStartDate = moment()
      .subtract("1", "month")
      .startOf("month")
      .format(orgDateFormat);
    this.filteredDateRange = prevMonthStartDate + " to " + currentDate;
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.accessFormName);
    this.fetchData();
  },
  methods: {
    onTabChange(tab) {
      if (tab !== this.accessFormName) {
        if (
          tab == this.compensatoryOffFormName &&
          this.$route.query?.from?.toLowerCase() === "myteam" &&
          this.compOffFormAccess
        ) {
          this.$router.push("/my-team/compensatory-off");
        } else if (
          tab == this.compensatoryOffFormName &&
          this.$route.query?.from?.toLowerCase() === "emp" &&
          this.employeeSelfServiceFormAccess
        ) {
          this.$router.push("/employee-self-service/compensatory-off");
        } else if (tab == this.compensatoryOffFormName) {
          window.location.href = this.baseUrl + "employees/compensatory-off";
        }
      }
    },
    resetDateRange() {
      // form the filter date as form prev month start date to current date
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      let currentDate = moment().format(orgDateFormat);
      let prevMonthStartDate = moment()
        .subtract("1", "month")
        .startOf("month")
        .format(orgDateFormat);
      this.filteredDateRange = prevMonthStartDate + " to " + currentDate;
      this.fetchData();
    },
    onChangeApprovalData(selectedDates, dateStr) {
      this.isExceed61Days = false;
      if (dateStr.includes("to")) {
        let splittedDate = dateStr.split(" to ");
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        const startMoment = moment(splittedDate[0], orgDateFormat);
        const endMoment = moment(splittedDate[1], orgDateFormat);
        const diffInDays = endMoment.diff(startMoment, "days");
        if (diffInDays <= 61) {
          this.filteredDateRange = dateStr;
        } else {
          this.isExceed61Days = true;
        }
      } else {
        this.filteredDateRange = dateStr;
      }
      if (this.isExceed61Days) {
        this.emptyFilterDateRange = this.filteredDateRange;
        let snackbarData = {
          isOpen: true,
          message: "Please select a date range of less than 61 days",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    resetFilter() {
      this.mainList = this.backupMainList;
      this.expiryFromDate = null;
      this.expiryToDate = null;
      this.workedToDate = null;
      this.workedFromDate = null;
      if (this.$refs.employeeDefaultFilter) {
        this.$refs.employeeDefaultFilter.resetAllModelValues();
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    applyFilter(filteredArray) {
      if (this.expiryFromDate) {
        filteredArray = filteredArray.filter((item) => {
          if (item) {
            return moment(item.Expiry_Date).isSameOrAfter(
              moment(this.expiryFromDate).format("YYYY-MM-DD")
            );
          } else return item;
        });
      }
      if (this.expiryToDate) {
        filteredArray = filteredArray.filter((item) => {
          if (item) {
            return moment(item.Expiry_Date).isSameOrBefore(
              moment(this.expiryToDate).format("YYYY-MM-DD")
            );
          } else return item;
        });
      }
      if (this.workedFromDate) {
        filteredArray = filteredArray.filter((item) => {
          if (item) {
            return moment(item.Worked_Date).isSameOrAfter(
              moment(this.workedFromDate).format("YYYY-MM-DD")
            );
          } else return item;
        });
      }
      if (this.workedToDate) {
        filteredArray = filteredArray.filter((item) => {
          if (item) {
            return moment(item.Worked_Date).isSameOrBefore(
              moment(this.workedToDate).format("YYYY-MM-DD")
            );
          } else return item;
        });
      }
      this.mainList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    openDialogForm() {
      this.openHistoryExportTemplate = true;
    },
    closeDialogForm() {
      this.openHistoryExportTemplate = false;
    },
    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showEditForm = true;
    },
    closeAllForms() {
      this.isEdit = false;
      this.showEditForm = false;
      this.showViewForm = false;
    },
    refetchData() {
      this.errorContent = "";
      this.isErrorInList = false;
      this.closeAllForms();
      this.fetchData();
    },
    onOpenViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    fetchData() {
      let vm = this;
      vm.listLoading = true;
      let sDate = "",
        eDate = "";
      if (vm.formName == "MyTeam") {
        let filterStartEndDate = vm.filteredApprovalStartAndEndDate;
        let orgDateFormat = vm.$store.state.orgDetails.orgDateFormat;
        sDate = moment(filterStartEndDate.startDate, orgDateFormat).format(
          "YYYY-MM-DD"
        );
        eDate = moment(filterStartEndDate.endDate, orgDateFormat).format(
          "YYYY-MM-DD"
        );
        vm.emptyFilterDateRange = vm.filteredDateRange;
      }
      vm.$apollo
        .query({
          query: LIST_COMPENSATORY_OFF_BALANCE,
          variables: {
            selfService: vm.formName == "MyTeam" ? 0 : 1,
            defaultFilterStartDate: sDate,
            defaultFilterEndDate: eDate,
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listCompensatoryOffBalance) {
            let mainList =
              response.data.listCompensatoryOffBalance.compOffBalanceDetails;
            vm.mainList = mainList;
            vm.backupMainList = mainList;
            vm.resetFilter();
            vm.listLoading = false;
          } else {
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.filter-menu-overlay {
  position: fixed;
  top: 4.4rem;
  right: 5rem;
  z-index: 999;
}
.comp-off-balance-container {
  padding: 5em 2em 0em 3em;
}
.v-dialog {
  box-shadow: none;
}
@media screen and (max-width: 805px) {
  .comp-off-balance-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
