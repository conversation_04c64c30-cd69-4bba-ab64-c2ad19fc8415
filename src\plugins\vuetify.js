import "@fortawesome/fontawesome-free/css/all.css";
import "vuetify/styles";
import { createVuetify } from "vuetify";
import { VCard } from "vuetify/lib/components/VCard";
import { fa, aliases } from "vuetify/iconsets/fa";
import { VTabs } from "vuetify/lib/components";
import { VBtn, VIcon, VTextField } from "vuetify/lib/components";
import { VSwitch } from "vuetify/lib/components";
import { VTimePicker } from "vuetify/labs/VTimePicker";

export default function createVuetifyInstance(colors) {
  return new createVuetify({
    icons: {
      defaultSet: "fa",
      sets: {
        fa,
      },
      aliases,
    },
    theme: {
      themes: {
        light: {
          colors: {
            primary: colors ? colors.Primary_Color : "#260029",
            secondary: colors ? colors.Secondary_Color : "#EC407A",
            lightblue: "#8b8d8e",
            hover: colors ? colors.Hover_Color : "#e5e9eb",
            header: colors ? colors.Table_Header_Color : "#FFFFFF",
            headerTextColor: colors
              ? colors.Table_Header_Text_Color
              : "#000000",
            grey: "#9E9E9E",
          },
        },
        dark: {
          colors: {
            primary: colors ? colors.Primary_Color : "#260029",
            secondary: colors ? colors.Secondary_Color : "#EC407A",
            lightblue: "#8b8d8e",
            hover: colors ? colors.Hover_Color : "#e5e9eb",
            header: colors ? colors.Table_Header_Color : "#FFFFFF",
            headerTextColor: colors
              ? colors.Table_Header_Text_Color
              : "#000000",
            grey: "#9E9E9E",
          },
        },
      },
    },
    aliases: {
      ProfileCard: VCard,
      FormTab: VTabs,
      FormButton: VBtn,
      editButton: VBtn,
      addButton: VBtn,
      formTextFeild: VTextField,
      iconInsideButton: VIcon,
      switchButton: VSwitch,
    },
    components: {
      VTimePicker,
    },
    defaults: {
      iconInsideButton: {
        color: "secondary",
        size: 12,
        class: "mr-1",
      },
      FormTab: {
        activeClass: "primary",
        inactiveClass: "grey lighten-2",
        ripple: true,
        small: false,
        disabled: false,
        underlineColor: "pink",
        showArrows: true,
        centerActive: true,
        class: "ml-5 mr-5 ",
      },
      editButton: {
        color: "secondary",
        variant: "text",
        class: "mx-2",
      },
      addButton: {
        color: "secondary",
        variant: "text",
        class: "ma-5",
      },
      switchButton: {
        color: "secondary",
      },
      formTextFeild: {
        variant: "solo",
      },
      ProfileCard: {
        elevation: 2,
        ripple: true,
        flat: true,
        width: "100%",
      },
      FormButton: {
        color: "secondary",
        rounded: "lg",
        class: "ma-2 pa-2",
      },
    },
  });
}
