<template>
  <div class="mt-3 mx-5" style="height: calc(100vh - 180px)">
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList && !isLoading"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      :button-text="'Retry'"
      @button-click="reFetchEarlyCheckoutList()"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="itemList.length === 0 && !isLoading"
      key="no-results-screen"
      :main-title="'There are no records for the selected filters/searches.'"
      :image-name="
        originalList?.length === 0
          ? 'workflow/empty-approval'
          : 'common/no-records'
      "
      :isSmallImage="originalList.length === 0"
    >
      <template #contentSlot>
        <div class="d-flex mb-2 flex-wrap justify-center align-center">
          <v-btn
            v-if="originalList.length === 0"
            class="bg-white my-2 ml-2"
            :style="'width: max-content'"
            :size="isMobileView ? 'small' : 'default'"
            rounded="lg"
            @click="$refs.datePicker.fp.open()"
          >
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            <span class="text-caption px-1">Date:</span>
            <flat-pickr
              ref="datePicker"
              v-model="appliedDateRange"
              :config="flatPickerOptions"
              placeholder="Select Date Range"
              class="ml-2 mt-1 date-range-picker-custom-bg"
              style="outline: 0px; color: var(--v-primary-base); width: 170px"
              @onChange="onChangeDateRange"
            ></flat-pickr>
          </v-btn>
          <v-btn
            v-if="originalList.length === 0"
            color="transparent"
            class="mt-1"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="reFetchEarlyCheckoutList()"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
          <v-btn
            v-if="originalList.length > 0"
            color="primary"
            variant="elevated"
            class="ml-4 mt-1"
            rounded="lg"
            :size="isMobileView ? 'small' : 'default'"
            @click="$emit('reset-filter')"
          >
            Reset Filter/Search
          </v-btn>
        </div>
      </template>
    </AppFetchErrorScreen>

    <div v-else>
      <div
        v-if="originalList.length"
        :class="{
          'mb-3': !isMobileView,
          'd-flex': true,
          'justify-space-between': !isMobileView,
          'align-center': true,
          'flex-column': isMobileView,
        }"
      >
        <v-btn
          class="bg-white my-2 ml-2"
          :class="isMobileView ? 'mb-2' : ''"
          :style="'width: max-content'"
          :size="isMobileView ? 'small' : 'default'"
          rounded="lg"
          @click="$refs.datePicker.fp.open()"
        >
          <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
          <span class="text-caption px-1">Date:</span>
          <flat-pickr
            ref="datePicker"
            v-model="appliedDateRange"
            :config="flatPickerOptions"
            placeholder="Select Date Range"
            class="ml-2 mt-1 date-range-picker-custom-bg"
            style="outline: 0px; color: var(--v-primary-base); width: 170px"
            @onChange="onChangeDateRange"
          ></flat-pickr>
        </v-btn>
        <div :class="isMobileView ? 'd-flex flex-column' : 'd-flex'">
          <v-btn
            color="primary"
            :class="isMobileView ? 'mb-2' : ''"
            rounded="lg"
            class="ml-2"
            @click="onClickInitiate()"
            :size="isMobileView ? 'small' : 'default'"
          >
            <span class="text-white ml-2">Initiate Leave as per policy</span>
          </v-btn>
          <v-btn
            color="primary"
            :class="isMobileView ? 'mb-2' : ''"
            rounded="lg"
            class="ml-2"
            @click="onIgnoreEarlyCheckout()"
            :size="isMobileView ? 'small' : 'default'"
          >
            <span class="text-white ml-2">Ignore</span>
          </v-btn>
          <div class="d-flex justify-center align-center">
            <v-btn
              color="transparent"
              class="mt-1"
              variant="flat"
              :size="isMobileView ? 'small' : 'default'"
              @click="reFetchEarlyCheckoutList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu v-model="openMoreMenu" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
                  <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                  <v-icon v-else>fas fa-caret-up</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                      >
                        {{ action.key }}
                      </v-list-item-title>
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
      </div>
      <v-row>
        <v-col v-if="originalList.length > 0">
          <v-data-table
            v-model="selectedRecords"
            :headers="tableHeaders"
            :items="itemList"
            item-value="uniqueId"
            fixed-header
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(290, itemList)
            "
            :show-select="!isMobileView"
            :items-per-page="50"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              {
                value: -1,
                title: '$vuetify.dataFooter.itemsPerPageAll',
              },
            ]"
          >
            <template v-slot:[`header.data-table-select`]="{ selectAll }">
              <v-checkbox-btn
                v-model="selectAllBox"
                color="primary"
                false-icon="far fa-circle"
                true-icon="fas fa-check-circle"
                :indeterminate="
                  selectedItems.length !== 0 &&
                  selectedItems.length !== itemList.length
                "
                indeterminate-icon="fas fa-minus-circle"
                @change="selectAll(selectAllBox)"
              ></v-checkbox-btn>
            </template>
            <template v-slot:item="{ item }">
              <tr
                class="data-table-tr bg-white cursor-pointer"
                :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
              >
                <td :class="isMobileView ? 'mt-3 mb-n5' : ''">
                  <v-checkbox-btn
                    v-model="item.isSelected"
                    color="primary"
                    false-icon="far fa-circle"
                    true-icon="fas fa-check-circle"
                    class="mt-n2 ml-n2"
                    @click.stop="
                      {
                      }
                    "
                    @change="checkAllSelected()"
                  ></v-checkbox-btn>
                </td>
                <td
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div v-if="isMobileView" class="font-weight-bold">
                    Employee
                  </div>
                  <section>
                    <div style="max-width: 200px" class="text-truncate">
                      <span class="text-primary text-body-2 font-weight-medium">
                        <v-tooltip :text="item.Employee_Name" location="bottom">
                          <template v-slot:activator="{ props }">
                            <span
                              v-bind="
                                item.Employee_Name &&
                                item.Employee_Name.length > 20
                                  ? props
                                  : ''
                              "
                              >{{ checkNullValue(item.Employee_Name) }}</span
                            >
                          </template>
                        </v-tooltip>
                        <v-tooltip
                          :text="item.User_Defined_Employee_Id?.toString()"
                          location="bottom"
                        >
                          <template v-slot:activator="{ props }">
                            <div
                              v-if="item.User_Defined_Employee_Id"
                              v-bind="
                                item.User_Defined_Employee_Id &&
                                item.User_Defined_Employee_Id.length > 20
                                  ? props
                                  : ''
                              "
                              class="text-grey"
                            >
                              {{
                                checkNullValue(item.User_Defined_Employee_Id)
                              }}
                            </div>
                          </template>
                        </v-tooltip>
                      </span>
                    </div>
                  </section>
                </td>
                <td
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div v-if="isMobileView" class="font-weight-bold">
                    Attendance Date
                  </div>
                  <section>
                    {{ formatDate(item.Attendance_Date) }}
                  </section>
                </td>
                <td
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div v-if="isMobileView" class="font-weight-bold">
                    Work Schedule (Shift Time)
                  </div>
                  <section>
                    {{ getShiftTime(item) }}
                  </section>
                </td>
                <td
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div v-if="isMobileView" class="font-weight-bold">
                    First In
                  </div>
                  <section>
                    {{ formatDateTime(item.First_In) }}
                  </section>
                </td>
                <td
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div v-if="isMobileView" class="font-weight-bold">
                    Last Out
                  </div>
                  <section>
                    {{ formatDateTime(item.Last_Out) }}
                  </section>
                </td>
                <td
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div v-if="isMobileView" class="font-weight-bold">
                    Early Checkout Hours
                  </div>
                  <section>
                    {{ formatTime(item.Early_Checkout_Hours) }}
                  </section>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </div>
  </div>
  <AppWarningModal
    v-if="confirmationModel"
    :open-modal="confirmationModel"
    :confirmation-heading="'Are you sure you want to proceed with initiating leave for early check-out on the selected records as per the policy?'"
    :icon-name="''"
    :icon-color="'red'"
    :icon-Size="'75'"
    @close-warning-modal="confirmationModel = false"
    @accept-modal="initiateEarlyCheckOut()"
  />
  <AppWarningModal
    v-if="ignoreModel"
    :open-modal="ignoreModel"
    :confirmation-heading="''"
    :icon-name="'fas fa-ban'"
    :icon-color="'red'"
    :icon-Size="'75'"
    @close-warning-modal="ignoreModel = false"
    @accept-modal="ignoreEarlyCheckOut()"
  >
    <template v-slot:warningModalContent>
      <div
        class="text-h6 justify-center font-weight-bold text-primary text-center px-6 pt-2"
      >
        Are you sure to proceed with this action?
      </div>
      <div class="text-center mt-3 text-grey">
        Early check-out is applicable as per the policy, but clicking the ignore
        option will exclude the selected record(s) from leave deduction
      </div>
    </template>
  </AppWarningModal>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import {
  LIST_EARLY_CHECKOUT,
  INITIATE_EARLY_CHECKOUT,
  IGNORE_EARLY_CHECKOUT,
} from "@/graphql/my-team/attendance-finalization";
import moment from "moment";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "EarlyCheckOut",
  components: { flatPickr },
  mixins: [FileExportMixin],
  emits: ["reset-filter", "list-count", "show-shortage-tab"],
  props: {
    salaryStartDate: { type: String, required: true },
    salaryEndDate: { type: String, required: true },
    filterObj: { type: Object, default: () => ({}) },
    filterAppliedCount: { type: Number, default: 0 },
    preReq: { type: Number, default: 0 },
    payslipEmployeeIds: { type: Array, default: () => [] },
  },
  data() {
    return {
      listLoading: false,
      isLoading: false,
      isErrorInList: false,
      errorContent: "",
      itemList: [],
      originalList: [],
      appliedDateRange: null,
      openMoreMenu: false,
      startDate: "",
      endDate: "",
      selectAllBox: false,
      selectedRecords: [],
      selectedItems: [],
      confirmationModel: false,
      maxDate: new Date(),
      ignoreModel: false,
    };
  },
  computed: {
    mainTabs() {
      return [this.accessRights(324).customFormName];
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(324);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment(this.maxDate).format(
          this.$store.state.orgDetails.orgDateFormat
        ),
      };
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgFormat);
        } else {
          return "-";
        }
      };
    },
    formatDateTime() {
      return (dateTime) => {
        let date = new Date(dateTime);
        if (moment(date).isValid()) {
          let orgFormat = this.$store.state.orgDetails.orgDateFormat + " HH:mm";
          return moment(date).format(orgFormat);
        } else {
          return "-";
        }
      };
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    tableHeaders() {
      return [
        {
          title: "Employee",
          align: "start",
          key: "Employee_Name",
        },
        {
          title: "Attendance Date",
          key: "Attendance_Date",
        },
        {
          title: "Work Schedule (Shift Time)",
          key: "Regular_From_Date_Time",
        },
        {
          title: "First In",
          key: "First_In",
        },
        {
          title: "Last Out",
          key: "Last_Out",
        },
        {
          title: "Early Checkout Hours",
          key: "Early_Checkout_Hours",
        },
      ];
    },
    formatTime() {
      return (time) => {
        if (time) {
          const [hours, minutes] = time.split(":").map(Number);
          let formattedTime = "";
          if (hours && minutes) {
            formattedTime = `${hours} Hrs ${minutes} Mins`;
          } else if (hours) {
            formattedTime = `${hours} Hrs`;
          } else if (minutes) {
            formattedTime = `0 Hrs ${minutes} Mins`;
          }
          return formattedTime;
        } else {
          return "-";
        }
      };
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    getShiftTime() {
      return (item) => {
        if (item.Regular_From_Date_Time && item.Regular_End_Date_Time) {
          if (
            moment(item.Regular_From_Date_Time).isValid() &&
            moment(item.Regular_End_Date_Time).isValid()
          ) {
            return (
              moment(item.Regular_From_Date_Time).format("HH:mm") +
              " - " +
              moment(item.Regular_End_Date_Time).format("HH:mm")
            );
          } else "-";
        } else return "-";
      };
    },
    convertMinToHrsMin() {
      return (totalMinutes) => {
        if (totalMinutes >= 0) {
          let hours = Math.floor(totalMinutes / 60);
          let minutes = totalMinutes % 60;
          return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
            2,
            "0"
          )}`;
        } else {
          return "  ";
        }
      };
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    selectedRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemLogList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.uniqueId)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
          let index = this.selectedItems.findIndex(
            (x) => x.uniqueId === item.uniqueId
          );
          if (index === -1) {
            this.selectedItems.push(item);
          }
        }
      } else {
        // Iterate through itemLogList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
        this.selectedItems = [];
      }
    },
    filterAppliedCount(val) {
      if (val) {
        this.applyFilter();
      } else {
        this.itemList = this.originalList;
      }
    },
    itemList: {
      handler(newList) {
        this.$emit("list-count", newList.length);
      },
      deep: true,
      immediate: true,
    },
    salaryStartDate(val) {
      if (val) {
        this.startDate = val;
        this.getSalaryDayDetails();
      }
    },
    salaryEndDate(val) {
      if (val) {
        this.endDate = val;
        this.getSalaryDayDetails();
      }
    },
  },
  mounted() {
    this.getSalaryDayDetails();
  },
  methods: {
    checkNullValue,
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectedItems = selectedItems;
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    applyFilter() {
      if (this.filterAppliedCount) {
        let filteredArray = this.originalList;
        if (this.filterObj.selectedEmployees.length > 0) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedEmployees.includes(item.Employee_Id);
          });
        }
        if (this.filterObj.selectedMinFirstIn) {
          let min = moment(this.filterObj.selectedMinFirstIn, "HH:mm");
          filteredArray = filteredArray.filter((item) => {
            return moment(item.First_In).format("HH:mm") >= min.format("HH:mm");
          });
        }
        if (this.filterObj.selectedMaxFirstIn) {
          let min = moment(this.filterObj.selectedMaxFirstIn, "HH:mm");
          filteredArray = filteredArray.filter((item) => {
            return moment(item.First_In).format("HH:mm") <= min.format("HH:mm");
          });
        }
        if (this.filterObj.selectedMinLastOut) {
          let min = moment(this.filterObj.selectedMinLastOut, "HH:mm");
          filteredArray = filteredArray.filter((item) => {
            return moment(item.Last_Out).format("HH:mm") >= min.format("HH:mm");
          });
        }
        if (this.filterObj.selectedMaxLastOut) {
          let min = moment(this.filterObj.selectedMaxLastOut, "HH:mm");
          filteredArray = filteredArray.filter((item) => {
            return moment(item.Last_Out).format("HH:mm") <= min.format("HH:mm");
          });
        }
        if (this.filterObj.rangeHour.length) {
          filteredArray = filteredArray.filter((item) => {
            let range = moment(item.Early_Checkout_Hours, "HH:mm:ss").format(
              "HH:mm"
            );
            return (
              range >= this.convertMinToHrsMin(this.filterObj.rangeHour[0]) &&
              range <= this.convertMinToHrsMin(this.filterObj.rangeHour[1])
            );
          });
        }
        if (this.filterObj.selectedServiceProvider.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedServiceProvider.includes(
              item.Service_Provider_Id
            );
          });
        }
        this.itemList = filteredArray;
      }
    },
    onClickInitiate() {
      let msg = "";
      if (this.formAccess.optionalChoice) {
        if (this.selectedItems.length === 0)
          msg = "For Initiate, you have to select atleast one record.";
        else msg = "";
      } else msg = "Sorry, you don't have access to perform this action.";
      if (msg === "") {
        this.confirmationModel = true;
      } else {
        let snackbarData = {
          isOpen: true,
          message: msg,
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    onIgnoreEarlyCheckout() {
      if (this.formAccess.optionalChoice) {
        if (this.selectedItems.length === 0) {
          let snackbarData = {
            isOpen: true,
            message: "For Ignore, you have to select atleast one record.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        } else {
          this.ignoreModel = true;
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Sorry, you don't have access to perform this action.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    onChangeDateRange(selectedDates) {
      if (selectedDates.length > 1) {
        // Parse the dates from the given format
        let parsedStartDate = moment(selectedDates[0], "DD/MM/YYYY");
        let parsedEndDate = moment(
          selectedDates.length > 1 ? selectedDates[1] : selectedDates[0],
          "DD/MM/YYYY"
        );

        // Format the dates into "YYYY-MM-DD" format
        this.startDate = parsedStartDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.fetchEarlyCheckoutList();
      }
    },
    onMoreAction(actionType) {
      if (actionType?.toLowerCase() === "export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Employee Id", key: "User_Defined_Employee_Id" },
        { header: "Employee", key: "Employee_Name" },
        { header: "Attendance Date", key: "Attendance_Date" },
        { header: "Work Schedule (Shift Time)", key: "Shift_Time" },
        { header: "First In", key: "First_In" },
        { header: "Last Out", key: "Last_Out" },
        { header: "Early Checkout Hours", key: "Early_Checkout_Hours" },
      ];
      let dataList = this.itemList.map((item) => {
        return {
          ...item,
          Attendance_Date: this.formatDate(item.Attendance_Date),
          First_In: this.formatDateTime(item.First_In),
          Last_Out: this.formatDateTime(item.Last_Out),
          Shift_Time: this.getShiftTime(item),
        };
      });

      const exportOptions = {
        fileExportData: dataList,
        fileName: "Early Checkout Report",
        sheetName: "Early checkout Details",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
      }
    },
    getSalaryDayDetails() {
      let orgFormat = this.$store.state.orgDetails.orgDateFormat;
      this.startDate = this.salaryStartDate;
      this.endDate = this.salaryEndDate;

      this.appliedDateRange =
        moment(this.startDate).format(orgFormat) +
        " to " +
        moment(this.endDate).format(orgFormat);
      this.fetchEarlyCheckoutList();
    },
    fetchEarlyCheckoutList() {
      let vm = this;
      vm.listLoading = true;
      if (!this.startDate || !this.endDate) return;
      vm.$apollo
        .query({
          query: LIST_EARLY_CHECKOUT,
          client: "apolloClientAC",
          variables: {
            startDate: this.startDate,
            endDate: this.endDate,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.listEarlyCheckOutEmployees &&
            data.listEarlyCheckOutEmployees.summaryDetails
          ) {
            let records = data.listEarlyCheckOutEmployees.summaryDetails;
            records = records.map((item) => {
              return {
                ...item,
                uniqueId: item.Employee_Id + "--" + item.Attendance_Date,
              };
            });
            vm.itemList = records;
            vm.originalList = records;
            vm.applyFilter();
          }
          this.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.$store
            .dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: "Early checkout",
              isListError: true,
            })
            .then((errorMessages) => {
              vm.errorContent = errorMessages;
              vm.isErrorInList = true;
            });
        });
    },
    reFetchEarlyCheckoutList() {
      this.selectAllBox = false;
      this.selectedItems = [];
      this.fetchEarlyCheckoutList();
    },
    initiateEarlyCheckOut() {
      let vm = this;
      vm.confirmationModel = false;
      vm.isLoading = true;
      let employeeData = this.selectedItems.map((item) => {
        return item.Attendance_Summary_Id;
      });
      vm.$apollo
        .mutate({
          mutation: INITIATE_EARLY_CHECKOUT,
          client: "apolloClientBH",
          variables: {
            attendanceSummaryIds: employeeData,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.triggerEarlyCheckoutProcess &&
            !response.data.triggerEarlyCheckoutProcess.errorCode
          ) {
            let snackbarData = {
              isOpen: true,
              message: "Early Checkout Initiated Successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            vm.selectedItems = [];
            vm.selectAllBox = false;
            vm.fetchEarlyCheckoutList();
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "Initiate",
            form: "Early Checkout",
            isListError: false,
          });
        });
    },
    ignoreEarlyCheckOut() {
      let vm = this;
      vm.ignoreModel = false;
      vm.isLoading = true;
      let employeeData = this.selectedItems.map((item) => {
        return item.Attendance_Summary_Id;
      });
      vm.$apollo
        .mutate({
          mutation: IGNORE_EARLY_CHECKOUT,
          client: "apolloClientAD",
          variables: {
            attendanceSummaryIds: employeeData,
            isIgnore: true,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.ignoreEarlyCheckout &&
            !response.data.ignoreEarlyCheckout.errorCode
          ) {
            let snackbarData = {
              isOpen: true,
              message:
                response.data.ignoreEarlyCheckout.message ||
                "Early Checkout Ignored Successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            vm.selectedItems = [];
            vm.selectAllBox = false;
            vm.fetchEarlyCheckoutList();
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "Ignore",
            form: "Early Checkout",
            isListError: false,
          });
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.early-checkout-container {
  padding: 5em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .early-checkout-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
