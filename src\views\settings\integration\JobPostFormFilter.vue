<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <p class="text-subtitle-1 text-grey-darken-1">Job Title</p>
            <v-autocomplete
              v-model="selectedJobTitle"
              color="primary"
              :items="jobTitleList"
              item-title="jobTitle"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <!-- <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <p class="text-subtitle-1 text-grey-darken-1">Client</p>
            <v-autocomplete
              v-model="selectedClient"
              color="primary"
              :items="clientList"
              item-title="Company_Name"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col> -->
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <p class="text-subtitle-1 text-grey-darken-1">Closing Date</p>
            <CustomSelect
              v-model="selectedClosingPeriod"
              :items="closingPeriod"
              density="compact"
              variant="solo"
              :isAutoComplete="true"
              :select-properties="{
                clearable: true,
                closableChips: true,
              }"
              :itemSelected="selectedClosingPeriod"
              @selected-item="selectedClosingPeriod = $event"
            ></CustomSelect>
          </v-col>
          <v-col
            v-if="selectedClosingPeriod == 'Custom Range'"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              Custom Range (in days) {{ otherClosingDays }}
            </p>
            <v-slider
              v-model="otherClosingDays"
              :max="365"
              :step="1"
              color="orange"
            ></v-slider>
          </v-col>
          <v-col
            v-if="displayServiceProvider"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ getCustomFieldName(115, "Service Provider") }}
            </p>
            <v-autocomplete
              v-model="selectedServiceProvider"
              color="primary"
              :items="serviceProviderList"
              item-title="Service_Provider_Name"
              item-value="Service_Provider_Id"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <p class="text-subtitle-1 text-grey-darken-1">Status</p>
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="statusList"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12">
            <p class="text-subtitle-1 text-grey-darken-1">
              Number of Vacancies
            </p>
            <v-row>
              <v-col :cols="windowWidth > 600 ? 6 : 12">
                <v-autocomplete
                  v-model="selectedOption"
                  color="primary"
                  :items="dropdownOptions"
                  label=">=,= or <="
                  closable-chips
                  chips
                  density="compact"
                  single-line
                  variant="solo"
                >
                </v-autocomplete>
              </v-col>
              <v-col :cols="windowWidth > 600 ? 6 : 12">
                <v-text-field
                  v-model="typableValue"
                  variant="solo"
                  :disabled="isVacancyValueDisabled()"
                  density="compact"
                  single-line
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <p class="text-subtitle-1 text-grey-darken-1">Posting Date</p>
            <CustomSelect
              v-model="selectedPostingPeriod"
              :items="postingPeriod"
              density="compact"
              variant="solo"
              :isAutoComplete="true"
              :select-properties="{
                clearable: true,
                closableChips: true,
              }"
              :itemSelected="selectedPostingPeriod"
              @selected-item="selectedPostingPeriod = $event"
            ></CustomSelect>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <p class="text-subtitle-1 text-grey-darken-1">Posted By</p>
            <CustomSelect
              v-model="selectedPostedBy"
              :items="PostedBy"
              density="compact"
              variant="solo"
              :isAutoComplete="true"
              :select-properties="{
                clearable: true,
                closableChips: true,
              }"
              :itemSelected="selectedPostedBy"
              @selected-item="selectedPostedBy = $event"
            ></CustomSelect>
          </v-col>
          <v-col
            v-if="selectedPostingPeriod == 'Custom Range'"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              Custom Range (in days) {{ otherPostingDays }}
            </p>
            <v-slider
              v-model="otherPostingDays"
              :max="365"
              :step="1"
              color="orange"
            ></v-slider>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { getCustomFieldName } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
export default defineComponent({
  name: "FormFilter",
  components: {
    CustomSelect,
  },
  data: () => ({
    openFormFilter: false,
    statusList: ["Waiting For Approval", "Open", "Closed", "Rejected"],
    selectedStatus: [],
    selectedPostingPeriod: null,
    selectedClosingPeriod: null,
    selectedPostedBy: null,
    postingPeriod: [
      "Last 7 days",
      "Last 30 days",
      "Last 90 days",
      "Custom Range",
    ],
    closingPeriod: [
      "Last 7 days",
      "Last 30 days",
      "Last 90 days",
      "Custom Range",
    ],
    clientList: [],
    serviceProviderList: [],
    selectedClient: [],
    selectedServiceProvider: [],
    selectedJobTitle: [],
    jobTitleList: [],
    PostedBy: [],
    selectedOption: null,
    dropdownOptions: [">=", "=", "<="],
    typableValue: "",
    fieldForce: 0,
    otherClosingDays: null,
    otherPostingDays: null,
  }),
  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  watch: {
    items() {
      this.formFilterData();
    },
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("22");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["update"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    displayServiceProvider() {
      if (this.fieldForce && this.formAccess && this.formAccess["update"]) {
        return true;
      }
      return false;
    },
  },
  mounted() {
    this.fnApplyFilter();
    this.formFilterData();
    this.retrieveDropDownDetails();
  },
  methods: {
    getCustomFieldName,
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.items;

      if (this.selectedJobTitle.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedJobTitle.includes(item.Job_Post_Name);
        });
      }
      if (this.selectedPostingPeriod) {
        if (this.selectedPostingPeriod == "Last 7 days") {
          filteredArray = filteredArray.filter((item) => item.Ageing <= 7);
        } else if (this.selectedPostingPeriod == "Last 30 days") {
          filteredArray = filteredArray.filter((item) => item.Ageing <= 30);
        } else if (this.selectedPostingPeriod == "Last 90 days") {
          filteredArray = filteredArray.filter((item) => item.Ageing <= 90);
        } else {
          filteredArray = filteredArray.filter(
            (item) => item.Ageing <= this.otherPostingDays
          );
        }
      }
      if (this.selectedClosingPeriod) {
        if (this.selectedClosingPeriod == "Last 7 days") {
          filteredArray = filteredArray.filter(
            (item) => item.Closed_Before <= 7 && item.Closed_Before > 0
          );
        } else if (this.selectedClosingPeriod == "Last 30 days") {
          filteredArray = filteredArray.filter(
            (item) => item.Closed_Before <= 30 && item.Closed_Before > 0
          );
        } else if (this.selectedClosingPeriod == "Last 90 days") {
          filteredArray = filteredArray.filter(
            (item) => item.Closed_Before <= 90 && item.Closed_Before > 0
          );
        } else {
          filteredArray = filteredArray.filter(
            (item) =>
              item.Closed_Before <= this.otherClosingDays &&
              item.Closed_Before > 0
          );
        }
      }
      if (this.typableValue) {
        switch (this.selectedOption) {
          case ">=":
            filteredArray = filteredArray.filter(
              (item) => item.No_Of_Vacancies >= this.typableValue
            );
            break;
          case "<=":
            filteredArray = filteredArray.filter(
              (item) => item.No_Of_Vacancies <= this.typableValue
            );
            break;
          case "=":
            filteredArray = filteredArray.filter(
              (item) => item.No_Of_Vacancies == this.typableValue
            );
            break;
          default:
            filteredArray = this.items;
        }
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Job_Post_Status);
        });
      }
      if (this.selectedPostedBy && this.selectedPostedBy.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedPostedBy.includes(item.Added_By);
        });
      }
      if (this.selectedClient.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedClient.includes(item.Client_Name);
        });
      }
      if (this.selectedServiceProvider && this.selectedServiceProvider.length) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedServiceProvider.includes(
            item.Service_Provider_Id
          );
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    formFilterData() {
      const postedBySet = new Set();
      for (let item of this.items) {
        if (item && item.Job_Post_Name) {
          this.jobTitleList.push({
            jobTitle: item.Job_Post_Name,
          });
        }
        if (item && item.Added_By) {
          postedBySet.add(item.Added_By);
        }
      }
      this.jobTitleList = this.removeDuplicatesFromArrayOfObject(
        this.jobTitleList,
        "jobTitle"
      );
      this.PostedBy = Array.from(postedBySet);
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    resetAllModelValues() {
      (this.selectedJobTitle = []), (this.selectedStatus = []);
      this.selectedClient = [];
      this.selectedPostingPeriod = null;
      this.selectedClosingPeriod = null;
      this.selectedPostedBy = null;
      this.selectedServiceProvider = [];
      this.otherClosingDays = 0;
      this.otherPostingDays = 0;
      this.typableValue = "";
      this.selectedOption = null;
      this.openFormFilter = false;
    },
    async retrieveDropDownDetails() {
      await this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((dropDownist) => {
          if (dropDownist.data && dropDownist.data.getDropDownBoxDetails) {
            let dropdownData = dropDownist.data.getDropDownBoxDetails;
            this.clientList = dropdownData["clients"];
            this.serviceProviderList = dropdownData["serviceProvider"];
            this.fieldForce = dropdownData["fieldForce"];
          }
        })
        .catch(() => {
          this.dropDownlLoading = false;
          this.dropDownClient = [];
        });
    },
    isVacancyValueDisabled() {
      if (this.selectedOption) {
        return false;
      } else {
        this.typableValue = "";
        return true;
      }
    },
  },
});
</script>
