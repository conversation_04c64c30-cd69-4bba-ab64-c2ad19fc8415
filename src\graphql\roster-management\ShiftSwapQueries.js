import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_SHIFT_LIST = gql`
  query shiftQuery(
    $sortField: Int
    $sortOrder: String
    $searchString: String
    $shiftName: String
    $minCount: Int
    $maxCount: Int
    $isDropDown: Int
    $employeeId: Int
    $swapDate: String
  ) {
    listShiftType(
      sortField: $sortField
      sortOrder: $sortOrder
      searchString: $searchString
      shiftName: $shiftName
      minCount: $minCount
      maxCount: $maxCount
      isDropDown: $isDropDown
      employeeId: $employeeId
      swapDate: $swapDate
    ) {
      errorCode
      success
      message
      shiftType {
        Shift_Id
        Shift_Name
        WorkSchedule_Id
        Minimum_Employee_Count
        Maximum_Employee_Count
        Holiday_Override
        Comments
        Colour_Code
        loginEmployeeIdShift
        Added_By
        Added_On
        Status
        Updated_By
        Updated_On
      }
      rosterSettings {
        Dynamic_Week_Off
        Overlap_Shift_Schedule
      }
    }
  }
`;

export const LIST_OVERTIME_PREREQUISITES = gql`
  query getOvertimePrerequisites(
    $employeeId: Int!
    $otStartTime: Date!
    $shiftDate: Date
  ) {
    getOvertimePrerequisites(
      employeeId: $employeeId
      otStartTime: $otStartTime
      shiftDate: $shiftDate
    ) {
      errorCode
      message
      otPreRequisites
    }
  }
`;

export const GET_SHIFT_SWAP_RANGE = gql`
  query getShiftSwapDateRange($employeeId: Int!) {
    getShiftSwapDateRange(employeeId: $employeeId) {
      errorCode
      message
      minimumDate
      maximumDate
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_EDIT_SHIFT_SWAP = gql`
  mutation addUpdateEmployeeShiftSwap(
    $employeeId: Int!
    $approverId: Int!
    $swapShiftTypeId: Int!
    $swapDate: String!
    $reason: String!
    $status: String!
    $swapId: Int
  ) {
    addUpdateEmployeeShiftSwap(
      employeeId: $employeeId
      approverId: $approverId
      swapShiftTypeId: $swapShiftTypeId
      swapDate: $swapDate
      reason: $reason
      status: $status
      swapId: $swapId
    ) {
      errorCode
      message
    }
  }
`;
