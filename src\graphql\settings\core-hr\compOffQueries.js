import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const RETRIEVE_COMP_OFF_RULES = gql`
  query retrieveCompoffRules {
    retrieveCompoffRules {
      errorCode
      message
      CompoffRules {
        Configuration_Id
        Custom_Group_Id
        Comp_Off_Expiry_Type
        compOffBalance
        Comp_Off_Balance_Id
        Comp_Off_Expiry_Days
        Comp_Off_Encashment
        Encashment_Mode
        Group_Name
        Comp_Off_Threshold
        Allow_Half_Day_Comp_Off_Credit
        Fixed_Regular_Hours
        Salary_Type
        Work_Day_Type
        Status
        Workflow_Approval
        Comp_Off_Applicability_For_Overtime_Hours
        Comp_Off_Balance_Approval
        Minimum_OT_Hours_For_Full_Day_Comp_Off
        Minimum_OT_Hours_For_Half_Day_Comp_Off
        Minimum_Hours_For_Half_Day_Comp_Off
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;

export const GET_OT_CONFIGURATION_DETAILS = gql`
  query getOTConfigurationDetails {
    getOTConfigurationDetails {
      errorCode
      message
      configurationData {
        minAndMaxYears
        overtimeSettings {
          Overtime_Part_Of_Payroll
          Overtime_Payment_Method
          Overtime_Flat_OR_Slab_Rate
          Comp_Off_Applicable_For_Overtime
          Shift_Allowance_Applicable_For_Overtime
        }
      }
    }
  }
`;
export const LIST_COMPOFF_CONFIG_HISTORY = gql`
  query listCompOffConfigHistory($configurationId: Int!) {
    listCompOffConfigHistory(configurationId: $configurationId) {
      errorCode
      message
      compOffHistory {
        History_Id
        Comp_Off_Balance_Approval
        Custom_Group_Id
        Group_Name
        Comp_Off_Expiry_Type
        Comp_Off_Expiry_Days
        Comp_Off_Encashment
        Encashment_Mode
        Comp_Off_Threshold
        Allow_Half_Day_Comp_Off_Credit
        Fixed_Regular_Hours
        Salary_Type
        Status
        Work_Day_Type
        Workflow_Approval
        Comp_Off_Balance_Id
        Comp_Off_Applicability_For_Overtime_Hours
        Minimum_OT_Hours_For_Full_Day_Comp_Off
        Minimum_OT_Hours_For_Half_Day_Comp_Off
        Minimum_Hours_For_Half_Day_Comp_Off
        Added_On
        Added_By
        Added_By_Name
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const ADD_UPDATE_COMP_OFF_RULES = gql`
  mutation addUpdateCompOffRules(
    $CustomGroup_Id: Int
    $Configuration_Id: Int!
    $Comp_Off_Expiry_Type: String!
    $Comp_Off_Expiry_Days: Int
    $Comp_Off_Encashment: String!
    $Encashment_Mode: String
    $Comp_Off_Threshold: String
    $Allow_Half_Day_Comp_Off_Credit: String
    $Fixed_Regular_Hours: Float
    $Salary_Type: String!
    $Work_Day_Type: String!
    $Status: String!
    $Workflow_Approval: Int
    $Comp_Off_Applicability_For_Overtime_Hours: String
    $Comp_Off_Balance_Approval: String
    $Minimum_OT_Hours_For_Full_Day_Comp_Off: Float
    $Minimum_OT_Hours_For_Half_Day_Comp_Off: Float
    $Minimum_Hours_For_Half_Day_Comp_Off: Float
  ) {
    addUpdateCompOffRules(
      CustomGroup_Id: $CustomGroup_Id
      Configuration_Id: $Configuration_Id
      Comp_Off_Expiry_Type: $Comp_Off_Expiry_Type
      Comp_Off_Expiry_Days: $Comp_Off_Expiry_Days
      Comp_Off_Encashment: $Comp_Off_Encashment
      Encashment_Mode: $Encashment_Mode
      Comp_Off_Threshold: $Comp_Off_Threshold
      Allow_Half_Day_Comp_Off_Credit: $Allow_Half_Day_Comp_Off_Credit
      Fixed_Regular_Hours: $Fixed_Regular_Hours
      Salary_Type: $Salary_Type
      Work_Day_Type: $Work_Day_Type
      Status: $Status
      Workflow_Approval: $Workflow_Approval
      Comp_Off_Applicability_For_Overtime_Hours: $Comp_Off_Applicability_For_Overtime_Hours
      Comp_Off_Balance_Approval: $Comp_Off_Balance_Approval
      Minimum_OT_Hours_For_Full_Day_Comp_Off: $Minimum_OT_Hours_For_Full_Day_Comp_Off
      Minimum_OT_Hours_For_Half_Day_Comp_Off: $Minimum_OT_Hours_For_Half_Day_Comp_Off
      Minimum_Hours_For_Half_Day_Comp_Off: $Minimum_Hours_For_Half_Day_Comp_Off
    ) {
      errorCode
      message
    }
  }
`;
