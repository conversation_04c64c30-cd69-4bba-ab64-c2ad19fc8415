import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_MEMBER_CONTRIBUTION = gql`
  query getMemberContributionReport($payRollMonth: String!) {
    getMemberContributionReport(payRollMonth: $payRollMonth) {
      errorCode
      message
      maillingAddress {
        street1
        street2
        cityName
        stateName
        countryName
        pincode
      }
      mobileNo
      employerName
      dateReceived
      employeeDetails {
        firstName
        middleName
        lastName
        employerShare
        personalShare
        rtnNumber
      }
      employerShareTotal
      personalShareTotal
    }
  }
`;

export const GET_CERTIFICATE_OF_PAYMENT_REPORT = gql`
  query getCertificateOfPaymentReport(
    $employeeId: Int!
    $assessmentYear: Int!
  ) {
    getCertificateOfPaymentReport(
      employeeId: $employeeId
      assessmentYear: $assessmentYear
    ) {
      errorCode
      message
      assessmentYear
      taxPayerIdentificationNo
      firstName
      lastName
      middleName
      dateofBirth
      mobileNo
      mobileCountryCode
      permenantAddress {
        apartmentName
        streetName
        city
        state
        country
        pincode
      }
      currentAddress {
        apartmentName
        streetName
        city
        state
        country
        pincode
      }
      employerIdentificationNo
      employerName
      maillingAddress {
        street1
        street2
        cityName
        stateName
        countryName
        pincode
        phone
      }
      qualifiedDependent
      totalExemptions
      premiumPaidHealth
      taxDue
      bPreviousEmployer
      salariesAndCompensation
      holidayPayMWE
      overtimePayMWE
      hazardPayMWE
      monthPay
      deMinimisBenefits
      basicSalary
      representation
      transportaion
      costLivingAllowance
      fixedHousingAllowance
      commission
      profitSharing
      feesIncludingDirector
      taxableMonthPay
      hazardPay
      overtimePay
      presentEmployerGrossIncome
      totalNonTaxable
      presentEmployerTaxableIncome
      previousEmployerTaxableIncome
      otherBenefitsGrossTaxableIncome
    }
  }
`;

export const GET_REMITTANCEREPORT = gql`
  query getEmployerRemittanceReport($payRollMonth: String!) {
    getEmployerRemittanceReport(payRollMonth: $payRollMonth) {
      errorCode
      message
      maillingAddress {
        street1
        street2
        cityName
        stateName
        countryName
        pincode
      }
      mobileNo
      employerTinNo
      employerName
      employerType
      reportType
      dateReceived
      employeeDetails {
        identificationNumber
        firstName
        middleName
        lastName
        dateOfBirth
        gender
        employerShare
        personalShare
      }
      employerShareTotal
      personalShareTotal
    }
  }
`;
export const GET_R3_CONTRIBUTION_REPORT = gql`
  query getR3MonthlyContributionReport($quarterEndMonth: String!) {
    getR3MonthlyContributionReport(quarterEndMonth: $quarterEndMonth) {
      errorCode
      message
      maillingAddress {
        street1
        street2
        cityName
        stateName
        countryName
        pincode
      }
      mobileNo
      employerName
      typeOfEmployer
      employeeDetails {
        identificationNumber
        firstName
        middleName
        lastName
        separationDate
        orgShareAmount1
        orgShareAmount2
        orgShareAmount3
        empShareAmount1
        empShareAmount2
        empShareAmount3
      }
      totalOrgShareAmount1
      totalOrgShareAmount2
      totalOrgShareAmount3
      totalEmpShareAmount1
      totalEmpShareAmount2
      totalEmpShareAmount3
    }
  }
`;

export const GET_R5_CONTRIBUTION_REPORT = gql`
  query getR5ContributionReport($assessmentYear: Int) {
    getR5ContributionReport(assessmentYear: $assessmentYear) {
      errorCode
      message
      maillingAddress {
        street1
        street2
        cityName
        stateName
        countryName
        pincode
        phone
      }
      employerTinNo
      employerName
      paymentDetails {
        salaryMonth
        orgShareAmount
        empShareAmount
      }
      orgShareAmountTotal
      employerShareTotal
    }
  }
`;

export const GET_ANNUAL_INFORMATION_RETURN_REPORT = gql`
  query getAnnualInformationReturnReport($assessmentYear: Int!) {
    getAnnualInformationReturnReport(assessmentYear: $assessmentYear) {
      errorCode
      message
      maillingAddress {
        street1
        street2
        cityName
        stateName
        countryName
        pincode
        phone
      }
      identificationNumber
      agentName
      amenededReturn
      numberOfSheetAttach
      withholdingAgent
      yearEndAdjustment
      paymentDetails {
        paymentDate
        bankName
        documentNo
        totalAmount
      }
    }
  }
`;

export const RETRIEVE_EXCEL_DETAILS = gql`
  query generateIndonesiaReports(
    $month: Int
    $year: Int!
    $reportType: String!
  ) {
    generateIndonesiaReports(
      month: $month
      year: $year
      reportType: $reportType
    ) {
      errorCode
      message
      headers
      reportData
    }
  }
`;

export const GET_WITHOLDING_TAX_PND = gql`
  query getPND1Report($payRollMonth: String!, $serviceProviderId: Int) {
    getPND1Report(
      payRollMonth: $payRollMonth
      serviceProviderId: $serviceProviderId
    ) {
      errorCode
      message
      data {
        maillingAddress {
          tan
          taxAgent
          street1
          street2
          cityName
          stateName
          countryName
          pincode
          mobileNo
        }
        taxDetails {
          Receipt_No
          paymentDate
          bankName
          branchName
          documentNo
          amountPaid
          totalAmountOfIncome
          totalAmountOfIncomeInWords
        }
        reportType
        employeeDetails {
          employeeCount
          taxableSalaryCount
        }
        employeeCount
        summaryHoldingTaxDetails {
          generalSalariesAndWages {
            name
            employeeCount
            totalAmountOfIncome
            amountWithHoldingTax
            total
          }
          approvedSalariesWithholdingTax {
            name
            employeeCount
            totalAmountOfIncome
            amountWithHoldingTax
            total
          }
          terminationPaymentIncome {
            name
            employeeCount
            totalAmountOfIncome
            amountWithHoldingTax
            total
          }
          residentIncomeSection40_2 {
            name
            employeeCount
            totalAmountOfIncome
            amountWithHoldingTax
            total
          }
          nonResidentIncomeSection40_2 {
            name
            employeeCount
            totalAmountOfIncome
            amountWithHoldingTax
            total
          }
        }
      }
    }
  }
`;

export const GET_WITHOLDING_TAX_CERTIFICATE = gql`
  query GetWithHoldTaxCertificate(
    $employeeId: Int!
    $assessmentYear: Int!
    $serviceProviderId: Int
  ) {
    getWithHoldTaxCertificate(
      employeeId: $employeeId
      assessmentYear: $assessmentYear
      serviceProviderId: $serviceProviderId
    ) {
      mailingCompanyAddress {
        tan
        taxAgent
        street1
        street2
        cityName
        stateName
        countryName
        pincode
        mobileNo
      }
      employeeDetails {
        tan
        personalIdentificationNo
        name
        address {
          apartmentName
          streetName
          city
          state
          country
          pincode
          mobileNo
          mobileNoCode
        }
      }
      reportType
      taxWithHoldingDetails {
        salaryWagePensionSection40_1 {
          amountPaid
          taxWithHeld
        }
        commissionsSection40_2 {
          amountPaid
          taxWithHeld
        }
        royaltiesSection40_3 {
          amountPaid
          taxWithHeld
        }
        interestSection40_4_a {
          amountPaid
          taxWithHeld
        }
        incomeSubjectToWithholdingSection3Tredecim {
          amountPaid
          taxWithHeld
        }
        others {
          amountPaid
          taxWithHeld
        }
      }
      errorCode
      message
    }
  }
`;

export const GENERATE_THAILAND_REPORT = gql`
  query generateThailandReports(
    $payrollMonthYear: String!
    $reportType: String!
  ) {
    generateThailandReports(
      payrollMonthYear: $payrollMonthYear
      reportType: $reportType
    ) {
      errorCode
      message
      reportDetails
      reportSummary
    }
  }
`;

export const GET_PND53_REPORT = gql`
  query getPND53Report($payRollMonth: String!, $serviceProviderId: Int) {
    getPND53Report(
      payRollMonth: $payRollMonth
      serviceProviderId: $serviceProviderId
    ) {
      errorCode
      message
      employeeDetails
    }
  }
`;

export const GET_PND3_REPORT = gql`
  query getPND3Report($serviceProviderId: Int, $payRollMonth: String!) {
    getPND3Report(
      serviceProviderId: $serviceProviderId
      payRollMonth: $payRollMonth
    ) {
      reportType
      pnd3Details
      errorCode
      message
    }
  }
`;

export const GET_PND1_REPORT = gql`
  query getPND1MonthlyReport($payRollMonth: String!, $serviceProviderId: Int) {
    getPND1MonthlyReport(
      payRollMonth: $payRollMonth
      serviceProviderId: $serviceProviderId
    ) {
      errorCode
      message
      data {
        personalIdentificationNo
        taxpayerIdentificationNo
        typeofIncome1
        typeofIncome2
        typeofIncome3
        typeofIncome4
        typeofIncome5
        branchNo
        paymentDate
        position
        payerResponsibleName
        fillingDate
        totalPaidAmount
        totalAmountOfTaxWithHeld
        employeeDetails {
          paidAmount
          amountOfTaxWithHeld
          empPersonalIdentificationNo
          empTaxIdentificationNo
          surname
          employeeName
          conditions
        }
      }
    }
  }
`;
