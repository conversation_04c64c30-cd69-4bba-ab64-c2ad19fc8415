FRONTEND DEVELOPMENT TEAM :

1.  Requirement:

2.  Existing System :

3.  Proposed System :

4.  [ ] I prepared the MySQL query and attached here :

          [ ] I updated the access rights query when the new form is added or removed :

          [ ] I updated the dependent form query when the new form is added or removed for plan details :


5.  [ ] I am reusing the existing components designed
        
        * Component Name: 

        * Module Name:


6.  [ ] I am creating New components for this task and attached it here

        * Component Name:


7. [ ] I checked the impact on access flow :  

       * Employee level access change : 
       * Manager  level access change :
       * Admin    level access change :

8. [ ] I checked the design in the user interface :

       * Form names: 
       * Fields names :
       * Grid header names:
       * Search :
       * Filter :
       * Sorting :
       * Message :
       * Tooltip :
       * Validations (Min, Max, Alphanumeric) :  
 
  
9. [ ] I checked Responsiveness for my design
         
        [ ] xs  (< 600px)
        [ ] sm  (600px > < 960px)
        [ ] md  (960px > < 1264px*)   
        [ ] lg  (1264px* > < 1904px*)
        [ ] xl  (> 1904px*)  

10. [ ] Is any difference in mobile and desktop view designs 
 
          * Description:

11. [ ] I have designed based on the wireFrame given and attached the UI screens here :


12. [ ] Reusing Existing Endpoint
            
          * Endpoint Name :

13. [ ] Endpoints need to create
       
          * Endpoint Name : 

14. [ ] I have attached the wireframe.

15. [ ] I have covered all scenarios and done unit testing.

16. [ ] I have done error handling for all API calls.

17. [ ] I updated/uploaded the reference link and other required files.

18. [ ] I have done the Design and Integration level analysis and updated the estimation.

19. [ ] I have added/updated all the IDs related to testing.

20. [ ] I have handled the set lock and clear lock wherever necessary.

21. [ ] I have self reviewed my code before sending the code to review.

22. [ ] I have covered everything listed in the [checklist document](https://docs.google.com/document/d/1MeaE6wAWzimJGC7X3grNoI7UNW4UDE31bsaU7WjxrRg)

23. [ ] I have run the Lighthouse audit for my screen.

       * Updated the fixed issue and unfixed error in the [Lighthouse Document](https://docs.google.com/document/d/1Muz75nu4DR86rUpcTj6sab8gFdHvpMun4ryhPEMwcoE)


**TESTING TEAM** :

1. [ ]  I analyze this task and its impacts

2. [ ]  I validate the analysis and prepare the test cases(#Description Mandatory)

3. [ ]  I update the test cases in ticket and respective document(with ticket number):

4. [ ]  This test cases reviewed by the lead 

5. [ ]  I test all the test cases which you documented

6. [ ]  I test the dependent changes of this task

7. [ ]  I test the application in all(Chrome,Edge,FireFox,IE,Safari) the browser

8. [ ]  I checked Responsiveness for this screen
         
        [ ] xs  (< 600px)
        [ ] sm  (600px > < 960px)
        [ ] md  (960px > < 1264px*)   
        [ ] lg  (1264px* > < 1904px*)
        [ ] xl  (> 1904px*)  

9. [ ]  The functionality covers the requirement

10. [ ] I tested the task for test suit(n-1) preparation.

11. [ ] I tested the Lighthouse audit performance report.

12. [ ] I have tested all scenarios listed in the [checklist document](https://docs.google.com/document/d/1MeaE6wAWzimJGC7X3grNoI7UNW4UDE31bsaU7WjxrRg)

13. [ ] I closed this issue after completed the testing.