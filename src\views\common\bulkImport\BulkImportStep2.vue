<template>
  <div>
    <v-row justify="center">
      <v-col cols="12" xs="12" md="12" lg="11" xlg="10">
        <div class="d-flex align-center">
          <v-icon color="purple-lighten-2" size="20">far fa-circle</v-icon>
          <span class="ml-2 step2-header text-primary font-weight-bold"
            >Map columns in your file to {{ organizationName }} properties</span
          >
        </div>
        <div class="mt-3 text-primary-lighten-4 ml-2 mb-4">
          Each column header below should be mapped to an
          {{ organizationName }} property. Some of these have already been
          mapped based on their names. Anything that hasn't been mapped yet can
          be manually mapped to a {{ organizationName }} property with the
          dropdown menu.
        </div>
        <div class="scroll-area" style="overflow: scroll">
          <div class="mapping-table mb-4">
            <v-data-table
              :headers="tableHeaders"
              :items="tableItems"
              fixed-header
              :items-per-page="50"
              :items-per-page-options="[
                { value: 50, title: '50' },
                { value: 100, title: '100' },
                { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
              ]"
            >
              <template #[`column.matched`]="{}">
                <div
                  class="custom-table-headers font-weight-bold text-body-1"
                  style="font-size: large"
                  color="grey-darken-3"
                >
                  Matched
                </div>
              </template>
              <template #[`column.file_header`]="{}">
                <div
                  class="custom-table-headers font-weight-bold text-body-1"
                  style="font-size: large"
                  color="grey-darken-3"
                >
                  Column header from file
                </div>
              </template>
              <template #[`column.file_preview`]="{}">
                <div
                  class="custom-table-headers font-weight-bold text-body-2"
                  style="font-size: large"
                  color="grey-darken-3"
                >
                  Preview information
                </div>
              </template>
              <template #[`column.hrapp_header`]="{}">
                <div
                  class="custom-table-headers font-weight-bold text-body-2"
                  style="font-size: large"
                  color="grey-darken-3"
                >
                  {{ organizationName }} Property
                </div>
              </template>
              <template #item="{ item }">
                <tr
                  class="data-table-tr bg-white common-box-shadow"
                  :class="
                    windowWidth <= 600 ? ' v-data-table__mobile-table-row' : ''
                  "
                >
                  <td
                    :class="windowWidth < 600 ? 'v-data-table__mobile-row' : ''"
                  >
                    <v-icon v-if="item.matched" color="green-accent-3" size="30"
                      >fas fa-check-circle</v-icon
                    >
                    <v-icon v-else color="red-accent-2" size="30"
                      >fas fa-times-circle</v-icon
                    >
                  </td>

                  <td
                    :class="
                      windowWidth <= 600 ? 'v-data-table__mobile-row' : ''
                    "
                  >
                    <div
                      v-if="windowWidth <= 600"
                      class="v-data-table__mobile-row__header"
                    >
                      File Header
                    </div>
                    <div class="text-primary text-body-1 font-weight-bold">
                      {{ item.file_header }}
                    </div>
                  </td>

                  <td
                    :class="
                      windowWidth <= 600 ? 'v-data-table__mobile-row' : ''
                    "
                  >
                    <div
                      v-if="windowWidth <= 600"
                      class="v-data-table__mobile-row__header"
                    >
                      Preview
                    </div>
                    <div class="text-primary text-body-1 font-weight-bold">
                      {{ item.file_preview }}
                    </div>
                  </td>

                  <td
                    :class="
                      windowWidth <= 600
                        ? 'v-data-table__mobile-row d-flex flex-column'
                        : ''
                    "
                    :style="windowWidth <= 600 ? 'height: 7em' : ''"
                  >
                    <div
                      v-if="windowWidth <= 600"
                      class="v-data-table__mobile-row__header"
                    >
                      {{ organizationName }} Property
                    </div>
                    <div class="pt-4"></div>
                    <v-autocomplete
                      ref="selecter"
                      v-model="item.hrapp_header"
                      style="max-width: 250px"
                      :items="predefinedHeader"
                      variant="solo"
                      clearable
                      @click:clear="updateMatchedColumn(item)"
                      @update:modelValue="updateMatchedColumn(item)"
                    ></v-autocomplete>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </div>
        </div>
      </v-col>
    </v-row>
  </div>
</template>
<script>
// plugins
import moment from "moment";

export default {
  name: "BulkImportStep2",
  props: {
    fileParams: {
      type: Array,
      required: true,
    },
    headersSelected: {
      type: Array,
      required: true,
    },
  },
  data: () => ({
    // table header
    tableHeaders: [
      {
        title: "Matched",
        align: "start",
        sortable: false,
        key: "matched",
      },
      {
        title: "Column header from file",
        key: "file_header",
        sortable: false,
      },
      {
        title: "Preview information",
        key: "file_preview",
        sortable: false,
      },
      {
        title: "HRAPP Property",
        key: "hrapp_header",
        sortable: false,
      },
    ],
    tableItems: [],
    mappedCount: 0,
    predefinedHeader: [],
  }),
  computed: {
    organizationName() {
      let domain = this.$store.getters.domain;
      //on receiving organization name update app title
      if (domain === "cannyhr") {
        return "CANNYHR";
      } else {
        return "HRAPP";
      }
    },

    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    orgDateFormat() {
      return this.$store.state.orgDetails.orgDateFormat;
    },
  },
  mounted() {
    this.predefinedHeader = this.headersSelected;
    // to form table items based on file header which is returned from step 1
    if (this.fileParams[0].length > 0) {
      let fileHeader = this.fileParams[0], // 0: headers list
        tableData = [];
      for (var i in fileHeader) {
        // don't allow Data_Validation column for mapping. because its added for validation alone.
        if (fileHeader[i] !== "Data_Validation") {
          // check file header value is available in our predefined header
          let headerIndex = this.predefinedHeader.findIndex(
            (obj) => obj.title === fileHeader[i]
          );
          let tableObject = {
            mapping_id: i,
            matched: headerIndex > -1 ? 1 : 0, // file header is matched to our column, then set as matched, else set as unmatched
            file_header: fileHeader[i], // headers list
            file_preview: this.fileParams[1][i]
              ? this.fileParams[1][i] instanceof Date // to check the data is date
                ? moment(this.fileParams[1][i]).format(this.orgDateFormat)
                : this.fileParams[1][i]
              : "Nothing to preview", // preview the first column of each excel cell
            hrapp_header:
              headerIndex > -1
                ? this.predefinedHeader[headerIndex]["title"]
                : "", // file header is matched to our column, then set the header value, else set as empty
          };
          tableData.push(tableObject);
        }
      }
      this.tableItems = tableData;
      this.findMatchedColumn();
    }
  },
  methods: {
    // find matched column and disable selected value in hrapp property select field
    findMatchedColumn() {
      // if the row data is disabled, then this is mapped row. So for mapped count we can use disabled count
      let disabledCount = 0,
        mappedFileHeader = [];
      // check two array to find same value, and disable it to prevent selecting duplicate one
      for (var i in this.predefinedHeader) {
        for (var j in this.tableItems) {
          if (
            this.tableItems[j].hrapp_header === this.predefinedHeader[i].title
          ) {
            disabledCount++;
            mappedFileHeader.push({
              file_header: j,
              hrapp_header: this.predefinedHeader[i].title,
            }); // to find the file header index
            this.predefinedHeader[i]["props"]["disabled"] = true;
            break;
          } else {
            this.predefinedHeader[i]["props"]["disabled"] = false;
          }
        }
      }
      this.mappedCount = disabledCount; // total columns mapped count
      this.$emit("column-mapped", [this.mappedCount, mappedFileHeader]);
    },
    // on change function for hrapp property header selection
    updateMatchedColumn(item) {
      this.findMatchedColumn();
      // we have to find index of change array
      let index = this.tableItems.findIndex(
        (obj) => obj.mapping_id === item.mapping_id
      );
      // if the selected header is removed we have to set the row as not-matched
      if (item.hrapp_header) {
        this.tableItems[index]["matched"] = 1; // if the header is selected, then set that row as matched
      } else {
        this.tableItems[index]["matched"] = 0; // if the header is removed, then set that row as un-matched
      }
    },
  },
};
</script>

<style lang="css" scoped>
@import "../../../assets/scss/dataTableStyle.scss";

th {
  background-color: red;
}

.step2-header {
  font-size: 1.4em;
}
.mapping-table {
  height: 600px;
}
.custom-table-headers {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 160px;
  display: inline;
}
.pages-list:hover {
  background: #ffebeb;
}
@media screen and (max-width: 700px) {
  .step1-header {
    font-size: 1.2em;
  }
}
@media (max-width: 600px) {
  .v-data-table thead tr:last-child th {
    margin-bottom: -60px;
  }
  .mapping-table {
    height: 100%;
  }
}
@media screen and (max-width: 600px) {
  .theme--light.v-data-table {
    background-color: #a7a7a714 !important;
  }
}
</style>
