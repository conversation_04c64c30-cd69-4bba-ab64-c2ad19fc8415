<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :show-bottom-sheet="showBottomSheet"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :class="showFilter ? '' : 'mr-8'"
                :isFilter="false"
              />
              <LeavesFilter
                v-if="showFilter"
                ref="formFilterRef"
                :form-id="332"
                callingFrom="myTeam"
                :item-list="originalList"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-container fluid class="leaves-card">
            <ProfileCard>
              <FormTab :model-value="openedSubTab" :hide-slider="true">
                <v-tab
                  v-for="tab in subTabItems"
                  :key="tab.value"
                  :value="tab.value"
                  :disabled="tab.disable"
                  @click="onChangeSubTabs(tab.value)"
                >
                  <div
                    :class="[
                      isActiveSubTab(tab.value)
                        ? 'text-primary font-weight-bold'
                        : 'text-grey-darken-2 font-weight-bold',
                    ]"
                  >
                    {{ tab.label }}
                    <div
                      v-if="isActiveSubTab(tab.value)"
                      class="mt-3 mb-n4"
                      style="border-bottom: 4px solid; width: 150px"
                    ></div>
                  </div>
                </v-tab>
              </FormTab>
            </ProfileCard>
          </v-container>
          <v-container fluid>
            <v-window v-model="openedSubTab">
              <v-window-item value="leaveRequests">
                <LeaveRequestList
                  v-if="openedSubTab === 'leaveRequests'"
                  callingFrom="myTeam"
                  :form-access="formAccess"
                  :form-id="332"
                  :leave-settings="leaveSettings"
                  :landed-form-name="landedFormName"
                  :filtered-list="itemList"
                  @opened-forms="presentBottomSheet($event)"
                  @send-list-data="updateList($event)"
                />
              </v-window-item>
              <v-window-item value="leavehistory">
                <LeaveBalance
                  v-if="openedSubTab === `leavehistory`"
                  :form-id="332"
                  :form-access="formAccess"
                  :filtered-list="itemList"
                  landed-form-name="Leave History"
                  @send-list-data="updateList($event)"
                />
              </v-window-item>
            </v-window>
          </v-container>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
      <AppLoading v-if="listLoading" />
    </v-container>
  </div>
</template>
<script>
const { defineAsyncComponent } = require("vue");
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const LeaveRequestList = defineAsyncComponent(() =>
  import("./leave-request/LeaveRequestList.vue")
);
const LeaveBalance = defineAsyncComponent(() =>
  import(
    "@/views/employee-self-service/time-off/leaves/leave-balance/LeaveBalance.vue"
  )
);
const LeavesFilter = defineAsyncComponent(() => import("./LeavesFilter.vue"));
import { GET_LEAVE_SETTINGS } from "@/graphql/settings/core-hr/leaveSettingsQueries.js";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "LeaveRequestListSetup",
  components: {
    EmployeeDefaultFilterMenu,
    LeaveRequestList,
    LeavesFilter,
    LeaveBalance,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      originalList: [],
      itemList: [],
      leaveSettings: null,
      showViewForm: false,
      showAddEditForm: false,
      isErrorInList: false,
      listLoading: false,
      openMoreMenu: false,
      isEdit: false,
      showBottomSheet: false,
      // Leaves
      currentTabItem: "",
      openedSubTab: "Leave Request",
      isFilterApplied: false,
    };
  },
  computed: {
    landedFormName() {
      let form = this.accessRights(332);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Leave Request";
    },
    myTeamTimeOffFormAccess() {
      return this.$store.getters.myTeamTimeOffFormAccess;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(332);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    approvalManagementFormAccess() {
      let formAccessRights = this.accessRights(184);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        this.leaveSettings?.Enable_Workflow?.toLowerCase() === "yes"
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.myTeamTimeOffFormAccess;
      if (isAnyOneFormHaveAccess) {
        // Define the desired order of tabs
        const tabOrder = [
          "Leave",
          "Compensatory Off",
          "Short Time Off",
          "Approvals",
          "Reports",
        ];

        // Create a map of available tabs the user has access to
        let availableTabs = {};
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            availableTabs[access] = true;
          }
        }
        // Add Approvals tab if user has access
        if (this.approvalManagementFormAccess) {
          availableTabs["Approvals"] = true;
        }

        // Create the final array in the correct order, only including tabs the user has access to
        let formAccessArray = tabOrder.filter((tab) => availableTabs[tab]);

        return formAccessArray;
      }
      return [];
    },
    subTabItems() {
      return [
        {
          label: "Leave Request",
          value: "leaveRequests",
          disable: false,
        },
        {
          label: "Leave History",
          value: "leavehistory",
          disable: false,
        },
      ];
    },
    showFilter() {
      return this.originalList?.length && this.openedSubTab === "leaveRequests";
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf("Leave");
    this.getLeaveSettings();
  },
  methods: {
    checkNullValue,
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    presentBottomSheet(val) {
      this.showBottomSheet = !val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    updateList(obj) {
      this.originalList = obj?.list || [];
      this.itemList = obj?.list || [];
      if (obj?.type === "updated") {
        this.$refs?.formFilterRef?.fnApplyFilter(obj?.list || []);
      } else this.resetFilter();
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
    },

    applyFilter(filter) {
      this.isFilterApplied = true;
      this.itemList = filter;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.myTeamTimeOffFormAccess;
        let clickedForm = formAccess[tab];
        if (tab === "Approvals")
          this.$router.push("/approvals/approval-management?form_id=31");
        else if (clickedForm.isVue3) {
          this.$router.push("/my-team/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/my-team/" + clickedForm.url;
        }
      }
    },
    getLeaveSettings() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_LEAVE_SETTINGS,
          variables: {
            formId: 332,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveLeaveSettings
          ) {
            const { leaveSettings } = response.data.retrieveLeaveSettings;
            vm.leaveSettings = leaveSettings;
          }
          vm.listLoading = false;
        })
        .catch(() => {
          vm.listLoading = false;
        });
    },
  },
};
</script>
<style scoped>
.container {
  padding: 3.7em 0em 0em 0em;
}
.leaves-card {
  padding: 0em 0em 0em 0em;
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
  .leaves-card {
    padding: 0em 0em 0em 0em;
  }
}
</style>
