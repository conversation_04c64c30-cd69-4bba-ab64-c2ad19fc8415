<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row justify="center">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isFilter="false"
            />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container v-if="formAccess?.view" fluid class="container">
      <div>
        <div v-if="listLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          key="error-screen"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          button-text="Retry"
          @button-click="refetchList()"
        ></AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="originalList.length === 0"
          key="no-data-screen"
          :main-title="emptyScenarioMsg"
          :isSmallImage="!isFilter"
          :image-name="!isFilter ? '' : 'common/no-records'"
        >
          <template v-if="!isFilter" #contentSlot>
            <div style="max-width: 80%">
              <v-row
                class="rounded-lg pa-5 mb-4"
                :style="!isFilter ? 'background: white' : ''"
              >
                <v-col cols="12">
                  <NotesCard
                    notes="No payslips are available for reconciliation for the selected month. Please verify payroll processing status and ensure payslips have been generated."
                    backgroundColor="transparent"
                    class="mb-4"
                  />
                </v-col>
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <datepicker
                    :format="'MMMM, yyyy'"
                    v-model="selectedMonthYear"
                    maximum-view="year"
                    minimum-view="month"
                    style="width: 220px"
                    :disabled-dates="getDisabledDates"
                    @input="fetchList()"
                  />
                  <v-btn
                    color="transparent"
                    variant="flat"
                    class="ml-2 mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="itemList.length == 0"
          key="no-results-screen"
          main-title="There are no employees matched for the selected filters/searches."
          image-name="common/no-records"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4">
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    variant="elevated"
                    color="primary"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    @click="resetFilter('grid')"
                  >
                    <span class="primary">Reset Filter/Search </span>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div v-else>
          <v-row>
            <v-col
              md="12"
              cols="12"
              xs="12"
              sm="12"
              class="d-flex mr-2 align-center"
              :class="{
                'flex-column': isMobileView,
                'justify-center': windowWidth < 1264,
                'justify-space-between': windowWidth >= 1264,
              }"
              style="flex-wrap: wrap"
              ><datepicker
                :format="'MMMM, yyyy'"
                v-model="selectedMonthYear"
                maximum-view="year"
                minimum-view="month"
                style="width: 220px"
                :disabled-dates="getDisabledDates"
                @input="fetchList()"
              />
              <div
                class="d-flex mr-2 align-center"
                :class="{
                  'flex-column': isMobileView,
                  'justify-center': windowWidth < 1264,
                  'justify-end': windowWidth >= 1264,
                }"
              >
                <v-btn
                  color="transparent"
                  variant="flat"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu class="mb-1" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                      <v-icon>fas fa-ellipsis-v</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <v-data-table
                :headers="headers"
                :items="searchList"
                :items-per-page="50"
                :group-by="Employee_Id"
                fixed-header
                :height="
                  searchList.length > 11
                    ? $store.getters.getTableHeight(270)
                    : ''
                "
                :sort-by="[{ key: 'Employee_Id', order: 'asc' }]"
                class="elevation-1"
                style="box-shadow: none !important"
              >
                <template v-slot:item="{ item }">
                  <tr
                    style="z-index: 200"
                    class="data-table-tr bg-white cursor-pointer"
                    :class="[
                      isMobileView
                        ? ' v-data-table__mobile-table-row ma-0 mt-2'
                        : '',
                    ]"
                  >
                    <td
                      v-if="item.Component?.toLowerCase() === `basic salary`"
                      style="max-width: 150px"
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Employee Name
                      </div>
                      <section class="d-flex align-center">
                        <v-tooltip
                          :text="item.Employee_Name"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                              v-bind="props"
                            >
                              {{ checkNullValue(item.Employee_Name) }}
                              <div v-if="item?.Employee_Id" class="text-grey">
                                {{ checkNullValue(item.Employee_Id) }}
                              </div>
                            </div>
                          </template>
                        </v-tooltip>
                      </section>
                    </td>
                    <td
                      v-else
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    ></td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Component
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Component.replace("_", " ")) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Salary Master (THB)
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Current_Amount) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Current Month Salary (THB)
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Previous_Amount) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Difference (THB)
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ item.Difference }}
                          <span class="ml-2">
                            <v-icon
                              v-if="item.Difference"
                              :color="item.Difference >= 0 ? 'green' : 'red'"
                              :class="
                                item.Difference >= 0
                                  ? 'fas fa-arrow-up'
                                  : 'fas fa-arrow-down'
                              "
                              size="15"
                            />
                          </span>
                        </span>
                      </section>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </div>
      </div>
    </v-container>
    <AppAccessDenied v-else />
  </div>
  <AppLoading v-if="isLoading" />

  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue } from "@/helper";
import moment from "moment";
import Datepicker from "vuejs3-datepicker";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
import FileExportMixin from "@/mixins/FileExportMixin";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);

export default {
  name: "PayrollReconciliation",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    Datepicker,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // tab
    currentTabItem: "",
    // table
    originalList: [],
    itemList: [],
    selectedMonthYear: new Date(),
    isFilter: false,
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    isLoading: false,
    // export
    openMoreMenu: false,
  }),
  computed: {
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    landedFormName() {
      let formName = this.accessIdRights("316");
      return formName?.formName;
    },
    payrollReconciliationFormAccess() {
      return this.$store.getters.payrollReconciliationFormAccess;
    },
    searchList() {
      return this.itemList.flatMap((emp) =>
        emp.Salary_Details.map((detail) => ({
          Employee_Id: emp.Employee_Id,
          Employee_Name: emp.Employee_Name,
          ...detail,
        }))
      );
    },
    getDisabledDates() {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth();
      const startOfPreviousMonth = new Date(currentYear, currentMonth - 1, 1);
      const endOfCurrentMonth = new Date(
        currentYear,
        currentMonth,
        new Date().getDate()
      );

      return {
        to: startOfPreviousMonth,
        from: endOfCurrentMonth,
        preventDisableDateSelection: true,
      };
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.payrollReconciliationFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    formAccess() {
      let formAccess = this.accessIdRights("316");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    headers() {
      return [
        { title: "Employee Name", key: "Employee_Name", align: "start" },
        { title: "Component", key: "Component", sortable: false },
        {
          title: "Salary Master (THB)",
          key: "Current_Amount",
          sortable: false,
        },
        {
          title: "Current Month Salary (THB)",
          key: "Previous_Amount",
          sortable: false,
        },
        { title: "Difference (THB)", key: "Difference", sortable: false },
      ];
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.isFilter) {
        msgText = "There are no employees for the selected filters/searches";
      }
      return msgText;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },
  methods: {
    checkNullValue,
    getEmployeeName(employeeId) {
      const employee = this.itemList.find(
        (emp) => emp.Employee_Id === employeeId
      );
      return employee ? employee.Employee_Name : "";
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.itemList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    // reset filter
    resetFilter() {
      this.itemList = this.originalList;
      this.isFilter = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },
    //function handling tab change
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.rosterManagementFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/payroll/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/payroll/" + clickedForm.url;
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    // Retrieving the list of Invited Individuals
    async fetchList() {
      let vm = this;
      try {
        vm.listLoading = true;
        vm.isErrorInList = false;
        let payslipMonth = moment(vm.selectedMonthYear).isValid()
          ? moment(vm.selectedMonthYear).format("M")
          : null;
        let payslipYear = moment(vm.selectedMonthYear).isValid()
          ? moment(vm.selectedMonthYear).format("YYYY")
          : null;
        const apiObj = {
          url:
            // `https://capricetest.hrapp.co.in/` +
            vm.baseUrl + "reports/hr-reports/list-payroll-reconciliation/",
          type: "POST",
          dataType: "json",
          data: {
            payslipMonth: parseInt(payslipMonth),
            payslipYear: parseInt(payslipYear),
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          vm.itemList = response?.payrollReconciliation;
          vm.originalList = response?.payrollReconciliation;
          vm.listLoading = false;
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please try after some time.",
          };
          vm.itemList = [];
          vm.originalList = [];
          vm.listLoading = false;
          vm.showAlert(snackbarData);
        }
      } catch (err) {
        vm.isErrorInList = true;
        vm.listLoading = false;
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: err?.response?.data?.msg
            ? err.response.data.msg
            : "Something went wrong. Please try again later.",
        });
      }
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Payroll Reconciliation",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.fetchList();
      this.resetFilter();
    },
    exportReportFile() {
      let exportData = [];

      this.originalList.forEach((employee) => {
        let firstRow = true;

        employee.Salary_Details.forEach((salary) => {
          let row = {
            Employee_Id: firstRow ? employee.Employee_Id : "",
            Employee_Name: firstRow ? employee.Employee_Name : "",
            Component: salary.Component.replace(/_/g, " "),
            Previous_Amount: salary.Previous_Amount,
            Current_Amount: salary.Current_Amount,
            Difference: salary.Difference,
          };

          exportData.push(row);
          firstRow = false; // Ensure Employee ID and Name only appear in the first row
        });
      });

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Payroll Reconciliation",
        sheetName: "Payroll Reconciliation",
        header: [
          { key: "Employee_Id", header: "Employee ID" },
          { key: "Employee_Name", header: "Employee Name" },
          { key: "Component", header: "Component" },
          { key: "Current_Amount", header: "Salary Master (THB)" },
          { key: "Previous_Amount", header: "Current Month Salary (THB)" },
          { key: "Difference", header: "Difference (THB)" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
