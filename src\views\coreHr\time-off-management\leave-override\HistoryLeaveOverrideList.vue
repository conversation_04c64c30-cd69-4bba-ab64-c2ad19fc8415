<template>
  <v-row class="d-flex justify-space-between my-2 pa-1">
    <v-col cols="6" sm="6">
      <div class="d-flex" :class="isMobileView ? 'mb-2' : ''">
        <v-btn
          color="white"
          rounded="lg"
          class="mr-2"
          @click="onCloseView()"
          :size="isMobileView ? 'small' : 'default'"
          ><v-icon color="primary" size="14" class="pr-2"
            >fas fa-chevron-left</v-icon
          ><span>Back</span></v-btn
        >
        <v-btn
          color="white"
          class="px-3 mr-3"
          rounded="lg"
          size="'default'"
          v-if="selectedEmployee"
        >
          <span v-if="selectedEmployee.employeeName">{{
            selectedEmployee.employeeName + " - "
          }}</span
          ><span v-if="selectedEmployee.userDefinedEmpId">{{
            selectedEmployee.userDefinedEmpId + " - "
          }}</span
          ><span class="text-primary" v-if="selectedEmployee.leaveType">{{
            selectedEmployee.leaveType
          }}</span></v-btn
        >
      </div>
    </v-col>
    <v-col
      cols="6"
      sm="6"
      class="d-flex justify-end"
      v-if="leaveOverrideHistory.length > 0"
    >
      <v-btn
        color="transparent"
        class="ml-1 mt-2"
        variant="flat"
        size="small"
        @click="getHistoryData(selectedEmployee)"
        ><v-icon color="grey">fas fa-redo-alt</v-icon></v-btn
      >
      <v-menu v-model="openMoreMenu" transition="scale-transition">
        <template v-slot:activator="{ props }">
          <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
            <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
            <v-icon v-else>fas fa-caret-up</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="action in moreActions"
            :key="action.key"
            @click="onMoreAction(action.key)"
          >
            <v-hover>
              <template v-slot:default="{ isHovering, props }">
                <v-list-item-title
                  v-bind="props"
                  class="py-2 px-3"
                  :class="{
                    'pink-lighten-5': isHovering,
                  }"
                  ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                  >{{ action.key }}</v-list-item-title
                >
              </template>
            </v-hover>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-col>
  </v-row>
  <div>
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else-if="isErrorInList">
      <AppFetchErrorScreen
        image-name="common/common-error-image"
        :content="errorContent"
        icon-name="fas fa-redo-alt"
        button-text="Retry"
        :isSmallImage="true"
        @button-click="getHistoryData(selectedEmployee)"
      >
      </AppFetchErrorScreen>
    </div>
    <div v-else-if="leaveOverrideHistory.length > 0">
      <v-data-table
        :headers="tableHeaders"
        :items="leaveOverrideHistory"
        :items-per-page="50"
        :show-select="false"
        :sort-by="[{ key: 'employeeId', order: 'asc' }]"
        :height="
          $store.getters.getTableHeightBasedOnScreenSize(
            290,
            leaveOverrideHistory,
            true
          )
        "
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
        fixed-header
        item-value="employeeId"
      >
        <template v-slot:item="{ item }">
          <tr
            class="data-table-tr bg-white cursor-pointer"
            :class="
              isMobileView ? 'v-data-table__mobile-table-row ma-0 mt-2' : ''
            "
          >
            <td id="mobile-view-td">
              <div id="mobile-header" class="font-weight-bold mt-2">
                Current Year Leave Entitlement
              </div>
              <section class="text-body-2 text-primary">
                {{ item?.currentYearTotalEligibleDays }}
              </section>
            </td>
            <td id="mobile-view-td">
              <div id="mobile-header" class="font-weight-bold mt-2">
                Leave Taken
              </div>
              <section class="text-body-2 text-primary">
                {{ item?.leavesTaken }}
              </section>
            </td>
            <td id="mobile-view-td">
              <div id="mobile-header" class="font-weight-bold mt-2">
                Carry Over
              </div>
              <section class="text-body-2 text-primary">
                {{ item?.carryOver }}
              </section>
            </td>
            <td id="mobile-view-td">
              <div id="mobile-header" class="font-weight-bold mt-2">
                Carry Over Balance
              </div>
              <section class="text-body-2 text-primary">
                {{ item?.lastCOBalance }}
              </section>
            </td>
            <td id="mobile-view-td">
              <div id="mobile-header" class="font-weight-bold mt-2">
                Updated On
              </div>
              <section class="text-body-2 text-primary">
                {{ formatDate(item?.updatedOn) }}
              </section>
            </td>
            <td id="mobile-view-td">
              <div id="mobile-header" class="font-weight-bold mt-2">
                Updated By
              </div>
              <section class="text-body-2 text-primary">
                {{ checkNullValue(item?.updatedBy) }}
              </section>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>

    <AppFetchErrorScreen
      v-else
      key="no-results-screen"
      main-title="There are no leave override history matched for the selected employee."
      image-name="common/no-records"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4">
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="windowWidth <= 960 ? 'small' : 'default'"
                @click="getHistoryData(selectedEmployee)"
              >
                Refresh
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
  </div>
</template>
<script>
import { GET_LEAVE_OVERRIDE_HISTORY } from "@/graphql/corehr/employeeDataQueries";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import moment from "moment";

export default {
  name: "HistoryLeaveOverride",
  mixins: [FileExportMixin],
  emits: ["close-split-view", "close-history-view"],
  props: {
    selectedHistory: {
      type: Object,
      required: true,
    },
    orgCode: {
      type: String,
      required: true,
    },
  },
  data: () => ({
    listLoading: false,
    leaveOverrideHistory: [],
    selectedEmployee: {},
    openMoreMenu: false,
    isErrorInList: false,
  }),
  computed: {
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    moreActions() {
      return [{ key: "Export", icon: "fas fa-file-export" }];
    },
    tableHeaders() {
      let headers = [
        {
          title: "Current Year Leave Entitlement",
          key: "currentYearLeaveEntitlement",
        },
        { title: "Leaves Taken", key: "leavesTaken" },
        { title: "Carry Over", key: "carryOver" },
        { title: "Carry Over Balance", key: "carryOverBalance" },
        { title: "Updated On", key: "updatedOn" },
        { title: "Updated By", key: "updatedBy" },
      ];
      return headers;
    },
  },
  watch: {
    selectedHistory: {
      immediate: true,
      handler(newData) {
        this.leaveOverrideHistory = [];
        this.selectedEmployee = newData;
        if (newData.employeeId && newData.leaveTypeId) {
          this.getHistoryData(newData);
        }
      },
    },
  },
  mounted() {},
  methods: {
    checkNullValue,
    getHistoryData(empData) {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_LEAVE_OVERRIDE_HISTORY,
          client: "apolloClientI",
          variables: {
            employeeId: empData.employeeId,
            leaveTypeId: empData.leaveTypeId,
            Org_Code: this.orgCode,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          vm.listLoading = false;
          if (
            res &&
            res.data &&
            res.data.retrieveLeaveOverrideHistory &&
            res.data.retrieveLeaveOverrideHistory.historyDetails
          ) {
            const resultData =
              res.data.retrieveLeaveOverrideHistory.historyDetails;
            this.leaveOverrideHistory = resultData;
          }
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Leave override history",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    onCloseView() {
      this.$emit("close-history-view");
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = this.leaveOverrideHistory;
      exportData = exportData.map((el) => ({
        ...el,
        employeeId: this.selectedEmployee.userDefinedEmpId,
        employeeName: this.selectedEmployee.employeeName,
        leaveType: this.selectedEmployee.leaveType,
        currentYearLeaveEntitlement: el.currentYearTotalEligibleDays,
        carryOverBalance: el.lastCOBalance,
        leavesTaken: el.leavesTaken,
        carryOver: el?.carryOver,
        updatedOn: el.updatedOn ? this.formatDate(el.updatedOn) : "",
        updatedBy: el.updatedBy,
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Leave override History",
        sheetName: "Leave override History",
        header: [
          { key: "employeeId", header: "Employee Id" },
          { key: "employeeName", header: "Employee Name" },
          { key: "leaveType", header: "Leave Type" },
          {
            key: "currentYearLeaveEntitlement",
            header: "Current Year Leave Entitlement",
          },
          { key: "carryOverBalance", header: "Carry Over Balance" },
          { key: "leavesTaken", header: "Leaves Taken" },
          { key: "carryOver", header: "Carry Over" },
          { key: "updatedOn", header: "Updated On" },
          { key: "updatedBy", header: "Updated By" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
