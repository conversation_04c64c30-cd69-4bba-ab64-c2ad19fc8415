<template>
  <v-file-input
    prepend-icon=""
    show-size
    v-model="fileContent"
    append-inner-icon="fas fa-paperclip"
    :label="fieldLabel"
    variant="solo"
    @change="onChangeFiles"
  ></v-file-input>
</template>

<script>
import moment from "moment";

export default {
  name: "FileUploadField",
  props: {
    fieldLabel: {
      type: String,
      default: "Document",
    },
    employeeId: {
      type: Number,
      required: true,
    },
    folderName: {
      type: String,
      required: true,
    },
    fileAction: {
      type: String,
      default: "upload",
    },
    fileUploadType: {
      type: String,
      default: "documents",
    },
    uploadedFileName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      fileContent: [],
      fileName: "",
      fileSize: "",
    };
  },
  computed: {
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    fileUploadUrl() {
      return (
        this.domainName +
        "/" +
        this.orgCode +
        "/" +
        this.folderName +
        "/" +
        this.uniqueFileName
      );
    },
    uniqueFileName() {
      return this.employeeId + "?" + this.currentTimeStamp + "?1?";
    },
  },
  methods: {
    onChangeFiles() {
      this.fileSize = this.fileContent[0].size;
      this.fileName = this.fileContent[0].name;
      this.$emit("on-change-file");
    },
    async uploadFileContents() {
      let vm = this;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: vm.fileUploadUrl + vm.fileName,
          action: vm.fileAction,
          type: vm.fileUploadType,
          fileContent: vm.fileContent[0],
        })
        .then(() => {
          vm.$emit("file-uploaded", {
            name: vm.fileName,
            size: vm.fileSize,
          });
        })
        .catch(() => {
          vm.$emit("file-upload-failed");
        });
    },
  },
};
</script>
