import gql from "graphql-tag";

///////////////////
// Queries ///////
//////////////////

export const LIST_EARLY_CHECKOUT = gql`
  query listEarlyCheckOutEmployees(
    $startDate: Date!
    $endDate: Date!
    $offset: Int
    $limit: Int
  ) {
    listEarlyCheckOutEmployees(
      startDate: $startDate
      endDate: $endDate
      offset: $offset
      limit: $limit
    ) {
      errorCode
      message
      summaryDetails {
        Attendance_Summary_Id
        Employee_Id
        Attendance_Date
        First_In
        Last_Out
        Early_Checkout_Hours
        Service_Provider_Id
        Employee_Name
        User_Defined_Employee_Id
        Regular_From_Date_Time
        Regular_End_Date_Time
        Consideration_From_Date_Time
        Consideration_To_Date_Time
        Work_Schedule_Title
      }
    }
  }
`;

///////////////////
// Mutations ///////
//////////////////

export const INITIATE_EARLY_CHECKOUT = gql`
  mutation triggerEarlyCheckoutProcess($attendanceSummaryIds: [Int]!) {
    triggerEarlyCheckoutProcess(attendanceSummaryIds: $attendanceSummaryIds) {
      errorCode
      message
    }
  }
`;

export const IGNORE_EARLY_CHECKOUT = gql`
  mutation ignoreEarlyCheckout($attendanceSummaryIds: [Int]!) {
    ignoreEarlyCheckout(attendanceSummaryIds: $attendanceSummaryIds) {
      errorCode
      message
    }
  }
`;
