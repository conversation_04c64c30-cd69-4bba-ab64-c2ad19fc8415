import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const LIST_DEPARTMENTS = gql`
  query listDepartments {
    listDepartments {
      errorCode
      message
      departments
    }
  }
`;
export const LIST_DEPARTMENT_HIERARCHY = gql`
  query listDepartmentHierarchy {
    listDepartmentHierarchy {
      errorCode
      message
      departmentHierarchy
    }
  }
`;
export const LIST_DEPARTMENT_HEADERS = gql`
  query listDepartmentHeaders {
    listDepartmentHeaders {
      errorCode
      message
      departmentHeaders
    }
  }
`;
export const LIST_BONUS_TYPES = gql`
  query listBonusTypes {
    listBonusTypes {
      errorCode
      message
      bonusTypes
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_DEPARTMENT = gql`
  mutation addUpdateDepartment(
    $Department_Id: Int
    $Department_Name: String!
    $Department_Code: String
    $Organization_Type_Id: Int!
    $Parent_Type_Id: Int!
    $BonusType_Id: Int
    $Department_Status: Status!
    $Description: String
  ) {
    addUpdateDepartment(
      Department_Id: $Department_Id
      Department_Name: $Department_Name
      Department_Code: $Department_Code
      Organization_Type_Id: $Organization_Type_Id
      Parent_Type_Id: $Parent_Type_Id
      BonusType_Id: $BonusType_Id
      Department_Status: $Department_Status
      Description: $Description
    ) {
      errorCode
      message
    }
  }
`;
export const DELETE_DEPARTMENT = gql`
  mutation deleteDepartment($Department_Id: PositiveInt!) {
    deleteDepartment(Department_Id: $Department_Id) {
      errorCode
      message
    }
  }
`;
