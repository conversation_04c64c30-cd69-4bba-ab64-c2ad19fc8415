<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            New
          </v-chip>
          <span class="text-subtitle-1 font-weight-bold">Shift Rotation</span>
        </div>
        <div class="d-flex pa-1">
          <v-icon color="primary" @click="closeAddForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <FormTab
        :model-value="openedSubTab"
        grow
        :hide-slider="true"
        style="border-bottom: 1px solid #cfcfcf; border-radius: 0px"
      >
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            {{ tab.label }}
            <div
              v-if="isActiveSubTab(tab.value)"
              class="mt-3 mb-n4"
              :class="[
                isActiveSubTab(tab.value)
                  ? 'text-primary font-weight-bold'
                  : 'text-grey-darken-2 font-weight-bold',
              ]"
              style="border-bottom: 4px solid; width: 200px"
            ></div>
          </div>
        </v-tab>
      </FormTab>
      <div style="height: fit-content">
        <v-card-text>
          <v-window v-model="openedSubTab" style="width: 100%">
            <v-window-item value="shift-rotation">
              <div style="height: calc(100vh - 400px); overflow: scroll">
                <v-form ref="ShiftForm">
                  <v-row class="px-sm-4 px-md-6 pt-sm-4">
                    <v-col cols="6" class="pl-sm-4 pl-md-6">
                      <div class="d-flex">
                        <v-text-field
                          ref="schedulerName"
                          v-model="schedulerName"
                          variant="solo"
                          :rules="[
                            required('Scheduler Name', schedulerName),
                            schedulerName
                              ? validateWithRulesAndReturnMessages(
                                  schedulerName,
                                  'Scheduler_Name',
                                  'Scheduler Name'
                                )
                              : true,
                          ]"
                          @update:model-value="deductFormChange()"
                          ><template v-slot:label>
                            <span>Scheduler Name</span>
                            <span style="color: red">*</span>
                          </template>
                        </v-text-field>
                      </div>
                    </v-col>

                    <v-col cols="6" class="pl-sm-4 pl-md-6">
                      <span class="v-label">
                        Repeat Schedule
                        <span class="ml-1" style="color: red">*</span></span
                      >
                      <v-switch
                        color="primary"
                        v-model="isRepeatSchedule"
                        :true-value="1"
                        :false-value="0"
                        @update:model-value="deductFormChange()"
                      ></v-switch
                    ></v-col>

                    <v-col
                      v-if="isRepeatSchedule"
                      cols="6"
                      class="pl-sm-4 pl-md-6"
                    >
                      <div class="d-flex">
                        <v-text-field
                          v-model="repeatsCount"
                          ref="repeatsCount"
                          variant="solo"
                          type="number"
                          :rules="[
                            required('Repeat count', repeatsCount),
                            numericValidation('Repeat count', repeatsCount),
                            (value) =>
                              (value >= 1 && value <= 10) ||
                              'Repeat count must range between 1 & 10',
                          ]"
                          @update:model-value="deductFormChange()"
                        >
                          <template v-slot:label>
                            <span>Repeat Count</span>
                            <span class="ml-1" style="color: red">*</span>
                          </template>
                        </v-text-field>
                      </div>
                    </v-col>

                    <v-col cols="6" class="pl-sm-4 pl-md-6">
                      <span class="v-label">
                        Enable Roster Leave
                        <span class="ml-1" style="color: red">*</span></span
                      >
                      <v-switch
                        color="primary"
                        v-model="isEnableRoster"
                        :true-value="1"
                        :false-value="0"
                        @update:model-value="deductFormChange()"
                      ></v-switch
                    ></v-col>

                    <v-col
                      v-if="isEnableRoster"
                      cols="6"
                      class="pl-sm-4 pl-md-6"
                    >
                      <div class="d-flex">
                        <v-text-field
                          v-model="leaveEntitlement"
                          ref="leaveEntitlement"
                          variant="solo"
                          type="number"
                          :max="10"
                          :rules="[
                            required(
                              'Leave Entitlement Per Roster Day',
                              leaveEntitlement
                            ),
                            (value) =>
                              (value >= 0.1 && value <= 1) ||
                              'Leave Entitlement Per Roster Day must range between 0.1 & 1',
                          ]"
                          @update:model-value="deductFormChange()"
                        >
                          <template v-slot:label>
                            <span>Leave Entitlement Per Roster Day</span>
                            <span class="ml-1" style="color: red">*</span>
                          </template>
                        </v-text-field>
                      </div>
                    </v-col>

                    <v-col
                      v-if="isEnableRoster"
                      cols="6"
                      class="pl-sm-4 pl-md-6"
                    >
                      <div class="d-flex">
                        <CustomSelect
                          v-model="leaveReplenishment"
                          ref="leaveReplenishment"
                          :items="listLeaveRelineshement"
                          label="Leave Replenishment Period"
                          :is-auto-complete="true"
                          :isRequired="true"
                          variant="solo"
                          :item-selected="leaveReplenishment"
                          :rules="[
                            required(
                              'Leave Replenishment Period',
                              leaveReplenishment
                            ),
                          ]"
                          @selected-item="
                            onChangeCustomSelectField(
                              $event,
                              'leaveReplenishment'
                            )
                          "
                          @update:model-value="deductFormChange()"
                        />
                      </div>
                    </v-col>

                    <v-col
                      v-if="isEnableRoster"
                      cols="6"
                      class="pl-sm-4 pl-md-6"
                    >
                      <div class="d-flex">
                        <CustomSelect
                          :items="listLeaveTypes"
                          v-model="leaveType"
                          ref="leaveType"
                          label="Leave Type"
                          itemValue="LeaveType_Id"
                          itemTitle="Leave_Name"
                          :rules="[required('Leave Type', leaveType)]"
                          :isLoading="isLeaveLoading"
                          :isAutoComplete="true"
                          :isRequired="true"
                          :item-selected="leaveType"
                          variant="solo"
                          @selected-item="
                            onChangeCustomSelectField($event, 'leaveType')
                          "
                          @update:model-value="deductFormChange()"
                        />
                      </div>
                    </v-col>
                  </v-row>
                </v-form>
              </div>
              <v-row>
                <v-col cols="12" class="d-flex justify-end">
                  <v-btn
                    v-if="formAccess.add"
                    :disabled="!isFormDirty"
                    class="primary"
                    variant="elevated"
                    rounded="lg"
                    @click="validate1stTab()"
                    >Next</v-btn
                  >
                </v-col>
              </v-row>
            </v-window-item>
            <v-window-item value="rotation-bywork-schedule">
              <div style="height: calc(100vh - 400px); overflow: scroll">
                <v-form ref="RotationForm">
                  <v-row>
                    <div
                      v-for="(rotation, index) in workScheduleList"
                      :key="index"
                      class="rounded-lg pa-3 ma-1 mb-4"
                      style="
                        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1) !important;
                      "
                    >
                      <v-row
                        v-if="
                          index === workScheduleList.length - 1 &&
                          workScheduleList.length > 1
                        "
                      >
                        <v-col cols="12" class="d-flex justify-end">
                          <v-icon
                            size="15"
                            class="fas fa-trash"
                            color="primary"
                            @click="removeLastRotation()"
                        /></v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="6" class="pl-sm-4 pl-md-6">
                          <CustomSelect
                            v-model="rotation.ShiftType_Id"
                            color="primary"
                            item-value="Shift_Id"
                            :ref="`shiftType ${index}`"
                            label="Shift Type"
                            :isRequired="true"
                            item-title="Shift_Name"
                            :rules="[
                              required('Shift Type', rotation.ShiftType_Id),
                            ]"
                            item-color="primary"
                            :isAutoComplete="true"
                            :itemSelected="rotation.ShiftType_Id"
                            :items="listShiftType"
                            variant="solo"
                            single-line
                            @selected-item="(e) => {}"
                            @update:model-value="deductFormChange()"
                          />
                        </v-col>
                        <v-col
                          cols="6"
                          class="pl-sm-4 pl-md-6 d-flex align-center"
                        >
                          <v-text-field
                            v-model="rotation.Applicable_Period"
                            type="number"
                            :ref="`applicablePeriod ${index}`"
                            style="max-width: 55%"
                            :rules="[
                              required(
                                'Applicable Period',
                                rotation.Applicable_Period
                              ),
                              numericValidation(
                                'Applicable Period',
                                rotation.Applicable_Period
                              ),
                              (value) =>
                                (value >= 1 && value <= 31) ||
                                'Applicable Period must range between 1 & 31',
                            ]"
                            required
                            variant="solo"
                          >
                            <template v-slot:label>
                              <span>Applicable Period</span>
                              <span style="color: red">*</span>
                            </template>
                          </v-text-field>
                          <CustomSelect
                            v-model="rotation.Period_Unit"
                            color="primary"
                            :items="listApplicablePeriod"
                            variant="solo"
                            class="ml-2"
                            :ref="`periodUnit ${index}`"
                            style="max-width: 45%"
                            single-line
                            item-color="primary"
                            :itemSelected="rotation.Period_Unit"
                            @selected-item="rotation.Period_Unit = $event"
                            @update:model-value="deductFormChange()"
                          />
                        </v-col>
                        <v-col cols="6" class="pl-sm-4 pl-md-6">
                          <p class="text-subtitle-1 text-grey-darken-1">
                            Shift Order Level
                          </p>
                          <p class="text-subtitle-1 font-weight-regular">
                            {{ rotation.Rotation_Level }}
                          </p>
                        </v-col>
                      </v-row>
                    </div>
                  </v-row>
                </v-form>
              </div>

              <v-row>
                <v-col cols="12" class="mb-1 mr-5 d-flex justify-space-between">
                  <v-btn
                    variant="outlined"
                    rounded="lg"
                    @click="openedSubTab = 'shift-rotation'"
                    ><span>Previous</span>
                  </v-btn>
                  <div class="d-flex">
                    <v-btn
                      class="secondary mr-2"
                      variant="outlined"
                      rounded="lg"
                      @click="addNewActivity()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon
                      ><span>Add New</span>
                    </v-btn>
                    <v-btn
                      class="primary"
                      variant="elevated"
                      rounded="lg"
                      :disabled="isLoading"
                      @click="validateAddForm()"
                      ><span>Save</span></v-btn
                    >
                  </div>
                </v-col>
              </v-row>
            </v-window-item>
          </v-window>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="showConfirmation"
      :open-modal="showConfirmation"
      imgUrl="common/exit_form"
      confirmation-heading="Are you sure to exit this form?"
      @close-warning-modal="showConfirmation = false"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 secondary"
            variant="text"
            @click="closeValidationAlert()"
          >
            <span class="primary">Close</span>
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
// Queries
import {
  LIST_SHIFT_LEAVE_TYPES,
  LIST_SHIFT_TYPE,
  ADD_UPDATE_SHIFT_ROTATION,
} from "@/graphql/roster-management/ShiftRotationQueries.js";
export default {
  name: "AddEditShiftRotation",

  components: {
    CustomSelect,
  },

  mixins: [validationRules],

  props: {
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  emits: ["close-form", "added-record"],

  data: () => ({
    workScheduleList: [
      {
        Rotation_Level: 1,
        ShiftType_Id: null,
        Applicable_Period: 1,
        Period_Unit: "day(s)",
      },
    ],
    // others
    showConfirmation: false,
    isFormDirty: false,
    isLoading: false,

    //Shit Schedule
    schedulerName: "",
    isRepeatSchedule: 0,
    repeatsCount: 1,
    isEnableRoster: 0,
    leaveEntitlement: 1,
    leaveReplenishment: null,
    leaveType: null,
    listLeaveTypes: [],
    listLeaveRelineshement: ["Daily"],
    isLeaveLoading: false,
    isShiftLoding: false,
    listShiftType: [],
    listApplicablePeriod: ["day(s)", "week(s)"],
    openedSubTab: "shift-rotation",

    // errors
    validationMessages: [],
    showValidationAlert: false,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    subTabItems() {
      return [
        {
          label: "Shift Rotation",
          value: "shift-rotation",
          disable: false,
        },
        {
          label: "Rotation By Work Schedule",
          value: "rotation-bywork-schedule",
          disable: true,
        },
      ];
    },
  },
  watch: {
    isRepeatSchedule(val) {
      if (!val) {
        this.repeatsCount = 1;
      }
    },
    isEnableRoster(val) {
      if (!val) {
        this.leaveEntitlement = 1;
        this.leaveReplenishment = null;
        this.leaveType = null;
      }
    },
  },

  mounted() {
    this.fetchLeaveType();
    this.fetchShiftScheduling();
  },

  methods: {
    removeLastRotation() {
      if (this.workScheduleList.length > 1) {
        this.workScheduleList.pop();
      }
    },
    addNewActivity() {
      const newActivity = {
        Rotation_Level: this.workScheduleList.length + 1,
        ShiftType_Id: null,
        Applicable_Period: 1,
        Period_Unit: "day(s)",
      };
      this.workScheduleList.push(newActivity);
    },
    onChangeCustomSelectField(value, field, index) {
      this.deductFormChange();
      this.workScheduleList[index][field] = value;
    },
    isActiveSubTab(val) {
      this.subTabItems.forEach((item) => {
        item.disable = item.value === val ? true : false;
      });
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },

    // called when on change function of all fields in this form
    deductFormChange() {
      this.isFormDirty = true;
    },

    // before closing the form we have ask close confirmation from user
    closeAddForm() {
      if (this.isFormDirty) {
        this.showConfirmation = true;
      } else {
        this.closeForm();
      }
    },

    // when accepting close in confirmation screen, we have to close the form
    acceptClose() {
      this.showConfirmation = false;
      this.closeForm();
    },

    closeForm() {
      this.isFormDirty = false;
      if (this.openedSubTab === "rotation-bywork-schedule") {
        this.$emit("close-form");
      } else {
        this.$emit("close-form", this.openCloneModal);
      }
    },

    async validate1stTab() {
      let isFormValid = await this.$refs.ShiftForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.openedSubTab = "rotation-bywork-schedule";
      } else {
        this.handleFocusFunction(["leaveReplenishment", "leaveType"]);
      }
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    fetchShiftScheduling() {
      let vm = this;
      vm.isShiftLoding = true;
      vm.$apollo
        .query({
          query: LIST_SHIFT_TYPE,
          client: "apolloClientC",
          fetchPolicy: "no-cache",
          variables: {
            sortField: 0,
            sortOrder: "",
            searchString: "",
            shiftName: "",
            minCount: 0,
            maxCount: 0,
            isDropDown: 0,
          },
        })
        .then(({ data }) => {
          if (
            data?.listShiftType?.shiftType &&
            !data?.listShiftType?.errorCode
          ) {
            let activeShiftTypes = data.listShiftType.shiftType.filter((el) => {
              return el.Status && el.Status.toLowerCase() === "active";
            });
            vm.listShiftType = activeShiftTypes;
            vm.isShiftLoding = false;
          } else {
            vm.handleListShiftError();
          }
        })
        .catch((err) => {
          vm.handleListShiftError(err);
        });
    },
    handleListShiftError(err = "") {
      this.isShiftLoding = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Shift Rotation",
        isListError: true,
      });
    },

    fetchLeaveType() {
      let vm = this;
      vm.isLeaveLoading = true;
      vm.$apollo
        .query({
          query: LIST_SHIFT_LEAVE_TYPES,
          client: "apolloClientL",
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (
            data &&
            data.listShiftLeaveTypes &&
            data.listShiftLeaveTypes.shiftLeaveTypes &&
            !data.listShiftLeaveTypes.errorCode
          ) {
            this.listLeaveTypes = JSON.parse(
              data.listShiftLeaveTypes.shiftLeaveTypes
            );
            vm.isLeaveLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.isLeaveLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Shift Rotation",
        isListError: true,
      });
    },
    async validateAddForm() {
      let isFormValid = await this.$refs.RotationForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.isLoading = true;
        this.addShiftRotation();
      } else {
        let arrayList = [];
        for (let i = 0; i < this.workScheduleList.length; i++) {
          arrayList.push(`shiftType ${i}`, `periodUnit ${i}`);
        }
        this.handleFocusFunction(arrayList);
      }
    },
    handleFocusFunction(arrayList) {
      const invalidFields = [];
      Object.keys(this.$refs).forEach((refName) => {
        const field = this.$refs[refName];
        if (field && field.rules) {
          let allTrue = field.rules.every((value) => value === true);
          if (field.rules.length > 0 && !allTrue) {
            invalidFields.push(refName);
          }
        }
      });
      // Log or handle the invalid fields
      if (invalidFields.length > 0) {
        const firstErrorField = invalidFields[0];
        this.$nextTick(() => {
          const fieldRef = this.$refs[firstErrorField];
          if (fieldRef) {
            let selectFields = arrayList;
            if (selectFields.includes(firstErrorField)) {
              fieldRef.onFocusCustomSelect
                ? fieldRef.onFocusCustomSelect()
                : fieldRef.focus();
            } else {
              // except for select
              fieldRef.focus();
            }
            if (fieldRef.$el) {
              const rect = fieldRef.$el.getBoundingClientRect();
              window.scrollTo({
                top: (window.scrollY + rect.top) * 0.4, // Adjust as needed
                behavior: "smooth",
              });
            }
          }
        });
      }
    },
    addShiftRotation() {
      let vm = this;
      vm.isLoading = true;
      let workScheduleList = vm.workScheduleList.map((item) => ({
        ...item,
        Applicable_Period: parseInt(item.Applicable_Period, 10),
        Period_Unit: item.Period_Unit !== "week(s)" ? "day" : "week",
      }));
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_SHIFT_ROTATION,
          client: "apolloClientM",
          variables: {
            Rotation_Id: 0,
            Scheduler_Name: vm.schedulerName,
            Repeat_Schedule: vm.isRepeatSchedule ? "Yes" : "No",
            Repeat_Count: vm.isRepeatSchedule ? parseInt(vm.repeatsCount) : 0,
            Enable_Roster_Leave: vm.isEnableRoster ? "Yes" : "No",
            Leave_Entitlement_Per_Roster_Day: vm.isEnableRoster
              ? parseFloat(vm.leaveEntitlement)
              : parseFloat(0),
            Leave_Replenishment_Period: vm.leaveReplenishment,
            LeaveType_Id: vm.leaveType,
            Shift_Rotation_Schedule: workScheduleList,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.addUpdateShiftRotation &&
            !response.data.addUpdateShiftRotation.errorCode
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Shift Rotation added successfully!",
            };
            vm.showAlert(snackbarData);
            vm.$emit("added-record");
            vm.isLoading = false;
          } else {
            vm.handleInviteErrors();
          }
        })
        .catch((error) => {
          vm.handleInviteErrors(error);
        });
    },
    handleInviteErrors(error) {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "adding",
        form: "shift rotation",
        isListError: false,
      });
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
