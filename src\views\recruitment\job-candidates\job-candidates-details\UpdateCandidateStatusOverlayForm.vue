<template>
  <div class="text-center">
    <v-overlay
      v-model="overlay"
      class="d-flex justify-end overlay-content-parent"
      @click:outside="onClickClose()"
      persistent
      style="z-index: 1000"
    >
      <template v-slot:default>
        <div class="overlay-card">
          <div
            class="d-flex align-center text-h6 text-medium-emphasis pa-2 bg-primary"
            style="width: 100%"
          >
            <v-icon
              v-if="displayCustomEmail && !noCustomTemplate"
              @click="
                displayCustomEmail
                  ? (displayCustomEmail = false)
                  : $emit('close-overlay')
              "
              size="17"
              class="mx-2"
              >fas fa-chevron-left</v-icon
            >
            <span class="ml-2"> Update Status </span>
            <v-spacer></v-spacer>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="onClickClose()"
            ></v-btn>
          </div>
          <div class="overlay-body">
            <v-form v-if="!displayCustomEmail" ref="cancelInterviewForm">
              <v-row class="px-sm-4 px-md-6 pt-sm-4">
                <v-col
                  v-if="selectedStage.toLowerCase() === 'archived'"
                  cols="12"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <CustomSelect
                    :items="archiveReasonList"
                    v-model="archiveReason"
                    label="Reason for Archiving"
                    itemValue="Reason_Id"
                    itemTitle="Reason"
                    ref="archiveReason"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :is-loading="isArchiveReasonLoading"
                    :rules="[required('Reason for Archiving', archiveReason)]"
                    :itemSelected="archiveReason"
                    @selected-item="archiveReason = $event"
                    @update:model-value="isFormDirty = true"
                  ></CustomSelect>
                </v-col>
                <v-col
                  v-if="selectedStage.toLowerCase() === 'archived'"
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <v-switch
                    v-model="notifyCandidate"
                    label="Notify Candidate"
                    :false-value="false"
                    :true-value="true"
                    color="primary"
                    hide-details
                  ></v-switch>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                  v-if="notifyCandidate"
                >
                  <CustomSelect
                    :items="notificationTimeList"
                    v-model="notificationTime"
                    label="Time"
                    itemValue="notificationTime"
                    itemTitle="notificationTime"
                    ref="notificationTime"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :rules="[required('Notification Time', notificationTime)]"
                    :itemSelected="notificationTime"
                    @selected-item="notificationTime = $event"
                    @update:model-value="isFormDirty = true"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  class="pl-sm-4 pl-md-6"
                  v-if="
                    (selectedStage.toLowerCase() === 'archived' &&
                      notifyCandidate &&
                      emailTemplateList?.length) ||
                    selectedStage.toLowerCase() !== 'archived'
                  "
                >
                  <CustomSelect
                    :items="emailTemplateList"
                    v-model="selectedEmailTemplate"
                    label="Email Template"
                    itemValue="Template_Id"
                    itemTitle="Template_Name"
                    ref="selectedEmailTemplate"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :rules="[required('Email Template', selectedEmailTemplate)]"
                    :itemSelected="selectedEmailTemplate"
                    @selected-item="selectedEmailTemplate = $event"
                    @update:model-value="isFormDirty = true"
                  ></CustomSelect>
                </v-col>
              </v-row>
            </v-form>
            <CustomEmail
              v-else
              ref="customEmail"
              :formId="16"
              :typeOfTemplate="typeOfTemplate"
              :typeOfSchedule="typeOfSchedule"
              :template-email="templateEmail"
              :template-data="templateData"
              :selectedCandidateId="candidateId"
              :emailRecievers="emailRecievers"
              :emailTemplateList="emailTemplateList"
              :selectedEmailTemplate="selectedEmailTemplate"
              :notificationTimeNow="
                selectedStage.toLowerCase() === 'archived'
                  ? notificationTime?.toLowerCase() === 'now'
                    ? true
                    : false
                  : true
              "
              :submitText="
                notificationTime?.toLowerCase() === 'now'
                  ? 'Send Email'
                  : 'Submit'
              "
              :noCustomTemplate="noCustomTemplate"
              @custom-email-sent="customEmailSent"
            />
          </div>
          <v-card class="overlay-footer" elevation="16">
            <v-btn
              class="mr-5"
              variant="outlined"
              @click="
                displayCustomEmail && !noCustomTemplate
                  ? (this.displayCustomEmail = false)
                  : onClickClose()
              "
              rounded="lg"
              >Cancel</v-btn
            >
            <v-btn
              color="primary"
              variant="elevated"
              rounded="lg"
              @click="validateUpdateStatusForm()"
            >
              {{
                displayCustomEmail
                  ? selectedStage.toLowerCase() === "archived"
                    ? notificationTime &&
                      notificationTime.toLowerCase() === "now"
                      ? "Send Email"
                      : "Submit"
                    : "Send Email"
                  : selectedStage.toLowerCase() === "archived"
                  ? notifyCandidate
                    ? "Preview Email"
                    : "Submit"
                  : "Preview Email"
              }}
            </v-btn>
          </v-card>
        </div>
        <AppLoading v-if="isEmailTemplateListLoading"></AppLoading>
      </template>
    </v-overlay>
  </div>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  ></AppWarningModal>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import CustomEmail from "@/views/common/customEmail/CustomEmailComponent.vue";
import { LIST_CUSTOM_EMAIL_TEMPLATES } from "@/graphql/settings/email-template/emailTemplateQueries.js";
import {
  ARCHIVE_CANDIDATE_DETAILS,
  GET_ARCHIVE_REASONS_LIST,
} from "@/graphql/recruitment/recruitmentQueries.js";
import moment from "moment";

export default {
  name: "UpdateCandidateStatusOverlayForm",
  components: {
    CustomSelect,
    CustomEmail,
  },
  props: {
    candidateId: {
      type: Number,
      required: true,
    },
    notificationTimeNow: {
      type: Boolean,
      default: true,
    },
    typeOfTemplate: {
      type: String,
      required: false,
    },
    templateEmail: {
      type: Array,
      default: function () {
        return [];
      },
    },
    templateData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    typeOfSchedule: {
      type: String,
      required: true,
    },
    emailRecievers: {
      type: Array,
      required: false,
      default: function () {
        return [];
      },
    },
    selectedStatus: {
      type: Number,
      required: true,
    },
    selectedStage: {
      type: String,
      default: "",
    },
  },
  mixins: [validationRules],
  emits: ["custom-email-cancel", "close-overlay"],

  data: () => ({
    isFormDirty: false,
    overlay: true,
    selectedEmailTemplate: null,
    displayCustomEmail: false,
    emailTemplateList: [],
    openConfirmationPopup: false,
    isEmailTemplateListLoading: false,
    noCustomTemplate: false,
    notifyCandidate: false,
    notificationTimeList: [
      "Now",
      "After 2 Hours",
      "After 8 Hours",
      "After 24 Hours",
      "After 48 Hours",
    ],
    notificationTime: null,
    isArchiveReasonLoading: false,
    archiveReasonList: [],
    archiveReason: null,
    htmlContent: null,
  }),

  mounted() {
    this.fetchEmailTemplates();
    this.fetchArchiveReasonList();
  },
  watch: {
    notifyCandidate(val) {
      if (val) {
        this.buttonText = "Next";
        this.fetchEmailTemplates();
      } else {
        this.buttonText = "Submit";
      }
    },
    archiveReason(val) {
      this.archiveReasonValue = this.getReasonById(this.archiveReasonList, val);
    },
  },
  methods: {
    onClickClose() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.$emit("custom-email-cancel", false);
      }
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("custom-email-cancel", false);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async validateUpdateStatusForm() {
      this.htmlContent = null;
      if (!this.displayCustomEmail) {
        let { valid } = await this.$refs.cancelInterviewForm.validate();
        if (valid) {
          if (this.selectedStage.toLowerCase() === "archived") {
            if (this.notifyCandidate) {
              // if email notification is enabled then show email preview
              this.displayCustomEmail = true;
            } else {
              // if email notification is not enabled then simple submit the form
              await this.archiveCandidate();
              this.customEmailSent();
            }
          } else {
            this.displayCustomEmail = true;
          }
        }
      } else {
        // Here on the main page of email template
        let customEmailRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        if (customEmailRef) {
          this.htmlContent = customEmailRef.htmlContent;
          let noPlaceholderFound = customEmailRef.noPlaceholderFound;
          if (noPlaceholderFound) {
            let snackbarData = {
              isOpen: true,
              message:
                "Some placeholders are not replaced, kindly replace or remove them before proceeding.",
              type: "warning",
            };
            this.showAlert(snackbarData);
            customEmailRef.noPlaceholderFound = false;
          } else {
            if (this.selectedStage.toLowerCase() === "archived") {
              if (this.notifyCandidate) {
                // if on final page stage is archived and notify candidate is enabled then call the archive api after validating the form
                const { valid } =
                  await customEmailRef.$refs.customEmailForm.validate();
                if (valid && customEmailRef.isContentPresent) {
                  this.archiveCandidate(customEmailRef);
                }
              }
            } else {
              // if on final page stage is not archived then don't call the archive api
              await customEmailRef.validateCustomEmailForm();
            }
          }
        }
      }
    },
    fetchEmailTemplates() {
      let vm = this;
      vm.isEmailTemplateListLoading = true;
      const statusMapping = {
        3: { categoryId: 6, categoryTypeId: 5 },
        19: { categoryId: 6, categoryTypeId: 4 },
        20: { categoryId: 6, categoryTypeId: 3 },
        71: { categoryId: 9, categoryTypeId: 6 },
        72: { categoryId: 9, categoryTypeId: 12 },
        73: { categoryId: 9, categoryTypeId: 7 },
        74: { categoryId: 9, categoryTypeId: 13 },
      };

      // Assigning default values
      let categoryId = null;
      let categoryTypeId = null;

      // Check if selectedStatus exists in the mapping
      if (statusMapping[this.selectedStatus]) {
        categoryId = statusMapping[this.selectedStatus].categoryId;
        categoryTypeId = statusMapping[this.selectedStatus].categoryTypeId;
      }
      vm.$apollo
        .query({
          query: LIST_CUSTOM_EMAIL_TEMPLATES,
          variables: {
            formId: 16,
            categoryId: categoryId,
            categoryTypeId: categoryTypeId,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listCustomEmailTemplates &&
            response.data.listCustomEmailTemplates.emailTemplates
          ) {
            this.emailTemplateList =
              response.data.listCustomEmailTemplates.emailTemplates;
            this.noCustomTemplate =
              this.emailTemplateList?.length === 0 ? true : false;
            if (this.noCustomTemplate) {
              this.displayCustomEmail = true;
            }
          }
          vm.isEmailTemplateListLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.isEmailTemplateListLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "email templates",
        isListError: false,
      });
    },
    customEmailSent() {
      this.$emit("custom-email-sent");
    },
    getCustomEmailRef() {
      return this.$refs.customEmail;
    },
    getReasonById(reasons, reasonId) {
      const result = reasons.find((item) => item.Reason_Id === reasonId);
      return result ? result.Reason : "";
    },
    fetchArchiveReasonList() {
      let vm = this;
      vm.isArchiveReasonLoading = true;
      vm.$apollo
        .query({
          query: GET_ARCHIVE_REASONS_LIST,
          client: "apolloClientAY",
          variables: {
            formId: 16,
            stageId: this.candidateDetails?.Hiring_Stage_Id,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getArchiveReasonsList &&
            response.data.getArchiveReasonsList.archiveReasonList &&
            !response.data.getArchiveReasonsList.archiveReasonList.errorCode
          ) {
            vm.archiveReasonList =
              response.data.getArchiveReasonsList.archiveReasonList;
            vm.isArchiveReasonLoading = false;
          } else {
            vm.archiveReasonList = [];
            vm.isArchiveReasonLoading = false;
          }
        })
        .catch((err) => {
          vm.isArchiveReasonLoading = false;
          vm.handleFetchArchiveReasonListError(err);
        });
    },
    handleFetchArchiveReasonListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "archive reason list",
        isListError: false,
      });
    },
    async archiveCandidate(customEmailRef = null) {
      let vm = this;
      vm.isEmailTemplateListLoading = true;
      const formmatedNotificationTime = this.getFormattedDate(
        vm.notificationTime
      );
      try {
        const response = await vm.$apollo.mutate({
          mutation: ARCHIVE_CANDIDATE_DETAILS,
          client: "apolloClientBF",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: parseInt(vm.candidateId),
            archiveReasonId: vm.archiveReason,
            notificationTime: formmatedNotificationTime,
            archiveStatusId: this.selectedStatus,
            mailContent:
              vm.notifyCandidate &&
              vm.notificationTime &&
              vm.notificationTime.toLowerCase() === "now"
                ? null
                : vm.htmlContent,
            archiveStatus: "No",
            action: "candidate",
          },
        });

        if (
          response &&
          response.data &&
          response.data.archiveCandidateDetails
        ) {
          const { errorCode, validationError } =
            response.data.archiveCandidateDetails;
          if (!errorCode && !validationError) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate archived successfully.",
            };
            // sending the custom email
            if (customEmailRef) {
              try {
                await customEmailRef.validateCustomEmailForm();
              } catch (error) {
                vm.handleArchiveCandidateError(error);
              }
            }
            vm.showAlert(snackbarData);
          } else {
            vm.handleArchiveCandidateError();
          }
        } else {
          vm.handleArchiveCandidateError();
        }
      } catch (err) {
        vm.handleArchiveCandidateError(err);
      } finally {
        vm.isEmailTemplateListLoading = false;
        this.overlay = true;
      }
    },
    handleArchiveCandidateError(err = "") {
      this.isEmailTemplateListLoading = false;
      this.trackingStatusLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "archiving",
        form: "candidate details",
        isListError: false,
      });
    },
    getFormattedDate(selectedItem) {
      if (!selectedItem) {
        return null;
      }
      let date = moment().utc();

      switch (selectedItem) {
        case "Now":
          date = null;
          break;
        case "After 2 Hours":
          date = date.add(2, "hours");
          break;
        case "After 8 Hours":
          date = date.add(8, "hours");
          break;
        case "After 24 Hours":
          date = date.add(24, "hours");
          break;
        case "After 48 Hours":
          date = date.add(48, "hours");
          break;
        default:
          return "Invalid selection";
      }
      return date ? date.format("YYYY-MM-DD HH:mm:ss") : null;
    },
  },
};
</script>

<style scoped>
.headingColor {
  background-color: rgb(var(--v-theme-primary));
}

.overlay-card {
  height: 100%;
  width: 100%;
  background: white;
}

.overlay-content-parent {
  z-index: 1000 !important;
}

.overlay-content-parent > .v-overlay__content {
  height: 100%;
  width: 700px;
}

@media only screen and (max-width: 600px) {
  .overlay-content-parent > .v-overlay__content {
    width: 100%;
  }
}

.overlay-body {
  padding: 15px;
  height: calc(100vh - 130px);
  overflow-y: scroll !important;
  overflow: hidden;
}

#job-options-card {
  min-height: 30%;
}

.overlay-footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
