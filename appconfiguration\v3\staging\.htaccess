RewriteEngine On

# prevents the rule from being overrided by .htaccess files in subdirectories
RewriteOptions InheritDownBefore

#To redirect from HTTP to HTTPS

#RewriteCond %{HTTPS} off 
RewriteCond %{HTTP:X-Forwarded-Proto} =http
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

RewriteBase /v3
Options All -Indexes

RewriteCond %{REQUEST_FILENAME} -s [OR]
RewriteCond %{REQUEST_FILENAME} -l [OR]
RewriteCond %{REQUEST_FILENAME} -d

RewriteRule ^.*$ - [NC,L]
RewriteRule ^.*$ index.html [NC,L]

#RewriteCond %{HTTP_HOST} ^(www\.)?hrapp\.co.in [NC]
#RewriteCond %{HTTP_HOST} !^www\.hrapp\.co.in$
#RewriteCond %{HTTP_HOST} ^(www\.)?hrapp\.co.in\appmanager\$1 [NC]
#RewriteCond %{HTTP_HOST} !^www\.hrapp\.co.in\appmanager\$1
#RewriteCond %{HTTP_HOST} ^(www\.)?\*\.hrapp\.co.in [NC]
#RewriteCond %{HTTP_HOST} !^www\.\*\.hrapp\.co.in$
#RewriteRule ^(.*)$ http://%1.hrapp.co.in/auth/$1 [R=301,L]

#Header unset ETag
#FileETag None
<FilesMatch "(?i)^.*.(ico|flv|jpg|jpeg|png|gif|js|css|webp)$">
#Header unset Last-Modified
#Header set Expires "Fri, 21 Dec 2012 00:00:00 GMT"
#Header set Cache-Control "public, no-transform"
Header set Cache-Control "max-age=86400, public, no-cache, no-transform"
</FilesMatch>

#error page
ErrorDocument 401 /includes/401.php
ErrorDocument 403 /includes/403.php
ErrorDocument 404 /includes/404.php
ErrorDocument 500 /includes/500.php

# ensure there is no /index.html in the address bar
    RewriteCond %{THE_REQUEST} ^[A-Z]{3,9}\ /.*index\.html\ HTTP/
    RewriteRule ^(.*)index\.html$ $1 [R=301,L] # this was my attempt to stop /dir/index.html and make it simply /dir/

    RewriteRule ^index\.html/(.*) $1 [NS,NC,L,R=301]

# Protect application and system files from being viewed
RewriteRule ^(application|conf|docs|library|layouts|log|tests) - [F,L]
# Add the following header to every response
Header add X-HeaderName "HRAPP - Dev"
Header set Access-Control-Allow-Origin "*"

#Change Charset and Language headers
AddDefaultCharset UTF-8
DefaultLanguage en-US

# explicitly disable caching for scripts and other dynamic files
<FilesMatch ".(pl|cgi|spl|scgi|fcgi)$">
Header unset Cache-Control
</FilesMatch>

#Disallow Script Execution
Options -ExecCGI
AddHandler cgi-script .pl .py .jsp .asp .sh .cgi

# deny all .htaccess, .DS_Store $hî†é and ._* (resource fork) files
<Files ~ "^\.([Hh][Tt]|[Dd][Ss]_[Ss]|[_])">
 Order allow,deny
 Deny from all
 Satisfy All
</Files>

# deny access to all .log and comment files
<Files ~ "^.*\.([Ll][Oo][Gg]|[cC][oO][mM][mM][eE][nN][tT])">
 Order allow,deny
 Deny from all
 Satisfy All
</Files>

# multiple file types
<FilesMatch ".(htaccess|htpasswd|ini|phps|fla|psd|log|sh)$">
 Order Allow,Deny
 Deny from all
</FilesMatch>

# block visitors referred from indicated domains
<IfModule mod_rewrite.c>
SetEnvIfNoCase ^User-Agent$ .*(craftbot|download|extract|stripper|sucker|ninja|clshttp|webspider|leacher|collector|grabber|webpictures) HTTP_SAFE_BADBOT
SetEnvIfNoCase ^User-Agent$ .*(libwww-perl|aesop_com_spiderman) HTTP_SAFE_BADBOT
Deny from env=HTTP_SAFE_BADBOT
</ifModule>
# ultimate hotlink protection
<IfModule mod_rewrite.c>
 RewriteEngine on
 RewriteCond %{HTTP_REFERER}     !^$
 RewriteCond %{REQUEST_FILENAME} -f
 RewriteCond %{REQUEST_FILENAME} \.(gif|jpe?g?|png)$           [NC]
 RewriteCond %{HTTP_REFERER}     !^http?://([^.]+\.)?hrapp.co.in\. [NC]
 RewriteRule \.(gif|jpe?g?|png)$                             - [F,NC,L]
</ifModule>
 #RewriteRule .*\.(jpg|jpeg|gif|png|bmp|exe|swf|ico)$ - [F,NC]

#add expires header for 3 months
#<IfModule mod_headers.c>
#    <FilesMatch "\.(js|css|jpg|png|jpeg|gif|ico|swf|txt|xml|woff|ttf)$">
#	   RequestHeader unset Cookie
#	   Header unset Set-Cookie
#	   Header set Cache-Control "max-age=7257600"
#  </FilesMatch>
#</IfModule>

#AddType image/x-icon .ico

#Gzip
#<ifmodule mod_deflate.c>
#AddOutputFilterByType DEFLATE text/text text/html text/plain text/xml text/css application/x- javascript application/javascript
#</ifmodule>
#End Gzip
