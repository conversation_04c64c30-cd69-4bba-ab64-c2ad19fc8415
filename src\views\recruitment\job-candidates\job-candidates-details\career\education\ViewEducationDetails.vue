<template>
  <div
    v-if="educationDetails && educationDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-subtitle-1 text-grey pl-7"
  >
    No education details have been added
  </div>
  <div class="ml-7 pb-4"></div>
  <v-card
    elevation="3"
    v-for="(data, index) in educationDetails"
    :key="index"
    class="card-item d-flex rounded-lg mr-4 mb-1 pa-4"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:450px; border-left: 7px solid ${generateRandomColor()}; height:200px;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <v-row class="text-body-1">
      <v-col cols="12" class="pa-0 pl-4 d-flex">
        <v-tooltip :text="data.Education_Type" location="bottom">
          <template v-slot:activator="{ props }">
            <div
              class="text-primary font-weight-bold text-subtitle-1 text-truncate"
              style="max-width: 350px"
              v-bind="data.Education_Type ? props : ''"
            >
              {{ checkNullValue(data.Education_Type) }}
              <span v-if="data.Year_Of_Passing">
                - {{ data.Year_Of_Passing }}</span
              >
            </div>
          </template>
        </v-tooltip>
      </v-col>
      <v-col
        v-if="labelList[174]?.Field_Visiblity?.toLowerCase() == 'yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="mr-2 d-flex flex-column justify-start">
          <b class="mb-1 text-grey justify-start"
            >{{ labelList[174].Field_Alias || "Specialization" }}
          </b>
          <span class="py-2"> {{ checkNullValue(data.Specialisation) }}</span>
        </div>
      </v-col>
      <v-col
        v-if="labelList[175].Field_Visiblity == 'Yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="d-flex flex-column justify-start">
          <b class="text-grey justify-start"
            >{{ labelList[175].Field_Alias }}
          </b>
          <span class="py-2"> {{ checkNullValue(data.Institute_Name) }}</span>
        </div>
      </v-col>
      <v-col
        v-if="labelList[177]?.Field_Visiblity?.toLowerCase() == 'yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="d-flex flex-column justify-start">
          <b class="text-grey justify-start"
            >{{ labelList[177].Field_Alias || "Percentage" }}
          </b>
          <span class="py-2"> {{ checkNullValue(data.Percentage) }}</span>
        </div>
      </v-col>
    </v-row>
  </v-card>
</template>

<script>
import { generateRandomColor, checkNullValue } from "@/helper";

export default {
  name: "ViewEducationDetails",
  props: {
    educationDetails: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
  },
};
</script>
