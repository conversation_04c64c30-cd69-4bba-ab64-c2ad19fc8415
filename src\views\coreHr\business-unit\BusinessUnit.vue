<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <BusinessUnitFilter
                v-if="!showAddEditForm && (itemList.length || isFilterApplied)"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilterFromFilterComponent($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="business-unit-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList('Business unit error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="Business Unit / Cost Center configuration refers to the process of setting up and customizing distinct organizational units within a larger business entity to effectively manage and control various aspects of the company's operations. These units are often designed to align with specific functional areas, like employee job details within the organization."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="The configuration of business units or cost centers is a crucial aspect of organizational structure and plays a pivotal role in how an enterprise manages its resources, data, and decision-making processes."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Within the evolving landscape of organizational management, the HRMS now offers an enhanced feature allowing for the seamless integration of business units or cost centers. By diving into this capability, administrators can carve out specific operational segments, thereby optimizing budgeting, resource allocation, and financial reporting. Whether you're defining standalone units or intricate hierarchies with parent-child relationships, the system ensures a streamlined experience."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Associated departments can be linked effortlessly, promoting a cohesive view of the organization's structure. As your business pivots and grows, the flexibility of our HRMS ensures that your business units and cost centers evolve right alongside it. Embrace this feature for an organized, clear, and efficient approach to managing your organization's diverse segments."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddOrganizationGroup()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span>Configure Business Unit / Cost Center</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <div
                v-if="originalList.length > 0 && !isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  v-if="formAccess.add"
                  prepend-icon="fas fa-plus"
                  variant="elevated"
                  rounded="lg"
                  class="mx-1 primary"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="onAddOrganizationGroup"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  <span>Add Configuration</span>
                </v-btn>
                <v-btn
                  rounded="lg"
                  color="transparent"
                  variant="flat"
                  class="mt-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-tooltip :text="action.message">
                              <template v-slot:activator="{ props }">
                                <div v-bind="action.message ? props : ''">
                                  {{ action.key }}
                                </div>
                              </template>
                            </v-tooltip>
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Business Unit / Cost Center
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.businessUnitId ===
                                  item.businessUnitId
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.businessUnit"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.businessUnit.length > 50 ? props : ''
                                  "
                                >
                                  {{ item.businessUnit }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Business Unit / Cost Center Code
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.businessUnitId ===
                                  item.businessUnitId
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.businessUnit"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.businessUnit.length > 50 ? props : ''
                                  "
                                >
                                  {{ item.businessUnitCode }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Parent Unit
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                !isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.businessUnitId ===
                                  item.businessUnitId
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.businessUnit"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.businessUnit.length > 50 ? props : ''
                                  "
                                >
                                  {{
                                    checkNullValue(item.businessUnitParentName)
                                  }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Description
                          </div>
                          <v-tooltip
                            :text="item.description"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.description.length > 100 ? props : ''
                                "
                                :style="
                                  !isMobileView
                                    ? 'max-width: 500px; '
                                    : 'max-width: 200px; '
                                "
                              >
                                {{ checkNullValue(item.description) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Status
                          </div>

                          <div
                            @click.stop="
                              {
                              }
                            "
                          >
                            <AppToggleButton
                              button-active-text="Active"
                              button-inactive-text="InActive"
                              button-active-color="#7de272"
                              button-inactive-color="red"
                              id-value="gab-analysis-based-on"
                              :current-value="
                                item.status === 'Active' ? true : false
                              "
                              :isDisableToggle="!formAccess.update"
                              :tooltipContent="
                                formAccess.update
                                  ? ''
                                  : `Sorry, you don't have access rights to update the status`
                              "
                              @chosen-value="updateStatus($event, item)"
                            ></AppToggleButton>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewBusinessUnit
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>

                <v-col
                  :cols="originalList.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264"
                >
                  <AddEditBusinessUnit
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :businessList="itemList"
                    @close-form="closeAllForms()"
                    @edit-updated="
                      refetchList(
                        'Business Unit / Cost Center updated successfully'
                      )
                    "
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditBusinessUnit
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :businessList="itemList"
        @close-form="closeAllForms()"
        @edit-updated="
          refetchList('Business Unit / Cost Center updated successfully')
        "
      />
      <ViewBusinessUnit
        v-if="showViewForm"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewBusinessUnit = defineAsyncComponent(() =>
  import("./ViewBusinessUnit.vue")
);
const AddEditBusinessUnit = defineAsyncComponent(() =>
  import("./AddEditBusinessUnit.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import { checkNullValue } from "@/helper.js";
// Queries
import {
  LIST_BUSINESS_UNIT_SETTINGS,
  ADD_UPDATE_BUSINESS_UNIT_SETTINGS,
} from "@/graphql/settings/core-hr/businessUnitQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import BusinessUnitFilter from "./BusinessUnitFilter.vue";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "BusinessUnit",
  components: {
    EmployeeDefaultFilterMenu,
    AddEditBusinessUnit,
    ViewBusinessUnit,
    NotesCard,
    BusinessUnitFilter,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    // add/update
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
    isFilterApplied: false,
    openMoreMenu: false,
  }),
  computed: {
    landedFormName() {
      return "Business Unit / Cost Center";
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    orgStructureFormAccess() {
      return this.$store.getters.orgStructureFormAccess;
    },
    formAccess() {
      let accessFormName = this.landedFormName.replace(/\s/g, "-");
      accessFormName = accessFormName.toLowerCase();
      let formAccessRights = this.accessRights(accessFormName);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    organizationGroupFormName() {
      let projectForm = this.accessIdRights("269");
      if (
        projectForm &&
        projectForm.customFormName &&
        projectForm.customFormName !== ""
      ) {
        return projectForm.customFormName;
      } else return "Organization Group";
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.orgStructureFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (formAccessArray && formAccessArray.includes("Organization Group")) {
          const index = formAccessArray.indexOf("Organization Group");
          formAccessArray[index] = this.organizationGroupFormName;
        }
        return formAccessArray;
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Business Unit / Cost Center",
            align: "start",
            key: "businessUnit",
          },
          {
            title: "Status",
            key: "status",
          },
        ];
      } else {
        return [
          {
            title: "Business Unit / Cost Center",
            align: "start",

            key: "businessUnit",
          },
          {
            title: "Business Unit / Cost Center Code",
            key: "businessUnitCode",
          },
          {
            title: "Parent Unit",
            key: "businessUnitParentName",
          },

          {
            title: "Description",
            key: "description",
          },
          {
            title: "Status",
            key: "status",
          },
        ];
      }
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText =
          "There are no Business Unit / Cost Center for the selected filters/searches.";
      }
      return msgText;
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    moreActions() {
      return [{ key: "Export" }];
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    onTabChange(tab) {
      mixpanel.track("Business unit form tab changed");
      if (tab !== this.landedFormName) {
        if (tab === this.organizationGroupFormName) {
          tab = "Organization Group";
        }
        this.isLoading = true;
        const { formAccess } = this.orgStructureFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },

    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
      let filterObj = {
        businessUnitCode: "",
        businessUnit: "",
        parentCode: "",
        status: null,
      };
      this.applyFilter(filterObj);
    },
    applyFilterFromFilterComponent(filter) {
      this.applyFilter(filter);
    },

    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter.businessUnitCode && filter.businessUnitCode != "") {
        filteredList = filteredList.filter((item) => {
          return filter.businessUnitCode == item.businessUnitCode;
        });
      }
      if (filter.businessUnit && filter.businessUnit != "") {
        filteredList = filteredList.filter((item) => {
          return filter.businessUnit == item.businessUnit;
        });
      }
      if (filter.status) {
        filteredList = filteredList.filter((item) => {
          return filter.status == item.status;
        });
      }
      if (filter.parentCode) {
        filteredList = filteredList.filter((item) => {
          return filter.parentCode == item.businessUnitParentId;
        });
      }
      this.isFilterApplied = true;
      this.filteredList = filteredList;
      this.itemList = filteredList;
    },

    // resetFilter() {
    //   this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    //   this.itemList = this.originalList;
    // },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType.key === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: "Business Unit - Cost Center Code",
          key: "businessUnitCode",
        },
        {
          header: "Parent Unit",
          key: "businessUnitParentName",
        },
        {
          header: "Business Unit - Cost Center",
          key: "businessUnit",
        },
        {
          header: "Description",
          key: "description",
        },
        {
          header: "Status",
          key: "status",
        },
      ];
      if (this.entomoIntegrationEnabled && this.isEntomoSyncTypePush) {
        exportHeaders.unshift({
          header: "Level",
          key: "level",
        });
      }
      let businessUnit = this.itemList;
      let exportOptions = {
        fileExportData: businessUnit,
        fileName: "Business Unit-Cost Center",
        sheetName: "Business Unit-Cost Center",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },

    openEditForm() {
      mixpanel.track("Business unit edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item) {
      mixpanel.track("Business unit view form opened");
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    onAddOrganizationGroup() {
      mixpanel.track("Business unit add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      mixpanel.track("Business unit all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_BUSINESS_UNIT_SETTINGS,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBusinessUnit &&
            !response.data.listBusinessUnit.errorCode
          ) {
            vm.itemList = response.data.listBusinessUnit.settings;
            vm.originalList = response.data.listBusinessUnit.settings;
            vm.listLoading = false;
            vm.onApplySearch();
            mixpanel.track("Business unit list retrieved");
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      mixpanel.track("Business unit error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "business unit / cost center configuration",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },

    updateStatus(statusVal, item) {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_BUSINESS_UNIT_SETTINGS,
            variables: {
              level: item.level ? parseInt(item.level) : null,
              businessUnitId: item.businessUnitId
                ? parseInt(item.businessUnitId)
                : 0,
              businessUnit: item.businessUnit ? item.businessUnit : "",
              description: item.description ? item.description : "",
              status: statusVal[1] ? "Active" : "InActive",
              isAdd: 0,
              oldStatus: item.status,
              businessUnitCode: item.businessUnitCode,
              businessUnitParentId: item.businessUnitParentId,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message:
                "Business Unit / Cost Center status updated successfully",
            };
            vm.showAlert(snackbarData);
            vm.refetchList("status-updated");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },

    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "business unit / cost center status",
        isListError: false,
      });
      this.refetchList("status-update-failed");
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.business-unit-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .business-unit-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
