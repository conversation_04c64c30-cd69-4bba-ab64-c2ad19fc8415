<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      :show-bottom-sheet="!listLoading"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>

    <v-container fluid>
      <div v-if="formAccess && (isAdmin || isRecruiter)">
        <div v-if="listLoading" class="mt-15">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          key="error-screen"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          button-text="Retry"
          @button-click="refetchList()"
        ></AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="enableLockScreen && !isEnableForm"
          key="no-data-screen"
        >
          <template #contentSlot>
            <div :style="{ maxWidth: windowWidth < 600 ? '80%' : '70%' }">
              <v-row class="rounded-lg pa-5 mb-4" style="background: white">
                <v-col cols="12">
                  <v-row>
                    <v-col
                      cols="3"
                      xs="12"
                      sm="12"
                      md="3"
                      v-if="windowWidth > 600"
                      class="d-flex justify-center"
                      ><img
                        :src="getImageUrl"
                        style="width: 180px; height: auto"
                        class="ml-n5 mr-5"
                        alt="idea-bulb"
                    /></v-col>
                    <v-col cols="12" xs="12" sm="12" md="9" xl="9">
                      <v-row
                        class="align-center"
                        :class="
                          windowWidth > 960 ? 'justify-start' : 'justify-center'
                        "
                      >
                        <v-col cols="10" class="px-0">
                          <div
                            class="text-h4 font-weight-bold text-primary my-5"
                            :class="
                              windowWidth > 960 ? 'text-left' : 'text-center'
                            "
                          >
                            Hiring Forecast is Locked
                          </div>
                        </v-col>
                        <div class="text-subtitle-1 text-center">
                          The release of the hiring forecast allows the Hiring
                          Manager to forecast staffing needs.
                        </div>
                        <v-col
                          cols="12"
                          class="d-flex align-center my-4"
                          :class="
                            windowWidth > 960
                              ? 'justify-start'
                              : 'justify-center'
                          "
                        >
                          <v-tooltip
                            :text="
                              !formAccess ||
                              !formAccess.update ||
                              !formAccess.add
                                ? `You don't have access to perform this action`
                                : ''
                            "
                            location="bottom"
                          >
                            <template v-slot:activator="{ props }">
                              <v-btn
                                :color="
                                  formAccess &&
                                  formAccess.update &&
                                  formAccess.add
                                    ? 'primary'
                                    : 'grey-lighten-1'
                                "
                                class=""
                                v-bind="
                                  !formAccess ||
                                  !formAccess.update ||
                                  !formAccess.add
                                    ? props
                                    : ''
                                "
                                rounded
                                @click="
                                  formAccess &&
                                  formAccess.update &&
                                  formAccess.add
                                    ? onReleaseLock()
                                    : {}
                                "
                              >
                                <v-icon color="white" class="pr-3"
                                  >fas fa-redo-alt</v-icon
                                >
                                Release Hiring Forecast
                              </v-btn>
                            </template>
                          </v-tooltip>
                        </v-col>
                      </v-row></v-col
                    >
                  </v-row>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div class="mt-12" v-else>
          <AddEditHiringForecast
            :formAccess="formAccess"
            :hireForecastData="hireForecastData"
            :rolesList="rolesList"
            :editFormAccess="isEnableForm"
            :rolesLoader="rolesLoader"
            :totalNumberHiringForecast="totalNumberHiringForecast"
            @on-close-form="isEnableForm = false"
            @refresh-list="retrieveForecastSettings()"
          />
        </div>
      </div>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      @close-warning-modal="openWarningModal = false"
      @accept-modal="onDelete()"
    ></AppWarningModal>
  </div>
</template>
<script>
import {
  RETRIEVE_HIRING_FORECAST_SETTINGS,
  RETRIEVE_LIST_ROLES,
} from "@/graphql/mpp/manPowerPlanningQueries";
import moment from "moment";
import { defineAsyncComponent } from "vue";

const AddEditHiringForecast = defineAsyncComponent(() =>
  import("./AddEditHiringForecast.vue")
);
export default {
  name: "HiringForecast",
  data() {
    return {
      errorContent: "",
      listLoading: false,
      openWarningModal: false,
      isEnableForm: false,
      rolesLoader: false,
      isErrorInList: false,
      hireForecastData: {},
      totalNumberHiringForecast: 0,
      rolesList: [],
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isRecruiter() {
      return this.$store.state.isRecruiter;
    },
    formAccess() {
      let individualFormAccess = this.accessRights("285");
      if (
        individualFormAccess &&
        individualFormAccess.accessRights &&
        individualFormAccess.accessRights["view"]
      ) {
        return individualFormAccess.accessRights;
      } else {
        return false;
      }
    },
    enableLockScreen() {
      const currentDate = moment().format("YYYY-MM-DD");
      if (this.hireForecastData && Object.keys(this.hireForecastData).length) {
        const endDate = moment(
          new Date(this.hireForecastData.Release_Date)
        ).format("YYYY-MM-DD");
        if (currentDate > endDate) return true;
        else return false;
      } else {
        return true;
      }
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/mpp/lock.webp");
      else return require("@/assets/images/mpp/lock.png");
    },
    generalFormAccess() {
      let individualFormAccess = this.accessRights("349");
      if (
        individualFormAccess &&
        individualFormAccess.accessRights &&
        individualFormAccess.accessRights["view"]
      ) {
        return individualFormAccess.accessRights;
      } else {
        return false;
      }
    },
    mainTabList() {
      return this.generalFormAccess && this.generalFormAccess.view
        ? ["Hiring Forecast", "General"]
        : ["Hiring Forecast"];
    },
    currentTabItem() {
      let index = this.mainTabList.indexOf("Hiring Forecast");
      return "tab-" + index;
    },
  },
  components: {
    AddEditHiringForecast,
  },
  mounted() {
    if (this.formAccess && (this.isRecruiter || this.isAdmin)) {
      this.getRolesList();
      this.retrieveForecastSettings();
    }
  },
  methods: {
    onReleaseLock() {
      this.isEnableForm = true;
    },
    onTabChange(tabName) {
      if (tabName.toLowerCase() === "general") {
        this.isLoading = true;
        this.$router.push("/man-power-planning/settings/general");
      }
    },
    getRolesList() {
      let vm = this;
      vm.rolesLoader = false;
      vm.$apollo
        .query({
          query: RETRIEVE_LIST_ROLES,
          client: "apolloClientAC",
          variables: {
            formId: 285,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listRoles &&
            response.data.listRoles.listRoles &&
            !response.data.listRoles.errorCode
          ) {
            vm.rolesList = response.data.listRoles.listRoles;
            vm.rolesLoader = false;
          } else {
            vm.rolesLoader = false;
            vm.handleRetrieveError();
          }
        })
        .catch((err) => {
          vm.rolesLoader = false;
          vm.handleRetrieveError(err);
        });
    },
    retrieveForecastSettings() {
      this.listLoading = true;
      this.$apollo
        .query({
          query: RETRIEVE_HIRING_FORECAST_SETTINGS,
          variables: {
            formId: 285,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.listLoading = false;

          if (res && res.data && res.data.retrieveForeCastSettings) {
            const hiringSettings = res.data.retrieveForeCastSettings || {};
            this.hireForecastData = hiringSettings.settings || {};
            this.totalNumberHiringForecast =
              hiringSettings.totalHiringForeCastRecords || 0;
          } else {
            this.hireForecastData = {};
            this.totalNumberHiringForecast = 0;
          }
        })
        .catch((err) => {
          this.listLoading = false;
          this.hireForecastData = {};
          this.totalNumberHiringForecast = 0;
          this.handleRetrieveError(err);
        });
    },
    handleRetrieveError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "Man Power Planning",
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    refetchList() {
      this.retrieveForecastSettings();
      this.errorContent = "";
      this.isErrorInList = false;
    },
  },
};
</script>
