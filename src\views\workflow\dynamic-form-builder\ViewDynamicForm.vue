<template>
  <v-card v-if="!isLoading" class="rounded-lg mt-2">
    <div
      class="d-flex align-center py-2"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center pl-4 py-2">
        <v-avatar class="mr-2" size="28" color="hover" variant="elevated">
          <v-icon class="primary" size="18">fas fa-file-alt</v-icon>
        </v-avatar>
        <div class="text-subtitle-1 font-weight-bold">
          {{ selectedItem.templateName + " - " + selectedItem.formFor + " - " }}
          <span
            :class="
              selectedItem.status &&
              selectedItem.status.toLowerCase() === 'active'
                ? 'text-green'
                : 'text-red'
            "
            >{{ selectedItem.status }}</span
          >
        </div>
      </div>
      <div class="d-flex align-center">
        <v-btn
          v-if="accessRights.update"
          class="mr-2"
          @click="$emit('open-edit-form')"
          size="small"
          color="primary"
          variant="elevated"
          rounded="lg"
          >Edit</v-btn
        >
        <v-icon class="mx-1" color="primary" @click="$emit('close-form')">
          fas fa-times
        </v-icon>
      </div>
    </div>
    <div style="height: calc(100vh - 260px); overflow: scroll">
      <form
        id="fb-render"
        ref="fbRender"
        class="renderTemplate form-horizontal form-validation pa-10 w-100"
        cf-context
      ></form>
    </div>
    <div>
      <v-row class="px-sm-8 px-md-10 mt-2 mb-2">
        <v-col v-if="moreDetailsList.length > 0" cols="12">
          <MoreDetails
            :more-details-list="moreDetailsList"
            :open-close-card="openMoreDetails"
            @on-open-close="openMoreDetails = $event"
          ></MoreDetails> </v-col
      ></v-row>
    </div>
  </v-card>
</template>
<script>
import { defineComponent } from "vue";
import "formBuilder/dist/form-render.min.js";
import "formBuilder";
import "jquery-validation/dist/jquery.validate.min.js";
import moment from "moment";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";

export default defineComponent({
  name: "ViewDynamicForm",
  data: () => ({
    isLoading: false,
    moreDetailsList: [],
    openMoreDetails: true,
  }),
  emits: ["close-form", "open-edit-form"],
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  computed: {
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
  },
  mounted() {
    //More Details
    this.moreDetailsList = [];
    // to form more details array based on this values
    const addedOn = this.formatDate(this.selectedItem.addedOn),
      addedByName = this.selectedItem.addedByUserName,
      updatedByName = this.selectedItem.updatedByUserName,
      updatedOn = this.formatDate(this.selectedItem.updatedOn);
    if (addedOn && addedByName) {
      this.moreDetailsList.push({
        actionDate: addedOn,
        actionBy: addedByName,
        text: "Added",
      });
    }
    if (updatedByName && updatedOn) {
      this.moreDetailsList.push({
        actionDate: updatedOn,
        actionBy: updatedByName,
        text: "Updated",
      });
    }

    let formData = this.selectedItem.formTemplate;
    $("#fb-render").formRender({ formData });
    $("#fb-render :input").prop("disabled", true);
  },
});
</script>
<style scoped>
@import url("../../../assets/css/dynamic-form-builder.css");
.error {
  color: red;
}
h1,
h2,
p {
  margin: 15px 0px;
}
.formbuilder-radio-group-label {
  margin: 15px 0px;
}
.radio-group,
.checkbox-group {
  margin: 10px 0px;
}
.formbuilder-checkbox {
  margin: 5px 0px;
}
.rendered-form .form-group {
  margin-bottom: 25px !important;
}
</style>
