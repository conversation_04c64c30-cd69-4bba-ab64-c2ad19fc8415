import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const RETRIEVE_USER_DATA_LIST = gql`
  query getUserWiseActivityData(
    $startDate: String!
    $endDate: String!
    $workScheduleId: Int
    $customGroupId: Int
  ) {
    getUserWiseActivityData(
      startDate: $startDate
      endDate: $endDate
      workScheduleId: $workScheduleId
      customGroupId: $customGroupId
    ) {
      errorCode
      message
      userWiseActivityData {
        Employee_Id
        sumOfSystemUpTime
        sumOfActiveTime
        sumOfComputerActivityTime
        sumOfProductiveAppsAndUrls
        Work_Location
        countRemote
        countOffice
        countHybrid
        countOffline
        percentageRemoteRecords
        percentageOfficeRecords
        percentageOfficeOrRemoteRecords
        percentageOfflineRecords
        employeeName
        averageSystemUpTime
        averageActiveTime
        averageComputerActivityTime
        averageProductiveAppsAndUrls
        activeDays
      }
    }
  }
`;

export const RETRIEVE_LOCATION_DATA_LIST = gql`
  query getLocationWiseActivtyData(
    $startDate: String!
    $endDate: String!
    $workScheduleId: Int
    $customGroupId: Int
  ) {
    getLocationWiseActivtyData(
      startDate: $startDate
      endDate: $endDate
      workScheduleId: $workScheduleId
      customGroupId: $customGroupId
    ) {
      errorCode
      message
      locationWiseActivityDetails {
        averageOfficeSystemUpTime
        averageRemoteSystemUpTime
        averageOfficeRemoteSystemUpTime
        averageBlacklistedOfflineSystemUpTime
        averageOfficeActiveTime
        averageRemoteActiveTime
        averageOfficeEndTime
        averageRemoteEndTime
        averageOfficeRemoteEndTime
        averageOfficeStartTime
        averageRemoteStartTime
        averageOfficeRemoteStartTime
        averageOfficeRemoteActiveTime
        averageBlacklistedOfflineActiveTime
        averageOfficeComputerActivityTime
        averageRemoteComputerActivityTime
        averageOfficeRemoteComputerActivityTime
        averageBlacklistedOfflineComputerActivityTime
        averageOfficeProductiveAppsAndUrls
        averageRemoteProductiveAppsAndUrls
        averageOfficeRemoteProductiveAppsAndUrls
        averageBlacklistedOfflineProductiveAppsAndUrls
        totalRecords
        percentageRemoteRecords
        percentageOfficeRecords
        percentageOfficeRemoteRecords
        percentageOffilineRecords
      }
    }
  }
`;

export const RETRIEVE_WEEK_WISE_LIST = gql`
  query getWeekWiseWorkLocation(
    $startDate: String!
    $endDate: String!
    $workScheduleId: Int
    $customGroupId: Int
  ) {
    getWeekWiseWorkLocation(
      startDate: $startDate
      endDate: $endDate
      workScheduleId: $workScheduleId
      customGroupId: $customGroupId
    ) {
      errorCode
      message
      weekWiseWorkLocationDetails
    }
  }
`;
