<template>
  <v-row class="set-layout">
    <v-col cols="12" class="d-flex flex-column justify-center align-center">
      <div
        v-if="topMessage"
        class="main-content-title mb-5"
        :class="'text-' + topMsgColor"
      >
        {{ topMessage }}
      </div>
      <div class="d-flex justify-center mb-5 mt-6">
        <img
          v-if="getImgUrl"
          :style="
            isSmallImage
              ? 'width: 50%;height: auto'
              : 'width: 100%;height: auto'
          "
          :src="getImgUrl"
          alt="No Records"
        />
        <v-icon v-if="mainIcon" size="100" color="grey">{{ mainIcon }}</v-icon>
      </div>
      <div
        v-if="mainTitle"
        class="main-content-title mb-5"
        :class="'text-' + mainTitleColor"
      >
        {{ mainTitle }}
      </div>
      <span
        class="mb-5 sub-content"
        :class="mainTitle ? '' : 'font-weight-bold'"
        >{{ content }}</span
      >
      <slot name="contentSlot"></slot>
      <div v-if="buttonText" class="mb-4">
        <v-btn
          v-if="!disableButton"
          variant="elevated"
          rounded="lg"
          color="primary"
          :size="isSmallImage ? 'small' : 'default'"
          @click="disableButton ? {} : $emit('button-click')"
        >
          <v-icon v-if="iconName" class="add-icon mr-1">
            {{ iconName }}
          </v-icon>
          {{ buttonText }}
        </v-btn>
        <v-tooltip v-else :text="tooltipData" location="bottom" max-width="400">
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              rounded="lg"
              variant="elevated"
              color="grey-lighten-3"
              class="tab-action-btn-disabled"
              :size="isSmallImage ? 'small' : 'default'"
            >
              <v-icon v-if="iconName" class="add-icon mr-1">
                {{ iconName }}
              </v-icon>
              {{ buttonText }}
            </v-btn>
          </template>
        </v-tooltip>
        <slot name="nextToButton"></slot>
      </div>
    </v-col>
  </v-row>
</template>

<script>
export default {
  name: "AppFetchErrorScreen",
  props: {
    buttonText: {
      type: String,
      default: "",
    },
    mainTitle: {
      type: String,
      default: "",
    },
    mainTitleColor: {
      type: String,
      default: "primary",
    },
    topMessage: {
      type: String,
      default: "",
    },
    topMsgColor: {
      type: String,
      default: "primary",
    },
    content: {
      type: String,
      default: "",
    },
    imageName: {
      type: String,
      default: "",
    },
    iconName: {
      type: String,
      default: "refresh",
    },
    disableButton: {
      type: Boolean,
      default: false,
    },
    tooltipData: {
      type: String,
      default: "",
    },
    isSmallImage: {
      type: Boolean,
      default: false,
    },
    mainIcon: {
      type: String,
      default: "",
    },
  },
  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    //render image based on webp support
    getImgUrl() {
      if (this.imageName) {
        if (this.isBrowserSupportWebp)
          return require("@/assets/images/" + this.imageName + ".webp");
        else return require("@/assets/images/" + this.imageName + ".png");
      } else return "";
    },
  },
};
</script>

<style lang="scss" scoped>
.add-icon {
  font-weight: bold !important;
  font-size: 1.2em !important;
  color: white !important;
}
.set-layout {
  margin-top: 7vh !important;
  width: 100%;
}
.main-content-title {
  max-width: 70%;
  text-align: center;
  font-weight: medium;
  font-size: 1.7em;
}
.sub-content {
  font-size: 1.5em;
  width: 85%;
  text-align: center;
  color: black;
}

.tab-action-btn-disabled {
  cursor: not-allowed !important;
  color: var(--v-grey-lighten1) !important;
}

@media screen and (max-width: 768px) {
  .main-content-title {
    max-width: 100% !important;
  }
  .sub-content {
    font-size: 1em;
    width: 100%;
    text-align: center;
  }
}
</style>
