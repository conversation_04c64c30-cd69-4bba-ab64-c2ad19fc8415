<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="primary" size="20">fas fa-file-alt</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              {{ accessFormName }}
            </div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-tooltip
            :text="
              editedData && editedData.Remaining_Days === 0
                ? 'You cannot override once the compensatory off balance has been utilized.'
                : ''
            "
            location="bottom"
          >
            <template v-slot:activator="{ props }">
              <v-card
                variant="flat"
                v-bind="
                  editedData && editedData.Remaining_Days === 0 ? props : {}
                "
              >
                <v-btn
                  v-if="formAccess.update && this.formName == 'MyTeam'"
                  @click="$emit('open-edit-form')"
                  size="small"
                  color="primary"
                  :disabled="editedData && editedData.Remaining_Days === 0"
                  variant="elevated"
                  rounded="lg"
                  >Edit</v-btn
                ></v-card
              ></template
            >
          </v-tooltip>
          <v-icon class="mx-1" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card>
        <div style="height: calc(100vh - 260px); overflow: scroll">
          <v-card-text>
            <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Employee Id</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.User_Defined_EmpId) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Employee Name</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.Employee_Name) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Worked Dates</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    editedData.Worked_Date
                      ? convertUTCToLocal(editedData.Worked_Date).substring(
                          0,
                          10
                        )
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Expiry Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    editedData.Expiry_Date
                      ? convertUTCToLocal(editedData.Expiry_Date).substring(
                          0,
                          10
                        )
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Total Days</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.Total_Days) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Remaining Days</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedData.Remaining_Days }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Comp Off Eligible Hours
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <v-icon
                        class="ml-1"
                        v-bind="props"
                        size="small"
                        color="info"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <div style="width: 350px !important">
                      If the worked date is a week off Or holiday then the comp
                      off eligible hours will be calculated from the attendance
                      punch in the date-time Or if it is a weekday then the comp
                      off eligible hours will be calculated from the next minute
                      of the regular to date-time till the attendance punch out
                      date-time.
                    </div>
                  </v-tooltip>
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.Total_Hours) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Balance added from
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.Source) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Compensatory Off Balance For Attendance
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedData.Comp_Off_Attendance_Balance }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Compensatory Off Balance For Additional Wage Claim
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedData.Comp_Off_Additional_Wage_Claim_Balance }}
                </p>
              </v-col>
              <v-col cols="12" :class="isMobileView ? ' ml-4' : ''">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Comp Off Balance Rules
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : 'mt-n3'"
              >
                <div
                  class="text-subtitle-1 font-weight-regular"
                  v-for="(value, key) in groupedRulesData[0]"
                  :key="key"
                >
                  <span>{{ checkNullValue(key) }}</span> :
                  <span>{{ checkNullValue(value) }}</span>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : 'mt-n3'"
              >
                <div
                  class="text-subtitle-1 font-weight-regular"
                  v-for="(value, key) in groupedRulesData[1]"
                  :key="key"
                >
                  <span>{{ checkNullValue(key) }}</span> :
                  <span>{{ checkNullValue(value) }}</span>
                </div>
              </v-col>
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-card-text>
        </div>
      </v-card>
    </v-card>
  </div>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "ViewProjectActivities",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
    accessFormName: {
      type: String,
      required: true,
    },
    formName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    editedData: {},
    rulesData: {},
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    groupedRulesData() {
      const keys = this.rulesData ? Object.keys(this.rulesData) : {};
      // Group keys based on their position (first two and last two)
      const groupedData = [
        keys.slice(0, 2).reduce((obj, key) => {
          obj[key] = this.rulesData[key];
          return obj;
        }, {}),
        keys.slice(2).reduce((obj, key) => {
          obj[key] = this.rulesData[key];
          return obj;
        }, {}),
      ];
      return groupedData;
    },
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.editedData = Object.assign({}, newData);
        this.rulesData = JSON.parse(this.editedData.Comp_Off_Balance_Rules);
        this.prefillMoreDetails();
      },
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const updatedByName = this.selectedItem.Updated_By;
      const updatedOn = this.convertUTCToLocal(this.selectedItem.Updated_On);
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>
