<template>
  <div class="mx-auto" style="max-width: 1200px">
    <!-- <v-btn-toggle
      v-model="filterSelected"
      class="d-flex mx-auto mb-10"
      style="width: 15em"
      color="success"
      rounded="lg"
      elevation="10"
      mandatory
      @update:model-value="filterData(val)"
    >
      <v-btn>Valid</v-btn>
      <v-btn>All</v-btn>
      <v-btn>Invalid</v-btn>
    </v-btn-toggle> -->

    <vue-excel-editor
      ref="excelEditor"
      style="display: flex; justify-content: center"
      v-model="editorData"
      filter-row
      :disable-panel-setting="true"
      no-header-edit
      autoFillWidth
      no-mouse-scroll
      :page="20"
    >
      <vue-excel-column
        v-for="(field, index) in fields"
        :key="index"
        :field="field.field"
        :label="field.label"
        :type="field.type"
        :width="field.width"
        :options="field.options"
        :readonly="field.readonly"
        :validate="validateExcelData"
      />
    </vue-excel-editor>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import ExcelValidatorMixins from "@/mixins/ExcelValidatorMixins";
export default {
  name: "ExcelEditor",
  mixins: [ExcelValidatorMixins],
  props: {
    fields: {
      type: Array,
      required: true,
    },
    jsonData: {
      type: Array,
      required: true,
    },
    typeOfImport: {
      type: String,
      required: true,
    },
    extendValidation: {
      type: [Array, Object],
      required: false,
    },
  },
  data() {
    return {
      editorData: [],
      invalidData: [],
      backupData: [],
      filterSelected: 1,
      isLoading: true,
    };
  },
  mounted() {
    // this.editorData = JSON.parse(JSON.stringify(this.jsonData));
    this.getDropDownBoxDetails().then(() => {
      for (var i in this.jsonData) {
        this.$refs.excelEditor.newRecord(this.jsonData[i]);
      }
      this.isLoading = false;
    });
    this.getTotalDesignatons();
  },
  computed: {
    filteredData() {
      const originalData = JSON.parse(JSON.stringify(this.jsonData));
      return originalData.filter((obj) => {
        const objId = obj["$id"];
        return !this.invalidData.includes(objId);
      });
    },
  },
  methods: {
    setFieldError(reason, row, field) {
      this.$refs.excelEditor.setFieldError(reason, row, field);
    },
    validateExcelData(content, oldContent, record, field) {
      let validate = {
        type: field.name ? field.name : "",
        changed: content,
        extendValidation: this.extendValidation,
      };
      let isValid = this.validateData(
        validate,
        this.typeOfImport,
        this.editorData,
        record
      );
      this.extendedValidation(
        validate,
        this.typeOfImport,
        this.editorData,
        record
      );
      this.getInvalidData();
      return isValid;
    },
    async getInvalidData() {
      if (this.$refs.excelEditor && this.$refs.excelEditor.errmsg) {
        let errorObj = await this.$refs.excelEditor.errmsg;
        this.invalidData = Object.keys(errorObj).map((key) => {
          let value = key.split("-")[1];
          return value;
        });
      }
    },
    filterData() {
      const originalData = JSON.parse(JSON.stringify(this.jsonData));

      if (this.filterSelected === 0) {
        if (this.invalidData.length) {
          this.editorData = originalData.filter((obj) => {
            const objId = obj["$id"];
            return !this.invalidData.includes(objId);
          });
        } else {
          this.editorData = originalData;
        }
      } else if (this.filterSelected === 2) {
        if (this.invalidData.length) {
          this.editorData = originalData.filter((obj) => {
            const objId = obj["$id"];
            return this.invalidData.includes(objId);
          });
        } else {
          this.editorData = [];
        }
      } else {
        this.editorData = originalData;
      }
    },
    async getTotalDesignatons() {
      this.isLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          offset: 1,
          limit: 1,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            let { totalRecords } = res.data.getDesignationDetails;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.apiCallCount = 0;
              this.totalApiCount = Math.ceil(totalRecords / 25000);
              for (let i = 0; i < this.totalApiCount; i++) {
                this.getDesignationList(i * 25000);
              }
            }
          }
          this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
        });
    },
  },
};
</script>
