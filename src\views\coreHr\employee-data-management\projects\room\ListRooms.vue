<template>
  <div>
    <div v-if="itemList.length > 0">
      <div
        v-if="!isSmallTable"
        class="d-flex align-center my-3"
        :class="isMobileView ? 'justify-center ' : 'justify-end'"
      >
        <v-btn
          prepend-icon="fas fa-plus"
          color="primary rounded-lg"
          @click="$emit('open-add-form')"
          v-if="formAccess.add"
        >
          <template v-slot:prepend>
            <v-icon></v-icon>
          </template>
          Add
        </v-btn>
        <v-btn
          rounded="lg"
          color="transparent"
          variant="flat"
          class="ml-3"
          @click="$emit('refetch-data')"
        >
          <v-icon>fas fa-redo-alt</v-icon>
        </v-btn>
        <v-menu v-model="openMoreMenu" transition="scale-transition">
          <template v-slot:activator="{ props }">
            <v-btn
              variant="plain"
              class="mt-1 ml-n2 mr-n5"
              :size="isMobileView ? 'small' : 'default'"
              v-bind="props"
            >
              <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
              <v-icon v-else>fas fa-caret-up</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="action in moreActions"
              :key="action.key"
              @click="onMoreAction(action.key)"
            >
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <v-list-item-title
                    v-bind="props"
                    class="pa-3"
                    :class="{
                      'pink-lighten-5': isHovering,
                    }"
                    ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                    >{{ action.key }}</v-list-item-title
                  >
                </template>
              </v-hover>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
      <v-row>
        <v-col :cols="12">
          <v-data-table
            :headers="tableHeaders"
            :items="itemList"
            fixed-header
            :items-per-page="50"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
            ]"
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(320, itemList)
            "
            style="box-shadow: none !important"
            class="elevation-1"
          >
            <template v-slot:item="{ item }">
              <tr
                class="data-table-tr bg-white cursor-pointer"
                :class="
                  isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''
                "
                @click="onSelectRoom(item)"
              >
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Room
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <div class="d-flex align-center">
                      <div
                        v-if="
                          isSmallTable &&
                          windowWidth > 1264 &&
                          selectedRoom &&
                          selectedRoom.Room_Id === item.Room_Id
                        "
                        class="data-table-side-border"
                      ></div>
                    </div>

                    <v-tooltip
                      :text="item.Room_No"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="
                            item.Room_No && item.Room_No.length > 40
                              ? props
                              : ''
                          "
                        >
                          {{ checkNullValue(item.Room_No) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Description
                  </div>
                  <section>
                    <v-tooltip
                      :text="item.Description"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="
                            item.Description && item.Description.length > 50
                              ? props
                              : ''
                          "
                        >
                          {{ checkNullValue(item.Description) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  v-if="!isSmallTable"
                  class="text-body-2"
                  style="width: 150px"
                >
                  <div class="d-flex align-center">
                    <ActionMenu
                      :actions="['Edit', 'Delete']"
                      iconColor="grey"
                      @selected-action="onActions($event, item)"
                      :access-rights="formAccess"
                    ></ActionMenu>
                  </div>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </div>
    <AppFetchErrorScreen
      v-else-if="itemList.length == 0 && !isSmallTable"
      key="no-results-screen"
      main-title="No matching filter/search results found"
      image-name="common/no-records"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4">
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="this.isMobileView ? 'small' : 'default'"
                @click="$emit('reset-search-filter')"
              >
                Reset Filter/Search
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      :confirmation-heading="warningText"
      :icon-name="warningIconClass"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="deleteRecord()"
    >
    </AppWarningModal>
  </div>
</template>
<script>
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "ListRooms",
  components: {
    ActionMenu,
  },
  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    formAccess: {
      type: Object,
      required: true,
    },
    isSmallTable: {
      type: Boolean,
      default: false,
    },
    backupMainList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  mixins: [FileExportMixin],
  data() {
    return {
      itemList: [],
      warningIconClass: "",
      openWarningModal: false,
      warningText: "Are you sure to delete the room?",
      deleteItem: null,
      selectedRoom: {},
      openMoreMenu: false,
    };
  },
  mounted() {
    if (this.items.length) {
      this.itemList = this.items;
    }
  },
  watch: {
    items(val) {
      this.itemList = val;
      this.onApplySearch();
    },
    searchValue() {
      this.onApplySearch();
    },
    isSmallTable(val) {
      if (val === false) {
        this.selectedRoom = {};
      }
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Room No",
            align: "start",
            key: "Room_No",
          },
          {
            title: "Description",
            key: "Description",
          },
        ];
      } else {
        return [
          {
            title: "Room No",
            align: "start",
            key: "Room_No",
          },
          {
            title: "Description",
            key: "Description",
          },
          { title: "Actions", key: "action" },
        ];
      }
    },
    moreActions() {
      let moreActions = [
        {
          key: "Export all rooms",
          icon: "fas fa-file-export",
        },
      ];
      return moreActions;
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        this.openWarningPopup(item, "fas fa-trash");
      } else {
        this.$emit("open-edit-form", item);
      }
    },
    onSelectRoom(item) {
      this.selectedRoom = item;
      this.$emit("open-view-form", item);
    },
    onApplySearch() {
      let val = this.searchValue;
      if (!val) {
        this.itemList = this.items;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.items;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.deleteItem = null;
    },
    openWarningPopup(item, warningIcon) {
      if (item === null) {
        this.warningIconClass = warningIcon;
        this.openWarningModal = false;
        return;
      }
      this.warningIconClass = warningIcon;
      this.openWarningModal = true;
      this.deleteItem = item;
    },
    deleteRecord() {
      this.openWarningModal = false;
      this.$emit("delete", this.deleteItem);
    },
    onMoreAction(actionType) {
      if (actionType === "Export all rooms") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let item = this.backupMainList;
      let itemList = item.map((item) => ({
        Room_No: item.Room_No,
        Description: item.Description,
        addedByName: item.Added_By_Name,
        addedOn: item.Added_On ? this.convertUTCToLocal(item.Added_On) : "-",
        updatedByName: item.Updated_By_Name,
        updatedOn: item.Updated_On
          ? this.convertUTCToLocal(item.Updated_On)
          : "-",
      }));
      let fileName = "Room";
      let exportHeaders = [
        { header: "Room No", key: "Room_No" },
        {
          header: "Description",
          key: "Description",
        },
        { header: "Added On", key: "addedOn" },
        { header: "Added By", key: "addedByName" },
        { header: "Update On", key: "updatedOn" },
        { header: "Update By", key: "updatedByName" },
      ];
      let exportOptions = {
        fileExportData: itemList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
<style scoped>
.data-table-side-border {
  margin-left: -1.2em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
