<template>
  <div>
    <v-dialog
      v-model="openBlockedAppModal"
      scrollable
      persistent
      :width="showCustomGroupSelectionCard ? '450' : '1000px'"
      class="full-width-model"
      @click:outside="$emit('close-modal')"
      @keydown.esc="$emit('close-modal')"
    >
      <v-card
        class="rounded-lg"
        :color="showCustomGroupSelectionCard ? 'primary' : ''"
        :min-height="showCustomGroupSelectionCard ? '350' : ''"
      >
        <div class="category-list-header">
          <div class="d-flex justify-end">
            <v-icon
              color="white"
              class="font-weight-bold ma-2"
              size="18"
              @click="$emit('close-modal')"
              >far fa-times
            </v-icon>
          </div>
          <div
            class="d-flex justify-center align-center"
            :style="showCustomGroupSelectionCard ? 'margin-top: 20%' : ''"
            :class="showCustomGroupSelectionCard ? '' : 'mt-n4'"
          >
            <v-icon color="warning " size="20">trip_origin</v-icon>
            <span
              class="ml-1 text-white font-weight-bold"
              style="font-size: 0.8em"
              >{{
                $t(
                  "settings." +
                    (categoryType === "App"
                      ? "appsBlockedUnblocked"
                      : "websitesBlockedUnblocked")
                )
              }}
            </span>
            {{ openAppURLCategoryModal }}
          </div>
          <div v-if="showAlertBox" class="ma-4">
            <v-alert v-model="showAlertBox" :type="alertType" closable>
              {{ alertMessage }}
            </v-alert>
          </div>
          <div
            v-if="modalType === 'group' && !showDomainWhiteListing"
            class="d-flex align-center justify-center"
            :class="windowWidth < 800 ? 'flex-wrap' : ''"
          >
            <div
              v-if="!showCustomGroupSelectionCard"
              class="text-white px-2 text-body-1"
            >
              {{ $t("settings.customGroup") }}
            </div>
            <v-autocomplete
              v-model="selectedCustomGroup"
              variant="solo"
              :placeholder="$t('settings.selectCustomGroup')"
              :style="
                showCustomGroupSelectionCard
                  ? 'max-width: 350px'
                  : 'max-width: 200px'
              "
              :items="customGroupList"
              item-title="Custom_Group_Name"
              item-value="Custom_Group_Id"
              class="mt-7"
              @update:model-value="onChangeCustomGroup()"
            ></v-autocomplete>
            <div
              v-if="!showCustomGroupSelectionCard"
              class="d-flex align-center text-white px-2 text-body-1"
              :class="windowWidth < 800 ? 'mt-n8' : ''"
            >
              {{ $t("settings.copyClassificationFrom") }}
              <div
                class="d-flex align-center"
                :class="isMobile ? 'flex-wrap' : ''"
              >
                <v-autocomplete
                  v-model="copiedCustomGroup"
                  variant="solo"
                  :placeholder="$t('settings.selectCustomGroup')"
                  :style="
                    showCustomGroupSelectionCard
                      ? 'max-width: 350px'
                      : 'max-width: 210px'
                  "
                  rounded="lg"
                  :items="copyCustomGroupList"
                  item-title="Custom_Group_Name"
                  item-value="Custom_Group_Id"
                  class="mt-7"
                  :class="isMobile ? 'mb-n4' : 'mx-2'"
                ></v-autocomplete>
                <v-btn
                  :loading="copiedCustomGroup && isCategoryLoading"
                  rounded="lg"
                  :color="copiedCustomGroup ? 'primary' : 'grey lighten-3'"
                  :class="copiedCustomGroup ? '' : 'pointer-block'"
                  @click="copiedCustomGroup ? copyCustomGroup() : {}"
                  >{{ $t("settings.copyNow") }}</v-btn
                >
              </div>
            </div>
          </div>
          <div
            v-if="
              !errorInCategoryList &&
              !showCustomGroupSelectionCard &&
              !showAddEditWhitelisting
            "
            class="d-flex justify-center"
          >
            <v-row class="ma-0 align-stretch">
              <v-col
                cols="12"
                :sm="windowWidth > 680 ? 4 : 12"
                class="pa-2 d-flex align-end justify-center"
              >
                <div
                  id="appUrlCategory"
                  class="d-flex align-end bg-primary-lighten-2 rounded-lg"
                  style="
                    background-color: #542d56;
                    color: white;
                    height: 100%;
                    width: 100%;
                  "
                >
                  <v-text-field
                    id="appUrlSearch"
                    v-model="searchInputData"
                    hide-details
                    variant="solo"
                    density="compact"
                    :placeholder="$t('settings.search')"
                    prepend-inner-icon="fas fa-search"
                  ></v-text-field>
                </div>
              </v-col>
              <v-col
                v-if="!showDomainWhiteListing"
                cols="12"
                :sm="windowWidth > 680 ? 8 : 12"
                class="pa-2"
              >
                <div
                  class="d-flex justify-center flex-wrap py-1 rounded-lg"
                  style="background-color: #542d56; color: white"
                >
                  <v-icon class="mt-2" color="white" size="16"
                    >fa fa-filter</v-icon
                  >
                  <v-btn
                    v-for="filter in filterVariables"
                    :key="filter"
                    rounded="lg"
                    size="small"
                    :variant="filteredCategory === filter ? 'flat' : 'outlined'"
                    class="ma-1 px-2"
                    color="
                    white
                  "
                    @click="selectedCategoryFilter(filter)"
                    >{{ filter }}</v-btn
                  >
                </div>
              </v-col>
            </v-row>
          </div>
        </div>
        <v-card-text
          v-if="!showCustomGroupSelectionCard"
          :class="{ 'pa-0': isMobile }"
        >
          <v-container
            v-if="!showDomainWhiteListing"
            :class="{ 'pa-0': isMobile }"
          >
            <div v-if="isCategoryLoading">
              <v-skeleton-loader
                class="mt-2 mb-2"
                type="table-thead"
              ></v-skeleton-loader>
              <v-skeleton-loader
                v-for="i in 4"
                :key="i"
                class="mx-auto pt-4 mt-4"
                type="list-item-avatar"
              ></v-skeleton-loader>
            </div>
            <div v-else-if="!errorInCategoryList">
              <div
                v-if="categoryType === 'Websites'"
                style="text-decoration: underline; cursor: pointer"
                class="text-blue pl-sm-6"
                @click="openDomainWhiteListing()"
              >
                <v-icon size="15" color="blace">fas fa-pencil-alt</v-icon>
                {{ $t("settings.wildcardConfiguration") }}
              </div>
              <div
                class="d-flex align-center"
                style="justify-content: space-between"
              >
                <AccessControlCard
                  v-if="!isCategoryLoading"
                  class="pl-sm-6"
                  :productive-count="
                    categoryCount.Blocked ? categoryCount.Blocked : 0
                  "
                  :unproductive-count="
                    categoryCount.Unblocked ? categoryCount.Unblocked : 0
                  "
                  :uncategorized-count="
                    categoryCount.Uncategorized
                      ? categoryCount.Uncategorized
                      : 0
                  "
                  :category-type="categoryType"
                  :can-update="canUpdate"
                  :is-loading="isCategoryLoading"
                  :show-action="false"
                  :is-data-tracked="1"
                >
                </AccessControlCard>
                <div class="d-flex justify-end my-2">
                  <v-btn
                    v-if="filteredCategoryList.length > 0"
                    rounded="lg"
                    color="primary"
                    :disabled="!isDisableSaveButton"
                    @click="exportAppURLCategory()"
                    >{{ $t("settings.export") }}</v-btn
                  >
                </div>
              </div>
              <div class="data-table-container">
                <v-data-table
                  id="app-url-category-list"
                  :style="
                    isMobile
                      ? 'background: #f1f1f1 !important'
                      : 'background: white !important'
                  "
                  :headers="
                    categoryType === 'App'
                      ? appCategoryHeaders
                      : urlCategoryHeaders
                  "
                  :items="filteredCategoryList"
                  hide-default-footer
                  fixed-header
                  height="500"
                  :search="searchInputData"
                  :items-per-page="itemsPerPage"
                  v-model:page="page"
                  :item-key="appURLUniqueId"
                  @page-count="pageCount = $event"
                >
                  <!-- Headers slot -->

                  <template
                    v-if="categoryType === 'App'"
                    #[`header.applicationName`]="{}"
                  >
                    <div
                      class="app-url-category-list-headers font-weight-bold text-body-2"
                      color="grey-darken-3"
                    >
                      {{ $t("settings.applicationName") }}
                    </div>
                  </template>

                  <template v-else #[`header.domainName`]="{}">
                    <div
                      class="app-url-category-list-headers font-weight-bold text-body-2"
                      color="grey-darken-3"
                    >
                      {{ $t("settings.websites") }}
                    </div>
                  </template>

                  <template #[`header.category`]="{}">
                    <div
                      class="app-url-category-list-headers font-weight-bold text-body-2"
                      color="grey-darken-3"
                    >
                      {{ $t("settings.blockAccess") }}
                    </div>
                  </template>

                  <template #item="{ item }">
                    <tr
                      class="data-table-tr popup-list-card"
                      style="height: 4em"
                      :class="{ 'v-data-table__mobile-table-row': isMobile }"
                    >
                      <td
                        :class="{
                          'v-data-table__mobile-row': isMobile,
                        }"
                      >
                        <div
                          v-if="isMobile"
                          class="v-data-table__mobile-row__header"
                        >
                          {{
                            $t(
                              "settings." +
                                (categoryType === "App"
                                  ? "applicationName"
                                  : "websites")
                            )
                          }}
                        </div>
                        <div
                          :class="{
                            'v-data-table__mobile-row__cell': isMobile,
                          }"
                        >
                          <section
                            v-if="categoryType === 'App'"
                            class="text-primary text-body-1 font-weight-medium"
                            style="width: 250px"
                          >
                            {{ checkNullValue(item.applicationName) }}
                          </section>
                          <section
                            v-else
                            class="text-primary text-body-1 font-weight-medium"
                            style="width: 250px"
                          >
                            {{ checkNullValue(item.domainName) }}
                          </section>
                        </div>
                      </td>
                      <td
                        :class="
                          windowWidth <= 600 && windowWidth > 500
                            ? 'v-data-table__mobile-row d-flex align-center'
                            : windowWidth <= 500
                            ? 'd-flex align-center flex-column pb-10'
                            : ''
                        "
                        :style="isMobile ? 'height:100%' : ''"
                      >
                        <div
                          v-if="isMobile"
                          class="v-data-table__mobile-row__header my-2"
                        >
                          {{ $t("settings.category") }}
                        </div>
                        <div
                          :class="
                            isMobile
                              ? 'v-data-table__mobile-row__cell d-flex justify-center'
                              : 'd-flex justify-end'
                          "
                        >
                          <AppToggleButton
                            :button-active-text="
                              item.category === 'Blocked'
                                ? $t('settings.blocked')
                                : $t('settings.block')
                            "
                            :button-inactive-text="
                              item.category === 'Unblocked'
                                ? $t('settings.unblocked')
                                : $t('settings.unblock')
                            "
                            button-active-color="#7de272"
                            button-inactive-color="red"
                            :current-value="
                              item.category === 'Blocked' ? true : false
                            "
                            @chosen-value="onCategoryUpdate($event, item)"
                            :class="isMobileView ? 'mt-5' : ''"
                            :tooltipContent="
                              !item.addedForWCConf
                                ? ''
                                : $t('settings.domainStatusCannotBeChanged')
                            "
                            :isDisableToggle="item.addedForWCConf"
                          ></AppToggleButton>
                        </div>
                      </td>
                    </tr>
                  </template>
                  <template #no-results>
                    <AppFetchErrorScreen
                      key="no-results-screen"
                      :button-text="$t('settings.clearAll')"
                      icon-name="fas fa-sync"
                      :main-title="noSearchTitle"
                      :content="searchNotFoundContent"
                      image-name="common/no-records"
                      @button-click="clearAllFilter()"
                    >
                    </AppFetchErrorScreen>
                  </template>
                </v-data-table>
              </div>
            </div>
            <v-row v-else>
              <v-col cols="12">
                <AppFetchErrorScreen
                  key="initial-error-screen"
                  :button-text="allowCategoryRetry ? 'Retry' : ''"
                  :content="errorMessage"
                  image-name="common/initial-fetch-error-image"
                  @button-click="refreshCategoryList(selectedDlpSetting)"
                >
                </AppFetchErrorScreen>
              </v-col>
            </v-row>
          </v-container>
          <v-row v-else>
            <v-col cols="12" style="min-height: 300px">
              <div style="display: block; margin-bottom: 2%">
                <div
                  v-if="!isDomainWhiteListingFetching"
                  style="
                    text-decoration: underline;
                    cursor: pointer;
                    display: block;
                    position: fixed;
                    width: 96%;
                    background-color: white;
                    z-index: 999;
                  "
                  class="text-primary pl-sm-6 pb-4 pt-4"
                  @click="onBack()"
                >
                  <v-icon size="15" color="primary">fas fa-arrow-left</v-icon>
                  {{ $t("settings.back") }}
                </div>
              </div>
              <div v-if="isDomainWhiteListingFetching">
                <v-skeleton-loader
                  class="mt-2 mb-2"
                  type="table-thead"
                ></v-skeleton-loader>
                <v-skeleton-loader
                  v-for="i in 4"
                  :key="i"
                  class="mx-auto pt-4 mt-4"
                  type="list-item-avatar"
                ></v-skeleton-loader>
              </div>
              <div v-else-if="showAddEditWhitelisting">
                <div class="d-flex justify-space-evenly" style="margin-top: 7%">
                  <v-form ref="domainNameForm">
                    <v-text-field
                      v-model="whitelistDomainName"
                      prefix="*."
                      variant="outlined"
                      :rules="[
                        required('Domain name', whitelistDomainName),
                        domainNameValidation(whitelistDomainName),
                      ]"
                      style="min-width: 400px"
                      :placeholder="$t('settings.domainName')"
                    ></v-text-field>
                  </v-form>
                  <NotesCard
                    v-if="whitelistDomainName"
                    class="mb-4 text-subtitle-1 font-weight-bold"
                    :notes="
                      matchedUrls && matchedUrls.length
                        ? $t('settings.followingWebsitesBlocked')
                        : $t('settings.noSuchDomain')
                    "
                  ></NotesCard>
                </div>
                <div
                  v-if="matchedUrls.length > 0"
                  style="
                    max-height: 300px;
                    overflow: hidden;
                    overflow-y: scroll;
                  "
                >
                  <v-card
                    v-for="(domainUrl, index) of matchedUrls"
                    :key="domainUrl.domainName + index"
                    class="pa-3 ma-2 text-body-1 text-primary font-weight-medium"
                    elevation="1"
                  >
                    {{ domainUrl.domainName }}
                  </v-card>
                </div>
              </div>
              <AppFetchErrorScreen
                v-else-if="errorInDomainWhitelist"
                :button-text="$t('settings.retry')"
                image-name="common/initial-fetch-error-image"
                :content="errorMessageInDomainWhitelist"
                @button-click="retryDomainWhitelistAPI()"
              >
              </AppFetchErrorScreen>
              <div v-else-if="domainWhitelist.length === 0">
                <AppFetchErrorScreen
                  v-if="domainWhitelistBackup.length > 0"
                  key="no-results-screen"
                  :button-text="$t('settings.clearAll')"
                  icon-name="fas fa-sync"
                  :main-title="noSearchTitle"
                  :content="searchNotFoundContent"
                  image-name="common/no-records"
                  @button-click="clearAllFilter()"
                >
                </AppFetchErrorScreen>
                <AppFetchErrorScreen
                  v-else
                  :button-text="$t('settings.add')"
                  icon-name="fas fa-plus"
                  :content="$t('settings.thereAreNoBlockedWebsites')"
                  :is-small-image="true"
                  image-name="common/no-records"
                  @button-click="onOpenAddEditDomainWhitelisting()"
                >
                </AppFetchErrorScreen>
              </div>
              <div v-else style="margin-top: 7%">
                <v-card
                  v-for="domain of domainWhitelist"
                  :key="domain.Domain_Whitelist_Id"
                  elevation="1"
                  class="ma-2"
                >
                  <v-row align="center" justify="space-between">
                    <v-col cols="4" class="w-100">
                      <div
                        class="text-body-1 text-primary font-weight-bold pl-4 py-5 w-100"
                      >
                        *.{{ domain.WC_Blocked_Domain }}
                      </div>
                    </v-col>
                    <v-col cols="1">
                      <v-avatar
                        size="25"
                        color="red"
                        style="cursor: pointer"
                        @click="singleDelete(domain.WC_Blocked_Domains_Id)"
                      >
                        <v-icon size="13" color="white"
                          >fas fa-trash-alt</v-icon
                        >
                      </v-avatar>
                    </v-col>
                  </v-row>
                </v-card>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
        <AppWarningModal
          v-if="openDeleteConfirmation"
          :open-modal="openDeleteConfirmation"
          iconName="fas fa-trash"
          @close-warning-modal="closeDeleteConfirmationModal()"
          @accept-modal="deleteDomain()"
        >
        </AppWarningModal>
        <v-card-actions
          v-if="
            !errorInCategoryList &&
            !showCustomGroupSelectionCard &&
            !isDomainWhiteListingFetching
          "
          class="d-flex justify-center my-1"
        >
          <v-btn
            v-if="
              showDomainWhiteListing &&
              !showAddEditWhitelisting &&
              domainWhitelist.length > 0
            "
            color="primary"
            :disabled="errorInDomainWhitelist"
            @click="onOpenAddEditDomainWhitelisting()"
            ><v-icon class="pr-1">fas fa-plus</v-icon
            >{{ $t("settings.addMore") }}</v-btn
          >
          <v-btn
            v-if="showDomainWhiteListing && showAddEditWhitelisting"
            color="primary"
            :loading="isCategoryListUpdating"
            @click="validateDomainName(selectedDlpSetting)"
            >{{ $t("settings.save") }}</v-btn
          >
          <v-btn
            v-else-if="!showDomainWhiteListing"
            id="app_url_category_settings_update"
            rounded="lg"
            color="primary"
            class="mr-3"
            :loading="isCategoryListUpdating"
            :disabled="isDisableSaveButton"
            @click="checkLevel(selectedDlpSetting)"
          >
            {{ $t("settings.save") }}
          </v-btn>
          <span v-if="!isDisableSaveButton && !showDomainWhiteListing">
            {{
              $t("settings.changesHaveBeenDone", {
                count: findModifiedItemInArray().length
                  ? findModifiedItemInArray().length
                  : 0,
              })
            }}
          </span>
          <AppLoading v-if="isLoading"></AppLoading>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          {{ $t("settings.close") }}
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import AccessControlCard from "./AccessControlCard";
import { defineAsyncComponent } from "vue";
import mixpanel from "mixpanel-browser";
const NotesCard = defineAsyncComponent(() =>
  import("../../../../components/helper-components/NotesCard.vue")
);
// import queries
import {
  RETRIEVE_APPS_CATEGORY_LIST,
  DLP_BLOCKED_DOMAIN_DATA,
  GET_BLOCKED_DOMAINS,
  ADD_EMPLOYEE_LEVEL_BLOCKED_DOMAIN,
  GET_EMPLOYEE_LEVEL_BLOCKED_DOMAINS,
  DELETE_ORGANIZATION_LEVEL_BLOCKED_DOMAINS,
  DELETE_EMPLOYEE_LEVEL_BLOCKED_DOMAINS,
} from "@/graphql/settings/data-loss-prevention/internetAccessControl.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";

//helper functions
import {
  filterList,
  compareAndReturnModifiedArray,
  checkNullValue,
} from "@/helper";
export default {
  name: "AppUrlCategoryModel",
  components: {
    AccessControlCard,
    NotesCard,
  },
  mixins: [FileExportMixin, validationRules],
  props: {
    backupOrganizationDetails: {
      type: Object,
      required: true,
    },
    selectedDlpSetting: {
      type: Object,
      required: false,
    },
    // openAppURLCategoryModal: {
    //   type: Boolean,
    //   required: true,
    // },
    // modalType: {
    //   type: String,
    //   required: true,
    // },
    // canUpdate: {
    //   type: Boolean,
    //   required: true,
    // },
    // categoryType: {
    //   type: String,
    //   required: true,
    // },
    // customGroupList: {
    //   type: Array,
    //   default: function () {
    //     return [];
    //   },
    // },
  },
  data() {
    return {
      openModal: false,
      modalType: "organization",
      canUpdate: true,
      categoryType: "Websites",
      customGroupList: [1, 2, 3, 4, 5],
      //filter and search variables
      filterVariables: [
        this.$t("settings.all"),
        this.$t("settings.blocked"),
        this.$t("settings.unblocked"),
      ],
      filteredCategory: this.$t("settings.all"),
      searchInputData: "",
      isCategoryLoading: false,
      errorMessage: "",
      errorInCategoryList: false,
      allowCategoryRetry: false,
      openBlockedAppModal: true,
      deleteDomainId: 0,
      openDeleteConfirmation: false,
      //app and url headers
      appCategoryHeaders: [
        { text: "Application Name", value: "applicationName" },
        {
          text: "Category",
          value: "category",
          align: "end",
          sortable: false,
        },
      ],
      urlCategoryHeaders: [
        { text: "Websites", value: "domainName" },
        {
          text: "Category",
          value: "category",
          align: "end",
          sortable: false,
        },
      ],
      showCustomGroupSelectionCard: false,
      selectedCustomGroup: null,
      selectedCustomGroupName: "",
      copiedCustomGroup: null,
      showAlertBox: false,
      alertMessage: "",
      alertType: "success",
      copyCustomGroupList: [],
      //category types
      category: ["Blocked", "Unblocked"],

      //categoryList
      filteredCategoryList: [], //filtered array
      modifyCategoryList: [], // modified array
      categoryList: [], //complete array
      categoryCount: {},
      isCategoryListUpdating: false,

      searchNotFoundContent:
        "Please try again by changing the filters or clear it to get all the data.",
      noSearchTitle: "No matching search results found.",

      // pagination variables
      pageCount: 1,
      page: 1,
      itemsPerPage: 50,
      pageNumber: 50,

      // domain whitelisting
      showDomainWhiteListing: false,
      isDomainWhiteListingFetching: false,
      domainWhitelist: [],
      domainWhitelistBackup: [],
      errorInDomainWhitelist: false,
      errorMessageInDomainWhitelist: "",
      showAddEditWhitelisting: false,
      whitelistDomainName: "",
      selectedDomainWhitelist: 0,
      matchedUrls: [],
      validationMessages: [],
    };
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    //returns true/false based on mobile window width size
    isMobile() {
      return this.$store.state.isMobileWindowSize;
    },
    // unique id based on category type (app/url)
    appURLUniqueId() {
      return this.categoryType === "App" ? "applicationId" : "domainId";
    },
    //disable save button if data is not modified
    isDisableSaveButton() {
      let modifiedArrayList = this.findModifiedItemInArray();
      return modifiedArrayList.length === 0;
    },
  },
  watch: {
    // when page number is changed
    pageNumber(val) {
      if (val === "All") {
        this.pageCount = 1;
        this.itemsPerPage = this.filteredCategoryList.length;
        this.page = 1;
      } else {
        let pageCount = this.filteredCategoryList.length / this.pageNumber;
        pageCount = pageCount <= 1 ? 1 : pageCount;
        this.pageCount = Math.round(pageCount);
        this.itemsPerPage = this.pageNumber;
        this.page = 1;
      }
    },

    // handling the alert to close within specific time duration automatically
    showAlertBox(val) {
      if (val) {
        setTimeout(() => {
          this.showAlertBox = false;
          this.alertType = "success";
        }, 3000);
      }
    },

    whitelistDomainName(val) {
      if (this.$refs.domainNameForm) {
        this.$refs.domainNameForm.validate().then((validationResponse) => {
          if (val && validationResponse) {
            let searchableValue = val;
            this.matchedUrls = this.filteredCategoryList.filter((el) =>
              el.domainName.endsWith(searchableValue)
            );
            this.matchedUrls = this.matchedUrls.filter(
              (item) => item.category !== "Blocked"
            );
          } else {
            this.matchedUrls = [];
          }
        });
      }
    },

    searchInputData(val) {
      if (this.showDomainWhiteListing) {
        if (!val) {
          this.domainWhitelist = JSON.parse(
            JSON.stringify(this.domainWhitelistBackup)
          );
        } else {
          let searchValue = val.toString().toLowerCase();
          let searchItems = this.domainWhitelistBackup;
          const filteredArray = searchItems.filter((item) => {
            return item.WC_Blocked_Domain.includes(searchValue);
          });
          this.domainWhitelist = filteredArray;
        }
      }
    },
  },
  mounted() {
    mixpanel.init("6df21e89c6a0f6b1bc345ae98b6ef36e", {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.modalType === "group") {
      this.showCustomGroupSelectionCard = false;
    } else {
      this.callAppsURLsCategories(this.selectedDlpSetting);
    }
  },
  methods: {
    checkNullValue,
    getActiveText(category) {
      return category === "Blocked" ? "Block" : "Custom Active Text";
    },
    // call apps/Websites categories based on category type
    callAppsURLsCategories(selectedDlpSetting) {
      if (selectedDlpSetting && selectedDlpSetting.employeeId) {
        this.fetchUrlsListForEmployees(selectedDlpSetting.employeeId);
      } else {
        this.fetchUrlsList();
      }
    },

    // while coping the custom group we have to refetch apps/Websites category based on copied custom group
    copyCustomGroup() {
      this.callAppsURLsCategories(this.selectedDlpSetting);
    },

    // while changing/selecting the custom group
    onChangeCustomGroup() {
      this.copiedCustomGroup = null;
      this.callAppsURLsCategories(this.selectedDlpSetting);
      // find selected custom group index in list
      let selectedGroupIndex = this.customGroupList.findIndex(
        (obj) => obj.Custom_Group_Id === this.selectedCustomGroup
      );
      // assign selected customGroup name from index
      this.selectedCustomGroupName =
        selectedGroupIndex > -1
          ? this.customGroupList[selectedGroupIndex]["Custom_Group_Name"]
          : "";
      let groupList = JSON.parse(JSON.stringify(this.customGroupList));
      let defaultCategory = [
        {
          Custom_Group_Id: "default",
          Custom_Group_Name: "Default",
        },
      ];
      this.copyCustomGroupList = removeItemFromArrayOfObject(
        groupList,
        this.selectedCustomGroup,
        "Custom_Group_Id"
      );
      this.copyCustomGroupList = defaultCategory.concat(
        this.copyCustomGroupList
      );
      this.showCustomGroupSelectionCard = false;
    },

    //filter the category list based on the selected value
    selectedCategoryFilter(filterName) {
      if (this.showDomainWhiteListing) {
        if (filterName === this.$t("settings.all")) {
          this.domainWhitelist = JSON.parse(
            JSON.stringify(this.domainWhitelistBackup)
          );
        } else {
          // Convert translated filter name back to English for filtering
          const filterValue =
            filterName === this.$t("settings.blocked")
              ? "Blocked"
              : "Unblocked";
          this.domainWhitelist = filterList(
            this.domainWhitelistBackup,
            filterValue,
            "Category"
          );
        }
      }
      this.filteredCategory = filterName;
      // Convert translated filter name back to English for filtering
      const filterValue =
        filterName === this.$t("settings.all")
          ? "All"
          : filterName === this.$t("settings.blocked")
          ? "Blocked"
          : "Unblocked";
      this.filteredCategoryList = filterList(
        this.modifyCategoryList,
        filterValue,
        "category"
      );
    },
    //clear filter and empty the search field
    clearAllFilter() {
      this.filteredCategory = this.$t("settings.all");
      this.searchInputData = "";
      this.domainWhitelist = JSON.parse(
        JSON.stringify(this.domainWhitelistBackup)
      );
    },
    //based on the value change(0,1,2) on button group assign the category value and
    //based on category type(App & url) assign unique field name and value
    onCategoryUpdate(value, item) {
      let vm = this;
      let uniqueId = item[vm.appURLUniqueId];
      let fieldName = vm.appURLUniqueId;
      let category, color;

      switch (value[1]) {
        case true:
          category = "Blocked";
          color = "green";
          break;
        case false:
          category = "Unblocked";
          color = "red";
          break;
      }
      let itemIndex1 = vm.filteredCategoryList.findIndex(
        (el) => el[fieldName] === uniqueId
      );
      let itemIndex2 = vm.modifyCategoryList.findIndex(
        (el) => el[fieldName] === uniqueId
      );
      let editedItem1 = {
        ...vm.filteredCategoryList[itemIndex1],
        ...{ category: category, color: color },
      };
      let editedItem2 = {
        ...vm.modifyCategoryList[itemIndex2],
        ...{ category: category, color: color },
      };
      // splice is used to replace the changed value in tableItems for impact on presentation
      vm.modifyCategoryList.splice(itemIndex2, 1, editedItem2);
      vm.filteredCategoryList.splice(itemIndex1, 1, editedItem1);
    },

    //find the modified item from the category list
    findModifiedItemInArray() {
      let originalArray = this.categoryList,
        modifiedArray = this.modifyCategoryList;
      let findColumn = ["category"];

      let modifiedArrayList = compareAndReturnModifiedArray(
        originalArray,
        modifiedArray,
        findColumn
      );
      return modifiedArrayList;
    },
    //format the final array send to the backend
    formatCategoryArray(modifiedArray, categoryType = "") {
      if (modifiedArray.length > 0) {
        let newArrayList = modifiedArray.map((obj) => {
          if (this.categoryType === "App") {
            let { applicationId, category } = obj;
            return {
              applicationId,
              category,
            };
          } else {
            let { domainId, category } = obj;
            category = categoryType ? categoryType : category;
            return {
              domainId,
              category,
            };
          }
        });
        return newArrayList;
      } else {
        return [];
      }
    },
    makeCopyOfCategoryList(list) {
      let appURLList = list.map((v) => ({
        ...v,
        color:
          v.category === "Blocked"
            ? "warning"
            : v.category === "Unblocked"
            ? "red"
            : "green",
      }));
      // don't modify the original array when custom group is copied
      if (!this.copiedCustomGroup) this.categoryList = [...appURLList];
      this.modifyCategoryList = [...appURLList];
      this.filteredCategoryList = [...appURLList];
      // reset copied custom group value and show success alert
      if (this.copiedCustomGroup) {
        this.copiedCustomGroup = null;
        this.alertMessage = "Classifications Copied Successfully!";
        this.alertType = "success";
        this.showAlertBox = true;
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    fetchAppsList() {
      let vm = this;
      vm.isCategoryLoading = true;
      try {
        vm.$apollo
          .query({
            query: RETRIEVE_APPS_CATEGORY_LIST,
            variables: {
              groupId: this.copiedCustomGroup
                ? this.copiedCustomGroup === "default"
                  ? null
                  : this.copiedCustomGroup
                : vm.selectedCustomGroup,
              source:
                this.copiedCustomGroup === "default" ? "default" : vm.modalType,
            },
            client: "apolloClientE",
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            vm.isCategoryLoading = false;
            // check if data is retrieved
            let {
              listAppCategory: { errorCode, applicationDetails },
            } = response.data; //destructuring data
            if (applicationDetails && !errorCode) {
              vm.makeCopyOfCategoryList(applicationDetails);
              let categoryCountResponse = {};
              applicationDetails.forEach(
                (v) =>
                  (categoryCountResponse[v.category] =
                    (categoryCountResponse[v.category] || 0) + 1)
              );
              vm.categoryCount = categoryCountResponse;
            } else {
              vm.handleListErrors("", "apps category list");
            }
          })
          .catch((appsListError) => {
            vm.isCategoryLoading = false;
            vm.handleListErrors(appsListError, "apps category list");
          });
      } catch (appsListError) {
        vm.isCategoryLoading = false;
        vm.handleListErrors(appsListError, "apps category list");
      }
    },
    fetchUrlsList() {
      let vm = this;
      vm.isCategoryLoading = true;

      try {
        vm.$apollo
          .query({
            query: GET_BLOCKED_DOMAINS,
            client: "apolloClientK",
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            vm.isCategoryLoading = false;

            // check if data is retrieved
            let {
              getDomainsToBeBlocked: { errorCode, blockedDomainData },
            } = response.data; //destructuring data
            if (blockedDomainData && !errorCode) {
              vm.makeCopyOfCategoryList(blockedDomainData);
              let categoryCountResponse = {};
              blockedDomainData.forEach(
                (v) =>
                  (categoryCountResponse[v.category] =
                    (categoryCountResponse[v.category] || 0) + 1)
              );
              vm.categoryCount = categoryCountResponse;
            } else {
              vm.handleListErrors("", "Websites category list organization");
            }
          })
          .catch((urlsListError) => {
            vm.isCategoryLoading = false;
            vm.handleListErrors(
              urlsListError,
              "Websites category list organization"
            );
          });
      } catch (urlsListError) {
        vm.isCategoryLoading = false;
        vm.handleListErrors(
          urlsListError,
          "Websites category list organization"
        );
      }
    },
    fetchUrlsListForEmployees(employeeId) {
      let vm = this;
      vm.isCategoryLoading = true;

      try {
        vm.$apollo
          .query({
            query: GET_EMPLOYEE_LEVEL_BLOCKED_DOMAINS,
            variables: {
              employeeId: employeeId,
            },
            client: "apolloClientK",
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            vm.isCategoryLoading = false;

            // check if data is retrieved
            let {
              getEmployeeLevelBlockedDomain: { errorCode, employeeDomainData },
            } = response.data; //destructuring data
            if (employeeDomainData && !errorCode) {
              vm.makeCopyOfCategoryList(employeeDomainData);
              let categoryCountResponse = {};
              employeeDomainData.forEach(
                (v) =>
                  (categoryCountResponse[v.category] =
                    (categoryCountResponse[v.category] || 0) + 1)
              );
              vm.categoryCount = categoryCountResponse;
            } else {
              vm.handleListErrors("", "Websites category list");
            }
          })
          .catch((urlsListError) => {
            vm.isCategoryLoading = false;
            vm.handleListErrors(urlsListError, "Websites category list");
          });
      } catch (urlsListError) {
        vm.isCategoryLoading = false;
        vm.handleListErrors(urlsListError, "Websites category list");
      }
    },
    // export apps/Websites category based on category type
    exportAppURLCategory() {
      let exportData = [];
      for (let category of this.filteredCategoryList) {
        let categoryObj = {
          ...category,
          // form classification based on group/org settings
          classification:
            this.modalType === "group"
              ? this.selectedCustomGroupName
              : "Default",
        };
        exportData.push(categoryObj);
      }
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Blocked & Unblocked Websites",
        sheetName: this.categoryType + "s Category",
        header:
          this.categoryType === "App"
            ? [
                { key: "applicationId", header: "Application ID" },
                { key: "applicationName", header: "Application Name" },
                { key: "category", header: "Category" },
                {
                  key: "classification",
                  header: "Classification (Default/Group)",
                },
              ]
            : [
                { key: "domainId", header: "Domain ID" },
                { key: "domainName", header: "Domain Name" },
                { key: "category", header: "Internet Access" },
                {
                  key: "classification",
                  header: "Classification (Default/Group)",
                },
              ],
      };
      this.exportExcelFile(exportOptions);
    },
    //handle error for apps& Websites settings list,  apps category and Websites category
    handleListErrors(err = "") {
      this.isCategoryListUpdating = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "listing",
          form: "Internet Access Control",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    //function to refresh the apps category list incase of error
    refreshCategoryList(selectedDlpSetting) {
      this.errorInCategoryList = false;
      this.errorMessage = "";
      if (selectedDlpSetting && selectedDlpSetting.employeeId) {
        this.fetchUrlsListForEmployees(selectedDlpSetting.employeeId);
      } else {
        this.fetchUrlsList();
      }
    },

    checkLevel(selectedDlpSetting) {
      if (selectedDlpSetting && selectedDlpSetting.employeeId) {
        this.updateEmployeeLevelBlockedDomainsToggle(
          selectedDlpSetting.employeeId
        );
      } else {
        this.addBlockedDomainsToggle();
      }
    },
    closeDeleteConfirmationModal() {
      this.deleteDomainId = null;
      this.openDeleteConfirmation = false;
      this.isLoading = false;
    },
    singleDelete(deleteId) {
      this.deleteDomainId = parseFloat(deleteId);
      this.openDeleteConfirmation = true;
    },
    deleteDomain() {
      if (this.selectedDlpSetting && this.selectedDlpSetting.employeeId) {
        this.deleteEmployeeDomain(this.selectedDlpSetting.employeeId);
      } else {
        this.deleteOrganizationDomain();
      }
    },
    async deleteOrganizationDomain() {
      let vm = this;
      vm.openDeleteConfirmation = false;
      vm.isLoading = true;
      try {
        await vm.$apollo
          .mutate({
            mutation: DELETE_ORGANIZATION_LEVEL_BLOCKED_DOMAINS,
            variables: {
              wCBlockedDomainsId: vm.deleteDomainId,
            },
            client: "apolloClientR",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: this.$t("settings.domainRemovedSuccess"),
            };
            mixpanel.track(
              "internetAccessControl-toggle-organization-blockedDomains-delete-success"
            );
            vm.$emit("refetch-setting-data");
            vm.fetchUrlsList();
            vm.showAlert(snackbarData);
          })
          .catch((deleteErr) => {
            mixpanel.track(
              "internetAccessControl-toggle-organization-blockedDomains-delete-error"
            );
            vm.handleDeleteError(deleteErr);
          });
      } catch {
        vm.handleDeleteError();
      }
    },
    async deleteEmployeeDomain(employeeId) {
      let vm = this;
      vm.openDeleteConfirmation = false;
      vm.isLoading = true;
      try {
        await vm.$apollo
          .mutate({
            mutation: DELETE_EMPLOYEE_LEVEL_BLOCKED_DOMAINS,
            variables: {
              wCBlockedDomainsId: vm.deleteDomainId,
            },
            client: "apolloClientR",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: this.$t("settings.domainRemovedSuccess"),
            };
            mixpanel.track(
              "internetAccessControl-toggle-employee-blockedDomains-delete-success"
            );
            vm.fetchUrlsListForEmployees(employeeId);
            vm.$emit("refetch-setting-data");
            vm.closeDeleteConfirmationModal();
            vm.showAlert(snackbarData);
          })
          .catch((deleteErr) => {
            mixpanel.track(
              "internetAccessControl-toggle-employee-blockedDomains-delete-error"
            );
            vm.handleDeleteError(deleteErr);
          });
      } catch {
        vm.handleDeleteError();
      }
    },
    //update the Websites category list
    addBlockedDomainsToggle() {
      let vm = this;
      vm.isCategoryListUpdating = true;
      let updateUrlsCategoryList = vm.findModifiedItemInArray();
      updateUrlsCategoryList = updateUrlsCategoryList.map((item) => ({
        domainId: item.domainId,
        status: item.category,
      }));
      try {
        vm.$apollo
          .mutate({
            mutation: DLP_BLOCKED_DOMAIN_DATA,
            variables: {
              dlpBlockedDomainData: updateUrlsCategoryList,
              Added_For_WC_Conf: 0,
            },
            client: "apolloClientR",
          })
          .then(() => {
            mixpanel.track(
              "internetAccessControl-toggle-organization-blockedDomains-update-success"
            );
            vm.isCategoryListUpdating = false;
            let snackbarData = {
              isOpen: true,
              message: this.$t("settings.domainStatusUpdatedSuccess"),
              type: "success",
            };
            this.isLoading = false;
            this.$emit("refetch-setting-data");
            vm.showAlert(snackbarData);
            vm.fetchUrlsList();
          })
          .catch((updateDlpSettingError) => {
            mixpanel.track(
              "internetAccessControl-toggle-organization-blockedDomains-update-error"
            );
            vm.isCategoryListUpdating = false;
            vm.handleUpdateError(updateDlpSettingError);
          });
      } catch {
        vm.handleUpdateError();
      }
    },

    // handle errors for update apps/Websites category
    handleUpdateError(err = "") {
      this.isCategoryListUpdating = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "Internet Access Control",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    handleDeleteError(err = "") {
      this.isCategoryListUpdating = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: "Internet Access Control",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    openDomainWhiteListing() {
      this.showDomainWhiteListing = true;
      this.fetchDomainWhitelistingUrlsList();
    },

    onOpenAddEditDomainWhitelisting(domain = "") {
      if (domain) {
        this.selectedDomainWhitelist = domain;
        let domainName = domain.Whitelisted_Domain.replace("*.", "");
        this.whitelistDomainName = domainName;
        let searchableValue = "." + domainName;
        this.matchedUrls = this.filteredCategoryList.filter((el) =>
          el.domainName.endsWith(searchableValue)
        );
      } else {
        this.whitelistDomainName = "";
        this.matchedUrls = [];
        this.selectedDomainWhitelist = {
          Domain_Whitelist_Id: 0,
          Category: "Blocked",
        };
      }
      this.showAddEditWhitelisting = true;
    },

    retryDomainWhitelistAPI() {
      this.errorInDomainWhitelist = false;
      this.fetchDomainWhitelistingUrlsList();
    },

    fetchDomainWhitelistingUrlsList() {
      let vm = this;
      vm.isDomainWhiteListingFetching = true;
      let retrievedDomainDetails;
      if (this.selectedDlpSetting && this.selectedDlpSetting.employeeId) {
        if (
          this.selectedDlpSetting.dlpBlockedDomainData &&
          this.selectedDlpSetting.dlpBlockedDomainData.length &&
          this.selectedDlpSetting.dlpBlockedDomainData[0].WC_Blocked_Domain
        ) {
          let wCBlockedDomains =
            this.selectedDlpSetting.dlpBlockedDomainData[0].WC_Blocked_Domain.split(
              ","
            );
          let wCBlockedDomainIds =
            this.selectedDlpSetting.dlpBlockedDomainData[0].WC_Blocked_Domains_Id.split(
              ","
            );
          retrievedDomainDetails = wCBlockedDomains.map((item, i) => {
            return {
              WC_Blocked_Domain: item,
              WC_Blocked_Domains_Id: wCBlockedDomainIds[i],
            };
          });
        } else {
          retrievedDomainDetails = [];
        }
      } else {
        retrievedDomainDetails = this.backupOrganizationDetails.blockedDomains;
      }
      vm.domainWhitelist = JSON.parse(JSON.stringify(retrievedDomainDetails));
      vm.domainWhitelistBackup = JSON.parse(
        JSON.stringify(retrievedDomainDetails)
      );
      vm.isDomainWhiteListingFetching = false;
    },

    validateDomainName(selectedDlpSetting) {
      this.$refs.domainNameForm.validate().then((validationResponse) => {
        // response will return true/false based on validation
        if (validationResponse && validationResponse.valid) {
          if (selectedDlpSetting && selectedDlpSetting.employeeId) {
            this.updateEmployeeLevelBlockedDomainsWildcard(
              selectedDlpSetting.employeeId
            );
          } else {
            this.addBlockedDomains();
          }
        }
      });
    },

    onBack() {
      if (this.showAddEditWhitelisting) this.showAddEditWhitelisting = false;
      else this.showDomainWhiteListing = false;
    },

    onChangeDomainCategory(val) {
      this.selectedDomainWhitelist["Category"] = this.category[val];
    },

    addBlockedDomains() {
      let vm = this;
      vm.isCategoryListUpdating = true;
      let dataTobeSent = this.matchedUrls.map((data) => ({
        domainId: data.domainId,
      }));
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: DLP_BLOCKED_DOMAIN_DATA,
            variables: {
              WC_Blocked_Domain: vm.whitelistDomainName,
              dlpBlockedDomainData: dataTobeSent,
              Added_For_WC_Conf: 1,
            },
            client: "apolloClientR",
          })
          .then(() => {
            mixpanel.track(
              "internetAccessControl-wildcard-organization-blockedDomains-add-success"
            );
            vm.isCategoryListUpdating = false;
            vm.changedRecords = [];
            let snackbarData = {
              isOpen: true,
              message: this.$t("settings.domainAddedSuccess"),
              type: "success",
            };
            this.isLoading = false;
            vm.showAlert(snackbarData);
            vm.fetchUrlsList();
            this.onBack();
            this.$emit("refetch-setting-data");
          })
          .catch((updateDlpSettingError) => {
            mixpanel.track(
              "internetAccessControl-wildcard-organization-blockedDomains-add-error"
            );
            vm.isCategoryListUpdating = false;
            this.isLoading = false;
            vm.handleUpdateError(updateDlpSettingError);
          });
      } catch {
        vm.handleAddblockedDomains();
      }
    },
    handleAddblockedDomains(err = "") {
      this.isCategoryListUpdating = false;
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "Internet Access Control",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    updateEmployeeLevelBlockedDomainsWildcard(employeeId) {
      let vm = this;
      vm.isCategoryListUpdating = true;
      let dataTobeSent = this.matchedUrls.map((data) => ({
        domainId: data.domainId,
      }));
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_EMPLOYEE_LEVEL_BLOCKED_DOMAIN,
            variables: {
              WC_Blocked_Domain: vm.whitelistDomainName,
              employeeId: employeeId,
              Added_For_WC_Conf: 1,
              dlpBlockedDomainData: dataTobeSent,
            },
            client: "apolloClientR",
          })
          .then(() => {
            mixpanel.track(
              "internetAccessControl-wildcard-employee-blockedDomains-add-success"
            );
            vm.isCategoryListUpdating = false;
            vm.changedRecords = [];
            let snackbarData = {
              isOpen: true,
              message: this.$t("settings.domainAddedSuccess"),
              type: "success",
            };
            this.isLoading = false;
            vm.showAlert(snackbarData);
            vm.fetchUrlsListForEmployees(employeeId);
            this.onBack();
            this.$emit("refetch-setting-data");
          })
          .catch((updateDlpSettingError) => {
            mixpanel.track(
              "internetAccessControl-wildcard-employee-blockedDomains-add-error"
            );
            vm.isCategoryListUpdating = false;
            vm.handleUpdateError(updateDlpSettingError);
          });
      } catch {
        vm.handleAddblockedDomains();
      }
    },
    updateEmployeeLevelBlockedDomainsToggle(employeeId) {
      let vm = this;
      vm.isCategoryListUpdating = true;
      let updateUrlsCategoryList = vm.findModifiedItemInArray();
      updateUrlsCategoryList = updateUrlsCategoryList.map((item) => ({
        domainId: item.domainId,
        status: item.category,
      }));

      try {
        vm.$apollo
          .mutate({
            mutation: ADD_EMPLOYEE_LEVEL_BLOCKED_DOMAIN,
            variables: {
              employeeId: employeeId,
              dlpBlockedDomainData: updateUrlsCategoryList,
              Added_For_WC_Conf: 0,
            },
            client: "apolloClientR",
          })
          .then(() => {
            mixpanel.track(
              "internetAccessControl-toggle-employee-blockedDomains-update-success"
            );
            vm.isCategoryListUpdating = false;
            let snackbarData = {
              isOpen: true,
              message: this.$t("settings.domainStatusUpdatedSuccess"),
              type: "success",
            };
            this.isLoading = false;
            this.$emit("refetch-setting-data");
            vm.showAlert(snackbarData);
            vm.fetchUrlsListForEmployees(employeeId);
          })
          .catch((updateDlpSettingError) => {
            vm.isCategoryListUpdating = false;
            vm.handleUpdateError(updateDlpSettingError);
          });
      } catch {
        vm.handleUpdateError();
      }
    },
  },
};
</script>
<style scoped>
.category-list-header {
  background-color: rgb(var(--v-theme-primary)) !important;
  font-size: 1.5em;
  text-transform: inherit;
  font-weight: 500;
  display: flex;
  flex-direction: column;
}

::v-deep
  #appUrlCategory
  > .v-text-field--solo
  > .v-input__control
  > .v-input__slot {
  background-color: var(--v-primary-lighten2);
  margin-bottom: 0px !important;
}
::v-deep #appUrlCategory > .v-text-field--solo {
  border-radius: 8px !important;
}
.v-data-table tbody td:nth-child(1) .v-data-table__mobile-row__header {
  display: block;
}
.v-data-table tbody .v-data-table__mobile-table-row td:nth-child(1) {
  justify-content: space-around !important;
}
.v-data-table > .v-data-table__wrapper > table > tbody > tr {
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12) !important;
}
.app-url-category-list-headers {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline;
}
@media screen and (max-width: 420px) {
  ::v-deep .v-dialog {
    margin: 0px;
    max-height: 100% !important;
  }
}
.v-dialog > .v-overlay__content > .v-card > .v-card-text,
.v-dialog > .v-overlay__content > form > .v-card > .v-card-text {
  font-size: inherit;
  letter-spacing: 0.03125em;
  line-height: inherit;
  padding: 0px 24px 10px;
}
.data-table-container {
  position: relative;
  height: 400px;
}
.v-data-table {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
