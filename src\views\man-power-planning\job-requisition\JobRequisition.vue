<template>
  <div v-if="mainTabs.length > 0">
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-if="isFilterEnable" style="width: 100%">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu class="justify-end" :isFilter="false" />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="job-requisition-container">
      <v-window v-model="currentTabItem" v-if="formAccess && formAccess.view">
        <v-window-item :value="currentTabItem">
          <RecruitmentRequestList
            v-if="currentTabItem === 'tab-0'"
            :groupList="originalGroupList"
            @enable-filter="enableFilter($event)"
          />
          <NewPositionList
            v-if="currentTabItem === 'tab-1'"
            :groupList="originalGroupList"
            @enable-filter="enableFilter($event)"
          />
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied
    ></v-container>
  </div>
</template>
<script>
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu.vue";
import RecruitmentRequestList from "./recruitment-request/RecruitmentRequestList.vue";
import NewPositionList from "./new-position/NewPositionList.vue";
import { LIST_OF_POSITION_LIST } from "@/graphql/mpp/manPowerPlanningQueries";

export default {
  name: "JobRequisition",
  data() {
    return {
      mainTabs: ["Approved & Forecasted Positions"],
      currentTabItem: "",
      isFilterEnable: false,
      positionListLoading: false,
      originalGroupList: [],
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("289");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    recruitmentRequestFormAccess() {
      let formAccess = this.accessRights("291");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    newPositionFormAccess() {
      let formAccess = this.accessRights("290");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  components: {
    RecruitmentRequestList,
    EmployeeDefaultFilterMenu,
    NewPositionList,
  },
  mounted() {
    if (this.formAccess && this.formAccess.view) {
      this.mainTabs = [
        "Approved & Forecasted Positions",
        "New Position & Additional Headcount",
        "Approvals",
      ];
      if (this.$route.query && this.$route.query.formId) {
        this.currentTabItem =
          this.$route.query.formId === "291" ? "tab-0" : "tab-1";
        this.retrievePositionList();
      } else {
        this.currentTabItem = "tab-0";
        this.retrievePositionList();
      }
    } else {
      this.currentTabItem = "tab-0";
    }
  },
  methods: {
    onTabChange(value) {
      if (this.mainTabs.indexOf(value) == 2) {
        if (this.currentTabItem === "tab-0") {
          this.$router.push("/approvals/approval-management?form_id=291");
        } else if (this.currentTabItem === "tab-1") {
          this.$router.push("/approvals/approval-management?form_id=290");
        }
      } else {
        this.currentTabItem = "tab-" + this.mainTabs.indexOf(value);
        this.retrievePositionList();
        this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      }
    },
    enableFilter(val) {
      this.isFilterEnable = val;
    },
    retrievePositionList() {
      if (this.formAccess && this.formAccess.view) {
        if (
          this.currentTabItem === "tab-0"
            ? this.recruitmentRequestFormAccess &&
              this.recruitmentRequestFormAccess.view
            : this.newPositionFormAccess && this.newPositionFormAccess.view
        ) {
          this.positionListLoading = true;
          this.$apollo
            .query({
              query: LIST_OF_POSITION_LIST,
              client: "apolloClientA",
              fetchPolicy: "no-cache",
              variables: {
                Form_Id: this.currentTabItem === "tab-0" ? 291 : 290,
                conditions: [
                  {
                    key: "Org_Level",
                    operator: "=",
                    value: "GRP",
                  },
                ],
              },
            })
            .then((res) => {
              if (
                res &&
                res.data &&
                res.data.jobTitleList &&
                res.data.jobTitleList.jobTitleResult
              ) {
                const tempGroupList = res.data.jobTitleList.jobTitleResult;
                tempGroupList.forEach((element) => {
                  element.Originalpos_Id = element.Originalpos_Id?.toString();
                });
                this.originalGroupList = [
                  {
                    Pos_Name: "No Group",
                    Pos_Code: "nogroup",
                    Pos_full_Name: "No Group",
                    Originalpos_Id: "nogroup",
                  },
                ].concat(tempGroupList);
              } else {
                this.originalGroupList = [];
              }
            })
            .catch((err) => {
              this.originalGroupList = [];
              this.handleRetrieveError(err);
            })
            .finally(() => {
              this.positionListLoading = false;
            });
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleRetrieveError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "group list",
        isListError: false,
      });
    },
  },
};
</script>
<style scoped>
.job-requisition-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .job-requisition-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
