<template>
  <div v-if="isMounted">
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>
    <v-container fluid class="pf-container">
      <v-window v-if="formAccess && isSuperAdmin" v-model="currentTabItem">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchData()"
          >
          </AppFetchErrorScreen>
          <div v-else>
            <v-row>
              <v-col v-if="!isEdit && !listLoading" cols="12">
                <ViewPFConfiguration
                  :editFormData="providentFundData"
                  @open-edit="openEditForm()"
                  :accessFormName="accessFormName"
                  :labelList="labelList"
                  :formAccess="formAccess"
                ></ViewPFConfiguration>
              </v-col>
              <v-col v-if="isEdit && !listLoading" cols="12">
                <EditPFConfiguration
                  :editFormData="providentFundData"
                  @refetch-data="refetchData()"
                  @close-form="closeEditForm()"
                  :accessFormName="accessFormName"
                  :labelList="labelList"
                >
                </EditPFConfiguration>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
const EditPFConfiguration = defineAsyncComponent(() =>
  import("./EditPFConfiguration.vue")
);
// components
import ViewPFConfiguration from "./ViewPFConfiguration.vue";

// Queries
import { GET_FORM_FIELDS_BY_FORM_ID } from "@/graphql/commonQueries.js";
import { RETRIEVE_PROVIDENT_FUND_CONFIGURATION } from "@/graphql/tax-and-statutory-compliance/providentFundConfiguration";

export default {
  name: "PFConfigurationMainForm",
  components: {
    ViewPFConfiguration,
    EditPFConfiguration,
  },
  data() {
    return {
      isLoading: false,
      currentTabItem: "tab-0",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      providentFundData: {},
      isEdit: false,
      labelList: [],
      // checkTabVisibility: true,
      isMounted: false,
      mainTabs: [],
      pfRuleslabelList: [],
    };
  },
  computed: {
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formAccess() {
      let pfAccess = this.accessRights("52");
      if (pfAccess && pfAccess.accessRights && pfAccess.accessRights["view"]) {
        return pfAccess.accessRights;
      } else return false;
    },
    accessFormName() {
      let pfAccess = this.accessRights("52");
      if (pfAccess && pfAccess.customFormName) {
        return pfAccess.customFormName;
      } else return "Provident Fund Configuration";
    },
    pfRuleFormAccess() {
      let pfAccess = this.accessRights("259");
      if (pfAccess && pfAccess.accessRights && pfAccess.accessRights["view"]) {
        return pfAccess.accessRights;
      } else return false;
    },
    pfRuleFormName() {
      let pfAccess = this.accessRights("259");
      if (pfAccess && pfAccess.customFormName) {
        return pfAccess.customFormName;
      } else return "Provident Fund Rules";
    },
    pfPaymentFormAccess() {
      let pfAccess = this.accessRights("71");
      if (pfAccess && pfAccess.accessRights && pfAccess.accessRights["view"]) {
        return pfAccess.accessRights;
      } else return false;
    },
    pfPaymentFormName() {
      let pfAccess = this.accessRights("71");
      if (pfAccess && pfAccess.customFormName) {
        return pfAccess.customFormName;
      } else return "PF Payment Tracker";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    checkTabVisibility() {
      return this.pfRuleslabelList.every(
        (field) => field.Field_Visiblity === "No"
      );
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("PF Error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },
  mounted() {
    if (this.formAccess && this.isSuperAdmin) {
      this.getLabelList(259);
      this.getLabelList(52);
      this.fetchProvidentFundDetails();
    }

    this.isMounted = true;
  },
  methods: {
    openEditForm() {
      this.isEdit = true;
    },
    closeEditForm() {
      this.isEdit = false;
    },
    onTabChange(tab) {
      if (tab !== this.accessFormName) {
        if (tab == this.pfPaymentFormName) {
          window.location.href = this.baseUrl + "payroll/provident-fund";
        } else if (tab == this.pfRuleFormName) {
          this.$router.push("/tax-and-statutory-compliance/provident-fund");
        }
      }
    },
    refetchData() {
      this.closeEditForm();
      this.fetchProvidentFundDetails();
    },
    fetchProvidentFundDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_PROVIDENT_FUND_CONFIGURATION,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveProvidentFundConfiguration
          ) {
            let providentFundData = JSON.parse(
              response.data.retrieveProvidentFundConfiguration.pfConfigData
            );
            vm.providentFundData = providentFundData[0];
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    async getLabelList(formId) {
      let vm = this;
      vm.isLoading = true;
      await vm.$apollo
        .query({
          query: GET_FORM_FIELDS_BY_FORM_ID,
          variables: {
            form_Id: formId,
          },
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getFormFieldsByFormId) {
            if (formId == 259) {
              vm.pfRuleslabelList =
                response.data.getFormFieldsByFormId.formFields;
              this.mainTabsList();
            } else {
              vm.labelList = response.data.getFormFieldsByFormId.formFields;
            }
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    mainTabsList() {
      let mainTabs = [];
      if (
        this.pfPaymentFormAccess &&
        this.pfRuleFormAccess &&
        this.formAccess
      ) {
        mainTabs = [this.accessFormName, this.pfPaymentFormName];
        if (!this.checkTabVisibility) {
          mainTabs = [
            this.accessFormName,
            this.pfPaymentFormName,
            this.pfRuleFormName,
          ];
        }
        this.mainTabs = mainTabs;
      } else if (this.pfPaymentFormAccess && this.pfRuleFormAccess) {
        mainTabs = [this.pfPaymentFormName];
        if (this.checkTabVisibility) {
          mainTabs = [this.pfPaymentFormName, this.pfRuleFormName];
        }
        this.mainTabs = mainTabs;
      } else if (this.formAccess && this.pfPaymentFormAccess) {
        this.mainTabs = [this.accessFormName, this.pfPaymentFormName];
      } else if (this.formAccess && this.pfRuleFormAccess) {
        mainTabs = [this.accessFormName];
        if (!this.checkTabVisibility) {
          mainTabs = [this.accessFormName, this.pfRuleFormName];
        }
        this.mainTabs = mainTabs;
      } else if (this.pfPaymentFormAccess) {
        this.mainTabs = [this.pfPaymentFormName];
      } else if (this.pfRuleFormAccess) {
        if (!this.checkTabVisibility) {
          mainTabs = [this.pfRuleFormName];
        }
        this.mainTabs = mainTabs;
      } else if (this.formAccess) {
        this.mainTabs = [this.accessFormName];
      } else {
        this.mainTabs = mainTabs;
      }
      this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.accessFormName);
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.pf-container {
  padding: 5em 3em 0em 3em;
}
@media screen and (max-width: 805px) {
  .pf-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
