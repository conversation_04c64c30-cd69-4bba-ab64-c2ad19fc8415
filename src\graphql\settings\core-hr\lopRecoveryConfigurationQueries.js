import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_LOP_RECOVERY = gql`
  query retrieveLopRecoverySettingsDetails {
    retrieveLopRecoverySettingsDetails {
      errorCode
      message
      lopRecoverySettingsDetails {
        Lop_Settings_Id
        Auto_LOP_Applicable
        Attendance_Shortage_Applicable
        Late_Attendance_Applicable
        Coverage
        Custom_Group_Id
        Group_Name
        Workflow_Id
        Workflow_Name
        Configuration_Status
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_LOP_RECOVERY = gql`
  mutation addUpdateLopRecoverySettings(
    $Lop_Settings_Id: Int!
    $CustomGroup_Id: Int
    $Attendance_Shortage_Applicable: String!
    $Auto_LOP_Applicable: String!
    $Workflow_Id: Int!
    $Late_Attendance_Applicable: String!
    $Configuration_Status: String!
  ) {
    addUpdateLopRecoverySettings(
      Lop_Settings_Id: $Lop_Settings_Id
      CustomGroup_Id: $CustomGroup_Id
      Attendance_Shortage_Applicable: $Attendance_Shortage_Applicable
      Auto_LOP_Applicable: $Auto_LOP_Applicable
      Workflow_Id: $Workflow_Id
      Late_Attendance_Applicable: $Late_Attendance_Applicable
      Configuration_Status: $Configuration_Status
    ) {
      errorCode
      message
    }
  }
`;
