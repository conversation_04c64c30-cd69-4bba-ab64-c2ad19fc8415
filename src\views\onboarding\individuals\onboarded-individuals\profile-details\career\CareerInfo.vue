<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs()"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else>
    <div v-if="!hideOtherDetails" class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs()" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <div id="skillDiv" class="mt-4">
      <SkillDetails
        :skillDetails="skillDetails"
        :selectedCandidateId="selectedCandidateId"
        :formAccess="formAccess"
        @edit-opened="hideOtherDetails = true"
        @edit-closed="hideOtherDetails = false"
        @refetch-career-details="handleUpdateSuccess()"
      ></SkillDetails>
    </div>
    <div v-if="!hideOtherDetails">
      <div id="awardDiv" class="mt-4 pb-8">
        <div class="d-flex">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="light-blue"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
              >Award Details</span
            >
          </div>
          <span v-if="enableAdd" class="d-flex justify-end ml-auto">
            <v-btn
              color="primary"
              variant="text"
              @click="showAddEditAwardForm = true"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
            >
          </span>
        </div>
        <v-dialog
          v-model="showAddEditAwardForm"
          max-width="70%"
          @click:outside="closeEditForm"
        >
          <AddEditAwardDetails
            :selectedAwardDetails="selectedAwardDetails"
            :selectedCandidateId="selectedCandidateId"
            :selectedCandidateDOB="selectedCandidateDOB"
            @close-award-form="closeEditForm"
            @refetch-career-details="handleUpdateSuccess"
          >
          </AddEditAwardDetails>
        </v-dialog>
        <div v-if="!isMobileView" class="d-flex">
          <v-slide-group
            class="px-4"
            selected-class="bg-primary"
            prev-icon="fas fa-chevron-circle-left"
            next-icon="fas fa-chevron-circle-right"
            show-arrows
          >
            <v-slide-group-item>
              <ViewAwardsDetails
                :awardDetails="awardDetails"
                :formAccess="formAccess"
                @on-open-edit="openEditForm($event)"
                @on-delete="onShowDeleteConfirmation($event)"
              />
            </v-slide-group-item>
          </v-slide-group>
        </div>
        <div v-else>
          <div class="card-container">
            <ViewAwardsDetails
              :awardDetails="awardDetails"
              :formAccess="formAccess"
              @on-open-edit="openEditForm($event)"
              @on-delete="onShowDeleteConfirmation($event)"
            />
          </div>
        </div>
      </div>
    </div>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      confirmation-heading="Are you sure you want to delete this record ?"
      icon-name="fas fa-trash-alt"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteSelectedCareerDetails()"
    ></AppWarningModal>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import ViewAwardsDetails from "./awards/ViewAwardsDetails.vue";
import SkillDetails from "./skills/SkillDetails.vue";
// components
const AddEditAwardDetails = defineAsyncComponent(() =>
  import("./awards/AddEditAwardDetails.vue")
);
import { RETRIEVE_EMP_CAREER_INFO } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "CareerInfo",
  components: {
    SkillDetails,
    ViewAwardsDetails,
    AddEditAwardDetails,
  },
  props: {
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
  },
  emits: ["details-retrieved"],
  data: () => ({
    // edit/add
    showAddEditAwardForm: false,
    selectedAwardDetails: {},
    // view
    awardDetails: [],
    skillDetails: [
      {
        Primary_Skill: "",
        Secondary_Skill: "",
        Known_Skills: "",
      },
    ],
    // delete
    selectedAwardDelateRecord: null,
    openWarningModal: false,
    // others
    hideOtherDetails: false,
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableAdd() {
      return this.formAccess && this.formAccess.add;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.getCareerDetails();
  },
  methods: {
    openEditForm(params) {
      mixpanel.track("Onboarded-candidate-career-edit-opened");
      let selectedItem = params[0],
        typeofSkill = params[1];
      if (typeofSkill == "award") {
        this.selectedAwardDetails = selectedItem;
        this.showAddEditAwardForm = true;
      }
    },
    closeEditForm() {
      mixpanel.track("Onboarded-candidate-career-edit-closed");
      this.selectedAwardDetails = {};
      this.hideOtherDetails = false;
      this.showAddEditAwardForm = false;
    },
    // this method opens the delete confirmation popup
    onShowDeleteConfirmation(params) {
      this.openWarningModal = true;
      let selectedItem = params[0],
        typeofSkill = params[1];
      // Set the form data to the selected card's data for editing
      if (typeofSkill == "award") {
        this.selectedAwardDelateRecord = selectedItem;
      }
    },
    onDeleteSelectedCareerDetails() {
      this.onCloseWarningModal();
    },
    //this method closes the delete confirmation popup
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedAwardDelateRecord = null;
    },
    handleUpdateSuccess() {
      this.closeEditForm();
      this.refetchAPIs("update");
    },
    refetchAPIs(type = "") {
      this.isErrorInList = false;
      mixpanel.track("Onboarded-candidate-career-refetch");
      this.getCareerDetails(type);
    },
    getCareerDetails(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_CAREER_INFO,
          client: "apolloClientV",
          variables: {
            candidateId: vm.selectedCandidateId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("Onboarded-candidate-career-fetch-success");
          if (response && response.data && response.data.retrieveCareerInfo) {
            const { awardDetails, skillDetails } =
              response.data.retrieveCareerInfo;
            vm.awardDetails = awardDetails ? JSON.parse(awardDetails) : [];
            vm.skillDetails = skillDetails
              ? JSON.parse(skillDetails)
              : vm.skillDetails;
            vm.$emit("details-retrieved", type);
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("Onboarded-candidate-career-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "career details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  /* The grid-gap property adds a 10-pixel gap between the cards. */
  grid-gap: 10px;
}
@media (max-width: 600px) {
  .card-container {
    grid-template-columns: 1fr;
  }
}
</style>
