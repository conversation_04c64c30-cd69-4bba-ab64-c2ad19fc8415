<template>
  <div>
    <AppTopBarTab
      v-if="mainTabs.length > 0"
      :tabs-list="mainTabs"
      :center-tab="true"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-if="uniformMonitoringListBackup.length > 0">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isDefaultFilter="false"
              :isFilter="false"
            ></EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="team-container">
      <section v-if="checkAccess">
        <v-window v-model="currentTabItem">
          <v-window-item :value="currentTabItem">
            <div v-if="listLoading" class="mt-3">
              <v-skeleton-loader
                ref="skeleton1"
                type="table-heading"
                class="mx-auto"
              ></v-skeleton-loader>
              <div v-for="i in 3" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item-avatar"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </div>
            <div v-else-if="isErrorInList">
              <AppFetchErrorScreen
                image-name="common/common-error-image"
                :content="errorContent"
                icon-name="fas fa-redo-alt"
                button-text="Retry"
                :isSmallImage="true"
                @button-click="refreshLists()"
              >
              </AppFetchErrorScreen>
            </div>
            <v-row v-else>
              <v-col cols="12">
                <div>
                  <v-row v-if="!showHistoryView">
                    <v-col
                      v-if="!isAddViewEditLeaveOpened && !showHistoryView"
                      cols="12"
                      class="d-flex flex-wrap align-center pb-0"
                      :class="isMobileView ? 'flex-column' : ''"
                      style="justify-content: space-between"
                    >
                      <div></div>
                      <div
                        v-if="uniformMonitoringListBackup.length !== 0"
                        class="d-flex align-center"
                        :class="isMobileView ? 'justify-center' : 'justify-end'"
                      >
                        <v-btn
                          color="transparent"
                          class="ml-1 mt-1"
                          variant="flat"
                          size="small"
                          @click="refreshLists()"
                          ><v-icon color="grey">fas fa-redo-alt</v-icon></v-btn
                        >
                        <v-menu
                          v-model="openMoreMenu"
                          transition="scale-transition"
                        >
                          <template v-slot:activator="{ props }">
                            <v-btn
                              variant="plain"
                              class="mt-1 ml-n3 mr-n5"
                              v-bind="props"
                            >
                              <v-icon v-if="!openMoreMenu"
                                >fas fa-ellipsis-v</v-icon
                              >
                              <v-icon v-else>fas fa-caret-up</v-icon>
                            </v-btn>
                          </template>
                          <v-list>
                            <v-list-item
                              v-for="action in moreActions"
                              :key="action.key"
                              @click="onMoreAction(action.key)"
                            >
                              <v-hover>
                                <template
                                  v-slot:default="{ isHovering, props }"
                                >
                                  <v-list-item-title
                                    v-bind="props"
                                    class="pa-3"
                                    :class="{
                                      'pink-lighten-5': isHovering,
                                    }"
                                    ><v-icon size="15" class="pr-2">{{
                                      action.icon
                                    }}</v-icon
                                    >{{ action.key }}</v-list-item-title
                                  >
                                </template>
                              </v-hover>
                            </v-list-item>
                          </v-list>
                        </v-menu>
                      </div>
                    </v-col>
                    <v-col
                      v-if="uniformMonitoringListBackup.length !== 0"
                      :cols="
                        isAddViewEditLeaveOpened && windowWidth >= 1264 ? 5 : 12
                      "
                    >
                      <UniformMonitoringList
                        :items="uniformMonitoringList"
                        :isSmallTable="isAddViewEditLeaveOpened"
                        :originalList="uniformMonitoringListBackup"
                        @on-select-item="openViewForm($event)"
                        @refetch-list="refreshLists()"
                      />
                    </v-col>
                    <v-col
                      v-if="
                        isAddViewEditLeaveOpened &&
                        windowWidth >= 1264 &&
                        uniformMonitoringListBackup.length !== 0
                      "
                      :cols="
                        uniformMonitoringList &&
                        uniformMonitoringList.length === 0
                          ? 12
                          : 7
                      "
                    >
                      <div>
                        <UniformMonitoringView
                          :employeeDetails="selectedEmployeeDetails"
                          :isSmallTable="isAddViewEditLeaveOpened"
                          @close-split-view="isAddViewEditLeaveOpened = false"
                          @refetch-list="refreshLists()"
                          @enable-loader="isLoading = true"
                          @disable-loader="isLoading = false"
                        /></div
                    ></v-col>
                  </v-row>
                </div>
                <AppFetchErrorScreen
                  v-if="uniformMonitoringListBackup.length === 0"
                  key="no-results-screen"
                >
                  <template #contentSlot>
                    <div style="max-width: 80%" class="mx-auto">
                      <v-row
                        style="background: white"
                        class="rounded-lg pa-5 mb-4"
                      >
                        <v-col cols="12">
                          <NotesCard
                            notes="No data available for uniform monitoring."
                            backgroundColor="transparent"
                            class="mb-4"
                          ></NotesCard>
                        </v-col>
                        <v-col
                          cols="12"
                          class="d-flex align-center justify-center mb-4"
                        >
                          <v-btn
                            rounded="lg"
                            class="mt-1 primary"
                            variant="elevated"
                            :size="isMobileView ? 'small' : 'default'"
                            @click="refreshLists()"
                          >
                            <v-icon>fas fa-redo-alt</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </div>
                  </template>
                </AppFetchErrorScreen>
              </v-col>
            </v-row>
          </v-window-item>
        </v-window>
      </section>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import moment from "moment";
import { checkNullValue } from "@/helper";
import {
  LIST_MY_TEAM_EMPLOYEES,
  RETRIEVE_TOTAL_EMP_COUNT,
} from "@/graphql/employee-profile/profileQueries";
import UniformMonitoringList from "./UniformMonitoringList.vue";
import UniformMonitoringView from "./UniformMonitoringView.vue";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu.vue";
import FileExportMixin from "@/mixins/FileExportMixin";

export default defineComponent({
  name: "UniformMonitoring",
  components: {
    NotesCard,
    UniformMonitoringList,
    EmployeeDefaultFilterMenu,
    UniformMonitoringView,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "tab-1",
      // list
      uniformMonitoringList: [],
      uniformMonitoringListBackup: [],
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      // selected Employee
      selectedEmployee: 0,
      selectedEmployeeDetails: {},
      openMoreMenu: false,

      isFormDirty: false,
      currentYearLeaveEntitlement: 0,
      selectedLeaveTaken: 0,
      carryOverBalance: 0,
      isAddViewEditLeaveOpened: false,
      allEmployeesList: [],
      isFetchingEmployees: false,
      showHistoryView: false,
      isLoading: false,
      employeesLimitToCallAPI: 3400,
      totalApiCount: 0,
      apiCallCount: 0,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    leaveOverrideAccess() {
      let formAccess = this.accessRights("277");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    leaveConfigAccess() {
      let formAccess = this.accessRights("263");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    checkAccess() {
      return (
        (this.leaveOverrideAccess &&
          (this.leaveOverrideAccess.admin === "admin" ||
            this.leaveOverrideAccess.isManager)) ||
        this.leaveConfigAccess
      );
    },
    mainTabs() {
      const orgCode = localStorage.getItem("orgCode");
      if (orgCode === "showpeople" || orgCode === "fieldforce")
        return ["My ESOP", "Uniform Monitoring", "Air Ticket Claim"];
      else return ["My ESOP", "Uniform Monitoring"];
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      return [{ key: "Export", icon: "fas fa-file-export" }];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  mounted() {
    this.getTotalEmpCount();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    openViewForm(item) {
      this.selectedEmployeeDetails = item;
      this.isAddViewEditLeaveOpened = true;
    },
    onTabChange(tab) {
      if (tab && tab.toLowerCase() === "my esop") {
        window.location.href = this.baseUrl + "in/benefits/esop";
      } else if (tab && tab.toLowerCase() === "air ticket claim") {
        this.$router.push("/benefits/air-ticket-claim");
      }
    },
    refreshLists() {
      this.uniformMonitoringList = [];
      this.uniformMonitoringListBackup = [];
      this.apiCallCount = 0;
      this.totalApiCount = 0;
      this.getTotalEmpCount();
    },
    getTotalEmpCount() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_TOTAL_EMP_COUNT,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveEmployeeCount
          ) {
            let { totalCount } = response.data.retrieveEmployeeCount;
            if (totalCount > 0) {
              totalCount = parseInt(totalCount);
              this.apiCallCount = 0;
              this.totalApiCount = Math.ceil(
                totalCount / this.employeesLimitToCallAPI
              );
              for (let i = 0; i < this.totalApiCount; i++) {
                this.getMyTeamEmployees(i);
              }
            } else {
              vm.listLoading = false;
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    getMyTeamEmployees(index) {
      let vm = this;
      vm.listLoading = true;
      let apiOffset = parseInt(index) * vm.employeesLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      vm.$apollo
        .query({
          query: LIST_MY_TEAM_EMPLOYEES,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.loginEmployeeId,
            offset: apiOffset,
            limit: vm.employeesLimitToCallAPI,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response && response.data && response.data.listMyTeam) {
            let { listMyTeam } = response.data.listMyTeam;
            listMyTeam = JSON.parse(listMyTeam);
            listMyTeam = listMyTeam.map((item) => {
              let empName = "";
              if (item["employeeFirstName"])
                empName += item["employeeFirstName"] + " ";
              if (item["employeeMiddleName"])
                empName += item["employeeMiddleName"] + " ";
              if (item["employeeLastName"]) empName += item["employeeLastName"];
              item["employeeName"] = empName;
              return item;
            });
            vm.uniformMonitoringList =
              vm.uniformMonitoringList.concat(listMyTeam);
            vm.uniformMonitoringListBackup =
              vm.uniformMonitoringListBackup.concat(listMyTeam);
            vm.apiCallCount += 1;
            if (vm.totalApiCount === vm.apiCallCount) {
              vm.listLoading = false;
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Uniform Monitoring",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onApplySearch(val) {
      if (!val) {
        this.uniformMonitoringList = this.uniformMonitoringListBackup;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.uniformMonitoringListBackup;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.uniformMonitoringList = searchItems;
      }
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = this.uniformMonitoringList;
      exportData = exportData.map((el) => ({
        ...el,
        employeeId: el?.userDefinedEmpId,
        employeeName: el?.employeeName,
        uniformType: el?.uniformType,
        gender: el?.gender,
        uniformSize: el?.uniformSize,
        uniformStatus: el?.uniformStatus,
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Uniform Monitoring",
        sheetName: "Uniform Monitoring",
        header: [
          { key: "employeeId", header: "Employee Id" },
          { key: "employeeName", header: "Employee Name" },
          { key: "uniformType", header: "Uniform Type" },
          {
            key: "gender",
            header: "Gender",
          },
          { key: "uniformSize", header: "Uniform Size" },
          { key: "uniformStatus", header: "Uniform Status" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
  },
});
</script>

<style>
.team-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .team-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
