<template>
  <div v-if="isMounted">
    <div
      v-if="labelList[458]?.Field_Visiblity?.toLowerCase() === 'yes'"
      class="d-flex align-center justify-space-between ml-2"
    >
      <v-switch
        v-model="noWorkExperience"
        density="compact"
        color="primary"
        :true-value="1"
        :false-value="0"
        class="mb-n4 mt-2"
        :label="labelList[458]?.Field_Alias"
      />
      <span class="mt-n4" v-if="experienceFormData.length > 0">
        <v-btn color="primary" variant="text" @click="onAddNew()">
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add New</v-btn
        >
      </span>
    </div>
    <span
      v-else
      class="d-flex"
      :class="
        experienceFormData.length > 0
          ? 'justify-end mt-n4'
          : 'justify-start ml-n3 mt-2 mb-n2'
      "
    >
      <v-btn color="primary" variant="text" @click="onAddNew()">
        <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add New</v-btn
      >
    </span>
    <v-form ref="addEditExperienceForm">
      <div v-for="(experience, i) in experienceFormData" :key="'exp' + i">
        <v-row align="center">
          <v-col cols="11">
            <v-row>
              <v-col
                v-if="labelList[279].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model="experienceFormData[i].Prev_Company_Name"
                  variant="solo"
                  :rules="[
                    labelList[279].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[279].Field_Alias}`,
                          experienceFormData[i].Prev_Company_Name
                        )
                      : true,
                    experienceFormData[i].Prev_Company_Name
                      ? validateWithRulesAndReturnMessages(
                          experienceFormData[i].Prev_Company_Name,
                          'companyName',
                          `${labelList[279].Field_Alias}`
                        )
                      : true,
                  ]"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    <span>{{ labelList[279].Field_Alias }}</span>
                    <span
                      v-if="labelList[279].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="labelList[280].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model="experienceFormData[i].Designation"
                  variant="solo"
                  :rules="[
                    labelList[280].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[280].Field_Alias}`,
                          experienceFormData[i].Designation
                        )
                      : true,
                    experienceFormData[i].Designation
                      ? validateWithRulesAndReturnMessages(
                          experienceFormData[i].Designation,
                          'designation',
                          `${labelList[280].Field_Alias}`
                        )
                      : true,
                  ]"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    <span>{{ labelList[280].Field_Alias }}</span>
                    <span
                      v-if="labelList[280].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <!-- companyLocation -->
              <v-col
                v-if="labelList[336]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model="experienceFormData[i].Prev_Company_Location"
                  :rules="[
                    labelList[336]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[336]?.Field_Alias,
                          experienceFormData[i].Prev_Company_Location
                        )
                      : true,
                    validateWithRulesAndReturnMessages(
                      experienceFormData[i].Prev_Company_Location,
                      'companyLocation',
                      labelList[336]?.Field_Alias
                    ),
                  ]"
                  clearable
                  variant="solo"
                  :ref="companyLocation + i"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[336]?.Field_Alias }}
                    <span
                      v-if="
                        labelList[336]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="labelList[281].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
                class="mt-n5"
              >
                <div class="text-caption">
                  {{ labelList[281].Field_Alias
                  }}<span
                    style="color: red"
                    v-if="labelList[281].Mandatory_Field == 'Yes'"
                    >*</span
                  >
                  <v-tooltip
                    v-if="!selectedCandidateDOB"
                    location="right"
                    text="Date of birth is required to add dates for experience."
                  >
                    <template v-slot:activator="{ props }">
                      <span v-bind="props" class="pa-1 cursor-not-allowed">
                        <v-icon
                          size="small"
                          color="info"
                          class="fas fa-info-circle"
                        ></v-icon>
                      </span>
                    </template>
                  </v-tooltip>
                </div>
                <v-menu
                  v-model="experienceFormData[i].showStartDate"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      :value="experienceFormData[i].startDateFormatted"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[
                        labelList[281].Mandatory_Field == 'Yes'
                          ? required(
                              labelList[281].Field_Alias,
                              experienceFormData[i].startDateFormatted
                            )
                          : true,
                      ]"
                      readonly
                      v-bind="props"
                      :disabled="!selectedCandidateDOB"
                      variant="solo"
                      ref="experienceStart"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    @update:modelValue="
                      onDateChange('startDateFormatted', i, $event)
                    "
                    v-model="experienceFormData[i].Start_Date"
                    :min="selectedEmpDobDate"
                    :max="startDateMax(i)"
                  />
                </v-menu>
              </v-col>
              <v-col
                v-if="labelList[282].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
                class="mt-n5"
              >
                <div class="text-caption">
                  {{ labelList[282].Field_Alias
                  }}<span
                    style="color: red"
                    v-if="labelList[282].Mandatory_Field == 'Yes'"
                    >*</span
                  >
                  <v-tooltip
                    v-if="!selectedCandidateDOB"
                    location="right"
                    text="Date of birth is required to add dates for experience."
                  >
                    <template v-slot:activator="{ props }">
                      <span v-bind="props" class="pa-1 cursor-not-allowed">
                        <v-icon
                          size="small"
                          color="info"
                          class="fas fa-info-circle"
                        ></v-icon>
                      </span>
                    </template>
                  </v-tooltip>
                </div>
                <v-menu
                  v-model="experienceFormData[i].showEndDate"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      :value="experienceFormData[i].endDateFormatted"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[
                        labelList[282].Mandatory_Field == 'Yes'
                          ? required(
                              labelList[282].Field_Alias,
                              experienceFormData[i].endDateFormatted
                            )
                          : true,
                      ]"
                      readonly
                      v-bind="props"
                      :disabled="
                        !experienceFormData[i].Start_Date ||
                        !selectedCandidateDOB
                      "
                      variant="solo"
                      ref="experienceEnd"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    @update:modelValue="
                      onDateChange('endDateFormatted', i, $event)
                    "
                    v-model="experienceFormData[i].End_Date"
                    :min="endDateMin(i)"
                    :max="currentDate"
                  />
                </v-menu>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    experienceFormData[i] && experienceFormData[i].Duration
                      ? convertMonthToYearMonthsDays(
                          experienceFormData[i].Duration
                        )
                      : "-"
                  }}
                </p>
              </v-col>
            </v-row>
            <div
              v-if="
                labelList[360]?.Field_Visiblity?.toLowerCase() === 'yes' ||
                labelList[361]?.Field_Visiblity?.toLowerCase() === 'yes' ||
                labelList[362]?.Field_Visiblity?.toLowerCase() === 'yes'
              "
              class="text-grey-darken-1 font-weight-bold my-1"
            >
              Reference Details
            </div>
            <v-row
              v-for="(reference, j) in experience.References"
              :key="`reference-${i}-${j}`"
              dense
            >
              <!-- Reference Name -->
              <v-col
                v-if="labelList[360]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  :ref="`referenceName-${i}-${j}`"
                  clearable
                  v-model="reference.Reference_Name"
                  :rules="[
                    labelList[360]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[360]?.Field_Alias,
                          reference.Reference_Name
                        )
                      : true,
                    validateWithRulesAndReturnMessages(
                      reference.Reference_Name,
                      'skillName',
                      labelList[360]?.Field_Alias
                    ),
                  ]"
                  @update:model-value="onChangeFields()"
                  variant="solo"
                >
                  <template v-slot:label>
                    <span>{{ labelList[360]?.Field_Alias }}</span>
                    <span
                      v-if="
                        labelList[360]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      class="ml-1"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>

              <!-- Reference Email -->
              <v-col
                v-if="labelList[361]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  :ref="`referenceEmail-${i}-${j}`"
                  clearable
                  v-model="reference.Reference_Email"
                  :rules="[
                    labelList[361]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[361]?.Field_Alias,
                          reference.Reference_Email
                        )
                      : true,
                    reference.Reference_Email
                      ? validateWithRulesAndReturnMessages(
                          reference.Reference_Email,
                          'empEmail',
                          labelList[361]?.Field_Alias
                        )
                      : true,
                  ]"
                  @update:model-value="onChangeFields()"
                  variant="solo"
                >
                  <template v-slot:label>
                    <span>{{ labelList[361]?.Field_Alias }}</span>
                    <span
                      v-if="
                        labelList[361]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      class="ml-1"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>

              <!-- Reference Number -->
              <v-col
                v-if="labelList[362]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <div class="d-flex align-center">
                  <!-- Text Field -->
                  <v-text-field
                    :ref="`referenceNumber-${i}-${j}`"
                    clearable
                    v-model="reference.Reference_Number"
                    :rules="[
                      labelList[362]?.Mandatory_Field?.toLowerCase() === 'yes'
                        ? required(
                            labelList[362]?.Field_Alias,
                            reference.Reference_Number
                          )
                        : true,
                      reference.Reference_Number
                        ? minLengthValidation(
                            labelList[362]?.Field_Alias,
                            reference.Reference_Number,
                            6
                          )
                        : true,
                    ]"
                    :counter="15"
                    :maxlength="15"
                    class="flex-grow-1"
                    max-width="90%"
                    @update:model-value="onChangeFields()"
                    variant="solo"
                  >
                    <template v-slot:label>
                      <span>{{ labelList[362]?.Field_Alias }}</span>
                      <span
                        v-if="
                          labelList[362]?.Mandatory_Field?.toLowerCase() ===
                          'yes'
                        "
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>

                  <!-- Icons -->
                  <div class="ml-2">
                    <!-- Delete Icon -->
                    <v-icon
                      v-if="experience.References.length > 1"
                      size="15"
                      class="fas fa-trash ml-1"
                      color="primary"
                      @click="deleteReference(i, j)"
                    />
                    <!-- Add Icon -->
                    <v-btn
                      v-if="j === experience.References.length - 1"
                      color="primary"
                      class="ml-2"
                      variant="text"
                      @click="addReference(i)"
                    >
                      <v-icon class="mr-1" size="15">fas fa-plus</v-icon> Add
                      New</v-btn
                    >
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="1" class="d-flex justify-center">
            <v-hover>
              <template v-slot:default="{ isHovering, props }">
                <v-icon
                  v-bind="props"
                  :color="isHovering ? 'red' : 'grey'"
                  @click="onDelate(i)"
                  >fas fa-trash</v-icon
                >
              </template>
            </v-hover></v-col
          >
        </v-row>
        <v-divider class="my-4"></v-divider>
      </div>
    </v-form>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import { convertMonthToYearMonthsDays, getDaysDifference } from "@/helper";
import moment from "moment";

export default {
  name: "AddEditExperienceDetails",
  mixins: [validationRules],
  props: {
    experienceDetails: {
      type: Object,
      required: false,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
    dateFormat: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      experienceFormData: [],
      startDateErrorMsg: [],
      endDateErrorMsg: [],
      isFormDirty: false,
      isMounted: false,
      endDateMenu: false,
      formattedEndDate: "",
      startDateMenu: false,
      formattedStartDate: "",
      noWorkExperience: 0,
    };
  },
  computed: {
    selectedEmpDobDate() {
      if (
        this.selectedCandidateDOB &&
        this.selectedCandidateDOB !== "0000-00-00" &&
        this.selectedCandidateDOB != "Invalid Date"
      ) {
        return moment(this.selectedCandidateDOB).format("YYYY-MM-DD");
      } else return this.currentDate;
    },
    orgDateFormat() {
      let format = this.dateFormat
        ? this.dateFormat
        : this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  watch: {
    experienceFormData: {
      deep: true,
      handler(newVal) {
        if (newVal?.length)
          this.$emit("update-experience-details", newVal, "experience");
        else this.noWorkExperience = 1;
      },
    },
    noWorkExperience: {
      handler(val) {
        if (val == 0) this.onAddNew();
        else this.experienceFormData.length = 0;
      },
      immediate: true,
    },
  },
  emits: ["update-experience-details"],

  mounted() {
    this.isMounted = false;
    // Set the form data when component is created
    let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
    if (
      this.experienceDetails &&
      Object.keys(this.experienceDetails).length > 0
    ) {
      this.experienceFormData = JSON.parse(
        JSON.stringify(this.experienceDetails)
      );
      for (let i = 0; i < this.experienceFormData.length; i++) {
        if (this.experienceFormData[i]["Start_Date"]) {
          this.experienceFormData[i]["Start_Date"] = moment(
            this.experienceFormData[i]["Start_Date"],
            "YYYY/MM/DD"
          )._d;
          this.experienceFormData[i]["startDateFormatted"] = this
            .experienceFormData[i]["Start_Date"]
            ? moment(this.experienceFormData[i]["Start_Date"]).format(
                orgDateFormat
              )
            : "";
        }
        if (this.experienceFormData[i]["End_Date"]) {
          this.experienceFormData[i]["End_Date"] = moment(
            this.experienceFormData[i]["End_Date"],
            "YYYY/MM/DD"
          )._d;
          this.experienceFormData[i]["endDateFormatted"] = this
            .experienceFormData[i]["End_Date"]
            ? moment(this.experienceFormData[i]["End_Date"]).format(
                orgDateFormat
              )
            : "";
        } else {
          const currentDate = new Date();
          this.experienceFormData[i]["End_Date"] = currentDate;
          this.experienceFormData[i]["endDateFormatted"] =
            moment(currentDate).format(orgDateFormat);
        }
        // Initialize References if not present
        if (!this.experienceFormData[i]["References"]?.length) {
          this.experienceFormData[i]["References"] = [
            {
              Reference_Name: null,
              Reference_Email: null,
              Reference_Number: null,
            },
          ];
        } else {
          this.experienceFormData[i]["References"] = this.experienceFormData[i][
            "References"
          ].map((ref) => ({
            Reference_Name: ref.Reference_Name || null,
            Reference_Email: ref.Reference_Email || null,
            Reference_Number: ref.Reference_Number || null,
          }));
        }
        this.calculateDuration(i);
      }
    } else {
      this.experienceFormData = [];
    }
    if (this.labelList[458]?.Field_Visiblity?.toLowerCase() === "yes")
      this.onAddNew();
    this.isMounted = true;
  },
  methods: {
    convertMonthToYearMonthsDays,
    startDateMax(index) {
      if (
        this.experienceFormData[index].End_Date &&
        this.experienceFormData[index].End_Date !== "0000-00-00" &&
        this.experienceFormData[index].End_Date != "Invalid Date"
      ) {
        const issueDateMs = new Date(this.experienceFormData[index].End_Date);
        return issueDateMs;
      }
      return this.currentDate;
    },
    endDateMin(index) {
      if (
        this.experienceFormData[index].Start_Date &&
        this.experienceFormData[index].Start_Date !== "0000-00-00" &&
        this.experienceFormData[index].Start_Date != "Invalid Date"
      ) {
        return moment(this.experienceFormData[index].Start_Date).format(
          "YYYY-MM-DD"
        );
      }
      return this.selectedEmpDobDate;
    },
    calculateDuration(index) {
      let dayDifference = getDaysDifference(
        this.experienceFormData[index].Start_Date,
        this.experienceFormData[index].End_Date
      );
      // More precise    calculation using moment duration
      this.experienceFormData[index]["Duration"] = moment
        .duration(dayDifference, "days")
        .asMonths()
        .toFixed(2);
    },
    addReference(experienceIndex) {
      this.experienceFormData[experienceIndex].References.push({
        Reference_Name: null,
        Reference_Email: null,
        Reference_Number: null,
      });
    },
    deleteReference(experienceIndex, referenceIndex) {
      if (this.experienceFormData[experienceIndex].References.length > 1) {
        this.experienceFormData[experienceIndex].References.splice(
          referenceIndex,
          1
        );
      }
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onDateChange(val, index, selectedDate) {
      this.isFormDirty = true;
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      const formattedDate = selectedDate
        ? moment(selectedDate).format(orgDateFormat)
        : "";
      this.experienceFormData[index][val] = formattedDate;
      if (val === "endDateFormatted") {
        this.experienceFormData[index]["showEndDate"] = false;
        let dayDifference = getDaysDifference(
          this.experienceFormData[index].Start_Date,
          selectedDate
        );
        this.experienceFormData[index]["Duration"] = (
          dayDifference / 30
        ).toFixed(2);
      } else {
        this.experienceFormData[index]["showStartDate"] = false;
        let dayDifference = getDaysDifference(
          selectedDate,
          this.experienceFormData[index].End_Date
        );
        this.experienceFormData[index]["Duration"] = (
          dayDifference / 30
        ).toFixed(2);
      }
    },
    onAddNew() {
      this.experienceFormData.push({
        Prev_Company_Name: "",
        Designation: "",
        Start_Date: null,
        End_Date: null,
        showEndDate: false,
        endDateFormatted: "",
        showStartDate: false,
        startDateFormatted: "",
        Prev_Company_Location: null,
        References: [
          {
            Reference_Name: null,
            Reference_Email: null,
            Reference_Number: null,
          },
        ],
      });
    },
    onDelate(index) {
      this.experienceFormData.splice(index, 1);
    },
    async validateExperienceDetails() {
      if (
        this.experienceFormData.length > 0 &&
        this.$refs.addEditExperienceForm
      ) {
        const { valid } = await this.$refs.addEditExperienceForm.validate();
        if (valid) {
          this.startDateErrorMsg = [];
          this.endDateErrorMsg = [];
          let experienceFormData = this.experienceFormData;
          for (let i = 0; i < this.experienceFormData.length; i++) {
            if (experienceFormData[i].Start_Date) {
              experienceFormData[i]["Start_Date"] = moment(
                experienceFormData[i]["Start_Date"]
              ).format("YYYY/MM/DD");
            } else {
              experienceFormData[i].Start_Date = null;
            }
            if (experienceFormData[i].End_Date) {
              experienceFormData[i]["End_Date"] = moment(
                experienceFormData[i]["End_Date"]
              ).format("YYYY/MM/DD");
            } else {
              experienceFormData[i].End_Date = null;
            }
            if (this.labelList[360]?.Field_Visiblity?.toLowerCase() !== "yes") {
              experienceFormData[i].References = null;
            }
          }
          return experienceFormData;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
  },
};
</script>
