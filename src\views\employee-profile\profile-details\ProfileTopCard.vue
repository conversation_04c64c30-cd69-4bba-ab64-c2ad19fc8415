<template>
  <div v-if="showTopCard">
    <div v-if="listLoading">
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else-if="!isErrorInList">
      <ProfileCard>
        <div class="d-flex justify-end mb-n8 mt-2 mr-1">
          <div class="d-flex align-center" v-if="callingFrom != 'profile'">
            <div v-if="showInviteOption">
              <v-btn
                v-if="authenticationMethods.length > 0"
                color="blue"
                rounded="lg"
                variant="outlined"
                size="small"
                :loading="fetchingAuthenticationMethods"
                @click="onOpenInviteChooseModal()"
                >Send Invite</v-btn
              >
              <v-tooltip v-else location="bottom">
                <template v-slot:activator="{ props }">
                  <v-chip
                    class="mr-3"
                    v-bind="props"
                    rounded="lg"
                    color="grey"
                    size="large"
                    >Send Invite</v-chip
                  >
                </template>
                <div style="max-width: 350px !important">
                  No authentication methods were configured
                </div>
              </v-tooltip>
            </div>
            <v-btn
              color="primary"
              rounded="lg"
              variant="outlined"
              class="ml-1 mr-2"
              size="small"
              @click="onChangeEmployee()"
              >Change Employee</v-btn
            >
          </div>
          <v-icon @click="refetchAPIs()" size="17" color="grey"
            >fas fa-redo-alt</v-icon
          >
        </div>
        <v-row>
          <v-col
            class="d-flex align-center"
            cols="2"
            xs="4"
            sm="3"
            md="3"
            lg="2"
          >
            <div
              v-if="callingFrom !== 'profile'"
              class="d-flex align-center ml-3"
            >
              <v-icon @click="goBackToList()" color="primary" size="x-large"
                >fas fa-angle-left fa-lg</v-icon
              >
            </div>
            <v-skeleton-loader
              v-if="isFetchingProfilePic"
              ref="skeleton3"
              type="avatar"
            ></v-skeleton-loader>
            <ProfileAvatar
              v-else
              :avatarSize="120"
              :profile-image-path="profileImage"
              :fileName="photoPath"
              :empStatus="employeeDetails.empStatus"
              :employeeId="employeeDetails.employeeId"
              :allowUpdate="selectedEmpStatus === 'Active' && enableEdit"
              @profile-uploaded="updateProfilePath($event)"
              @profile-uploading="isLoading = $event"
            ></ProfileAvatar>
          </v-col>
          <v-col cols="3" xs="5" sm="6" md="3" lg="3">
            <ProfileDetails
              v-if="!isMobileView"
              class="ma-5 d-flex flex-column justify-center"
              :employeeName="employeeDetails.employeeName"
              :empUserDefId="employeeDetails.userDefinedEmpId"
              :empDepartment="employeeDetails.departmentName"
              :empDesignation="employeeDetails.designationName"
            ></ProfileDetails>
          </v-col>
          <v-col cols="6" xs="8" sm="8" md="6" lg="6">
            <ContactDetails
              :employeeName="employeeDetails.employeeName"
              :empEmailAddress="employeeDetails.empEmail"
              :empMobileNo="employeeDetails.mobileNo"
              :empMobileNoCode="employeeDetails.mobileNoCountryCode"
              :address="employeeDetails.address"
              :invitationStatus="employeeDetails.invitationStatus"
              :isEdit="false"
            ></ContactDetails>
          </v-col>
        </v-row>
      </ProfileCard>
    </div>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openConfirmationModal"
    :open-modal="openConfirmationModal"
    icon-name="fas fa-check-circle"
    icon-color="green"
    confirmationHeading="Employee record submitted successfully!"
    :confirmationText="
      inviteButtonAccess ? 'Would you like to send the signup invitation?' : ''
    "
    :closeButtonText="inviteButtonAccess ? 'No' : null"
    :acceptButtonText="inviteButtonAccess ? 'Yes' : null"
    @close-warning-modal="closeSuccessModal()"
    @accept-modal="onSendInvite()"
  ></AppWarningModal>
  <v-dialog
    v-if="openInviteModal"
    v-model="openInviteModal"
    width="800"
    @click:outside="closeSuccessModal"
  >
    <v-card class="rounded-lg">
      <div class="d-flex justify-end">
        <v-icon
          color="primary"
          class="pr-4 pt-4 font-weight-bold"
          @click="closeSuccessModal()"
          >fas fa-times</v-icon
        >
      </div>
      <v-card-title class="mt-n4">
        <div class="text-primary text-center font-weight-medium">
          <v-progress-circular
            model-value="100"
            color="purple"
            :size="22"
            class="mt-n1 mr-1"
          ></v-progress-circular>
          Choose your invite option
        </div>
      </v-card-title>
      <v-card-text class="d-flex align-center justify-center flex-column pa-6">
        <v-row justify="center">
          <v-tooltip location="top">
            <template v-slot:activator="{ props }">
              <v-col
                v-bind="employeeDetails.empEmail ? '' : props"
                v-if="displayEmail"
                cols="12"
                md="6"
              >
                <v-card
                  :disabled="!employeeDetails.empEmail"
                  class="rounded-lg text-center pa-4"
                  height="100%"
                  :elevation="!employeeDetails.empEmail ? 1 : 4"
                  @click="formData('Email')"
                >
                  <v-icon size="60" color="blue" class="py-3"
                    >fas fa-envelope</v-icon
                  >
                  <p style="font-weight: bold; font-size: 1em">
                    Invite By Email
                  </p>
                  <p class="text-grey text-darken-4 py-2">
                    Inviting employees by email is the most common method and
                    typically involves sending an invitation message to the
                    employee's personal or professional email address.
                  </p>
                </v-card>
              </v-col>
            </template>
            <div style="max-width: 350px !important">
              Selected employee do not have email address to enable email login
            </div>
          </v-tooltip>
          <v-tooltip location="top">
            <template v-slot:activator="{ props }">
              <v-col
                v-bind="
                  !employeeDetails.mobileNo || isMobileSignInEnabled
                    ? props
                    : ''
                "
                v-if="displayMobile"
                cols="12"
                md="6"
              >
                <v-card
                  :disabled="!employeeDetails.mobileNo || isMobileSignInEnabled"
                  class="rounded-lg text-center pa-4"
                  height="100%"
                  :elevation="
                    !employeeDetails.mobileNo || isMobileSignInEnabled ? 1 : 4
                  "
                  @click="formData('Phone')"
                >
                  <v-icon size="60" color="green" class="py-3"
                    >fas fa-mobile-alt</v-icon
                  >
                  <div style="font-weight: bold; font-size: 1em">
                    Enable Mobile OTP Sign In
                  </div>
                  <p class="text-grey text-darken-4 py-2">
                    Enabling by Mobile on the other hand, involves enabling the
                    sign in for the employee's phone number. This method is
                    typically quicker and more direct
                  </p>
                </v-card></v-col
              >
            </template>
            <div style="max-width: 350px !important">
              {{
                isMobileSignInEnabled
                  ? "Mobile sign in was already enabled"
                  : "Selected employee do not have email address to enable mobile login."
              }}
            </div>
          </v-tooltip>
          <v-tooltip location="top">
            <template v-slot:activator="{ props }">
              <v-col
                v-bind="employeeDetails.empEmail ? '' : props"
                v-if="displayGoogle"
                cols="12"
                md="6"
              >
                <v-card
                  :disabled="!employeeDetails.empEmail"
                  class="rounded-lg text-center pa-4"
                  height="100%"
                  :elevation="!employeeDetails.empEmail ? 1 : 4"
                  @click="formData('Google')"
                >
                  <v-icon size="60" color="#00B6F2" class="py-3"
                    >fab fa-google</v-icon
                  >
                  <div style="font-weight: bold; font-size: 1em">
                    Enable Google Sign In
                  </div>
                  <p class="text-grey text-darken-4 py-2">
                    Enabling the Google sign-in feature will allow employees to
                    log in to their accounts using their Google credentials. By
                    implementing this feature, employees can easily access their
                    account without the need for creating a new username and
                    password.
                  </p>
                </v-card></v-col
              >
            </template>
            <div style="max-width: 350px !important">
              Selected employee do not have email address to enable google sign
              in.
            </div>
          </v-tooltip>
          <v-tooltip location="top">
            <template v-slot:activator="{ props }">
              <v-col
                v-bind="employeeDetails.empEmail ? '' : props"
                v-if="displayMicrosoft"
                cols="12"
                md="6"
              >
                <v-card
                  :disabled="!employeeDetails.empEmail"
                  class="rounded-lg text-center pa-4"
                  height="100%"
                  :elevation="!employeeDetails.empEmail ? 1 : 4"
                  @click="formData('Microsoft')"
                >
                  <v-icon size="60" color="#00B6F2" class="py-3"
                    >fab fa-microsoft</v-icon
                  >
                  <div style="font-weight: bold; font-size: 1em">
                    Enable Microsoft Sign In
                  </div>
                  <p class="text-grey text-darken-4 py-2">
                    Enabling the Microsoft sign-in feature will allow employees
                    to log in to their accounts using their Microsoft
                    credentials. By implementing this feature, employees can
                    easily access their account without the need for creating a
                    new username and password.
                  </p>
                </v-card></v-col
              >
            </template>
            <div style="max-width: 350px !important">
              Selected employee do not have email address to enable microsoft
              login.
            </div>
          </v-tooltip>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="12" class="d-flex justify-center align-center">
          <div>
            <p class="text-subtitle-2 font-weight-medium">
              Upon finalizing the employee record, the user will be successfully
              registered and granted access to the platform.
            </p>
          </div>
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            style="
              height: 40px;
              margin-top: 10px;
              background: green;
              border-color: white;
            "
            @click="submitEmployee()"
            ><span class="text-subtitle-2 font-weight-bold" style="color: white"
              >Finalize</span
            ></v-btn
          >
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <EmployeesListModal
    v-if="showEmpListModal"
    :show-modal="showEmpListModal"
    :employeesList="myTeamList"
    :showFilterSearch="true"
    selectStrategy="single"
    employeeIdKey="employeeId"
    userDefinedEmpIdKey="userDefinedEmpId"
    employeeNameKey="employeeName"
    deptNameKey="departmentName"
    designationKey="designationName"
    :isApplyFilter="true"
    departmentIdKey="departmentId"
    designationIdKey="designationId"
    locationIdKey="locationId"
    empTypeIdKey="empTypeId"
    workScheduleIdKey="workSchedule"
    rolesIdKey="rolesId"
    :showServiceProvider="true"
    :showRoles="true"
    @on-select-employee="onSelectEmployee($event)"
    @close-modal="showEmpListModal = false"
  ></EmployeesListModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import ProfileAvatar from "@/components/custom-components/ProfileAvatar.vue";
import ProfileDetails from "@/components/custom-components/ProfileDetails.vue";
import ContactDetails from "@/components/custom-components/ContactDetails.vue";
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal")
);
import {
  RETRIEVE_EMP_PROFILE_CARD_DETAILS,
  UPDATE_PROFILE_PICTURE_PATH,
  EMPLOYEE_FINAL_SUBMIT,
  SEND_FIREBASE_INVITATION,
  GET_AUTHENTICATION_METHODS,
} from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "ProfileTopCard",
  components: {
    ProfileAvatar,
    ProfileDetails,
    ContactDetails,
    EmployeesListModal,
  },
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    updateCount: {
      type: Number,
      default: 0,
    },
    selectedEmployeeDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    myTeamList: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  emits: ["close-profile", "on-change-employee", "form-submitted"],
  data() {
    return {
      tab: null,
      profileImage: "",
      employeeDetails: {
        employeeName: "",
        userDefinedEmpId: "",
        departmentName: "",
        designationName: "",
        empEmail: "",
        mobileNo: "",
        mobileNoCountryCode: "",
        empStatus: "Active",
        address: "",
      },
      listLoading: false,
      isErrorInList: false,
      photoPath: "",
      isFetchingProfilePic: false,
      isLoading: false,
      showTopCard: false,
      openConfirmationModal: false,
      inviteAction: "",
      openInviteModal: false,
      authenticationMethods: [],
      fetchingAuthenticationMethods: false,
      openBottomSheet: false,
      showEmpListModal: false,
    };
  },
  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isAdmin() {
      let formAccessRights = this.accessRights("22");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["update"]
      ) {
        return true;
      } else return false;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.admin === "admin" &&
          (this.formAccess.update || this.formAccess.add))
      );
    },
    enableFinalBtn() {
      if (this.employeeDetails && this.employeeDetails.mobileNo) {
        return true;
      } else return false;
    },
    inviteButtonAccess() {
      //Enable for admin, employee admin and service provider admin
      const isAdmin = this.isAdmin;
      const employeeAdminAccess =
        this.accessRights("148")?.accessRights?.update;
      const serviceProviderAdminAccess =
        this.accessRights("219")?.accessRights?.update;

      return isAdmin || employeeAdminAccess || serviceProviderAdminAccess;
    },
    showInviteOption() {
      if (
        this.inviteButtonAccess &&
        this.actionType === "edit" &&
        this.selectedEmpStatus === "Active" &&
        this.employeeDetails.invitationStatus !== "Signed Up"
      ) {
        return true;
      } else return false;
    },
    isMobileSignInEnabled() {
      if (
        this.selectedEmployeeDetails &&
        this.selectedEmployeeDetails.Enable_Sign_In_With_Mobile_No
      ) {
        return true;
      } else return false;
    },
    displayEmail() {
      if (this.authenticationMethods.length) {
        for (let auth of this.authenticationMethods) {
          if (auth.Authentication_Method_Id === 1) {
            return true;
          }
        }
      }
      return false;
    },
    displayMobile() {
      if (this.authenticationMethods.length) {
        for (let auth of this.authenticationMethods) {
          if (auth.Authentication_Method_Id === 2) {
            return true;
          }
        }
      }
      return false;
    },
    displayGoogle() {
      if (this.authenticationMethods.length) {
        for (let auth of this.authenticationMethods) {
          if (auth.Authentication_Method_Id === 3) {
            return true;
          }
        }
      }
      return false;
    },
    displayMicrosoft() {
      if (this.authenticationMethods.length) {
        for (let auth of this.authenticationMethods) {
          if (auth.Authentication_Method_Id === 4) {
            return true;
          }
        }
      }
      return false;
    },
    isEditFormOpenedCount() {
      return this.$store.state.employeeProfile.isEditFormOpenedCount;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  watch: {
    selectedEmpId(id) {
      if (id) {
        this.getPersonalDetails();
      }
    },
    updateCount(val) {
      if (val > 0) {
        this.getPersonalDetails();
      }
    },
    isEditFormOpenedCount(val) {
      let showFinalBtn = "";
      if (val && val.includes("-")) {
        let sVal = val.split("-");
        showFinalBtn = sVal[1];
      }
      if (showFinalBtn == "true") {
        this.openBottomSheet = false;
      } else if (this.actionType === "add" && this.enableFinalBtn) {
        this.openBottomSheet = true;
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.selectedEmpId || this.callingFrom === "profile") {
      this.getPersonalDetails();
    }
    if (this.callingFrom !== "profile" && this.selectedEmpStatus === "Active") {
      this.getAuthenticationMethods();
    }
  },

  methods: {
    onSelectEmployee(employee) {
      this.$emit("on-change-employee", employee);
      this.showEmpListModal = false;
    },
    onChangeEmployee() {
      this.showEmpListModal = true;
    },
    refetchAPIs() {
      this.isErrorInList = false;
      mixpanel.track("EmpProfile-topCard-refetch");
      this.getPersonalDetails();
    },
    async retrieveProfilePhoto() {
      let vm = this;
      vm.isFetchingProfilePic = true;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName:
            "hrapp_upload/" + vm.orgCode + "_tmp/images/" + vm.photoPath,
          action: "data",
          type: "profile",
          destinationBucket: "",
          destinationFileKey: "",
        })
        .then((presignedUrl) => {
          mixpanel.track("EmpProfile-topCard-profileImg-fetch-success");
          if (presignedUrl) {
            vm.profileImage = presignedUrl;
          }
          vm.isFetchingProfilePic = false;
        })
        .catch(() => {
          vm.isFetchingProfilePic = false;
          mixpanel.track("EmpProfile-topCard-profileImg-fetch-error");
        });
    },
    getPersonalDetails() {
      let vm = this;
      vm.showTopCard = true;
      vm.listLoading = true;
      vm.profileImage = "";
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_PROFILE_CARD_DETAILS,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-topCard-fetch-success");
          if (response && response.data && response.data.retrieveMyProfile) {
            const { employeeProfile } = response.data.retrieveMyProfile;
            const {
              street,
              city,
              apartmentName,
              state,
              pinCode,
              country,
              photoPath,
              useLocationAddress,
              locationStreet1,
              locationStreet2,
              locationCity,
              locationState,
              locationPinCode,
              locationCountry,
            } = employeeProfile;
            let allAddress = [];
            if (useLocationAddress) {
              if (locationStreet1) allAddress.push(locationStreet1);
              if (locationStreet2) allAddress.push(locationStreet2);
              if (locationCity) allAddress.push(locationCity);
              if (state) allAddress.push(locationState);
              if (locationCountry) allAddress.push(locationCountry);
              if (locationPinCode) allAddress.push(locationPinCode);
            } else {
              if (apartmentName) allAddress.push(apartmentName);
              if (street) allAddress.push(street);
              if (city) allAddress.push(city);
              if (state) allAddress.push(state);
              if (country) allAddress.push(country);
              if (pinCode) allAddress.push(pinCode);
            }
            allAddress = allAddress.join(", ");
            vm.employeeDetails = employeeProfile;
            vm.employeeDetails["address"] = allAddress;
            if (vm.actionType === "add" && vm.enableFinalBtn) {
              vm.openBottomSheet = true;
            }
            vm.photoPath = photoPath;
            if (photoPath) {
              vm.retrieveProfilePhoto();
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("EmpProfile-topCard-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "profile details",
          isListError: true,
        })
        .then(() => {
          this.isErrorInList = true;
        });
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    updateProfilePath(newPhotoPath) {
      let vm = this;
      mixpanel.track("EmpProfile-topCard-profileImg-upload-start");
      if (vm.photoPath !== newPhotoPath) {
        vm.photoPath = newPhotoPath;
        vm.isLoading = true;
        vm.$apollo
          .mutate({
            mutation: UPDATE_PROFILE_PICTURE_PATH,
            variables: {
              employeeId: vm.selectedEmpId,
              photoPath: newPhotoPath,
              formName: vm.callingFrom === "profile" ? "My Profile" : "",
            },
            client: "apolloClientAD",
          })
          .then(() => {
            mixpanel.track("EmpProfile-topCard-profileImg-upload-success");
            vm.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message: `Profile picture ${
                newPhotoPath ? "updated" : "deleted"
              } successfully`,
              type: "success",
            };
            vm.showAlert(snackbarData);
          })
          .catch((err) => {
            vm.handleUpdateError(err, newPhotoPath);
          });
      } else {
        mixpanel.track("EmpProfile-topCard-profileImg-upload-success");
        let snackbarData = {
          isOpen: true,
          message: `Profile picture ${
            newPhotoPath ? "updated" : "deleted"
          } successfully`,
          type: "success",
        };
        vm.showAlert(snackbarData);
      }
    },

    handleUpdateError(err = "", filePath) {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: filePath ? "updating" : "deleting",
        form: "profile picture",
        isListError: false,
      });
      mixpanel.track("EmpProfile-topCard-profileImg-upload-error");
    },

    onOpenInviteChooseModal() {
      mixpanel.track("EmpProfile-topCard-chooseInvite-modal-opened");
      this.inviteAction = "send-invite";
      this.openInviteModal = true;
    },

    closeSuccessModal() {
      this.openInviteModal = false;
      mixpanel.track("EmpProfile-topCard-close-invite-success-modal");
      if (this.inviteAction === "employee-submit") {
        this.$emit("form-submitted");
      }
    },

    goBackToList() {
      mixpanel.track("EmpProfile-topCard-backTo-team-list");
      this.$emit("close-profile");
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        "0-false"
      );
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
    },

    onSendInvite() {
      this.openConfirmationModal = false;
      this.openInviteModal = true;
    },

    submitEmployee() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: EMPLOYEE_FINAL_SUBMIT,
          client: "apolloClientAD",
          variables: {
            employeeId: vm.selectedEmpId,
          },
        })
        .then((response) => {
          mixpanel.track("EmpProfile-topCard-finalize-success");
          if (
            response.data &&
            response.data.employeeFinalSubmit &&
            !response.data.employeeFinalSubmit.errorCode
          ) {
            vm.inviteAction = "employee-submit";
            if (!this.entomoIntegrationEnabled) vm.openConfirmationModal = true;
            vm.openBottomSheet = false;
          } else {
            vm.$store.dispatch("handleApiErrors", {
              error: "",
              action: "submitting",
              form: "employee details",
              isListError: false,
            });
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "submitting",
            form: "employee details",
            isListError: false,
          });
          vm.isLoading = false;
          mixpanel.track("EmpProfile-topCard-finalize-error");
        });
    },

    sendInvitation(inviteArray) {
      let vm = this;
      mixpanel.track("EmpProfile-topCard-sendInvite-start");
      vm.openInviteModal = false;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: SEND_FIREBASE_INVITATION,
          client: "apolloClientJ",
          variables: {
            employeeData: inviteArray,
          },
        })
        .then((response) => {
          mixpanel.track("EmpProfile-topCard-sendInvite-success");
          if (
            response.data &&
            response.data.triggerBulkInviteEmployees &&
            !response.data.triggerBulkInviteEmployees.errorCode
          ) {
            let snackbarData = {
              isOpen: true,
              message: "Employee Invitation initiated successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            if (vm.inviteAction === "employee-submit") {
              vm.$store.commit(
                "employeeProfile/UPDATE_EDIT_FORM_CHANGED",
                false
              );
              vm.$store.commit(
                "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
                "0-false"
              );
              vm.$emit("form-submitted");
            }
            vm.isLoading = false;
          } else {
            vm.handleFirebaseInvitationError();
          }
        })
        .catch((err) => {
          vm.handleFirebaseInvitationError(err);
        });
    },

    handleFirebaseInvitationError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "sending",
        form: "invitation",
        isListError: false,
      });
      this.isLoading = false;
      if (this.inviteAction === "employee-submit") {
        this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
        this.$store.commit(
          "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
          "0-false"
        );
        this.$emit("form-submitted");
      }
      mixpanel.track("EmpProfile-topCard-sendInvite-error");
    },

    getAuthenticationMethods() {
      let vm = this;
      vm.fetchingAuthenticationMethods = true;
      vm.$apollo
        .query({
          query: GET_AUTHENTICATION_METHODS,
          client: "apolloClientI",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-topCard-authMethods-fetch-success");
          if (
            response &&
            response.data &&
            response.data.getAuthenticationMethods
          ) {
            const { authenticationMethods } =
              response.data.getAuthenticationMethods;
            vm.authenticationMethods = authenticationMethods;
          }
          vm.fetchingAuthenticationMethods = false;
        })
        .catch(() => {
          vm.fetchingAuthenticationMethods = false;
          mixpanel.track("EmpProfile-topCard-authMethods-fetch-error");
        });
    },
    formData(source) {
      let inviteArray = [
        {
          firstName: this.employeeDetails.employeeName,
          lastName: "",
          employeeId: this.employeeDetails.employeeId,
          email: null,
          phone: null,
          requestType: source,
        },
      ];
      if (source === "Phone") {
        inviteArray[0]["phone"] =
          this.employeeDetails.mobileNoCountryCode +
          this.employeeDetails.mobileNo;
      } else {
        inviteArray[0]["email"] = this.employeeDetails.empEmail;
      }
      mixpanel.track("EmpProfile-topCard-sendInvite-option-clicked");
      this.sendInvitation(inviteArray);
    },
  },
};
</script>
