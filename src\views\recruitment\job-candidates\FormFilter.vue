<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" width="500">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row
          class="my-4 mr-4"
          style="height: calc(100vh - 200px); overflow-y: scroll !important"
        >
          <v-col
            v-if="!talentPoolTab"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="statusList"
              item-title="Status"
              item-value="Id"
              label="Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="preferredLocation"
              color="primary"
              :items="locations"
              item-title="Location_Name"
              item-value="Id"
              label="Preferred Location"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-text-field
              v-model="currentLocation"
              color="primary"
              label="Current Location"
              density="compact"
              variant="solo"
            >
            </v-text-field>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="source"
              color="primary"
              :items="sourceItemsList"
              item-title="Source_Title"
              item-value="Source_Title"
              :loading="sourceListLoading"
              label="Source"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-text-field
              v-model="skills"
              label="Skills"
              density="compact"
              single-line
              variant="solo"
            >
            </v-text-field>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              color="primary"
              :items="genderList"
              v-model="gender"
              label="Gender"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col
            v-if="archiveTab"
            class="py-2"
            :cols="windowWidth > 600 ? (talentPoolTab ? 6 : 12) : 12"
          >
            <v-autocomplete
              v-model="selectedArchiveReasons"
              color="primary"
              :items="archiveReasons"
              item-title="Status"
              item-value="Id"
              label="Archive Reason"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="12">
            <div style="font-size: 1em; color: grey">
              Experience in years (Range: 0-20)
            </div>
            <v-range-slider
              v-model="experienceInYears"
              :min="0"
              :max="20"
              step="1"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="experienceInYears[0]"
                  :min="0"
                  :max="20"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="experienceInYears[1]"
                  :min="0"
                  :max="20"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
            </v-range-slider>
          </v-col>

          <v-col class="py-2" :cols="12">
            <div style="font-size: 1em; color: grey">
              Experience in months (Range: 0-11)
            </div>
            <v-range-slider
              v-model="experienceInMonths"
              :min="0"
              :max="11"
              step="1"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="experienceInMonths[0]"
                  :min="0"
                  :max="11"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="experienceInMonths[1]"
                  :min="0"
                  :max="20"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
            </v-range-slider>
          </v-col>
          <v-col class="py-2" :cols="12">
            <div style="font-size: 1em; color: grey">
              Job Match Score (Range: 0-100)
            </div>
            <v-range-slider
              v-model="percentage"
              :min="0"
              :max="100"
              step="1"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="percentage[0]"
                  :min="0"
                  :max="100"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="percentage[1]"
                  :min="0"
                  :max="100"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
            </v-range-slider>
          </v-col>
          <v-col class="py-2" :cols="12">
            <div style="font-size: 16px; color: grey">
              Expected Basic Salary (Range: 0-20000000)
            </div>
            <v-range-slider
              v-model="expectedCTC"
              :min="0"
              :max="20000000"
              step="10000"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="expectedCTC[0]"
                  :min="0"
                  :max="20000000"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="expectedCTC[1]"
                  :min="0"
                  :max="20000000"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
            </v-range-slider>
          </v-col>
          <v-col class="py-2" :cols="12">
            <div style="font-size: 1em; color: grey">
              Current Basic Salary (Range: 0-20000000)
            </div>
            <v-range-slider
              v-model="currentCTC"
              :min="0"
              :max="20000000"
              step="10000"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="currentCTC[0]"
                  :min="0"
                  :max="20000000"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="currentCTC[1]"
                  :min="0"
                  :max="20000000"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
            </v-range-slider>
          </v-col>
          <v-col class="py-2" :cols="12">
            <div style="font-size: 1em; color: grey">
              Availability to join(in days) (Range: 0-180)
            </div>
            <v-range-slider
              v-model="daysToJoin"
              :min="0"
              :max="180"
              step="1"
              strict
              thumb-label
              color="primary"
              class="ma-0"
            >
              <template v-slot:prepend>
                <v-text-field
                  v-model="daysToJoin[0]"
                  :min="0"
                  :max="180"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
              <template v-slot:append>
                <v-text-field
                  v-model="daysToJoin[1]"
                  :min="0"
                  :max="180"
                  density="compact"
                  style="width: 100px"
                  type="number"
                  variant="solo"
                  hide-details
                ></v-text-field>
              </template>
            </v-range-slider>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { LIST_SOURCE_OF_APPLICATION } from "@/graphql/recruitment/recruitmentQueries";
export default defineComponent({
  name: "FormFilter",

  emits: ["apply-filter", "reset-filter"],

  data: () => ({
    openFormFilter: false,
    status: [],
    selectedStatus: [],
    locations: [],
    preferredLocation: [],
    currentLocation: "",
    source: [],
    skills: "",
    gender: [],
    genderList: ["Male", "Female", "Other"],
    archiveReasons: [
      "Candidate Not Interested",
      "Candidate Not Reachable",
      "Not Fit for Current Need",
      "Not Qualified",
      "Not Suitable",
      "Out of Budget",
      "Over Qualified",
      "Under Qualified",
    ],
    selectedArchiveReasons: [],
    sourceItemsList: [],
    experienceInYears: [0, 20],
    experienceInMonths: [0, 11],
    currentCTC: [],
    expectedCTC: [],
    percentage: [],
    daysToJoin: [],
    sourceListLoading: false,
  }),

  props: {
    dropDown: {
      type: Object,
      default: function () {
        return {};
      },
    },
    statusList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    archiveTab: {
      default: false,
      type: Boolean,
    },
    talentPoolTab: {
      default: false,
      type: Boolean,
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.locations = this.dropDown.locations;
    this.status = this.dropDown.status.filter((el) =>
      [10, 12, 11, 19, 20, 3, 21, 22, 25, 23, 24, 26, 28].includes(el.Id)
    );
    this.retrieveSource();
  },
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        status: this.selectedStatus,
        archiveReasons: this.selectedArchiveReasons,
        preferredLocation: this.preferredLocation,
        currentLocation: this.currentLocation,
        source: this.source,
        skills: this.skills,
        gender: this.gender,
        experienceInYears: this.experienceInYears,
        experienceInMonths: this.experienceInMonths,
        currentCTC: this.currentCTC,
        expectedCTC: this.expectedCTC,
        applicantRanking: this.percentage,
        daysToJoin: this.daysToJoin,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedStatus = [];
      this.selectedArchiveReasons = [];
      this.openFormFilter = false;
      this.preferredLocation = [];
      this.currentLocation = "";
      this.source = [];
      this.skills = "";
      this.gender = [];
      this.experienceInYears = [0, 20];
      this.experienceInMonths = [0, 11];
      this.currentCTC = [];
      this.expectedCTC = [];
      this.percentage = [];
      this.daysToJoin = [];
    },
    retrieveSource() {
      let vm = this;
      vm.sourceListLoading = true;
      vm.$apollo
        .query({
          query: LIST_SOURCE_OF_APPLICATION,
          client: "apolloClientA",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveSourceMasterList &&
            !response.data.retrieveSourceMasterList.errorCode
          ) {
            const { source } = response.data.retrieveSourceMasterList;
            vm.sourceItemsList = source;
          }
          vm.sourceListLoading = false;
        })
        .catch(() => {
          vm.sourceListLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              "Something went wrong while retrieving the source list. Please try after some time.",
            type: "warning",
          };
          vm.showAlert(snackbarData);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
