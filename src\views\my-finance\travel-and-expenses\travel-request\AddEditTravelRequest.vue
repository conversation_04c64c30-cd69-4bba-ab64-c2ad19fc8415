<template>
  <v-app v-if="!isLoading">
    <v-main>
      <v-container fluid class="pa-0">
        <v-card class="full-height pa-3" style="overflow: visible">
          <v-alert v-if="displayPassportBanner" type="warning" dense>
            <template v-if="callingFrom === 'team'">
              The employee's profile is missing passport details. A valid
              passport is required for international travel. Please
              <a :href="passportUrl">update it here</a>
              to proceed.
            </template>
            <template v-else>
              Your passport details are not configured in your profile. A valid
              passport is required for international travel. Please
              <a :href="passportUrl">update it here</a>
              to proceed or contact your HR team.
            </template>
          </v-alert>

          <div
            class="d-flex ml-7 align-center pt-8 text-h6 text-grey-darken-1 font-weight-bold"
          >
            <v-progress-circular
              model-value="100"
              color="orange"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            Travel Request
          </div>

          <v-card-text class="pa-6">
            <v-form ref="travelRequestForm">
              <!-- Basic Information -->
              <v-row>
                <v-col v-if="callingFrom === 'team'" cols="12" md="4">
                  <CustomSelect
                    v-model="employeeId"
                    :items="employeeList"
                    label="Employee"
                    itemValue="Employee_Id"
                    itemTitle="Employee_Name"
                    :isRequired="true"
                    :itemSelected="employeeId"
                    :disableBreak="true"
                    :rules="[required('Employee', employeeId)]"
                    :disabled="isEdit"
                    :isLoading="employeeListLoading"
                    :isAutoComplete="true"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model.trim="tripName"
                    type="text"
                    variant="solo"
                    @update:model-value="deductFormChange()"
                    :rules="[
                      required('Travel Title', tripName),
                      tripName
                        ? validateWithRulesAndReturnMessages(
                            tripName,
                            'tripName',
                            'Travel Title'
                          )
                        : true,
                    ]"
                  >
                    <template v-slot:label>
                      <span>Travel Title</span>
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4">
                  <v-radio-group
                    v-model="travelType"
                    inline
                    @update:model-value="deductFormChange()"
                  >
                    <template v-slot:label>
                      <div>Travel Type</div>
                    </template>
                    <v-radio label="Domestic" value="Domestic"></v-radio>
                    <v-radio
                      label="International"
                      value="International"
                    ></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col
                  cols="12"
                  md="4"
                  v-if="travelType?.toLowerCase() === 'international'"
                >
                  <v-radio-group
                    v-model="visaRequired"
                    inline
                    @update:model-value="deductFormChange()"
                  >
                    <template v-slot:label>
                      <div>Is Visa required?</div>
                    </template>
                    <v-radio label="Yes" :value="1"></v-radio>
                    <v-radio label="No" :value="0"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col
                  v-if="
                    travelType?.toLowerCase() === 'international' &&
                    visaRequired === 1
                  "
                  cols="12"
                  md="4"
                >
                  <CustomSelect
                    v-model="destinationCountry"
                    :items="countries"
                    label="Destination Country for Visa"
                    itemValue="Country_Name"
                    itemTitle="Country_Name"
                    :isRequired="true"
                    :itemSelected="destinationCountry"
                    :rules="[
                      required('Destination Country', destinationCountry),
                    ]"
                    :isLoading="countryListLoading"
                    :isAutoComplete="true"
                    :disableBreak="true"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col cols="12" md="4">
                  <DatePickerField
                    v-model="startDate"
                    label="Travel Start Date"
                    :is-required="true"
                    :max-date="endDate ? endDate : null"
                    :rules="[required('Travel Start Date', startDate)]"
                    @update:model-value="deductFormChange()"
                  />
                </v-col>
                <v-col cols="12" md="4">
                  <DatePickerField
                    v-model="endDate"
                    label="Travel End Date"
                    :is-required="true"
                    :min-date="startDate ? startDate : null"
                    :rules="[required('Travel End Date', endDate)]"
                    @update:model-value="deductFormChange()"
                  />
                </v-col>
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model.trim="budgetAmount"
                    variant="solo"
                    type="number"
                    :rules="[
                      required('Budget Amount', budgetAmount),
                      validateWithRulesAndReturnMessages(
                        budgetAmount,
                        'budgetAmount',
                        'Budget Amount',
                        true
                      ),
                    ]"
                    @update:model-value="deductFormChange()"
                  >
                    <template v-slot:label>
                      Budget Amount
                      <span v-if="payrollCurrency">
                        (in {{ payrollCurrency }})</span
                      ><span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4">
                  <CustomSelect
                    v-model="seatPreference"
                    :items="seatPreferences"
                    label="Seat Preference"
                    :isRequired="true"
                    :disableBreak="true"
                    :itemSelected="seatPreference"
                    :rules="[required('Seat Preference', seatPreference)]"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col cols="12" md="4">
                  <CustomSelect
                    v-model="mealPreference"
                    :items="mealPreferences"
                    label="Meal Preference"
                    :isRequired="true"
                    :disableBreak="true"
                    :itemSelected="mealPreference"
                    :rules="[required('Meal Preference', mealPreference)]"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col cols="12" md="4">
                  <v-textarea
                    v-model.trim="businessPurpose"
                    label="Business Purpose"
                    auto-grow
                    rows="1"
                    :rules="[
                      businessPurpose
                        ? validateWithRulesAndReturnMessages(
                            businessPurpose,
                            'entityDescription',
                            'Business Purpose'
                          )
                        : true,
                    ]"
                    variant="solo"
                    @update:model-value="deductFormChange()"
                  ></v-textarea>
                </v-col>
              </v-row>

              <!-- Flight Section -->
              <v-card class="mb-6">
                <v-card-title
                  class="text-h6 bg-grey-lighten-4 py-4 px-6 d-flex align-center"
                >
                  <v-progress-circular
                    model-value="100"
                    color="light-blue-darken-1"
                    :size="22"
                    class="mr-2"
                  ></v-progress-circular>
                  Flight
                  <v-btn
                    v-if="
                      tripType?.toLowerCase() === 'multi-city' ||
                      !flights?.length
                    "
                    color="primary"
                    variant="text"
                    class="ms-auto"
                    @click="addFlight"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon>
                    Add Flight</v-btn
                  >
                </v-card-title>
                <v-card-text class="mt-4">
                  <v-radio-group
                    v-if="flights?.length"
                    v-model="tripType"
                    inline
                    class="mb-4"
                    @update:model-value="deductFormChange()"
                  >
                    <v-radio label="One Way" value="One Way"></v-radio>
                    <v-radio
                      class="mx-4"
                      label="Round Trip"
                      value="Round Trip"
                    ></v-radio>
                    <v-radio label="Multi-City" value="Multi-City"></v-radio>
                  </v-radio-group>

                  <v-expansion-panels v-model="openFlightPanels" multiple>
                    <v-expansion-panel
                      v-for="(flight, index) in flights"
                      :key="flight.id"
                    >
                      <v-expansion-panel-title>
                        <div class="d-flex align-center">
                          <span class="primary font-weight-bold mr-3">
                            Flight {{ index + 1 }}
                          </span>
                          <v-switch
                            v-model="flight.selfBooking"
                            label="Is Self Booking ?"
                            class="ml-3"
                            color="primary"
                            :true-value="1"
                            :false-value="0"
                            hide-details
                            @update:model-value="deductFormChange()"
                            @click.stop=""
                          ></v-switch>
                        </div>

                        <template v-slot:actions>
                          <v-icon
                            size="15"
                            class="fas fa-trash ml-1"
                            color="primary"
                            @click="removeFlight(index)"
                            @click.stop=""
                          />
                        </template>
                      </v-expansion-panel-title>
                      <v-expansion-panel-text>
                        <v-row>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              v-model="flight.departFrom"
                              :index="'flightDepartFrom' + index"
                              label="Depart From"
                              :isRequired="true"
                              :rules="[
                                required('Depart From', flight.departFrom),
                                validateWithRulesAndReturnMessages(
                                  flight.departFrom,
                                  'departfromArriveAt',
                                  'Depart From'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            ></GoogleAddress>
                          </v-col>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              v-model="flight.arriveAt"
                              :index="'flightArriveAt' + index"
                              label="Arrive At"
                              :isRequired="true"
                              :rules="[
                                required('Arrive At', flight.arriveAt),
                                validateWithRulesAndReturnMessages(
                                  flight.arriveAt,
                                  'departfromArriveAt',
                                  'Arrive At'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            ></GoogleAddress>
                          </v-col>
                          <v-col cols="12" md="4">
                            <CustomSelect
                              v-model="flight.airlinePreference"
                              :items="flightPreferences"
                              label="Flight Preference"
                              :isRequired="true"
                              :itemSelected="flight.airlinePreference"
                              :rules="[
                                required(
                                  'Flight Preference',
                                  flight.airlinePreference
                                ),
                              ]"
                              :disableBreak="true"
                              @update:model-value="deductFormChange()"
                            ></CustomSelect>
                          </v-col>
                          <v-col cols="12" md="4">
                            <DatePickerField
                              v-model="flight.departureDate"
                              label="Departure Date"
                              :is-required="true"
                              :max-date="
                                flight.returnDate ? flight.returnDate : null
                              "
                              :rules="[
                                required(
                                  'Departure Date',
                                  flight.departureDate
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <CustomSelect
                              v-model="flight.departureTimePreference"
                              :items="timePreferences"
                              :itemSelected="flight.departureTimePreference"
                              label="Departure Time Preference"
                              @update:model-value="deductFormChange()"
                              :disableBreak="true"
                            ></CustomSelect>
                          </v-col>
                          <v-col
                            cols="12"
                            md="4"
                            v-if="tripType?.toLowerCase() === 'round trip'"
                          >
                            <DatePickerField
                              v-model="flight.returnDate"
                              label="Return Date"
                              :min-date="
                                flight.departureDate
                                  ? flight.departureDate
                                  : null
                              "
                              :is-required="true"
                              :rules="[
                                required('Return Date', flight.returnDate),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col
                            v-if="tripType?.toLowerCase() === 'round trip'"
                            cols="12"
                            md="4"
                          >
                            <CustomSelect
                              v-model="flight.arrivalTimePreference"
                              :items="timePreferences"
                              :itemSelected="flight.arrivalTimePreference"
                              label="Return Time Preference"
                              @update:model-value="deductFormChange()"
                              :disableBreak="true"
                            ></CustomSelect>
                          </v-col>

                          <v-col cols="12" md="4">
                            <v-textarea
                              v-model.trim="flight.entityDescription"
                              label="Description"
                              auto-grow
                              rows="1"
                              :rules="[
                                flight.entityDescription
                                  ? validateWithRulesAndReturnMessages(
                                      flight.entityDescription,
                                      'entityDescription',
                                      'Description'
                                    )
                                  : true,
                              ]"
                              variant="solo"
                              @update:model-value="deductFormChange()"
                              class="mb-6"
                            ></v-textarea>
                          </v-col>
                        </v-row>
                      </v-expansion-panel-text>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-card-text>
              </v-card>

              <!-- Hotels Section -->
              <v-card class="mb-6">
                <v-card-title
                  class="text-h6 bg-grey-lighten-4 py-4 px-6 d-flex align-center"
                >
                  <v-progress-circular
                    model-value="100"
                    color="green-darken-2"
                    :size="22"
                    class="mr-2"
                  ></v-progress-circular>
                  Hotel
                  <v-btn
                    color="primary"
                    variant="text"
                    class="ms-auto"
                    @click="addHotel"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon>
                    Add Hotel</v-btn
                  >
                </v-card-title>
                <v-card-text class="mt-4">
                  <v-expansion-panels v-model="openHotelPanels" multiple>
                    <v-expansion-panel
                      v-for="(hotel, index) in hotels"
                      :key="hotel.id"
                    >
                      <v-expansion-panel-title>
                        <span class="primary font-weight-bold"
                          >Hotel {{ index + 1 }}</span
                        >
                        <v-switch
                          v-model="hotel.selfBooking"
                          label="Is Self Booking ?"
                          class="ml-3"
                          color="primary"
                          :true-value="1"
                          :false-value="0"
                          hide-details
                          @update:model-value="deductFormChange()"
                          @click.stop=""
                        ></v-switch>
                        <template v-slot:actions>
                          <v-icon
                            size="15"
                            class="fas fa-trash ml-1"
                            color="primary"
                            @click="removeHotel(index)"
                            @click.stop=""
                          />
                        </template>
                      </v-expansion-panel-title>
                      <v-expansion-panel-text>
                        <v-row>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              v-model="hotel.location"
                              :index="'hotelLocation' + index"
                              label="Location"
                              :isRequired="true"
                              :rules="[
                                required('Location', hotel.location),
                                validateWithRulesAndReturnMessages(
                                  hotel.location,
                                  'departfromArriveAt',
                                  'Location'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            ></GoogleAddress>
                          </v-col>
                          <v-col cols="12" md="4">
                            <DatePickerField
                              v-model="hotel.checkInDatetime"
                              label="Check In"
                              :max-date="hotel.checkOutDatetime"
                              :is-required="true"
                              :is-date-time="true"
                              :rules="[
                                required('Check In', hotel.checkInDatetime),
                                minMaxDateValidation(
                                  'Check In',
                                  hotel.checkInDatetime,
                                  null,
                                  hotel.checkOutDatetime
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <DatePickerField
                              v-model="hotel.checkOutDatetime"
                              label="Check Out"
                              :min-date="hotel.checkInDatetime"
                              :is-required="true"
                              :is-date-time="true"
                              :rules="[
                                required('Check Out', hotel.checkOutDatetime),
                                minMaxDateValidation(
                                  'Check Out',
                                  hotel.checkOutDatetime,
                                  hotel.checkInDatetime,
                                  null
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <CustomSelect
                              v-model="hotel.hotelPreference"
                              :items="hotelPreferences"
                              :isRequired="true"
                              :rules="[
                                required(
                                  'Hotel Preference',
                                  hotel.hotelPreference
                                ),
                              ]"
                              :itemSelected="hotel.hotelPreference"
                              label="Hotel Preference"
                              @update:model-value="deductFormChange()"
                              :disableBreak="true"
                            ></CustomSelect>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-textarea
                              v-model.trim="hotel.entityDescription"
                              label="Description"
                              auto-grow
                              rows="1"
                              variant="solo"
                              :rules="[
                                hotel.entityDescription
                                  ? validateWithRulesAndReturnMessages(
                                      hotel.entityDescription,
                                      'entityDescription',
                                      'Description'
                                    )
                                  : true,
                              ]"
                              @update:model-value="deductFormChange()"
                            ></v-textarea>
                          </v-col>
                        </v-row>
                      </v-expansion-panel-text>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-card-text>
              </v-card>

              <!-- Car Rentals Section -->
              <v-card class="mb-6">
                <v-card-title
                  class="text-h6 bg-grey-lighten-4 py-4 px-6 d-flex align-center"
                >
                  <v-progress-circular
                    model-value="100"
                    color="orange"
                    :size="22"
                    class="mr-2"
                  ></v-progress-circular>
                  Car Rental
                  <v-btn
                    color="primary"
                    variant="text"
                    class="ms-auto"
                    @click="addCarRental"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon>
                    Add Car Rental
                  </v-btn>
                </v-card-title>
                <v-card-text class="mt-4">
                  <v-expansion-panels v-model="openCarRentalPanels" multiple>
                    <v-expansion-panel
                      v-for="(car, index) in carRentals"
                      :key="car.id"
                    >
                      <v-expansion-panel-title>
                        <div class="d-flex align-center">
                          <span class="primary font-weight-bold">
                            Car Rental {{ index + 1 }}
                          </span>
                          <v-switch
                            v-model="car.selfBooking"
                            label="Is Self Booking ?"
                            color="primary"
                            :true-value="1"
                            :false-value="0"
                            hide-details
                            class="ml-2"
                            @change="deductFormChange()"
                            @click.stop=""
                          ></v-switch>
                        </div>
                        <template v-slot:actions>
                          <v-icon
                            size="15"
                            class="fas fa-trash ml-1"
                            color="primary"
                            @click="removeCarRental(index)"
                            @click.stop=""
                          />
                        </template>
                      </v-expansion-panel-title>
                      <v-expansion-panel-text>
                        <v-row>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              v-model="car.departFrom"
                              :index="'carDepartFrom' + index"
                              label="Depart From"
                              :types="['address']"
                              :isRequired="true"
                              :rules="[
                                required('Depart From', car.departFrom),
                                validateWithRulesAndReturnMessages(
                                  car.departFrom,
                                  'departfromArriveAt',
                                  'Depart From'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            ></GoogleAddress>
                          </v-col>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              v-model="car.arriveAt"
                              :index="'carArriveAt' + index"
                              :types="['address']"
                              label="Arrive At"
                              :isRequired="true"
                              :rules="[
                                required('Arrive At', car.arriveAt),
                                validateWithRulesAndReturnMessages(
                                  car.arriveAt,
                                  'departfromArriveAt',
                                  'Arrive At'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            ></GoogleAddress>
                          </v-col>
                          <v-col cols="12" md="4">
                            <DatePickerField
                              v-model="car.pickUpDateTime"
                              label="Pick-up Date"
                              :max-date="car.dropDateTime"
                              :is-required="true"
                              :is-date-time="true"
                              :rules="[
                                required('Pick-up Date', car.pickUpDateTime),
                                minMaxDateValidation(
                                  'Pick-up Date',
                                  car.pickUpDateTime,
                                  null,
                                  car.dropDateTime
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <DatePickerField
                              v-model="car.dropDateTime"
                              label="Drop-off Date"
                              :min-date="car.pickUpDateTime"
                              :is-required="true"
                              :is-date-time="true"
                              :rules="[
                                required('Drop-off Date', car.dropDateTime),
                                minMaxDateValidation(
                                  'Drop-off Date',
                                  car.dropDateTime,
                                  car.pickUpDateTime,
                                  null
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <CustomSelect
                              v-model="car.carType"
                              :items="carTypes"
                              label="Car Type"
                              :isRequired="true"
                              :itemSelected="car.carType"
                              :rules="[required('Car Type', car.carType)]"
                              @update:model-value="deductFormChange()"
                              :disableBreak="true"
                            ></CustomSelect>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-radio-group
                              v-model="car.driverNeeded"
                              inline
                              @change="deductFormChange()"
                            >
                              <template v-slot:label>
                                <div>Driver Needed</div>
                              </template>
                              <v-radio label="Yes" :value="1"></v-radio>
                              <v-radio label="No" :value="0"></v-radio>
                            </v-radio-group>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-textarea
                              v-model.trim="car.entityDescription"
                              label="Description"
                              auto-grow
                              rows="1"
                              :rules="[
                                car.entityDescription
                                  ? validateWithRulesAndReturnMessages(
                                      car.entityDescription,
                                      'entityDescription',
                                      'Description'
                                    )
                                  : true,
                              ]"
                              variant="solo"
                              @update:model-value="deductFormChange()"
                            ></v-textarea>
                          </v-col>
                        </v-row>
                      </v-expansion-panel-text>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-card-text>
              </v-card>

              <!-- Buses Section -->
              <v-card class="mb-6">
                <v-card-title
                  class="text-h6 bg-grey-lighten-4 py-4 px-6 d-flex align-center"
                >
                  <v-progress-circular
                    model-value="100"
                    color="light-blue-darken-1"
                    :size="22"
                    class="mr-2"
                  ></v-progress-circular>
                  Bus
                  <v-btn
                    color="primary"
                    variant="text"
                    class="ms-auto"
                    @click="addBus"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon>
                    Add Bus
                  </v-btn>
                </v-card-title>
                <v-card-text class="mt-4">
                  <v-expansion-panels v-model="openBusPanels" multiple>
                    <v-expansion-panel
                      v-for="(bus, index) in buses"
                      :key="bus.id"
                    >
                      <v-expansion-panel-title>
                        <div class="d-flex align-center">
                          <span class="primary font-weight-bold">
                            Bus {{ index + 1 }}
                          </span>
                          <v-switch
                            v-model="bus.selfBooking"
                            label="Is Self Booking ?"
                            color="primary"
                            :true-value="1"
                            :false-value="0"
                            hide-details
                            class="ml-3"
                            @change="deductFormChange()"
                            @click.stop=""
                          ></v-switch>
                        </div>
                        <template v-slot:actions>
                          <v-icon
                            size="15"
                            class="fas fa-trash ml-1"
                            color="primary"
                            @click="removeBus(index)"
                            @click.stop=""
                          />
                        </template>
                      </v-expansion-panel-title>
                      <v-expansion-panel-text>
                        <v-row>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              :index="'busDepartFrom' + index"
                              v-model="bus.departFrom"
                              :items="cities"
                              label="Depart From"
                              :isRequired="true"
                              :rules="[
                                required('Depart From', bus.departFrom),
                                validateWithRulesAndReturnMessages(
                                  bus.departFrom,
                                  'departfromArriveAt',
                                  'Depart From'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            ></GoogleAddress>
                          </v-col>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              v-model="bus.arriveAt"
                              :index="'busArriveAt' + index"
                              label="Arrive At"
                              :isRequired="true"
                              :rules="[
                                required('Arrive At', bus.arriveAt),
                                validateWithRulesAndReturnMessages(
                                  bus.arriveAt,
                                  'departfromArriveAt',
                                  'Arrive At'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            ></GoogleAddress>
                          </v-col>
                          <v-col cols="12" md="4">
                            <DatePickerField
                              v-model="bus.departureDate"
                              label="Departure Date"
                              :is-required="true"
                              :rules="[
                                required('Departure Date', bus.departureDate),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <CustomSelect
                              v-model="bus.timePreference"
                              :items="timePreferences"
                              label="Time Preference"
                              :isRequired="false"
                              :itemSelected="bus.timePreference"
                              @update:model-value="deductFormChange()"
                              :disableBreak="true"
                            ></CustomSelect>
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-textarea
                              v-model.trim="bus.entityDescription"
                              label="Description"
                              auto-grow
                              rows="1"
                              variant="solo"
                              :rules="[
                                bus.entityDescription
                                  ? validateWithRulesAndReturnMessages(
                                      bus.entityDescription,
                                      'entityDescription',
                                      'Description'
                                    )
                                  : true,
                              ]"
                              @update:model-value="deductFormChange()"
                            ></v-textarea>
                          </v-col>
                        </v-row>
                      </v-expansion-panel-text>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-card-text>
              </v-card>

              <!-- Trains Section -->
              <v-card class="mb-6">
                <v-card-title
                  class="text-h6 bg-grey-lighten-4 py-4 px-6 d-flex align-center"
                >
                  <v-progress-circular
                    model-value="100"
                    color="green-darken-2"
                    :size="22"
                    class="mr-2"
                  ></v-progress-circular>
                  Train
                  <v-btn
                    color="primary"
                    variant="text"
                    class="ms-auto"
                    @click="addTrain"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon>
                    Add Train
                  </v-btn>
                </v-card-title>
                <v-card-text class="mt-4">
                  <v-expansion-panels v-model="openTrainPanels" multiple>
                    <v-expansion-panel
                      v-for="(train, index) in trains"
                      :key="train.id"
                    >
                      <v-expansion-panel-title>
                        <div class="d-flex align-center">
                          <span class="primary font-weight-bold">
                            Train {{ index + 1 }}
                          </span>
                          <v-switch
                            v-model="train.selfBooking"
                            label="Is Self Booking ?"
                            class="ml-3"
                            color="primary"
                            :true-value="1"
                            :false-value="0"
                            hide-details
                            @update:model-value="deductFormChange()"
                            @click.stop=""
                          ></v-switch>
                        </div>
                        <template v-slot:actions>
                          <v-icon
                            size="15"
                            class="fas fa-trash ml-1"
                            color="primary"
                            @click="removeTrain(index)"
                            @click.stop=""
                          />
                        </template>
                      </v-expansion-panel-title>
                      <v-expansion-panel-text>
                        <v-row>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              :key="'trainDepartFrom' + index"
                              v-model="train.departFrom"
                              :index="'trainDepartFrom' + index"
                              label="Depart From"
                              :isRequired="true"
                              :rules="[
                                required('Depart From', train.departFrom),
                                validateWithRulesAndReturnMessages(
                                  train.departFrom,
                                  'departfromArriveAt',
                                  'Depart From'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <GoogleAddress
                              :key="'trainArriveAt' + index"
                              v-model="train.arriveAt"
                              :index="'trainArriveAt' + index"
                              label="Arrive At"
                              :isRequired="true"
                              :rules="[
                                required('Arrive At', train.arriveAt),
                                validateWithRulesAndReturnMessages(
                                  train.arriveAt,
                                  'departfromArriveAt',
                                  'Arrive At'
                                ),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <DatePickerField
                              v-model="train.departureDate"
                              label="Departure Date"
                              :is-required="true"
                              :rules="[
                                required('Departure Date', train.departureDate),
                              ]"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <CustomSelect
                              v-model="train.timePreference"
                              label="Time Preference"
                              :items="timePreferences"
                              :isRequired="false"
                              :itemSelected="train.timePreference"
                              @update:model-value="deductFormChange()"
                            />
                          </v-col>
                          <v-col cols="12" md="4">
                            <v-textarea
                              v-model.trim="train.entityDescription"
                              label="Description"
                              auto-grow
                              rows="1"
                              variant="solo"
                              :rules="[
                                train.entityDescription
                                  ? validateWithRulesAndReturnMessages(
                                      train.entityDescription,
                                      'entityDescription',
                                      'Description'
                                    )
                                  : true,
                              ]"
                              @update:model-value="deductFormChange()"
                            ></v-textarea>
                          </v-col>
                        </v-row>
                      </v-expansion-panel-text>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-card-text>
              </v-card>
            </v-form>
          </v-card-text>
        </v-card>
      </v-container>
    </v-main>
  </v-app>
  <v-bottom-navigation name="'add-edit-travel-request'">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2 pt-1">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="onCloseEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4 pt-1">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="saveAndSubmit()"
          >
            <span class="primary">Submit</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  >
  </AppWarningModal>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    :timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="mt-n5 primary"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading" />
</template>

<script>
import { defineComponent } from "vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import DatePickerField from "@/components/custom-components/DatePickerField.vue";
import {
  ADD_UPDATE_TRAVEL_REQUEST,
  RETRIEVE_TRAVEL_REQUEST,
  RETRIEVE_EMP_PASSPORT_INFO,
} from "@/graphql/employee-self-service/travelRequestQueries.js";
import validationRules from "@/mixins/validationRules";
import { LIST_CITIES } from "@/graphql/dropDownQueries.js";
import GoogleAddress from "@/components/custom-components/GoogleAddress.vue";
import moment from "moment";

export default defineComponent({
  name: "AddEditEmployeeTravel",
  mixins: [validationRules],
  components: {
    CustomSelect,
    DatePickerField,
    GoogleAddress,
  },
  emits: ["close-form", "refetch-data"],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    travelRequest: {
      type: Object,
      required: false,
    },
    formId: {
      type: Number,
      required: true,
    },
    travelCustomizedFormName: {
      type: String,
      default: "Travel Request",
    },
    callingFrom: {
      type: String,
      default: "employee",
    },
  },
  data() {
    return {
      requestId: null,
      employeeId: null,
      tripName: null,
      travelType: "Domestic",
      budgetAmount: null,
      destinationCountry: null,
      visaRequired: 0,
      businessPurpose: "",
      tripType: "One Way",
      seatPreference: "No Preference",
      mealPreference: "No Preference",
      startDate: null,
      endDate: null,
      isLoading: false,
      flights: [],
      hotels: [],
      carRentals: [],
      buses: [],
      trains: [],

      countries: [],

      cities: [],

      timePreferences: [
        "12 AM - 8 AM",
        "8 AM - 12 PM",
        "12 PM - 8 PM",
        "8 PM - 12 AM",
      ],

      hotelPreferences: [
        "No Preference",
        "Standard Room",
        "Deluxe Room",
        "Suite",
        "Single Bed",
        "Double Bed",
        "King Bed",
        "Queen Bed",
        "Sea View",
        "City View",
        "Garden View",
        "Pool View",
        "Breakfast Included",
        "Half Board (Breakfast & Dinner)",
        "Full Board (All Meals)",
        "Early Check-in Request",
        "Late Check-out Request",
        "Accessible Room",
      ],

      carTypes: [
        "Small",
        "Medium",
        "Large",
        "Estate",
        "Premium",
        "People Carrier",
        "SUV",
      ],

      employeeList: [],

      seatPreferences: ["No Preference", "Window", "Aisle", "Middle"],

      mealPreferences: [
        "No Preference",
        "Asian Vegetarian Meal",
        "Vegetarian Meal (non-diary)",
        "Vegetarian Meal (lacto-ovo)",
        "Vegetarian Jain Meal",
        "Baby Meal",
        "Child Meal",
        "Diabetic Meal",
        "Bland Meal",
        "Fruit Platter",
        "Kosher",
        "Gluten-free Meal",
        "Hindu (Non-Vegetarian) Meal",
        "Kosher Meal",
        "Muslim Meal",
        "Low Cholesterol / Low Fat Meal",
        "Low Sodium Meal (no salt)",
        "Non-Lactose Meal",
        "Low Calories Meal",
      ],

      flightPreferences: [
        "No Preference",
        "Economy",
        "Premium Economy",
        "Business",
        "First",
      ],

      countryListLoading: false,
      cityListLoading: false,
      employeeListLoading: false,
      isFormDirty: false,
      openFlightPanels: [],
      openHotelPanels: [],
      openCarRentalPanels: [],
      openBusPanels: [],
      openTrainPanels: [],
      validationMessages: [],
      showValidationAlert: false,
      openConfirmationPopup: false,
      displayPassportBanner: false,
    };
  },
  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    passportUrl() {
      if (this.callingFrom === "team") {
        let encodedEmployeeId = btoa(this.employeeId);
        return `${this.baseUrl}v3/my-team/team-summary/${encodedEmployeeId}/Personal_Info`;
      }
      return `${this.baseUrl}v3/employee-profile`;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.retrieveCountries();
    this.retrieveCities();
    this.getEmpList();
    let travelRequestId = this.travelRequest?.requestId;
    if (this.isEdit && travelRequestId) {
      this.retrieveTravelRequestData(travelRequestId);
    }
  },
  methods: {
    getPersonalDetails(employeeId) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_PASSPORT_INFO,
          client: "apolloClientAC",
          variables: {
            employeeId: employeeId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response && response.data && response.data.retrievePersonalInfo) {
            let { passportDetails } = response.data.retrievePersonalInfo;
            passportDetails = JSON.parse(passportDetails);
            if (passportDetails && passportDetails.length > 0) {
              vm.displayPassportBanner = false;
            } else {
              vm.displayPassportBanner = true;
            }
          } else {
            vm.displayPassportBanner = false;
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.displayPassportBanner = false;
          vm.isLoading = false;
        });
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },
    async getEmpList() {
      let formId = this.formId;
      this.employeeListLoading = true;
      const empData = await this.$store.dispatch("getEmployeesList", {
        formName: "Attendance",
        formId: formId,
        flag: "payslipreq",
      });

      if (empData && empData.length) {
        // Map and filter the data to create itemList and originalList
        const processedData = empData.map((item) => ({
          Employee_Name: item.employeeName + " - " + item.userDefinedEmpId,
          Employee_Id: item.employeeId,
          Status: item.empStatus,
        }));

        this.employeeList = processedData.filter(
          (el) => el.Status === "Active"
        );

        //If the callingFrom is employee, filter by employeeId and select the employee
        if (this.callingFrom === "employee") {
          this.employeeList = this.employeeList.filter(
            (el) => el.Employee_Id === this.loginEmployeeId
          );
          this.employeeId = this.loginEmployeeId;
        }
      } else {
        this.employeeList = [];
      }
      this.employeeListLoading = false;
    },
    retrieveTravelRequestData(requestId) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_TRAVEL_REQUEST,
          client: "apolloClientAC",
          variables: {
            requestId: requestId,
            formId: this.formId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveEmployeeTravel &&
            response.data.retrieveEmployeeTravel.travelDetails
          ) {
            this.prefillFormData(
              response.data.retrieveEmployeeTravel.travelDetails
            );
            vm.isLoading = false;
          } else {
            vm.handleRetrieveError(
              (err = ""),
              this.travelCustomizedFormName?.toLowerCase()
            );
          }
        })
        .catch((err) => {
          vm.handleRetrieveError(
            err,
            this.travelCustomizedFormName?.toLowerCase()
          );
        });
    },
    handleRetrieveError(err = "", formName) {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
        });
    },
    prefillFormData(travelData) {
      this.requestId = travelData.tripId;
      this.employeeId = travelData.employeeId;
      this.tripName = travelData.tripName;
      this.startDate =
        travelData.travelStartDate &&
        travelData.travelStartDate !== "0000-00-00"
          ? travelData.travelStartDate
          : null;
      this.endDate =
        travelData.travelEndDate && travelData.travelEndDate !== "0000-00-00"
          ? travelData.travelEndDate
          : null;
      this.travelType = travelData.travelType
        ? travelData.travelType
        : "Domestic";
      this.budgetAmount = travelData.budgetAmount;
      this.destinationCountry = travelData.destinationCountry;
      this.visaRequired = travelData.visaRequired ? 1 : 0;
      this.mealPreference = travelData.mealPreference;
      this.seatPreference = travelData.seatPreference;
      this.businessPurpose = travelData.businessPurpose;
      this.tripType = travelData.tripType;
      this.flights =
        travelData.flightDetails && travelData.flightDetails?.length
          ? travelData.flightDetails
          : [];
      this.hotels =
        travelData.hotelStays && travelData.hotelStays?.length
          ? travelData.hotelStays
          : [];
      this.carRentals =
        travelData.carDetails && travelData.carDetails?.length
          ? travelData.carDetails
          : [];
      this.buses =
        travelData.busDetails && travelData.busDetails?.length
          ? travelData.busDetails
          : [];
      this.trains =
        travelData.trainDetails && travelData.trainDetails?.length
          ? travelData.trainDetails
          : [];
    },
    deductFormChange() {
      this.isFormDirty = true;
    },
    retrieveCountries() {
      this.countryListLoading = true;
      this.countries = [];
      this.$store
        .dispatch("listCountries")
        .then((langList) => {
          this.countries = langList;
          this.countryListLoading = false;
        })
        .catch(() => {
          this.countryListLoading = false;
        });
    },
    retrieveCities() {
      let vm = this;
      vm.cityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES,
          client: "apolloClientAC",
          variables: {
            Form_Id: 243,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            !response.data.getCityListWithState.errorCode
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.cities = cityDetails;
          }
          vm.cityListLoading = false;
        })
        .catch(() => {
          vm.cityListLoading = false;
        });
    },
    addFlight() {
      this.flights.push({
        departFrom: "",
        arriveAt: "",
        departureDate: "",
        airlinePreference: "No Preference",
        returnDate: "",
        departureTimePreference: "",
        arrivalTimePreference: "",
        entityDescription: "",
        selfBooking: 0,
      });
    },
    removeFlight(index) {
      this.flights.splice(index, 1);
    },
    addHotel() {
      this.hotels.push({
        location: "",
        checkInDatetime: "",
        checkOutDatetime: "",
        hotelPreference: "No Preference",
        entityDescription: "",
        selfBooking: 0,
      });
    },
    removeHotel(index) {
      this.hotels.splice(index, 1);
    },
    addCarRental() {
      this.carRentals.push({
        departFrom: "",
        arriveAt: "",
        pickUpDateTime: "",
        dropDateTime: "",
        carType: "",
        driverNeeded: 1,
        entityDescription: "",
        selfBooking: 0,
      });
    },
    removeCarRental(index) {
      this.carRentals.splice(index, 1);
    },
    addBus() {
      this.buses.push({
        departFrom: "",
        arriveAt: "",
        departureDate: "",
        timePreference: "",
        entityDescription: "",
        selfBooking: 0,
      });
    },
    removeBus(index) {
      this.buses.splice(index, 1);
    },
    addTrain() {
      this.trains.push({
        departFrom: "",
        arriveAt: "",
        departureDate: "",
        timePreference: "",
        entityDescription: "",
        selfBooking: 0,
      });
    },
    removeTrain(index) {
      this.trains.splice(index, 1);
    },
    saveAndSubmit() {
      this.validateEditForm();
    },
    async validateEditForm() {
      // Validate if all dates are within the main start and end dates
      const validateDatesWithinRange = () => {
        const errors = [];

        // Check flights
        this.flights?.forEach((flight, index) => {
          if (
            flight.departureDate &&
            !moment(flight.departureDate).isBetween(
              this.startDate + " 00:00:00",
              this.endDate + " 23:59:59",
              null,
              "[]"
            )
          ) {
            errors.push(
              `Flight ${
                index + 1
              }: Departure date must be between overall start and end date`
            );
          }
          if (
            flight.returnDate &&
            !moment(flight.returnDate).isBetween(
              this.startDate + " 00:00:00",
              this.endDate + " 23:59:59",
              null,
              "[]"
            )
          ) {
            errors.push(
              `Flight ${
                index + 1
              }: Return date must be between overall start and end date`
            );
          }
        });

        // Check hotels
        this.hotels?.forEach((hotel, index) => {
          if (
            hotel.checkInDatetime &&
            !moment(hotel.checkInDatetime).isBetween(
              this.startDate + " 00:00:00",
              this.endDate + " 23:59:59",
              null,
              "[]"
            )
          ) {
            errors.push(
              `Hotel ${
                index + 1
              }: Check-in date must be between overall start and end date`
            );
          }
          if (
            hotel.checkOutDatetime &&
            !moment(hotel.checkOutDatetime).isBetween(
              this.startDate + " 00:00:00",
              this.endDate + " 23:59:59",
              null,
              "[]"
            )
          ) {
            errors.push(
              `Hotel ${
                index + 1
              }: Check-out date must be between overall start and end date`
            );
          }
        });

        // Check car rentals
        this.carRentals?.forEach((car, index) => {
          if (
            car.pickUpDateTime &&
            !moment(car.pickUpDateTime).isBetween(
              this.startDate + " 00:00:00",
              this.endDate + " 23:59:59",
              null,
              "[]"
            )
          ) {
            errors.push(
              `Car Rental ${
                index + 1
              }: Pick-up date must be between overall start and end date`
            );
          }
          if (
            car.dropDateTime &&
            !moment(car.dropDateTime).isBetween(
              this.startDate + " 00:00:00",
              this.endDate + " 23:59:59",
              null,
              "[]"
            )
          ) {
            errors.push(
              `Car Rental ${
                index + 1
              }: Drop-off date must be between overall start and end date`
            );
          }
        });

        // Check buses
        this.buses?.forEach((bus, index) => {
          if (
            bus.departureDate &&
            !moment(bus.departureDate).isBetween(
              this.startDate + " 00:00:00",
              this.endDate + " 23:59:59",
              null,
              "[]"
            )
          ) {
            errors.push(
              `Bus ${
                index + 1
              }: Departure date must be between overall start and end date`
            );
          }
        });

        // Check trains
        this.trains?.forEach((train, index) => {
          if (
            train.departureDate &&
            !moment(train.departureDate).isBetween(
              this.startDate + " 00:00:00",
              this.endDate + " 23:59:59",
              null,
              "[]"
            )
          ) {
            errors.push(
              `Train ${
                index + 1
              }: Departure date must be between overall start and end date`
            );
          }
        });

        return errors;
      };

      const dateRangeErrors = validateDatesWithinRange();
      if (dateRangeErrors.length > 0) {
        this.validationMessages = dateRangeErrors;
        this.showValidationAlert = true;
        return;
      }

      let isFormValid = await this.$refs.travelRequestForm.validate();
      if (!isFormValid.valid && isFormValid.errors.length > 0) {
        // Validate If flightPanel is open
        if (!this.openFlightPanels?.length > 0 && this.flights?.length > 0) {
          this.openFlightPanels = this.flights.map((_, index) => index);
        }

        // Validate If hotelPanel is open
        if (!this.openHotelPanels?.length > 0 && this.hotels?.length > 0) {
          this.openHotelPanels = this.hotels.map((_, index) => index);
        }

        // Validate If carRentalPanel is open
        if (
          !this.openCarRentalPanels?.length > 0 &&
          this.carRentals?.length > 0
        ) {
          this.openCarRentalPanels = this.carRentals.map((_, index) => index);
        }

        // Validate If busPanel is open
        if (!this.openBusPanels?.length > 0 && this.buses?.length > 0) {
          this.openBusPanels = this.buses.map((_, index) => index);
        }

        // Validate If trainPanel is open
        if (!this.openTrainPanels?.length > 0 && this.trains?.length > 0) {
          this.openTrainPanels = this.trains.map((_, index) => index);
        }

        //Validate the form again for the panels
        await this.$refs.travelRequestForm.validate();

        // Get the first invalid input ID
        const firstErrorId = isFormValid.errors[0].id;

        // Use Vue's nextTick to ensure DOM updates before focusing
        this.$nextTick(() => {
          const inputElement = document.getElementById(firstErrorId);
          if (inputElement) {
            inputElement.focus();
          }
        });
      } else {
        //Form the data
        let data = {
          formId: this.formId,
          requestId: this.requestId ? this.requestId : null,
          employeeId: this.employeeId,
          tripName: this.tripName,
          travelType: this.travelType,
          mealPreference: this.mealPreference,
          seatPreference: this.seatPreference,
          businessPurpose: this.businessPurpose,
          tripType: this.tripType,
          budgetAmount: parseFloat(this.budgetAmount),
          visaRequired:
            this.visaRequired && this.travelType?.toLowerCase() !== "domestic"
              ? 1
              : 0,
          destinationCountry:
            this.visaRequired &&
            this.travelType?.toLowerCase() === "international"
              ? this.destinationCountry
              : null,
          flightDetails: this.flights,
          hotelStays: this.hotels,
          carDetails: this.carRentals,
          busDetails: this.buses,
          trainDetails: this.trains,
          status: "Applied",
          travelStartDate: this.startDate,
          travelEndDate: this.endDate,
        };
        this.addUpdateTravelRequest(data);
      }
    },
    addUpdateTravelRequest(travelRequest) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_TRAVEL_REQUEST,
          variables: travelRequest,
          client: "apolloClientAD",
        })
        .then(() => {
          vm.isLoading = false;
          vm.showAlert({
            isOpen: true,
            message: `${this.travelCustomizedFormName} ${
              this.isEdit ? "updated" : "added"
            } successfully`,
            type: "success",
          });
          vm.$emit("refetch-data");
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.travelCustomizedFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
  watch: {
    tripType(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (
          this.newValue?.toLowerCase() !== "multi-city" &&
          this.flights?.length > 1
        ) {
          //Keep only the first flight
          this.flights = [this.flights[0]];
        }
      }
    },
    employeeId(newValue, oldValue) {
      if (
        newValue !== oldValue &&
        this.travelType?.toLowerCase() === "international" &&
        newValue
      ) {
        //Get the passport details
        this.getPersonalDetails(newValue);
      }
    },
    travelType(newValue, oldValue) {
      if (
        newValue !== oldValue &&
        newValue?.toLowerCase() === "international" &&
        this.employeeId
      ) {
        //Get the passport details
        this.getPersonalDetails(this.employeeId);
      } else if (newValue?.toLowerCase() === "domestic") {
        this.displayPassportBanner = false;
      }
    },
    flights: {
      handler(newFlights) {
        this.openFlightPanels = newFlights.map((_, index) => index);
      },
      deep: true,
      immediate: true,
    },
    hotels: {
      handler(newHotels) {
        this.openHotelPanels = newHotels.map((_, index) => index);
      },
      deep: true,
      immediate: true,
    },
    carRentals: {
      handler(newCarRentals) {
        this.openCarRentalPanels = newCarRentals.map((_, index) => index);
      },
      deep: true,
      immediate: true,
    },
    buses: {
      handler(newBuses) {
        this.openBusPanels = newBuses.map((_, index) => index);
      },
      deep: true,
      immediate: true,
    },
    trains: {
      handler(newTrains) {
        this.openTrainPanels = newTrains.map((_, index) => index);
      },
      deep: true,
      immediate: true,
    },
  },
});
</script>

<style scoped>
.full-height {
  min-height: 100vh;
}

.v-card-title {
  color: #1867c0;
}

.google-auto-complete-address-field {
  width: 100% !important;
  border: none !important;
}
.google-auto-complete-address-field:focus {
  outline: none !important;
}

.google-auto-complete {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: text;
}
</style>
