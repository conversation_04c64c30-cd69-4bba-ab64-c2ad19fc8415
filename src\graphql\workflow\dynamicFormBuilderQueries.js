import gql from "graphql-tag";
// ===============
// Queries
// ===============

export const LIST_DYNAMIC_FORM = gql`
  mutation GetAllDynamicFormTemplate($filter: Filter!) {
    getAllDynamicFormTemplate(filter: $filter) {
      error {
        code
        message
      }
      result {
        templateId
        templateName
        status
        addedOn
        addedByUserId
        addedByUserName
        updatedOn
        updatedByUserId
        updatedByUserName
        conversational
        formFor
        required
      }
    }
  }
`;

export const RETRIEVE_DYNAMIC_FORM = gql`
  mutation getDynamicFormTemplate($templateId: Int!) {
    getDynamicFormTemplate(templateId: $templateId) {
      error {
        code
        message
      }
      result {
        templateId
        templateName
        status
        addedOn
        formTemplate
        formFor
        addedByUserId
        addedByUserName
        updatedOn
        updatedByUserId
        updatedByUserName
        conversational
      }
    }
  }
`;

export const CHECK_ALREADY_EXISTS = gql`
  mutation checkTemplateNameAlreadyExist($templateName: String!) {
    checkTemplateNameAlreadyExist(templateName: $templateName) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          field
          problem
        }
      }
    }
  }
`;

export const ADD_UPDATE_DYNAMIC_FORM = gql`
  mutation updateDynamicFormTemplate(
    $templateId: Int
    $dynamicForm: DynamicForm!
  ) {
    updateDynamicFormTemplate(
      templateId: $templateId
      dynamicForm: $dynamicForm
    ) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          field
          problem
        }
      }
    }
  }
`;

export const DELETE_DYNAMIC_FORM = gql`
  mutation deleteDynamicFormTemplate($templateId: Int!) {
    deleteDynamicFormTemplate(templateId: $templateId) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          field
          problem
        }
      }
    }
  }
`;
