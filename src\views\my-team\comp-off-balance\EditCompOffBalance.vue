<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl text-white"
            style="background-color: rgb(var(--v-theme-primary)) !important"
            size="large"
            label
          >
            {{ "Edit" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">Comp Off Balance</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              color="primary"
              class="mb-2"
              @click="validateForm"
              >Save</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Save</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" color="primary" class="mr-1">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="height: calc(100vh - 260px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="compOffBalanceForm">
            <v-row>
              <v-col cols="12" sm="6" md="6">
                <p class="custom-label mt-n3">
                  Expiry Date<span style="color: red">*</span>
                </p>
                <datepicker
                  :format="orgDateFormat"
                  v-model="expiryDates"
                  placeholder="Expiry Date*"
                  style="min-width: 300px !important"
                  :open-date="new Date(expiryDates)"
                  :disabled-dates="{
                    to: new Date(expiryDateValidation),
                    from: new Date(exitDateValidation),
                  }"
                  @input="onChangeDateFields()"
                ></datepicker>
                <div
                  v-if="expiryDatesErrorMsg"
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ !this.expiryDates ? expiryDatesErrorMsg : "" }}
                </div>
              </v-col>
              <v-col>
                <v-text-field
                  v-model="remainingDay"
                  :min="0"
                  :max="remainingDays - appliedDate"
                  variant="solo"
                  :rules="[
                    numericRequiredValidation('Remaining Days', remainingDay),
                    validateWithRulesAndReturnMessages(
                      remainingDay,
                      'remainingDays',
                      'Remaining Days',
                      true,
                      remainingDays - appliedDate
                    ),
                  ]"
                  :disabled="
                    remainingDays - appliedDate == 0 || remainingDays == 0
                  "
                  @update:model-value="validateInput('remainingDay')"
                  style="max-width: 300px !important"
                >
                  <template v-slot:label>
                    <span>Remaining Days</span>
                    <span class="ml-1" style="color: red">*</span>
                  </template>
                </v-text-field>
                <NotesCard
                  v-if="remainingDays - appliedDate == 0 || remainingDays == 0"
                  notes="Currently, there is no comp off balance available, and as a result, there are no remaining days to update."
                  class="mb-4"
                ></NotesCard>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
// Queries
import { UPDATE_COMPENSATORY_OFF_BALANCE } from "@/graphql/my-team/comp-off-balance/compOffBalanceQueries.js";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
export default {
  name: "EditCompOffBalance",
  mixins: [validationRules],
  props: {
    selectedItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    accessFormName: {
      type: String,
      required: true,
    },
  },
  components: {
    Datepicker,
    NotesCard,
  },
  emits: ["close-form", "refetch-data"],
  data() {
    return {
      isMounted: false,
      isLoadingDetails: false,
      openConfirmationPopup: false,
      isFormDirty: false,
      validationMessages: [],
      showValidationAlert: false,
      description: "",
      billable: "No",
      compOffBalanceId: null,
      workedDates: null,
      expiryDates: null,
      remainingDays: 0,
      appliedDate: 0,
      remainingDay: null,
      exitDate: null,
      expiryDatesErrorMsg: "",
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    exitDateValidation() {
      if (this.exitDate) {
        return new Date(this.exitDate).toISOString().substring(0, 10);
      } else {
        return "";
      }
    },
    expiryDateValidation() {
      if (this.workedDates) {
        const originalDate = new Date(this.workedDates);
        originalDate.setDate(originalDate.getDate() + 1);
        return originalDate.toISOString().substring(0, 10);
      } else {
        return null;
      }
    },
  },
  mounted() {
    if (this.isEdit) {
      const {
        Comp_Off_Balance_Id,
        Worked_Date,
        Expiry_Date,
        Remaining_Days,
        Total_Applied_Returned_Days,
        Resignation_Date,
      } = this.selectedItem;
      this.compOffBalanceId = Comp_Off_Balance_Id ? Comp_Off_Balance_Id : null;
      this.workedDates = Worked_Date ? Worked_Date : null;
      this.expiryDates = Expiry_Date ? Expiry_Date : null;
      this.remainingDay = Remaining_Days ? Remaining_Days : 0;
      this.remainingDays = Remaining_Days ? Remaining_Days : 0;
      this.appliedDate = Total_Applied_Returned_Days
        ? Total_Applied_Returned_Days.split(",").reduce(
            (acc, num) => acc + parseFloat(num),
            0
          )
        : 0;
      this.exitDate = Resignation_Date ? Resignation_Date : "";
    }
    this.isFormDirty = false;
    this.isMounted = true;
  },
  methods: {
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
    },
    onChangeDateFields() {
      this.isFormDirty = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },
    async validateForm() {
      const { valid } = await this.$refs.compOffBalanceForm.validate();
      if (valid && this.expiryDates) {
        this.addUpdateData();
      } else {
        this.expiryDatesErrorMsg = "Expiry Date is required";
      }
    },
    addUpdateData() {
      let vm = this;
      vm.isLoadingDetails = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_COMPENSATORY_OFF_BALANCE,
            variables: {
              compOffBalanceId: vm.compOffBalanceId
                ? parseInt(vm.compOffBalanceId)
                : 0,
              remainingDays: parseFloat(vm.remainingDay),
              expiryDate: moment(vm.expiryDates).format("YYYY-MM-DD"),
            },
            client: "apolloClientAD",
          })
          .then(() => {
            vm.isLoadingDetails = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `${this.accessFormName} updated successfully.`,
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.isLoadingDetails = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: this.accessFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
