<template>
  <v-card
    class="mx-auto my-3 mr-3 ml-1 list-action-card"
    :style="
      !isClickable ? 'cursor: auto;' : 'cursor: pointer;' + cardProperty.style
    "
    min-height="50"
    @click="isClickable ? $emit('action-triggered', listIndex) : {}"
  >
    <v-list :style="cardProperty.style">
      <v-list-item :style="cardProperty.style">
        <v-list-item-title class="d-flex align-center">
          <div class="d-flex flex-column list-action-avatar">
            <v-icon :color="cardProperty.color" style="font-size: 20px">{{
              iconName
            }}</v-icon>
          </div>
          <div
            class="text-primary font-weight-bold pt-1 text-body-2"
            :class="
              calledFrom === 'notification'
                ? 'notification-class text-caption font-weight-bold'
                : ''
            "
          >
            {{ title }}
          </div>
        </v-list-item-title>
      </v-list-item>
    </v-list>
  </v-card>
</template>

<script>
export default {
  name: "ListActionCard",
  props: {
    // returns background(card), icon(icon-color)
    cardProperty: {
      type: Object,
      default: function () {
        return {
          style: "#FFFFFF",
          color: "primary",
        };
      },
    },
    // return icon name to show in left side of card
    iconName: {
      type: String,
      default: "",
    },
    // title of the card
    title: {
      type: String,
      default: "",
    },
    // unique key and also this value is emitted when the card clicked to find which one is clicked
    listIndex: {
      type: Number,
      required: true,
    },
    // used to check the card is clickable or not
    isClickable: {
      type: Boolean,
      default: false,
    },
    // for notification, we show the text without truncating. So this is returned only from notifications
    calledFrom: {
      type: String,
      default: "",
    },
  },

  computed: {
    // width of current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // change the avatar size based on window size to avoid responsive issues
    avatarSize() {
      if (this.windowWidth <= 600) {
        return "40";
      } else if (this.windowWidth <= 400) {
        return "30";
      } else {
        return "45";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.action-content {
  justify-content: flex-end;
}
@media screen and (max-width: 500px) {
  .action-content {
    margin-top: -10px;
    padding-right: 20px;
  }
}
.list-action-card {
  border-radius: 12px !important;
  padding: 3px;
  margin: 10px;
}
.list-action-card:hover {
  background: #ffffff !important;
  box-shadow: 0px 0px 8px rgba(141, 181, 220, 0.46) !important;
}
.list-action-avatar {
  margin-right: 10px;
}
.notification-class {
  white-space: initial;
  text-align: center;
}
</style>
