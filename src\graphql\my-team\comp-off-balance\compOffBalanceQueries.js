import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const LIST_COMPENSATORY_OFF_BALANCE = gql`
  query listCompensatoryOffBalance(
    $selfService: Int!
    $defaultFilterStartDate: Date
    $defaultFilterEndDate: Date
  ) {
    listCompensatoryOffBalance(
      selfService: $selfService
      defaultFilterStartDate: $defaultFilterStartDate
      defaultFilterEndDate: $defaultFilterEndDate
    ) {
      errorCode
      message
      compOffBalanceDetails {
        Comp_Off_Balance_Id
        Employee_Id
        User_Defined_EmpId
        Department_Id
        Department_Name
        WorkSchedule_Id
        Work_Schedule
        Designation_Id
        Designation_Name
        EmpType_Id
        Employee_Type
        Location_Name
        Location_Id
        Total_Days
        Remaining_Days
        Comp_Off_Balance_Rules
        Worked_Date
        Expiry_Date
        Total_Hours
        Employee_Name
        Source
        Comp_Off_Additional_Wage_Claim_Balance
        Comp_Off_Attendance_Balance
        Total_Applied_Returned_Days
        Resignation_Date
        Updated_By
        Updated_On
        Service_Provider_Id
      }
    }
  }
`;
export const LIST_COMP_OFF = gql`
  query listCompOff($formId: Int!, $startDate: String!, $endDate: String!) {
    listCompOff(formId: $formId, startDate: $startDate, endDate: $endDate) {
      errorCode
      message
      compOffDetails
    }
  }
`;
export const EXPORT_COMP_OFF_HISTORY = gql`
  query exportCompOffHistory($workedDateFrom: String!, $workedDateTo: String!) {
    exportCompOffHistory(
      workedDateFrom: $workedDateFrom
      workedDateTo: $workedDateTo
    ) {
      errorCode
      message
      compOffHistoryData {
        employeeId
        userDefinedEmpId
        employeeName
        designationName
        departmentName
        compOffEligibleHours
        workSchedule
        employeeType
        compOffBalanceForAdditionalWageClaim
        locationName
        workedDate
        expiryDate
        totalDays
        remainingDays
        compOffBalanceForAttendance
        balanceAddedFrom
        updatedOn
        updatedByName
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const UPDATE_COMPENSATORY_OFF_BALANCE = gql`
  mutation updateCompensatoryOffBalance(
    $compOffBalanceId: Int!
    $remainingDays: Float!
    $expiryDate: Date!
  ) {
    updateCompensatoryOffBalance(
      compOffBalanceId: $compOffBalanceId
      remainingDays: $remainingDays
      expiryDate: $expiryDate
    ) {
      errorCode
      message
    }
  }
`;
