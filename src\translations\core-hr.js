export default {
  en: {
    /* ----- Employee Data Management ----- */
    // Tabs
    customGroup: "Custom Group",
    projects: "Projects",
    holidays: "Holidays",
    userAccounts: "User Accounts",
    documentSubtype: "Document Subtype",
    employeeDataImport: "Employee Data Import",
    accreditationCategoryAndType: "Accreditation Category and Type",
    // ----- Accreditations Category and Type -----
    // section titles
    viewAccreditationCategoryAndType: "View Accreditation Category and Type",
    addAccreditationCategoryAndType: "Add Accreditation Category and Type",
    editAccreditationCategoryAndType: "Edit Accreditation Category and Type",
    addAccreditationEnforcementGroup: "Add Accreditation Enforcement Group",
    newAccreditationEnforcementGroup: "New Accreditation Enforcement Group",

    // Table headers
    accreditationCategory: "Accreditation Category",
    accreditationType: "Accreditation Type",
    mandatorySelfOnboarding: "Mandatory during Self Onboarding",
    enforceDependent: "Enforce Dependent",
    document: "Document",
    actions: "Actions",
    instruction: "Instruction",

    // Filter labels
    mandatoryDuringSelfOnboarding: "Mandatory during Self Onboarding",
    yes: "Yes",
    no: "No",
    accreditationEnforcementGroup: "Accreditation Enforcement Group",

    // Buttons and actions
    addNew: "Add New",
    resetFilterSearch: "Reset Filter/Search",
    export: "Export",
    edit: "Edit",
    new: "New",
    close: "Close",
    retry: "Retry",
    viewDocument: "view document",
    cancel: "Cancel",
    save: "Save",
    submit: "Submit",
    add: "Add",
    delete: "Delete",
    addDocumentEnforcementGroup: "Add Document Enforcement Group",

    // Messages
    noAccreditationsForFilters:
      "There are no accreditations for the selected filters/searches.",
    accessDeniedMessage: "You don't have access to perform this action.",
    accreditationHelp1:
      "Accreditation Category and Type helps manage different accreditations and handle them accordingly",
    accreditationHelp2:
      "Accreditation Category and Type helps manage, update, and add new accreditations as needed.",
    exitFormWarning: "Are you sure to exit this form?",
    accreditationTypeUpdated: "Accreditation type updated successfully.",
    accreditationTypeAdded: "Accreditation type added successfully.",
    accreditationEnforcementGroupAdded:
      "Accreditation Enforcement Group added successfully.",
    fileHint:
      "Max size: 3 MB. Supported formats: png, jpeg, jpg, pdf, doc, docx, txt.",
    fileSizeError: "The file size should be less than 3 MB.",
    instructionError:
      "Please create the instruction according to hints provided",
    instructionHint1:
      "The instruction must contain {here} for presenting the uploaded document",
    instructionHint2:
      "Example: Please download the document {here}. Fill it and then upload it.",
    added: "Added",
    updated: "Updated",
    editorError: "Error initializing editor",

    /* ----- Document Subtype ----- */
    // Section titles
    viewDocumentSubtype: "View Document Subtype",
    addDocumentSubtype: "Add Document Subtype",
    editDocumentSubtype: "Edit Document Subtype",

    // Table headers
    documentType: "Document Type",
    documentCategory: "Document Category",
    enforcedInSelfOnboarding: "Enforced in Self Onboarding",
    enforceDuringOnboarding: "Enforce during Onboarding",
    allowedForEmailCommunication: "Allowed for Email Communication",
    emailTemplates: "Email Templates",
    documentDownloadedDuringOnboarding:
      "Document to be download during self onboarding",
    documentEnforcementGroup: "Document Enforcement Group",
    instructionForPresentingDocument:
      "Instruction for Presenting the document in Self Onboarding",
    addedOn: "Added On",
    addedBy: "Added By",
    updatedOn: "Updated On",
    updatedBy: "Updated By",

    // Messages
    noDocumentSubtypes:
      "There are no Document subtype for the selected filters/searches.",
    documentSubtypeUpdated: "Document subtype updated successfully",
    documentSubtypeAdded: "Document subtype added successfully",
    documentSubtypeDeleted: "Document subtype deleted successfully",
    documentSubtypeHelp1:
      "Document subtype helps manage different documents and handle them according to their categories and types",
  },
  fr: {
    /* ----- Employee Data Management ----- */
    // Tabs
    customGroup: "Groupe personnalisé",
    projects: "Projets",
    holidays: "Congés",
    userAccounts: "Comptes utilisateurs",
    documentSubtype: "Sous-type de document",
    employeeDataImport: "Importation des données employés",
    accreditationCategoryAndType: "Catégorie et type d'accréditation",

    // section titles
    viewAccreditationCategoryAndType:
      "Voir la catégorie et le type d'accréditation",
    addAccreditationCategoryAndType:
      "Ajouter une catégorie et un type d'accréditation",
    editAccreditationCategoryAndType:
      "Modifier la catégorie et le type d'accréditation",
    addAccreditationEnforcementGroup:
      "Ajouter un groupe d'application d'accréditation",
    newAccreditationEnforcementGroup:
      "Nouveau groupe d'application d'accréditation",

    // Table headers
    accreditationCategory: "Catégorie d'accréditation",
    accreditationType: "Type d'accréditation",
    mandatorySelfOnboarding: "Obligatoire pendant l'intégration autonome",
    enforceDependent: "Appliquer la dépendance",
    document: "Document",
    actions: "Actions",
    instruction: "Instruction",

    // Filter labels
    mandatoryDuringSelfOnboarding: "Obligatoire pendant l'intégration autonome",
    yes: "Oui",
    no: "Non",
    accreditationEnforcementGroup: "Groupe d'application d'accréditation",

    // Buttons and actions
    addNew: "Ajouter nouveau",
    resetFilterSearch: "Réinitialiser filtre/recherche",
    export: "Exporter",
    edit: "Modifier",
    new: "Nouveau",
    close: "Fermer",
    retry: "Réessayer",
    viewDocument: "voir le document",
    cancel: "Annuler",
    save: "Enregistrer",
    submit: "Soumettre",
    add: "Ajouter",
    delete: "Supprimer",
    addDocumentEnforcementGroup:
      "Ajouter un groupe d'application des documents",

    // Messages
    noAccreditationsForFilters:
      "Il n'y a pas d'accréditations pour les filtres/recherches sélectionnés.",
    accessDeniedMessage: "Vous n'avez pas accès pour effectuer cette action.",
    accreditationHelp1:
      "La catégorie et le type d'accréditation permettent de gérer différentes accréditations et de les traiter en conséquence",
    accreditationHelp2:
      "La catégorie et le type d'accréditation permettent de gérer, mettre à jour et ajouter de nouvelles accréditations selon les besoins.",
    exitFormWarning: "Êtes-vous sûr de vouloir quitter ce formulaire ?",
    accreditationTypeUpdated: "Type d'accréditation mis à jour avec succès.",
    accreditationTypeAdded: "Type d'accréditation ajouté avec succès.",
    accreditationEnforcementGroupAdded:
      "Groupe d'application d'accréditation ajouté avec succès.",
    fileHint:
      "Taille max : 3 Mo. Formats supportés : png, jpeg, jpg, pdf, doc, docx, txt.",
    fileSizeError: "La taille du fichier doit être inférieure à 3 Mo.",
    instructionError: "Veuillez créer l'instruction selon les conseils fournis",
    instructionHint1:
      "L'instruction doit contenir {here} pour présenter le document téléchargé",
    instructionHint2:
      "Exemple : Veuillez télécharger le document {here}. Remplissez-le puis téléchargez-le.",
    added: "Ajouté",
    updated: "Mis à jour",
    editorError: "Erreur d'initialisation de l'éditeur",

    /* ----- Document Subtype ----- */
    // Section titles
    viewDocumentSubtype: "Voir le sous-type de document",
    addDocumentSubtype: "Ajouter un sous-type de document",
    editDocumentSubtype: "Modifier le sous-type de document",

    // Table headers
    documentType: "Type de document",
    documentCategory: "Catégorie de document",
    enforcedInSelfOnboarding: "Obligatoire dans l'auto-intégration",
    enforceDuringOnboarding: "Appliquer pendant l'intégration",
    allowedForEmailCommunication: "Autorisé pour la communication par e-mail",
    emailTemplates: "Modèles d'e-mail",
    documentDownloadedDuringOnboarding:
      "Document à télécharger pendant l'intégration",
    documentEnforcementGroup: "Groupe d'application des documents",
    instructionForPresentingDocument:
      "Instruction pour présenter le document pendant l'intégration",
    addedOn: "Ajouté le",
    addedBy: "Ajouté par",
    updatedOn: "Mis à jour le",
    updatedBy: "Mis à jour par",

    // Messages
    noDocumentSubtypes:
      "Il n'y a pas de sous-type de document pour les filtres/recherches sélectionnés.",
    documentSubtypeUpdated: "Sous-type de document mis à jour avec succès",
    documentSubtypeAdded: "Sous-type de document ajouté avec succès",
    documentSubtypeDeleted: "Sous-type de document supprimé avec succès",
    documentSubtypeHelp1:
      "Le sous-type de document aide à gérer différents documents et à les traiter selon leurs catégories et types",
  },
  ja: {
    /* ----- Employee Data Management ----- */
    // Tabs
    customGroup: "カスタムグループ",
    projects: "プロジェクト",
    holidays: "休日",
    userAccounts: "ユーザーアカウント",
    documentSubtype: "文書サブタイプ",
    employeeDataImport: "従業員データインポート",
    accreditationCategoryAndType: "認定カテゴリーと種類",

    // section titles
    viewAccreditationCategoryAndType: "認定カテゴリーと種類を表示",
    addAccreditationCategoryAndType: "認定カテゴリーと種類を追加",
    editAccreditationCategoryAndType: "認定カテゴリーと種類を編集",
    addAccreditationEnforcementGroup: "認定執行グループを追加",
    newAccreditationEnforcementGroup: "新規認定執行グループ",

    // Table headers
    accreditationCategory: "認定カテゴリー",
    accreditationType: "認定種類",
    mandatorySelfOnboarding: "セルフオンボーディング時の必須",
    enforceDependent: "依存関係を強制",
    document: "文書",
    actions: "アクション",
    instruction: "指示",

    // Filter labels
    mandatoryDuringSelfOnboarding: "セルフオンボーディング時の必須",
    yes: "はい",
    no: "いいえ",
    accreditationEnforcementGroup: "認定執行グループ",

    // Buttons and actions
    addNew: "新規追加",
    resetFilterSearch: "フィルター/検索をリセット",
    export: "エクスポート",
    edit: "編集",
    new: "新規",
    close: "閉じる",
    retry: "再試行",
    viewDocument: "文書を表示",
    cancel: "キャンセル",
    save: "保存",
    submit: "送信",
    add: "追加",
    delete: "削除",
    addDocumentEnforcementGroup: "文書執行グループを追加",

    // Messages
    noAccreditationsForFilters:
      "選択されたフィルター/検索に該当する認定はありません。",
    accessDeniedMessage: "このアクションを実行する権限がありません。",
    accreditationHelp1:
      "認定カテゴリーと種類は、様々な認定を管理し、適切に処理するのに役立ちます",
    accreditationHelp2:
      "認定カテゴリーと種類は、必要に応じて認定の管理、更新、新規追加を行うのに役立ちます。",
    exitFormWarning: "このフォームを終了してもよろしいですか？",
    accreditationTypeUpdated: "認定種類が正常に更新されました。",
    accreditationTypeAdded: "認定種類が正常に追加されました。",
    accreditationEnforcementGroupAdded:
      "認定執行グループが正常に追加されました。",
    fileHint:
      "最大サイズ：3MB。対応フォーマット：png、jpeg、jpg、pdf、doc、docx、txt。",
    fileSizeError: "ファイルサイズは3MB未満である必要があります。",
    instructionError: "提供されたヒントに従って指示を作成してください",
    instructionHint1:
      "指示には、アップロードされた文書を表示するために {here} を含める必要があります",
    instructionHint2:
      "例：文書 {here} をダウンロードしてください。記入後、アップロードしてください。",
    added: "追加済み",
    updated: "更新済み",
    editorError: "エディターの初期化エラー",

    /* ----- Document Subtype ----- */
    // Section titles
    viewDocumentSubtype: "文書サブタイプを表示",
    addDocumentSubtype: "文書サブタイプを追加",
    editDocumentSubtype: "文書サブタイプを編集",

    // Table headers
    documentType: "文書タイプ",
    documentCategory: "文書カテゴリー",
    enforcedInSelfOnboarding: "セルフオンボーディングで強制",
    enforceDuringOnboarding: "オンボーディング中に適用",
    allowedForEmailCommunication: "メール通信許可",
    emailTemplates: "メールテンプレート",
    documentDownloadedDuringOnboarding:
      "オンボーディング中にダウンロードする文書",
    documentEnforcementGroup: "文書執行グループ",
    instructionForPresentingDocument:
      "オンボーディング中に文書を提示するための指示",
    addedOn: "追加日",
    addedBy: "追加者",
    updatedOn: "更新日",
    updatedBy: "更新者",

    // Messages
    noDocumentSubtypes:
      "選択されたフィルター/検索に該当する文書サブタイプはありません。",
    documentSubtypeUpdated: "文書サブタイプが正常に更新されました",
    documentSubtypeAdded: "文書サブタイプが正常に追加されました",
    documentSubtypeDeleted: "文書サブタイプが正常に削除されました",
    documentSubtypeHelp1:
      "文書サブタイプは、異なる文書を管理し、それらのカテゴリーと種類に応じて処理するのに役立ちます",
  },
};
