.table {
  margin-bottom: .5rem;
  margin-left: auto;
  margin-right: auto;
  border-collapse: separate
}

.chartNode {
  box-sizing: border-box;
  display: inline-flex;
  flex-direction: column;
  position: relative;
  margin: 0 1px 2px 1px;
  max-width: 20px;
  border: 1px solid #c6c6c6;
  text-align: center;
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .3s;
  border-radius: .35rem
}

/* .chartNode:hover {
  box-shadow: 0 0 5px #f56868;
  cursor: default;
  z-index: 20
} */

.chartTitle {
  font-weight: 700;
  line-height: 1.25rem;
  background: #f56868;
  color: #fff;
  border-top-left-radius: .25rem;
  border-top-right-radius: .25rem
}

.chartContent,
.chartTitle {
  text-align: center;
  font-size: .75rem;
  overflow: hidden;
  white-space: nowrap
}

.chartContent {
  box-sizing: border-box;
  width: 100%;
  line-height: 1rem;
  border: #f56868;
  border-bottom-right-radius: .25rem;
  border-bottom-left-radius: .25rem;
  background: #fff;
  color: #000
}

.chartContent,
.chartLines {
  height: 1.25rem
}

.chartDownLine {
  background: #c6c6c6;
  margin-left: auto;
  margin-right: auto;
  height: 1.25rem;
  width: .125rem;
  float: none
}

.chartTopLine {
  border-top-color: #c6c6c6;
  border-top-style: solid;
  border-top-width: 2px
}

.chartRightLine {
  border-right-style: solid;
  border-right-width: 1px;
  border-left-color: #c6c6c6;
  border-right-color: #c6c6c6;
}

.chartLeftLine {
  border-left-color: #c6c6c6;
  border-left-style: solid;
  border-left-width: 1px
}

p {
  margin-top: 0;
  margin-bottom: 0
}

.chartOrgchartContainer {
  width: 100%;
  position: relative;
  display: inline-block;
  border: 1px dashed #d3d3d3;
  border-radius: .25rem;
  overflow: auto;
  text-align: center
}

.chartOrgchart {
  box-sizing: border-box;
  display: inline-block;
  width: 100%;
}

.chartOrgchart table {
  border-spacing: 0;
  width: 100%;
}

.chartOrgchart td {
  text-align: center;
  vertical-align: top;
  padding: 0
}

.border {
  border-width: 0px !important;
}