<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="false"
              />
              <MyShiftSwapFilter
                v-if="itemList.length || isFilterApplied"
                ref="formFilterRef"
                :itemList="originalList"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="my-shift-swap-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="Shift swapping enables managers to optimize team schedules effectively while ensuring smooth operations. It provides flexibility for employees by allowing adjustments to their shifts when necessary, promoting a balanced work environment. Managers can review and approve shift changes to maintain proper staffing levels and ensure team efficiency."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="This process fosters collaboration and accountability, ensuring that shifts are managed without disrupting overall productivity. By handling shift swaps within an intuitive system, managers can easily accommodate team needs while adhering to organizational guidelines."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="Shift swapping also helps in maintaining fairness and transparency in scheduling, as it offers a structured way to address employee preferences while balancing business requirements. This ensures that shifts are evenly distributed and aligned with the organization's operational demands, leading to better employee satisfaction and improved workplace harmony."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      class="mt-1"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div class="mt-12">
              <div
                v-if="originalList.length > 0 && !isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Employee Name
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Swap_Id === item.Swap_Id
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Employee_Name"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Employee_Name.length > 50 ? props : ''
                                  "
                                >
                                  {{ checkNullValue(item.Employee_Name) }}
                                  <div
                                    v-if="item.User_Defined_EmpId"
                                    class="text-grey"
                                  >
                                    {{
                                      checkNullValue(item.User_Defined_EmpId)
                                    }}
                                  </div>
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Approver
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Swap_Id === item.Swap_Id
                              "
                              class="d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Approver_Name"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Approver_Name.length > 50 ? props : ''
                                  "
                                >
                                  {{ checkNullValue(item.Approver_Name) }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Swap Date
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Swap_Id === item.Swap_Id
                              "
                              class="d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Swap_Date"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Swap_Date.length > 50 ? props : ''
                                  "
                                >
                                  {{
                                    checkNullValue(formatDate(item.Swap_Date))
                                  }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>

                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Shift Type
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Swap_Id === item.Swap_Id
                              "
                              class="d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Shift_Name"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Shift_Name.length > 50 ? props : ''
                                  "
                                >
                                  {{ checkNullValue(item.Shift_Name) }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Status
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Swap_Id === item.Swap_Id
                              "
                              class="d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Approval_Status"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Approval_Status.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ checkNullValue(item.Approval_Status) }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Actions
                          </div>

                          <div>
                            <section
                              class="d-flex justify-center align-center"
                              style="width: 100%"
                            >
                              <div class="d-flex align-center">
                                <v-tooltip
                                  text="You don't have access to perform this action."
                                  location="top"
                                >
                                  <template v-slot:activator="{ props }">
                                    <i
                                      class="hr-workflow-task-management-approve text-green text-h5 pl-2 cursor-pointer"
                                      aria-hidden="true"
                                      title="Approve"
                                      id="idApproveButton"
                                      v-bind="
                                        formAccess && formAccess.update
                                          ? {}
                                          : props
                                      "
                                      @click.stop="
                                        formAccess && formAccess.update
                                          ? changeStatus(item, 'approve')
                                          : {}
                                      "
                                    />
                                    <i
                                      class="hr-workflow-task-management-reject text-red text-h5 pl-2 cursor-pointer"
                                      aria-hidden="true"
                                      title="Reject"
                                      id="idRejectButton"
                                      v-bind="
                                        formAccess && formAccess.update
                                          ? {}
                                          : props
                                      "
                                      @click.stop="
                                        formAccess && formAccess.update
                                          ? changeStatus(item, 'reject')
                                          : {}
                                      "
                                    />
                                  </template>
                                </v-tooltip>
                              </div>
                            </section>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewShiftSwap
                    :selectedItem="selectedItem"
                    :formApprovalView="true"
                    :landedFormName="landedFormName"
                    @close-form="closeAllForms()"
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <AppLoading v-if="listLoading" />
    <v-dialog
      :model-value="openFormInModal"
      class="rounded-lg"
      max-width="1000"
      @click:outside="closeAllForms()"
    >
      <v-card v-if="showEmpListModal" class="px-8 rounded-lg" min-width="100%">
        <v-card-title>
          <div class="text-primary text-center font-weight-medium ma-4">
            Select an employee for the Shift Swap
          </div>
        </v-card-title>
        <div v-if="listLoading" class="mt-3">
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            />
          </div>
        </div>
        <div v-else style="overflow: scroll">
          <v-row no-gutters>
            <v-col cols="12" class="pa-0 mb-4 flex align-center">
              <v-data-table
                v-if="popupEmployeeList.length > 0"
                v-model="selectedEmpRecords"
                id="gridView"
                :headers="tableListHeaders"
                :show-select="selectStrategy !== 'single' && !isMobileView"
                :select-strategy="selectStrategy"
                :items="popupEmployeeList"
                :items-per-page="50"
                fixed-header
                :height="
                  $store.getters.getTableHeightBasedOnScreenSize(
                    200,
                    popupEmployeeList
                  )
                "
                :items-per-page-options="[
                  { value: 50, title: '50' },
                  { value: 100, title: '100' },
                  { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
                ]"
              >
                <template #item="{ item }">
                  <tr
                    class="data-table-tr bg-white cursor-pointer"
                    :class="
                      isMobileView
                        ? 'v-data-table__mobile-table-row'
                        : selectStrategy === 'single'
                        ? 'cursor-pointer'
                        : ''
                    "
                    @click="
                      selectStrategy === 'single' ? selectItemInPopUp(item) : {}
                    "
                  >
                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Employee Name
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2">
                          {{ checkNullValue(item.Employee_Name) }}
                        </section>
                      </div>
                    </td>

                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Approver
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2">
                          {{ checkNullValue(item.Approver_Name) }}
                        </section>
                      </div>
                    </td>

                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Swap Date
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2">
                          {{ checkNullValue(item.Swap_Date) }}
                        </section>
                      </div>
                    </td>

                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Shift Type
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2">
                          {{ checkNullValue(item.Shift_Name) }}
                        </section>
                      </div>
                    </td>
                    <td
                      :class="[
                        isMobileView ? 'v-data-table__mobile-row' : '',
                        {
                          'selected-row':
                            selectedRowIndex === item[Object.keys(item)[0]],
                        },
                      ]"
                    >
                      <div
                        v-if="isMobileView"
                        class="v-data-table__mobile-row__header"
                      >
                        Status
                      </div>
                      <div
                        :class="
                          isMobileView ? 'v-data-table__mobile-row__cell' : ''
                        "
                      >
                        <section class="text-primary text-body-2">
                          {{ checkNullValue(item.Approval_Status) }}
                        </section>
                      </div>
                    </td>
                  </tr>
                </template>
              </v-data-table>

              <AppFetchErrorScreen
                v-else
                key="no-results-screen"
                main-title="No matching search results found"
                image-name="common/no-records"
              ></AppFetchErrorScreen>
            </v-col>
          </v-row>
        </div>

        <div class="text-center">
          <div class="text-center pb-4">
            <v-btn
              rounded="lg"
              class="primary"
              :disabled="!selectedRowIndex"
              @click="onShiftSwapForBothEmployees()"
            >
              <span class="primary">Submit</span>
            </v-btn>
          </div>
        </div>
      </v-card>
      <ViewShiftSwap
        v-if="showViewForm"
        :formApprovalView="true"
        :selectedItem="selectedItem"
        :landedFormName="landedFormName"
        @close-form="closeAllForms()"
      />
    </v-dialog>
    <AppWarningModal
      v-if="conformationModel"
      :open-modal="conformationModel"
      confirmation-heading="Please confirm if you want to change the status of the selected record?"
      :icon-name="
        selectedItem?.statusRequested?.toLowerCase() === 'reject'
          ? `fas fa-times-circle`
          : `fas fa-check-circle`
      "
      :icon-color="
        selectedItem?.statusRequested?.toLowerCase() === 'reject'
          ? `red`
          : `success`
      "
      icon-Size="75"
      @close-warning-modal="conformationModel = false"
      @accept-modal="
        selectedItem?.statusRequested?.toLowerCase() === 'reject'
          ? rejectStatus()
          : approveStatus()
      "
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewShiftSwap = defineAsyncComponent(() =>
  import("../shift-swap/ViewShiftSwap.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);

import { checkNullValue, convertUTCToLocal } from "@/helper.js";
// Queries
import {
  LIST_SHIFT_SWAPPING,
  EVALUATE_SHIFT_SWAP_STATUS,
  REJECT_SHIFT_SWAP,
  APPROVE_MATCHING_SWAP_REQUEST,
} from "@/graphql/roster-management/TeamShiftSwapQueries.js";
import MyShiftSwapFilter from "./ShiftSwapApprovalFilter.vue";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "ShiftSwapApproval",
  components: {
    EmployeeDefaultFilterMenu,
    ViewShiftSwap,
    NotesCard,
    MyShiftSwapFilter,
  },
  mixins: [FileExportMixin],
  data: () => ({
    selectedEmpRecords: [],
    popupEmployeeList: [],
    selectStrategy: "single",
    selectedRowIndex: null,
    // list
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    showEmpListModal: false,
    otherShiftEmployees: [],
    conformationModel: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
    isFilterApplied: false,
    openMoreMenu: false,
  }),
  computed: {
    landedFormName() {
      let formName = this.accessIdRights("307");
      return formName?.formName;
    },

    rosterManagementFormAccess() {
      return this.$store.getters.shiftSwapFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.rosterManagementFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessIdRights("307");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Employee Name",
            align: "start",
            key: "Employee_Name",
          },
          {
            title: "Approver",
            key: "Approver_Name",
          },
        ];
      } else {
        return [
          {
            title: "Employee Name",
            align: "start",
            key: "Employee_Name",
          },
          {
            title: "Approver",
            key: "Approver_Name",
          },
          {
            title: "Swap Date",
            key: "Swap_Date",
          },
          {
            title: "Shift Type",
            key: "Shift_Name",
          },
          {
            title: "Status",
            key: "Approval_Status",
          },
          {
            title: "Actions",
            key: "action",
            align: "center",
            sortable: false,
          },
        ];
      }
    },
    tableListHeaders() {
      return [
        {
          title: "Employee Name",
          align: "start",
          key: "Employee_Name",
        },
        {
          title: "Approver",
          key: "Approver_Name",
        },
        {
          title: "Swap Date",
          key: "Swap_Date",
        },
        {
          title: "Shift Type",
          key: "Shift_Name",
        },
        {
          title: "Status",
          key: "Approval_Status",
        },
      ];
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showViewForm || this.showEmpListModal)
      );
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText =
          "There are no Shift Swap Approvals for the selected filters/searches.";
      }
      return msgText;
    },
    openFormInModal() {
      if (this.showViewForm && this.windowWidth < 1264) {
        return true;
      } else if (this.showEmpListModal) {
        return true;
      }
      return false;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
  },
  created() {
    this.fetchList();
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.rosterManagementFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/roster-management/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/roster-management/" + clickedForm.url;
        }
      }
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },

    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
      let filterObj = {
        selectedShiftType: null,
        selectedSwapDate: null,
        selectedStatus: null,
      };
      this.applyFilter(filterObj);
    },

    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter.selectedShiftType && filter.selectedShiftType != "") {
        filteredList = filteredList.filter((item) => {
          return filter.selectedShiftType == item.Shift_Name;
        });
      }
      if (
        filter.selectedSwapDate &&
        filter.selectedSwapDate !== "" &&
        filter.selectedSwapDate !== "Invalid date"
      ) {
        filteredList = filteredList.filter((item) => {
          let selectedSwapDate = moment(filter.selectedSwapDate).format(
            "YYYY-MM-DD"
          );
          let Swap_Date = moment(item.Swap_Date).format("YYYY-MM-DD");
          return selectedSwapDate === Swap_Date;
        });
      }
      if (filter.selectedStatus && filter.selectedStatus != "") {
        filteredList = filteredList.filter((item) => {
          return filter.selectedStatus == item.Approval_Status;
        });
      }
      this.isFilterApplied = true;
      this.filteredList = filteredList;
      this.itemList = filteredList;
    },
    onMoreAction(actionType) {
      if (actionType.key === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    changeStatus(item, type) {
      this.selectedItem = item;
      this.selectedItem.statusRequested = type;
      this.conformationModel = true;
    },
    onShiftSwapForBothEmployees() {
      this.listLoading = true;
      this.$apollo
        .mutate({
          mutation: APPROVE_MATCHING_SWAP_REQUEST,
          client: "apolloClientM",
          variables: {
            Swap_ID: this.selectedItem.Swap_Id, // NORMAL LIST EMPLOYEE SWAP ID
            Matching_Swap_ID: this.selectedEmpRecords.Swap_Id, // POP-UP EMPLOYEE SWAP ID
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.approveMathchingSwapRequest &&
            !response.data.approveMathchingSwapRequest.errorCode
          ) {
            const snackbarData = {
              isOpen: true,
              type: "success",
              message: response.data.approveMathchingSwapRequest.message,
            };
            this.showAlert(snackbarData);
            this.closeAllForms();
            this.fetchList();
          } else {
            this.handleStatusErrors(
              response.data.approveMathchingSwapRequest.errorCode
            );
          }
        })
        .catch((error) => {
          this.handleStatusErrors(error); // Handle catch block errors
        })
        .finally(() => {
          this.listLoading = false; // Ensure loader stops in all cases
        });
    },
    selectItemInPopUp(item) {
      const itemId = item[Object.keys(item)[0]];
      if (this.selectedRowIndex === itemId) {
        // Unselect if already selected
        this.selectedRowIndex = null;
      } else {
        // Select the clicked row
        this.selectedRowIndex = itemId;
        this.selectedEmpRecords = item;
      }
    },
    rejectStatus() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .mutate({
          mutation: REJECT_SHIFT_SWAP,
          client: "apolloClientM",
          variables: {
            swapId: vm.selectedItem.Swap_Id,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.rejectShiftSwap &&
            !response.data.rejectShiftSwap.errorCode
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Employee Shift Swap Rejected successfully!",
            };
            vm.conformationModel = false;
            vm.selectedItem = null;
            vm.showAlert(snackbarData);
            vm.fetchList();
          } else {
            let error = response.data.rejectShiftSwap.errorCode;
            vm.handleStatusErrors(error);
          }
          vm.listLoading = false;
        })
        .catch((error) => {
          vm.listLoading = false;
          vm.handleStatusErrors(error);
        });
    },

    approveStatus() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .mutate({
          mutation: EVALUATE_SHIFT_SWAP_STATUS,
          client: "apolloClientM",
          variables: {
            swapId: vm.selectedItem.Swap_Id,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.evaluateAndProcessShiftSwapStatus &&
            !response.data.evaluateAndProcessShiftSwapStatus.errorCode
          ) {
            let matchingSwapIds =
              response.data.evaluateAndProcessShiftSwapStatus.matchingSwapIds;
            if (matchingSwapIds && matchingSwapIds.length) {
              vm.fetchList(matchingSwapIds);
            } else {
              var snackbarData = {
                isOpen: true,
                type: "success",
                message: "Employee Shift Swap Approved successfully!",
              };
              vm.showAlert(snackbarData);
              vm.closeAllForms();
              vm.fetchList();
            }
          } else {
            let error =
              response.data.evaluateAndProcessShiftSwapStatus.errorCode;
            vm.handleStatusErrors(error);
          }
          vm.conformationModel = false;
          vm.listLoading = false;
        })
        .catch((error) => {
          vm.listLoading = false;
          vm.handleStatusErrors(error);
        });
    },
    handleStatusErrors(error = "") {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "update",
        form: "Shift Swap Approvals",
        isListError: false,
      });
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        Added_On: el.Added_On ? this.convertUTCToLocal(el.Added_On) : "",
        Updated_On: el.Updated_On ? this.convertUTCToLocal(el.Updated_On) : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Shift Swap Approvals",
        sheetName: "Shift Swap Approvals",
        header: [
          {
            header: "Employee Id",
            key: "User_Defined_EmpId",
          },
          {
            header: "Employee Name",
            key: "Employee_Name",
          },
          {
            header: "Approver",
            key: "Approver_Name",
          },
          {
            header: "Swap Date",
            key: "Swap_Date",
          },
          {
            header: "Shift Type",
            key: "Shift_Name",
          },
          {
            header: "Status",
            key: "Approval_Status",
          },
          {
            header: "Added On",
            key: "Added_On",
          },
          {
            header: "Added By",
            key: "Added_By_Name",
          },
          {
            header: "Updated On",
            key: "Updated_On",
          },
          {
            header: "Updated By",
            key: "Updated_By_Name",
          },
        ],
      };
      this.exportExcelFile(exportOptions);
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },

    closeAllForms() {
      this.showViewForm = false;
      this.selectedItem = null;
      this.showEmpListModal = false;
      this.conformationModel = false;
      this.selectedEmpRecords = [];
      this.selectedRowIndex = null;
      this.conformationModel = false;
    },

    fetchList(swapIdList = null) {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_SHIFT_SWAPPING,
          client: "apolloClientL",
          variables: {
            formId: 307,
            swapIdList: swapIdList,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listshiftSwapping &&
            response.data.listshiftSwapping.shiftSwapping &&
            !response.data.listshiftSwapping.errorCode
          ) {
            if (swapIdList && swapIdList.length) {
              vm.popupEmployeeList =
                response.data.listshiftSwapping.shiftSwapping;
              vm.showEmpListModal = true;
            } else {
              let tempData = response.data.listshiftSwapping.shiftSwapping;
              vm.itemList = tempData;
              vm.originalList = tempData;
              vm.onApplySearch();
            }
            vm.listLoading = false;
          } else {
            let error = response.data.listshiftSwapping.errorCode;
            vm.handleListError(error);
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Shift Swap Approvals",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.my-shift-swap-container {
  padding: 1em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

.selected-row {
  background-color: rgb(var(--v-theme-hover));
}
@media screen and (max-width: 805px) {
  .my-shift-swap-container {
    padding: 10em 1em 0em 1em;
  }
}
</style>
