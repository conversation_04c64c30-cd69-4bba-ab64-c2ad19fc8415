<template>
  <div style="max-width: 100%">
    <div
      v-if="windowWidth > 960 || (windowWidth >= 1280 && centerTab)"
      class="top-bar"
      flat
      height="4em"
    >
      <v-toolbar class="active_tab_bg" height="58">
        <v-row class="mr-1 d-flex align-center">
          <v-col md="7" class="main-tab pb-0">
            <v-tabs
              v-model="currentTabItem"
              class="active_tab_bg form-tabs"
              color="primary"
              :disabled="disableTab"
            >
              <v-tab
                v-for="(tab, i) in tabsList"
                :key="i + '-tab'"
                :value="'tab-' + i"
                class="font-weight-bold"
                style="text-transform: capitalize !important"
                @click="$emit('tab-clicked', tab)"
              >
                <a
                  v-if="
                    tabListRedirectionURLs.length > 0 &&
                    tabListRedirectionURLs[i]
                  "
                  :href="baseUrl + tabListRedirectionURLs[i]"
                  style="text-decoration: none"
                  :class="
                    currentTabItem === 'tab-' + i
                      ? 'text-primary'
                      : 'text-grey-darken-2'
                  "
                  ><span>{{ tab }}</span></a
                >
                <span
                  v-else
                  :class="
                    currentTabItem === 'tab-' + i
                      ? 'text-primary'
                      : 'text-grey-darken-2'
                  "
                  >{{ tab }}</span
                >
                <div
                  v-if="tabCount && tabCount.length > 0 && tabCount[i] > 0"
                  class="ml-2"
                >
                  <v-chip
                    v-if="currentTabItem === 'tab-' + i"
                    color="primary"
                    variant="elevated"
                    size="small"
                    >{{ tabCount[i] }}</v-chip
                  >
                  <span v-else>- {{ tabCount[i] }}</span>
                </div>
              </v-tab>
            </v-tabs>
          </v-col>
          <v-col cols="5" class="d-flex justify-end pa-0">
            <slot name="topBarContent"></slot>
          </v-col>
        </v-row>
      </v-toolbar>
    </div>

    <!-- mobile topbar design -->
    <div
      v-if="windowWidth <= 960 || (windowWidth < 1280 && centerTab)"
      class="top-bar"
      flat
      height="4em"
    >
      <v-toolbar class="active_tab_bg">
        <v-tabs
          v-model="currentTabItem"
          class="active_tab_bg"
          show-arrows
          center-active
          :disabled="disableTab"
        >
          <v-tab
            v-for="(tab, i) in tabsList"
            :key="i + '-tab'"
            :value="'tab-' + i"
            :href="
              tabListRedirectionURLs.length > 0 && tabListRedirectionURLs[i]
                ? baseUrl + tabListRedirectionURLs[i]
                : ''
            "
            class="active_tab_bg font-weight-bold"
            style="text-transform: capitalize !important"
            @click="$emit('tab-clicked', tab)"
          >
            {{ tab }}
            <div
              v-if="tabCount && tabCount.length > 0 && tabCount[i] > 0"
              class="ml-2"
            >
              <v-chip
                v-if="currentTabItem === 'tab-' + i"
                color="primary"
                variant="elevated"
                size="small"
                >{{ tabCount[i] }}</v-chip
              >
              <span v-else>- {{ tabCount[i] }}</span>
            </div>
          </v-tab>
        </v-tabs>
      </v-toolbar>
      <v-bottom-navigation
        v-if="$slots.topBarContent && showBottomSheet"
        color="teal"
        elevation="15"
        bg-color="white"
        class="align-center"
      >
        <slot name="topBarContent"></slot>
      </v-bottom-navigation>
    </div>
  </div>
</template>

<script>
export default {
  name: "AppTopBarTab",

  props: {
    // have to change this as Array and check
    tabsList: {
      type: Array,
      required: true,
    },
    currentTab: {
      type: String,
      default: "tab-0",
    },
    showBottomSheet: {
      type: Boolean,
      default: true,
    },
    tabName: {
      type: String,
      default: "New",
    },
    centerTab: {
      type: Boolean,
      default: false,
    },
    tabCount: {
      type: Array,
      default: function () {
        return [];
      },
    },
    disableTab: {
      type: Boolean,
      default: false,
    },
    tabListRedirectionURLs: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },

  data: () => ({
    currentTabItem: "",
    // forms for which new label has to be shown
    newForms: [],
  }),

  computed: {
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  watch: {
    //on current active tab change update the currentTabItem value to highlight the updated active tab
    currentTab(val) {
      this.currentTabItem = val;
    },
  },

  mounted() {
    this.currentTabItem = this.currentTab;
  },
};
</script>
<style scoped lang="css">
.active_tab_bg {
  background: #f9f9f9 !important;
}
.main-tab {
  padding: 0em 2.5em;
}
@media screen and (max-width: 1264px) {
  .main-tab {
    padding-left: oem 1.2em !important;
  }
}
@media screen and (max-width: 500px) {
  .main-tab {
    padding-left: 0em 0.5em !important;
  }
}
.top-bar {
  height: 5em;
  position: fixed !important;
  width: calc(100% - 105px) !important;
  z-index: 4;
}
@media screen and (max-width: 1279px) {
  .top-bar {
    width: 100% !important;
    margin-left: 0px !important;
  }
}

.hrapp-module-new-label {
  width: 45px;
  background: #ff743c;
  border-radius: 25px;
  opacity: 1;
  z-index: 5;
  height: 18px;
  font-family: inherit;
  font-size: 0.9em;
  color: #fff !important;
  margin-top: -10px;
  margin-left: -1px;
}
</style>
