<template>
  <div v-if="listItems.length > 0 && !showTaxReliefImport">
    <div class="d-flex flex-wrap align-center justify-end mb-1">
      <v-btn
        color="transparent"
        class="ml-2 mt-1"
        variant="flat"
        size="small"
        @click="$emit('refetch-list')"
        ><v-icon color="grey">fas fa-redo-alt</v-icon></v-btn
      >
      <v-menu v-model="openMoreMenu" transition="scale-transition">
        <template v-slot:activator="{ props }">
          <v-btn variant="plain" class="mt-1 ml-n2 mr-n5" v-bind="props">
            <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
            <v-icon v-else>fas fa-caret-up</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="mAction in moreActions"
            :key="mAction.key"
            @click="onMoreAction(mAction.key)"
          >
            <v-hover>
              <template v-slot:default="{ isHovering, props }">
                <v-list-item-title
                  v-bind="props"
                  class="pa-3"
                  :class="{
                    'pink-lighten-5': isHovering,
                  }"
                  ><v-icon size="15" class="pr-2">{{ mAction.icon }}</v-icon
                  >{{ mAction.key }}</v-list-item-title
                >
              </template>
            </v-hover>
          </v-list-item>
        </v-list>
      </v-menu>
    </div>
    <div
      :style="'overflow: scroll; height: ' + $store.getters.getTableHeight(230)"
    >
      <v-form ref="addEditTaxReliefForm">
        <table>
          <thead>
            <tr>
              <th
                class="bg-blue text-white"
                v-for="header in listHeaders"
                :key="header"
              >
                {{ header }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in listItems"
              :key="index + 'tax-relief-declarations'"
            >
              <td
                v-if="item.isChild"
                class="text-body-2"
                style="max-width: 200px"
              >
                <v-row
                  style="
                    border: 1px solid grey;
                    margin: 0px;
                    border-radius: 7px;
                  "
                >
                  <v-col
                    v-if="
                      !openBottomSheet &&
                      item.Approval_Status !== 'Approved' &&
                      item.Approval_Status != 'Rejected'
                    "
                    cols="12"
                    class="d-flex justify-end mb-n6"
                  >
                    <v-icon
                      color="secondary"
                      size="14"
                      @click="openEditForm(index)"
                      >fas fa-edit</v-icon
                    >
                  </v-col>
                  <v-col cols="12" xl="6">
                    <p class="text-body-2 text-grey-darken-1">
                      Relief Category
                      <span
                        v-if="item.isAddEdit && action === 'add'"
                        class="text-red"
                        >*</span
                      >
                    </p>
                    <CustomSelect
                      v-if="item.isAddEdit && action === 'add'"
                      :items="reliefCategoryList"
                      label=""
                      :itemSelected="
                        item.Tax_Relief_Category_Id
                          ? parseInt(item.Tax_Relief_Category_Id)
                          : null
                      "
                      :rules="[
                        required(
                          'Relief Category',
                          item.Tax_Relief_Category_Id
                        ),
                      ]"
                      variantType="underlined"
                      itemValue="Tax_Relief_Category_Id"
                      itemTitle="Tax_Relief_Title"
                      :isRequired="true"
                      :isAutoComplete="true"
                      :isLoading="reliefCategoryListLoading"
                      :noDataText="
                        reliefCategoryListLoading
                          ? 'Loading...'
                          : 'No data available'
                      "
                      subText="Maximum_Amount"
                      subTextTitle="Max Limit"
                      class="mt-n5"
                      @selected-item="
                        onChangeCustomSelectField(
                          $event,
                          index,
                          'Tax_Relief_Category_Id'
                        )
                      "
                    ></CustomSelect>
                    <p v-else class="text-body-2 font-weight-regular">
                      {{ checkNullValue(item.Tax_Relief_Title) }}
                    </p>
                    <div
                      v-if="item.Category_Maximum_Amount"
                      class="text-caption text-grey"
                      :class="{ 'mt-n4': item.isAddEdit && action === 'add' }"
                      style="line-height: 1.2"
                    >
                      Max Limit: {{ item.Category_Maximum_Amount }}
                    </div>
                  </v-col>
                  <v-col cols="12" xl="6">
                    <p class="text-body-2 text-grey-darken-1">Documents</p>
                    <p
                      v-if="
                        item.Approval_Status !== 'Approved' &&
                        item.Approval_Status != 'Rejected'
                      "
                      @click="addEditDocuments(index)"
                      class="text-body-2 font-weight-regular d-flex flex-wrap mt-1 cursor-pointer"
                      style="
                        text-decoration: underline;
                        color: #004aff;
                        width: 100px;
                        display: block;
                      "
                    >
                      {{
                        item.taxReliefDocuments &&
                        item.taxReliefDocuments.length > 0
                          ? "View & update"
                          : "Add"
                      }}
                    </p>
                    <p
                      v-else-if="
                        item.taxReliefDocuments &&
                        item.taxReliefDocuments.length > 0
                      "
                      class="text-body-2 font-weight-regular d-flex flex-wrap mt-1 cursor-pointer"
                      style="
                        color: #004aff;
                        width: 100px;
                        display: block;
                        text-decoration: underline;
                      "
                      @click="addEditDocuments(index)"
                    >
                      View
                    </p>
                    <p
                      v-else
                      class="text-body-2 font-weight-regular d-flex flex-wrap mt-1"
                      style="color: #004aff; width: 100px; display: block"
                    >
                      -
                    </p>
                  </v-col>
                </v-row>
                <v-row
                  class="mt-2"
                  style="
                    border: 1px solid grey;
                    margin: 0px;
                    border-radius: 7px;
                  "
                >
                  <v-col
                    v-if="!item.isAddEdit"
                    cols="12"
                    class="d-flex justify-end mb-n6"
                  >
                    <v-icon
                      color="secondary"
                      size="14"
                      @click="openStatusEdit(index)"
                      >fas fa-edit</v-icon
                    >
                  </v-col>
                  <v-col cols="12" xl="6">
                    <p class="text-body-2 text-grey-darken-1">
                      Approval Amount
                    </p>
                    <p class="text-body-2 font-weight-regular">
                      {{ checkNullValue(item.Approved_Amount) }}
                    </p>
                  </v-col>
                  <v-col cols="12" xl="6">
                    <p class="text-body-2 text-grey-darken-1">
                      Status
                      <v-tooltip :text="item.Comment" location="bottom">
                        <template v-slot:activator="{ props }">
                          <v-icon
                            v-if="item.Comment"
                            class="ml-2"
                            color="blue"
                            size="17"
                            v-bind="props"
                            >far fa-comment-alt</v-icon
                          >
                        </template>
                      </v-tooltip>
                    </p>
                    <p class="text-body-2 font-weight-regular">
                      {{ checkNullValue(item.Approval_Status) }}
                    </p>
                  </v-col>
                </v-row>
              </td>
              <td
                v-else
                style="max-width: 200px"
                @click="
                  item.taxReliefDeclarations &&
                  item.taxReliefDeclarations.length > 0
                    ? expandEmployee(index)
                    : {}
                "
                :class="
                  item.taxReliefDeclarations &&
                  item.taxReliefDeclarations.length > 0
                    ? 'cursor-pointer'
                    : ''
                "
              >
                <div class="d-flex text-primary text-body-2 font-weight-medium">
                  {{ item.Employee_Name }}
                  <v-spacer></v-spacer>
                  <v-menu
                    v-if="!item.isExpand && !openBottomSheet"
                    transition="scale-transition"
                  >
                    <template v-slot:activator="{ props }">
                      <v-btn variant="plain" v-bind="props">
                        <v-icon color="grey">fas fa-ellipsis-v</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="empAction in empMoreActions"
                        :key="empAction.key"
                        @click="onEmpMoreAction(index)"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="13" class="pr-2">{{
                                empAction.icon
                              }}</v-icon
                              >{{ empAction.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
                <div
                  class="text-grey text-body-2"
                  :class="{ 'mt-n1': !item.isExpand && !openBottomSheet }"
                >
                  {{ item.User_Defined_EmpId }}
                </div>
              </td>
              <td
                v-for="monthYearObj of monthYearListObj"
                :key="'td_' + monthYearObj.month"
                class="text-body-2"
                style="width: 100px"
              >
                <v-text-field
                  v-if="item.isAddEdit"
                  v-model="item['Mon_' + monthYearObj.month]"
                  :rules="[
                    numericRequiredValidation(
                      'Amount',
                      item['Mon_' + monthYearObj.month]
                    ),
                    validateWithRulesAndReturnMessages(
                      item['Mon_' + monthYearObj.month],
                      'Mon',
                      'Amount',
                      true,
                      item.Category_Maximum_Amount
                    ),
                  ]"
                  variant="underlined"
                  density="compact"
                  style="max-width: 80px"
                  :disabled="disableMonthAmountField(monthYearObj, item)"
                  @update:model-value="onChangeFields(item)"
                ></v-text-field>
                <span v-else>{{
                  item.isChild
                    ? item["Mon_" + monthYearObj.month]
                    : item["Total_Mon_" + monthYearObj.month]
                }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </v-form>
    </div>
    <div class="pt-1">
      1 to {{ listItems.length }} of {{ listItems.length }}
    </div>
  </div>
  <div v-else-if="showTaxReliefImport">
    <ImportTaxReliefVue
      :employee-list="employeeIdAndName"
      :assessment-year="assessmentYear"
      :category-list="categoryIdAndName"
      :monthYearListObj="monthYearListObj"
      :monthsList="monthsList"
      @close-import-modal="closeImportModal($event)"
    ></ImportTaxReliefVue>
  </div>
  <AppFetchErrorScreen
    v-else
    key="no-results-screen"
    :main-title="
      originalList.length === 0
        ? ''
        : 'There are no tax relief declarations found for the selected filters/searches.'
    "
    :image-name="originalList.length === 0 ? 'tax/no-tax' : 'common/no-records'"
    :isSmallImage="originalList.length === 0"
  >
    <template #contentSlot>
      <div class="d-flex mt-n12 flex-wrap justify-center">
        <a
          v-if="originalList.length === 0"
          :href="baseUrl + 'payroll/proof-of-investment'"
          style="text-decoration: none"
          class="mt-n5"
          ><v-btn
            color="white mt-n12"
            rounded="lg"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>Back To Proof Of Investment</span></v-btn
          ></a
        >
        <v-btn
          v-if="originalList.length === 0"
          color="white"
          rounded="lg"
          class="ml-2 mt-n12"
          :size="isMobileView ? 'small' : 'default'"
          @click="$emit('refetch-list')"
          ><v-icon>fas fa-redo-alt</v-icon></v-btn
        >
        <v-btn
          v-if="originalList.length > 0"
          color="primary"
          variant="elevated"
          class="ml-4 mt-6 mb-2"
          rounded="lg"
          :size="isMobileView ? 'small' : 'default'"
          @click.stop="$emit('reset-filter')"
        >
          Reset Filter/Search
        </v-btn>
      </div>
    </template>
  </AppFetchErrorScreen>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeAddEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateAddEditForm()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <DocumentUploadAndView
    v-if="showDocUploadModal"
    :employeeId="selectedEmpId"
    :uploadedFiles="selectedDocFiles"
    :restrictAddEditDocument="restrictAddEditDocument"
    @document-uploaded="onUploadSuccess($event)"
    @close-doc-upload-modal="showDocUploadModal = false"
  ></DocumentUploadAndView>
  <v-dialog v-model="showStatusUpdateForm" max-width="500">
    <v-card class="rounded-lg pa-4">
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Status Update</span
        >
        <v-spacer></v-spacer>
        <v-icon color="grey" size="25" @click="cancelUpdateStatusApproval()"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-form ref="statusForm" class="pa-2">
        <v-row>
          <v-col cols="12">
            <CustomSelect
              :items="filteredStatusList"
              label="Approval Status"
              :itemSelected="selectedStatusUpdateData.Approval_Status"
              :rules="[
                required(
                  'Approval Status',
                  selectedStatusUpdateData.Approval_Status
                ),
              ]"
              :isRequired="true"
              @selected-item="onChangeStatusField($event)"
              :isAutoComplete="true"
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            v-if="selectedStatusUpdateData.Approval_Status === 'Approved'"
          >
            <v-text-field
              v-model="selectedStatusUpdateData.Approved_Amount"
              :rules="[
                required(
                  'Approved Amount',
                  selectedStatusUpdateData.Approved_Amount
                ),
                validateWithRulesAndReturnMessages(
                  selectedStatusUpdateData.Approved_Amount,
                  'Mon',
                  'Approved Amount',
                  true
                ),
              ]"
              label="Approved Amount"
              variant="solo"
              @update:model-value="onChangeFields()"
              ><template v-slot:label>
                Approved Amount<span style="color: red"> *</span>
              </template></v-text-field
            >
          </v-col>
          <v-col
            v-if="
              selectedStatusUpdateData.Approval_Status == 'Rejected' ||
              selectedStatusUpdateData.Approval_Status == 'Returned'
            "
            cols="12"
            class="pb-0 mb-2"
          >
            <v-textarea
              v-model="selectedStatusUpdateData.Comment"
              variant="solo"
              auto-grow
              label="Comment"
              rows="2"
              :rules="[
                validateWithRulesAndReturnMessages(
                  selectedStatusUpdateData.Comment,
                  'description',
                  'Comment'
                ),
              ]"
              @update:model-value="onChangeFields()"
            ></v-textarea>
          </v-col>
          <v-col cols="12">
            <div class="d-flex justify-end">
              <v-btn
                @click="cancelUpdateStatusApproval()"
                class="ma-2 pa-2"
                color="grey"
              >
                Cancel
              </v-btn>
              <v-btn
                class="ma-2 pa-1"
                color="secondary"
                :disabled="!isFormDirty"
                @click="validateUpdateApprovalStatus()"
              >
                Save
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script>
import { checkNullValue } from "@/helper.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import {
  LIST_TAX_RELIEF_CATEGORIES,
  ADD_UPDATE_TAX_RELIEF,
  RETRIEVE_MAX_PAYSLIP_MONTH,
  UPDATE_TAX_RELIEF_STATUS,
} from "@/graphql/tax-and-statutory-compliance/taxRelief.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import DocumentUploadAndView from "./DocumentUploadAndView.vue";
import ImportTaxReliefVue from "./ImportTaxRelief.vue";

export default {
  name: "ListTaxRelief",

  mixins: [FileExportMixin, validationRules],

  components: { CustomSelect, DocumentUploadAndView, ImportTaxReliefVue },

  props: {
    consolidatedList: {
      type: Array,
      required: true,
    },
    originalList: {
      type: Array,
      required: true,
    },
    selectedStatus: {
      type: Array,
      required: true,
    },
    assessmentYear: {
      type: [String, Number],
      required: true,
    },
    statusList: {
      type: Array,
      required: true,
    },
    displayImport: {
      type: Boolean,
      default: false,
    },
    fiscalStartMonth: {
      type: String,
      default: "January",
    },
  },

  emits: [
    "refetch-list",
    "reset-filter",
    "add-edit-updated",
    "close-bulk-import",
  ],

  data() {
    return {
      action: "view",
      openMoreMenu: false,
      isLoading: false,
      // list
      listItems: [],
      reliefCategoryList: [],
      reliefCategoryListLoading: false,
      selectedEmpId: 0,
      // add/edit
      isFormDirty: false,
      addEditItemIndex: 0,
      validationMessages: [],
      showValidationAlert: false,
      openBottomSheet: false,
      maxPayslipMonthYear: {},
      // document
      showDocUploadModal: false,
      restrictAddEditDocument: false,
      selectedDocFiles: [],
      // status update
      filteredStatusList: [],
      showStatusUpdateForm: false,
      selectedStatusUpdateData: {},
      // import
      showTaxReliefImport: false,
    };
  },

  computed: {
    categoryIdAndName() {
      let categoryList = this.reliefCategoryList.map((el) => {
        return {
          Tax_Relief_Category_Id: el.Tax_Relief_Category_Id,
          Tax_Relief_Title: el.Tax_Relief_Title,
        };
      });
      if (categoryList && categoryList.length) {
        categoryList.unshift("All");
      }
      return categoryList;
    },
    employeeIdAndName() {
      let employeeList = this.originalList.map((el) => {
        return {
          Employee_Id: el.User_Defined_EmpId,
          Employee_ID: el.Employee_Id,
          Employee_Id_Name: el.Employee_Name + " - " + el.User_Defined_EmpId,
          Employee_Name: el.Employee_Name,
          Date_Of_Join: el.Date_Of_Join,
        };
      });
      if (employeeList && employeeList.length) {
        employeeList.unshift("All");
      }
      return employeeList;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    monthYearList() {
      // Set the current year
      const endMonthAndYear =
        this.fiscalStartMonth + " - " + this.assessmentYear;
      // Parse the endMonthAndYear string
      const endMoment = moment(endMonthAndYear, "MMMM - YYYY");
      // Set the start month based on the parsed endMonthAndYear
      let startMoment;
      if (this.fiscalStartMonth === "January") {
        startMoment = endMoment;
      } else {
        startMoment = endMoment.clone().subtract(12, "months");
      }

      // Initialize an array to store the 12 months
      const monthYear = [];

      // Loop through 12 months starting from the start month
      for (let i = 0; i < 12; i++) {
        const currentMonth = startMoment.clone().add(i, "months");

        // Concatenate the month and year
        const formattedMonth = currentMonth.format("MMM - YYYY");
        monthYear.push(formattedMonth);
      }
      return monthYear;
    },
    monthYearListObj() {
      // Set the current year
      const endMonthAndYear =
        this.fiscalStartMonth + " - " + this.assessmentYear;
      // Parse the endMonthAndYear string
      const endMoment = moment(endMonthAndYear, "MMMM - YYYY");

      // Set the start month based on the parsed endMonthAndYear
      let startMoment;
      if (this.fiscalStartMonth === "January") {
        startMoment = endMoment;
      } else {
        startMoment = endMoment.clone().subtract(12, "months");
      }

      // Initialize an array to store the 12 months
      const monthYearObj = [];

      // Loop through 12 months starting from the start month
      for (let i = 0; i < 12; i++) {
        const currentMonth = startMoment.clone().add(i, "months");

        // Concatenate the month and year
        const formattedMonth = currentMonth.format("MMM - YYYY");
        monthYearObj.push({
          month: currentMonth.format("MMM"),
          monthYear: formattedMonth,
        });
      }
      return monthYearObj;
    },
    monthsList() {
      // Set the current year
      const endMonthAndYear =
        this.fiscalStartMonth + " - " + this.assessmentYear;
      // Parse the endMonthAndYear string
      const endMoment = moment(endMonthAndYear, "MMMM - YYYY");

      // Set the start month based on the parsed endMonthAndYear
      let startMoment;
      if (this.fiscalStartMonth === "January") {
        startMoment = endMoment;
      } else {
        startMoment = endMoment.clone().subtract(12, "months");
      }

      // Initialize an array to store the 12 months
      const monthsList = [];

      // Loop through 12 months starting from the start month
      for (let i = 0; i < 12; i++) {
        const currentMonth = startMoment.clone().add(i, "months");
        monthsList.push(currentMonth.format("MMM"));
      }
      return monthsList;
    },
    listHeaders() {
      return ["Employees"].concat(this.monthYearList);
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
        {
          key: "Import",
          icon: "fas fa-file-import",
        },
      ];
      return actions;
    },
    empMoreActions() {
      let actions = [
        {
          key: "Add",
          icon: "fas fa-plus",
        },
      ];
      return actions;
    },
    // search in shares list
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return moment(date).format(orgDateFormat);
      };
    },
    disableMonthAmountField() {
      return (monthYearObj, item) => {
        const checkMonth = moment(monthYearObj.monthYear, "MMM - YYYY");
        const doj = moment(item.Date_Of_Join, "YYYY-MM-DD").format(
          "MMM - YYYY"
        );
        if (checkMonth.isBefore(doj)) {
          return true;
        } else if (
          this.maxPayslipMonthYear &&
          Object.keys(this.maxPayslipMonthYear).length > 0
        ) {
          let maxPayslip = this.maxPayslipMonthYear[this.selectedEmpId];
          const referenceDate = moment(maxPayslip, "MMM,YYYY");
          if (checkMonth.isAfter(referenceDate)) {
            return false;
          } else {
            return true;
          }
        } else return false;
      };
    },
  },

  watch: {
    searchValue() {
      this.onApplySearch();
    },
    consolidatedList(val) {
      this.listItems = val;
      this.onApplySearch();
    },
    openBottomSheet(val) {
      this.$emit("is-bottom-sheet-opened", val);
    },
  },

  mounted() {
    if (this.displayImport) {
      this.showTaxReliefImport = true;
    }
    this.listItems = this.consolidatedList;
    this.onApplySearch();
    this.retrieveReliefCategories();
  },

  methods: {
    checkNullValue,
    closeImportModal(refetch) {
      if (refetch) {
        this.$emit("refetch-list");
      }
      this.showTaxReliefImport = false;
      this.$emit("close-bulk-import");
    },
    cancelUpdateStatusApproval() {
      this.showStatusUpdateForm = false;
      this.formDirty = false;
    },
    async validateUpdateApprovalStatus() {
      let isFormValid = await this.$refs.statusForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.updateApprovalStatus();
      }
    },
    updateApprovalStatus() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_TAX_RELIEF_STATUS,
          variables: {
            taxReliefDeclarationId:
              this.selectedStatusUpdateData.Tax_Relief_Declaration_Id,
            approvalStatus: this.selectedStatusUpdateData.Approval_Status,
            approvedAmount:
              this.selectedStatusUpdateData.Approval_Status === "Approved" &&
              this.selectedStatusUpdateData.Approved_Amount
                ? this.selectedStatusUpdateData.Approved_Amount.toString()
                : "",
            comment:
              (this.selectedStatusUpdateData.Approval_Status === "Rejected" ||
                this.selectedStatusUpdateData.Approval_Status === "Returned") &&
              this.selectedStatusUpdateData.Comment
                ? this.selectedStatusUpdateData.Comment
                : "",
          },
          client: "apolloClientAK",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Tax relief status updated successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.cancelUpdateStatusApproval();
          vm.$emit("refetch-list");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },
    onChangeStatusField(status) {
      this.onChangeFields();
      this.selectedStatusUpdateData.Approval_Status = status;
    },
    onApplySearch() {
      let val = this.searchValue;
      if (!val) {
        this.listItems = this.consolidatedList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.consolidatedList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.listItems = searchItems;
      }
    },
    expandEmployee(selIndex) {
      if (!this.listItems[selIndex].isExpand) {
        let originalArray = this.listItems;
        let taxDecArray = [];
        if (this.action === "add") {
          taxDecArray.push({
            Mon_Jan: 0,
            Mon_Feb: 0,
            Mon_Mar: 0,
            Mon_Apr: 0,
            Mon_May: 0,
            Mon_Jun: 0,
            Mon_Jul: 0,
            Mon_Aug: 0,
            Mon_Sep: 0,
            Mon_Oct: 0,
            Mon_Nov: 0,
            Mon_Dec: 0,
            Approved_Amount: null,
            Approval_Status: "Declared",
            Tax_Relief_Category_Id: 0,
            Category_Maximum_Amount: "",
            Tax_Relief_Declaration_Id: 0,
            taxReliefDocuments: [],
            isAddEdit: true,
            isChild: true,
            Employee_Id: this.listItems[selIndex].Employee_Id,
            Date_Of_Join: this.listItems[selIndex].Date_Of_Join,
          });
          this.addEditItemIndex = selIndex + 1;
          this.retrieveMaxPayslipMonth();
        } else {
          this.listItems[selIndex].taxReliefDeclarations.forEach((obj) => {
            if (this.selectedStatus.length > 0) {
              if (this.selectedStatus.includes(obj.Approval_Status)) {
                taxDecArray.push({
                  isChild: true,
                  ...this.listItems[selIndex],
                  ...obj,
                });
              }
            } else {
              taxDecArray.push({
                isChild: true,
                ...this.listItems[selIndex],
                ...obj,
              });
            }
          });
        }
        const newArray = [
          ...originalArray.slice(0, selIndex + 1),
          ...taxDecArray,
          ...originalArray.slice(selIndex + 1),
        ];
        this.listItems = newArray;
        this.listItems[selIndex].isExpand = true;
      } else {
        this.action = "view";
        this.listItems[selIndex].isExpand = false;
        this.listItems = this.consolidatedList;
        this.onApplySearch();
      }
    },
    onEmpMoreAction(index) {
      this.action = "add";
      this.selectedEmpId = this.listItems[index].Employee_Id;
      this.openBottomSheet = true;
      this.expandEmployee(index);
    },
    openEditForm(index) {
      this.action = "edit";
      this.addEditItemIndex = index;
      this.selectedEmpId = this.listItems[index].Employee_Id;
      this.openBottomSheet = true;
      this.listItems[index].isAddEdit = true;
      this.retrieveMaxPayslipMonth();
    },
    openStatusEdit(index) {
      this.showStatusUpdateForm = true;
      this.selectedStatusUpdateData = JSON.parse(
        JSON.stringify(this.listItems[index])
      );
      //Form the filterStatusOptions
      this.formFilteredStatus(this.selectedStatusUpdateData.Approval_Status);
    },
    formFilteredStatus(data) {
      switch (data) {
        case "Declared":
          this.filteredStatusList = ["Declared", "Applied"];
          break;
        case "Applied":
          this.filteredStatusList = [
            "Applied",
            "Approved",
            "Rejected",
            "Returned",
          ];
          break;
        case "Approved":
          this.filteredStatusList = ["Approved", "Reopened"];
          break;
        case "Rejected":
          this.filteredStatusList = ["Rejected", "Reopened"];
          break;
        case "Reopened":
          this.filteredStatusList = [
            "Reopened",
            "Applied",
            "Approved",
            "Rejected",
          ];
          break;
        case "Returned":
          this.filteredStatusList = ["Returned", "Applied", "Rejected"];
          break;
      }
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      } else {
        this.showTaxReliefImport = true;
      }
      this.openMoreMenu = false;
    },

    onChangeFields(item = null) {
      this.isFormDirty = true;
      if (item) {
        let monthTotalMount = 0;
        const totalAmounts = {
          Mon_Jan: 0,
          Mon_Feb: 0,
          Mon_Mar: 0,
          Mon_Apr: 0,
          Mon_May: 0,
          Mon_Jun: 0,
          Mon_Jul: 0,
          Mon_Aug: 0,
          Mon_Sep: 0,
          Mon_Oct: 0,
          Mon_Nov: 0,
          Mon_Dec: 0,
        };
        for (const month in totalAmounts) {
          if (item[month]) {
            monthTotalMount += parseInt(item[month]);
          }
        }
        let maxLimit = item.Category_Maximum_Amount;
        if (!maxLimit) {
          let selectedReliefCategory = this.reliefCategoryList.filter(
            (el) => el.Tax_Relief_Category_Id == item.Tax_Relief_Category_Id
          );
          maxLimit =
            selectedReliefCategory && selectedReliefCategory.length > 0
              ? selectedReliefCategory[0].Maximum_Amount
              : "";
        }
        if (maxLimit && monthTotalMount > maxLimit) {
          let snackbarData = {
            isOpen: true,
            message: `Total amount should not exceed the maximum tax relief limit of ${maxLimit}`,
            type: "warning",
          };
          this.showAlert(snackbarData);
        } else {
          let snackbarData = {
            isOpen: false,
            message: "",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      }
    },

    onChangeCustomSelectField(value, index, field) {
      if (field == "Tax_Relief_Category_Id") {
        let selectedReliefCategory = this.reliefCategoryList.filter(
          (el) => el.Tax_Relief_Category_Id == value
        );
        this.listItems[index]["Category_Maximum_Amount"] =
          selectedReliefCategory && selectedReliefCategory.length > 0
            ? selectedReliefCategory[0].Maximum_Amount
            : "";
      }
      this.listItems[index][field] = value;
      this.$refs.addEditTaxReliefForm.validate();
      this.onChangeFields();
    },

    onUploadSuccess(fileNames) {
      this.listItems[this.addEditItemIndex].taxReliefDocuments = fileNames;
      this.selectedDocFiles = fileNames;
      this.showDocUploadModal = false;
      if (!this.openBottomSheet) {
        this.addUpdateTaxReliefDeclarations();
      } else {
        this.isFormDirty = true;
      }
    },

    addEditDocuments(index) {
      this.addEditItemIndex = index;
      this.selectedDocFiles = this.listItems[index].taxReliefDocuments;
      this.restrictAddEditDocument =
        this.listItems[index].Approval_Status === "Approved" ||
        this.listItems[index].Approval_Status === "Rejected";
      this.showDocUploadModal = true;
    },

    retrieveMaxPayslipMonth() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_MAX_PAYSLIP_MONTH,
          client: "apolloClientAI",
          variables: {
            employeeIds: [vm.selectedEmpId],
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveMaxPayslip &&
            !response.data.retrieveMaxPayslip.errorCode
          ) {
            const { maxPayslip } = response.data.retrieveMaxPayslip;
            vm.maxPayslipMonthYear[vm.selectedEmpId] =
              maxPayslip[0].Max_Payslip;
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },

    retrieveReliefCategories() {
      let vm = this;
      vm.reliefCategoryListLoading = true;
      vm.$apollo
        .query({
          query: LIST_TAX_RELIEF_CATEGORIES,
          client: "apolloClientAI",
          variables: {
            assessmentYear: vm.assessmentYear.toString(),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listTaxReliefCategories &&
            !response.data.listTaxReliefCategories.errorCode
          ) {
            const { taxReliefCategories } =
              response.data.listTaxReliefCategories;
            vm.reliefCategoryList = taxReliefCategories
              ? JSON.parse(taxReliefCategories)
              : [];
          }
          vm.reliefCategoryListLoading = false;
        })
        .catch(() => {
          vm.reliefCategoryListLoading = false;
        });
    },

    closeAddEditForm() {
      this.action = "view";
      this.openBottomSheet = false;
      this.listItems = this.consolidatedList;
      this.listItems = this.listItems.map((item) => {
        item["isExpand"] = false;
        return item;
      });
      this.onApplySearch();
    },

    async validateAddEditForm() {
      let isFormValid = await this.$refs.addEditTaxReliefForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.addUpdateTaxReliefDeclarations();
      }
    },

    addUpdateTaxReliefDeclarations() {
      let vm = this;
      vm.isLoading = true;
      let addEditItem = vm.listItems[vm.addEditItemIndex];
      let documents = vm.ensureArrayFormat(addEditItem.taxReliefDocuments);
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_TAX_RELIEF,
          variables: {
            Tax_Relief_Declaration_Id: addEditItem.Tax_Relief_Declaration_Id
              ? addEditItem.Tax_Relief_Declaration_Id
              : 0,
            Tax_Relief_Category_Id: addEditItem.Tax_Relief_Category_Id,
            Employee_Id: addEditItem.Employee_Id,
            Mon_Jan: addEditItem.Mon_Jan ? addEditItem.Mon_Jan.toString() : "0",
            Mon_Feb: addEditItem.Mon_Feb ? addEditItem.Mon_Feb.toString() : "0",
            Mon_Mar: addEditItem.Mon_Mar ? addEditItem.Mon_Mar.toString() : "0",
            Mon_Apr: addEditItem.Mon_Apr ? addEditItem.Mon_Apr.toString() : "0",
            Mon_May: addEditItem.Mon_May ? addEditItem.Mon_May.toString() : "0",
            Mon_Jun: addEditItem.Mon_Jun ? addEditItem.Mon_Jun.toString() : "0",
            Mon_Jul: addEditItem.Mon_Jul ? addEditItem.Mon_Jul.toString() : "0",
            Mon_Aug: addEditItem.Mon_Aug ? addEditItem.Mon_Aug.toString() : "0",
            Mon_Sep: addEditItem.Mon_Sep ? addEditItem.Mon_Sep.toString() : "0",
            Mon_Oct: addEditItem.Mon_Oct ? addEditItem.Mon_Oct.toString() : "0",
            Mon_Nov: addEditItem.Mon_Nov ? addEditItem.Mon_Nov.toString() : "0",
            Mon_Dec: addEditItem.Mon_Dec ? addEditItem.Mon_Dec.toString() : "0",
            Assessment_Year: vm.assessmentYear.toString(),
            Documents: documents,
          },
          client: "apolloClientAK",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.action === "add" && !vm.selectedEmpDoj
                ? "Tax relief details added successfully"
                : "Tax relief details updated successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("add-edit-updated");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.action === "add" ? "adding" : "updating",
          form: "tax relief details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    ensureArrayFormat(inputArray) {
      if (
        inputArray &&
        inputArray.length > 0 &&
        inputArray[0].hasOwnProperty("File_Name")
      ) {
        return inputArray.map((item) => item.File_Name);
      } else {
        return !inputArray ? [] : inputArray;
      }
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    exportReportFile() {
      let exportArray = [];
      this.listItems.forEach((item) => {
        if (
          item.taxReliefDeclarations &&
          item.taxReliefDeclarations.length > 0 &&
          !item.isChild
        ) {
          item.taxReliefDeclarations.forEach((obj) => {
            exportArray.push({
              ...item,
              ...obj,
              empName: item.Salutation + ". " + item.Employee_Name,
              addedOnFormatted: obj.Added_On
                ? this.formatDate(obj.Added_On)
                : "",
              updatedOnFormatted: obj.Updated_On
                ? this.formatDate(obj.Updated_On)
                : "",
              approvedOnFormatted: obj.Approved_On
                ? this.formatDate(obj.Approved_On)
                : "",
            });
          });
        }
      });
      if (exportArray && exportArray.length > 0) {
        let exportOptions = {
          fileExportData: exportArray,
          fileName: "Tax Relief Declarations",
          sheetName: "Tax Relief Declarations",
          header: [
            {
              header: "Employee Id",
              key: "User_Defined_EmpId",
            },
            {
              header: "Employee Name",
              key: "empName",
            },
            { header: "Relief Category", key: "Tax_Relief_Title" },
            { header: "Jan", key: "Mon_Jan" },
            { header: "Feb", key: "Mon_Feb" },
            { header: "Mar", key: "Mon_Mar" },
            { header: "Apr", key: "Mon_Apr" },
            { header: "May", key: "Mon_May" },
            { header: "Jun", key: "Mon_Jun" },
            { header: "Jul", key: "Mon_Jul" },
            { header: "Aug", key: "Mon_Aug" },
            { header: "Sep", key: "Mon_Sep" },
            { header: "Oct", key: "Mon_Oct" },
            { header: "Nov", key: "Mon_Nov" },
            { header: "Dec", key: "Mon_Dec" },
            { header: "Status", key: "Approval_Status" },
            { header: "Approved Amount", key: "Approved_Amount" },
            { header: "Approved By", key: "Approved_By_Name" },
            { header: "Approved On", key: "approvedOnFormatted" },
            { header: "Added By", key: "Added_By_Name" },
            { header: "Added On", key: "addedOnFormatted" },
            { header: "Updated By", key: "Updated_By_Name" },
            { header: "Updated On", key: "updatedOnFormatted" },
          ],
        };
        this.exportExcelFile(exportOptions);
      } else {
        let snackbarData = {
          isOpen: true,
          message:
            "Unable to export as there are no tax relief declarations applied for any of the employees who's listed here. Please make sure to set up tax relief before attempting to export again.",
          type: "warning",
        };

        this.showAlert(snackbarData);
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
table {
  border-collapse: collapse;
  width: 100%;
}

th {
  border: 2px solid #dddddd;
  text-align: center;
  padding: 8px;
  background-color: #ffffff;
}

td {
  border: 2px solid #dddddd;
  text-align: left;
  padding: 8px;
  background-color: #ffffff;
}

th:first-child {
  position: sticky;
  left: 0;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border: 0px;
}

th:last-child {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  border: 0px;
}

thead th {
  position: sticky;
  top: 0;
  z-index: 2000;
}

@media screen and (max-width: 600px) {
  thead {
    display: contents !important;
  }
  thead th {
    position: relative;
  }
  th:first-child {
    position: relative;
  }
}
</style>
