<template>
  <div class="pa-4 text-center overflow-y-visible">
    <v-btn-group color="secondary" density="comfortable">
      <v-btn class="pe-2" prepend-icon="fas fa-paper-plane" variant="flat">
        <div class="text-none font-weight-regular">Send Invite(s)</div>

        <v-dialog activator="parent" max-width="1000">
          <template v-slot:default="{ isActive }">
            <v-card rounded="lg">
              <v-container class="pr-0">
                <v-row align="center">
                  <v-col cols="7">
                    <div class="text-h5 text-medium-emphasis ps-2">
                      <b>Send Email</b>
                    </div>
                  </v-col>
                  <v-col cols="4">
                    <CustomSelect
                      :items="['Template1', 'Template2', 'Template3']"
                      label="Select Template"
                      density="compact"
                    ></CustomSelect>
                  </v-col>
                  <v-col class="pl-5" cols="1">
                    <v-btn
                      icon="fas fa-times"
                      variant="text"
                      @click="isActive.value = false"
                      color="secondary"
                    ></v-btn>
                  </v-col>
                </v-row>
              </v-container>

              <v-divider class="mb-4"></v-divider>

              <v-card class="px-9 overflow-y-auto">
                <v-container class="pa-0">
                  <v-row justify="space-between" align="center">
                    <v-col class="" cols="10">
                      <CustomSelect
                        label="To"
                        :items="candidates"
                        :chips="true"
                        :closableChips="true"
                        :multiple="true"
                      >
                      </CustomSelect>
                    </v-col>
                    <v-col cols="1.5">
                      <span
                        class="mr-1 cursor-pointer text-blue"
                        color
                        @click="showCC = !showCC"
                        >CC</span
                      >
                      <span
                        @click="showBCC = !showBCC"
                        class="cursor-pointer text-blue"
                        >BCC</span
                      >
                    </v-col>
                  </v-row>
                </v-container>

                <CustomSelect
                  label="CC"
                  :items="candidates"
                  :chips="true"
                  :closableChips="true"
                  :multiple="true"
                  :class="showCC ? '' : 'd-none'"
                >
                </CustomSelect>
                <CustomSelect
                  label="BCC"
                  :items="candidates"
                  :chips="true"
                  :closableChips="true"
                  :multiple="true"
                  :class="showBCC ? '' : 'd-none'"
                >
                </CustomSelect>
                <v-text-field
                  variant="solo"
                  label="From"
                  v-model="sender"
                  disabled
                ></v-text-field>

                <v-container class="ma-0 pa-0">
                  <v-row justify="space-between" align="center">
                    <v-col>Subject</v-col>
                    <v-col cols="2" justify="center">
                      <v-menu>
                        <template v-slot:activator="{ props }">
                          <v-btn
                            v-bind="props"
                            variant="text"
                            append-icon="fas fa-chevron-down"
                            >Placeholder</v-btn
                          >
                        </template>
                        <v-list>
                          <v-list-item
                            v-for="(item, index) in items"
                            :key="index"
                            :value="index"
                          >
                            <v-list-item-title
                              @click="subject = '{{' + item.title + '}}'"
                              >{{ item.title }}</v-list-item-title
                            >
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </v-col>
                  </v-row>
                </v-container>

                <v-text-field
                  label="Subject"
                  variant="solo"
                  single-line
                  v-model="subject"
                ></v-text-field>

                <QuillEditor @editorContent="changeContent"></QuillEditor>

                <v-switch
                  v-model="model"
                  label="Attach job description"
                  :false-value="true"
                  :true-value="false"
                  hide-details
                  color="secondary"
                ></v-switch>

                <v-btn
                  prepend-icon="fas fa-paperclip"
                  variant="primary"
                  color="secondary"
                  :class="model ? 'd-none' : ''"
                  class="h-50"
                >
                  <input type="file" id="attachments" :file="file" />
                  <div>
                    <p>Add Documents</p>
                    <p>8 mb max size</p>
                  </div>
                  {{ file }}
                </v-btn>
              </v-card>

              <v-divider class="mt-2"></v-divider>

              <v-card-actions class="my-2 d-flex justify-end">
                <v-btn
                  class="text-none"
                  text="Cancel"
                  @click="isActive.value = false"
                  rounded="lg"
                ></v-btn>

                <v-btn
                  class="text-none"
                  color="secondary"
                  text="Send"
                  variant="flat"
                  rounded="lg"
                  @click="
                    isActive.value = false;
                    handleSubmit();
                  "
                ></v-btn>
              </v-card-actions>
            </v-card>
          </template>
        </v-dialog>
      </v-btn>
    </v-btn-group>
  </div>
</template>

<script>
import QuillEditor from "./QuillEditor";
import CustomSelect from "../../components/custom-components/CustomSelect.vue";

export default {
  components: {
    QuillEditor,
    CustomSelect,
  },
  data() {
    return {
      model: true,
      file: "",
      showCC: false,
      showBCC: false,
      recipient: "",
      candidates: [
        "Sanket sayal",
        "Sai Nitesh",
        "Prem Kumar",
        "Abhishek",
        "Pasha",
        "sohan",
      ],
      recipients: [],
      sender: "",
      subject: "",
      content: "",
      bcc: "",
      cc: "",
      items: [
        { title: "Hello" },
        { title: "Candidate" },
        { title: "sir/madam" },
        { title: "yours sincierly" },
      ],
      icon: "fas fa-yin-yang",
    };
  },
  methods: {
    handleSubmit() {
      // const user = {
      //   to: this.recipients,
      //   from: this.sender,
      //   cc: this.cc,
      //   bcc: this.bcc,
      //   subject: this.subject,
      //   content: this.content,
      // };
    },
    changeContent(data) {
      this.content = data;
    },
    addRecipients() {
      this.recipients.push(this.recipient);
      this.recipient = "";
    },
    removeRecipient(index) {
      this.recipients.splice(index, 1);
    },
  },
};
</script>
<style>
.v-input_details {
  padding: 0;
}

#recipient {
  border-bottom: 1px solid black;
  padding: 0;
}

#attachments {
  opacity: 0;
  position: absolute;
  z-index: 2;
}

.recipient-chips {
  color: white;
  background-color: #e91e63;
  border-radius: 20px;
  width: max-content;
}
</style>
