import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const LIST_JOB_CANDIDATES = gql`
  mutation CommentQuery(
    $searchString: String!
    $isDropDownCall: Int!
    $jobPostId: [Int]
    $preferredLocation: [Int]
    $status: [Int]
    $action: String
    $employeeId: [Int]
    $stageId: Int
    $candidateId: Int
    $talentPoolId: Int
    $formId: Int
    $offset: Int
    $limit: Int
  ) {
    listJobCandidates(
      searchString: $searchString
      isDropDownCall: $isDropDownCall
      jobPostId: $jobPostId
      preferredLocation: $preferredLocation
      status: $status
      action: $action
      employeeId: $employeeId
      stageId: $stageId
      candidateId: $candidateId
      talentPoolId: $talentPoolId
      formId: $formId
      offset: $offset
      limit: $limit
    ) {
      errorCode
      message
      isRecruiter
      candidateTotalCount
      jobCandidates {
        Candidate_Id
        Source_Type
        First_Name
        Last_Name
        Middle_Name
        Mobile_No
        Mobile_No_Country_Code
        Job_Post_Id
        Job_Post_Name
        Dynamic_Form_Id
        Workflow_Task_Id
        Job_Post_Creator
        First_Interviewer
        Resume
        Other_Attachments {
          File_Name
          Document_Name
        }
        Candidate_Status
        Status_Id
        Preferred_Location
        Last_Interview_Date
        First_Interview_Date
        Last_Stage
        Personal_Email
        Recruiter_Id
        Source
        Skill_Set
        Expected_CTC
        Current_CTC
        Total_Experience_In_Months
        Total_Experience_In_Years
        Notice_Period
        Gender
        Current_Location
        Applicant_Ranking
        Added_On
        Hiring_Date
        Shortlisting_Date
        Offer_Letter_Response_Date
        Offer_Letter_Rolled_Out_Date
        Hiring_Stage_Id
        Hiring_Stage
        Is_Duplicate
        Duplicate_Count
        MPP_Position_Type
        MPP_Status
        Archive_Reason
        Archive_Comment
        Talent_Pool_Name
        DOB
        Blacklisted
        Blacklisted_Comments
        Blacklisted_Attachment_File_Name
        Blacklisted_Reason
        Portal_Access_Enabeld
        Sponsored
      }
    }
  }
`;

export const GET_DUPLICATE_CANDIDATE_LIST = gql`
  query getDuplicateCandidateList($candidateId: Int!) {
    getDuplicateCandidateList(candidateId: $candidateId) {
      errorCode
      message
      duplicateCandidateData
    }
  }
`;
export const GET_JOB_CANDIDATE_ACTIVITY_LOG = gql`
  query getSystemActivityLogs(
    $formId: Int!
    $uniqueId: Int
    $logsFormId: [Int]
  ) {
    getSystemActivityLogs(
      formId: $formId
      uniqueId: $uniqueId
      logsFormId: $logsFormId
    ) {
      errorCode
      message
      activityLogs {
        employeeName
        logTimestamp
        userAction
        uniqueId
        description
        formName
      }
    }
  }
`;

export const LIST_JOB_POSTS = gql`
  query CommentQuery(
    $searchString: String
    $designation: Int
    $location: [Int]
    $functionalArea: Int
    $jobType: Int
    $closingDate: Date
    $status: Int
    $skills: [Int]
    $qualification: [Int]
    $isDropDownCall: Int!
    $action: String
    $formId: Int
    $stageId: Int
  ) {
    listJobPost(
      searchString: $searchString
      designation: $designation
      location: $location
      functionalArea: $functionalArea
      jobType: $jobType
      closingDate: $closingDate
      status: $status
      skills: $skills
      qualification: $qualification
      isDropDownCall: $isDropDownCall
      action: $action
      formId: $formId
      stageId: $stageId
    ) {
      errorCode
      message
      JobpostDetails {
        Job_Post_Id
        Job_Post_Name
        Client_Name
        No_Of_Vacancies
        Number_Of_Applicants
        Event_Id
        Job_Post_Status
        Status_Id
        Interview_Status
        Service_Provider_Id
        Candidate_Count
      }
    }
  }
`;

export const GET_PANEL_MEMBER_UPCOMMING_INTERVIEW = gql`
  query getPanelMemberUpcomingInterview {
    getPanelMemberUpcomingInterview {
      message
      errorCode
      panelMembers {
        Interview_Id
        Interview_Name
        Round_Id
        Round_Name
        Round_Start_Date_Time
        Round_End_Date_Time
        Round_Status
        Job_Post_Id
        Job_Post_Name
        Candidate_Id
        Candidate_Name
        Panel_Member_Id
        Panel_Member_Name
      }
    }
  }
`;

export const GET_PANEL_MEMBER_PENDING_FEEDBACK = gql`
  query getPanelMemberPendingFeedback {
    getPanelMemberPendingFeedback {
      message
      errorCode
      panelMembers {
        Interview_Id
        Interview_Name
        Round_Id
        Round_Name
        Round_Start_Date_Time
        Round_End_Date_Time
        Round_Status
        Job_Post_Id
        Job_Post_Name
        Candidate_Id
        Candidate_Name
        Panel_Member_Id
        Panel_Member_Name
      }
    }
  }
`;
export const CANDIDATES_DROP_DOWN = gql`
  query samplequery {
    getDropDownBoxDetails {
      errorCode
      message
      locations {
        Location_Id
        Location_Name
        Currency_Id
      }
      currency {
        Currency_Id
        Currency_Name
      }
      workAuthorizations {
        Work_Authorization_Id
        Work_Authorization_Name
      }
      status {
        Id
        Status
      }
    }
  }
`;

export const GET_ROLE_BASED_JOB_POST_MEMBER = gql`
  query getRoleBasedJobPostMember($jobPostId: Int!) {
    getRoleBasedJobPostMember(jobPostId: $jobPostId) {
      message
      errorCode
      panelMembers {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
        Emp_Email
        Mobile_No
        Microsoft_Email
      }
      recruiters {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
        Emp_Email
        Mobile_No
        Microsoft_Email
      }
      hiringManager {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
        Emp_Email
        Mobile_No
        Microsoft_Email
      }
      onboardSpecialList {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
        Emp_Email
        Mobile_No
        Microsoft_Email
      }
    }
  }
`;
export const RETRIVE_CANDIDATE_INVITED_INDIVIDUALS_DETAILS = gql`
  query retrieveCandidateIndividualsDetail($candidateId: Int!) {
    retrieveCandidateIndividualsDetail(candidateId: $candidateId) {
      message
      errorCode
      candidateData {
        name
        email
        designationId
        designationName
        departmentId
        departmentName
        locationId
        locationName
        jobCode
        dateOfJoin
        probationDate
        expireValue
        expireType
        empTypeId
        employeeTypeName
        workScheduleId
        workScheduleName
        organizationGroupId
        organizationGroup
        managerId
        managerName
        organizationUnitId
        organizationUnitName
        businessUnitId
        businessUnit
      }
    }
  }
`;
export const RETRIEVE_JOB_CANDIDATE_DETAILS = gql`
  mutation CommentQuery($candidateId: Int!, $action: String!) {
    retrieveJobCandidates(candidateId: $candidateId, action: $action) {
      errorCode
      message
      validationError
      jobCandidateDetails {
        Candidate_Id
        Candidate_First_Name
        Candidate_Last_Name
        Candidate_Middle_Name
        Emp_Pref_First_Name
        Salutation
        Gender
        Suffix
        Gender_Id
        Gender_Expression
        Gender_Expression_Id
        Gender_Identity_Id
        Gender_Identity
        Physically_Challenged
        Gender_Orientations
        Pronoun
        Personal_Email
        DOB
        Marital_Status_Name
        Marital_Status_Id
        Nationality
        Nationality_Id
        Nationality_Table_Name
        Blood_Group
        pApartment_Name
        pStreet_Name
        pCity
        pBarangay
        pRegion
        pState
        pCountry
        pPincode
        Emergency_Contact_Name
        Emergency_Contact_Relation
        Country_Name
        Current_Employer
        Hiring_Stage
        Notice_Period
        Current_CTC
        Expected_CTC
        Expected_CTC_Sourced_From_Seek
        Resume
        Cover_Letter
        National_Identification_Number
        Passport_No
        Verifier_Name
        Verifier_Phone_Number
        Verifier_Email_Id
        Source_Type
        Photo_Path
        Resume_File_Size
        Mobile_No
        Job_Post_Id
        Job_Post_Name
        Fax_No
        Currency
        Currency_Name
        Status_Id
        Status
        Total_Experience_In_Years
        Total_Experience_In_Months
        Source
        pBarangay_Id
        pCity_Id
        Preferred_Location {
          Location_Id
          Location_Name
        }
        Lang_Known {
          Lang_Id
          Language_Name
          Lang_Spoken
          Lang_Read_Write
          Lang_Proficiency
        }
        Candidate_Dependent {
          Dependent_Id
          Dependent_First_Name
          Relationship
          Gender
        }
        Work_Permit {
          Work_Permit_Id
          Work_Permit
        }
        Other_Attachments {
          File_Name
          Document_Name
        }
        Other_Work_Permit
        Candidate_Education {
          Education_Id
          Education_Type_Id
          Education_Type
          Specialisation
          Institute_Name
          Year_Of_Passing
          Year_Of_Start
          Percentage
          Specialization_Id
          Specialization_Code
          Specialization_Name
          Institution_Id
          Institution_Code
          Institution_Name
          Grade
          Start_Date
          End_Date
          City
          State
          Country
        }
        Candidate_Experience {
          Experience_Id
          Prev_Company_Name
          Prev_Company_Location
          Designation
          Start_Date
          End_Date
          Duration
          Years
          Months
          References {
            Reference_Name
            Reference_Email
            Reference_Number
          }
        }
        Candidate_Certifications {
          Certification_Id
          Certification_Name
          Received_Date
          Certificate_Received_From
          Certificate_File_Name
          Ranking
        }
        Skill_Set
        Mobile_No_Country_Code
        MPP_Position_Type
        MPP_Status
        Blacklisted
        Blacklisted_Comments
        Blacklisted_Attachment_File_Name
        Blacklisted_Reason
        Blacklisted_On
        Blacklisted_By_Name
        Talent_Pool_Id
        Archived
        Sponsored
      }
    }
  }
`;

export const LIST_JOB_LOCATIONS = gql`
  query CommentQuery($jobpostId: Int!) {
    listJobPostLocations(jobpostId: $jobpostId) {
      errorCode
      message
      jobpostLocations {
        Location_Id
        Location_Name
        Currency_Id
      }
    }
  }
`;

export const RETRIEVE_CANDIDATE_INTERVIEW = gql`
  query ($candidateId: Int!) {
    retrieveCandidatesInterview(candidateId: $candidateId) {
      errorCode
      message
      candidateInterview {
        Interview_Id
        Interview_Name
        Interview_Image
        End_Date
        Start_Date
        Job_Post_Id
        Job_Post_Name
        Status
        Client_Id
        Company_Name
        Company_Logo
        Round_Id
        Round_Status
        Round_Name
        Venue
        Completed_Rounds
        Total_Rounds
        PanelMember_Calendar_Event_Id
      }
    }
  }
`;

export const RETRIEVE_CANDIDATE_ROUNDS = gql`
  query ($candidateId: Int!, $interviewId: Int!) {
    retrieveCandidatesRounds(
      candidateId: $candidateId
      interviewId: $interviewId
    ) {
      errorCode
      message
      candidateRounds {
        Interview_Id
        Candidate_Id
        Round_Id
        Round_Name
        Round_result
        Total_Round_Score
        Panel_Score
        Passing_Score
        Round_Start_Date_Time
        Round_End_Date_Time
        Round_Sequence
        Added_On
        Added_By
        panelDetails {
          Panel_Member_Id
          Id_Without_Prefix
          Panel_Member_Name
          Panel_Member_Email
          Designation_Name
          Status
          Total_Score
        }
      }
    }
  }
`;

export const RETRIEVE_CANDIDATE_SCORE_DETAILS = gql`
  query (
    $interviewId: Int!
    $candidateId: Int!
    $roundId: Int!
    $panelMemberId: Int!
  ) {
    retrieveCandidateSkillsScore(
      interviewId: $interviewId
      candidateId: $candidateId
      roundId: $roundId
      panelMemberId: $panelMemberId
    ) {
      errorCode
      message
      candidateSkills {
        Round_Id
        Round_Name
        Candidate_Obtained_Score
        Rating
        Note
        Max_Score_Per_Skill
        Passing_Score
        SkillsCategory {
          Skill_Category_Id
          Skill_Category
          Skills {
            Skill_Id
            Skill_Name
            Skill_Score
            Skill_Comment
            Questions
          }
        }
      }
    }
  }
`;

export const RESUME_PARSER = gql`
  query ($FileName: String!, $Content: String!) {
    resumeParser(FileName: $FileName, Content: $Content) {
      message
      errorCode
      responseData
    }
  }
`;

export const RETRIEVE_OVERALL_CANDIDATE_INTERVIEW_DETAILS = gql`
  query retrieveCandidatesInterviewRounds($candidateId: Int!) {
    retrieveCandidatesInterviewRounds(candidateId: $candidateId) {
      message
      errorCode
      interviewRoundList {
        Interview_Id
        Interview_Name
        Panel_member_Id
        Panel_Member_Name
        Total_Score
        Status
        Rating
        Note
      }
    }
  }
`;

export const LIST_SOURCE_OF_APPLICATION = gql`
  query retrieveSourceMasterList {
    retrieveSourceMasterList {
      message
      errorCode
      source {
        Source_Id
        Source_Title
      }
    }
  }
`;

export const GET_STATUS_LIST = gql`
  query getAtsStatusList($formId: Int!, $conditions: [commonApplyCondition]) {
    getAtsStatusList(formId: $formId, conditions: $conditions) {
      errorCode
      message
      statusList {
        Id
        Status
        Form_Id
        Stage
        Stage_Id
        Is_Default
      }
    }
  }
`;
// ===============
// Mutations
// ===============

export const UPDATE_ROLE_BASED_JOB_POST_MEMBERS = gql`
  mutation updateRoleBasedJobPostMembers(
    $jobPostId: Int!
    $panelMembers: [Int]
    $recruiters: [Int]
    $hiringManager: [Int]
    $onboardSpecialist: [Int]
  ) {
    updateRoleBasedJobPostMembers(
      jobPostId: $jobPostId
      panelMembers: $panelMembers
      recruiters: $recruiters
      hiringManager: $hiringManager
      onboardSpecialist: $onboardSpecialist
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_CANDIDATE_JOB = gql`
  mutation CommentQuery($candidateId: Int!) {
    deleteJobCandidates(candidateId: $candidateId) {
      errorCode
      message
      validationError
    }
  }
`;

export const ADD_JOB_CANDIDATE_DETAILS = gql`
  mutation CommentQuery(
    $salutation: String
    $firstName: String!
    $lastName: String!
    $middleName: String
    $genderId: Int
    $gender: String!
    $dob: String!
    $bloodGroup: String
    $maritalStatus: Int
    $nationality: String
    $nationalityId: Int
    $emailId: String!
    $languagesKnown: [languagesKnown]
    $mobileNo: String!
    $apartmentName: String
    $street: String
    $city: String
    $state: String
    $country: String
    $pincode: String
    $preferredLocation: [Int]
    $candidateEducation: [educationDetails]
    $candidateExperience: [experienceDetails]
    $candidateSkills: [skillDetails]
    $passportNo: String
    $candidateDependent: [String]
    $candidateCertification: [certificationDetails]
    $candidateWorkPermit: [Int]
    $candidateOtherWorkPermit: String
    $verifierName: String
    $verifierPhoneNo: String
    $verifierEmailId: String
    $jobPost: Int
    $currentEmployer: String
    $noticePeriod: Int
    $currentCTC: Float
    $expectedCTC: Float
    $currency: Int
    $resume: String!
    $nationalIdentificationNumber: String
    $candidateProfilePicture: String
    $resumeFileSize: String!
    $status: Int!
    $totalExperienceInYears: Int
    $totalExperienceInMonths: Int
    $source: String
    $skillSet: [String]
    $mobileNoCountryCode: String
    $genderOrientations: String
    $pronoun: String
    $Suffix: String
    $knownAs: String
    $genderIdentityId: Int
    $genderExpressionId: Int
    $physicallyChallenged: Int
    $barangay: String
    $region: String
    $emergencyContactName: String
    $emergencyContactRelation: String
    $emergencyContactNo: String
    $customFieldInputs: customFieldInputs
    $dataPrivacyStatement: Int
    $permanent_barangay_id: Int
    $permanent_city_id: Int
  ) {
    insertJobCandidates(
      salutation: $salutation
      firstName: $firstName
      lastName: $lastName
      middleName: $middleName
      genderId: $genderId
      gender: $gender
      dob: $dob
      bloodGroup: $bloodGroup
      maritalStatus: $maritalStatus
      nationality: $nationality
      nationalityId: $nationalityId
      emailId: $emailId
      languagesKnown: $languagesKnown
      mobileNo: $mobileNo
      apartmentName: $apartmentName
      street: $street
      city: $city
      state: $state
      country: $country
      pincode: $pincode
      preferredLocation: $preferredLocation
      candidateEducation: $candidateEducation
      candidateExperience: $candidateExperience
      candidateSkills: $candidateSkills
      passportNo: $passportNo
      candidateDependent: $candidateDependent
      candidateCertification: $candidateCertification
      candidateWorkPermit: $candidateWorkPermit
      candidateOtherWorkPermit: $candidateOtherWorkPermit
      verifierName: $verifierName
      verifierPhoneNo: $verifierPhoneNo
      verifierEmailId: $verifierEmailId
      jobPost: $jobPost
      currentEmployer: $currentEmployer
      noticePeriod: $noticePeriod
      currentCTC: $currentCTC
      expectedCTC: $expectedCTC
      currency: $currency
      resume: $resume
      nationalIdentificationNumber: $nationalIdentificationNumber
      candidateProfilePicture: $candidateProfilePicture
      resumeFileSize: $resumeFileSize
      status: $status
      totalExperienceInYears: $totalExperienceInYears
      totalExperienceInMonths: $totalExperienceInMonths
      source: $source
      skillSet: $skillSet
      mobileNoCountryCode: $mobileNoCountryCode
      genderOrientations: $genderOrientations
      pronoun: $pronoun
      Suffix: $Suffix
      knownAs: $knownAs
      genderIdentityId: $genderIdentityId
      genderExpressionId: $genderExpressionId
      physicallyChallenged: $physicallyChallenged
      barangay: $barangay
      region: $region
      emergencyContactName: $emergencyContactName
      emergencyContactRelation: $emergencyContactRelation
      emergencyContactNo: $emergencyContactNo
      customFieldInputs: $customFieldInputs
      dataPrivacyStatement: $dataPrivacyStatement
      permanent_barangay_id: $permanent_barangay_id
      permanent_city_id: $permanent_city_id
    ) {
      errorCode
      message
      validationError
      data
    }
  }
`;

export const UPDATE_JOB_CANDIDATE_DETAILS = gql`
  mutation CommentQuery(
    $candidateId: Int!
    $action: String
    $salutation: String
    $firstName: String!
    $lastName: String!
    $middleName: String
    $genderId: Int
    $gender: String!
    $dob: String!
    $bloodGroup: String
    $maritalStatus: Int
    $nationality: String
    $nationalityId: Int
    $emailId: String!
    $languagesKnown: [languagesKnown]
    $mobileNo: String!
    $apartmentName: String
    $street: String
    $city: String
    $state: String
    $country: String
    $pincode: String
    $preferredLocation: [Int]
    $candidateEducation: [educationDetails]
    $candidateExperience: [experienceDetails]
    $candidateSkills: [skillDetails]
    $passportNo: String
    $candidateDependent: [String]
    $candidateCertification: [certificationDetails]
    $candidateWorkPermit: [Int]
    $candidateOtherWorkPermit: String
    $verifierName: String
    $verifierPhoneNo: String
    $verifierEmailId: String
    $jobPost: Int
    $currentEmployer: String
    $noticePeriod: Int
    $currentCTC: Float
    $expectedCTC: Float
    $currency: Int
    $resume: String!
    $nationalIdentificationNumber: String
    $candidateProfilePicture: String
    $resumeFileSize: String!
    $status: Int!
    $totalExperienceInYears: Int
    $totalExperienceInMonths: Int
    $source: String
    $skillSet: [String]
    $mobileNoCountryCode: String
    $genderOrientations: String
    $pronoun: String
    $Suffix: String
    $knownAs: String
    $genderIdentityId: Int
    $genderExpressionId: Int
    $physicallyChallenged: Int
    $permanent_barangay_id: Int
    $permanent_city_id: Int
    $barangay: String
    $region: String
    $emergencyContactName: String
    $emergencyContactRelation: String
    $emergencyContactNo: String
    $customFieldInputs: customFieldInputs
  ) {
    updateJobCandidates(
      candidateId: $candidateId
      action: $action
      salutation: $salutation
      firstName: $firstName
      lastName: $lastName
      middleName: $middleName
      genderId: $genderId
      gender: $gender
      dob: $dob
      bloodGroup: $bloodGroup
      maritalStatus: $maritalStatus
      nationality: $nationality
      nationalityId: $nationalityId
      emailId: $emailId
      languagesKnown: $languagesKnown
      mobileNo: $mobileNo
      apartmentName: $apartmentName
      street: $street
      city: $city
      state: $state
      country: $country
      pincode: $pincode
      preferredLocation: $preferredLocation
      candidateEducation: $candidateEducation
      candidateExperience: $candidateExperience
      candidateSkills: $candidateSkills
      passportNo: $passportNo
      candidateDependent: $candidateDependent
      candidateCertification: $candidateCertification
      candidateWorkPermit: $candidateWorkPermit
      candidateOtherWorkPermit: $candidateOtherWorkPermit
      verifierName: $verifierName
      verifierPhoneNo: $verifierPhoneNo
      verifierEmailId: $verifierEmailId
      jobPost: $jobPost
      currentEmployer: $currentEmployer
      noticePeriod: $noticePeriod
      currentCTC: $currentCTC
      expectedCTC: $expectedCTC
      currency: $currency
      resume: $resume
      nationalIdentificationNumber: $nationalIdentificationNumber
      candidateProfilePicture: $candidateProfilePicture
      resumeFileSize: $resumeFileSize
      status: $status
      totalExperienceInYears: $totalExperienceInYears
      totalExperienceInMonths: $totalExperienceInMonths
      source: $source
      skillSet: $skillSet
      mobileNoCountryCode: $mobileNoCountryCode
      genderOrientations: $genderOrientations
      pronoun: $pronoun
      Suffix: $Suffix
      knownAs: $knownAs
      genderIdentityId: $genderIdentityId
      genderExpressionId: $genderExpressionId
      physicallyChallenged: $physicallyChallenged
      barangay: $barangay
      region: $region
      emergencyContactName: $emergencyContactName
      emergencyContactRelation: $emergencyContactRelation
      emergencyContactNo: $emergencyContactNo
      customFieldInputs: $customFieldInputs
      permanent_barangay_id: $permanent_barangay_id
      permanent_city_id: $permanent_city_id
    ) {
      errorCode
      message
      validationError
    }
  }
`;

export const UPDATE_CANDIDATE_SCORE = gql`
  mutation (
    $interviewId: Int!
    $formId: Int!
    $roundId: Int!
    $panelMemberId: Int!
    $candidateId: Int!
    $note: String
    $rating: String
    $candidateScore: Float!
    $passingScore: Float!
    $noOfPanelMembers: Int!
    $roundSequence: Int!
    $skills: [candidatesSkillsData]!
  ) {
    updateCandidateScore(
      interviewId: $interviewId
      formId: $formId
      roundId: $roundId
      panelMemberId: $panelMemberId
      candidateId: $candidateId
      note: $note
      rating: $rating
      candidateScore: $candidateScore
      passingScore: $passingScore
      noOfPanelMembers: $noOfPanelMembers
      roundSequence: $roundSequence
      skills: $skills
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_INTERVIEW_ROUND_STATUS = gql`
  mutation (
    $interviewId: Int!
    $roundId: Int!
    $candidateId: Int!
    $roundStatus: Int!
  ) {
    updateInterviewRoundStatus(
      interviewId: $interviewId
      roundId: $roundId
      candidateId: $candidateId
      roundStatus: $roundStatus
    ) {
      message
      errorCode
    }
  }
`;

export const UPDATE_CANDIDATE_STATUS = gql`
  mutation (
    $candidateId: [Int!]
    $candidateStatus: Int!
    $portalAccessEnabeld: String
  ) {
    updateCandidateStatus(
      candidateId: $candidateId
      candidateStatus: $candidateStatus
      portalAccessEnabeld: $portalAccessEnabeld
    ) {
      message
      errorCode
    }
  }
`;

export const CANCEL_DECLINE_ROUNDS = gql`
  mutation addCandidatesInterviewRoundFeedBack(
    $status: String!
    $rating: String!
    $candidateRounds: [interviewCandidateRounds]!
    $action: String
  ) {
    addCandidatesInterviewRoundFeedBack(
      status: $status
      rating: $rating
      candidateRounds: $candidateRounds
      action: $action
    ) {
      message
      errorCode
    }
  }
`;
export const SEND_CUSTOM_EMAIL = gql`
  query sendScheduledInterviewEmail(
    $typeOfInterview: String!
    $typeOfSchedule: String!
    $bccEmails: [String!]
    $subject: String!
    $htmlContent: String!
    $summary: String
    $description: String
    $startDateTime: String
    $endDateTime: String
    $location: String
    $formId: Int
    $candidateId: Int
    $templateName: String
    $toMailIds: [String]
    $ccEmails: [String]
    $senderName: String
    $attchmentList: [attachment]
  ) {
    sendScheduledInterviewEmail(
      typeOfInterview: $typeOfInterview
      typeOfSchedule: $typeOfSchedule
      bccEmails: $bccEmails
      ccEmails: $ccEmails
      htmlContent: $htmlContent
      subject: $subject
      summary: $summary
      description: $description
      startDateTime: $startDateTime
      endDateTime: $endDateTime
      location: $location
      formId: $formId
      candidateId: $candidateId
      templateName: $templateName
      toMailIds: $toMailIds
      senderName: $senderName
      attchmentList: $attchmentList
    ) {
      errorCode
      message
    }
  }
`;

export const GET_JOB_HEADER = gql`
  query recruitmentSetting($formId: Int) {
    recruitmentSetting(formId: $formId) {
      errorCode
      message
      settingResult {
        Coverage
        Setting_Id
        Candidate_Portal_Login_Access
        Centralised_Recruitment
        Equal_opportunity_stmt
        Candidate_Application_Form_Type
        Enable_Multiple_Application
        Exp_portal_Expiry_Time_In_Mins
        Career_Portal_Filters
      }
    }
  }
`;
export const GET_PARTNER_ID = gql`
  query getCompanyPartner($Org_Code: String!) {
    getCompanyPartner(Org_Code: $Org_Code) {
      partnerIntegration
    }
  }
`;

export const GET_TALENT_POOL_CANDIDATE_LIST = gql`
  query getTalentPoolCandidateList($talentPoolId: Int!) {
    getTalentPoolCandidateList(talentPoolId: $talentPoolId) {
      message
      errorCode
      candidateList {
        Candidate_Id
        Emp_First_Name
        Emp_Middle_Name
        Emp_Last_Name
        Personal_Email
        DOB
        Mobile_No
        Skill_Set
        Talent_Pool_Id
        Talent_Pool
        Total_Experience_In_Years
        Total_Experience_In_Months
        Job_Post_Name
        Job_Post_Id
      }
    }
  }
`;

export const GET_TALENT_POOL_LIST = gql`
  query getTalentPoolList($formId: Int!) {
    getTalentPoolList(formId: $formId) {
      errorCode
      message
      talentPoolListData {
        talentPoolId
        talentPool
        addedOn
        addedBy
        updatedOn
        updatedBy
        addedByName
        updatedByName
        candidateCount
      }
    }
  }
`;

export const ADD_UPDATE_TALENT_POOL = gql`
  mutation addUpdateTalentPool($talentPoolId: Int!, $talentPool: String!) {
    addUpdateTalentPool(talentPoolId: $talentPoolId, talentPool: $talentPool) {
      errorCode
      message
    }
  }
`;

export const DELETE_TALENT_POOL_DETAILS = gql`
  mutation deleteTalentPoolDetails($talentPoolId: Int!) {
    deleteTalentPoolDetails(talentPoolId: $talentPoolId) {
      errorCode
      message
    }
  }
`;

export const ARCHIVE_CANDIDATE_DETAILS = gql`
  mutation archiveCandidateDetails(
    $candidateId: Int!
    $archiveReasonId: Int!
    $notificationTime: String
    $archiveComment: String
    $archiveStatusId: Int
    $mailContent: String
    $action: String!
    $archiveStatus: String
    $talentPoolId: Int
  ) {
    archiveCandidateDetails(
      candidateId: $candidateId
      archiveReasonId: $archiveReasonId
      notificationTime: $notificationTime
      archiveComment: $archiveComment
      archiveStatusId: $archiveStatusId
      mailContent: $mailContent
      action: $action
      archiveStatus: $archiveStatus
      talentPoolId: $talentPoolId
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_CANDIDATE_TO_TALENT_POOL = gql`
  mutation addCandidateToTalentPool(
    $candidateId: Int!
    $talentPoolId: Int!
    $archiveStatusId: Int!
    $archiveReasonId: Int!
    $notificationTime: String
    $archiveComment: String
    $mailContent: String
  ) {
    addCandidateToTalentPool(
      candidateId: $candidateId
      talentPoolId: $talentPoolId
      archiveStatusId: $archiveStatusId
      archiveReasonId: $archiveReasonId
      notificationTime: $notificationTime
      archiveComment: $archiveComment
      mailContent: $mailContent
    ) {
      errorCode
      message
    }
  }
`;

export const MOVE_TO_JOB_POST = gql`
  mutation moveToJobPost(
    $candidateId: Int!
    $jobPostId: Int!
    $status: Int!
    $formId: Int!
    $talentPoolId: Int
  ) {
    moveToJobPost(
      candidateId: $candidateId
      jobPostId: $jobPostId
      status: $status
      formId: $formId
      talentPoolId: $talentPoolId
    ) {
      errorCode
      message
      validationError
    }
  }
`;

export const MOVE_TO_ANOTHER_TALENT_POOL = gql`
  mutation moveToAnotherTalentPool(
    $candidateId: Int!
    $formId: Int!
    $talentPoolId: Int!
  ) {
    moveToAnotherTalentPool(
      candidateId: $candidateId
      formId: $formId
      talentPoolId: $talentPoolId
    ) {
      errorCode
      message
      validationError
    }
  }
`;

export const GET_ARCHIVE_REASONS_LIST = gql`
  query getArchiveReasonsList($formId: Int!, $stageId: Int) {
    getArchiveReasonsList(formId: $formId, stageId: $stageId) {
      message
      errorCode
      archiveReasonList {
        Reason_Id
        Reason
        Stage
      }
    }
  }
`;

export const UPLOAD_BULK_CANDIDATE_DETAILS = gql`
  mutation uploadBulkCandidateDetails($candidateList: [candidateData!]!) {
    uploadBulkCandidateDetails(candidateList: $candidateList) {
      errorCode
      message
      validationError
    }
  }
`;

export const MOVE_ARCHIVE_TO_CANDIDATE = gql`
  mutation moveArchiveToCandidate($candidateId: Int!, $formId: Int!) {
    moveArchiveToCandidate(formId: $formId, candidateId: $candidateId) {
      errorCode
      message
      validationError
    }
  }
`;

export const MOVE_CANDIDATE_TO_BLACKLIST = gql`
  mutation moveCandidateToBlackedList(
    $candidateId: Int!
    $blackedReasonId: Int!
    $blackedComment: String
    $blackedAttachment: [BlackedListAttachmentInput!]
  ) {
    moveCandidateToBlackedList(
      candidateId: $candidateId
      blackedReasonId: $blackedReasonId
      blackedComment: $blackedComment
      blackedAttachment: $blackedAttachment
    ) {
      errorCode
      message
      validationError
    }
  }
`;

export const REMOVE_CANDIDATE_FROM_BLACKLIST = gql`
  mutation moveBlackedListToCandidate($candidateId: Int!) {
    moveBlackedListToCandidate(candidateId: $candidateId) {
      errorCode
      message
      validationError
    }
  }
`;

export const GENERATE_PASSCODE_FOR_PORTAL_ACCESS = gql`
  mutation generatePassCodeForPortalAccess($emailId: String!) {
    generatePassCodeForPortalAccess(emailId: $emailId) {
      errorCode
      message
    }
  }
`;
export const PASSCODE_VALIDATE_PORTAL_ACCESS = gql`
  mutation passcodeValidatePortalAccess($emailId: String!, $passCode: Int!) {
    passcodeValidatePortalAccess(emailId: $emailId, passCode: $passCode) {
      errorCode
      message
      data
    }
  }
`;
export const RETRIVE_CANDIDATE_APPLICATION_STATUS = gql`
  query retrieveCandidateApplicationStatus($candidateId: Int!) {
    retrieveCandidateApplicationStatus(candidateId: $candidateId) {
      errorCode
      message
      applicationStatus {
        candidateId
        jobPostId
        jobPostName
        status
        lastUpdated
        jobPostStatus
      }
    }
  }
`;
export const APPLY_CANDIDATE_MULTIPLE_JOBS = gql`
  mutation applyCandidateMultipleJobs(
    $candidateId: Int!
    $jobPostIds: [Int!]!
  ) {
    applyCandidateMultipleJobs(
      candidateId: $candidateId
      jobPostIds: $jobPostIds
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_CANDIDATE_PORTAL_ACCESS = gql`
  mutation updateCandidatePortalAccess(
    $candidateId: [Int!]!
    $portalAccessEnabeld: String!
  ) {
    updateCandidatePortalAccess(
      candidateId: $candidateId
      portalAccessEnabeld: $portalAccessEnabeld
    ) {
      message
      errorCode
    }
  }
`;
