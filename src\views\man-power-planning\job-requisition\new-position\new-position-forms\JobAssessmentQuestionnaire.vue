<template>
  <div>
    <v-overlay
      :model-value="showAddForm"
      @click:outside="onCloseOverlay()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:93vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">
              Job Analysis Questionnaire
            </div>
            <v-btn
              icon
              class="clsBtn cursor-pointer"
              variant="text"
              @click="onCloseOverlay()"
            >
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </v-card-title>
          <v-form ref="workflowForm" class="px-8">
            <v-card class="pa-3 mt-5">
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="green-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Duties And Responsibilities
              </div>
              <div class="my-4">
                <v-data-table
                  :headers="tableHeaders"
                  :items="dutiesFormData"
                  fixed-header
                  :hide-default-footer="true"
                  style="box-shadow: none !important"
                  class="elevation-1 custom-scroll-table"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white"
                      :class="
                        isMobileView
                          ? ' v-data-table__mobile-table-row mt-2'
                          : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
                        >
                          Regular Duties
                        </div>
                        <section class="d-flex align-center">
                          <v-tooltip
                            :text="item?.Regular_Duties"
                            location="bottom"
                            max-width="300"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                v-bind="props"
                                class="text-body-2 text-truncate text-start text-primary"
                                style="max-width: 200px"
                              >
                                {{ checkNullValue(item.Regular_Duties) }}
                              </div>
                            </template>
                          </v-tooltip>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
                        >
                          No. of Hours Performed per Period
                        </div>
                        <section
                          class="d-flex align-center"
                          style="max-width: 300px"
                        >
                          {{ checkNullValue(item.No_Of_Hours_Period) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView
                            ? `width: ${windowWidth - 100}px`
                            : 'width: 200px'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
                        >
                          Period
                        </div>
                        <section
                          class="d-flex align-center"
                          style="max-width: 300px"
                        >
                          {{ checkNullValue(item.Period) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView
                            ? `width: ${windowWidth - 100}px`
                            : 'width: 200px'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
                        >
                          Competencies
                        </div>
                        <section
                          class="d-flex align-center"
                          style="max-width: 300px"
                        >
                          {{ checkNullValue(item.Competency) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
                        >
                          Required Rating of Competency
                        </div>
                        <section
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 500px; '
                              : 'max-width: 200px; '
                          "
                        >
                          <v-rating
                            v-model="item.Rating_Of_Competency"
                            readonly
                            density="default"
                            half-increments
                            :length="5"
                            :size="40"
                            active-color="primary"
                            color="orange-lighten-1"
                          ></v-rating>
                        </section>
                      </td>
                      <td
                        class="text-body-2 text-center"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        @click.stop="
                          {
                          }
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
                        >
                          Actions
                        </div>
                        <ActionMenu
                          v-if="formAccess && formAccess.update"
                          @selected-action="onActionsDuties($event, item)"
                          :actions="['Edit', 'Delete']"
                          :access-rights="formAccess"
                          :disableActionButtons="actionsDisable"
                          iconColor="grey"
                        ></ActionMenu>
                        <div v-else>-</div>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </div>
              <div class="d-flex justify-start py-2">
                <v-btn
                  @click="addJobAssessment()"
                  class="px-6 mr-2 primary"
                  variant="elevated"
                  size="default"
                >
                  <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
                  <span class="primary"> Add New</span></v-btn
                >
              </div>
            </v-card>
            <v-card class="pa-3 mt-10">
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="light-blue"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Working Conditions
              </div>
              <div class="my-4">
                <v-data-table
                  :headers="workConditionHeaders"
                  :items="workFormData"
                  fixed-header
                  :hide-default-footer="true"
                  style="box-shadow: none !important"
                  class="elevation-1"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white"
                      :class="
                        isMobileView
                          ? ' v-data-table__mobile-table-row mt-2'
                          : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Work Area
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.Working_Area) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Percent of Time Spent
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.Time_Spent) }}
                        </section>
                      </td>
                      <td
                        class="text-body-2 text-center"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        @click.stop="
                          {
                          }
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
                        >
                          Actions
                        </div>
                        <ActionMenu
                          v-if="formAccess && formAccess.update"
                          @selected-action="onActionsWork($event, item)"
                          :actions="['Edit', 'Delete']"
                          :access-rights="formAccess"
                          :disableActionButtons="actionsDisable"
                          iconColor="grey"
                        ></ActionMenu>
                        <div v-else>-</div>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </div>
              <div class="d-flex justify-start py-2 pb-8">
                <v-btn
                  @click="addWorkConditions()"
                  class="px-6 mr-2 primary"
                  variant="elevated"
                  size="default"
                >
                  <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
                  <span class="primary"> Add New</span></v-btn
                >
              </div>
            </v-card>
            <div class="card-actions-div">
              <v-card-actions class="d-flex align-end">
                <v-sheet class="align-center text-center" style="width: 100%">
                  <v-row justify="center">
                    <v-col cols="12" class="d-flex justify-space-between pr-6">
                      <div class="d-flex align-center">
                        <v-btn
                          v-if="
                            selectedPositionData?.Position_Title &&
                            this.workFormData.length > 0 &&
                            this.dutiesFormData.length > 0
                          "
                          rounded="lg"
                          variant="outlined"
                          class="mr-3 primary"
                          :disabled="disabledNext"
                          @click="gotoNextEducation()"
                        >
                          Skip
                        </v-btn>
                      </div>
                      <div class="d-flex align-center">
                        <v-btn
                          rounded="lg"
                          class="mr-6 primary"
                          @click="onCloseOverlay()"
                          variant="outlined"
                        >
                          Cancel
                        </v-btn>
                        <v-btn
                          rounded="lg"
                          class="mr-1 primary"
                          @click="submitJobAssessment()"
                          variant="elevated"
                          :disabled="!isFormDirty"
                        >
                          Save & Continue
                        </v-btn>
                      </div>
                    </v-col>
                  </v-row>
                </v-sheet>
              </v-card-actions>
            </div>
          </v-form>
        </v-card>
        <v-overlay
          :model-value="openAddEditDuties"
          @click:outside="
            {
            }
          "
          persistent
          class="d-flex justify-end"
        >
          <template v-slot:default="{}">
            <v-card
              class="overlay-card"
              :style="
                windowWidth <= 1264
                  ? 'width:100vw; height: 100vh'
                  : 'width:40vw; height: 100vh'
              "
            >
              <v-card-title class="d-flex bg-primary">
                <span class="text-h6 text-white font-weight-bold py-2">
                  {{
                    selectedDutyForm &&
                    selectedDutyForm.Duties_Responsibility_Id
                      ? "Edit"
                      : "Add"
                  }}
                  Duties And Responsibilities</span
                >
                <v-spacer></v-spacer>
                <div
                  @click="onCloseConfirmDuties()"
                  icon
                  class="clsBtn cursor-pointer"
                  variant="text"
                >
                  <v-icon size="25">fas fa-times</v-icon>
                </div>
              </v-card-title>
              <v-divider></v-divider>
              <v-card-text>
                <v-card
                  class="mx-auto px-3 pt-2 bg-white"
                  rounded="lg"
                  variant="text"
                >
                  <div>
                    <v-form ref="dutiesRefForm">
                      <v-row class="d-flex justify-start align-center">
                        <v-col cols="12">
                          <v-textarea
                            v-model="duties"
                            rows="2"
                            row-height="10"
                            color="primary"
                            hide-details="auto"
                            variant="solo"
                            label="Regular Duties"
                            counter="150"
                            max-length="150"
                            :isRequired="true"
                            :rules="[
                              required('Regular Duties', duties),
                              validateWithRulesAndReturnMessages(
                                duties,
                                'regularDuties',
                                'Regular Duties'
                              ),
                            ]"
                            @update:model-value="onChangeValue()"
                          >
                            <template v-slot:label>
                              Regular Duties
                              <span style="color: red">*</span>
                              <span class=""
                                >(i.e., weekly, monthly, quarterly, or at other
                                regular intervals)</span
                              >
                            </template></v-textarea
                          >
                        </v-col>
                        <v-col cols="12">
                          <v-text-field
                            v-model="noOfHours"
                            ref="'performance'"
                            variant="solo"
                            :isRequired="true"
                            label=""
                            type="number"
                            :min="0"
                            :max="24"
                            active
                            :rules="[
                              numericRequiredValidation(
                                'No. of Hours Performed per Period',
                                noOfHours
                              ),
                              minMaxNumberValidation(
                                'No. of Hours Performed per Period',
                                parseInt(noOfHours),
                                0,
                                selectedPeriod === 'Daily'
                                  ? 24
                                  : selectedPeriod === 'Weekly'
                                  ? 168
                                  : selectedPeriod === 'Monthly'
                                  ? 720
                                  : selectedPeriod === 'Quarterly'
                                  ? 2160
                                  : 8760
                              ),
                            ]"
                            suffix="hour(s)"
                            @update:model-value="onChangeValue()"
                          >
                            <template v-slot:label>
                              No. of Hours Performed per Period
                              <span style="color: red">*</span>
                              <span class="">(e.g. 15 mins. = 0.25 hour)</span>
                            </template></v-text-field
                          >
                        </v-col>
                        <v-col cols="12">
                          <CustomSelect
                            label="Period"
                            :item-selected="selectedPeriod"
                            v-model="selectedPeriod"
                            item-title="name"
                            item-value="name"
                            :items="periodList"
                            :isRequired="true"
                            :isAutoComplete="true"
                            :rules="[required('Period', selectedPeriod)]"
                            @update:model-value="onChangePeriod()"
                          ></CustomSelect
                          ><span class="text-grey-darken-1 text-body-2"
                            >(e.g. daily, weekly, monthly, quarterly,
                            annually)</span
                          >
                        </v-col>
                        <v-col cols="12">
                          <v-text-field
                            v-model="competencies"
                            ref="competencies"
                            variant="solo"
                            :isRequired="true"
                            label=""
                            active
                            :rules="[
                              required('Competencies', competencies),
                              validateWithRulesAndReturnMessages(
                                competencies,
                                'competency',
                                'Competencies'
                              ),
                            ]"
                            @update:model-value="onChangeValue()"
                          >
                            <template v-slot:label>
                              Competencies
                              <span style="color: red">*</span>
                            </template></v-text-field
                          >
                        </v-col>
                        <v-col cols="12">
                          <div>
                            Required Rating of Competency
                            <span class="text-grey-darken-1 text-body-2"
                              >(e.g rate from 1 to 5; 1= lowest, 5=
                              highest)</span
                            >
                          </div>
                          <v-rating
                            :item-labels="['', '', '', '', '']"
                            item-label-position="top"
                            @update:model-value="onChangeValue()"
                            density="default"
                            v-model="rating"
                            :length="5"
                            :size="40"
                            active-color="primary"
                            color="orange-lighten-1"
                          >
                          </v-rating>
                        </v-col>
                      </v-row>
                    </v-form>
                  </div>
                </v-card>
                <div class="card-actions-div">
                  <v-card-actions>
                    <v-sheet
                      class="align-center text-center px-3"
                      style="width: 100%"
                    >
                      <v-row class="d-flex justify-end py-3">
                        <v-btn
                          rounded="lg"
                          variant="outlined"
                          class="mr-3 primary"
                          @click="onCloseConfirmDuties()"
                        >
                          Cancel
                        </v-btn>
                        <v-btn
                          rounded="lg"
                          class="secondary"
                          variant="elevated"
                          @click="addEditDuties()"
                          :disabled="!isUpdatedValue"
                        >
                          {{
                            selectedDutyForm &&
                            selectedDutyForm.Duties_Responsibility_Id
                              ? "Update"
                              : "Submit"
                          }}
                        </v-btn>
                      </v-row>
                    </v-sheet>
                  </v-card-actions>
                </div></v-card-text
              ></v-card
            >
          </template>
        </v-overlay>
        <v-overlay
          :model-value="openAddEditWork"
          @click:outside="
            {
            }
          "
          persistent
          class="d-flex justify-end"
        >
          <template v-slot:default="{}">
            <v-card
              class="overlay-card"
              :style="
                windowWidth <= 1264
                  ? 'width:100vw; height: 100vh'
                  : 'width:40vw; height: 100vh'
              "
            >
              <v-card-title class="d-flex bg-primary">
                <span class="text-h6 text-white font-weight-bold py-2">
                  {{
                    selectedWorkAreaForm &&
                    selectedWorkAreaForm.Working_Condition_Id
                      ? "Edit"
                      : "Add"
                  }}
                  Work Conditions</span
                >
                <v-spacer></v-spacer>
                <div
                  @click="openAddEditWork = false"
                  icon
                  class="clsBtn cursor-pointer"
                  variant="text"
                >
                  <v-icon size="25">fas fa-times</v-icon>
                </div>
              </v-card-title>
              <v-divider></v-divider>
              <v-card-text>
                <v-card
                  class="mx-auto px-3 pt-2 bg-white"
                  rounded="lg"
                  variant="text"
                >
                  <div>
                    <v-form ref="workConditionsRefForm">
                      <v-row class="d-flex justify-start align-center">
                        <v-col cols="12">
                          <CustomSelect
                            label="Work Area"
                            :item-selected="selectedWorkArea"
                            v-model="selectedWorkArea"
                            item-title="name"
                            item-value="name"
                            :items="workAreaList"
                            :isRequired="true"
                            :isAutoComplete="true"
                            :rules="[required('Work Area', selectedWorkArea)]"
                            @update:model-value="onChangeFields()"
                          ></CustomSelect>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field
                            v-model="selectedWorkPercentage"
                            ref="PercentOfTimeSpent"
                            variant="solo"
                            :isRequired="true"
                            :rules="[
                              numericRequiredValidation(
                                'Percent of Time Spent',
                                selectedWorkPercentage
                              ),
                              minMaxNumberValidation(
                                'Percent of Time Spent',
                                parseInt(selectedWorkPercentage),
                                0,
                                100
                              ),
                              numericValidation(
                                'Percent of Time Spent',
                                selectedWorkPercentage
                              ),
                            ]"
                            label="Percent of Time Spent"
                            @update:model-value="onChangeFields()"
                          >
                            <template v-slot:label>
                              Percent of Time Spent
                              <span style="color: red">*</span>
                            </template></v-text-field
                          >
                        </v-col>
                      </v-row>
                    </v-form>
                  </div>
                </v-card>
                <div class="card-actions-div">
                  <v-card-actions class="d-flex align-end">
                    <v-sheet
                      class="align-center text-center"
                      style="width: 100%"
                    >
                      <v-row justify="center">
                        <v-col cols="12" class="d-flex justify-end pr-6">
                          <v-btn
                            rounded="lg"
                            variant="outlined"
                            class="mr-3 primary"
                            @click="openAddEditWork = false"
                          >
                            Cancel
                          </v-btn>
                          <v-btn
                            rounded="lg"
                            class="secondary"
                            variant="elevated"
                            @click="addEditWork()"
                            :disabled="!isWorkFormUpdate"
                          >
                            {{
                              selectedWorkAreaForm &&
                              selectedWorkAreaForm.Working_Condition_Id
                                ? "Update"
                                : "Submit"
                            }}
                          </v-btn>
                        </v-col></v-row
                      ></v-sheet
                    ></v-card-actions
                  >
                </div></v-card-text
              ></v-card
            ></template
          >
        </v-overlay>
      </template>
    </v-overlay>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit Job Analysis Questionnaire form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="confirmClose()"
  >
  </AppWarningModal>
  <AppWarningModal
    v-if="openConfirmationDelete"
    :open-modal="openConfirmationDelete"
    confirmation-heading="Are you sure you want to delete this record ?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openConfirmationDelete = false"
    @accept-modal="confirmDelete()"
  >
  </AppWarningModal>
</template>
<script>
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_EDIT_DUTIES_RESPONSIBILITIES,
  ADD_EDIT_WORKING_CONDITIONS,
  DELETE_JOB_ASSESSMENT,
} from "@/graphql/mpp/newPositionQueries";
import { checkNullValue } from "@/helper";
import validationRules from "@/mixins/validationRules";

export default {
  name: "JobAssessmentQuestionnaire",
  mixins: [validationRules],
  emits: [
    "submitJobAssessment",
    "closeJobAssessmentForm",
    "refresh-form-data",
    "goto-next-education",
  ],
  props: {
    showJobAssessmentForm: {
      type: Boolean,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
    selectedPositionData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      showAddForm: false,
      isFormDirty: false,
      isFormUpdate: false,
      isWorkFormUpdate: false,
      isUpdatedValue: false,
      openAddEditDuties: false,
      openAddEditWork: false,
      isLoading: false,
      openConfirmationPopup: false,
      openConfirmationDelete: false,
      selectedDeleteForm: "",
      duties: "",
      noOfHours: "",
      selectedPeriod: "Daily",
      competencies: "",
      rating: 0,
      dutiesFormData: [],
      selectedDutyForm: null,
      tempSelectedDutyForm: null,
      periodList: [
        { id: 1, name: "Daily" },
        { id: 2, name: "Weekly" },
        { id: 3, name: "Monthly" },
        { id: 4, name: "Quarterly" },
        { id: 5, name: "Annually" },
      ],
      workFormData: [],
      workAreaList: [
        { id: 1, name: "Office" },
        { id: 2, name: "Field" },
      ],
      selectedWorkPercentage: "",
      selectedWorkArea: null,
      selectedWorkAreaForm: null,
      tempSelectedWorkAreaForm: null,
    };
  },
  components: {
    CustomSelect,
    ActionMenu,
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    disabledNext() {
      if (this.isFormDirty) {
        return true;
      } else {
        if (this.workFormData.length > 0 && this.dutiesFormData.length > 0) {
          return false;
        }
        return true;
      }
    },
    actionsDisable() {
      if (
        this.formAccess &&
        !this.formAccess.delete &&
        !this.formAccess.update
      ) {
        return ["Delete", "Edit"];
      } else if (this.formAccess && !this.formAccess.update) {
        return ["Delete"];
      } else if (this.formAccess && !this.formAccess.delete) {
        return ["Edit"];
      } else {
        return [];
      }
    },
    tableHeaders() {
      let headers = [
        {
          title: "Regular Duties",
          align: "start",
          key: "Regular_Duties",
        },
        {
          title: "No. of Hours Performed per Period",
          align: "start",
          key: "No_Of_Hours_Period",
        },
        { title: "Period", align: "start", key: "Period" },
        {
          title: "Competencies",
          align: "start",
          key: "competency",
        },
        {
          title: "Required Rating of Competency",
          align: "start",
          key: "Rating_Of_Competency",
        },
        { title: "Actions", align: "start", sortable: false, key: "" },
      ];
      return headers;
    },
    workConditionHeaders() {
      let headers = [
        {
          title: "Work Area",
          align: "start",
          key: "Working_Area",
        },
        {
          title: "Percent of Time Spent",
          align: "start",
          key: "Time_Spent",
        },
        { title: "Actions", align: "start", sortable: false, key: "" },
      ];
      return headers;
    },
  },
  watch: {
    showJobAssessmentForm(val) {
      this.showAddForm = val;
    },
    openAddEditDuties(val) {
      if (!val) {
        this.isUpdatedValue = false;
      }
    },
    selectedPositionData(val) {
      if (val) {
        if (val.DutiesResponsibilities && val.DutiesResponsibilities.length) {
          this.dutiesFormData = val.DutiesResponsibilities;
        } else {
          this.dutiesFormData = [];
        }
        if (val.WorkingConditions && val.WorkingConditions.length) {
          this.workFormData = val.WorkingConditions;
        } else {
          this.workFormData = [];
        }
      }
    },
  },
  methods: {
    checkNullValue,
    onChange() {
      this.isFormDirty = true;
    },
    onCloseConfirmDuties() {
      this.openAddEditDuties = false;
    },
    gotoNextEducation() {
      this.onCloseConfirmDuties();
      this.$emit("goto-next-education");
    },
    addJobAssessment() {
      this.openAddEditDuties = true;
      this.duties = "";
      this.noOfHours = "";
      this.selectedPeriod = "Daily";
      this.competencies = "";
      this.rating = 0;
      this.selectedDutyForm = null;
      this.tempSelectedDutyForm = null;
    },
    onActionsDuties(key, item) {
      this.selectedDutyForm = item;
      this.tempSelectedDutyForm = item.Period;
      if (key === "Delete") {
        this.selectedDeleteForm = "duties";
        this.openConfirmationDelete = true;
      } else {
        this.isFormUpdate = false;
        this.openAddEditDuties = true;
        this.duties = item.Regular_Duties;
        this.noOfHours = item.No_Of_Hours_Period;
        this.selectedPeriod = item.Period;
        this.competencies = item.Competency;
        this.rating = item.Rating_Of_Competency;
      }
    },
    confirmDelete() {
      this.openConfirmationDelete = false;
      this.handleDeleteDutiesWork(
        this.selectedDeleteForm == "duties"
          ? this.selectedDutyForm?.Duties_Responsibility_Id
          : this.selectedWorkAreaForm?.Working_Condition_Id,
        this.selectedDeleteForm
      );
    },
    onActionsWork(key, item) {
      this.selectedWorkAreaForm = item;
      this.tempSelectedWorkAreaForm = item.Working_Area;
      if (key === "Delete") {
        this.selectedDeleteForm = "workingConditions";
        this.openConfirmationDelete = true;
      } else {
        this.isWorkFormUpdate = false;
        this.openAddEditWork = true;
        this.selectedWorkPercentage = item.Time_Spent;
        this.selectedWorkArea = item.Working_Area;
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onChangeValue() {
      this.onChange();
      this.isUpdatedValue = true;
    },
    onChangePeriod() {
      this.isUpdatedValue = true;
      this.isFormUpdate = true;
    },
    async addEditDuties() {
      const { valid } = await this.$refs.dutiesRefForm.validate();
      if (valid) {
        this.isFormUpdate = false;
        this.addEditDutiesValue();
      }
    },
    onChangeFields() {
      this.isWorkFormUpdate = true;
      this.isFormDirty = true;
    },
    addEditDutiesValue() {
      this.isLoading = true;
      let requestPayload = {
        dutiesResponsibilityId: 0,
        positionRequestId: this.selectedPositionData?.Position_Request_Id,
        regularDuties: this.duties,
        noOfHoursPeriod: parseFloat(this.noOfHours),
        period: this.selectedPeriod,
        competenciesRequired: "Yes",
        competency: this.competencies,
        ratingOfCompetency: this.rating,
        eventId:
          (this.selectedPositionData && this.selectedPositionData.Event_Id) ||
          null,
        status:
          (this.selectedPositionData && this.selectedPositionData.Status) ||
          "Draft",
      };
      if (
        this.selectedDutyForm &&
        this.selectedDutyForm.Duties_Responsibility_Id
      ) {
        requestPayload.dutiesResponsibilityId =
          this.selectedDutyForm.Duties_Responsibility_Id;
        requestPayload.positionRequestId =
          this.selectedPositionData?.Position_Request_Id;
        requestPayload.regularDuties = this.duties;
        requestPayload.noOfHoursPeriod = parseFloat(this.noOfHours);
        requestPayload.period = this.selectedPeriod;
        requestPayload.competenciesRequired = "Yes";
        requestPayload.competency = this.competencies;
        requestPayload.ratingOfCompetency = this.rating;
      }
      this.$apollo
        .mutate({
          mutation: ADD_EDIT_DUTIES_RESPONSIBILITIES,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.addUpdateDutiesResponsibilities &&
            res.data.addUpdateDutiesResponsibilities.message
          ) {
            let snackbarData = {
              isOpen: true,
              message: res.data.addUpdateDutiesResponsibilities.message,
              type: "success",
            };
            this.showAlert(snackbarData);
            this.onCloseConfirmDuties();
            this.$emit("refresh-form-data");
          } else {
            this.handleJobAssessmentErrors();
          }
        })
        .catch((err) => {
          this.handleJobAssessmentErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    addWorkConditions() {
      this.openAddEditWork = true;
      this.selectedWorkArea = "Office";
      this.selectedWorkPercentage = "";
      this.selectedWorkAreaForm = null;
      this.tempSelectedWorkAreaForm = null;
    },
    async addEditWork() {
      const { valid } = await this.$refs.workConditionsRefForm.validate();
      if (valid) {
        this.isWorkFormUpdate = false;
        this.addEditWorkValue();
      }
    },
    addEditWorkValue() {
      this.isLoading = true;
      let requestPayload = {
        workingConditionId: 0,
        workingArea: this.selectedWorkArea,
        timeSpent: parseInt(this.selectedWorkPercentage),
        positionRequestId: this.selectedPositionData?.Position_Request_Id,
        status:
          (this.selectedPositionData && this.selectedPositionData.Status) ||
          "Draft",
        eventId:
          (this.selectedPositionData && this.selectedPositionData.Event_Id) ||
          null,
      };
      if (
        this.selectedWorkAreaForm &&
        this.selectedWorkAreaForm.Working_Condition_Id
      ) {
        requestPayload.workingConditionId =
          this.selectedWorkAreaForm.Working_Condition_Id;
        requestPayload.workingArea = this.selectedWorkArea;
        requestPayload.timeSpent = parseInt(this.selectedWorkPercentage);
        requestPayload.positionRequestId =
          this.selectedPositionData?.Position_Request_Id;
        requestPayload.status =
          (this.selectedPositionData && this.selectedPositionData.Status) ||
          "Draft";
        requestPayload.eventId =
          (this.selectedPositionData && this.selectedPositionData.Event_Id) ||
          null;
      }
      this.$apollo
        .mutate({
          mutation: ADD_EDIT_WORKING_CONDITIONS,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.addUpdateWorkingConditions &&
            res.data.addUpdateWorkingConditions.message
          ) {
            let snackbarData = {
              isOpen: true,
              message: res.data.addUpdateWorkingConditions.message,
              type: "success",
            };
            this.showAlert(snackbarData);
            this.openAddEditWork = false;
            this.$emit("refresh-form-data");
          } else {
            this.handleJobAssessmentErrors();
          }
        })
        .catch((err) => {
          this.handleJobAssessmentErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    handleJobAssessmentErrors(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "Updating",
          form: "Job Analysis Questionnaire details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    onCloseOverlay() {
      this.openConfirmationPopup = true;
    },
    confirmClose() {
      this.openConfirmationPopup = false;
      this.showAddForm = false;
      this.isFormDirty = false;
      this.$emit("closeJobAssessmentForm");
    },
    submitJobAssessment() {
      if (this.dutiesFormData && this.dutiesFormData.length) {
        if (this.workFormData && this.workFormData.length) {
          this.showAddForm = false;
          this.isFormDirty = false;
          this.$emit("submitJobAssessment");
        } else {
          let snackbarData = {
            isOpen: true,
            message:
              "Please at least add one work area before submitting the form.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please at least add one duty before submitting the form.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    handleDeleteDutiesWork(deleteId, deleteItem) {
      this.isLoading = true;
      const requestPayload = {
        deleteId: deleteId,
        tableKeyword: deleteItem,
        positionRequestId: this.selectedPositionData?.Position_Request_Id || 0,
        status:
          (this.selectedPositionData && this.selectedPositionData.Status) ||
          "Draft",
      };
      this.$apollo
        .mutate({
          mutation: DELETE_JOB_ASSESSMENT,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.deleteOpenPositionSubTable &&
            res.data.deleteOpenPositionSubTable.message
          ) {
            let snackbarData = {
              isOpen: true,
              message: res.data.deleteOpenPositionSubTable.message,
              type: "success",
            };
            this.showAlert(snackbarData);
            this.$emit("refresh-form-data");
          } else {
            this.handleJobAssessmentErrors();
          }
        })
        .catch((err) => {
          this.handleJobAssessmentErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
  },
};
</script>
<style scoped>
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
