import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_NPS_FUND_RULES = gql`
  query listNpsRules {
    listNpsRules {
      errorCode
      message
      npsRulesDetails
    }
  }
`;
export const RETRIEVE_NPS_FUND_SLABES = gql`
  query listNpsSlabsDetails {
    listNpsSlabsDetails {
      errorCode
      message
      npsSlabDetails
    }
  }
`;
export const RETRIEVE_NPS_CONFIGURATION_DETAILS = gql`
  query listNpsConfigurationDetails {
    listNpsConfigurationDetails {
      errorCode
      message
      npsConfigurationDetails
    }
  }
`;

//mutation

export const UPDATE_NPS_FUND_RULES = gql`
  mutation updateHdmfRules(
    $npsRulesId: Int!
    $autoDeclaration: String!
    $autoDeclarationApplicableFor: String
    $investmentCategoryId: Int
    $npsDeductionPercentage: Float
  ) {
    updateHdmfRules(
      npsRulesId: $npsRulesId
      autoDeclaration: $autoDeclaration
      autoDeclarationApplicableFor: $autoDeclarationApplicableFor
      investmentCategoryId: $investmentCategoryId
      npsDeductionPercentage: $npsDeductionPercentage
    ) {
      errorCode
      message
    }
  }
`;
export const UPDATE_NPS_CONFIGURATION_RULES = gql`
  mutation addUpdateHdmfConfiguration(
    $npsConfigurationId: Int!
    $employerShare: Float
    $employeeShare: Float
  ) {
    addUpdateHdmfConfiguration(
      npsConfigurationId: $npsConfigurationId
      employerShare: $employerShare
      employeeShare: $employeeShare
    ) {
      errorCode
      message
    }
  }
`;
