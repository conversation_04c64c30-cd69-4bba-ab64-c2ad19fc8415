<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="secondary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12">
            <v-text-field
              v-model="orgGroupCode"
              label="Organisation Group Code"
              density="compact"
              single-line
              variant="solo"
            >
            </v-text-field
          ></v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12">
            <v-text-field
              v-model="orgGroup"
              label="Organisation Group"
              density="compact"
              single-line
              variant="solo"
            >
            </v-text-field
          ></v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12">
            <v-radio-group inline color="secondary" v-model="status">
              <template v-slot:label> Status </template>
              <v-radio label="Active" value="Active"></v-radio>
              <v-radio label="Inactive" value="Inactive"></v-radio>
            </v-radio-group>
          </v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "FormFilter",
  data: () => ({
    openFormFilter: false,
    orgGroupCode: null,
    orgGroup: "",
    status: null,
  }),

  props: {},

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {},
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        orgGroupCode: this.orgGroupCode,
        orgGroup: this.orgGroup,
        status: this.status,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.orgGroupCode = null;
      this.orgGroup = "";
      this.status = null;
    },
  },
};
</script>
