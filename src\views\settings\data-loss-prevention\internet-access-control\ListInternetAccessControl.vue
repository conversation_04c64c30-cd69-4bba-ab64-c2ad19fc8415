<template>
  <div>
    <v-card
      class="py-9 rounded-lg fill-height"
      :class="isMobileView ? '' : 'px-5'"
      elevation="5"
    >
      <v-card-text>
        <v-form ref="dlpSettingsOrg">
          <v-row class="d-flex justify-space-between mb-4">
            <div class="d-flex align-center">
              <v-progress-circular
                model-value="100"
                color="green"
                :size="22"
                class="mr-1"
              ></v-progress-circular>
              <span class="text-h6 text-grey-darken-1 font-weight-bold pl-2">
                {{ accessFormName }}
              </span>
            </div>
          </v-row>
          <v-row>
            <v-col cols="12" sm="6" md="4" lg="4">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("settings.enableInternetAccessControl") }}
              </p>
              <div class="d-flex">
                <span>
                  <v-switch
                    color="primary"
                    class="mt-n2"
                    v-model="enableInternetAccessControl"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                    @change="orgHandleToggleChange()"
                  ></v-switch>
                </span>
                <span class="ml-10 mt-1">
                  <v-chip
                    color="light-blue"
                    variant="flat"
                    @click="
                      updateOrgSettingsToEmpLevel(
                        enableInternetAccessControl,
                        'enableInternetAccessControl',
                        1
                      )
                    "
                    >{{ $t("settings.setAll") }}</v-chip
                  >
                </span>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="4" lg="4">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("settings.notifyInternetAccessViolation") }}
              </p>
              <div class="d-flex">
                <span>
                  <v-switch
                    color="primary"
                    class="mt-n2"
                    v-model="notifyInternetAccessViolation"
                    :disabled="enableInternetAccessControl === 'No'"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                    @change="orgHandleToggleChange()"
                  ></v-switch>
                </span>

                <span class="ml-10 mt-1">
                  <v-chip
                    color="light-blue"
                    variant="flat"
                    @click="
                      updateOrgSettingsToEmpLevel(
                        notifyInternetAccessViolation,
                        'notifyInternetAccessViolation',
                        1
                      )
                    "
                    >{{ $t("settings.setAll") }}</v-chip
                  >
                </span>
              </div>
            </v-col>
            <v-col
              cols="12"
              sm="6"
              md="4"
              lg="4"
              v-if="enableInternetAccessControl === 'Yes' && adminAccess"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("settings.blockedDomains") }}
              </p>
              <p>
                <v-tooltip location="bottom" v-if="isFirstFormDirty">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      :class="
                        enableInternetAccessControl === 'Yes' ||
                        adminAccess ||
                        IsSecondFormDirty
                          ? 'disableAvatar'
                          : 'cursor-pointer'
                      "
                      rounded="lg"
                      v-bind="props"
                      variant="elevated"
                      size="small"
                      class="primary"
                      @click="openEditForm()"
                      >{{ $t("settings.edit") }}</v-btn
                    >
                  </template>
                  <div style="max-width: 200px !important; min-width: 120px">
                    {{ tooltipContentOrg }}
                  </div>
                </v-tooltip>
                <v-btn
                  v-else
                  rounded="lg"
                  variant="elevated"
                  size="small"
                  color="primary"
                  @click="openEditForm()"
                  >{{ $t("settings.edit") }}</v-btn
                >
              </p>
            </v-col>
          </v-row>
          <v-row v-if="enableInternetAccessControl === 'Yes' && adminAccess">
            <v-col cols="12" sm="6" md="4" lg="4">
              <v-text-field
                v-model="internetAccessUpdateFrequency"
                type="number"
                :min="0"
                :max="10"
                variant="solo"
                suffix="min(s)"
                :rules="[
                  numericRequiredValidation(
                    this.$t('settings.internetAccessUpdateFrequency'),
                    internetAccessUpdateFrequency
                  ),
                  numericValidation(
                    this.$t('settings.internetAccessUpdateFrequency'),
                    internetAccessUpdateFrequency
                  ),
                  minMaxNumberValidation(
                    this.$t('settings.internetAccessUpdateFrequency'),
                    internetAccessUpdateFrequency,
                    1,
                    10
                  ),
                ]"
                style="max-width: 300px"
                @update:model-value="
                  validateInput(
                    this.$t('settings.internetAccessUpdateFrequency')
                  )
                "
              >
                <template v-slot:label>
                  <span>{{
                    $t("settings.internetAccessUpdateFrequency")
                  }}</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
          </v-row>
          <AccessControlModal
            :backupOrganizationDetails="backupOrganizationDetails"
            :selectedDlpSetting="selectedDlpSetting"
            v-if="openAppURLCategoryModal || openEmployeeDomainModel"
            @close-modal="closeEditForm()"
            @refetch-setting-data="$emit('refetch-data')"
          />
        </v-form>
      </v-card-text>
    </v-card>
    <div
      class="d-flex align-center my-3"
      :class="isMobileView ? 'justify-center ' : 'justify-end'"
    >
      <v-btn
        color="white"
        rounded="lg"
        class="ml-2 mt-1"
        :size="this.isMobileView ? 'small' : 'default'"
        @click="$emit('refetch-list-data')"
      >
        <v-icon>fas fa-redo-alt</v-icon>
      </v-btn>
    </div>
    <v-row>
      <v-col :cols="12" class="mb-12">
        <div v-if="listLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <v-data-table
          v-else
          :headers="tableHeaders"
          :items="itemList"
          :search="searchValue"
          fixed-header
          :items-per-page="50"
          :items-per-page-options="[
            { value: 50, title: '50' },
            { value: 100, title: '100' },
            { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
          ]"
          :height="
            $store.getters.getTableHeightBasedOnScreenSize(290, itemList)
          "
          style="box-shadow: none !important"
          class="elevation-1"
        >
          <template v-slot:[`header.wcBlockedDomain`]="{ column }">
            <div class="d-flex align-center">
              <span>{{ column.title }}</span>
              <v-tooltip location="bottom">
                <template v-slot:activator="{ props }">
                  <img
                    v-bind="props"
                    v-if="imageName"
                    :src="getFooterImage"
                    style="width: 30px; height: auto"
                    class="mr-5 ml-2"
                    alt="idea-bulb"
                  />
                </template>
                <div style="max-width: 200px !important; min-width: 150px">
                  {{ tooltipDomainContent }}
                </div>
              </v-tooltip>
            </div>
          </template>
          <template
            v-slot:[`header.notifyInternetAccessViolation`]="{ column }"
          >
            <div class="d-flex align-center">
              <span>{{ column.title }}</span>
              <v-tooltip location="bottom">
                <template v-slot:activator="{ props }">
                  <img
                    v-bind="props"
                    v-if="imageName"
                    :src="getFooterImage"
                    style="width: 30px; height: auto"
                    class="mr-5 ml-2"
                    alt="idea-bulb"
                  />
                </template>
                <div style="max-width: 200px !important; min-width: 150px">
                  {{ notifyButtonContent }}
                </div>
              </v-tooltip>
            </div>
          </template>
          <template v-slot:item="{ item }">
            <tr
              class="data-table-tr bg-white"
              :class="
                isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''
              "
            >
              <td
                class="d-none"
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : ' pl-5 font-weight-small'
                "
              >
                <div
                  v-if="isMobileView"
                  class="text-subtitle-1 text-grey-darken-1"
                >
                  {{ $t("settings.employeeId") }}
                </div>
                <section class="d-flex align-center">
                  <span class="text-primary text-body-2 font-weight-regular">
                    <v-tooltip :text="item.employeeName" location="bottom">
                      <template v-slot:activator="{ props }">
                        <span
                          v-bind="
                            item.userDefinedEmpId &&
                            item.userDefinedEmpId.length > 20
                              ? props
                              : ''
                          "
                          >{{ item.userDefinedEmpId }}</span
                        >
                      </template>
                    </v-tooltip>
                  </span>
                </section>
              </td>
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : ' pl-5 font-weight-small'
                "
              >
                <div
                  v-if="isMobileView"
                  class="text-subtitle-1 text-grey-darken-1"
                >
                  {{ $t("settings.employeeName") }}
                </div>
                <section class="d-flex align-center">
                  <span class="text-primary text-body-2 font-weight-regular">
                    <v-tooltip :text="item.employeeName" location="bottom">
                      <template v-slot:activator="{ props }">
                        <span
                          v-bind="
                            item.employeeName && item.employeeName.length > 20
                              ? props
                              : ''
                          "
                          >{{ item.employeeName }}</span
                        >
                      </template>
                    </v-tooltip>
                    <v-tooltip :text="item.userDefinedEmpId" location="bottom">
                      <template v-slot:activator="{ props }">
                        <div
                          v-if="item.userDefinedEmpId"
                          v-bind="
                            item.userDefinedEmpId &&
                            item.userDefinedEmpId.length > 20
                              ? props
                              : ''
                          "
                          class="text-grey"
                        >
                          {{ item.userDefinedEmpId }}
                        </div>
                      </template>
                    </v-tooltip>
                  </span>
                </section>
              </td>
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : ' pl-5'
                "
              >
                <div
                  v-if="isMobileView"
                  class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                >
                  {{ $t("settings.enableInternetAccessControl") }}
                </div>
                <section>
                  <v-switch
                    color="primary"
                    v-model="item.enableInternetAccessControl"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                    @change="handleToggleChange(item)"
                  ></v-switch>
                </section>
              </td>
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : ' pl-5'
                "
              >
                <div
                  v-if="isMobileView"
                  class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                >
                  {{ $t("settings.notifyInternetAccessViolation") }}
                </div>
                <section>
                  <v-switch
                    color="primary"
                    v-model="item.notifyInternetAccessViolation"
                    :disabled="item.enableInternetAccessControl === 'No'"
                    :class="
                      item.enableInternetAccessControl === 'No' || !adminAccess
                        ? 'disableAvatar'
                        : ''
                    "
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                    @change="handleToggleChange()"
                  ></v-switch>
                </section>
              </td>
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : ' pl-5'
                "
              >
                <div
                  v-if="isMobileView"
                  class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                >
                  <v-tooltip location="bottom">
                    <template v-slot:activator="{ props }">
                      <img
                        v-bind="props"
                        v-if="imageName"
                        :src="getFooterImage"
                        style="width: 30px; height: auto"
                        class="mr-5 ml-2"
                        alt="idea-bulb"
                      />
                    </template>
                    <div style="max-width: 200px !important; min-width: 150px">
                      {{ tooltipDomainContent }}
                    </div>
                  </v-tooltip>
                  {{ $t("settings.overrideInternetAccess") }}
                </div>
                <section
                  v-if="
                    item.enableInternetAccessControl === 'No' ||
                    !adminAccess ||
                    IsSecondFormDirty
                  "
                  :style="item.enableInternetAccessControl === 'No'"
                >
                  <v-tooltip location="bottom">
                    <template v-slot:activator="{ props }">
                      <v-avatar
                        v-bind="props"
                        :size="30"
                        :class="
                          item.enableInternetAccessControl === 'No' ||
                          !adminAccess ||
                          IsSecondFormDirty
                            ? 'disableAvatar'
                            : 'cursor-pointer'
                        "
                        color="primary"
                        :disabled="
                          item.enableInternetAccessControl === 'No' ||
                          !adminAccess ||
                          IsSecondFormDirty
                        "
                      >
                        <v-icon size="15" color="white"
                          >fas fa-pencil-alt</v-icon
                        >
                      </v-avatar>
                    </template>
                    <div style="max-width: 200px !important; min-width: 120px">
                      {{ tooltipContent }}
                    </div>
                  </v-tooltip>
                </section>
                <section v-else>
                  <v-avatar
                    v-bind="props"
                    :size="30"
                    color="primary"
                    style="cursor: pointer"
                    @click="onSelectProject(item)"
                  >
                    <v-icon size="15" color="white">fas fa-pencil-alt</v-icon>
                  </v-avatar>
                </section>
              </td>
            </tr>
          </template>
        </v-data-table>
      </v-col>

      <v-bottom-navigation v-if="isFormDirty">
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
          style="width: 100%"
        >
          <v-row class="d-flex justify-start">
            <v-col cols="12" class="d-flex justify-start pl-2">
              <v-btn
                rounded="lg"
                variant="outlined"
                size="small"
                class="primary"
                style="height: 40px"
                :style="
                  isMobileView ? '' : 'margin-top: 20px;margin-bottom:20px;'
                "
                @click="cancelConfirmation()"
                ><span class="primary">{{ $t("settings.cancel") }}</span></v-btn
              >
              <v-btn
                rounded="lg"
                size="small"
                :disabled="botomNvaigationDisableCheck"
                :dense="isMobileView"
                variant="elevated"
                class="primary"
                style="height: 40px; margin-left: 15px"
                :style="
                  isMobileView ? '' : 'margin-top: 20px;margin-bottom:20px;'
                "
                @click="handleSave()"
              >
                {{ $t("settings.save") }}
              </v-btn>
              <span
                v-if="windowWidth > 450"
                style="height: 40px; margin-left: 15px"
                :style="isMobileView ? '' : 'margin-top: 35px;'"
                >{{
                  $t("settings.changedSettings", {
                    count:
                      secondFormCount + firstFormChangeCount + frequencyCount,
                  })
                }}</span
              >
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
    </v-row>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          {{ $t("settings.close") }}
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoadingDetails || isLoadingTrue"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
import {
  UPDATE_ORGANIZATION_LEVEL_DLP_SETTINGS,
  UPDATE_EMPLOYEE_LEVEL_DLP_SETTINGS,
} from "@/graphql/settings/data-loss-prevention/internetAccessControl.js";
const AccessControlModal = defineAsyncComponent(() =>
  import("./AccessControlModal.vue")
);
import mixpanel from "mixpanel-browser";
import { compareAndReturnModifiedArray } from "@/helper";
import validationRules from "@/mixins/validationRules";
export default {
  name: "ListInternetAccessControl",
  components: {
    AccessControlModal,
  },
  mixins: [validationRules],
  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    backupMainList: {
      type: Array,
      required: true,
      default: () => [],
    },
    accessFormName: {
      type: String,
      required: true,
    },
    listLoading: {
      type: Boolean,
      required: true,
    },
    organizationDetails: {
      type: Object,
      required: true,
    },
    backupOrganizationDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      itemList: [],
      selectedItem: {},
      notifyInternetAccessViolation: "",
      enableInternetAccessControl: "",
      openAppURLCategoryModal: false,
      openEmployeeDomainModel: false,
      validationMessages: [],
      showValidationAlert: false,
      isLoadingDetails: false,
      isLoadingTrue: false,
      isFormDirty: false,
      toggleChangesCount: 0,
      toggleChangesHistory: [],
      changedRecords: [],
      changesCheckCount: 0,
      isFirstFormDirty: false,
      IsSecondFormDirty: false,
      modifiedArray: [],
      selectedDlpSetting: null,
      ComparedCountArray: [],
      internetAccessUpdateFrequency: 1,
      imageName: "common/idea-bulb",
      frequencyCount: 0,
      tooltipDomainContent: this.$t("settings.tooltipDomainContent"),
      notifyButtonContent: this.$t("settings.notifyButtonContent"),
    };
  },
  mounted() {
    mixpanel.init("6df21e89c6a0f6b1bc345ae98b6ef36e", {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.items && this.items.length) {
      this.itemList = this.items;
    }
    if (
      this.backupOrganizationDetails &&
      Object.keys(this.backupOrganizationDetails).length > 0
    ) {
      const {
        enableInternetAccessControl,
        notifyInternetAccessViolation,
        internetAccessUpdateFrequency,
      } = this.backupOrganizationDetails;
      this.enableInternetAccessControl = enableInternetAccessControl
        ? enableInternetAccessControl
        : "No";
      this.notifyInternetAccessViolation = notifyInternetAccessViolation
        ? notifyInternetAccessViolation
        : "No";
      this.internetAccessUpdateFrequency = internetAccessUpdateFrequency
        ? internetAccessUpdateFrequency
        : 1;
    }
  },
  watch: {
    items(val) {
      this.itemList = val;
    },
    itemList() {
      let val = this.findModifiedItemInArray();
      this.modifiedArray = [...val];
    },
    openEmployeeDomainModel(val) {
      if (val === false) {
        this.selectedDlpSetting = null;
      }
    },
    internetAccessUpdateFrequency(val) {
      if (
        parseInt(val) ===
        this.backupOrganizationDetails.internetAccessUpdateFrequency
      ) {
        this.frequencyCount = 0;
        this.isFirstFormDirty = false;
      } else {
        this.frequencyCount = 1;
      }
    },
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    tooltipContent() {
      if (!this.adminAccess) {
        return this.$t("settings.accessDeniedUpdateMessage");
      } else if (this.IsSecondFormDirty) {
        return this.$t("settings.saveOrCancelChanges");
      } else {
        return this.$t("settings.enableInternetAccessMessage");
      }
    },
    tooltipContentOrg() {
      if (!this.adminAccess) {
        return this.$t("settings.accessDeniedUpdateMessage");
      } else if (this.isFirstFormDirty) {
        return this.$t("settings.saveOrCancelChanges");
      } else {
        return this.$t("settings.enableInternetAccessMessage");
      }
    },
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getFooterImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.imageName + ".webp");
      else return require("@/assets/images/" + this.imageName + ".png");
    },
    adminAccess() {
      if (!this.formAccess) return false;
      else {
        return (
          this.formAccess.update === 1 &&
          this.formAccess.admin.toLocaleLowerCase() === "admin"
        );
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    botomNvaigationDisableCheck() {
      if (this.isFirstFormDirty === true || this.IsSecondFormDirty === true) {
        return false;
      } else {
        return true;
      }
    },
    firstFormChangeCount() {
      let count = 0;
      if (
        this.enableInternetAccessControl.toLocaleLowerCase() !==
        this.backupOrganizationDetails.enableInternetAccessControl.toLocaleLowerCase()
      ) {
        count++;
      }
      if (
        this.notifyInternetAccessViolation.toLocaleLowerCase() !==
        this.backupOrganizationDetails.notifyInternetAccessViolation.toLocaleLowerCase()
      ) {
        count++;
      }
      return count;
    },
    secondFormCount() {
      return this.compareChanges();
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    tableHeaders() {
      return [
        {
          title: this.$t("settings.employeeId"),
          key: "userDefinedEmpId",
          align: " d-none",
        },
        {
          title: this.$t("settings.employeeName"),
          align: "start",
          key: "employeeName",
        },
        {
          title: this.$t("settings.enableInternetAccessControl"),
          key: "enableInternetAccessControl",
          sortable: true,
        },
        {
          title: this.$t("settings.notifyInternetAccessViolation"),
          key: "notifyInternetAccessViolation",
          sortable: true,
        },
        {
          title: this.$t("settings.overrideInternetAccess"),
          key: "wcBlockedDomain",
        },
      ];
    },
  },
  methods: {
    openEditForm() {
      if (
        this.enableInternetAccessControl === "No" ||
        !this.adminAccess ||
        this.isFirstFormDirty
      ) {
        return;
      }
      this.openAppURLCategoryModal = true;
    },
    compareChanges() {
      let changedCount = 0;

      // Iterate through changed records
      for (let modifiedArray of this.ComparedCountArray) {
        const dlpSettingId = modifiedArray.dlpSettingId;
        const originalRecord = this.backupMainList.find(
          (record) => record.dlpSettingId === dlpSettingId
        );

        // If original record found, compare values
        if (this.backupMainList) {
          if (
            originalRecord.enableInternetAccessControl !==
            modifiedArray.enableInternetAccessControl
          ) {
            changedCount++;
          }
          if (
            originalRecord.notifyInternetAccessViolation !==
            modifiedArray.notifyInternetAccessViolation
          ) {
            changedCount++;
          }
        }
      }
      return changedCount;
    },
    handleToggleChange(item = null) {
      if (
        item &&
        item.enableInternetAccessControl.toLocaleLowerCase() === "no"
      ) {
        this.itemList = this.itemList.map((data) => {
          if (data.dlpSettingId === item.dlpSettingId) {
            // Update the matching item's notifyInternetAccessViolation value
            return {
              ...data, // Spread the existing properties of data
              notifyInternetAccessViolation: "No", // Update notifyInternetAccessViolation
            };
          } else {
            // For non-matching items, keep them unchanged
            return data;
          }
        });
      }
      this.isFormDirty = true;
      this.modifiedArray = [...this.findModifiedItemInArray()];
      if (this.modifiedArray && this.modifiedArray.length > 0) {
        this.IsSecondFormDirty = true;
      } else {
        this.IsSecondFormDirty = false;
      }
    },
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
      this.isFirstFormDirty = true;
    },
    orgHandleToggleChange() {
      if (this.enableInternetAccessControl.toLocaleLowerCase() === "no") {
        this.notifyInternetAccessViolation = "No";
        this.internetAccessUpdateFrequency =
          this.backupOrganizationDetails.internetAccessUpdateFrequency;
      }
      this.isFormDirty = true;
      this.isFirstFormDirty = true;
      if (
        this.enableInternetAccessControl.toLocaleLowerCase() !==
          this.backupOrganizationDetails.enableInternetAccessControl.toLocaleLowerCase() ||
        this.notifyInternetAccessViolation.toLocaleLowerCase() !==
          this.backupOrganizationDetails.notifyInternetAccessViolation.toLocaleLowerCase()
      ) {
        this.isFirstFormDirty = true;
      } else {
        this.isFirstFormDirty = false;
      }
    },
    updateOrgSettingsToEmpLevel(value, key) {
      this.itemList.forEach((obj) => {
        if (
          key === "notifyInternetAccessViolation" &&
          obj["enableInternetAccessControl"].toLocaleLowerCase() === "no"
        ) {
          return;
        }
        obj[key] = value;
      });
      if (
        key === "enableInternetAccessControl" &&
        value.toLowerCase() === "no"
      ) {
        this.itemList.forEach((obj) => {
          obj["notifyInternetAccessViolation"] = "No";
        });
      }
      this.handleToggleChange();
    },
    findModifiedItemInArray() {
      let originalArray = this.backupMainList,
        modifiedArray = this.itemList;
      let findColumn = [
        "enableInternetAccessControl",
        "notifyInternetAccessViolation",
      ];

      let modifiedArrayList = compareAndReturnModifiedArray(
        originalArray,
        modifiedArray,
        findColumn
      );
      this.ComparedCountArray = JSON.parse(JSON.stringify(modifiedArrayList));

      if (modifiedArrayList && modifiedArrayList.length > 0) {
        modifiedArrayList = modifiedArrayList.map((item) => {
          return {
            employeeId: item.employeeId,
            enableInternetAccessControl: item.enableInternetAccessControl,
            notifyInternetAccessViolation: item.notifyInternetAccessViolation,
          };
        });
      }
      return modifiedArrayList;
    },
    closeEditForm() {
      mixpanel.track("internetAccessControl domain view form closed");
      this.openAppURLCategoryModal = false;
      this.openEmployeeDomainModel = false;
      this.selectedDlpSetting = null;
    },
    cancelConfirmation() {
      mixpanel.track("internetAccessControl dlp settings changes canceled");
      // reset the form with backup values
      this.enableInternetAccessControl =
        this.backupOrganizationDetails.enableInternetAccessControl;
      this.notifyInternetAccessViolation =
        this.backupOrganizationDetails.notifyInternetAccessViolation;
      this.internetAccessUpdateFrequency =
        this.backupOrganizationDetails.internetAccessUpdateFrequency;
      this.itemList = JSON.parse(JSON.stringify(this.backupMainList));
      this.isFormDirty = false;
      this.isFirstFormDirty = false;
      this.IsSecondFormDirty = false;
      this.modifiedArray = [];
    },
    async handleSave() {
      if (this.isFirstFormDirty) {
        mixpanel.track(
          "internetAccessControl-organization-dlpsettings-submit-click"
        );
        const { valid } = await this.$refs.dlpSettingsOrg.validate();
        if (valid) {
          this.updateDlpSettings();
        }
      }
      if (this.IsSecondFormDirty) {
        mixpanel.track(
          "internetAccessControl-employee-dlpsettings-submit-click"
        );
        this.updateEmployeeLevelDlpSettings();
      } else return;
    },
    updateDlpSettings() {
      let vm = this;
      vm.isLoadingTrue = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_ORGANIZATION_LEVEL_DLP_SETTINGS,
            variables: {
              Dlp_Settings_Id: vm.backupOrganizationDetails.Dlp_Settings_Id,
              enableInternetAccessControl: vm.enableInternetAccessControl,
              notifyInternetAccessViolation: vm.notifyInternetAccessViolation,
              internetAccessUpdateFrequency: parseInt(
                vm.internetAccessUpdateFrequency
              ),
            },
            client: "apolloClientR",
          })
          .then(() => {
            mixpanel.track(
              "internetAccessControl-dlpSettings-organization-update-success"
            );
            this.isFormDirty = false;
            vm.changedRecords = [];
            let snackbarData = {
              isOpen: true,
              message: this.$t("settings.settingsUpdatedSuccess"),
              type: "success",
            };
            this.isLoadingTrue = false;
            this.$emit("refetch-data");
            vm.showAlert(snackbarData);
          })
          .catch((updateDlpSettingError) => {
            mixpanel.track(
              "internetAccessControl-dlpSettings-organization-update-error"
            );
            vm.handleUpdateError(updateDlpSettingError);
          });
      } catch {
        vm.handleUpdateError();
      }
    },
    onSelectProject(item) {
      if (item.enableInternetAccessControl === "No") {
        return;
      }
      mixpanel.track("internetAccessControl domain view form opened");
      this.openEmployeeDomainModel = true;
      this.selectedDlpSetting = {
        employeeId: item.employeeId,
        dlpBlockedDomainData: [
          {
            WC_Blocked_Domain: item.wcBlockedDomain,
            WC_Blocked_Domains_Id: item.wcBlockedDomainId,
          },
        ],
      };
    },
    updateEmployeeLevelDlpSettings() {
      let vm = this;
      vm.isLoadingDetails = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_EMPLOYEE_LEVEL_DLP_SETTINGS,
            variables: {
              dlpSettingsData: this.modifiedArray,
            },
            client: "apolloClientR",
          })
          .then(() => {
            mixpanel.track(
              "internetAccessControl-dlpSettings-employee-update-success"
            );
            this.isFormDirty = false;
            vm.changedRecords = [];
            let snackbarData = {
              isOpen: true,
              message: this.$t("settings.settingsUpdatedSuccess"),
              type: "success",
            };
            this.isLoadingDetails = false;
            this.$emit("refetch-data");
            vm.showAlert(snackbarData);
          })
          .catch((updateDlpSettingError) => {
            mixpanel.track(
              "internetAccessControl-dlpSettings-employee-update-error"
            );
            vm.handleUpdateError(updateDlpSettingError);
          });
      } catch {
        vm.handleUpdateError();
      }
    },
    handleUpdateError(err = "") {
      this.isLoadingDetails = false;
      this.isLoadingTrue = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: this.accessFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style scoped>
.disableAvatar {
  cursor: not-allowed;
}
.cursorPointer::hover {
  cursor: pointer !important;
}
</style>
