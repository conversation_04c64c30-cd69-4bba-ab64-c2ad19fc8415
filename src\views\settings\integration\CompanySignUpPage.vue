<template>
  <AppLoading v-if="isIrukkaStatusLoading"></AppLoading>
  <ProfileCard class="mt-4 px-4" v-if="formAccess">
    <div id="recruitment" class="rounded-lg ma-1" color="#FDFEFF">
      <v-form ref="companyDetailForm" @submit.prevent="validateCompanyDetails">
        <div class="mb-4 d-flex align-center justify-space-between">
          <div class="mt-4 d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="#7986CB"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Company Info</span
            >
          </div>
        </div>

        <v-row class="mt-1 mb-6">
          <v-col class="ml-4" cols="12" md="3" sm="6">
            <CustomSelect
              v-model="companyDetails.selectedCompanyType"
              :items="companyDetails.dropdownCompanyType"
              item-title="name"
              item-value="companyTypeId"
              label="Company Type"
              :isLoading="isCompanyTypeLoading"
              :rules="[
                required('Company Type', companyDetails.selectedCompanyType),
              ]"
              :isRequired="true"
              :itemSelected="companyDetails.selectedCompanyType"
              @selected-item="companyDetails.selectedCompanyType = $event"
            ></CustomSelect>
          </v-col>
          <v-col class="ml-8" cols="12" md="4" sm="6">
            <div
              :style="!isMobileView && !isTabletView ? 'margin-left: 8vw' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1">Company Name</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(companyDetails.companyName) }}
              </p>
            </div>
          </v-col>
          <v-col class="ml-8" cols="12" md="2" sm="6">
            <div
              :style="!isMobileView && !isTabletView ? 'margin-left: 8vw' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1 full-text">
                Company Description
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(companyDetails.selectedCompanyDescription) }}
              </p>
            </div>
          </v-col>
        </v-row>

        <div class="mb-4 d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="#004D40"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >User Details</span
          >
        </div>
        <v-row class="mt-1">
          <v-col class="ml-4" cols="12" md="3" sm="6">
            <CustomSelect
              v-model="companyDetails.selectedLocation"
              :items="companyDetails.dropdownLocation"
              item-title="Location_Name"
              item-value="Location_Id"
              label="Preferred Location"
              :isLoading="isPreferredLocationLoading"
              :rules="[
                required('Preferred Location', companyDetails.selectedLocation),
              ]"
              :itemSelected="companyDetails.selectedLocation"
              :isRequired="true"
              @selected-item="
                fillOtherLocationDetails(
                  'Location_Id',
                  companyDetails.selectedLocation
                )
              "
            ></CustomSelect>
          </v-col>
          <v-col class="ml-8" cols="12" md="4" sm="6">
            <div
              :style="!isMobileView && !isTabletView ? 'margin-left: 8vw' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1">Age</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(companyDetails.age) }}
              </p>
            </div>
          </v-col>
          <v-col class="ml-8" cols="12" md="2" sm="6">
            <div
              :style="!isMobileView && !isTabletView ? 'margin-left: 8vw' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1 full-text">
                User Name
              </p>
              <p class="text-subtitle-1 font-weight-regular full-text">
                {{ checkNullValue(companyDetails.userName) }}
              </p>
            </div>
          </v-col>
        </v-row>
        <v-row class="mt-1 mb-6">
          <v-col class="ml-4" cols="12" md="3" sm="6">
            <CustomSelect
              v-model="companyDetails.selectedGender"
              :items="companyDetails.dropdownGender"
              item-value="genderId"
              item-title="gender"
              :isLoading="isGenderListLoading"
              label="Gender"
              :itemSelected="companyDetails.selectedGender"
              @selected-item="companyDetails.selectedGender = $event"
            ></CustomSelect>
          </v-col>
          <v-col class="ml-8" cols="12" md="4" sm="6">
            <div
              :style="!isMobileView && !isTabletView ? 'margin-left: 8vw' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1">Mobile Number</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(companyDetails.mobileNumber) }}
              </p>
            </div>
          </v-col>
          <v-col class="ml-8" cols="12" md="2" sm="6">
            <div
              :style="!isMobileView && !isTabletView ? 'margin-left: 8vw' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1">Email</p>
              <p class="text-subtitle-1 font-weight-regular full-text">
                {{ checkNullValue(companyDetails.emailAddress) }}
              </p>
            </div>
          </v-col>
          <v-col class="ml-4" cols="12" md="3" sm="6">
            <CustomSelect
              v-model="companyDetails.selectedLanguage"
              :items="companyDetails.dropdownLanguage"
              item-value="languageId"
              item-title="name"
              :isLoading="isLanguageListLoading"
              label="Language"
              :rules="[required('Language', companyDetails.selectedLanguage)]"
              :isRequired="true"
              :itemSelected="companyDetails.selectedLanguage"
              @selected-item="companyDetails.selectedLanguage = $event"
            ></CustomSelect>
          </v-col>
        </v-row>

        <div class="mb-4 d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="#9E9D24"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Address
          </span>
        </div>

        <v-row class="mt-1">
          <v-col class="ml-4" cols="12" md="3" sm="6">
            <CustomSelect
              v-model="companyDetails.selectedAddressType"
              :items="companyDetails.dropdownAddressType"
              item-title="name"
              item-value="companyAddressTypeId"
              :isLoading="isAddressTypeListLoading"
              label="Address Type"
              :rules="[
                required('Address Type', companyDetails.selectedAddressType),
              ]"
              :isRequired="true"
              :itemSelected="companyDetails.selectedAddressType"
              @selected-item="companyDetails.selectedAddressType = $event"
            ></CustomSelect>
          </v-col>
          <v-col class="ml-8" cols="12" md="4" sm="6">
            <div
              :style="!isMobileView && !isTabletView ? 'margin-left: 8vw' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1">City</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(companyDetails.city) }}
              </p>
            </div>
          </v-col>
          <v-col class="ml-8" cols="12" md="2" sm="12">
            <div
              :style="!isMobileView && !isTabletView ? 'margin-left: 8vw' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1">State</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(companyDetails.state) }}
              </p>
            </div>
          </v-col>
          <v-col class="ml-8 mb-6" cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Country</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(companyDetails.country) }}
            </p>
          </v-col>
          <v-col class="ml-8 mb-6" cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Pin code</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(companyDetails.pinCode) }}
            </p>
          </v-col>
        </v-row>
        <div class="mt-4 mb-4 d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="dark-grey"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Identity</span
          >
        </div>
        <v-row class="mt-1 mb-10">
          <v-col class="ml-4" cols="12" md="3" sm="6">
            <CustomSelect
              v-model="companyDetails.selectedIdType"
              :items="companyDetails.dropdownIdType"
              item-title="name"
              item-value="idTypeId"
              :isLoading="isIdTypeLoading"
              label="ID Type"
              :rules="[required('ID Type', companyDetails.selectedIdType)]"
              :isRequired="true"
              :itemSelected="companyDetails.selectedIdType"
              @selected-item="companyDetails.selectedIdType = $event"
            ></CustomSelect>
          </v-col>
          <v-col class="ml-8" cols="12" md="4" sm="6">
            <div
              :style="
                !isMobileView && !isTabletView
                  ? 'margin-left: 7vw'
                  : 'margin-left:-17px'
              "
            >
              <v-text-field
                v-model="companyDetails.idNumber"
                @click="showBottomNavigation = true"
                :rules="[
                  required('ID Number', companyDetails.idNumber),
                  alphaNumericCheck('ID Number', companyDetails.idNumber),
                ]"
                variant="solo"
              >
                <template v-slot:label>
                  <span>ID Number</span>
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </div>
          </v-col>
        </v-row>

        <v-bottom-navigation
          class="bottom-navigation"
          style="height: 50px; background-color: white"
          elevation="24"
          horizontal
          :active="!isFormFilled"
        >
          <v-btn
            class="mb-1 rounded-lg primary"
            variant="outlined"
            size="small"
            style="height: 40px; margin-top: 10px"
            @click="reset"
            ><span class="primary">Reset</span></v-btn
          >
          <v-btn
            size="small"
            class="mr-1 mb-1 rounded-lg primary"
            :dense="isMobileView"
            variant="elevated"
            style="height: 40px; margin-top: 10px"
            type="submit"
          >
            <span class="primary">Integrate Now</span>
          </v-btn>
        </v-bottom-navigation>
      </v-form>
    </div>
  </ProfileCard>
  <AppAccessDenied v-else></AppAccessDenied>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
import axios from "axios";
import validationRules from "../../../mixins/validationRules";
import { checkNullValue } from "@/helper";
import {
  GET_COMPANY_AND_USER_DETAILS,
  GET_LOCATION_DETAILS,
} from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
//importing config file
import Config from "../../../config.js";
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
export default {
  name: "IrukkaSignIn",
  components: { CustomSelect },
  mixins: [validationRules],

  data() {
    return {
      tab: null,
      errorPage: false,
      showSignUp: true,
      otp: "",
      show: false,
      mainTabList: ["Recruitment", "Others"],
      isClicked: false,
      counter: 0,
      mobileNoCountryCode: null,
      showBottomNavigation: false,
      isTabletView: false,
      idToken: "",
      errorMessage: "",
      companyDetails: {
        companyName: "",
        selectedCompanyType: null,
        dropdownCompanyType: [],
        userName: "",
        selectedCompanyDescription: null,
        dropdownCompanyDescription: [],
        age: "",
        selectedLocation: null,
        dropdownLocation: [],
        selectedLanguage: 0,
        selectedGender: null,
        dropdownGender: [],
        dropdownLanguage: [],
        mobileNumber: "",
        emailAddress: "",
        selectedAddressType: null,
        dropdownAddressType: [],
        city: "",
        state: "",
        country: "",
        pinCode: "",
        selectedIdType: null,
        dropdownIdType: [],
        idNumber: "",
        mytoken: "",
        additionalLocationDetails: [],
      },
      showValidationAlert: false,
      isLoading: false,
      isLoadingCompanyAndUserDetails: false,
      validationMessages: [],
      isCompanyTypeLoading: false,
      isLanguageListLoading: false,
      isAddressTypeListLoading: false,
      isGenderListLoading: false,
      isIdTypeLoading: false,
      isPreferredLocationLoading: false,
      irukkaStatus: null,
      isIrukkaStatusLoading: false,
    };
  },

  computed: {
    landedFormName() {
      return "Recruitment";
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    formAccess() {
      let accessFormName = this.landedFormName.replace(/\s/g, "-");
      accessFormName = accessFormName.toLowerCase();
      let formAccessRights = this.accessRights(accessFormName);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["add"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isClickCheck() {
      if (this.isClicked) {
        return false;
      } else {
        return true;
      }
    },
    isFormFilled() {
      if (this.showBottomNavigation) {
        return false;
      } else {
        return true;
      }
    },
  },
  mounted() {
    this.loadAuthToken();
    this.pathRouter();
  },
  methods: {
    checkNullValue,
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    // method for getting the auth token
    loadAuthToken() {
      this.mytoken = window.$cookies.get("irukkaAuthToken");
    },
    pathRouter() {
      if (!this.mytoken) {
        this.$router.push("/settings/integration");
      } else {
        this.companyAndUserDetails();
        this.locationDetails();
        //for middle screens
        this.checkTabletView();
        this.onShowCompanyDetails();
        this.fetchIntegrationStatus();
        window.addEventListener("resize", this.handleResize);
      }
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    fillOtherLocationDetails(key, value) {
      if (this.companyDetails.selectedLocation) {
        let selectedObject = this.companyDetails.additionalLocationDetails.find(
          (item) => item[key] === value
        );
        this.companyDetails.city = selectedObject.City_Name;
        this.companyDetails.state = selectedObject.State_Name;
        this.companyDetails.country = selectedObject.Country_Name;
        this.companyDetails.pinCode = selectedObject.Pincode;
      }
    },
    calculateAge(dateOfBirth) {
      if (dateOfBirth) {
        const dob = new Date(dateOfBirth);
        const today = new Date();
        let age = today.getFullYear() - dob.getFullYear();
        const monthDiff = today.getMonth() - dob.getMonth();

        if (
          monthDiff < 0 ||
          (monthDiff === 0 && today.getDate() < dob.getDate())
        ) {
          age--;
        }

        this.companyDetails.age = age;
      } else {
        this.companyDetails.age = null;
      }
    },
    async companyAndUserDetails() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_COMPANY_AND_USER_DETAILS,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getCompanySignUpDetails) {
            this.companyDetails.companyName =
              response.data.getCompanySignUpDetails.getCompanyDetails[0].Org_Name;
            this.companyDetails.selectedCompanyDescription =
              response.data.getCompanySignUpDetails.getCompanyDetails[0].Org_Description;
            this.companyDetails.userName =
              response.data.getCompanySignUpDetails.getPersonalDetails[0].Updated_By_Name;
            this.companyDetails.mobileNumber =
              response.data.getCompanySignUpDetails.getPersonalDetails[0].Mobile_Number;
            this.companyDetails.emailAddress =
              response.data.getCompanySignUpDetails.getPersonalDetails[0].Emp_Email;
            this.calculateAge(
              response.data.getCompanySignUpDetails.getPersonalDetails[0].DOB
            );
          }
        })
        .catch((err) => {
          vm.handleRetrieveCompanyAndUserDetailsError(err);
        });
    },
    handleRetrieveCompanyAndUserDetailsError(err = "") {
      this.isLoadingCompanyAndUserDetails = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "recruitment",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    async locationDetails() {
      this.isPreferredLocationLoading = true;
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_LOCATION_DETAILS,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getLocationDetails) {
            this.companyDetails.dropdownLocation =
              response.data.getLocationDetails.locationDetails;
            this.companyDetails.additionalLocationDetails =
              response.data.getLocationDetails.locationDetails;
            this.isPreferredLocationLoading = false;
          }
        })
        .catch((err) => {
          this.isPreferredLocationLoading = false;
          vm.handleRetrieveLocationDetailsError(err);
        });
    },
    handleRetrieveLocationDetailsError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "recruitment",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    onShowCompanyDetails() {
      //calling the function to fetch the API data
      this.fetchCompanyTypeData();
      this.fetchCompanyAddressTypeData();
      this.fetchLanguageTypeData();
      this.fetchIdTypeData();
      this.fetchGenderListData();
    },
    //validate function
    async validateCompanyDetails() {
      // checking whether the fields of form are valid or not
      const { valid } = await this.$refs.companyDetailForm.validate();
      // submit the form only if all the fields are filled
      if (valid) {
        this.doCompanySignUp();
        this.companyDetailForm = {};
      }
    },
    getCountryCode(mobileNoCountryCode) {
      if (mobileNoCountryCode) {
        this.mobileNoCountryCode = parseInt(mobileNoCountryCode.dialCode);
        return this.mobileNoCountryCode;
      } else {
        return "";
      }
    },
    reset() {
      this.showBottomNavigation = false;
      this.$refs.companyDetailForm.reset();
    },

    async fetchCompanyTypeData() {
      this.isCompanyTypeLoading = true;
      try {
        const url = Config.irukkaUrl + "/company/types";
        let headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };

        headers.Authorization = this.mytoken;

        const response = await axios.get(url, { headers });
        this.companyDetails.dropdownCompanyType = response.data.payload;
        this.isCompanyTypeLoading = false;
      } catch (error) {
        this.handleDropdownDataError();
        this.isCompanyTypeLoading = false;
      }
    },
    async fetchLanguageTypeData() {
      this.isLanguageListLoading = true;
      try {
        const url = Config.irukkaUrl + "/languages";
        const headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };

        const response = await axios.get(url, { headers });
        this.companyDetails.dropdownLanguage =
          response.data.payload._embedded.language;
        this.companyDetails.selectedLanguage =
          this.companyDetails.dropdownLanguage[0].languageId;
        this.isLanguageListLoading = false;
      } catch (error) {
        this.handleDropdownDataError();
        this.isLanguageListLoading = false;
      }
    },
    async fetchCompanyAddressTypeData() {
      this.isAddressTypeListLoading = true;
      try {
        const url = Config.irukkaUrl + "/company/address/types";
        const headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };

        const response = await axios.get(url, { headers });
        this.companyDetails.dropdownAddressType = response.data.payload;
        this.isAddressTypeListLoading = false;
      } catch (error) {
        this.handleDropdownDataError();
        this.isAddressTypeListLoading = false;
      }
    },
    async fetchIdTypeData() {
      this.isIdTypeLoading = true;
      try {
        const url = Config.irukkaUrl + "/id/types";
        const headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };

        const response = await axios.get(url, { headers });
        this.companyDetails.dropdownIdType = response.data.payload;
        this.isIdTypeLoading = false;
      } catch (error) {
        this.handleDropdownDataError();
        this.isIdTypeLoading = false;
      }
    },
    async fetchGenderListData() {
      this.isGenderListLoading = true;
      try {
        const url = Config.irukkaUrl + "/gender";
        let headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };

        headers.Authorization = this.mytoken;

        const response = await axios.get(url, { headers });
        this.companyDetails.dropdownGender = response.data.payload;
        this.isGenderListLoading = false;
      } catch (error) {
        this.handleDropdownDataError();
        this.isGenderListLoading = false;
      }
    },
    async doCompanySignUp() {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "",
      };
      try {
        const url = Config.irukkaUrl + "/companySignup";
        const headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3",
          Authorization: this.mytoken,
        };
        const data = {
          companyName: this.companyDetails.companyName,
          companyTypeId: this.companyDetails.selectedCompanyType,
          user: {
            name: this.companyDetails.userName,
            age: this.companyDetails.age,
            genderId: this.companyDetails.selectedGender,
            preferredLocation: this.companyDetails.selectedLocation,
            languageId: this.companyDetails.selectedLanguage,
            loginType: "MOBILE_REGISTRATION",
            loginName: this.companyDetails.mobileNumber,
          },
          address: [
            {
              companyAddressTypeId: this.companyDetails.selectedAddressType,
              companyContactNo: this.companyDetails.mobileNumber,
              companyEmailId: this.companyDetails.emailAddress,
              city: this.companyDetails.city,
              state: this.companyDetails.state,
              country: this.companyDetails.country,
              pinCode: this.companyDetails.pinCode,
            },
          ],
          //  currently not present in the form
          companyDescription: this.companyDetails.selectedCompanyDescription,
          companyProofs: [
            {
              idTypeId: this.companyDetails.selectedIdType,
              idNumber: this.companyDetails.idNumber,
              // currently not present in the form
              documentLink: "/company/odooTek/aadhar.jpg",
            },
          ],
        };
        await axios.post(url, data, { headers });
        window.location.href =
          this.$store.getters.baseUrl + "settings/integration";
        window.$cookies.set("companySignupId", 123, "59MIN");
      } catch (error) {
        if (error.response.status == 417) {
          snackbarData.type = "error";
          snackbarData.message = "This contact is already registered.";
          this.showAlert(snackbarData);
        }
      }
    },

    //method for checking middle screens
    checkTabletView() {
      this.isTabletView = window.innerWidth >= 600 && window.innerWidth < 960;
    },
    handleResize() {
      this.checkTabletView();
    },
    handleDropdownDataError() {
      this.companyDetails.dropdownCompanyType = [];
      this.companyDetails.dropdownLocation = [];
      this.companyDetails.dropdownAddressType = [];
      this.companyDetails.dropdownLanguage = [];
      this.companyDetails.dropdownIdType = [];
      this.companyDetails.dropdownGender = [];
    },
    async fetchIntegrationStatus() {
      let vm = this;
      vm.isIrukkaStatusLoading = true;
      await vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 242,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          vm.isIrukkaStatusLoading = false;
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            this.irukkaStatus =
              response.data.jobBoardIntegrationStatus.getStatus[0].Integration_Status;
            //validating the form as soon as the page loads
            this.validateCompanyDetails();
          }
        })
        .catch((err) => {
          vm.isIrukkaStatusLoading = false;
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "integration status",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.handleResize);
  },
};
</script>

<style scoped>
.same-as-vue-tel > input:focus {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
}
.hand-cursor {
  cursor: pointer;
}
.bottom-navigation :deep() .v-bottom-navigation__content {
  background-color: white;
  justify-content: flex-start !important;
  align-items: center !important;
}
.bottom-navigation :deep() .v-bottom-navigation__content > .v-btn {
  font-size: inherit;
  height: 45px;
  max-width: 200px;
  min-width: 100px;
  font-size: 1.2rem;
  text-transform: none;
  transition: inherit;
  width: auto;
  border-radius: 0;
  margin-left: 20px !important;
}
.full-text {
  white-space: nowrap;
  overflow: visible;
}
</style>
