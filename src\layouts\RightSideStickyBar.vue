<template>
  <div
    v-if="
      showOfferOption ||
      canAccessDemoBookingAndOffers ||
      dashboardType === 'EMPLOYEEMONITORINGDASHBOARD'
    "
  >
    <div v-if="showBarOptions" class="right-side-sticky-bar">
      <ul class="pl-1 right-side-sticky-bar-content">
        <li
          v-if="showOfferOption"
          style="background: #8cccff"
          @click="showDiscountOffers()"
        >
          <img
            style="height: 27; width: 27"
            title=""
            alt=""
            :src="offerIconImage"
          />
          <div>Offer</div>
        </li>
        <li
          v-if="canAccessDemoBookingAndOffers"
          style="background: #9cecc9"
          @click="onShowPanel('demoBooking')"
        >
          <img
            style="height: 20; width: 20"
            title=""
            alt=""
            :src="demoBookingImage"
          />
          <div>Book a Demo</div>
        </li>
        <li
          v-if="dashboardType === 'EMPLOYEEMONITORINGDASHBOARD'"
          style="background: #fccdcd; min-height: 66px"
          @click="onShowPanel('productDemo')"
        >
          <img
            style="height: 17; width: 20"
            title=""
            alt=""
            :src="productDemoImage"
          />
          <div>Watch Product Demo</div>
        </li>
      </ul>
    </div>
    <div v-else class="discount-panel-container" :class="stickyPanelClass">
      <div class="discount-panel rounded-lg pa-2">
        <div class="d-flex justify-end mt-n5 mr-n4">
          <v-btn
            icon
            size="small"
            color="secondary"
            style="z-index: 4"
            @click="closeStickyBar()"
          >
            <v-icon color="white" class="font-weight-bold">close</v-icon>
          </v-btn>
        </div>
        <div v-if="showDiscountPanel">
          <div
            v-if="!claimSuccess"
            class="text-center"
            style="margin-top: -15%"
          >
            <img style="width: 150" :src="discountOfferImg" alt="image" />
          </div>
          <div v-else class="text-center">
            <img
              style="width: 150"
              :src="discountOfferSuccessImg"
              alt="image"
            />
          </div>
          <div class="text-h5 pa-4 text-center font-weight-bold">
            {{ discountOfferDetails.title }}
          </div>
          <div class="text-body-1 px-4 pb-8 text-center">
            {{ discountOfferDetails.description }}
          </div>
          <div v-if="!claimSuccess" class="mb-4 text-center">
            <v-btn
              color="secondary"
              rounded="lg"
              size="large"
              @click="claimOffer()"
              >Claim Now</v-btn
            >
          </div>
        </div>
        <v-overlay
          :modal-value="isLoading"
          contained
          class="d-flex align-center justify-center"
        >
          <v-progress-circular color="secondary" indeterminate size="64">
          </v-progress-circular>
        </v-overlay>
      </div>
    </div>

    <!-- help video modal -->
    <v-dialog
      v-model="openOptionsModal"
      width="800px"
      @click:outside="closeHelpVideoModal()"
    >
      <v-card class="rounded-lg">
        <div class="d-flex justify-end">
          <v-icon
            color="secondary"
            class="pr-4 pt-4 font-weight-bold"
            @click="closeHelpVideoModal()"
            >close</v-icon
          >
        </div>
        <div class="video-player-container">
          <div class="video-wrapper video-player-box">
            <video-embed :src="videoSrc"></video-embed>
          </div>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import Vue from "vue";
// video player
import Embed from "v-video-embed";
Vue.use(Embed);
// import queries
import { SEND_COUPON_EMAIL_REQUEST } from "@/graphql/layout/layoutQueries.js";
import { getErrorCodes } from "@/helper.js";

export default {
  name: "RightSideStickyBar",

  data: () => ({
    openInstallerDetails: false,
    openOnboardHelperDetails: false,
    stickyPanelClass: "",
    isLoading: false,

    // offer screen
    showBarOptions: true,
    discountOfferDetails: {
      title: "Decide early and get the best offer",
      description:
        "Subscribe before the end of the trial period to claim a discount of up to 40%",
    },
    claimSuccess: false,
    showDiscountPanel: false,

    // help screen
    openOptionsModal: false,
    videoSrc: "",
  }),

  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },

    discountOfferImg() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/billing/discount-offer.webp");
      else return require("@/assets/images/billing/discount-offer.png");
    },

    discountOfferSuccessImg() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/billing/discount-panel.webp");
      else return require("@/assets/images/billing/discount-panel.png");
    },

    offerIconImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/billing/offer-icon.webp");
      else return require("@/assets/images/billing/offer-icon.png");
    },

    demoBookingImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/billing/demo-booking.webp");
      else return require("@/assets/images/billing/demo-booking.png");
    },

    productDemoImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/billing/video.webp");
      else return require("@/assets/images/billing/video.png");
    },

    // to fetch access rights
    accessRights() {
      return this.$store.getters.formAccessRights;
    },

    // check Billing form rights
    billingFormRights() {
      let formRights = this.accessRights("billing");
      if (
        formRights.accessRights &&
        formRights.accessRights["view"] &&
        formRights.accessRights["update"] &&
        formRights.accessRights["admin"] === "admin"
      ) {
        return true;
      }
      return false;
    },

    isAutoBilling() {
      return this.$store.state.isAutoBilling;
    },

    // tracking mode (stealth/consent)
    appTrackingMode() {
      return this.$store.state.employeeMonitoring.appTrackingMode;
    },

    // dashboard type chosen in plan
    dashboardType() {
      return this.$store.state.planDashboardType;
    },

    // current status of auto-billing plan
    planCurrentStatus() {
      return this.$store.state.autoBillingPlanCurrentStatus;
    },

    // remaining trial days
    remainingTrialDays() {
      return this.$store.state.trialPeriod;
    },

    // demo-booking & offers is presented for emp-monitoring plan with auto-billing and for billing admin when in trial without card
    canAccessDemoBookingAndOffers() {
      if (
        this.dashboardType === "EMPLOYEEMONITORINGDASHBOARD" &&
        this.isAutoBilling &&
        this.billingFormRights &&
        this.planCurrentStatus === "trial-subscription-skipped-card-not-exist"
      ) {
        return true;
      }
      return false;
    },

    // auto-billing subscription details
    autoBillingDetails() {
      return this.$store.state.autoBillingDetails;
    },

    // offers is presented for emp-monitoring plan with auto-billing and for billing admin when in trial without card on 2days before trial
    showOfferOption() {
      if (
        this.canAccessDemoBookingAndOffers &&
        this.autoBillingDetails.subscriptionCurrency === "INR" &&
        this.remainingTrialDays >= 2 // offer is only applicable for first 5days of trial
      ) {
        // we have hide the offer for all the customers. so i returned here as false instead of true. If required in future we can enable it
        return false;
      }
      return false;
    },

    // download type is based on tracking mode
    downloadType() {
      return this.trackingMode === "STEALTH MODE" ? "agent" : "client";
    },

    // os list based on app tracking mode
    osList() {
      if (this.appTrackingMode === "STEALTH MODE") {
        return ["Windows"];
      } else return ["Windows", "Linux", "Mac"];
    },

    // os list based on app tracking mode
    onboardingTypes() {
      if (this.appTrackingMode === "STEALTH MODE") {
        return ["Individual Member", "Bulk Import", "Manual"];
      } else return ["Bulk Import", "Manual"];
    },
  },

  methods: {
    // while close the help/discount panel, need to reset all the values
    closeStickyBar() {
      this.stickyPanelClass = "";
      setTimeout(() => {
        this.showBarOptions = true;
        this.claimSuccess = false;
        this.showDiscountPanel = false;
        this.openOnboardHelperDetails = false;
        this.openInstallerDetails = false;
        this.discountOfferDetails = {
          title: "Decide early and get the best offer",
          description:
            "Subscribe before the end of the trial period to claim a discount of up to 40%",
        };
      }, 450);
    },

    // present discount panel when clicking offers option
    showDiscountOffers() {
      this.stickyPanelClass = "visible";
      this.showDiscountPanel = true;
      this.showBarOptions = false;
    },

    onShowPanel(type = "") {
      if (type === "productDemo") {
        this.videoSrc =
          this.$store.getters["employeeMonitoring/demoVideoSourceURL"];
        this.openOptionsModal = true;
      } else if (type === "demoBooking") {
        window.open("https://meetings.hubspot.com/chandra18", "_blank");
      }
    },

    // while clicking 'Claim Now' button, need to call backend to request email for coupon
    claimOffer() {
      let vm = this;
      vm.isLoading = true;
      const { organizationName } = vm.$store.state.orgDetails;
      try {
        vm.$apollo
          .query({
            query: SEND_COUPON_EMAIL_REQUEST,
            variables: {
              organizationName: organizationName,
            },
            client: "apolloClientG",
          })
          .then((couponReqResponse) => {
            if (couponReqResponse.data) {
              // present success screen
              vm.discountOfferDetails = {
                title: "Congratulations!",
                description:
                  "You will receive the coupon code to apply the discount on the list price. Use it within 2 hours to avail of the offer.",
              };
              vm.claimSuccess = true;
              vm.isLoading = false;
            } else {
              vm.handleCouponReqError();
            }
          })
          .catch((couponReqError) => {
            vm.handleCouponReqError(couponReqError);
          });
      } catch (catchError) {
        vm.handleCouponReqError(catchError);
      }
    },

    // close the video help screen modal
    closeHelpVideoModal() {
      this.videoSrc = "";
      this.openOptionsModal = false;
    },

    // handle backend errors for coupon request
    handleCouponReqError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      this.isLoading = false;
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "PBP0105": //  Error in sending email
          case "BB0120": // Error while processing the request to send the claim coupon email.
          case "IVE0000": // Invalid input request.
          case "IVE0128": // Organization name is required.
          default:
            snackbarData.message =
              "Something went wrong while requesting the coupon code. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while requesting the coupon code. If you continue to see this issue, please contact the platform administrator.";
      }
      this.showAlert(snackbarData);
    },

    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style lang="scss">
.discount-panel-container {
  position: fixed;
  right: 0;
  bottom: 50px;
  transform: translateX(100%);
  transition: transform 0.4s ease-in-out;
  z-index: 5;
}

.discount-panel-container.visible {
  transform: translateX(-10px);
}

.discount-panel {
  background-color: #fff;
  border: 2px solid var(--v-secondary-base);
  position: relative;
  min-height: 350px;
  width: 400px;
  font-size: 15px;
  max-width: calc(100% - 10px);
  box-shadow: 0px 1px 15px rgb(255 181 206) !important;
}

@media screen and (max-width: 480px) {
  .discount-panel-container.visible {
    transform: translateX(0px);
  }
  .discount-panel {
    width: 100% !important;
  }
}

@media screen and (max-width: 600px) {
  .discount-panel {
    min-height: 400px;
  }
  .right-side-sticky-bar {
    top: 70% !important;
  }
}

@media screen and (max-width: 960px) {
  .right-side-sticky-bar {
    top: 50% !important;
  }
}

.right-side-sticky-bar {
  padding: 0px;
  margin: 0px;
  position: fixed;
  right: 0px;
  top: 45%;
  width: 43px;
  z-index: 5;
}

.right-side-sticky-bar-content {
  padding: 3px;
  background: white;
  font-size: 9px;
  color: var(--v-primary-base);
  position: absolute;
}

.right-side-sticky-bar-content li {
  list-style-type: none;
  background-color: #fff;
  height: 60px;
  cursor: pointer;
  padding: 5px 0px 5px;
  text-align: center;
}
</style>
