import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_DYNAMIC_WEEKOFF_SETTINGS = gql`
  query retrieveRosterManagmentSetting {
    retrieveRosterManagmentSetting {
      errorCode
      message
      rosterManagmentSetting {
        Dynamic_Week_Off
        Updated_On
        Updated_By
        Enable_Shift_Swap_Restriction
        Max_Swap_Requests_Per_Month
        Allow_Past_Shift_Swaps
        Max_Shift_Swap_Days
      }
    }
  }
`;
// Mutations
// ===============
export const UPDATE_DYNAMIC_WEEKOFF_SETTINGS = gql`
  mutation updateRosterManagmentSetting(
    $dynamicWeekOff: Int!
    $swapApprovalRestriction: String!
    $maxSwapRequestsPerMonth: Int
    $allowPastShiftSwaps: String!
    $maxShiftSwapDays: Int
  ) {
    updateRosterManagmentSetting(
      dynamicWeekOff: $dynamicWeekOff
      swapApprovalRestriction: $swapApprovalRestriction
      maxSwapRequestsPerMonth: $maxSwapRequestsPerMonth
      allowPastShiftSwaps: $allowPastShiftSwaps
      maxShiftSwapDays: $maxShiftSwapDays
    ) {
      errorCode
      message
    }
  }
`;
