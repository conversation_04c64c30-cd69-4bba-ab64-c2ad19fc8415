<template>
  <v-col :cols="12" :md="4" :sm="6">
    <p class="text-subtitle-1 text-grey-darken-1">{{ label }}</p>
    <span
      v-if="hasDifference && oldValue"
      class="text-subtitle-1 font-weight-regular"
      :class="
        hasDifference ? 'text-decoration-line-through text-error mr-1' : ''
      "
    >
      {{ checkNullValue(oldValue) }}
    </span>
    <span
      class="text-subtitle-1 font-weight-regular"
      :class="hasDifference ? 'text-success' : ''"
    >
      {{ checkNullValue(newValue) }}
    </span>
  </v-col>
</template>

<script>
import { checkNullValue } from "@/helper";
export default {
  props: {
    label: String,
    newValue: [String, Number, Object],
    oldValue: [String, Number, Object],
    oldDataAvailable: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    hasDifference() {
      if (this.oldDataAvailable && this.oldValue !== this.newValue) {
        return true;
      } else {
        return false;
      }
    },
  },
  methods: {
    checkNullValue,
  },
};
</script>
