# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Container Scanning customization: https://docs.gitlab.com/ee/user/application_security/container_scanning/#customizing-the-container-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
stages:
- Code_Quality
- SAST
- build
- deploy

sast:
  stage: SAST
  variables:
    SAST_EXCLUDED_PATHS: spec, test, tests, tmp, dist

code_quality:
  stage: Code_Quality

code_quality_html:
  extends: code_quality
  variables:
    REPORT_FORMAT: html
  artifacts:
    paths: [gl-code-quality-report.html]

include:
- template: Security/SAST.gitlab-ci.yml
- template: Code-Quality.gitlab-ci.yml

build dev:
  image: node:14
  stage: build
  only:
  - develop
  script:
  - npm install -g @vue/cli@latest
  - cp dev-package.json package.json
  - npm install
  - node --max_old_space_size=4096 node_modules/.bin/vue-cli-service build
  artifacts:
    paths:
    - dist/
    expire_in: 1 hour
  environment:
    name: dev
deploy dev:
  image: python:latest
  stage: deploy
  dependencies:
  - build dev
  only:
  - develop
  script:
  - pip install awscli
  - apt-get update -y
  - apt-get install zip -y
  - cd dist/
  - mkdir public
  - shopt -s extglob dotglob
  - mv !(public) public
  - shopt -u dotglob
  - cd ..
  - cp appconfiguration/v3/staging/.htaccess dist/public/
  - cp appconfiguration/v3/staging/beforeInstall.sh codedeploy-scripts/
  - cp appconfiguration/v3/staging/afterInstall.sh codedeploy-scripts/
  - cp appconfiguration/v3/staging/appspec.yml dist/
  - cp -R conf dist/
  - cp -R codedeploy-scripts dist/
  - cd dist
  - zip -r v3.zip .
  - chmod 655 v3.zip
  - aws s3 cp v3.zip s3://uivue3.hrapp.co.in
  # - pip install awscli
  # - cd dist/
  # - aws s3 sync . s3://s3.staging.v3.hrapp.co.in/v3/ --delete
  - aws cloudfront create-invalidation --distribution-id E15JB8BM053XG8 --paths "/*"
  environment:
    name: dev
build prod:
  image: node:14
  stage: build
  only:
  - master
  script:
  - npm install -g @vue/cli@latest
  - cp prod-package.json package.json
  - npm install
  - node --max_old_space_size=4096 node_modules/.bin/vue-cli-service build
  artifacts:
    paths:
    - dist/
    expire_in: 1 hour
  environment:
    name: prod
deploy prod:
  image: python:latest
  stage: deploy
  dependencies:
  - build prod
  only:
  - master
  script:
  - pip install awscli
  - apt-get update -y
  - apt-get install zip -y
  - cd dist/
  - mkdir public
  - shopt -s extglob dotglob
  - mv !(public) public
  - shopt -u dotglob
  - cd ..
  - cp appconfiguration/v3/prod/.htaccess dist/public/
  - cp appconfiguration/v3/prod/beforeInstall.sh codedeploy-scripts/
  - cp appconfiguration/v3/prod/afterInstall.sh codedeploy-scripts/
  - cp appconfiguration/v3/prod/appspec.yml dist/
  - cp -R conf dist/
  - cp -R codedeploy-scripts dist/
  - cd dist
  - zip -r hrappuivue3.zip .
  - chmod 655 hrappuivue3.zip
  - aws s3 cp hrappuivue3.zip s3://vue3ui.hrapp.co
  environment:
    name: prod
build cannyhr:
  image: node:14
  stage: build
  only:
  - master
  script:
  - npm install -g @vue/cli@latest
  - cp prod-package.json package.json
  - npm install
  - cp -R appconfiguration/v3/cannyhr/public .
  - cp -R appconfiguration/v3/cannyhr/logo.png src/assets
  - cp -R appconfiguration/v3/cannyhr/config.js src
  - node --max_old_space_size=4096 node_modules/.bin/vue-cli-service build
  artifacts:
    paths:
    - dist/
    expire_in: 1 hour
  environment:
    name: cannyhr
deploy cannyhr:
  image: python:latest
  stage: deploy
  dependencies:
  - build cannyhr
  only:
  - master
  script:
  - pip install awscli
  - apt-get update -y
  - apt-get install zip -y
  - cd dist/
  - mkdir public
  - shopt -s extglob dotglob
  - mv !(public) public
  - shopt -u dotglob
  - cd ..
  - cp appconfiguration/v3/cannyhr/.htaccess dist/public/
  - cp appconfiguration/v3/cannyhr/beforeInstall.sh codedeploy-scripts/
  - cp appconfiguration/v3/cannyhr/afterInstall.sh codedeploy-scripts/
  - cp appconfiguration/v3/cannyhr/appspec.yml dist/
  - cp -R conf dist/
  - cp -R codedeploy-scripts dist/
  - cd dist
  - zip -r cannyhruivue3.zip .
  - chmod 655 cannyhruivue3.zip
  - aws s3 cp cannyhruivue3.zip s3://cannyhr-ci-cd
  environment:
    name: cannyhr
