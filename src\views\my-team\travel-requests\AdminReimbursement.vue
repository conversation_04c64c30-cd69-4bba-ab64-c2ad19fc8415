<template>
  <ReimbursementRequest
    :form-id="338"
    calling-from="team"
  ></ReimbursementRequest>
</template>
<script>
import { defineAsyncComponent } from "vue";
const ReimbursementRequest = defineAsyncComponent(() =>
  import(
    "@/views/my-finance/travel-and-expenses/claim-request/ReimbursementRequest.vue"
  )
);
export default {
  name: "AdminReimbursement",
  components: { ReimbursementRequest },
};
</script>
