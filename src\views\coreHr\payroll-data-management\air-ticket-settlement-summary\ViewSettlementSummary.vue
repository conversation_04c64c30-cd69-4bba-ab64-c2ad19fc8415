<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="closeViewForm()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:40vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">
              View {{ landedFormName }}
            </div>
            <v-btn icon class="clsBtn" variant="text" @click="closeViewForm()">
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </v-card-title>

          <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
            <div class="px-6 py-2">
              <v-row>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">Employee ID</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.User_Defined_EmpId) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Employee Name
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Employee_Name) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Destination City
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Destination_City) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Destination Country
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Destination_Country) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Air Ticket Category
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Air_Ticketing_Category) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    No. of Tickets
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.No_Of_Dependents + 1) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">Availed Date</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ formatDate(selectedItem.Availed_Date) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Payroll Month
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ formatPayrollMonth(selectedItem.Payroll_Month) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Accrual Basis for Infant
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    <span
                      v-if="
                        payrollCurrency && selectedItem.Infant_Policy_Amount
                      "
                      >{{ payrollCurrency }}</span
                    >
                    {{ checkNullValue(selectedItem.Infant_Policy_Amount) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Accrual Basis for Child
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    <span
                      v-if="payrollCurrency && selectedItem.Child_Policy_Amount"
                      >{{ payrollCurrency }}</span
                    >
                    {{ checkNullValue(selectedItem.Child_Policy_Amount) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Accrual Basis for Adult
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    <span v-if="payrollCurrency">{{ payrollCurrency }}</span>
                    {{ checkNullValue(selectedItem.Adult_Policy_Amount) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Settlement Amount
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    <span v-if="payrollCurrency">{{ payrollCurrency }}</span>
                    {{ checkNullValue(selectedItem.Settlement_Amount) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Air Ticket to Dependent
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Air_Ticket_To_Dependent) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Dependent Relationship
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        JSON.parse(selectedItem?.Dependent_Relationship)?.join(
                          ", "
                        )
                      )
                    }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Eligibility of Ticket Claim (In Months)
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        selectedItem.Eligibility_Of_Ticket_Claim_Months
                      )
                    }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Effective Date Type
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Effective_Date_Enable) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Effective Date
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ formatDate(selectedItem.Effective_Date) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">Status</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Settlement_Status) }}
                  </p>
                </v-col>
              </v-row>
            </div>
          </v-card-text>
        </v-card>
      </template>
    </v-overlay>
  </div>
</template>
<script>
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import moment from "moment";
export default {
  name: "ViewAitTicketPolicy",
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form"],
  data: () => ({
    showViewForm: true,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    closeViewForm() {
      this.showViewForm = false;
      this.$emit("close-form");
    },
    formatPayrollMonth(value) {
      if (!value) return "-";
      const [month, year] = value.split(",");
      return moment(`${year}-${month}-01`).format("MMM, YYYY");
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
