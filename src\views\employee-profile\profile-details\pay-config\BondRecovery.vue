<template>
  <div class="mt-4">
    <div class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="green"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Bond Recovery</span
        >
      </div>
      <div v-if="!showEditForm && formAccess && formAccess.update && allowEdit">
        <v-btn @click="openEditForm" color="primary" variant="text">
          <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
        </v-btn>
      </div>
    </div>
    <div v-if="showEditForm">
      <v-form ref="bonRecoveryObserver">
        <v-row class="pa-4 ma-2">
          <v-col
            v-if="
              labelList['110'] && labelList['110'].Field_Visiblity === 'Yes'
            "
            cols="12"
            sm="4"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["110"].Field_Alias }}
            </p>
            <v-tooltip
              text="Since the payslip has already been generated, updates to this field are unavailable. If any changes are required, please delete the Salary Payslip and Full & Final Settlement."
            >
              <template v-slot:activator="{ props }">
                <v-card
                  width="max-content"
                  variant="plain"
                  v-bind="fieldsEditable ? '' : props"
                  :class="fieldsEditable ? '' : 'cursor-not-allowed'"
                >
                  <v-switch
                    color="primary"
                    v-model="editBondRecoveryData.Bond_Recovery_Applicable"
                    :disabled="!fieldsEditable"
                    true-value="Yes"
                    false-value="No"
                    @update:model-value="onChangeFields"
                  ></v-switch>
                </v-card>
              </template>
            </v-tooltip>
          </v-col>
          <v-col
            v-if="
              editBondRecoveryData.Bond_Recovery_Applicable === 'Yes' &&
              labelList['111'] &&
              labelList['111'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="4"
            sm="6"
          >
            <v-text-field
              v-model="editBondRecoveryData.Minimum_Months_To_Be_Served"
              type="number"
              variant="solo"
              :rules="[
                labelList['111'].Mandatory_Field === 'Yes'
                  ? numericRequiredValidation(
                      labelList['111'].Field_Alias,
                      editBondRecoveryData.Minimum_Months_To_Be_Served
                    )
                  : true,
                validateWithRulesAndReturnMessages(
                  editBondRecoveryData.Minimum_Months_To_Be_Served,
                  'minimumMonthsToBeServed',
                  labelList['111'].Field_Alias,
                  true
                ),
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["111"].Field_Alias
                }}<span
                  v-if="labelList['111'].Mandatory_Field === 'Yes'"
                  style="color: red"
                  >*</span
                >
              </template>
            </v-text-field>
          </v-col>
          <v-col
            v-if="
              editBondRecoveryData.Bond_Recovery_Applicable === 'Yes' &&
              labelList['112'] &&
              labelList['112'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="4"
            sm="6"
          >
            <v-text-field
              v-model="editBondRecoveryData.Bond_Value"
              variant="solo"
              type="number"
              :rules="[
                labelList['112'].Mandatory_Field === 'Yes'
                  ? numericRequiredValidation(
                      labelList['112'].Field_Alias,
                      editBondRecoveryData.Bond_Value
                    )
                  : true,
                validateWithRulesAndReturnMessages(
                  editBondRecoveryData.Bond_Value,
                  'bondValue',
                  labelList['112'].Field_Alias,
                  true
                ),
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["112"].Field_Alias
                }}<span
                  v-if="labelList['112'].Mandatory_Field === 'Yes'"
                  style="color: red"
                  >*</span
                >
              </template>
            </v-text-field>
          </v-col>
        </v-row>
      </v-form>
    </div>
    <div v-else>
      <v-row class="pa-4 ma-2 card-blue-background">
        <v-col
          v-if="labelList['111'] && labelList['111'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["110"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              !viewBondRecoveryData.Bond_Recovery_Applicable ||
              viewBondRecoveryData.Bond_Recovery_Applicable === "No"
                ? "No"
                : "Yes"
            }}
          </p>
        </v-col>
        <v-col
          v-if="
            viewBondRecoveryData.Bond_Recovery_Applicable === 'Yes' &&
            labelList['111'] &&
            labelList['111'].Field_Visiblity === 'Yes'
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["111"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              viewBondRecoveryData.Minimum_Months_To_Be_Served
                ? viewBondRecoveryData.Minimum_Months_To_Be_Served
                : 0
            }}
          </p>
        </v-col>
        <v-col
          v-if="
            viewBondRecoveryData.Bond_Recovery_Applicable === 'Yes' &&
            labelList['112'] &&
            labelList['112'].Field_Visiblity === 'Yes'
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["112"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              viewBondRecoveryData.Bond_Value
                ? viewBondRecoveryData.Bond_Value
                : 0
            }}
          </p>
        </v-col>
      </v-row>
    </div>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            :dense="isMobileView"
            variant="elevated"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateBondRecovery()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import { UPDATE_BOND_RECOVERY_CONFIG } from "@/graphql/employee-profile/profileQueries.js";
import validationRules from "@/mixins/validationRules";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "BondRecovery",
  mixins: [validationRules],
  props: {
    bondRecoveryData: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      required: true,
    },
    allowEdit: {
      type: Boolean,
      required: true,
    },
    labelList: {
      type: Array,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    validationData: {
      type: Object,
      required: true,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
  },

  emits: ["update-success", "edit-form-opened"],

  data: () => ({
    viewBondRecoveryData: {},
    editBondRecoveryData: {},
    showEditForm: false,
    isFormDirty: false,
    isLoading: false,
    openMoreDetails: false,
    validationMessages: [],
    showValidationAlert: false,
    openWarningModal: false,
    openBottomSheet: false,
    fieldsEditable: true,
  }),
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.viewBondRecoveryData = this.bondRecoveryData;
    this.fieldsEditable =
      this.selectedEmpStatus == "Active" ||
      this.validationData.salaryNotGenerated;
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    openBottomSheet(val) {
      this.$emit("edit-form-opened", val);
    },
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },

    openEditForm() {
      mixpanel.track("EmpProfile-payConfig-bond-edit-opened");
      this.editBondRecoveryData = JSON.parse(
        JSON.stringify(this.viewBondRecoveryData)
      );
      this.showEditForm = true;
      this.openBottomSheet = true;
    },

    closeEditForm() {
      mixpanel.track("EmpProfile-payConfig-bond-edit-closed");
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.showEditForm = false;
      }
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    async validateBondRecovery() {
      const { valid } = await this.$refs.bonRecoveryObserver.validate();
      mixpanel.track("EmpProfile-payConfig-bond-submit-click");
      if (valid) {
        this.updateBondRecovery();
      }
    },

    updateBondRecovery() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_BOND_RECOVERY_CONFIG,
          variables: {
            employeeId: vm.selectedEmpId,
            bondRecoveryApplicable:
              vm.editBondRecoveryData.Bond_Recovery_Applicable === "Yes"
                ? 1
                : 0,
            minimumMonthsToBeServed:
              vm.editBondRecoveryData.Bond_Recovery_Applicable === "Yes" &&
              vm.editBondRecoveryData.Minimum_Months_To_Be_Served
                ? parseInt(vm.editBondRecoveryData.Minimum_Months_To_Be_Served)
                : 0,
            bondValue:
              vm.editBondRecoveryData.Bond_Recovery_Applicable === "Yes" &&
              vm.editBondRecoveryData.Bond_Value
                ? vm.editBondRecoveryData.Bond_Value.toString()
                : "",
          },
          client: "apolloClientAD",
        })
        .then(() => {
          mixpanel.track("EmpProfile-payConfig-bond-edit-success");
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Bond recovery configuration updated successfully.",
          };
          vm.showAlert(snackbarData);
          vm.openBottomSheet = false;
          vm.showEditForm = false;
          vm.isLoading = false;
          vm.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
          vm.$store.commit(
            "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
            "0-false"
          );
          vm.$emit("update-success");
        })
        .catch((updateError) => {
          vm.handleUpdateError(updateError);
        });
    },

    handleUpdateError(err) {
      mixpanel.track("EmpProfile-payConfig-bond-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "bond recovery configuration",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
