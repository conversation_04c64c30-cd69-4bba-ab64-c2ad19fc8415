<template>
  <div>
    <div v-if="isLoading || appsLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="showFetchErrorScreen"
      :button-text="emptyScreenData.buttonText"
      :content="listAppFetchError ? emptyScreenData.content : ''"
      :icon-name="emptyScreenData.iconName"
      :image-name="emptyScreenData.imageName"
      :is-small-image="true"
      @button-click="listAppFetchError ? fetchList() : {}"
    >
      <template v-if="!listAppFetchError" #contentSlot>
        <span
          v-if="this.$store.state.isAdmin === 'Admin' && !searchValue"
          class="mb-5 sub-content font-weight-bold"
        >
          {{ $t("dataLossPrevention.noKeyLogsAvailable") }}
          {{ $t("dataLossPrevention.configureHere") }}
          <span class="text-primary cursor_pointer">{{
            $t("dataLossPrevention.configureHere")
          }}</span>
        </span>
        <span
          v-else-if="!searchValue"
          class="mb-5 sub-content font-weight-bold"
        >
          {{ $t("dataLossPrevention.talkToAdmin") }}
        </span>
        <span v-else class="mb-5 sub-content font-weight-bold">
          {{ $t("dataLossPrevention.noRecordsMatch") }}
        </span>
      </template>
    </AppFetchErrorScreen>

    <div v-else>
      <div>
        <v-row>
          <v-col cols="12" class="mb-12">
            <v-data-table
              :headers="tableHeaders"
              :items="appUrlActivityData"
              fixed-header
              :height="
                $store.getters.getTableHeightBasedOnScreenSize(
                  290,
                  appUrlActivityData
                )
              "
              :sort-by="[{ key: 'activityStartDateTime', order: 'desc' }]"
              :items-per-page="50"
              :items-per-page-options="[
                { value: 50, title: '50' },
                { value: 100, title: '100' },
                {
                  value: -1,
                  title: $t('dataLossPrevention.itemsPerPageAll'),
                },
              ]"
              :footer-props="{
                itemsPerPageText: $t('dataLossPrevention.itemsPerPage'),
              }"
            >
              <template v-slot:item="{ item }">
                <tr
                  class="data-table-tr bg-white cursor-pointer"
                  :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
                >
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2 w-50"
                    >
                      {{ $t("dataLossPrevention.applicationName") }}
                    </div>
                    <section
                      class="d-flex align-center"
                      :class="isMobileView ? 'justify-start w-50' : ''"
                    >
                      <v-tooltip
                        :text="item.Application_Name"
                        location="top"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate text-start"
                            style="max-width: 150px"
                            v-bind="
                              item.Application_Name.length > 20 ? props : ''
                            "
                          >
                            {{ checkNullValue(item.Application_Name) }}
                          </div>
                        </template>
                      </v-tooltip>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2 w-50"
                    >
                      {{ $t("dataLossPrevention.applicationTitle") }}
                    </div>
                    <div
                      class="d-flex align-center"
                      :class="isMobileView ? 'justify-start w-50' : ''"
                    >
                      <section
                        class="text-subtitle-1 font-weight-regular text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 250px;'
                            : 'max-width: 150px;'
                        "
                      >
                        <v-tooltip
                          :text="item.App_Title"
                          location="top"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate text-start"
                              :style="
                                !isMobileView
                                  ? 'max-width: 250px; '
                                  : 'max-width: 150px; '
                              "
                              v-bind="item.App_Title?.length > 30 ? props : ''"
                            >
                              {{ checkNullValue(item.App_Title) }}
                            </div>
                          </template>
                        </v-tooltip>
                      </section>
                    </div>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between flex-wrap'
                        : ''
                    "
                    :style="
                      isMobileView
                        ? `width: ${
                            windowWidth - 40
                          }px; white-space: normal; height: auto;`
                        : ''
                    "
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2 w-50"
                    >
                      {{ $t("dataLossPrevention.keyLogs") }}
                    </div>
                    <div
                      class="d-flex align-center flex-wrap"
                      :class="isMobileView ? 'justify-start w-50' : ''"
                    >
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-start"
                        :style="{
                          'white-space': 'normal',
                          'word-break': 'break-word',
                          'overflow-wrap': 'anywhere',
                          width: isMobileView ? `150px` : '100%',
                        }"
                      >
                        {{ checkNullValue(item.keyStrokes) }}
                      </div>
                    </div>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="
                      isMobileView
                        ? `width: ${windowWidth - 40}px`
                        : 'width: max-content'
                    "
                    :cols="2"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 w-50"
                    >
                      {{ $t("dataLossPrevention.activityStartTime") }}
                    </div>
                    <div
                      class="d-flex align-center"
                      :class="isMobileView ? 'justify-start w-50' : ''"
                    >
                      <section
                        class="text-subtitle-1 font-weight-regular text-truncate text-start"
                        :style="
                          !isMobileView
                            ? 'max-width: 300px;'
                            : 'max-width: 200px;'
                        "
                      >
                        {{
                          presentWithoutSecs(
                            convertUTCToLocal(item.activityStartDateTime)
                          )
                        }}
                      </section>
                    </div>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="
                      isMobileView
                        ? `width: ${windowWidth - 40}px`
                        : 'width: max-content'
                    "
                    :cols="2"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 w-50"
                    >
                      {{ $t("dataLossPrevention.activityEndTime") }}
                    </div>
                    <div
                      class="d-flex align-center"
                      :class="isMobileView ? 'justify-start w-50' : ''"
                    >
                      <section
                        class="text-subtitle-1 font-weight-regular text-truncate text-start"
                        :style="
                          !isMobileView
                            ? 'max-width: 300px;'
                            : 'max-width: 200px;'
                        "
                      >
                        {{
                          presentWithoutSecs(
                            convertUTCToLocal(item.activityEndDateTime)
                          )
                        }}
                      </section>
                    </div>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </div>
    </div>
  </div>
</template>
<script>
// import helper function
import {
  getErrorCodesWithValidation,
  checkNullValue,
  convertUTCToLocal,
  handleNetworkErrors,
} from "@/helper";

// import queries
import { LIST_MY_ACTIVITY_APPS } from "@/graphql/data-loss-prevention/keyLoggerQueries";
import moment from "moment";

export default {
  name: "EmployeeTrackingDetails",

  data: () => ({
    isListExpanded: false,
    appsLoading: false,
    // activity Data
    appUrlActivityData: [],
    appUrlActivityDataBackup: [],
    listAppFetchError: false,
    errorContent: "",
    showRetryButton: true,
  }),

  props: {
    isFromEmployeeLogin: {
      type: Boolean,
      default: false,
    },
    selectedDate: {
      type: Date,
      required: true,
    },
    selectedEmployeeId: {
      type: Number,
      required: true,
    },
    selectedWorkSchedule: {
      type: [Number, String],
      required: true,
    },
    source: {
      type: String,
      required: true,
    },
    timezone: {
      type: String,
      required: true,
    },
    listType: {
      type: String,
      default: "",
    },
    refreshCallView: {
      type: Boolean,
      default: false,
    },
    formId: {
      type: Number,
      required: true,
    },
  },

  emits: ["app-url-data", "refresh-done"],

  computed: {
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      return [
        {
          title: this.$t("dataLossPrevention.applicationName"),
          key: "Application_Name",
        },
        {
          title: this.$t("dataLossPrevention.applicationTitle"),
          key: "App_Title",
        },
        { title: this.$t("dataLossPrevention.keyLogs"), key: "keyStrokes" },
        {
          title: this.$t("dataLossPrevention.activityStartTime"),
          key: "activityStartDateTime",
        },
        {
          title: this.$t("dataLossPrevention.activityEndTime"),
          key: "activityEndDateTime",
        },
      ];
    },

    // check if the data is loading
    isLoading() {
      if (!this.listAppFetchError) {
        return this.appsLoading;
      }
      return false;
    },

    // show the error / empty screen
    showFetchErrorScreen() {
      return (
        this.listAppFetchError ||
        (this.appUrlActivityData.length === 0 && !this.isLoading)
      );
    },

    // error / empty screen data
    emptyScreenData() {
      let data = {};
      let imagePath = "employee-monitoring/activity-tracker/no-apps-activity";
      // check if there are any error from BE
      if (this.listAppFetchError) {
        data = {
          buttonText: this.showRetryButton ? "Retry" : "",
          content: this.errorContent,
          iconName: "refresh",
          imageName: imagePath,
        };
      } else {
        data = {
          buttonText: "",
          content: this.$t("dataLossPrevention.noKeyLogsAvailable"),
          iconName: "",
          imageName: imagePath,
        };
      }

      return data;
    },

    // data table search
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    refreshCallView(val) {
      if (val) {
        this.fetchList();
        this.$emit("refresh-done");
      }
    },
    selectedDate(val) {
      if (val) {
        this.fetchList();
      }
    },
    isFromEmployeeLogin: {
      handler(val) {
        if (val) {
          this.fetchList();
        }
      },
      immediate: true,
    },

    selectedEmployeeId(val) {
      if (val) {
        this.fetchList();
      }
    },

    selectedWorkSchedule(val) {
      if (val) {
        this.fetchList();
      }
    },

    appUrlActivityData(data) {
      if (data && data.length > 0) {
        this.$emit("app-url-data", data && data.length > 0 ? data : []);
      }
    },
  },
  mounted() {
    this.fetchList();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    presentWithoutSecs(datetimeStr) {
      if (!datetimeStr) return "-";
      const [date, time] = datetimeStr.split(" ");
      const [hours, minutes] = time.split(":");
      return `${date} ${hours}:${minutes}`;
    },
    onApplySearch(val) {
      if (!val) {
        this.appUrlActivityData = this.appUrlActivityDataBackup;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.appUrlActivityDataBackup;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.appUrlActivityData = searchItems;
      }
    },

    fetchList() {
      let vm = this;
      vm.appsLoading = true;
      vm.listAppFetchError = "";
      vm.$apollo
        .query({
          query: LIST_MY_ACTIVITY_APPS,
          variables: {
            employeeId: vm.selectedEmployeeId,
            date: moment(vm.selectedDate).format("YYYY-MM-DD"),
            callFromMyTeamActivityForm: vm.source === "myActivity" ? 0 : 1,
            timeZone: vm.timezone,
            workScheduleId: vm.selectedWorkSchedule,
            formId: parseInt(vm.formId),
            tab: "keyStrokes",
          },
          client: "apolloClientE",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listMyActivityApps &&
            response.data.listMyActivityApps.appDetails &&
            !response.data.listMyActivityApps.errorCode
          ) {
            let tempData = JSON.parse(
              response.data.listMyActivityApps.appDetails
            );
            vm.appUrlActivityData = tempData;
            vm.appUrlActivityDataBackup = tempData;
            vm.appsLoading = false;
          } else {
            vm.appUrlActivityData = [];
            vm.appUrlActivityDataBackup = [];
            vm.handleErrors(response.data.listMyActivityApps?.errorCode || "");
          }
        })
        .catch((err) => {
          vm.appUrlActivityData = [];
          vm.appUrlActivityDataBackup = [];
          vm.handleErrors(err, "apps");
        });
    },

    // handle error from BE
    handleErrors(err, source) {
      this.appsLoading = false;
      if (err && err.graphQLErrors) {
        this.showRetryButton = true;
        let errorCode = getErrorCodesWithValidation(err);
        if (errorCode) {
          switch (errorCode[0]) {
            // technical errors
            case "_DB0000": // db error - common for all endpoint
              this.errorContent = `It’s us! There seems to be some technical difficulties while fetching the ${
                source?.toLowerCase() === "apps" ? source + " usage " : source
              } details. Please try after some time.`;
              break;
            case "_DB0100": // view access denied
              this.showRetryButton = false;
              this.errorContent = `Sorry, you don't have access rights to view your ${source} details. Please contact the HR administrator.`;
              break;
            // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
            case "BAD_USER_INPUT":
              var validationErrors = errorCode[1]; // validation error json
              // array of validation error codes
              var validationErrorArray = validationErrors
                ? Object.keys(validationErrors)
                : [];
              // add all the backend validation error messages as single sentence to present it to the users
              var validationMessages = "";
              // check validation json exist with json keys length
              if (validationErrorArray.length > 0) {
                for (var i in validationErrorArray) {
                  // IVE0054: You can access data only for the current month and the previous month.
                  if (validationErrorArray[i] === "IVE0152") {
                    validationMessages =
                      validationMessages +
                      " " +
                      validationErrors[validationErrorArray[i]];
                  }
                }
              }
              // if validation messages are available, we have to present that error messages
              if (validationMessages) {
                this.showRetryButton = false;
                this.errorContent = validationMessages;
              } else {
                // "IVE0194" - Please provide a valid timezone.
                // "IVE0150" - Please provide a valid work schedule id.
                this.errorContent = `Something went wrong while fetching the ${
                  source?.toLowerCase() === "apps" ? source + " usage " : source
                } details. If you continue to see this issue, please contact the platform support team.`;
              }
              break;
            // functional errors
            case "IVE0044": // When the select date is invalid
            case "EM0126": // Error while processing the request to retrieve activity apps.
            case "EM0127": // Error while processing the request to retrieve activity urls.
            case "EM0045": // Error while retrieving activity urls.
            case "EM0044": // Error while retrieving activity apps.
            case "EM0175": // Error while retrieving the url/domain details.
            case "EM0174": // Error while retrieving the apps details.
            case "_DB0001": // Error while retrieving the employee access rights
            case "_UH0001": //  unhandled error // common for all endpoint
            case "EM0198": // Error while calculating the minimum and maximum time range based on work schedule.
            default:
              this.errorContent = `Something went wrong while fetching the ${
                source?.toLowerCase() === "apps" ? source + " usage " : source
              } details. If you continue to see this issue, please contact the platform support team.`;
              break;
          }
        } else {
          this.errorContent = `Something went wrong while fetching the ${
            source?.toLowerCase() === "apps" ? source + " usage " : source
          } details. If you continue to see this issue, please contact the platform support team.`;
        }
      } else if (err && err.networkError) {
        this.errorContent = handleNetworkErrors(err);
      } else {
        this.errorContent = `Something went wrong while fetching the ${
          source?.toLowerCase() === "apps" ? source + " usage " : source
        } details. If you continue to see this issue, please contact the platform support team.`;
      }
      if (source?.toLowerCase() === "apps") this.listAppFetchError = true;
    },
  },
};
</script>
