<template>
  <v-row>
    <v-col
      v-if="isSearch"
      :xlg="isFilter ? 8 : 9"
      :lg="isFilter ? 7 : 9"
      :md="isFilter ? 9 : 10"
      :sm="isFilter ? 10 : 11"
      :cols="isFilter ? 10 : 11"
      class="d-flex justify-end align-center"
      :style="parentComponent === 'compOffBalance' ? 'margin-right: 20px' : ''"
    >
      <v-text-field
        v-show="showSearch"
        ref="EmpFilterSearch"
        v-model="searchInput"
        class="pr-5 search-input"
        :class="windowWidth < 960 ? 'pl-5' : ''"
        density="compact"
        clearable
        hide-details
        :placeholder="$t('searchPlaceholder')"
        variant="underlined"
        prepend-inner-icon="fas fa-search"
        color="primary"
        @blur="closeSearch()"
      ></v-text-field>
      <v-avatar
        v-show="!showSearch"
        class="cursor-pointer"
        size="38"
        color="primary"
        @click="openSearchField()"
      >
        <v-icon size="14" color="white">fas fa-search</v-icon>
      </v-avatar>
    </v-col>
    <v-col
      v-if="isFilter"
      xlg="1"
      :lg="isFilter ? 2 : 1"
      md="1"
      sm="2"
      cols="2"
      class="text-center d-flex align-center pl-1"
      :class="windowWidth > 960 ? 'justify-center' : ''"
    >
      <v-menu
        v-model="openEmpFilter"
        :location="menuPosition"
        z-index="10000"
        :close-on-content-click="false"
        class="filter-menu-position"
      >
        <template v-slot:activator="{ props }">
          <v-avatar
            class="cursor-pointer"
            size="38"
            color="primary"
            v-bind="props"
          >
            <v-icon size="13" color="white">fas fa-filter</v-icon>
          </v-avatar>
        </template>
        <v-list class="pa-5" min-width="300" max-width="600" max-height="80vh">
          <div>
            <div class="d-flex justify-end mt-n2 mr-n2">
              <v-icon
                color="primary"
                style="position: fixed"
                @click="openEmpFilter = !openEmpFilter"
              >
                fas fa-times</v-icon
              >
            </div>
            <section>
              <slot name="new-filter"></slot>
              <v-row v-if="isDefaultFilter" class="mr-2">
                <slot name="filter-menu"></slot>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-autocomplete
                    v-model="selectedDesignation"
                    color="primary"
                    :items="designationList"
                    label="Designation"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                    placeholder="Type minimum 3 characters to list"
                    :loading="designationListLoading"
                    :no-data-text="noDataText"
                    item-title="Designation_Name"
                    item-value="Designation_Id"
                    @update:search="callDesignationList($event)"
                  >
                  </v-autocomplete>
                </v-col>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-autocomplete
                    v-model="selectedDepartment"
                    color="primary"
                    :items="departmentList"
                    label="Department"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                    :loading="loadingData"
                    item-title="Department_Name"
                    item-value="Department_Id"
                  >
                  </v-autocomplete>
                </v-col>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-autocomplete
                    v-model="selectedEmpType"
                    color="primary"
                    :items="empTypeList"
                    label="Employee Type"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                    :loading="loadingData"
                    item-title="Employee_Type"
                    item-value="EmpType_Id"
                  >
                  </v-autocomplete>
                </v-col>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-autocomplete
                    v-model="selectedLocation"
                    color="primary"
                    :items="locationList"
                    label="Location"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                    :loading="loadingData"
                    item-title="Location_Name"
                    item-value="Location_Id"
                  >
                  </v-autocomplete>
                </v-col>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-autocomplete
                    v-model="selectedWorkSchedule"
                    color="primary"
                    :items="workScheduleList"
                    label="Work Schedule"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                    :loading="loadingData"
                    item-title="Title"
                    item-value="WorkSchedule_Id"
                  >
                  </v-autocomplete>
                </v-col>
                <v-col
                  v-if="
                    fieldForce &&
                    showServiceProvider &&
                    (isAdmin || isServiceProviderAdmin)
                  "
                  :cols="windowWidth > 600 ? 6 : 12"
                  class="py-2"
                >
                  <v-autocomplete
                    v-model="selectedServiceProvider"
                    color="primary"
                    :items="serviceProviderList"
                    :label="getCustomFieldName(115, 'Service Provider')"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                    :loading="loadingData"
                    item-title="Service_Provider_Name"
                    item-value="Service_Provider_Id"
                  >
                  </v-autocomplete>
                </v-col>
                <v-col
                  v-if="showRoles"
                  :cols="windowWidth > 600 ? 6 : 12"
                  class="py-2"
                >
                  <v-autocomplete
                    v-model="selectedRoles"
                    color="primary"
                    :items="rolesList"
                    label="Role (Access Rights)"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                    :loading="loadingData"
                    item-value="Roles_Id"
                    item-title="Roles_Name"
                  >
                  </v-autocomplete>
                </v-col>
                <slot name="bottom-filter-menu"></slot>
              </v-row>
            </section>
            <v-btn
              variant="elevated"
              class="mr-4 primary"
              rounded="lg"
              @click.stop="fnApplyFilter()"
            >
              <span class="primary">Apply</span>
            </v-btn>
            <v-btn
              class="primary"
              rounded="lg"
              variant="outlined"
              @click="resetFilterValues()"
            >
              <span class="primary">Reset</span>
            </v-btn>
          </div>
        </v-list>
      </v-menu>
    </v-col>
  </v-row>
</template>
<script>
import { getCustomFieldName } from "@/helper";
export default {
  name: "EmployeeDefaultFilterMenu",
  props: {
    isSearch: {
      type: Boolean,
      default: true,
    },
    isFilter: {
      type: Boolean,
      default: true,
    },
    menuPosition: {
      type: String,
      default: "bottom",
    },
    resetFilterCount: {
      type: Number,
      default: 0,
    },
    appliedFilterCount: {
      type: Number,
      default: 0,
    },
    isApplyFilter: {
      type: Boolean,
      default: false,
    },
    isDefaultFilter: {
      type: Boolean,
      default: true,
    },
    departmentIdKey: {
      type: String,
      default: "Department_Id",
    },
    designationIdKey: {
      type: String,
      default: "Designation_Id",
    },
    empTypeIdKey: {
      type: String,
      default: "EmpType_Id",
    },
    locationIdKey: {
      type: String,
      default: "Location_Id",
    },
    workScheduleIdKey: {
      type: String,
      default: "Work_Schedule",
    },
    serviceProviderIdKey: {
      type: String,
      default: "Service_Provider_Id",
    },
    rolesIdKey: {
      type: String,
      default: "Roles_Id",
    },
    showRoles: {
      type: Boolean,
      default: false,
    },
    showServiceProvider: {
      type: Boolean,
      default: false,
    },
    parentComponent: {
      type: String,
      default: "",
    },
    listItems: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data: () => ({
    loadingData: true,
    // search and filter
    searchInput: "",
    showSearch: false,
    openEmpFilter: false,
    departmentList: [],
    designationList: [],
    locationList: [],
    empTypeList: [],
    workScheduleList: [],
    selectedDesignation: [],
    selectedDepartment: [],
    selectedEmpType: [],
    selectedLocation: [],
    selectedWorkSchedule: [],
    isDropdownDataRetrieved: false,
    fieldForce: 0,
    serviceProviderList: [],
    selectedServiceProvider: [],
    rolesList: [],
    selectedRoles: [],
    designationListLoading: false,
    searchString: "",
  }),
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // retrieve state value and assign it
    empSearchValue() {
      return this.$store.state.empSearchValue;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    noDataText() {
      if (this.designationListLoading) {
        return "Loading...";
      } else if (
        !this.designationListLoading &&
        this.designationList.length == 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
  },
  watch: {
    searchInput(val) {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", val);
    },
    // watch the state value, because we update this value in parent component and this should be affected in child component
    empSearchValue(val) {
      if (!val) this.showSearch = false;
      this.searchInput = val;
    },
    // watch the filter menu, when its opened, we need to close the search bar
    openEmpFilter() {
      this.fetchDropdownData();
    },
    showSearch(val) {
      this.$emit("search-opened", val);
    },
    resetFilterCount() {
      this.resetFilterValues();
    },
    appliedFilterCount() {
      this.fnApplyFilter();
    },
  },
  methods: {
    getCustomFieldName,
    fetchDropdownData() {
      if (!this.isDropdownDataRetrieved) {
        this.$store
          .dispatch("getDefaultDropdownList", { formId: 15 })
          .then((res) => {
            if (
              res.data &&
              res.data.getDropDownBoxDetails &&
              !res.data.getDropDownBoxDetails.errorCode
            ) {
              const {
                departments,
                locations,
                employeeType,
                workSchedules,
                fieldForce,
                serviceProvider,
                roles,
              } = res.data.getDropDownBoxDetails;
              this.departmentList = departments;
              this.locationList = locations;
              this.empTypeList = employeeType;
              this.workScheduleList = workSchedules;
              this.isDropdownDataRetrieved = true;
              this.serviceProviderList = serviceProvider;
              this.rolesList = roles;
              this.fieldForce = fieldForce;
              this.loadingData = false;
            } else {
              this.handleDropdownDataError();
            }
          })
          .catch(() => {
            this.handleDropdownDataError();
          });
      }
    },
    handleDropdownDataError() {
      this.departmentList = [];
      this.locationList = [];
      this.empTypeList = [];
      this.workScheduleList = [];
      this.loadingData = false;
    },
    // apply filter
    fnApplyFilter() {
      this.openEmpFilter = false;
      if (this.isApplyFilter) {
        this.onApplyFilter();
      } else {
        this.$emit("apply-emp-filter");
      }
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-emp-filter");
    },
    resetAllModelValues() {
      this.openEmpFilter = false;
      this.selectedDepartment = [];
      this.selectedDesignation = [];
      this.selectedEmpType = [];
      this.selectedLocation = [];
      this.selectedWorkSchedule = [];
      this.selectedServiceProvider = [];
      this.selectedRoles = [];
    },
    openSearchField() {
      this.showSearch = true;
      this.$nextTick(() => {
        this.$refs.EmpFilterSearch.focus();
      });
    },
    closeSearch() {
      // we have to present the search text field until the value is cleared
      if (!this.searchInput) {
        this.showSearch = false;
      }
    },
    onApplyFilter() {
      let filteredArray = this.listItems;
      if (this.selectedDesignation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDesignation.includes(item[this.designationIdKey]);
        });
      }
      if (this.selectedDepartment.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDepartment.includes(item[this.departmentIdKey]);
        });
      }
      if (this.selectedEmpType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmpType.includes(item[this.empTypeIdKey]);
        });
      }
      if (this.selectedLocation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLocation.includes(item[this.locationIdKey]);
        });
      }
      if (this.selectedWorkSchedule.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedWorkSchedule.includes(
            item[this.workScheduleIdKey]
          );
        });
      }
      if (this.selectedServiceProvider.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedServiceProvider.includes(
            item[this.serviceProviderIdKey]
          );
        });
      }
      if (this.selectedRoles.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedRoles.includes(item[this.rolesIdKey]);
        });
      }
      this.$emit("applied-filter", filteredArray);
    },

    callDesignationList(searchString) {
      this.searchString = searchString;
      if (searchString.length >= 3) {
        this.getDesignationList(searchString);
      }
    },

    async getDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designationList = designationResult;
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.dropdownDesignation = [];
        });
    },
  },
};
</script>
<style scoped>
.filter-menu-position {
  margin-top: 28px !important;
  border-radius: 25px !important;
}

.mobile_view_filter_icon {
  border-radius: 50% !important;
  min-width: 40px !important;
  min-height: 40px;
}

.search-input >>> .v-icon {
  font-size: 15px !important;
  margin-top: 5px;
}
</style>
