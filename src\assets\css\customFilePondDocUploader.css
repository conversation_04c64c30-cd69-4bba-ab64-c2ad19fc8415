.filepond--panel-root {
    border-radius: 0.5em;
}
.filepond--drop-label.filepond--drop-label label {
    margin: 5px !important;
    border: 2px solid #cecece;
    border-radius : 10px !important;
    border-style: dashed;
    width: 100%;
    height : 240px !important;
    display : flex !important;
    justify-content : center !important;
    align-items : center !important;
    padding-top: 4em !important;
    cursor: pointer;
}
.filepond--drip-blob {
    background-color: purple;
}
.filepond--root .filepond--drop-label {
    height : 250px !important;
    border-radius : 10px !important
}
.custom-file-pond-label-header {
    white-space: pre-line;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    text-align : center;

}
.custom-file-pond-label-content {
    white-space: pre-line;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    text-align : center;
    color : grey;
    margin-bottom : 6em
}
.custom-file-upload-label-icon {
    height: 100%;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}
.custom-file-upload-label-icon1 {
    font-size : 80px !important;
    color : grey;
}
.custom-file-pond-label-sub-header1 {
    font-size : 1.2em !important;
    color : var(--v-secondary-base);
    font-weight : 700
}
.custom-file-pond-label-sub-header2 {
    color : grey;

}
.filepond--credits{
    display: none;
}
@media screen and (max-width:1475px) and (min-width: 1264px){
    .custom-file-pond-label-content{
        margin-top: 50px;
    }
}
@media screen and (max-width:600px) {
    .custom-file-pond-label-content{
        margin-top: 50px;
    }
    .custom-file-pond-label-header{
        display: none;
    }
    .custom-file-pond-label-content{
        display: none;
    }
}
@media screen and (min-width:601px){
    .custom-label-less-than-450{
        display: none;
    }
}