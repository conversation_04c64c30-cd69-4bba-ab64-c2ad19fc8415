<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedClaimCurrency"
              :items="claimCurrencyList"
              item-title="currencyName"
              item-value="currencyId"
              label="Claim Currency"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedPayrollCurrency"
              :items="payrollCurrencyList"
              item-title="payrollCurrency"
              label="Payroll Currency"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedConversionType"
              :items="conversionTypeList"
              label="Conversion Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
export default defineComponent({
  name: "FormFilter",

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },

  data: () => ({
    openFormFilter: false,
    conversionTypeList: ["Manual", "Auto"],
    selectedConversionType: [],
    payrollCurrencyList: [],
    selectedPayrollCurrency: [],
    claimCurrencyList: [],
    selectedClaimCurrency: [],
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    fieldForce() {
      const { fieldForce } = this.$store.state.orgDetails;
      return fieldForce;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.fnApplyFilter();
    this.formFilterData();
  },
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.items;

      if (this.selectedConversionType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedConversionType.includes(item.conversionType);
        });
      }
      if (this.selectedPayrollCurrency.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedPayrollCurrency.includes(
            item.payrollCurrencyName
          );
        });
      }
      if (this.selectedClaimCurrency.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedClaimCurrency.includes(item.claimCurrencyId);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    formFilterData() {
      for (let item of this.items) {
        if (item && item.claimCurrencyName) {
          if (item && (item.claimCurrencyName || item.claimCurrencyId)) {
            this.claimCurrencyList.push({
              currencyName:
                item.claimCurrencyName + " - " + item.claimCurrencyCode,
              currencyId: item.claimCurrencyId,
            });
          }
          this.payrollCurrencyList.push({
            payrollCurrency: item.payrollCurrencyName,
          });
        }
      }
      this.claimCurrencyList = this.removeDuplicatesFromArrayOfObject(
        this.claimCurrencyList,
        "currencyName"
      );
      this.payrollCurrencyList = this.removeDuplicatesFromArrayOfObject(
        this.payrollCurrencyList,
        "payrollCurrency"
      );
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedConversionType = [];
      this.selectedPayrollCurrency = [];
      this.selectedClaimCurrency = [];
      this.openFormFilter = false;
    },
  },
});
</script>
