<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="secondary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedAutoLopApplicable"
              color="secondary"
              :items="yesNoDropdownList"
              label="Auto LOP"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedAttendanceShortageApplicable"
              color="secondary"
              :items="yesNoDropdownList"
              label="Attendance Shortage"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedLateAttendanceApplicable"
              color="secondary"
              :items="yesNoDropdownList"
              label="Late Attendance"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
export default defineComponent({
  name: "FormFilter",

  props: {
    items: {
      type: Array,
      default: () => [],
    },
  },

  data: () => ({
    openFormFilter: false,
    yesNoDropdownList: ["Yes", "No"],
    selectedAutoLopApplicable: [],
    selectedAttendanceShortageApplicable: [],
    selectedLateAttendanceApplicable: [],
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.fnApplyFilter();
  },
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.items;
      if (this.selectedAutoLopApplicable.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedAutoLopApplicable.includes(
            item.Auto_LOP_Applicable
          );
        });
      }
      if (this.selectedAttendanceShortageApplicable.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedAttendanceShortageApplicable.includes(
            item.Attendance_Shortage_Applicable
          );
        });
      }
      if (this.selectedLateAttendanceApplicable.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLateAttendanceApplicable.includes(
            item.Late_Attendance_Applicable
          );
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedAutoLopApplicable = [];
      this.selectedAttendanceShortageApplicable = [];
      this.selectedLateAttendanceApplicable = [];
      this.openFormFilter = false;
    },
  },
});
</script>
