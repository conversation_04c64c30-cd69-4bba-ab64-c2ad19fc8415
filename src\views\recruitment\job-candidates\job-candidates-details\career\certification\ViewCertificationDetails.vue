<template>
  <div
    v-if="certificationDetails && certificationDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-subtitle-1 text-grey pl-7"
  >
    No certifications have been uploaded
  </div>
  <div class="ml-7 pb-4"></div>
  <v-card
    elevation="3"
    v-for="(data, index) in certificationDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg mr-4 mb-1"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:400px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <v-row>
      <v-col
        v-if="labelList[284].Field_Visiblity?.toLowerCase() === 'yes'"
        cols="12"
        class="pa-0 pl-4 d-flex"
      >
        <v-tooltip :text="data.Certification_Name" location="bottom">
          <template v-slot:activator="{ props }">
            <div
              class="text-primary font-weight-bold text-subtitle-1 text-truncate"
              :style="isMobileView ? 'max-width: 180px' : 'max-width: 350px'"
              v-bind="data.Certification_Name ? props : ''"
            >
              {{ checkNullValue(data.Certification_Name) }}
            </div>
          </template>
        </v-tooltip>
      </v-col>
      <v-col
        v-if="labelList[286].Field_Visiblity?.toLowerCase() === 'yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="mt-2 mr-2 d-flex flex-column justify-start text-body-1">
          <b class="mr-2 text-grey justify-start">
            {{ labelList[286].Field_Alias }}
          </b>
          <v-tooltip
            :text="data.Certificate_Received_From"
            location="bottom"
            max-width="400"
          >
            <template v-slot:activator="{ props }">
              <span class="pb-1 pt-1" v-bind="props">
                <div
                  :style="isMobileView ? 'max-width: 200px' : 'max-width:140px'"
                  class="text-truncate"
                >
                  {{ checkNullValue(data.Certificate_Received_From) }}
                </div></span
              ></template
            ></v-tooltip
          >
        </div>
      </v-col>
      <v-col
        v-if="labelList[285].Field_Visiblity?.toLowerCase() === 'yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="mt-2 mr-2 d-flex flex-column justify-start text-body-1">
          <b class="mr-2 text-grey justify-start">
            {{ labelList[285].Field_Alias }}
          </b>
          <span class="pb-1 pt-1">{{ formatDate(data.Received_Date) }}</span>
        </div></v-col
      >
      <v-col
        v-if="labelList[337].Field_Visiblity?.toLowerCase() === 'yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="mt-2 mr-2 d-flex flex-column justify-start text-body-1">
          <b class="mr-2 text-grey justify-start">
            {{ labelList[337].Field_Alias }}
          </b>
          <v-tooltip :text="data.Ranking" location="bottom" max-width="400">
            <template v-slot:activator="{ props }">
              <span class="pb-1 pt-1" v-bind="props">
                <div
                  :style="isMobileView ? 'max-width: 200px' : 'max-width:140px'"
                  class="text-truncate"
                >
                  {{ checkNullValue(data.Ranking) }}
                </div></span
              ></template
            ></v-tooltip
          >
        </div>
      </v-col>
    </v-row>
  </v-card>
</template>

<script>
import { generateRandomColor, checkNullValue } from "@/helper";
import moment from "moment";

export default {
  name: "ViewCertificationDetails",
  props: {
    certificationDetails: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date, "YYYY/MM/DD").format(orgDateFormat);
        } else return "-";
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
  },
};
</script>
