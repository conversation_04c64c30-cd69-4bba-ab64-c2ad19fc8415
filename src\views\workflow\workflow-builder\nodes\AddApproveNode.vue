<template>
  <div>
    <Handle type="target" :position="targetPosition"> </Handle>
    <div
      style="
        position: absolute;
        bottom: -10px;
        z-index: 99999;
        left: 50%;
        transform: translate(-50%);
      "
      v-if="data.addNew"
    >
      <div class="" style="position: relative">
        <div
          :class="
            'glow-button' + (!showMenu ? ' glow-button-active' : ' dark-button')
          "
          v-click-outside="() => (showMenu = false)"
          @click="() => (showMenu = !showMenu)"
        >
          <v-icon class="white" size="8">fas fa-plus</v-icon>
        </div>
        <v-expand-x-transition>
          <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
            <MenuItems
              v-if="showMenu"
              @handleProcessNode="handleToStartNode"
            ></MenuItems>
          </div>
        </v-expand-x-transition>
      </div>
    </div>
    <div class="" style="padding: 7.2px"></div>
  </div>
</template>
<script>
import { Position, Handle } from "@vue-flow/core";
import MenuItems from "../components/menus/MainMenu.vue";
export default {
  name: "AddApproveNode",
  emits: ["handleToStart"],
  props: {
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    sourcePosition: {
      type: String,
      required: true,
    },
    targetPosition: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      leftPosition: Position.Top,
      rightPosition: Position.Bottom,
      showMenu: false,
    };
  },
  methods: {
    handleToStartNode(type) {
      this.$emit("handleToStart", type, this.data, true, 0);
    },
  },
  components: {
    Handle,
    MenuItems,
  },
};
</script>
<style></style>
