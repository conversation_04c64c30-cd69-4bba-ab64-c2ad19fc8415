<template>
  <v-row class="d-flex justify-space-between my-2 pa-1">
    <v-col cols="6" sm="6">
      <div class="d-flex" :class="isMobileView ? 'mb-2' : ''">
        <v-btn
          color="white"
          rounded="lg"
          class="mr-2"
          @click="onCloseView()"
          :size="isMobileView ? 'small' : 'default'"
          ><v-icon color="primary" size="14" class="pr-2"
            >fas fa-chevron-left</v-icon
          ><span>Back</span></v-btn
        >
        <v-btn
          color="white"
          class="px-3 mr-3 text-primary"
          rounded="lg"
          size="'default'"
          v-if="selectedHistory"
        >
          <span v-if="selectedHistory.Salary_Type">{{
            selectedHistory.Salary_Type + " - "
          }}</span
          ><span v-if="selectedHistory.Work_Day_Type">{{
            selectedHistory.Work_Day_Type
          }}</span></v-btn
        >
      </div>
    </v-col>
    <v-col
      cols="6"
      sm="6"
      class="d-flex justify-end"
      v-if="compOffHistory?.length > 0"
    >
      <v-btn
        color="transparent"
        class="ml-1 mt-2"
        variant="flat"
        size="small"
        @click="getHistoryData()"
        ><v-icon color="primary">fas fa-redo-alt</v-icon></v-btn
      >
      <v-menu v-model="openMoreMenu" transition="scale-transition">
        <template v-slot:activator="{ props }">
          <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
            <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
            <v-icon v-else>fas fa-caret-up</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="action in moreActions"
            :key="action.key"
            @click="onMoreAction(action.key)"
          >
            <v-hover>
              <template v-slot:default="{ isHovering, props }">
                <v-list-item-title
                  v-bind="props"
                  class="py-2 px-3"
                  :class="{
                    'pink-lighten-5': isHovering,
                  }"
                  ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                  >{{ action.key }}</v-list-item-title
                >
              </template>
            </v-hover>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-col>
  </v-row>
  <div>
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else-if="isErrorInList">
      <AppFetchErrorScreen
        image-name="common/common-error-image"
        :content="errorContent"
        icon-name="fas fa-redo-alt"
        button-text="Retry"
        :isSmallImage="true"
        @button-click="getHistoryData()"
      >
      </AppFetchErrorScreen>
    </div>
    <div v-else-if="compOffHistory?.length > 0">
      <v-row
        ><v-col :cols="isSmallTable ? 5 : 12">
          <v-data-table
            :headers="tableHeaders"
            :items="compOffHistory"
            fixed-header
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(
                290,
                compOffHistory
              )
            "
            :items-per-page="50"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              {
                value: -1,
                title: '$vuetify.dataFooter.itemsPerPageAll',
              },
            ]"
          >
            <template v-slot:item="{ item }">
              <tr
                @click="openHistoryViewForm(item)"
                class="data-table-tr bg-white cursor-pointer"
                :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
              >
                <td
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div
                    v-if="isMobileView"
                    class="text-subtitle-1 text-grey-darken-1 mt-2"
                  >
                    Comp Off Threshold
                  </div>
                  <section class="d-flex align-center">
                    <div
                      v-if="
                        isSmallTable &&
                        !isMobileView &&
                        selectedHistoryItem?.History_Id === item?.History_Id
                      "
                      class="data-table-side-border selected-item-border-color d-flex"
                    ></div>
                    <v-tooltip
                      :text="item.Comp_Off_Threshold"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="
                            item.Comp_Off_Threshold?.length > 50 ? props : ''
                          "
                        >
                          {{ checkNullValue(item.Comp_Off_Threshold) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div
                    v-if="isMobileView"
                    class="text-subtitle-1 text-grey-darken-1 mt-2"
                  >
                    Comp Off Expiry Type
                  </div>
                  <section class="d-flex align-center">
                    <div
                      v-if="
                        isSmallTable &&
                        !isMobileView &&
                        selectedItem &&
                        selectedItem.Custom_Group_Id === item.Custom_Group_Id
                      "
                      class="d-flex"
                    ></div>
                    <v-tooltip
                      :text="item.Comp_Off_Expiry_Type"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="
                            item.Comp_Off_Expiry_Type?.length > 50 ? props : ''
                          "
                        >
                          {{ checkNullValue(item.Comp_Off_Expiry_Type) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  v-if="!isSmallTable"
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div
                    v-if="isMobileView"
                    class="text-subtitle-1 text-grey-darken-1 mt-2"
                  >
                    Additional Comp Off For Overtime Hours
                  </div>
                  <section class="d-flex align-center">
                    <div
                      v-if="
                        isSmallTable &&
                        !isMobileView &&
                        selectedItem &&
                        selectedItem.Custom_Group_Id === item.Custom_Group_Id
                      "
                      class="d-flex"
                    ></div>
                    <v-tooltip
                      :text="item.Comp_Off_Applicability_For_Overtime_Hours"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="
                            item.Comp_Off_Applicability_For_Overtime_Hours
                              ?.length > 50
                              ? props
                              : ''
                          "
                        >
                          {{
                            checkNullValue(
                              item.Comp_Off_Applicability_For_Overtime_Hours
                            )
                          }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  v-if="!isSmallTable"
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div
                    v-if="isMobileView"
                    class="text-subtitle-1 text-grey-darken-1 mt-2"
                  >
                    Custom Group
                  </div>
                  <section class="d-flex align-center">
                    <div
                      v-if="
                        isSmallTable &&
                        !isMobileView &&
                        selectedItem &&
                        selectedItem.Custom_Group_Id === item.Custom_Group_Id
                      "
                      class="d-flex"
                    ></div>
                    <v-tooltip
                      :text="item?.Group_Name"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="item.Group_Name?.length > 50 ? props : ''"
                        >
                          {{ checkNullValue(item?.Group_Name) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  v-if="!isSmallTable"
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div
                    v-if="isMobileView"
                    class="text-subtitle-1 text-grey-darken-1 mt-2"
                  >
                    Updated On
                  </div>
                  <section class="d-flex align-center">
                    <div
                      v-if="
                        isSmallTable &&
                        !isMobileView &&
                        selectedItem &&
                        selectedItem.Custom_Group_Id === item.Custom_Group_Id
                      "
                      class="d-flex"
                    ></div>
                    <v-tooltip
                      :text="item?.Added_On"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="item.Added_On?.length > 50 ? props : ''"
                        >
                          {{
                            checkNullValue(
                              this.formatDate(
                                new Date(item?.Added_On + ".000Z")
                              )
                            )
                          }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  v-if="!isSmallTable"
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div
                    v-if="isMobileView"
                    class="text-subtitle-1 text-grey-darken-1 mt-2"
                  >
                    Updated By
                  </div>
                  <section class="d-flex align-center">
                    <div
                      v-if="
                        isSmallTable &&
                        !isMobileView &&
                        selectedItem &&
                        selectedItem.Custom_Group_Id === item.Custom_Group_Id
                      "
                      class="d-flex"
                    ></div>
                    <v-tooltip
                      :text="item?.Added_By_Name"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="item.Added_By_Name?.length > 50 ? props : ''"
                        >
                          {{ checkNullValue(item?.Added_By_Name) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-col>
        <v-col cols="7" v-if="showHistoryViewForm && windowWidth >= 1264">
          <!-- <ViewCompOffHistory
            :selectedItem="selectedHistoryItem"
            :access-rights="formAccess"
            @close-form="closeAllForms()"
          /> -->
          <ViewCompOff
            formName="compoffhistory"
            :compOffData="selectedHistoryItem"
            :selectedItem="selectedHistoryItem"
            :isHistory="true"
            :access-rights="formAccess"
            @close-split-view="closeAllForms()"
          /> </v-col
      ></v-row>
      <AppLoading v-if="listLoading"></AppLoading>
      <v-dialog
        :model-value="openFormInModal"
        class="pl-4"
        width="900"
        @click:outside="closeAllForms()"
      >
        <!-- <ViewCompOffHistory
          v-if="showHistoryViewForm"
          :selectedItem="selectedHistoryItem"
          @close-form="closeAllForms()"
        /> -->
        <ViewCompOff
          :compOffData="selectedHistoryItem"
          :selectedItem="selectedHistoryItem"
          :isHistory="true"
          :access-rights="formAccess"
          @close-split-view="closeAllForms()"
        />
      </v-dialog>
    </div>

    <AppFetchErrorScreen
      v-else
      key="no-results-screen"
      main-title="There are no history matched for the selected Comp-off."
      image-name="common/no-records"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4">
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="windowWidth <= 960 ? 'small' : 'default'"
                @click="getHistoryData()"
              >
                Refresh
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
  </div>
</template>
<script>
import { LIST_COMPOFF_CONFIG_HISTORY } from "@/graphql/settings/core-hr/compOffQueries.js";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import moment from "moment";
import { defineAsyncComponent } from "vue";
const ViewCompOff = defineAsyncComponent(() => import("../ViewCompOff.vue"));
export default {
  name: "CompOffHistoryView",
  mixins: [FileExportMixin],
  components: {
    ViewCompOff,
  },
  emits: ["close-history-view"],
  props: {
    selectedHistory: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  data: () => ({
    listLoading: false,
    compOffHistory: [],
    openMoreMenu: false,
    isErrorInList: false,
    showHistoryViewForm: false,
    selectedHistoryItem: null,
  }),
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat + " HH:mm:ss") : "";
        }
      };
    },
    isSmallTable() {
      return !this.openFormInModal && this.showHistoryViewForm;
    },
    openFormInModal() {
      if (this.showHistoryViewForm && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    moreActions() {
      return [{ key: "Export", icon: "fas fa-file-export" }];
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Comp Off Threshold",
            align: "start",
            key: "Comp_Off_Threshold",
          },
          {
            title: "Comp Off Expiry Type",
            key: "Comp_Off_Expiry_Type",
          },
        ];
      } else {
        return [
          {
            title: "Comp Off Threshold",
            align: "start",
            key: "Comp_Off_Threshold",
          },
          {
            title: "Comp Off Expiry Type",
            key: "Comp_Off_Expiry_Type",
          },
          {
            title: "Additional Comp Off For Overtime Hours",
            key: "Comp_Off_Applicability_For_Overtime_Hours",
          },
          {
            title: "Custom Group",
            key: "Group_Name",
          },
          {
            title: "Updated On",
            key: "Added_On",
          },
          {
            title: "Updated By",
            key: "Added_By_Name",
          },
        ];
      }
    },
  },
  watch: {},
  mounted() {
    this.getHistoryData();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    closeAllForms() {
      this.showHistoryViewForm = false;
      this.selectedHistoryItem = null;
    },
    openHistoryViewForm(item) {
      this.showHistoryViewForm = true;
      this.selectedHistoryItem = item;
    },
    getHistoryData() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_COMPOFF_CONFIG_HISTORY,
          client: "apolloClientI",
          variables: {
            configurationId: vm.selectedHistory?.Configuration_Id,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          vm.listLoading = false;
          if (
            res &&
            res.data &&
            res.data.listCompOffConfigHistory &&
            res.data.listCompOffConfigHistory.compOffHistory &&
            !res.data.listCompOffConfigHistory.errorCode
          ) {
            const resultData = res.data.listCompOffConfigHistory.compOffHistory;
            resultData.sort(
              (a, b) => new Date(b.Added_On) - new Date(a.Added_On)
            );
            this.compOffHistory = resultData;
          }
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Comp Off",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    onCloseView() {
      this.$emit("close-history-view");
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = this.compOffHistory;
      exportData = exportData.map((el) => ({
        ...el,
        updatedOn: el.updatedOn ? this.convertUTCToLocal(el.updatedOn) : "",
        updatedBy: el.updatedBy,
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName + "History",
        sheetName: this.landedFormName + "History",
        header: [
          { header: "Salary Type", key: "Salary_Type" },
          { header: "Work Day Type", key: "Work_Day_Type" },
          { header: "Custom Group", key: "Group_Name" },
          {
            header: "Comp Off Balance Accrual",
            key: "Comp_Off_Balance_Approval",
          },
          { header: "Comp Off Threshold", key: "Comp_Off_Threshold" },
          { header: "Fixed Regular Hours", key: "Fixed_Regular_Hours" },
          {
            header: "Allow Half Day Comp Off Credit",
            key: "Allow_Half_Day_Comp_Off_Credit",
          },
          { header: "Comp Off Expiry Type", key: "Comp_Off_Expiry_Type" },
          { header: "Comp Off Expiry After", key: "Comp_Off_Expiry_Days" },
          {
            header: "Additional Comp Off For Overtime Hours",
            key: "Comp_Off_Applicability_For_Overtime_Hours",
          },
          {
            header: "Minimum Overtime Hours For Full Day Comp Off",
            key: "Minimum_OT_Hours_For_Full_Day_Comp_Off",
          },
          {
            header: "Minimum Overtime Hours For Half Day Comp Off",
            key: "Minimum_OT_Hours_For_Half_Day_Comp_Off",
          },
          { header: "Comp Off Encashment", key: "Comp_Off_Encashment" },
          { header: "Encashment Mode", key: "Encashment_Mode" },
          { header: "Status", key: "Status" },
          { header: "Update By", key: "Added_By_Name" },
          { header: "Update On", key: "Added_On" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>

<style scoped>
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 3.5em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
.selected-item-border-color {
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
