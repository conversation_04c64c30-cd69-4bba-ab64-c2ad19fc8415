import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const GET_USER_SIGNIN_DETAILS = gql`
  query getUserAuthDetails($refreshToken: String!) {
    getUserAuthDetails(refreshToken: $refreshToken) {
      errorCode
      message
      authDetails
    }
  }
`;

export const GET_ENTOMO_USER_DETAILS = gql`
  query getEntomoUserAuthDetails($userName: String!) {
    getEntomoUserAuthDetails(userName: $userName) {
      errorCode
      message
      authDetails
    }
  }
`;
