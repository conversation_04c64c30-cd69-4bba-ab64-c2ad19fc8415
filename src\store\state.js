const state = {
  windowWidth: null, //used to access screen size changes in all component
  isMobileWindowSize: false, //to check mobile view window size
  currentUser: "", //current logged in user firebase object
  userIpAddress: "", // user Ip address
  formAccessRights: null, //Access rights for all forms
  formIdAccessRights: null,
  sideBarMenuList: [], //sidebar menu list
  isAdmin: 0, //to check user is an admin or not
  isManager: 0, //to check user is an manager or not
  isRecruiter: 0, //to check user is an recruiter or not
  empSearchValue: "", //employees search and filter form data
  isWebpSupport: false, //browser support webp
  planDashboardType: "", // dashboard type chosen in plan
  //snackbar data
  showSnackbar: false,
  snackBarMessage: "",
  snackBarType: "warning",
  appTrackingMode: null,
  //vue forms list hard coded to route vue forms differently
  vue3Forms: [
    { formId: 15, formName: "Job Posts" },
    { formId: 16, formName: "Job Candidates" },
    { formId: 23, formName: "Timesheets" },
    { formId: 34, formName: "Exit Management" },
    { formId: 52, formName: "Provident Fund" },
    { formId: 126, formName: "NPS" },
    { formId: 140, formName: "Compensatory Off Balance" },
    { formId: 173, formName: "Workflow Builder" },
    { formId: 178, formName: "Individuals" },
    { formId: 182, formName: "Interview Rounds Master" },
    { formId: 184, formName: "Approval Management" },
    { formId: 188, formName: "Dynamic Form Builder" },
    { formId: 205, formName: "Payroll" },
    { formId: 239, formName: "Full & Final Settlement" },
    { formId: 240, formName: "Pre Approvals" },
    { formId: 241, formName: "Integration" },
    { formId: 243, formName: "Team Summary" },
    { formId: 247, formName: "Pre Approval" },
    { formId: 252, formName: "LOP Recovery" },
    { formId: 256, formName: "LOP Recovery" },
    { formId: 257, formName: "Pre Approval" },
    { formId: 262, formName: "Timesheets" },
    { formId: 264, formName: "Compensatory Off Balance" },
    { formId: 276, formName: "Time Off Management" },
    { formId: 281, formName: "Recruitment" },
    { formId: 283, formName: "Org Structure" },
    { formId: 285, formName: "Settings" },
    { formId: 287, formName: "Hiring Forecast" },
    { formId: 288, formName: "Table of Organization" },
    { formId: 289, formName: "Job Requisition" },
    { formId: 295, formName: "Location Intelligence" },
    { formId: 303, formName: "Key Logger" },
    { formId: 304, formName: "Attendance" },
    { formId: 305, formName: "Attendance" },
    { formId: 306, formName: "Shift Swap" },
    { formId: 308, formName: "My Integration" },
    { formId: 316, formName: "Payroll Reconciliation" },
    { formId: 318, formName: "Payroll Data Management" },
    { formId: 325, formName: "Recruitment Dashboard" },
    { formId: 336, formName: "Payroll Management" },
    { formId: 330, formName: "Time Off" },
    { formId: 331, formName: "Time Off" },
    { formId: 340, formName: "Travel and Expenses" },
    { formId: 343, formName: "Travel and Expenses" },
    { formId: 344, formName: "My Pay" },
  ],
  vue2Forms: [
    "Productivity Monitoring",
    "Activity Tracker",
    "Members",
    "Reports",
    "Shift Scheduling",
    "Salary Template",
    "Additional Wage Claim",
    "Activity Dashboard",
    "Tax and Statutory Compliance",
    "Assets",
    "Performance Management",
    "Billing",
    "ESOP",
    "Workforce Analytics",
    "DocuSign",
    "Register Face",
    "Core HR",
    "File Transfers",
    "Data Loss Prevention",
    "Vendors",
    "Accreditation",
    "Employee Data Management",
    "Performance Evaluation",
  ],
  partnerForms: ["Trulead"],
  // organization details all in single object
  orgDetails: {
    organizationName: "",
    orgDateFormat: "YYYY/MM/DD", //by default we have this format.Change based on org format
    assessmentYear: "", // assessment year of the organization
    paycycle: "",
    employeeId: 0, // employee id of the login user
    advancePayroll: "",
    employeeEdit: false,
    fieldForce: false,
    autoUpdateEffectiveDateForJobDetails: "No",
    organization: {},
  },
  // login emp details
  userDetails: {
    employeeId: null,
    userDefinedEmployeeId: "",
    employeeFullName: "",
    employeeFirstName: "",
    employeePhotoPath: "",
    employeeEmail: "",
    employeeLastName: "",
  },
  payrollUrl: null,
  // perfect-scrollbar settings with wheel propagation to avoid external scrolling
  scrollbarWithWheelPropagationSettings: {
    maxScrollbarLength: 60,
    wheelSpeed: 0.6,
    wheelPropagation: false,
  },
  // perfect-scrollbar settings without wheelPropagation
  scrollbarSettings: {
    maxScrollbarLength: 60,
    wheelSpeed: 0.6,
  },
  // perfect-scrollbar settings without wheelPropagation
  smallScrollbarSettings: {
    maxScrollbarLength: 30,
    wheelSpeed: 0.6,
  },
  autoBillingDetails: {
    planId: "",
    subscriptionId: "",
    subscribedPlanQuantity: 0,
    subscriptionCurrency: "",
    subscriptionBillingPeriod: 0,
  },
  trialPeriod: null, // pending days of trial period
  planSubscriptionStatus: "", // it maybe none, skipSub, or subscribed
  isAutoBilling: null, // in default it was set null, after retrieving the value may be 0 or 1
  autoBillingPlanCurrentStatus: "", // it defines the current status of the plan
  clearTopBarSearch: false,
  showRequestAccessNotification: false,
  chargeBeeSiteName: "",
  adminForms: [
    "Admin",
    "Employee Admin",
    "Payroll Admin",
    "Service Provider Admin",
    "Productivity Monitoring Admin",
  ],
  payrollCountry: null,
  // order should be based on tab order
  employeeDataSetupForms: [
    {
      formName: "custom-employee-groups",
      displayFormName: "Custom Group",
      url: "custom-employee-groups",
      isVue3: false,
    },
    {
      formName: "projects",
      displayFormName: "Projects",
      url: "projects",
      isVue3: true,
    },
    {
      formName: "holidays",
      displayFormName: "Holidays",
      url: "holidays",
      isVue3: false,
    },
    {
      formName: "user-accounts",
      displayFormName: "User Accounts",
      url: "user-accounts",
      isVue3: false,
    },
    {
      formName: "document-sub-type",
      displayFormName: "Document Subtype",
      url: "document-subtype",
      isVue3: true,
    },
    {
      formName: "employee-data-import",
      displayFormName: "Employee Data Import",
      url: "employee-data-import",
      isVue3: true,
    },
    {
      formName: "accreditation-category-and-type",
      displayFormName: "Accreditation Category and Type",
      url: "accreditation-category-and-type",
      isVue3: true,
    },
  ],
  coreHrTimeOffForms: [
    {
      formId: 359,
      displayName: "Time Off Closure",
      url: "/core-hr/time-off-management/time-off-closure",
    },
    {
      formId: 277,
      displayName: "Leave Override",
      url: "/core-hr/time-off-management/leave-override",
    },
    {
      formId: 347,
      displayName: "Leave Policy",
      url: "/core-hr/leave-policy",
    },
    {
      formId: 356,
      displayName: "Leave Closure",
      url: "/core-hr/leave-closure",
    },
  ],
  empDataManagementForms: [
    {
      formName: "customGroups",
      displayFormName: "coreHr.customGroup",
      formId: 180,
      url: "custom-employee-groups",
      isVue3: false,
    },
    {
      formName: "projects",
      displayFormName: "coreHr.projects",
      url: "projects",
      formId: 3,
      isVue3: true,
    },
    {
      formName: "holidays",
      displayFormName: "coreHr.holidays",
      url: "holidays",
      formId: 8,
      isVue3: false,
    },
    {
      formName: "userAccounts",
      displayFormName: "coreHr.userAccounts",
      url: "user-accounts",
      formId: 238,
      isVue3: false,
    },
    {
      formName: "documentSubtype",
      displayFormName: "coreHr.documentSubtype",
      url: "document-subtype",
      formId: 177,
      isVue3: true,
    },
    {
      formName: "employeeDataImport",
      displayFormName: "coreHr.employeeDataImport",
      url: "employee-data-import",
      formId: -1,
      isVue3: true,
    },
    {
      formName: "accreditationCategoryAndType",
      displayFormName: "coreHr.accreditationCategoryAndType",
      url: "accreditation-category-and-type",
      formId: 351,
      isVue3: true,
    },
  ],
  employeeAttendanceForms: [
    {
      formName: "attendance",
      formId: 305,
      displayFormName: "Attendance",
      url: "attendance",
      isVue3: true,
    },
  ],
  myTeamAttendanceForms: [
    {
      formName: "attendance",
      formId: 304,
      displayFormName: "Attendance",
      url: "attendance",
      isVue3: true,
    },
    {
      formName: "approvals",
      formId: 313,
      displayFormName: "Approvals",
      url: "attendance/attendance-approvals",
      isVue3: true,
    },
    {
      formName: "attendance-bulk-action",
      formId: 320,
      displayFormName: "Attendance Bulk Action",
      url: "attendance/attendance-bulk-action",
      isVue3: true,
    },
    {
      formName: "attendance-finalization",
      formId: 357,
      displayFormName: "Attendance Finalization",
      url: "attendance/attendance-finalization",
      isVue3: true,
    },
  ],
  activityDashboardFormAccess: [
    {
      formName: "cxo-dashboard",
      displayFormName: "productivityMonitoring.cxoDashboard",
      url: "activity-dashboard/cxo-dashboard",
      formId: 266,
      isVue3: true,
    },
  ],

  coreHrOrgStructureForms: [
    {
      formName: "employee-type",
      displayFormName: "Employee Type",
      url: "employee-type",
      isVue3: true,
    },
    {
      formName: "grades",
      displayFormName: "Grades",
      url: "grades",
      isVue3: true,
    },
    {
      formName: "locations",
      displayFormName: "Locations",
      url: "locations",
      isVue3: true,
    },
    {
      formName: "department-hierarchy",
      displayFormName: "Department Hierarchy",
      url: "department-hierarchy",
      isVue3: true,
    },
    {
      formName: "designations/positions",
      displayFormName: "Designations/Positions",
      url: "designations",
      isVue3: false,
    },
    {
      formName: "job-roles",
      displayFormName: "Job Roles",
      url: "job-roles",
      isVue3: true,
    },
    {
      formName: "work-schedule",
      displayFormName: "Work Schedule",
      url: "work-schedule",
      isVue3: false,
    },
    {
      formName: "business-unit-/-cost-center",
      displayFormName: "Business Unit / Cost Center",
      url: "business-unit",
      isVue3: true,
    },
    {
      formName: "organization-group",
      displayFormName: "Organization Group",
      url: "organization-group",
      isVue3: true,
    },
  ],
  coreHrPayrollDataManagementForms: [
    {
      formName: "salary info",
      formId: 207,
      displayFormName: "Salary Info",
      url: "payroll-data-management/salary-info",
      isVue3: true,
    },
    {
      formName: "air-ticketing-policy",
      formId: 319,
      displayFormName: "Air Ticketing Policy",
      url: "air-ticketing-policy",
      isVue3: true,
    },
    {
      formName: "air-ticket-settlement-summary",
      formId: 321,
      displayFormName: "Air Ticket Settlement Summary",
      url: "air-ticket-settlement-summary",
      isVue3: true,
    },
    {
      formName: "travel-and-expenses",
      formId: 355,
      displayFormName: "Travel and Expenses",
      url: "travel-and-expense",
      isVue3: true,
    },
    {
      formName: "reports",
      formId: 362,
      displayFormName: "Reports",
      url: "reports",
      isVue3: true,
    },
  ],
  coreHrSettingsForms: [
    {
      formName: "leave",
      displayFormName: "Time Off",
      url: "time-off",
      isVue3: false,
      subForms: ["short-time-off-(permission)", "short-time-off-(on-duty)"],
    },
    {
      formName: "comp-off",
      displayFormName: "Comp Off",
      url: "comp-off",
      isVue3: true,
    },
    {
      formName: "special-wages",
      displayFormName: "Special Wages",
      url: "special-wages",
      isVue3: true,
    },
    {
      formName: "employee-number-series",
      displayFormName: "Employee Number Series",
      url: "employee-number-series",
      isVue3: true,
    },
    {
      formName: "attendance-configuration",
      displayFormName: "Attendance Configuration",
      url: "attendance-configuration",
      isVue3: true,
    },
    {
      formName: "geo-fencing-&-selfie-attendance",
      displayFormName: "Geo-Fencing & Selfie Attendance",
      url: "geo-fencing-&-selfie-attendance",
      isVue3: false,
    },
    {
      formName: "holidays",
      displayFormName: "Holidays",
      url: "holidays",
      isVue3: false,
    },
    {
      formName: "pre-approvals",
      displayFormName: "Pre Approvals",
      url: "pre-approvals",
      isVue3: true,
    },
    {
      formName: "lop-recovery",
      formId: "253",
      displayFormName: "LOP Recovery",
      url: "lop-recovery",
      isVue3: true,
    },
    {
      formName: "roster",
      displayFormName: "Roster",
      url: "roster",
      isVue3: true,
    },
    {
      formName: "super-admin",
      formId: "147",
      displayFormName: "Roles",
      url: "roles",
      isVue3: true,
      accessType: "optionalChoice",
    },
  ],
  dataLossPreventionForm: [
    {
      formName: "dataLossPrevention",
      displayFormName: "settings.dataLossPrevention",
      formId: 230,
      url: "data-loss-prevention",
      isVue3: false,
    },
    {
      formName: "internetAccessControl",
      displayFormName: "settings.internetAccessControl",
      formId: 265,
      url: "data-loss-prevention/internet-access-control",
      isVue3: true,
    },
    {
      formName: "locationTracking",
      displayFormName: "settings.locationTracking",
      formId: 284,
      url: "data-loss-prevention/location-tracking",
      isVue3: true,
    },
    {
      formName: "keyLogging",
      displayFormName: "settings.keyLogging",
      formId: 299,
      url: "data-loss-prevention/key-logging",
      isVue3: true,
    },
    {
      formName: "additionalScreenshots",
      displayFormName: "settings.additionalScreenshots",
      formId: 300,
      url: "data-loss-prevention/additional-screenshots",
      isVue3: true,
    },
  ],
  rosterManagementForm: [
    {
      formName: "shift-scheduling",
      displayFormName: "Shift Scheduling",
      url: "shift-scheduling",
      isVue3: false,
    },
    {
      formName: "shift-rotation",
      displayFormName: "Shift Rotation",
      url: "shift-rotation",
      isVue3: true,
    },
  ],
  PayrollReconciliationForm: [
    {
      formName: "payroll-reconciliation",
      displayFormName: "Payroll Reconciliation",
      url: "payroll-reconciliation",
      isVue3: true,
    },
  ],
  shiftSwapForm: [
    {
      formName: "shift-swap",
      displayFormName: "Shift Swap",
      url: "shift-swap",
      isVue3: true,
    },
    {
      formName: "approvals",
      displayFormName: "Approvals",
      url: "shift-swap-approvals",
      isVue3: true,
    },
  ],
  selfServiveTimeOffForms: [
    {
      formName: "leave-request",
      displayFormName: "Leave",
      url: "leave-request",
      isVue3: true,
      formId: 333,
    },
    {
      formName: "compensatory-off",
      displayFormName: "Compensatory Off",
      url: "compensatory-off",
      isVue3: true,
      formId: 335,
    },
    {
      formName: "short-time-off",
      displayFormName: "Short Time Off",
      url: "short-time-off",
      isVue3: true,
      formId: 353,
    },
  ],
  myTeamTimeOffForms: [
    {
      formName: "leave-request",
      displayFormName: "Leave",
      url: "leave-request",
      isVue3: true,
      formId: 332,
    },
    {
      formName: "compensatory-off",
      displayFormName: "Compensatory Off",
      url: "compensatory-off",
      isVue3: true,
      formId: 334,
    },
    {
      formName: "short-time-off",
      displayFormName: "Short Time Off",
      url: "short-time-off",
      isVue3: true,
      formId: 352,
    },
    {
      formName: "reports",
      displayFormName: "Reports",
      url: "reports",
      isVue3: true,
      formId: 348,
    },
  ],
  //Custom Form Fields
  customFormFields: [],
  currentApprovalCount: 0,
  // project label based on partnerId
  projectLabel: "Project",
  // Show Job Rounds Tab setting
  showJobRoundsTab: "No",
};
export default state;
