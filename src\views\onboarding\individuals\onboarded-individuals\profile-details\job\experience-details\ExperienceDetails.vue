<template>
  <div
    ref="experienceDetails"
    v-if="!typeOfHeading || typeOfHeading == 'experienceDetails'"
  >
    <div class="d-flex">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="primary"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Experience Details</span
        >
      </div>

      <span v-if="enableAdd" class="d-flex justify-end ml-auto">
        <v-btn
          color="primary"
          variant="text"
          @click="showAddEditExperienceForm = true"
        >
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
        >
      </span>
    </div>
    <v-dialog
      v-model="showAddEditExperienceForm"
      width="80%"
      style="height: 100% !important"
      @click:outside="closeAddEditForm()"
    >
      <AddEditExperienceDetails
        :experienceDetails="selectedExperienceDetails"
        :selectedCandidateId="selectedCandidateId"
        :selectedCandidateDOB="selectedCandidateDOB"
        :selectedEmpDoj="selectedEmpDoj"
        @close-experience-form="closeAddEditForm()"
        @refetch-experience-details="submitExperienceFormData()"
      >
      </AddEditExperienceDetails>
    </v-dialog>
    <div v-if="!isMobileView" class="d-flex">
      <v-slide-group
        class="pa-4"
        selected-class="bg-primary"
        prev-icon="fas fa-chevron-circle-left"
        next-icon="fas fa-chevron-circle-right"
        show-arrows
      >
        <v-slide-group-item>
          <ViewExperienceDetails
            :experienceDetails="experienceDetails"
            :formAccess="formAccess"
            @on-open-edit="openEditForm($event)"
            @on-delete="openWarningPopUp($event)"
          />
        </v-slide-group-item>
      </v-slide-group>
    </div>
    <div v-else>
      <div class="card-container">
        <ViewExperienceDetails
          :experienceDetails="experienceDetails"
          :formAccess="formAccess"
          @on-open-edit="openEditForm($event)"
          @on-delete="openWarningPopUp($event)"
        ></ViewExperienceDetails>
      </div>
    </div>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      confirmation-heading="Are you sure you want to delete this record ?"
      icon-name="fas fa-trash-alt"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDelate()"
    >
    </AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
// components
const AddEditExperienceDetails = defineAsyncComponent(() =>
  import("./AddEditExperienceDetails.vue")
);
const ViewExperienceDetails = defineAsyncComponent(() =>
  import("./ViewExperienceDetails.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "ExperienceDetails",
  components: {
    AddEditExperienceDetails,
    ViewExperienceDetails,
  },
  props: {
    experienceDetailsData: {
      type: Array,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-job-details"],
  data() {
    return {
      typeOfHeading: "",
      experienceDetails: [],
      selectedExperienceDeleteRecord: null,
      showAddEditExperienceForm: false,
      selectedExperienceDetails: {},
      openWarningModal: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    enableAdd() {
      return this.formAccess && this.formAccess.add;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.experienceDetailsData && this.experienceDetailsData.length > 0) {
      this.experienceDetails = this.experienceDetailsData;
    }
  },

  methods: {
    submitExperienceFormData() {
      this.closeAddEditForm();
      this.$emit("refetch-job-details");
    },

    closeAddEditForm() {
      mixpanel.track("Onboarded-candidate-exp-edit-closed");
      this.showAddEditExperienceForm = false;
      this.selectedExperienceDetails = {};
    },

    openEditForm(selectedItem) {
      mixpanel.track("Onboarded-candidate-exp-edit-opened");
      this.selectedExperienceDetails = selectedItem;
      this.showAddEditExperienceForm = true;
    },

    onDelate() {
      this.openWarningModal = false;
    },

    openWarningPopUp(selectedItem) {
      this.openWarningModal = true;
      this.selectedExperienceDeleteRecord = selectedItem;
    },

    //this method closes the delete confirmation popup
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedExperienceDeleteRecord = null;
    },
  },
};
</script>
