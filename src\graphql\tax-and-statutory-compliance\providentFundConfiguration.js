import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_PROVIDENT_FUND_CONFIGURATION = gql`
  query retrieveProvidentFundConfiguration {
    retrieveProvidentFundConfiguration {
      errorCode
      message
      pfConfigData
    }
  }
`;

// ===============
// Mutations
// ===============
export const UPDATE_PROVIDENT_FUND_CONFIGURATION = gql`
  mutation updateProvidentFundConfiguration(
    $EPF_Number: String
    $Employee_Contribution_Rate: String
    $Employer_Contribution_Rate: String
    $PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit: String
    $Employer_Contribution_Part_Of_CTC: String
    $Admin_Charge_Part_Of_CTC: String
    $Edli_Charge_Part_Of_CTC: String
    $Override_PF_Contribution_Rate_At_Employee_Level: String
    $Pro_Rate_Restricted_PF_Wage: String
    $Consider_All_Salary_Components_For_LOP: String
    $Updated_On: Date
    $Updated_By: Int
  ) {
    updateProvidentFundConfiguration(
      EPF_Number: $EPF_Number
      Employee_Contribution_Rate: $Employee_Contribution_Rate
      Employer_Contribution_Rate: $Employer_Contribution_Rate
      PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit: $PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit
      Employer_Contribution_Part_Of_CTC: $Employer_Contribution_Part_Of_CTC
      Admin_Charge_Part_Of_CTC: $Admin_Charge_Part_Of_CTC
      Edli_Charge_Part_Of_CTC: $Edli_Charge_Part_Of_CTC
      Override_PF_Contribution_Rate_At_Employee_Level: $Override_PF_Contribution_Rate_At_Employee_Level
      Pro_Rate_Restricted_PF_Wage: $Pro_Rate_Restricted_PF_Wage
      Consider_All_Salary_Components_For_LOP: $Consider_All_Salary_Components_For_LOP
      Updated_On: $Updated_On
      Updated_By: $Updated_By
    ) {
      errorCode
      message
    }
  }
`;
