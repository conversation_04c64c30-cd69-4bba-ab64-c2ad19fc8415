<template>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppAccessDenied v-else-if="isAccessDenied"></AppAccessDenied>
</template>

<script>
export default {
  name: "OrgStructureSetup",

  data() {
    return {
      isLoading: true,
      isAccessDenied: false,
    };
  },

  computed: {
    // returns baseurl of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  mounted() {
    let { isAnyOneFormHaveAccess, formAccess } =
      this.$store.getters.orgStructureFormAccess;
    if (isAnyOneFormHaveAccess) {
      for (let access in formAccess) {
        if (formAccess[access].havingAccess) {
          this.redirectToRelevantForm(formAccess[access]);
          break;
        }
      }
    } else {
      this.isAccessDenied = true;
      this.isLoading = false;
    }
  },

  methods: {
    redirectToRelevantForm(formData) {
      if (formData.isVue3) {
        this.$router.push("/core-hr/" + formData.url);
      } else {
        window.location.href = this.baseUrl + "in/core-hr/" + formData.url;
      }
    },
  },
};
</script>
