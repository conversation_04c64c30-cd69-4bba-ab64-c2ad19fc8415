<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div
        v-if="!showEmployeesList"
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold"> Comp Off</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-tooltip v-model="showToolTip" location="top">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="isFormDirty ? '' : props"
                  rounded="lg"
                  variant="elevated"
                  class="mb-2 primary"
                  type="submit"
                  @click="savePreApprovalDetails"
                  ><span class="px-2">Save</span></v-btn
                >
              </template>
              <div v-if="!isFormDirty">There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" class="mr-1"> fas fa-times </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="overflow: scroll"
        :style="
          showEmployeesList
            ? 'height: calc(100vh - 200px)'
            : 'height: calc(100vh - 240px)'
        "
      >
        <v-card-text>
          <v-form v-if="!showEmployeesList" ref="compOffForm">
            <v-row>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="['Monthly', 'Hourly']"
                  :itemSelected="Salary_Type"
                  label="Salary Type"
                  :is-auto-complete="true"
                  :isRequired="true"
                  @selected-item="onChangeIsFormDirty($event, 'Salary_Type')"
                  :rules="[required('Salary Type', Salary_Type)]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="[
                    'Extra Work Hours(Weekday)',
                    'Holiday',
                    'Mandatory',
                    'Work Schedule Holiday(Week Off)',
                  ]"
                  label="Work Day Type"
                  :disabledValue="activeWorkDayTypes"
                  :isRequired="true"
                  :itemSelected="Work_Day_Type"
                  @selected-item="onChangeIsFormDirty($event, 'Work_Day_Type')"
                  :is-auto-complete="true"
                  :rules="[required('Work Day Type', Work_Day_Type)]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="openCustomGroupDropDown"
              >
                <div class="d-flex">
                  <CustomSelect
                    :items="customGroupList"
                    label="Custom Group"
                    :isRequired="true"
                    :itemSelected="customGroupId"
                    @selected-item="
                      onChangeIsFormDirty($event, 'customGroupId')
                    "
                    item-title="Custom_Group_Name"
                    item-value="Custom_Group_Id"
                    :is-auto-complete="true"
                    :is-loading="customGroupLoading"
                    :rules="[required('Custom Group', customGroupId)]"
                    style="max-width: 300px"
                  >
                  </CustomSelect>
                  <v-btn
                    rounded="lg"
                    class="ml-2 mt-2 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="retrieveCustomGroups('Comp Off', 250)"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
                <div class="mb-1">
                  <v-btn
                    color="primary"
                    variant="text"
                    :href="baseUrl + 'in/core-hr/custom-employee-groups'"
                    target="_blank"
                  >
                    <v-icon size="14" class="mr-1">fas fa-plus</v-icon> Add
                    Custom Group
                  </v-btn>
                </div>
              </v-col>
              <v-col
                v-if="customGroupId"
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div v-if="isLoadingCard">
                  <v-skeleton-loader
                    type="list-item-two-line"
                    class="ml-n4 mt-n2"
                    width="80%"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >Employees - {{ empListInSelectedGroup.length }}</span
                  >
                  <div
                    v-if="empListInSelectedGroup.length === 0"
                    class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  >
                    <v-icon color="warning" size="25"
                      >fas fa-exclamation-triangle</v-icon
                    >
                    <span
                      v-if="errorInFetchEmployeesList"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                      >Something went wrong while fetching the employees list.
                      Please try again.
                      <a class="text-primary" @click="fetchCustomEmployeesList"
                        >Refresh
                      </a>
                    </span>
                    <span
                      v-else-if="isNoEmployees"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                    >
                      It seems like there are no employees associated with the
                      selected custom group. Please add some employees under the
                      selected group or try choosing an another group.</span
                    >
                  </div>
                  <div v-else class="d-flex align-center">
                    <AvatarOrderedList
                      v-if="empListInSelectedGroup.length > 0"
                      class="mt-2"
                      :ordered-list="empListInSelectedGroup"
                    ></AvatarOrderedList>
                    <v-btn
                      rounded
                      color="primary"
                      size="small"
                      class="mt-2"
                      @click="openCustomGroupEmpList()"
                    >
                      View All
                    </v-btn>
                  </div>
                </div>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="Work_Day_Type !== 'Extra Work Hours(Weekday)'"
              >
                <CustomSelect
                  :items="['Work Schedule Hours', 'Fixed Hours']"
                  label="Comp Off Threshold"
                  :isRequired="true"
                  :itemSelected="Comp_Off_Threshold"
                  @selected-item="
                    onChangeIsFormDirty($event, 'Comp_Off_Threshold')
                  "
                  :is-auto-complete="true"
                  :rules="[required('Comp Off Threshold', Comp_Off_Threshold)]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="
                  Comp_Off_Threshold == 'Fixed Hours' &&
                  Work_Day_Type !== 'Extra Work Hours(Weekday)'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Minimum regular hours for full day comp off
                  <span class="text-red">*</span>
                </p>
                <v-row>
                  <v-col cols="6">
                    <v-text-field
                      v-model="fullDayHoursCompOff"
                      type="number"
                      :min="0"
                      :max="23"
                      variant="solo"
                      active
                      :rules="[
                        numericRequiredValidation('Hours', fullDayHoursCompOff),
                        minMaxNumberValidation(
                          'This field',
                          parseInt(fullDayHoursCompOff),
                          0,
                          23
                        ),
                        numericValidation('Hours', fullDayHoursCompOff),
                      ]"
                      suffix="hour(s)"
                      @update:model-value="validateInput('fullDayHoursCompOff')"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      v-model="fullDayMinutesCompOff"
                      type="number"
                      :min="0"
                      :max="59"
                      variant="solo"
                      active
                      :rules="[
                        numericRequiredValidation(
                          'Minutes',
                          fullDayMinutesCompOff
                        ),
                        minMaxNumberValidation(
                          'Minimum Overtime Hours For Full Day Comp Off',
                          parseInt(fullDayMinutesCompOff),
                          fullDayHoursCompOff == 0 ? 2 : 0,
                          59
                        ),
                        !fullDayMinutesCompOff ||
                          numericValidation('minutes', fullDayMinutesCompOff),
                      ]"
                      suffix="min(s)"
                      @update:model-value="
                        validateInput('fullDayMinutesCompOff')
                      "
                    >
                    </v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="Work_Day_Type !== 'Extra Work Hours(Weekday)'"
              >
                <div class="d-flex">
                  <span class="v-label pr-3 pb-5"
                    >Allow Half Day Comp Off Credit</span
                  >
                  <v-switch
                    color="primary"
                    class="ml-2"
                    v-model="Allow_Half_Day_Comp_Off_Credit"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="
                      onChangeIsFormDirty(
                        $event,
                        'Allow_Half_Day_Comp_Off_Credit'
                      )
                    "
                  ></v-switch>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="
                  Allow_Half_Day_Comp_Off_Credit == 'Yes' &&
                  Comp_Off_Threshold == 'Fixed Hours'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Minimum regular hours for half a day comp off
                  <span class="text-red">*</span>
                </p>
                <v-row>
                  <v-col cols="6">
                    <v-text-field
                      v-model="halfDayHoursCompOff"
                      type="number"
                      :min="0"
                      variant="solo"
                      active
                      :rules="[
                        numericRequiredValidation('Hours', halfDayHoursCompOff),
                        minMaxNumberValidation(
                          'This field',
                          halfDayHoursCompOff,
                          0,
                          fullDayMinutesCompOff && fullDayMinutesCompOff != 0
                            ? fullDayHoursCompOff
                            : fullDayHoursCompOff - 1
                        ),
                        numericValidation('Hours', halfDayHoursCompOff),
                      ]"
                      suffix="hour(s)"
                      :disabled="!fullDayHoursCompOff && !fullDayMinutesCompOff"
                      @update:model-value="validateInput('halfDayHoursCompOff')"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      v-model="halfDayMinutesCompOff"
                      type="number"
                      :min="0"
                      :max="
                        fullDayMinutesCompOff ? fullDayMinutesCompOff - 1 : 59
                      "
                      variant="solo"
                      active
                      :rules="[
                        numericRequiredValidation(
                          'Minutes',
                          halfDayMinutesCompOff
                        ),
                        minMaxNumberValidation(
                          'This field',
                          halfDayMinutesCompOff,
                          0,
                          adjustedHalfDayMinutesCompOff
                        ),
                        !halfDayMinutesCompOff ||
                          numericValidation('minutes', halfDayMinutesCompOff),
                      ]"
                      :disabled="!fullDayHoursCompOff && !fullDayMinutesCompOff"
                      suffix="min(s)"
                      @update:model-value="
                        validateInput('halfDayMinutesCompOff')
                      "
                    >
                    </v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="[
                    'Same Payroll Month',
                    'Fixed Days',
                    'Calendar Year',
                    'Fiscal Year',
                  ]"
                  label="Comp Off Expiry Type"
                  :isRequired="true"
                  :itemSelected="Comp_Off_Expiry_Type"
                  @selected-item="
                    onChangeIsFormDirty($event, 'Comp_Off_Expiry_Type')
                  "
                  :is-auto-complete="true"
                  :rules="[
                    required('Comp Off Expiry Type', Comp_Off_Expiry_Type),
                  ]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="Comp_Off_Expiry_Type == 'Fixed Days'"
              >
                <v-text-field
                  v-model="Comp_Off_Expiry_Days"
                  type="number"
                  :min="1"
                  :max="10000"
                  variant="solo"
                  active
                  :rules="[
                    minMaxNumberValidation(
                      'Comp Off Expiry After',
                      parseInt(Comp_Off_Expiry_Days),
                      1,
                      10000
                    ),
                    numericRequiredValidation(
                      'Comp Off Expiry After',
                      Comp_Off_Expiry_Days
                    ),
                    numericValidation(
                      'Comp Off Expiry After',
                      Comp_Off_Expiry_Days
                    ),
                  ]"
                  suffix="day(s)"
                  style="max-width: 300px"
                  @update:model-value="validateInput('Comp_Off_Expiry_Days')"
                >
                  <template v-slot:label>
                    <span>Comp Off Expiry After</span>
                    <span class="ml-1" style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="[
                    'Full Day',
                    'Half Day',
                    'Both Full Day & Half Day',
                    'Not Applicable',
                  ]"
                  label="Additional Comp Off For Overtime Hours"
                  :isRequired="true"
                  :itemSelected="Comp_Off_Applicability_For_Overtime_Hours"
                  @selected-item="
                    onChangeIsFormDirty(
                      $event,
                      'Comp_Off_Applicability_For_Overtime_Hours'
                    )
                  "
                  :is-auto-complete="true"
                  :rules="[
                    required(
                      'Additional Comp Off For Overtime Hours',
                      Comp_Off_Applicability_For_Overtime_Hours
                    ),
                  ]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="
                  Comp_Off_Applicability_For_Overtime_Hours == 'Full Day' ||
                  (Comp_Off_Applicability_For_Overtime_Hours ==
                    'Both Full Day & Half Day' &&
                    isListEmpty)
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Minimum Overtime Hours For Full Day Comp Off
                  <span class="text-red">*</span>
                </p>
                <v-row>
                  <v-col cols="6">
                    <v-text-field
                      v-model="fullDayHours"
                      type="number"
                      :min="0"
                      :max="23"
                      variant="solo"
                      active
                      :rules="[
                        numericRequiredValidation('Hours', fullDayHours),
                        minMaxNumberValidation(
                          'This field',
                          parseInt(fullDayHours),
                          0,
                          23
                        ),
                        numericValidation('Hours', fullDayHours),
                      ]"
                      suffix="hour(s)"
                      @update:model-value="validateInput('fullDayHours')"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      v-model="fullDayMinutes"
                      type="number"
                      :min="0"
                      :max="59"
                      variant="solo"
                      active
                      :rules="[
                        numericRequiredValidation('Minutes', fullDayMinutes),
                        minMaxNumberValidation(
                          'Minimum Overtime Hours For Full Day Comp Off',
                          parseInt(fullDayMinutes),
                          Comp_Off_Applicability_For_Overtime_Hours ==
                            'Both Full Day & Half Day' && fullDayHours == 0
                            ? 2
                            : 0,
                          59
                        ),
                        !fullDayMinutes ||
                          numericValidation('minutes', fullDayMinutes),
                      ]"
                      suffix="min(s)"
                      @update:model-value="validateInput('fullDayMinutes')"
                    >
                    </v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="
                  Comp_Off_Applicability_For_Overtime_Hours == 'Half Day' ||
                  (Comp_Off_Applicability_For_Overtime_Hours ==
                    'Both Full Day & Half Day' &&
                    isListEmpty)
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Minimum Overtime Hours For Half Day Comp Off
                  <span class="text-red">*</span>
                </p>
                <v-row>
                  <v-col cols="6">
                    <v-text-field
                      v-model="halfDayHours"
                      type="number"
                      :min="0"
                      variant="solo"
                      active
                      :rules="
                        Comp_Off_Applicability_For_Overtime_Hours ==
                        'Both Full Day & Half Day'
                          ? [
                              numericRequiredValidation('Hours', halfDayHours),
                              minMaxNumberValidation(
                                'This field',
                                halfDayHours,
                                0,
                                fullDayMinutes && fullDayMinutes != 0
                                  ? fullDayHours
                                  : fullDayHours - 1
                              ),
                              numericValidation('Hours', halfDayHours),
                            ]
                          : [
                              numericRequiredValidation('Hours', halfDayHours),

                              minMaxNumberValidation(
                                'This field',
                                parseInt(halfDayHours),
                                0,
                                23
                              ),
                              numericValidation('Hours', halfDayHours),
                            ]
                      "
                      :disabled="
                        Comp_Off_Applicability_For_Overtime_Hours ==
                        'Both Full Day & Half Day'
                          ? !fullDayHours && !fullDayMinutes
                          : false
                      "
                      suffix="hour(s)"
                      @update:model-value="validateInput('halfDayHours')"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      v-model="halfDayMinutes"
                      type="number"
                      :min="0"
                      :max="fullDayMinutes ? fullDayMinutes - 1 : 59"
                      variant="solo"
                      active
                      :rules="
                        Comp_Off_Applicability_For_Overtime_Hours ==
                        'Both Full Day & Half Day'
                          ? [
                              numericRequiredValidation(
                                'Minutes',
                                halfDayMinutes
                              ),
                              minMaxNumberValidation(
                                'This field',
                                halfDayMinutes,
                                0,
                                adjustedHalfDayMinutes
                              ),
                              !halfDayMinutes ||
                                numericValidation('minutes', halfDayMinutes),
                            ]
                          : [
                              numericRequiredValidation(
                                'Minutes',
                                halfDayMinutes
                              ),
                              minMaxNumberValidation(
                                'This field',
                                halfDayMinutes,
                                0,
                                59
                              ),
                              !halfDayMinutes ||
                                numericValidation('minutes', halfDayMinutes),
                            ]
                      "
                      :disabled="
                        Comp_Off_Applicability_For_Overtime_Hours ==
                        'Both Full Day & Half Day'
                          ? !fullDayHours && !fullDayMinutes
                          : false
                      "
                      suffix="min(s)"
                      @update:model-value="validateInput('halfDayMinutes')"
                    >
                    </v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col
                cols="12"
                v-if="
                  Comp_Off_Applicability_For_Overtime_Hours ==
                    'Both Full Day & Half Day' && !isListEmpty
                "
              >
                <v-row>
                  <v-col
                    cols="12"
                    sm="6"
                    :md="isListEmpty ? 4 : 6"
                    class="px-md-6 pb-0 mb-2"
                  >
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Minimum Overtime Hours For Full Day Comp Off
                      <span class="text-red">*</span>
                    </p>
                    <v-row>
                      <v-col cols="6">
                        <v-text-field
                          v-model="fullDayHours"
                          type="number"
                          :min="0"
                          :max="23"
                          variant="solo"
                          active
                          :rules="[
                            numericRequiredValidation('Hours', fullDayHours),
                            minMaxNumberValidation(
                              'This field',
                              parseInt(fullDayHours),
                              0,
                              23
                            ),
                            numericValidation('Hours', fullDayHours),
                          ]"
                          suffix="hour(s)"
                          @update:model-value="validateInput('fullDayHours')"
                        >
                        </v-text-field>
                      </v-col>
                      <v-col cols="6">
                        <v-text-field
                          v-model="fullDayMinutes"
                          type="number"
                          :min="0"
                          :max="59"
                          variant="solo"
                          active
                          :rules="[
                            numericRequiredValidation(
                              'Minutes',
                              fullDayMinutes
                            ),
                            minMaxNumberValidation(
                              'Minimum Overtime Hours For Full Day Comp Off',
                              parseInt(fullDayMinutes),
                              fullDayHours == 0 ? 2 : 0,
                              59
                            ),
                            !fullDayMinutes ||
                              numericValidation('minutes', fullDayMinutes),
                          ]"
                          suffix="min(s)"
                          @update:model-value="validateInput('fullDayMinutes')"
                        >
                        </v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col
                    cols="12"
                    sm="6"
                    :md="isListEmpty ? 4 : 6"
                    class="px-md-6 pb-0 mb-2"
                  >
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Minimum Overtime Hours For Half Day Comp Off
                      <span class="text-red">*</span>
                    </p>
                    <v-row>
                      <v-col cols="6">
                        <v-text-field
                          v-model="halfDayHours"
                          type="number"
                          :min="0"
                          variant="solo"
                          active
                          :rules="[
                            numericRequiredValidation('Hours', halfDayHours),
                            minMaxNumberValidation(
                              'This field',
                              halfDayHours,
                              0,
                              fullDayMinutes && fullDayMinutes != 0
                                ? fullDayHours
                                : fullDayHours - 1
                            ),
                            numericValidation('Hours', halfDayHours),
                          ]"
                          suffix="hour(s)"
                          :disabled="!fullDayHours && !fullDayMinutes"
                          @update:model-value="validateInput('halfDayHours')"
                        >
                        </v-text-field>
                      </v-col>
                      <v-col cols="6">
                        <v-text-field
                          v-model="halfDayMinutes"
                          type="number"
                          :min="0"
                          :max="fullDayMinutes ? fullDayMinutes - 1 : 59"
                          variant="solo"
                          active
                          :rules="[
                            numericRequiredValidation(
                              'Minutes',
                              halfDayMinutes
                            ),
                            minMaxNumberValidation(
                              'This field',
                              halfDayMinutes,
                              0,
                              adjustedHalfDayMinutes
                            ),
                            !halfDayMinutes ||
                              numericValidation('minutes', halfDayMinutes),
                          ]"
                          :disabled="!fullDayHours && !fullDayMinutes"
                          suffix="min(s)"
                          @update:model-value="validateInput('halfDayMinutes')"
                        >
                        </v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="
                  Comp_Off_Applicability_For_Overtime_Hours &&
                  Comp_Off_Applicability_For_Overtime_Hours !== 'Not Applicable'
                "
              >
                <div class="v-label ml-2 mb-1">Comp Off Balance Accrual</div>
                <div>
                  <v-tooltip
                    v-model="toolTipForDisabledMessage"
                    location="right"
                  >
                    <template v-slot:activator="{ props }">
                      <v-btn-toggle
                        v-model="selectedBalanceAccrual"
                        rounded="xl"
                        class="custom-box-shadow"
                        :class="overtimeFlag == 1 ? 'cursor-not-allow' : ''"
                        mandatory
                        density="comfortable"
                        v-bind="overtimeFlag == 1 ? props : ''"
                        :disabled="overtimeFlag == 1"
                        @update:modelValue="
                          onChangeOvertimeEligibility(selectedBalanceAccrual)
                        "
                      >
                        <v-tooltip
                          v-model="showAutomaticToolTip"
                          location="top"
                        >
                          <template v-slot:activator="{ props }">
                            <v-btn
                              v-bind="props"
                              class="text-start text-wrap"
                              color="primary"
                              >Automatic</v-btn
                            >
                          </template>
                          <div
                            style="
                              width: 150px !important;
                              height: 100px !important;
                            "
                          >
                            Comp Off balance is awarded upon attendance approval
                            automatically.
                          </div>
                        </v-tooltip>
                        <v-tooltip v-model="showManualToolTip" location="top">
                          <template v-slot:activator="{ props }">
                            <v-btn
                              v-bind="props"
                              class="text-start text-wrap"
                              color="primary"
                              >Manual</v-btn
                            >
                          </template>
                          <div
                            style="
                              width: 150px !important;
                              height: 100px !important;
                            "
                          >
                            Comp Off balance is allocated via an additional wage
                            claim.
                          </div>
                        </v-tooltip>
                      </v-btn-toggle>
                    </template>
                    <div
                      v-if="overtimeFlag == 1"
                      style="width: 150px !important; height: 150px !important"
                    >
                      Manual comp off accrual is not enabled at the organization
                      level. Kindly reach out to the HR administrator for
                      further assistance.
                    </div>
                  </v-tooltip>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex">
                  <span class="v-label pr-3 pb-5">Comp Off Encashment</span>
                  <v-switch
                    color="primary"
                    class="ml-2"
                    v-model="Comp_Off_Encashment"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="compOffEncashmentChange($event)"
                  ></v-switch>
                </div>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="Comp_Off_Encashment == 'Yes'"
              >
                <CustomSelect
                  :items="['Auto']"
                  label="Encashment Mode"
                  :isRequired="true"
                  :itemSelected="Encashment_Mode"
                  @selected-item="
                    onChangeIsFormDirty($event, 'Encashment_Mode')
                  "
                  :is-auto-complete="true"
                  :rules="[required('Encashment Mode', Encashment_Mode)]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <!-- Kept for future use -->
              <!-- <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex">
                  <CustomSelect
                    :items="workflowApprovalList"
                    label="Workflow Approval"
                    :isRequired="true"
                    :is-loading="workflowApprovalLoading"
                    :itemSelected="Workflow_Approval"
                    :is-auto-complete="true"
                    :disabled="true"
                    @selected-item="onChangeIsFormDirty($event, 'workflowName')"
                    style="max-width: 300px"
                    listWidth="max-width: 300px !important"
                  ></CustomSelect>
                   kept code for future use
              <v-btn
                    color="white"
                    rounded
                    class="ml-2 mt-2"
                    :size="isMobileView ? 'small' : 'default'"
                    :disabled="true"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
              </div>
              <div class="mb-1">
                  <v-btn
                    color="primary"
                    variant="text"
                    :href="baseUrl + 'workflow/workflow-builder'"
                    target="_blank"
                    :disabled="true"
                  >
                    <v-icon size="14" class="mr-1">fas fa-plus</v-icon> Add
                    Workflow
                  </v-btn>
                </div>
              </v-col> -->

              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="isEdit"
              >
                <div class="v-label ml-2 mb-2">Status</div>
                <AppToggleButton
                  button-active-text="Active"
                  button-inactive-text="Inactive"
                  button-active-color="#7de272"
                  button-inactive-color="red"
                  id-value="comp-off-app-toggle"
                  :current-value="Status === 'Active' ? true : false"
                  @chosen-value="onChangeStatus($event)"
                ></AppToggleButton>
              </v-col>
            </v-row>
          </v-form>

          <div v-if="showEmployeesList">
            <div class="d-flex" :class="isMobileView ? 'mb-2' : 'mt-n8 mb-2'">
              <v-btn rounded color="primary" @click="showEmployeesList = false">
                <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
                Back
              </v-btn>
            </div>
            <EmployeeListCard
              v-if="showEmployeesList"
              :show-modal="showEmployeesList"
              modal-title="Custom Group Employee(s)"
              :employeesList="empListForComponent"
              :selectable="false"
              :showFilter="false"
              :showFilterSearch="true"
              :isApplyFilter="true"
              @close-modal="showEmployeesList = false"
            ></EmployeeListCard>
          </div>
        </v-card-text>
      </div>
    </v-card>

    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    />
    <AppWarningModal
      v-if="warningPopupModel"
      :open-modal="warningPopupModel"
      icon-name="fas fa-exclamation-triangle"
      icon-Size="50"
      confirmationHeading=""
      @close-warning-modal="warningPopupModel = false"
      @accept-modal="addUpdateCompOff()"
    >
      <template v-slot:warningModalContent>
        <div class="text-primary text-left">
          <p>
            The new compensatory off policy will take effect starting today and
            will not apply to any attendance data that has already been
            processed. The policy will only be applicable to unprocessed
            attendance data or if any existing attendance data for past dates is
            deleted and reprocessed.
          </p>
          <div class="font-weight-bold">
            Are you sure to proceed with the changes?
          </div>
        </div>
      </template>
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 primary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_UPDATE_COMP_OFF_RULES,
  GET_OT_CONFIGURATION_DETAILS,
} from "@/graphql/settings/core-hr/compOffQueries";
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import EmployeeListCard from "@/components/helper-components/EmployeeListCard.vue";

export default {
  name: "AddEditCompOff",
  mixins: [validationRules],
  emits: ["close-edit-form"],
  components: {
    CustomSelect,
    AvatarOrderedList,
    EmployeeListCard,
  },
  props: {
    editedCompOffDetails: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isListEmpty: {
      type: Boolean,
      default: false,
    },
    coverage: {
      type: String,
      required: true,
    },
    compOffData: {
      type: Array,
      required: false,
    },
  },
  data() {
    return {
      Salary_Type: "Monthly",
      Work_Day_Type: "",
      Comp_Off_Threshold: "Work Schedule Hours",
      Fixed_Regular_Hours: null,
      Allow_Half_Day_Comp_Off_Credit: "No",
      Comp_Off_Expiry_Type: "Same Payroll Month",
      Comp_Off_Expiry_Days: null,
      Comp_Off_Applicability_For_Overtime_Hours: null,
      Status: "Active",
      Comp_Off_Encashment: "No",
      Encashment_Mode: "Auto",
      fullDayHours: null,
      fullDayMinutes: null,
      halfDayHours: null,
      halfDayMinutes: null,
      isMounted: false,
      isLoadingDetails: false,
      openConfirmationPopup: false,
      workflowName: "",
      Workflow_Approval: 0,
      customGroupRetrieved: null,
      workflowApprovalList: [],
      workflowApprovalLoading: false,
      isFormDirty: false,
      showToolTip: false,
      customGroupId: null,
      customGroupList: [],
      validationMessages: [],
      showValidationAlert: false,
      Comp_Off_Balance_Approval: "Automatic",
      selectedBalanceAccrual: 0,
      showAutomaticToolTip: false,
      showManualToolTip: false,
      empListForComponent: [],
      empListInSelectedGroup: [],
      showEmployeesList: false,
      errorInFetchEmployeesList: false,
      isNoEmployees: false,
      isLoadingCard: false,
      overtimeFlag: 0,
      listLoading: false,
      toolTipForDisabledMessage: false,
      fullDayHoursCompOff: null,
      fullDayMinutesCompOff: null,
      halfDayHoursCompOff: null,
      halfDayMinutesCompOff: null,
      warningPopupModel: false,
    };
  },
  watch: {
    customGroupId(val) {
      if (!val) {
        this.empListInSelectedGroup = [];
      } else {
        this.fetchCustomEmployeesList();
      }
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    openCustomGroupDropDown() {
      if (this.isEdit) {
        return this.customGroupRetrieved !== null ? true : false;
      } else {
        return this.coverage === "Custom Group" ? true : false;
      }
    },

    adjustedHalfDayMinutes() {
      if (this.fullDayHours) {
        if (this.fullDayHours > this.halfDayHours) {
          return 59;
        } else {
          return this.fullDayMinutes ? this.fullDayMinutes - 1 : 59;
        }
      } else {
        return this.fullDayMinutes ? this.fullDayMinutes - 1 : null;
      }
    },
    adjustedHalfDayMinutesCompOff() {
      if (this.fullDayHoursCompOff) {
        if (this.fullDayHoursCompOff > this.halfDayHoursCompOff) {
          return 59;
        } else {
          return this.fullDayMinutesCompOff
            ? this.fullDayMinutesCompOff - 1
            : 59;
        }
      } else {
        return this.fullDayMinutesCompOff
          ? this.fullDayMinutesCompOff - 1
          : null;
      }
    },
    activeWorkDayTypes() {
      const monthlyActiveWorkDayTypes = [];
      const hourlyActiveWorkDayTypes = [];
      if (this.compOffData.length) {
        this.compOffData.forEach((item) => {
          if (
            item.Custom_Group_Id === null &&
            item.Status === "Active" &&
            item.Salary_Type === "Monthly"
          ) {
            monthlyActiveWorkDayTypes.push(item.Work_Day_Type);
          } else if (
            item.Custom_Group_Id === null &&
            item.Status === "Active" &&
            item.Salary_Type === "Hourly"
          ) {
            hourlyActiveWorkDayTypes.push(item.Work_Day_Type);
          }
        });
      }

      if (this.Salary_Type == "Hourly") {
        return hourlyActiveWorkDayTypes;
      } else {
        return monthlyActiveWorkDayTypes;
      }
    },
  },
  mounted() {
    if (this.isEdit) {
      const {
        Configuration_Id,
        Custom_Group_Id,
        Salary_Type,
        Work_Day_Type,
        Comp_Off_Threshold,
        Comp_Off_Expiry_Type,
        Comp_Off_Applicability_For_Overtime_Hours,
        Status,
        Allow_Half_Day_Comp_Off_Credit,
        Comp_Off_Expiry_Days,
        Fixed_Regular_Hours,
        Minimum_Hours_For_Half_Day_Comp_Off,
        Comp_Off_Encashment,
        Encashment_Mode,
        Minimum_OT_Hours_For_Full_Day_Comp_Off,
        Minimum_OT_Hours_For_Half_Day_Comp_Off,
        Workflow_Approval,
        Comp_Off_Balance_Approval,
      } = this.editedCompOffDetails;
      this.Configuration_Id = Configuration_Id ? Configuration_Id : 0;
      this.customGroupRetrieved = Custom_Group_Id ? Custom_Group_Id : null;

      this.Salary_Type = Salary_Type ? Salary_Type : "Monthly";
      this.Work_Day_Type = Work_Day_Type ? Work_Day_Type : "Holiday";
      this.Comp_Off_Threshold = Comp_Off_Threshold
        ? Comp_Off_Threshold
        : "Work Schedule Hours";
      this.Comp_Off_Expiry_Type = Comp_Off_Expiry_Type
        ? Comp_Off_Expiry_Type
        : "Same Payroll Month";
      this.Comp_Off_Applicability_For_Overtime_Hours =
        Comp_Off_Applicability_For_Overtime_Hours
          ? Comp_Off_Applicability_For_Overtime_Hours
          : null;
      this.Status = Status ? Status : "Active";
      this.Allow_Half_Day_Comp_Off_Credit = Allow_Half_Day_Comp_Off_Credit
        ? Allow_Half_Day_Comp_Off_Credit
        : "No";
      this.Comp_Off_Expiry_Days = Comp_Off_Expiry_Days
        ? Comp_Off_Expiry_Days
        : null;
      this.Fixed_Regular_Hours = Fixed_Regular_Hours
        ? JSON.parse(Fixed_Regular_Hours)
        : null;
      this.fullDayHoursCompOff = Math.floor(Fixed_Regular_Hours);
      this.fullDayMinutesCompOff = Math.round(
        (Fixed_Regular_Hours - this.fullDayHoursCompOff) * 60
      );
      this.halfDayHoursCompOff = Math.floor(
        Minimum_Hours_For_Half_Day_Comp_Off
      );
      this.halfDayMinutesCompOff = Math.round(
        (Minimum_Hours_For_Half_Day_Comp_Off - this.halfDayHoursCompOff) * 60
      );
      this.Comp_Off_Encashment = Comp_Off_Encashment
        ? Comp_Off_Encashment
        : "No";
      this.Encashment_Mode = Encashment_Mode ? Encashment_Mode : "Auto";
      this.Minimum_OT_Hours_For_Full_Day_Comp_Off =
        Minimum_OT_Hours_For_Full_Day_Comp_Off
          ? Minimum_OT_Hours_For_Full_Day_Comp_Off
          : null;
      this.fullDayHours = Math.floor(Minimum_OT_Hours_For_Full_Day_Comp_Off);
      this.fullDayMinutes = Math.round(
        (Minimum_OT_Hours_For_Full_Day_Comp_Off - this.fullDayHours) * 60
      );
      this.Minimum_OT_Hours_For_Half_Day_Comp_Off =
        Minimum_OT_Hours_For_Half_Day_Comp_Off
          ? Minimum_OT_Hours_For_Half_Day_Comp_Off
          : null;
      this.halfDayHours = Math.floor(Minimum_OT_Hours_For_Half_Day_Comp_Off);
      this.halfDayMinutes = Math.round(
        (Minimum_OT_Hours_For_Half_Day_Comp_Off - this.halfDayHours) * 60
      );
      this.Comp_Off_Balance_Approval = Comp_Off_Balance_Approval
        ? Comp_Off_Balance_Approval
        : "Automatic";
      if (this.Comp_Off_Balance_Approval == "Automatic") {
        this.selectedBalanceAccrual = 0;
      } else {
        this.selectedBalanceAccrual = 1;
      }
      this.Workflow_Approval = Workflow_Approval ? Workflow_Approval : 0;

      this.retrieveCustomGroups("Comp Off", "edit");
      this.fetchCustomEmployeesList();
    }
    this.retrieveCustomGroups("Comp Off");
    this.getOTConfigurationDetails();
    this.isFormDirty = false;
    this.isMounted = true;
  },
  methods: {
    // open employees list to view the employees when the coverage is custom-group
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    // on changing the custom group we need to fetch the employees list relevant to the selected group
    async fetchCustomEmployeesList() {
      if (this.customGroupId) {
        let vm = this;
        vm.isLoadingCard = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(this.customGroupId),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty && this.isEdit) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
      this.showEmployeesList = false;
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      if (this.isEdit) {
        this.$emit("close-edit-form");
      } else {
        this.$emit("close-split-view");
      }
    },
    onChangeIsFormDirty(val, field) {
      if (field == "Salary_Type") {
        this.Salary_Type = val;
        this.Work_Day_Type = null;
        this.resetAllModelValues();
      } else if (field == "Work_Day_Type") {
        this.Work_Day_Type = val;
        this.resetAllModelValues();
      } else if (field == "Comp_Off_Threshold") {
        this.Comp_Off_Threshold = val;
        if (val == "Work Schedule Hours") {
          this.fullDayHoursCompOff = null;
          this.fullDayMinutesCompOff = null;
        }
      } else if (field == "Comp_Off_Expiry_Type") {
        this.Comp_Off_Expiry_Type = val;
        if (val == "Same Payroll Month") {
          this.Comp_Off_Expiry_Days = null;
        }
      } else if (field == "Comp_Off_Applicability_For_Overtime_Hours") {
        this.Comp_Off_Applicability_For_Overtime_Hours = val;
        this.fullDayHours = null;
        this.fullDayMinutes = null;
        this.halfDayHours = null;
        this.halfDayMinutes = null;
      } else if (field == "Encashment_Mode") {
        this.Encashment_Mode = val;
      } else if (field == "workflowName") {
        this.workflowName = val;
      } else if (field == "customGroupId") {
        this.customGroupId = val;
      } else if (field == "Allow_Half_Day_Comp_Off_Credit") {
        this.halfDayHoursCompOff = null;
        this.halfDayMinutesCompOff = null;
      }
      this.isFormDirty = true;
    },
    resetAllModelValues() {
      this.Comp_Off_Threshold = "Work Schedule Hours";
      this.Comp_Off_Expiry_Type = "Same Payroll Month";
      this.Comp_Off_Applicability_For_Overtime_Hours = null;
      this.Allow_Half_Day_Comp_Off_Credit = "No";
      this.Comp_Off_Expiry_Days = null;

      this.Comp_Off_Encashment = "No";
      this.Encashment_Mode = "Auto";
      this.Comp_Off_Balance_Approval = "Automatic";
      this.selectedBalanceAccrual = 0;
      if (this.openCustomGroupDropDown) {
        this.customGroupId = null;
      }
      this.fullDayHours = null;
      this.fullDayMinutes = null;
      this.halfDayHours = null;
      this.halfDayMinutes = null;
      this.fullDayHoursCompOff = null;
      this.fullDayMinutesCompOff = null;
      this.halfDayHoursCompOff = null;
      this.halfDayMinutesCompOff = null;
    },
    compOffEncashmentChange() {
      this.isFormDirty = true;
    },
    async savePreApprovalDetails() {
      const { valid } = await this.$refs.compOffForm.validate();
      if (valid) {
        if (this.isEdit) {
          if (this.isFormDirty) {
            this.warningPopupModel = true;
            this.isFormDirty = false;
          } else {
            if (this.isEdit) {
              this.$emit("close-edit-form");
            } else {
              this.$emit("close-split-view");
            }
          }
        } else {
          if (this.isFormDirty) {
            this.addUpdateCompOff();
            this.isFormDirty = false;
          } else {
            if (this.isEdit) {
              this.$emit("close-edit-form");
            } else {
              this.$emit("close-split-view");
            }
          }
        }
      }
    },
    addUpdateCompOff() {
      // For full Day
      const fullDayHours = parseInt(this.fullDayHours);
      const fullDayMinutes = parseInt(this.fullDayMinutes);
      // Convert to decimal format with up to 2 decimal places
      const fullDayValue = (fullDayHours + fullDayMinutes / 60).toFixed(2);
      // For Half Day
      const halfDayHours = parseInt(this.halfDayHours);
      const halfDayMinutes = parseInt(this.halfDayMinutes);
      const halfDayValue = (halfDayHours + halfDayMinutes / 60).toFixed(2);
      // For Fixed Regular Hours Full Day
      const fullDayHoursCompOff = parseInt(this.fullDayHoursCompOff);
      const fullDayMinutesCompOff = parseInt(this.fullDayMinutesCompOff);
      const fullDayCompOffValue = (
        fullDayHoursCompOff +
        fullDayMinutesCompOff / 60
      ).toFixed(2);
      //  For Fixed Regular Hours Half Day
      const halfDayHoursCompOff = parseInt(this.halfDayHoursCompOff);
      const halfDayMinutesCompOff = parseInt(this.halfDayMinutesCompOff);
      const halfDayCompOffValue = (
        halfDayHoursCompOff +
        halfDayMinutesCompOff / 60
      ).toFixed(2);
      let vm = this;
      vm.isLoadingDetails = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_COMP_OFF_RULES,
            variables: {
              Configuration_Id: vm.Configuration_Id
                ? parseInt(vm.Configuration_Id)
                : 0,
              Salary_Type: vm.Salary_Type ? vm.Salary_Type : "Monthly",

              Work_Day_Type: vm.Work_Day_Type ? vm.Work_Day_Type : "Holiday",
              Comp_Off_Threshold:
                vm.Work_Day_Type !== "Extra Work Hours(Weekday)"
                  ? vm.Comp_Off_Threshold
                  : null,
              Comp_Off_Expiry_Type: vm.Comp_Off_Expiry_Type
                ? vm.Comp_Off_Expiry_Type
                : "Same Payroll Month",
              Comp_Off_Applicability_For_Overtime_Hours:
                vm.Comp_Off_Applicability_For_Overtime_Hours
                  ? vm.Comp_Off_Applicability_For_Overtime_Hours
                  : null,

              Allow_Half_Day_Comp_Off_Credit:
                vm.Work_Day_Type !== "Extra Work Hours(Weekday)"
                  ? vm.Allow_Half_Day_Comp_Off_Credit
                  : null,

              Comp_Off_Expiry_Days:
                vm.Comp_Off_Expiry_Type == "Fixed Days" &&
                vm.Comp_Off_Expiry_Days
                  ? parseInt(vm.Comp_Off_Expiry_Days)
                  : null,
              Fixed_Regular_Hours: parseFloat(fullDayCompOffValue)
                ? parseFloat(fullDayCompOffValue)
                : null,
              Minimum_Hours_For_Half_Day_Comp_Off: parseFloat(
                halfDayCompOffValue
              )
                ? parseFloat(halfDayCompOffValue)
                : 0,
              Comp_Off_Encashment: vm.Comp_Off_Encashment
                ? vm.Comp_Off_Encashment
                : "No",
              Encashment_Mode:
                vm.Comp_Off_Encashment == "Yes" ? vm.Encashment_Mode : null,
              Minimum_OT_Hours_For_Full_Day_Comp_Off: parseFloat(fullDayValue)
                ? parseFloat(fullDayValue)
                : null,
              Minimum_OT_Hours_For_Half_Day_Comp_Off: parseFloat(halfDayValue)
                ? parseFloat(halfDayValue)
                : null,
              Comp_Off_Balance_Approval:
                vm.Comp_Off_Applicability_For_Overtime_Hours !==
                "Not Applicable"
                  ? vm.Comp_Off_Balance_Approval
                  : null,
              Status: vm.Status === "Inactive" ? "Inactive" : "Active",
              Workflow_Approval: 0,
              CustomGroup_Id: vm.customGroupId ? vm.customGroupId : null,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoadingDetails = false;
            if (this.isEdit) {
              vm.$emit("save-edited-data");
              vm.$emit("close-split-view");
            } else {
              vm.$emit("add-data");
              vm.$emit("close-split-view");
            }
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.isLoadingDetails = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: "comp off configuration",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    getOTConfigurationDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_OT_CONFIGURATION_DETAILS,
          client: "apolloClientC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getOTConfigurationDetails) {
            vm.overtimeFlag =
              response.data.getOTConfigurationDetails.configurationData.overtimeSettings.Overtime_Part_Of_Payroll;
          } else {
            vm.handleListError();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "overtime configuration",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    onChangeStatus(value) {
      this.Status = value[1] ? "Active" : "Inactive";
      this.isFormDirty = true;
    },
    onChangeOvertimeEligibility(value) {
      if (value == 0) {
        this.Comp_Off_Balance_Approval = "Automatic";
      } else {
        this.Comp_Off_Balance_Approval = "Manual";
      }
      this.isFormDirty = true;
    },
    closeForm() {
      if (this.isEdit) {
        this.$emit("close-edit-form");
      } else {
        this.$emit("close-split-view");
      }
    },
    // retrieve custom group based on form name
    async retrieveCustomGroups(formName, action) {
      this.customGroupLoading = true;
      this.customGroupList = [];

      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: formName,
        })
        .then((groupList) => {
          if (groupList && groupList.length > 0) {
            this.customGroupList = groupList;
            if (action == "edit") {
              this.customGroupId = this.customGroupRetrieved;
            }
          }
          this.customGroupLoading = false;
        })
        .catch(() => {
          this.customGroupLoading = false;
        });
    },
  },
};
</script>
<style scoped>
.custom-box-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}
</style>
