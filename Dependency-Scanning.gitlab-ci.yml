# Read more about this feature here: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/
#
# Configure the scanning tool through the environment variables.
# List of the variables: https://gitlab.com/gitlab-org/security-products/dependency-scanning#settings
# How to set: https://docs.gitlab.com/ee/ci/yaml/#variables

dependency_scanning:
  stage: dependency_scanning
  image: docker:stable
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  allow_failure: true
  services:
    - docker:stable-dind
  script:
    - export DS_VERSION=${SP_VERSION:-$(echo "$CI_SERVER_VERSION" | sed 's/^\([0-9]*\)\.\([0-9]*\).*/\1-\2-stable/')}
    - |
      if ! docker info &>/dev/null; then
        if [ -z "$DOCKER_HOST" -a "$KUBERNETES_PORT" ]; then
          export DOCKER_HOST='tcp://localhost:2375'
        fi
      fi
    - | # this is required to avoid undesirable reset of Docker image ENV variables being set on build stage
      function propagate_env_vars() {
        CURRENT_ENV=$(printenv)

        for VAR_NAME; do
          echo $CURRENT_ENV | grep "${VAR_NAME}=" > /dev/null && echo "--env $VAR_NAME "
        done
      }
    - |
      docker run \
        $(propagate_env_vars \
          DS_ANALYZER_IMAGES \
          DS_ANALYZER_IMAGE_PREFIX \
          DS_ANALYZER_IMAGE_TAG \
          DS_DEFAULT_ANALYZERS \
          DS_EXCLUDED_PATHS \
          DEP_SCAN_DISABLE_REMOTE_CHECKS \
          DS_DOCKER_CLIENT_NEGOTIATION_TIMEOUT \
          DS_PULL_ANALYZER_IMAGE_TIMEOUT \
          DS_RUN_ANALYZER_TIMEOUT \
          DS_PYTHON_VERSION \
          DS_PIP_DEPENDENCY_PATH \
          PIP_INDEX_URL \
          PIP_EXTRA_INDEX_URL \
        ) \
        --volume "$PWD:/code" \
        --volume /var/run/docker.sock:/var/run/docker.sock \
        "registry.gitlab.com/gitlab-org/security-products/dependency-scanning:$DS_VERSION" /code
  artifacts:
    reports:
      dependency_scanning: gl-dependency-scanning-report.json
  dependencies: []
  only:
    refs:
      - branches
    variables:
      - $GITLAB_FEATURES =~ /\bdependency_scanning\b/
  except:
    variables:
      - $DEPENDENCY_SCANNING_DISABLED
