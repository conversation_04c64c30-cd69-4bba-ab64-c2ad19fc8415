import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_EMPLOYEE_NUMBER_SERIES = gql`
  query ListEmployeeIdPrefixSettings(
    $formId: Int!
    $serviceProviderId: Int
    $status: String
  ) {
    listEmployeeIdPrefixSettings(
      formId: $formId
      serviceProviderId: $serviceProviderId
      status: $status
    ) {
      errorCode
      message
      employeeIdPrefixSettings {
        empPrefixSettingId
        serviceProviderId
        serviceProviderName
        prefix
        suffix
        noOfDigits
        nextNumber
        status
        addedOn
        addedByName
        updatedOn
        updatedByName
      }
      config {
        empPrefixConfigId
        isEnabled
        configLevel
        addedOn
        addedByName
        updatedOn
        updatedByName
      }
    }
  }
`;
export const GET_MAX_EMPLOYEE_ID = gql`
  query retrieveMaxEmployeeId($serviceProviderId: Int) {
    retrieveMaxEmployeeId(serviceProviderId: $serviceProviderId) {
      errorCode
      message
      maxEmployeeId
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_EMPLOYEE_NUMBER_SERIES = gql`
  mutation addUpdateEmployeeIdPrefixSettings(
    $empPrefixSettingId: Int!
    $serviceProviderId: Int
    $prefix: String
    $suffix: String
    $noOfDigits: Int!
    $nextNumber: Int!
    $status: String!
    $formId: Int!
  ) {
    addUpdateEmployeeIdPrefixSettings(
      empPrefixSettingId: $empPrefixSettingId
      prefix: $prefix
      serviceProviderId: $serviceProviderId
      suffix: $suffix
      noOfDigits: $noOfDigits
      nextNumber: $nextNumber
      status: $status
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_GLOBAL_SETTING = gql`
  mutation UpdateEmployeeIdPrefixConfig(
    $empPrefixConfigId: Int!
    $isEnabled: Boolean!
    $configLevel: String!
    $formId: Int!
  ) {
    updateEmployeeIdPrefixConfig(
      empPrefixConfigId: $empPrefixConfigId
      isEnabled: $isEnabled
      configLevel: $configLevel
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;
