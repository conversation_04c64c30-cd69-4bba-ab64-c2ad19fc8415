<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>
    <v-container fluid class="cxo-dashboard-container">
      <v-window v-if="cxoFormAccess.view" v-model="currentTabItem">
        <v-window-item :value="currentTabItem">
          <ProfileCard class="mt-5">
            <FormTab :model-value="openedSubTab" :hide-slider="true">
              <v-tab
                v-for="tab in subTabItems"
                :key="tab.value"
                :value="tab.value"
                :disabled="tab.disable"
                @click="onChangeSubTabs(tab.value)"
              >
                <div
                  :class="[
                    isActiveSubTab(tab.value)
                      ? 'text-primary font-weight-bold'
                      : 'text-grey-darken-2 font-weight-bold',
                  ]"
                >
                  {{ tab.label }}
                  <div
                    v-if="isActiveSubTab(tab.value)"
                    class="mt-3 mb-n4"
                    style="border-bottom: 4px solid; width: 150px"
                  ></div>
                </div>
              </v-tab>
            </FormTab>
          </ProfileCard>
          <v-row>
            <v-col cols="12">
              <v-window v-model="openedSubTab" style="width: 100%">
                <v-window-item value="productivity">
                  <productivity-charts
                    v-if="openedSubTab == 'productivity'"
                  ></productivity-charts>
                </v-window-item>
                <v-window-item value="location">
                  <team-activity
                    v-if="openedSubTab == 'location'"
                  ></team-activity>
                </v-window-item>
              </v-window>
            </v-col>
          </v-row>
        </v-window-item>
      </v-window>
    </v-container>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const ProductivityCharts = defineAsyncComponent(() =>
  import("./ProductivityCharts.vue")
);
const TeamActivity = defineAsyncComponent(() => import("./TeamActivity.vue"));
export default {
  name: "CXODashboard",
  components: { ProductivityCharts, TeamActivity },
  data() {
    return {
      cxoDashboardFormId: "266",
      activityDashboardFormId: "211",
      currentTabItem: "tab-2",
      mainTabList: [],
      openedSubTab: "productivity",
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    cxoFormAccess() {
      let cxoFormAccessRights = this.accessRights(this.cxoDashboardFormId);
      if (
        cxoFormAccessRights &&
        cxoFormAccessRights.accessRights &&
        cxoFormAccessRights.accessRights["view"]
      ) {
        return cxoFormAccessRights.accessRights;
      } else return false;
    },
    activityDashboardFormAccess() {
      let activityDashboardRights = this.accessRights(
        this.activityDashboardFormId
      );
      if (
        activityDashboardRights &&
        activityDashboardRights.accessRights &&
        activityDashboardRights.accessRights["view"]
      ) {
        return activityDashboardRights.accessRights;
      } else return false;
    },
    insightsFormAccess() {
      let activityDashboardRights = this.accessRights(294);
      if (
        activityDashboardRights &&
        activityDashboardRights.accessRights &&
        activityDashboardRights.accessRights["view"]
      ) {
        return activityDashboardRights.accessRights;
      } else return false;
    },
    subTabItems() {
      let initialTabs = [
        {
          label: this.$t("productivityMonitoring.productivityInsights"),
          value: "productivity",
          disable: false,
        },
      ];
      if (this.insightsFormAccess.view) {
        initialTabs.push({
          label: this.$t("productivityMonitoring.locationInsights"),
          value: "location",
          disable: false,
        });
      }
      return initialTabs;
    },
  },
  mounted() {
    this.appendDashboardForms();
  },
  methods: {
    appendDashboardForms() {
      let dashboardForms = [];
      // for Admin/Manager (show both dashboard) only when privacy mode not enabled and should have access for activity-dashboard
      if (
        (this.activityDashboardFormAccess &&
          this.activityDashboardFormAccess.admin.toLowerCase() === "admin") ||
        this.activityDashboardFormAccess.isManager === 1
      ) {
        if (
          this.activityDashboardFormAccess &&
          this.activityDashboardFormAccess.view
        ) {
          dashboardForms.push(
            this.$t("productivityMonitoring.teamActivityDashboard"),
            this.$t("productivityMonitoring.myActivityDashboard")
          );
        }
        if (this.cxoFormAccess && this.cxoFormAccess.view) {
          dashboardForms.push(this.$t("productivityMonitoring.cxoDashboard"));
        }
      } else {
        if (
          this.activityDashboardFormAccess &&
          this.activityDashboardFormAccess.view
        ) {
          dashboardForms.push(
            this.$t("productivityMonitoring.myActivityDashboard")
          );
        }
        if (this.cxoFormAccess && this.cxoFormAccess.view) {
          dashboardForms.push(this.$t("productivityMonitoring.cxoDashboard"));
        }
        this.currentTabItem = "tab-1";
      }
      if (dashboardForms.length > 0) {
        this.mainTabList = dashboardForms;
      } else {
        this.mainTabList = [
          this.$t("productivityMonitoring.myActivityDashboard"),
        ];
      }
    },
    onTabChange(tabName) {
      if (tabName === this.$t("productivityMonitoring.cxoDashboard")) {
        this.currentTabItem = "tab-2";
      } else if (
        tabName == this.$t("productivityMonitoring.teamActivityDashboard")
      ) {
        window.location.href =
          this.$store.getters.baseUrl +
          "in/productivity-monitoring/activity-dashboard?form=team";
      } else if (
        tabName == this.$t("productivityMonitoring.myActivityDashboard")
      ) {
        window.location.href =
          this.$store.getters.baseUrl +
          "in/productivity-monitoring/activity-dashboard?form=member";
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
  },
};
</script>
<style>
.cxo-dashboard-container {
  padding: 4em 2em 0em 3em;
  margin-bottom: 1em;
}
@media screen and (max-width: 805px) {
  .cxo-dashboard-container {
    padding: 6em 1em 0em 1em;
  }
}
</style>
