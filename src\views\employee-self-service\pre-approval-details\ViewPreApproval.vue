<template>
  <div>
    <v-card v-if="!isEdit" class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="40" color="hover" variant="elevated">
            <i class="primary hr-workflow-approval-management text-h6"></i>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              <span v-if="!isMobileView">View</span>
              {{ labelList[465]?.Field_Alias }} Details
            </div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-btn
            @click="this.$emit('open-edit-form')"
            size="small"
            v-if="
              accessRights.update === 1 &&
              selectedItem.status !== 'Rejected' &&
              selectedItem.status !== 'Cancelled' &&
              selectedItem.status !== 'Cancel Applied' &&
              selectedItem.status !== 'Approved'
            "
            color="primary"
            variant="elevated"
            rounded="lg"
            >Edit</v-btn
          >
          <v-icon class="mx-1" color="primary" @click="closeSplitViewForm">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card>
        <div
          :style="
            isMobileView ? 'height: calc(100vh - 350px); overflow: scroll' : ''
          "
        >
          <v-card-text>
            <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
              <v-col
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Employee Id</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ selectedItem.userDefinedEmpId }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Employee Name</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ selectedItem.employeeName }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[464]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[464]?.Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ selectedItem.preApprovalType }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Start Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ formatDate(selectedItem.startDate).substring(0, 10) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">End Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ formatDate(selectedItem.endDate).substring(0, 10) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ selectedItem.duration }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                v-if="
                  selectedItem.period && selectedItem.duration === 'Half Day'
                "
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Period</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ selectedItem.period }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Total Days</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ selectedItem.totalDays }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Status</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ selectedItem.status }}
                </p>
              </v-col>
              <v-col
                v-if="selectedItem.fileName && selectedItem.fileName[0]"
                :cols="selectedItem.fileName ? 6 : 12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0 cursor-pointer"
                :class="isMobileView ? ' ml-4' : 'mb-2'"
              >
                <span class="text-blue-grey-darken-6">
                  <span
                    style="text-decoration: underline"
                    @click="retrieveExpDocuments(selectedItem.fileName)"
                    class="text-green"
                  >
                    View Document</span
                  >
                </span>
              </v-col>
              <v-col
                v-if="labelList[466]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols=""
                :class="isMobileView ? ' ml-4' : 'mb-2'"
                class="px-md-6 pb-0"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[466]?.Field_Alias }}
                </p>
                <p
                  class="text-subtitle-1 font-weight-regular"
                  :style="selectedItem.reason ? '' : 'margin-left: 15px'"
                >
                  {{ selectedItem.reason ? selectedItem.reason : "-" }}
                </p>
              </v-col>
              <v-col cols="12" class="px-md-6 pb-0 mb-2">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-card-text>
        </div>
      </v-card>
    </v-card>
    <div v-else>
      <AddEditPreApprovals
        :fromViewForm="fromViewForm"
        :selectedItem="selectedItem"
        @close-view-form="closeSplitViewForm"
        @close-edit-form="closeEditView"
        @save-edited-data="saveselectedItem"
        @handle-add-edit-response="handleAddEditResponse"
        @save-added-data="saveselectedItem"
        :isEdit="isEdit"
      />
    </div>
  </div>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="PreApproval Request"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import { defineAsyncComponent } from "vue";
const AddEditPreApprovals = defineAsyncComponent(() =>
  import("./AddEditPreApproval.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
export default {
  name: "ViewPreApprovals",
  components: {
    MoreDetails,
    AddEditPreApprovals,
    FilePreviewModal,
  },
  props: {
    preApprovalData: {
      type: Array,
      required: true,
    },
    selectedItem: {
      type: Object,
      required: true,
    },
    isEdit: {
      type: Boolean,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      fromViewForm: true,
      retrievedFileName: "",
      openModal: false,
    };
  },
  methods: {
    closeEditView() {
      this.saveselectedItem();
      this.$emit("close-edit-form");
    },
    saveselectedItem() {
      this.$emit("reload-view", this.selectedItem.preApprovalId);
    },
    retrieveExpDocuments(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
    updateVal() {
      this.$emit("refresh-list");
    },
    handleAddEditResponse() {
      this.saveselectedItem();
      this.updateVal();
      this.pushMoreDetails();
    },
    closeSplitViewForm() {
      this.$emit("close-split-view");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values

      if (this.selectedItem) {
        const addedOn = this.formatDate(
          new Date(this.selectedItem.addedOn + ".000Z")
        );

        const addedByName = this.selectedItem.addedByName;
        const updatedByName = this.selectedItem.updatedByName;
        const updatedOn = this.formatDate(
          new Date(this.selectedItem.updatedOn + ".000Z")
        );

        if (addedOn && addedByName) {
          this.moreDetailsList.push({
            actionDate: addedOn,
            actionBy: addedByName,
            text: "Added",
          });
        }
        if (updatedByName && updatedOn) {
          this.moreDetailsList.push({
            actionDate: updatedOn,
            actionBy: updatedByName,
            text: "Updated",
          });
        }
      }
    },
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler() {
        this.prefillMoreDetails();
      },
    },
  },
};
</script>
