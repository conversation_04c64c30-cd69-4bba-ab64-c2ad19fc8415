import gql from "graphql-tag";
// ===============
// Queries
// ===============

export const ADD_EDIT_NEW_POSITION_REQUEST = gql`
  mutation addUpdateNewPosition(
    $positionRequestId: Int
    $organizationStructureId: Int
    $originalPositionId: String
    $positionCode: String
    $positionTitle: String!
    $groupName: String
    $groupCode: String
    $divisionCode: String
    $divisionName: String
    $departmentCode: String
    $departmentName: String
    $sectionCode: String
    $sectionName: String
    $PositionLevel: Int
    $costCenter: String
    $employeeType: String
    $employeeTypeName: String
    $noOfPosition: Int!
    $licenseCertificate: String
    $workflowId: Int
    $status: String!
    $eventId: String
    $reasonForReplacement: String
    $groupId: String
    $divisionId: String
    $departmentId: String
    $sectionId: String
    $customGroupId: Int
  ) {
    addUpdateNewPosition(
      organizationStructureId: $organizationStructureId
      positionRequestId: $positionRequestId
      originalPositionId: $originalPositionId
      positionCode: $positionCode
      positionTitle: $positionTitle
      groupName: $groupName
      groupCode: $groupCode
      divisionCode: $divisionCode
      divisionName: $divisionName
      departmentCode: $departmentCode
      departmentName: $departmentName
      sectionCode: $sectionCode
      sectionName: $sectionName
      costCenter: $costCenter
      PositionLevel: $PositionLevel
      employeeType: $employeeType
      employeeTypeName: $employeeTypeName
      noOfPosition: $noOfPosition
      groupId: $groupId
      divisionId: $divisionId
      departmentId: $departmentId
      sectionId: $sectionId
      licenseCertificate: $licenseCertificate
      workflowId: $workflowId
      reasonForReplacement: $reasonForReplacement
      status: $status
      eventId: $eventId
      customGroupId: $customGroupId
    ) {
      errorCode
      message
      validationError
      positionRequestId
    }
  }
`;

export const ORG_STRUCTURE_BASED_ON_GROUP = gql`
  query listDetailsBasedOnGroupCode(
    $formId: Int!
    $postionParentId: String
    $offset: Int
    $limit: Int
  ) {
    listDetailsBasedOnGroupCode(
      formId: $formId
      postionParentId: $postionParentId
      offset: $offset
      limit: $limit
    ) {
      errorCode
      message
      totalRecords
      positionDetails {
        divisionList {
          Originalpos_Id
          Pos_Code
          Pos_Name
          Pos_full_Name
        }
        sectionList {
          Pos_Code
          Originalpos_Id
          Pos_Name
          Pos_full_Name
        }
        deptList {
          Originalpos_Id
          Pos_Code
          Pos_Name
          Pos_full_Name
        }
        costCodeList
      }
    }
  }
`;

export const EDUCATION_STATIC_DATA_LIST = gql`
  query retrieveEducationLabelList {
    retrieveEducationLabelList {
      errorCode
      message
      educationLabelList {
        Mpp_Education_Requirements_Id
        Education_Type
      }
    }
  }
`;

export const ADD_EDIT_DUTIES_RESPONSIBILITIES = gql`
  mutation addUpdateDutiesResponsibilities(
    $dutiesResponsibilityId: Int
    $positionRequestId: Int!
    $regularDuties: String!
    $noOfHoursPeriod: Float!
    $period: String!
    $competenciesRequired: String!
    $competency: String!
    $ratingOfCompetency: Float
    $status: String!
    $eventId: String
  ) {
    addUpdateDutiesResponsibilities(
      dutiesResponsibilityId: $dutiesResponsibilityId
      positionRequestId: $positionRequestId
      regularDuties: $regularDuties
      noOfHoursPeriod: $noOfHoursPeriod
      period: $period
      competenciesRequired: $competenciesRequired
      ratingOfCompetency: $ratingOfCompetency
      competency: $competency
      status: $status
      eventId: $eventId
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_EDIT_EXPERIENCES = gql`
  mutation addUpdateExperience(
    $experienceId: Int
    $typeOfJobs: String!
    $months: Int
    $years: Int
    $positionRequestId: Int!
    $status: String!
    $eventId: String
  ) {
    addUpdateExperience(
      experienceId: $experienceId
      typeOfJobs: $typeOfJobs
      months: $months
      years: $years
      positionRequestId: $positionRequestId
      status: $status
      eventId: $eventId
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_EDIT_WORKING_CONDITIONS = gql`
  mutation addUpdateWorkingConditions(
    $workingConditionId: Int
    $workingArea: String!
    $timeSpent: Int
    $positionRequestId: Int!
    $status: String!
    $eventId: String
  ) {
    addUpdateWorkingConditions(
      workingConditionId: $workingConditionId
      workingArea: $workingArea
      timeSpent: $timeSpent
      positionRequestId: $positionRequestId
      status: $status
      eventId: $eventId
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_EDIT_EXT_INT_NEW_POSITION = gql`
  mutation updateExtIntNewPosition(
    $positionRequestId: Int!
    $internalOperatingNetwork: [String]
    $externalOperatingNetwork: [String]
    $licenseCertificateDetails: String
    $licenseCertificate: String
    $comments: String
    $status: String!
    $eventId: String
  ) {
    updateExtIntNewPosition(
      positionRequestId: $positionRequestId
      internalOperatingNetwork: $internalOperatingNetwork
      externalOperatingNetwork: $externalOperatingNetwork
      licenseCertificateDetails: $licenseCertificateDetails
      licenseCertificate: $licenseCertificate
      comments: $comments
      status: $status
      eventId: $eventId
    ) {
      errorCode
      message
    }
  }
`;

export const RETRIEVE_NEW_POSITION_DETAILS = gql`
  query retrieveNewPositionDetails($positionRequestId: Int!) {
    retrieveNewPositionDetails(positionRequestId: $positionRequestId) {
      errorCode
      message
      openPositiontRequestRetrieveDetails {
        Position_Request_Id
        Original_Position_Id
        Organization_Structure_Id
        Position_Title
        Pos_Code
        Event_Id
        Employee_Type_Name
        Group_Name
        Division_Name
        Department_Name
        Section_Name
        Group_Code
        Division_Code
        Department_Code
        Section_Code
        Cost_Center
        Position_Level
        Position_Level_Id
        Reason_For_Replacement
        Employee_Type
        No_Of_Position
        Comments
        Workflow_Id
        Status
        License_Certificate
        License_Certificate_Details
        Custom_Group_Id
        CustomGroupName
        Added_By
        Added_On
        Updated_On
        Updated_By
        Internal_Operating_Network
        External_Operating_Network
        Experience {
          Experience_Id
          Type_Of_Jobs
          Months
          Years
        }
        WorkingConditions {
          Working_Condition_Id
          Working_Area
          Time_Spent
        }
        DutiesResponsibilities {
          Duties_Responsibility_Id
          Regular_Duties
          No_Of_Hours_Period
          Period
          Competencies_Required
          Competency
          Rating_Of_Competency
        }
        education {
          Description
          Education_Type
          Position_Request_Id
          Mpp_Education_Requirements_Descriptions_Id
        }
      }
    }
  }
`;

export const ADD_EDIT_EDUCATION = gql`
  mutation addUpdateEducationRequirementsDescriptions(
    $status: String!
    $eventId: String
    $action: String!
    $positionRequestId: Int!
    $input: [educationDescriptionsIndividual!]!
  ) {
    addUpdateEducationRequirementsDescriptions(
      status: $status
      eventId: $eventId
      action: $action
      positionRequestId: $positionRequestId
      input: $input
    ) {
      errorCode
      message
      __typename
    }
  }
`;

export const DELETE_JOB_ASSESSMENT = gql`
  mutation deleteOpenPositionSubTable(
    $deleteId: Int!
    $tableKeyword: String!
    $positionRequestId: Int
    $status: String!
    $eventId: String
  ) {
    deleteOpenPositionSubTable(
      deleteId: $deleteId
      tableKeyword: $tableKeyword
      positionRequestId: $positionRequestId
      status: $status
      eventId: $eventId
    ) {
      errorCode
      message
      validationError
    }
  }
`;

export const POSITION_LEVEL_LIST = gql`
  query retrievePositionLevel {
    retrievePositionLevel {
      errorCode
      message
      positionLevelList {
        Position_Level_Id
        Position_Level
      }
    }
  }
`;
