export default {
  en: {
    /* ----- Data Loss Prevention ----- */
    // Module names
    dataLossPrevention: "Data Loss Prevention",
    keyLogger: "Key Logger",
    myKeyLogs: "My Key Logs",
    myTeamKeyLogs: "My Team Key Logs",
    locationIntelligence: "Location Intelligence",
    internetAccessControl: "Internet Access Control",
    locationTracking: "Location Tracking",
    additionalScreenshots: "Additional Screenshots",

    // Section titles
    viewMyKeyLogs: "View My Key Logs",
    viewMyTeamKeyLogs: "View My Team Key Logs",
    viewLocationIntelligence: "View Location Intelligence",
    keyLoggerDashboard: "Key Logger Dashboard",
    locationIntelligenceDashboard: "Location Intelligence Dashboard",

    // Key Logger - Common
    date: "Date",
    workSchedule: "Work Schedule",
    timezone: "Timezone",
    selectManager: "Select Manager",
    back: "Back",
    change: "Change",
    all: "All",
    online: "Online",
    offline: "Offline",
    employeeIdShort: "E-Id",
    configureHere: "here",
    noKeyLogsAvailable:
      "Key logs are not available for this user, please configure",
    talkToAdmin:
      "Key logs are not available for this user, please talk to the DLP administrator",
    noEmployeesFound: "No employees found for the applied filter",
    noDataAvailable: "No data available",

    // Table headers
    employeeName: "Employee Name",
    employeeId: "Employee ID",
    department: "Department",
    keyLogDate: "Key Log Date",
    keyLogTime: "Key Log Time",
    keyStrokeCount: "Key Stroke Count",
    applicationName: "Application Name",
    applicationTitle: "Application Title",
    windowTitle: "Window Title",
    keyLogDuration: "Key Log Duration",
    keyLogs: "Key Logs",
    activityStartTime: "Activity Start Time",
    activityEndTime: "Activity End Time",
    location: "Location",
    ipAddress: "IP Address",
    deviceId: "Device ID",
    deviceName: "Device Name",
    lastSeen: "Last Seen",
    locationAccuracy: "Location Accuracy",
    locationTimestamp: "Location Timestamp",
    actions: "Actions",
    status: "Status",
    role: "Role",
    manager: "Manager",

    // Key Logger - Productivity
    productive: "Productive",
    unproductive: "Unproductive",
    neutral: "Neutral",
    userProductivity: "User Productivity",
    productiveTooltip:
      "Time spent on applications and websites classified as productive.",
    unproductiveTooltip:
      "Time spent on applications and websites classified as unproductive.",
    neutralTooltip:
      "Time spent on applications and websites which are not classified(Uncategorized).",
    userProductivityTooltip:
      "User productivity percentage on productive & neutral applications and websites.",
    unproductiveTime: "Unproductive Time",
    noRecordsMatch: "No records match your search",

    // Dashboard metrics
    totalKeyLogs: "Total Key Logs",
    averageKeyStrokes: "Average Key Strokes",
    topApplications: "Top Applications",
    keyLogTrend: "Key Log Trend",
    locationDistribution: "Location Distribution",
    deviceDistribution: "Device Distribution",
    totalEmployees: "Total Employees",
    activeEmployees: "Active Employees",

    // Location Intelligence specific
    workedDate: "Worked Date",
    customGroup: "Custom Group",
    systemUpTime: "System Up Time",
    activeTime: "Active Time",
    computerActivity: "Computer activity(Mouse and Keyboard)",
    productiveTime: "Productive Time(Apps and URL)",
    employeeData: "Employee Data",
    activeDays: "Active Days",
    daysOffice: "Days Office",
    daysRemote: "Days Remote",
    daysHybrid: "Days Remote/Office",
    inOffice: "in Office",
    inRemote: "in Remote",
    inHybrid: "in Office/Remote",
    locationDataEmpty:
      "There are no records for the selected filters/searches.",
    summary: "Summary",
    day: "day",

    // Filter labels
    dateRange: "Date Range",
    dateRangeSelector: "Select Date Range",
    applicationFilter: "Application",
    employeeFilter: "Employee",
    departmentFilter: "Department",
    locationFilter: "Location",
    deviceFilter: "Device",
    statusFilter: "Status",

    // Buttons and actions
    viewDetails: "View Details",
    export: "Export",
    resetFilterSearch: "Reset Filter/Search",
    refresh: "Refresh",
    close: "Close",
    cancel: "Cancel",
    configureKeyLogger: "Configure Key Logger",
    employees: "Employee(s)",

    // Data table
    itemsPerPage: "Items per page",
    itemsPerPageAll: "All",

    // Messages
    noKeyLogsFound: "No key logs found for the selected filters/searches.",
    locationDataNotFound:
      "No location data found for the selected filters/searches.",
    accessDeniedMessage: "You don't have access to perform this action.",
    dateRangeExceeded: "Please select a date range of less than 31 days",
    dataLossPreventionHelp:
      "Data Loss Prevention helps monitor and protect sensitive information.",
    keyLoggerHelp:
      "Key Logger tracks keyboard activity to monitor productivity and detect potential data leaks.",
    locationIntelligenceHelp:
      "Location Intelligence tracks employee locations to ensure compliance with work policies.",
  },
  fr: {
    /* ----- Data Loss Prevention ----- */
    // Module names
    dataLossPrevention: "Prévention de la perte de données",
    keyLogger: "Enregistreur de frappe",
    myKeyLogs: "Mes journaux de frappe",
    myTeamKeyLogs: "Journaux de frappe de mon équipe",
    locationIntelligence: "Intelligence de localisation",
    internetAccessControl: "Contrôle d'accès Internet",
    locationTracking: "Suivi de localisation",
    additionalScreenshots: "Captures d'écran supplémentaires",

    // Section titles
    viewMyKeyLogs: "Voir mes journaux de frappe",
    viewMyTeamKeyLogs: "Voir les journaux de frappe de mon équipe",
    viewLocationIntelligence: "Voir l'intelligence de localisation",
    keyLoggerDashboard: "Tableau de bord de l'enregistreur de frappe",
    locationIntelligenceDashboard:
      "Tableau de bord d'intelligence de localisation",

    // Key Logger - Common
    date: "Date",
    workSchedule: "Horaire de travail",
    timezone: "Fuseau horaire",
    selectManager: "Sélectionner le gestionnaire",
    back: "Retour",
    change: "Changer",
    all: "Tous",
    online: "En ligne",
    offline: "Hors ligne",
    employeeIdShort: "ID-E",
    configureHere: "ici",
    noKeyLogsAvailable:
      "Les journaux de frappe ne sont pas disponibles pour cet utilisateur, veuillez configurer",
    talkToAdmin:
      "Les journaux de frappe ne sont pas disponibles pour cet utilisateur, veuillez contacter l'administrateur DLP",
    noEmployeesFound: "Aucun employé trouvé pour le filtre appliqué",
    noDataAvailable: "Aucune donnée disponible",

    // Table headers
    employeeName: "Nom de l'employé",
    employeeId: "ID de l'employé",
    department: "Département",
    keyLogDate: "Date du journal de frappe",
    keyLogTime: "Heure du journal de frappe",
    keyStrokeCount: "Nombre de frappes",
    applicationName: "Nom de l'application",
    applicationTitle: "Titre de l'application",
    windowTitle: "Titre de la fenêtre",
    keyLogDuration: "Durée du journal de frappe",
    keyLogs: "Journaux de frappe",
    activityStartTime: "Heure de début d'activité",
    activityEndTime: "Heure de fin d'activité",
    location: "Emplacement",
    ipAddress: "Adresse IP",
    deviceId: "ID de l'appareil",
    deviceName: "Nom de l'appareil",
    lastSeen: "Dernière vue",
    locationAccuracy: "Précision de localisation",
    locationTimestamp: "Horodatage de localisation",
    actions: "Actions",
    status: "Statut",
    role: "Rôle",
    manager: "Gestionnaire",

    // Key Logger - Productivity
    productive: "Productif",
    unproductive: "Improductif",
    neutral: "Neutre",
    userProductivity: "Productivité de l'utilisateur",
    productiveTooltip:
      "Temps passé sur des applications et des sites web classés comme productifs.",
    unproductiveTooltip:
      "Temps passé sur des applications et des sites web classés comme improductifs.",
    neutralTooltip:
      "Temps passé sur des applications et des sites web non classés (non catégorisés).",
    userProductivityTooltip:
      "Pourcentage de productivité de l'utilisateur sur les applications et sites web productifs et neutres.",
    unproductiveTime: "Temps improductif",
    noRecordsMatch: "Aucun enregistrement ne correspond à votre recherche",

    // Dashboard metrics
    totalKeyLogs: "Total des journaux de frappe",
    averageKeyStrokes: "Frappes moyennes",
    topApplications: "Applications principales",
    keyLogTrend: "Tendance des journaux de frappe",
    locationDistribution: "Distribution des emplacements",
    deviceDistribution: "Distribution des appareils",
    totalEmployees: "Nombre total d'employés",
    activeEmployees: "Employés actifs",

    // Location Intelligence specific
    workedDate: "Date de travail",
    customGroup: "Groupe personnalisé",
    systemUpTime: "Temps de fonctionnement du système",
    activeTime: "Temps actif",
    computerActivity: "Activité informatique (souris et clavier)",
    productiveTime: "Temps productif (applications et URL)",
    employeeData: "Données des employés",
    activeDays: "Jours actifs",
    daysOffice: "Jours au bureau",
    daysRemote: "Jours à distance",
    daysHybrid: "Jours bureau/distance",
    inOffice: "au bureau",
    inRemote: "à distance",
    inHybrid: "au bureau/distance",
    locationDataEmpty:
      "Il n'y a pas d'enregistrements pour les filtres/recherches sélectionnés.",
    summary: "Résumé",
    day: "jour",

    // Filter labels
    dateRange: "Plage de dates",
    dateRangeSelector: "Sélectionner la plage de dates",
    applicationFilter: "Application",
    employeeFilter: "Employé",
    departmentFilter: "Département",
    locationFilter: "Emplacement",
    deviceFilter: "Appareil",
    statusFilter: "Statut",

    // Buttons and actions
    viewDetails: "Voir les détails",
    export: "Exporter",
    resetFilterSearch: "Réinitialiser filtre/recherche",
    refresh: "Actualiser",
    close: "Fermer",
    cancel: "Annuler",
    configureKeyLogger: "Configurer l'enregistreur de frappe",
    employees: "Employé(s)",

    // Data table
    itemsPerPage: "Articles par page",
    itemsPerPageAll: "Tous",

    // Messages
    noKeyLogsFound:
      "Aucun journal de frappe trouvé pour les filtres/recherches sélectionnés.",
    locationDataNotFound:
      "Aucune donnée de localisation trouvée pour les filtres/recherches sélectionnés.",
    accessDeniedMessage: "Vous n'avez pas accès pour effectuer cette action.",
    dateRangeExceeded:
      "Veuillez sélectionner une plage de dates inférieure à 31 jours",
    dataLossPreventionHelp:
      "La prévention de la perte de données aide à surveiller et à protéger les informations sensibles.",
    keyLoggerHelp:
      "L'enregistreur de frappe suit l'activité du clavier pour surveiller la productivité et détecter les fuites de données potentielles.",
    locationIntelligenceHelp:
      "L'intelligence de localisation suit les emplacements des employés pour assurer la conformité aux politiques de travail.",
  },
  ja: {
    /* ----- Data Loss Prevention ----- */
    // Module names
    dataLossPrevention: "データ損失防止",
    keyLogger: "キーロガー",
    myKeyLogs: "自分のキーログ",
    myTeamKeyLogs: "チームのキーログ",
    locationIntelligence: "位置情報インテリジェンス",
    internetAccessControl: "インターネットアクセス制御",
    locationTracking: "位置追跡",
    additionalScreenshots: "追加スクリーンショット",

    // Section titles
    viewMyKeyLogs: "自分のキーログを表示",
    viewMyTeamKeyLogs: "チームのキーログを表示",
    viewLocationIntelligence: "位置情報インテリジェンスを表示",
    keyLoggerDashboard: "キーロガーダッシュボード",
    locationIntelligenceDashboard: "位置情報インテリジェンスダッシュボード",

    // Key Logger - Common
    date: "日付",
    workSchedule: "勤務スケジュール",
    timezone: "タイムゾーン",
    selectManager: "管理者を選択",
    back: "戻る",
    change: "変更",
    all: "すべて",
    online: "オンライン",
    offline: "オフライン",
    employeeIdShort: "従業員ID",
    configureHere: "ここ",
    noKeyLogsAvailable:
      "このユーザーのキーログは利用できません。設定してください",
    talkToAdmin:
      "このユーザーのキーログは利用できません。DLP管理者に連絡してください",
    noEmployeesFound: "適用されたフィルターに該当する従業員が見つかりません",
    noDataAvailable: "データが利用できません",

    // Table headers
    employeeName: "従業員名",
    employeeId: "従業員ID",
    department: "部署",
    keyLogDate: "キーログ日付",
    keyLogTime: "キーログ時間",
    keyStrokeCount: "キーストローク数",
    applicationName: "アプリケーション名",
    applicationTitle: "アプリケーションタイトル",
    windowTitle: "ウィンドウタイトル",
    keyLogDuration: "キーログ期間",
    keyLogs: "キーログ",
    activityStartTime: "アクティビティ開始時間",
    activityEndTime: "アクティビティ終了時間",
    location: "場所",
    ipAddress: "IPアドレス",
    deviceId: "デバイスID",
    deviceName: "デバイス名",
    lastSeen: "最終確認",
    locationAccuracy: "位置精度",
    locationTimestamp: "位置情報タイムスタンプ",
    actions: "アクション",
    status: "ステータス",
    role: "役割",
    manager: "管理者",

    // Key Logger - Productivity
    productive: "生産的",
    unproductive: "非生産的",
    neutral: "中立的",
    userProductivity: "ユーザー生産性",
    productiveTooltip:
      "生産的と分類されたアプリケーションとウェブサイトに費やされた時間。",
    unproductiveTooltip:
      "非生産的と分類されたアプリケーションとウェブサイトに費やされた時間。",
    neutralTooltip:
      "分類されていないアプリケーションとウェブサイトに費やされた時間（未分類）。",
    userProductivityTooltip:
      "生産的および中立的なアプリケーションとウェブサイトにおけるユーザー生産性の割合。",
    unproductiveTime: "非生産的時間",
    noRecordsMatch: "検索に一致するレコードがありません",

    // Dashboard metrics
    totalKeyLogs: "キーログ合計",
    averageKeyStrokes: "平均キーストローク",
    topApplications: "トップアプリケーション",
    keyLogTrend: "キーログトレンド",
    locationDistribution: "位置分布",
    deviceDistribution: "デバイス分布",
    totalEmployees: "従業員総数",
    activeEmployees: "アクティブな従業員",

    // Location Intelligence specific
    workedDate: "勤務日",
    customGroup: "カスタムグループ",
    systemUpTime: "システム稼働時間",
    activeTime: "アクティブ時間",
    computerActivity: "コンピュータアクティビティ（マウスとキーボード）",
    productiveTime: "生産性の高い時間（アプリとURL）",
    employeeData: "従業員データ",
    activeDays: "アクティブ日数",
    daysOffice: "オフィス日数",
    daysRemote: "リモート日数",
    daysHybrid: "リモート/オフィス日数",
    inOffice: "オフィス内",
    inRemote: "リモート内",
    inHybrid: "オフィス/リモート内",
    locationDataEmpty: "選択したフィルター/検索のレコードがありません。",
    summary: "概要",
    day: "日",

    // Filter labels
    dateRange: "日付範囲",
    dateRangeSelector: "日付範囲を選択",
    applicationFilter: "アプリケーション",
    employeeFilter: "従業員",
    departmentFilter: "部署",
    locationFilter: "場所",
    deviceFilter: "デバイス",
    statusFilter: "ステータス",

    // Buttons and actions
    viewDetails: "詳細を表示",
    export: "エクスポート",
    resetFilterSearch: "フィルター/検索をリセット",
    refresh: "更新",
    close: "閉じる",
    cancel: "キャンセル",
    configureKeyLogger: "キーロガーを設定",
    employees: "従業員",

    // Data table
    itemsPerPage: "ページあたりの項目",
    itemsPerPageAll: "すべて",

    // Messages
    noKeyLogsFound: "選択されたフィルター/検索に該当するキーログはありません。",
    locationDataNotFound:
      "選択されたフィルター/検索に該当する位置情報はありません。",
    accessDeniedMessage: "このアクションを実行する権限がありません。",
    dateRangeExceeded: "31日未満の日付範囲を選択してください",
    dataLossPreventionHelp:
      "データ損失防止は、機密情報の監視と保護に役立ちます。",
    keyLoggerHelp:
      "キーロガーはキーボード活動を追跡して生産性を監視し、潜在的なデータ漏洩を検出します。",
    locationIntelligenceHelp:
      "位置情報インテリジェンスは従業員の位置を追跡して、勤務方針への準拠を確保します。",
  },
  sp: {
    /* ----- Data Loss Prevention ----- */
    // Module names
    dataLossPrevention: "Prevención de Pérdida de Datos",
    keyLogger: "Key Logger",
    myKeyLogs: "Mis Key Logs",
    myTeamKeyLogs: "Key Logs de Mi Equipo",
    locationIntelligence: "Inteligencia de ubicación",
    internetAccessControl: "Control de Acceso a Internet",
    locationTracking: "Seguimiento de Ubicación",
    additionalScreenshots: "Capturas de Pantalla Adicionales",

    // Section titles
    viewMyKeyLogs: "Ver Mis Key Logs",
    viewMyTeamKeyLogs: "Ver Key Logs de Mi Equipo",
    viewLocationIntelligence: "Ver inteligencia de ubicación",
    keyLoggerDashboard: "Panel de Key Logger",
    locationIntelligenceDashboard: "Panel de inteligencia de ubicación",

    // Key Logger - Common
    date: "Fecha",
    workSchedule: "Horario de Trabajo",
    timezone: "Zona Horaria",
    selectManager: "Seleccionar Gerente",
    back: "Atrás",
    change: "Cambiar",
    all: "Todos",
    online: "En línea",
    offline: "Desconectado",
    employeeIdShort: "ID-E",
    configureHere: "aquí",
    noKeyLogsAvailable:
      "Los key logs no están disponibles para este usuario, por favor configure",
    talkToAdmin:
      "Los key logs no están disponibles para este usuario, por favor hable con el administrador de DLP",
    noEmployeesFound: "No se encontraron empleados para el filtro aplicado",
    noDataAvailable: "No hay datos disponibles",

    // Table headers
    employeeName: "Nombre del Empleado",
    employeeId: "ID del Empleado",
    department: "Departamento",
    keyLogDate: "Fecha de Key Log",
    keyLogTime: "Hora de Key Log",
    keyStrokeCount: "Conteo de Pulsaciones",
    applicationName: "Nombre de la Aplicación",
    applicationTitle: "Título de la Aplicación",
    windowTitle: "Título de la Ventana",
    keyLogDuration: "Duración del Key Log",
    keyLogs: "Key Logs",
    activityStartTime: "Hora de Inicio de Actividad",
    activityEndTime: "Hora de Fin de Actividad",
    location: "Ubicación",
    ipAddress: "Dirección IP",
    deviceId: "ID del Dispositivo",
    deviceName: "Nombre del Dispositivo",
    lastSeen: "Visto por Última Vez",
    locationAccuracy: "Precisión de Ubicación",
    locationTimestamp: "Marca de Tiempo de Ubicación",
    actions: "Acciones",
    status: "Estado",
    role: "Rol",
    manager: "Gerente",

    // Key Logger - Productivity
    productive: "Productivo",
    unproductive: "Improductivo",
    neutral: "Neutral",
    userProductivity: "Productividad del Usuario",
    productiveTooltip:
      "Tiempo dedicado a aplicaciones y sitios web clasificados como productivos.",
    unproductiveTooltip:
      "Tiempo dedicado a aplicaciones y sitios web clasificados como improductivos.",
    neutralTooltip:
      "Tiempo dedicado a aplicaciones y sitios web que no están clasificados (Sin categorizar).",
    userProductivityTooltip:
      "Porcentaje de productividad del usuario en aplicaciones y sitios web productivos y neutrales.",
    unproductiveTime: "Tiempo Improductivo",
    noRecordsMatch: "Ningún registro coincide con su búsqueda",

    // Dashboard metrics
    totalKeyLogs: "Total de Key Logs",
    averageKeyStrokes: "Pulsaciones Promedio",
    topApplications: "Aplicaciones Principales",
    keyLogTrend: "Tendencia de Key Logs",
    locationDistribution: "Distribución de Ubicaciones",
    deviceDistribution: "Distribución de Dispositivos",
    totalEmployees: "Total de Empleados",
    activeEmployees: "Empleados Activos",

    // Location Intelligence specific
    workedDate: "Fecha Trabajada",
    customGroup: "Grupo Personalizado",
    systemUpTime: "Tiempo de Actividad del Sistema",
    activeTime: "Tiempo Activo",
    computerActivity: "Actividad del Computador (Ratón y Teclado)",
    productiveTime: "Tiempo Productivo (Apps y URL)",
    employeeData: "Datos del Empleado",
    activeDays: "Días Activos",
    daysOffice: "Días en Oficina",
    daysRemote: "Días Remotos",
    daysHybrid: "Días Remotos/Oficina",
    inOffice: "en Oficina",
    inRemote: "en Remoto",
    inHybrid: "en Oficina/Remoto",
    locationDataEmpty:
      "No hay registros para los filtros/búsquedas seleccionados.",
    summary: "Resumen",
    day: "día",

    // Filter labels
    dateRange: "Rango de Fechas",
    dateRangeSelector: "Seleccionar Rango de Fechas",
    applicationFilter: "Aplicación",
    employeeFilter: "Empleado",
    departmentFilter: "Departamento",
    locationFilter: "Ubicación",
    deviceFilter: "Dispositivo",
    statusFilter: "Estado",

    // Buttons and actions
    viewDetails: "Ver Detalles",
    export: "Exportar",
    resetFilterSearch: "Restablecer Filtro/Búsqueda",
    refresh: "Actualizar",
    close: "Cerrar",
    cancel: "Cancelar",
    configureKeyLogger: "Configurar Key Logger",
    employees: "Empleado(s)",

    // Data table
    itemsPerPage: "Elementos por página",
    itemsPerPageAll: "Todos",

    // Messages
    noKeyLogsFound:
      "No se encontraron key logs para los filtros/búsquedas seleccionados.",
    locationDataNotFound:
      "No se encontraron datos de ubicación para los filtros/búsquedas seleccionados.",
    accessDeniedMessage: "No tienes acceso para realizar esta acción.",
    dateRangeExceeded:
      "Por favor selecciona un rango de fechas de menos de 31 días",
    dataLossPreventionHelp:
      "La prevención de pérdida de datos ayuda a monitorear y proteger información confidencial.",
    keyLoggerHelp:
      "Key Logger rastrea la actividad del teclado para monitorear la productividad y detectar posibles fugas de datos",
    locationIntelligenceHelp:
      "Inteligencia de ubicación rastrea las ubicaciones de los empleados para garantizar el cumplimiento de las políticas laborales.",
  },
};
