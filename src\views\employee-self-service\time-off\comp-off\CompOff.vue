<template>
  <div v-if="isMounted">
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row
            justify="center"
            v-if="backupCompOffData.length > 0"
            class="mr-4"
          >
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                v-if="
                  openedSubTab == 'Comp Off Request' &&
                  backupCompOffData.length > 0
                "
                class="justify-end"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <FormFilter
                v-if="openedSubTab == 'Comp Off Request'"
                ref="formFilterRef"
                :items="backupCompOffData"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              >
              </FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="comp-off-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-container fluid class="comp-off-card">
            <ProfileCard>
              <FormTab :model-value="openedSubTab" :hide-slider="true">
                <v-tab
                  v-for="tab in subTabItems"
                  :key="tab.value"
                  :value="tab.value"
                  :disabled="tab.disable"
                  @click="onChangeSubTabs(tab.value)"
                >
                  <div
                    :class="[
                      isActiveSubTab(tab.value)
                        ? 'text-primary font-weight-bold'
                        : 'text-grey-darken-2 font-weight-bold',
                    ]"
                  >
                    {{ tab.label }}
                    <div
                      v-if="isActiveSubTab(tab.value)"
                      class="mt-3 mb-n4"
                      style="border-bottom: 4px solid; width: 150px"
                    ></div>
                  </div>
                </v-tab>
              </FormTab>
            </ProfileCard>
          </v-container>
          <v-container fluid class="comp-off-data">
            <v-window v-model="openedSubTab">
              <v-window-item value="Comp Off Request">
                <div>
                  <div v-if="listLoading" class="mt-3">
                    <v-skeleton-loader
                      ref="skeleton1"
                      type="table-heading"
                      class="mx-auto"
                    ></v-skeleton-loader>
                    <div v-for="i in 3" :key="i" class="mt-4">
                      <v-skeleton-loader
                        ref="skeleton2"
                        type="list-item-avatar"
                        class="mx-auto"
                      ></v-skeleton-loader>
                    </div>
                  </div>
                  <div v-else-if="!isErrorInList">
                    <div
                      v-if="compOffData.length > 0"
                      class="d-flex flex-wrap align-center my-3"
                      :class="isMobileView ? 'flex-column' : ''"
                      style="justify-content: space-between"
                    >
                      <div
                        class="d-flex align-center flex-wrap"
                        :class="isMobileView ? 'justify-center' : ''"
                      >
                        <CustomSelect
                          v-model="selectedPeriod"
                          :items="periodList"
                          :itemSelected="selectedPeriod"
                          :isAutoComplete="true"
                          variant="solo"
                          class="mt-3"
                          label="Period"
                          placeholder="Select Period"
                          density="compact"
                          min-width="150px"
                          max-width="500px"
                          @selected-item="onChangePeriod($event)"
                        />
                        <v-btn
                          class="bg-white mb-3 ml-2"
                          :style="'width: max-content'"
                          :size="isMobileView ? 'small' : 'default'"
                          rounded="lg"
                          @click="
                            $refs.datePicker.fp.open(), resetDateFilters()
                          "
                        >
                          <v-icon color="primary" size="14"
                            >fas fa-calendar-alt</v-icon
                          >
                          <span class="text-caption px-1 pt-1">Date:</span>
                          <flat-pickr
                            ref="datePicker"
                            v-model="selectedMonthYear"
                            :config="flatPickerOptions"
                            placeholder="Select Date Range"
                            class="ml-2 mt-1 date-range-picker-custom-bg"
                            style="
                              outline: 0px;
                              color: var(--v-primary-base);
                              width: 170px;
                            "
                            @onChange="onChangeDateRange"
                          />
                        </v-btn>
                      </div>
                      <div
                        v-if="formAccess && formAccess.add"
                        class="d-flex align-center"
                        :class="isMobileView ? 'justify-center' : 'justify-end'"
                      >
                        <v-btn
                          @click="addButtonClicked()"
                          class="primary"
                          variant="elevated"
                          :size="isMobileView ? 'small' : 'default'"
                        >
                          <v-icon size="15" class="pr-1 primary"
                            >fas fa-plus</v-icon
                          >
                          <span class="primary">Apply</span></v-btn
                        >
                        <v-btn
                          color="transparent"
                          variant="flat"
                          rounded="lg"
                          :size="isMobileView ? 'small' : 'default'"
                          @click="refetchCompOffList()"
                        >
                          <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
                        </v-btn>
                        <v-menu class="mb-1 mt-1" transition="scale-transition">
                          <template v-slot:activator="{ props }">
                            <v-btn variant="plain" v-bind="props">
                              <v-icon>fas fa-ellipsis-v</v-icon>
                            </v-btn>
                          </template>
                          <v-list>
                            <v-list-item
                              v-for="action in moreActions"
                              :key="action.key"
                              @click="onMoreAction(action.key)"
                            >
                              <v-hover>
                                <template
                                  v-slot:default="{ isHovering, props }"
                                >
                                  <v-list-item-title
                                    v-bind="props"
                                    class="pa-3"
                                    :class="{
                                      hover: isHovering,
                                    }"
                                    >{{ action.key }}</v-list-item-title
                                  >
                                </template>
                              </v-hover>
                            </v-list-item>
                          </v-list>
                        </v-menu>
                      </div>
                    </div>
                    <v-data-table
                      v-if="compOffData.length > 0"
                      :headers="headers"
                      :items="compOffData"
                      :items-per-page="50"
                      :items-per-page-options="[
                        { value: 50, title: '50' },
                        { value: 100, title: '100' },
                        {
                          value: -1,
                          title: '$vuetify.dataFooter.itemsPerPageAll',
                        },
                      ]"
                      fixed-header
                      :sort-by="[{ key: 'Compensatory_Date', order: 'desc' }]"
                      :height="
                        $store.getters.getTableHeightBasedOnScreenSize(
                          290,
                          compOffData
                        )
                      "
                      class="elevation-1"
                      style="box-shadow: none !important"
                    >
                      <template v-slot:item="{ item, index }">
                        <tr
                          style="z-index: 200; cursor: pointer"
                          class="data-table-tr bg-white cursor-pointer"
                          @click="viewCompOffDetails(item, index)"
                          :class="[
                            isMobileView
                              ? ' v-data-table__mobile-table-row ma-0 mt-2'
                              : '',
                          ]"
                        >
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5'
                            "
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Worked Date
                            </div>
                            <section
                              style="height: 3em"
                              class="d-flex align-center"
                            >
                              <div class="d-flex align-center">
                                <div
                                  v-if="windowWidth > 1264"
                                  class="data-table-side-border d-flex"
                                  style="height: 3em"
                                  :style="getBorderStyle(item.Approval_Status)"
                                ></div>
                              </div>
                              <span
                                class="text-primary text-body-2 font-weight-regular text-truncate"
                              >
                                <section
                                  :class="isMobileView ? '' : 'text-truncate'"
                                >
                                  <span
                                    class="text-primary font-weight-regular"
                                  >
                                    {{ checkNullValue(item.Compensated_Date) }}
                                  </span>
                                </section>
                              </span>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5 font-weight-small'
                            "
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Duration
                            </div>
                            <section>
                              <span class="text-body-2 font-weight-regular">
                                {{ checkNullValue(item.Duration) }}
                              </span>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5 font-weight-small'
                            "
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Period
                            </div>
                            <section>
                              <span class="text-body-2 font-weight-regular">
                                {{ checkNullValue(item.Period) }}
                              </span>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5 font-weight-small'
                            "
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Compensatory Off Date
                            </div>
                            <section>
                              <span class="text-body-2 font-weight-regular">
                                {{ checkNullValue(item.Compensatory_Date) }}
                              </span>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5'
                            "
                            :style="isMobileView ? '' : 'max-width: 100px'"
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Status
                            </div>
                            <section
                              :class="getStatusClass(item.Approval_Status)"
                            >
                              <span class="text-body-2 font-weight-regular">
                                {{ checkNullValue(item.Approval_Status) }}
                              </span>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2'
                            "
                            @click.stop=""
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Actions
                            </div>
                            <section>
                              <div class="d-flex justify-center">
                                <ActionMenu
                                  v-if="getActions.length"
                                  @selected-action="onActions($event, item)"
                                  :actions="getActions(item)"
                                  :access-rights="checkAccess()"
                                  :disableActionButtons="
                                    getDisabledButtons(item)
                                  "
                                  :tooltipActionButtons="
                                    getDisabledButtons(item)
                                  "
                                  :tooltipMessage="getTooltipMessage(item)"
                                  iconColor="grey"
                                ></ActionMenu>
                                <section
                                  class="text-body-2 font-weight-medium"
                                  v-else
                                >
                                  -
                                </section>
                              </div>
                            </section>
                          </td>
                        </tr>
                      </template>
                    </v-data-table>
                  </div>
                  <div v-if="!listLoading && backupCompOffData.length == 0">
                    <AppFetchErrorScreen key="no-results-screen">
                      <template #contentSlot>
                        <div style="max-width: 80%">
                          <v-row
                            v-if="!isLoading"
                            style="background: white"
                            class="rounded-lg pa-5 mb-4"
                          >
                            <v-col cols="12">
                              <NotesCard
                                notes="The Comp Off Request feature allows employees to request compensatory 
leave for additional hours worked, such as working on weekends or public 
holidays. Employees can specify the date(s) worked and the requested 
compensatory leave period, ensuring a smooth and transparent process. The 
system automatically validates requests based on eligibility criteria, ensuring 
that only qualified employees can apply for compensatory leave."
                                backgroundColor="transparent"
                                class="mb-2"
                              ></NotesCard>
                              <NotesCard
                                notes="Once submitted, requests are routed to the designated manager for review. 
Managers can approve, reject, or request modifications based on company 
policies and operational requirements. Approved compensatory leave requests 
are immediately deducted from the employee’s available balance. This feature 
ensures that employees receive fair time-off compensation while maintaining an
organized and compliant leave approval process."
                                backgroundColor="transparent"
                                class="mb-4"
                              ></NotesCard>
                            </v-col>

                            <v-col
                              cols="12"
                              class="d-flex align-center justify-center flex-wrap mb-4"
                            >
                              <CustomSelect
                                v-model="selectedPeriod"
                                :items="periodList"
                                :itemSelected="selectedPeriod"
                                :isAutoComplete="true"
                                variant="solo"
                                class="mt-3"
                                label="Period"
                                placeholder="Select Period"
                                density="compact"
                                min-width="150px"
                                :max-width="isMobileView ? `200px` : `300px`"
                                @selected-item="onChangePeriod($event)"
                              />
                              <v-btn
                                class="bg-white mb-3 mx-4"
                                :style="'width: max-content'"
                                :size="isMobileView ? 'small' : 'default'"
                                rounded="lg"
                                @click="
                                  $refs.datePicker.fp.open(), resetDateFilters()
                                "
                              >
                                <v-icon color="primary" size="14"
                                  >fas fa-calendar-alt</v-icon
                                >
                                <span class="text-caption px-1 pt-1"
                                  >Date:</span
                                >
                                <flat-pickr
                                  ref="datePicker"
                                  v-model="selectedMonthYear"
                                  :config="flatPickerOptions"
                                  placeholder="Select Date Range"
                                  class="ml-2 mt-1 date-range-picker-custom-bg"
                                  style="
                                    outline: 0px;
                                    color: var(--v-primary-base);
                                  "
                                  :style="`width: ${
                                    isMobileView ? `160px` : `170px`
                                  }`"
                                  @onChange="onChangeDateRange"
                                />
                              </v-btn>
                              <div v-if="formAccess && formAccess.add">
                                <v-btn
                                  @click="addButtonClicked()"
                                  class="px-6 seconary mr-2"
                                  variant="elevated"
                                >
                                  <v-icon size="15" class="pr-1"
                                    >fas fa-plus</v-icon
                                  ><span class="primary">Apply</span>
                                </v-btn>
                              </div>
                              <v-btn
                                color="transparent"
                                variant="flat"
                                rounded="lg"
                                :size="isMobileView ? 'small' : 'default'"
                                @click="refetchCompOffList()"
                              >
                                <v-icon class="pr-1" color="grey"
                                  >fas fa-redo-alt</v-icon
                                >
                              </v-btn>
                            </v-col>
                          </v-row>
                        </div>
                      </template>
                    </AppFetchErrorScreen>
                  </div>
                  <div
                    v-if="
                      compOffData?.length == 0 &&
                      !listLoading &&
                      emptyFilterScreen
                    "
                  >
                    <AppFetchErrorScreen
                      image-name="common/no-records"
                      main-title="There are no comp off records for the selected filters/searches."
                    >
                      <template #contentSlot>
                        <div class="d-flex mb-2 flex-wrap justify-center">
                          <v-btn
                            color="primary"
                            variant="elevated"
                            class="ml-4 mt-1"
                            rounded="lg"
                            :size="isMobileView ? 'small' : 'default'"
                            @click.stop="resetFilter()"
                          >
                            Reset Filter/Search
                          </v-btn>
                        </div>
                      </template>
                    </AppFetchErrorScreen>
                  </div>
                  <div
                    v-else-if="isErrorInList && backupCompOffData.length != 0"
                  >
                    <AppFetchErrorScreen
                      :content="errorContent"
                      icon-name="fas fa-redo-alt"
                      :button-text="showRetryBtn ? 'Retry' : ''"
                      @button-click="refetchCompOffList()"
                    >
                    </AppFetchErrorScreen>
                  </div>
                </div>
              </v-window-item>
              <v-window-item value="Comp Off Balance">
                <CompOffBalance
                  formName="EmployeeSelfService"
                  formId="140"
                ></CompOffBalance>
              </v-window-item>
            </v-window>
          </v-container>
        </v-window-item>
      </v-window>

      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <CompOffForm
      v-if="openCompOffForm"
      :selectedCompOffData="selectedCompOffRecord"
      :isEditForm="isEditForm"
      @on-close-add-form="closeCompOffForm()"
      @refetch-list="refetchCompOffList($event)"
    />
    <ViewCompOff
      :selectedCompOffData="selectedCompOffRecord"
      :enable-view="showViewForm"
      @close-view-details="closeView()"
      @edit-comp-off-record="editCompOff()"
    />
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      :confirmation-heading="warningText"
      :icon-name="warningIconClass"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="deleteCompOffRecord()"
    >
      <template>
        <v-form>
          <v-textarea
            variant="outlined"
            auto-grow
            rows="1"
            class="mt-4"
            :style="isMobileView ? '' : 'min-width: 300px'"
          ></v-textarea>
        </v-form>
      </template>
    </AppWarningModal>
    <AppWarningModal
      v-if="openCancelCompOffForm"
      :open-modal="openCancelCompOffForm"
      confirmation-heading="Are you sure to cancel the selected record?"
      icon-name="far fa-times-circle"
      icon-Size="75"
      :acceptButtonDisable="!comment"
      @close-warning-modal="closeCancelCompOffForm()"
      @accept-modal="addUpdateCompOff()"
    >
      <template v-slot:warningModalContent>
        <div class="mt-2">
          <CustomSelect
            v-model="selectedStatus"
            :items="statusList"
            :itemSelected="selectedStatus"
            item-title="text"
            item-value="value"
            :rules="[required('Status', selectedStatus)]"
            :is-required="true"
            label="Status"
            variant="solo"
            min-width="350px"
          />
          <v-textarea
            ref="Comment"
            v-model="comment"
            :rules="[
              required('Comment', comment),
              validateWithRulesAndReturnMessages(
                comment,
                'description',
                'Comment'
              ),
            ]"
            rows="3"
            :clearable="true"
            auto-grow
            variant="solo"
            min-width="350px"
            ><template v-slot:label>
              Comment
              <span style="color: red">*</span>
            </template></v-textarea
          >
        </div>
      </template>
    </AppWarningModal>
    <ApprovalFlowModal
      v-if="openApprovalModal"
      :task-id="processId"
      @close-modal="openApprovalModal = false"
    ></ApprovalFlowModal>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const CompOffForm = defineAsyncComponent(() => import("./AddEditCompOff.vue"));
const ViewCompOff = defineAsyncComponent(() => import("./ViewCompOff.vue"));
import { LIST_COMP_OFF } from "@/graphql/my-team/comp-off-balance/compOffBalanceQueries.js";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import FormFilter from "./FormFilter.vue";
import ApprovalFlowModal from "@/components/custom-components/ApprovalFlowModal.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import CompOffBalance from "@/views/my-team/comp-off-balance/CompOffBalance.vue";

export default {
  name: "CompOff",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    FormFilter,
    ActionMenu,
    CompOffForm,
    ViewCompOff,
    CustomSelect,
    ApprovalFlowModal,
    flatPickr,
    CompOffBalance,
  },
  mixins: [FileExportMixin, validationRules],
  data: () => ({
    openedSubTab: "Comp Off Request",
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    isLoading: false,
    isEdit: false,
    selectedData: [],
    currentTabItem: "",
    showRetryBtn: true,
    compOffData: [],
    selectedItem: null,
    validationMessages: [],
    showValidationAlert: false,
    isMounted: false,
    emptyFilterScreen: false,
    backupCompOffData: [],
    backupFilterData: [],
    toolTipForConf: false,
    showListHistory: false,
    havingAccess: {},
    openCompOffForm: false,
    showViewForm: false,
    selectedCompOffRecord: {},
    isEditForm: false,
    openWarningModal: false,
    warningText: "Are you sure to delete the comp off request?",
    warningIconClass: "",
    deleteItem: null,
    openCancelCompOffForm: false,
    statusList: ["Cancel CompOff"],
    selectedStatus: null,
    comment: "",
    openApprovalModal: false,
    processId: null,
    startDate: null,
    endDate: null,
    selectedPeriod: null,
    selectedMonthYear: null,
  }),
  computed: {
    landedFormName() {
      let form = this.accessRights(335);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Compensatory Off Request";
    },
    moreActions() {
      return [
        {
          key: "Export",
        },
      ];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.selfServiceTimeOffFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("335");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForCompOffBalance() {
      let formAccess = this.accessRights("140");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    headers() {
      return [
        {
          title: "Worked Date",
          key: "Compensated_Date",
        },
        {
          title: "Duration",
          key: "Duration",
        },
        {
          title: "Period",
          key: "Period",
        },
        {
          title: "Compensatory Off Date",
          key: "Compensatory_Date",
        },
        {
          title: "Status",
          key: "Approval_Status",
        },
        {
          title: "Actions",
          key: "action",
          align: "center",
          sortable: false,
        },
      ];
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
      };
    },
    periodList() {
      return [
        "Last 7 Days",
        "This Month",
        "Last Month",
        "Next 90 Days",
        "Custom",
      ];
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    selfServiceTimeOffFormAccess() {
      return this.$store.getters.selfServiceTimeOffFormAccess;
    },
    subTabItems() {
      let initialTabs = [
        {
          label: "Comp Off Request",
          value: "Comp Off Request",
          disable: false,
        },
      ];
      if (this.formAccessForCompOffBalance) {
        initialTabs.push({
          label: "Comp Off Balance",
          value: "Comp Off Balance",
          disable: false,
        });
      }
      return initialTabs;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.getCurrentDateRange();
    this.isMounted = true;
    this.openedSubTab = "Comp Off Request";
  },
  methods: {
    checkNullValue,
    getCurrentDateRange() {
      // Leave Date From = Current Month
      const leaveDateFrom = moment().startOf("month").format("YYYY-MM-DD");
      const leaveDateTo = moment().endOf("month").format("YYYY-MM-DD");
      this.selectedPeriod = "This Month";
      // Set the Date Array instead of String
      this.selectedMonthYear = [
        this.formatDate(leaveDateFrom),
        this.formatDate(leaveDateTo),
      ];
      this.startDate = leaveDateFrom;
      this.endDate = leaveDateTo;

      this.fetchCompOffDetails();
    },
    onChangePeriod(period) {
      if (period === "Custom") {
        this.selectedMonthYear = null;
      } else {
        let startDate, endDate;

        switch (period) {
          case "Today":
            startDate = endDate = moment().format("YYYY-MM-DD");
            break;
          case "Yesterday":
            startDate = endDate = moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD");
            break;
          case "Last 7 Days":
            startDate = moment().subtract(6, "days").format("YYYY-MM-DD");
            endDate = moment().format("YYYY-MM-DD");
            break;
          case "This Month":
            startDate = moment().startOf("month").format("YYYY-MM-DD");
            endDate = moment().endOf("month").format("YYYY-MM-DD");
            break;
          case "Last Month":
            startDate = moment()
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD");
            endDate = moment()
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD");
            break;
          case "Next 90 Days":
            startDate = moment().format("YYYY-MM-DD");
            endDate = moment().add(90, "days").format("YYYY-MM-DD");
            break;
        }

        if (startDate && endDate) {
          this.selectedMonthYear = [
            this.formatDate(startDate),
            this.formatDate(endDate),
          ];
        }
      }
    },
    onChangeDateRange(selectedDates) {
      if (selectedDates?.length > 1) {
        // Format the start and end dates
        let leaveStartDate = moment(selectedDates[0]).format("YYYY-MM-DD");
        let leaveEndDate = moment(selectedDates[1]).format("YYYY-MM-DD");

        // Calculate the difference in days
        let dateDifference = moment(leaveEndDate).diff(
          moment(leaveStartDate),
          "days"
        );
        const differenceAllowed = 365;
        // Prevent if the range is more than difference allowed days
        if (dateDifference > differenceAllowed) {
          this.selectedMonthYear = [
            this.formatDate(this.startDate),
            this.formatDate(this.endDate),
          ];
          this.showAlert({
            isOpen: true,
            message: `The selected date range cannot exceed ${differenceAllowed} days. Please select a valid date range.`,
            type: "warning",
          });
          return;
        }
        if (
          moment(selectedDates[0]).format("YYYY-MM-DD") != this.startDate ||
          moment(selectedDates[1]).format("YYYY-MM-DD") != this.endDate
        ) {
          // Set the dates and fetch the list
          this.startDate = leaveStartDate;
          this.endDate = leaveEndDate;
          this.fetchCompOffDetails();
        }
      }
    },
    getActions(item) {
      const status = item.Approval_Status?.toLowerCase();
      const { update, delete: canDelete, view } = this.formAccess || {};
      const optionMenu = [];

      const statusRules = {
        approved: () => update && optionMenu.push("Cancel"),
        rejected: () => canDelete && optionMenu.push("Delete"),
        "cancel applied": () => canDelete && optionMenu.push("Delete"),
        cancelled: () => canDelete && optionMenu.push("Delete"),
        applied: () => {
          if (update) optionMenu.push("Edit");
          if (canDelete) optionMenu.push("Delete");
        },
        default: () => {
          if (update) optionMenu.push("Edit", "Cancel");
          if (canDelete) optionMenu.push("Delete");
        },
      };

      (statusRules[status] || statusRules.default)();

      if (item?.Process_Instance_Id && view) {
        optionMenu.push("Approval Workflow");
      }

      return optionMenu;
    },

    onChangeSubTabs(val) {
      this.openedSubTab = val;
      // For future use
      // if (val.toLowerCase() == "comp off balance") {
      //   this.$router.push(
      //     "/employee-self-service/compensatory-off-balance?from=emp"
      //   );
      // }
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    addButtonClicked() {
      this.isEditForm = false;
      this.openCompOffForm = true;
      this.selectedCompOffRecord = {};
    },
    onActions(type, item) {
      let isPayslipGenerated = item.M == 1 || item.H == 1;
      // Check if the action is disabled due to payslip generation
      if (
        isPayslipGenerated &&
        (type.toLowerCase() === "cancel" ||
          type.toLowerCase() === "edit" ||
          type.toLowerCase() === "delete")
      )
        return;
      if (type && type.toLowerCase() === "edit") {
        this.editCompOff(item);
      } else if (type && type.toLowerCase() === "delete") {
        this.validateDeletionOfEmailTemplate(item, "fas fa-trash");
      } else if (type && type.toLowerCase() === "cancel") {
        this.cancelCompOff(item);
      } else if (type && type.toLowerCase() === "approval workflow") {
        this.openApprovalFlow(item.Process_Instance_Id);
      }
    },
    checkAccess() {
      this.havingAccess["update"] = this.formAccess?.update ? 1 : 0;
      this.havingAccess["delete"] = this.formAccess?.delete ? 1 : 0;
      this.havingAccess["cancel"] = this.formAccess?.update ? 1 : 0;
      this.havingAccess["approval workflow"] = this.formAccess?.view ? 1 : 0;
      return this.havingAccess;
    },
    resetFilter() {
      this.compOffData = this.backupCompOffData;
      this.emptyFilterScreen = false;
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.applyFilter(this.compOffData);
    },
    applyFilter(filteredArray) {
      this.compOffData = filteredArray;
      this.backupFilterData = filteredArray;
      if (this.compOffData.length == 0) {
        this.emptyFilterScreen = true;
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onApplySearch(val) {
      if (!val) {
        this.compOffData = this.backupFilterData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupFilterData;
        // Defining the keys that are present in the v-data-table
        const searchableKeys = [
          "Compensated_Date",
          "Duration",
          "Period",
          "Compensatory_Date",
          "Approval_Status",
        ];

        searchItems = searchItems.filter((item) => {
          return searchableKeys.some((key) => {
            const value = item[key];
            return (
              value && value.toString().toLowerCase().includes(searchValue)
            );
          });
        });
        this.compOffData = searchItems;
        if (this.compOffData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    getStatusClass(status) {
      if (status === "Approved") {
        return "text-green";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    formatIntoHoursAndMinutes(item) {
      let hours = Math.floor(item);
      let minutes = Math.round((item - hours) * 60);
      return `${hours} hour(s) ${minutes} min(s)`;
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
    },

    exportReportFile() {
      let compOffList = this.compOffData.map((item) => ({
        ...item,
      }));
      let fileName = "Comp Off";
      let exportHeaders = [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Worked Date", key: "Compensated_Date" },
        { header: "Duration", key: "Duration" },
        { header: "Period", key: "Period" },
        { header: "Compensatory Off Date", key: "Compensatory_Date" },
        {
          header: "Status",
          key: "Approval_Status",
        },
        { header: "Reason", key: "Reason" },
        { header: "Added By", key: "Added_By_Name" },
        { header: "Added On", key: "Added_On" },
        { header: "Updated By", key: "Updated_By_Name" },
        { header: "Updated On", key: "Updated_On" },
      ];
      let exportOptions = {
        fileExportData: compOffList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    closeEditForm() {
      this.isEdit = false;
    },
    openEditForm() {
      this.isEdit = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.selfServiceTimeOffFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/employee-self-service/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/employee-self-service/" + clickedForm.url;
        }
      }
    },
    validateDeletionOfEmailTemplate(item, warningicon) {
      this.openWarningPopUp(item, warningicon);
    },
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.deleteItem = null;
    },
    openWarningPopUp(item, warningicon) {
      if (item === null) {
        this.warningIconClass = warningicon;
        this.openWarningModal = true;
        return;
      } else {
        this.warningIconClass = warningicon;
        this.openWarningModal = true;
        this.deleteItem = item;
      }
    },
    deleteCompOffRecord() {
      let vm = this;
      vm.isLoading = true;
      const apiObj = {
        url: vm.baseUrl + "employees/compensatory-off/delete-compensatory-off",
        type: "POST",
        dataType: "json",
        data: {
          compOffId: this.deleteItem.Compensatory_Off_Id,
        },
      };
      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.success) {
            let snackbarData = {
              isOpen: true,
              message: "Compensatory off request deleted successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
          } else {
            let snackbarData = {
              isOpen: true,
              message: res.msg,
              type: "warning",
            };
            vm.showAlert(snackbarData);
          }
          this.refetchCompOffList();
          this.onCloseWarningModal();
          this.isLoading = false;
        })
        .catch((err) => {
          this.isLoading = false;
          this.onCloseWarningModal();
          vm.handleDeleteCompOffRecordError(err);
        });
    },
    handleDeleteCompOffRecordError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "comp off details",
        isListError: false,
      });
    },
    closeCancelCompOffForm() {
      this.openCancelCompOffForm = false;
    },
    fetchCompOffDetails() {
      let vm = this;

      // Directly use the array
      let leaveStartDate = this.startDate;
      let leaveEndDate = this.endDate;

      if (
        !moment(leaveStartDate).isValid() ||
        !moment(leaveEndDate).isValid() ||
        !this.selectedMonthYear?.length ||
        !this.selectedPeriod
      ) {
        vm.showAlert({
          isOpen: true,
          message: "Please select a valid Date Range/ Period.",
          type: "warning",
        });
        return;
      }
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_COMP_OFF,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            formId: 335,
            startDate: leaveStartDate,
            endDate: leaveEndDate,
          },
        })
        .then((response) => {
          if (response.data?.listCompOff?.compOffDetails) {
            vm.backupCompOffData = JSON.parse(
              response.data.listCompOff.compOffDetails
            );
            vm.compOffData = vm.backupCompOffData;
            if (vm.compOffData && vm.compOffData.length) {
              vm.applyFilter(vm.compOffData);
            }
          } else {
            vm.handleListError();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "comp off details",
        isListError: true,
      });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    refetchCompOffList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.openCompOffForm = false;
      this.openCancelCompOffForm = false;
      this.fetchCompOffDetails();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
    },
    cancelCompOff(item = {}) {
      this.openCancelCompOffForm = true;
      this.comment = "";
      if (Object.keys(item).length) {
        this.selectedCompOffRecord = item;
        this.selectedStatus = this.statusList[0];
      }
    },
    addUpdateCompOff() {
      let vm = this;
      vm.isLoading = true;
      const apiObj = {
        url: vm.baseUrl + "employees/compensatory-off/update-comp-off-status",
        type: "POST",
        dataType: "json",
        data: {
          Compensatory_Off_Id: vm.selectedCompOffRecord.Compensatory_Off_Id,
          Approver_Id: vm.selectedCompOffRecord.Approver_Id,
          Employee_Id: vm.selectedCompOffRecord.Employee_Id,
          Added_By: vm.loginEmployeeId,
          Status: "Cancel Applied",
          Status_Text: vm.selectedStatus,
          Comments: vm.comment,
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.success) {
            let snackbarData = {
              isOpen: true,
              message: "Compensatory Off status updated successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            this.isLoading = false;
          } else {
            let snackbarData = {
              isOpen: true,
              message: res.msg,
              type: "warning",
            };
            vm.showAlert(snackbarData);
            this.isLoading = false;
          }
          vm.refetchCompOffList();
        })
        .catch((err) => {
          vm.handleAddUpdateError(err);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.refetchCompOffList();
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEdit ? "updating" : "adding",
        form: "comp off",
        isListError: false,
      });
    },
    closeCompOffForm() {
      this.openCompOffForm = false;
    },
    closeView() {
      this.showViewForm = false;
    },
    viewCompOffDetails(item) {
      this.showViewForm = true;
      this.selectedCompOffRecord = item;
    },
    editCompOff(item = {}) {
      this.openCompOffForm = true;
      this.isEditForm = true;
      this.showViewForm = false;
      if (Object.keys(item).length) {
        this.selectedCompOffRecord = item;
      }
    },
    openApprovalFlow(ProcessInstanceId) {
      this.processId = ProcessInstanceId;
      this.openApprovalModal = true;
    },
    resetDateFilters() {
      this.selectedPeriod = "Custom";
      this.startDate = null;
      this.endDate = null;
      this.selectedMonthYear = null;
    },
    getDisabledButtons(item) {
      let disabledButtons = [];

      // Add Delete button if it should be disabled
      if (this.enableToltipForDelete(item)) {
        disabledButtons.push("Delete");
      }

      // Add Cancel and Edit buttons if payslip is generated
      if (item.M == 1 || item.H == 1) {
        disabledButtons.push("Cancel", "Edit");
        if (!disabledButtons?.includes("Delete"))
          disabledButtons.push("Delete");
      }
      return disabledButtons;
    },
    getTooltipMessage(item) {
      // If payslip is generated, show that message
      if (item.M == 1 || item.H == 1) {
        return "Actions are disabled as the payslip has been generated";
      }
      return "";
    },
    enableToltipForDelete(item) {
      return item.Approval_Status.toLowerCase() === "approved";
    },
    getBorderStyle(status) {
      const greenStatuses = ["Approved", "Cancelled"];
      const redStatuses = ["Rejected"];
      const amberStatuses = ["Cancel Applied", "Applied"];

      let color = "#ffffff"; // default

      if (greenStatuses.includes(status)) {
        color = "#2ecc40"; // green
      } else if (redStatuses.includes(status)) {
        color = "#ff4d4f"; // red
      } else if (amberStatuses.includes(status)) {
        color = "#ffc107"; // amber
      }

      return {
        borderLeft: `7px solid ${color}`,
      };
    },
  },
};
</script>

<style scoped>
.comp-off-container {
  padding: 3.7em 0em 0em 0em;
}
.custom-box-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.comp-off-card {
  padding: 0em 0em 0em 0em;
}

.comp-off-data {
  padding-left: 2em;
  padding-right: 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__value) {
  min-width: 160px;
  display: flex;
  align-items: center;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__calendar) {
  right: 1px;
}

@media screen and (max-width: 805px) {
  .comp-off-container {
    padding: 4em 1em 0em 1em;
  }
  .comp-off-card {
    padding: 0em 0em 0em 0em;
  }
  .comp-off-data {
    padding: 0em 0em 0em 0em;
  }
}
</style>
