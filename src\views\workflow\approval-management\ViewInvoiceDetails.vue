<template>
  <v-dialog
    v-model="openModal"
    persistent
    width="1600"
    @click:outside="closeModal()"
  >
    <v-card class="rounded-lg" min-height="400px">
      <v-card-title>
        <div class="d-flex" style="width: 100%">
          Invoice Details
          <v-spacer></v-spacer>
          <div
            v-if="invoiceDetails.length > 1"
            class="d-flex align-center pr-2"
          >
            <v-icon
              size="17"
              class="mr-2"
              :color="currentInvoice === 1 ? 'grey' : 'primary'"
              @click="currentInvoice === 1 ? {} : prevInvoice()"
            >
              fa fa-chevron-left
            </v-icon>
            <span class="text-black"
              >{{ currentInvoice }} / {{ invoiceDetails.length }}</span
            >
            <v-icon
              size="17"
              class="ml-2"
              :color="
                currentInvoice === invoiceDetails.length ? 'grey' : 'primary'
              "
              @click="
                currentInvoice === invoiceDetails.length ? {} : nextInvoice()
              "
            >
              fa fa-chevron-right
            </v-icon>
          </div>
          <v-icon color="primary" @click="closeModal()">fas fa-times</v-icon>
        </div>
      </v-card-title>
      <v-card-text
        :style="`
          max-height: calc(100vh - 240px);
          overflow: scroll;
        `"
      >
        <v-row>
          <v-col cols="12">
            <v-row>
              <v-col cols="12" sm="6" md="3">
                <p class="text-subtitle-1 text-grey-darken-1">Invoice Number</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(selectedInvoice.Invoice_No) }}
                </p>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <p class="text-subtitle-1 text-grey-darken-1">Invoice Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(selectedInvoice.Invoice_Date) }}
                </p>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <p class="text-subtitle-1 text-grey-darken-1">Invoice Amount</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(selectedInvoice.Actual_Amount) }}
                </p>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Approval Amount
                </p>
                <div
                  v-if="showApprovalEditForm"
                  class="text-subtitle-1 font-weight-regular d-flex align-center"
                >
                  <v-form ref="approvalAmountForm">
                    <v-text-field
                      v-model="editedApprovalAmount"
                      variant="outlined"
                      style="width: 200px"
                      type="number"
                      density="compact"
                      :rules="[
                        approvalAmountRules.required,
                        approvalAmountRules.min,
                        approvalAmountRules.max,
                      ]"
                    ></v-text-field>
                  </v-form>
                  <i
                    class="hr-workflow-task-management-approve text-h5 text-green pl-2 cursor-pointer"
                    aria-hidden="true"
                    title="Save"
                    @click="onSaveApprovalAmount()"
                  ></i>
                  <i
                    class="hr-workflow-task-management-reject text-h5 text-red pl-2 cursor-pointer"
                    aria-hidden="true"
                    title="Cancel"
                    @click="showApprovalEditForm = false"
                  ></i>
                </div>
                <p
                  v-else
                  class="text-subtitle-1 font-weight-regular d-flex align-center"
                >
                  {{ checkNullValue(selectedInvoice.Invoice_Amount) }}
                  <v-btn
                    v-if="!isApprovalHistory && formUpdateAccess"
                    color="secondary"
                    variant="text"
                    @click="onShowApprovalEdit(selectedInvoice.Invoice_Amount)"
                  >
                    <v-icon class="ml-1" size="14">fas fa-edit</v-icon>
                  </v-btn>
                </p>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Reimbursement Mode
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(selectedInvoice.Reimbursement_Mode) }}
                </p>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <p class="text-subtitle-1 text-grey-darken-1">Expense Type</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    parseInt(selectedInvoice.Expense_Type_Id) === 0
                      ? checkNullValue(selectedInvoice.Other_Expense)
                      : checkNullValue(selectedInvoice.Expense_Type)
                  }}
                </p>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <p class="text-subtitle-1 text-grey-darken-1">Allowance Type</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(selectedInvoice.Allowance_Type) }}
                </p>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <p class="text-subtitle-1 text-grey-darken-1">Description</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(selectedInvoice.Description) }}
                </p>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <div style="height: 100%">
              <div
                style="height: 100%"
                class="d-flex justify-center flex-column"
              >
                <div
                  class="text-subtitle-1 text-grey-darken-1 d-flex align-center"
                >
                  Document(s)
                  <span
                    v-if="selectedInvoiceDocument.length > 0"
                    class="px-1 text-primary font-weight-medium"
                  >
                    - {{ formattedFileName }}
                  </span>
                  <div
                    v-if="selectedInvoiceDocument.length > 1"
                    class="d-flex align-center pl-2"
                  >
                    <v-icon
                      size="17"
                      class="mr-2"
                      :color="currentDocument === 1 ? 'grey' : 'primary'"
                      @click="currentDocument === 1 ? {} : prevDocument()"
                    >
                      fa fa-chevron-left </v-icon
                    ><span class="text-black pl-2">
                      {{ currentDocument }} /
                      {{ selectedInvoiceDocument.length }}</span
                    >
                    <v-icon
                      size="17"
                      class="ml-2"
                      :color="
                        currentDocument === selectedInvoiceDocument.length
                          ? 'grey'
                          : 'primary'
                      "
                      @click="
                        currentDocument === selectedInvoiceDocument.length
                          ? {}
                          : nextDocument()
                      "
                    >
                      fa fa-chevron-right
                    </v-icon>
                  </div>
                </div>
                <div v-if="selectedInvoiceDocument.length > 0">
                  <img
                    v-if="imgSrc"
                    :src="imgSrc"
                    alt="image source"
                    style="width: 100%"
                  />
                  <vue-pdf-app
                    v-else-if="pdfSrc"
                    style="height: 100vh"
                    :pdf="pdfSrc"
                  ></vue-pdf-app>
                  <div v-else class="pa-4">
                    <div class="my-4">No preview available</div>
                    <v-btn
                      @click="downloadFile()"
                      rounded="lg"
                      color="blue"
                      variant="outlined"
                      >Download</v-btn
                    >
                  </div>
                </div>
                <div v-else>
                  <p class="text-subtitle-1 font-weight-regular">
                    No document(s) submitted
                  </p>
                </div>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isFetchingFiles"
        scrim="#fff"
      >
        <v-progress-circular color="secondary" indeterminate size="54">
        </v-progress-circular>
      </v-overlay>
      <v-card-actions
        v-if="
          currentInvoice === invoiceDetails.length &&
          !isApprovalHistory &&
          !isGroupApproval
        "
        class="d-flex justify-center"
      >
        <v-btn variant="outlined" rounded="lg" @click="rejectRequest()">
          <i
            class="hr-workflow-task-management-reject text-red pr-1 text-body-1"
          ></i>
          Reject
        </v-btn>
        <v-btn variant="outlined" @click="approveRequest()" rounded="lg"
          ><i
            class="hr-workflow-task-management-approve text-green pr-1 text-body-1"
          ></i
          >Approve
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import VuePdfApp from "vue3-pdf-app";
import "vue3-pdf-app/dist/icons/main.css";
import axios from "axios";
import { checkNullValue } from "@/helper";

export default {
  name: "ViewInvoiceDetails",

  components: { VuePdfApp },
  props: {
    requestId: {
      type: Number,
      required: true,
    },
    approvalType: {
      type: String,
      default: "",
    },
    processInstanceId: {
      type: String,
      default: "",
    },
  },
  emits: [
    "close-modal",
    "reject-task",
    "approve-task",
    "approval-amount-updated",
  ],
  data() {
    return {
      openModal: false,
      isFetchingFiles: false,
      pdfSrc: "",
      imgSrc: "",
      downloadLink: "",
      isLoading: false,
      invoiceDetails: [],
      currentInvoice: 1,
      selectedInvoice: {},
      selectedInvoiceDocument: [],
      showApprovalEditForm: false,
      editedApprovalAmount: null,
      // file
      currentDocument: 1,
      selectedDocumentName: "",
    };
  },
  computed: {
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    formattedFileName() {
      if (this.selectedDocumentName) {
        let fileNameChunks = this.selectedDocumentName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "-";
      }
      return "File Name";
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    isApprovalHistory() {
      return this.approvalType === "Approval History";
    },
    isGroupApproval() {
      return this.approvalType === "Group Approvals";
    },
    approvalAmountRules() {
      var rules = {
        required: (value) => !!value || `Approval amount is required.`,
        min: (value) =>
          parseFloat(value) >= 0 || `Approval amount should not be less than 0`,
        max: (value) =>
          parseFloat(value) <= parseFloat(this.selectedInvoice.Actual_Amount) ||
          `Approval amount should not be greater than invoice amount`,
      };
      return rules;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formUpdateAccess() {
      let projectFormAccess = this.accessRights("50");
      if (
        projectFormAccess &&
        projectFormAccess.accessRights &&
        projectFormAccess.accessRights["update"]
      ) {
        return true;
      } else {
        return false;
      }
    },
  },

  mounted() {
    this.retrieveInvoiceDetails();
  },

  methods: {
    checkNullValue,
    downloadFile() {
      window.open(this.downloadLink, "_blank");
    },
    nextInvoice() {
      this.currentInvoice += 1;
      this.assignSelectedInvoiceDetails();
    },
    prevInvoice() {
      this.currentInvoice -= 1;
      this.assignSelectedInvoiceDetails();
    },
    assignSelectedInvoiceDetails() {
      this.currentDocument = 1;
      this.selectedInvoice = this.invoiceDetails[this.currentInvoice - 1];
      this.selectedInvoiceDocument = this.selectedInvoice
        .Reimbursement_File_Path
        ? this.selectedInvoice.Reimbursement_File_Path
        : [];
      if (this.selectedInvoiceDocument.length > 0) {
        this.assignDocumentDetails();
      }
    },
    nextDocument() {
      this.currentDocument += 1;
      this.assignDocumentDetails();
    },
    prevDocument() {
      this.currentDocument -= 1;
      this.assignDocumentDetails();
    },
    assignDocumentDetails() {
      this.selectedDocumentName =
        this.selectedInvoiceDocument[this.currentDocument - 1].File_Name;
      this.retrieveFileContents();
    },
    onShowApprovalEdit(amount) {
      this.editedApprovalAmount = amount;
      this.showApprovalEditForm = true;
    },
    async onSaveApprovalAmount() {
      let isFormValid = await this.$refs.approvalAmountForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.updateDeductionAmount();
      }
    },
    updateDeductionAmount() {
      let vm = this;
      vm.isLoading = true;
      try {
        axios
          .post(
            vm.baseUrl +
              `payroll/reimbursement/update-deduction/requestId/${vm.requestId}/isVueCall/true`,
            {
              requestId: vm.requestId,
              processInstanceId: vm.processInstanceId,
              lineId: vm.selectedInvoice.Line_Item_Id,
              deductionInvoiceAmount: vm.selectedInvoice.Actual_Amount,
              deduction:
                parseFloat(vm.selectedInvoice.Actual_Amount) -
                parseFloat(vm.editedApprovalAmount),
              payable: this.editedApprovalAmount,
            }
          )
          .then((result) => {
            if (result && result.data && result.data.success) {
              vm.selectedInvoice["Invoice_Amount"] = this.editedApprovalAmount;
              vm.showApprovalEditForm = false;
              let snackbarData = {
                isOpen: true,
                message: "Approval amount updated successfully",
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.$emit("approval-amount-updated");
              vm.isLoading = false;
            } else {
              if (result.data.msg === "Session Expired") {
                vm.isLoading = false;
                vm.$store.dispatch("clearUserLock");
              } else {
                vm.handleUpdateError(result.data.msg);
              }
            }
          })
          .catch((error) => {
            if (error.status == 200) {
              vm.isLoading = false;
              vm.$store.dispatch("clearUserLock");
            } else {
              vm.handleUpdateError();
            }
          });
      } catch {
        vm.handleUpdateError();
      }
    },
    handleUpdateError() {
      let snackbarData = {
        isOpen: true,
        message:
          "Something went wrong while updating the approval amount. Please try after some time.",
        type: "warning",
      };
      this.showAlert(snackbarData);
      this.isLoading = false;
    },
    retrieveInvoiceDetails() {
      let vm = this;
      vm.isLoading = true;
      try {
        axios
          .post(
            vm.baseUrl +
              `payroll/reimbursement/reimbursement-subgrid/requestId/${vm.requestId}`
          )
          .then((result) => {
            if (result && result.data && !result.data.msg) {
              if (result.data.aaData && result.data.aaData.length > 0) {
                this.invoiceDetails = result.data.aaData;
                this.openModal = true;
              } else {
                let snackbarData = {
                  isOpen: true,
                  message: "No invoice details found",
                  type: "warning",
                };
                vm.showAlert(snackbarData);
              }
              vm.assignSelectedInvoiceDetails();
              vm.isLoading = false;
            } else {
              if (result.data.msg === "Session Expired") {
                vm.isLoading = false;
                vm.$store.dispatch("clearUserLock");
              } else {
                vm.handleRetrieveError(result.data.msg);
              }
            }
          })
          .catch((error) => {
            if (error.status == 200) {
              vm.isLoading = false;
              vm.$store.dispatch("clearUserLock");
            } else {
              vm.handleRetrieveError();
            }
          });
      } catch {
        vm.handleRetrieveError();
      }
    },
    handleRetrieveError(msg = "") {
      let snackbarData = {
        isOpen: true,
        message: msg
          ? msg
          : "Something went wrong while retrieving the invoice details. Please try after some time.",
        type: "warning",
      };
      this.showAlert(snackbarData);
      this.isLoading = false;
      this.closeModal();
    },
    closeModal() {
      this.openModal = false;
      this.$emit("close-modal");
    },
    async retrieveFileContents() {
      let vm = this;
      vm.downloadLink = "";
      vm.pdfSrc = "";
      vm.imgSrc = "";
      vm.isFetchingFiles = true;
      let fullFilePath =
        vm.domainName +
        "/" +
        vm.orgCode +
        "/Reimbursement/" +
        vm.selectedDocumentName;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fullFilePath,
          action: "download",
          type: "documents",
        })
        .then((presignedUrl) => {
          vm.downloadLink = presignedUrl;
          let docName = vm.selectedDocumentName
            ? vm.selectedDocumentName.toLowerCase()
            : "";
          if (docName.includes("pdf")) {
            vm.pdfSrc = presignedUrl;
          } else if (
            docName.includes("png") ||
            docName.includes("jpg") ||
            docName.includes("jpeg")
          ) {
            vm.imgSrc = presignedUrl;
          }
          vm.openModal = true;
          vm.isFetchingFiles = false;
        })
        .catch(() => {
          vm.isFetchingFiles = false;
        });
    },
    approveRequest() {
      this.$emit("approve-task");
    },
    rejectRequest() {
      this.$emit("reject-task");
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
