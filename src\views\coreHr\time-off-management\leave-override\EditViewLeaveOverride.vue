<template>
  <v-card class="rounded-lg mb-5">
    <v-card-title class="pa-0">
      <div
        class="d-flex align-center pa-0"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label" v-if="isEdit">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ "Edit" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">
            {{ "Leave Override Details" }}</span
          >
        </div>
        <div class="d-flex align-center pl-4 py-2" v-else>
          <v-avatar class="mr-2" size="40" color="hover" variant="elevated">
            <i class="primary hr-workflow-approval-management text-h6"></i>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              Leave Override Details
            </p>
          </div>
        </div>
        <div class="pa-3 d-flex">
          <v-btn
            color="primary"
            rounded="lg"
            v-if="!isEdit"
            variant="elevated"
            size="small"
            class="mr-5"
            :disabled="
              employeeDetails.encashmentProcessedClosurePending === 'Yes'
            "
            @click="openEditForm()"
          >
            Edit
          </v-btn>
          <v-icon color="primary" @click="$emit('close-split-view')">
            fas fa-times
          </v-icon>
        </div>
      </div>
    </v-card-title>
    <div class="py-3">
      <v-form ref="leaveOverrideForm">
        <v-row
          class="px-5 pt-3 gap-5"
          style="height: calc(100vh - 300px); overflow: scroll"
        >
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Employee Id</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.userDefinedEmpId) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Employee Name</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.employeeName) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Leave Type</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.leaveType) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Leave Taken</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.leavesTaken) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
            v-if="!isEdit"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              Current year leave entitlement
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.currentYearTotalEligibleDays) }}
            </p>
          </v-col>
          <v-col cols="6" sm="12" md="6" v-else>
            <v-text-field
              v-model="currentYearLeaveEntitlement"
              variant="solo"
              type="number"
              :min="0"
              :max="365"
              :rules="[
                numericRequiredValidation(
                  'Current year leave entitlement',
                  currentYearLeaveEntitlement
                ),
                leaveOverrideCarry(
                  'Current year leave entitlement',
                  currentYearLeaveEntitlement
                ),
              ]"
              @update:model-value="onChangeFields()"
              :style="isMobileView ? 'max-width: 180px' : 'max-width: 350px'"
            >
              <template v-slot:label>
                Current year leave entitlement<span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
            v-if="!isEdit"
          >
            <p class="text-subtitle-1 text-grey-darken-1">Carry Over Balance</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.lastCOBalance) }}
            </p>
          </v-col>
          <v-col cols="6" sm="12" md="6" v-else>
            <v-text-field
              type="number"
              :min="0"
              :max="1500"
              v-model="carryOverBalance"
              variant="solo"
              :rules="[
                employeeDetails.carryOver === 'Yes'
                  ? numericRequiredValidation(
                      'Carry Over Balance',
                      carryOverBalance
                    )
                  : true,
                employeeDetails.carryOver === 'Yes'
                  ? leaveOverrideCarry('Carry Over Balance', carryOverBalance)
                  : true,
              ]"
              :disabled="employeeDetails.carryOver === 'Yes' ? false : true"
              :style="isMobileView ? 'max-width: 180px' : 'max-width: 350px'"
              @update:model-value="onChangeFields()"
            >
              <template v-slot:label>
                Carry Over Balance<span
                  style="color: red"
                  v-if="employeeDetails.carryOver === 'Yes'"
                  >*</span
                >
              </template>
            </v-text-field>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Pending Approval</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.totalAppliedLeaveDays) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Designation</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.designationName) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Department</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.departmentName) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Location</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.locationName) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Carry Over</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.carryOver) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Carry Over Accumulation Limit
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.carryOverAccumulationLimit) }}
            </p>
          </v-col>
          <v-col v-if="!isEdit" cols="12" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Reason</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(employeeDetails.leaveOverrideReason) }}
            </p>
          </v-col>
          <v-col v-else cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <v-text-field
              v-model="leaveOverrideReason"
              variant="solo"
              label="Reason"
              :rules="[
                leaveOverrideReason
                  ? validateWithRulesAndReturnMessages(
                      leaveOverrideReason,
                      'departmentDescription',
                      'Reason'
                    )
                  : true,
              ]"
            />
          </v-col>
        </v-row>
        <v-row class="px-3 mt-2">
          <v-col cols="12">
            <v-btn
              v-if="isEdit"
              :disabled="!isFormDirty"
              class="primary"
              style="width: 100%"
              @click="validateAddUpdateForm()"
              >Save</v-btn
            >
          </v-col>
        </v-row>
      </v-form>
    </div>
  </v-card>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="mt-n5 primary"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import { UPDATE_LEAVE_OVERRIDE } from "@/graphql/corehr/employeeDataQueries";
import { checkNullValue } from "@/helper";
import validationRules from "@/mixins/validationRules";
export default {
  name: "ViewLeaveOverride",
  mixins: [validationRules],
  emits: [
    "close-split-view",
    "refetch-list",
    "enable-loader",
    "disable-loader",
  ],
  props: {
    employeeDetails: {
      type: Object,
      required: true,
    },
    isSmallTable: {
      type: Boolean,
      required: false,
    },
  },
  data() {
    return {
      carryOver: 0,
      currentYearLeaveEntitlement: 0,
      carryOverBalance: 0,
      leaveOverrideReason: "",
      viewFormTab: 1,
      isEdit: false,
      isFormDirty: false,
      showValidationAlert: false,
      validationMessages: [],
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  watch: {
    employeeDetails: {
      immediate: true,
      handler(newData) {
        this.currentYearLeaveEntitlement = newData.currentYearTotalEligibleDays;
        this.carryOverBalance = newData.lastCOBalance;
        this.carryOver = newData.carryOver;
        this.leaveOverrideReason = newData.leaveOverrideReason;
      },
    },
  },
  methods: {
    checkNullValue,
    openEditForm() {
      this.isEdit = true;
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    async validateAddUpdateForm() {
      const { valid } = await this.$refs.leaveOverrideForm.validate();
      if (valid) {
        this.updateLeaveOverride();
      }
    },
    updateLeaveOverride() {
      this.$emit("enable-loader");
      const empData = {
        overrideDetails: [
          {
            employeeId: this.employeeDetails.employeeId,
            leaveTypeId: this.employeeDetails.leaveTypeId,
            currentYearEligibleDays: parseFloat(
              this.currentYearLeaveEntitlement
            ),
            coBalance: parseFloat(this.carryOverBalance),
            leaveOverrideReason: this.leaveOverrideReason,
          },
        ],
      };
      this.$apollo
        .query({
          query: UPDATE_LEAVE_OVERRIDE,
          client: "apolloClientJ",
          variables: empData,
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.$emit("disable-loader");
          if (
            res &&
            res.data &&
            res.data.overrideEmployeeLeaves &&
            res.data.overrideEmployeeLeaves.message
          ) {
            const snackbarData = {
              isOpen: true,
              type: "success",
              message: res.data.overrideEmployeeLeaves.message,
            };
            this.$emit("refetch-list");
            this.showAlert(snackbarData);
            this.$emit("close-split-view");
          } else {
            const snackbarData = {
              isOpen: true,
              type: "error",
              message: "Something went wrong try again",
            };
            this.showAlert(snackbarData);
          }
        })
        .catch((err) => {
          this.$emit("disable-loader");
          this.handleAddUpdateError(err);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoadingCard = false;

      let validationMessages = [];

      // Extract and handle errors from the API response
      if (err?.graphQLErrors?.length) {
        err.graphQLErrors.forEach((error) => {
          const errorMessage = error.message;
          validationMessages.push(errorMessage);
        });
      } else {
        // Fallback for unexpected errors
        const fallbackMessage = err?.message;
        validationMessages.push(fallbackMessage);
      }

      // Assign validation messages and show alert
      this.validationMessages = validationMessages;
      this.showValidationAlert = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
