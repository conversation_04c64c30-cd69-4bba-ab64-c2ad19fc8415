<template>
  <div>
    <v-card class="rounded-lg mt-4 common-box-shadow">
      <v-card-text>
        <v-row align="center">
          <v-col
            cols="12"
            lg="2"
            md="3"
            sm="4"
            xs="6"
            class="d-flex align-center pl-5"
          >
            <v-icon class="pr-4" color="primary" @click="backToList()"
              >fas fa-chevron-left
            </v-icon>
            <v-list lines="two">
              <v-list-item
                :key="selectedItem.userDefinedEmpId + 'empId'"
                :title="selectedItem.employeeName"
                :subtitle="selectedItem.userDefinedEmpId"
              >
              </v-list-item>
            </v-list>
          </v-col>
          <v-col cols="12" lg="3" md="3" sm="4" xs="6">
            <v-card class="pa-2 rounded-lg common-box-shadow">
              <v-list lines="two">
                <v-list-item
                  :key="settlementDetails.noticeDate + 'noticeDate'"
                  title="Applied Date"
                  :subtitle="formatDate(settlementDetails.noticeDate)"
                >
                  <template v-slot:prepend>
                    <v-avatar size="40" color="green-lighten-4">
                      <v-icon size="20" color="green"
                        >far fa-calendar-alt</v-icon
                      >
                    </v-avatar>
                  </template>
                </v-list-item>
              </v-list>
            </v-card>
          </v-col>
          <v-col cols="12" lg="3" md="3" sm="4" xs="6">
            <v-card class="pa-2 rounded-lg common-box-shadow">
              <v-list lines="two">
                <v-list-item
                  :key="settlementDetails.resignationDate + 'resignationDate'"
                  title="Exit Date"
                  :subtitle="formatDate(settlementDetails.resignationDate)"
                >
                  <template v-slot:prepend>
                    <v-avatar size="40" color="purple-lighten-4">
                      <v-icon size="20" color="purple"
                        >far fa-calendar-alt</v-icon
                      >
                    </v-avatar>
                  </template>
                </v-list-item>
              </v-list>
            </v-card>
          </v-col>
          <v-col cols="12" md="3" sm="4">
            <v-card class="pa-2 rounded-lg common-box-shadow">
              <v-list lines="two">
                <v-list-item
                  :key="settlementDetails.relievingReason + 'reason'"
                  title="Relieving Reason"
                  :subtitle="
                    settlementDetails.relievingReason
                      ? settlementDetails.relievingReason
                      : '-'
                  "
                >
                  <template v-slot:prepend>
                    <v-avatar size="40" color="blue-lighten-4">
                      <v-icon size="20" color="blue">far fa-sticky-note</v-icon>
                    </v-avatar>
                  </template>
                </v-list-item>
              </v-list>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <div>
      <v-card
        class="rounded-lg mt-4"
        min-height="300"
        style="
          height: calc(100vh - 310px);
          overflow: hidden;
          overflow-y: scroll;
        "
      >
        <v-card-text class="mb-6">
          <v-form ref="settlementEditForm">
            <v-row>
              <v-col
                v-if="
                  !isEdit &&
                  selectedItem.settlementStatus === 'Pending Approval' &&
                  accessRights.update &&
                  restrictAccess
                "
                cols="12"
                class="d-flex justify-end mb-n10"
                ><v-icon color="primary" size="20" @click="openEditForm()"
                  >fas fa-edit</v-icon
                ></v-col
              >
              <v-col v-if="earningsList.length > 0" cols="12">
                <div class="d-flex align-center mb-4">
                  <v-progress-circular
                    model-value="100"
                    color="green-darken-2"
                    size="20"
                  ></v-progress-circular>
                  <div class="pl-1 text-h6 font-weight-bold">Earnings</div>
                </div>

                <v-row class="pl-6">
                  <v-col
                    cols="12"
                    lg="4"
                    sm="6"
                    class="pa-2"
                    v-for="(earning, index) of earningsList"
                    :key="earning.Settlement_Type + index"
                  >
                    <v-card
                      class="rounded-lg pa-2 pl-6 common-box-shadow"
                      height="100%"
                      style="border-left: 5px solid #8bc34a"
                    >
                      <v-row>
                        <v-col cols="12" :sm="isEdit ? 5 : 6">
                          <div class="text-subtitle-1 font-weight-medium">
                            {{
                              payrollCountry?.toLowerCase() === "th" &&
                              earning.Settlement_Type?.toLowerCase() ===
                                "notice pay"
                                ? "Severance Pay"
                                : earning.Settlement_Type
                            }}
                          </div>
                          <div class="value-text">
                            {{
                              "₹ " +
                              (
                                earning.Periodical_Amount * earning.Multiply_By
                              ).toFixed(2)
                            }}
                          </div>
                        </v-col>
                        <v-col cols="12" :sm="isEdit ? 7 : 6">
                          <div
                            v-if="!isEdit"
                            class="text-subtitle-1 font-weight-medium"
                          >
                            {{ earning.Multiply_By }}
                            <span class="value-text"> day(s)</span>
                          </div>
                          <v-text-field
                            v-else
                            v-model="earning.Multiply_By"
                            density="compact"
                            variant="outlined"
                            suffix="day(s)"
                            :class="isMobileView ? '' : 'mt-2'"
                            type="number"
                            :rules="[
                              floatMinRules(earning.Multiply_By),
                              floatMaxRules(
                                earning.Multiply_By,
                                earningsListBackup[index].Multiply_By
                              ),
                            ]"
                          ></v-text-field>
                          <div
                            class="value-text cursor-pointer"
                            @click="
                              onOpenMoreDetailsModal(earning.Settlement_Type)
                            "
                          >
                            <span
                              style="text-decoration: underline"
                              class="text-primary"
                              >More Details</span
                            >
                          </div>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                  <v-col cols="12" lg="4" sm="6" class="pa-2">
                    <v-card
                      class="rounded-lg pa-3 pl-6 common-box-shadow"
                      height="100%"
                      style="border-left: 5px solid #8bc34a"
                    >
                      <v-row>
                        <v-col cols="12">
                          <div class="text-subtitle-1 font-weight-medium">
                            Bonus
                          </div>
                          <div
                            class="value-text cursor-pointer"
                            @click="redirectToForm('bonus')"
                          >
                            <span
                              style="text-decoration: underline"
                              class="text-primary"
                              >Add/Update Bonus</span
                            >
                          </div>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                  <v-col cols="12" lg="4" sm="6" class="pa-2">
                    <v-card
                      class="rounded-lg pa-3 pl-6 common-box-shadow"
                      height="100%"
                      style="border-left: 5px solid #8bc34a"
                    >
                      <v-row>
                        <v-col cols="12">
                          <div class="text-subtitle-1 font-weight-medium">
                            Commission
                          </div>
                          <div
                            class="value-text cursor-pointer"
                            @click="redirectToForm('commission')"
                          >
                            <span
                              style="text-decoration: underline"
                              class="text-primary"
                              >Add/Update Commission</span
                            >
                          </div>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>

              <v-col v-if="deductionList.length > 0" cols="12">
                <div class="d-flex align-center mb-4">
                  <v-progress-circular
                    model-value="100"
                    color="red"
                    size="20"
                  ></v-progress-circular>
                  <div class="pl-1 text-h6 font-weight-bold">Deductions</div>
                </div>
                <v-row class="pl-6">
                  <v-col
                    cols="12"
                    lg="4"
                    sm="6"
                    class="pa-2"
                    v-for="(deductions, index) of deductionList"
                    :key="deductions.Settlement_Type + index"
                  >
                    <v-card
                      class="rounded-lg pa-2 pl-6 common-box-shadow"
                      height="100%"
                      :style="`border-left: 5px solid #FF5722`"
                    >
                      <v-row>
                        <v-col cols="12" :sm="isEdit ? 5 : 6">
                          <div class="text-subtitle-1 font-weight-medium">
                            {{
                              payrollCountry?.toLowerCase() === "th" &&
                              deductions.Settlement_Type?.toLowerCase() ===
                                "notice pay"
                                ? "Severance Pay"
                                : deductions.Settlement_Type
                            }}
                          </div>
                          <div class="value-text">
                            {{
                              "₹ " +
                              (
                                deductions.Periodical_Amount *
                                deductions.Multiply_By
                              ).toFixed(2)
                            }}
                          </div>
                        </v-col>
                        <v-col cols="12" :sm="isEdit ? 7 : 6">
                          <div
                            v-if="!isEdit"
                            class="text-subtitle-1 font-weight-medium"
                          >
                            {{ deductions.Multiply_By }}
                            <span class="value-text"> day(s)</span>
                          </div>
                          <v-text-field
                            v-else
                            v-model="deductions.Multiply_By"
                            density="compact"
                            variant="outlined"
                            suffix="day(s)"
                            :class="isMobileView ? '' : 'mt-2'"
                            type="number"
                            :rules="[
                              floatMinRules(deductions.Multiply_By),
                              floatMaxRules(
                                deductions.Multiply_By,
                                deductionListBackup[index].Multiply_By
                              ),
                            ]"
                          ></v-text-field>
                          <div
                            v-if="
                              deductions.Settlement_Type === 'Bond Recovery'
                            "
                            class="text-caption text-blue"
                          >
                            {{
                              convertDaysToYearMonthsDays(
                                deductions.Multiply_By
                              )
                            }}
                          </div>
                          <div
                            class="value-text cursor-pointer"
                            @click="
                              onOpenMoreDetailsModal(deductions.Settlement_Type)
                            "
                          >
                            <span
                              style="text-decoration: underline"
                              class="text-primary"
                              >More Details</span
                            >
                          </div>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </v-card>
    </div>
    <v-bottom-navigation v-if="openBottomSheet">
      <v-sheet
        class="align-center text-center"
        :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
        style="width: 100%"
      >
        <v-row justify="center">
          <v-col cols="6" class="d-flex justify-start pl-2">
            <v-btn
              rounded="lg"
              variant="outlined"
              size="small"
              style="height: 40px; margin-top: 10px"
              @click="closeForm()"
              ><span class="primary">Cancel</span></v-btn
            >
          </v-col>
          <v-col cols="6" class="d-flex justify-end pr-4">
            <v-btn
              rounded="lg"
              size="small"
              class="mr-1"
              color="primary"
              variant="elevated"
              :dense="isMobileView"
              style="height: 40px; margin-top: 10px"
              @click="validateEditForm('Pending Approval')"
            >
              <span class="primary">Save As Draft</span>
            </v-btn>
            <v-btn
              rounded="lg"
              size="small"
              class="mr-10"
              color="primary"
              variant="elevated"
              :dense="isMobileView"
              style="height: 40px; margin-top: 10px"
              @click="validateEditForm('approve')"
            >
              <span class="primary">Approve</span>
            </v-btn>
          </v-col>
        </v-row>
      </v-sheet>
    </v-bottom-navigation>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      v-model="openMoreDetailsModal"
      width="550px"
      @click:outside="openMoreDetailsModal = false"
    >
      <v-card class="rounded-lg mt-4">
        <div class="d-flex justify-end">
          <v-icon
            color="primary"
            class="pr-4 pt-4 font-weight-bold"
            @click="openMoreDetailsModal = false"
            >fas fa-times</v-icon
          >
        </div>
        <v-card-title>
          <div class="pl-1 text-h6 font-weight-bold mt-n4">
            {{
              payrollCountry?.toLowerCase() === "th" &&
              selectedMoreDetailType?.toLowerCase() === "notice pay"
                ? "Severance Pay"
                : selectedMoreDetailType
            }}
          </div>
        </v-card-title>
        <v-card-text
          class="mb-6"
          style="
            height: calc(100vh - 410px);
            overflow: hidden;
            overflow-y: scroll;
          "
        >
          <div v-if="selectedMoreDetailType === 'Leave Encashment'">
            <div v-if="settlementMoreDetails.leaveEncashmentApplicable">
              <v-card
                v-for="(
                  moreEarnings, index
                ) of settlementMoreDetails.leaveEncashmentDetails"
                :key="'leave-encashment-more-details-' + index"
                class="ma-4 common-box-shadow"
              >
                <v-row class="pa-2">
                  <v-col cols="12" class="my-2 pa-0 pl-4">
                    <div
                      class="text-subtitle-2 font-weight-medium text-primary"
                    >
                      {{ moreEarnings.Leave_Name }}
                    </div>
                  </v-col>
                  <v-col cols="6" class="my-2 pa-0 pl-4"
                    ><div class="text-subtitle-2 font-weight-medium">
                      Current Leave Eligibility
                    </div></v-col
                  >
                  <v-col cols="6" class="pa-0 pl-4">
                    <div class="ma-0 value-text">
                      {{ moreEarnings.Exit_Date_Current_Eligibility }}
                    </div></v-col
                  >
                  <v-col cols="6" class="mb-2 pa-0 pl-4"
                    ><div class="text-subtitle-2 font-weight-medium">
                      Leave Taken
                    </div></v-col
                  >
                  <v-col cols="6" class="pa-0 pl-4">
                    <div class="ma-0 value-text">
                      {{ moreEarnings.Leaves_Taken }}
                    </div></v-col
                  >
                  <v-col cols="6" class="mb-2 pa-0 pl-4"
                    ><div class="text-subtitle-2 font-weight-medium">
                      Encashed Days
                    </div></v-col
                  >
                  <v-col cols="6" class="pa-0 pl-4">
                    <div class="ma-0 value-text">
                      {{
                        moreEarnings.Exit_Date_Remaining_Days
                          ? moreEarnings.Exit_Date_Remaining_Days
                          : 0
                      }}
                    </div></v-col
                  >
                </v-row>
              </v-card>
            </div>
            <div v-else>
              <AppFetchErrorScreen
                key="no-results-screen"
                main-title="There are no leave types applicable for leave encashment"
                :isSmallImage="true"
                mainIcon="fas fa-money-check-alt"
              ></AppFetchErrorScreen>
            </div>
          </div>
          <div v-if="selectedMoreDetailType === 'Compoff Encashment'">
            <div v-if="settlementMoreDetails.compOffEncashmentApplicable">
              <v-card class="ma-4 common-box-shadow">
                <v-row class="pa-2">
                  <v-col cols="6" class="my-2 pa-0 pl-4"
                    ><div class="text-subtitle-2 font-weight-medium">
                      Encashment Amount
                    </div></v-col
                  >
                  <v-col cols="6" class="my-2 pa-0 pl-4">
                    <div class="ma-0 value-text">
                      {{ settlementMoreDetails.compOffEncashmentAmount }}
                    </div></v-col
                  >
                  <v-col cols="6" class="mb-2 pa-0 pl-4"
                    ><div class="text-subtitle-2 font-weight-medium">
                      Remaining Days
                    </div></v-col
                  >
                  <v-col cols="6" class="pa-0 pl-4">
                    <div class="ma-0 value-text">
                      {{ settlementMoreDetails.compOffRemainingDays }}
                    </div></v-col
                  >
                </v-row>
              </v-card>
            </div>
            <div v-else>
              <AppFetchErrorScreen
                key="no-results-screen"
                main-title="Employee not applicable for compoff encashment"
                :isSmallImage="true"
                mainIcon="fas fa-money-check-alt"
              ></AppFetchErrorScreen>
            </div>
          </div>
          <div v-if="selectedMoreDetailType.includes('Leave Deduction')">
            <div v-if="settlementMoreDetails.leaveDeductionApplicable">
              <v-card
                v-for="(
                  moreDeductions, index
                ) of settlementMoreDetails.leaveDeductionDetails"
                :key="'leave-deduct-more-details-' + index"
                class="ma-4 common-box-shadow"
              >
                <v-row class="pa-2">
                  <v-col cols="12" class="my-2 pa-0 pl-4">
                    <div
                      class="text-subtitle-2 font-weight-medium text-primary"
                    >
                      {{ moreDeductions.Leave_Name }}
                    </div>
                  </v-col>
                  <v-col cols="6" class="my-2 pa-0 pl-4"
                    ><div class="text-subtitle-2 font-weight-medium">
                      Current Leave Eligibility
                    </div></v-col
                  >
                  <v-col cols="6" class="pa-0 pl-4">
                    <div class="ma-0 value-text">
                      {{ moreDeductions.Exit_Date_Current_Eligibility }}
                    </div></v-col
                  >
                  <v-col cols="6" class="mb-2 pa-0 pl-4"
                    ><div class="text-subtitle-2 font-weight-medium">
                      Leave Taken
                    </div></v-col
                  >
                  <v-col cols="6" class="pa-0 pl-4">
                    <div class="ma-0 value-text">
                      {{ moreDeductions.Leaves_Taken }}
                    </div></v-col
                  >
                  <v-col cols="6" class="mb-2 pa-0 pl-4"
                    ><div class="text-subtitle-2 font-weight-medium">
                      Deduction Days
                    </div></v-col
                  >
                  <v-col cols="6" class="pa-0 pl-4">
                    <div class="ma-0 value-text">
                      {{
                        moreDeductions.Exit_Date_Deduction_Days
                          ? moreDeductions.Exit_Date_Deduction_Days
                          : 0
                      }}
                    </div></v-col
                  >
                </v-row>
              </v-card>
            </div>
            <div v-else>
              <AppFetchErrorScreen
                key="no-results-screen"
                main-title="There are no leave types applicable for paid leave deduction"
                :isSmallImage="true"
                mainIcon="fas fa-money-check-alt"
              ></AppFetchErrorScreen>
            </div>
          </div>
          <div v-if="selectedMoreDetailType === 'Notice Pay'">
            <div v-if="settlementMoreDetails.employeeHoldsNoticePay">
              <v-row class="pa-2">
                <v-col cols="6" class="my-2 pa-0 pl-4"
                  ><div class="text-subtitle-2 font-weight-medium">
                    {{
                      payrollCountry?.toLowerCase() === "th"
                        ? "Severance Pay Amount"
                        : "Notice Pay Amount"
                    }}
                  </div></v-col
                >
                <v-col cols="6" class="my-2 pa-0 pl-4">
                  <div class="ma-0 value-text">
                    {{ settlementMoreDetails.noticePayAmount }}
                  </div></v-col
                ><v-col cols="6" class="mb-2 pa-0 pl-4">
                  <div class="text-subtitle-2 font-weight-medium">
                    {{
                      payrollCountry?.toLowerCase() === "th"
                        ? "Severance Pay"
                        : "Notice Period"
                    }}
                  </div>
                </v-col>

                <v-col cols="6" class="pa-0 pl-4">
                  <div class="ma-0 value-text">
                    {{ settlementMoreDetails.empTotalNoticePeriod }} day(s)
                  </div></v-col
                >
                <v-col cols="6" class="mb-2 pa-0 pl-4"
                  ><div class="text-subtitle-2 font-weight-medium">
                    Remaining Days
                  </div></v-col
                >
                <v-col cols="6" class="pa-0 pl-4">
                  <div class="ma-0 value-text">
                    {{ settlementMoreDetails.noticePayRemainingDays }}
                  </div></v-col
                >
              </v-row>
            </div>
            <div v-else>
              <AppFetchErrorScreen
                key="no-results-screen"
                main-title="Employee not applicable for notice pay"
                :isSmallImage="true"
                mainIcon="fas fa-money-check-alt"
              ></AppFetchErrorScreen>
            </div>
          </div>
          <div v-if="selectedMoreDetailType === 'Bond Recovery'">
            <div v-if="settlementMoreDetails.bondRecoveryApplicable">
              <v-row class="pa-2">
                <v-col cols="6" class="my-2 pa-0 pl-4"
                  ><div class="text-subtitle-2 font-weight-medium">
                    Bond Value
                  </div></v-col
                >
                <v-col cols="6" class="my-2 pa-0 pl-4">
                  <div class="ma-0 value-text">
                    {{ settlementMoreDetails.totalBondValue }}
                  </div></v-col
                >
                <v-col cols="6" class="mb-2 pa-0 pl-4"
                  ><div class="text-subtitle-2 font-weight-medium">
                    Bond Period
                  </div></v-col
                >
                <v-col cols="6" class="pa-0 pl-4">
                  <div class="ma-0 value-text">
                    {{ settlementMoreDetails.minimumMonthsToBeServed }}
                    month(s)
                  </div></v-col
                >
                <v-col cols="6" class="mb-2 pa-0 pl-4"
                  ><div class="text-subtitle-2 font-weight-medium">
                    Date of Join
                  </div></v-col
                >
                <v-col cols="6" class="pa-0 pl-4">
                  <div class="ma-0 value-text">
                    {{ formatDate(settlementMoreDetails.dateOfJoin) }}
                  </div></v-col
                >
                <v-col cols="6" class="mb-2 pa-0 pl-4"
                  ><div class="text-subtitle-2 font-weight-medium">
                    Remaining Months
                  </div></v-col
                >
                <v-col cols="6" class="pa-0 pl-4">
                  <div class="ma-0 value-text">
                    {{
                      convertDaysToYearMonthsDays(
                        settlementMoreDetails.bondRecoveryRemainingDays
                      )
                    }}
                  </div></v-col
                >
                <v-col cols="6" class="mb-2 pa-0 pl-4"
                  ><div class="text-subtitle-2 font-weight-medium">
                    Bond Recovery Amount
                  </div></v-col
                >
                <v-col cols="6" class="pa-0 pl-4">
                  <div class="ma-0 value-text">
                    {{ settlementMoreDetails.bondRecoveryAmount }}
                  </div></v-col
                >
              </v-row>
            </div>
            <div v-else>
              <AppFetchErrorScreen
                key="no-results-screen"
                main-title="Employee not applicable for bond recovery"
                :isSmallImage="true"
                mainIcon="fas fa-money-check-alt"
              ></AppFetchErrorScreen>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <AppWarningModal
      v-if="showApproveSuccessModal"
      :open-modal="showApproveSuccessModal"
      iconName="hr-workflow-task-management-approve"
      iconColor="green"
      confirmationHeading="Settlement status updated successfully!"
      closeButtonText=""
      acceptButtonText=""
      @close-warning-modal="closeApproveSuccessModal()"
    >
      <template #warningModalContent>
        <div style="display: inline-block" class="text-center">
          Please generate the
          <a
            class="text-primary px-1"
            :href="baseUrl + 'payroll/salary-payslip'"
            >payslip</a
          >
          to conclude the full & final settlement
        </div>
      </template>
    </AppWarningModal>
  </div>
</template>

<script>
import moment from "moment";
import { defineComponent } from "vue";
import { APPROVE_SETTLEMENT } from "@/graphql/payroll/fullAndFinalSettlementQueries.js";
import { getErrorCodes, convertDaysToYearMonthsDays } from "@/helper.js";
import axios from "axios";

export default defineComponent({
  name: "ViewEditSettlements",

  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    settlementDetails: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
    restrictAccess: {
      type: Number,
      required: true,
    },
    selectedPayrollMonth: {
      type: Number,
      required: true,
    },
    selectedPayrollYear: {
      type: Number,
      required: true,
    },
  },

  data: () => ({
    isFormDirty: false,
    noticePayType: "",
    bondRecoveryType: "",
    earningsList: [],
    earningsListBackup: [],
    deductionList: [],
    deductionListBackup: [],
    isEdit: false,
    openBottomSheet: false,
    isLoading: false,
    showApproveSuccessModal: false,
    // more
    openMoreDetailsModal: false,
    selectedMoreDetailType: "",
    settlementMoreDetails: {},
  }),

  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    payrollCountry() {
      return this.$store.state.payrollCountry;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return moment(date).format(orgDateFormat);
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },

  mounted() {
    const { earningsDeductions } = this.settlementDetails;
    let earningsList = [],
      deductionList = [];
    for (let item of earningsDeductions) {
      if (item.Mode === "Earnings") {
        earningsList.push(item);
      }
      if (item.Mode === "Deductions") {
        deductionList.push(item);
      }
    }
    this.earningsList = earningsList;
    this.earningsListBackup = JSON.parse(JSON.stringify(earningsList));
    this.deductionList = deductionList;
    this.deductionListBackup = JSON.parse(JSON.stringify(deductionList));
  },

  methods: {
    convertDaysToYearMonthsDays,
    redirectToForm(form) {
      window.open(this.baseUrl + "payroll/" + form);
    },
    floatMaxRules(value, maxVal) {
      return (
        parseFloat(value) <= maxVal ||
        `The value should be less than or equal to ${maxVal}`
      );
    },

    floatMinRules(value) {
      return (
        parseFloat(value) >= 0 ||
        `The value should be greater than or equal to 0`
      );
    },

    backToList() {
      this.openBottomSheet = false;
      this.isEdit = false;
      this.$emit("back-to-list");
    },

    closeForm() {
      this.earningsList = JSON.parse(JSON.stringify(this.earningsListBackup));
      this.deductionList = JSON.parse(JSON.stringify(this.deductionListBackup));
      this.openBottomSheet = false;
      this.isEdit = false;
    },

    openEditForm() {
      this.isEdit = true;
      this.openBottomSheet = true;
    },

    async validateEditForm(type) {
      let isFormValid = await this.$refs.settlementEditForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.onApproveSettlement(type);
      }
    },

    onApproveSettlement(type) {
      let vm = this;
      vm.isLoading = true;
      let earningsAndDeductions = vm.earningsList.concat(vm.deductionList);
      let earnDeductArray = [];
      for (let earnDeduct of earningsAndDeductions) {
        earnDeductArray.push({
          Amount: parseFloat(
            (earnDeduct.Periodical_Amount * earnDeduct.Multiply_By).toFixed(2)
          ),
          Calculate_By: earnDeduct.Calculate_By,
          Employee_Id: earnDeduct.Employee_Id,
          Mode: earnDeduct.Mode,
          Multiply_By: parseFloat(earnDeduct.Multiply_By),
          Periodical_Amount: earnDeduct.Periodical_Amount,
          Settlement_Type: earnDeduct.Settlement_Type,
        });
      }
      vm.$apollo
        .mutate({
          mutation: APPROVE_SETTLEMENT,
          variables: {
            isEdit: type === "Pending Approval" ? 1 : 0,
            earningsAndDeductions: earnDeductArray,
            employeeId: vm.selectedItem.employeeId,
          },
          client: "apolloClientAB",
        })
        .then(() => {
          vm.isLoading = false;
          if (type === "Pending Approval") {
            let snackbarData = {
              isOpen: true,
              message: "Full & final settlement details updated successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            vm.closeForm();
            vm.$emit("refetch-list");
          } else {
            vm.showApproveSuccessModal = true;
          }
        })
        .catch((err) => {
          vm.handleApproveError(err);
        });
    },

    closeApproveSuccessModal() {
      this.showApproveSuccessModal = false;
      this.closeForm();
      this.$emit("refetch-list");
    },

    onOpenMoreDetailsModal(type) {
      this.selectedMoreDetailType = type;
      this.getMoreDetailsOfSettlement();
    },

    getMoreDetailsOfSettlement() {
      if (
        !this.settlementMoreDetails ||
        Object.keys(this.settlementMoreDetails).length === 0
      ) {
        let vm = this;
        vm.isLoading = true;
        try {
          axios
            .post(
              vm.baseUrl +
                "payroll/salary-payslip/get-employee-settlement-details",
              {
                loginEmployeeId: vm.loginEmployeeId,
                employeeDetails: [
                  {
                    employeeId: vm.selectedItem.employeeId,
                    noticeDate: vm.settlementDetails.noticeDate,
                    resignationDate: vm.settlementDetails.resignationDate,
                  },
                ],
                salaryMonth: vm.selectedPayrollMonth,
                salaryYear: vm.selectedPayrollYear,
              }
            )
            .then((result) => {
              if (result.data && result.data.success) {
                const { settlementDetails } = result.data.settlementDetails;
                if (settlementDetails && settlementDetails.length > 0) {
                  vm.settlementMoreDetails = settlementDetails[0];
                  vm.openMoreDetailsModal = true;
                } else {
                  let snackbarData = {
                    isOpen: true,
                    message:
                      "Something went wrong while retrieving the settlement details. Please try after some time.",
                    type: "warning",
                  };
                  vm.showAlert(snackbarData);
                }
              } else {
                if (result.data.msg === "Session Expired") {
                  vm.$store.dispatch("clearUserLock");
                } else {
                  let snackbarData = {
                    isOpen: true,
                    message: result.data.msg,
                    type: "warning",
                  };
                  vm.showAlert(snackbarData);
                }
              }
              vm.isLoading = false;
            })
            .catch((error) => {
              if (error.status == 200) {
                vm.$store.dispatch("clearUserLock");
              } else {
                let snackbarData = {
                  isOpen: true,
                  message:
                    "Something went wrong while retrieving the settlement details. Please try after some time.",
                  type: "warning",
                };
                vm.showAlert(snackbarData);
              }
              vm.isLoading = false;
            });
        } catch (e) {
          let snackbarData = {
            isOpen: true,
            message:
              "Something went wrong while retrieving the settlement details. Please try after some time.",
            type: "warning",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
        }
      } else {
        this.openMoreDetailsModal = true;
      }
    },

    handleApproveError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "PFF0010": // Login employee is not allowed to initiate the full and final settlement.
          case "_DB0114": // This employee does not have admin or manager access.
            snackbarData.message =
              "Sorry, you don't have access rights to approve the settlement. Please contact the HR administrator.";
            break;
          case "IVE0000": // Invalid input request.
          case "IVE0299": // Earnings and deductions is required
          case "_EC0007": // invalid inputs
            snackbarData.message =
              "Please retry as the input request was not received as expected. If you encounter this message repeatedly, kindly reach out to the platform administrator.";
            break;
          case "_EC0006": // Record(s) are already deleted in the same or some other user session.
            snackbarData.message =
              "Unable to approve the settlement as it was deleted already in the same or some other user session";
            this.$emit("refetch-list");
            break;
          case "PFF0004": // Login employee is not eligible to add or update or delete the full and final settlement.
            snackbarData.message =
              "You are unable to approve your own record. Please contact the platform administrator for further assistance.";
            break;
          case "SGE0105": // Organization details does not exists.
            snackbarData.message =
              "Something went wrong while fetching the organization details when approving the settlement details. Please try after some time.";
            break;
          case "SGE0112": // Error while getting the restrict financial access for manager flag.
            snackbarData.message =
              "Something went wrong while getting the restrict financial access when approving the settlement details. Please try after some time.";
            break;
          case "PFF0008": // Error while approving the full and final settlement.
          case "PFF0105": // Error while processing the request to approve the full and final settlement.
          case "_UH0001": // unhandled error
          default:
            snackbarData.message =
              "Something went wrong while approving the settlement. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while approving the settlement. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
