import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const LIST_SHIFT_SWAPPING = gql`
  query listshiftSwapping(
    $formId: Int!
    $employeeId: Int
    $swapIdList: [Int]
    $alternateSwapDate: String
  ) {
    listshiftSwapping(
      formId: $formId
      employeeId: $employeeId
      swapIdList: $swapIdList
      alternateSwapDate: $alternateSwapDate
    ) {
      errorCode
      message
      shiftSwapping {
        Swap_Id
        Employee_Id
        Approver_Id
        Swap_Shift_Type_Id
        Swap_Date
        Reason
        Approval_Status
        Added_On
        Added_By
        Updated_On
        Updated_By
        Shift_Name
        Employee_Name
        Added_By_Name
        Updated_By_Name
        Approver_Name
        User_Defined_EmpId
        Current_Shift_Type_Id
        Current_Shift_Name
      }
    }
  }
`;

export const REJECT_SHIFT_SWAP = gql`
  mutation rejectShiftSwap($swapId: Int!) {
    rejectShiftSwap(swapId: $swapId) {
      errorCode
      message
    }
  }
`;

export const EVALUATE_SHIFT_SWAP_STATUS = gql`
  mutation evaluateAndProcessShiftSwapStatus($swapId: Int!) {
    evaluateAndProcessShiftSwapStatus(swapId: $swapId) {
      errorCode
      message
      matchingSwapIds
    }
  }
`;

export const APPROVE_MATCHING_SWAP_REQUEST = gql`
  mutation approveMathchingSwapRequest(
    $Swap_ID: Int!
    $Matching_Swap_ID: Int!
  ) {
    approveMathchingSwapRequest(
      Swap_ID: $Swap_ID
      Matching_Swap_ID: $Matching_Swap_ID
    ) {
      errorCode
      message
    }
  }
`;
