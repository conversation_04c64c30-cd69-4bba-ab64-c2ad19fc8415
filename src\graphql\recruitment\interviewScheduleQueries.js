import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_INTERVIEW_ROUNDS_LIST = gql`
  query CommentQuery($jobpostId: Int!) {
    listJobPostRounds(jobpostId: $jobpostId) {
      errorCode
      message
      jobpostRounds {
        Round_Id
        Round_Name
        Maximum_Candidates
        Minimum_Panel_Members
        Maximum_Panel_members
      }
    }
  }
`;

export const GET_PANEL_MEMBERS_LIST = gql`
  query getRoleBasedJobPostMember($jobPostId: Int!, $formId: Int) {
    getRoleBasedJobPostMember(jobPostId: $jobPostId, formId: $formId) {
      message
      errorCode
      panelMembers {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
        Emp_Email
        Microsoft_Email
      }
      recruiters {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
        Emp_Email
        Microsoft_Email
      }
      hiringManager {
        Employee_Id
        Emp_Name
        User_Defined_EmpId
        Emp_Email
        Microsoft_Email
      }
    }
  }
`;

export const RETRIEVE_INTERVIEW_DETAILS = gql`
  query CommentQuery($interviewId: Int!, $employeeId: Int!, $formName: String) {
    retrieveInterview(
      interviewId: $interviewId
      employeeId: $employeeId
      formName: $formName
    ) {
      errorCode
      message
      interviewInfo {
        Interview_Id
        Interview_Name
        Venue
        Calendar_Link
        Assessment_Link
        Additional_Notes
        Start_Date
        End_Date
        Total_Vacancy
        Interview_Image
        Interview_Status
        Status_Id
        Job_Post_Id
        Job_Post_Name
        Latitude
        Longitude
        Calendar_Type
        Meeting_Type
        Online_Meeting_Id
        CandidatesData {
          Candidate_Id
          Candidate_Name
          Round_Status
          Panel_Score
          Is_Invite_Sent
        }
        rounds {
          Round_Id
          Round_Name
          Round_Start_Date_Time
          Round_End_Date_Time
          Round_Status
          Panel_Accepted_Invite_Count
          Round_Sequence
          panelMembers {
            Panel_Member_Id
            Panel_Member_Name
            Panel_Member_Invite_Status
            Is_Invite_Sent
          }
        }
      }
    }
  }
`;

export const GET_PANEL_MEMBERS_SCHEDULE = gql`
  query getMicrosoftCalendarEvent(
    $panelMembers: [Int!]!
    $startDateTime: String!
    $endDateTime: String!
    $formId: Int!
  ) {
    getMicrosoftCalendarEvent(
      panelMembers: $panelMembers
      startDateTime: $startDateTime
      endDateTime: $endDateTime
      formId: $formId
    ) {
      errorCode
      message
      validationError
      data
    }
  }
`;

export const GENERATE_MEETING_URL = gql`
  query getMicrosoftOnlineMeeting(
    $action: String!
    $panelMembers: [Int!]!
    $startDateTime: String!
    $endDateTime: String!
    $formId: Int!
    $onlineMeetingId: String
  ) {
    getMicrosoftOnlineMeeting(
      action: $action
      panelMembers: $panelMembers
      startDateTime: $startDateTime
      endDateTime: $endDateTime
      formId: $formId
      onlineMeetingId: $onlineMeetingId
    ) {
      errorCode
      message
      validationError
      data
    }
  }
`;

export const GET_PANEL_INTERNAL_SCHEDULE = gql`
  query getPanelMemberScheduledInterview(
    $panelMembers: [Int!]!
    $startDateTime: String!
    $endDateTime: String!
    $formId: Int!
  ) {
    getPanelMemberScheduledInterview(
      panelMembers: $panelMembers
      startDateTime: $startDateTime
      endDateTime: $endDateTime
      formId: $formId
    ) {
      errorCode
      message
      validationError
      data
    }
  }
`;

// ===============
// Mutations
// ===============

export const ADD_INTERVIEW_DETAILS = gql`
  mutation (
    $interviewName: String!
    $formId: Int!
    $jobPostId: Int!
    $venue: String!
    $calendarLink: String
    $assessmentLink: String
    $additionalNotes: String
    $startDate: Date
    $endDate: Date
    $status: Int
    $totalVacancy: Int!
    $candidates: [Int]!
    $rounds: [interviewRounds]!
    $interviewImage: String
    $employeeId: Int!
    $formName: String
    $latitude: String
    $longitude: String
    $subject: String
    $emailBody: String
    $calendarType: String
    $candidateEmailBody: String
    $candidateSubject: String
    $meetingType: String
    $onlineMeetingId: String
  ) {
    addInterview(
      interviewName: $interviewName
      formId: $formId
      jobPostId: $jobPostId
      venue: $venue
      calendarLink: $calendarLink
      assessmentLink: $assessmentLink
      additionalNotes: $additionalNotes
      startDate: $startDate
      endDate: $endDate
      status: $status
      totalVacancy: $totalVacancy
      candidates: $candidates
      rounds: $rounds
      interviewImage: $interviewImage
      employeeId: $employeeId
      formName: $formName
      latitude: $latitude
      longitude: $longitude
      subject: $subject
      emailBody: $emailBody
      calendarType: $calendarType
      candidateEmailBody: $candidateEmailBody
      candidateSubject: $candidateSubject
      meetingType: $meetingType
      onlineMeetingId: $onlineMeetingId
    ) {
      message
      errorCode
      validationError
      data
    }
  }
`;

export const UPDATE_INTERVIEW_DETAILS = gql`
  mutation (
    $interviewId: Int!
    $interviewName: String!
    $jobPostId: Int!
    $venue: String!
    $calendarLink: String
    $assessmentLink: String
    $additionalNotes: String
    $startDate: Date!
    $endDate: Date!
    $status: Int
    $totalVacancy: Int!
    $candidates: [editInterviewCandidates]!
    $rounds: [editInterviewRounds]!
    $interviewImage: String
    $employeeId: Int!
    $formName: String
    $latitude: String
    $longitude: String
    $subject: String
    $emailBody: String
    $calendarType: String
    $candidateEmailBody: String
    $candidateSubject: String
    $meetingType: String
  ) {
    editInterview(
      interviewId: $interviewId
      interviewName: $interviewName
      jobPostId: $jobPostId
      venue: $venue
      calendarLink: $calendarLink
      assessmentLink: $assessmentLink
      additionalNotes: $additionalNotes
      startDate: $startDate
      endDate: $endDate
      status: $status
      totalVacancy: $totalVacancy
      candidates: $candidates
      rounds: $rounds
      interviewImage: $interviewImage
      employeeId: $employeeId
      formName: $formName
      latitude: $latitude
      longitude: $longitude
      subject: $subject
      emailBody: $emailBody
      calendarType: $calendarType
      candidateEmailBody: $candidateEmailBody
      candidateSubject: $candidateSubject
      meetingType: $meetingType
    ) {
      message
      errorCode
      validationError
    }
  }
`;
