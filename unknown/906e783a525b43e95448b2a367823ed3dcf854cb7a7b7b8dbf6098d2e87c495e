import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_DOCUMENT_SUBTYPE = gql`
  query listDocumentSubTypeDetails {
    listDocumentSubTypeDetails {
      errorCode
      message
      data {
        documentTypeId
        documentSubTypeId
        documentSubType
        mandatory
        onlyForEmail
        instruction
        fileName
        presentInPreOnboardingEmail
        fieldVisibility
        addedOn
        updatedOn
        documentType
        categoryFields
        categoryId
        Group_Ids
        Group_Names
        emailTemplates
        addedBy
        updatedBy
        isDefault
      }
    }
  }
`;
export const LIST_EMAIL_TEMPLATE = gql`
  query ($type: String!, $formIds: [Int]) {
    getNotificationTemplates(type: $type, formIds: $formIds) {
      errorCode
      message
      notificationTemplates {
        Template_Id
        TemplateName
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_DOCUMENT_SUBTYPE = gql`
  mutation addUpdateDocumentSubType(
    $documentSubTypeId: Int
    $documentTypeId: Int
    $documentSubType: String!
    $onlyForEmail: String
    $instruction: String
    $fileName: String
    $Group_Ids: [Int]
    $emailTemplates: [String]
    $mandatory: String
  ) {
    addUpdateDocumentSubType(
      documentSubTypeId: $documentSubTypeId
      documentTypeId: $documentTypeId
      documentSubType: $documentSubType
      onlyForEmail: $onlyForEmail
      instruction: $instruction
      fileName: $fileName
      Group_Ids: $Group_Ids
      emailTemplates: $emailTemplates
      mandatory: $mandatory
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_DOCUMENT_SUBTYPE = gql`
  mutation deleteDocumentSubType($documentSubTypeId: Int!) {
    deleteDocumentSubType(documentSubTypeId: $documentSubTypeId) {
      errorCode
      message
    }
  }
`;

export const ADD_NEW_TAGS = gql`
  mutation addDocumentEnforcementGroup($groupNames: [String]!) {
    addDocumentEnforcementGroup(groupNames: $groupNames) {
      errorCode
      message
      __typename
    }
  }
`;
