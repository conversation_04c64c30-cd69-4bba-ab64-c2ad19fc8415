<template>
  <div>
    <section>
      <v-row class="d-flex justify-end">
        <v-col
          md="12"
          cols="12"
          xs="12"
          sm="12"
          class="d-flex ml-md-4 mt-4 align-center"
          :class="{
            'flex-column': isMobileView,
            'justify-center': windowWidth < 1264,
            'justify-end': windowWidth >= 1264,
          }"
          style="flex-wrap: wrap"
        >
          <v-btn
            class="bg-white my-2 ml-2"
            :size="isMobileView ? 'small' : 'default'"
            rounded="lg"
          >
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            <span class="text-caption px-1"
              >{{ $t("productivityMonitoring.dateRange") }}:</span
            >
            <flat-pickr
              v-model="appliedDateRange"
              :config="flatPickerOptions"
              placeholder="Select Date Range"
              class="ml-2 mt-1 date-range-picker-custom-bg"
              style="outline: 0px; color: var(--v-primary-base)"
              @onChange="onChangeDateRange"
            ></flat-pickr>
          </v-btn>
          <v-menu
            v-model="customGroupMenu"
            :close-on-content-click="true"
            transition="scale-transition"
            offset-y
            min-width="290px"
          >
            <template v-slot:activator="{ props: activatorProps }">
              <v-btn
                class="white my-2 ml-2"
                rounded="lg"
                dense
                v-bind="activatorProps"
              >
                <span class="text-caption px-1"
                  >{{ $t("productivityMonitoring.customGroup") }}:</span
                >
                <span class="text-primary font-weight-bold"> </span>
                {{ selectedCustomGroup ? selectedCustomGroup : "-" }}
                <v-icon color="white" class="ml-1" size="17">{{
                  customGroupMenu ? "fas fa-caret-up" : "fas fa-caret-down"
                }}</v-icon>
              </v-btn>
            </template>
            <div
              style="
                min-height: 100px;
                max-height: 300px;
                overflow-y: scroll;
                background-color: white;
              "
              class="white pa-2"
            >
              <div v-if="customGroupList && customGroupList.length > 0">
                <div
                  v-for="customGroup in customGroupList"
                  :key="customGroup.WorkSchedule_Id"
                  @click="onSelectCustomGroup(customGroup)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <div
                        v-bind="props"
                        class="pa-2 my-2 rounded-lg cursor-pointer"
                        :class="{
                          'bg-hover':
                            isHovering &&
                            selectedCustomGroup !==
                              customGroup.Custom_Group_Name,
                          'bg-primary text-white':
                            selectedCustomGroup ===
                            customGroup.Custom_Group_Name,
                          'bg-grey-lighten-4':
                            !isHovering &&
                            selectedCustomGroup !==
                              customGroup.Custom_Group_Name,
                        }"
                      >
                        <div class="text-body-2">
                          {{ customGroup.Custom_Group_Name }}
                        </div>
                        <div
                          style="font-size: 12px"
                          class="pt-1"
                          :class="{
                            'text-white':
                              selectedCustomGroup ===
                              customGroup.Custom_Group_Name,
                            'text-primary text-lighten-3':
                              selectedCustomGroup !==
                              customGroup.Custom_Group_Name,
                          }"
                        ></div>
                      </div>
                    </template>
                  </v-hover>
                </div>
              </div>
              <div
                v-else
                style="height: 100px"
                class="text-grey rounded-lg d-flex justify-center align-center"
              >
                {{ $t("productivityMonitoring.noDataAvailable") }}
              </div>
            </div>
          </v-menu>
          <v-menu
            id="activitytracker_my_activity_date_picker"
            v-model="workScheduleMenu"
            :close-on-content-click="true"
            transition="scale-transition"
            offset-y
            min-width="290px"
          >
            <template v-slot:activator="{ props: activatorProps }">
              <v-btn
                class="bg-white my-2 ml-2"
                rounded="lg"
                dense
                v-bind="activatorProps"
              >
                <v-icon color="primary" class="mr-1" size="17"
                  >far fa-clock</v-icon
                >
                <span class="text-caption primary px-1"
                  >{{ $t("productivityMonitoring.workSchedule") }}:</span
                >
                <span class="text-primary font-weight-bold">
                  {{ employeeWorkSchedule ? employeeWorkSchedule : "-" }}
                </span>
                <v-icon color="primary" class="ml-1" size="17">{{
                  workScheduleMenu ? "fas fa-caret-up" : "fas fa-caret-down"
                }}</v-icon>
              </v-btn>
            </template>
            <div
              style="
                min-height: 100px;
                max-height: 300px;
                overflow-y: scroll;
                background-color: white;
              "
              class="white pa-2"
            >
              <div v-if="workScheduleList && workScheduleList.length > 0">
                <div
                  v-for="workSchedule in workScheduleList"
                  :key="workSchedule.WorkSchedule_Id"
                  @click="onSelectWorkSchedule(workSchedule)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <div
                        v-bind="props"
                        class="pa-2 my-2 rounded-lg cursor-pointer"
                        :class="{
                          'bg-hover':
                            isHovering &&
                            employeeWorkSchedule !==
                              workSchedule.WorkSchedule_Name,
                          'bg-primary text-white':
                            employeeWorkSchedule ===
                            workSchedule.WorkSchedule_Name,
                          'bg-grey-lighten-4 text-primary':
                            !isHovering &&
                            employeeWorkSchedule !==
                              workSchedule.WorkSchedule_Name,
                        }"
                      >
                        <div class="text-body-2">
                          {{ workSchedule.WorkSchedule_Name }}
                        </div>
                        <div
                          style="font-size: 12px"
                          class="pt-1"
                          :class="{
                            'text-white':
                              employeeWorkSchedule ===
                              workSchedule.WorkSchedule_Name,
                            'text-primary text-lighten-3':
                              employeeWorkSchedule !==
                              workSchedule.WorkSchedule_Name,
                          }"
                        >
                          {{ workSchedule.Time_Zone }}
                        </div>
                      </div>
                    </template>
                  </v-hover>
                </div>
              </div>
              <div
                v-else
                style="height: 100px"
                class="text-grey rounded-lg d-flex justify-center align-center"
              >
                {{ $t("productivityMonitoring.noDataAvailable") }}
              </div>
            </div>
          </v-menu>
        </v-col>
      </v-row>
      <div v-if="isNoRecord">
        <AppFetchErrorScreen
          image-name="common/no-records"
          :main-title="$t('productivityMonitoring.noActivitiesFound')"
        >
          <template #contentSlot>
            <div class="d-flex mb-2 flex-wrap justify-center">
              <v-btn
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click.stop="resetFilter()"
              >
                {{ $t("productivityMonitoring.resetFilterSearch") }}
              </v-btn>
            </div>
          </template>
        </AppFetchErrorScreen>
      </div>
      <div v-else>
        <v-row v-if="!isChartClicked">
          <v-col md="6" sm="12" cols="12">
            <div
              v-if="systemUpTimeChartLoading"
              style="height: 100%"
              class="mt-2"
            >
              <v-row>
                <v-skeleton-loader
                  type="article"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
              <v-row>
                <v-skeleton-loader
                  type="article, actions"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
            </div>
            <v-card v-else id="chart" class="pa-2">
              <div class="d-flex align-center justify-space-between mt-n6">
                <span class="ml-2 text-primary subtitle font-weight-bold"
                  >{{ $t("productivityMonitoring.systemUpTime") }}
                  <v-icon class="ml-1" color="indigo-darken-4" size="15"
                    >fas fa-clock</v-icon
                  ></span
                >
                <div class="d-flex align-center">
                  <v-tooltip
                    :text="$t('productivityMonitoring.lowestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.lowest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="systemUpTimeSort"
                    :true-value="1"
                    :false-value="0"
                  ></v-switch>
                  <v-tooltip
                    :text="$t('productivityMonitoring.highestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.highest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                </div>
              </div>
              <!-- rendering the chart -->
              <apexchart
                v-if="
                  systemUpTimeChartSeries.length > 0 &&
                  systemUpTimeChartSeries[0].data &&
                  systemUpTimeChartSeries[0].data.length > 0
                "
                type="bar"
                :options="systemUpTimeChartOptions"
                :series="systemUpTimeChartSeries"
                @click="closeModal"
                class="mt-n8"
              ></apexchart>
            </v-card>
          </v-col>
          <v-col md="6" sm="12" cols="12">
            <div
              v-if="activeTimeChartLoading"
              style="height: 100%"
              class="mt-2"
            >
              <v-row>
                <v-skeleton-loader
                  type="article"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
              <v-row>
                <v-skeleton-loader
                  type="article, actions"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
            </div>
            <v-card v-else class="pa-2"
              ><div class="d-flex align-center justify-space-between mt-n6">
                <span class="ml-2 text-primary subtitle font-weight-bold"
                  >{{ $t("productivityMonitoring.activeTime") }}
                  <v-icon class="ml-1" color="indigo-darken-4" size="15"
                    >fas fa-clock</v-icon
                  ></span
                >
                <div class="d-flex align-center">
                  <v-tooltip
                    :text="$t('productivityMonitoring.lowestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.lowest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="activeTimeSort"
                    :true-value="1"
                    :false-value="0"
                  ></v-switch>
                  <v-tooltip
                    :text="$t('productivityMonitoring.highestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.highest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                </div>
              </div>
              <apexchart
                v-if="
                  activeTimeChartSeries.length > 0 &&
                  activeTimeChartSeries[0].data &&
                  activeTimeChartSeries[0].data.length > 0
                "
                type="bar"
                :options="activeTimeChartOptions"
                :series="activeTimeChartSeries"
                @click="closeModal"
                class="mt-n8"
              ></apexchart>
            </v-card>
          </v-col>
          <v-col md="6" sm="12" cols="12">
            <div v-if="idleTimeChartLoading" style="height: 100%">
              <v-row>
                <v-skeleton-loader
                  type="article"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
              <v-row>
                <v-skeleton-loader
                  type="article, actions"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
            </div>
            <v-card v-else class="pa-2">
              <div class="d-flex align-center justify-space-between mt-n6">
                <span class="ml-2 text-primary subtitle font-weight-bold"
                  >{{ $t("productivityMonitoring.idleTime") }}
                  <v-icon class="ml-1" color="indigo-darken-4" size="15"
                    >fas fa-clock</v-icon
                  ></span
                >
                <div class="d-flex align-center">
                  <v-tooltip
                    :text="$t('productivityMonitoring.lowestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.lowest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="idleTimeSort"
                    :true-value="1"
                    :false-value="0"
                  ></v-switch>
                  <v-tooltip
                    :text="$t('productivityMonitoring.highestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.highest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                </div>
              </div>
              <apexchart
                v-if="
                  idleTimeChartSeries.length > 0 &&
                  idleTimeChartSeries[0].data &&
                  idleTimeChartSeries[0].data.length > 0
                "
                type="bar"
                :options="idleTimeChartOptions"
                :series="idleTimeChartSeries"
                @click="closeModal"
                class="mt-n8"
              ></apexchart>
            </v-card>
          </v-col>
          <v-col md="6" sm="12" cols="12">
            <div v-if="productiveTimeChartLoading" style="height: 100%">
              <v-row>
                <v-skeleton-loader
                  type="article"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
              <v-row>
                <v-skeleton-loader
                  type="article, actions"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
            </div>
            <v-card v-else class="pa-2">
              <div class="d-flex align-center justify-space-between mt-n6">
                <span class="ml-2 text-primary subtitle font-weight-bold"
                  >{{ $t("productivityMonitoring.productiveTime") }}
                  <v-icon class="ml-1" color="indigo-darken-4" size="15"
                    >fas fa-clock</v-icon
                  ></span
                >
                <div class="d-flex align-center">
                  <v-tooltip
                    :text="$t('productivityMonitoring.lowestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.lowest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="productiveTimeSort"
                    :true-value="1"
                    :false-value="0"
                  ></v-switch>
                  <v-tooltip
                    :text="$t('productivityMonitoring.highestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.highest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                </div>
              </div>
              <apexchart
                v-if="
                  productiveTimeChartSeries.length > 0 &&
                  productiveTimeChartSeries[0].data &&
                  productiveTimeChartSeries[0].data.length > 0
                "
                type="bar"
                :options="productiveTimeChartOptions"
                :series="productiveTimeChartSeries"
                @click="closeModal"
                class="mt-n8"
              ></apexchart>
            </v-card>
          </v-col>
          <v-col md="6" sm="12" cols="12">
            <div v-if="computerActivityTimeChartLoading" style="height: 100%">
              <v-row>
                <v-skeleton-loader
                  type="article"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
              <v-row>
                <v-skeleton-loader
                  type="article, actions"
                  width="100%"
                ></v-skeleton-loader>
              </v-row>
            </div>
            <v-card v-else class="pa-2">
              <div class="d-flex align-center justify-space-between mt-n6">
                <span class="ml-2 text-primary subtitle font-weight-bold"
                  >{{ $t("productivityMonitoring.computerActivity") }}
                  <v-icon class="ml-1" color="indigo-darken-4" size="15"
                    >fas fa-clock</v-icon
                  ></span
                >
                <div class="d-flex align-center">
                  <v-tooltip
                    :text="$t('productivityMonitoring.lowestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.lowest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="computerActivityTimeSort"
                    :true-value="1"
                    :false-value="0"
                  ></v-switch>
                  <v-tooltip
                    :text="$t('productivityMonitoring.highestSystemUpTime')"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="props"
                        :size="isMobileView ? 'small' : 'default'"
                        class="rounded-border text-amber mr-2"
                      >
                        <span class="text-subtitle-1 v-label text-black">{{
                          $t("productivityMonitoring.highest")
                        }}</span>
                      </span>
                    </template>
                  </v-tooltip>
                </div>
              </div>
              <apexchart
                v-if="
                  computerActivityTimeChartSeries.length > 0 &&
                  computerActivityTimeChartSeries[0].data &&
                  computerActivityTimeChartSeries[0].data.length > 0
                "
                type="bar"
                :options="computerActivityTimeChartOptions"
                :series="computerActivityTimeChartSeries"
                @click="closeModal"
                class="mt-n8"
              ></apexchart>
            </v-card>
          </v-col>
        </v-row>
        <v-row v-if="isChartClicked">
          <v-col cols="12" class="d-flex justify-center">
            <v-card
              :style="windowWidth <= 950 ? 'width: 100%' : 'width: 90%'"
              class="pa-2"
            >
              <div class="ml-3 mt-2">
                <v-icon @click="isChartClicked = false" color="primary"
                  >fas fa-arrow-left</v-icon
                >
              </div>
              <div v-if="commonChartLoading">
                <v-row class="mt-2">
                  <v-skeleton-loader
                    type="article"
                    width="100%"
                  ></v-skeleton-loader>
                </v-row>
                <v-row class="mt-2">
                  <v-skeleton-loader
                    type="article"
                    width="100%"
                  ></v-skeleton-loader>
                </v-row>
              </div>
              <div class="d-flex flex-column align-center" v-else>
                <div class="d-flex align-center">
                  <span
                    class="ml-2 mt-2 text-primary subtitle font-weight-bold"
                    >{{ chartName }}</span
                  >
                  <v-icon class="ml-1 mt-2" color="purple lighten-1" size="15"
                    >fas fa-clock</v-icon
                  >
                </div>
                <div :style="windowWidth <= 950 ? 'width: 100%' : 'width: 55%'">
                  <apexchart
                    v-if="
                      individualChartSeries.length > 0 &&
                      individualChartSeries[0].data &&
                      individualChartSeries[0].data.length > 0
                    "
                    type="bar"
                    height="300"
                    :options="individualChartOptions"
                    :series="individualChartSeries"
                    @click="checkIndividual"
                  ></apexchart>
                  <div class="d-flex align-center justify-center">
                    <span
                      class="ml-2 mt-2 text-primary subtitle font-weight-bold"
                      >Overall {{ chartName }}
                      <v-icon class="ml-1" color="purple lighten-1" size="15"
                        >fas fa-clock</v-icon
                      >
                    </span>
                  </div>
                  <div style="width: 100%">
                    <apexchart
                      v-if="heatChartSeries.length > 0"
                      type="heatmap"
                      height="300"
                      :options="heatChartOption"
                      :series="heatChartSeries"
                      @click="changeFileName"
                    ></apexchart>
                    <apexchart
                      v-if="lineChartSeries.length > 0"
                      height="300"
                      :options="lineChartOptions"
                      :series="lineChartSeries"
                      @click="changeLineChartFileName"
                    ></apexchart>
                  </div>
                </div>
              </div>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </section>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="mt-n5 primary"
          variant="text"
          @click="closeValidationAlert()"
        >
          {{ $t("productivityMonitoring.close") }}
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import VueApexCharts from "vue3-apexcharts";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import moment from "moment";
import {
  GET_TOTAL_PRODUCTIVE_HOURS_FOR_A_MONTH,
  LIST_WORK_SCHEDULE,
  GET_PER_DAY_PRODUCTIVE_HOURS,
} from "@/graphql/productivity-monitoring/activityDashboardQueries";
export default {
  name: "ProductivityDashboard",
  components: {
    apexchart: VueApexCharts,
    flatPickr,
  },
  data() {
    return {
      systemUpTimeChartLoading: true,
      isChartClicked: false,
      systemUpTimeSort: 0,
      activeTimeSort: 0,
      idleTimeSort: 1,
      productiveTimeSort: 0,
      computerActivityTimeSort: 0,
      openedChartOrder: 0,
      sortingMetric: "",
      customGroupMenu: false,
      workScheduleMenu: false,
      activeTimeChartLoading: true,
      idleTimeChartLoading: true,
      productiveTimeChartLoading: true,
      computerActivityTimeChartLoading: true,
      commonChartLoading: true,
      customGroupId: 0,
      employeeWorkScheduleId: 0,
      selectedCustomGroup: "All",
      employeeWorkSchedule: "",
      customGroupList: [],
      workScheduleList: [],
      appliedDateRange: null,
      currentDateRange: "",
      startDate: "",
      endDate: "",
      chartName: "",
      showValidationAlert: false,
      validationMessages: [],
      isNoRecord: false,
      // Defining chart options and series data
      systemUpTimeChartOptions: {
        chart: {
          type: "bar",
          height: 350,
          name: this.$t("productivityMonitoring.systemUpTime"),
        },
        plotOptions: {
          bar: {
            borderRadius: 0,
            horizontal: true,
          },
        },
        colors: ["#286345"],
        dataLabels: {
          enabled: false,
        },
        xaxis: {
          title: {
            text: this.$t("productivityMonitoring.timeInHours"),
          },
        },
      },
      systemUpTimeChartSeries: [
        {
          name: this.$t("productivityMonitoring.hours"),
          data: [],
        },
      ],
      activeTimeChartOptions: {
        chart: {
          type: "bar",
          height: 350,
          name: this.$t("productivityMonitoring.activeTime"),
        },
        plotOptions: {
          bar: {
            borderRadius: 0,
            horizontal: true,
          },
        },
        colors: ["#232340"],
        dataLabels: {
          enabled: false,
        },
        xaxis: {
          title: {
            text: this.$t("productivityMonitoring.timeInHours"),
          },
        },
      },
      activeTimeChartSeries: [
        {
          name: this.$t("productivityMonitoring.hours"),
          data: [],
        },
      ],
      idleTimeChartOptions: {
        chart: {
          type: "bar",
          height: 350,
          name: this.$t("productivityMonitoring.idleTime"),
        },
        plotOptions: {
          bar: {
            borderRadius: 0,
            horizontal: true,
          },
        },
        colors: ["#c2b659"],
        dataLabels: {
          enabled: false,
        },
        xaxis: {
          title: {
            text: this.$t("productivityMonitoring.timeInHours"),
          },
        },
      },
      idleTimeChartSeries: [
        {
          name: this.$t("productivityMonitoring.hours"),
          data: [],
        },
      ],
      productiveTimeChartOptions: {
        chart: {
          type: "bar",
          height: 350,
          name: this.$t("productivityMonitoring.productiveTime"),
        },
        plotOptions: {
          bar: {
            borderRadius: 0,
            horizontal: true,
          },
        },
        colors: ["#3cbd4f"],
        dataLabels: {
          enabled: false,
        },
        xaxis: {
          title: {
            text: this.$t("productivityMonitoring.timeInHours"),
          },
        },
      },
      productiveTimeChartSeries: [
        {
          name: this.$t("productivityMonitoring.hours"),
          data: [],
        },
      ],
      computerActivityTimeChartOptions: {
        chart: {
          type: "bar",
          height: 350,
          name: this.$t("productivityMonitoring.activityLevel"),
        },
        plotOptions: {
          bar: {
            borderRadius: 0,
            horizontal: true,
          },
        },
        colors: ["#753e6c"],
        dataLabels: {
          enabled: false,
        },
        xaxis: {
          title: {
            text: this.$t("productivityMonitoring.timeInHours"),
          },
        },
      },
      computerActivityTimeChartSeries: [
        {
          name: this.$t("productivityMonitoring.hours"),
          data: [],
        },
      ],
      individualChartOptions: {
        chart: {
          height: 350,
          type: "bar",
        },
        colors: [],
        plotOptions: {
          bar: {
            columnWidth: "45%",
            distributed: true,
          },
        },
        dataLabels: {
          enabled: false,
        },
        legend: {
          show: false,
        },
        xaxis: {},
        yaxis: {
          title: {
            text: this.$t("productivityMonitoring.totalTimeInHours"),
          },
        },
      },
      individualChartSeries: [
        {
          name: this.$t("productivityMonitoring.hours"),
          data: [],
        },
      ],
      heatChartOption: {
        chart: {
          height: 0,
          width: 0,
          type: "heatmap",
        },
        stroke: {
          width: 0,
        },
        plotOptions: {
          heatmap: {
            radius: 0,
            enableShades: false,
            colorScale: {
              ranges: [
                {
                  from: 0,
                  to: 0.99,
                  color: " #f2f2f2",
                },
                {
                  from: 1,
                  to: 4.99,
                  color: "#a1c6e3",
                },
                {
                  from: 5,
                  to: 9.99,
                  color: "#4a89ba",
                },
                {
                  from: 10,
                  to: 14.99,
                  color: "#034d87",
                },
                {
                  from: 15,
                  to: 24,
                  color: "#052f78",
                },
              ],
            },
          },
        },
        dataLabels: {
          enabled: false,
          style: {
            colors: ["#fff"],
          },
        },
        xaxis: {
          type: "category",
        },
        yaxis: {
          title: {
            text: this.$t("productivityMonitoring.totalTimeInHours"),
          },
        },
      },
      heatChartSeries: [],
      lineChartOptions: {
        chart: {
          height: 350,
          type: "line",
          zoom: {
            enabled: false,
          },
        },
        dataLabels: {
          enabled: false,
        },
        stroke: {
          curve: "straight",
        },
        title: {
          text: this.$t("productivityMonitoring.productivityTrendPeriod"),
          align: "center",
        },
        grid: {
          row: {
            colors: ["#f3f3f3", "transparent"], // takes an array which will be repeated on columns
            opacity: 0.5,
          },
        },
        yaxis: {
          title: {
            text: this.$t("productivityMonitoring.totalTimeInHours"),
          },
        },
      },
      lineChartSeries: [
        {
          name: this.$t("productivityMonitoring.hours"),
        },
      ],
    };
  },
  watch: {
    systemUpTimeSort(order) {
      this.getTotalSystemUpTimeHoursForAMonth(
        "systemUpTime",
        order,
        this.employeeWorkScheduleId,
        this.customGroupId
      );
    },
    activeTimeSort(order) {
      this.getTotalActiveTimeHoursForAMonth(
        "activeTime",
        order,
        this.employeeWorkScheduleId,
        this.customGroupId
      );
    },
    idleTimeSort(order) {
      this.getTotalIdleTimeHoursForAMonth(
        "idleTime",
        order,
        this.employeeWorkScheduleId,
        this.customGroupId
      );
    },
    productiveTimeSort(order) {
      this.getTotalProductiveAppHoursForAMonth(
        "timeSpentOnProductiveApps",
        order,
        this.employeeWorkScheduleId,
        this.customGroupId
      );
    },
    computerActivityTimeSort(order) {
      this.getTotalComputerActiveTimeHoursForAMonth(
        "computerActivityTime",
        order,
        this.employeeWorkScheduleId,
        this.customGroupId
      );
    },
    appliedDateRange() {
      if (this.isExceed31Days) {
        this.appliedDateRange = this.currentDateRange;
        this.isExceed31Days = false;
      }
    },
  },
  mounted() {
    this.retrieveCustomGroups();
    this.getCurrentDateRange();
    this.fetchEmpWorkScheduleDetails();
    this.fetchWorkScheduleList();
  },
  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formatDate() {
      return (date, withTime = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      // Get the current date
      const currentDate = moment().toDate();
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment().format(this.$store.state.orgDetails.orgDateFormat),
        // Disable the current date
        disable: [currentDate],
      };
    },
  },
  methods: {
    // if the path is dashboard, then show the tabs based on member role
    closeModal(event, chartContext, config) {
      // Extracting data about the clicked bar from the event
      const clickedBarIndex = config.dataPointIndex;
      this.individualChartOptions.xaxis.categories =
        config.config.xaxis.categories;
      this.individualChartSeries[0].data = config.config.series[0].data;
      this.chartName = config.config.chart.name;
      config.globals.chartID = this.chartName;
      if (this.chartName.toLowerCase() == "system up time") {
        this.sortingMetric = "totalSystemUpTimeInMinutes";
      }
      if (this.chartName.toLowerCase() == "active time") {
        this.sortingMetric = "totalActiveTimeInMinutes";
      }
      if (this.chartName.toLowerCase() == "idle time") {
        this.sortingMetric = "totalIdleTimeInMinutes";
      }
      if (this.chartName.toLowerCase() == "productive time") {
        this.sortingMetric = "totalTimeSpentOnProductiveAppsAndUrlsInMinutes";
      }
      if (this.chartName.toLowerCase() == "computer activity(keyboard/mouse)") {
        this.sortingMetric = "totalComputerActivityTimeInMinutes";
      }
      this.openedChartOrder = config.config.chart.order;
      this.getPerDayProductiveHours();
      this.individualChartOptions.colors = config.config.colors;

      if (clickedBarIndex != -1) {
        this.isChartClicked = true;
      }
    },
    checkIndividual(event, chartContext, config) {
      // Extracting data about the clicked bar from the event
      const clickedBarIndex = config.dataPointIndex;
      config.globals.chartID = this.chartName;
      if (clickedBarIndex != -1) {
        this.isChartClicked = true;
      }
    },
    changeFileName(event, chartContext, config) {
      config.globals.chartID = "Overall " + this.chartName;
    },
    changeLineChartFileName(event, chartContext, config) {
      config.globals.chartID = this.$t(
        "productivityMonitoring.productivityTrend"
      );
    },
    async getTotalSystemUpTimeHoursForAMonth(
      activityType,
      order = 0,
      workScheduleId,
      customGroupId
    ) {
      let vm = this;
      this.isNoRecord = false;
      vm.systemUpTimeChartLoading = true;
      vm.systemUpTimeChartOptions.chart.order = vm.systemUpTimeSort;
      await vm.$apollo
        .query({
          query: GET_TOTAL_PRODUCTIVE_HOURS_FOR_A_MONTH,
          variables: {
            startDate: this.startDate,
            endDate: this.endDate,
            workScheduleId: workScheduleId,
            productivityType: activityType,
            customGroupId: customGroupId,
          },
          client: "apolloClientK",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getTotalProductiveHoursForAMonth &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData.length
          ) {
            const employeeData =
              response.data.getTotalProductiveHoursForAMonth
                .totalProductiveHoursData;
            if (order === 0) {
              // Sort in ascending order
              employeeData.sort(
                (a, b) => a.totalSystemUpTimeHours - b.totalSystemUpTimeHours
              );
            } else if (order === 1) {
              // Sort in descending order
              employeeData.sort(
                (a, b) => b.totalSystemUpTimeHours - a.totalSystemUpTimeHours
              );
            }
            const firstTenRecords = employeeData.slice(0, 10);
            const employeeNames = firstTenRecords.map(
              (data) => data.employeeName
            );
            this.systemUpTimeChartOptions.xaxis.categories = employeeNames;
            const prductiveAppUrlData = firstTenRecords.map(
              (data) => data.totalSystemUpTimeHours
            );
            this.systemUpTimeChartSeries[0].data = prductiveAppUrlData;
            if (this.chartName.toLowerCase() == "system up time") {
              this.individualChartOptions.xaxis.categories = employeeNames;
              this.individualChartSeries[0].data = prductiveAppUrlData;
            }
          } else {
            this.isNoRecord = true;
            this.individualChartOptions.xaxis.categories = [];
            this.individualChartSeries[0].data = [];
            this.systemUpTimeChartOptions.xaxis.categories = [];
            this.systemUpTimeChartSeries[0].data = [];
          }
          vm.systemUpTimeChartLoading = false;
        })
        .catch((err) => {
          vm.systemUpTimeChartLoading = false;
          this.handleGetTotalProductiveHoursError(err);
        });
    },
    async getTotalActiveTimeHoursForAMonth(
      activityType,
      order = 0,
      workScheduleId,
      customGroupId
    ) {
      let vm = this;
      vm.activeTimeChartLoading = true;
      vm.activeTimeChartOptions.chart.order = vm.activeTimeSort;
      await vm.$apollo
        .query({
          query: GET_TOTAL_PRODUCTIVE_HOURS_FOR_A_MONTH,
          variables: {
            startDate: this.startDate,
            endDate: this.endDate,
            workScheduleId: workScheduleId,
            productivityType: activityType,
            customGroupId: customGroupId,
          },
          client: "apolloClientK",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getTotalProductiveHoursForAMonth &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData.length
          ) {
            const employeeData =
              response.data.getTotalProductiveHoursForAMonth
                .totalProductiveHoursData;
            if (order === 0) {
              // Sort in ascending order
              employeeData.sort(
                (a, b) =>
                  a.totalUserActiveTimeHours - b.totalUserActiveTimeHours
              );
            } else if (order === 1) {
              // Sort in descending order
              employeeData.sort(
                (a, b) =>
                  b.totalUserActiveTimeHours - a.totalUserActiveTimeHours
              );
            }
            const firstTenRecords = employeeData.slice(0, 10);
            const employeeNames = firstTenRecords.map(
              (data) => data.employeeName
            );
            this.activeTimeChartOptions.xaxis.categories = employeeNames;
            const prductiveAppUrlData = firstTenRecords.map(
              (data) => data.totalUserActiveTimeHours
            );
            this.activeTimeChartSeries[0].data = prductiveAppUrlData;
            if (this.chartName.toLowerCase() == "active time") {
              this.individualChartOptions.xaxis.categories = employeeNames;
              this.individualChartSeries[0].data = prductiveAppUrlData;
            }
          } else {
            this.individualChartOptions.xaxis.categories = [];
            this.individualChartSeries[0].data = [];
            this.activeTimeChartOptions.xaxis.categories = [];
            this.activeTimeChartSeries[0].data = [];
          }
          vm.activeTimeChartLoading = false;
        })
        .catch((err) => {
          vm.activeTimeChartLoading = false;
          this.handleGetTotalProductiveHoursError(err);
        });
    },
    async getTotalIdleTimeHoursForAMonth(
      activityType,
      order = 1,
      workScheduleId,
      customGroupId
    ) {
      let vm = this;
      vm.idleTimeChartLoading = true;
      vm.idleTimeChartOptions.chart.order = vm.idleTimeSort;
      await vm.$apollo
        .query({
          query: GET_TOTAL_PRODUCTIVE_HOURS_FOR_A_MONTH,
          variables: {
            startDate: this.startDate,
            endDate: this.endDate,
            workScheduleId: workScheduleId,
            productivityType: activityType,
            customGroupId: customGroupId,
          },
          client: "apolloClientK",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getTotalProductiveHoursForAMonth &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData.length
          ) {
            const employeeData =
              response.data.getTotalProductiveHoursForAMonth
                .totalProductiveHoursData;
            if (order === 0) {
              // Sort in ascending order
              employeeData.sort(
                (a, b) =>
                  a.totalIdleAndNotAcitveHours - b.totalIdleAndNotAcitveHours
              );
            } else if (order === 1) {
              // Sort in descending order
              employeeData.sort(
                (a, b) =>
                  b.totalIdleAndNotAcitveHours - a.totalIdleAndNotAcitveHours
              );
            }
            const firstTenRecords = employeeData.slice(0, 10);
            const employeeNames = firstTenRecords.map(
              (data) => data.employeeName
            );
            this.idleTimeChartOptions.xaxis.categories = employeeNames;
            const prductiveAppUrlData = firstTenRecords.map(
              (data) => data.totalIdleAndNotAcitveHours
            );
            this.idleTimeChartSeries[0].data = prductiveAppUrlData;
            if (this.chartName.toLowerCase() == "idle time") {
              this.individualChartOptions.xaxis.categories = employeeNames;
              this.individualChartSeries[0].data = prductiveAppUrlData;
            }
          } else {
            this.individualChartOptions.xaxis.categories = [];
            this.individualChartSeries[0].data = [];
            this.idleTimeChartOptions.xaxis.categories = [];
            this.idleTimeChartSeries[0].data = [];
          }
          vm.idleTimeChartLoading = false;
        })
        .catch((err) => {
          vm.idleTimeChartLoading = false;
          this.handleGetTotalProductiveHoursError(err);
        });
    },
    async getTotalProductiveAppHoursForAMonth(
      activityType,
      order = 0,
      workScheduleId,
      customGroupId
    ) {
      let vm = this;
      vm.productiveTimeChartLoading = true;
      vm.productiveTimeChartOptions.chart.order = vm.productiveTimeSort;
      await vm.$apollo
        .query({
          query: GET_TOTAL_PRODUCTIVE_HOURS_FOR_A_MONTH,
          variables: {
            startDate: this.startDate,
            endDate: this.endDate,
            workScheduleId: workScheduleId,
            productivityType: activityType,
            customGroupId: customGroupId,
          },
          client: "apolloClientK",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getTotalProductiveHoursForAMonth &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData.length
          ) {
            const employeeData =
              response.data.getTotalProductiveHoursForAMonth
                .totalProductiveHoursData;
            if (order === 0) {
              // Sort in ascending order
              employeeData.sort(
                (a, b) =>
                  a.totalTimeSpentOnProductiveAppsAndUrls -
                  b.totalTimeSpentOnProductiveAppsAndUrls
              );
            } else if (order === 1) {
              // Sort in descending order
              employeeData.sort(
                (a, b) =>
                  b.totalTimeSpentOnProductiveAppsAndUrls -
                  a.totalTimeSpentOnProductiveAppsAndUrls
              );
            }
            const firstTenRecords = employeeData.slice(0, 10);
            const employeeNames = firstTenRecords.map(
              (data) => data.employeeName
            );
            this.productiveTimeChartOptions.xaxis.categories = employeeNames;
            const prductiveAppUrlData = firstTenRecords.map(
              (data) => data.totalTimeSpentOnProductiveAppsAndUrls
            );
            this.productiveTimeChartSeries[0].data = prductiveAppUrlData;
            if (this.chartName.toLowerCase() == "productive time") {
              this.individualChartOptions.xaxis.categories = employeeNames;
              this.individualChartSeries[0].data = prductiveAppUrlData;
            }
          } else {
            this.individualChartOptions.xaxis.categories = [];
            this.individualChartSeries[0].data = [];
            this.productiveTimeChartOptions.xaxis.categories = [];
            this.productiveTimeChartSeries[0].data = [];
          }
          vm.productiveTimeChartLoading = false;
        })
        .catch((err) => {
          vm.productiveTimeChartLoading = false;
          this.handleGetTotalProductiveHoursError(err);
        });
    },
    async getTotalComputerActiveTimeHoursForAMonth(
      activityType,
      order = 0,
      workScheduleId,
      customGroupId
    ) {
      let vm = this;
      vm.computerActivityTimeChartLoading = true;
      vm.computerActivityTimeChartOptions.chart.order =
        vm.computerActivityTimeSort;
      await vm.$apollo
        .query({
          query: GET_TOTAL_PRODUCTIVE_HOURS_FOR_A_MONTH,
          variables: {
            startDate: this.startDate,
            endDate: this.endDate,
            workScheduleId: workScheduleId,
            productivityType: activityType,
            customGroupId: customGroupId,
          },
          client: "apolloClientK",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getTotalProductiveHoursForAMonth &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData &&
            response.data.getTotalProductiveHoursForAMonth
              .totalProductiveHoursData.length
          ) {
            const employeeData =
              response.data.getTotalProductiveHoursForAMonth
                .totalProductiveHoursData;
            if (order === 0) {
              // Sort in ascending order
              employeeData.sort(
                (a, b) =>
                  a.totalComputerActivityTimeHours -
                  b.totalComputerActivityTimeHours
              );
            } else if (order === 1) {
              // Sort in descending order
              employeeData.sort(
                (a, b) =>
                  b.totalComputerActivityTimeHours -
                  a.totalComputerActivityTimeHours
              );
            }
            const firstTenRecords = employeeData.slice(0, 10);
            const employeeNames = firstTenRecords.map(
              (data) => data.employeeName
            );
            this.computerActivityTimeChartOptions.xaxis.categories =
              employeeNames;
            const prductiveAppUrlData = firstTenRecords.map(
              (data) => data.totalComputerActivityTimeHours
            );
            this.computerActivityTimeChartSeries[0].data = prductiveAppUrlData;
            if (
              this.chartName.toLowerCase() ==
              "computer activity(keyboard/mouse)"
            ) {
              this.individualChartOptions.xaxis.categories = employeeNames;
              this.individualChartSeries[0].data = prductiveAppUrlData;
            }
          } else {
            this.individualChartOptions.xaxis.categories = [];
            this.individualChartSeries[0].data = [];
            this.computerActivityTimeChartOptions.xaxis.categories = [];
            this.computerActivityTimeChartSeries[0].data = [];
          }
          vm.computerActivityTimeChartLoading = false;
        })
        .catch((err) => {
          vm.computerActivityTimeChartLoading = false;
          this.handleGetTotalProductiveHoursError(err);
        });
    },
    async getPerDayProductiveHours() {
      let vm = this;
      vm.commonChartLoading = true;
      await vm.$apollo
        .query({
          query: GET_PER_DAY_PRODUCTIVE_HOURS,
          variables: {
            startDate: this.startDate,
            endDate: this.endDate,
            workScheduleId: this.employeeWorkScheduleId,
            customGroupId: this.customGroupId,
          },
          client: "apolloClientK",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getPerDayProductiveHours &&
            response.data.getPerDayProductiveHours.activityDetails &&
            response.data.getPerDayProductiveHours.activityDetails.length
          ) {
            const perDayEmployeeData =
              response.data.getPerDayProductiveHours.activityDetails;
            // Grouping data by employeeId
            const groupedData = this.groupDataByEmployeeId(perDayEmployeeData);

            // Sorting each group based on totalSystemUpTimeInMinutes
            const sortedData = this.sortGroupedData(
              groupedData,
              this.openedChartOrder,
              this.sortingMetric
            );
            // Output the grouped data
            const firstTenRecords = sortedData.slice(0, 10);

            // get dateArray
            const dateArray = this.generateDateArray(
              this.startDate,
              this.endDate
            );

            let structuredRecords = firstTenRecords.map(({ employeeName }) => ({
              name: employeeName,
              data: dateArray.map((date) => ({
                x: date.activityDate,
                y: 0,
              })),
            }));

            structuredRecords.forEach((record, index) => {
              record.data.forEach((dataItem) => {
                const matchingActivity = firstTenRecords[
                  index
                ].activityData.find(
                  (activity) => activity.activityDate === dataItem.x
                );
                if (matchingActivity) {
                  dataItem.y = (
                    matchingActivity[this.sortingMetric] / 60
                  ).toFixed(2);
                }
              });
            });
            this.heatChartSeries = structuredRecords;
            this.lineChartSeries = structuredRecords;
          } else {
            this.heatChartSeries = [];
            this.lineChartSeries = [];
          }
          vm.commonChartLoading = false;
        })
        .catch((err) => {
          vm.commonChartLoading = false;
          this.handleGetPerDayProductiveHoursError(err);
        });
    },
    // get employee's default work schedule details
    async fetchEmpWorkScheduleDetails() {
      let vm = this;
      try {
        await this.$store
          .dispatch("getEmpWorkScheduleDetails", {
            employeeId: vm.loginEmployeeId,
          })
          .then((employeeWorkScheduleDetails) => {
            const { workSchedule, workScheduleId, timeZone } =
              employeeWorkScheduleDetails;
            vm.employeeWorkScheduleId = workScheduleId;
            this.reloadCharts(vm.employeeWorkScheduleId, vm.customGroupId);
            this.getPerDayProductiveHours();
            vm.employeeWorkSchedule = workSchedule;
            vm.employeeTimezone = timeZone;
            vm.loginEmployeeWSDetails = employeeWorkScheduleDetails;
          })
          .catch(() => {
            vm.handleEmpWorkScheduleError();
          });
      } catch {
        vm.handleEmpWorkScheduleError();
      }
    },

    // set default value((1) when we get error while retrieving employee's work-schedule details
    handleEmpWorkScheduleError() {
      this.employeeWorkScheduleId = 1;
      this.reloadCharts(this.employeeWorkScheduleId, this.customGroupId);
      this.getPerDayProductiveHours();
      this.employeeTimezone = "";
    },
    // get work schedules list
    fetchWorkScheduleList() {
      let vm = this;
      try {
        vm.$apollo
          .query({
            query: LIST_WORK_SCHEDULE,
            client: "apolloClientC",
            variables: {
              formName: "cxo-dashboard",
            },
          })
          .then((response) => {
            const { errorCode, workSchedule } = response.data.listWorkSchedule;
            if (!errorCode && workSchedule) {
              let WSList = [
                {
                  WorkSchedule_Name: "All",
                  WorkSchedule_Id: 0,
                  Time_Zone: "",
                },
              ];
              vm.workScheduleList = WSList.concat(workSchedule);
            } else {
              vm.handleWorkScheduleListErr();
            }
          })
          .catch(() => {
            vm.handleWorkScheduleListErr();
          });
      } catch {
        vm.handleWorkScheduleListErr();
      }
    },

    handleWorkScheduleListErr() {
      this.workScheduleList = [];
    },
    // on changing the work schedule we have to set timezone and work schedule details
    onSelectWorkSchedule(workSchedule) {
      this.customGroupId = 0;
      this.selectedCustomGroup = "All";
      this.reloadCharts(workSchedule.WorkSchedule_Id, this.customGroupId);
      this.employeeWorkScheduleId = workSchedule.WorkSchedule_Id;
      this.getPerDayProductiveHours();
      this.employeeWorkSchedule = workSchedule.WorkSchedule_Name;
      if (workSchedule.WorkSchedule_Name === "All") {
        this.employeeTimezone = this.loginEmployeeWSDetails.timeZone;
      } else {
        this.employeeTimezone = workSchedule.TimeZone_Id;
      }
    },
    reloadCharts(workScheduleId = 0, customGroupId = 0) {
      this.getTotalSystemUpTimeHoursForAMonth(
        "systemUpTime",
        this.systemUpTimeSort,
        workScheduleId,
        customGroupId
      );
      this.getTotalActiveTimeHoursForAMonth(
        "activeTime",
        this.activeTimeSort,
        workScheduleId,
        customGroupId
      );
      this.getTotalIdleTimeHoursForAMonth(
        "idleTime",
        this.idleTimeSort,
        workScheduleId,
        customGroupId
      );
      this.getTotalProductiveAppHoursForAMonth(
        "timeSpentOnProductiveApps",
        this.productiveTimeSort,
        workScheduleId,
        customGroupId
      );
      this.getTotalComputerActiveTimeHoursForAMonth(
        "computerActivityTime",
        this.computerActivityTimeSort,
        workScheduleId,
        customGroupId
      );
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    onChangeDateRange(selectedDates, dateStr) {
      this.isExceed31Days = false;
      if (dateStr.includes("to")) {
        if (dateStr != this.currentDateRange) {
          let splittedDate = dateStr.split(" to ");
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          const startMoment = moment(splittedDate[0], orgDateFormat);
          const endMoment = moment(splittedDate[1], orgDateFormat);
          const diffInDays = endMoment.diff(startMoment, "days") + 1; // Adding 1 to include both the start and end dates
          if (diffInDays <= 31) {
            // Parse the dates from the given format
            let parsedStartDate = moment(splittedDate[0], "DD/MM/YYYY");
            let parsedEndDate = moment(splittedDate[1], "DD/MM/YYYY");

            // Format the dates into "YYYY-MM-DD" format
            this.startDate = parsedStartDate.format("YYYY-MM-DD");
            this.endDate = parsedEndDate.format("YYYY-MM-DD");
            this.reloadCharts(this.employeeWorkScheduleId, this.customGroupId);
            this.getPerDayProductiveHours();
          } else {
            this.isExceed31Days = true;
          }
        }
      } else {
        this.$emit("on-change-date-range", dateStr);
      }
      if (this.isExceed31Days) {
        this.appliedDateRange = this.currentDateRange;
        let snackbarData = {
          isOpen: true,
          message: "Please select a date range of less than 31 days",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    getCurrentDateRange() {
      // Get current month's 1st date
      const firstDayOfMonth = moment().startOf("month").format("DD/MM/YYYY");
      // Get yesterday's date
      const yesterdayDate = moment().subtract(1, "day").format("DD/MM/YYYY");
      // Parse the dates from the given format
      let parsedStartDate = moment(firstDayOfMonth, "DD/MM/YYYY");
      let parsedEndDate = moment(yesterdayDate, "DD/MM/YYYY");
      const differenceInDays = parsedEndDate.diff(parsedStartDate, "days");
      if (differenceInDays > 0) {
        // Format the dates into "YYYY-MM-DD" format
        this.startDate = parsedStartDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.currentDateRange = firstDayOfMonth + " to " + yesterdayDate;
      } else {
        this.startDate = parsedEndDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.currentDateRange = yesterdayDate + " to " + yesterdayDate;
      }
      this.appliedDateRange = this.currentDateRange;
    },
    handleGetPerDayProductiveHoursError(err = "") {
      // this.isCategoryListUpdating = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "CXO Dashboard",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    handleGetTotalProductiveHoursError(err = "") {
      // this.isCategoryListUpdating = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "CXO Dashboard",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    sortGroupedData(groupedData, order, sortingMetric) {
      return Object.values(groupedData).sort((a, b) => {
        const sumA = a.activityData.reduce(
          (sum, data) => sum + data[sortingMetric],
          0
        );
        const sumB = b.activityData.reduce(
          (sum, data) => sum + data[sortingMetric],
          0
        );
        if (order === 1) {
          return sumB - sumA; // Sorting in descending order
        } else {
          return sumA - sumB;
        }
      });
    },
    groupDataByEmployeeId(perDayEmployeeData) {
      const groupedData = perDayEmployeeData.reduce((acc, curr) => {
        const { employeeId, employeeName, activityData } = curr;
        if (!acc[employeeId]) {
          acc[employeeId] = {
            employeeId,
            employeeName,
            activityData: [],
          };
        }
        acc[employeeId].activityData.push(activityData);
        return acc;
      }, {});
      return groupedData;
    },
    generateDateArray(startDate, endDate) {
      const dateArray = [];
      let currentDate = new Date(startDate);
      const lastDate = new Date(endDate);

      while (currentDate <= lastDate) {
        const formattedDate = currentDate.toISOString().slice(0, 10);
        dateArray.push({ activityDate: formattedDate });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return dateArray;
    },
    async retrieveCustomGroups() {
      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "productivityMonitoring",
        })
        .then((groupList) => {
          if (groupList && groupList.length) {
            // this.customGroupList = groupList ? groupList : [];
            const customGroups = groupList;
            let CGList = [
              {
                Custom_Group_Name: "All",
                Custom_Group_Id: 0,
              },
            ];
            this.customGroupList = CGList.concat(customGroups);
          } else {
            this.customGroupList = [];
          }
        })
        .catch(() => {
          this.customGroupList = [];
        });
    },
    onSelectCustomGroup(customGroup) {
      this.employeeWorkScheduleId = 0;
      this.employeeWorkSchedule = "All";
      this.customGroupId = customGroup.Custom_Group_Id;
      this.reloadCharts(this.employeeWorkScheduleId, this.customGroupId);
      this.getPerDayProductiveHours();
      this.selectedCustomGroup = customGroup.Custom_Group_Name;
    },
    resetFilter() {
      this.selectedCustomGroup = "All";
      this.customGroupId = 0;
      this.employeeWorkScheduleId = 0;
      this.employeeWorkSchedule = "All";
      this.reloadCharts(this.employeeWorkScheduleId, this.customGroupId);
      this.getPerDayProductiveHours();
    },
  },
};
</script>
<style scoped>
::v-deep .v-switch__track {
  height: 8px;
  width: 15px;
}
::v-deep .v-switch__thumb {
  height: 13px;
  width: 13px;
}
</style>
