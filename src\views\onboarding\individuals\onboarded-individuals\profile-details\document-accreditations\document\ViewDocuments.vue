<template>
  <div
    v-if="documentDetails && documentDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No documents have been uploaded
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in documentDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width: 400px; border-left: 7px solid ${generateRandomColor()}; height:200px;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip :text="data.Document_Name" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="data.Document_Name ? props : ''"
                    >
                      {{ checkNullValue(data.Document_Name) }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>

      <div class="card-columns w-100 mt-n6">
        <span
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Category </b>
              <v-tooltip :text="data.Category_Fields" location="bottom">
                <template v-slot:activator="{ props }">
                  <div
                    class="py-2"
                    :style="
                      isMobileView ? 'max-width: 200px' : 'max-width:140px'
                    "
                    v-bind="data.Category_Fields ? props : ''"
                  >
                    {{ checkNullValue(data.Category_Fields) }}
                  </div>
                </template>
              </v-tooltip>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Sub Type </b>
              <span class="py-2"> {{ data.Document_Sub_Type }}</span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Type </b>
              <span class="py-2">
                {{ checkNullValue(data.Document_Type) }}</span
              >
            </div>
            <div
              v-if="data.File_Name"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span class="text-blue-grey-darken-3 font-weight-bold"></span>
              <span class="text-blue-grey-darken-6">
                <span
                  style="text-decoration: underline"
                  @click="
                    retrieveDocuments(
                      data.File_Name,
                      data.Document_Sub_Type_Id,
                      index
                    )
                  "
                  class="text-green cursor-pointer"
                >
                  View Document</span
                >
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employees Document Upload"
    fileRetrieveType="documents"
    :heading="heading"
    :current="selectedIndex"
    :length="documentDetails.length"
    :document-details="selectedDocument"
    :visibleDetails="visibleDetails"
    @prev-document="changeSelectedDocument(-1)"
    @next-document="changeSelectedDocument(1)"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

export default {
  name: "ViewDocuments",
  components: { FilePreviewModal, ActionMenu },

  props: {
    documentDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    customDocumentList: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      retrievedFileName: "",
      openModal: false,
      havingAccess: {},
      selectedDocument: {},
      selectedIndex: 0,
      selectedSubType: null,
      documentInfo: {},
      heading: "",
      visibleDetails: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return this.formAccess && this.formAccess.update;
    },
  },
  methods: {
    //using the generateRandomColor function of helper.js file
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.documentDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    retrieveDocuments(fileName, documentId, index) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.selectedIndex = index;
      vm.heading = this.documentDetails[index].Document_Sub_Type;
      vm.selectedSubType = documentId;
      vm.selectedDocument = this.customDocumentList[documentId];
      if (vm.selectedDocument && vm.selectedDocument.length > 0) {
        for (let doc of vm.selectedDocument) {
          if (doc.Field_Visiblity == "Yes") {
            this.visibleDetails = true;
            break;
          }
          this.visibleDetails = false;
        }
      } else {
        this.visibleDetails = false;
      }
      vm.openModal = true;
    },
    changeSelectedDocument(step) {
      this.selectedIndex += step;
      if (
        this.documentDetails[this.selectedIndex] &&
        this.documentDetails[this.selectedIndex]?.File_Name
      ) {
        this.retrievedFileName =
          this.documentDetails[this.selectedIndex]?.File_Name;
        this.heading =
          this.documentDetails[this.selectedIndex]?.Document_Sub_Type;

        this.selectedSubType =
          this.documentDetails[this.selectedIndex]?.Document_Sub_Type_Id;
        this.selectedDocument = this.customDocumentList[this.selectedSubType];
        if (
          this.selectedDocument &&
          this.selectedDocument.length &&
          this.selectedDocument.length > 0
        ) {
          for (let doc of this.selectedDocument) {
            if (doc.Field_Visiblity == "Yes") {
              this.visibleDetails = true;
              break;
            }
            this.visibleDetails = false;
          }
        } else {
          this.visibleDetails = false;
        }
      }
    },
  },
};
</script>
