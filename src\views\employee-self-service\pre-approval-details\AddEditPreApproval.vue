<template>
  <div :class="isListEmpty ? 'd-flex align-center justify-center' : ''">
    <v-card
      :width="isListEmpty && !isMobileView ? '80%' : ''"
      class="rounded-lg"
      :class="
        isListEmpty ? 'd-flex align-center flex-column justify-center h-50' : ''
      "
    >
      <div class="d-flex align-center" :style="containerStyle">
        <div class="d-flex align-center text-label">
          <v-chip
            class="text-subtitle-1 pa-7 mr-2 rounded-ts-lg rounded-be-xl text-white"
            style="background-color: rgb(var(--v-theme-primary)) !important"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">
            {{ labelList[465]?.Field_Alias }}</span
          >
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-tooltip v-model="showToolTip" location="top">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="isFormDirty ? '' : props"
                  rounded="lg"
                  color="primary"
                  class="mb-2"
                  type="submit"
                  @click="isFormDirty ? addEditResponse() : ''"
                  ><span class="px-2">Apply</span></v-btn
                >
              </template>
              <div v-if="!isFormDirty">There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" color="primary" class="mr-1">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <div
        style="height: calc(100vh - 250px); overflow-y: scroll"
        :class="containerClass"
      >
        <v-card-text class="pa-8">
          <v-form ref="preApprovalForm">
            <v-row>
              <v-col
                v-if="labelList[464]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0"
              >
                <v-autocomplete
                  v-if="isEdit"
                  :items="preApprovalTypes"
                  item-title="selectedItem.preApprovalType"
                  item-value="selectedItem.preApprovalType"
                  :loading="preApprovalTypeLoading"
                  v-model="preApprovalType"
                  :disabled="true"
                  variant="solo"
                  ><template v-slot:label>
                    <span>{{ labelList[464].Field_Alias }}</span>
                    <span
                      v-if="
                        labelList[464].Mandatory_Field.toLowerCase() == 'yes'
                      "
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template></v-autocomplete
                >
                <CustomSelect
                  v-else
                  :items="preApprovalTypeItems"
                  :is-auto-complete="true"
                  :label="labelList[464].Field_Alias"
                  :isRequired="
                    labelList[464].Mandatory_Field.toLowerCase() === 'yes'
                  "
                  :itemSelected="preApprovalType"
                  @selected-item="onPreApprovalSelect($event)"
                  :rules="[
                    labelList[464].Mandatory_Field.toLowerCase() === 'yes'
                      ? required(labelList[464].Field_Alias, preApprovalType)
                      : true,
                  ]"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0"
              >
                <v-autocomplete
                  v-if="isEdit"
                  :items="employeeNameList"
                  item-title="empNameId"
                  item-value="employeeId"
                  label="Employee Name"
                  v-model="employeeName"
                  :disabled="true"
                  variant="solo"
                  ><template v-slot:label>
                    <span>Employee Name</span>
                    <span class="ml-1" style="color: red">*</span>
                  </template></v-autocomplete
                >
                <CustomSelect
                  v-else
                  isAutoComplete="true"
                  :isRequired="true"
                  :items="employeeNameList"
                  item-title="empNameId"
                  item-value="employeeId"
                  id-required="employeeId"
                  label="Employee Name"
                  :itemSelected="employeeId"
                  :isLoading="empListFetching"
                  @selected-item="
                    employeeId = $event;
                    getPreApprovalSetting();
                    presentStartMonthIndex = null;
                    presentEndMonthIndex = null;
                    weekOffDurationData = [];
                    this.isFormDirty = true;
                    this.totalDays = 0;
                  "
                  :rules="[required('Employee Name', employeeId)]"
                ></CustomSelect>
              </v-col>
              <v-col
                v-if="
                  preApprovalType !== 'Work during week off' &&
                  preApprovalType !== 'Work during holiday'
                "
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0"
              >
                <CustomSelect
                  :items="durationOptions"
                  label="Duration"
                  isAutoComplete="true"
                  :isRequired="true"
                  :itemSelected="duration"
                  @selected-item="
                    duration = $event;
                    handleDurationAndDate();
                  "
                  :rules="[required('Duration', duration)]"
                ></CustomSelect>
              </v-col>
              <v-col
                v-if="
                  duration === 'Half Day' &&
                  (preApprovalType?.toLowerCase() === 'work from home' ||
                    preApprovalType?.toLowerCase() === 'on duty')
                "
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0"
              >
                <CustomSelect
                  :items="periodOptions"
                  label="Period"
                  :isRequired="true"
                  isAutoComplete="true"
                  :itemSelected="period"
                  @selected-item="
                    period = $event;
                    this.isFormDirty = true;
                  "
                  :rules="[required('Period', period)]"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0"
              >
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0"
              >
                <p class="custom-label">
                  Start Date<span class="ml-1" style="color: red">*</span>
                </p>
                <datepicker
                  :format="orgDateFormat"
                  :disabled="!startDateDisabled || setDatePickerDisable"
                  input-class="date-input"
                  :open-date="setStartDate"
                  v-model="startDate"
                  class="custom-date-picker"
                  name="Start Date*"
                  @click="getDatesToDisable()"
                  @changed-month="
                    handleChangeMonth($event);
                    handleChangeEndMonth($event);
                  "
                  @input="
                    formatDuration();
                    getDatesToDisable();
                    getEndDatesTodisable();
                    changeTotalDays();
                    generateDatesArray();
                    endDate = startDateInput;
                    this.isFormDirty = true;
                  "
                  :rules="[required('Start Date', startDate)]"
                  :disabled-dates="{
                    to: new Date(maxDate),
                    from: new Date(maxStartDate),
                    dates: disabledDatesArray,
                  }"
                ></datepicker>
                <div
                  v-if="startDateErrorMsg"
                  class="text-caption ml-4 mt-1"
                  style="color: #b00020"
                >
                  {{ startDateErrorMsg }}
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0"
              >
                <p class="custom-label">
                  End Date<span class="ml-1" style="color: red">*</span>
                </p>

                <datepicker
                  :disabled="
                    this.duration === 'Half Day' ||
                    endDateDisabled ||
                    setDatePickerDisable
                  "
                  input-class="date-input"
                  :format="orgDateFormat"
                  :open-date="startDate"
                  v-model="endDate"
                  @input="
                    generateDatesArray();
                    formatDuration();
                    this.isFormDirty = true;
                  "
                  @changed-month="handleChangeEndMonth($event)"
                  class="custom-date-picker"
                  :disabled-dates="{
                    to: new Date(minDate),
                    from: new Date(maxEndDate),
                    dates: [...endDisabledDatesArray],
                  }"
                  :rules="[required('End Date', endDate)]"
                ></datepicker>
                <div
                  v-if="endDateErrorMsg"
                  class="text-caption ml-4 mt-1"
                  style="color: #b00020"
                >
                  {{ endDateErrorMsg }}
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mt-3"
              >
                <v-text-field
                  v-model="totalDays"
                  label="Total Days*"
                  readonly="true"
                  :disabled="!totalDays"
                  suffix="day(s)"
                  variant="solo"
                  :rules="
                    totalDays
                      ? [
                          required('Total Days', totalDays),
                          validateTotalDays(totalDays),
                        ]
                      : [true]
                  "
                  ><template v-slot:label>
                    <span>Totat Days</span>
                    <span class="ml-1" style="color: red">*</span>
                  </template></v-text-field
                >
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mt-4"
                v-if="isEdit"
              >
                <v-autocomplete
                  :items="['Applied', 'Approved', 'Rejected', 'Cancelled']"
                  label="Status*"
                  disabled="true"
                  v-model="status"
                  :rules="[required('Status', status)]"
                ></v-autocomplete>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                :lg="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mt-4"
                v-if="isDurationGreaterThanThreshold"
              >
                <v-file-input
                  prepend-icon=""
                  show-size
                  :model-value="fileContent"
                  append-inner-icon="fas fa-paperclip"
                  label="Document*"
                  variant="solo"
                  :rules="[required('Document', fileContentRuleValue)]"
                  accept="image/png, image/jpeg, image/jpg, application/pdf"
                  @update:modelValue="onChangeFiles"
                  @click:clear="removeFiles"
                ></v-file-input>
              </v-col>
            </v-row>
            <v-col
              v-if="labelList[466]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              class="mt-8 pb-0"
            >
              <v-textarea
                class="text-area-class"
                v-model="reason"
                :label="labelList[466].Field_Alias"
                variant="outlined"
                @update:model-value="this.isFormDirty = true"
                auto-grow
                :rules="[
                  minMaxStringValidation('reason', reason, 3, 500),
                  alphaNumSpaceWithElevenSymbolValidation(reason),
                ]"
                rows="1"
              ></v-textarea
            ></v-col>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_UPDATE_PREAPPROVAL,
  RETRIEVE_PRE_APPROVAL_SETTINGS,
  GET_DISABLED_DATES,
} from "@/graphql/settings/core-hr/preApprovalQueries";
import { LIST_WORK_PLACES } from "@/graphql/organisation/employeetype/employeeTypeQueries";
import { getErrorCodesWithValidation, getErrorCodes } from "@/helper.js";
export default {
  name: "AddEditPreApprovals",
  mixins: [validationRules],
  components: {
    Datepicker,
    CustomSelect,
  },
  props: {
    selectedItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEmpty: {
      type: Boolean,
      default: false,
    },

    editedPreApprovalDetails: {
      type: Object,
      default: () => {
        return {};
      },
    },
    preApprovalEmployeeObject: {
      type: Object,
      required: true,
    },
    fromViewForm: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isListEmpty: {
      type: Boolean,
      default: false,
    },
    callingFrom: {
      type: String,
      default: "employee",
    },
  },
  data() {
    return {
      openConfirmationPopup: false,
      preApprovalType: !this.isEdit ? "" : this.selectedItem.preApprovalType,
      preApprovalId: !this.isEdit ? 0 : this.selectedItem.preApprovalId,
      employeeName: !this.isEdit ? "" : this.selectedItem.employeeName,
      employeeId: !this.isEdit ? null : this.selectedItem.employeeId,
      userDefinedEmpId: this.isEdit ? this.selectedItem.userDefinedEmpId : 0,
      saveButtonDisabled: false,
      isDateChanged: false,
      userIs: "",
      status: !this.isEdit ? "Applied" : this.selectedItem.status,
      duration: !this.isEdit ? "Full Day" : this.selectedItem.duration,
      totalDays: !this.isEdit ? "" : parseInt(this.selectedItem.totalDays),
      advanceNotificationDays: 0,
      noOfPreApprovalRequest: null,
      reason: !this.isEdit ? "" : this.selectedItem.reason,
      period: !this.isEdit ? "" : this.selectedItem.period,
      periodOptions: ["First Half", "Second Half"],
      employeeNameList: [],
      startDate: !this.isEdit ? "" : this.selectedItem.startDate,
      endDate: !this.isEdit ? "" : this.selectedItem.endDate,
      durationOptions: ["Full Day", "Half Day"],
      startDateErrorMsg: "",
      endDateErrorMsg: "",
      setDatePickerDisable: false,
      tooltipContent: "Please enter the correct details",
      empListFetching: false,
      isFormDirty: false,
      isLoading: false,
      presentStartMonthIndex: null,
      presentEndMonthIndex: null,
      presentStartYear: null,
      presentEndYear: null,
      endDisabledDatesArray: [],
      endDateHolidayDates: [],
      dateArray: [],
      count: 0,
      employeeDetailsObject: {},
      preApprovalTypeItems: [],
      disabledDatesArray: [],
      holidayDates: [],
      weekOffDurationData: [],
      maxDaysForDocumentUpload: 0,
      maxDaysAllowedPerRequest: null,
      documentUpload: "No",
      fileName: !this.isEdit ? "" : this.selectedItem.fileName,
      fileSize: !this.isEdit ? "" : this.selectedItem.fileSize,
      isFileChanged: false,
      fileContent: null,
      showToolTip: false,
      prevVal: null,
      preApprovalTypes: ["Work during week off", "Work during holiday"],
      preApprovalTypeLoading: false,
      typeOfDay: null,
      lastDisableDatesParams: {
        month: null,
        year: null,
        employeeId: null,
        preApprovalType: null,
      },
    };
  },
  methods: {
    retrievePreApprovalTypes() {
      let vm = this;
      vm.preApprovalTypeLoading = true;
      vm.$apollo
        .query({
          query: LIST_WORK_PLACES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listWorkPlaces) {
            let responseData = JSON.parse(
              response.data.listWorkPlaces.workPlaces
            );
            if (responseData?.length) {
              //Filter the responseData where Pre_Approval is Yes
              let applicablePreApprovals = responseData
                .filter((item) => {
                  return item.Pre_Approval == "Yes";
                })
                .map((item) => item.Work_Place);
              vm.preApprovalTypes = applicablePreApprovals.length
                ? vm.preApprovalTypes.concat(applicablePreApprovals)
                : vm.preApprovalTypes;
            }
          }
          vm.preApprovalTypeLoading = false;
        })
        .catch(() => {
          vm.preApprovalTypeLoading = false;
        });
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File";
      }
      return "";
    },
    onChangeFiles(value) {
      this.fileContent = value;
      if (this.fileContent && this.fileContent.name && this.fileContent.size) {
        this.fileName =
          this.employeeId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent.name;
        this.fileSize = this.fileContent.size.toString();
        this.isFileChanged = true;
        this.onChangeIsFormDirty();
      }
    },
    removeFiles() {
      this.fileName = "";
      this.fileSize = "";
      this.fileContent = null;
      this.onChangeIsFormDirty();
    },
    async uploadFileContents() {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/PreApproval Request/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.fileName,
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            message:
              this.isEdit === true
                ? "Pre-approval request updated successfully."
                : "Pre-approval request added successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          if (this.fromViewForm) {
            this.$emit("handle-add-edit-response");
          } else {
            this.$emit("refetch-list");
          }
          this.isFormDirty = true;
          vm.$emit("close-loader");
          vm.isLoading = false;
          this.acceptClose();
          this.$emit("close-view-form");
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
    async formatDuration() {
      if (this.startDate && this.duration === "Half Day") {
        this.endDate = this.startDate;
      }
      const start = new Date(this.startDate);
      const end = new Date(this.endDate);
      const options = { month: "short", day: "numeric", year: "numeric" };
      const formattedStartDate = start.toLocaleDateString("en-US", options);
      const startMoment = moment(this.startDate);
      const endMoment = moment(this.endDate);

      // Calculate the duration between the two dates

      const formattedEndDate = end.toLocaleDateString("en-US", options);
      if (
        this.startDate &&
        this.endDate &&
        this.duration === "Half Day" &&
        (this.preApprovalType.toLowerCase() === "work from home" ||
          this.preApprovalType.toLowerCase() === "on duty")
      ) {
        this.endDate = this.startDate;
        this.totalDays = "0.5";
        return;
      }
      if (
        !this.startDate &&
        !this.endDate &&
        this.duration === "Half Day" &&
        (this.preApprovalType.toLowerCase() === "work from home" ||
          this.preApprovalType.toLowerCase() === "on duty")
      ) {
        this.startDate = "";
        this.endDate = "";
        this.totalDays = "0.5";
        return;
      }
      if (
        this.startDate &&
        !this.endDate &&
        this.duration === "Half Day" &&
        (this.preApprovalType.toLowerCase() === "work from home" ||
          this.preApprovalType.toLowerCase() === "on duty")
      ) {
        this.endDate = this.startDate;
        this.totalDays = "0.5";
        return;
      }
      if (
        this.startDate &&
        this.endDate &&
        formattedStartDate === formattedEndDate
      ) {
        if (this.duration === "Full Day" || this.duration === "") {
          this.totalDays = "1";
          this.period = "";
        } else {
          this.startDate = start;
          this.endDate = start;
          this.totalDays = "0.5";
          return;
        }
      } else if (
        this.startDate &&
        this.endDate &&
        formattedEndDate !== formattedStartDate
      ) {
        this.period = "";
        const duration =
          moment.duration(endMoment.diff(startMoment)).asDays() + 1;
        if (
          this.preApprovalType.toLowerCase() === "work from home" ||
          this.preApprovalType.toLowerCase() === "on duty"
        ) {
          this.getWeekendCount();
          this.totalDays = parseInt(duration - this.count);
        } else {
          this.totalDays = parseInt(duration);
        }
      } else {
        this.totalDays = "";
      }
    },

    getWeekendCount() {
      const formattedDisabledDates = this.disabledDatesArray.map((date) =>
        new Date(date).toISOString().slice(0, 10)
      );
      this.count = this.dateArray.filter((date) =>
        formattedDisabledDates.includes(date)
      ).length;
      return formattedDisabledDates;
    },
    generateNoOfPreApprovalMaxCount() {
      if (this.startDate) {
        // let totalDisableCount=0;
        const dateArray = [];
        // this.getDatesToDisable();
        const start = new Date(this.startDate);
        const formattedDisabledDates = this.disabledDatesArray.map((date) =>
          new Date(date).toISOString().slice(0, 10)
        );
        while (dateArray.length < this.noOfPreApprovalRequest) {
          if (
            !formattedDisabledDates.includes(
              new Date(start).toISOString().substring(0, 10)
            )
          ) {
            dateArray.push(new Date(start).toISOString().substring(0, 10));
          }
          start.setDate(start.getDate() + 1);
        }

        return dateArray[dateArray.length - 1];
      }
    },
    generateDatesArray() {
      if (this.startDate && this.endDate) {
        this.dateArray = [];
        const start = new Date(this.startDate);
        const end = new Date(this.endDate);

        while (start <= end) {
          this.dateArray.push(new Date(start).toISOString().substring(0, 10));
          start.setDate(start.getDate() + 1);
        }

        this.datesBetween = this.dateArray;
      }
    },
    handleChangeMonth(payload) {
      let date;
      if (payload.timestamp) {
        date = new Date(payload.timestamp);
      } else {
        date = new Date(payload);
      }
      this.presentStartMonthIndex = date.getMonth() + 1;
      this.presentStartYear = date.getFullYear();
      this.getDatesToDisable();
    },
    handleChangeEndMonth(payload) {
      const date = new Date(payload);
      this.presentEndMonthIndex = date.getMonth() + 1;
      this.getEndDatesTodisable();
    },

    getDatesToDisable() {
      if (this.employeeId) {
        if (this.startDate && !this.presentStartMonthIndex) {
          const date = new Date(this.startDate);
          this.presentStartMonthIndex = date.getMonth() + 1;
          this.presentStartYear = date.getFullYear();
        } else if (!this.startDate && !this.presentStartMonthIndex) {
          const currentDate = new Date();
          const date = new Date(
            currentDate.setDate(
              currentDate.getDate() + this.advanceNotificationDays
            )
          );
          this.presentStartMonthIndex = date.getMonth() + 1;
          this.presentStartYear = date.getFullYear();
        }
        this.getDisableDates(
          this.presentStartMonthIndex,
          "startDateDatesFetch"
        );
      } else {
        this.isLoading = false;
        this.disabledDatesArray = [];
      }
    },
    getDisableDates(monthIndex, identity) {
      let vm = this;
      const currentParams = {
        month: monthIndex,
        year: this.presentStartYear,
        employeeId: this.employeeId,
        preApprovalType: this.preApprovalType,
      };

      // Check if parameters are the same as the last call
      const isSameAsLastCall = Object.entries(currentParams).every(
        ([key, value]) => this.lastDisableDatesParams[key] === value
      );

      // If parameters haven't changed, skip the API call
      if (isSameAsLastCall) {
        return;
      }

      // Update last used parameters
      this.lastDisableDatesParams = { ...currentParams };
      vm.disabledDatesArray = [];
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_DISABLED_DATES,
          fetchPolicy: "no-cache",
          variables: {
            month: monthIndex,
            year: this.presentStartYear,
            employeeId: this.employeeId,
            formId: vm.formId,
          },
          client: "apolloClientI",
        })
        .then(async (res) => {
          if (res && res.data && res.data.getEmployeeWeekOffAndHolidayDetails) {
            const { weekOffAndHolidayDetails } =
              res.data.getEmployeeWeekOffAndHolidayDetails;
            if (this.preApprovalType === "Work during week off") {
              if (identity === "startDateDatesFetch") {
                this.disabledDatesArray = weekOffAndHolidayDetails
                  .filter((obj) => obj.isWeekOffDay !== 1)
                  .map((obj) => new Date(obj.date));
              } else {
                this.endDisabledDatesArray = weekOffAndHolidayDetails
                  .filter((obj) => obj.isWeekOffDay !== 1)
                  .map((obj) => new Date(obj.date));
              }
            } else if (this.preApprovalType === "Work during holiday") {
              if (identity === "startDateDatesFetch") {
                this.disabledDatesArray = weekOffAndHolidayDetails
                  .filter((obj) => obj.isHoliday !== 1)
                  .map((obj) => new Date(obj.date));
              } else {
                this.endDisabledDatesArray = weekOffAndHolidayDetails
                  .filter((obj) => obj.isHoliday !== 1)
                  .map((obj) => new Date(obj.date));
              }
            } else if (
              this.preApprovalType.toLowerCase() === "work from home"
            ) {
              if (identity === "startDateDatesFetch") {
                this.disabledDatesArray = weekOffAndHolidayDetails
                  .filter((obj) => {
                    return (
                      obj.isShiftScheduled === 0 ||
                      obj.isHoliday === 1 ||
                      (obj.isWeekOffDay === 1 && obj.weekOffDuration === 1)
                    );
                  })
                  .map((obj) => new Date(obj.date));
              } else {
                this.endDisabledDatesArray = weekOffAndHolidayDetails
                  .filter((obj) => {
                    return (
                      obj.isShiftScheduled === 0 ||
                      obj.isHoliday === 1 ||
                      (obj.isWeekOffDay === 1 && obj.weekOffDuration === 1)
                    );
                  })
                  .map((obj) => new Date(obj.date));
              }
            } else if (this.preApprovalType?.toLowerCase() === "on duty") {
              let typeOfDay = JSON.parse(this.typeOfDay);
              if (typeOfDay && typeOfDay.length) {
                if (identity === "startDateDatesFetch") {
                  this.disabledDatesArray = weekOffAndHolidayDetails
                    .filter((obj) => {
                      if (
                        typeOfDay.includes("Business Working Day") &&
                        typeOfDay.includes("Holiday") &&
                        typeOfDay.includes("Week Off")
                      ) {
                        return obj.isShiftScheduled === 0;
                      } else if (
                        typeOfDay.includes("Business Working Day") &&
                        typeOfDay.length === 1
                      ) {
                        return (
                          obj.isShiftScheduled === 0 ||
                          obj.isWeekOffDay === 1 ||
                          obj.isHoliday === 1
                        );
                      } else if (
                        typeOfDay.includes("Week Off") &&
                        typeOfDay.length === 1
                      ) {
                        return obj.isWeekOffDay === 0 || obj.isHoliday === 1;
                      } else if (
                        typeOfDay.includes("Holiday") &&
                        typeOfDay.length === 1
                      ) {
                        return obj.isHoliday === 0 || obj.isWeekOffDay === 1;
                      } else if (
                        typeOfDay.includes("Business Working Day") &&
                        typeOfDay.includes("Week Off")
                      ) {
                        return (
                          obj.isShiftScheduled === 0 || obj.isHoliday === 1
                        );
                      } else if (
                        typeOfDay.includes("Business Working Day") &&
                        typeOfDay.includes("Holiday")
                      ) {
                        return (
                          obj.isShiftScheduled === 0 || obj.isWeekOffDay === 1
                        );
                      } else if (
                        typeOfDay.includes("Week Off") &&
                        typeOfDay.includes("Holiday")
                      ) {
                        return obj.isWeekOffDay === 0 && obj.isHoliday === 0;
                      }
                    })
                    .map((obj) => new Date(obj.date));
                } else {
                  this.endDisabledDatesArray = weekOffAndHolidayDetails
                    .filter((obj) => {
                      if (
                        typeOfDay.includes("Business Working Day") &&
                        typeOfDay.includes("Holiday") &&
                        typeOfDay.includes("Week Off")
                      ) {
                        return obj.isShiftScheduled === 0;
                      } else if (
                        typeOfDay.includes("Business Working Day") &&
                        typeOfDay.length === 1
                      ) {
                        return (
                          obj.isShiftScheduled === 0 ||
                          obj.isWeekOffDay === 1 ||
                          obj.isHoliday === 1
                        );
                      } else if (
                        typeOfDay.includes("Week Off") &&
                        typeOfDay.length === 1
                      ) {
                        return obj.isWeekOffDay === 0 || obj.isHoliday === 1;
                      } else if (
                        typeOfDay.includes("Holiday") &&
                        typeOfDay.length === 1
                      ) {
                        return obj.isHoliday === 0 || obj.isWeekOffDay === 1;
                      } else if (
                        typeOfDay.includes("Business Working Day") &&
                        typeOfDay.includes("Week Off")
                      ) {
                        return (
                          obj.isShiftScheduled === 0 || obj.isHoliday === 1
                        );
                      } else if (
                        typeOfDay.includes("Business Working Day") &&
                        typeOfDay.includes("Holiday")
                      ) {
                        return (
                          obj.isShiftScheduled === 0 || obj.isWeekOffDay === 1
                        );
                      } else if (
                        typeOfDay.includes("Week Off") &&
                        typeOfDay.includes("Holiday")
                      ) {
                        return obj.isWeekOffDay === 0 && obj.isHoliday === 0;
                      }
                    })
                    .map((obj) => new Date(obj.date));
                }
              }
            }
            vm.isLoading = false;
          } else {
            vm.isLoading = false;
            this.disabledDatesArray = [];
          }
        })

        .catch((err) => {
          this.handleDatesApiErrors(err);
          vm.isLoading = false;
        });
    },
    getEndDatesTodisable() {
      if (this.employeeId) {
        this.endDisabledDatesArray = [];
        if (this.startDate && this.presentEndMonthIndex) {
          const date = new Date(this.startDate);
          this.presentEndMonthIndex = date.getMonth() + 1;
        }
        if (this.endDate && !this.presentEndMonthIndex) {
          const date = new Date(this.endDate);
          this.presentEndMonthIndex = date.getMonth() + 1;
        } else if (this.startDate && !this.presentEndMonthIndex) {
          const date = new Date(this.startDate);
          this.presentEndMonthIndex = date.getMonth() + 1;
        } else if (!this.endDate && !this.presentEndMonthIndex) {
          const date = new Date();
          this.presentEndMonthIndex = date.getMonth() + 1;
        }
        this.getDisableDates(this.presentEndMonthIndex, "endDateDatesFetch");
      } else {
        return;
      }
    },

    getPreApprovalSetting() {
      let vm = this;
      vm.isLoading = true;
      vm.maxDaysAllowedPerRequest = null;
      if (
        (this.isEdit && this.employeeId) ||
        (this.preApprovalType && this.employeeId)
      ) {
        if (!this.isEdit) {
          this.startDate = "";
          this.endDate = "";
        }
        vm.$apollo
          .query({
            query: RETRIEVE_PRE_APPROVAL_SETTINGS,
            variables: {
              employeeId: this.employeeId,
              preApprovalType: this.preApprovalType,
              startDate: "",
              endDate: "",
              preApprovalId: this.preApprovalId,
              formId: vm.formId,
            },
            client: "apolloClientI",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            this.typeOfDay =
              res.data.retrievePreApprovalSettings.preApprovalSettings.typeOfDay;
            this.advanceNotificationDays =
              res.data.retrievePreApprovalSettings.preApprovalSettings.advanceNotificationDays;
            this.noOfPreApprovalRequest =
              res.data.retrievePreApprovalSettings.preApprovalSettings.noOfPreApprovalRequest;
            this.documentUpload =
              res.data.retrievePreApprovalSettings.preApprovalSettings.documentUpload;
            this.maxDaysForDocumentUpload =
              res.data.retrievePreApprovalSettings.preApprovalSettings.maxDaysForDocumentUpload;
            this.maxDaysAllowedPerRequest =
              res?.data?.retrievePreApprovalSettings?.preApprovalSettings?.maxDaysAllowedPerRequest;
            vm.isLoading = false;
            this.saveButtonDisabled = false;
            this.setDatePickerDisable = false;
            this.presentStartMonthIndex = null;
            this.presentEndMonthIndex = null;
            this.getDatesToDisable();
            this.getEndDatesTodisable();
          })
          .catch((err) => {
            this.setDatePickerDisable = true;
            this.handlePreApprovalApiError(err);
            this.saveButtonDisabled = true;
            vm.isLoading = false;
          });
      } else {
        vm.isLoading = false;
        return;
      }
    },

    async addEditResponse() {
      const { valid } = await this.$refs.preApprovalForm.validate();
      if (valid && this.startDate && this.endDate) {
        this.isLoading = true;
        let vm = this;
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_PREAPPROVAL,
            variables: {
              preApprovalId: this.preApprovalId,
              employeeId: this.employeeId
                ? parseInt(this.employeeId)
                : vm.loginEmployeeId,
              preApprovalType: this.preApprovalType,
              duration: this.duration,
              period: this.period,
              startDate: moment(this.startDate).format("yyyy-MM-DD"),
              endDate: moment(this.endDate).format("yyyy-MM-DD"),
              totalDays: parseInt(this.totalDays),
              reason: this.reason,
              fileName: this.fileName,
              fileSize: this.fileSize,
              status: this.status === "Approved" ? this.status : "Applied",
              formId: vm.formId,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            if (vm.fileName && vm.isFileChanged) {
              vm.uploadFileContents();
            } else {
              let snackbarData = {
                isOpen: true,
                message:
                  this.isEdit === true
                    ? "Pre-approval request updated successfully."
                    : "Pre-approval request added successfully.",
                type: "success",
              };
              vm.showAlert(snackbarData);
              if (this.fromViewForm) {
                this.$emit("handle-add-edit-response");
              } else {
                this.$emit("refetch-list");
              }
              vm.$emit("close-loader");
              vm.isLoading = false;
              vm.isFormDirty = false;
              this.acceptClose();
              this.$emit("close-view-form");
            }
          })
          .catch((err) => {
            vm.isLoading = false;

            vm.handleAddEditError(err);
          });
      } else {
        if (!this.startDate && !this.endDate) {
          this.startDateErrorMsg = "Start Date is required.";
          this.endDateErrorMsg = "End Date is required.";
        } else if (!this.startDate && this.endDate) {
          this.startDateErrorMsg = "Start Date is required.";
        } else if (this.startDate && !this.endDate) {
          this.endDateErrorMsg = "End Date is required.";
        }
      }
    },
    onChangeIsFormDirty() {
      this.isFormDirty = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.acceptClose();
      }
    },

    acceptClose() {
      this.$emit("close-edit-form");
      this.$emit("close-split-view");
    },
    handleDurationAndDate() {
      this.formatDuration();
      this.isFormDirty = true;
    },
    onPreApprovalSelect(type) {
      this.preApprovalType = type;
      this.isFormDirty = true;
    },
    async savePreApprovalDetails() {
      const { valid } = await this.$refs.preApprovalForm.validate();
      if (valid) {
        if (this.isEdit) {
          const updatedData = {
            employeeName: this.employeeName,
            employeeId: this.employeeId,
            totalDays: this.totalDays,
            preApprovalType: this.preApprovalType,
            startDate: this.startDate,
            endDate: this.endDate,
            status: this.status,
            duration: this.duration,
            reason: this.reason,
          };
          this.$emit("save-edited-data", updatedData);
          this.$emit("close-edit-form");
        } else {
          // this.$emit("add-preApproval-details",updatedData);
          this.$emit("close-split-view");
        }
      }
    },
    formatDate(date, withTime = false) {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
      return date ? moment(date).format(orgDateFormat) : "-";
    },
    //getPreApprovalEmployeeList

    getEmployeeList() {
      this.employeeDetailsObject = this.preApprovalEmployeeObject;
      for (let i in this.preApprovalEmployeeObject) {
        if (this.preApprovalEmployeeObject[i]) {
          if (
            i === "workDuringHoliday" &&
            this.preApprovalEmployeeObject[i].length > 0 &&
            !this.preApprovalTypeItems.includes("Work during holiday")
          ) {
            this.preApprovalTypeItems.push("Work during holiday");
          } else if (
            i === "workDuringWeekOff" &&
            this.preApprovalEmployeeObject[i].length > 0 &&
            !this.preApprovalTypeItems.includes("Work during week off")
          ) {
            this.preApprovalTypeItems.push("Work during week off");
          } else if (
            i === "workFromHome" &&
            this.preApprovalEmployeeObject[i].length > 0 &&
            !this.preApprovalTypeItems.includes("Work from home")
          ) {
            this.preApprovalTypeItems.push("Work from home");
          } else if (
            i === "onDuty" &&
            this.preApprovalEmployeeObject[i].length > 0 &&
            !this.preApprovalTypeItems.includes("On Duty")
          ) {
            this.preApprovalTypeItems.push("On Duty");
          }

          if (this.preApprovalTypeItems.length > 3) {
            break;
          }
        }
      }
      this.getPreApprovalSetting();
      if (!this.isEdit && !this.preApprovalType)
        this.preApprovalType = this.preApprovalTypeItems[0];
      if (!this.preApprovalTypeItems.length) {
        let snackbarData = {
          isOpen: true,
          message:
            "No active configuration exists for the selected pre-approval type",
          type: "warning",
        };
        this.showAlert(snackbarData);
        if (!this.isEdit) {
          this.employeeId = null;
          this.employeeName = null;
          this.presentEndMonthIndex = null;
        }
      }
    },

    validateTotalDays(value) {
      if (!value) return true; // Allow empty values if not required
      if (!this.maxDaysAllowedPerRequest) return true;
      return value <= this.maxDaysAllowedPerRequest
        ? true
        : "Total number of days should not be more than max days allowed per request ie  " +
            this.maxDaysAllowedPerRequest;
    },
    handlePreApprovalApiError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "CHR0043": // Technical error
            snackbarData.message =
              "The pre-approval configuration does not exist for the selected employee.";
            break;
          case "_DB0100 ":
            snackbarData.message =
              "Sorry, you don't have access rights to view the pre-approval-details details. Please contact the HR administrator";
            break;
          case "CHR0051":
            snackbarData.message =
              "The pre-approval configuration does not exist for the selected employee";
            break;
          case "CHR0052":
            snackbarData.message =
              "The pre-approval request is already exist for the given date.";
            break;
          default:
            snackbarData.message =
              "Something went wrong while retrieving the details pre-approval-settings. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while while retrieving the details pre-approval-settings. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    setUser() {
      let userDetails = this.accessRights("pre-approvals");
      if (userDetails.accessRights.admin === "admin") {
        this.userIs = "admin";
      } else if (userDetails.accessRights.isManager === 1) {
        this.userIs = "manager";
      }
    },
    handleAddEditError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodesWithValidation(err);
        var validationErrors = errorCode[1];
        var validationMessages = "";
        var errCode = "";
        switch (errorCode[0]) {
          case "CHR0050": // Technical error
            snackbarData.message =
              "Error while Add / Update the Pre-approval Request";
            break;
          case "CHR0051": // This employee does not have admin or manager access.
            snackbarData.message =
              "The pre-approval configuration does not exist for the selected employee.";
            break;
          case "CHR0052": // Organization details does not exists.
            snackbarData.message =
              "Error while deleting the workflow details. Please contact the system admin.";
            break;
          case "CHR0053":
            snackbarData.message =
              "There are some difficulties to add / update the pre-approval request. Please contact the system admin.";
            break;
          case "CHR0054":
            snackbarData.message =
              "Error while retrieving the workflow details. Please contact the system admin.";
            break;
          case "CHR0055":
            snackbarData.message =
              "Error while retrieving the pre-approval details. Please contact the system admin.";
            break;
          case "CHR0065":
            validationMessages = err.message;
            if (validationMessages) {
              snackbarData.message = validationMessages;
            }
            break;
          case "CHR0068":
            snackbarData.message =
              "Oops! Something went wrong while adding/updating pre-approval request, please contact the platform administrator.";
            break;

          case "CHR0069":
            snackbarData.message =
              "Sorry, an error occurred while adding/updating pre-approval request, please contact the platform administrator.";
            break;
          case "CHR0070":
            snackbarData.message =
              "The sandwich policy prohibits the selected dates due to the enabled restrict sandwich flag; kindly choose alternative dates.";
            break;
          case "CHR0071":
            snackbarData.message =
              "The restrict sandwich flag is enabled please select dates which will not violate the sandwich policy.";
            break;
          case "BAD_USER_INPUT":
            // add all the backend validation error messages as single sentence to present it to the users
            if (validationErrors) {
              for (errCode in validationErrors) {
                // IVE0316 - message: "Start date should not be empty.",message1: "Start date should not be less than the advance notification days".
                //  code: 'IVE0317',message: "End date should not be empty.", message1: "End date should be greater than start date.", message2: "You have already applied the pre-approval request for the selected date range",message3: "You are not allowed to apply more than the configuration limit"
                if (
                  errCode === "IVE0312" ||
                  errCode === "IVE0313" ||
                  errCode === "IVE0316" ||
                  errCode === "IVE0317"
                ) {
                  validationMessages =
                    validationMessages + " " + validationErrors[errCode];
                }
              }
            }
            if (validationMessages) {
              snackbarData.message = validationMessages;
            }
            // other validation errors are not handled by users. So as of now it was considers as common error.
            else {
              // IVE0083 - Please provide a valid Employee Id.
              snackbarData.message =
                "Something went wrong while adding/updating pre-approval-request. If you continue to see this issue please contact the platform administrator.";
            }

            break;
          default:
            snackbarData.message =
              "Something went wrong while adding/updating pre-approval-request. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while adding/updating pre-approval-request. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    handleDatesApiErrors(err) {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "CHR0061": // Technical error
            snackbarData.message =
              "Error while getting the week-off and holiday details of the employee.";
            break;
          case "_EC0010":
            snackbarData.message =
              "Error while retrieving the week-off and holiday dates for the employee.";
            break;
          case "_DB0100":
            snackbarData.message =
              "Sorry, you don't have access rights to view the pre-approval-details. Please contact the HR administrator";
            break;
          default:
            snackbarData.message =
              "Something went wrong while getting the week-off and holiday details of the employee";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while getting the week-off and holiday details of the employee";
      }
      this.showAlert(snackbarData);
    },
    async changeTotalDays() {
      if (this.duration === "Half Day") this.totalDays = "0.5";
      else this.totalDays = "0";
    },
  },

  computed: {
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
    formId() {
      let fId = this.callingFrom === "team" ? "257" : "247";
      return parseInt(fId);
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    startDateInput() {
      if (this.duration === "Half Day") {
        return this.startDate;
      } else {
        return "";
      }
    },
    setStartDate() {
      const currentDate = new Date();
      return this.isEdit
        ? new Date(this.startDate)
        : new Date(
            currentDate.setDate(
              currentDate.getDate() + this.advanceNotificationDays
            )
          );
    },
    containerClass() {
      if (this.isMobileView) {
        return "pa-4";
      } else {
        return this.isListEmpty ? "pa-12" : "pa-8";
      }
    },
    textAreaStyle() {
      if (this.isMobileView) {
        return { width: "100%" };
      } else {
        return { maxWidth: this.isListEmpty ? "92%" : "775px" };
      }
    },
    containerStyle() {
      if (this.isListEmpty) {
        return {
          justifyContent: "space-between",
          borderBottom: "2px solid #e5e5e5",
          width: "100%",
        };
      } else {
        return {
          justifyContent: "space-between",
          borderBottom: "2px solid #e5e5e5",
        };
      }
    },
    isDurationDisabled() {
      if (
        (this.startDate && this.endDate && parseFloat(this.totalDays) === 1) ||
        this.duration === "Half Day"
      ) {
        return false;
      } else {
        return true;
      }
    },
    endDateDisabled() {
      return (
        !(this.employeeId && this.preApprovalType) ||
        !this.startDate ||
        !(this.employeeId && this.preApprovalType)
      );
    },
    endDateWarningMessage() {
      return (
        (this.employeeId && this.preApprovalType) ||
        this.duration !== "Full Day"
      );
    },
    startDateDisabled() {
      return this.employeeId && this.preApprovalType;
    },
    minDate() {
      if (this.startDate) {
        const issueDateMs = new Date(this.startDate)
          .toISOString()
          .substring(0, 10);
        return issueDateMs;
      }
      return null;
    },
    maxEndDate() {
      if (this.startDate) {
        if (
          this.preApprovalType === "Work during week off" ||
          this.preApprovalType === "Work during holiday" ||
          this.preApprovalType === "On Duty"
        ) {
          let currentDate = moment(this.startDate);
          const disabledDates = this.disabledDatesArray.map((date) =>
            moment(date)
          );
          // Loop through the dates and check if they are present in the disabledDates array
          for (let i = 0; i < 31; i++) {
            if (
              !disabledDates.some((disabledDate) =>
                disabledDate.isSame(currentDate, "day")
              )
            ) {
              // If the current date is disabled, skip it and move to the next day
              currentDate.add(1, "day");
            } else {
              // If the current date is not disabled, proceed to the next steps
              break;
            }
          }
          return currentDate;
        }
        const preApprovalCount = this.generateNoOfPreApprovalMaxCount();
        const endOfMonth = moment(this.startDate).endOf("month");

        var newDate = "";
        if (this.isEdit) {
          newDate = moment(preApprovalCount);
          newDate = newDate.add(1, "day");
        } else {
          newDate = moment(preApprovalCount);
          newDate = newDate.add(1, "day");
        }
        let minDate = newDate;
        if (newDate.isAfter(endOfMonth)) {
          minDate = endOfMonth;
        }
        return minDate;
      }

      return null;
    },
    checkEndDateLesser() {
      return new Date(this.startDate) > new Date(this.endDate) ? false : true;
    },
    maxStartDate() {
      if (this.userIs === "admin") {
        const nextMonthLastDay = moment()
          .add(1, "month")
          .endOf("month")
          .format("YYYY-MM-DD");
        return nextMonthLastDay;
      }

      return "";
    },

    maxDate() {
      const today = new Date();
      if (this.userIs === "admin") {
        const previousMonthFirstDay = moment()
          .subtract(1, "month")
          .startOf("month")
          .format("YYYY-MM-DD");
        return previousMonthFirstDay;
      }
      if (this.advanceNotificationDays <= 0) {
        return "";
      }
      today.setDate(today.getDate() + this.advanceNotificationDays);
      const formattedDate = today.toISOString().substring(0, 10);

      return formattedDate;
    },
    startMessage() {
      if (this.startDate && this.endDate && !this.startDateDisabled) {
        return "Please change the duration to modify the date.";
      } else if (!this.startDateDisabled) {
        return "Please select the employee name and pre-approval-type to enable date.";
      } else {
        return "";
      }
    },
    endMessage() {
      if (this.startDate && this.endDate && !this.endDateWarningMessage) {
        return "Please change the duration to modify the end date.";
      } else if (!this.endDateWarningMessage) {
        return "Please select the employee name and pre-approval type to enable date";
      } else {
        return "";
      }
    },
    minStartDate() {
      if (this.endDate) {
        const issueDateMs = new Date(this.endDate)
          .toISOString()
          .substring(0, 10);

        return issueDateMs;
      }
      return "";
    },
    formattedStartDate() {
      return this.formatDate(this.startDate);
    },
    formattedEndDate() {
      return this.formatDate(this.endDate);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;

      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    isDurationGreaterThanThreshold() {
      if (
        this.documentUpload === "Yes" &&
        this.maxDaysForDocumentUpload &&
        this.maxDaysForDocumentUpload <= this.totalDays &&
        (this.preApprovalType === "work from home" ||
          this.preApprovalType.toLowerCase() === "on duty")
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  watch: {
    preApprovalType(val) {
      if (val) {
        this.presentStartMonthIndex = null;
        this.weekOffDurationData = [];
        this.removeFiles();
        this.fileContent = null;
        this.presentStartMonthIndex = null;
        this.employeeName = "";
        this.employeeId = null;
        this.empNameId = "";
        this.startDate = "";
        this.endDate = "";
        this.duration = "Full Day";
        this.totalDays = null;
        let changedApprovaltype = "";
        if (val == "Work during holiday") {
          changedApprovaltype = "workDuringHoliday";
        } else if (val == "Work during week off") {
          changedApprovaltype = "workDuringWeekOff";
        } else if (val == "Work from home") {
          changedApprovaltype = "workFromHome";
        } else {
          changedApprovaltype = "onDuty";
        }
        const { [changedApprovaltype]: value } = this.employeeDetailsObject;
        if (value) {
          this.employeeNameList = value.map((item) => ({
            ...item,
            empNameId: item.userDefinedEmpId
              ? `${item.employeeName} - ${item.userDefinedEmpId}`
              : item.employeeName,
          }));
          if (
            value.some(
              (employee) =>
                employee.employeeId === this.loginEmployeeId && !this.isEdit
            )
          ) {
            this.employeeName = this.loginEmployeeName;
            this.employeeId = this.loginEmployeeId;
          } else {
            this.empListFetching = false;
            this.employeeName = "";
            this.employeeId = null;
          }
          this.empListFetching = false;
        }
        this.getEmployeeList();
      }
    },
    isDurationGreaterThanThreshold(val) {
      if (!val) {
        this.removeFiles();
        this.fileContent = null;
      }
    },
    startDate(val) {
      const date = new Date(val);
      this.presentStartMonthIndex = date.getMonth() + 1;
      this.presentStartYear = date.getFullYear();
      this.getDatesToDisable();
    },
    presentStartMonthIndex() {
      this.getDatesToDisable();
    },
    endDate(val) {
      const date = new Date(val);
      this.presentEndMonthIndex = date.getMonth() + 1;
      this.getEndDatesTodisable();
    },
    presentEndMonthIndex() {
      this.getEndDatesTodisable();
    },
  },

  mounted() {
    this.isFormDirty = false;
    this.getEmployeeList();
    this.retrievePreApprovalTypes();
    if (this.isEdit) {
      const {
        employeeId,
        preApprovalType,
        employeeName,
        status,
        duration,
        totalDays,
        startDate,
        endDate,
        reason,
        fileName,
        fileSize,
      } = !this.isEdit ? "" : this.selectedItem;
      (this.employeeId = employeeId ? employeeId : ""),
        (this.reason = reason ? reason : ""),
        (this.employeeName = employeeName ? employeeName : "");
      this.preApprovalType = preApprovalType === 0 ? 0 : preApprovalType;
      this.status = status ? status : "Active";
      this.totalDays = totalDays ? totalDays : 0;
      this.duration = duration ? duration : null;
      this.startDate = startDate ? startDate : null;
      this.endDate = endDate ? endDate : null;
      this.totalDays = totalDays ? totalDays : 0;
      this.fileContent =
        fileName && fileSize
          ? {
              name: this.formattedFileName(fileName),
              size: fileSize,
            }
          : [];
    }
    if (this.isEdit) {
      const date = new Date(this.selectedItem.startDate);
      this.presentStartMonthIndex = date.getMonth() + 1;
      this.presentStartYear = date.getFullYear();
      this.getEndDatesTodisable();
      this.generateDatesArray();
    }
    this.setUser();

    if (this.isEdit) {
      this.getWeekendCount();
    }
  },
};
</script>
<style>
.custom-date-picker {
  width: 100% !important;
}
.custom-date-picker :deep() .vuejs3-datepicker__value {
  /* max-width: 300px;
  min-width: 300px; */
  width: 100% !important;
  padding-top: 16px;
  padding-bottom: 16px;
  margin-bottom: 20px !important;
  z-index: 1000 !important;
  border: 0.5px solid rgb(163, 158, 158) !important;
}
.vuejs3-datepicker__value {
  width: 100%;
}
.text-area-class {
  background-color: white !important;
}
.text-area-class:hover {
  background-color: white !important;
}

@media (max-width: 400px) {
  .custom-date-picker :deep() .vuejs3-datepicker__value {
    min-width: 70vw;
  }
  .text-area-class {
    width: 100% !important;
  }
}
</style>
