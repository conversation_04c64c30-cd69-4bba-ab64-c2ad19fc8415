<template>
  <div class="pb-10">
    <div>
      <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
        >Air Ticket Policy</span
      >
      <v-form ref="editAirTicketForm" class="pa-2">
        <div>
          <v-row>
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="listPlaceOfOrigin"
                label="Place of Origin"
                item-value="PlaceOfOrigin"
                item-title="PlaceOfOrigin"
                :isAutoComplete="true"
                :isRequired="true"
                :rules="[required('Place of Origin', selectedplaceOfOrigin)]"
                clearable
                v-model="selectedplaceOfOrigin"
                :itemSelected="selectedplaceOfOrigin"
                ref="placeOfOrigin"
                :isLoading="isLoading"
                @selected-item="onChangeFields($event)"
              />
            </v-col>
            <!-- Air Ticketing category -->
            <v-col cols="12" md="4" sm="6">
              <v-row>
                <v-col cols="10">
                  <CustomSelect
                    :items="listAirCategory"
                    label="Air Ticketing category"
                    item-value="Air_Ticket_Setting_Id"
                    item-title="Air_Ticketing_Category"
                    :isRequired="true"
                    :rules="[
                      required('Air Ticketing category', selectedAirCategory),
                    ]"
                    clearable
                    :isAutoComplete="true"
                    :disabled="!selectedplaceOfOrigin"
                    v-model="selectedAirCategory"
                    :itemSelected="selectedAirCategory"
                    ref="airTicketCAtegory"
                    :isLoading="isLoading"
                    @selected-item="callSettingsFormData($event)"
                    @update:model-value="isFormDirty = true"
                  />
                </v-col>
                <v-col cols="2">
                  <v-btn
                    color="transparent"
                    variant="flat"
                    class="ml-n5 mt-3"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="fetchAirTicketingSettings()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
              <v-btn
                class="mt-n4 ml-n2"
                color="primary"
                variant="text"
                size="small"
                @click="openRedirectingForm('v3/core-hr/air-ticketing-policy')"
              >
                <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add New
              </v-btn>
            </v-col>
            <!-- Amount -->
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Air Fare Entitlement
                <span v-if="payrollCurrency" class="mr-1"
                  >(in {{ payrollCurrency }})</span
                >
              </p>
              <p
                v-if="selectedAirCategory"
                class="text-subtitle-1 font-weight-regular"
              >
                <span
                  >Infant -
                  {{
                    selectedData?.Infant_Amount
                      ? selectedData?.Infant_Amount
                      : 0
                  }},</span
                >
                <span class="ml-1"
                  >Child -
                  {{
                    selectedData?.Child_Amount ? selectedData?.Child_Amount : 0
                  }},</span
                >
                <span class="m-1">
                  Adult -
                  {{ checkNullValue(selectedData?.Adult_Amount) }}</span
                >
              </p>
              <p v-else>-</p>
            </v-col>
            <!-- Effective From -->
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="listEffectiveFrom"
                label="Effective From"
                :isRequired="true"
                :rules="[required('Effective From', chooseEffectiveDateType)]"
                :isAutoComplete="true"
                v-model="chooseEffectiveDateType"
                :itemSelected="chooseEffectiveDateType"
                ref="effectiveFrom"
                :isLoading="isLoading"
                @update:model-value="onChangeFields($event)"
              />
            </v-col>
            <!-- Effective Date -->
            <v-col cols="12" md="4" sm="6">
              <p
                v-if="chooseEffectiveDateType?.toLowerCase() !== 'custom'"
                class="text-subtitle-1 text-grey-darken-1"
              >
                Effective Date
              </p>
              <p
                v-if="chooseEffectiveDateType?.toLowerCase() === 'date of join'"
                class="text-subtitle-1 font-weight-regular"
              >
                {{ checkNullValue(formattedEffectiveDate) }}
              </p>
              <p
                v-if="
                  chooseEffectiveDateType?.toLowerCase() === 'confirmation date'
                "
                class="text-subtitle-1 font-weight-regular"
              >
                {{ checkNullValue(formattedEffectiveDate) }}
              </p>
              <section
                v-if="chooseEffectiveDateType?.toLowerCase() === 'custom'"
                class="text-body-2"
              >
                <v-menu
                  v-model="EffectiveDateMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                  ><template v-slot:activator="{ props }">
                    <v-text-field
                      ref="dob"
                      v-model="formattedEffectiveDate"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[
                        required('Effective Date', formattedEffectiveDate),
                      ]"
                      readonly
                      v-bind="props"
                      variant="solo"
                      @update:model-value="onChangeFields($event)"
                    >
                      <template v-slot:label>
                        Effective Date
                        <span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-model="selectedEffectiveDate"
                    @update:modelValue="onChangeFields($event)"
                    :max="maxEffectiveDate"
                    :min="minEffectiveDate"
                  />
                </v-menu>
              </section>
            </v-col>
            <!-- Eligibility of Ticket Claim (In Months) -->
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                variant="solo"
                ref="ticketClaim"
                v-model="selectedEligibilityMonths"
                clearable
                :rules="[
                  required(
                    'Eligibility of Ticket Claim (In Months)',
                    selectedEligibilityMonths
                  ),
                  minMaxNumberValidation(
                    'Eligibility of Ticket Claim (In Months)',
                    selectedEligibilityMonths,
                    1,
                    200
                  ),
                ]"
                type="number"
                min="1"
                max="200"
                @update:model-value="onChangeFields($event)"
              >
                <template v-slot:label>
                  <span>Eligibility of Ticket Claim (in Months)</span>
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <!-- Air Ticket to Dependent -->
            <v-col cols="12" md="4" sm="6">
              <div class="d-flex mb-n6">
                <span class="v-label pr-3 pb-5">Air Ticket to Dependent</span>
                <v-switch
                  color="primary"
                  v-model="enableDependent"
                  true-value="Yes"
                  false-value="No"
                  @update:model-value="onChangeFields($event)"
                />
              </div>
            </v-col>
            <!-- Dependent Relationship -->
            <v-col
              v-if="enableDependent?.toLowerCase() === 'yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                :items="dependentList"
                label="Dependent Relationship"
                :isRequired="true"
                :isAutoComplete="true"
                clearable
                item-title="Dependent_Relationship"
                item-value="Dependent_Relationship"
                :isLoading="relationShipListLoading"
                v-model="selectedDependents"
                :itemSelected="selectedDependents"
                :rules="[
                  required(
                    'Dependent Relationship',
                    selectedDependents?.length ? selectedDependents : null
                  ),
                ]"
                :selectProperties="{
                  multiple: true,
                  chips: true,
                  clearable: true,
                  closableChips: true,
                }"
                @selected-item="onChangeFields($event)"
                ref="dependentRelationship"
              />
            </v-col>
            <!-- Status -->
            <v-col cols="12" md="4" sm="6">
              <div class="v-label mr-4">
                <span>Status</span>
              </div>
              <AppToggleButton
                button-active-text="Active"
                button-inactive-text="InActive"
                button-active-color="#7de272"
                button-inactive-color="red"
                id-value="gab-analysis-based-on"
                style="width: fit-content"
                :current-value="selectedStatus === 'Active' ? true : false"
                @chosen-value="onChangeStatus($event)"
                @update:model-value="isFormDirty = true"
              />
            </v-col>
          </v-row>
        </div>
      </v-form>
    </div>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span>Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1"
            color="primary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            <span>Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading" />
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { checkNullValue } from "@/helper.js";
import { RETRIEVE_EMP_JOB_INFO } from "@/graphql/employee-profile/profileQueries.js";
import { LIST_MARITAL_STATUS } from "@/graphql/dropDownQueries.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  RETRIEVE_AIR_TICKET_SETTINGS,
  ADD_UPDATE_EMP_AIR_TICKET_POLICY,
} from "@/graphql/corehr/payrollDataManagement.js";

export default {
  name: "AirTicketPolicyEdit",
  mixins: [validationRules],
  components: {
    CustomSelect,
  },
  emits: ["close-edit-form", "edit-updated"],

  props: {
    airTicketDetails: {
      type: Array,
      default: () => [],
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dropdownLoading: false,
      isFormDirty: false,
      selectedplaceOfOrigin: null,
      selectedAirCategory: null,
      selectedStatus: "Active",
      chooseEffectiveDateType: "",
      selectedEligibilityMonths: null,
      enableDependent: "No",
      selectedDependents: null,
      dependentList: [],
      jobDetailsData: {},
      airTicketSettings: [],
      selectedData: null,
      //Date-picker
      EffectiveDateMenu: false,
      formattedEffectiveDate: "",
      selectedEffectiveDate: null,
      openWarningModal: false,
      // edit
      openBottomSheet: true,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      relationShipListLoading: false,
    };
  },

  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    // Compute unique list of Place of Origin values along with Air_Ticket_Setting_Id
    listPlaceOfOrigin() {
      const uniquePlaces = new Map();

      this.airTicketSettings.forEach((item) => {
        const key = `${item.Destination_City} - ${item.Destination_Country}`;
        if (!uniquePlaces.has(key)) {
          uniquePlaces.set(key, {
            Air_Ticket_Setting_Id: item.Air_Ticket_Setting_Id,
            PlaceOfOrigin: key,
          });
        }
      });

      return Array.from(uniquePlaces.values());
    },
    // Compute Air Ticketing Categories based on selected place of origin
    listAirCategory() {
      if (!this.selectedplaceOfOrigin) return [];

      return this.airTicketSettings
        .filter(
          (item) =>
            `${item.Destination_City} - ${item.Destination_Country}` ===
            this.selectedplaceOfOrigin
        )
        .map((item) => ({
          Air_Ticket_Setting_Id: item.Air_Ticket_Setting_Id,
          Air_Ticketing_Category: item.Air_Ticketing_Category,
        }));
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    minEffectiveDate() {
      if (this.chooseEffectiveDateType?.toLowerCase() === "date of join") {
        return moment(this.jobDetailsData?.Date_Of_Join).format("YYYY-MM-DD");
      } else if (
        this.chooseEffectiveDateType?.toLowerCase() === "confirmation date"
      )
        return moment(this.jobDetailsData?.Confirmation_Date).format(
          "YYYY-MM-DD"
        );
      return moment(this.jobDetailsData?.Date_Of_Join).format("YYYY-MM-DD");
    },
    maxEffectiveDate() {
      if (this.chooseEffectiveDateType?.toLowerCase() === "custom")
        return moment(this.jobDetailsData?.Emp_InActive_Date).format(
          "YYYY-MM-DD"
        );
      return null;
    },
    listEffectiveFrom() {
      const list = ["Date of Join"];
      if (this.jobDetailsData?.Confirmation_Date)
        list.push("Confirmation Date");
      list.push("Custom");
      return list;
    },
  },
  watch: {
    selectedplaceOfOrigin: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          if (oldVal) this.selectedAirCategory = null;
        }
      },
      immediate: true,
    },
    chooseEffectiveDateType: {
      handler(val) {
        if (val?.toLowerCase() === "date of join")
          this.selectedEffectiveDate = new Date(
            this.jobDetailsData?.Date_Of_Join
          );
        else if (val?.toLowerCase() === "confirmation date")
          this.selectedEffectiveDate = new Date(
            this.jobDetailsData?.Confirmation_Date
          );
        else {
          this.formattedEffectiveDate = "";
          // this.selectedEffectiveDate = null;
        }
      },
      immediate: true,
    },
    selectedEffectiveDate: function (val) {
      if (val) {
        this.EffectiveDateMenu = false;
        this.formattedEffectiveDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },

  mounted() {
    this.getJobDetails();
    this.fetchAirTicketingSettings();
    this.retrieveRelationShipList();
    if (this.isEdit) this.prefillRetriveData();
  },

  methods: {
    checkNullValue,
    onChangeFields() {
      this.isFormDirty = true;
    },
    prefillRetriveData() {
      const ticketData = this.airTicketDetails?.length
        ? this.airTicketDetails[0]
        : {};
      this.chooseEffectiveDateType = ticketData.Effective_Date_Enable;
      this.selectedData = ticketData;
      this.selectedEligibilityMonths =
        ticketData.Eligibility_Of_Ticket_Claim_Months;
      this.enableDependent = ticketData.Air_Ticket_To_Dependent;
      this.selectedDependents = JSON.parse(ticketData?.Dependent_Relationship);
      this.selectedAirCategory = ticketData.Air_Ticket_Setting_Id;
      this.selectedplaceOfOrigin =
        ticketData.Destination_City + " - " + ticketData.Destination_Country;
      this.selectedEffectiveDate = new Date(ticketData.Effective_Date);
    },
    callSettingsFormData(setId) {
      let matchedObject = this.airTicketSettings.find(
        (item) => item.Air_Ticket_Setting_Id === setId
      );
      this.selectedData = matchedObject;
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        // if (this.actionType === "add" && !this.selectedEmpId) {
        //   this.$emit("close-edit-form");
        // } else {
        //   this.openBottomSheet = false;
        //   this.$emit("close-edit-form");
        // }
        this.openBottomSheet = false;
        this.$emit("close-edit-form");
      }
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.editAirTicketForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.addUpdateDetails();
      } else {
        // Check the validity of each field
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "placeOfOrigin",
                "airTicketCategory",
                "effectiveFrom",
                "dependentRelationShip",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 2, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },
    fetchAirTicketingSettings() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_AIR_TICKET_SETTINGS,
          client: "apolloClientI",
          variables: {
            formId: vm.callingFrom === "profile" ? 323 : 322,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveAirTicketSettings &&
            response.data.retrieveAirTicketSettings.airTicketSettingData &&
            !response.data.retrieveAirTicketSettings.errorCode
          ) {
            let tempData =
              response.data.retrieveAirTicketSettings.airTicketSettingData;
            vm.airTicketSettings = tempData.filter(
              (item) => item.Status === "Active"
            );
            vm.isLoading = false;
          } else {
            vm.handleAirTicketError(
              response.data.retrieveAirTicketSettings?.errorCode
            );
          }
        })
        .catch((err) => {
          vm.handleAirTicketError(err);
        });
    },
    handleAirTicketError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: this.callingFrom,
        isListError: true,
      });
    },
    getJobDetails() {
      let vm = this;
      vm.jobDetails = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_JOB_INFO,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response?.data?.retrieveJobInfo?.jobInfoDetails) {
            const jobInfoDetails = JSON.parse(
              response.data.retrieveJobInfo.jobInfoDetails || "[]"
            );
            vm.jobDetailsData = jobInfoDetails[0] || {};
          } else {
            vm.handleJobDetailsError();
          }
          vm.isLoading = false;
          if (!vm.isEdit) vm.chooseEffectiveDateType = "Date of Join";
          else
            vm.selectedEffectiveDate =
              new Date(vm.airTicketDetails[0]?.Effective_Date) || null;
        })
        .catch((err) => {
          vm.isLoading = false;
          if (!vm.isEdit) vm.chooseEffectiveDateType = "Date of Join";
          else
            vm.chooseEffectiveDateType =
              new Date(vm.airTicketDetails[0]?.Effective_Date) || null;
          vm.handleJobDetailsError(err);
        });
    },
    handleJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "job details",
      });
    },
    retrieveRelationShipList() {
      let vm = this;
      vm.relationShipListLoading = true;
      vm.$apollo
        .query({
          query: LIST_MARITAL_STATUS,
          client: "apolloClientAC",
          variables: {
            retrieveRelationship: 1,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveMaritalStatus &&
            !response.data.retrieveMaritalStatus.errorCode
          ) {
            const { maritalStatus } = response.data.retrieveMaritalStatus;
            vm.dependentList = maritalStatus ? JSON.parse(maritalStatus) : [];
          }
          vm.relationShipListLoading = false;
        })
        .catch(() => {
          vm.relationShipListLoading = false;
        });
    },
    addUpdateDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_EMP_AIR_TICKET_POLICY,
          variables: {
            employeeId: vm.selectedEmpId,
            formId: vm.callingFrom === "profile" ? 323 : 322,
            airTicketSettingId: vm.selectedAirCategory,
            status: vm.selectedStatus,
            effectiveDateEnable: vm.chooseEffectiveDateType,
            effectiveDate: moment(vm.selectedEffectiveDate).isValid()
              ? moment(vm.selectedEffectiveDate).format("YYYY-MM-DD")
              : null,
            eligibilityOfTicketClaimMonths: parseInt(
              vm.selectedEligibilityMonths
            ),
            airTicketToDependent: vm.enableDependent,
            dependentRelationship:
              vm.enableDependent?.toLowerCase() === "yes"
                ? vm.selectedDependents
                : [],
            action: vm.isEdit ? "update" : "add",
          },
          client: "apolloClientAD",
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            message:
              vm.actionType === "add" && !vm.selectedEmpId
                ? "Air Ticket Details added successfully"
                : "Air Ticket Details updated successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.$emit("edit-updated");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action:
            this.actionType === "add" && !this.selectedEmpId
              ? "adding"
              : "updating",
          form: "Air Ticket Details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    // change the mode of performance management
    onChangeStatus(value) {
      this.selectedStatus = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    openRedirectingForm(path) {
      const redirectionPath = `${this.baseUrl}${path}`;
      window.open(redirectionPath, "_blank");
    },
  },
};
</script>
