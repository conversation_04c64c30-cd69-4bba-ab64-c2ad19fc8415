<template>
  <div>
    <div class="d-flex justify-space-between align-center">
      <v-row>
        <v-col cols="12" class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="red"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
            >Air Ticket Accrual</span
          >
        </v-col>
      </v-row>
    </div>
    <div
      v-if="airTicketDetails?.length === 0"
      class="d-flex align-center justify-start fill-height text-h6 text-grey pa-4"
    >
      No Air Ticket Accrual have been added
    </div>
    <v-row v-else class="pa-4 ma-2 card-blue-background">
      <v-col cols="12" md="4" sm="6">
        <p class="text-subtitle-1 text-grey-darken-1">
          Accrual Basis
          <span v-if="payrollCurrency">(in {{ payrollCurrency }})</span>
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          <span v-if="airTicketData?.Infant_Amount"
            >Infant - {{ checkNullValue(airTicketData?.Infant_Amount) }},</span
          >
          <span class="ml-1" v-if="airTicketData?.Child_Amount"
            >Child - {{ checkNullValue(airTicketData?.Child_Amount) }},</span
          >
          <span class="m-1">
            Adult -
            {{ checkNullValue(airTicketData?.Adult_Amount) }}</span
          >
        </p> </v-col
      ><v-col cols="12" md="4" sm="6">
        <p class="text-subtitle-1 text-grey-darken-1">
          No. of Dependents Eligible for Ticket
        </p>
        <p
          class="text-subtitle-1 font-weight-regular"
          v-if="airTicketData?.No_Of_Dependents"
        >
          <span v-if="airTicketData?.Infant_Count"
            >Infant - {{ checkNullValue(airTicketData?.Infant_Count) }},</span
          >
          <span class="ml-1" v-if="airTicketData?.Child_Count"
            >Child - {{ checkNullValue(airTicketData?.Child_Count) }},</span
          >
          <span class="m-1" v-if="airTicketData?.Adult_Count">
            Adult -
            {{ checkNullValue(airTicketData?.Adult_Count) }}</span
          >
        </p>
        <p v-else>-</p>
      </v-col>
      <v-col cols="12" md="4" sm="6">
        <p class="text-subtitle-1 text-grey-darken-1">
          Accrued Tickets (Self + Dependents)
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          <span
            >Infant -
            {{
              eligibleData[0]?.infantTickets
                ? eligibleData[0]?.infantTickets
                : 0
            }},</span
          >
          <span class="ml-1"
            >Child -
            {{
              eligibleData[0]?.childTickets ? eligibleData[0]?.childTickets : 0
            }},</span
          >
          <span class="m-1">
            Adult -
            {{ checkNullValue(eligibleData[0]?.adultTickets) }}</span
          >
        </p>
      </v-col>
      <v-col cols="12" md="4" sm="6">
        <p class="text-subtitle-1 text-grey-darken-1">Total Accrued Tickets</p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ checkNullValue(eligibleData[0]?.accruedTickets) }}
        </p>
      </v-col>
      <v-col cols="12" md="4" sm="6">
        <p class="text-subtitle-1 text-grey-darken-1">
          Accrued Amount
          <span v-if="payrollCurrency">(in {{ payrollCurrency }})</span>
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          <span
            >Infant -
            {{
              eligibleData[0]?.infantAmount ? eligibleData[0]?.infantAmount : 0
            }},</span
          >
          <span class="ml-1"
            >Child -
            {{
              eligibleData[0]?.childAmount ? eligibleData[0]?.childAmount : 0
            }},</span
          >
          <span class="m-1">
            Adult -
            {{ checkNullValue(eligibleData[0]?.adultAmount) }}</span
          >
        </p>
      </v-col>
      <v-col cols="12" md="4" sm="6">
        <p class="text-subtitle-1 text-grey-darken-1">
          Total Accrued Amount
          <span v-if="payrollCurrency">(in {{ payrollCurrency }})</span>
        </p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ checkNullValue(eligibleData[0]?.accruedAmount) }}
        </p>
      </v-col>
      <v-col cols="12" md="4" sm="6">
        <p class="text-subtitle-1 text-grey-darken-1">Availed Date</p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ formatDate(airTicketData.Last_Availed_Date) }}
        </p>
      </v-col>
      <v-col cols="12" md="4" sm="6">
        <p class="text-subtitle-1 text-grey-darken-1">No. of Days Due</p>
        <p class="text-subtitle-1 font-weight-regular">
          {{ checkNullValue(eligibleData[0]?.eligibleDays) }}
        </p>
      </v-col>
    </v-row>
  </div>
</template>
<script>
import { checkNullValue, generateRandomColor } from "@/helper";
import moment from "moment";

export default {
  name: "AirTicketAccural",
  props: {
    airTicketDetails: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      airTicketData: [],
    };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    eligibleData() {
      return this.airTicketDetails.map((ticket) => {
        const currentDate = moment();
        const lastAvailedDate = moment(ticket.Last_Availed_Date).isValid()
          ? moment(ticket.Last_Availed_Date)
          : null;
        const effectiveDate = moment(ticket.Effective_Date).isValid()
          ? moment(ticket.Effective_Date)
          : null;
        // Calculate Eligible Days
        let eligibleDays = 0;
        if (lastAvailedDate) {
          eligibleDays = currentDate.diff(lastAvailedDate, "days");
        } else if (effectiveDate) {
          eligibleDays = currentDate.diff(effectiveDate, "days");
        }

        // Number of dependents
        let adultDependents = 1; // Default: employee
        let childDependents = 0;
        let infantDependents = 0;

        if (
          ticket.Air_Ticket_To_Dependent?.toLowerCase() === "yes" &&
          ticket.No_Of_Dependents
        ) {
          if (ticket.Adult_Count) adultDependents += ticket.Adult_Count;

          if (ticket.Child_Count) childDependents += ticket.Child_Count;

          if (ticket.Infant_Count) infantDependents += ticket.Infant_Count;
        }
        let totalDays =
          (365 * parseInt(ticket.Eligibility_Of_Ticket_Claim_Months)) / 12;
        // Accrued Ticket Calculation
        let adultTickets = (eligibleDays * adultDependents) / totalDays;
        let childTickets = (eligibleDays * childDependents) / totalDays;
        let infantTickets = (eligibleDays * infantDependents) / totalDays;
        let accruedTickets = adultTickets + childTickets + infantTickets;
        // Accrued Amount Calculation
        let infantAmount =
          infantTickets?.toFixed(2) * ticket.Infant_Amount || 0;
        let childAmount = childTickets?.toFixed(2) * ticket.Child_Amount || 0;
        let adultAmount = adultTickets?.toFixed(2) * ticket.Adult_Amount || 0;
        let accruedAmount = infantAmount + childAmount + adultAmount;
        return {
          ...ticket,
          eligibleDays,
          adultTickets: adultTickets?.toFixed(2),
          childTickets: childTickets?.toFixed(2),
          infantTickets: infantTickets?.toFixed(2),
          accruedTickets: accruedTickets?.toFixed(2),
          childAmount: childAmount?.toFixed(2),
          infantAmount: infantAmount?.toFixed(2),
          adultAmount: adultAmount?.toFixed(2),
          accruedAmount: accruedAmount?.toFixed(2),
        };
      });
    },
  },

  mounted() {
    this.airTicketData = this.airTicketDetails[0] || {};
  },

  methods: {
    checkNullValue,
    generateRandomColor,
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
