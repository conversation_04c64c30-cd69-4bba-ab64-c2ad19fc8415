<template>
  <v-overlay
    :model-value="showAddEditForm"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:50vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">
            {{ isEdit ? "Edit" : "Add" }}
            {{ landedFormName }}
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <div class="px-5 py-6">
            <v-form ref="customFieldsForm">
              <v-row>
                <!-- Field Name -->
                <v-col cols="12" sm="6" md="6" class="px-md-6">
                  <v-text-field
                    variant="solo"
                    ref="fieldName"
                    v-model="fieldName"
                    clearable
                    :rules="[
                      required('Field Name', fieldName),
                      validateWithRulesAndReturnMessages(
                        fieldName,
                        'skillCategory',
                        'Field Name'
                      ),
                    ]"
                  >
                    <template v-slot:label>
                      Field Name
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Field Type -->
                <v-col cols="12" sm="6" md="6" class="px-md-6">
                  <CustomSelect
                    ref="fieldType"
                    v-model="selectedFieldType"
                    :items="listFieldTypes"
                    :rules="[required('Field Type', selectedFieldType)]"
                    label="Field Type"
                    clearable
                    :is-required="true"
                    variant="solo"
                    :isAutoComplete="true"
                    :itemSelected="selectedFieldType"
                    @selected-item="
                      onChangeFieldType($event, selectedFieldType)
                    "
                  />
                </v-col>

                <!-- Dropdown Values -->
                <v-col
                  v-if="
                    selectedFieldType?.toLowerCase() === 'single choice' ||
                    selectedFieldType?.toLowerCase() === 'multiple choice'
                  "
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Dropdown Values
                    <span style="color: red">*</span>
                  </p>
                  <v-text-field
                    ref="options"
                    v-model="inputOptions"
                    @update:modelValue="showAddIcon()"
                    variant="solo"
                    :rules="[
                      required('Dropdown Values', selectedOptions[0]),
                      validateWithRulesAndReturnMessages(
                        selectedOptions[0],
                        'skillCategory',
                        'Dropdown Values'
                      ),
                    ]"
                    @keydown.enter.prevent="addChip()"
                  >
                    <template v-slot:default>
                      <v-icon
                        v-if="showIconOptions"
                        @click="addChip()"
                        size="x-small"
                        >fas fa-plus</v-icon
                      >
                      <v-chip
                        v-for="(chip, index) in selectedOptions"
                        append-icon="fas fa-times-circle"
                        :key="index"
                        class="ma-1"
                        @click="removeChip(index)"
                      >
                        {{ chip }}
                      </v-chip>
                    </template>
                  </v-text-field>
                </v-col>

                <!-- Min Character -->
                <v-col
                  v-if="
                    selectedFieldType?.toLowerCase() === 'text field' ||
                    selectedFieldType?.toLowerCase() === 'text area'
                  "
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <v-text-field
                    ref="minCharacter"
                    :min="1"
                    v-model="minCharacterText"
                    :rules="[
                      required('Minimum Characters', minCharacterText),
                      minNumberValidation(
                        'Minimum Characters',
                        minCharacterText,
                        1
                      ),
                    ]"
                    variant="solo"
                    type="number"
                  >
                    <template v-slot:label>
                      Minimum Characters
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Max Character -->
                <v-col
                  v-if="
                    selectedFieldType?.toLowerCase() === 'text field' ||
                    selectedFieldType?.toLowerCase() === 'text area'
                  "
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <v-text-field
                    ref="maxCharacter"
                    v-model="maxCharacterText"
                    :rules="[
                      required('Maximum Characters', maxCharacterText),
                      minMaxNumberValidation(
                        'Minimum Characters',
                        parseInt(maxCharacterText),
                        parseInt(minCharacterText),
                        selectedFieldType?.toLowerCase() === 'text field'
                          ? 150
                          : 5000
                      ),
                    ]"
                    :min="minCharacterText"
                    :disabled="!minCharacterText"
                    variant="solo"
                    type="number"
                  >
                    <template v-slot:label>
                      Maximum Characters
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Validation Rule -->
                <v-col
                  v-if="
                    selectedFieldType?.toLowerCase() === 'text field' ||
                    selectedFieldType?.toLowerCase() === 'text area'
                  "
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <v-tooltip
                    :text="selectedValidationType ? validationMessage : null"
                    location="top"
                    max-width="350"
                  >
                    <template v-slot:activator="{ props }">
                      <CustomSelect
                        ref="validationType"
                        v-model="selectedValidationType"
                        :items="listValidations"
                        item-title="Validation_Name"
                        item-value="Validation_Id"
                        v-bind="selectedValidationType ? props : {}"
                        :rules="[
                          required('Validation Rule', selectedValidationType),
                        ]"
                        label="Validation Rule"
                        clearable
                        :is-required="true"
                        variant="solo"
                        :isAutoComplete="true"
                        :itemSelected="selectedValidationType"
                        @selected-item="selectedValidationType = $event"
                      />
                    </template>
                  </v-tooltip>
                </v-col>

                <!-- Data Range -->
                <v-col
                  v-if="selectedFieldType?.toLowerCase() === 'number'"
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6 pb-3"
                >
                  <div>
                    <p class="text-subtitle-1 text-grey-darken-1 ml-3">
                      Data Range<span style="color: red">*</span>
                    </p>
                    <v-range-slider
                      v-model="dataRangeNumber"
                      :step="1"
                      :max="dataRangeNumber[1]"
                      :min="dataRangeNumber[0]"
                      strict
                      thumb-size="15"
                      track-size="2"
                      color="orange"
                      hide-details
                      class="align-center"
                    >
                      <template v-slot:prepend>
                        <v-text-field
                          ref="minDateRange"
                          v-model="dataRangeNumber[0]"
                          single-line
                          type="number"
                          variant="solo"
                          :min="0"
                          :rules="[
                            required('Data Range', String(dataRangeNumber[0])),
                            minNumberValidation(
                              'Data Range',
                              parseInt(dataRangeNumber[0]),
                              0
                            ),
                          ]"
                          step="1"
                          thumb-label
                          density="comfortable"
                          style="width: 100px"
                        ></v-text-field>
                      </template>
                      <template v-slot:append>
                        <v-text-field
                          ref="maxDateRange"
                          v-model="dataRangeNumber[1]"
                          single-line
                          type="number"
                          :min="dataRangeNumber[1]"
                          :max="10000000"
                          :rules="[
                            required('Data Range', dataRangeNumber[1]),
                            minMaxNumberValidation(
                              'Data Range',
                              parseInt(dataRangeNumber[1]),
                              dataRangeNumber[0],
                              10000000
                            ),
                          ]"
                          variant="solo"
                          density="comfortable"
                          style="width: 100px"
                        ></v-text-field>
                      </template>
                    </v-range-slider>
                  </div>
                </v-col>

                <!-- URL Link -->
                <v-col
                  v-if="selectedFieldType?.toLowerCase() === 'url'"
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <v-text-field
                    variant="solo"
                    ref="urlLink"
                    v-model="urlLinkValue"
                    clearable
                    :rules="[
                      required('URL Link', urlLinkValue),
                      urlValidation(urlLinkValue),
                    ]"
                  >
                    <template v-slot:label>
                      <span>URL Link</span>
                      <span class="ml-1" style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>

                <!-- Roles DropDown -->
                <v-col
                  :class="
                    selectedFieldType?.toLowerCase() === 'single choice' ||
                    selectedFieldType?.toLowerCase() === 'multiple choice' ||
                    selectedFieldType?.toLowerCase() === 'number'
                      ? 'mt-5'
                      : ''
                  "
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <CustomSelect
                    v-model="selectedRoles"
                    ref="roles"
                    :items="listRoles"
                    label="Who can see the field"
                    variant="solo"
                    :isLoading="rolesLoader"
                    item-value="Roles_Id"
                    item-title="Roles_Name"
                    :isAutoComplete="true"
                    :select-properties="{
                      multiple: true,
                      chips: true,
                      clearable: true,
                      closableChips: true,
                    }"
                    :itemSelected="selectedRoles"
                    @selected-item="selectedRoles = $event"
                  />
                </v-col>
                <!-- Prerequisite Field -->
                <v-col cols="12" sm="6" md="6" class="px-md-6">
                  <v-row>
                    <v-col cols="10">
                      <CustomSelect
                        v-model="selectedParentField"
                        ref="parentField"
                        :items="listDependentFields"
                        label="Prerequisite Field"
                        variant="solo"
                        item-value="Custom_Field_Id"
                        item-title="Custom_Field_Name"
                        :isAutoComplete="true"
                        clearable
                        :itemSelected="selectedParentField"
                        @selected-item="selectedParentField = $event"
                        @update:model-value="selectedParentType = null"
                      />
                    </v-col>
                    <v-col cols="2">
                      <v-tooltip
                        location="top"
                        text="Select another custom field that controls the visibility of this field."
                      >
                        <template v-slot:activator="{ props }">
                          <span v-bind="props" class="pa-1 cursor-not-allowed">
                            <v-icon
                              size="small"
                              color="info"
                              class="fas fa-info-circle d-flex align-center justify-center"
                            ></v-icon>
                          </span>
                        </template>
                      </v-tooltip>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- Prerequisite Value Type -->
                <v-col
                  v-if="selectedParentField"
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <CustomSelect
                    v-model="selectedParentType"
                    ref="parentType"
                    :items="['Any', 'Specific']"
                    label="Prerequisite Value Type"
                    :rules="[
                      required('Prerequisite Value Type', selectedParentType),
                    ]"
                    :is-required="true"
                    variant="solo"
                    :isAutoComplete="true"
                    :itemSelected="selectedParentType"
                    @selected-item="selectedParentType = $event"
                    @update:model-value="selectedParentValue = null"
                  />
                </v-col>
                <v-col
                  v-if="selectedParentType?.toLowerCase() === 'specific'"
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <!-- Text Field for parent value -->
                  <v-text-field
                    v-if="
                      getParentFieldType === 'text field' ||
                      getParentFieldType === 'text area'
                    "
                    v-model="selectedParentValue"
                    ref="parentValue"
                    :rules="[
                      required('Prerequisite Value', selectedParentValue),
                    ]"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Prerequisite Value
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>

                  <!-- Number Field for parent value -->
                  <v-text-field
                    v-else-if="getParentFieldType === 'number'"
                    v-model="selectedParentValue"
                    ref="parentValue"
                    type="number"
                    :rules="[
                      required('Prerequisite Value', selectedParentValue),
                    ]"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Prerequisite Value
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>

                  <!-- Date Picker for parent value -->
                  <v-menu
                    v-else-if="getParentFieldType === 'date'"
                    v-model="parentDateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        v-model="parentFormattedDate"
                        ref="parentValue"
                        prepend-inner-icon="fas fa-calendar"
                        readonly
                        :rules="[
                          required('Prerequisite Value', selectedParentValue),
                        ]"
                        clearable
                        v-bind="props"
                        variant="solo"
                      >
                        <template v-slot:label>
                          Prerequisite Value
                          <span style="color: red">*</span>
                        </template>
                      </v-text-field>
                    </template>
                    <v-date-picker v-model="selectedParentValue" />
                  </v-menu>

                  <!-- Dropdown for parent value -->
                  <CustomSelect
                    v-else-if="
                      getParentFieldType === 'single choice' ||
                      getParentFieldType === 'multiple choice'
                    "
                    v-model="selectedParentValue"
                    ref="parentValue"
                    :items="getDependentFieldOptions"
                    label="Prerequisite Value"
                    :is-required="true"
                    :select-properties="selectDependentProperties"
                    variant="solo"
                    :isAutoComplete="true"
                    :rules="[
                      required('Prerequisite Value', selectedParentValue),
                    ]"
                    :itemSelected="selectedParentValue"
                    @selected-item="selectedParentValue = $event"
                  />
                </v-col>
              </v-row>
            </v-form>

            <!-- Preview Screen -->
            <v-row v-if="showPreviewScreen">
              <v-col cols="12" sm="6" md="6" class="px-md-6">
                <v-card
                  class="px-4"
                  style="box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1)"
                >
                  <p class="text-subtitle-1 text-grey-darken-1 py-2">Preview</p>
                  <!-- Dropdown -->
                  <CustomSelect
                    v-if="
                      (selectedFieldType?.toLowerCase() === 'single choice' ||
                        selectedFieldType?.toLowerCase() ===
                          'multiple choice') &&
                      selectedOptions?.length
                    "
                    ref="previewDropdown"
                    v-model="selectedPreviewDropdown"
                    :rules="[
                      required(fieldName || 'field', selectedPreviewDropdown),
                    ]"
                    :items="selectedOptions"
                    :label="fieldName"
                    :isRequired="true"
                    variant="solo"
                    :select-properties="selectProperties"
                    :isAutoComplete="true"
                    :itemSelected="selectedPreviewDropdown"
                    @selected-item="selectedPreviewDropdown = $event"
                  />
                  <!-- Text Field -->
                  <v-text-field
                    v-if="
                      selectedFieldType?.toLowerCase() === 'text field' &&
                      minCharacterText &&
                      maxCharacterText &&
                      selectedValidationType
                    "
                    ref="previewTextField"
                    v-model="previewTextField"
                    variant="solo"
                    :rules="[
                      required(fieldName || 'field', previewTextField),
                      dynamicValidationRule(previewTextField),
                      minLengthValidation(
                        fieldName || 'field',
                        previewTextField,
                        minCharacterText
                      ),
                      maxLengthValidation(
                        fieldName || 'field',
                        previewTextField,
                        maxCharacterText
                      ),
                    ]"
                  >
                    <template v-slot:label>
                      {{ fieldName }}
                      <span v-if="fieldName" style="color: red">*</span>
                    </template>
                  </v-text-field>
                  <!-- Text Area -->
                  <v-textarea
                    v-if="
                      selectedFieldType?.toLowerCase() === 'text area' &&
                      minCharacterText &&
                      maxCharacterText &&
                      selectedValidationType
                    "
                    v-model="previewTextArea"
                    rows="2"
                    color="primary"
                    ref="previewTextArea"
                    hide-details="auto"
                    variant="solo"
                    :counter="maxCharacterText"
                    :rules="[
                      required(fieldName || 'field', previewTextArea),
                      dynamicValidationRule(previewTextArea),
                      minLengthValidation(
                        fieldName || 'field',
                        previewTextArea,
                        minCharacterText
                      ),
                      maxLengthValidation(
                        fieldName || 'field',
                        previewTextArea,
                        maxCharacterText
                      ),
                    ]"
                  >
                    <template v-slot:label>
                      {{ fieldName }}
                      <span v-if="fieldName" style="color: red">*</span>
                    </template>
                  </v-textarea>
                  <!-- Number Field -->
                  <v-text-field
                    v-if="
                      selectedFieldType?.toLowerCase() === 'number' &&
                      dataRangeNumber?.length
                    "
                    ref="previewNumberField"
                    v-model="previewNumberField"
                    :min="dataRangeNumber[0]"
                    :max="dataRangeNumber[1]"
                    :rules="[
                      required(
                        fieldName || 'field',
                        previewNumberField?.length
                      ),
                      minMaxNumberValidation(
                        fieldName || 'field',
                        previewNumberField,
                        dataRangeNumber[0],
                        dataRangeNumber[1]
                      ),
                    ]"
                    variant="solo"
                    type="number"
                  >
                    <template v-slot:label>
                      {{ fieldName }}
                      <span v-if="fieldName" style="color: red">*</span>
                    </template>
                  </v-text-field>
                  <!-- Date Picker -->
                  <v-menu
                    v-if="selectedFieldType?.toLowerCase() === 'date'"
                    v-model="dateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        v-model="formattedDate"
                        prepend-inner-icon="fas fa-calendar"
                        readonly
                        :rules="[required(fieldName || 'field', formattedDate)]"
                        clearable
                        v-bind="props"
                        variant="solo"
                      >
                        <template v-slot:label>
                          {{ fieldName }}
                          <span v-if="fieldName" style="color: red">*</span>
                        </template></v-text-field
                      >
                    </template>
                    <v-date-picker v-model="selectedDate" />
                  </v-menu>
                  <!-- URL -->
                  <v-btn
                    v-if="selectedFieldType?.toLowerCase() === 'url'"
                    color="primary"
                    class="text-decoration-underline"
                    variant="text"
                    :disabled="!urlLinkValue"
                    @click="urlRedirection(urlLinkValue)"
                  >
                    {{ fieldName }}</v-btn
                  >
                </v-card>
              </v-col>
            </v-row>

            <!-- Forms -->
            <v-row>
              <v-col cols="12" class="my-2">
                <v-card
                  class="mx-3 pl-4 pb-4"
                  style="box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1)"
                >
                  <p class="text-subtitle-1 text-grey-darken-1 py-2">
                    Add Fields To:
                  </p>
                  <div>
                    <!-- Dynamic form rendering -->
                    <div>
                      <v-row>
                        <v-col cols="3">Form Name</v-col>
                        <v-col cols="3">Field Visibility</v-col>
                        <v-col cols="3">Required</v-col>
                        <v-col cols="3" class="ml-n6"
                          >Integration Mapping Key</v-col
                        >
                      </v-row>
                      <!-- Header checkboxes for bulk selection -->
                      <v-row class="d-flex align-center justify-start my-n6">
                        <v-col cols="3"></v-col>
                        <v-col cols="3">
                          <v-checkbox
                            ref="visibilityCheckbox"
                            v-model="selectAllVisibility"
                            color="primary"
                            @change="toggleSelectAll('Field_Visibility')"
                          />
                        </v-col>
                        <v-col cols="3">
                          <v-checkbox
                            ref="mandatoryCheckbox"
                            v-model="selectAllMandatory"
                            color="primary"
                            :disabled="!isPreviewFieldRequired"
                            @change="toggleSelectAll('Mandatory')"
                          />
                        </v-col>
                      </v-row>
                      <!-- Render each form -->
                      <v-row
                        v-for="(form, formIndex) in selectedFormValue"
                        :key="formIndex"
                        class="align-center my-n7"
                      >
                        <v-col cols="3">{{ form.Form_Name }}</v-col>
                        <v-col cols="3">
                          <v-checkbox
                            v-model="form.Field_Visibility"
                            :ref="`visibilityCheckbox${formIndex}`"
                            color="primary"
                            @change="handleFieldVisibilityChange(formIndex)"
                          />
                        </v-col>
                        <v-col cols="3">
                          <v-checkbox
                            v-model="form.Mandatory"
                            :ref="`mandatoryCheckbox${formIndex}`"
                            color="primary"
                            :disabled="!form.Field_Visibility"
                          />
                        </v-col>
                        <v-col cols="3">
                          <CustomSelect
                            v-model="form.Integration_Mapping_Key"
                            :ref="`integrationMapping${formIndex}`"
                            :items="listMappingKeys"
                            :isAutoComplete="true"
                            item-title="Integration_Mapping_Key"
                            item-value="Integration_Mapping_Key"
                            class="ml-n6"
                            max-width="150px"
                            clearable
                            label="Integration Mapping Key"
                            density="compact"
                            :itemSelected="form.Integration_Mapping_Key"
                            @selected-item="
                              form.Integration_Mapping_Key = $event
                            "
                          />
                        </v-col>
                      </v-row>
                    </div>
                  </div>
                </v-card>
              </v-col>
            </v-row>
            <div class="card-actions-div">
              <v-sheet class="align-center text-center" style="width: 100%">
                <v-col cols="12" class="d-flex justify-end pr-4">
                  <v-btn
                    rounded="lg"
                    class="primary mr-6"
                    variant="outlined"
                    @click="$emit('close-form')"
                  >
                    Cancel
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    class="mr-1 primary"
                    variant="elevated"
                    :disabled="!isPreviewFieldRequired"
                    @click="validateCustomFields()"
                  >
                    Save
                  </v-btn>
                </v-col>
              </v-sheet>
            </div>
            <AppLoading v-if="isLoading" />
            <AppSnackBar
              v-if="showValidationAlert"
              :show-snack-bar="showValidationAlert"
              snack-bar-type="warning"
              timeOut="-1"
              @close-snack-bar="closeValidationAlert"
            >
              <template #custom-alert>
                <div
                  v-for="(validationMsg, index) of validationMessages"
                  :key="validationMsg + index"
                  class="text-subtitle-1"
                >
                  {{ validationMsg }}
                </div>
                <div class="d-flex justify-end">
                  <v-btn
                    class="mt-n5 secondary"
                    variant="text"
                    @click="closeValidationAlert()"
                  >
                    Close
                  </v-btn>
                </div>
              </template>
            </AppSnackBar>
          </div>
        </v-card-text>
      </v-card>
    </template>
  </v-overlay>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { RETRIEVE_LIST_ROLES } from "@/graphql/mpp/manPowerPlanningQueries";
import { ADD_UPDATE_CUSTOM_FIELD } from "@/graphql/settings/general/customFieldsQuries.js";
import { listInputValidations } from "@/helper";
export default {
  name: "AddCustomFields",
  data() {
    return {
      showAddEditForm: true,
      isLoading: false,
      fieldName: null,
      selectedFieldType: null,
      listFieldTypes: [
        "Text Area",
        "Text Field",
        "Single Choice",
        "Multiple Choice",
        "Number",
        "Date",
        "URL",
      ],
      listRoles: [],
      listValidations: [],
      rolesLoader: false,
      selectedRoles: null,
      //options field
      inputOptions: "",
      selectedOptions: [],
      showIconOptions: false,
      // text field/area
      minCharacterText: null,
      maxCharacterText: null,
      selectedValidationType: null,
      // Number field
      dataRangeNumber: [0, 1000],
      // Url Link
      urlLinkValue: null,
      // Preview
      selectedPreviewDropdown: null,
      previewTextField: null,
      previewTextArea: null,
      previewNumberField: [],
      // Preview Date Picker
      dateMenu: false,
      formattedDate: "",
      selectedDate: null,
      // Forms
      selectAllVisibility: false,
      selectAllMandatory: false,
      selectedFormValue: [],
      showValidationAlert: false,
      validationMessages: [],
      // Parent Fields
      selectedParentField: null,
      selectedParentType: null,
      selectedParentValue: null,
      parentDateMenu: false,
      parentFormattedDate: "",
    };
  },
  components: {
    CustomSelect,
  },
  mixins: [validationRules],
  emits: ["close-form", "custom-fields-updated"],
  props: {
    selectedItem: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    listMappingKeys: {
      type: Array,
      required: true,
    },
    listForms: {
      type: Array,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
    listData: {
      type: Array,
      required: true,
    },
  },
  computed: {
    listDependentFields() {
      return (
        this.listData?.filter(
          (item) =>
            item.Custom_Field_Id !== this.selectedItem?.Custom_Field_Id &&
            item.Custom_Field_Type?.toLowerCase() !== "url"
        ) || []
      );
    },
    getParentFieldType() {
      if (!this.selectedParentField) return null;

      const dependentField = this.listData.find(
        (item) => item.Custom_Field_Id == this.selectedParentField
      );

      return dependentField?.Custom_Field_Type?.toLowerCase() || null;
    },
    getDependentFieldOptions() {
      if (!this.selectedParentField) return [];

      const dependentField = this.listData.find(
        (item) => item.Custom_Field_Id === this.selectedParentField
      );

      if (
        dependentField?.Custom_Field_Type?.toLowerCase() === "single choice" ||
        dependentField?.Custom_Field_Type?.toLowerCase() === "multiple choice"
      ) {
        return JSON.parse(dependentField.Dropdown_Values || "[]");
      }

      return [];
    },
    validationMessage() {
      if (this.selectedValidationType) {
        return this.listValidations.find(
          (item) => item.Validation_Id === this.selectedValidationType
        ).Description;
      }
      return null;
    },
    isPreviewFieldRequired() {
      return this.selectedFormValue.some((form) => form.Field_Visibility);
    },
    showPreviewScreen() {
      if (this.fieldName && this.selectedFieldType) {
        if (
          (this.selectedFieldType.toLowerCase() === "multiple choice" ||
            this.selectedFieldType.toLowerCase() === "single choice") &&
          this.selectedOptions?.length
        )
          return true;
        else if (
          (this.selectedFieldType.toLowerCase() === "text area" ||
            this.selectedFieldType.toLowerCase() === "text field") &&
          this.minCharacterText &&
          this.maxCharacterText &&
          this.selectedValidationType
        )
          return true;
        else if (
          this.selectedFieldType.toLowerCase() === "number" &&
          this.dataRangeNumber
        )
          return true;
        else if (this.selectedFieldType.toLowerCase() === "date") return true;
        else if (
          this.selectedFieldType.toLowerCase() === "url" &&
          this.urlLinkValue
        )
          return true;
        else return false;
      } else return false;
    },
    selectProperties() {
      if (this.selectedFieldType?.toLowerCase() === "multiple choice") {
        return {
          multiple: true,
          chips: true,
          clearable: true,
          closableChips: true,
        };
      } else {
        return {
          clearable: true,
        };
      }
    },
    selectDependentProperties() {
      if (this.getParentFieldType?.toLowerCase() === "multiple choice") {
        return {
          multiple: true,
          chips: true,
          clearable: true,
          closableChips: true,
        };
      } else {
        return {
          clearable: true,
        };
      }
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
  },
  watch: {
    selectedDate(val) {
      if (val) {
        this.dateMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedDate = dateValue;
      }
    },
    minCharacterText() {
      if (this.minCharacterText > this.maxCharacterText) {
        this.maxCharacterText = null;
      }
    },
    selectedValidationType() {
      this.previewTextArea = null;
      this.previewTextField = null;
      this.$refs["previewTextField"]?.reset();
      this.$refs["previewTextArea"]?.reset();
    },
    selectedParentValue(val) {
      if (this.getParentFieldType === "date" && val) {
        this.parentDateMenu = false;
        this.parentFormattedDate = this.formatDate(val);
      }
    },
    selectedParentField() {
      this.parentFormattedDate = "";
      this.parentDateMenu = false;
    },
  },
  created() {
    this.getRolesList();
  },
  mounted() {
    this.listValidations = this.listInputValidations();
    if (this.isEdit) {
      this.fieldName = this.selectedItem?.Custom_Field_Name || "";
      this.selectedFieldType = this.selectedItem?.Custom_Field_Type || null;
      this.selectedRoles = this.selectedItem.Roles_Id?.length
        ? this.selectedItem.Roles_Id.map((role) => role.Roles_Id)
        : null;
      this.minCharacterText = this.selectedItem.Min_Validation || null;
      this.maxCharacterText = this.selectedItem.Max_Validation || null;
      this.selectedValidationType = this.selectedItem.Validation_Id || null;
      this.selectedOptions = JSON.parse(this.selectedItem.Dropdown_Values);
      this.dataRangeNumber = [
        this.selectedItem.Min_Validation,
        this.selectedItem.Max_Validation,
      ];
      this.urlLinkValue = this.selectedItem.Url_Link || null;

      // Handle visibility condition if it exists
      if (this.selectedItem.Visibility_Condition) {
        const visibilityCondition =
          typeof this.selectedItem.Visibility_Condition === "string"
            ? JSON.parse(this.selectedItem.Visibility_Condition)
            : this.selectedItem.Visibility_Condition;
        this.selectedParentField = visibilityCondition.Custom_Field_Id;

        // Set the parent type (Any or Specific)
        this.selectedParentType = visibilityCondition.Type || null;

        // Only process parent value if type is Specific
        if (this.selectedParentType === "Specific") {
          // Handle different types of parent values
          if (this.getParentFieldType === "multiple choice") {
            // For multiple choice, ensure the value is an array
            // Check if the value is a JSON string and parse it
            if (
              typeof visibilityCondition.Value === "string" &&
              visibilityCondition.Value.startsWith("[") &&
              visibilityCondition.Value.endsWith("]")
            ) {
              try {
                this.selectedParentValue = JSON.parse(
                  visibilityCondition.Value
                );
              } catch (e) {
                this.selectedParentValue = [visibilityCondition.Value];
              }
            } else if (Array.isArray(visibilityCondition.Value)) {
              this.selectedParentValue = visibilityCondition.Value;
            } else {
              this.selectedParentValue = [visibilityCondition.Value];
            }
          } else {
            this.selectedParentValue = visibilityCondition.Value;

            // Format date if the parent field is a date type
            if (
              this.getParentFieldType === "date" &&
              this.selectedParentValue
            ) {
              this.parentFormattedDate = this.formatDate(
                this.selectedParentValue
              );
            }
          }
        }
      }

      this.convertingToValidFormsStructure(this.selectedItem);
    } else {
      const constructedForm = this.listForms.map((form) => ({
        Form_Id: form.formId,
        Form_Name: form.formName,
        Field_Visibility: false,
        Integration_Mapping_Key: null,
        Mandatory: false,
      }));
      this.selectedFormValue = constructedForm;
    }
  },
  methods: {
    listInputValidations,
    onCloseOverlay() {
      this.showAddEditForm = false;
      this.$emit("close-form");
    },
    convertingToValidFormsStructure(value) {
      if (Array.isArray(value.FormIds) && value?.FormIds?.length) {
        // Create a Set of Form_Ids from the selectedItem.FormIds for quick lookup
        const selectedFormIds = new Set(
          value.FormIds.map((form) => form.Form_Id)
        );

        // Add all matching forms and include missing forms from listForms
        this.selectedFormValue = [
          ...value.FormIds.map((form) => ({
            ...form,
            Field_Visibility: true,
            Mandatory: form.Mandatory?.toLowerCase() === "yes",
          })),
          ...this.listForms
            .filter((form) => !selectedFormIds.has(form.formId))
            .map((form) => ({
              Form_Id: form.formId,
              Form_Name: form.formName,
              Integration_Mapping_Key: null,
              Mandatory: false,
              Field_Visibility: false,
            })),
        ];
      } else {
        this.selectedFormValue = [];
      }
    },
    dynamicValidationRule(value) {
      if (!value) return true;
      // Find the selected validation object
      const selectedValidation = this.listValidations.find(
        (validation) => validation.Validation_Id === this.selectedValidationType
      );
      if (selectedValidation && selectedValidation.Regular_Expression) {
        const regex = new RegExp(selectedValidation.Regular_Expression, "u");
        return regex.test(value) ? true : `${selectedValidation.Description} `;
      }
      return true;
    },
    getRolesList() {
      let vm = this;
      vm.rolesLoader = true;
      vm.$apollo
        .query({
          query: RETRIEVE_LIST_ROLES,
          client: "apolloClientAC",
          variables: {
            formId: 309,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listRoles &&
            response.data.listRoles.listRoles &&
            !response.data.listRoles.errorCode
          ) {
            vm.listRoles = response.data.listRoles.listRoles;
          } else {
            this.handleRetrieveError(response.data.listRoles.errorCode);
          }
          vm.rolesLoader = false;
        })
        .catch((err) => {
          vm.rolesLoader = false;
          vm.handleRetrieveError(err);
        });
    },
    handleRetrieveError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: this.landedFormName,
        isListError: false,
      });
    },
    // Toggle all checkboxes for a given field
    toggleSelectAll(field) {
      const value =
        field === "Field_Visibility"
          ? this.selectAllVisibility
          : this.selectAllMandatory;
      this.selectedFormValue.forEach((form) => {
        if (field === "Field_Visibility") {
          form[field] = value;
          this.selectAllMandatory = false;
          if (!value) {
            form.Mandatory = false; // Uncheck Mandatory if Visibility is unchecked
          }
        } else {
          if (form.Field_Visibility) {
            form[field] = value;
          }
        }
      });
    },
    // Handle individual Field Visibility change
    handleFieldVisibilityChange(formIndex) {
      const form = this.selectedFormValue[formIndex];
      if (!form.Field_Visibility) {
        form.Mandatory = false; // Uncheck Mandatory if Visibility is unchecked
      }
    },
    onChangeFieldType() {
      this.selectedPreviewDropdown = null;
      this.previewTextField = null;
      this.previewTextArea = null;
      this.inputOptions = "";
      this.maxCharacterText = null;
      this.$refs["maxCharacter"]?.reset();
      this.dataRangeNumber = [0, 1000];
      this.selectedOptions = [];
      this.previewNumberField = [];
      this.dateMenu = false;
      this.formattedDate = "";
      this.selectedDate = null;
      this.selectedParentField = null;
      this.selectedParentValue = null;
      this.parentDateMenu = false;
      this.parentFormattedDate = "";
    },
    showAddIcon() {
      this.showIconOptions = !!this.inputOptions.trim();
    },
    addChip() {
      if (this.inputOptions.trim()) {
        this.selectedOptions.push(this.inputOptions.trim());
        this.inputOptions = "";
        this.showIconOptions = false;
      }
    },
    removeChip(index) {
      this.selectedOptions.splice(index, 1);
    },
    urlRedirection(redirectionPath) {
      if (redirectionPath) {
        window.open(redirectionPath, "_blank");
      }
    },
    async validateCustomFields() {
      // checking whether the fields of form are valid or not
      const { valid } = await this.$refs.customFieldsForm.validate();
      // check wheather atleast one form Field_Visibility is selected
      const invalidFormCheck = this.selectedFormValue.some(
        (form) => form.Field_Visibility === true
      );
      // submit the form only if all the fields are filled
      if (valid && invalidFormCheck) {
        this.addUpdateCustomFields(this.constructVariableToAPI());
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "fieldType",
                "roles",
                "validationType",
                "parentField",
                "parentType",
                "parentValue",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 0.4, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },
    constructVariableToAPI() {
      let params = {};

      // Common fields that always get added
      params.Custom_Field_Id = this.isEdit
        ? this.selectedItem.Custom_Field_Id
        : null;
      params.Custom_Field_Name = this.fieldName;
      params.Custom_Field_Type = this.selectedFieldType;
      params.Roles_Id = this.selectedRoles;

      let Form_Id_Associated = [];
      if (Array.isArray(this.selectedFormValue)) {
        Form_Id_Associated = this.selectedFormValue
          .filter((form) => form.Field_Visibility === true)
          .map((form) => ({
            Form_Id: form.Form_Id,
            Mandatory: form.Mandatory === true ? "Yes" : "No",
            Integration_Mapping_Key: form.Integration_Mapping_Key,
          }));
      } else {
        Form_Id_Associated = [];
      }

      params.Form_Id_Associated = Form_Id_Associated;

      // Add visibility condition if parent field is selected
      if (this.selectedParentField) {
        params.Visibility_Condition = {
          Custom_Field_Id: this.selectedParentField,
          Type: this.selectedParentField ? this.selectedParentType : null,
          Value:
            this.selectedParentType === "Any" && this.selectedParentType
              ? null
              : this.getParentFieldType === "multiple choice"
              ? JSON.stringify(this.selectedParentValue)
              : this.selectedParentValue,
        };
      }

      // Conditional logic based on the selectedFieldType
      if (
        this.selectedFieldType.toLowerCase() === "text area" ||
        this.selectedFieldType.toLowerCase() === "text field"
      ) {
        params.Min_Validation = parseInt(this.minCharacterText);
        params.Max_Validation = parseInt(this.maxCharacterText);
        params.Validation_Id = parseInt(this.selectedValidationType);
      }

      if (
        this.selectedFieldType.toLowerCase() === "single choice" ||
        this.selectedFieldType.toLowerCase() === "multiple choice"
      ) {
        params.Dropdown_Values = this.selectedOptions;
      }

      if (this.selectedFieldType.toLowerCase() === "number") {
        params.Min_Validation = parseInt(this.dataRangeNumber[0]);
        params.Max_Validation = parseInt(this.dataRangeNumber[1]);
      }

      if (this.selectedFieldType.toLowerCase() === "url") {
        params.Url_Link = this.urlLinkValue;
      }

      // Return the final parameters object
      return params;
    },
    addUpdateCustomFields(params = {}) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_CUSTOM_FIELD,
          client: "apolloClientJ",
          variables: params,
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.addUpdateCustomField &&
            !response.data.addUpdateCustomField.errorCode
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: response.data.addUpdateCustomField?.message,
            };
            vm.showAddEditForm = false;
            vm.showAlert(snackbarData);
            vm.$emit("custom-fields-updated");
          } else {
            vm.handleAddUpdateErrors(
              response.data.addUpdateCustomField.errorCode
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.handleAddUpdateErrors(error);
        });
    },
    handleAddUpdateErrors(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.landedFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
<style scoped>
#integration-form > .v-overlay__content {
  height: 100%;
  width: 50%;
}
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
