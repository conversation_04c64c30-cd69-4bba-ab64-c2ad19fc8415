<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="onCloseView()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:93vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div
              class="text-h6 text-medium ps-2 flex-wrap"
              style="max-width: 90%; text-wrap: auto"
            >
              New Position & Additional Headcount Details
            </div>
            <div class="d-flex align-center">
              <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
                <v-icon>fas fa-times</v-icon>
              </v-btn>
            </div>
          </v-card-title>
          <div class="d-flex justify-end align-center">
            <v-btn
              v-if="
                formAccess &&
                formAccess.update &&
                selectedPositionData &&
                (isAdmin ||
                  loginEmployeeName?.toLowerCase() ===
                    selectedPositionData?.Added_By?.toLowerCase()) &&
                (selectedPositionData?.Status?.toLowerCase() ==
                  'waiting for approval' ||
                  selectedPositionData?.Status?.toLowerCase() == 'draft')
              "
              @click="onEditPosition()"
              class="mr-3 mt-3 bg-white text-primary"
              variant="text"
              rounded="lg"
            >
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <div
            class="px-6"
            v-if="selectedPositionData && selectedPositionData.Position_Title"
          >
            <v-row>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Position</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Position_Title)
                    }}{{
                      selectedPositionData.Pos_Code
                        ? " - " + selectedPositionData.Pos_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Group</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Group_Name)
                    }}{{
                      selectedPositionData.Group_Code
                        ? " - " + selectedPositionData.Group_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Division</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Division_Name)
                    }}{{
                      selectedPositionData.Division_Code
                        ? " - " + selectedPositionData.Division_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Department</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Department_Name)
                    }}{{
                      selectedPositionData.Department_Code
                        ? " - " + selectedPositionData.Department_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Section</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Section_Name)
                    }}{{
                      selectedPositionData.Section_Code
                        ? " - " + selectedPositionData.Section_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Cost Center</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Cost_Center) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Position Level
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Position_Level) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Reason for Request
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{
                      checkNullValue(
                        selectedPositionData.Reason_For_Replacement
                      )
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Employee Type
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{
                      checkNullValue(selectedPositionData.Employee_Type_Name)
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  No of Positions
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.No_Of_Position) }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="
                  customGroupCoverage &&
                  customGroupCoverage.toLowerCase() === 'custom group'
                "
                cols="12"
                sm="6"
                md="4"
                class="px-md-6 pb-0"
              >
                <div class="text-subtitle-1 font-weight-bold">
                  Recruiters Group for Notification
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.CustomGroupName) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="4" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Status</div>
                <div class="value-text">
                  <section class="text-body-2">
                    <span
                      :class="getStatusClass(selectedPositionData.Status)"
                      class="text-body-2 font-weight-regular d-flex align-center"
                      >{{ checkNullValue(selectedPositionData.Status) }}</span
                    >
                  </section>
                </div>
              </v-col>
            </v-row>
            <v-card
              class="pa-4 mt-4"
              v-if="
                selectedPositionData.Pos_Code === null ||
                selectedPositionData.Pos_Code === undefined
              "
            >
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="green-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Duties And Responsibilities
              </div>
              <div class="my-4">
                <v-data-table
                  :headers="tableHeaders"
                  :items="selectedPositionData?.DutiesResponsibilities"
                  fixed-header
                  :hide-default-footer="true"
                  style="box-shadow: none !important"
                  class="elevation-1"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView
                            ? `width: ${windowWidth - 100}px`
                            : 'width: 200px'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Regular Duties
                        </div>
                        <section class="d-flex align-center">
                          <v-tooltip
                            :text="item?.Regular_Duties"
                            location="bottom"
                            max-width="300"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                v-bind="props"
                                class="text-body-2 text-truncate text-start text-primary"
                                style="max-width: 200px"
                              >
                                {{ checkNullValue(item.Regular_Duties) }}
                              </div>
                            </template>
                          </v-tooltip>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView
                            ? `width: ${windowWidth - 100}px`
                            : 'width: 200px'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          No. of Hours Performed per Period
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.No_Of_Hours_Period) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView
                            ? `width: ${windowWidth - 100}px`
                            : 'width: 200px'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Period
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.Period) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView
                            ? `width: ${windowWidth - 100}px`
                            : 'width: 200px'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Competencies
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.Competency) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView
                            ? `width: ${windowWidth - 100}px`
                            : 'width: 200px'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Required Rating of Competency
                        </div>
                        <section
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 500px; '
                              : 'max-width: 200px; '
                          "
                        >
                          <v-rating
                            v-model="item.Rating_Of_Competency"
                            readonly
                            density="default"
                            half-increments
                            :length="5"
                            :size="40"
                            active-color="primary"
                            color="orange-lighten-1"
                          ></v-rating>
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </div>
            </v-card>
            <v-card
              class="pa-4 mt-5"
              v-if="
                selectedPositionData.Pos_Code === null ||
                selectedPositionData.Pos_Code === undefined
              "
            >
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="light-blue"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Working Conditions
              </div>
              <div class="my-4">
                <v-data-table
                  :headers="workConditionHeaders"
                  :items="selectedPositionData?.WorkingConditions"
                  fixed-header
                  :hide-default-footer="true"
                  style="box-shadow: none !important"
                  class="elevation-1"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Work Area
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.Working_Area) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Percent of Time Spent
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.Time_Spent) }}
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </div>
            </v-card>
            <v-card
              class="pa-4 mt-5"
              v-if="
                selectedPositionData.Pos_Code === null ||
                selectedPositionData.Pos_Code === undefined
              "
            >
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="deep-orange-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Education Requirements
              </div>
              <div class="my-4 d-flex">
                <v-col cols="12" sm="6" md="6" class="px-md-5 pb-0">
                  <div class="text-subtitle-1 font-weight-bold">
                    Education Type
                  </div>
                  <div class="text-subtitle-1 text-grey-darken-1">
                    <section class="text-body-2">
                      {{
                        checkNullValue(
                          selectedPositionData.education[0]?.Education_Type
                        )
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-5 pb-0">
                  <div class="text-subtitle-1 font-weight-bold">
                    Education Description
                  </div>
                  <div class="text-subtitle-1 text-grey-darken-1">
                    <section class="text-body-2">
                      {{
                        checkNullValue(
                          selectedPositionData.education[0]?.Description
                        )
                      }}
                    </section>
                  </div>
                </v-col>
              </div>
            </v-card>
            <v-card
              class="pa-4 mt-5"
              v-if="
                selectedPositionData.Pos_Code === null ||
                selectedPositionData.Pos_Code === undefined
              "
            >
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="deep-orange-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Experience Requirements
              </div>
              <div class="my-4">
                <v-data-table
                  :headers="experienceHeaders"
                  :items="selectedPositionData?.Experience"
                  fixed-header
                  :hide-default-footer="true"
                  style="box-shadow: none !important"
                  class="elevation-1"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Type of Job/Positions or Area of Expertise
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.Type_Of_Jobs) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          No. of Years/Months Required
                        </div>
                        <section class="d-flex align-center">
                          {{
                            `${item?.Years || 0} Years, 
                            ${item?.Months || 0} Months`
                          }}
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </div>
            </v-card>
            <v-card
              class="pa-4 mt-5"
              v-if="
                selectedPositionData.Pos_Code === null ||
                selectedPositionData.Pos_Code === undefined
              "
            >
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="pink-lighten-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                License And/ Or Certifications
              </div>
              <div class="my-4 pl-6">
                {{ checkNullValue(selectedPositionData?.License_Certificate) }}
              </div>
              <v-col cols="12" class="px-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  License Certificate Details
                </div>
                <div class="text-subtitle-1 text-grey-darken-1">
                  <section class="text-body-2">
                    {{
                      checkNullValue(
                        selectedPositionData.License_Certificate_Details
                      )
                    }}
                  </section>
                </div>
              </v-col>
            </v-card>
            <v-card
              class="pa-4 mt-5"
              v-if="
                selectedPositionData.Pos_Code === null ||
                selectedPositionData.Pos_Code === undefined
              "
            >
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="teal-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Operating Network / Working Relationships
              </div>
              <div class="my-4">
                <v-row>
                  <v-col cols="12" md="6" class="px-6 pb-0">
                    <div class="text-subtitle-1 font-weight-bold">
                      Internal Operating Network
                    </div>
                    <div class="value-text">
                      <section class="text-body-2">
                        {{
                          selectedPositionData.Internal_Operating_Network.length
                            ? selectedPositionData.Internal_Operating_Network.join(
                                ","
                              )
                            : "-"
                        }}
                      </section>
                    </div>
                  </v-col>
                  <v-col cols="12" md="6" class="px-6 pb-0">
                    <div class="text-subtitle-1 font-weight-bold">
                      External Operating Network
                    </div>
                    <div class="value-text">
                      <section class="text-body-2">
                        {{
                          selectedPositionData.External_Operating_Network.length
                            ? selectedPositionData.External_Operating_Network.join(
                                ","
                              )
                            : "-"
                        }}
                      </section>
                    </div>
                  </v-col>
                </v-row>
              </div>
            </v-card>
            <v-card
              class="pa-3 mt-5"
              v-if="
                selectedPositionData.Pos_Code === null ||
                selectedPositionData.Pos_Code === undefined
              "
            >
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="purple-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Additional Comments
              </div>
              <div class="my-4">
                <v-row>
                  <v-col cols="12" class="pl-6">
                    {{ checkNullValue(selectedPositionData.Comments) }}
                  </v-col>
                </v-row>
              </div>
            </v-card>
            <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails> </v-col
            ></v-row>
          </div>
          <v-card-text
            class="card mb-3 px-0"
            style="overflow-y: auto"
          ></v-card-text>
        </v-card>
      </template>
    </v-overlay>
  </div>
</template>

<script>
import { checkNullValue } from "@/helper";
import moment from "moment";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { GET_CUSTOM_GROUP_COVERAGE } from "@/graphql/settings/irukka-integration/jobPostFormQueries.js";

export default {
  name: "NewPositionView",
  emits: ["close-view-details", "edit-position-details"],
  props: {
    showViewDetails: {
      type: Boolean,
      required: true,
    },
    selectedPositionData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      showViewForm: false,
      moreDetailsList: [],
      openMoreDetails: true,
      customGroupCoverage: null,
    };
  },
  watch: {
    showViewDetails(val) {
      this.showViewForm = val;
      this.prefillMoreDetails();
    },
  },
  components: {
    MoreDetails,
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formAccess() {
      let formAccess = this.accessRights("290");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    tableHeaders() {
      let headers = [
        {
          title: "Regular Duties",
          align: "start",
          key: "Regular_Duties",
        },
        {
          title: "No. of Hours Performed per Period",
          align: "start",
          key: "No_Of_Hours_Period",
        },
        { title: "Period", align: "start", key: "Period" },
        {
          title: "Competencies",
          align: "start",
          key: "Competency",
        },
        {
          title: "Required Rating of Competency",
          align: "start",
          key: "Rating_Of_Competency",
        },
      ];
      return headers;
    },
    workConditionHeaders() {
      let headers = [
        {
          title: "Work Area",
          align: "start",
          key: "Working_Area",
        },
        {
          title: "Percent of Time Spent",
          align: "start",
          key: "Time_Spent",
        },
      ];
      return headers;
    },
    experienceHeaders() {
      return [
        {
          title: "Type of Job/Positions or Area of Expertise",
          align: "start",
          key: "Type_Of_Jobs",
        },
        {
          title: "No. of Years/Months Required",
          align: "start",
          sortable: false,
          key: "Months",
        },
      ];
    },
  },
  mounted() {
    this.getCustomGroupCoverage();
  },
  methods: {
    checkNullValue,
    onCloseView() {
      this.showViewForm = false;
      this.$emit("close-view-details");
    },
    onEditPosition() {
      this.showViewForm = false;
      this.$emit("edit-position-details");
    },
    getStatusClass(status) {
      if (status === "Open") {
        return "text-amber-darken-4";
      } else if (status === "Closed") {
        return "text-amber";
      } else if (status === "Draft") {
        return "text-purple-darken-4";
      } else if (status === "Scheduled For Interview") {
        return "text-green";
      } else if (status === "Approved") {
        return "text-brown-darken-4";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.selectedPositionData.Added_On + ".000Z")
        ),
        addedByName = this.selectedPositionData.Added_By,
        updatedByName = this.selectedPositionData.Updated_By,
        updatedOn = this.formatDate(
          new Date(this.selectedPositionData.Updated_On + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    async getCustomGroupCoverage() {
      let vm = this;
      vm.listLoading = true;
      await vm.$apollo
        .query({
          query: GET_CUSTOM_GROUP_COVERAGE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.recruitmentSetting &&
            response.data.recruitmentSetting.settingResult &&
            response.data.recruitmentSetting.settingResult.length
          ) {
            this.customGroupCoverage =
              response.data.recruitmentSetting.settingResult[0].Coverage;
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleGetCustomGroupCoverageError(err);
        });
    },
    handleGetCustomGroupCoverageError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "custom group settings",
        isListError: false,
      });
    },
  },
};
</script>
<style scoped>
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
