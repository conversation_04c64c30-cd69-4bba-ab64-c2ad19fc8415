<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">Grade</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mr-1">
            <v-tooltip grades="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="isFormDirty ? '' : props"
                  rounded="lg"
                  class="mb-2 secondary"
                  variant="elevated"
                  type="submit"
                  @click="isFormDirty ? validateGradeForm() : {}"
                  ><span class="px-2 primary">Save</span></v-btn
                >
              </template>
              <div v-if="!isFormDirty">There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon class="mt-n2" color="primary" @click="closeAddForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card-text style="height: calc(100vh - 300px); overflow: scroll">
        <v-form ref="GradeAddForm" @submit.prevent="[]">
          <v-row class="px-sm-4 px-md-6 pt-sm-4">
            <!-- Level -->
            <v-col
              v-if="entomoIntegrationEnabled && isEntomoSyncTypePush"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="selectedLevel"
                variant="solo"
                :rules="[
                  required(`Level`, selectedLevel),
                  selectedLevel
                    ? validateWithRulesAndReturnMessages(
                        selectedLevel,
                        'level',
                        'Level',
                        true
                      )
                    : true,
                ]"
                type="number"
                style="max-width: 300px"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  Level
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <!-- Grade Code -->
            <v-col
              v-if="entomoIntegrationEnabled"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="gradeCode"
                variant="solo"
                :rules="[
                  required(
                    labelList[311]?.Field_Alias || 'Grade Code',
                    gradeCode
                  ),
                  gradeCode
                    ? validateWithRulesAndReturnMessages(
                        gradeCode,
                        'gradeCode',
                        labelList[311]?.Field_Alias || 'Grade Code'
                      )
                    : true,
                  gradeCode
                    ? minMaxStringValidation(
                        labelList[311]?.Field_Alias || 'Grade Code',
                        gradeCode,
                        1,
                        100
                      )
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>{{ labelList[311]?.Field_Alias || "Grade Code" }}</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-else-if="
                labelList[311] &&
                labelList[311].Field_Visiblity.toLowerCase() === 'yes'
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="gradeCode"
                variant="solo"
                :rules="[
                  labelList[311].Mandatory_Field === 'Yes'
                    ? required(`${labelList[311].Field_Alias}`, gradeCode)
                    : true,
                  gradeCode
                    ? validateWithRulesAndReturnMessages(
                        gradeCode,
                        'gradeCode',
                        labelList[311].Field_Alias
                      )
                    : true,
                  gradeCode
                    ? minMaxStringValidation(
                        `${labelList[311].Field_Alias}`,
                        gradeCode,
                        1,
                        100
                      )
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>{{ labelList[311].Field_Alias }}</span>
                  <span
                    v-if="labelList[311].Mandatory_Field === 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="employeeGrade"
                variant="solo"
                ref="employeeGrade"
                :rules="[
                  required('Grade', employeeGrade),
                  gradeFirstCharacterValidation(employeeGrade),
                  validateWithRulesAndReturnMessages(
                    employeeGrade,
                    'employeeGrade',
                    'Grade'
                  ),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Grade</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="minimumGrossAnnualSalary"
                variant="solo"
                ref="minimumGrossAnnualSalary"
                type="number"
                :rules="[
                  minMaxNumberValidation(
                    'Minimum Gross Annual Salary',
                    minimumGrossAnnualSalary,
                    1,
                    9999999999999
                  ),
                  required(
                    'Minimum Gross Annual Salary',
                    minimumGrossAnnualSalary
                  ),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Minimum Gross Annual Salary</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="maximumGrossAnnualSalary"
                variant="solo"
                ref="maximumGrossAnnualSalary"
                type="number"
                :rules="[
                  minMaxNumberValidation(
                    'Maximum Gross Annual Salary',
                    maximumGrossAnnualSalary,
                    1,
                    9999999999999
                  ),
                  required(
                    'Maximum Gross Annual Salary',
                    maximumGrossAnnualSalary
                  ),
                  salaryComparisonRule(maximumGrossAnnualSalary),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Maximum Gross Annual Salary</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="minimumHourlyWages"
                variant="solo"
                type="number"
                :rules="[
                  !minimumHourlyWages ||
                    minMaxNumberValidation(
                      'Minimum Hourly Wages',
                      minimumHourlyWages,
                      1,
                      9999999
                    ),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Minimum Hourly Wages</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="maximumHourlyWages"
                variant="solo"
                type="number"
                :rules="[
                  !maximumHourlyWages ||
                    minMaxNumberValidation(
                      'Maximum Hourly Wages',
                      maximumHourlyWages,
                      1,
                      9999999
                    ),
                  hourlyWageComparisonRule(maximumHourlyWages),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Maximum Hourly Wages</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="minimumOvertimeHourlyWages"
                maxlength="2"
                variant="solo"
                type="number"
                :rules="[
                  !minimumOvertimeHourlyWages ||
                    minMaxNumberValidation(
                      'Minimum Overtime Hourly Wages',
                      minimumOvertimeHourlyWages,
                      1,
                      9999999
                    ),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Minimum Overtime Hourly Wages</span>
                  <!-- <span class="ml-1" style="color: red">*</span> -->
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="maximumOvertimeHourlyWages"
                maxlength="50"
                variant="solo"
                type="number"
                :rules="[
                  !maximumOvertimeHourlyWages ||
                    minMaxNumberValidation(
                      'Maximum Overtime Hourly Wages',
                      maximumOvertimeHourlyWages,
                      1,
                      9999999
                    ),
                  overtimeHourlyWageComparisonRule(maximumOvertimeHourlyWages),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Maximum Overtime Hourly Wages</span>
                  <!-- <span class="ml-1" style="color: red">*</span> -->
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                :items="parentGradesList"
                v-model="ParentGrade"
                label="Parent Grade"
                itemValue="parentGradeId"
                itemTitle="parentGradeDisplay"
                ref="parentgrade"
                :isAutoComplete="true"
                :itemSelected="parentGrade"
                @selected-item="parentGrade = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-row class="d-flex align-center">
                <v-col class="d-flex align-center">
                  <p class="text-subtitle-1 text-grey-darken-1 m-0">
                    Overtime Eligibility
                  </p>
                </v-col>
                <v-col class="d-flex justify-end">
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="overtimeEligibility"
                    true-value="Yes"
                    false-value="No"
                    @update:model-value="deductFormChange()"
                  ></v-switch>
                </v-col>
              </v-row>
            </v-col>

            <v-col
              v-if="overtimeEligibility === 'Yes'"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                :items="overtimeAllocationList"
                v-model="overtimeAllocation"
                label="Overtime Allocation"
                itemValue="value"
                itemTitle="text"
                ref="overtimeAllocation"
                :rules="[required('Overtime Allocation', overtimeAllocation)]"
                :isAutoComplete="true"
                :isRequired="true"
                @update:model-value="deductFormChange()"
              >
              </CustomSelect>
            </v-col>

            <!-- Overtime Fixed Amount Field -->
            <v-col
              v-if="
                overtimeEligibility.toLowerCase() === 'yes' &&
                overtimeAllocation === 'Fixed Amount'
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="overtimeFixedAmount"
                variant="solo"
                ref="overtimeFixedAmount"
                type="number"
                :rules="[
                  minMaxNumberValidation(
                    'Overtime Fixed Amount',
                    overtimeFixedAmount,
                    0,
                    99999999
                  ),
                  required('Overtime Fixed Amount', overtimeFixedAmount),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Overtime Fixed Amount</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <!-- Wage Index Field -->
            <v-col
              v-if="
                overtimeEligibility.toLowerCase() === 'yes' &&
                overtimeAllocation === 'Wage Index'
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="wageIndex"
                variant="solo"
                type="number"
                :disabled="!overtimeEligibility"
                :rules="[
                  minMaxNumberValidation('Wage Index', wageIndex, 0, 100),
                  required('Wage Index', wageIndex),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:prepend-inner>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs, props }">
                      <v-icon
                        class="ml-1"
                        size="small"
                        color="blue"
                        v-bind="(attrs, props)"
                        v-on="on"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <span
                      ><p>
                        Wage Index allows the user to enter the times of salary
                        to be provided for working on this holiday.
                      </p>
                      <p>
                        Holiday special wages may be double or triple their
                        daily wages. Eg, 2 for twice the daily wages
                      </p></span
                    >
                  </v-tooltip>
                </template>
                <template v-slot:label>
                  <span>Wage Index Per Hour</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              cols="12"
              sm="12"
              lg="12"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-textarea
                v-model="description"
                rows="2"
                row-height="10"
                color="primary"
                hide-details="auto"
                variant="solo"
                label="Description"
                counter="600"
                :rules="[
                  description
                    ? minMaxStringValidation('description', description, 5, 600)
                    : true,
                  description
                    ? validateWithRulesAndReturnMessages(
                        description,
                        'description',
                        'Description'
                      )
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isLoadingCard"
        scrim="#fff"
      >
        <v-progress-circular color="secondary" indeterminate size="54">
        </v-progress-circular>
      </v-overlay>
    </v-card>
    <AppWarningModal
      v-if="showConfirmation"
      :open-modal="showConfirmation"
      imgUrl="common/exit_form"
      confirmation-heading="Are you sure to exit this form?"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 secondary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  LIST_PARENT_GRADES,
  ADD_UPDATE_EMPLOYEE_GRADE,
} from "@/graphql/organisation/grades/gradeQueries";
import mixpanel from "mixpanel-browser";

export default {
  name: "AddEditEmployeesGrade",
  mixins: [validationRules],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    CustomSelect,
  },
  emits: ["close-form", "form-updated"],
  data: () => ({
    // others
    selectedLevel: null,
    showConfirmation: false,
    isFormDirty: false,

    // data
    employeeGrade: "",
    gradeCode: null,
    parentGradeList: [],
    grade: null,
    gradeId: 0,
    parentId: null,
    parentGradeId: null,
    parentGrade: null,
    minExperience: null,
    maxExperience: null,
    minHourWages: null,
    maxHourWages: null,
    minOvertimeWages: null,
    maxOvertimeWages: null,
    description: null,
    minAnnualSalary: null,
    maxAnnualSalary: null,
    overTimeFixedAmount: null,
    eligibleOvertime: null,
    overTimeAllocation: null,
    overTimeWageIndex: null,
    minimumHourlyWages: null,
    maximumHourlyWages: "",
    minimumOvertimeHourlyWages: null,
    maximumOvertimeHourlyWages: null,
    minimumGrossAnnualSalary: "",
    maximumGrossAnnualSalary: "",
    overtimeEligibility: "",
    overtimeAllocation: null,
    overtimeAllocationList: [
      { value: "Wage Index", text: "Wage Index" },
      { value: "Fixed Amount", text: "Fixed Amount" },
    ],
    overtimeFixedAmount: null,
    wageIndex: null,

    // loading
    isLoadingCard: false,

    // errors
    validationMessages: [],
    originalParentGradesList: [],
    parentGradesList: [],
    showValidationAlert: false,
  }),

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },

  mounted() {
    if (this.isEdit) {
      const {
        grade,
        gradeId,
        gradeCode,
        parentGrade,
        parentGradeId,
        minHourWages,
        maxHourWages,
        minOvertimeWages,
        maxOvertimeWages,
        minAnnualSalary,
        maxAnnualSalary,
        eligibleOvertime,
        overTimeAllocation,
        overTimeFixedAmount,
        overTimeWageIndex,
        description,
        level,
      } = this.editFormData;

      this.selectedLevel = level;
      this.employeeGrade = grade ? grade : "";
      this.gradeId = gradeId ? parseInt(gradeId) : 0;
      this.gradeCode = gradeCode ? gradeCode : null;
      this.ParentGrade = parentGrade ? parentGrade : null;
      this.parentGrade = parentGradeId ? parentGradeId : null;
      this.minimumHourlyWages = minHourWages ? minHourWages : null;
      this.maximumHourlyWages = maxHourWages ? maxHourWages : null;
      this.minimumOvertimeHourlyWages = minOvertimeWages
        ? minOvertimeWages
        : null;
      this.maximumOvertimeHourlyWages = maxOvertimeWages
        ? maxOvertimeWages
        : null;
      this.minimumGrossAnnualSalary = minAnnualSalary ? minAnnualSalary : null;
      this.maximumGrossAnnualSalary = maxAnnualSalary ? maxAnnualSalary : null;
      this.overtimeAllocation = overTimeAllocation ? overTimeAllocation : "";
      this.overtimeFixedAmount =
        overTimeFixedAmount !== null ? overTimeFixedAmount : null;
      this.wageIndex = overTimeWageIndex !== null ? overTimeWageIndex : null;
      this.description = description ? description : null;
      this.overtimeEligibility = eligibleOvertime === 1 ? "Yes" : "No";
    }
    this.fetchParentGrades();
  },
  methods: {
    gradeFirstCharacterValidation(value) {
      if (value && value.startsWith(" ")) {
        return "The first character of Grade Name cannot be a space.";
      }
      return true;
    },
    async validateGradeForm() {
      // Validate the form fields
      const { valid } = await this.$refs.GradeAddForm.validate();

      if (valid) {
        this.addupdateList();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });

        // Handle and log invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "employeeGrade",
                "minimumGrossAnnualSalary",
                "maximumGrossAnnualSalary",
                "wageIndex",
                "overtimeAllocation",
                "overtimeFixedAmount",
              ];

              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect
                  ? fieldRef.onFocusCustomSelect()
                  : fieldRef.focus();
              } else {
                fieldRef.focus();
              }

              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 0.4,
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },

    async addupdateList() {
      let isFormValid = await this.$refs.GradeAddForm.validate();
      let vm = this;

      if (isFormValid && isFormValid.valid) {
        try {
          vm.isLoadingCard = true;
          let isUpdate = !!vm.gradeId; // Check if it's an update or add operation

          vm.$apollo
            .mutate({
              mutation: ADD_UPDATE_EMPLOYEE_GRADE, // Mutation for grade details
              client: "apolloClientBB",
              fetchPolicy: "no-cache",
              variables: {
                level: vm.selectedLevel ? parseInt(vm.selectedLevel) : null,
                gradeId: vm.gradeId,
                parentId: vm.parentGrade,
                parentGrade: vm.parentGrade,
                gradeCode:
                  vm.gradeCode === null || vm.gradeCode === ""
                    ? null
                    : vm.gradeCode,
                employeeGrade: vm.employeeGrade,
                minHourlyWages:
                  vm.minimumHourlyWages !== "" && vm.minimumHourlyWages !== null
                    ? parseFloat(vm.minimumHourlyWages)
                    : null,
                maxHourlyWages:
                  vm.maximumHourlyWages !== "" && vm.maximumHourlyWages !== null
                    ? parseFloat(vm.maximumHourlyWages)
                    : null,
                minOvertimeWages:
                  vm.minimumOvertimeHourlyWages !== "" &&
                  vm.minimumOvertimeHourlyWages !== null
                    ? parseFloat(vm.minimumOvertimeHourlyWages)
                    : null,
                maxOvertimeWages:
                  vm.maximumOvertimeHourlyWages !== "" &&
                  vm.maximumOvertimeHourlyWages !== null
                    ? parseFloat(vm.maximumOvertimeHourlyWages)
                    : null,
                minAnnualSalary:
                  vm.minimumGrossAnnualSalary !== "" &&
                  vm.minimumGrossAnnualSalary !== null
                    ? parseFloat(vm.minimumGrossAnnualSalary)
                    : null,
                maxAnnualSalary:
                  vm.maximumGrossAnnualSalary !== "" &&
                  vm.maximumGrossAnnualSalary !== null
                    ? parseFloat(vm.maximumGrossAnnualSalary)
                    : null,
                eligibleOvertime: vm.overtimeEligibility == "Yes" ? 1 : 0,
                overtimeAllocation:
                  vm.overtimeEligibility == "Yes"
                    ? vm.overtimeAllocation
                    : null,
                overtimeWageIndex:
                  vm.overtimeEligibility == "Yes" &&
                  vm.overtimeAllocation == "Wage Index" &&
                  vm.wageIndex !== "" &&
                  vm.wageIndex !== null
                    ? parseFloat(vm.wageIndex)
                    : null,
                overtimeFixedAmount:
                  vm.overtimeEligibility == "Yes" &&
                  vm.overtimeAllocation == "Fixed Amount" &&
                  vm.overtimeFixedAmount !== "" &&
                  vm.overtimeFixedAmount !== null
                    ? parseFloat(vm.overtimeFixedAmount)
                    : null,
                description:
                  vm.description === null || vm.description === ""
                    ? null
                    : vm.description,
              },
            })
            .then((response) => {
              if (
                response &&
                response.data &&
                response.data.addUpdateEmployeeGrade
              ) {
                const { errorCode, validationError } =
                  response.data.addUpdateEmployeeGrade;
                if (!errorCode && !validationError) {
                  let snackbarData = {
                    isOpen: true,
                    type: "success",
                    message: isUpdate
                      ? "Grade updated successfully."
                      : "Grade added successfully.",
                  };
                  vm.showAlert(snackbarData);
                  vm.$emit("form-updated", isUpdate);
                  vm.isLoadingCard = false;
                } else {
                  vm.handleAddEditError(isUpdate ? "updating" : "adding");
                }
              } else {
                vm.handleAddEditError(isUpdate ? "updating" : "adding");
              }
            })
            .catch((addEditError) => {
              vm.handleAddEditError(
                isUpdate ? "updating" : "adding",
                addEditError
              );
            });
        } catch (e) {
          vm.handleAddEditError(isUpdate ? "updating" : "adding");
        }
      }
    },
    handleAddEditError(action, err = "") {
      this.isLoadingCard = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: action,
          form: "grades",
          isListError: false,
        })
        .then((validationErrors) => {
          this.validationMessages = validationErrors;
          this.showValidationAlert = true;
        });
    },

    fetchParentGrades() {
      let vm = this;
      let gradeId = this.gradeId || 0;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_PARENT_GRADES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
          variables: {
            gradeId: gradeId,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listParentGrades.listParentGradesData
          ) {
            const responseData =
              response.data.listParentGrades.listParentGradesData;
            vm.parentGradesList = responseData.map((item) => ({
              ...item,
              parentGrade: item.parentGrade || "No Parent Grades",
              parentGradeDisplay: item.parentGradeCode
                ? `${item.parentGradeCode} - ${item.parentGrade}`
                : item.parentGrade,
            }));
            vm.originalParentGradesList = responseData;
            vm.listLoading = false;
            mixpanel.track("Parent grades list retrieved");
          } else {
            vm.handleParentGradesError();
          }
        })
        .catch((err) => {
          vm.handleParentGradesError(err);
        });
    },
    handleParentGradesError(err = "") {
      mixpanel.track("Parent grades error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Parent Grades",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    deductFormChange() {
      this.isFormDirty = true;
    },

    closeAddForm() {
      if (this.isFormDirty) {
        this.showConfirmation = true;
      } else {
        this.closeForm();
      }
    },
    abortClose() {
      this.showConfirmation = false;
    },

    acceptClose() {
      this.showConfirmation = false;
      this.closeForm();
    },

    closeForm() {
      this.isFormDirty = false;
      this.$emit("close-form");
    },
    hourlyWageComparisonRule() {
      return (value) => {
        if (!value) return true;
        const minHourlyWages = parseFloat(this.minimumHourlyWages);
        const maxHourlyWages = parseFloat(this.maximumHourlyWages);
        const inputValue = parseFloat(value);

        if (
          isNaN(minHourlyWages) ||
          isNaN(maxHourlyWages) ||
          isNaN(inputValue)
        ) {
          return "Please enter valid numbers for hourly wages.";
        }

        return (
          maxHourlyWages === null ||
          inputValue > minHourlyWages ||
          "Maximum hourly wage must be greater than minimum hourly wage"
        );
      };
    },

    overtimeHourlyWageComparisonRule() {
      return (value) => {
        if (!value) return true;
        const minOvertimeHourlyWages = parseFloat(
          this.minimumOvertimeHourlyWages
        );
        const maxOvertimeHourlyWages = parseFloat(
          this.maximumOvertimeHourlyWages
        );
        const inputValue = parseFloat(value);

        if (
          isNaN(minOvertimeHourlyWages) ||
          isNaN(maxOvertimeHourlyWages) ||
          isNaN(inputValue)
        ) {
          return "Please enter valid numbers for overtime hourly wages.";
        }

        return (
          maxOvertimeHourlyWages === null ||
          inputValue > minOvertimeHourlyWages ||
          "Maximum overtime hourly wage must be greater than minimum overtime hourly wage"
        );
      };
    },

    salaryComparisonRule() {
      return (value) => {
        const minSalary = parseFloat(this.minimumGrossAnnualSalary);
        const maxSalary = parseFloat(this.maximumGrossAnnualSalary);
        const inputValue = parseFloat(value);

        if (isNaN(minSalary) || isNaN(maxSalary) || isNaN(inputValue)) {
          return "Please enter valid numbers for salaries.";
        }

        return (
          maxSalary === null ||
          inputValue > minSalary ||
          "Maximum salary must be greater than minimum salary"
        );
      };
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
