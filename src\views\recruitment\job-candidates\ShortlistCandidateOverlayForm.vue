<template>
  <div class="text-center">
    <v-overlay
      v-model="overlay"
      class="d-flex justify-end overlay-content-parent"
      @click:outside="onClickClose()"
      persistent
      style="z-index: 1000"
    >
      <template v-slot:default>
        <div class="overlay-card">
          <div
            class="d-flex align-center text-h6 text-medium-emphasis pa-2 bg-primary"
            style="width: 100%"
          >
            <v-icon
              v-if="displayCustomEmail && !noCustomTemplate"
              @click="
                displayCustomEmail
                  ? (displayCustomEmail = false)
                  : $emit('close-overlay')
              "
              size="17"
              class="mx-2"
              >fas fa-chevron-left</v-icon
            >
            <span class="ml-2"> Shortlist Candidate </span>
            <v-spacer></v-spacer>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="onClickClose()"
            ></v-btn>
          </div>
          <div class="overlay-body">
            <v-form v-if="!displayCustomEmail" ref="cancelInterviewForm">
              <div
                class="d-flex justify-space-between mt-5"
                style="max-height: 80px; flex: 1"
                v-if="emailTemplateList?.length"
              >
                <CustomSelect
                  :items="emailTemplateList"
                  v-model="selectedEmailTemplate"
                  label="Email Template"
                  itemValue="Template_Id"
                  itemTitle="Template_Name"
                  ref="selectedEmailTemplate"
                  :isAutoComplete="true"
                  :isRequired="true"
                  :rules="[required('Email Template', selectedEmailTemplate)]"
                  :itemSelected="selectedEmailTemplate"
                  @selected-item="selectedEmailTemplate = $event"
                  @update:model-value="isFormDirty = true"
                ></CustomSelect>
              </div>
            </v-form>
            <CustomEmail
              v-else
              ref="customEmail"
              :formId="16"
              typeOfTemplate="ShortlistCandidate"
              :templateData="templateData"
              :notificationTimeNow="notificationTimeNow"
              :typeOfSchedule="noncalendar"
              :selectedCandidateId="candidateId"
              :templateEmail="[candidateEmail]"
              :emailTemplateList="emailTemplateList"
              :selectedEmailTemplate="selectedEmailTemplate"
              :noCustomTemplate="noCustomTemplate"
              @custom-email-sent="customEmailSent"
            />
          </div>
          <v-card class="overlay-footer" elevation="16">
            <v-btn
              class="mr-5"
              variant="outlined"
              @click="
                displayCustomEmail && !noCustomTemplate
                  ? (this.displayCustomEmail = false)
                  : onClickClose()
              "
              rounded="lg"
              >Cancel</v-btn
            >
            <v-btn
              color="primary"
              variant="elevated"
              rounded="lg"
              @click="validateCancelInterviewForm()"
            >
              {{ displayCustomEmail ? "Send Email" : "Preview Email" }}
            </v-btn>
          </v-card>
        </div>
        <AppLoading v-if="isLoading"></AppLoading>
      </template>
    </v-overlay>
  </div>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  ></AppWarningModal>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import CustomEmail from "@/views/common/customEmail/CustomEmailComponent.vue";
import { UPDATE_CANDIDATE_STATUS } from "@/graphql/recruitment/recruitmentQueries.js";

export default {
  name: "ShortlistCandidateOverlayForm",
  components: {
    CustomSelect,
    CustomEmail,
  },
  props: {
    candidateId: {
      type: Number,
      required: true,
    },
    emailTemplateList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    noCustomTemplate: {
      type: Boolean,
      default: false,
    },
    candidateEmail: {
      type: String,
      required: true,
    },
    templateData: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  mixins: [validationRules],
  emits: ["close-shortlist-candidate-window", "close-overlay", "refetch-data"],

  data: () => ({
    isFormDirty: false,
    overlay: true,
    selectedEmailTemplate: null,
    displayCustomEmail: false,
    openConfirmationPopup: false,
    isLoading: false,
    notificationTimeNow: true,
  }),
  mounted() {
    if (this.noCustomTemplate) {
      this.displayCustomEmail = true;
    }
  },
  methods: {
    onClickClose() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.$emit("close-shortlist-candidate-window", false);
      }
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-shortlist-candidate-window", false);
    },
    async validateCancelInterviewForm() {
      if (!this.displayCustomEmail) {
        let { valid } = await this.$refs.cancelInterviewForm.validate();
        if (valid) {
          this.displayCustomEmail = true;
        }
      } else {
        let customEmailRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        if (customEmailRef) {
          let noPlaceholderFound = customEmailRef.noPlaceholderFound;
          if (noPlaceholderFound) {
            let snackbarData = {
              isOpen: true,
              message:
                "Some placeholders are not replaced, kindly replace or remove them before proceeding.",
              type: "warning",
            };
            this.showAlert(snackbarData);
            customEmailRef.noPlaceholderFound = false;
          } else {
            const { valid } =
              await customEmailRef.$refs.customEmailForm.validate();
            if (valid && customEmailRef.isContentPresent) {
              this.shortlistCandidates(customEmailRef);
            }
          }
        }
      }
    },
    customEmailSent() {
      this.$emit("custom-email-sent");
    },
    getCustomEmailRef() {
      return this.$refs.customEmail;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    shortlistCandidates(customEmailRef) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_CANDIDATE_STATUS,
          variables: {
            candidateId: this.candidateId,
            candidateStatus: 12,
          },
          client: "apolloClientAM",
        })
        .then(async () => {
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidates have been shortlisted successfully",
          };
          vm.isLoading = false;
          await customEmailRef.validateCustomEmailForm();
          vm.showAlert(snackbarData);
          this.$emit("refetch-data");
          this.$emit("close-shortlist-candidate-window", false);
        })
        .catch((err) => {
          vm.isLoading = false;
          this.handleShorlistError(err);
        });
    },
    handleShorlistError(err = "") {
      this.$emit("refetch-data");
      this.$emit("close-shortlist-candidate-window", false);
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "candidate status",
        isListError: false,
      });
    },
  },
};
</script>

<style scoped>
.headingColor {
  background-color: rgb(var(--v-theme-primary));
}

.overlay-card {
  height: 100%;
  width: 100%;
  background: white;
}

.overlay-content-parent {
  z-index: 1000 !important;
}

.overlay-content-parent > .v-overlay__content {
  height: 100%;
  width: 700px;
}

@media only screen and (max-width: 600px) {
  .overlay-content-parent > .v-overlay__content {
    width: 100%;
  }
}

.overlay-body {
  padding: 15px;
  height: calc(100vh - 130px);
  overflow-y: scroll !important;
  overflow: hidden;
}

#job-options-card {
  min-height: 30%;
}

.overlay-footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
