<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTab"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="showTopBarFilter">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              ></EmployeeDefaultFilterMenu>
              <FinalizeAndClosureFilter
                :dropdownList="dropdownList"
                @apply-filter="onApplyFilter($event)"
                @reset-filter="onResetFilter()"
              ></FinalizeAndClosureFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="finalize-closure">
      <v-window v-model="currentTab" v-if="formAccess && formAccess.view">
        <v-window-item :value="currentTab">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="
              itemList.length === 0 && !openEmployeeFilter && !openApprovalsForm
            "
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="The time-off closure or finalization process is a crucial step for the HR Admin before initiating the payroll run. It involves ensuring that all leave applications, attendance records, short time-off entries, and compensatory off requests are properly reviewed, approved, and locked for the payroll period. The HR Admin must validate that there are no pending approvals and that all regularizations or corrections are completed."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Once the data is accurate and consistent, the attendance and leave modules are finalized, preventing any further modifications. This ensures that payroll calculations are based on confirmed time-off data, leading to precise earnings, deductions, and leave balances for the month."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onInitiateFinalizeClosure()"
                    >
                      <span>Initiate</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      class="ml-2 mt-1 primary"
                      variant="text"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <FinalizeAndClosureGeneration
            v-else-if="openEmployeeFilter"
            :dropdownList="dropdownList"
            @close-form="openEmployeeFilter = false"
          ></FinalizeAndClosureGeneration>

          <div v-else>
            <div>
              <div
                v-if="originalList.length > 0 && !isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  v-if="formAccess.add"
                  variant="elevated"
                  rounded="lg"
                  class="mx-1 primary"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="onInitiateFinalizeClosure()"
                >
                  <span>Initiate</span>
                </v-btn>
                <v-btn
                  rounded="lg"
                  variant="text"
                  class="ml-2 mt-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-tooltip :text="action.message">
                              <template v-slot:activator="{ props }">
                                <div v-bind="action.message ? props : ''">
                                  {{ action.key }}
                                </div>
                              </template>
                            </v-tooltip>
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    v-model="selectedEmployeeRecords"
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    item-value="employeeId"
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <!-- <template
                      v-slot:[`header.data-table-select`]="{ selectAll }"
                    >
                      <v-checkbox-btn
                        v-model="selectAllBox"
                        color="primary"
                        false-icon="far fa-circle"
                        true-icon="fas fa-check-circle"
                        indeterminate-icon="fas fa-minus-circle"
                        class="mt-1"
                        @change="selectAll(selectAllBox)"
                      ></v-checkbox-btn>
                    </template> -->
                    <template v-slot:item="{ item }">
                      <tr
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <!-- <td :class="isMobileView ? 'mt-5 mb-n5' : ''">
                          <v-checkbox-btn
                            v-model="item.isSelected"
                            color="primary"
                            false-icon="far fa-circle"
                            true-icon="fas fa-check-circle"
                            class="mt-n2 ml-n2"
                            @click.stop="
                              {
                              }
                            "
                            @change="checkAllSelected()"
                          ></v-checkbox-btn>
                        </td> -->
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Employee Name
                          </div>
                          <section class="d-flex align-center">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              <div>
                                {{ checkNullValue(item.Employee_Name) }}
                              </div>
                              <div class="text-grey">
                                {{ checkNullValue(item.User_Defined_EmpId) }}
                              </div>
                            </div>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Closure Month
                          </div>
                          <section class="d-flex align-center">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              {{
                                checkNullValue(
                                  item.Timeoff_Closure_Month_Display
                                )
                              }}
                            </div>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Paid Leave Days
                          </div>
                          <section class="d-flex align-center">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              {{
                                item.Paid_Leave_Days ? item.Paid_Leave_Days : 0
                              }}
                            </div>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Unpaid Leave Days
                          </div>
                          <section class="d-flex align-center">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              {{
                                item.Unpaid_Leave_Days
                                  ? item.Unpaid_Leave_Days
                                  : 0
                              }}
                            </div>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Paid Days
                          </div>
                          <section class="d-flex align-center">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              {{ item.Paid_Days ? item.Paid_Days : 0 }}
                            </div>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between'
                              : 'd-flex justify-end'
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <ActionMenu
                            v-if="formAccess && formAccess.delete"
                            @selected-action="onMoreAction($event, item)"
                            :actions="['Delete']"
                            :access-rights="havingAccess"
                            :is-present-tooltip="false"
                            iconColor="grey"
                          ></ActionMenu>
                          <div v-else>-</div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import moment from "moment";
import {
  RETRIEVE_FINALIZED_EMPLOYEES,
  DELETE_TIMEOFF_CLOSURE,
} from "@/graphql/corehr/timeOffClosure";
import { checkNullValue } from "@/helper";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const FinalizeAndClosureFilter = defineAsyncComponent(() =>
  import("./FinalizeAndClosureFilter")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const FinalizeAndClosureGeneration = defineAsyncComponent(() =>
  import("./FinalizeAndClosureGeneration.vue")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "TimeOffClosure",
  components: {
    EmployeeDefaultFilterMenu,
    FinalizeAndClosureFilter,
    NotesCard,
    FinalizeAndClosureGeneration,
    ActionMenu,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      errorContent: "error in fetching list",
      selectedItem: null,
      //flags
      isErrorInList: false,
      showRetryBtn: true,
      showAddEditForm: false,
      showViewForm: false,
      openFormInModal: false,
      isEdit: false,
      openMoreMenu: false,
      selectAllBox: false,
      openEmployeeFilter: false,
      //loaders
      listLoading: false,
      //lists
      originalList: [],
      itemList: [],
      selectedEmployeeRecords: [],
      dropdownList: {},
      isLoading: false,
    };
  },
  computed: {
    havingAccess() {
      let access = {};
      access["delete"] = this.formAccess.delete;
      return access;
    },
    landedFormName() {
      let form = this.accessRights(359);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Time Off Closure";
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText =
          "There are no time off closures for the selected filters/searches.";
      }
      return msgText;
    },
    coreHrTimeOffTabs() {
      return this.$store.getters.coreHrTimeOffTabs;
    },
    currentTab() {
      return "tab-" + this.mainTabs.indexOf(this.landedFormName);
    },
    mainTabs() {
      if (this.coreHrTimeOffTabs.length) {
        let tabs = [];
        for (let tab of this.coreHrTimeOffTabs) {
          if (tab.havingAccess || tab.displayName === this.landedFormName)
            tabs.push(tab.displayName);
        }
        return tabs;
      }
      return [];
    },
    formAccess() {
      let formAccess = this.accessRights(359);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    moreActions() {
      return [{ key: "Export" }];
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [{}];
      } else {
        return [
          { title: "Employee Name", key: "Employee_Name", align: "start" },
          { title: "Closure Month", key: "Timeoff_Closure_Month_Display" },
          { title: "Paid Leave Days", key: "Paid_Leave_Days" },
          { title: "Unpaid Leave Days", key: "Unpaid_Leave_Days" },
          { title: "Paid Days", key: "Paid_Days" },
          { title: "Actions", key: "", sortable: false, align: "end" },
        ];
      }
    },
    showTopBarFilter() {
      if (this.openEmployeeFilter || this.originalList.length <= 0) {
        return false;
      }
      return true;
    },
    formatTimeOffClosureMonth() {
      return (date) => {
        if (date && moment(date, "MM,YYYY").isValid())
          return moment(date, "MM,YYYY").format("MMM YYYY");
        return "";
      };
    },
  },
  watch: {
    selectedEmployeeRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.employeeId)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
      }
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.retrieveFinalizedEmployees();
    this.fetchDropdownData();
  },
  methods: {
    checkNullValue,
    onTabChange(tabName) {
      if (tabName !== this.landedFormName) {
        let clickedTab = this.coreHrTimeOffTabs.find(
          (tab) => tab.displayName === tabName
        );
        this.isLoading = true;
        if (clickedTab && clickedTab.url) {
          this.$router.push(clickedTab.url);
        }
      }
    },
    fetchDropdownData() {
      this.isLoading = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 359 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              locations,
              employeeType,
              workSchedules,
              fieldForce,
              serviceProvider,
            } = res.data.getDropDownBoxDetails;
            this.dropdownList.departmentList = departments;
            this.dropdownList.locationList = locations;
            this.dropdownList.empTypeList = employeeType;
            this.dropdownList.workScheduleList = workSchedules;
            this.dropdownList.serviceProviderList = serviceProvider;
            this.dropdownList.fieldForce = fieldForce;
            this.dropdownList.businessUnit = [];
            this.dropdownList.designationList = [];
            this.loadingData = false;
          } else {
            this.handleDropdownDataError();
          }
          this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
          this.handleDropdownDataError();
        });
    },
    handleDropdownDataError() {
      this.dropdownList.departmentList = [];
      this.dropdownList.locationList = [];
      this.dropdownList.empTypeList = [];
      this.dropdownList.workScheduleList = [];
      this.dropdownList.serviceProviderList = [];
      this.dropdownList.fieldForce = 0;
      this.dropdownList.designationList = [];
      this.dropdownList.businessUnit = [];
    },
    retrieveFinalizedEmployees() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_FINALIZED_EMPLOYEES,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            formId: 359,
            year: parseInt(moment().format("YYYY")),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listTimeoffClosure &&
            response.data.listTimeoffClosure.timeoffClosureDetails
          ) {
            let { timeoffClosureDetails } = response.data.listTimeoffClosure;
            timeoffClosureDetails = timeoffClosureDetails.map((item) => {
              return {
                ...item,
                Timeoff_Closure_Month_Display: this.formatTimeOffClosureMonth(
                  item.Timeoff_Closure_Month
                ),
              };
            });
            vm.originalList = timeoffClosureDetails;
            vm.itemList = vm.originalList;
          } else {
            vm.originalList = [];
            vm.itemList = [];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.originalList = [];
          vm.itemList = [];
          vm.$store
            .dispatch("handleApiError", {
              error: err,
              action: "retrieving",
              form: "time off closure",
              isListError: true,
            })
            .then((errorMessages) => {
              this.errorContent = errorMessages;
              this.isErrorInList = true;
            });
        });
    },
    onInitiateFinalizeClosure() {
      this.openEmployeeFilter = true;
    },
    onApplyFilter(filterObj) {
      let filteredList = this.originalList;
      if (
        filterObj.finalizeClosureMonth &&
        filterObj.finalizeClosureMonth.length > 0
      ) {
        filteredList = filteredList.filter((item) => {
          return filterObj.finalizeClosureMonth.includes(
            item.Timeoff_Closure_Month_Display
          );
        });
      }
      if (filterObj.employeeType && filterObj.employeeType.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.employeeType.includes(item.Employee_Type);
        });
      }
      if (filterObj.location && filterObj.location.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.location.includes(item.Location_Id);
        });
      }
      if (filterObj.department && filterObj.department.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.department.includes(item.Department_Id);
        });
      }
      if (filterObj.serviceProvider && filterObj.serviceProvider.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.serviceProvider.includes(item.Service_Provider_Id);
        });
      }
      this.itemList = filteredList;
    },
    onResetFilter() {
      this.itemList = this.originalList;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.retrieveFinalizedEmployees();
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },
    onMoreAction(actionType, item) {
      if (actionType?.toLowerCase() === "delete") {
        this.deleteItem(item);
      } else if (actionType?.toLowerCase() === "export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    deleteItem(item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_TIMEOFF_CLOSURE,
          client: "apolloClientAD",
          variables: {
            timeoffClosureId: item.Timeoff_Closure_Id,
            formId: 359,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.deleteTimeoffClosure &&
            !response.data.deleteTimeoffClosure.errorCode
          ) {
            vm.showAlert({
              isOpen: true,
              message: "Timeoff Closure deleted successfully",
              type: "success",
            });
            vm.refetchList();
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "deleting",
            form: "time off closure",
            isListError: false,
          });
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: "Employee",
          key: "Employee_Name",
        },
        {
          header: "Closure Month",
          key: "Timeoff_Closure_Month_Display",
        },
        {
          header: "Paid Leave Days",
          key: "Paid_Leave_Days",
        },
        {
          header: "Unpaid Leave Days",
          key: "Unpaid_Leave_Days",
        },
        {
          header: "Paid Days",
          key: "Paid_Days",
        },
        {
          header: "Location",
          key: "Location_Name",
        },
        {
          header: "Department",
          key: "Department_Name",
        },
        {
          header: "Designation",
          key: "Designation_Name",
        },
        {
          header: "Employee Type",
          key: "Employee_Type",
        },
      ];
      if (
        this.labelList[115]?.Field_Visiblity?.toLowerCase() === "Yes" &&
        this.dropdownList.fieldForce &&
        (this.isAdmin || this.isServiceProviderAdmin)
      ) {
        exportHeaders.push({
          header: "Service Provider",
          key: "Service_Provider_Name",
        });
      }
      let exportOptions = {
        fileExportData: this.itemList,
        fileName: "Timeoff Closure",
        sheetName: "Timeoff Closure",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
      let filterObj = {
        businessUnitCode: "",
        businessUnit: "",
        parentCode: "",
        status: null,
      };
      this.applyFilter(filterObj);
    },
  },
};
</script>
<style scoped>
.finalize-closure {
  padding: 5em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .finalize-closure {
    padding: 4em 1em 0em 1em;
  }
}
</style>
