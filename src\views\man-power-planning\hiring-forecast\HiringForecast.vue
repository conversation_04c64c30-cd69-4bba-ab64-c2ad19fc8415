<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
    >
      <template #topBarContent>
        <v-row justify="center" v-if="originalList.length > 0">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isDefaultFilter="false"
              @reset-emp-filter="resetFilter()"
              @apply-emp-filter="applyFilter()"
            >
              <template #new-filter>
                <v-row class="mt-5">
                  <v-col :cols="12" class="py-2">
                    <CustomSelect
                      v-model="selectedPosition"
                      color="primary"
                      :items="originalGroupList"
                      clearable
                      :isAutoComplete="true"
                      label="Position Group"
                      item-title="Pos_full_Name"
                      :itemSelected="selectedPosition"
                      item-value="Originalpos_Id"
                      density="compact"
                      :disabled="!(isAdmin || isRecruiter)"
                      @update:model-value="updateGroup()"
                      single-line
                    >
                    </CustomSelect>
                  </v-col>
                  <v-col :cols="12" class="py-2">
                    <CustomSelect
                      v-if="selectedPosition?.toLowerCase() === 'nogroup'"
                      v-model="selectedDivision"
                      color="primary"
                      :items="divisionList"
                      clearable
                      :isAutoComplete="true"
                      label="Division"
                      item-title="Pos_full_Name"
                      :itemSelected="selectedDivision"
                      :is-loading="divisionListLoading"
                      item-value="Originalpos_Id"
                      density="compact"
                      :disabled="!(isAdmin || isRecruiter)"
                      single-line
                    >
                    </CustomSelect>
                  </v-col>
                </v-row>
              </template>
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container fluid class="man-power-planning-container">
      <div v-if="formAccess">
        <div v-if="listLoading">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          key="error-screen"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          button-text="Retry"
          @button-click="resetFilterList()"
        ></AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="originalList.length == 0 || !hireForecastData?.End_Month"
          key="no-data-screen"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4" style="background: white">
                <v-col cols="12">
                  <NotesCard
                    notes="Effective manpower planning requires a well-defined hiring forecast to ensure that the right talent is available at the right time to meet organizational goals."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="A hiring forecast provides insights into future staffing needs based on business growth, project requirements, and strategic objectives. By regularly updating the hiring forecast, teams can align recruitment efforts with the organization's workforce planning strategy."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="Currently, there are no hiring forecasts available for presentation. Please add your hiring forecast for your group, or update the criteria to view the relevant hiring forecasts."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                </v-col>
                <v-col cols="12">
                  <div
                    class="d-flex flex-row align-center flex-wrap justify-center"
                  >
                    <CustomSelect
                      v-model="selectedPosition"
                      color="primary"
                      :itemSelected="selectedPosition"
                      :items="originalGroupList"
                      clearable
                      :isAutoComplete="true"
                      label="Position Group"
                      item-title="Pos_full_Name"
                      item-value="Originalpos_Id"
                      density="compact"
                      :disabled="!(isAdmin || isRecruiter)"
                      class="mt-5 mr-3"
                      style="
                        max-width: 220px !important;
                        width: 150px !important;
                      "
                      @update:model-value="updatePosition()"
                    />
                    <CustomSelect
                      v-model="selectedDivision"
                      color="primary"
                      :items="divisionList"
                      clearable
                      :isAutoComplete="true"
                      label="Division"
                      item-title="Pos_full_Name"
                      :itemSelected="selectedDivision"
                      :is-loading="divisionListLoading"
                      item-value="Originalpos_Id"
                      density="compact"
                      style="
                        max-width: 220px !important;
                        width: 150px !important;
                      "
                      class="mt-5 mr-3"
                      :disabled="
                        !((isAdmin || isRecruiter) && selectedPosition)
                      "
                      @update:model-value="updateDivision()"
                    />

                    <CustomSelect
                      v-model="selectedDepartment"
                      color="primary"
                      :items="departmentList"
                      :loading="divisionListLoading"
                      class="mt-5 mr-3"
                      clearable
                      :isAutoComplete="true"
                      label="Department"
                      placeholder="Department"
                      item-title="Pos_full_Name"
                      :itemSelected="selectedDepartment"
                      :disabled="!selectedDivision"
                      item-value="Originalpos_Id"
                      density="compact"
                      style="
                        max-width: 220px !important;
                        width: 150px !important;
                      "
                      @update:model-value="updateDepartment()"
                    />
                    <CustomSelect
                      v-model="selectedSection"
                      color="primary"
                      :items="sectionList"
                      :loading="divisionListLoading"
                      class="mt-5 mr-3"
                      clearable
                      :isAutoComplete="true"
                      label="Section"
                      placeholder="Section"
                      item-title="Pos_full_Name"
                      :itemSelected="selectedSection"
                      :disabled="!selectedDepartment"
                      item-value="Originalpos_Id"
                      density="compact"
                      style="
                        max-width: 220px !important;
                        width: 150px !important;
                      "
                      @update:model-value="updateSection()"
                    />
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  class="mb-4"
                  :class="
                    (
                      selectedPosition === ''
                        ? windowWidth >= 1720
                        : windowWidth >= 1560
                    )
                      ? 'd-flex align-center justify-center'
                      : 'flex-wrap d-flex align-center justify-center'
                  "
                >
                  <div class="pr-3 mt-5 d-flex justify-center align-center">
                    <CustomSelect
                      v-if="
                        forecastYearList &&
                        forecastYearList.length &&
                        !isLoading
                      "
                      v-model="selectedForecastYear"
                      :items="forecastYearList"
                      itemValue="yearId"
                      itemTitle="yearValue"
                      :item-selected="selectedForecastYear"
                      label="Forecast Year"
                      style="
                        max-width: 300px !important;
                        min-width: 300px !important;
                      "
                      density="compact"
                      @update:model-value="retrieveTotalHiringForecast()"
                    ></CustomSelect>
                  </div>
                  <div
                    class="d-flex justify-center align-center"
                    v-if="formAccess && formAccess.add"
                  >
                    <v-tooltip location="bottom" v-if="!expiredDate">
                      <template v-slot:activator="{ props }">
                        <v-chip
                          class="mr-3"
                          v-bind="props"
                          rounded="lg"
                          color="grey"
                          size="large"
                          >Add New Forecast</v-chip
                        >
                      </template>
                      <div style="max-width: 350px !important">
                        Hiring forecast is not allowed for the past months
                      </div>
                    </v-tooltip>
                    <v-tooltip
                      v-else-if="enableEditAction"
                      text="Please select at least one filter to proceed."
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-card
                          v-bind="disableAddButton ? props : ''"
                          variant="text"
                        >
                          <v-btn
                            @click="addForecast()"
                            class="px-6 mr-2 primary"
                            variant="elevated"
                            :size="isMobileView ? 'small' : 'default'"
                            :disabled="disableAddButton"
                          >
                            <v-icon size="15" class="pr-1 primary"
                              >fas fa-plus</v-icon
                            >
                            <span class="primary">Add New Forecast</span></v-btn
                          >
                        </v-card>
                      </template>
                    </v-tooltip>
                    <v-tooltip location="bottom" v-else>
                      <template v-slot:activator="{ props }">
                        <v-chip
                          class="mr-3"
                          v-bind="props"
                          rounded="lg"
                          color="grey"
                          size="large"
                          >Add New Forecast</v-chip
                        >
                      </template>
                      <div style="max-width: 350px !important">
                        Hiring Forecast release date is expired.
                      </div>
                    </v-tooltip>
                  </div>
                  <v-tooltip location="bottom" v-else>
                    <template v-slot:activator="{ props }">
                      <v-chip
                        class="mr-3"
                        v-bind="props"
                        rounded="lg"
                        color="grey"
                        size="large"
                      >
                        Add New Forecast</v-chip
                      >
                    </template>
                    <div style="max-width: 350px !important">
                      Your don't have access to perform this action
                    </div>
                  </v-tooltip>
                  <v-btn
                    color="transparent"
                    variant="flat"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="retrieveTotalHiringForecast()"
                  >
                    <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="forecastList.length == 0"
          key="no-results-screen"
          main-title="There are no group matched for the selected filters/searches."
          image-name="common/no-records"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4">
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    color="primary"
                    variant="elevated"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    @click="resetFilterList()"
                  >
                    Reset Filter/Search
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div v-else>
          <div
            v-if="forecastYearList.length > 0"
            class="d-flex flex-wrap align-center my-3"
            :class="isMobileView ? 'flex-column' : ''"
            style="justify-content: space-between"
          >
            <div
              class="d-flex align-center flex-wrap"
              :class="isMobileView ? 'justify-center' : ''"
            >
              <CustomSelect
                v-model="selectedPosition"
                color="primary"
                :items="originalGroupList"
                :loading="divisionListLoading"
                class="mt-5 mr-3"
                clearable
                :isAutoComplete="true"
                label="Position Group"
                placeholder="Position Group"
                item-title="Pos_full_Name"
                :itemSelected="selectedPosition"
                item-value="Originalpos_Id"
                density="compact"
                min-width="150px"
                max-width="200px"
                :disabled="disableGroup"
                @update:model-value="updatePosition()"
              />
              <CustomSelect
                v-model="selectedDivision"
                color="primary"
                :items="divisionList"
                :loading="divisionListLoading"
                class="mt-5 mr-3"
                clearable
                :isAutoComplete="true"
                label="Division"
                placeholder="Division"
                item-title="Pos_full_Name"
                :itemSelected="selectedDivision"
                item-value="Originalpos_Id"
                density="compact"
                min-width="150px"
                max-width="200px"
                :disabled="disableDivision"
                @update:model-value="updateDivision()"
              />
              <CustomSelect
                v-model="selectedDepartment"
                color="primary"
                :items="departmentList"
                :loading="divisionListLoading"
                class="mt-5 mr-3"
                clearable
                :isAutoComplete="true"
                label="Department"
                placeholder="Department"
                item-title="Pos_full_Name"
                :itemSelected="selectedDepartment"
                item-value="Originalpos_Id"
                density="compact"
                min-width="150px"
                max-width="200px"
                :disabled="disableDepartment"
                @update:model-value="updateDepartment()"
              />
              <CustomSelect
                v-model="selectedSection"
                color="primary"
                :items="sectionList"
                :loading="divisionListLoading"
                class="mt-5 mr-3"
                clearable
                :isAutoComplete="true"
                label="Section"
                placeholder="Section"
                item-title="Pos_full_Name"
                :itemSelected="selectedSection"
                :disabled="disableSection"
                item-value="Originalpos_Id"
                density="compact"
                min-width="150px"
                max-width="200px"
                @update:model-value="updateSection()"
              />
            </div>
            <div class="d-flex justify-center align-center">
              <div class="pr-3 mt-5">
                <CustomSelect
                  label="Forecast Year"
                  min-width="150px"
                  max-width="200px"
                  v-model="selectedForecastYear"
                  :item-selected="selectedForecastYear"
                  :items="forecastYearList"
                  itemValue="yearId"
                  itemTitle="yearValue"
                  density="compact"
                  @update:model-value="retrieveTotalHiringForecast($event)"
                ></CustomSelect>
              </div>
              <div v-if="formAccess && formAccess.add">
                <v-tooltip location="bottom" v-if="!expiredDate">
                  <template v-slot:activator="{ props }">
                    <v-chip
                      class="mr-3"
                      v-bind="props"
                      rounded="lg"
                      color="grey"
                      size="large"
                      >Add New Forecast</v-chip
                    >
                  </template>
                  <div style="max-width: 350px !important">
                    Hiring forecast is not allowed for the past months
                  </div>
                </v-tooltip>
                <v-btn
                  v-else-if="enableEditAction"
                  @click="addForecast()"
                  class="px-6 mr-2 primary"
                  variant="elevated"
                  :size="isMobileView ? 'small' : 'default'"
                >
                  <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
                  <span class="primary">Add New Forecast</span></v-btn
                >
                <v-tooltip location="bottom" v-else>
                  <template v-slot:activator="{ props }">
                    <v-chip
                      class="mr-3"
                      v-bind="props"
                      rounded="lg"
                      color="grey"
                      size="large"
                      >Add New Forecast</v-chip
                    >
                  </template>
                  <div style="max-width: 350px !important">
                    Hiring Forecast release date is expired.
                  </div>
                </v-tooltip>
              </div>
              <v-tooltip location="bottom" v-else>
                <template v-slot:activator="{ props }">
                  <v-chip
                    class="mr-3"
                    v-bind="props"
                    rounded="lg"
                    color="grey"
                    size="large"
                  >
                    Add New Forecast</v-chip
                  >
                </template>
                <div style="max-width: 350px !important">
                  Your don't have access to perform this action
                </div>
              </v-tooltip>
              <v-btn
                color="transparent"
                variant="flat"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="retrieveTotalHiringForecast()"
              >
                <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu class="mb-1" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                    <v-icon>fas fa-ellipsis-v</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action.key"
                    @click="onMoreAction(action.key)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                          ><v-icon size="15" class="pr-2">{{
                            action.icon
                          }}</v-icon
                          >{{ action.key }}</v-list-item-title
                        >
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </div>

          <v-data-table
            :headers="tableHeaders"
            :items="forecastList"
            fixed-header
            :items-per-page="50"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
            ]"
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(
                290,
                forecastList,
                true
              )
            "
            style="box-shadow: none !important"
            class="elevation-1 custom-scroll-table"
          >
            <template v-slot:item="{ item }">
              <tr
                class="data-table-tr bg-white cursor-pointer"
                :class="
                  isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''
                "
                @click="openViewForm(item)"
              >
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <v-tooltip
                    :text="item?.Position_Title"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Position_Title ? props : {}"
                        class="text-body-2 text-truncate text-start text-primary"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Position_Title) }}
                      </div>
                    </template>
                  </v-tooltip>
                  <v-tooltip
                    :text="item?.Pos_Code"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Pos_Code ? props : {}"
                        class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Pos_Code) }}
                      </div>
                    </template></v-tooltip
                  >
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <v-tooltip
                    :text="item?.Group_Name"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Group_Name ? props : {}"
                        class="text-body-2 text-truncate text-start text-primary"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Group_Name) }}
                      </div>
                    </template>
                  </v-tooltip>
                  <v-tooltip
                    :text="item?.Group_Code"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Group_Code ? props : {}"
                        class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Group_Code) }}
                      </div>
                    </template></v-tooltip
                  >
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <v-tooltip
                    :text="item?.Division_Name"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Division_Name ? props : {}"
                        class="text-body-2 text-truncate text-start text-primary"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Division_Name) }}
                      </div>
                    </template>
                  </v-tooltip>
                  <v-tooltip
                    :text="item?.Division_Code"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Division_Code ? props : {}"
                        class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Division_Code) }}
                      </div>
                    </template></v-tooltip
                  >
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <v-tooltip
                    :text="item?.Department_Name"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Department_Name ? props : {}"
                        class="text-body-2 text-truncate text-start text-primary"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Department_Name) }}
                      </div>
                    </template>
                  </v-tooltip>
                  <v-tooltip
                    :text="item?.Department_Code"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Department_Code ? props : {}"
                        class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Department_Code) }}
                      </div>
                    </template></v-tooltip
                  >
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <v-tooltip
                    :text="item?.Section_Name"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Section_Name ? props : {}"
                        class="text-body-2 text-truncate text-start text-primary"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Section_Name) }}
                      </div>
                    </template>
                  </v-tooltip>
                  <v-tooltip
                    :text="item?.Section_Code"
                    location="bottom"
                    max-width="300"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="item?.Section_Code ? props : {}"
                        class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 100px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ checkNullValue(item.Section_Code) }}
                      </div>
                    </template></v-tooltip
                  >
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Approved Positions
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <div
                      class="text-subtitle-1 font-weight-regular text-truncate"
                      :style="
                        !isMobileView
                          ? 'max-width: 80px; '
                          : 'max-width: 200px; '
                      "
                    >
                      {{ checkNullValue(item.Approved_Position) }}
                    </div>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Warm Bodies
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <div
                      class="text-subtitle-1 font-weight-regular text-truncate"
                      :style="
                        !isMobileView
                          ? 'max-width: 80px; '
                          : 'max-width: 200px; '
                      "
                    >
                      {{ checkNullValue(item.Warm_Bodies) }}
                    </div>
                  </section>
                </td>
                <td
                  v-for="(n, i) in Array.from({ length: 12 }, (v, i) => i + 1)"
                  :key="n"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    {{ monthNameConversion(i) }}
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <div
                      class="text-subtitle-1 font-weight-regular text-truncate"
                      :style="
                        !isMobileView
                          ? 'max-width: 100px; '
                          : 'max-width: 200px; '
                      "
                    >
                      {{ item[monthNameConversion(i)] ?? 0 }}
                    </div>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Approved Vacant Positions
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <div
                      class="text-subtitle-1 font-weight-regular text-truncate"
                      :style="
                        !isMobileView
                          ? 'max-width: 100px; '
                          : 'max-width: 200px; '
                      "
                    >
                      {{ checkNullValue(item.To_Be_Hired) }}
                    </div>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Vacancies for TO Review
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <div
                      class="text-subtitle-1 font-weight-regular text-truncate"
                      :style="
                        !isMobileView
                          ? 'max-width: 100px; '
                          : 'max-width: 200px; '
                      "
                    >
                      {{ checkNullValue(item.To_Be_Source) }}
                    </div>
                  </section>
                </td>
                <td
                  class="text-body-2"
                  @click.stop="
                    {
                    }
                  "
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : ''
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Actions
                  </div>
                  <div
                    class="d-flex align-center justify-center"
                    v-if="
                      isAdmin ||
                      loginEmployeeName?.toLowerCase() ===
                        item?.Added_By?.toLowerCase()
                    "
                  >
                    <ActionMenu
                      :actions="['Edit', 'Delete']"
                      iconColor="grey"
                      @selected-action="onActions($event, item)"
                      :access-rights="formAccess"
                      :disableActionButtons="
                        !expiredDate
                          ? ['Edit', 'Delete']
                          : enableEditAction
                          ? []
                          : ['Edit', 'Delete']
                      "
                      :tooltipActionButtons="
                        !expiredDate
                          ? ['Edit', 'Delete']
                          : enableEditAction
                          ? []
                          : ['Edit', 'Delete']
                      "
                      :tooltipMessage="
                        !expiredDate
                          ? ` Hiring forecast is not allowed for the past months`
                          : enableEditAction
                          ? ``
                          : `Please update the hiring forecast end date`
                      "
                    ></ActionMenu>
                  </div>
                  <div v-else class="d-flex justify-center">-</div>
                </td>
              </tr>
            </template>
          </v-data-table>
        </div>
      </div>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <v-overlay
    :model-value="overlay"
    @click:outside="onCloseOverlay()"
    persistent
    class="container d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card"
        :style="windowWidth >= 1264 ? '' : 'width: 100vw'"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">
            {{ selectedForecast ? "Edit" : "Add" }} Hiring Forecast
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text
          class="card mb-3 px-0"
          style="overflow-y: auto"
          v-if="originalGroupList.length"
        >
          <AddEditHiringForecast
            :position-group="originalGroupList"
            :hireForecastData="hireForecastData"
            :selectedForecastYear="selectedForecastYear"
            :selectedForecast="selectedForecast"
            :selectedPositionTitle="selectedPosition"
            :selectedDivisionTitle="selectedDivision"
            @refresh-list="refreshList()"
            @close-overlay="onCloseOverlay()"
          />
        </v-card-text> </v-card></template
  ></v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit Hiring Forecast form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onClosePopup()"
  >
  </AppWarningModal>
  <AppWarningModal
    v-if="openConfirmationDelete"
    :open-modal="openConfirmationDelete"
    confirmation-heading="Are you sure to delete Hiring Forecast?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openConfirmationDelete = false"
    @accept-modal="onDeleteHiringForecast()"
  >
  </AppWarningModal>
  <HiringForecastView
    :showViewDetails="enableViewForm"
    :selectedForecastData="selectedForecast"
    :hireForecastData="hireForecastData"
    :selectedForecastYear="selectedForecastYear"
    :enable-edit-action="enableEditAction && expiredDate"
    @close-view-details="onCloseView()"
    @edit-forecast-details="onEditPosition()"
  />
</template>
<script>
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu.vue";
import NotesCard from "@/components/helper-components/NotesCard.vue";
import {
  DELETE_HIRING_FORECAST,
  LIST_OF_FORECAST,
  LIST_OF_POSITION_LIST,
  RETRIEVE_HIRING_FORECAST_SETTINGS,
} from "@/graphql/mpp/manPowerPlanningQueries";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";

import moment from "moment";
import HiringForecastView from "./HiringForecastView.vue";
import AddEditHiringForecast from "./AddEditHiringForecast.vue";
import { ORG_STRUCTURE_BASED_ON_GROUP } from "@/graphql/mpp/newPositionQueries";

export default {
  name: "HiringForecast",
  data() {
    return {
      originalList: [],
      forecastList: [],
      originalGroupList: [],
      selectedPosition: null,
      isErrorInList: false,
      errorContent: "",
      mainTabList: ["Hiring Forecast"],
      currentTabItem: "tab-0",
      listLoading: true,
      isLoading: false,
      openMoreMenu: false,
      forecastYearList: [],
      selectedForecastYear: moment().year(),
      selectedForecast: null,
      overlay: false,
      hireForecastData: {},
      openConfirmationPopup: false,
      openConfirmationDelete: false,
      hiringForeCastLimitToCallAPI: 10000,
      totalApiCount: 0,
      apiCallCount: 0,
      hiringTotalApiCount: 0,
      hiringApiCallCount: 0,
      enableViewForm: false,
      selectedDivision: null,
      divisionList: [],
      divisionListLoading: false,
      selectedSection: null,
      sectionList: [],
      sectionListLoading: false,
      selectedDepartment: null,
      departmentList: [],
      allExport: false,
      orgLevel: "",
      parentGroup: "",
    };
  },
  mixins: [FileExportMixin],
  mounted() {
    if (this.formAccess && this.formAccess.view) {
      this.retrieveForecastSettings();
      this.retrievePositionList();
    }
  },
  computed: {
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isRecruiter() {
      return this.$store.state.isRecruiter;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      if (this.isAdmin || this.isRecruiter) {
        actions.push({
          key: "Export All",
          icon: "fas fa-file-export",
        });
      }

      return actions;
    },
    disableAddButton() {
      const isInvalid = (val, match) =>
        !val || val.toString().toLowerCase() === match;

      return (
        isInvalid(this.selectedPosition, "nogroup") &&
        isInvalid(this.selectedDivision, "nodivision") &&
        isInvalid(this.selectedDepartment, "nodepartment") &&
        isInvalid(this.selectedSection, "nosection")
      );
    },
    disableSection() {
      if (!this.selectedDepartment) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (this.orgLevel?.toLowerCase() == "sec") {
        return true;
      }
      return false;
    },
    disableDepartment() {
      if (!this.selectedDivision) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
    disableDivision() {
      if (!this.selectedPosition) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "div" ||
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
    disableGroup() {
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "grp" ||
        this.orgLevel?.toLowerCase() == "div" ||
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    formAccess() {
      let formAccess = this.accessRights("287");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    enableEditAction() {
      const currentDate = moment().format("YYYY-MM-DD");
      if (this.hireForecastData && Object.keys(this.hireForecastData).length) {
        const endDate = moment(
          new Date(this.hireForecastData?.Release_Date)
        ).format("YYYY-MM-DD");
        return currentDate <= endDate;
      } else {
        return false;
      }
    },
    filteredYearMonth() {
      return this.hireForecastData.End_Month === 12
        ? `${this.selectedForecastYear}-01-01`
        : `${this.selectedForecastYear}-${
            this.hireForecastData.End_Month + 1
          }-01`;
    },
    expiredDate() {
      const currentMonth = moment().month() + 1;
      const currentYear = moment().year();
      const date = this.hireForecastData.End_Month;
      const monthNumber = parseInt(date);
      const yearValue = parseInt(
        this.hireForecastData.End_Month === 12
          ? this.selectedForecastYear
          : this.selectedForecastYear + 1
      );
      return (
        yearValue > currentYear ||
        (yearValue === currentYear && monthNumber >= currentMonth)
      );
    },
    tableHeaders() {
      let headers = [];
      headers.push(
        {
          title: "Position",
          align: "start",
          key: "Pos_Code",
        },
        {
          title: "Group",
          align: "start",
          key: "Group_Code",
        },
        {
          title: "Division",
          align: "start",
          key: "Division_Code",
        },
        {
          title: "Department",
          align: "start",
          key: "Department_Code",
        },
        {
          title: "Section",
          align: "start",
          key: "Section_Code",
        },
        {
          title: "Approved Positions",
          align: "start",
          key: "Approved_Position",
        },
        {
          title: "Warm Bodies",
          align: "start",
          key: "Warm_Bodies",
        }
      );
      if (this.hireForecastData?.Release_Date) {
        for (let i = 0; i < 12; i++) {
          const monthValue = moment()
            .month(this.hireForecastData?.End_Month + i)
            .format("MMM");
          headers.push({
            title: monthValue,
            key: monthValue,
          });
        }
      }
      headers.push(
        {
          title: "Approved Vacant Positions",
          align: "start",
          key: "To_Be_Hired",
        },
        {
          title: "Vacancies for TO Review",
          align: "start",
          key: "To_Be_Source",
        },
        {
          title: "Actions",
          align: "start",
          sortable: false,
          key: "",
        }
      );
      return headers;
    },
  },
  components: {
    EmployeeDefaultFilterMenu,
    CustomSelect,
    NotesCard,
    AddEditHiringForecast,
    HiringForecastView,
    ActionMenu,
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  methods: {
    checkNullValue,
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      } else if (actionType.toLowerCase() === "export all") {
        this.allExport = true;
        this.retrieveTotalHiringForecast();
      }
      this.openMoreMenu = false;
    },
    monthNameConversion(month) {
      return moment()
        .month(this.hireForecastData?.End_Month + month)
        .format("MMM");
    },
    onClosePopup() {
      this.openConfirmationPopup = false;
      this.overlay = false;
    },
    addForecast() {
      this.selectedForecast = null;
      this.overlay = true;
    },
    onCloseOverlay() {
      this.openConfirmationPopup = true;
    },
    applyFilter() {
      this.retrieveTotalHiringForecast();
    },
    resetFilter() {
      this.selectedPosition = null;
      this.selectedDivision = null;
      this.retrieveTotalHiringForecast();
    },
    resetFilterList() {
      this.selectedForecastYear = moment().year();
      this.selectedPosition = null;
      this.selectedDivision = null;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.retrieveTotalHiringForecast();
    },
    refreshList() {
      this.retrieveTotalHiringForecast();
      this.overlay = false;
    },
    onApplySearch(val) {
      if (!val) {
        this.forecastList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.forecastList = searchItems;
      }
    },
    updateGroup() {
      if (this.selectedPosition) {
        this.selectedDivision = null;
      }
    },
    updateDivision() {
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedDivision) {
        this.retrieveCountGroupPosition("division");
        this.retrieveTotalHiringForecast();
      }
    },
    updateDepartment() {
      this.selectedSection = null;
      if (this.selectedDepartment) {
        this.retrieveCountGroupPosition("department");
        this.retrieveTotalHiringForecast();
      }
    },
    updateSection() {
      if (this.selectedSection) {
        this.retrieveTotalHiringForecast();
      }
    },
    retrieveCountGroupPosition(type = "") {
      this.divisionListLoading = true;
      let groupId = this.selectPositionId() || "0";
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(groupId),
            limit: this.hiringForeCastLimitToCallAPI,
            offset: 0,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listDetailsBasedOnGroupCode) {
            if (res.data.listDetailsBasedOnGroupCode.positionDetails) {
              const tempData =
                res.data.listDetailsBasedOnGroupCode.positionDetails;
              if (type === "mounted") {
                this.divisionList = tempData.divisionList || [];
                this.departmentList = tempData.deptList || [];
                this.sectionList = tempData.sectionList || [];
              }
              if (type === "group")
                this.divisionList = tempData.divisionList || [];
              if (type === "division")
                this.departmentList = tempData.deptList || [];
              if (type === "department")
                this.sectionList = tempData.sectionList || [];
              if (
                !this.divisionList.some(
                  (item) => item.Originalpos_Id === "nodivision"
                )
              )
                this.divisionList.unshift({
                  Pos_Name: "No Division",
                  Pos_full_Name: "No Division",
                  Originalpos_Id: "nodivision",
                });

              if (
                !this.departmentList.some(
                  (item) => item.Originalpos_Id === "nodepartment"
                )
              )
                this.departmentList.unshift({
                  Pos_Name: "No Department",
                  Pos_full_Name: "No Department",
                  Originalpos_Id: "nodepartment",
                });
              if (
                !this.sectionList.some(
                  (item) => item.Originalpos_Id === "nosection"
                )
              )
                this.sectionList.unshift({
                  Pos_Name: "No Section",
                  Pos_full_Name: "No Section",
                  Originalpos_Id: "nosection",
                });
            } else {
              this.resetTempList();
            }
            let { totalRecords } = res.data.listDetailsBasedOnGroupCode;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.hiringApiCallCount = 1;
              this.hiringTotalApiCount = Math.ceil(
                totalRecords / this.hiringForeCastLimitToCallAPI
              );
              for (let i = 1; i < this.hiringTotalApiCount; i++) {
                this.updateGroupPosition(i, String(groupId), type);
              }
              this.divisionListLoading = false;
            } else {
              this.divisionListLoading = false;
            }
          } else {
            this.divisionListLoading = false;
          }
        })
        .catch((err) => {
          this.divisionList = [];
          this.handleRetrieveError(err);
          this.divisionListLoading = false;
          this.resetTempList();
        });
    },
    updateGroupPosition(index = 1, groupId = "", type = "") {
      this.divisionListLoading = true;
      let apiOffset = parseInt(index) * this.hiringForeCastLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.isFormDirty = true;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(groupId),
            limit: this.hiringForeCastLimitToCallAPI,
            offset: apiOffset,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listDetailsBasedOnGroupCode &&
            res.data.listDetailsBasedOnGroupCode.positionDetails
          ) {
            const tempData =
              res.data.listDetailsBasedOnGroupCode.positionDetails;
            if (
              tempData.divisionList &&
              tempData.divisionList.length > 0 &&
              (type === "group" || type === "mounted")
            ) {
              this.divisionList = [
                ...this.divisionList,
                ...tempData.divisionList,
              ];
            }
            if (
              tempData.deptList &&
              tempData.deptList.length > 0 &&
              (type === "division" || type === "mounted")
            ) {
              this.departmentList = [
                ...this.departmentList,
                ...tempData.deptList,
              ];
            }
            if (
              tempData.sectionList &&
              tempData.sectionList.length > 0 &&
              (type === "department" || type === "mounted")
            ) {
              this.sectionList = [...this.sectionList, ...tempData.sectionList];
            }
            this.hiringApiCallCount = this.hiringApiCallCount + 1;
            if (this.hiringTotalApiCount === this.hiringApiCallCount) {
              this.divisionListLoading = false;
            }
          } else {
            this.resetTempList();
            this.divisionListLoading = false;
          }
        })
        .catch((err) => {
          this.divisionList = [];
          this.handleRetrieveError(err);
          this.resetTempList();
          this.divisionListLoading = false;
        });
    },
    resetTempList() {
      this.divisionList = [];
      this.departmentList = [];
      this.sectionList = [];
    },
    onDeleteHiringForecast() {
      this.openConfirmationDelete = false;
      this.isLoading = true;
      this.$apollo
        .mutate({
          mutation: DELETE_HIRING_FORECAST,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: {
            originalPositionId:
              this.selectedForecast?.Originalpos_Id?.toString(),
            forecastingYear: this.selectedForecastYear?.toString(),
          },
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.deleteHiringForeCast &&
            res.data.deleteHiringForeCast.message
          ) {
            let snackbarData = {
              isOpen: true,
              message: res.data.deleteHiringForeCast.message,
              type: "success",
            };
            this.showAlert(snackbarData);
            this.retrieveTotalHiringForecast();
          } else {
            this.handleRetrieveError();
          }
        })
        .catch((err) => {
          this.handleRetrieveError(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getForecastYears() {
      const years = [-1, 0, 1]?.map((offset) =>
        moment().add(offset, "years").year()
      );
      this.forecastYearList = years?.map((year, index) => ({
        yearId: year,
        yearValue:
          this.hireForecastData.End_Month === 12
            ? year
            : `${year} - ${
                years[index + 1] || moment().add(2, "years").year()
              }`,
      }));
      this.retrieveTotalHiringForecast("mounted");
    },
    onActions(key, item) {
      if (this.enableEditAction) {
        if (this.expiredDate) {
          if (key === "Edit") {
            this.selectedForecast = item;
            this.overlay = true;
          } else if (key === "Delete") {
            this.selectedForecast = item;
            this.openConfirmationDelete = true;
          }
        }
      }
    },
    openViewForm(item) {
      this.selectedForecast = item;
      this.enableViewForm = true;
    },
    onCloseView() {
      this.enableViewForm = false;
    },
    onEditPosition() {
      this.overlay = true;
      this.onCloseView();
    },
    retrieveForecastSettings() {
      this.isLoading = true;
      this.$apollo
        .query({
          query: RETRIEVE_HIRING_FORECAST_SETTINGS,
          variables: {
            formId: 287,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.retrieveForeCastSettings &&
            res.data.retrieveForeCastSettings.settings
          ) {
            this.hireForecastData = res.data.retrieveForeCastSettings.settings;
            this.getForecastYears();
          } else {
            this.hireForecastData = {};
            this.listLoading = false;
          }
        })
        .catch((err) => {
          this.hireForecastData = {};
          this.handleRetrieveError(err);
          this.listLoading = false;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    updatePosition() {
      this.selectedDivision = null;
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedPosition) {
        this.retrieveCountGroupPosition("group");
        this.retrieveTotalHiringForecast();
      }
    },
    retrievePositionList() {
      this.isLoading = true;
      this.$apollo
        .query({
          query: LIST_OF_POSITION_LIST,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            Form_Id: 287,
            conditions: [
              {
                key: "Org_Level",
                operator: "=",
                value: "GRP",
              },
            ],
          },
        })
        .then((res) => {
          this.isLoading = false;
          if (
            res &&
            res.data &&
            res.data.jobTitleList &&
            res.data.jobTitleList.jobTitleResult
          ) {
            res.data.jobTitleList.jobTitleResult.forEach((element) => {
              element.Originalpos_Id = element.Originalpos_Id?.toString();
            });
            this.originalGroupList = [
              {
                Pos_Name: "No Group",
                Pos_full_Name: "No Group",
                Originalpos_Id: "nogroup",
              },
            ].concat(res.data.jobTitleList.jobTitleResult);
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.originalGroupList = [];
          this.handleRetrieveError(err);
        });
    },
    handleRetrieveError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "Man Power Planning",
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    selectPositionId() {
      let code = "";
      if (
        this.selectedSection &&
        this.selectedSection.toString().toLowerCase() != "nosection"
      )
        code = this.selectedSection.toString();
      else if (
        this.selectedDepartment &&
        this.selectedDepartment.toString().toLowerCase() != "nodepartment"
      )
        code = this.selectedDepartment.toString();
      else if (
        this.selectedDivision &&
        this.selectedDivision.toString().toLowerCase() != "nodivision"
      )
        code = this.selectedDivision.toString();
      else if (!this.selectedPosition) {
        code = "";
      } else if (
        this.selectedPosition &&
        this.selectedPosition.toString().toLowerCase() != "nogroup"
      ) {
        code = this.selectedPosition.toString();
      }
      return code;
    },
    retrieveTotalHiringForecast(type = "") {
      if (!this.allExport) {
        this.originalList = [];
        this.forecastList = [];
        this.listLoading = true;
      } else {
        this.isLoading = true;
      }
      let positionDetails = this.selectPositionId();
      let apiVariables = {
        forecastingYear: this.selectedForecastYear,
        postionParentId: positionDetails,
        limit: this.hiringForeCastLimitToCallAPI,
        offset: 0,
        alexport: this.allExport,
      };
      if (this.selectedPosition)
        apiVariables.groupFilter = {
          Org_Level: "GRP",
          id: String(
            this.selectedPosition == "nogroup" ? "0" : this.selectedPosition
          ),
        };
      if (this.selectedDivision)
        apiVariables.divisionFilter = {
          Org_Level: "DIV",
          id: String(
            this.selectedDivision == "nodivision" ? "0" : this.selectedDivision
          ),
        };
      if (this.selectedDepartment)
        apiVariables.departmentFilter = {
          Org_Level: "DEPT",
          id: String(
            this.selectedDepartment == "nodepartment"
              ? "0"
              : this.selectedDepartment
          ),
        };
      if (this.selectedSection)
        apiVariables.sectionFilter = {
          Org_Level: "SEC",
          id: String(
            this.selectedSection == "nosection" ? "0" : this.selectedSection
          ),
        };

      this.$apollo
        .query({
          query: LIST_OF_FORECAST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: apiVariables,
        })
        .then((res) => {
          this.listLoading = false;
          if (res && res.data && res.data.listHiringForecast) {
            if (!this.allExport) {
              let { totalCountResult, orgLevel, groupId } =
                res.data.listHiringForecast;
              if (type === "mounted") {
                this.parentGroup = groupId;
                this.orgLevel = String(orgLevel || "").trim();
                this.selectDefaltPosition();
              }
              if (
                res.data.listHiringForecast.result &&
                res.data.listHiringForecast.result.length
              ) {
                const hiringLists = JSON.parse(
                  res.data.listHiringForecast.result
                );
                if (hiringLists && hiringLists.length) {
                  this.prepareMonthData(hiringLists);
                } else {
                  this.originalList = [];
                  this.forecastList = [];
                }
              }
              if (totalCountResult > 0) {
                totalCountResult = parseInt(totalCountResult);
                this.apiCallCount = 1;
                this.totalApiCount = Math.ceil(
                  totalCountResult / this.hiringForeCastLimitToCallAPI
                );
                for (let i = 1; i < this.totalApiCount; i++) {
                  this.retrieveHiringForecast(i, positionDetails);
                }
              }
            } else {
              let sheetUrl = res.data.listHiringForecast.s3Url;
              if (sheetUrl) {
                window.open(sheetUrl, "_blank");
              }
            }
          }
          this.allExport = false;
          this.isLoading = false;
        })
        .catch((err) => {
          this.allExport = false;
          this.isLoading = false;
          this.listLoading = false;
          this.handleRetrieveError(err);
        });
    },
    selectDefaltPosition() {
      if (this.orgLevel) {
        if (this.orgLevel === "GRP") {
          if (this.parentGroup) {
            this.selectedPosition = this.parentGroup;
          } else {
            this.selectedPosition = "nogroup";
          }
        } else if (this.orgLevel === "DIV") {
          this.selectedPosition = "nogroup";
          if (this.parentGroup) {
            this.selectedDivision = this.parentGroup;
          } else {
            this.selectedDivision = "nodivision";
          }
        } else if (this.orgLevel === "DEPT") {
          this.selectedPosition = "nogroup";
          this.selectedDivision = "nodivision";
          if (this.parentGroup) {
            this.selectedDepartment = this.parentGroup;
          } else {
            this.selectedDepartment = "nodepartment";
          }
        } else if (this.orgLevel === "SEC") {
          this.selectedPosition = "nogroup";
          this.selectedDivision = "nodivision";
          this.selectedDepartment = "nodepartment";
          if (this.parentGroup) {
            this.selectedSection = this.parentGroup;
          } else {
            this.selectedSection = "nosection";
          }
        }
        this.retrieveCountGroupPosition("mounted");
      } else {
        this.resetTempList();
        this.selectedPosition = "nogroup";
        this.setListWithNoFilters(true, true, true);
      }
    },
    prepareMonthData(hiringLists) {
      let tempHiringList = [];
      hiringLists?.forEach((list) => {
        if (list && list.foreCastList) {
          let tempLists = {};
          const forecastListData = JSON.parse(list.foreCastList);
          for (let i = 0; i < 12; i++) {
            forecastListData?.forEach((yearData) => {
              if (yearData.Forecast_Month === i + 1) {
                tempLists = {
                  ...tempLists,
                  [moment().month(i).format("MMM")]: yearData.No_Of_Position,
                };
              }
            });
          }
          tempHiringList.push({ ...tempLists, ...list });
        }
      });
      this.originalList = [...this.originalList, ...tempHiringList];
      this.forecastList = [...this.forecastList, ...tempHiringList];
    },
    retrieveHiringForecast(index = 1, positionDetails = "") {
      let apiOffset = parseInt(index) * this.hiringForeCastLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.listLoading = true;
      let apiVariables = {
        forecastingYear: this.selectedForecastYear,
        postionParentId: positionDetails,
        limit: this.hiringForeCastLimitToCallAPI,
        offset: apiOffset,
      };
      if (this.selectedPosition)
        apiVariables.groupFilter = {
          Org_Level: "GRP",
          id: String(
            this.selectedPosition == "nogroup" ? "0" : this.selectedPosition
          ),
        };
      if (this.selectedDivision)
        apiVariables.divisionFilter = {
          Org_Level: "DIV",
          id: String(
            this.selectedDivision == "nodivision" ? "0" : this.selectedDivision
          ),
        };
      if (this.selectedDepartment)
        apiVariables.departmentFilter = {
          Org_Level: "DEPT",
          id: String(
            this.selectedDepartment == "nodepartment"
              ? "0"
              : this.selectedDepartment
          ),
        };
      if (this.selectedSection)
        apiVariables.sectionFilter = {
          Org_Level: "SEC",
          id: String(
            this.selectedSection == "nosection" ? "0" : this.selectedSection
          ),
        };

      this.$apollo
        .query({
          query: LIST_OF_FORECAST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: apiVariables,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listHiringForecast &&
            res.data.listHiringForecast.result &&
            res.data.listHiringForecast.result.length
          ) {
            const hiringLists = JSON.parse(res.data.listHiringForecast.result);
            if (hiringLists && hiringLists.length) {
              this.prepareMonthData(hiringLists);
              this.apiCallCount = this.apiCallCount + 1;
              if (this.totalApiCount === this.apiCallCount) {
                this.listLoading = false;
              }
            } else {
              this.listLoading = false;
            }
          } else {
            this.listLoading = false;
          }
        })
        .catch((err) => {
          this.listLoading = false;
          this.handleRetrieveError(err);
        });
    },
    findByOriginalId(list, parentId) {
      return list?.find(
        (item) => parseInt(item.Originalpos_Id) === parseInt(parentId)
      );
    },
    setListWithNoFilters(
      division = false,
      department = false,
      section = false
    ) {
      if (division)
        this.divisionList.unshift({
          Pos_Name: "No Division",
          Pos_full_Name: "No Division",
          Originalpos_Id: "nodivision",
        });

      if (department)
        this.departmentList.unshift({
          Pos_Name: "No Department",
          Pos_full_Name: "No Department",
          Originalpos_Id: "nodepartment",
        });
      if (section)
        this.sectionList.unshift({
          Pos_Name: "No Section",
          Pos_full_Name: "No Section",
          Originalpos_Id: "nosection",
        });
    },
    exportReportFile() {
      let itemList = this.forecastList.map((item) => ({
        Pos_Name: item.Pos_Name,
        Pos_Code: item.Pos_Code,
        Approved_Position: item.Approved_Position,
        Warm_Bodies: item.Warm_Bodies,
        Group_Name: item.Group_Name,
        Group_Code: item.Group_Code,
        Division_Name: item.Division_Name,
        Division_Code: item.Division_Code,
        Department_Name: item.Department_Name,
        Department_Code: item.Department_Code,
        Section_Name: item.Section_Name,
        Section_Code: item.Section_Code,
        Jan: item?.Jan ?? 0,
        Feb: item?.Feb ?? 0,
        Mar: item?.Mar ?? 0,
        Apr: item?.Apr ?? 0,
        May: item?.May ?? 0,
        Jun: item?.Jun ?? 0,
        Jul: item?.Jul ?? 0,
        Aug: item?.Aug ?? 0,
        Sep: item?.Sep ?? 0,
        Oct: item?.Oct ?? 0,
        Nov: item?.Nov ?? 0,
        Dec: item?.Dec ?? 0,
        To_Be_Hired: item.To_Be_Hired,
        To_Be_Source: item.To_Be_Source,
      }));
      let fileName = "Hiring Forecast";
      let exportHeaders = [
        {
          header: "Position Title",
          key: "Pos_Name",
        },
        {
          header: "Position Code",
          key: "Pos_Code",
        },
        {
          header: "Approved Positions",
          key: "Approved_Position",
        },
        { header: "Warm Bodies", key: "Warm_Bodies" },
        {
          header: "Group",
          key: "Group_Name",
        },
        {
          header: "Group Code",
          key: "Group_Code",
        },
        {
          header: "Division",
          key: "Division_Name",
        },
        {
          header: "Division Code",
          key: "Division_Code",
        },
        {
          header: "Department",
          key: "Department_Name",
        },
        {
          header: "Department Code",
          key: "Department_Code",
        },
        {
          header: "Section",
          key: "Section_Name",
        },
        {
          header: "Section Code",
          key: "Section_Code",
        },
      ];
      for (let i = 0; i < 12; i++) {
        exportHeaders.push({
          header: this.monthNameConversion(i),
          key: this.monthNameConversion(i),
        });
      }
      exportHeaders.push(
        { header: "Approved Vacant Positions", key: "To_Be_Hired" },
        { header: "Vacancies for TO Review", key: "To_Be_Source" }
      );
      let exportOptions = {
        fileExportData: itemList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100vh;
  width: 50vw;
  overflow-y: auto;
}

.man-power-planning-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .man-power-planning-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
