<template>
  <v-card class="rounded-lg mt-2">
    <div
      class="d-flex align-center"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center pl-4 py-2">
        <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
          <v-icon class="primary" size="20">fas fa-tasks</v-icon>
        </v-avatar>

        <section style="max-width: 250px" class="text-truncate">
          <div class="text-subtitle-1 font-weight-bold d-flex align-center">
            <!-- Tooltip for Round Name (only if length > 25) -->
            <template
              v-if="
                selectedItem.Round_Name && selectedItem.Round_Name.length > 25
              "
            >
              <v-tooltip location="bottom" :text="selectedItem.Round_Name">
                <template v-slot:activator="{ props }">
                  <v-card variant="flat" v-bind="props">
                    <span class="d-inline-block">
                      {{ selectedItem.Round_Name.slice(0, 25) + "..." }}
                    </span>
                  </v-card>
                </template>
              </v-tooltip>
            </template>
            <template v-else>
              <v-card variant="flat">
                <span class="d-inline-block">{{
                  selectedItem.Round_Name
                }}</span>
              </v-card>
            </template>
          </div>
        </section>
      </div>

      <div class="d-flex align-center">
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <v-btn
              v-if="accessRights.update"
              v-bind="selectedItem.Check_FeedBack === 'Yes' ? props : ''"
              @click="
                selectedItem.Check_FeedBack === 'No'
                  ? $emit('open-edit-form')
                  : {}
              "
              size="small"
              color="primary"
              rounded="lg"
              >Edit</v-btn
            >
          </template>
          <div v-if="selectedItem.Check_FeedBack === 'Yes'">
            You are unable to update the interview round details because the
            interviews have already been scheduled.
          </div>
        </v-tooltip>
        <v-icon class="mx-1" color="primary" @click="$emit('close-form')">
          fas fa-times
        </v-icon>
      </div>
    </div>
    <div style="height: calc(100vh - 300px); overflow: scroll">
      <v-card-text>
        <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Max Score Per Skill
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Max_Score_Per_Skill
                  ? selectedItem.Max_Score_Per_Skill
                  : 0
              }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Passing Score</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Passing_Score ? selectedItem.Passing_Score : 0 }}
              /
              {{ selectedItem.Total_Score ? selectedItem.Total_Score : 0 }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Minimum Panel Members
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Minimum_Panel_Members
                  ? selectedItem.Minimum_Panel_Members
                  : 0
              }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Maximum Panel Members
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Maximum_Panel_members
                  ? selectedItem.Maximum_Panel_members
                  : 0
              }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Maximum Candidates</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Maximum_Candidates
                  ? selectedItem.Maximum_Candidates
                  : 0
              }}
            </p>
          </v-col>
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Description</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Description ? selectedItem.Description : "-" }}
            </p>
          </v-col>
          <v-col cols="12">
            <p class="text-subtitle-1 text-grey-darken-1 mb-2">
              Skill Category
            </p>
            <v-expansion-panels v-if="skillCategory && skillCategory.length">
              <v-expansion-panel
                v-for="(category, categoryIndex) in skillCategory"
                :key="categoryIndex"
              >
                <!-- Panel Title -->
                <v-expansion-panel-title>
                  <v-row no-gutters>
                    <v-col class="d-flex justify-start" cols="12">
                      <p class="text-subtitle-1">
                        {{ checkNullValue(category.Skill_Category) }}
                      </p>
                    </v-col>
                  </v-row>
                </v-expansion-panel-title>

                <!-- Panel Content -->
                <v-expansion-panel-text>
                  <v-row class="bg-hover my-2">
                    <v-col class="d-flex justify-start" cols="3">
                      <p class="text-subtitle-1 text-grey-darken-1">Skill</p>
                    </v-col>
                    <v-col class="d-flex justify-start" cols="9">
                      <p class="text-subtitle-1 text-grey-darken-1">
                        Instructions to Interviewer
                      </p>
                    </v-col>
                  </v-row>
                  <v-row
                    no-gutters
                    v-for="(skill, skillIndex) in category.Skills"
                    :key="skillIndex"
                    class="mb-2"
                  >
                    <v-col class="d-flex justify-start" cols="3">
                      <p class="text-subtitle-1" style="max-width: 95%">
                        {{ checkNullValue(skill.Skill_Name) }}
                      </p>
                    </v-col>
                    <v-col class="d-flex justify-start" cols="9">
                      <p
                        class="text-body-1 text-grey-darken-1"
                        style="max-width: 95%"
                      >
                        {{ checkNullValue(skill.Questions) }}
                      </p>
                    </v-col>
                    <v-divider class="my-1" />
                  </v-row>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
            <div v-else>
              <p class="text-subtitle-1 font-weight-regular mt-n2">-</p>
            </div>
          </v-col>
          <v-col v-if="moreDetailsList.length > 0" cols="12">
            <MoreDetails
              :more-details-list="moreDetailsList"
              :open-close-card="openMoreDetails"
              @on-open-close="openMoreDetails = $event"
            ></MoreDetails>
          </v-col>
        </v-row>
      </v-card-text>
    </div>
  </v-card>
</template>
<script>
import { defineComponent, defineAsyncComponent } from "vue";
import moment from "moment";
import { checkNullValue } from "@/helper";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);
export default defineComponent({
  name: "ViewInterviewRounds",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    moreDetailsList: [],
    skillCategory: [],
  }),
  computed: {
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  watch: {
    "selectedItem.SkillsCategory": {
      handler(newValue) {
        this.skillCategory = JSON.parse(newValue);
      },
      deep: true,
    },
  },
  mounted() {
    this.prefillMoreDetails();
    this.skillCategory = JSON.parse(this.selectedItem.SkillsCategory);
  },
  methods: {
    checkNullValue,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(this.selectedItem.addedOn),
        addedByName = this.selectedItem.addedByName,
        updatedByName = this.selectedItem.updatedByName,
        updatedOn = this.formatDate(this.selectedItem.updatedOn);
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
});
</script>
