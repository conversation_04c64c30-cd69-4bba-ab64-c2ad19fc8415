import gql from "graphql-tag";
// ===============
// Queries
// ===============

export const GET_HIRING_HEALTH = gql`
  query retrieveHiringHealth($employeeIds: [Int]) {
    retrieveHiringHealth(employeeIds: $employeeIds) {
      errorCode
      message
      positionsToBeHired
    }
  }
`;

export const GET_HIRED_CANDIDATES = gql`
  query hiredCandidateCount(
    $employeeIds: [Int]
    $startDate: String!
    $endDate: String!
  ) {
    hiredCandidateCount(
      employeeIds: $employeeIds
      startDate: $startDate
      endDate: $endDate
    ) {
      errorCode
      message
      totalHiredCandidates
    }
  }
`;

export const GET_ACCEPTENCE_RATE = gql`
  query getOfferAcceptanceRate(
    $employeeIds: [Int]
    $startDate: String!
    $endDate: String!
  ) {
    getOfferAcceptanceRate(
      employeeIds: $employeeIds
      startDate: $startDate
      endDate: $endDate
    ) {
      errorCode
      message
      offerAcceptanceRate
    }
  }
`;

export const GET_POSITIONS_OVERDUE = gql`
  query getPositionsOverdue($employeeIds: [Int]) {
    getPositionsOverdue(employeeIds: $employeeIds) {
      errorCode
      message
      noOfPositionsOverdue
    }
  }
`;

export const GET_SOURCE_TO_HIRE = gql`
  query retrieveSourceToHireRatio(
    $employeeIds: [Int]
    $startDate: String!
    $endDate: String!
  ) {
    retrieveSourceToHireRatio(
      employeeIds: $employeeIds
      startDate: $startDate
      endDate: $endDate
    ) {
      errorCode
      message
      sourceToHireRatio
    }
  }
`;

export const GET_TIME_TO_HIRE = gql`
  query retrieveTimeToHire(
    $employeeIds: [Int]
    $startDate: String!
    $endDate: String!
  ) {
    retrieveTimeToHire(
      employeeIds: $employeeIds
      startDate: $startDate
      endDate: $endDate
    ) {
      errorCode
      message
      timeToHire
    }
  }
`;

export const GET_ORG_UNIT_LIST = gql`
  query getOrgUnitList($employeeIds: [Int]) {
    getOrgUnitList(employeeIds: $employeeIds) {
      errorCode
      message
      serviceProviderWiseJobpostDetails {
        id
        name
        jobs
        totalPositions
        hiredPositions
      }
      departmentWiseJobpostDetails {
        id
        name
        jobs
        totalPositions
        hiredPositions
      }
    }
  }
`;

export const GET_ORGANIZATION_WISE_DEPARTMENT_LIST = gql`
  query retrieveDepartmentWiseJobPostDetails(
    $employeeIds: [Int]
    $orgUnitId: Int!
  ) {
    retrieveDepartmentWiseJobPostDetails(
      employeeIds: $employeeIds
      orgUnitId: $orgUnitId
    ) {
      errorCode
      message
      departmentWiseJobpostDetails {
        departmentId
        departmentName
        jobs
        totalPositions
        hiredPositions
      }
    }
  }
`;

export const GET_RECRUITER_PERFORMANCE_DETAILS = gql`
  query retrieveRecuiterPerformanceDetails(
    $startDate: String!
    $endDate: String!
    $employeeIds: [Int]
  ) {
    retrieveRecuiterPerformanceDetails(
      startDate: $startDate
      endDate: $endDate
      employeeIds: $employeeIds
    ) {
      errorCode
      message
      retrieveRecuiterPerformanceDetails {
        name
        positionHired
        timeToHire
        jobs
      }
    }
  }
`;

export const GET_TIME_TO_HIRE_OVER_PERIOD = gql`
  query retrieveTimeToHireOverAPeriod(
    $startDate: String!
    $endDate: String!
    $employeeIds: [Int]
  ) {
    retrieveTimeToHireOverAPeriod(
      startDate: $startDate
      endDate: $endDate
      employeeIds: $employeeIds
    ) {
      errorCode
      message
      timeToHireDetails
    }
  }
`;
