<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row
            v-if="originalList.length > 0 && !showAddEditForm"
            justify="center"
          >
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="false"
              />
              <FormFilter
                :originalList="originalList"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="locations">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList('Locations error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="originalList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="Managing location records in your  system is crucial for maintaining accurate employee data. Whether you're adding, editing, or deleting records, it's essential to ensure all information is up-to-date and correctly entered."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Ensure consistency in data entry and accessibility for authorized personnel to effectively manage and utilize location records within the system."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 secondary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span class="primary">Add New</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && originalList.length"
            main-title="There are no locations matched for the selected filters/searches."
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div
              v-if="originalList.length > 0 && !isSmallTable"
              class="d-flex flex-wrap align-center my-2"
              :class="isMobileView ? 'flex-column' : ''"
              style="justify-content: space-between"
            >
              <div
                class="d-flex align-center flex-wrap"
                :class="isMobileView ? 'justify-center' : ''"
              >
                <v-btn
                  rounded="lg"
                  style="pointer-events: none"
                  variant="flat"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  >Active:
                  <span class="text-green font-weight-bold">{{
                    activeLocation
                  }}</span>
                </v-btn>
                <v-btn
                  rounded="lg"
                  style="pointer-events: none"
                  variant="flat"
                  class="ml-2"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  >InActive:
                  <span class="text-red font-weight-bold">{{
                    inactiveLocation
                  }}</span></v-btn
                >
              </div>
              <div
                v-if="!isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  v-if="formAccess.add"
                  prepend-icon="fas fa-plus"
                  variant="elevated"
                  class="mx-1 secondary"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="openAddForm()"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  <span class="primary">Add New</span>
                </v-btn>
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                  ><v-icon>fas fa-redo-alt</v-icon></v-btn
                >
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>

            <v-row>
              <v-col
                v-if="originalList.length > 0"
                class="mb-12"
                :cols="isSmallTable && windowWidth >= 1264 ? 6 : 12"
              >
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                  :sort-by="[{ key: 'Location_Name', order: 'asc' }]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView
                          ? 'v-data-table__mobile-table-row ma-0 mt-2'
                          : ''
                      "
                    >
                      <!-- <td id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Location
                          </div>
                          <section class="d-flex align-center">
                            <div class="d-flex align-center">
                              <div
                                v-if="
                                  isSmallTable &&
                                  windowWidth > 1264 &&
                                  selectedItem &&
                                  selectedItem.Location_Name ===
                                    item.Location_Id
                                "
                                class="data-table-side-border"
                              ></div>
                            </div>
                            <div
                              class="text-body-2 font-weight-medium text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                              v-bind="
                                item.Location_Name.length > 50 ? props : ''
                              "
                            >
                              {{ item.Location_Name }}
                            </div>
                          </section>
                        </td> -->
                      <td id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold mt-2">
                          Location
                        </div>
                        <section class="d-flex align-center">
                          <div
                            v-if="
                              isSmallTable &&
                              !isMobileView &&
                              selectedItem &&
                              selectedItem.Location_Id === item.Location_Id
                            "
                            class="data-table-side-border d-flex"
                          ></div>
                          <div style="max-width: 200px" class="text-truncate">
                            <span
                              class="text-primary text-body-2 font-weight-medium"
                            >
                              <v-tooltip
                                :text="item.Location_Name"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-bind="
                                      item.Location_Name &&
                                      item.Location_Name.length > 20
                                        ? props
                                        : ''
                                    "
                                    >{{ item.Location_Name }}</span
                                  >
                                </template>
                              </v-tooltip>
                              <v-tooltip
                                :text="item.Location_Code"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    v-if="item.Location_Code"
                                    v-bind="
                                      item.Location_Code &&
                                      item.Location_Code.length > 20
                                        ? props
                                        : ''
                                    "
                                    class="text-grey"
                                  >
                                    {{ item.Location_Code }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </span>
                          </div>
                        </section>
                      </td>
                      <!-- <td v-if="!isSmallTable" id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Location Code
                          </div>
                          <section class="text-body-2 text-primary">
                            {{ item.Location_Code ? item.Location_Code : "-" }}
                          </section>
                        </td> -->
                      <td id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Location Type
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ item.Location_Type ? item.Location_Type : "-" }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          City
                          <div
                            v-if="
                              isSmallTable &&
                              windowWidth > 1264 &&
                              selectedItem &&
                              selectedItem.locationId === item.locationId
                            "
                            class="data-table-side-border d-flex"
                          ></div>
                        </div>
                        <section class="text-body-2 text-primary max-w-50">
                          {{ item.City_Name ? item.City_Name : "-" }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          State
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ item.State_Name ? item.State_Name : "-" }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Country
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ item.Country_Name ? item.Country_Name : "-" }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Status
                        </div>
                        <section class="text-body-2 text-primary">
                          <!-- Toggle button for Active/Inactive -->
                          <AppToggleButton
                            button-active-text="Active"
                            button-inactive-text="InActive"
                            button-active-color="#7de272"
                            button-inactive-color="red"
                            id-value="gab-analysis-based-on"
                            :current-value="item.Location_Status === 'Active'"
                            @chosen-value="
                              (Location_Status) =>
                                updateStatus(Location_Status, item)
                            "
                            :isDisableToggle="!formAccess.update"
                            :tooltipContent="
                              formAccess.update
                                ? ''
                                : `Sorry, you don't have access rights to update the status`
                            "
                          ></AppToggleButton>
                        </section>
                      </td>

                      <td
                        v-if="!isSmallTable"
                        class="text-body-2 text-end"
                        style="width: 150px"
                      >
                        <ActionMenu
                          @selected-action="onActions($event, item)"
                          :actions="['Edit', 'Delete']"
                          :access-rights="formAccess"
                        ></ActionMenu>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
              <v-col
                :cols="originalList.length === 0 ? 12 : 6"
                v-if="isSmallTable && windowWidth >= 1264"
              >
                <AddEditLocation
                  v-if="showAddEditForm"
                  :isEdit="isEdit"
                  :editFormData="selectedItem"
                  :access-rights="formAccess"
                  @close-form="closeAllForms()"
                  @form-updated="refetchList('Location was added/updated')"
                ></AddEditLocation>
                <ViewLocation
                  v-else
                  :selectedItem="selectedItem"
                  :access-rights="formAccess"
                  @close-form="closeAllForms()"
                  @open-edit-form="openEditForm()"
                ></ViewLocation>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <v-dialog
      v-if="openFormInModal"
      :model-value="openFormInModal"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditLocation
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :formAccess="formAccess"
        @close-form="closeAllForms()"
        @form-updated="refetchList('Location was added/updated')"
      ></AddEditLocation>
      <ViewLocation
        v-else
        :selectedItem="selectedItem"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      ></ViewLocation>
    </v-dialog>
    <!-- <div v-else-if="isImportModel">
      <LocationImport
        @close-import-model="closeImportModel()"
        @refetch-data="refetchList()"
        :backupMainList="backupMainList"
      ></LocationImport>
    </div> -->
    <AppLoading v-if="isLoading"></AppLoading>

    <!-- Warning Modal for Delete Confirmation -->
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteLocation()"
    ></AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const ViewLocation = defineAsyncComponent(() => import("./ViewLocation.vue"));
const AddEditLocation = defineAsyncComponent(() =>
  import("./AddEditLocation.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import FormFilter from "./LocationFilter.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);

import {
  LIST_LOCATION_DETAILS,
  ADD_EDIT_LIST_LOCATION_DETAILS,
  DELETE_LOCATION_DETAILS,
} from "@/graphql/organisation/location/OrganisationQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import FileExportMixin from "@/mixins/FileExportMixin";
// const LocationImport = defineAsyncComponent(() =>
//   import("./LocationImport.vue")
// );
import AppToggleButton from "@/components/base-components/AppToggleButton.vue";

export default {
  name: "LocationsDetails",
  components: {
    FormFilter,
    EmployeeDefaultFilterMenu,
    AddEditLocation,
    ViewLocation,
    NotesCard,
    // LocationImport,
    AppToggleButton,
    ActionMenu,
  },
  mixins: [FileExportMixin],
  data: () => ({
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    openWarningModal: false,
    openMoreMenu: false,
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    selectedItem: null,
    showViewForm: false,
    resetFilterCount: 0,
    // isImportModel: false,
    currentTabItem: "",
    editFormData: {},
  }),
  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    landedFormName() {
      return "Locations";
    },
    orgStructureFormAccess() {
      return this.$store.getters.orgStructureFormAccess;
    },
    organizationGroupFormName() {
      let projectForm = this.accessRights("269");
      if (
        projectForm &&
        projectForm.customFormName &&
        projectForm.customFormName !== ""
      ) {
        return projectForm.customFormName;
      } else return "Organization Group";
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.orgStructureFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (formAccessArray && formAccessArray.includes("Organization Group")) {
          const index = formAccessArray.indexOf("Organization Group");
          formAccessArray[index] = this.organizationGroupFormName;
        }
        return formAccessArray;
      }
      return [];
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("1");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
        // {
        //   key: "Import",
        //   icon: "fas fa-file-import",
        // },
      ];
      return actions;
    },
    isSmallTable() {
      return this.showViewForm || this.showAddEditForm;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Location",
            align: "start",
            key: "Location_Name",
          },
          {
            title: "Location Type",
            key: "Location_Type",
          },
        ];
      } else {
        return [
          {
            title: "Location",
            align: "start",
            key: "Location_Name",
          },
          {
            title: "Location Type",
            key: "Location_Type",
          },
          {
            title: "City",
            key: "City_Name",
          },
          {
            title: "State",
            key: "State_Name",
          },
          {
            title: "Country",
            key: "Country_Name",
          },
          {
            title: "Status",
            key: "Location_Status",
          },
          {
            title: "Actions",
            key: "action",
            align: "end",
            sortable: false,
          },
        ];
      }
    },
    activeLocation() {
      let locationList = this.originalList.filter(
        (el) => el.Location_Status === "Active"
      );
      return locationList.length;
    },
    inactiveLocation() {
      let locationList = this.originalList.filter(
        (el) => el.Location_Status === "InActive"
      );
      return locationList.length;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = "There are no locations for the selected filters/searches.";
      }
      return msgText;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    openFormInModal() {
      if (this.isSmallTable && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    let msg =
      "Something went wrong while loading the locations. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: false,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    updateStatus(Location_Status, item) {
      let vm = this;
      // Extract the boolean value from the array
      const newStatus = Array.isArray(Location_Status)
        ? Location_Status[1]
        : Location_Status;

      // Check if the new status is different from the current status
      const currentStatus = item.Location_Status === "Active";

      if (currentStatus === newStatus) {
        // If the status hasn't changed, do nothing
        return;
      }

      this.isLoading = true;
      try {
        this.$apollo
          .mutate({
            mutation: ADD_EDIT_LIST_LOCATION_DETAILS,
            variables: {
              locationId: item.Location_Id ? parseInt(item.Location_Id) : 0,
              locationName: item.Location_Name || "",
              locationType: item.Location_Type || "",
              street1: item.Street1 || "",
              street2: item.Street2 || "",
              cityId: item.City_Id ? parseInt(item.City_Id) : 0,
              stateId: item.State_Id ? parseInt(item.State_Id) : 0,
              countryCode: item.Country_Code || "",
              phone: item.Phone_Number || "",
              currencySymbol: item.Currency_Symbol || "",
              locationStatus: newStatus ? "Active" : "InActive",
              locationCode: item.Location_Code || "",
              pincode: item.Pincode || "",
              description: item.Description || "",
              orgId: item.Org_Id ? parseInt(item.Org_Id) : 0,
              zoneId: item.TimeZone_Id ? parseInt(item.TimeZone_Id) : 0,
            },
            client: "apolloClientJ",
          })
          .then((response) => {
            if (response && response.data && response.data.addUpdateLocation) {
              const { errorCode, validationError } =
                response.data.addUpdateLocation;
              if (!errorCode && !validationError) {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Location status updated successfully.",
                };
                vm.showAlert(snackbarData);
                this.refetchList("status-update-success");
              } else {
                vm.handleAddUpdateError("updating");
              }
            } else {
              vm.handleAddUpdateError("updating");
            }
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError("updating", addEditError);
          });

        // Update the local state with the new status
        item.Location_Status = newStatus ? "Active" : "InActive";

        this.isLoading = false;
        this.refetchList("status-update-success");
      } catch (addEditError) {
        this.handleAddUpdateError(addEditError);
      }
    },

    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "locations",
        isListError: false,
      });

      this.refetchList("status-update-failed");
    },
    onDeleteLocation() {
      let vm = this;
      vm.isLoading = true;
      const { Location_Id } = this.selectedItem;
      vm.$apollo
        .mutate({
          mutation: DELETE_LOCATION_DETAILS,
          variables: {
            locationId: Location_Id,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Location deleted successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.refetchList("Location deleted");
          vm.openWarningModal = false;
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      mixpanel.track("Location-delete-error");

      this.isLoading = false;

      // Dispatch the error handling action with updated context for deleting locations
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "locations",
        isListError: false,
      });

      this.openWarningModal = false;
    },

    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        this.onDelete(item);
      } else {
        this.onEdit(item);
      }
    },

    onDelete(item) {
      this.selectedItem = item;
      this.openWarningModal = true;
    },

    onEdit(item) {
      this.selectedItem = item;
      this.openEditForm();
    },

    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedItem = null;
    },
    // openImportModel() {
    //   this.isImportModel = true;
    // },
    // closeImportModel() {
    //   this.isImportModel = false;
    // },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        mixpanel.track("Location-export-click");
        this.exportReportFile();
      }
      // else if (actionType === "Import") {
      //   this.openImportModel();
      // }
      this.openMoreMenu = false;
    },

    onApplySearch(val) {
      if (!val) {
        this.resetFilter();
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.itemList;
        searchItems = searchItems.filter((item) => {
          let newObj = {
            Location_Type: item.Location_Type,
            Location_Code: item.Location_Code,
            Location_Name: item.Location_Name,
            City_Name: item.City_Name,
            State_Name: item.State_Name,
            Country_Name: item.Country_Name,
          };
          return Object.keys(newObj).some((k) => {
            if (
              item[k] &&
              item[k].toString().toLowerCase().includes(searchValue)
            ) {
              return true;
            } else {
              return false;
            }
          });
        });
        this.itemList = searchItems;
      }
    },
    applyFilter(filteredArray) {
      this.itemList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    resetFilter() {
      this.itemList = this.originalList.filter(
        (res) => res.Location_Status === "Active"
      );
      this.isFilter = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },

    openEditForm() {
      mixpanel.track("Location edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item) {
      mixpanel.track("Location form opened");
      this.selectedItem = item;
      this.showViewForm = true;
    },

    openAddForm() {
      mixpanel.track("Location add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      mixpanel.track("Location all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.openWarningModal = false;
    },

    onTabChange(tab) {
      mixpanel.track("Locations form tab changed");
      if (tab !== this.landedFormName) {
        if (tab === this.organizationGroupFormName) {
          tab = "Organization Group";
        }
        this.isLoading = true;
        const { formAccess } = this.orgStructureFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_LOCATION_DETAILS,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: {
            limit: 0,
            offset: 0,
          },
        })
        .then((response) => {
          if (response.data && response.data.listLocationDetails.location) {
            const responseData = response.data.listLocationDetails.location;
            vm.itemList = responseData.filter(
              (res) => res.Location_Status === "Active"
            );
            vm.originalList = responseData;
            vm.listLoading = false;
            mixpanel.track("Location list retrieved");
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      mixpanel.track("Location error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Locations",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
      this.resetFilter();
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Location Code", key: "Location_Code" },
        { header: "Location", key: "Location_Name" },
        { header: "Location Type", key: "Location_Type" },
        { header: "Street1", key: "Street1" },
        { header: "Street2", key: "Street2" },
        { header: "Country", key: "Country_Name" },
        { header: "State", key: "State_Name" },
        { header: "City", key: "City_Name" },
        { header: "PinCode", key: "Pincode" },
        { header: "TimeZone", key: "TimeZone_Id" },
        { header: "Currency Symbol", key: "Currency_Symbol" },
        { header: "Contact Number", key: "Phone_Number" },
        { header: "Status", key: "newStatus" },
        { header: "Description", key: "Description" },
        { header: "Added On", key: "Added_On" },
        { header: "Added By", key: "Added_By_Name" },
        { header: "Updated On", key: "Updated_On" },
        { header: "Updated By", key: "Updated_By_Name" },
      ];

      const exportList = this.itemList.map((item) => ({
        Location_Code: item.Location_Code,
        Location_Name: item.Location_Name,
        Location_Type: item.Location_Type,
        Street1: item.Street1,
        Street2: item.Street2,
        Country_Name: item.Country_Name,
        State_Name: item.State_Name,
        City_Name: item.City_Name,
        Pincode: item.Pincode,
        TimeZone_Id: item.TimeZone_Name,
        Currency_Symbol: item.Currency_Symbol,
        Phone_Number: item.Phone,
        newStatus: item.Location_Status,
        Description: item.Description,
        Added_On: item.Added_On,
        Added_By_Name: item.Added_By_Name,
        Updated_On: item.Updated_On,
        Updated_By_Name: item.Updated_By_Name,
      }));

      const exportOptions = {
        fileExportData: exportList,
        fileName: "Location Details",
        sheetName: "Location Details",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
      mixpanel.track("Location-Details-exported");
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.locations {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
@media screen and (max-width: 805px) {
  .locations {
    padding: 4em 1em 0em 1em;
  }
}
</style>
