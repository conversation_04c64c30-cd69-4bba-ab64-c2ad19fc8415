<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-menu
              v-model="swapDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="formattedSwapDate"
                  v-model="formattedSwapDate"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  label="Swap Date"
                  v-bind="props"
                  :clearable="true"
                  variant="solo"
                />
              </template>
              <v-date-picker v-model="selectedSwapDate" />
            </v-menu>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedShiftType"
              color="primary"
              :items="listShiftType"
              label="Shift Type"
              multiple
              variant="solo"
              clearable
              closable-chips
              chips
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="listStatus"
              label="Status"
              multiple
              variant="solo"
              clearable
              closable-chips
              chips
              single-line
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "ShiftSwapApprovalFilter",
  data: () => ({
    openFormFilter: false,
    selectedSwapDate: null,
    selectedStatus: null,
    // Date Picker
    selectedShiftType: null,
    swapDateMenu: false,
    formattedSwapDate: "",
  }),

  props: {
    itemList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    listShiftType() {
      const filteredItemList = this.itemList.filter(
        (item) => item.Shift_Name && item.Shift_Name.trim() !== ""
      );
      // Use Set to filter out duplicate employee Name values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.Shift_Name)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    listStatus() {
      const filteredItemList = this.itemList.filter(
        (item) => item.Approval_Status && item.Approval_Status.trim() !== ""
      );
      // Use Set to filter out duplicate employee Name values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.Approval_Status)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
  },
  watch: {
    selectedSwapDate(val) {
      if (val) {
        this.swapDateMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedSwapDate = dateValue;
      }
    },
    formattedSwapDate(val) {
      if (!val) {
        this.selectedSwapDate = null;
      }
    },
  },
  mounted() {},
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        selectedShiftType: this.selectedShiftType,
        selectedSwapDate: this.selectedSwapDate,
        selectedStatus: this.selectedStatus,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedShiftType = null;
      this.selectedSwapDate = null;
      this.selectedStatus = null;
      this.formattedSwapDate = "";
    },
  },
};
</script>
