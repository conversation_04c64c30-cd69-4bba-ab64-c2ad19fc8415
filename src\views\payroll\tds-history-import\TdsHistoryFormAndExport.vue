<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div class="d-flex align-center justify-space-between px-4 pt-4">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="secondary"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Download TDS history template</span
          >
        </div>
        <v-icon @click="closeForm" color="secondary" class="mr-1">
          fas fa-times
        </v-icon>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-4'"
        style="height: calc(100vh - 500px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="tdsHistoryImportForm">
            <v-row>
              <v-col cols="12" sm="6" md="6">
                <CustomSelect
                  :items="historyTypeItems"
                  label="History Type*"
                  :is-auto-complete="true"
                  :itemSelected="historyType"
                  @selected-item="onChangeCustomSelect($event, 'historyType')"
                  :rules="[required('History Type', historyType)]"
                ></CustomSelect>
              </v-col>

              <v-col cols="12" sm="6" md="6" lg="6">
                <datepicker
                  v-model="fromDate"
                  :minimum-view="'month'"
                  :maximum-view="'year'"
                  placeholder="From Date*"
                  style="min-width: 100% !important"
                  :rules="[required('From Date', fromDate)]"
                  @input="onChangeDateFields()"
                ></datepicker>
                <div
                  v-if="fromDateErrorMsg"
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ !this.fromDate ? fromDateErrorMsg : "" }}
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" lg="6">
                <datepicker
                  v-model="toDate"
                  :minimum-view="'month'"
                  :maximum-view="'year'"
                  placeholder="To Date*"
                  style="min-width: 100% !important"
                  :rules="[required('To Date', toDate)]"
                  @input="onChangeDateFields()"
                ></datepicker>
                <div
                  v-if="toDateErrorMsg"
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ !this.toDate ? toDateErrorMsg : "" }}
                </div>
              </v-col>
              <v-col cols="12">
                <div class="d-flex justify-center mt-16">
                  <v-btn
                    id="bulk_sheet_download"
                    rounded="lg"
                    size="small"
                    color="secondary"
                    class="font-weight-bold"
                    @click="saveData"
                  >
                    <v-icon class="mr-2" size="16"
                      >fas fa-cloud-download-alt</v-icon
                    >
                    Download
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import Datepicker from "vuejs3-datepicker";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
//Mixins
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "TdsHistoryTemplateExport",
  mixins: [validationRules, FileExportMixin],
  components: {
    CustomSelect,
    Datepicker,
  },
  data() {
    return {
      isMounted: false,
      isLoadingDetails: false,
      historyType: "",
      fromDate: null,
      toDate: null,
      isFormDirty: false,
      fromDateErrorMsg: "",
      toDateErrorMsg: "",
      openConfirmationPopup: false,
      historyTypeItems: [
        "New Registration",
        "New Joinees With Previous Employment",
      ],
      exportListData: [
        {
          User_Defined_EmpId: "HACHADRE43",
          Employee_Name: "shyam",
          History_Type: "New Registration",
          From_Date: "Feb, 2022",
          To_Date: "Jul, 2022",
          Gross_Salary: 140000,
          Basic_Pay: 0,
          Dearness_Allowance: 0,
          House_Rent_Allowance: 0,
          Other_Allowance: 0,
          Commission: 0,
          Perquisites: 0,
          Profits_In_Lieu_Of_Salary: 0,
          Professional_Tax: 0,
          Total_Income_Tax_Paid: 0,
          Medical_Expense: 0,
          Medical_Insurance: 0,
          Leave_Encashment_Exemptions: 0,
          Gratuity_Amount: 0,
          VRS_Exemption_Amount: 0,
          No_of_times_LTA_claimed: 0,
          LTA_Exemption_carried_forward: "No",
          No_Of_times_LTA_carried_forward: 0,
          Education_cess: 0,
          Surcharge: 0,
          Total_Employee_PF_contribution: 0,
          Total_Employee_VPF_Contribution: 0,
          Total_Employer_NPS_Contribution: 0,
        },
        {
          User_Defined_EmpId: "HRAPP05",
          Employee_Name: "suhan",
          History_Type: "New Registration",
          From_Date: "Feb, 2022",
          To_Date: "Jul, 2022",
          Gross_Salary: 10000,
          Basic_Pay: 0,
          Dearness_Allowance: 0,
          House_Rent_Allowance: 0,
          Other_Allowance: 0,
          Commission: 0,
          Perquisites: 0,
          Profits_In_Lieu_Of_Salary: 0,
          Professional_Tax: 0,
          Total_Income_Tax_Paid: 0,
          Medical_Expense: 0,
          Medical_Insurance: 0,
          Leave_Encashment_Exemptions: 0,
          Gratuity_Amount: 0,
          VRS_Exemption_Amount: 0,
          No_of_times_LTA_claimed: 0,
          LTA_Exemption_carried_forward: "No",
          No_Of_times_LTA_carried_forward: 0,
          Education_cess: 0,
          Surcharge: 0,
          Total_Employee_PF_contribution: 0,
          Total_Employee_VPF_Contribution: 0,
          Total_Employer_NPS_Contribution: 0,
        },
        {
          User_Defined_EmpId: "HRAPP02",
          Employee_Name: "abhishekh",
          History_Type: "New Registration",
          From_Date: "Feb, 2022",
          To_Date: "Jul, 2022",
          Gross_Salary: 20000,
          Basic_Pay: 0,
          Dearness_Allowance: 0,
          House_Rent_Allowance: 0,
          Other_Allowance: 0,
          Commission: 0,
          Perquisites: 0,
          Profits_In_Lieu_Of_Salary: 0,
          Professional_Tax: 0,
          Total_Income_Tax_Paid: 0,
          Medical_Expense: 0,
          Medical_Insurance: 0,
          Leave_Encashment_Exemptions: 0,
          Gratuity_Amount: 0,
          VRS_Exemption_Amount: 0,
          No_of_times_LTA_claimed: 0,
          LTA_Exemption_carried_forward: "No",
          No_Of_times_LTA_carried_forward: 0,
          Education_cess: 0,
          Surcharge: 0,
          Total_Employee_PF_contribution: 0,
          Total_Employee_VPF_Contribution: 0,
          Total_Employer_NPS_Contribution: 0,
        },
      ],
    };
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    closeForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-dialog-form");
    },
    onChangeDateFields() {
      this.isFormDirty = true;
    },
    onChangeCustomSelect(val, field) {
      if (field == "historyType") {
        this.historyType = val;
      }
      this.isFormDirty = true;
    },
    async saveData() {
      const { valid } = await this.$refs.tdsHistoryImportForm.validate();
      if (valid && this.fromDate && this.toDate) {
        this.exportReportFile();
      } else {
        if (!this.fromDate) {
          this.fromDateErrorMsg = "From Date is required";
        }
        if (!this.toDate) {
          this.toDateErrorMsg = "To Date is required";
        }
      }
    },
    exportReportFile() {
      let exportData = this.exportListData;
      let headers = [];
      headers.unshift(
        { key: "User_Defined_EmpId", header: "Employee Id" },
        { key: "Employee_Name", header: "Employee Name" },
        { key: "History_Type", header: "History Type" },
        { key: "From_Date", header: "From Date" },
        { key: "To_Date", header: "To Date" },
        { key: "Gross_Salary", header: "Gross Salary" },
        { key: "Basic_Pay", header: "Basic Pay" },
        { key: "Dearness_Allowance", header: "Dearness Allowance" },
        { key: "House_Rent_Allowance", header: "House Rent Allowance" },
        { key: "Other_Allowance", header: "Other Allowance" },
        { key: "Commission", header: "Commission" },
        { key: "Perquisites", header: "Perquisites" },
        {
          key: "Profits_In_Lieu_Of_Salary",
          header: "Profits In Lieu Of Salary",
        },
        { key: "Gratuity_Amount", header: "Gratuity Amount" },
        { key: "VRS_Exemption_Amount", header: "VRS Exemption Amount" },
        { key: "No_of_times_LTA_claimed", header: "No of times LTA claimed" },
        { key: "Medical_Expense", header: "Medical Expense" },
        { key: "Medical_Insurance", header: "Medical Insurance" },
        {
          key: "Leave_Encashment_Exemptions",
          header: "Leave Encashment Exemptions",
        },
        { key: "Professional_Tax", header: "Professional Tax" },
        { key: "Total_Income_Tax_Paid", header: "Total Income Tax Paid" },
        {
          key: "LTA_Exemption_carried_forward",
          header: "LTA Exemption carried forward",
        },
        {
          key: "No_Of_times_LTA_carried_forward",
          header: "No Of times LTA carried forward",
        },
        { key: "Education_cess", header: "Education cess" },
        { key: "Surcharge", header: "Surcharge" },
        {
          key: "Total_Employee_PF_contribution",
          header: "Total Employee PF contribution",
        },
        {
          key: "Total_Employee_VPF_Contribution",
          header: "Total Employee VPF Contribution",
        },
        {
          key: "Total_Employer_NPS_Contribution",
          header: "Total Employer NPS Contribution",
        }
      );

      let exportOptions = {
        fileExportData: exportData,
        fileName: "TDS history template.xlsx",
        sheetName: "TDS history sheet",
        header: headers,
        requiredHeaders: [
          "Employee Id",
          "Employee Name",
          "History Type",
          "From Date",
          "To Date",
          "Gross Salary",
          "Basic Pay",
          "Dearness Allowance",
          "House Rent Allowance",
          "Other Allowance",
          "Commission",
          "Perquisites",
          "Profits In Lieu Of Salary",
          "Medical Expense",
          "Gratuity Amount",
          "VRS Exemption Amount",
          "No of times LTA claimed",
        ],
      };
      this.exportExcelFile(exportOptions);
      this.$emit("close-dialog-form");
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
  },
  mounted() {
    this.isFormDirty = false;
    this.isMounted = true;
  },
};
</script>
