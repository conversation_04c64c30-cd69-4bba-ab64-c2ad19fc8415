<template>
  <AppFetchErrorScreen
    v-if="isListEmpty || isSearchEmpty"
    key="no-results-screen"
    :main-title="
      isListEmpty
        ? ' '
        : 'There are no team members matched for the selected filters/searches.'
    "
    :isSmallImage="isListEmpty"
    :image-name="isListEmpty ? '' : 'common/no-records'"
  >
    <template #contentSlot>
      <div style="max-width: 80%">
        <v-row
          :style="isListEmpty ? 'background: white' : ''"
          class="rounded-lg pa-5 mb-4"
        >
          <v-col v-if="isListEmpty" cols="12">
            <NotesCard
              notes="Team timesheets offer a consolidated view of everyone's efforts, helping managers make informed decisions and plan resources effectively."
              backgroundColor="transparent"
              class="mb-4"
            ></NotesCard>
            <NotesCard
              notes="Analyzing team timesheets allows for the identification of workload trends, enabling proactive measures to address potential challenges and optimize work distribution."
              backgroundColor="transparent"
              class="mb-4"
            ></NotesCard>
            <NotesCard
              notes="Easily identify tasks or projects that may require additional support by examining how time is distributed among team members. This facilitates better task allocation for improved efficiency."
              backgroundColor="transparent"
              class="mb-4"
            ></NotesCard>
            <NotesCard
              notes="Managers can streamline the approval process by reviewing and approving team timesheets, ensuring accuracy before further processing."
              backgroundColor="transparent"
              class="mb-4"
            ></NotesCard>
          </v-col>
          <v-col cols="12" class="d-flex align-center justify-center mb-4">
            <v-card
              v-if="isListEmpty"
              class="my-2"
              :size="isMobileView ? 'small' : 'default'"
              rounded="lg"
              style="width: 340px; background: white !important"
            >
              <v-icon
                color="primary"
                size="17"
                class="mx-2"
                @click="$emit('fetch-prev-week')"
              >
                fa fa-chevron-left
              </v-icon>
              <v-icon
                color="primary"
                size="17"
                class="mx-2"
                @click="$emit('fetch-next-week')"
              >
                fa fa-chevron-right
              </v-icon>
              <v-menu
                v-model="openDateMenu"
                transition="scale-transition"
                :close-on-content-click="false"
              >
                <template v-slot:activator="{ props }">
                  <v-btn v-bind="props" rounded="lg" variant="flat">
                    <v-icon color="grey" size="14" class="pr-1"
                      >fas fa-calendar-alt</v-icon
                    >{{ selectedWeekRange }}
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item>
                    <datepicker
                      inline
                      :format="orgDateFormat"
                      v-model="weekEndDate"
                      style="min-width: 100%"
                      :disabled-dates="{
                        dates: disabledDatesArray,
                      }"
                      @input="onChangeWeekRange"
                      @changed-month="onChangeMonth"
                      @changed-year="onChangeYear"
                    ></datepicker>
                  </v-list-item>
                </v-list>
              </v-menu>
              <v-tooltip location="bottom">
                <template v-slot:activator="{ props }">
                  <v-icon
                    v-if="!isCurrentWeek"
                    v-bind="props"
                    color="primary"
                    size="13"
                    @click="$emit('fetch-current-week')"
                    >fas fa-redo-alt</v-icon
                  >
                </template>
                <div>This week</div>
              </v-tooltip>
            </v-card>
            <v-btn
              v-if="
                isListEmpty &&
                formAccess &&
                formAccess.add &&
                (!selfService || (selfService && allRejected))
              "
              variant="elevated"
              color="primary"
              class="ml-4 mt-1"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              @click="fetchEmployeesList()"
            >
              <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
              Add New
            </v-btn>
            <v-btn
              v-if="!isListEmpty"
              color="primary"
              variant="elevated"
              class="ml-4 mt-1"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              @click="$emit('reset-filter')"
            >
              Reset Filter/Search
            </v-btn>
            <v-btn
              v-if="isListEmpty"
              color="white"
              rounded="lg"
              class="ml-2 mt-1"
              :size="isMobileView ? 'small' : 'default'"
              @click="$emit('refetch-list')"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </template>
  </AppFetchErrorScreen>
  <div v-else>
    <div
      class="d-flex flex-wrap align-center my-2"
      :class="isMobileView ? 'flex-column' : ''"
      style="justify-content: space-between"
    >
      <div
        class="d-flex align-center flex-wrap"
        :class="isMobileView ? 'justify-center' : ''"
      >
        <v-card
          class="my-2"
          :size="isMobileView ? 'small' : 'default'"
          rounded="lg"
          style="width: 340px; background: white !important"
        >
          <v-icon
            color="primary"
            class="mx-2"
            size="17"
            @click="$emit('fetch-prev-week')"
          >
            fa fa-chevron-left
          </v-icon>
          <v-icon
            color="primary"
            size="17"
            class="mx-2"
            @click="$emit('fetch-next-week')"
          >
            fa fa-chevron-right
          </v-icon>
          <v-menu
            v-model="openDateMenu"
            transition="scale-transition"
            :close-on-content-click="false"
          >
            <template v-slot:activator="{ props }">
              <v-btn v-bind="props" variant="flat">
                <v-icon color="grey" size="14" class="pr-1"
                  >fas fa-calendar-alt</v-icon
                >{{ selectedWeekRange }}
              </v-btn>
            </template>
            <v-list>
              <v-list-item>
                <datepicker
                  inline
                  :format="orgDateFormat"
                  v-model="weekEndDate"
                  style="min-width: 100%"
                  :disabled-dates="{
                    dates: disabledDatesArray,
                  }"
                  @input="onChangeWeekRange"
                  @changed-month="onChangeMonth"
                  @changed-year="onChangeYear"
                  @opened="onOpenDatepicker"
                ></datepicker>
              </v-list-item>
            </v-list>
          </v-menu>
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-icon
                v-if="!isCurrentWeek"
                v-bind="props"
                color="primary"
                size="13"
                @click="$emit('fetch-current-week')"
                >fas fa-redo-alt</v-icon
              >
            </template>
            <div>This week</div>
          </v-tooltip>
        </v-card>
      </div>
      <div
        class="d-flex align-center"
        :class="isMobileView ? 'justify-center' : 'justify-end'"
      >
        <v-btn
          v-if="
            formAccess &&
            formAccess.add &&
            (!selfService || (selfService && allRejected))
          "
          variant="elevated"
          color="primary"
          class="mt-1"
          rounded="lg"
          :size="windowWidth <= 960 ? 'small' : 'default'"
          @click="fetchEmployeesList()"
        >
          <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
          Add New
        </v-btn>
        <v-btn
          color="transparent"
          class="ml-1 mt-1"
          variant="flat"
          size="small"
          @click="$emit('refetch-list')"
          ><v-icon color="grey">fas fa-redo-alt</v-icon></v-btn
        >
        <v-menu v-model="openMoreMenu" transition="scale-transition">
          <template v-slot:activator="{ props }">
            <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
              <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
              <v-icon v-else>fas fa-caret-up</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="action in moreActions"
              :key="action.key"
              @click="onMoreAction(action.key)"
            >
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <v-list-item-title
                    v-bind="props"
                    class="pa-3"
                    :class="{
                      'pink-lighten-5': isHovering,
                    }"
                    ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                    >{{ action.key }}</v-list-item-title
                  >
                </template>
              </v-hover>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </div>
    <v-data-table
      :headers="tableHeaders"
      :items="itemList"
      :items-per-page="50"
      :height="
        $store.getters.getTableHeightBasedOnScreenSize(250, itemList, true)
      "
      :items-per-page-options="[
        { value: 50, title: '50' },
        { value: 100, title: '100' },
        { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
      ]"
      fixed-header
      item-value="Employee_Id"
    >
      <template v-slot:[`header.Day1`]="{ column }">
        <div>{{ column.day }}</div>
        <div
          class="text-caption"
          :class="{
            'bg-orange text-white rounded-lg text-center': column.isCurrentDay,
          }"
          style="width: 60px"
        >
          {{ column.date }}
        </div>
      </template>
      <template v-slot:[`header.Day2`]="{ column }">
        <div>{{ column.day }}</div>
        <div
          class="text-caption"
          :class="{
            'bg-orange text-white rounded-lg text-center': column.isCurrentDay,
          }"
          style="width: 60px"
        >
          {{ column.date }}
        </div>
      </template>
      <template v-slot:[`header.Day3`]="{ column }">
        <div>{{ column.day }}</div>
        <div
          class="text-caption"
          :class="{
            'bg-orange text-white rounded-lg text-center': column.isCurrentDay,
          }"
          style="width: 60px"
        >
          {{ column.date }}
        </div>
      </template>
      <template v-slot:[`header.Day4`]="{ column }">
        <div>{{ column.day }}</div>
        <div
          class="text-caption"
          :class="{
            'bg-orange text-white rounded-lg text-center': column.isCurrentDay,
          }"
          style="width: 60px"
        >
          {{ column.date }}
        </div>
      </template>
      <template v-slot:[`header.Day5`]="{ column }">
        <div>{{ column.day }}</div>
        <div
          class="text-caption"
          :class="{
            'bg-orange text-white rounded-lg text-center': column.isCurrentDay,
          }"
          style="width: 60px"
        >
          {{ column.date }}
        </div>
      </template>
      <template v-slot:[`header.Day6`]="{ column }">
        <div>{{ column.day }}</div>
        <div
          class="text-caption"
          :class="{
            'bg-orange text-white rounded-lg text-center': column.isCurrentDay,
          }"
          style="width: 60px"
        >
          {{ column.date }}
        </div>
      </template>
      <template v-slot:[`header.Day7`]="{ column }">
        <div>{{ column.day }}</div>
        <div
          class="text-caption"
          :class="{
            'bg-orange text-white rounded-lg text-center': column.isCurrentDay,
          }"
          style="width: 60px"
        >
          {{ column.date }}
        </div>
      </template>
      <template v-slot:item="{ item }">
        <tr
          @click="onSelectItem(item)"
          class="data-table-tr bg-white cursor-pointer"
          :class="
            isMobileView ? 'v-data-table__mobile-table-row ma-0 mt-2' : ''
          "
        >
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Employee</div>
            <section style="max-width: 200px" class="text-truncate">
              <span class="text-primary text-body-2 font-weight-medium">
                <v-tooltip :text="item.Employee_Name" location="bottom">
                  <template v-slot:activator="{ props }">
                    <span
                      v-bind="
                        item.Employee_Name && item.Employee_Name.length > 20
                          ? props
                          : ''
                      "
                      ><span v-if="item.salutation">{{
                        item.salutation + ". "
                      }}</span
                      >{{ item.Employee_Name }}</span
                    >
                  </template>
                </v-tooltip>
                <v-tooltip :text="item.userDefinedEmpId" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      v-if="item.userDefinedEmpId"
                      v-bind="item.userDefinedEmpId.length > 20 ? props : ''"
                      class="text-grey"
                    >
                      {{ item.userDefinedEmpId }}
                    </div>
                  </template>
                </v-tooltip>
              </span>
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Day 1</div>
            <section class="text-body-2 text-primary">
              {{ item.Day1 ? decimalToHours(item.Day1) : "-" }}
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Day 2</div>
            <section class="text-body-2 text-primary">
              {{ item.Day2 ? decimalToHours(item.Day2) : "-" }}
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Day 3</div>
            <section class="text-body-2 text-primary">
              {{ item.Day3 ? decimalToHours(item.Day3) : "-" }}
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Day 4</div>
            <section class="text-body-2 text-primary">
              {{ item.Day4 ? decimalToHours(item.Day4) : "-" }}
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Day 5</div>
            <section class="text-body-2 text-primary">
              {{ item.Day5 ? decimalToHours(item.Day5) : "-" }}
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Day 6</div>
            <section class="text-body-2 text-primary">
              {{ item.Day6 ? decimalToHours(item.Day6) : "-" }}
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Day 7</div>
            <section class="text-body-2 text-primary">
              {{ item.Day7 ? decimalToHours(item.Day7) : "-" }}
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Status</div>
            <section
              class="text-body-2"
              :class="
                item.Approval_Status === 'Applied'
                  ? 'text-blue'
                  : item.Approval_Status === 'Returned'
                  ? 'text-amber'
                  : item.Approval_Status === 'Approved'
                  ? 'text-green'
                  : item.Approval_Status === 'Rejected'
                  ? 'text-red'
                  : 'text-primary'
              "
            >
              {{ item.Approval_Status ? item.Approval_Status : "-" }}
            </section>
          </td>
        </tr>
      </template>
    </v-data-table>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <EmployeesListModal
    v-if="showEmpListModal"
    :show-modal="showEmpListModal"
    :employeesList="allEmployeesList"
    :showFilterSearch="true"
    selectStrategy="single"
    :isApplyFilter="true"
    employeeIdKey="employeeId"
    userDefinedEmpIdKey="userDefinedEmpId"
    employeeNameKey="employeeName"
    deptNameKey="departmentName"
    designationKey="designationName"
    departmentIdKey="departmentId"
    designationIdKey="designationId"
    locationIdKey="locationId"
    empTypeIdKey="empTypeId"
    workScheduleIdKey="workSchedule"
    @on-select-employee="onSelectEmployee($event)"
    @close-modal="showEmpListModal = false"
  ></EmployeesListModal>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import { decimalToHours } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);

export default defineComponent({
  name: "ListTeamSheets",
  components: { Datepicker, EmployeesListModal, NotesCard },
  emits: [
    "on-select-item",
    "reset-filter",
    "add-employee",
    "refetch-list",
    "on-change-week-range",
    "fetch-prev-week",
    "fetch-next-week",
    "fetch-current-week",
    "add-new-timesheet",
  ],
  mixins: [FileExportMixin],

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    formAccess: {
      type: Object,
      required: true,
    },
    weekRange: {
      type: String,
      required: true,
    },
    originalList: {
      type: Array,
      required: true,
      default: () => [],
    },
    selfService: {
      type: Number,
      default: 0,
    },
  },

  data: () => ({
    // list
    itemList: [],
    openMoreMenu: false,
    openExportMenu: false,
    // date
    selectedWeekRange: "",
    openDateMenu: false,
    weekEndDate: "",
    disabledDatesArray: [],
    changedYear: "",
    // add
    allEmployeesList: [],
    isLoading: false,
    showEmpListModal: false,
  }),
  computed: {
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isListEmpty() {
      if (!this.originalList || this.originalList.length === 0) {
        return true;
      } else return false;
    },
    isSearchEmpty() {
      if (!this.itemList || this.itemList.length === 0) {
        return true;
      } else return false;
    },
    allRejected() {
      let rejectedRecords = this.itemList.filter(
        (el) => el.Approval_Status === "Rejected"
      );
      return rejectedRecords.length == this.itemList.length;
    },

    orgDateFormat() {
      return this.$store.state.orgDetails.orgDateFormat;
    },

    selectedStartEndDates() {
      const date = this.selectedWeekRange;
      const dateSplit = date.split(" to ");
      const startDate = dateSplit[0];
      const endDate = dateSplit[1];
      return { start: startDate, end: endDate };
    },
    tableDateDayHeader() {
      const start = moment(
        this.selectedStartEndDates.start,
        this.orgDateFormat
      );
      const end = moment(this.selectedStartEndDates.end, this.orgDateFormat);
      const secondHeader = [];
      const currentDate = moment();
      let dayIndex = 0;
      // Iterate through the date range
      for (
        let current = start.clone();
        current.isSameOrBefore(end);
        current.add(1, "days")
      ) {
        dayIndex += 1;
        const dateObject = {
          title: "Day " + dayIndex,
          key: "Day" + dayIndex,
          date: current.format("DD MMM"), // Formatted date in "DD MMM" format,
          day: current.format("ddd"), // Three-letter abbreviation for the day (e.g., Sun, Mon)
          isCurrentDay: current.isSame(currentDate, "day"), // Check if it's the current day
        };
        secondHeader.push(dateObject);
      }
      return secondHeader;
    },
    tableHeaders() {
      let firstHeader = [
        {
          title: "Employee",
          key: "Employee_Name",
        },
      ];
      let secondHeader = this.tableDateDayHeader;
      secondHeader.push({
        title: "Status",
        key: "Approval_Status",
      });
      return firstHeader.concat(secondHeader);
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    isCurrentWeek() {
      const startOfWeek = moment().clone().startOf("week");
      const endOfWeek = moment().clone().endOf("week");
      const rangeStart = moment(
        this.selectedStartEndDates.start,
        this.orgDateFormat
      );
      const rangeEnd = moment(
        this.selectedStartEndDates.end,
        this.orgDateFormat
      );

      return (
        rangeStart.isSameOrAfter(startOfWeek) &&
        rangeEnd.isSameOrBefore(endOfWeek)
      );
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.selectedWeekRange = this.weekRange;
    let weekEndDate =
      this.selectedWeekRange && this.selectedWeekRange.includes("to")
        ? this.selectedWeekRange.split(" to ")
        : this.selectedWeekRange;
    this.weekEndDate = weekEndDate =
      weekEndDate && weekEndDate.length >= 2
        ? weekEndDate[1]
        : weekEndDate && weekEndDate.length >= 1
        ? weekEndDate[0]
        : "";
    if (this.items.length) {
      this.itemList = this.items;
      this.onApplySearch();
    }
    this.findNonSaturdayDatesInMonth(
      moment(this.weekEndDate, this.orgDateFormat)
    );
  },

  watch: {
    items(val) {
      this.itemList = val;
      this.onApplySearch();
    },
    searchValue() {
      this.onApplySearch();
    },
    openDateMenu() {
      this.findNonSaturdayDatesInMonth(moment());
    },
  },

  methods: {
    decimalToHours,
    findNonSaturdayDatesInMonth(dateString) {
      const currentDate = moment(dateString);
      const month = currentDate.month(); // Get the month
      const year = currentDate.year(); // Get the year
      const daysInMonth = moment(
        `${year}-${month + 1}`,
        "YYYY-MM"
      ).daysInMonth(); // Get the number of days in the month

      const nonSaturdayDates = [];

      // Iterate over all dates in the month
      for (let i = 1; i <= daysInMonth; i++) {
        const date = moment(`${year}-${month + 1}-${i}`, "YYYY-MM-DD");
        // Check if the day is not Saturday (6 represents Saturday in Moment.js)
        if (date.day() !== 6) {
          nonSaturdayDates.push(new Date(date));
        }
      }
      this.disabledDatesArray = nonSaturdayDates;
    },
    onChangeWeekRange() {
      this.openDateMenu = false;
      let prevSunday = moment(this.weekEndDate)
        .subtract("6", "days")
        .format(this.orgDateFormat);
      this.selectedWeekRange =
        prevSunday +
        " to " +
        moment(this.weekEndDate).format(this.orgDateFormat);
      this.$emit("on-change-week-range", this.selectedWeekRange);
    },
    onChangeMonth(monthDate) {
      if (monthDate && Object.keys(monthDate).length > 0) {
        const monthName = monthDate.month;
        const monthNumber = moment().month(monthName).format("M");
        const formattedDate = moment(`${monthNumber}-01-${this.changedYear}`);
        this.findNonSaturdayDatesInMonth(formattedDate);
      } else {
        this.findNonSaturdayDatesInMonth(monthDate);
      }
    },
    onChangeYear(year) {
      this.changedYear = year.year;
    },
    onSelectItem(item) {
      this.$emit("on-select-item", item);
    },
    onApplySearch(itemValues) {
      let val = this.searchValue;
      let listItems = itemValues ? itemValues : this.items;
      if (!val) {
        this.itemList = listItems;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = listItems;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    async fetchEmployeesList() {
      if (this.selfService) {
        this.$emit("add-new-timesheet");
      } else {
        if (this.allEmployeesList.length === 0) {
          this.isLoading = true;
          await this.$store
            .dispatch("getEmployeesList", {
              formName: "Timesheets",
              formId: 23,
            })
            .then((empData) => {
              this.allEmployeesList = empData;
              this.allEmployeesList = this.allEmployeesList.filter(
                (el) => el.empStatus === "Active"
              );
              this.showEmpListModal = true;
              this.isLoading = false;
            })
            .catch((err) => {
              let snackbarData = {
                isOpen: true,
                message: "",
                type: "warning",
              };
              if (err === "error") {
                snackbarData.message =
                  "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
              } else {
                snackbarData.message = err;
              }
              this.showAlert(snackbarData);
              this.isLoading = false;
            });
        } else {
          this.showEmpListModal = true;
        }
      }
    },
    onSelectEmployee(employee) {
      let empObj = {
        Employee_Id: employee.employeeId,
        Employee_Name: employee.employeeName,
        userDefinedEmpId: employee.userDefinedEmpId,
      };
      this.$emit("add-employee", empObj);
      this.showEmpListModal = false;
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        mixpanel.track("Team-Timesheets-export-employees-click");
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportHeaders1 = [];
      for (let header of this.tableDateDayHeader) {
        if (header.title !== "Status") {
          exportHeaders1.push({
            header: header.day + " - " + header.date,
            key: header.key.toLowerCase(),
          });
        }
      }
      let timesheetsList = this.itemList;
      timesheetsList = timesheetsList.map((item) => ({
        ...item,
        day1: item.Day1 ? this.decimalToHours(item.Day1) : "",
        day2: item.Day2 ? this.decimalToHours(item.Day2) : "",
        day3: item.Day3 ? this.decimalToHours(item.Day3) : "",
        day4: item.Day4 ? this.decimalToHours(item.Day4) : "",
        day5: item.Day5 ? this.decimalToHours(item.Day5) : "",
        day6: item.Day6 ? this.decimalToHours(item.Day6) : "",
        day7: item.Day7 ? this.decimalToHours(item.Day7) : "",
      }));
      let exportHeaders = [
        {
          header: "Employee Id",
          key: "userDefinedEmpId",
        },
        {
          header: "Employee Name",
          key: "Employee_Name",
        },
      ];
      exportHeaders = exportHeaders.concat(exportHeaders1);
      exportHeaders.push({
        header: "Status",
        key: "Approval_Status",
      });
      let exportOptions = {
        fileExportData: timesheetsList,
        fileName: "Team Timesheets",
        sheetName: "Team Timesheets",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
      mixpanel.track("Team-Timesheets-employees-exported");
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
