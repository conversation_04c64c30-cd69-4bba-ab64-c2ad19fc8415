<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs()"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else>
    <div v-if="showEditForm">
      <EditContactInfo
        :contactDetails="contactDetails"
        :selectedCandidateId="selectedCandidateId"
        @close-edit-form="closeEditForm"
        @edit-updated="editUpdated"
      ></EditContactInfo>
    </div>

    <div v-else>
      <div class="d-flex justify-end mt-n2 mr-n2">
        <v-icon @click="refetchAPIs()" size="17" color="grey"
          >fas fa-redo-alt</v-icon
        >
      </div>
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="purple"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Permanent Address</span
            >
          </div>
          <div v-if="enableEdit">
            <v-btn @click="openEditDialog" color="primary" variant="text">
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
        </div>
        <v-row class="pa-4 ma-2 card-blue-background">
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[233].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.pApartment_Name) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Street 2</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.pStreet_Name) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[340]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[340]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.pBarangay) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">City</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.pCity) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[402]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.pState) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[341]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[341]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.pRegion) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Country</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.pCountry_Name) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[144].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.pPincode) }}
            </p>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="teal"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Current Address</span
            >
          </div>
        </div>
        <v-row class="pa-4 ma-2 card-blue-background">
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[233].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.cApartment_Name) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Street 2</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.cStreet_Name) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[342]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[342]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.cBarangay) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">City</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.cCity) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[402]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.cState) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[343]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[343]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.cRegion) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Country</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.cCountry_Name) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[144].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.cPincode) }}
            </p>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="lime"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Office Address</span
            >
          </div>
        </div>
        <v-row class="pa-4 ma-2 card-blue-background">
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[234].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.oApartment_Name) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Street 2</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.oStreet_Name) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[344]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[344]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.oBarangay) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">City</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.oCity) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[402]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.oState) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[345]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[345]?.Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.oRegion) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Country</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.oCountry_Name) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[145].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.oPincode) }}
            </p>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="deep-orange"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Contact Information</span
            >
          </div>
        </div>
        <v-row class="pa-4 ma-2 card-blue-background">
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Mobile Number</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                contactDetails.Mobile_No_Country_Code
                  ? contactDetails.Mobile_No_Country_Code
                  : ""
              }}
              {{ checkNullValue(contactDetails.Mobile_No) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">Telephone Number</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.Land_Line_No) }}
            </p>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <p
              class="text-subtitle-1 text-grey-darken-1"
              v-if="labelList[436]?.Field_Visiblity?.toLowerCase() === 'yes'"
            >
              {{ labelList[436].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.Fax_No) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[435]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[435].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.Emergency_Contact_Name) }}
            </p>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[437]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[437].Field_Alias }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(contactDetails.Emergency_Contact_Relation) }}
            </p>
          </v-col>
        </v-row>
      </v-card-text>
    </div>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const EditContactInfo = defineAsyncComponent(() =>
  import("./EditContactInfo.vue")
);
import { RETRIEVE_EMP_CONTACT_INFO } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import { checkNullValue } from "@/helper.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "ContactInfo",
  components: {
    EditContactInfo,
  },
  props: {
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  emits: ["details-retrieved", "details-updated"],
  data() {
    return {
      clickedSection: "",
      contactDetails: {
        pApartment_Name: "",
        pStreet_Name: "",
        pCity: "",
        pState: "",
        pCountry: null,
        pCountry_Name: "",
        pPincode: "",
        cApartment_Name: "",
        cStreet_Name: "",
        cCity: "",
        cState: "",
        cCountry: null,
        cCountry_Name: "",
        cPincode: "",
        oApartment_Name: "",
        oStreet_Name: "",
        oCity: "",
        oState: "",
        oCountry: null,
        oCountry_Name: "",
        oPincode: "",
        Mobile_No_Country_Code: "",
        Mobile_No: "",
        Land_Line_No: "",
        Work_No: "",
        Fax_No: "",
        Use_Location_Address: 1,
      },
      showEditForm: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
    };
  },

  computed: {
    enableEdit() {
      return this.formAccess && this.formAccess.update;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.getContactDetails("initial");
  },

  watch: {
    showEditForm(val) {
      let editFormOpened = this.$store.state.onboarding.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "onboarding/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  methods: {
    checkNullValue,
    editUpdated() {
      this.showEditForm = false;
      this.$emit("details-updated");
      this.getContactDetails("update");
    },
    openEditDialog() {
      this.showEditForm = true;
      mixpanel.track("Onboarded-candidate-contact-edit-opened");
    },
    closeEditForm() {
      this.showEditForm = false;
      mixpanel.track("Onboarded-candidate-contact-edit-closed");
    },
    refetchAPIs() {
      this.isErrorInList = false;
      mixpanel.track("Onboarded-candidate-contact-refetch");
      this.getContactDetails();
    },
    getContactDetails(type) {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_CONTACT_INFO,
          client: "apolloClientV",
          variables: {
            candidateId: vm.selectedCandidateId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("Onboarded-candidate-contact-fetch-success");
          if (response && response.data && response.data.retrieveContactInfo) {
            const { contactDetails } = response.data.retrieveContactInfo;
            let contactData = contactDetails ? JSON.parse(contactDetails) : [];
            vm.contactDetails =
              contactData.length > 0 ? contactData[0] : vm.contactDetails;
            if (vm.contactDetails["Use_Location_Address"]) {
              vm.contactDetails["oApartment_Name"] =
                vm.contactDetails["Street1"];
              vm.contactDetails["oStreet_Name"] = vm.contactDetails["Street2"];
              vm.contactDetails["oCity"] = vm.contactDetails["City_Name"];
              vm.contactDetails["oState"] = vm.contactDetails["State_Name"];
              vm.contactDetails["oCountry_Name"] =
                vm.contactDetails["Country_Name"];
              vm.contactDetails["oCountry"] = vm.contactDetails["Country_Code"];
              vm.contactDetails["oPincode"] = vm.contactDetails["Pincode"];
            }
            vm.$emit("details-retrieved", [type, vm.contactDetails]);
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("Onboarded-candidate-contact-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "contact details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
.text-subtitle-1 font-weight-regular {
  color: #222121 !important;
  font-size: 15px;
  margin: 12px 0px;
  overflow-wrap: break-word;
  max-width: 360px;
}
.bottom-navigation :deep() .v-bottom-navigation__content {
  background-color: white;
  justify-content: flex-start !important;
  align-items: center !important;
}
.bottom-navigation :deep() .v-bottom-navigation__content > .v-btn {
  font-size: inherit;
  height: 45px;
  max-width: 120px;
  min-width: 100px;
  font-size: 1.2rem;
  text-transform: none;
  transition: inherit;
  width: auto;
  border-radius: 0;
  margin-left: 20px !important;
}
</style>
