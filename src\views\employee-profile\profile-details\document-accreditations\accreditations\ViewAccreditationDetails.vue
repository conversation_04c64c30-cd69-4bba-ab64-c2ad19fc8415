<template>
  <div
    v-if="accreditationDetails && accreditationDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No {{ labelList[229]?.Field_Alias.toLowerCase() }} have been submitted
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in accreditationDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:550px; max-width:550px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex justify-start">
                <v-tooltip
                  :text="data.Accreditation_Category"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="data.Accreditation_Category ? props : ''"
                    >
                      {{ checkNullValue(data.Accreditation_Category) }}
                    </div>
                  </template>
                </v-tooltip>
                <div
                  v-if="data.Dependent_Id"
                  class="text-primary font-weight-bold text-h6 text-truncate"
                >
                  ({{
                    (data.Dependent_First_Name
                      ? data.Dependent_First_Name
                      : "-") +
                    " " +
                    (data.Dependent_Last_Name
                      ? data.Dependent_Last_Name
                      : "-") +
                    "-" +
                    (data.Relationship ? data.Relationship : "-")
                  }})
                </div>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n3">
        <span
          :style="!isMobileView ? 'width:60%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">
                {{ labelList[229].Field_Alias }}
              </b>
              <span class="py-2">{{
                checkNullValue(data.Accreditation_Type)
              }}</span>
            </div>
            <div
              v-if="labelList[245]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[245]?.Field_Alias }}
              </b>
              <span class="py-2">{{ formatDate(data.Received_Date) }}</span>
            </div>
            <div
              v-if="labelList[391]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[391]?.Field_Alias }}
              </b>
              <span class="py-2">{{
                data.Exam_Rating || data.Exam_Rating === 0
                  ? data.Exam_Rating
                  : "-"
              }}</span>
            </div>
            <div
              v-if="labelList[392]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[392]?.Field_Alias }}
              </b>
              <span class="py-2">{{
                checkNullValue(data.Exam_Date_Year)
              }}</span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Status </b>
              <span
                class="py-2"
                :class="
                  checkStatus(data.Expiry_Date) === 'Expired'
                    ? 'text-red'
                    : 'text-green'
                "
                >{{ checkStatus(data.Expiry_Date) }}</span
              >
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:40%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              v-if="labelList[390]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[390]?.Field_Alias }}
              </b>
              <span class="py-2">{{ checkNullValue(data.Identifier) }}</span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Expiry Date </b>
              <span class="py-2">{{ formatDate(data.Expiry_Date) }}</span>
            </div>
            <div
              v-if="labelList[393]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[393]?.Field_Alias }}
              </b>
              <span class="py-2">{{
                checkNullValue(data.Exam_Date_Month)
              }}</span>
            </div>
            <div
              v-if="data.File_Name"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span
                style="text-decoration: underline"
                @click="retrieveAccreditations(data.File_Name)"
                class="text-green cursor-pointer"
              >
                View Document</span
              >
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employee Accreditation"
    fileRetrieveType="accreditation"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

export default {
  name: "ViewAccreditationDetails",
  components: { FilePreviewModal, ActionMenu },

  props: {
    accreditationDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      retrievedFileName: "",
      openModal: false,
      havingAccess: {},
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    checkStatus() {
      return (expDate) => {
        let currentDate = moment().format("YYYY-MM-DD");
        let isBeforeCurrentDate = moment(expDate).isBefore(currentDate);
        return isBeforeCurrentDate ? "Expired" : "Active";
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    //using the generateRandomColor function of helper.js file
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.accreditationDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    retrieveAccreditations(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
  },
};
</script>
