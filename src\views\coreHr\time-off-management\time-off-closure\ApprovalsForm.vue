<template>
  <v-row>
    <v-col cols="12">
      <v-card width="100%" height="100%" class="pa-5">
        <div>
          <v-icon color="primary" @click="goBack()">fas fa-arrow-left</v-icon>
        </div>
        <div class="ml-8 my-2">
          <div class="text-h6 text-primary">Pending Approvals</div>
          <v-row class="mt-2">
            <v-col
              v-for="item in approvalsList"
              :key="item"
              xl="3"
              lg="3"
              md="4"
              sm="6"
              cols="12"
            >
              <v-hover>
                <template #default="{ hover }">
                  <v-card class="imgHover pa-2 rounded-lg" height="100%">
                    <v-card-title
                      :class="{ 'common-box-shadow': hover }"
                      class="d-flex align-center"
                      min-height="65"
                    >
                      <v-avatar
                        :color="
                          item.pending > 0 ? 'red-lighten-1' : 'green-lighten-1'
                        "
                        size="30"
                      >
                        <span v-if="item.pending > 0" class="text-white">{{
                          item.pending
                        }}</span>
                        <v-icon v-else color="white" size="17" class="mt-1"
                          >fas fa-check</v-icon
                        >
                      </v-avatar>

                      <v-tooltip
                        :text="item?.name"
                        location="top"
                        max-width="500"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate ml-3"
                            v-bind="props"
                          >
                            {{ item?.name }}
                          </div>
                        </template>
                      </v-tooltip>
                      <v-spacer />
                      <v-icon
                        class="fas fa-external-link-alt"
                        size="17"
                        color="primary"
                        @click="openUrl(item.url)"
                      >
                        fas fa-external-link-alt
                      </v-icon>
                    </v-card-title>
                  </v-card>
                </template>
              </v-hover>
            </v-col>
          </v-row>

          <div class="text-h6 mt-10 text-primary">Closure Processes</div>
          <v-row class="mt-2 mb-8">
            <v-col
              v-for="item in closureList"
              :key="item"
              xl="3"
              lg="3"
              md="4"
              sm="6"
              cols="12"
            >
              <v-hover>
                <template #default="{ hover }">
                  <v-card class="imgHover pa-2 rounded-lg" height="100%">
                    <v-card-title
                      :class="{ 'common-box-shadow': hover }"
                      class="d-flex align-center"
                      min-height="65"
                    >
                      <v-avatar
                        :color="
                          item.pending > 0 ? 'red-lighten-1' : 'green-lighten-1'
                        "
                        size="30"
                      >
                        <span v-if="item.pending > 0" class="text-white">{{
                          item.pending
                        }}</span>
                        <v-icon v-else color="white" size="17" class="mt-1"
                          >fas fa-check</v-icon
                        >
                      </v-avatar>

                      <v-tooltip
                        :text="item?.name"
                        location="top"
                        max-width="500"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate ml-3"
                            v-bind="props"
                          >
                            {{ item?.name }}
                          </div>
                        </template>
                      </v-tooltip>
                      <v-spacer />
                      <v-icon
                        class="fas fa-external-link-alt"
                        size="17"
                        color="primary"
                        @click="openUrl(item.url)"
                      >
                        fas fa-external-link-alt
                      </v-icon>
                    </v-card-title>
                  </v-card>
                </template>
              </v-hover>
            </v-col>
          </v-row>
        </div>
      </v-card>
    </v-col>
  </v-row>
</template>
<script>
export default {
  name: "ApprovalsForm",
  props: {
    approvalsList: {
      type: Array,
      required: true,
    },
    closureList: {
      type: Array,
      required: true,
    },
  },
  emits: ["close"],
  methods: {
    openUrl(URL) {
      window.open(URL, "_blank");
    },
    goBack() {
      this.$emit("close");
    },
  },
};
</script>
<style scoped>
/* Zoom In #1 */
.imgHover img {
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
}
.imgHover:hover img {
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
}
</style>
