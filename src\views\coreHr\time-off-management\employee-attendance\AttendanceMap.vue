<template>
  <v-dialog
    v-model="openMapDialogs"
    @click:outside="closeModal"
    width="100%"
    height="100%"
  >
    <v-card>
      <!-- Top Section: Date -->
      <v-card-title class="d-flex justify-space-between align-center bg-hover">
        <span class="text-h6 font-weight-bold">
          Map View -
          {{ checkNullValue(selectedLogItem.Display_Punching_Date) }} -
          {{ checkNullValue(employeeData) }}
        </span>
        <v-icon
          color="primary"
          class="fas fa-times"
          size="20"
          @click="closeModal()"
        ></v-icon>
      </v-card-title>

      <!-- Main Section: Geo Coordinates and Map -->
      <v-card-text>
        <v-row class="d-flex">
          <!-- Left Section: Geo Coordinates -->
          <v-col cols="12" md="2">
            <v-card
              v-for="(logItem, index) in selectedLogItem.details"
              :key="index"
              class="pa-3 mb-3"
            >
              <strong class="text-subtitle-1 mb-2 d-block"
                >Check-in and Check-out</strong
              >
              <div class="mb-3">
                <v-icon
                  color="success mr-1"
                  class="fas fa-arrow-up"
                  size="15"
                  style="transform: rotate(45deg)"
                ></v-icon>
                {{
                  checkNullValue(
                    getHourAndMinutes(logItem.Display_PunchIn_Time)
                  )
                }}
                <div>
                  <a
                    href="#"
                    class="text-primary font-weight-bold"
                    :class="
                      logItem.Attendance_Id === selectedAttendanceId &&
                      selectedType === 'checkin'
                        ? 'text-hover font-weight-bold'
                        : ''
                    "
                    @click.prevent="
                      handleClick(
                        logItem.Checkin_Latitude,
                        logItem.Checkin_Longitude,
                        logItem.Attendance_Id,
                        'checkin'
                      )
                    "
                  >
                    {{
                      emptyScenarios(
                        logItem.Checkin_Latitude,
                        logItem.Checkin_Longitude
                      )
                    }}
                  </a>
                  <p>
                    {{ checkNullValue(logItem.Checkin_Address) }}
                  </p>
                </div>
              </div>
              <div>
                <v-icon
                  v-if="logItem.Actual_PunchOut_Time"
                  color="red mr-1"
                  class="fas fa-arrow-down"
                  size="15"
                  style="transform: rotate(45deg)"
                ></v-icon>
                {{
                  checkNullValue(
                    getHourAndMinutes(logItem.Display_PunchOut_Time)
                  )
                }}
                <div>
                  <a
                    href="#"
                    class="text-primary font-weight-bold"
                    :class="
                      logItem.Attendance_Id === selectedAttendanceId &&
                      selectedType === 'checkout'
                        ? 'text-hover font-weight-bold'
                        : ''
                    "
                    @click.prevent="
                      handleClick(
                        logItem.Checkout_Latitude,
                        logItem.Checkout_Longitude,
                        logItem.Attendance_Id,
                        'checkout'
                      )
                    "
                    >{{
                      emptyScenarios(
                        logItem.Checkout_Latitude,
                        logItem.Checkout_Longitude
                      )
                    }}
                  </a>
                  <p>
                    {{ checkNullValue(logItem.Checkout_Address) }}
                  </p>
                </div>
              </div>
            </v-card>
          </v-col>

          <!-- Right Section -->
          <v-col cols="12" md="10">
            <v-card>
              <GoogleMap
                style="width: 100%; height: 700px"
                :zoom="18"
                :center="{
                  lat: parseFloat(selectedLat),
                  lng: parseFloat(selectedLong),
                }"
              >
                <MarkerCluster>
                  <Marker
                    v-for="(logItem, index) in selectedLogItem.details"
                    :key="index"
                    :options="{
                      position: {
                        lat: parseFloat(logItem?.Checkin_Latitude),
                        lng: parseFloat(logItem?.Checkin_Longitude),
                      },
                      title: `Marker ${index + 1}`,
                    }"
                    @click="onMarkerClick($event)"
                  />
                  <Marker
                    v-for="(logItem, index) in selectedLogItem.details"
                    :key="index"
                    :options="{
                      position: {
                        lat: parseFloat(logItem?.Checkout_Latitude),
                        lng: parseFloat(logItem?.Checkout_Longitude),
                      },
                      title: `Marker ${index + 1}`,
                    }"
                    @click="onMarkerClick()"
                  />
                </MarkerCluster>
              </GoogleMap>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import { GoogleMap, Marker } from "vue3-google-map";
import { checkNullValue } from "@/helper";
export default {
  name: "AttendanceMap",
  components: {
    GoogleMap,
    Marker,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    selectedEmployee: {
      type: Number,
      default: 0,
    },
    employeeData: {
      type: String,
      required: true,
    },
    openMapDialog: {
      type: Boolean,
      default: false,
    },
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
    selectedLogItem: {
      type: Object,
      required: true,
    },
    selectedProperties: {
      type: Object,
      required: false,
    },
  },
  data() {
    return {
      checkInMapDialog: false,
      checkOutMapDialog: false,
      selectedMarkerPosition: null,
      openMapDialogs: false,
      selectedLat: null,
      selectedLong: null,
      selectedAttendanceId: 0,
      selectedType: "",
    };
  },
  mounted() {
    this.openMapDialogs = this.openMapDialog;
    if (this.selectedProperties) {
      this.selectedAttendanceId = this.selectedProperties?.Attendance_Id;
      this.selectedLat = this.selectedProperties.lat;
      this.selectedLong = this.selectedProperties.long;
      this.selectedType = this.selectedProperties.type;
    } else if (
      this.selectedLogItem &&
      this.selectedLogItem.details?.length &&
      this.selectedLogItem.details[0].Checkin_Latitude?.length &&
      this.selectedLogItem.details[0].Checkin_Longitude?.length
    ) {
      this.selectedLat = this.selectedLogItem.details[0].Checkin_Latitude;
      this.selectedLong = this.selectedLogItem.details[0].Checkin_Longitude;
      this.selectedAttendanceId = this.selectedLogItem.details[0].Attendance_Id;
      this.selectedType = "checkin";
    } else {
      this.selectedLat = 0;
      this.selectedLong = 0;
      this.selectedAttendanceId = 0;
      this.selectedType = "";
    }
  },
  methods: {
    checkNullValue,
    handleClick(lat, lng, selectedAttendanceId, selectedType) {
      this.selectedLong = lng;
      this.selectedLat = lat;
      this.selectedAttendanceId = selectedAttendanceId;
      this.selectedType = selectedType;
    },
    getHourAndMinutes(timeString) {
      // Ensure the timeString is valid
      if (!timeString || typeof timeString !== "string") return null;

      // Split the timeString by ':' and return the first two parts
      const [hours, minutes] = timeString.split(":");
      return `${hours}:${minutes}`;
    },

    emptyScenarios(lat, lng) {
      if (lat.length && lng.length) {
        return lat + "-" + lng;
      }
      return "No co-ordinates found";
    },
    openMap(type) {
      if (type === "checkin") {
        this.checkInMapDialog = true;
      } else if (type === "checkout") {
        this.checkOutMapDialog = true;
      }
    },
    onMarkerClick(markerPosition) {
      this.selectedMarkerPosition = markerPosition;
    },
    closeModal() {
      this.$emit("close-map-modal"); // Emit the close event for the parent to handle
    },
  },
};
</script>
<style scoped>
.text-muted {
  color: #999999;
}

.text-subtitle-1 {
  font-size: 1.1rem;
  font-weight: 500;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.pa-3 {
  padding: 1rem !important;
}
</style>
