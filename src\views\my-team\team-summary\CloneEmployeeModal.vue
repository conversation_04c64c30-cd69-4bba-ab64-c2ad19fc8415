<template>
  <v-dialog
    v-model="openCloneModal"
    width="700px"
    @click:outside="closeModal()"
  >
    <v-card class="rounded-lg">
      <div class="d-flex justify-end">
        <v-icon
          color="primary"
          class="pr-4 pt-4 font-weight-bold"
          @click="closeModal()"
          >fas fa-times</v-icon
        >
      </div>
      <v-card-title class="mt-n4">
        <div class="text-primary text-center font-weight-medium">
          Clone Employee
        </div>
      </v-card-title>
      <v-card-text class="d-flex align-center justify-center flex-column">
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="cloneEmployeeForm" class="pa-2">
          <v-row>
            <v-col cols="12" md="6">
              <p class="text-subtitle-1 text-grey-darken-1">Employee Name</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ selectedEmployee.employeeName }}
              </p>
            </v-col>
            <v-col cols="12" md="6">
              <p class="text-subtitle-1 text-grey-darken-1">Date of Birth</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ formatDate(selectedEmployee.dob) }}
              </p>
            </v-col>
            <v-col
              v-if="
                fieldForce &&
                idGenerationconfig &&
                idGenerationCoverage &&
                idGenerationCoverage.toLowerCase() === 'serviceprovider'
              "
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="serviceProviders"
                :label="getCustomFieldName(115, 'Service Provider')"
                :isRequired="true"
                :rules="[
                  required(
                    getCustomFieldName(115, 'Service Provider'),
                    cloneEmployeeDetails.Service_Provider_Id
                  ),
                ]"
                :isLoading="dropdownListFetching"
                :itemSelected="cloneEmployeeDetails.Service_Provider_Id"
                itemValue="Service_Provider_Id"
                itemTitle="Service_Provider_Name"
                @selected-item="
                  onChangeCustomSelectField($event, 'Service_Provider_Id')
                "
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="cloneEmployeeDetails.User_Defined_EmpId"
                :rules="[
                  alreadyExistErrMsg['User_Defined_EmpId'],
                  required(
                    'Employee Id',
                    cloneEmployeeDetails.User_Defined_EmpId
                  ),
                  validateWithRulesAndReturnMessages(
                    cloneEmployeeDetails.User_Defined_EmpId,
                    'userDefinedEmpId',
                    'Employee Id'
                  ),
                ]"
                variant="solo"
                :loading="fetchingMaxEmpId"
                ref="User_Defined_EmpId"
                @update:model-value="onChangeFields('', 'User_Defined_EmpId')"
                @change="
                  validateFieldAlreadyExist('User_Defined_EmpId', 'Employee Id')
                "
              >
                <template v-slot:label>
                  Employee Id<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="cloneEmployeeDetails.External_EmpId"
                label="Biometric Integration Id"
                :rules="
                  cloneEmployeeDetails.External_EmpId
                    ? [
                        alreadyExistErrMsg['External_EmpId'],
                        validateWithRulesAndReturnMessages(
                          cloneEmployeeDetails.External_EmpId,
                          'biometricIntegraionId',
                          'Biometric Integration Id'
                        ),
                      ]
                    : []
                "
                ref="External_EmpId"
                variant="solo"
                @update:model-value="onChangeFields('', 'External_EmpId')"
                @change="
                  validateFieldAlreadyExist(
                    'External_EmpId',
                    'Biometric Integration Id'
                  )
                "
              >
              </v-text-field>
              <div
                v-if="isValidExternalEmpId"
                class="text-caption mt-n4 ml-2"
                style="color: #f3012d; line-height: 1.2"
              >
                Please enter the biometric integration id for biometric data
                processing.
              </div>
            </v-col>
            <v-col cols="12" md="6">
              <p class="custom-label mt-n3 mb-n2">
                Date of Join<span style="color: red">*</span>
              </p>
              <section class="text-body-2 mt-2">
                <datepicker
                  :format="orgDateFormat"
                  v-model="cloneEmployeeDetails.Date_Of_Join"
                  class="custom-date-picker"
                  style="min-width: 100% !important"
                  :disabled-dates="{
                    to: new Date(selectedEmpDobDate),
                  }"
                  @input="onChangeFields('dateOfJoinErrorMsg')"
                ></datepicker>
                <span
                  v-if="
                    dateOfJoinErrorMsg && !cloneEmployeeDetails.Date_Of_Join
                  "
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ dateOfJoinErrorMsg }}
                </span>
              </section>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="roles"
                label="Role"
                :isRequired="true"
                :rules="[required('Role', cloneEmployeeDetails.Roles_Id)]"
                :isLoading="dropdownListFetching"
                :itemSelected="cloneEmployeeDetails.Roles_Id"
                itemValue="Roles_Id"
                itemTitle="Roles_Name"
                @selected-item="onChangeCustomSelectField($event, 'Roles_Id')"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="departments"
                label="Department"
                :isRequired="true"
                :rules="[
                  required('Department', cloneEmployeeDetails.Department_Id),
                ]"
                :isLoading="dropdownListFetching"
                :itemSelected="cloneEmployeeDetails.Department_Id"
                itemValue="Department_Id"
                itemTitle="Department_Name"
                @selected-item="
                  onChangeCustomSelectField($event, 'Department_Id')
                "
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="designations"
                label="Designation"
                :isRequired="true"
                :rules="[
                  required('Designation', cloneEmployeeDetails.Designation_Id),
                ]"
                :isLoading="designationListLoading"
                :itemSelected="cloneEmployeeDetails.Designation_Id"
                itemValue="Designation_Id"
                itemTitle="Designation_Name"
                placeholder="Type minimum 3 characters to list"
                :isAutoComplete="true"
                @selected-item="
                  onChangeCustomSelectField($event, 'Designation_Id')
                "
                @update-search-value="callDesignationList($event)"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[424]?.Field_Visiblity.toLowerCase() === 'yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                ref="selectedServiceProvider"
                v-model="selectedJobRoles"
                :items="jobRolesList"
                :isLoading="jobRolesListLoading"
                item-title="Job_Role"
                item-value="Job_Role_Id"
                :label="labelList[424].Field_Alias"
                :isAutoComplete="true"
                :isRequired="
                  labelList[424].Mandatory_Field.toLowerCase() === 'yes'
                "
                :rules="[
                  labelList[424].Mandatory_Field.toLowerCase() == 'yes'
                    ? selectedJobRoles
                      ? required(
                          `${labelList[424].Field_Alias}`,
                          selectedJobRoles[0]
                        )
                      : required(
                          `${labelList[424].Field_Alias}`,
                          selectedJobRoles
                        )
                    : true,
                ]"
                variant="solo"
                :selectProperties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                  clearable: true,
                }"
                :itemSelected="selectedJobRoles"
                @selected-item="selectedJobRoles = $event"
                @update:model-value="onChangeFields"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="employeeTypes"
                label="Employee Type"
                :isLoading="dropdownListFetching"
                :itemSelected="cloneEmployeeDetails.EmpType_Id"
                itemValue="EmpType_Id"
                itemTitle="Employee_Type"
                :isRequired="true"
                :rules="[
                  required('Employee Type', cloneEmployeeDetails.EmpType_Id),
                ]"
                @selected-item="onChangeCustomSelectField($event, 'EmpType_Id')"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="locations"
                label="Location"
                :isRequired="true"
                :rules="[
                  required('Location', cloneEmployeeDetails.Location_Id),
                ]"
                :isLoading="dropdownListFetching"
                :itemSelected="cloneEmployeeDetails.Location_Id"
                itemValue="Location_Id"
                itemTitle="Location_Name"
                @selected-item="
                  onChangeCustomSelectField($event, 'Location_Id')
                "
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="workSchedules"
                label="Work Schedule"
                :isRequired="true"
                :rules="[
                  required('Work Schedule', cloneEmployeeDetails.Work_Schedule),
                ]"
                :isLoading="dropdownListFetching"
                :itemSelected="cloneEmployeeDetails.Work_Schedule"
                itemValue="WorkSchedule_Id"
                itemTitle="Title"
                @selected-item="
                  onChangeCustomSelectField($event, 'Work_Schedule')
                "
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="
                fieldForce &&
                (!idGenerationconfig ||
                  idGenerationCoverage?.toLowerCase() !== 'serviceprovider')
              "
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="serviceProviders"
                :label="getCustomFieldName(115, 'Service Provider')"
                :isRequired="true"
                :rules="[
                  required(
                    getCustomFieldName(115, 'Service Provider'),
                    cloneEmployeeDetails.Service_Provider_Id
                  ),
                ]"
                :isLoading="dropdownListFetching"
                :itemSelected="cloneEmployeeDetails.Service_Provider_Id"
                itemValue="Service_Provider_Id"
                itemTitle="Service_Provider_Name"
                @selected-item="
                  onChangeCustomSelectField($event, 'Service_Provider_Id')
                "
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6">
              <p class="custom-label mt-n3 mb-n2">
                Probation Date<span style="color: red">*</span>
              </p>
              <section
                class="text-body-2 mt-2"
                :class="
                  cloneEmployeeDetails.Date_Of_Join &&
                  cloneEmployeeDetails.Designation_Id
                    ? ''
                    : 'cursor-not-allow'
                "
              >
                <datepicker
                  :format="orgDateFormat"
                  v-model="cloneEmployeeDetails.Probation_Date"
                  class="custom-date-picker"
                  style="min-width: 100% !important"
                  :disabled="
                    !cloneEmployeeDetails.Date_Of_Join ||
                    !cloneEmployeeDetails.Designation_Id
                  "
                  :disabled-dates="{
                    to: new Date(dateOfJoinDate),
                  }"
                  :style="
                    cloneEmployeeDetails.Date_Of_Join &&
                    cloneEmployeeDetails.Designation_Id
                      ? ''
                      : 'pointer-events: none'
                  "
                  @input="onChangeFields('probationDateErrorMsg')"
                ></datepicker>
                <span
                  v-if="
                    probationDateErrorMsg &&
                    !cloneEmployeeDetails.Probation_Date
                  "
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ probationDateErrorMsg }}
                </span>
              </section>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="businessUnits"
                label="Business Unit / Cost Center"
                :isLoading="businessUnitListFetching"
                :itemSelected="cloneEmployeeDetails.Business_Unit_Id"
                itemValue="businessUnitId"
                itemTitle="businessUnit"
                @selected-item="
                  onChangeCustomSelectField($event, 'Business_Unit_Id', 6)
                "
              ></CustomSelect>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <div class="text-center pb-10">
        <v-btn
          rounded="lg"
          variant="outlined"
          class="mr-2"
          @click="closeModal()"
        >
          Cancel
        </v-btn>
        <v-btn rounded="lg" color="primary" @click="validateEditForm()">
          Submit
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
import {
  CLONE_EMPLOYEE,
  RETRIEVE_MAX_EMP_ID,
  VALIDATE_FIELD_AVAILABILITY,
  RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION,
} from "@/graphql/employee-profile/profileQueries.js";
import { LIST_BUSINESS_UNIT } from "@/graphql/dropDownQueries";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import {
  getErrorCodesAndMessagesWithValidation,
  getCustomFieldName,
} from "@/helper";
import { LIST_JOB_ROLES } from "@/graphql/onboarding/individualQueries.js";
import {
  RETRIEVE_EMPLOYEE_NUMBER_SERIES,
  GET_MAX_EMPLOYEE_ID,
} from "@/graphql/settings/core-hr/employeeNumberSeries.js";

export default {
  name: "CloneEmployeeModal",

  mixins: [validationRules],

  components: {
    Datepicker,
    CustomSelect,
  },

  props: {
    openModal: {
      type: Boolean,
      required: true,
    },
    selectedEmployee: {
      type: Object,
      required: true,
    },
  },

  emits: ["employee-cloned", "close-modal", "employee-clone-failed"],

  data() {
    return {
      // form
      isMounted: false,
      openCloneModal: false,
      // error
      dateOfJoinErrorMsg: "",
      probationDateErrorMsg: "",
      alreadyExistErrMsg: {
        External_EmpId: true,
        User_Defined_EmpId: true,
      },
      // list
      managers: [],
      locations: [],
      workSchedules: [],
      employeeTypes: [],
      designations: [],
      departments: [],
      serviceProviders: [],
      businessUnits: [],
      roles: [],
      // loading
      dropdownListFetching: false,
      businessUnitListFetching: false,
      designationListLoading: false,
      fetchingMaxEmpId: false,
      isLoading: false,
      // edit
      isFormDirty: false,
      validationMessages: [],
      showValidationAlert: false,
      fieldForce: 0,
      cloneEmployeeDetails: {
        User_Defined_EmpId: "",
        External_EmpId: "",
        Employee_Id: "",
        Date_Of_Join: "",
        Probation_Date: "",
        Department_Id: null,
        Designation_Id: null,
        EmpType_Id: null,
        Location_Id: null,
        Work_Schedule: null,
        Service_Provider_Id: null,
        Business_Unit_Id: null,
        Roles_Id: null,
      },
      jobRolesListLoading: false,
      jobRolesList: [],
      selectedJobRoles: [],
      idGenerationconfig: false,
      idGenerationCoverage: "",
      settingsArray: [],
    };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    selectedEmpDobDate() {
      if (this.selectedEmployee.dob) {
        return new Date(this.selectedEmployee.dob)
          .toISOString()
          .substring(0, 10);
      } else return null;
    },
    dateOfJoinDate() {
      if (this.cloneEmployeeDetails.Date_Of_Join) {
        const today = new Date(this.cloneEmployeeDetails.Date_Of_Join)
          .toISOString()
          .substring(0, 10);
        return today;
      }
      return null;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    isValidExternalEmpId() {
      if (this.cloneEmployeeDetails.External_EmpId) {
        let field = this.$refs.External_EmpId;
        if (field && field.rules && field.rules.length > 0) {
          return field.rules.every((value) => value === true);
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  watch: {
    openModal(val) {
      this.openCloneModal = val;
    },
    "cloneEmployeeDetails.Designation_Id": {
      handler(val) {
        if (val) {
          this.fetchJobRoles(val);
        }
      },
      immediate: true,
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.openCloneModal = this.openModal;
    this.retrieveDropdownDetails();
    this.retrieveBusinessUnit();
    this.getCoverageSetting();
  },

  methods: {
    getCustomFieldName,
    closeModal() {
      this.openCloneModal = false;
      mixpanel.track("MyTeam-clone-form-closed");
      this.$emit("close-modal");
    },
    getCoverageSetting() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMPLOYEE_NUMBER_SERIES,
          variables: { formId: 243 },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listEmployeeIdPrefixSettings) {
            vm.idGenerationconfig =
              response.data.listEmployeeIdPrefixSettings.config?.isEnabled ||
              false;
            vm.idGenerationCoverage =
              response.data.listEmployeeIdPrefixSettings.config?.configLevel ||
              "";
            vm.settingsArray =
              response.data.listEmployeeIdPrefixSettings.employeeIdPrefixSettings;
            if (
              vm.idGenerationconfig &&
              vm.idGenerationCoverage?.toLowerCase() !== "serviceprovider"
            ) {
              vm.getEmployeeId();
            } else if (!vm.idGenerationconfig) {
              vm.retrieveEmpMaxId();
            }
          } else {
            vm.idGenerationconfig = false;
            vm.idGenerationCoverage = "";
            vm.settingsArray = [];
            vm.retrieveEmpMaxId();
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.idGenerationconfig = false;
          vm.idGenerationCoverage = "";
          vm.settingsArray = [];
          vm.isLoading = false;
          vm.retrieveEmpMaxId();
        });
    },

    onChangeFields(dateErrMsgVariable = "", alreadyExitsField = "") {
      if (dateErrMsgVariable) {
        this[dateErrMsgVariable] = "";
        if (dateErrMsgVariable === "dateOfJoinErrorMsg") {
          this.retrieveProbationDate();
        }
      }
      if (alreadyExitsField) {
        this.alreadyExistErrMsg[alreadyExitsField] = true;
      }
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.cloneEmployeeDetails[field] = value;
      this.onChangeFields();
      if (field === "Designation_Id") {
        this.retrieveProbationDate();
      }
      if (
        field === "Service_Provider_Id" &&
        this.idGenerationconfig &&
        this.idGenerationCoverage?.toLowerCase() === "serviceprovider"
      ) {
        this.getEmployeeId();
      }
    },
    getEmployeeId() {
      let vm = this;
      vm.fetchingMaxEmpId = true;
      vm.$apollo
        .query({
          query: GET_MAX_EMPLOYEE_ID,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            serviceProviderId:
              vm.idGenerationCoverage?.toLowerCase() === "serviceprovider"
                ? this.cloneEmployeeDetails.Service_Provider_Id
                : null,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveMaxEmployeeId &&
            !response.data.retrieveMaxEmployeeId.errorCode
          ) {
            const { maxEmployeeId } = response.data.retrieveMaxEmployeeId;
            vm.cloneEmployeeDetails["User_Defined_EmpId"] = maxEmployeeId
              ? maxEmployeeId.toString()
              : "";
            vm.validateFieldAlreadyExist("User_Defined_EmpId", "Employee Id");
          } else {
            console.log(response.data);
            vm.cloneEmployeeDetails["User_Defined_EmpId"] = "";
            vm.showAlert({
              isOpen: true,
              message:
                response.data?.retrieveMaxEmployeeId?.message ||
                "Something went wrong while retrieving the employee id. Please try after some time.",
              type: "warning",
            });
          }
          vm.fetchingMaxEmpId = false;
        })
        .catch((err) => {
          vm.fetchingMaxEmpId = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "employee id",
            isListError: false,
          });
        });
    },
    async validateEditForm() {
      let isFormValid = await this.$refs.cloneEmployeeForm.validate();
      mixpanel.track("MyTeam-clone-submit-btn-click");
      if (
        isFormValid &&
        isFormValid.valid &&
        this.cloneEmployeeDetails.Date_Of_Join &&
        this.cloneEmployeeDetails.Probation_Date
      ) {
        this.probationDateErrorMsg = "";
        this.dateOfJoinErrorMsg = "";
        this.cloneEmployee();
      } else {
        if (!this.cloneEmployeeDetails.Date_Of_Join) {
          this.dateOfJoinErrorMsg = "Date of join is required";
        }
        if (!this.cloneEmployeeDetails.Probation_Date) {
          this.probationDateErrorMsg = "Probation Date is required";
        }
      }
    },

    cloneEmployee() {
      let vm = this;
      vm.isLoading = true;
      let settingId = null;
      if (this.idGenerationconfig) {
        if (this.idGenerationCoverage?.toLowerCase() === "serviceprovider") {
          settingId = this.settingsArray.find((item) => {
            return (
              item.serviceProviderId ===
              this.cloneEmployeeDetails.Service_Provider_Id
            );
          })?.empPrefixSettingId;
        } else {
          settingId = this.settingsArray.find((item) => {
            return item.serviceProviderId === null;
          })?.empPrefixSettingId;
        }
      }
      vm.$apollo
        .mutate({
          mutation: CLONE_EMPLOYEE,
          variables: {
            oldEmployeeId: vm.selectedEmployee.employeeId,
            User_Defined_EmpId: vm.cloneEmployeeDetails.User_Defined_EmpId,
            External_EmpId: vm.cloneEmployeeDetails.External_EmpId,
            Designation_Id: vm.cloneEmployeeDetails.Designation_Id,
            Department_Id: vm.cloneEmployeeDetails.Department_Id,
            Location_Id: vm.cloneEmployeeDetails.Location_Id,
            Roles_Id: vm.cloneEmployeeDetails.Roles_Id,
            EmpType_Id: vm.cloneEmployeeDetails.EmpType_Id,
            Work_Schedule: vm.cloneEmployeeDetails.Work_Schedule,
            Service_Provider_Id: vm.cloneEmployeeDetails.Service_Provider_Id
              ? vm.cloneEmployeeDetails.Service_Provider_Id
              : 0,
            Date_Of_Join: moment(vm.cloneEmployeeDetails.Date_Of_Join).isValid()
              ? moment(vm.cloneEmployeeDetails.Date_Of_Join).format(
                  "YYYY-MM-DD"
                )
              : null,
            Business_Unit_Id: vm.cloneEmployeeDetails.Business_Unit_Id,
            Probation_Date: moment(
              vm.cloneEmployeeDetails.Probation_Date
            ).isValid()
              ? moment(vm.cloneEmployeeDetails.Probation_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            Job_Role_Ids: vm.selectedJobRoles,
            empPrefixSettingId: settingId,
          },
          client: "apolloClientAD",
        })
        .then(() => {
          mixpanel.track("MyTeam-clone-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Employee cloned successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("employee-cloned");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      let errorCode = getErrorCodesAndMessagesWithValidation(err);
      if (errorCode && errorCode.length) {
        errorCode = errorCode[0];
      }
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "cloning",
          form: "employee",
          isListError: false,
        })
        .then((validationErrors) => {
          if (errorCode === "ESS0126" && validationErrors.length) {
            //Form the messages
            let backupValidations = validationErrors;
            validationErrors = {};
            for (let error of backupValidations) {
              if (error === "ESS0138") {
                validationErrors["ESS0138"] =
                  "ESS0138 - Employee ID is already assigned to an active employee. Please confirm and update the active employee's Employee ID to clone the employee.";
              } else if (error === "ESS0140") {
                validationErrors["ESS0140"] =
                  "ESS0140 - Employee email is already registered for an active employee. Please confirm and update the active employee's email to clone the employee.";
              } else if (error === "ESS0141") {
                validationErrors["ESS0141"] =
                  "ESS0141 - Biometric Integration ID is already assigned to an active employee. Please confirm and update the active employee's Biometric Integration ID to clone the employee.";
              } else if (error === "ESS0136") {
                validationErrors["ESS0136"] =
                  "ESS0136 - Bank account number is already linked to an active employee. Please confirm and update the active employee's account number and clone the employee.";
              } else if (error === "ESS0139") {
                validationErrors["ESS0139"] =
                  "ESS0139 - Mobile number is already linked to an active employee. Please confirm and update the active employee's mobile number to clone the employee.";
              } else if (error === "ESS0146") {
                validationErrors["ESS0146"] =
                  "ESS0146 - UAN is already linked to an active employee. Please confirm and update the active employee's UAN to clone the employee.";
              } else if (error === "ESS0142") {
                validationErrors["ESS0142"] =
                  "ESS0142 - PAN is already linked to an active employee. Please confirm and update the active employee's PAN to clone the employee.";
              } else if (error === "ESS0143") {
                validationErrors["ESS0143"] =
                  "ESS0143 - National Identity Number (Aadhar/Social Security) is already linked to an active employee. Please confirm and update the active employee's National Identity Number (Aadhar/Social Security) to clone the employee.";
              } else if (error === "ESS0144") {
                validationErrors["ESS0144"] =
                  "ESS0144 - PF Number is already linked to an active employee. Please confirm and update the active employee's PF Number to clone the employee.";
              }
            }
          }
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          if (this.validationMessages && this.validationMessages.length) {
            this.showValidationAlert = true;
          }
        });
      mixpanel.track("MyTeam-clone-error");
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveDropdownDetails() {
      this.dropdownListFetching = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              locations,
              employeeType,
              workSchedules,
              managers,
              serviceProvider,
              fieldForce,
              roles,
            } = res.data.getDropDownBoxDetails;
            this.departments = departments;
            this.locations = locations;
            this.employeeTypes = employeeType;
            this.workSchedules = workSchedules;
            this.managers = managers;
            this.serviceProviders = serviceProvider;
            this.fieldForce = fieldForce;
            this.roles = roles;
          }
          this.dropdownListFetching = false;
        })
        .catch(() => {
          this.dropdownListFetching = false;
        });
    },

    retrieveBusinessUnit() {
      let vm = this;
      vm.businessUnitListFetching = true;
      vm.$apollo
        .query({
          query: LIST_BUSINESS_UNIT,
          client: "apolloClientI",
          variables: {
            action: "active",
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBusinessUnitInDropdown &&
            !response.data.listBusinessUnitInDropdown.errorCode
          ) {
            const { settings } = response.data.listBusinessUnitInDropdown;
            vm.businessUnits = settings && settings.length > 0 ? settings : [];
          }
          vm.businessUnitListFetching = false;
        })
        .catch(() => {
          vm.businessUnitListFetching = false;
        });
    },

    retrieveEmpMaxId() {
      let vm = this;
      if (!vm.cloneEmployeeDetails["User_Defined_EmpId"]) {
        vm.fetchingMaxEmpId = true;
        vm.$apollo
          .query({
            query: RETRIEVE_MAX_EMP_ID,
            client: "apolloClientAC",
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveMaxEmployeeId &&
              !response.data.retrieveMaxEmployeeId.errorCode
            ) {
              const { maxEmployeeId } = response.data.retrieveMaxEmployeeId;
              let maxEmpId = maxEmployeeId ? parseInt(maxEmployeeId) + 1 : 0;
              vm.cloneEmployeeDetails["User_Defined_EmpId"] =
                maxEmpId.toString();
            }
            vm.fetchingMaxEmpId = false;
          })
          .catch(() => {
            vm.fetchingMaxEmpId = false;
          });
      }
    },

    retrieveProbationDate() {
      let vm = this;
      if (
        vm.cloneEmployeeDetails.Designation_Id &&
        vm.cloneEmployeeDetails.Date_Of_Join
      ) {
        vm.$apollo
          .query({
            query: RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION,
            client: "apolloClientAC",
            variables: {
              designationId: vm.cloneEmployeeDetails.Designation_Id,
              dateOfJoin: vm.cloneEmployeeDetails.Date_Of_Join,
            },
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveProbationDate &&
              !response.data.retrieveProbationDate.errorCode
            ) {
              const { probationDate } = response.data.retrieveProbationDate;
              vm.cloneEmployeeDetails.Probation_Date = probationDate;
            } else {
              this.$store.dispatch("handleApiErrors", {
                error: "",
                action: "retrieving",
                form: "probation date",
                isListError: false,
              });
            }
          })
          .catch((err) => {
            this.$store.dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: "probation date",
              isListError: false,
            });
          });
      }
    },

    async validateFieldAlreadyExist(field, label) {
      let vm = this;
      const fieldValid = await this.$refs[field].validate();
      if (fieldValid.length === 0 && vm.cloneEmployeeDetails[field]) {
        vm.$apollo
          .mutate({
            mutation: VALIDATE_FIELD_AVAILABILITY,
            client: "apolloClientAC",
            variables: {
              employeeId: 0,
              columnValue: vm.cloneEmployeeDetails[field],
              columnName: field,
              tableName: "emp_job",
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateCommonAvailability &&
              !response.data.validateCommonAvailability.errorCode
            ) {
              const { isAvailable } = response.data.validateCommonAvailability;
              if (!isAvailable) {
                vm.alreadyExistErrMsg[field] = label + " already exist";
              } else {
                vm.alreadyExistErrMsg[field] = true;
              }
            }
            vm.$refs.cloneEmployeeForm.validate();
          })
          .catch((err) => {
            vm.alreadyExistErrMsg[field] = true;
            vm.$refs.cloneEmployeeForm.validate();
            let fieldLabel = label.toLowerCase();
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: fieldLabel,
              isListError: false,
            });
          });
      }
    },

    callDesignationList(searchString) {
      if (
        searchString.length >= 3 &&
        !this.cloneEmployeeDetails.Designation_Id
      ) {
        this.getDesignationList(searchString);
      }
    },

    async getDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designations = designationResult;
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.dropdownDesignation = [];
        });
    },
    fetchJobRoles(designationId) {
      let vm = this;
      vm.jobRolesListLoading = true;
      vm.isErrorInSeconLineManager = false;
      vm.$apollo
        .query({
          query: LIST_JOB_ROLES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
          variables: { formId: 243, designationId: parseInt(designationId) },
        })
        .then(({ data }) => {
          vm.jobRolesListLoading = false;
          if (data && data.listJobRoles) {
            const response = data.listJobRoles;
            if (!response.errorCode) {
              vm.jobRolesList = [...JSON.parse(response.jobRoles)];
            } else {
              vm.jobRolesList = [];
            }
          } else {
            vm.jobRolesList = [];
          }
        })
        .catch(() => {
          vm.jobRolesListLoading = false;
          vm.jobRolesList = [];
        });
    },
  },
};
</script>
