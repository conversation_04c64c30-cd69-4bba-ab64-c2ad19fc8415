<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedLeaveType"
              density="compact"
              :items="leaveTypeList"
              item-title="leaveType"
              item-value="leaveType"
              label="Leave Type"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedCarryOver"
              density="compact"
              :items="['Yes', 'No']"
              label="Carry Over"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedLeaveEncashment"
              density="compact"
              :items="['Yes', 'No']"
              label="Leave Encashment"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              density="compact"
              :items="['Active', 'Inactive']"
              variant="solo"
              label="Status"
              single-line
              multiple
              chips
              closable-chips
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "LeavePolicyFilter",
  data: () => ({
    openFormFilter: false,
    selectedLeaveType: null,
    selectedCarryOver: null,
    selectedLeaveEncashment: null,
    selectedStatus: ["Active"],
    // Lists
    leaveTypeList: [],
  }),
  props: {
    itemList: {
      type: Array,
      default: () => [],
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.formFilterData();
  },
  methods: {
    formFilterData() {
      for (let item of this.itemList) {
        if (item && item.Leave_Type) {
          this.leaveTypeList.push({
            leaveType: item.Leave_Type,
          });
        }
      }
      this.leaveTypeList = this.removeDuplicatesFromArrayOfObject(
        this.leaveTypeList,
        "leaveType"
      );
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        leaveType: this.selectedLeaveType,
        carryOver: this.selectedCarryOver,
        encashment: this.selectedLeaveEncashment,
        status: this.selectedStatus,
      };
      this.applyFilter(filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter", "Active");
    },
    applyFilter(filter) {
      let filteredList = this.itemList;
      if (filter.leaveType && filter.leaveType?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.leaveType.includes(item.Leave_Type);
        });
      }
      if (filter.carryOver && filter.carryOver?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.carryOver.includes(item.Carry_Over);
        });
      }
      if (filter.encashment && filter.encashment?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.encashment.includes(item.Encashment);
        });
      }
      if (filter.status && filter.status?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.status.includes(item.Leave_Status);
        });
      }
      this.$emit("apply-filter", filteredList);
    },
    resetAllModelValues() {
      this.selectedLeaveType = null;
      this.selectedCarryOver = null;
      this.selectedLeaveEncashment = null;
      this.selectedStatus = ["Active"];
    },
  },
};
</script>
