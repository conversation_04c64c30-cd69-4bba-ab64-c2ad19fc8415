/**
 * File that contains all Constant values*
 */
export default {
    urls:{
        baseIp:'https://irukkaa.loophole.site'
    },
    apiUrls:{
        getCurrentUser:'/uat/up/api/v1/user/me',
        getGender:'/uat/up/api/v1/gender'
    },
    loginType: {
        cognito: "Cognito-Authentication",
        firebase: "Firebase-Authentication",
    },
    loginTypes: {
        MOBILE: "MOBILE_REGISTRATION",
        EMAIL: "EMAIL_REGISTRATION",
        FACEBOOK: "FACEBOOK_REGISTRATION",
        EMAILPASSWORD: "EMAIL_PASSWORD_REGISTRATION",
    },
    errorCodes:{
        success: 200,
        created: 201,
        error: 500,
        badRequest: 400,
        unAuthorized: 401,
        fileNotFound: 404,
        forbidden: 403,
        unSupportedMediaType: 415,
        gatewayTimeoutError: 504,
        badGatewayError: 502,
        pending: 202,
    },
    colors:{
        primary: '#ff0061'
    },
    api<PERSON><PERSON>: "****",
    googleConfig:{
        clientId:"******"
    },
    mapApiKey: "*****"
}
