<template>
  <div v-if="mainTabs.length > 0">
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :disableTab="isLoading"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row style="width: 100%">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end"
              :isFilter="false"
              :isDefaultFilter="false"
            />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="key-logs-container">
      <v-window v-model="currentTabItem" v-if="formAccess && formAccess.view">
        <v-window-item :value="currentTabItem">
          <MyKeyLogs
            v-if="currentTabItem === 'tab-0'"
            :search-data="searchData"
            :formAccess="formAccess"
            :formId="303"
            @my-key-loader="isDisableTabs"
          />
          <TeamKeyLogs
            v-if="currentTabItem === 'tab-1'"
            :search-data="searchData"
            :formAccess="formAccess"
            :formId="303"
            @my-team-loader="isDisableTabs"
          />
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
</template>
<script>
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu.vue";
import MyKeyLogs from "./MyKeyLogs.vue";
import TeamKeyLogs from "./TeamKeyLogs.vue";

export default {
  name: "MainKeyLogs",
  data() {
    return {
      currentTabItem: "",
      mainTabs: [],
      searchData: "",
      isLoading: false,
    };
  },
  components: {
    MyKeyLogs,
    EmployeeDefaultFilterMenu,
    TeamKeyLogs,
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    landedFormName() {
      let keyLoggerFormName = this.accessRights("303");
      if (keyLoggerFormName && keyLoggerFormName.customFormName) {
        return [keyLoggerFormName.customFormName];
      } else return [this.$t("dataLossPrevention.keyLogs")];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formAccess() {
      let formAccess = this.accessRights("303");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  watch: {
    searchValue(val) {
      this.searchData = val;
    },
  },
  mounted() {
    if (this.formAccess && this.formAccess.view) {
      if (this.isAdmin || this.formAccess.isManager || this.formAccess.admin) {
        this.mainTabs = [
          this.$t("dataLossPrevention.myKeyLogs"),
          this.$t("dataLossPrevention.myTeamKeyLogs"),
        ];
        this.currentTabItem = "tab-1";
      } else {
        this.mainTabs = [this.$t("dataLossPrevention.myKeyLogs")];
        this.currentTabItem = "tab-0";
      }
    } else {
      this.currentTabItem = "tab-0";
    }
  },
  methods: {
    isDisableTabs(isLoading) {
      this.isLoading = isLoading;
    },
    onTabChange(value) {
      if (value && this.mainTabs.includes(value)) {
        this.currentTabItem = "tab-" + this.mainTabs.indexOf(value);
      }
    },
  },
};
</script>
<style scoped>
.key-logs-container {
  padding: 4em 2em 0em 2em;
}

@media screen and (max-width: 805px) {
  .key-logs-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
