<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <!-- Top bar content for filtering -->
        <template v-slot:topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="8" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                v-if="!showViewForm"
                class="justify-end"
                :isFilter="false"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <!-- Main content area -->
    <v-container fluid class="attendance">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-container fluid class="attendance-card">
            <ProfileCard v-if="formAccess && selectedItem">
              <FormTab :model-value="openedSubTab" :hide-slider="true">
                <v-tab
                  v-for="tab in subTabItems"
                  :key="tab.value"
                  :value="tab.value"
                  :disabled="tab.disable"
                  @click="onChangeSubTabs(tab.value)"
                >
                  <div
                    :class="[
                      isActiveSubTab(tab.value)
                        ? 'text-primary font-weight-bold'
                        : 'text-grey-darken-2 font-weight-bold',
                    ]"
                  >
                    {{ tab.label }}
                    <div
                      v-if="isActiveSubTab(tab.value)"
                      class="mt-3 mb-n4"
                      style="border-bottom: 4px solid; width: 150px"
                    ></div>
                  </div>
                </v-tab>
              </FormTab>
            </ProfileCard>
          </v-container>
          <v-window v-model="openedSubTab">
            <v-window-item value="Attendance Log">
              <EmployeeAttendance
                v-if="showViewForm && openedSubTab === 'Attendance Log'"
                :selectedItem="selectedItem"
                :editFormData="selectedItem"
                :callingFrom="callingFrom"
                :access-rights="formAccess"
                @close-form="closeAllForms()"
                @close-view-form="closeViewForm()"
                @open-edit-form="openEditForm()"
              />
            </v-window-item>
            <v-window-item value="Calendar">
              <calendar-view
                v-if="showViewForm && openedSubTab === 'Calendar'"
                :callingForm="callingFrom"
                :empData="selectedItem"
                @close-view-form="closeViewForm()"
              ></calendar-view>
            </v-window-item>
          </v-window>
          <v-container fluid class="attendance-data" v-if="formAccess.view">
            <!-- Loading skeleton or error screens -->
            <div v-if="listLoading" class="mt-3">
              <!-- Skeleton loaders -->
              <v-skeleton-loader
                ref="skeleton1"
                type="table-heading"
                class="mx-auto"
              ></v-skeleton-loader>
              <div v-for="i in 3" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item-avatar"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </div>

            <!-- Error screens for fetching data -->
            <AppFetchErrorScreen
              v-else-if="isErrorInList"
              :content="errorContent"
              icon-name="fas fa-redo-alt"
              image-name="common/human-error-image"
              :button-text="'Retry'"
              @button-click="refetchList('Attendance error refetch')"
            >
            </AppFetchErrorScreen>

            <!-- No matching Attendance found -->
            <AppFetchErrorScreen
              v-else-if="
                itemList.length === 0 && originalList.length && !showViewForm
              "
              :main-title="'There are no Attendance matched for the selected filters/searches.'"
              image-name="common/no-records"
            >
              <template v-slot:contentSlot>
                <div style="max-width: 80%">
                  <v-row class="rounded-lg pa-5 mb-4">
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <!-- Button to reset filter/search -->
                      <v-btn
                        variant="elevated"
                        color="primary"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="windowWidth <= 960 ? 'small' : 'default'"
                        @click="resetFilter('grid')"
                      >
                        <span class="primary">Reset Filter/Search </span>
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>

            <!-- Content area for attendance -->
            <div v-else-if="!showViewForm">
              <!-- Buttons for actions like add, refetch, and more actions -->
              <div
                v-if="originalList.length > 0"
                class="d-flex align-center justify-end mb-2"
              >
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </div>

              <v-row>
                <v-col v-if="originalList.length > 0" class="mb-12" :cols="12">
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                    :sort-by="[{ key: 'Employee_Name', order: 'asc' }]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView
                            ? 'v-data-table__mobile-table-row ma-0 mt-2'
                            : ''
                        "
                      >
                        <td
                          :class="{
                            'd-flex align-start': isMobileView,
                          }"
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold mt-2 mr-5"
                            style="width: 30%"
                          >
                            Employee
                          </div>
                          <section
                            class="d-flex align-center"
                            :class="{ 'mt-2 text-truncate': isMobileView }"
                            :style="isMobileView ? 'max-width: 60%' : ''"
                          >
                            <span
                              class="text-primary text-body-2 font-weight-medium"
                            >
                              <v-tooltip
                                :text="item.Employee_Name"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-bind="
                                      item.Employee_Name &&
                                      item.Employee_Name.length > 20
                                        ? props
                                        : ''
                                    "
                                    >{{
                                      checkNullValue(item.Employee_Name)
                                    }}</span
                                  >
                                </template>
                              </v-tooltip>
                              <v-tooltip
                                :text="item.Employee_Id"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    v-if="item.Employee_Id"
                                    v-bind="
                                      item.Employee_Id &&
                                      item.Employee_Id.length > 20
                                        ? props
                                        : ''
                                    "
                                    class="text-grey"
                                  >
                                    {{ checkNullValue(item.Employee_Id) }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </span>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </div>
          </v-container>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeAttendance = defineAsyncComponent(() =>
  import("./EmployeeAttendance.vue")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const CalendarView = defineAsyncComponent(() => import("./CalenderView.vue"));
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "AdminAttendance",
  components: {
    EmployeeAttendance,
    EmployeeDefaultFilterMenu,
    CalendarView,
  },
  props: {
    callingFrom: {
      type: String,
      default: "employee",
    },
  },
  mixins: [FileExportMixin],
  data: () => ({
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    openedSubTab: "Attendance Log",
    errorContent: "",
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    selectedItem: null,
    showViewForm: false,
    currentTabItem: "",
  }),
  computed: {
    subTabItems() {
      let initialTabs = [
        {
          label: "Attendance Log",
          value: "Attendance Log",
          disable: false,
        },
        {
          label: "Calendar",
          value: "Calendar",
          disable: false,
        },
      ];
      return initialTabs;
    },
    formId() {
      let fId = this.callingFrom === "team" ? "304" : "305";
      return parseInt(fId);
    },
    // check the selected filter month is current month or not
    landedFormName() {
      let attendance = this.accessRights(this.formId);
      if (attendance && attendance.customFormName) {
        return attendance.customFormName;
      } else return "Attendance";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    attendanceFormAccess() {
      if (this.callingFrom === "team")
        return this.$store.getters.attendanceFormAccess;
      return this.$store.getters.employeeAttendanceTabs;
    },

    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.attendanceFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },

    formAccess() {
      let formAccessRights = this.accessRights(this.formId);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        if (this.formId === 304) {
          if (
            formAccessRights.accessRights.isManager ||
            formAccessRights.accessRights.admin === "admin"
          ) {
            return formAccessRights.accessRights;
          }
          return false;
        } else return formAccessRights.accessRights;
      } else return false;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    isSmallTable() {
      return this.showViewForm || this.showAddEditForm;
    },
    tableHeaders() {
      return [
        {
          title: "Employee",
          key: "Employee_Name",
        },
      ];
    },
    emptyScenarioMsg() {
      return this.originalList.length
        ? "There are no employees for the selected filters/searches."
        : "";
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.callingFrom !== "team") {
      this.showViewForm = true;
      this.selectedItem = "attendance";
    }
    this.openedSubTab = "Attendance Log";
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.getEmpList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    async getEmpList() {
      let formId = this.formId;
      this.listLoading = true;
      try {
        const empData = await this.$store.dispatch("getEmployeesList", {
          formName: "Attendance",
          formId: formId,
          flag: "payslipreq",
        });

        if (empData && empData.length) {
          // Map and filter the data to create itemList and originalList
          const processedData = empData.map((item) => ({
            ...item,
            Employee_Name: item.employeeName,
            Employee_Id: item.userDefinedEmpId,
          }));

          this.itemList = processedData.filter(
            (el) => el.empStatus === "Active"
          );
          this.originalList = processedData; // Store all unfiltered data here

          this.selectedEmployee = this.loginEmployeeId;
          mixpanel.track("Employee list retrieved");
        } else {
          this.itemList = [];
          this.originalList = [];
          this.showAlert({
            isOpen: true,
            message: "No employees found.",
            type: "warning",
          });
        }
      } catch (err) {
        this.errorContent =
          "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
        this.isErrorInList = true;
      } finally {
        this.listLoading = false;
      }
    },
    closeViewForm() {
      this.$emit("close-view-form");
      this.showViewForm = false;
      this.selectedItem = null;
      this.showAddEditForm = false;
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    onApplySearch(val) {
      if (!val) {
        this.resetFilter();
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          let newObj = {
            Employee_Name: item.Employee_Name,
            Employee_Id: item.Employee_Id,
          };
          return Object.keys(newObj).some((k) => {
            if (
              newObj[k] &&
              newObj[k].toString().toLowerCase().includes(searchValue)
            ) {
              return true;
            } else {
              return false;
            }
          });
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.itemList = this.originalList;
      this.isFilter = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    openEditForm() {
      mixpanel.track("Attendance edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    openViewForm(item) {
      mixpanel.track("Attendance view form opened");
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
      this.itemList = this.originalList;
    },

    closeAllForms() {
      mixpanel.track("Attendance all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },

    onTabChange(tab) {
      mixpanel.track("Attendance form tab changed");
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.attendanceFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/my-team/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/my-team/" + clickedForm.url;
        }
      }
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.getEmpList();
      this.resetFilter();
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.attendance {
  padding: 3.7em 0em 0em 0em;
}
.attendance-card {
  padding: 0em 0em 0em 0em;
}
.attendance-data {
  padding-left: 2em;
  padding-right: 3em;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

.notification-bar {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-badge {
  background-color: #ff6f61;
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  font-weight: bold;
  margin-right: 10px;
}
.v-dialog {
  box-shadow: none;
}

@media screen and (max-width: 805px) {
  .attendance {
    padding: 4em 1em 0em 1em;
  }
  .attendance-data {
    padding: 0em 2em 0em 3em;
  }
  .attendance-card {
    padding: 0em 0em 0em 0em;
  }
}
</style>
