<template>
  <div>
    <v-row v-if="!enableCandidatePortal" class="full-height">
      <v-col v-if="windowWidth > 960" cols="6" class="bg-primary"></v-col>
      <v-col cols="12" sm="12" lg="6" md="6" xl="6">
        <v-row class="full-height">
          <v-col
            cols="12"
            sm="12"
            md="10"
            class="d-flex justify-center flex-column align-center"
          >
            <div class="welcome-text my-5 font-weight-bold">Sign in</div>
            <div
              v-if="!presentOTPSetion"
              class="text-subtitle-1 text-center font-weight-regular px-12"
            >
              Access the available job opportunities, track your application
              status and keep your profile up to date.
            </div>
            <div
              v-else
              style="padding: 0px"
              class="text-subtitle-1 font-weight-regular text-center text-green"
            >
              We sent OTP to your email address: {{ signInEmail }}
              <p>Please enter the OTP to sign in</p>
              <p class="text-caption text-red">
                The OTP will expire after {{ otpExpireTime }} minutes
              </p>
            </div>
            <div class="my-10">
              <v-row>
                <v-col v-if="!presentOTPSetion" cols="12">
                  <v-text-field
                    variant="solo"
                    ref="signInEmail"
                    v-model="signInEmail"
                    min-width="300px"
                    :rules="[
                      validateWithRulesAndReturnMessages(
                        signInEmail,
                        'empEmail',
                        'Email Address'
                      ),
                    ]"
                    placeholder="Email Address"
                    label="Email Address"
                  />
                  <v-row class="d-flex justify-center mt-2">
                    <v-btn
                      :disabled="disableNextButtton"
                      color="primary"
                      class="justify-center"
                      @click="validateSignEmail"
                    >
                      Next
                    </v-btn>
                  </v-row>
                </v-col>
                <v-col v-else cols="12">
                  <v-text-field
                    variant="solo"
                    ref="OTP"
                    v-model="signInPassword"
                    type="number"
                    min-width="300px"
                    :rules="[required('OTP', signInPassword)]"
                    label="OTP"
                  />
                  <v-row class="d-flex justify-center my-2">
                    <v-btn
                      variant="text"
                      class="text-info text-decoration-underline justify-center"
                      @click="validateSignEmail()"
                      >Re-send OTP</v-btn
                    >
                  </v-row>
                  <v-row class="d-flex justify-center mt-2">
                    <v-btn color="primary" @click="validatesignOTP()"
                      >Sign in</v-btn
                    >
                  </v-row>
                </v-col>
              </v-row>
            </div>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <div v-else>
      <CandidatePortal
        :setting-result="settingResult"
        @signout-success="signoutCandidate()"
      />
    </div>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import Cookies from "js-cookie";
import validationRules from "@/mixins/validationRules";
import {
  GENERATE_PASSCODE_FOR_PORTAL_ACCESS,
  PASSCODE_VALIDATE_PORTAL_ACCESS,
  GET_JOB_HEADER,
} from "@/graphql/recruitment/recruitmentQueries.js";
const CandidatePortal = defineAsyncComponent(() =>
  import("./CandidatePortal.vue")
);
export default {
  name: "CandidateSiginPortal",
  data() {
    return {
      isLoading: false,
      presentOTPSetion: false,
      signInEmail: "",
      signInPassword: "",
      enableCandidatePortal: false,
      otpExpireTime: 60,
      settingResult: {},
    };
  },
  mixins: [validationRules],
  components: {
    CandidatePortal,
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    disableNextButtton() {
      return (
        !this.signInEmail || this.$refs["signInEmail"]?.errorMessages?.length
      );
    },
  },
  mounted() {
    const candidateId = Cookies.get("portalCandidateId");
    if (candidateId) {
      this.enableCandidatePortal = true;
    }
    this.checkSessionValidity();
    this.retrieveJobHeader();
  },
  methods: {
    signoutCandidate(sessionOut = false) {
      this.enableCandidatePortal = false;
      this.signInEmail = "";
      this.signInPassword = "";
      this.presentOTPSetion = false;
      // // Clear session storage
      Cookies.remove("portalCandidateId");
      // Remove cookies on logout
      Cookies.remove("portalCandidateExpiry");
      if (sessionOut)
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: "You have been signed out because of session timeout",
        });
    },
    checkSessionValidity() {
      setInterval(() => {
        const expiryTime = Cookies.get("portalCandidateExpiry");

        if (expiryTime && new Date() > new Date(expiryTime)) {
          this.signoutCandidate(true);
        }
      }, 5000); // Check every 5 seconds
    },

    validateSignEmail() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: GENERATE_PASSCODE_FOR_PORTAL_ACCESS,
          client: "apolloClientBG",
          variables: {
            emailId: String(vm.signInEmail).trim(),
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.generatePassCodeForPortalAccess &&
            !response.data.generatePassCodeForPortalAccess.errorCode
          ) {
            vm.presentOTPSetion = true;
          } else {
            vm.handleSigninPortal(
              response.data.generatePassCodeForPortalAccess.errorCode
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.handleSigninPortal(error);
        });
    },
    handleSigninPortal(error = "") {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "generate",
        form: "candidate portal",
        isListError: false,
      });
    },
    validatesignOTP() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: PASSCODE_VALIDATE_PORTAL_ACCESS,
          client: "apolloClientBG",
          variables: {
            emailId: String(vm.signInEmail).trim(),
            passCode: parseInt(vm.signInPassword),
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.passcodeValidatePortalAccess &&
            !response.data.passcodeValidatePortalAccess.errorCode
          ) {
            const candidateId = parseInt(
              response.data.passcodeValidatePortalAccess?.data
            );
            vm.enableCandidatePortal = true;
            // Set session expiration for 1 day
            const expiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day (24 hours)

            // Store data in cookies
            Cookies.set("portalCandidateId", candidateId, {
              expires: expiryTime,
            });
            Cookies.set("portalCandidateExpiry", expiryTime.toUTCString(), {
              expires: expiryTime,
            }); // Expires in 1 day
            vm.checkSessionValidity();
          } else {
            vm.handleSigninPortal(
              response.data.passcodeValidatePortalAccess.errorCode
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.handleSigninPortal(error);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    retrieveJobHeader() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_JOB_HEADER,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.recruitmentSetting &&
            res.data.recruitmentSetting.settingResult &&
            res.data.recruitmentSetting.settingResult[0]
          ) {
            this.settingResult = res.data.recruitmentSetting.settingResult[0];
            this.otpExpireTime =
              res.data.recruitmentSetting.settingResult[0]?.Exp_portal_Expiry_Time_In_Mins;
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
  },
};
</script>
<style scoped>
.welcome-text {
  color: var(--v-primary-base) !important;
  font-size: 40px;
}
.full-height {
  height: 100vh;
}
.login-bg {
  background: var(--v-primary-base) !important;
}
</style>
