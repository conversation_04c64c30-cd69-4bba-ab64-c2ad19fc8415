<template>
  <v-card
    elevation="3"
    class="card-item d-flex rounded-lg ma-2 cursor-pointer"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `border-left: 7px solid ${colorCode(
            workflowDetails.Module_Id
          )}; min-height: 250px;`
        : `border-left: 7px solid ${colorCode(workflowDetails.Module_Id)}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 d-flex justify-space-between">
        <span style="max-width: 80%">
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex align-center">
              <!-- <v-icon class="px-2" color="#1E3050">fas fa-users-cog</v-icon> -->
              <div
                class="mx-2 d-flex flex-column justify-start"
                style="max-width: 100%"
              >
                <v-tooltip
                  :open-on-click="true"
                  :text="workflowDetails.Workflow_Name"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-subtitle-1 text-truncate"
                      style="max-width: 100%"
                      v-bind="workflowDetails.Workflow_Name ? props : ''"
                    >
                      {{ checkNullValue(workflowDetails.Workflow_Name) }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
        <ActionMenu
          v-if="
            workflowDetails.Default_Workflow === 1 && accessMenuRights['update']
          "
          :disableActionButtons="['Delete']"
          :access-rights="accessMenuRights"
          :actions="['Edit', 'Edit Flow']"
          @selected-action="handleActions($event)"
        ></ActionMenu>
        <ActionMenu
          v-else-if="accessMenuRights['update'] && accessMenuRights['delete']"
          :actions="['Edit', 'Delete', 'Edit Flow']"
          :disableActionButtons="['Delete']"
          :access-rights="accessMenuRights"
          @selected-action="handleActions($event)"
        ></ActionMenu>
      </div>
      <div class="card-columns w-100 mt-n8">
        <span
          class="d-flex align-start flex-column ml-3 mt-3 mb-n6"
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Module Name </b>
              <v-tooltip location="bottom">
                <template v-slot:activator="{ props }">
                  <div
                    class="text-truncate py-2"
                    style="max-width: 200px"
                    v-bind="workflowDetails.Module_Name ? props : ''"
                  >
                    {{ checkNullValue(workflowDetails.Module_Name) }}
                  </div>
                </template>
                <div style="max-width: 250px !important">
                  {{ workflowDetails.Module_Name }}
                </div>
              </v-tooltip>
            </div>
            <!-- <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Default Template </b>
              <span class="py-2">
                {{ workflowDetails.Is_Template ? "Yes" : "No" }}</span
              >
            </div> -->
            <!-- <div class="d-flex flex-column justify-start">
              <b class="mt-1 mr-2 text-grey justify-start">Added By </b>
              <span class="py-2">
                {{ checkNullValue(workflowDetails.Added_By) }}</span
              >
            </div>
            <div
              v-if="workflowDetails.Updated_By && workflowDetails.Updated_On"
              class="d-flex flex-column justify-start"
            >
              <b class="mt-1 mr-2 text-grey justify-start">Updated By </b>
              <span class="py-2">
                {{ checkNullValue(workflowDetails.Updated_By) }}</span
              >
            </div> -->
          </v-card-text>
        </span>
        <span
          class="d-flex align-start flex-column ml-3 mt-3 mb-n6"
          :style="
            !isMobileView
              ? 'width:50%'
              : 'width:100% ; margin-top:-28px !important;margin-bottom: 10px !important;'
          "
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex flex-column justify-start">
              <b class="mt-2 text-grey justify-start">Form Name </b>
              <span class="py-2">
                {{ checkNullValue(workflowDetails.Form_Name) }}</span
              >
            </div>
            <div class="d-flex justify-start pl-6 pb-2">
              <v-badge
                v-if="workflowDetails.Default_Workflow === 1"
                bordered
                class="mt-3"
                color="primary"
                content="Default workflow"
                v-bind="props"
              ></v-badge>
            </div>
          </v-card-text>
        </span>
      </div>
      <span>
        <v-card-text class="text-body-1 font-weight-regular">
          <div
            class="d-flex flex-column justify-start"
            v-if="workflowDetails.Designations.length"
          >
            <b class="mt-2 text-grey justify-start">Designations </b>
            <div class="d-flex flex-wrap">
              <div
                v-for="(designation, index) in workflowDetails.Designations"
                :key="designation.Designation_Id"
              >
                <span class="py-2">
                  {{ designation.Designation_Name
                  }}{{
                    workflowDetails.Designations.length - 1 !== index
                      ? ","
                      : "."
                  }}</span
                >
              </div>
            </div>
          </div>
        </v-card-text>
      </span>
    </div>
    <div class="ml-auto"></div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    iconName="fas fa-trash"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="deleteWorkflowDetails()"
  >
  </AppWarningModal>
</template>

<script>
import {
  generateRandomColor,
  checkNullValue,
  colorCode,
  // getOrigin,
} from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { DELETE_WORKFLOW_DETAILS } from "@/graphql/commonQueries";
import Config from "@/config.js";
import axios from "axios";

export default {
  name: "WorkflowCard",
  props: {
    workflowDetails: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: false,
    },
  },
  components: { ActionMenu },
  emits: ["refetch-list", "on-open-edit", "edit-flow"],
  data() {
    return {
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      openWarningModal: false,
      accessMenuRights: [],
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    this.accessMenuRights = this.accessRights;
    this.accessMenuRights["Edit Flow"] = this.accessRights["update"];
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    colorCode,
    handleActions(action) {
      if (action === "Delete") {
        this.openWarningModal = true;
      } else if (action === "Edit Flow") {
        this.$emit("edit-flow");
      } else {
        this.$emit("on-open-edit");
      }
    },
    deleteWorkflowDetails() {
      let vm = this;
      const payload = {
        query_key: "query.event.upsert",
        data: {
          status_id: "1005",
          event_id: this.workflowDetails.Event_Id,
        },
      };
      axios
        .post(
          Config.workflowUrl + "/master/" + this.workflowDetails.Event_Id,
          JSON.stringify(payload),
          {
            headers: {
              org_code: vm.orgCode,
              employee_id: vm.loginEmployeeId,
              // Origin: getOrigin(),
              Db_prefix: vm.domainName,
              "Content-Type":
                "application/x-www-form-urlencoded; charset=UTF-8",
              Authorization: window.$cookies.get("accessToken")
                ? window.$cookies.get("accessToken")
                : "",
            },
          }
        )
        .then((res) => {
          if (res) {
            this.deleteWorkflow();
          }
        });
    },
    deleteWorkflow() {
      let vm = this;
      vm.openWarningModal = false;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_WORKFLOW_DETAILS,
          variables: {
            employeeId: vm.loginEmployeeId,
            workflowId: this.workflowDetails.Workflow_Id,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Workflow details deleted successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-list");
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: "roles",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
