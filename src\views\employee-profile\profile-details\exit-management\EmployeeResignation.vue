<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs('error')"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else>
    <div class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs('refresh')" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <ViewResignation
      :formAccess="formAccess"
      :resignationDetails="resignationDetails"
      :selectedEmpId="selectedEmpId"
      :selectedEmpStatus="selectedEmpStatus"
      @refetch-resignation-details="refetchAPIs()"
    />
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import ViewResignation from "./resignation/ViewResignation.vue";
import { GET_ALL_RESIGNATION } from "@/graphql/my-team/exitManagement.js";
export default {
  name: "EmployeeResignation",
  components: {
    ViewResignation,
  },
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: true,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
  },
  emits: ["details-retrieved"],
  data: () => ({
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    isLoading: false,
    resignationDetails: [],
  }),
  computed: {
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },
  mounted() {
    if (this.selectedEmpId) {
      this.getAllResignations();
    }
  },
  methods: {
    getAllResignations(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_ALL_RESIGNATION,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: this.loginEmployeeId,
              orgCode: this.orgCode,
              formId: 292,
            },
            filter: {
              searchValue: "",
              limit: 1000,
              offset: 0,
              status: [],
              employees: [this.loginEmployeeId],
              noticeDate: {
                start: "",
                end: "",
              },
              resignationDate: {
                start: "",
                end: "",
              },
              designationId: null,
              departmentId: null,
            },
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getAllResignation &&
            !response.data.getAllResignation.error
          ) {
            vm.resignationDetails = response.data.getAllResignation.result;
            this.resignationDetails = this.resignationDetails.filter(
              (resignation) => resignation.employeeId === this.loginEmployeeId
            );
            vm.$emit("details-retrieved", type);
            vm.listLoading = false;
          } else {
            this.listLoading = false;
            vm.handleListError();
          }
        })
        .catch((err) => {
          this.listLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },
    refetchAPIs(type) {
      this.isErrorInList = false;
      this.getAllResignations(type);
    },
  },
};
</script>
