<template>
  <v-overlay
    :model-value="showAddEditForm"
    @click:outside="
      isFormDirty ? (openConfirmationPopup = true) : onCloseOverlay()
    "
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        style="width: 100vw; height: 100vh"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center fixed-title"
        >
          <div class="text-h6 text-medium ps-2">
            {{ isEdit ? "Edit" : "Apply" }}
            {{ landedFormName }}
          </div>
          <v-btn
            icon
            class="clsBtn"
            variant="text"
            @click="
              isFormDirty ? (openConfirmationPopup = true) : onCloseOverlay()
            "
          >
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text
          class="card mb-3 px-0"
          style="overflow-y: auto; padding-bottom: 50px"
        >
          <div class="px-5 py-6">
            <v-form ref="addEditFormValidator">
              <v-row>
                <v-col :cols="!isMobileView ? 7 : 12">
                  <v-row class="mb-2">
                    <!-- Employee Name -->
                    <v-col cols="12" sm="6" class="px-md-6">
                      <CustomSelect
                        ref="employeeName"
                        v-model="editedLeaveData.Employee_Id"
                        :items="listEmployees"
                        itemTitle="text"
                        itemValue="value"
                        :loading="employeesLoading"
                        :rules="[
                          required('Employee', editedLeaveData.Employee_Id),
                        ]"
                        clearable
                        :disabled="callingFrom?.toLowerCase() === `selfservice`"
                        label="Employee"
                        :is-required="true"
                        variant="solo"
                        :isAutoComplete="true"
                        :itemSelected="editedLeaveData.Employee_Id"
                        @selected-item="
                          onChangeFieldType($event, `Employee_Id`)
                        "
                      />
                    </v-col>
                    <!-- Leave Type -->
                    <v-col cols="12" sm="6" class="px-md-6">
                      <CustomSelect
                        ref="leaveTypeId"
                        v-model="editedLeaveData.LeaveType_Id"
                        :items="leaveTypeList"
                        itemTitle="leaveName"
                        itemValue="LeaveType_Id"
                        :loading="leaveTypeLoading"
                        :rules="[
                          required('Leave Type', editedLeaveData.LeaveType_Id),
                        ]"
                        clearable
                        label="Leave Type"
                        :is-required="true"
                        variant="solo"
                        :isAutoComplete="true"
                        :itemSelected="editedLeaveData.LeaveType_Id"
                        @selected-item="
                          onChangeFieldType($event, `LeaveType_Id`)
                        "
                      />
                    </v-col>
                    <!-- Duration -->
                    <v-col cols="12" sm="6" class="px-md-6">
                      <CustomSelect
                        ref="duration"
                        v-model="editedLeaveData.Duration"
                        :items="leaveDurationList"
                        itemTitle="text"
                        itemValue="value"
                        :loading="isLoading"
                        :rules="[
                          required('Duration', editedLeaveData.Duration),
                        ]"
                        clearable
                        label="Duration"
                        :disabled="!editedLeaveData.LeaveType_Id"
                        :is-required="true"
                        variant="solo"
                        :isAutoComplete="true"
                        :itemSelected="editedLeaveData.Duration"
                        @selected-item="onChangeFieldType($event, `Duration`)"
                      />
                    </v-col>
                    <!-- Leave Period -->
                    <v-col cols="12" sm="6" class="px-md-6">
                      <CustomSelect
                        ref="leavePeriod"
                        v-model="editedLeaveData.Leave_Period"
                        :items="leavePeriodList"
                        :loading="isLoading"
                        :rules="[
                          editedLeaveData.isLeavePeriodRequired
                            ? required('Period', editedLeaveData.Leave_Period)
                            : true,
                        ]"
                        clearable
                        :disabled="!editedLeaveData.presentLeavePeriod"
                        label="Period"
                        :is-required="editedLeaveData.isLeavePeriodRequired"
                        variant="solo"
                        :isAutoComplete="true"
                        :itemSelected="editedLeaveData.Leave_Period"
                        @selected-item="
                          onChangeFieldType($event, `Leave_Period`)
                        "
                      />
                    </v-col>
                    <!-- Leave Date From -->
                    <v-col cols="12" sm="4" md="6" class="px-md-6">
                      <section class="text-body-2">
                        <v-menu
                          v-model="editedLeaveData.fromMenu"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="auto"
                          ><template v-slot:activator="{ props }">
                            <v-text-field
                              ref="leaveFrom"
                              v-model="editedLeaveData.formattedFrom"
                              prepend-inner-icon="fas fa-calendar"
                              :rules="[
                                required(
                                  'Start Date',
                                  editedLeaveData.formattedFrom
                                ),
                              ]"
                              :disabled="
                                editedLeaveData.Duration == 1
                                  ? !editedLeaveData.Duration
                                  : !editedLeaveData.Leave_Period ||
                                    !editedLeaveData.Duration
                              "
                              readonly
                              v-bind="props"
                              variant="solo"
                            >
                              <template v-slot:label>
                                From
                                <span style="color: red">*</span>
                              </template></v-text-field
                            >
                          </template>
                          <v-date-picker
                            v-model="editedLeaveData.Leave_From"
                            :min="editedLeaveData.minDateFrom"
                            :max="editedLeaveData.maxDateFrom"
                            :allowed-dates="disableSpecificDates"
                            @update:model-value="
                              onChangeFieldType($event, `Leave_From`)
                            "
                          />
                        </v-menu>
                      </section>
                    </v-col>
                    <!-- Leave Date To -->
                    <v-col cols="12" sm="4" md="4" class="pl-md-6 pr-md-2">
                      <section class="text-body-2">
                        <v-menu
                          v-model="editedLeaveData.fromTo"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="auto"
                          ><template v-slot:activator="{ props }">
                            <v-text-field
                              ref="leaveTo"
                              v-model="editedLeaveData.formattedTo"
                              prepend-inner-icon="fas fa-calendar"
                              :rules="[
                                required(
                                  'End Date',
                                  editedLeaveData.formattedTo
                                ),
                              ]"
                              readonly
                              v-bind="props"
                              variant="solo"
                              :disabled="
                                !editedLeaveData.Leave_From ||
                                editedLeaveData.isLeaveToDisabled
                              "
                            >
                              <template v-slot:label>
                                To
                                <span style="color: red">*</span>
                              </template></v-text-field
                            >
                          </template>
                          <v-date-picker
                            v-model="editedLeaveData.Leave_To"
                            :min="editedLeaveData.minDateTo"
                            :max="editedLeaveData.maxDateTo"
                            :allowed-dates="disableSpecificDates"
                            @update:model-value="
                              onChangeFieldType($event, `Leave_To`)
                            "
                          />
                        </v-menu>
                        <span v-if="errorsMessages.length">
                          <span
                            class="text-caption text-red"
                            v-for="(error, index) in errorsMessages"
                            :key="index"
                          >
                            {{ error }}
                          </span>
                        </span>
                      </section>
                    </v-col>
                    <!-- Total Days -->
                    <v-col cols="12" sm="4" md="2" class="px-md-2">
                      <div class="text-subtitle-1 text-grey-darken-1">
                        Total Days
                      </div>
                      <div class="text-subtitle-1 font-weight-regular">
                        <section class="text-body-2">
                          {{
                            loaderIsTrue
                              ? "-"
                              : checkNullValue(editedLeaveData.Total_Days)
                          }}
                        </section>
                      </div>
                    </v-col>
                    <!-- Reason -->
                    <v-col cols="12" sm="6" class="px-md-6"
                      ><v-textarea
                        ref="reason"
                        v-model="editedLeaveData.Reason"
                        variant="solo"
                        auto-grow
                        label="Reason"
                        rows="1"
                        :rules="[
                          required('Reason', editedLeaveData.Reason),
                          editedLeaveData.Reason
                            ? validateWithRulesAndReturnMessages(
                                editedLeaveData.Reason,
                                'description',
                                'Reason'
                              )
                            : true,
                        ]"
                        clearable
                        @update:model-value="
                          onChangeFieldType($event, `Reason`)
                        "
                        ><template v-slot:label>
                          Reason
                          <span style="color: red">*</span>
                        </template></v-textarea
                      >
                    </v-col>
                    <!-- Document -->
                    <v-col
                      v-if="
                        editedLeaveData.showDocumentUpload &&
                        editedLeaveData.isDocumentRequired
                      "
                      cols="12"
                      class="px-md-6"
                    >
                      <v-file-input
                        ref="documentUpload"
                        prepend-icon=""
                        clearable
                        multiple
                        chips
                        :model-value="editedLeaveData.documentUpload"
                        append-inner-icon="fas fa-paperclip"
                        variant="solo"
                        :rules="[
                          editedLeaveData.isDocumentRequired
                            ? required(
                                'Documents',
                                editedLeaveData.documentUpload?.length
                              )
                            : true,
                          checkSize(editedLeaveData),
                          checkDuplicateFiles(editedLeaveData),
                        ]"
                        :persistent-hint="true"
                        hint="Max Size: 3MB. Allowed formats: .jpg, .jpeg, .png, .pdf, .doc, .docx, .txt"
                        accept=".jpg, .jpeg, .png, .pdf, .doc, .docx, .txt"
                        @update:model-value="
                          onUploadFile($event, editedLeaveData)
                        "
                        @click:clear="editedLeaveData.documentUpload = []"
                      >
                        <template v-slot:label>
                          Documents<span
                            v-if="editedLeaveData.isDocumentRequired"
                            style="color: red"
                            >*</span
                          >
                        </template>
                        <template v-slot:selection="{}">
                          <v-chip
                            v-for="(
                              file, index
                            ) in editedLeaveData.documentUpload"
                            :key="file?.name + '_' + index"
                            draggable
                            small
                            closable
                            @click:close="
                              editedLeaveData.documentUpload.splice(index, 1)
                            "
                          >
                            <span class="text-truncate">
                              {{
                                file.name.length > 25
                                  ? file.name.slice(0, 22) + "..."
                                  : file.name
                              }}
                            </span>
                          </v-chip>
                        </template>
                      </v-file-input></v-col
                    >
                    <!-- Alternate Person  (428)-->
                    <v-col
                      v-if="
                        labelList[428]?.Field_Visiblity?.toLowerCase() === 'yes'
                      "
                      cols="12"
                      sm="6"
                      class="px-md-6"
                    >
                      <CustomSelect
                        ref="Alternate_Person"
                        v-model="editedLeaveData.Alternate_Person"
                        :items="listAlternativeDetails"
                        itemTitle="Employee_Name"
                        itemValue="Manager_Id"
                        :loading="nameChangeLoding"
                        :label="labelList[428].Field_Alias"
                        variant="solo"
                        :select-properties="{
                          multiple: true,
                          chips: true,
                          clearable: true,
                          closableChips: true,
                        }"
                        :rules="[
                          leaveSettings?.Enforce_Alternate_Person_For_Leave?.toLowerCase() ===
                            'yes' ||
                          labelList[428].Mandatory_Field?.toLowerCase() ===
                            'yes'
                            ? required(
                                labelList[428].Field_Alias,
                                editedLeaveData.Alternate_Person
                              )
                            : true,
                        ]"
                        :is-required="
                          leaveSettings?.Enforce_Alternate_Person_For_Leave?.toLowerCase() ===
                            'yes' ||
                          labelList[428].Mandatory_Field?.toLowerCase() ===
                            'yes'
                        "
                        :isAutoComplete="true"
                        :itemSelected="editedLeaveData.Alternate_Person"
                        @selected-item="
                          onChangeFieldType($event, `Alternate_Person`)
                        "
                      />
                    </v-col>
                    <!-- Reason Type (427)-->
                    <v-col
                      v-if="
                        editedLeaveData.showReasonType &&
                        labelList[427]?.Field_Visiblity?.toLowerCase() === 'yes'
                      "
                      cols="12"
                      sm="6"
                      class="px-md-6"
                    >
                      <CustomSelect
                        ref="reasonType"
                        v-model="editedLeaveData.Reason_Type"
                        :items="reasonTypeList"
                        itemTitle="ESIC_Reason"
                        itemValue="Reason_Id"
                        :loading="isLoading"
                        :rules="[
                          editedLeaveData.isReasonTypeMandatory ||
                          labelList[427].Mandatory_Field?.toLowerCase() ===
                            'yes'
                            ? required(
                                labelList[427].Field_Alias,
                                editedLeaveData.Reason_Type
                              )
                            : true,
                        ]"
                        :label="labelList[427].Field_Alias"
                        :is-required="
                          editedLeaveData.isReasonTypeMandatory ||
                          labelList[427].Mandatory_Field?.toLowerCase() ===
                            'yes'
                        "
                        variant="solo"
                        :isAutoComplete="true"
                        :itemSelected="editedLeaveData.Reason_Type"
                        @selected-item="
                          onChangeFieldType($event, `Reason_Type`)
                        "
                      />
                    </v-col>
                    <!-- leaveExceptionRules -->
                    <v-col
                      v-if="editedLeaveData.showLeaveExceptionRules"
                      cols="12"
                      class="px-md-6"
                    >
                      <div class="text-subtitle-1 text-grey-darken-1">
                        Leave Exception Rules
                      </div>
                      <div class="text-subtitle-1 font-weight-regular">
                        <section class="text-body-2">
                          Only when the evidence for medical grounds is
                          submitted and authorised by the reviewing manager,
                          medical leave will be considered, in all other cases
                          half paid leave will be considered for the applied
                          leave days and an equivalent days are deducted as
                          unpaid leave.
                        </section>
                      </div>
                    </v-col>
                    <v-col
                      v-if="isMobileView && editedLeaveData.showCamuCalendar"
                      cols="12"
                      class="px-md-6"
                    >
                      <CamuCalender
                        :get-staff-schedules-by-date-range="
                          getStaffSchedulesByDateRange
                        "
                      />
                    </v-col>
                    <!-- Mobile View: Expansion Panel -->
                    <v-col
                      v-if="isMobileView && presentLeaveBalance"
                      cols="12"
                      class="px-md-6"
                    >
                      <v-expansion-panels>
                        <v-expansion-panel>
                          <!-- Panel Title -->
                          <v-expansion-panel-title>
                            <v-row no-gutters>
                              <v-col class="d-flex justify-start" cols="9">
                                <p class="text-subtitle-1 font-weight-bold">
                                  Leaves as on {{ todayDate }}
                                </p>
                              </v-col>
                              <v-col class="d-flex justify-end" cols="3">
                                <p class="text-subtitle-1 font-weight-bold">
                                  Day(s)
                                </p>
                              </v-col>
                            </v-row>
                          </v-expansion-panel-title>

                          <!-- Panel Content -->
                          <v-expansion-panel-text>
                            <v-row>
                              <v-col cols="9">
                                <div class="text-subtitle-1 text-truncate">
                                  <v-tooltip
                                    location="top"
                                    text="Leave taken during the leave year"
                                  >
                                    <template v-slot:activator="{ props }">
                                      <span v-bind="props" class="pa-1">
                                        Leave Taken
                                      </span>
                                    </template>
                                  </v-tooltip>
                                </div>
                              </v-col>
                              <v-col cols="3" class="text-center">
                                <span class="pr-2">{{
                                  checkNullValue(leaveBalanceData?.leavesTaken)
                                }}</span>
                              </v-col>

                              <v-col cols="9">
                                <div class="text-subtitle-1 text-truncate">
                                  <v-tooltip
                                    location="top"
                                    text="Current Leave Eligibility - Leave Taken"
                                  >
                                    <template v-slot:activator="{ props }">
                                      <span v-bind="props" class="pa-1">
                                        Leave Balance(Period Based)
                                      </span>
                                    </template>
                                  </v-tooltip>
                                </div>
                              </v-col>
                              <v-col cols="3" class="text-center">
                                <span class="pr-2">{{
                                  checkNullValue(
                                    leaveBalanceData?.leaveBalanceBasedOnPeriod
                                  )
                                }}</span>
                              </v-col>

                              <v-col cols="9">
                                <div class="text-subtitle-1 text-truncate">
                                  <v-tooltip
                                    location="top"
                                    text="Total Leave Eligibility - Leave Taken"
                                  >
                                    <template v-slot:activator="{ props }">
                                      <span v-bind="props" class="pa-1">
                                        Leave Balance(Per Annum)
                                      </span>
                                    </template>
                                  </v-tooltip>
                                </div>
                              </v-col>
                              <v-col cols="3" class="text-center">
                                <span class="pr-2">{{
                                  checkNullValue(
                                    leaveBalanceData?.leavesBalancePerAnnum
                                  )
                                }}</span>
                              </v-col>

                              <v-col cols="9">
                                <div class="text-subtitle-1 text-truncate">
                                  <v-tooltip
                                    location="top"
                                    max-width="300px"
                                    text="Current leave eligibility is based on leave period configuration and carry over balance"
                                  >
                                    <template v-slot:activator="{ props }">
                                      <span v-bind="props" class="pa-1">
                                        Current Leave Eligibility
                                      </span>
                                    </template>
                                  </v-tooltip>
                                </div>
                              </v-col>
                              <v-col cols="3" class="text-center">
                                <span class="pr-2">{{
                                  checkNullValue(
                                    leaveBalanceData?.currentEligibility
                                  )
                                }}</span>
                              </v-col>

                              <v-col cols="9">
                                <div class="text-subtitle-1 text-truncate">
                                  <v-tooltip
                                    location="top"
                                    max-width="300px"
                                    text="Total leave eligibility is based on leave year and carry over balance"
                                  >
                                    <template v-slot:activator="{ props }">
                                      <span v-bind="props" class="pa-1">
                                        Total Leave Eligibility
                                      </span>
                                    </template>
                                  </v-tooltip>
                                </div>
                              </v-col>
                              <v-col cols="3" class="text-center">
                                <span class="pr-2">{{
                                  checkNullValue(
                                    leaveBalanceData?.totalEligibility
                                  )
                                }}</span>
                              </v-col>
                            </v-row>
                          </v-expansion-panel-text>
                        </v-expansion-panel>
                      </v-expansion-panels>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- The Card Presentation -->
                <v-col v-if="!isMobileView" :cols="!isMobileView ? 5 : 0">
                  <v-card
                    v-if="presentLeaveBalance"
                    class="card-item d-flex pa-4 rounded-lg mr-2 mb-1 leaveBalanceDiv"
                    color="grey-lighten-5"
                    :style="`width:100%; height:auto`"
                  >
                    <v-row>
                      <v-col cols="9"
                        ><span
                          class="font-weight-bold text-subtitle-1 text-truncate"
                          >Leaves as on {{ todayDate }}</span
                        ></v-col
                      >
                      <v-col cols="3" class="text-center"
                        ><span class="pr-2 font-weight-bold text-subtitle-1"
                          >Day(s)</span
                        ></v-col
                      >
                      <v-col cols="9"
                        ><div class="text-subtitle-1 text-truncate">
                          <v-tooltip
                            location="top"
                            text="Leave taken during the leave year"
                          >
                            <template v-slot:activator="{ props }">
                              <span v-bind="props" class="pl-1">
                                Leave Taken
                              </span>
                            </template>
                          </v-tooltip>
                        </div></v-col
                      >
                      <v-col cols="3" class="text-center"
                        ><span class="pr-2">{{
                          leaveBalanceData?.leavesTaken
                            ? leaveBalanceData?.leavesTaken
                            : leaveBalanceData?.leavesTaken === 0
                            ? 0
                            : "-"
                        }}</span></v-col
                      >
                      <v-col cols="9"
                        ><div class="text-subtitle-1 text-truncate">
                          <v-tooltip
                            location="top"
                            text="Current Leave Eligibility - Leave Taken"
                          >
                            <template v-slot:activator="{ props }">
                              <span v-bind="props" class="pl-1">
                                Leave Balance(Period Based)
                              </span>
                            </template>
                          </v-tooltip>
                        </div></v-col
                      >
                      <v-col cols="3" class="text-center"
                        ><span class="pr-2">{{
                          checkNullValue(
                            leaveBalanceData?.leaveBalanceBasedOnPeriod
                          )
                        }}</span></v-col
                      >
                      <v-col cols="9"
                        ><div class="text-subtitle-1 text-truncate">
                          <v-tooltip
                            location="top"
                            text="Total Leave Eligibility - Leave Taken"
                          >
                            <template v-slot:activator="{ props }">
                              <span v-bind="props" class="pl-1">
                                Leave Balance(Per Annum)
                              </span>
                            </template>
                          </v-tooltip>
                        </div></v-col
                      >
                      <v-col cols="3" class="text-center"
                        ><span class="pr-2">{{
                          checkNullValue(
                            leaveBalanceData?.leavesBalancePerAnnum
                          )
                        }}</span></v-col
                      >
                      <v-col cols="9"
                        ><div class="text-subtitle-1 text-truncate">
                          <v-tooltip
                            location="top"
                            max-width="300px"
                            text="Current leave eligibility is based on leave period configuration and carry over balance"
                          >
                            <template v-slot:activator="{ props }">
                              <span v-bind="props" class="pl-1">
                                Current Leave Eligibility
                              </span>
                            </template>
                          </v-tooltip>
                        </div></v-col
                      >
                      <v-col cols="3" class="text-center"
                        ><span class="pr-2">{{
                          checkNullValue(leaveBalanceData?.currentEligibility)
                        }}</span></v-col
                      >
                      <v-col cols="9"
                        ><div class="text-subtitle-1 text-truncate">
                          <v-tooltip
                            location="top"
                            max-width="300px"
                            text="Total leave eligibility is based on leave year and carry over balance"
                          >
                            <template v-slot:activator="{ props }">
                              <span v-bind="props" class="pl-1">
                                Total Leave Eligibility
                              </span>
                            </template>
                          </v-tooltip>
                        </div></v-col
                      >
                      <v-col cols="3" class="text-center"
                        ><span class="pr-2">{{
                          checkNullValue(leaveBalanceData?.totalEligibility)
                        }}</span></v-col
                      >
                    </v-row>
                  </v-card>
                  <div
                    v-else-if="
                      !presentLeaveBalance &&
                      editedLeaveData.LeaveType_Id &&
                      !loaderIsTrue
                    "
                    class="d-flex align-center justify-center"
                    style="height: 100%; min-height: 200px"
                  >
                    <p class="text-body-1 text-primary text-center mx-8">
                      The 'Show Balance' option has been restricted by the
                      Platform Admin; therefore, leave balance will not be
                      displayed for this leave type.
                    </p>
                  </div>
                  <CamuCalender
                    v-if="editedLeaveData.showCamuCalendar"
                    :get-staff-schedules-by-date-range="
                      getStaffSchedulesByDateRange
                    "
                  />
                </v-col>
              </v-row>
            </v-form>
            <AppSnackBar
              v-if="showValidationAlert"
              :show-snack-bar="showValidationAlert"
              snack-bar-type="warning"
              timeOut="-1"
              @close-snack-bar="closeValidationAlert"
            >
              <template #custom-alert>
                <div
                  v-for="(validationMsg, index) of validationMessages"
                  :key="validationMsg + index"
                  class="text-subtitle-1"
                >
                  {{ validationMsg }}
                </div>
                <div class="d-flex justify-end">
                  <v-btn
                    class="mt-n5 secondary"
                    variant="text"
                    @click="closeValidationAlert()"
                  >
                    Close
                  </v-btn>
                </div>
              </template>
            </AppSnackBar>
          </div>
        </v-card-text>
      </v-card>
      <v-card class="overlay-footer bottom-0 position-fixed w-100">
        <div class="d-flex justify-end pa-4">
          <v-btn
            rounded="lg"
            class="mr-6"
            variant="outlined"
            @click="
              isFormDirty ? (openConfirmationPopup = true) : $emit('close-form')
            "
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            :disabled="isLoading || editedLeaveData.disabledSubmitButton"
            @click="validateCustomFields()"
          >
            Submit
          </v-btn>
        </div>
      </v-card>
    </template>
  </v-overlay>
  <AppLoading v-if="isLoading || frequencyLoading || calenderLoading" />
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onCloseOverlay()"
  />
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import CamuCalender from "./CamuCalender.vue";
import validationRules from "@/mixins/validationRules";
import {
  checkNullValue,
  getErrorCodesAndMessagesWithValidation,
} from "@/helper";
import moment from "moment";
import Cookies from "js-cookie";
import {
  LIST_LEAVE_HISTORY,
  GET_STAFF_SCHEDULES_BY_DATE_RANGE,
} from "@/graphql/workflow/approvalManagementQueries.js";
export default {
  name: "AddEditLeaveRequest",
  data() {
    return {
      // Fields
      editedLeaveData: {
        Leave_Id: 0,
        Employee_Id: null,
        LeaveType_Id: null,
        showReasonType: false,
        isReasonTypeMandatory: false,
        Reason_Type: null,
        Reason: null,
        Duration: null,
        Leave_Period: null,
        presentLeavePeriod: false,
        isLeavePeriodRequired: false,
        Hours: null,
        Alternate_Person: null,
        Comment: null,
        Contact_No: null,
        Timesheet_Hours: null,
        documentUpload: [],
        showDocumentUpload: false,
        isDocumentRequired: false,
        maxDaysForDocumentUpload: null,
        Total_Days: 0,
        showLeaveExceptionRules: false,
        showCamuCalendar: false,
        disabledSubmitButton: false,
        // Dates
        fromMenu: false,
        formattedFrom: "",
        Leave_From: null,
        fromTo: false,
        formattedTo: "",
        Leave_To: null,
        isLeaveToDisabled: false,
        freezeDates: [],
        minDateTo: null,
        maxDateTo: null,
        minDateFrom: null,
        maxDateFrom: null,
        leaveId: null,
      },
      // Leaves
      showAddEditForm: true,
      isLoading: false,
      errorsMessages: [],
      showValidationAlert: false,
      validationMessages: [],
      isFormDirty: false,
      openConfirmationPopup: false,
      leaveBalanceData: null,
      presentLeaveBalance: false,
      //Loaders, lists
      employeesLoading: false,
      listEmployees: [],
      leaveTypeLoading: false,
      leaveTypeList: [],
      leavePeriodList: [],
      leaveDurationList: [],
      nameChangeLoding: false,
      listAlternativeDetails: [],
      documentUploadArr: [],
      getStaffSchedulesByDateRange: null,
      calenderAPICallCount: 0,
      frequencyLoading: false,
      calenderLoading: false,
    };
  },
  components: {
    CustomSelect,
    CamuCalender,
  },
  mixins: [validationRules],
  emits: ["close-form", "edit-updated"],
  props: {
    selectedItem: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    callingFrom: {
      type: String,
      required: true,
    },
    formId: {
      type: Number,
      required: true,
    },
    leaveSettings: {
      type: Object,
      default: () => {},
    },
    formAccess: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
    reasonTypeList: {
      type: Array,
      required: true,
    },
  },
  computed: {
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    loaderIsTrue() {
      return (
        this.isLoading ||
        this.employeesLoading ||
        this.leaveTypeLoading ||
        this.nameChangeLoding ||
        this.frequencyLoading ||
        this.calenderLoading
      );
    },
    selectedReasonType() {
      return (
        this.reasonTypeList?.find(
          (reasonType) =>
            reasonType.Reason_Id == this.editedLeaveData.Reason_Type
        )?.ESIC_Reason || null
      );
    },
    todayDate() {
      return this.formatDate(moment().format("YYYY-MM-DD"));
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
  },
  watch: {
    "editedLeaveData.Leave_From": function (val) {
      if (val) {
        this.editedLeaveData.fromMenu = false;
        this.editedLeaveData.formattedFrom = this.formatDate(val);
        this.isFormDirty = true;
      } else this.editedLeaveData.formattedFrom = "";
    },
    "editedLeaveData.Leave_To": function (val) {
      if (val) {
        this.editedLeaveData.fromTo = false;
        this.editedLeaveData.formattedTo = this.formatDate(val);
        this.isFormDirty = true;
        this.leaveToChangeEvent(this.editedLeaveData.Leave_From, val);
      } else this.editedLeaveData.formattedTo = "";
    },
  },
  mounted() {
    if (this.isEdit) this.prefillDetails();
    else this.editedLeaveData.Employee_Id = String(this.loginEmployeeId);
    this.listEmployeeDetails();
    this.employeeNameChangeEvent(
      this.isEdit ? this.selectedItem.Employee_Id : this.loginEmployeeId
    );
    this.fetchEmployeeHistory(
      this.isEdit ? this.selectedItem?.Employee_Id : this.loginEmployeeId
    );
  },
  methods: {
    checkNullValue,
    onCloseOverlay() {
      this.showAddEditForm = false;
      this.$emit("close-form");
    },
    prefillDetails() {
      if (!this.selectedItem) return;
      let vm = this;
      let docs =
        JSON.parse(JSON.stringify(vm.selectedItem.Document_File_Path)) || [];

      docs =
        docs?.map((doc) => {
          // Ensure doc is a string before splitting
          return {
            name: doc.split("?")[3] || "", // Extract filename from string
            formattedName: doc, // Keep the full path
          };
        }) || [];

      vm.editedLeaveData = {
        Leave_Id: vm.selectedItem.Leave_Id || 0,
        Employee_Id: String(vm.selectedItem.Employee_Id) || null,
        LeaveType_Id: vm.selectedItem.LeaveType_Id || null,
        Reason_Type:
          vm.reasonTypeList.find(
            (x) => x.Reason_Id == vm.selectedItem.Reason_Id
          )?.Reason_Id || null,
        Reason: vm.selectedItem.Reason || null,
        Duration: String(vm.selectedItem.Leave_Duration) || null,
        Leave_Period: vm.selectedItem.Leave_Period || null,
        Hours: vm.selectedItem.Hours || null,
        Alternate_Person:
          vm.selectedItem.Alternate_Person_Id?.split(",") || null,
        Comment: vm.selectedItem.Comment || "",
        Contact_No: vm.selectedItem.Contact_Details || null,
        Timesheet_Hours: vm.selectedItem.Regular_Hours || null,
        Total_Days: vm.selectedItem.Total_Days || 0,

        // Dates
        formattedFrom: vm.formatDate(vm.selectedItem.Start_Date),
        Leave_From: new Date(vm.selectedItem.Start_Date) || null,
        formattedTo: vm.formatDate(vm.selectedItem.End_Date),
        Leave_To: new Date(vm.selectedItem.End_Date) || null,
        // minDateTo: vm.selectedItem.Start_Date || null,

        // Conditions
        showReasonType: vm.selectedItem.Reason_Id > 0, // Show only if Reason_Id exists
        isReasonTypeMandatory: vm.selectedItem.Reason_Id > 0,
        presentLeavePeriod: vm.selectedItem.Leave_Period ? true : false,
        isLeavePeriodRequired: vm.selectedItem.Leave_Period ? true : false,

        // Document Handling
        documentUpload: docs,
        showDocumentUpload: vm.selectedItem.Document_File_Path?.length > 0,
        isDocumentRequired: vm.selectedItem.Document_File_Path?.length > 0,

        leaveId: vm.selectedItem.Leave_Id || null,
      };

      // Handle additional fields like date ranges
      if (vm.selectedItem.Leave_Closure_Start_Date) {
        vm.editedLeaveData.minDateFrom =
          vm.selectedItem.Leave_Closure_Start_Date;
      }
      if (vm.selectedItem.Leave_Closure_End_Date) {
        vm.editedLeaveData.maxDateTo = vm.selectedItem.Leave_Closure_End_Date;
      }

      vm.leaveTypeActivationDate(vm.selectedItem.LeaveType_Id);
      vm.durationChangeEvent(vm.selectedItem.Leave_Duration);
    },
    /** Check if all uploaded files are within the size limit **/
    checkSize(item) {
      return () => {
        if (item.documentUpload?.length) {
          for (let doc of item.documentUpload) {
            if (doc.size > 3000000) {
              return "Each file should be less than 3 MB.";
            }
          }
        }
        return true;
      };
    },
    /** Prevent duplicate file uploads **/
    checkDuplicateFiles(item) {
      return () => {
        const fileNames = item.documentUpload?.map((doc) => doc.name);
        const uniqueFiles = new Set(fileNames);
        if (fileNames.length !== uniqueFiles.size) {
          return "Duplicate files detected. Please remove duplicates.";
        }
        return true;
      };
    },
    onUploadFile(files, item) {
      if (!files || files.length === 0) return;

      this.isFormDirty = true;
      // this.editedLeaveData.documentUpload = files || [];
      this.editedLeaveData.documentUpload = [...item.documentUpload, ...files];
      const timestamp = moment().unix();

      for (let doc of this.editedLeaveData.documentUpload) {
        doc.formattedName =
          this.editedLeaveData.Employee_Id +
          "?" +
          timestamp +
          "?" +
          `1` +
          "?" +
          doc.name;
      }
    },

    onChangeFieldType(value, type) {
      if (type == "Employee_Id" && value) {
        this.editedLeaveData.LeaveType_Id = null;
        this.editedLeaveData.Reason_Type = null;
        this.editedLeaveData.Reason = null;
        this.editedLeaveData.uploadedDocument = [];
        this.editedLeaveData.Duration = null;
        this.editedLeaveData.Leave_Period = null;
        this.editedLeaveData.Leave_From = null;
        this.editedLeaveData.Leave_To = null;
        this.editedLeaveData.Total_Days = null;
        this.editedLeaveData.minDateFrom = null;
        this.editedLeaveData.minDateTo = null;
        this.editedLeaveData.maxDateTo = null;
        this.editedLeaveData.maxDateFrom = null;
        this.leaveTypeList = [];
        this.leaveBalanceData = null;
        this.editedLeaveData.showDocumentUpload = false;
        this.editedLeaveData.documentUpload.length = 0;
        this.editedLeaveData.isDocumentRequired = false;
        this.showCalendar();
        this.employeeNameChangeEvent(value);
        this.fetchEmployeeHistory(value);
      }
      if (type === "LeaveType_Id" && value) {
        this.changeLeaveTypeActions(value);
      }
      if (type == "Duration" && value) {
        this.editedLeaveData.Leave_From = null;
        this.editedLeaveData.Leave_To = null;
        this.editedLeaveData.Total_Days = null;
        this.editedLeaveData.Leave_Period = null;
        this.errorsMessages.length = 0;
        this.durationChangeEvent(value);
      }
      if (type == "Leave_Period") {
        this.editedLeaveData.Leave_From = null;
        this.editedLeaveData.Leave_To = null;
        this.editedLeaveData.Total_Days = null;
        this.errorsMessages.length = 0;
        this.showCalendar();
      }

      if (type === "Leave_From") {
        this.editedLeaveData.Total_Days = null;
        this.leaveFromChangeEvent();
      }
      if (type === "Leave_To" && value) {
        this.editedLeaveData.Total_Days = null;
        this.leaveToChangeEvent(this.editedLeaveData.Leave_From, value);
      }
      this.isFormDirty = true;
    },
    changeLeaveTypeActions(value) {
      this.editedLeaveData.Duration = null;
      this.editedLeaveData.Leave_Period = null;
      this.editedLeaveData.Leave_From = null;
      this.editedLeaveData.Leave_To = null;
      this.editedLeaveData.Total_Days = null;
      this.errorsMessages.length = 0;
      this.editedLeaveData.showDocumentUpload = false;
      this.editedLeaveData.documentUpload.length = 0;
      this.editedLeaveData.isDocumentRequired = false;
      this.leaveTypeActivationDate(value);
      this.checkLeaveType(value);
    },
    async listEmployeeDetails() {
      let vm = this;
      try {
        vm.employeesLoading = true;
        let apiObj = {
          url: vm.baseUrl + "default/employee-info/list-employee-details",
          type: "POST",
          async: false,
          dataType: "json",
          data: {
            _fId: "Leaves",
          },
        };
        if (this.callingFrom?.toLowerCase() === `myteam`)
          apiObj.data.formId = parseInt(vm.formId);
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response) {
          let tempData = response;
          vm.listEmployees = tempData;
          if (!vm.isEdit) {
            const isMatching = vm.listEmployees?.some(
              (emp) => emp.value == vm.loginEmployeeId
            );
            if (!isMatching) {
              // reset teh prefilled value to null if loginEmployeeId is not matching with list dropdown
              vm.editedLeaveData.Employee_Id = null;
              // Close the self dervice add form when loginEmployeeId is not matching with list dropdown
              if (vm.callingFrom?.toLowerCase() === "selfservice") {
                vm.$emit("close-form");
                vm.showAlert({
                  isOpen: true,
                  type: "warning",
                  message:
                    "Your employee profile is incomplete or not yet finalized. Please update it or contact your HR team for assistance before submitting a leave request.",
                });
              }
            }
          }
          vm.employeesLoading = false;
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please try after some time.",
          };
          vm.listEmployees = [];
          vm.employeesLoading = false;
          vm.showAlert(snackbarData);
        }
      } catch (err) {
        vm.employeesLoading = false;
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: err || "Something went wrong. Please try again later.",
        });
      }
    },
    async employeeNameChangeEvent(Employee_Id = 0) {
      let vm = this;
      try {
        vm.nameChangeLoding = true;
        const apiObj = {
          url:
            vm.baseUrl +
            "default/employee-info/list-approver-details/employeeId/" +
            parseInt(Employee_Id) +
            "/formName/leaves",
          type: "POST",
          async: false,
          dataType: "json",
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response) {
          vm.nameChangeLoding = false;
          let result = response;

          // List Alternative details
          if (result.alternatePersonDetails?.length)
            this.listAlternativeDetails = result.alternatePersonDetails;

          // ✅ Check if Manager exists
          if (result.Manager) {
            if (result.ActualManager?.length) {
              // ✅ If Actual Manager Exists
              if (result.ActualManager[0]?.Mobile_No !== "")
                this.editedLeaveData.Contact_No =
                  result.ActualManager[0]?.Mobile_No || null;

              this.editedLeaveData.Timesheet_Hours =
                result.ActualManager[0]?.Regular_Hours || null;
            } else if (result.Manager?.length) {
              // ✅ If Manager Exists
              if (result.Manager[0]?.Mobile_No !== "")
                this.editedLeaveData.Contact_No =
                  result.Manager[0]?.Mobile_No || null;

              this.editedLeaveData.Timesheet_Hours =
                result.Manager[0]?.Regular_Hours || null;
            } else {
              // ✅ If no Actual Manager or Manager, fallback to IsAdmin
              if (result.IsAdmin[0]?.Mobile_No !== "")
                this.editedLeaveData.Contact_No =
                  result.IsAdmin[0]?.Mobile_No || null;

              this.editedLeaveData.Timesheet_Hours =
                result.IsAdmin[0]?.Regular_Hours || null;
            }
          } else {
            // ✅ If no Manager exists, check IsAdmin
            if (result.IsAdmin?.length) {
              if (result.IsAdmin[0]?.Mobile_No !== "")
                this.editedLeaveData.Contact_No =
                  result.IsAdmin[0]?.Mobile_No || null;
              this.editedLeaveData.Timesheet_Hours =
                result.IsAdmin[0]?.Regular_Hours || null;
            } else {
              // ✅ If no Manager and no Admin, reset fields and show alert
              this.editedLeaveData.Contact_No = "";
              this.editedLeaveData.Timesheet_Hours = "";
              this.showAlert({
                isOpen: true,
                type: "info",
                message: "No manager found",
              });
            }
          }
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please try after some time.",
          };
          vm.nameChangeLoding = false;
          vm.showAlert(snackbarData);
        }
      } catch (err) {
        vm.nameChangeLoding = false;
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: err || "Something went wrong. Please try again later.",
        });
      }
    },
    fetchEmployeeHistory(Employee_Id = 0) {
      let vm = this;
      vm.leaveTypeLoading = true;
      vm.$apollo
        .query({
          query: LIST_LEAVE_HISTORY,
          variables: {
            employeeId: parseInt(Employee_Id),
            source: "leave",
          },
          client: "apolloClientC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.empLeaveHistory &&
            response.data.empLeaveHistory.leaveHistory &&
            !response.data.empLeaveHistory.errorCode
          ) {
            let tempData = JSON.parse(
              response.data.empLeaveHistory.leaveHistory
            );
            if (tempData?.length) {
              vm.leaveTypeList = [];
              // Assign the options to the dropdown
              vm.leaveTypeList = tempData || [];
              if (vm.isEdit) vm.checkLeaveType(vm.selectedItem.LeaveType_Id);
              // Check if it's in edit mode or a pre-filled value exists
              if (vm.isEdit && vm.editedLeaveData.LeaveType_Id) {
                vm.editedLeaveData.LeaveType_Id = vm.selectedItem.LeaveType_Id;
              }
              // Trigger change event if value is prefilled
              if (vm.editedLeaveData.LeaveType_Id && !vm.isEdit) {
                // Need to add this function call after disussion
                this.editedLeaveData.Leave_From = null;
                this.editedLeaveData.Leave_To = null;
                this.editedLeaveData.Total_Days = null;
                this.editedLeaveData.Reason = null;
                this.editedLeaveData.Duration = null;
                this.editedLeaveData.Leave_Period = null;
              }
            } else
              this.showAlert({
                isOpen: true,
                type: "warning",
                message:
                  response.data.empLeaveHistory?.errorCode ||
                  "You are not applicable for any of the leave type(s).",
              });
          } else
            this.showAlert({
              isOpen: true,
              type: "warning",
              message: "You are not applicable for any of the leave type(s).",
            });

          vm.leaveTypeLoading = false;
        })
        .catch((error) => {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message:
              error || "Something went wrong. Please try after some time.",
          });
          vm.leaveTypeLoading = false;
        });
    },
    async leaveTypeActivationDate(LeaveType_Id = 0) {
      let vm = this;
      try {
        vm.isLoading = true;
        vm.editedLeaveData.minDateFrom = null;
        vm.editedLeaveData.maxDateFrom = null;
        vm.editedLeaveData.minDateTo = null;
        vm.editedLeaveData.maxDateTo = null;
        // Need to conform below line
        // this.showLvApplicableDateRangeInfo = false;

        const apiObj = {
          url: vm.baseUrl + "employees/leaves/leave-type-activation-date",
          type: "POST",
          async: false,
          dataType: "json",
          data: {
            LeaveType_Id: LeaveType_Id,
            Employee_Id: parseInt(this.editedLeaveData.Employee_Id),
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response) {
          // Handle Freeze Dates
          if (response.freezeDateRange && response.freezeDateRange.length) {
            this.editedLeaveData.freezeDates = response.freezeDateRange;
          } else {
            this.editedLeaveData.freezeDates = [];
          }

          // Set Minimum Leave Date Based on Activation and Other Dates
          let minLeaveDate = response.activationDate
            ? new Date(response.activationDate)
            : null;
          if (response.futureLeaveStartDate) {
            const futureLeaveStartDate = new Date(
              response.futureLeaveStartDate
            );
            if (minLeaveDate && minLeaveDate < futureLeaveStartDate) {
              minLeaveDate = futureLeaveStartDate;
            }
          }
          if (response.maxPayslipMnth) {
            const maxPayslipMonth = new Date(response.maxPayslipMnth);
            if (minLeaveDate && minLeaveDate < maxPayslipMonth) {
              minLeaveDate = maxPayslipMonth;
            }
          }
          if (response.leaveClosureStartDate) {
            const leaveClosureStartDate = new Date(
              response.leaveClosureStartDate
            );
            if (minLeaveDate && minLeaveDate < leaveClosureStartDate) {
              minLeaveDate = leaveClosureStartDate;
            }
          }
          this.editedLeaveData.minDateFrom =
            moment(minLeaveDate).format("YYYY-MM-DD");
          this.editedLeaveData.minDateTo =
            moment(minLeaveDate).format("YYYY-MM-DD");

          // Set Maximum Leave Date Based on Resignation Date or Closure End Date
          let maxLeaveDate = response.resignationDate
            ? new Date(response.resignationDate)
            : new Date(response.leaveClosureEndDate);
          this.editedLeaveData.maxDateFrom =
            moment(maxLeaveDate).format("YYYY-MM-DD");
          this.editedLeaveData.maxDateTo =
            moment(maxLeaveDate).format("YYYY-MM-DD");

          // (Need to ask php ologic then implement this)
          // Show Applicable Date Range Info
          // if (response.accumulateEligibleDaysEnabled && maxLeaveDate) {
          //   const formattedMaxDate = this.$dateFns.format(
          //     maxLeaveDate,
          //     "dd MMM yyyy"
          //   );
          //   this.lvApplicableDateRangeMsg = `You are allowed to apply leave till ${formattedMaxDate}`;
          //   this.showLvApplicableDateRangeInfo = true;
          // }

          vm.isLoading = false;
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please contact system admin",
          };
          vm.isLoading = false;
          vm.showAlert(snackbarData);
        }
      } catch (err) {
        vm.isLoading = false;
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: err || "Something went wrong. Please contact system admin",
        });
      }
    },
    async checkLeaveType(LeaveType_Id = 0) {
      let vm = this;
      try {
        vm.isLoading = true;
        const apiObj = {
          url: vm.baseUrl + "employees/leaves/check-leave-type",
          type: "POST",
          async: false,
          dataType: "json",
          data: {
            LeaveType_Id: LeaveType_Id,
            Employee_Id: parseInt(vm.editedLeaveData.Employee_Id),
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response) {
          // Handle Reason Type visibility and requirement
          if (response.Leave_Type === "Unpaid Leave") {
            vm.editedLeaveData.showReasonType = true;
            vm.editedLeaveData.isReasonTypeMandatory = true;
            if (!vm.isEdit) {
              vm.reasonTypeList?.find((item) => {
                if (item.ESIC_Reason?.toLowerCase() == "on leave") {
                  vm.editedLeaveData.Reason_Type =
                    Number(item.Reason_Id) || null;
                }
              });
            }
          } else {
            vm.editedLeaveData.showReasonType = false;
            vm.editedLeaveData.isReasonTypeMandatory = false;
          }

          // Handle Document Upload Visibility
          vm.documentUploadArr = response;
          let totalDays = parseFloat(response.Total_Days);
          let documentSubmission = parseFloat(response.Document_Upload);
          let maxDaysForDocumentUpload = parseFloat(
            response.Max_Days_For_Document_Upload
          );

          if (documentSubmission === 1) {
            if (maxDaysForDocumentUpload && totalDays) {
              if (totalDays >= maxDaysForDocumentUpload) {
                vm.editedLeaveData.showDocumentUpload = true;
                // vm.editedLeaveData.isDocumentRequired = true; // Present only when change date fileds
              } else {
                vm.editedLeaveData.showDocumentUpload = true;
                vm.editedLeaveData.isDocumentRequired = false;
              }
            } else {
              vm.editedLeaveData.showDocumentUpload = true;
              vm.editedLeaveData.isDocumentRequired = false;
            }
          } else {
            vm.editedLeaveData.showDocumentUpload = false;
          }

          // Handle Leave Exception Rules
          if (
            response.Enable_Leave_Exception === "Yes" &&
            documentSubmission === 1
          ) {
            vm.editedLeaveData.showLeaveExceptionRules = true;
          } else {
            vm.editedLeaveData.showLeaveExceptionRules = false;
          }

          // Handle Reason Type Selection in Edit Mode
          if (vm.isEdit && response.Leave_Type === "Unpaid Leave") {
            vm.editedLeaveData.Reason_Type = vm.selectedItem?.Reason_Id;
          }

          // Reset and Populate Leave Duration
          vm.leaveDurationList = [];
          if (response.leaveTypeDuration) {
            for (let key in response.leaveTypeDuration) {
              vm.leaveDurationList.push({
                value: key,
                text: response.leaveTypeDuration[key],
              });
            }
          }
          // Handle Leave Balance Data
          if (parseInt(response.Show_Statistics_In_Dashboard) === 1) {
            const filteredLeaveType = this.leaveTypeList.filter(
              (el) => el.LeaveType_Id == LeaveType_Id
            );
            if (filteredLeaveType?.length) {
              const leaveData = filteredLeaveType[0];
              const leavesTaken = leaveData.leavesTaken || 0;
              const eligibleDays = parseFloat(leaveData.eligibleDays || 0);
              const coDays = parseFloat(leaveData.coDays || 0);
              const currentYearEligibleDays = parseFloat(
                leaveData.currentYearEligibleDays || 0
              );
              const currentEligibility = eligibleDays + coDays;
              const totalEligibility = currentYearEligibleDays + coDays;

              let leaveBalanceBasedOnPeriod = 0;
              if (
                leaveData.accuralRestriction?.toLowerCase() === "yes" &&
                leaveData.Accrual === "Based on the custom configuration" &&
                [1, 3, 6].includes(parseInt(leaveData.Period))
              ) {
                leaveBalanceBasedOnPeriod = parseFloat(
                  leaveData.remainingDaysTillCurrentLeavePeriod
                );
              } else {
                leaveBalanceBasedOnPeriod = currentEligibility - leavesTaken;
              }
              let leavesBalancePerAnnum =
                parseFloat(totalEligibility) - parseFloat(leavesTaken);

              if (leaveData.showBalance === 1) {
                vm.leaveBalanceData = {
                  leavesTaken: Math.floor(leavesTaken * 100) / 100,
                  leaveBalanceBasedOnPeriod:
                    Math.floor(leaveBalanceBasedOnPeriod * 100) / 100,
                  leavesBalancePerAnnum:
                    Math.floor(leavesBalancePerAnnum * 100) / 100,
                  totalEligibility: Math.floor(totalEligibility * 100) / 100,
                  currentEligibility:
                    Math.floor(currentEligibility * 100) / 100,
                };
                vm.presentLeaveBalance = true;
              }
            } else {
              vm.presentLeaveBalance = false;
              vm.leaveBalanceData = null;
            }
          } else {
            vm.presentLeaveBalance = false;
            vm.leaveBalanceData = null;
          }
          vm.isLoading = false;
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please contact system admin",
          };
          vm.isLoading = false;
          vm.showAlert(snackbarData);
        }
      } catch (err) {
        vm.isLoading = false;
        vm.showAlert({
          isOpen: true,
          type: "warning",
          message: err || "Something went wrong. Please contact system admin",
        });
      }
    },
    durationChangeEvent(Duration = null) {
      const duration = Number(Duration);
      if (duration !== "") {
        // this.editedLeaveData.Leave_From = null;
        // this.editedLeaveData.Leave_To = null;
        // this.editedLeaveData.Total_Days = null;
        const leaveFrom = this.editedLeaveData.Leave_From;
        const regularHrs = this.editedLeaveData.Timesheet_Hours;

        switch (duration) {
          case 0.5:
            this.editedLeaveData.presentLeavePeriod = true;
            this.editedLeaveData.isLeavePeriodRequired = true;
            this.prefillLeavePeriodOptions("0.5");
            this.editedLeaveData.isLeaveToDisabled = true;
            if (leaveFrom) {
              this.editedLeaveData.Leave_To = leaveFrom;
              this.editedLeaveData.Total_Days = duration;
              this.editedLeaveData.Hours = duration * regularHrs;
              // Need to check this should we need to call it or not
              this.fnFrequencyCheck();
            }
            break;

          case 1:
            this.editedLeaveData.presentLeavePeriod = false;
            this.editedLeaveData.isLeavePeriodRequired = false;
            this.editedLeaveData.Leave_Period = null;
            this.editedLeaveData.isLeaveToDisabled = false;
            if (leaveFrom && this.editedLeaveData.Leave_To) {
              this.fnFrequencyCheck();
            }
            break;

          case 0.25:
            this.editedLeaveData.presentLeavePeriod = true;
            this.editedLeaveData.isLeavePeriodRequired = true;
            this.prefillLeavePeriodOptions("0.25");
            this.editedLeaveData.isLeaveToDisabled = true;
            if (leaveFrom) {
              this.editedLeaveData.Leave_To = leaveFrom;
              this.editedLeaveData.Total_Days = duration;
              this.editedLeaveData.Hours = duration * regularHrs;
              this.fnFrequencyCheck();
            }
            break;
        }
        // Need to connfirm wheather this should be called or not (Need for Stage -2)
        this.showCalendar();
      }
    },
    prefillLeavePeriodOptions(leaveDuration) {
      let periodList =
        leaveDuration == 0.5
          ? ["First Half", "Second Half"]
          : [
              "First Quarter",
              "Second Quarter",
              "Third Quarter",
              "Fourth Quarter",
            ];

      if (
        periodList.includes(this.editedLeaveData.Leave_Period && !this.isEdit)
      ) {
        return 1;
      } else {
        // if (!this.isEdit) this.editedLeaveData.Leave_Period = null;
        this.leavePeriodList = periodList;
      }
    },
    // Function to check frequency for leave
    async fnFrequencyCheck() {
      const {
        Leave_From,
        Leave_To,
        Leave_Id,
        Duration,
        LeaveType_Id,
        Employee_Id,
        Leave_Period,
        documentUpload,
      } = this.editedLeaveData;

      this.errorsMessages = [];

      // Validate Leave Dates
      if (Leave_From && Leave_To && Leave_From > Leave_To) {
        const errorMessage =
          "Leave To should be greater than or equal to Leave From.";
        if (!this.errorsMessages.includes(errorMessage)) {
          this.errorsMessages.push(errorMessage);
        }

        return;
      }

      // Prepare API request data
      const requestData = {
        leaveId: Number(Leave_Id) || 0,
        employeeId: Number(Employee_Id),
        leaveTypeId: Number(LeaveType_Id),
        leaveFrom: Leave_From ? moment(Leave_From).format("YYYY-MM-DD") : null,
        leaveTo: Leave_To ? moment(Leave_To).format("YYYY-MM-DD") : null,
        leavePeriod: Leave_Period,
        duration: Number(Duration),
        documentUpload: documentUpload || [],
      };

      if (this.frequencyLoading) return;
      this.frequencyLoading = true;
      try {
        let apiObj = {
          type: "POST",
          dataType: "json",
          // async: false,
          url: this.baseUrl + "employees/leaves/frequency-check",
          data: requestData,
        };
        const result = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (result) {
          // Handle Freeze Dates
          if (result.leaveFreeze && result.leaveFreeze > 0) {
            const errorMessage =
              "Leave dates are falling in a freeze period. Please select dates outside this period.";
            if (!this.errorsMessages.includes(errorMessage)) {
              this.errorsMessages.push(errorMessage);
            }
          }

          // Handle Leave Frequency Rules
          if (result.frequency) {
            const { startMsg, balance, ActivationDays } = result.frequency;
            if (startMsg && !this.errorsMessages.includes(startMsg))
              this.errorsMessages.push(startMsg);

            if (balance) {
              this.editedLeaveData.Hours = balance;
            }

            if (ActivationDays) {
              this.editedLeaveData.Total_Days = ActivationDays;
            }
          }

          // Handle Max Limit Rules
          if (result.maxlimit) {
            const { endMsg, duration, balance } = result.maxlimit;
            if (endMsg) {
              const errorMessage = endMsg.replace(/<\/?[^>]+(>|$)/g, "");
              if (!this.errorsMessages.includes(errorMessage)) {
                this.errorsMessages.push(errorMessage);
              }
            }

            if (duration != 0.5 && duration != 0.25) {
              this.editedLeaveData.Total_Days = duration;
              this.editedLeaveData.Hours = duration * result.regularHours;
            } else if (requestData.duration == 1 && duration == 0.5) {
              this.showAlert({
                isOpen: true,
                type: "warning",
                message:
                  "The selected date falls under a half-day week off, so a full-day leave cannot be applied. Please adjust the duration and submit your leave request.",
              });
            }

            if (balance) {
              const errorMessage = `Leave type cannot be used within ${balance} day(s).`;
              if (!this.errorsMessages.includes(errorMessage)) {
                this.errorsMessages.push(errorMessage);
              }
            }
          }

          // Handle Short Time Off
          if (result.shortTimeOffExist > 0) {
            this.showAlert({
              isOpen: true,
              type: "warning",
              message:
                "Warning : Short time off is already applied for this date. Submitting leave will override it. To revert the short time off balance, please cancel it or contact your HR/Admin.",
            });
          }
        }
      } catch (error) {
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: "Something went wrong. Please contact system admin.",
        });
      } finally {
        this.frequencyLoading = false;
      }
    },

    leaveFromChangeEvent() {
      if (!this.editedLeaveData.Leave_From) return;
      this.$nextTick(() => {
        const leaveFrom = this.editedLeaveData.Leave_From;
        const leaveType = this.editedLeaveData.LeaveType_Id;
        const duration = this.editedLeaveData.Duration;
        const leaveTo = this.editedLeaveData.Leave_To;
        // Update minDate for Leave_To
        this.editedLeaveData.minDateTo = moment(leaveFrom).format("YYYY-MM-DD");

        // If duration is half-day or quarter-day, trigger duration change
        if (duration == 0.5 || duration == 0.25) {
          this.durationChangeEvent(duration);
        }

        if (leaveType && duration) {
          if (leaveTo) {
            if (new Date(leaveFrom) > new Date(leaveTo)) {
              const errorMessage = `Start Date should be less than or equal to End Date`;
              if (!this.errorsMessages.includes(errorMessage)) {
                this.errorsMessages.push(errorMessage);
              }
              return;
            } else {
              const days = moment(leaveTo).diff(moment(leaveFrom), "days");

              if (days >= 0) {
                if (days === 0 && duration == 0.5) {
                  this.editedLeaveData.Total_Days = 0.5;
                  this.editedLeaveData.Leave_To = leaveFrom;
                } else if (days === 0 && duration == 0.25) {
                  this.editedLeaveData.Total_Days = 0.25;
                } else if (days > 0) {
                  this.editedLeaveData.Total_Days = days + 1;
                } else {
                  this.editedLeaveData.Total_Days = 0;
                  this.editedLeaveData.Hours = 0;
                }
              }
            }
            // Call frequency check function
            this.fnFrequencyCheck();
          }

          // Document upload conditions
          const totalDays = parseFloat(this.editedLeaveData.Total_Days);
          const maxDaysForDocumentUpload = parseFloat(
            this.documentUploadArr.Max_Days_For_Document_Upload || 0
          );

          if (
            totalDays &&
            maxDaysForDocumentUpload &&
            this.editedLeaveData.showDocumentUpload
          ) {
            this.editedLeaveData.isDocumentRequired =
              totalDays >= maxDaysForDocumentUpload;
          }

          // If leave duration is half or quarter, set Leave_To same as Leave_From
          if (duration == 0.5 || duration == 0.25) {
            this.editedLeaveData.Leave_To = leaveFrom;
          }
        }
        if (this.editedLeaveData.Duration == 1) this.showCalendar();
      });
    },
    leaveToChangeEvent(Leave_From, Leave_To) {
      if (!this.editedLeaveData.Leave_To) return;

      this.$nextTick(() => {
        const leaveTo = Leave_To;
        const leaveFrom = Leave_From;
        const leaveType = this.editedLeaveData.LeaveType_Id;
        const duration = this.editedLeaveData.Duration;

        // Ensure Leave_To is not before Leave_From
        if (leaveFrom && new Date(leaveFrom) > new Date(leaveTo)) {
          this.editedLeaveData.Leave_To = null;
          this.editedLeaveData.Total_Days = null;
          return;
        }

        if (leaveFrom && leaveType && duration) {
          this.fnFrequencyCheck();

          // Update Total_Days calculation
          const days = moment(leaveTo).diff(moment(leaveFrom), "days");

          if (days >= 0) {
            this.editedLeaveData.Total_Days =
              days === 0 && (duration == 0.5 || duration == 0.25)
                ? duration
                : days + 1;
          } else {
            this.editedLeaveData.Total_Days = 0;
            this.editedLeaveData.Hours = 0;
          }

          // Document upload conditions
          const totalDays = parseFloat(this.editedLeaveData.Total_Days);
          const maxDaysForDocumentUpload = parseFloat(
            this.documentUploadArr.Max_Days_For_Document_Upload || 0
          );
          if (
            totalDays &&
            maxDaysForDocumentUpload &&
            this.editedLeaveData.showDocumentUpload
          ) {
            this.editedLeaveData.isDocumentRequired =
              totalDays >= maxDaysForDocumentUpload;
          }
        }
        if (this.editedLeaveData.Duration == 1) this.showCalendar();
      });
    },
    showCalendar() {
      let vm = this;
      vm.editedLeaveData.showCamuCalendar = false;
      vm.editedLeaveData.disabledSubmitButton = false;
      let partnerId = Cookies.get("partnerid") || "-";

      if (
        vm.leaveSettings?.Enable_CAMU_Scheduler?.toLowerCase() === "yes" &&
        partnerId?.toLowerCase() === "camu"
      ) {
        let {
          Leave_From: leaveFrom,
          Leave_To: leaveTo,
          Leave_Period: leavePeriod,
          Duration: duration,
          Employee_Id: employeeId,
        } = vm.editedLeaveData;
        if (
          leaveFrom &&
          leaveTo &&
          (duration == "1" || (leavePeriod && duration))
        ) {
          let currentDate = new Date();
          currentDate.setHours(0, 0, 0, 0);

          if (new Date(leaveTo) >= currentDate) {
            if (vm.calenderLoading) return;
            vm.calenderLoading = true;
            let staffId = "";
            const findEmployeeUserdefinedId =
              vm.listEmployees?.find((item) => item.value == employeeId)
                ?.User_Defined_EmpId || "";

            if (!vm.isEdit) {
              staffId = findEmployeeUserdefinedId;
            } else if (vm.calenderAPICallCount < 3) {
              staffId = vm.selectedItem?.User_Defined_EmpId || "";
              vm.calenderAPICallCount++;
            } else {
              staffId = findEmployeeUserdefinedId;
            }

            vm.$apollo
              .query({
                query: GET_STAFF_SCHEDULES_BY_DATE_RANGE,
                client: "apolloClientAG",
                variables: {
                  staffId: staffId,
                  fromDate: moment(leaveFrom).isValid()
                    ? moment(leaveFrom).format("YYYY-MM-DD")
                    : null,
                  toDate: moment(leaveTo).isValid()
                    ? moment(leaveTo).format("YYYY-MM-DD")
                    : null,
                  duration,
                  leavePeriod,
                },
                fetchPolicy: "no-cache",
              })
              .then((response) => {
                if (
                  response &&
                  response.data &&
                  response.data.getStaffSchedulesByDateRange &&
                  !response.data.getStaffSchedulesByDateRange.errorCode
                ) {
                  const { processedSchedules } =
                    response.data.getStaffSchedulesByDateRange;
                  let hasConflict = false;
                  processedSchedules.forEach((event) => {
                    if (event.isScheduled && duration == "1") {
                      hasConflict = true;
                    } else if (duration == "0.25" || duration == "0.5") {
                      if (event.isScheduled && event.isScheduledPartially) {
                        hasConflict = true;
                      }
                    }
                  });
                  if (hasConflict) {
                    vm.getStaffSchedulesByDateRange =
                      response.data.getStaffSchedulesByDateRange;
                    vm.editedLeaveData.showCamuCalendar = true;
                    vm.editedLeaveData.disabledSubmitButton = true;
                    vm.showAlert({
                      isOpen: true,
                      type: "info",
                      message:
                        "You have sessions scheduled during this period and requires to be delegated to others in CAMU before applying the leave",
                    });
                  } else {
                    vm.editedLeaveData.disabledSubmitButton = false;
                    vm.showAlert({
                      isOpen: true,
                      type: "info",
                      message:
                        "You dont have any schedule for the selected date(s) and duration.",
                    });
                  }
                } else {
                  vm.handleApiErrors(response);
                }
                vm.calenderLoading = false;
              })
              .catch((err) => {
                vm.calenderLoading = false;
                vm.editedLeaveData.disabledSubmitButton = true;
                vm.handleApiErrors(err);
              });
          }
        }
      }
    },
    handleApiErrors(error = null) {
      return new Promise((resolve) => {
        // Default error message
        let errorMessage = "Something went wrong. Please try again later.";
        let secondMsg = " Please try after some time.";

        // Handle CAMU specific error codes
        if (error?.errors && error.errors.length > 0) {
          let errorCode = error.errors[0]?.extensions?.code;

          switch (errorCode) {
            case "DB0000":
              errorMessage =
                "There seems to be some technical issues. Please try after some time.";
              break;
            case "EI00105":
              this.editedLeaveData.disabledSubmitButton = false;
              return resolve();
            case "EI00111":
              errorMessage =
                "Unable to process the request for staff schedules based on a specific date range";
              break;
            case "EI00106":
              errorMessage = "Please provide a valid staff ID.";
              break;
            case "EI00107":
              errorMessage = "Staff details not found in CAMU.";
              break;
            case "EI00109":
              errorMessage = "Please provide valid date range";
              break;
            case "EI00110":
              errorMessage =
                "Please provide the date in the following format (YYYY-MM-DD). You can configure the same in organization settings.";
              break;
          }
        }
        // Handle GraphQL errors
        else if (
          error &&
          error.graphQLErrors &&
          error.graphQLErrors.length > 0
        ) {
          // error capture
          var errorCode = getErrorCodesAndMessagesWithValidation(error);
          if (errorCode && errorCode.length > 0) {
            switch (errorCode[0]) {
              case "_DB0000": // Technical error
                errorMessage =
                  "It's us! There seems to be some technical difficulties. Please try after some time - _DB0000";
                break;
              case "DB0100": // This employee do not have view access rights
                errorMessage =
                  "Sorry, you don't have access rights. Please contact the HR administrator - DB0100";
                break;
              case "DB0111": // This employee do not have edit access rights
                errorMessage =
                  "Sorry, you don't have edit access rights. Please contact the HR administrator - DB0111";
                break;
              case "_UH0001": // unhandled error
                errorMessage =
                  errorMessage +
                  " If you continue to see this issue please contact the platform administrator - _UH0001";
                break;
              case "ESS0126":
              case "EI00105":
                this.editedLeaveData.disabledSubmitButton = false;
                return resolve();
              case "BAD_USER_INPUT":
                if (errorCode[2]) {
                  errorMessage = "";
                  return resolve(errorCode[2]);
                }
                break;
              default:
                errorMessage =
                  errorCode[1] + " - " + errorCode[0] ||
                  errorMessage + secondMsg;
                break;
            }
          }
        } else {
          errorMessage = errorMessage + secondMsg;
        }

        if (errorMessage) {
          this.editedLeaveData.disabledSubmitButton = true;
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: errorMessage,
          });
        }

        resolve(errorMessage);
      });
    },
    async validateCustomFields() {
      // checking whether the fields of form are valid or not
      const { valid } = await this.$refs.addEditFormValidator.validate();
      // submit the form only if all the fields are filled
      if (valid && this.errorsMessages.length == 0) {
        this.validateDocuments();
      }
    },
    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        let neededFiles =
          this.editedLeaveData.documentUpload?.filter((doc) => doc.size) || [];
        if (neededFiles?.length) {
          for (let i = 0; i < neededFiles.length; i++) {
            await this.uploadLeaveFile(neededFiles[i]);
          }
        }
        this.addEditLeaveRequest();
      } catch (error) {
        this.isLoading = false;
        this.showAlert({
          isOpen: true,
          type: "warning",
          message:
            "Something went wrong while uploading documents. Please try again.",
        });
      }
    },
    async addEditLeaveRequest() {
      let vm = this;
      this.isLoading = true;

      // Handle document upload rules
      let maxDaysForDocumentUpload =
        parseFloat(this.documentUploadArr.Max_Days_For_Document_Upload) || 0;
      let documentSubmission = null;

      if (this.editedLeaveData.showLeaveExceptionRules) {
        documentSubmission = this.getLeaveExceptionDocumentSubmission(
          this.editedLeaveData.documentUpload,
          this.editedLeaveData.Total_Days,
          maxDaysForDocumentUpload
        );

        if (documentSubmission === 0) {
          maxDaysForDocumentUpload = 0;
        }
      } else {
        documentSubmission = parseFloat(this.documentUploadArr.Document_Upload);
        maxDaysForDocumentUpload = parseFloat(
          this.documentUploadArr.Max_Days_For_Document_Upload
        );
      }
      if (!maxDaysForDocumentUpload) maxDaysForDocumentUpload = 0;
      // Prepare leaveDocuments array with only name and size
      const leaveDocuments =
        this.editedLeaveData.documentUpload
          ?.filter((doc) => doc.size) // Remove items without a size
          ?.map((doc) => ({
            Name: doc.formattedName, // Use formatted name for API
            Size: doc.size, // Keep file size
          })) || [];

      let deleteFiles = [];

      if (vm.isEdit)
        deleteFiles =
          vm.selectedItem.Document_File_Path?.filter((doc) => {
            return vm.editedLeaveData.documentUpload?.some((uploadedDoc) => {
              return uploadedDoc.formattedName != doc;
            });
          }) || [];

      // Delete Documents files
      if (deleteFiles?.length)
        for (let i = 0; i < deleteFiles.length; i++) {
          await this.deleteDucumentsFile(deleteFiles[i]);
          await this.removeFileContent(deleteFiles[i]);
        }
      // Prepare payload
      const payload = {
        url: this.baseUrl + "employees/leaves/update-leave",
        type: "POST",
        // async: false,
        dataType: "json",
        data: {
          Leave_Id: this.isEdit ? this.selectedItem.Leave_Id : 0,
          Employee_Id: parseInt(this.editedLeaveData.Employee_Id),
          Forward_To: "",
          Org_Enable_Workflow: this.leaveSettings?.Enable_Workflow || "",
          Org_Enforce_Comment_For_Leave:
            this.leaveSettings?.Enforce_Comment_For_Leave || "No",
          LeaveType_Id: this.editedLeaveData.LeaveType_Id,
          Reason_Type: this.editedLeaveData.Reason_Type || "",
          ESIC_Reason: this.selectedReasonType || "",
          Reason: this.editedLeaveData.Reason || "",
          Duration: parseFloat(this.editedLeaveData.Duration),
          Leave_Period: this.editedLeaveData.Leave_Period || "",
          Leave_From: moment(this.editedLeaveData.Leave_From).isValid()
            ? moment(this.editedLeaveData.Leave_From).format("YYYY-MM-DD")
            : null,
          Leave_To: moment(this.editedLeaveData.Leave_To).isValid()
            ? moment(this.editedLeaveData.Leave_To).format("YYYY-MM-DD")
            : null,
          Total_Days: parseFloat(this.editedLeaveData.Total_Days),
          Hours: this.editedLeaveData.Hours
            ? parseFloat(this.editedLeaveData.Hours)
            : null,
          Contact_No: Number(
            (this.editedLeaveData.Contact_No || "").replace(/\s+/g, "")
          ),
          Alternate_Person: this.editedLeaveData.Alternate_Person,
          Comment: this.editedLeaveData.Comment,
          documentUpload: documentSubmission,
          maxDaysForDocumentUpload: maxDaysForDocumentUpload,
          leaveDocuments: leaveDocuments,
        },
      };
      try {
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          payload
        );

        if (response?.success) {
          vm.editedLeaveData.leaveId = response?.leaveId;

          this.showAlert({
            isOpen: true,
            type: "success",
            message:
              response?.msg ||
              `Leave request ${
                this.isEdit ? "updated" : "added"
              } successfully.`,
          });
          this.$emit("edit-updated");
        } else {
          this.showAlert({
            isOpen: true,
            type: response?.type || "warning",
            message: response?.msg || "Something went wrong. Please try again.",
          });
        }
      } catch (error) {
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: error || "Something went wrong. Please try again.",
        });
      } finally {
        this.isLoading = false;
      }
    },
    async uploadLeaveFile(file) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Leave Documents/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + file.formattedName,
          action: "upload",
          type: "documents",
          fileContent: file,
        })
        .catch((error) => {
          throw error;
        });
    },
    async deleteDucumentsFile(files) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Leave Documents/";
      await vm.$store.dispatch("deletes3File", {
        fileName: fileUploadUrl + files,
        type: "documents",
      });
    },
    async removeFileContent(fileName) {
      let apiobj = {
        url:
          this.baseUrl +
          "employees/leaves/remove-uploaded-files/leaveId/" +
          this.selectedItem.Leave_Id,
        type: "POST",
        dataType: "json",
        data: {
          _leaveDocumentsUploadFileName: fileName,
        },
      };
      await this.$store.dispatch("triggerControllerFunction", apiobj);
    },
    getLeaveExceptionDocumentSubmission(
      uploadedDocument,
      totalDays,
      maxDaysForDocumentUpload
    ) {
      const enableLeaveException =
        this.documentUploadArr.Enable_Leave_Exception || "";

      return enableLeaveException === "Yes" &&
        uploadedDocument.length > 0 &&
        totalDays >= maxDaysForDocumentUpload
        ? 1
        : 0;
    },
    disableSpecificDates(date) {
      let dateObj = moment(date).format("YYYY-MM-DD");
      return !this.editedLeaveData.freezeDates.includes(dateObj);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
<style scoped>
#integration-form > .v-overlay__content {
  height: 100%;
  width: 50%;
}
.fixed-title {
  position: sticky;
  top: 0;
  z-index: 10;
}
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}

.leaveBalanceDiv {
  box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px,
    rgba(17, 17, 26, 0.1) 0px 8px 24px, rgba(17, 17, 26, 0.1) 0px 16px 56px;
}
</style>
