<template>
  <v-card class="rounded-lg mt-2">
    <!-- Header Section -->
    <div
      class="d-flex align-center"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center pl-4 py-2">
        <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
          <v-icon class="text-primary" size="20">fas fa-tasks</v-icon>
        </v-avatar>

        <section style="max-width: 250px" class="text-truncate">
          <div class="text-subtitle-1 font-weight-bold d-flex align-center">
            <!-- Tooltip for Employee_Type_Code (only if length > 5) -->
            <template
              v-if="
                selectedFormData.Employee_Type_Code &&
                selectedFormData.Employee_Type_Code.length > 5
              "
            >
              <v-tooltip
                location="bottom"
                :text="selectedFormData.Employee_Type_Code"
              >
                <template v-slot:activator="{ props }">
                  <v-card variant="flat" v-bind="props">
                    <span class="d-inline-block">
                      {{
                        selectedFormData.Employee_Type_Code.slice(0, 5) + "..."
                      }}
                    </span>
                  </v-card>
                </template>
              </v-tooltip>
            </template>
            <template v-else>
              <v-card variant="flat">
                <span class="d-inline-block">{{
                  selectedFormData.Employee_Type_Code
                }}</span>
              </v-card>
            </template>

            <!-- Dash between Employee_Type_Code and Employee_Type -->
            <span
              v-if="
                selectedFormData.Employee_Type_Code &&
                selectedItem.Employee_Type
              "
              class="text-bold mx-1"
              >-</span
            >

            <!-- Tooltip for Employee_Type (only if length > 20) -->
            <template
              v-if="
                selectedItem.Employee_Type &&
                selectedItem.Employee_Type.length > 20
              "
            >
              <v-tooltip location="bottom" :text="selectedItem.Employee_Type">
                <template v-slot:activator="{ props }">
                  <v-card variant="flat" v-bind="props">
                    <span class="d-inline-block">
                      {{ selectedItem.Employee_Type.slice(0, 20) + "..." }}
                    </span>
                  </v-card>
                </template>
              </v-tooltip>
            </template>
            <template v-else>
              <v-card variant="flat">
                <span class="d-inline-block">{{
                  selectedItem.Employee_Type
                }}</span>
              </v-card>
            </template>
          </div>
        </section>
      </div>

      <div class="d-flex align-center">
        <!-- Edit Button -->
        <v-tooltip placement="bottom">
          <template v-slot:activator="{ props }">
            <v-btn
              v-if="accessRights.update"
              v-bind="selectedItem.isAssociated ? props : ''"
              @click="$emit('open-edit-form')"
              size="small"
              color="primary"
              rounded="lg"
              >Edit</v-btn
            >
          </template>
        </v-tooltip>
        <!-- Close Button -->
        <v-icon class="mx-1" color="primary" @click="$emit('close-form')">
          fas fa-times
        </v-icon>
      </div>
    </div>

    <!-- Main Content Section -->
    <div style="height: calc(100vh - 300px); overflow: scroll">
      <v-card-text>
        <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
          <!-- Level -->
          <v-col
            v-if="entomoIntegrationEnabled && isEntomoSyncTypePush"
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
          >
            <p class="text-subtitle-1 text-grey-darken-1">Level</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Level) }}
            </p>
          </v-col>

          <v-col
            v-if="
              labelList[315]?.Field_Visiblity.toLowerCase() === 'yes' ||
              entomoIntegrationEnabled
            "
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[315]?.Field_Alias || "Employee Code" }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Employee_Type_Code) }}
            </p>
          </v-col>
          <!-- Employee Type -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Employee Type</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Employee_Type) }}
            </p>
          </v-col>

          <!-- Work Schedule -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Work Schedule</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Work_Schedule) }}
            </p>
          </v-col>

          <!-- Eligible for Benefits -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Eligible for Benefits
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ mapBinary(selectedItem.Benefits_Applicable) }}
            </p>
          </v-col>

          <!-- Holiday Eligibility -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Holiday Eligibility
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ mapBinary(selectedItem.Holiday_Eligiblity) }}
            </p>
          </v-col>
          <!-- Salary Calculation Days -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Salary Calculation Days
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Salary_Calc_Days_Flag) }}
            </p>
          </v-col>

          <!-- Fixed Days -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Fixed Days</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Fixed_Days) }}
            </p>
          </v-col>

          <!-- Comp Off Calculation Days -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Comp Off Calculation Days
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Comp_Off_Days_Flag) }}
            </p>
          </v-col>

          <!-- Display Duration in Hours and Minutes -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Display Duration in Hours and Minutes
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ mapBinary(selectedItem.Display_Total_Hours_In_Minutes) }}
            </p>
          </v-col>

          <!-- Exclude Break Hours -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Exclude Break Hours
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ mapBinary(selectedItem.Exclude_Break_Hours) }}
            </p>
          </v-col>

          <!-- Processed Biometric Attendance Record Status -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Processed Biometric Attendance Record Status
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Attendance_Process_Status) }}
            </p>
          </v-col>

          <!-- Enable Work Place -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Enable Work Place</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ mapBinary(selectedItem.Enable_Work_Place) }}
            </p>
          </v-col>

          <!-- Approve Dashboard Attendance -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Approve Dashboard Attendance
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                checkNullValue(selectedFormData.Approve_Dashboard_Attendance)
              }}
            </p>
          </v-col>
          <!-- Auto Approval Work Places -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Auto Approval Work Places
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Auto_Approval_Work_Place) }}
            </p>
          </v-col>

          <!-- Work Place -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Work Place</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Work_Place) }}
            </p>
          </v-col>

          <!-- Status -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Status</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.EmployeeType_Status) }}
            </p>
          </v-col>

          <!-- Description -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Description</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.Description) }}
            </p>
          </v-col>

          <!-- Additional Details Component -->
          <v-col cols="12">
            <MoreDetails :more-details-list="prefillMoreDetails"></MoreDetails>
          </v-col>
        </v-row>
      </v-card-text>
    </div>
  </v-card>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import moment from "moment";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
export default defineComponent({
  name: "ViewEmployeeType",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    moreDetailsList: [],
  }),
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    prefillMoreDetails() {
      let detailsList = [];
      const Added_On = this.formatDate(this.selectedItem.Added_On + ".000Z"),
        Added_By_Name = this.selectedItem.Added_By_Name,
        Updated_By_Name = this.selectedItem.Updated_By_Name,
        Updated_On = this.formatDate(this.selectedItem.Updated_On + ".000Z");
      let addedDateLocal = this.convertUTCToLocal(this.selectedItem.Added_On);
      let updateDateLocal = this.convertUTCToLocal(
        this.selectedItem.Updated_On
      );

      if (Added_On && Added_By_Name) {
        detailsList.push({
          actionDate: Added_On,
          actionBy: Added_By_Name,
          text: "Added",
          actionDateLocal: addedDateLocal,
        });
      }
      if (Updated_By_Name && Updated_On) {
        detailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By_Name,
          text: "Updated",
          actionDateLocal: updateDateLocal,
        });
      }
      return detailsList;
    },
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);

        // Check if Work_Place is a string before splitting
        if (
          this.selectedFormData.Work_Place &&
          typeof this.selectedFormData.Work_Place === "string"
        ) {
          this.selectedFormData.Work_Place =
            this.selectedFormData.Work_Place.split(",").join(", ");
        } else {
          this.selectedFormData.Work_Place = "";
        }
        if (
          this.selectedFormData.Auto_Approval_Work_Place &&
          typeof this.selectedFormData.Auto_Approval_Work_Place === "string"
        ) {
          this.selectedFormData.Auto_Approval_Work_Place =
            this.selectedFormData.Auto_Approval_Work_Place.split(",").join(
              ", "
            );
        } else {
          this.selectedFormData.Auto_Approval_Work_Place = "";
        }
      },
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    mapBinary(value) {
      return value === 1 ? "Yes" : "No";
    },
  },
});
</script>
