<template>
  <AppAccessDenied v-if="!jobCandidateFormAccess"></AppAccessDenied>
  <div v-else>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-if="originalData.length > 0" justify="center">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isFilter="false"
              :isApplyFilter="false"
              :list-items="originalData"
              :reset-filter-count="resetFilterCount"
              :appliedFilterCount="appliedFilterCount"
              ref="formFilterRef"
              @reset-emp-filter="resetFilter()"
              @applied-filter="onApplyFilter($event)"
            >
            </EmployeeDefaultFilterMenu>
            <PendingFeedbackFilter
              :dropdown="originalData"
              @apply-filter="applyFilterFromComponent($event)"
              @reset-filter="resetFilter()"
            ></PendingFeedbackFilter>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container fluid class="onboarding-container">
      <div v-if="listLoading" class="mt-3">
        <v-skeleton-loader
          ref="skeleton1"
          type="table-heading"
          class="mx-auto"
        ></v-skeleton-loader>
        <div v-for="i in 4" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <AppFetchErrorScreen
        v-else-if="isErrorInList"
        :content="errorContent"
        key="error-screen"
        icon-name="fas fa-redo-alt"
        image-name="common/human-error-image"
        button-text="Retry"
        @button-click="refetchList()"
      ></AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="originalData.length === 0"
        key="no-data-screen"
        :main-title="emptyScenarioMsg"
        :isSmallImage="!isFilter"
        :image-name="!isFilter ? '' : 'common/no-records'"
      >
        <template v-if="!isFilter" #contentSlot>
          <div style="max-width: 80%">
            <v-row
              class="rounded-lg pa-5 mb-4"
              :style="!isFilter ? 'background: white' : ''"
            >
              <v-col cols="12">
                <NotesCard
                  notes="There is no feedback pending."
                  backgroundColor="transparent"
                  class="mb-4"
                ></NotesCard>
              </v-col>
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-2 mt-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchPendingFeedback()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="itemList.length === 0"
        key="no-results-screen"
        main-title="There are no Pending Feedback matched for the selected filters/searches."
        image-name="common/no-records"
      >
        <template #contentSlot>
          <div style="max-width: 80%">
            <v-row class="rounded-lg pa-5 mb-4">
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <v-btn
                  color="primary"
                  variant="elevated"
                  class="ml-4 mt-1"
                  rounded="lg"
                  @click="refetchList()"
                >
                  Reset Filter/Search
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <div v-else>
        <div v-if="showViewForm">
          <JobCandidatesDetails
            :formAccess="jobCandidateFormAccess"
            :selectedJobCandidateId="
              selectedItem ? selectedItem.Candidate_Id : 0
            "
            :selectedJobPostId="selectedItem ? selectedItem.Job_Post_Id : 0"
            :selectedJobPanelMembers="
              selectedItem ? selectedItem.Panel_Member_Id : 0
            "
            :candidateChangeCount="candidateChangeCount"
            :candidateList="itemList"
            :selectedItem="selectedItem"
            :isRecruiter="userIsRecruiter"
            @retrieve-error="closeViewForm($event)"
            @close-view-form="closeViewForm($event)"
            @on-change-candidate="onChangeCandidate($event)"
          ></JobCandidatesDetails>
        </div>
        <div v-else>
          <div
            v-if="!showViewForm"
            class="d-flex align-center"
            :class="
              isMobileView
                ? 'd-flex flex-wrap align-center my-6 justify-center flex-column'
                : 'd-flex flex-wrap align-center my-4 justify-end'
            "
          >
            <v-btn
              color="transparent"
              variant="flat"
              class="mt-1"
              @click="refetchList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu class="mb-1" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="transparent" class="mt-1" v-bind="props">
                  <v-icon>fas fa-ellipsis-v</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'pink-lighten-5': isHovering,
                        }"
                        ><v-icon size="15" class="pr-2">{{
                          action.icon
                        }}</v-icon
                        >{{ action.key }}</v-list-item-title
                      >
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
          <v-row>
            <v-col>
              <v-data-table
                :headers="headers"
                :items="itemList"
                :items-per-page="50"
                fixed-header
                :height="
                  itemList.length > 11 ? $store.getters.getTableHeight(270) : ''
                "
                item-value="name"
                class="elevation-1"
                style="box-shadow: none !important"
              >
                <template v-slot:item="{ item }">
                  <tr
                    style="z-index: 200"
                    class="data-table-tr bg-white cursor-pointer"
                    @click="onOpenViewForm(item)"
                    :class="[
                      isMobileView
                        ? ' v-data-table__mobile-table-row ma-0 mt-2'
                        : '',
                    ]"
                  >
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Candidate
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 200px"
                      >
                        <span class="text-primary font-weight-regular">
                          {{ checkNullValue(item.Candidate_Name) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Job Title
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 200px"
                      >
                        <span class="text-primary font-weight-regular">
                          {{ checkNullValue(item.Job_Post_Name) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Interview Round Name
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Round_Name) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Panel Members
                      </div>
                      <span class="text-body-2 font-weight-regular">
                        <div
                          v-if="panelMembers(item.Panel_Member_Name).length > 0"
                        >
                          <v-menu max-width="200px" open-on-hover
                            ><template v-slot:activator="{ props }">
                              <AvatarOrderedList
                                v-bind="props"
                                :hideToolTip="true"
                                :maximumListLength="3"
                                empNameKey="panelMember"
                                :ordered-list="
                                  panelMembers(item.Panel_Member_Name)
                                "
                              ></AvatarOrderedList>
                            </template>
                            <v-card class="rounded-lg">
                              <v-card-text>
                                <div
                                  v-for="member in panelMembers(
                                    item.Panel_Member_Name
                                  )"
                                  :key="member.panelMember"
                                  class="py-2"
                                >
                                  {{ member.panelMember }}
                                </div>
                              </v-card-text>
                            </v-card>
                          </v-menu>
                        </div>
                        <p v-else class="text-subtitle-1 font-weight-regular">
                          -
                        </p>
                      </span>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Start date & Time
                      </div>
                      <section>
                        <span class="text-body-2 text-truncate">
                          {{ formatDate(item.Round_Start_Date_Time) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        End date & Time
                      </div>
                      <section>
                        <span class="text-body-2 text-truncate">
                          {{ formatDate(item.Round_End_Date_Time) }}
                        </span>
                      </section>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </div>
      </div>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue } from "@/helper";
import moment from "moment";
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import FileExportMixin from "@/mixins/FileExportMixin";
import { GET_PANEL_MEMBER_PENDING_FEEDBACK } from "@/graphql/recruitment/recruitmentQueries.js";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const JobCandidatesDetails = defineAsyncComponent(() =>
  import("../job-candidates-details/JobCandidatesDetails.vue")
);
const PendingFeedbackFilter = defineAsyncComponent(() =>
  import("./PendingFeedbackFilter.vue")
);

export default {
  name: "PendingFeedback",
  components: {
    EmployeeDefaultFilterMenu,
    JobCandidatesDetails,
    AvatarOrderedList,
    NotesCard,
    PendingFeedbackFilter,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // add,edit,view
    isListError: false,
    showViewForm: false,
    // table
    originalData: [],
    itemList: [],
    selectedItem: null,
    isFilter: false,
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    hoveredItemId: null,
    isLoading: false,
    // export
    openMoreMenu: false,
    // filter
    selectedStatus: [],
    resetFilterCount: 0,
    appliedFilterCount: 0,
  }),
  mounted() {
    this.fetchPendingFeedback();
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  computed: {
    currentTabItem() {
      let index = this.mainTabList.indexOf("Pending Feedback");
      return "tab-" + index;
    },
    userIsRecruiter() {
      return this.$store.state.isRecruiter ? "Yes" : "No";
    },
    mainTabList() {
      let tab = [];
      const addTalentPool = this.talentFormAccess; // Check for talentFormAccess

      if (
        this.jobCandidateFormAccess &&
        this.jobCandidateFormAccess.view &&
        this.formAccess
      ) {
        tab.push("Job Candidates", "Upcoming Interviews", "Pending Feedback");
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Archived");
        tab.push("Duplicate Candidates", "Onboarding");
      } else if (
        this.jobCandidateFormAccess &&
        this.jobCandidateFormAccess.view &&
        !this.formAccess
      ) {
        tab.push("Job Candidates", "Upcoming Interviews", "Pending Feedback");
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Archived");
        tab.push("Duplicate Candidates");
      } else if (this.formAccess) {
        tab.push("Pending Feedback");
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Onboarding");
      } else {
        tab.push("Pending Feedback");
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
      }

      return tab;
    },
    talentFormAccess() {
      let formAccess = this.accessRights("297");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        formAccess.accessRights["archive"] = formAccess.accessRights["update"];
        formAccess.accessRights["move candidate to job"] =
          formAccess.accessRights["update"];
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("178");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    jobCandidateFormAccess() {
      let formAccess = this.accessRights("16");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    headers() {
      return [
        {
          title: "Candidate",
          align: "start",
          key: "Candidate_Name",
        },
        { title: "Job Title", key: "Job_Post_Name" },
        { title: "Interview Round", key: "Round_Name" },
        { title: "Panel Members", key: "Panel_Member_Name" },
        { title: "Start date & Time", key: "Round_Start_Date_Time" },
        { title: "End date & Time", key: "Round_End_Date_Time" },
      ];
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.isFilter) {
        msgText = "There or no Pending Feedbacks available";
      }
      return msgText;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  methods: {
    checkNullValue,
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onApplyFilter(filteredArray) {
      let filteredList = filteredArray;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedStatus.includes(item.Candidate_Status);
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = filteredList;
    },
    // reset filter
    resetFilter(calledFrom) {
      if (calledFrom === "grid") {
        this.resetFilterCount += 1;
      }
      this.selectedStatus = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = this.originalData;
      this.isFilter = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },
    //function handling tab change
    onTabChange(tabName) {
      if (tabName === "Job Candidates") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates");
      } else if (tabName === "Duplicate Candidates") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/duplicate-candidates");
      } else if (tabName === "Onboarding") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/onboarding");
      } else if (tabName === "Upcoming Interviews") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/upcoming-interviews");
      } else if (tabName === "Talent Pool") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/talent-pool");
      } else if (tabName === "Archived") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/archived-candidates");
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    panelMembers(panelMemberNames) {
      if (!panelMemberNames) return [];
      const splitNames = panelMemberNames.split(",");
      return splitNames.map((name) => ({ panelMember: name.trim() }));
    },
    formatDate(date, withoutTime) {
      let orgDateFormat = withoutTime
        ? this.$store.state.orgDetails.orgDateFormat
        : this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
      return date ? moment(date).format(orgDateFormat) : "-";
    },
    // Retrieving the list of Invited Individuals
    fetchPendingFeedback() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_PANEL_MEMBER_PENDING_FEEDBACK,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getPanelMemberPendingFeedback &&
            !response.data.getPanelMemberPendingFeedback.errorCode
          ) {
            vm.itemList =
              response.data.getPanelMemberPendingFeedback.panelMembers;
            vm.originalData = vm.itemList;
            vm.listLoading = false;
          } else {
            vm.listLoading = false;
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Pending Feedback",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.resetFilter();
      this.fetchPendingFeedback();
    },
    onOpenViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    closeViewForm() {
      this.selectedItem = null;
      this.showViewForm = false;
    },
    onChangeCandidate(item) {
      this.selectedItem = item;
      this.candidateChangeCount += 1;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.itemList));
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Pending Feedback",
        sheetName: "Pending Feedback",
        header: [
          { key: "Interview_Name", header: "Interview Name" },
          { key: "Round_Id", header: "Round Id." },
          { key: "Round_Name", header: "Round Name" },
          { key: "Round_Start_Date_Time", header: "Round Start Date Time" },
          { key: "Round_End_Date_Time", header: "Round End Date Time" },
          { key: "Round_Status", header: "Round Status" },
          { key: "Job_Post_Id", header: "JobPost Id" },
          { key: "Job_Post_Name", header: "JobPost Name" },
          { key: "Candidate_Id", header: "Candidate Id" },
          { key: "Candidate_Name", header: "Candidate Name" },
          { key: "Panel_Member_Id", header: "Panel_Member Id" },
          { key: "Panel_Member_Name", header: "Panel_Member Name" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
    applyFilterFromComponent(filterObj) {
      let filteredList = this.originalData;
      if (filterObj.minStartDate) {
        filteredList = filteredList.filter((data) => {
          let itemDate = data.Round_Start_Date_Time.split("T")[0];
          return filterObj.minStartDate <= new Date(itemDate);
        });
      }
      if (filterObj.maxStartDate) {
        filteredList = filteredList.filter((data) => {
          let itemDate = data.Round_Start_Date_Time.split("T")[0];
          return filterObj.maxStartDate >= new Date(itemDate);
        });
      }
      if (filterObj.minEndDate) {
        filteredList = filteredList.filter((data) => {
          let itemDate = data.Round_End_Date_Time.split("T")[0];
          return filterObj.minEndDate <= new Date(itemDate);
        });
      }
      if (filterObj.maxEndDate) {
        filteredList = filteredList.filter((data) => {
          let itemDate = data.Round_End_Date_Time.split("T")[0];
          return filterObj.maxEndDate >= new Date(itemDate);
        });
      }
      if (filterObj.candidateName && filterObj.candidateName.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.candidateName.includes(item.Candidate_Name);
        });
      }
      if (filterObj.jobTitle && filterObj.jobTitle.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.jobTitle.includes(item.Job_Post_Name);
        });
      }
      if (filterObj.interviewRound && filterObj.interviewRound.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.interviewRound.includes(item.Round_Name);
        });
      }
      if (filterObj.panelMember && filterObj.panelMember.length > 0) {
        filteredList = filteredList.filter((item) => {
          let members = item.Panel_Member_Name.split(", ");
          for (let i of members) {
            if (filterObj.panelMember.includes(i)) {
              return true;
            }
          }
          return false;
        });
      }
      this.itemList = filteredList;
    },
  },
};
</script>
<style scoped>
.onboarding-container {
  padding: 5em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .onboarding-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
