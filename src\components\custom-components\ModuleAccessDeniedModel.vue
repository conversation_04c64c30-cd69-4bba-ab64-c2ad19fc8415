<template>
  <div>
    <v-dialog
      v-model="openAccessDeniedModal"
      persistent
      class="box-radius"
      width="900px"
      min-height="600px"
    >
      <v-card class="box-radius" color="white">
        <v-row style="padding: 3em" class="d-flex justify-space-between">
          <v-col
            cols="12"
            xs="12"
            sm="5"
            md="5"
            lg="5"
            class="d-flex justify-center align-center px-4"
          >
            <img
              :src="getImageUrl"
              style="height: auto; width: 100%"
              alt="access-denied"
            />
          </v-col>
          <v-col
            cols="12"
            xs="12"
            sm="7"
            md="7"
            lg="7"
            class="d-flex flex-column py-3 px-5"
          >
            <div class="mb-2">
              <span class="text-primary text-h4 text-h6 font-weight-bold"
                >Hello! {{ userName ? userName : "" }},</span
              >
            </div>
            <div>
              <span class="text-primary text-h6 font-weight-bold">{{
                modalTitle
              }}</span>
            </div>
            <div class="my-4 text-grey">
              <span>{{ modalContent }}</span>
            </div>
            <div
              class="d-flex justify-start mt-3"
              :class="{ 'flex-column': isMobileView }"
            >
              <v-btn
                rounded="lg"
                :loading="isLoading"
                class="mr-2 secondary"
                @click="$emit('request-action')"
              >
                <span class="font-weight-medium primary">{{ buttonText }}</span>
              </v-btn>
              <v-btn
                v-if="secondaryButtonText"
                rounded="lg"
                variant="outlined"
                class="d-flex primary"
                :class="{ 'mt-4': isMobileView }"
                @click="$emit('skip-action')"
              >
                <span class="font-weight-medium primary">{{
                  secondaryButtonText
                }}</span>
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: "ModuleAccessDeniedModel",
  props: {
    openAlertModal: {
      type: Boolean,
      required: true,
    },
    userName: {
      type: String,
      default: "",
    },
    imageName: {
      type: String,
      required: true,
    },
    buttonText: {
      type: String,
      default: "Request Access",
    },
    secondaryButtonText: {
      type: String,
      default: "",
    },
    modalContent: {
      type: String,
      required: true,
    },
    modalTitle: {
      type: String,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      openAccessDeniedModal: false,
    };
  },
  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.imageName + ".webp");
      else return require("@/assets/images/" + this.imageName + ".png");
    },
  },
  mounted() {
    this.openAccessDeniedModal = this.openAlertModal;
  },
};
</script>

<style scoped>
.box-radius {
  border-radius: 15px !important;
  box-shadow: 1px 3px 15px rgba(15, 84, 136, 0.14) !important;
}
</style>
