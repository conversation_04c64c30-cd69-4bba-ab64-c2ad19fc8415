<template>
  <v-container>
    <div v-if="!message" class="timeline-container" style="height: 40px">
      <div
        v-for="(record, index) in attendanceRecords"
        :key="index"
        class="timeline-row"
      >
        <div class="timeline-grid">
          <!-- Time grid lines -->
          <div
            v-for="hour in hoursInTimeline"
            :key="hour"
            class="grid-line"
          ></div>
        </div>
        <div class="timeline-bars" color="primary">
          <div
            v-for="(segment, segIndex) in record.segments"
            :key="segIndex"
            class="time-segment"
            :style="getSegmentStyle(segment, record)"
          >
            <v-tooltip activator="parent" location="top">
              {{ showTooltip(record, segment) }}
            </v-tooltip>
          </div>
        </div>
        <!-- Shift indicators -->
        <div class="shift-indicators">
          <div
            class="shift-start"
            :style="getShiftIndicatorStyle(record.shiftStart, record)"
          >
            <v-tooltip :text="showShiftStartEndTime('start')" location="bottom">
              <template v-slot:activator="{ props }">
                <span v-bind="props">▲</span>
              </template>
            </v-tooltip>
          </div>
          <div
            class="shift-end"
            :style="getShiftIndicatorStyle(record.shiftEnd, record)"
          >
            <v-tooltip :text="showShiftStartEndTime('end')" location="bottom">
              <template v-slot:activator="{ props }">
                <span v-bind="props">▲</span>
              </template>
            </v-tooltip>
          </div>
        </div>
      </div>
    </div>
  </v-container>
</template>

<script>
export default {
  name: "AttendanceTimeline",
  props: {
    attendanceRecords: {
      type: Array,
      required: true,
    },
    message: {
      type: String,
      default: null,
    },
  },
  computed: {
    hoursInTimeline() {
      const record = this.attendanceRecords[0];
      let hours = 0;
      if (record.considerationCheckout > record.considerationCheckin) {
        hours = record.considerationCheckout - record.considerationCheckin;
      } else {
        hours = 24 - record.considerationCheckin + record.considerationCheckout;
      }
      return Math.ceil(hours);
    },
  },

  methods: {
    showTooltip(record, segment) {
      segment.showTooltip = true;
      segment.tooltipText = `${this.formatTime(
        segment.start
      )} - ${this.formatTime(segment.end)}`;
      return segment.tooltipText;
    },
    formatTime(time) {
      const hours = Math.floor(time);
      const minutes = Math.round((time - hours) * 60);
      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}`;
    },
    showShiftStartEndTime(type) {
      if (type?.toLowerCase() === "start") {
        let time = this.formatTime(this.attendanceRecords[0].shiftStart);
        return "Shift Start Time - " + time;
      } else {
        let time = this.formatTime(this.attendanceRecords[0].shiftEnd);
        return "Shift End Time - " + time;
      }
    },
    normalizeTime(time, record) {
      if (time < record.considerationCheckin) {
        time += 24;
      }
      return time - record.considerationCheckin;
    },
    getSegmentStyle(segment, record) {
      if (segment?.start !== null && segment?.end !== null) {
        const normalizedStart = this.normalizeTime(segment.start, record);
        const normalizedEnd = this.normalizeTime(segment.end, record);
        const totalHours = this.hoursInTimeline;

        return {
          left: `${(normalizedStart / totalHours) * 100}%`,
          width: `${((normalizedEnd - normalizedStart) / totalHours) * 100}%`,
        };
      }
    },
    getShiftIndicatorStyle(time, record) {
      const normalizedTime = this.normalizeTime(time, record);
      const totalHours = this.hoursInTimeline;
      return {
        left: `${(normalizedTime / totalHours) * 100}%`,
      };
    },
  },
};
</script>

<style scoped>
/* Styles remain the same as in the original component */
.timeline-container {
  position: relative;
  width: 100%;
}

.timeline-row {
  position: relative;
  height: 20px;
  margin-bottom: 10px;
}

.timeline-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  pointer-events: none;
}

.grid-line {
  flex: 1;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.timeline-bars {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  right: 0;
  height: 20px;
}

.time-segment {
  position: absolute;
  height: 100%;
  background-color: #b9ecb9;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.time-segment:hover {
  filter: brightness(0.5);
}

.shift-indicators {
  position: absolute;
  bottom: -12px;
  left: 0;
  right: 0;
  height: 15px;
}

.shift-start,
.shift-end {
  position: absolute;
  font-size: 12px;
  color: #757575;
  transform: translateX(-50%);
}

.date-label {
  position: absolute;
  left: -80px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #757575;
}
</style>
