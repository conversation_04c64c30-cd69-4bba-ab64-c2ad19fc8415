import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const LIST_EMP_TIMESHEETS = gql`
  query retrieveTimeSheetProjectDetails(
    $selfService: Int!
    $weekendDate: String!
    $employeeView: Int!
    $employeeId: Int!
    $requestId: Int
    $status: String
  ) {
    retrieveTimeSheetProjectDetails(
      selfService: $selfService
      weekendDate: $weekendDate
      employeeView: $employeeView
      employeeId: $employeeId
      requestId: $requestId
      status: $status
    ) {
      errorCode
      message
      timesheetActivityDetails
    }
  }
`;
export const RETRIEVE_EMPLOYEE_OTHER_DETAILS = gql`
  query retrieveTimesheetEmpLeaveWeekOff(
    $weekendDate: String!
    $employeeId: Int!
  ) {
    retrieveTimesheetEmpLeaveWeekOff(
      weekendDate: $weekendDate
      employeeId: $employeeId
    ) {
      errorCode
      message
      employeeDetails
    }
  }
`;
export const RETRIEVE_TIMESHEETS_SETTINGS = gql`
  query retrieveTimesheetSettings($formId: Int!) {
    retrieveTimesheetSettings(formId: $formId) {
      errorCode
      message
      timesheetSettingData {
        Timesheet_Setting_Id
        Timesheet_Submission_Before_Weekend_Date
        Present_Time_Slot
        Enforce_Note
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const ADD_UPDATE_TIMESHEETS = gql`
  mutation addUpdateempTimesheet(
    $timesheetId: Int!
    $requestId: Int!
    $employeeId: Int!
    $weekEndingDate: String!
    $projectId: Int!
    $timesheetType: String!
    $projectActivityId: Int!
    $day1: Float
    $day2: Float
    $day3: Float
    $day4: Float
    $day5: Float
    $day6: Float
    $day7: Float
    $dayValue: Int!
    $dayDetails: [dayDetails]
    $selfService: Int!
  ) {
    addUpdateempTimesheet(
      timesheetId: $timesheetId
      requestId: $requestId
      employeeId: $employeeId
      weekEndingDate: $weekEndingDate
      projectId: $projectId
      timesheetType: $timesheetType
      projectActivityId: $projectActivityId
      day1: $day1
      day2: $day2
      day3: $day3
      day4: $day4
      day5: $day5
      day6: $day6
      day7: $day7
      dayValue: $dayValue
      dayDetails: $dayDetails
      selfService: $selfService
    ) {
      errorCode
      message
      optionsObject {
        requestId
        timesheetId
      }
    }
  }
`;
export const DELETE_TIMESHEETS = gql`
  mutation deleteTimesheetActivity(
    $timesheetId: Int
    $selfService: Int!
    $parentDelete: Int!
    $requestId: Int
    $detailsBytimeId: Int
  ) {
    deleteTimesheetActivity(
      timesheetId: $timesheetId
      selfService: $selfService
      requestId: $requestId
      parentDelete: $parentDelete
      detailsBytimeId: $detailsBytimeId
    ) {
      errorCode
      message
    }
  }
`;
export const SUBMIT_FOR_APPROVAL = gql`
  mutation timesheetSubmitForApproval($selfService: Int!, $requestId: Int!) {
    timesheetSubmitForApproval(
      selfService: $selfService
      requestId: $requestId
    ) {
      errorCode
      message
    }
  }
`;
export const WITHDRAW_APPROVAL = gql`
  mutation timesheetApprovalWithdraw($selfService: Int!, $requestId: Int!) {
    timesheetApprovalWithdraw(
      selfService: $selfService
      requestId: $requestId
    ) {
      errorCode
      message
    }
  }
`;
export const RETURN_TIMESHEETS = gql`
  mutation timesheetApprovalReturn(
    $selfService: Int!
    $requestId: Int!
    $returnedComment: String!
  ) {
    timesheetApprovalReturn(
      selfService: $selfService
      requestId: $requestId
      returnedComment: $returnedComment
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_PREV_WEEK_RECORDS = gql`
  mutation addTimeSheetPrevWeek(
    $selfService: Int!
    $prevWeekEndingDate: String!
    $employeeId: Int!
    $weekEndingDate: String!
    $timesheetType: String!
  ) {
    addTimeSheetPrevWeek(
      selfService: $selfService
      prevWeekEndingDate: $prevWeekEndingDate
      employeeId: $employeeId
      weekEndingDate: $weekEndingDate
      timesheetType: $timesheetType
    ) {
      errorCode
      message
    }
  }
`;
