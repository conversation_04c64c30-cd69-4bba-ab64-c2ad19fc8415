import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const LIST_INTERVIEW_ROUNDS = gql`
  query CommentQuery(
    $searchString: String!
    $isDropDownCall: Int!
    $employeeId: Int!
  ) {
    listRounds(
      searchString: $searchString
      isDropDownCall: $isDropDownCall
      employeeId: $employeeId
    ) {
      errorCode
      message
      roundsDetails {
        Round_Id
        Round_Name
        Description
        Max_Score_Per_Skill
        Passing_Score
        Total_Score
        Minimum_Panel_Members
        Maximum_Panel_members
        Maximum_Candidates
        ScoringParameters
        Check_FeedBack
        SkillsCategory
        Skills {
          Skill_Id
          Skill_Name
        }
        isAssociated
      }
    }
  }
`;
export const GET_SKILL_LIST_FOR_INTERVIEW = gql`
  query getSkillListForInterview {
    getSkillListForInterview {
      errorCode
      message
      skillResult {
        Skill_Name
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const ADD_INTERVIEW_ROUNDS = gql`
  mutation MyMutation(
    $roundName: String!
    $description: String
    $passingScore: Float!
    $totalScore: Float!
    $minPanelMembers: Int!
    $maxPanelMembers: Int!
    $maxCandidates: Int!
    $maximumScorePerSkill: Float!
    $skillCategory: [addEditSkillCategory]
    $addedBy: Int!
  ) {
    addRounds(
      roundName: $roundName
      description: $description
      passingScore: $passingScore
      totalScore: $totalScore
      minPanelMembers: $minPanelMembers
      maxPanelMembers: $maxPanelMembers
      maxCandidates: $maxCandidates
      maximumScorePerSkill: $maximumScorePerSkill
      skillCategory: $skillCategory
      addedBy: $addedBy
    ) {
      message
      errorCode
      validationError
      data
    }
  }
`;
export const UPDATE_INTERVIEW_ROUNDS = gql`
  mutation (
    $roundId: Int!
    $roundName: String!
    $description: String
    $passingScore: Float!
    $totalScore: Float!
    $minPanelMembers: Int!
    $maxPanelMembers: Int!
    $maxCandidates: Int!
    $maximumScorePerSkill: Float!
    $skillCategory: [addEditSkillCategory]
    $updatedBy: Int!
  ) {
    editRounds(
      roundId: $roundId
      roundName: $roundName
      description: $description
      passingScore: $passingScore
      totalScore: $totalScore
      minPanelMembers: $minPanelMembers
      maxPanelMembers: $maxPanelMembers
      maxCandidates: $maxCandidates
      maximumScorePerSkill: $maximumScorePerSkill
      skillCategory: $skillCategory
      updatedBy: $updatedBy
    ) {
      message
      errorCode
      validationError
    }
  }
`;
export const DELETE_INTERVIEW_ROUNDS = gql`
  mutation CommentQuery($roundId: Int!, $employeeId: Int!) {
    deleteRounds(roundId: $roundId, employeeId: $employeeId) {
      errorCode
      message
      validationError
    }
  }
`;
