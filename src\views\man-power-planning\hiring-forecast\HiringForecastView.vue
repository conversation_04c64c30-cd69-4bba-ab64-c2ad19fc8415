<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="onCloseView()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:50vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">Hiring Forecast Details</div>
            <div class="d-flex align-center">
              <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
                <v-icon>fas fa-times</v-icon>
              </v-btn>
            </div>
          </v-card-title>
          <div
            class="px-6"
            style="height: 90%; overflow-y: auto; overflow-x: hidden"
            v-if="selectedForecastData && selectedForecastData.Pos_Name"
          >
            <div class="d-flex justify-end align-center">
              <v-btn
                v-if="
                  formAccess &&
                  formAccess.update &&
                  enableEditAction &&
                  selectedForecastData &&
                  (isAdmin ||
                    loginEmployeeName?.toLowerCase() ===
                      selectedForecastData?.Added_By?.toLowerCase())
                "
                @click="onEditPosition()"
                class="mr-3 bg-white text-primary"
                variant="text"
                rounded="lg"
              >
                <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
              </v-btn>
            </div>
            <v-card class="rounded-lg pa-3 my-3">
              <v-row>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold">Position</div>
                  <div class="value-text">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedForecastData.Pos_Name)
                      }}{{
                        selectedForecastData.Pos_Code
                          ? " - " + selectedForecastData.Pos_Code
                          : ""
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold">Group</div>
                  <div class="value-text">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedForecastData.Group_Name)
                      }}{{
                        selectedForecastData.Group_Code
                          ? " - " + selectedForecastData.Group_Code
                          : ""
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold">Division</div>
                  <div class="value-text">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedForecastData.Division_Name)
                      }}{{
                        selectedForecastData.Division_Code
                          ? " - " + selectedForecastData.Division_Code
                          : ""
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold">Department</div>
                  <div class="value-text">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedForecastData.Department_Name)
                      }}{{
                        selectedForecastData.Department_Code
                          ? " - " + selectedForecastData.Department_Code
                          : ""
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold">Section</div>
                  <div class="value-text">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedForecastData.Section_Name)
                      }}{{
                        selectedForecastData.Section_Code
                          ? " - " + selectedForecastData.Section_Code
                          : ""
                      }}
                    </section>
                  </div>
                </v-col>
              </v-row>
            </v-card>
            <v-card class="rounded-lg my-5">
              <v-row>
                <v-col :cols="windowWidth >= 1264 ? 3 : 6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold text-center">
                    Approved Positions
                  </div>
                  <div class="value-text">
                    <section class="text-body-2 text-center">
                      {{
                        checkNullValue(selectedForecastData.Approved_Position)
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col :cols="windowWidth >= 1264 ? 3 : 6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold text-center">
                    Warm Bodies
                  </div>
                  <div class="value-text">
                    <section class="text-body-2 text-center">
                      {{ checkNullValue(selectedForecastData.Warm_Bodies) }}
                    </section>
                  </div>
                </v-col>
                <v-col :cols="windowWidth >= 1264 ? 3 : 6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold text-center">
                    Approved Vacant Positions
                  </div>
                  <div class="value-text">
                    <section class="text-body-2 text-center">
                      {{ checkNullValue(selectedForecastData.To_Be_Hired) }}
                    </section>
                  </div>
                </v-col>
                <v-col :cols="windowWidth >= 1264 ? 3 : 6" class="px-md-6 pb-0">
                  <div class="text-subtitle-2 font-weight-bold text-center">
                    Vacancies for TO Review
                  </div>
                  <div class="value-text">
                    <section class="text-body-2 text-center">
                      {{ checkNullValue(selectedForecastData.To_Be_Source) }}
                    </section>
                  </div>
                </v-col>
              </v-row>
            </v-card>
            <v-card class="rounded-lg my-5">
              <v-row>
                <v-col
                  :cols="windowWidth >= 1264 ? 3 : 6"
                  class="px-md-6 py-2"
                  v-for="(item, index) in monthList"
                  :key="index"
                >
                  <div class="text-subtitle-2 font-weight-bold text-center">
                    {{ item.month + ", " + item.year }}
                  </div>
                  <div class="value-text">
                    <section class="text-body-2 text-center">
                      {{ item.value || 0 }}
                    </section>
                  </div>
                </v-col>
              </v-row>
            </v-card>
            <v-row class="px-sm-8 px-md-10 mb-2">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails> </v-col
            ></v-row></div></v-card></template
    ></v-overlay>
  </div>
</template>
<script>
import { checkNullValue } from "@/helper";
import moment from "moment";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";

export default {
  name: "HiringForecastView",
  emits: ["close-view-details", "edit-forecast-details"],
  data() {
    return {
      showViewForm: false,
      monthList: [],
      tempPositionTitle: null,
      selectedPosition: null,
      moreDetailsList: [],
      openMoreDetails: true,
    };
  },
  props: {
    showViewDetails: {
      type: Boolean,
      required: true,
    },
    selectedForecastData: {
      type: Object,
      required: true,
    },
    hireForecastData: {
      type: Object,
      required: true,
    },
    selectedForecastYear: {
      type: Number,
      required: true,
    },
    enableEditAction: {
      type: Boolean,
      required: true,
    },
  },
  components: {
    MoreDetails,
  },
  watch: {
    showViewDetails() {
      this.showViewForm = this.showViewDetails;
    },
    selectedForecastData() {
      this.preparingMonthData();
      this.prefillMoreDetails();
    },
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formAccess() {
      let formAccess = this.accessRights("287");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    filteredYearMonth() {
      return this.hireForecastData.End_Month === 12
        ? `${this.selectedForecastYear}-01-01`
        : `${this.selectedForecastYear}-${
            this.hireForecastData.End_Month + 1
          }-01`;
    },
  },
  mounted() {
    this.preparingMonthData();
  },
  methods: {
    checkNullValue,
    onCloseView() {
      this.$emit("close-view-details");
    },
    onEditPosition() {
      this.$emit("edit-forecast-details");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.selectedForecastData?.Added_On + ".000Z")
        ),
        addedByName = this.selectedForecastData?.Added_By,
        updatedByName = this.selectedForecastData?.Updated_By,
        updatedOn = this.formatDate(
          new Date(this.selectedForecastData?.Updated_On + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    preparingMonthData() {
      if (this.selectedForecastData && this.selectedForecastData.foreCastList) {
        const forecastList = JSON.parse(this.selectedForecastData.foreCastList);
        this.monthList = moment.months()?.map((month, index) => {
          const date = moment(this.filteredYearMonth)
            .clone()
            .add(index, "months");

          const monthName = date.format("MMM");
          const monthNumber = parseInt(date.format("MM"));
          const yearValue = parseInt(date.format("YYYY"));
          const selectedValue = forecastList.find(
            (list) =>
              list?.Forecast_Month === monthNumber &&
              list.Forecast_Year === yearValue
          );
          return {
            id: index,
            month: monthName,
            monthNumber: monthNumber,
            value: selectedValue?.No_Of_Position ?? 0,
            year: yearValue,
            hiringForecastId: selectedValue?.Hiring_Forecast_Id ?? 0,
          };
        });
      }
    },
  },
};
</script>
<style>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start !important;
}
</style>
