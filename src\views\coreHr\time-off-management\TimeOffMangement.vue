<template>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppAccessDenied v-else-if="isAccessDenied"></AppAccessDenied>
</template>

<script>
export default {
  name: "TimeOffManagement",
  data() {
    return { isLoading: true, isAccessDenied: false };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(276);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    coreHrTimeOffTabs() {
      return this.$store.getters.coreHrTimeOffTabs;
    },
  },
  mounted() {
    for (let form of this.coreHrTimeOffTabs) {
      if (form.havingAccess) {
        this.$router.push(form.url);
        break;
      }
    }
    this.isAccessDenied = true;
    this.isLoading = false;
  },
};
</script>
