<template>
  <div class="fill-height">
    <AppTopBarTab
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      :center-tab="true"
      :showBottomSheet="!listLoading && !showViewEditForm"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-show="!listLoading && !showViewEditForm">
          <v-col
            cols="12"
            class="d-flex"
            :class="settlementListBackup.length > 0 ? '' : 'justify-end'"
            style="margin-left: -108px"
          >
            <EmployeeDefaultFilterMenu
              v-if="settlementListBackup.length > 0"
              class="justify-end"
              :reset-filter-count="resetFilterCount"
              :list-items="settlementListBackup"
              :isApplyFilter="true"
              departmentIdKey="departmentId"
              designationIdKey="designationId"
              locationIdKey="locationId"
              empTypeIdKey="empTypeId"
              workScheduleIdKey="workSchedule"
              @reset-emp-filter="resetFilter('filter')"
              @applied-filter="applyFilter($event)"
            >
              <template #bottom-filter-menu>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedStatus"
                    color="primary"
                    :items="[
                      'Pending Approval',
                      'Yet to be settled',
                      'Settled',
                    ]"
                    label="Status"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                  >
                  </v-select>
                </v-col>
              </template>
            </EmployeeDefaultFilterMenu>
            <AppButtonWithDropdown
              :list-items="yearsList"
              :button-value="selectedYear"
              :is-override-button="windowWidth < 1264 ? true : false"
              background-color="primary"
              :class="isMobileView ? 'ml-4' : 'ml-1'"
              @selected-value="
                applyMonthYearFilter(selectedMonth, $event, 'initial')
              "
            ></AppButtonWithDropdown>
            <AppButtonWithDropdown
              :list-items="monthsList"
              :button-value="selectedMonth"
              :is-override-button="windowWidth < 1264 ? true : false"
              background-color="primary"
              class="ml-3 mr-2"
              @selected-value="
                applyMonthYearFilter($event, selectedYear, 'initial')
              "
            ></AppButtonWithDropdown>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="settlement-container">
      <v-window
        v-model="currentTabItem"
        id="settlement-tab"
        v-if="settlementFormAccess"
      >
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else-if="isErrorInList">
            <AppFetchErrorScreen
              image-name="payroll/full-and-final-settlement/fnf-settlement-error"
              :content="errorContent"
              icon-name="fas fa-redo-alt"
              :button-text="showRetryButton ? 'Retry' : ''"
              @button-click="refetchAPIs()"
            >
            </AppFetchErrorScreen>
          </div>
          <v-row v-else>
            <v-col v-if="showViewEditForm" cols="12">
              <ViewEditSettlements
                :selectedItem="selectedItem"
                :settlementDetails="settlementDetails"
                :access-rights="settlementFormAccess"
                :restrict-access="restrictAccess"
                :selected-payroll-month="monthNoFromName"
                :selected-payroll-year="selectedYear"
                @back-to-list="closeViewEditForm()"
                @refetch-list="refetchAPIs()"
              ></ViewEditSettlements>
            </v-col>
            <v-col v-else cols="12">
              <ListSettlements
                :items="settlementList"
                :original-list="settlementListBackup"
                :selected-payroll-month="monthNoFromName"
                :selected-payroll-year="selectedYear"
                :access-rights="settlementFormAccess"
                :restrict-access="restrictAccess"
                :isInitialCall="isInitialCall"
                @refetch-list="refetchAPIs()"
                @reset-filter="resetFilter('grid')"
                @on-select-item="openViewForm($event)"
                @approve-settlement="approveSettlement($event)"
                @resigned-employees-fetched="isInitialCall = false"
              ></ListSettlements>
            </v-col>
          </v-row>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="showApproveSuccessModal"
    :open-modal="showApproveSuccessModal"
    iconName="hr-workflow-task-management-approve"
    iconColor="green"
    confirmationHeading="Settlement status updated successfully!"
    closeButtonText=""
    acceptButtonText=""
    @close-warning-modal="showApproveSuccessModal = false"
    @accept-modal="showApproveSuccessModal = false"
  >
    <template #warningModalContent>
      <div style="display: inline-block" class="text-center">
        Please generate the
        <a class="text-primary px-1" :href="baseUrl + 'payroll/salary-payslip'"
          >payslip</a
        >
        to conclude the full & final settlement
      </div>
    </template>
  </AppWarningModal>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import moment from "moment";
// queries
import {
  GET_SETTLEMENT_LIST,
  GET_CURRENT_PAYROLL_MONTH,
  VIEW_SETTLEMENT,
  APPROVE_SETTLEMENT,
} from "@/graphql/payroll/fullAndFinalSettlementQueries.js";
// components
import ListSettlements from "./ListSettlements.vue";
const ViewEditSettlements = defineAsyncComponent(() =>
  import("./ViewEditSettlements.vue")
);
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import { getErrorCodes } from "@/helper.js";

export default defineComponent({
  name: "FullAndFinalSettlement",

  components: {
    EmployeeDefaultFilterMenu,
    ListSettlements,
    ViewEditSettlements,
  },

  data() {
    return {
      isLoading: false,
      mainTabList: ["Salary Payslip", "Full & Final Settlement"],
      currentTabItem: "tab-1",
      restrictAccess: 0,
      isInitialCall: false,
      // list
      listLoading: true,
      isErrorInList: false,
      errorContent: "",
      showRetryButton: true,
      settlementList: [],
      settlementListBackup: [],
      showApproveSuccessModal: false,
      // view/edit
      showViewEditForm: false,
      selectedItem: {},
      settlementDetails: {},
      // filter/search
      resetFilterCount: 0,
      selectedYear: "",
      selectedMonth: "",
      selectedStatus: [],
    };
  },

  computed: {
    domainName() {
      return this.$store.getters.domain;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    settlementFormAccess() {
      let formAccess = this.accessRights("full-&-final-settlement");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights.view
      ) {
        return formAccess.accessRights;
      } else return false;
    },
    checkAdminManagerEmp() {
      if (this.settlementFormAccess) {
        const { admin, isManager } = this.settlementFormAccess;
        return admin === "admin"
          ? "admin"
          : isManager === 1
          ? "manager"
          : "employee";
      }
      return "";
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // months list as sort form
    monthsList() {
      return moment.monthsShort();
    },
    yearsList() {
      let currentYear = moment().year();
      // assign previous year and current year as years list
      return [currentYear - 1, currentYear];
    },
    // return month number(1,2,..) based on month name(Jan, Feb..)
    monthNoFromName() {
      let monthNumber = moment().month(this.selectedMonth).format("M");
      return parseInt(monthNumber, 10);
    },
    currentYear() {
      return moment().year();
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  mounted() {
    if (this.settlementFormAccess) {
      let salMonth = this.$route.query.salaryMonth;
      let salYear = this.$route.query.salaryYear;
      if (salMonth && salYear) {
        this.selectedYear = parseInt(salYear, 10);
        this.selectedMonth = moment()
          .month(salMonth - 1)
          .format("MMM");
        this.isLoading = false;
        this.restrictAccess =
          this.checkAdminManagerEmp && this.checkAdminManagerEmp !== "employee"
            ? 1
            : 0;
        this.getSettlementsList("initial");
      } else {
        this.getCurrentPayrollMonth("initial");
      }
    } else {
      this.isLoading = false;
    }
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("f&f error", err, info);
    let msg =
      "Something went wrong while loading the full & final settlement details. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  methods: {
    onTabChange(tabName) {
      if (tabName === "Salary Payslip") {
        this.isLoading = true;
        window.location.href = this.baseUrl + "payroll/salary-payslip";
      }
    },

    async approveSettlement(item) {
      this.selectedItem = item;
      await this.getSelectedSettlementRecordDetails(false);
      this.onApproveSettlement();
    },

    onApproveSettlement() {
      let vm = this;
      vm.isLoading = true;
      let earnDeductArray = [];
      if (this.settlementDetails && this.settlementDetails.earningsDeductions) {
        const { earningsDeductions } = this.settlementDetails;
        for (let earnDeduct of earningsDeductions) {
          earnDeductArray.push({
            Amount: earnDeduct.Amount,
            Calculate_By: earnDeduct.Calculate_By,
            Employee_Id: earnDeduct.Employee_Id,
            Mode: earnDeduct.Mode,
            Multiply_By: parseFloat(earnDeduct.Multiply_By),
            Periodical_Amount: earnDeduct.Periodical_Amount,
            Settlement_Type: earnDeduct.Settlement_Type,
          });
        }
      }
      vm.$apollo
        .mutate({
          mutation: APPROVE_SETTLEMENT,
          variables: {
            isEdit: 0,
            earningsAndDeductions: earnDeductArray,
            employeeId: vm.selectedItem.employeeId,
          },
          client: "apolloClientAB",
        })
        .then(() => {
          vm.isLoading = false;
          vm.showApproveSuccessModal = true;
          vm.refetchAPIs();
        })
        .catch((err) => {
          vm.handleApproveError(err);
        });
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.getSelectedSettlementRecordDetails();
    },

    closeViewEditForm() {
      this.showViewEditForm = false;
      this.selectedItem = {};
    },

    resetAllValues() {
      this.showViewEditForm = false;
      this.isErrorInList = false;
      this.errorContent = "";
      this.settlementList = [];
      this.settlementListBackup = [];
      this.resetFilterCount += 1;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    applyMonthYearFilter(month, year, type) {
      this.selectedMonth = month;
      this.selectedYear = parseInt(year, 10);
      this.refetchAPIs(type);
    },

    resetFilter(calledFrom) {
      if (calledFrom === "grid") {
        this.resetFilterCount += 1;
      }
      this.selectedStatus = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.settlementList = this.settlementListBackup;
    },

    applyFilter(filteredArray) {
      let filteredList = filteredArray;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedStatus.includes(item.settlementStatus);
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.settlementList = filteredList;
    },

    refetchAPIs(type) {
      this.resetAllValues();
      this.getSettlementsList(type);
    },

    getCurrentPayrollMonth(type) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_CURRENT_PAYROLL_MONTH,
          client: "apolloClientAA",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getCurrentSalaryMonth
          ) {
            const {
              displaySalaryMonth,
              displaySalaryYear,
              restrictFinancialAccessForManager,
            } = response.data.getCurrentSalaryMonth;
            vm.selectedYear = displaySalaryYear
              ? parseInt(displaySalaryYear, 10)
              : parseInt(vm.currentYear);
            vm.selectedMonth = displaySalaryMonth
              ? moment()
                  .month(displaySalaryMonth - 1)
                  .format("MMM")
              : moment().startOf("month").format("MMM");
            vm.isLoading = false;
            vm.restrictAccess =
              vm.checkAdminManagerEmp === "admin"
                ? 1
                : vm.checkAdminManagerEmp === "manager"
                ? restrictFinancialAccessForManager
                : 0;
            vm.getSettlementsList(type);
          } else {
            vm.selectedMonth = moment().startOf("month").format("MMM");
            vm.selectedYear = parseInt(vm.currentYear);
            vm.isLoading = false;
            vm.getSettlementsList(type);
          }
        })
        .catch(() => {
          vm.selectedMonth = moment().startOf("month").format("MMM");
          vm.selectedYear = parseInt(vm.currentYear);
          vm.isLoading = false;
          vm.getSettlementsList(type);
        });
    },

    getSettlementsList(type) {
      let vm = this;
      vm.listLoading = true;
      vm.isInitialCall = type === "initial";
      vm.$apollo
        .query({
          query: GET_SETTLEMENT_LIST,
          client: "apolloClientAA",
          variables: {
            salaryMonth: vm.monthNoFromName,
            salaryYear: vm.selectedYear,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listFullAndFinalSettlement
          ) {
            const { employeeDetails } =
              response.data.listFullAndFinalSettlement;
            vm.settlementList = employeeDetails;
            vm.settlementListBackup = employeeDetails;
          } else {
            vm.handleListError();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },

    getSelectedSettlementRecordDetails(showViewEditForm = true) {
      let vm = this;
      vm.isLoading = true;

      return new Promise((resolve, reject) => {
        vm.$apollo
          .query({
            query: VIEW_SETTLEMENT,
            client: "apolloClientAA",
            variables: {
              employeeId: vm.selectedItem.employeeId,
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response &&
              response.data &&
              response.data.viewFullAndFinalSettlement
            ) {
              const { employeeDetails } =
                response.data.viewFullAndFinalSettlement;
              if (employeeDetails && employeeDetails.length > 0) {
                const { earningsDeductions } = employeeDetails[0];
                if (earningsDeductions && earningsDeductions.length > 0) {
                  vm.settlementDetails = employeeDetails[0];
                  if (showViewEditForm) {
                    vm.showViewEditForm = true;
                  }
                } else {
                  if (showViewEditForm) {
                    let snackbarData = {
                      isOpen: true,
                      message:
                        "There are no earnings and deductions found for the selected employee",
                      type: "warning",
                    };
                    vm.showAlert(snackbarData);
                  }
                }
              } else {
                let snackbarData = {
                  isOpen: true,
                  message:
                    "There are no earnings and deductions found for the selected employee",
                  type: "warning",
                };
                vm.showAlert(snackbarData);
              }
              vm.isLoading = false;
              resolve();
            } else {
              vm.handleViewError();
              reject();
            }
          })
          .catch((err) => {
            vm.handleViewError(err);
            reject();
          });
      });
    },

    handleListError(err = "") {
      this.listLoading = false;
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        if (errorCode) {
          switch (errorCode) {
            case "_DB0000": // technical errors
              this.errorContent =
                "It’s us! There seem to be some technical difficulties. Please try after some time.";
              break;
            case "_EC0007": // Invalid input field(s).
              this.errorContent =
                "Please retry as the input request was not received as expected. If you encounter this message repeatedly, kindly reach out to the platform administrator";
              break;
            case "PFF0010": // Login employee is not allowed to initiate the full and final settlement.
            case "_DB0100": // This employee do not have view access rights
            case "_DB0114": // This employee does not have admin or manager access.
              this.errorContent =
                "Sorry, you don't have access rights to view the settlement details. Please contact the HR administrator.";
              break;
            case "SGE0105": // Organization details does not exists.
              this.errorContent =
                "Something went wrong while fetching the organization details for listing the settlement details. Please try after some time.";
              break;
            case "SGE0112": // Error while getting the restrict financial access for manager flag.
              this.errorContent =
                "Something went wrong while getting the restrict financial access for listing the settlement details. Please try after some time.";
              break;
            case "PFF0101": // Error while processing the request to retrieve the full and final settlement employees.
            case "_UH0001": // unhandled error
            case "PFF0003": // Error while retrieving the full and final settlement employees.
            default:
              this.errorContent =
                "Something went wrong while retrieving the settlement details. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        } else {
          this.errorContent =
            "Something went wrong while retrieving the settlement details. Please try after some time.";
        }
      } else {
        this.errorContent =
          "Something went wrong while retrieving the settlement details. Please try after some time.";
      }
      this.isErrorInList = true;
    },

    handleApproveError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        //  IVE0299:`Earnings and deductions is required `,

        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "PFF0010": // Login employee is not allowed to initiate the full and final settlement.
          case "_DB0114": // This employee does not have admin or manager access.
            snackbarData.message =
              "Sorry, you don't have access rights to approve the settlement. Please contact the HR administrator.";
            break;
          case "IVE0000": // Invalid input request.
          case "_EC0007": // invalid inputs
            snackbarData.message =
              "Please retry as the input request was not received as expected. If you encounter this message repeatedly, kindly reach out to the platform administrator.";
            break;
          case "_EC0006": // Record(s) are already deleted in the same or some other user session.
            snackbarData.message =
              "Unable to approve the settlement as it was deleted already in the same or some other user session";
            this.$emit("refetch-list");
            break;
          case "PFF0004": // Login employee is not eligible to add or update or delete the full and final settlement.
            snackbarData.message =
              "You are unable to approve your own record. Please contact the platform administrator for further assistance.";
            break;
          case "SGE0105": // Organization details does not exists.
            snackbarData.message =
              "Something went wrong while fetching the organization details when approving the settlement details. Please try after some time.";
            break;
          case "SGE0112": // Error while getting the restrict financial access for manager flag.
            snackbarData.message =
              "Something went wrong while getting the restrict financial access when approving the settlement details. Please try after some time.";
            break;
          case "PFF0008": // Error while approving the full and final settlement.
          case "PFF0105": // Error while processing the request to approve the full and final settlement.
          case "_UH0001": // unhandled error
          default:
            snackbarData.message =
              "Something went wrong while approving the settlement. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while approving the settlement. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    handleViewError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0114": // This employee does not have admin or manager access.
          case "_DB0100": // This employee do not have view access rights
            snackbarData.message =
              "Sorry, you don't have access rights to view the settlement details. Please contact the HR administrator.";
            break;
          case "_EC0007": // invalid inputs
            snackbarData.message =
              "Please retry as the input request was not received as expected. If you encounter this message repeatedly, kindly reach out to the platform administrator.";
            break;
          case "PFF0106": // Error in retrieving the employee earnings and deductions.
            snackbarData.message =
              "Something went wrong while retrieving the employee's earnings and deductions. Please try after some time.";
            break;
          case "_EC0006": // Record(s) are already deleted in the same or some other user session.
            snackbarData.message =
              "Unable to retrieve settlement details as it was deleted already in the same or some other user session";
            this.refetchAPIs();
            break;
          case "PFF0107": // Error while processing the request to view the full and final settlement.
          case "PFF0009": // Error while viewing the full and final settlement employees.
          case "_UH0001": // unhandled error
          default:
            snackbarData.message =
              "Something went wrong while retrieving the settlement details. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while retrieving the settlement details. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style>
.settlement-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .settlement-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
