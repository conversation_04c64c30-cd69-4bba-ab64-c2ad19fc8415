export default {
  methods: {
    downloadReportDocument(summaryData, detailsData, fileName = "File") {
      // Format summary section
      const summaryLines =
        summaryData && Object.keys(summaryData).length > 0
          ? Object.values(summaryData).join("")
          : null;

      // Format details section dynamically for all key-value pairs
      const detailsLines = detailsData
        ? detailsData.map((row) => {
            return (
              Object.entries(row)
                // eslint-disable-next-line no-unused-vars
                .map(([key, value]) => `${value}`)
                .join("")
            ); // Join each key-value pair with a space
          })
        : "";
      // Combine all parts
      const textContent = summaryLines
        ? [summaryLines, ...detailsLines].join("\n")
        : detailsLines.join("\n");

      // Create Blob and download
      const blob = new Blob([textContent], { type: "text/plain" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;

      // Generate filename
      link.download = `${fileName}.txt`;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return textContent;
    },
  },
};
