<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>
    <v-container fluid class="nps-container">
      <v-window v-if="formAccess && isSuperAdmin" v-model="currentTabItem">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchData()"
          >
          </AppFetchErrorScreen>
          <div v-else>
            <v-card
              class="py-9 rounded-lg fill-height"
              :class="isMobileView ? '' : 'px-5'"
              elevation="5"
            >
              <v-card-text>
                <v-row>
                  <v-col v-if="!isEdit && !listLoading" cols="12">
                    <ViewNpsConfiguration
                      :editFormData="npsConfigurationData"
                      @open-edit="openEditForm()"
                      :accessFormName="accessFormName"
                      :getFieldAlias="labelList"
                      :formAccess="formAccess"
                    ></ViewNpsConfiguration>
                  </v-col>
                  <v-col v-if="isEdit && !listLoading" cols="12">
                    <EditNpsConfiguration
                      :editFormData="npsConfigurationData"
                      @refetch-data="refetchData()"
                      @close-form="closeEditForm()"
                      :accessFormName="accessFormName"
                      :getFieldAlias="labelList"
                    >
                    </EditNpsConfiguration>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
// components
import ViewNpsConfiguration from "./ViewNpsConfiguration";
import EditNpsConfiguration from "./EditNpsConfiguration.vue";
// Queries
import { RETRIEVE_NPS_CONFIGURATION_DETAILS } from "@/graphql/tax-and-statutory-compliance/npsRules";
export default {
  name: "NPSMainForm",
  components: {
    ViewNpsConfiguration,
    EditNpsConfiguration,
  },
  data() {
    return {
      isLoading: false,
      currentTabItem: "tab-0",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      npsConfigurationData: {},
      isEdit: false,
      slabList: [],
    };
  },
  computed: {
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formAccess() {
      let npsConfigAccess = this.accessRights("126");
      if (
        npsConfigAccess &&
        npsConfigAccess.accessRights &&
        npsConfigAccess.accessRights["view"]
      ) {
        return npsConfigAccess.accessRights;
      } else return false;
    },
    accessFormName() {
      let npsConfigAccess = this.accessRights("126");
      if (npsConfigAccess && npsConfigAccess.customFormName) {
        return npsConfigAccess.customFormName;
      } else return "HDMF Configuration";
    },
    npsPaymentFormAccess() {
      let npsConfigAccess = this.accessRights("127");
      if (
        npsConfigAccess &&
        npsConfigAccess.accessRights &&
        npsConfigAccess.accessRights["view"]
      ) {
        return npsConfigAccess.accessRights;
      } else return false;
    },
    npsPaymentFormName() {
      let npsConfigAccess = this.accessRights("127");
      if (npsConfigAccess && npsConfigAccess.customFormName) {
        return npsConfigAccess.customFormName;
      } else return "NPS Payment Tracker";
    },
    npsRulesformAccess() {
      let pfAccess = this.accessRights("260");
      if (pfAccess && pfAccess.accessRights && pfAccess.accessRights["view"]) {
        return pfAccess.accessRights;
      } else return false;
    },
    npsRulesFormName() {
      let pfAccess = this.accessRights("260");
      if (pfAccess && pfAccess.customFormName) {
        return pfAccess.customFormName;
      } else return "HDMF Rules";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    mainTabs() {
      if (
        this.npsPaymentFormAccess &&
        this.npsRulesformAccess &&
        this.formAccess
      ) {
        return [
          this.accessFormName,
          this.npsPaymentFormName,
          this.npsRulesFormName,
        ];
      } else if (this.npsPaymentFormAccess && this.npsRulesformAccess) {
        return [this.npsPaymentFormName, this.npsRulesFormName];
      } else if (this.npsPaymentFormAccess && this.formAccess) {
        return [this.accessFormName, this.npsPaymentFormName];
      } else if (this.npsRulesformAccess && this.formAccess) {
        return [this.accessFormName, this.npsRulesFormName];
      } else if (this.npsPaymentFormAccess) {
        return [this.npsPaymentFormName];
      } else if (this.npsRulesformAccess) {
        return [this.npsRulesFormName];
      } else if (this.formAccess) {
        return [this.accessFormName];
      } else {
        return [];
      }
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("NPS Error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.accessFormName);
    if (this.formAccess && this.isSuperAdmin) {
      this.fetchNpsConfigurationDetails();
    }
  },
  methods: {
    openEditForm() {
      this.isEdit = true;
    },
    closeEditForm() {
      this.isEdit = false;
    },
    onTabChange(tab) {
      if (tab !== this.accessFormName) {
        if (tab == this.npsPaymentFormName) {
          window.location.href = this.baseUrl + "payroll/etf";
        } else if (tab == this.npsRulesFormName) {
          this.$router.push("/tax-and-statutory-compliance/nps");
        }
      }
    },
    refetchData() {
      this.closeEditForm();
      this.fetchNpsConfigurationDetails();
    },
    fetchNpsConfigurationDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_NPS_CONFIGURATION_DETAILS,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listNpsConfigurationDetails.npsConfigurationDetails
          ) {
            let npsConfigurationData = JSON.parse(
              response.data.listNpsConfigurationDetails.npsConfigurationDetails
            );

            vm.npsConfigurationData = npsConfigurationData[0];
            vm.listLoading = false;
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.nps-container {
  padding: 5em 3em 0em 3em;
}
@media screen and (max-width: 805px) {
  .nps-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
