<template>
  <div class="d-flex flex-column justify-center align-center error-screen-bg">
    <v-row class="set-layout">
      <v-col cols="12" class="d-flex flex-column justify-center align-center">
        <div class="mb-5">
          <img
            style="width: 100%; height: auto"
            :src="getImgUrl"
            alt="error_found"
          />
        </div>
        <div v-if="mainTitle" class="main-content-title text-primary mb-5">
          {{ mainTitle }}
        </div>
        <span class="mb-5 sub-content">{{ content }}</span>
        <div v-if="buttonText" class="mb-4">
          <v-btn rounded="lg" color="secondary" @click="$emit('button-click')">
            <v-icon v-if="icon" class="add-icon mr-1">
              {{ iconName }}
            </v-icon>
            {{ buttonText }}
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: "FullPageErrorScreen",
  props: {
    buttonText: {
      type: String,
      default: "",
    },
    mainTitle: {
      type: String,
      default: "",
    },
    content: {
      type: String,
      default: "",
    },
    imageName: {
      type: String,
      required: true,
    },
    icon: {
      type: Boolean,
      default: true,
    },
    iconName: {
      type: String,
      default: "fas fa-redo-alt",
    },
  },
  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getImgUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.imageName + ".webp");
      else return require("@/assets/images/" + this.imageName + ".png");
    },
  },
};
</script>

<style lang="scss" scoped>
.add-icon {
  font-weight: bold !important;
  font-size: 1em !important;
}
.set-layout {
  margin-top: 5%;
}
.main-content-title {
  font-weight: bold;
  font-size: 2em;
}
.sub-content {
  font-size: 1.5em;
  width: 85%;
  text-align: center;
  color: white;
}
.error-screen-bg {
  height: 100%;
  background-color: var(--v-primary-base);
}

@media screen and (max-width: 768px) {
  .sub-content {
    font-size: 1em;
    width: 100%;
    text-align: center;
  }
}
</style>
