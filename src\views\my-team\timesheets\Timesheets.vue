<template>
  <div>
    <AppTopBarTab
      v-if="mainTabs.length > 0"
      :tabs-list="mainTabs"
      :show-bottom-sheet="!listLoading && !showMyTimesheetForm"
      :current-tab="currentTabItem"
      :tabListRedirectionURLs="mainTabsURLs"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-show="!listLoading && !showMyTimesheetForm">
          <v-col
            cols="12"
            class="d-flex"
            :class="teamTimesheetsListBackup.length > 0 ? '' : 'justify-end'"
            style="margin-left: -108px"
          >
            <EmployeeDefaultFilterMenu
              v-if="teamTimesheetsListBackup.length > 0"
              class="justify-end"
              :reset-filter-count="resetFilterCount"
              :list-items="teamTimesheetsListBackup"
              :isApplyFilter="true"
              :showServiceProvider="true"
              @reset-emp-filter="resetFilter('filter')"
              @applied-filter="applyFilter($event)"
            >
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="team-timesheets-container">
      <section v-if="teamTimesheetsAccess">
        <v-window v-model="currentTabItem">
          <v-window-item :value="currentTabItem">
            <div v-if="listLoading" class="mt-3">
              <v-skeleton-loader
                ref="skeleton1"
                type="table-heading"
                class="mx-auto"
              ></v-skeleton-loader>
              <div v-for="i in 3" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item-avatar"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </div>
            <div v-else-if="isErrorInList">
              <AppFetchErrorScreen
                image-name="common/common-error-image"
                :content="errorContent"
                icon-name="fas fa-redo-alt"
                button-text="Retry"
                :isSmallImage="true"
                @button-click="refetchAPIs()"
              >
              </AppFetchErrorScreen>
            </div>
            <v-row v-else>
              <v-col v-if="showMyTimesheetForm" cols="12">
                <v-menu
                  v-if="showBackBtn"
                  v-model="openEmpListMenu"
                  transition="scale-transition"
                >
                  <template v-slot:activator="{ props }">
                    <v-card
                      v-bind="props"
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="d-flex align-center pa-2 mb-n4"
                      width="230"
                      @click="fetchEmployeesList()"
                    >
                      <v-icon
                        class="pr-6"
                        color="primary"
                        size="25"
                        @click.stop="closeMyTimeSheetForm()"
                        >fas fa-angle-left fa-lg</v-icon
                      >
                      <v-avatar color="purple-lighten-3" size="30">
                        {{
                          selectedEmployee.Employee_Name
                            ? selectedEmployee.Employee_Name.charAt(
                                0
                              ).toUpperCase()
                            : ""
                        }}
                      </v-avatar>
                      <div class="d-flex flex-column text-left px-2">
                        <div class="font-weight-medium overflow-ellipsis">
                          {{ selectedEmployee.Employee_Name }}
                        </div>
                        <div class="font-weight-regular overflow-ellipsis">
                          {{ selectedEmployee.userDefinedEmpId }}
                        </div>
                      </div>
                      <v-spacer></v-spacer>
                      <v-icon class="px-3" size="14" v-if="openEmpListMenu"
                        >fas fa-chevron-up</v-icon
                      >
                      <v-icon class="px-3" size="14" v-else
                        >fas fa-chevron-down</v-icon
                      >
                    </v-card>
                  </template>
                  <v-list>
                    <v-card v-if="isFetchingEmployees">
                      <v-progress-circular
                        color="primary"
                        :size="50"
                        indeterminate
                      ></v-progress-circular>
                    </v-card>
                    <div
                      v-else
                      style="
                        max-width: 300px;
                        max-height: 400px;
                        overflow: scroll;
                      "
                    >
                      <v-list-item
                        v-for="emp in allEmployeesList"
                        :key="emp.employeeId"
                        @click="onChangeEmployee(emp)"
                      >
                        <v-list-item-title class="pa-3"
                          >{{ emp.employeeName }}
                          <div class="text-grey">
                            {{ emp.userDefinedEmpId }}
                          </div>
                        </v-list-item-title>
                      </v-list-item>
                    </div>
                  </v-list>
                </v-menu>
                <EmpTimeSheets
                  :formId="23"
                  :employeeId="selectedEmployee.Employee_Id"
                  :weekRangeSelected="weekRange"
                  :requestId="selectedReqId"
                  :approvalStatus="selectedStatus"
                  :selectedEmployeeUserDefId="selectedEmployee.userDefinedEmpId"
                  :selectedEmployeeName="selectedEmployee.Employee_Name"
                  @timesheet-updated="timesheetUpdateCount += 1"
                  @individual-view="showBackBtn = !$event"
                ></EmpTimeSheets>
              </v-col>
              <v-col v-else cols="12">
                <ListTeamTimesheets
                  :items="teamTimesheetsList"
                  :originalList="teamTimesheetsListBackup"
                  :weekRange="weekRange"
                  :formAccess="teamTimesheetsAccess"
                  @on-select-item="openMyTimeSheetForm($event)"
                  @reset-filter="resetFilter('grid')"
                  @refetch-list="refetchAPIs()"
                  @add-employee="openAddForm($event)"
                  @on-change-week-range="onWeekChange($event)"
                  @fetch-current-week="onFetchCurrentWeek()"
                  @fetch-prev-week="onClickPrevWeek()"
                  @fetch-next-week="onClickNextWeek()"
                ></ListTeamTimesheets>
              </v-col>
            </v-row>
          </v-window-item>
        </v-window>
      </section>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineComponent /*,defineAsyncComponent*/ } from "vue";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import { LIST_EMP_TIMESHEETS } from "@/graphql/my-team/timesheets.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import moment from "moment";
import ListTeamTimesheets from "./ListTeamTimesheets.vue";
import EmpTimeSheets from "../../employee-self-service/timesheets/Timesheets.vue";

export default defineComponent({
  name: "TeamTimeSheets",

  components: {
    EmployeeDefaultFilterMenu,
    ListTeamTimesheets,
    EmpTimeSheets,
  },

  data() {
    return {
      currentTabItem: "tab-0",
      isLoading: false,
      mainTabs: [],
      mainTabsURLs: [],
      openEmpListMenu: false,
      allEmployeesList: [],
      isFetchingEmployees: false,
      // list
      teamTimesheetsList: [],
      teamTimesheetsListBackup: [],
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      weekRange: "",
      selectedReqId: 0,
      selectedStatus: "",
      // filter
      resetFilterCount: 0,
      // view/edit
      showBackBtn: true,
      showMyTimesheetForm: false,
      selectedEmployee: {},
      timesheetUpdateCount: 0,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    teamTimesheetsAccess() {
      let formAccess = this.accessRights("23");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    accessFormName() {
      let fAccess = this.accessRights("23");
      if (fAccess && fAccess.customFormName) {
        return fAccess.customFormName;
      } else return "Timesheets";
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("Team timesheets Error:", err);
    let msg =
      "Something went wrong while loading the team timesheets form. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  mounted() {
    // timesheets, approval-management
    let formIds = ["23", "184"],
      mTabs = [],
      mTabURLs = [];
    for (let form of formIds) {
      let fAccess = this.accessRights(form);
      if (fAccess && fAccess.accessRights && fAccess.accessRights["view"]) {
        if (form == 23) {
          mTabs.push(this.accessFormName);
          mTabURLs.push("");
        }
        if (form == 184) {
          mTabs.push("Approvals");
          mTabURLs.push("v3/approvals/approval-management?form_id=268");
        }
      }
    }
    this.mainTabs = mTabs;
    this.mainTabsURLs = mTabURLs;
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.accessFormName);
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.onFetchCurrentWeek();
  },

  methods: {
    onTabChange(tabName) {
      if (tabName !== this.accessFormName) {
        this.isLoading = true;
        this.$router.push("/approvals/approval-management?form_id=268");
      }
    },
    onFetchCurrentWeek() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      const currentDate = moment(); // current date
      // Get the week start and end dates
      const weekStart = currentDate
        .clone()
        .startOf("week")
        .format(orgDateFormat);
      const weekEnd = currentDate.clone().endOf("week").format(orgDateFormat);
      this.weekRange = weekStart + " to " + weekEnd;
      this.refetchAPIs();
    },
    onWeekChange(week) {
      this.weekRange = week;
      this.refetchAPIs();
    },

    onClickPrevWeek() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      let weekEndDate =
        this.weekRange && this.weekRange.includes("to")
          ? this.weekRange.split(" to ")
          : [this.weekRange, this.weekRange];
      const startOfWeek = moment(weekEndDate[0], orgDateFormat)
        .subtract(7, "days")
        .format(orgDateFormat);
      const endOfWeek = moment(weekEndDate[1], orgDateFormat)
        .subtract(7, "days")
        .format(orgDateFormat);
      this.weekRange = startOfWeek + " to " + endOfWeek;
      this.refetchAPIs();
    },

    onClickNextWeek() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      let weekEndDate =
        this.weekRange && this.weekRange.includes("to")
          ? this.weekRange.split(" to ")
          : [this.weekRange, this.weekRange];
      const startOfWeek = moment(weekEndDate[0], orgDateFormat)
        .add(7, "days")
        .format(orgDateFormat);
      const endOfWeek = moment(weekEndDate[1], orgDateFormat)
        .add(7, "days")
        .format(orgDateFormat);
      this.weekRange = startOfWeek + " to " + endOfWeek;
      this.refetchAPIs();
    },
    refetchAPIs() {
      mixpanel.track("Team-Timesheets-list-refetch");
      this.listLoading = true;
      this.timesheetUpdateCount = 0;
      this.errorContent = "";
      this.isErrorInList = false;
      this.showMyTimesheetForm = false;
      let weekEndDate =
        this.weekRange && this.weekRange.includes("to")
          ? this.weekRange.split(" to ")
          : this.weekRange;
      weekEndDate =
        weekEndDate && weekEndDate.length >= 2
          ? weekEndDate[1]
          : weekEndDate && weekEndDate.length >= 1
          ? weekEndDate[0]
          : "";
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      weekEndDate = moment(weekEndDate, orgDateFormat).format("YYYY-MM-DD");
      this.getTeamTimesheets(weekEndDate);
    },
    openMyTimeSheetForm(item) {
      this.selectedReqId = item.Request_Id;
      this.selectedStatus = item.Approval_Status;
      this.selectedEmployee = item;
      this.showMyTimesheetForm = true;
      mixpanel.track("Team-Timesheets-my-timesheet-opened");
    },
    openAddForm(item) {
      this.selectedReqId = 0;
      this.selectedStatus = "";
      this.selectedEmployee = item;
      this.showMyTimesheetForm = true;
      mixpanel.track("Team-Timesheets-employee-add-opened");
    },
    closeMyTimeSheetForm() {
      if (this.timesheetUpdateCount > 0) {
        this.refetchAPIs();
      }
      this.showMyTimesheetForm = false;
      mixpanel.track("Team-Timesheets-my-timesheet-closed");
    },
    resetFilter(calledFrom) {
      if (calledFrom === "grid") {
        this.resetFilterCount += 1;
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.teamTimesheetsList = this.teamTimesheetsListBackup;
      mixpanel.track("Team-Timesheets-filter-reset");
    },
    applyFilter(filteredArray) {
      let filteredList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.teamTimesheetsList = filteredList;
      mixpanel.track("Team-Timesheets-filter-applied");
    },
    getTeamTimesheets(weekEndDate) {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_EMP_TIMESHEETS,
          client: "apolloClientI",
          variables: {
            selfService: 0,
            weekendDate: weekEndDate,
            employeeView: 1,
            employeeId: vm.loginEmployeeId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveTimeSheetProjectDetails
          ) {
            mixpanel.track("Team-Timesheets-list-fetch-success");
            let { timesheetActivityDetails } =
              response.data.retrieveTimeSheetProjectDetails;
            timesheetActivityDetails = timesheetActivityDetails
              ? JSON.parse(timesheetActivityDetails)
              : [];
            const dataArray = timesheetActivityDetails;
            vm.teamTimesheetsList = dataArray;
            vm.teamTimesheetsListBackup = dataArray;
          } else {
            vm.handleListError();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "team timesheets",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
      mixpanel.track("Team-Timesheets-list-fetch-error");
    },
    onChangeEmployee(employee) {
      this.selectedStatus = "";
      this.selectedReqId = 0;
      let empObj = {
        Employee_Id: employee.employeeId,
        Employee_Name: employee.employeeName,
        userDefinedEmpId: employee.userDefinedEmpId,
      };
      this.selectedEmployee = empObj;
      this.openEmpListMenu = false;
    },
    async fetchEmployeesList() {
      if (this.allEmployeesList.length === 0) {
        this.isFetchingEmployees = true;
        await this.$store
          .dispatch("getEmployeesList", {
            formName: "Timesheets",
            formId: 23,
          })
          .then((empData) => {
            this.allEmployeesList = empData;
            this.allEmployeesList = this.allEmployeesList.filter(
              (el) => el.empStatus === "Active"
            );
            this.isFetchingEmployees = false;
          })
          .catch((err) => {
            let snackbarData = {
              isOpen: true,
              message: "",
              type: "warning",
            };
            if (err === "error") {
              snackbarData.message =
                "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
            } else {
              snackbarData.message = err;
            }
            this.showAlert(snackbarData);
            this.isFetchingEmployees = false;
            this.openEmpListMenu = false;
          });
      }
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style>
.team-timesheets-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .team-timesheets-container {
    padding: 5em 1em 0em 1em;
  }
}

.overflow-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 120px;
}
</style>
