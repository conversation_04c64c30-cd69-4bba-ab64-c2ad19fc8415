<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary darken-1"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <!-- Filter fields here -->
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedLocationCode"
              color="primary"
              :items="locationCodeList"
              label="Location Code"
              :itemSelected="selectedLocationCode"
              variant="solo"
              @selected-item="
                onChangeSelectField($event, 'selectedLocationCode')
              "
              multiple
              closable-chips
              chips
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedLocation"
              color="primary"
              :items="locationList"
              label="Location Name"
              :itemSelected="selectedLocation"
              variant="solo"
              @selected-item="onChangeSelectField($event, 'selectedLocation')"
              multiple
              closable-chips
              chips
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedLocationType"
              color="primary"
              :items="locationTypeList"
              label="Location Type"
              :itemSelected="selectedLocationType"
              variant="solo"
              @selected-item="
                onChangeSelectField($event, 'selectedLocationType')
              "
              multiple
              closable-chips
              chips
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCountry"
              color="primary"
              :items="countryList"
              label="Country"
              :itemSelected="selectedCountry"
              variant="solo"
              @selected-item="onChangeSelectField($event, 'selectedCountry')"
              multiple
              closable-chips
              chips
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedState"
              color="primary"
              :items="stateList"
              label="State"
              :itemSelected="selectedState"
              variant="solo"
              @selected-item="onChangeSelectField($event, 'selectedState')"
              multiple
              closable-chips
              chips
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCity"
              color="primary"
              :items="cityList"
              label="City"
              :itemSelected="selectedCity"
              variant="solo"
              @selected-item="onChangeSelectField($event, 'selectedCity')"
              multiple
              closable-chips
              chips
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="statusList"
              label="Status"
              :itemSelected="selectedStatus"
              variant="solo"
              @selected-item="onChangeSelectField($event, 'selectedStatus')"
              multiple
              closable-chips
              chips
              single-line
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click="fnApplyFilter"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
export default defineComponent({
  name: "LocationFilter",
  props: {
    originalList: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openFormFilter: false,
      selectedLocationCode: [],
      selectedLocation: [],
      selectedLocationType: [],
      selectedCountry: [],
      selectedState: [],
      selectedCity: [],
      filterItemList: [],
      locationCodeList: [],
      locationList: [],
      locationTypeList: [],
      countryList: [],
      stateList: [],
      cityList: [],
      selectedStatus: ["Active"],
      statusList: ["Active", "InActive"],
    };
  },
  mounted() {
    this.filterItemList = this.originalList;
    this.formFilterData();
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    fnApplyFilter() {
      let filteredArray = this.filterItemList;
      if (this.selectedLocationCode.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLocationCode.includes(item.Location_Code);
        });
      }
      if (this.selectedLocation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLocation.includes(item.Location_Name);
        });
      }
      if (this.selectedLocationType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLocationType.includes(item.Location_Type);
        });
      }
      if (this.selectedCountry.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCountry.includes(item.Country_Name);
        });
      }
      if (this.selectedState.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedState.includes(item.State_Name);
        });
      }
      if (this.selectedCity.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCity.includes(item.City_Name);
        });
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Location_Status);
        });
      }
      this.openFormFilter = false;
      this.$emit("apply-filter", filteredArray);
    },
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedLocationCode = [];
      this.selectedLocation = [];
      this.selectedLocationType = [];
      this.selectedCountry = [];
      this.selectedState = [];
      this.selectedCity = [];
      this.selectedStatus = ["Active"];
    },
    formFilterData() {
      const locationCodeSet = new Set();
      const locationSet = new Set();
      const locationTypeSet = new Set();
      const countrySet = new Set();
      const stateSet = new Set();
      const citySet = new Set();
      const statusSet = new Set();

      for (let item of this.originalList) {
        if (item && item.Location_Name) {
          this.locationList.push({
            locationSet: item.Location_Name,
          });
        }
        if (item && item.Location_Code) {
          locationCodeSet.add(item.Location_Code);
        }
        if (item && item.Location_Name) {
          locationSet.add(item.Location_Name);
        }
        if (item && item.Location_Type) {
          locationTypeSet.add(item.Location_Type);
        }
        if (item && item.Country_Name) {
          countrySet.add(item.Country_Name);
        }
        if (item && item.State_Name) {
          stateSet.add(item.State_Name);
        }
        if (item && item.City_Name) {
          citySet.add(item.City_Name);
        }
        if (item && item.Location_Status) {
          statusSet.add(item.Location_Status);
        }
      }

      this.locationList = this.removeDuplicatesFromArrayOfObject(
        this.locationList,
        "locationList"
      ).map((item) => item.locationList);
      this.locationCodeList = Array.from(locationCodeSet);
      this.locationList = Array.from(locationSet);
      this.locationTypeList = Array.from(locationTypeSet);
      this.countryList = Array.from(countrySet);
      this.stateList = Array.from(stateSet);
      this.cityList = Array.from(citySet);
      this.statusList = Array.from(statusSet);
    },
    removeDuplicatesFromArrayOfObject(array, key) {
      const keyValueMap = new Map();
      array.forEach((item) => {
        keyValueMap.set(item[key], item);
      });
      return Array.from(keyValueMap.values());
    },
  },
});
</script>
