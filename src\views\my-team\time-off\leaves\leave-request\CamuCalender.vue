<template>
  <div>
    <v-card
      elevation="3"
      class="card-item d-flex rounded-lg my-2"
      color="grey-lighten-5"
    >
      <v-row>
        <v-col cols="12">
          <div
            class="bg-primary pa-2 d-flex justify-space-between align-center"
          >
            <v-icon
              size="20"
              @click="!disableLeft ? changeMonth(-1) : {}"
              :disabled="disableLeft"
              no-details
              >fas fa-chevron-left</v-icon
            >
            <span class="text-h6">{{ formattedSelectedMonth }}</span>
            <v-icon
              size="20"
              @click="disableRight ? {} : changeMonth(1)"
              :disabled="disableRight"
              :style="disableRight ? 'cursor: not-allowed' : ''"
              no-details
              >fas fa-chevron-right</v-icon
            >
          </div>
          <vue-cal
            class="attendance-calendar"
            :disable-views="['years', 'year', 'week', 'day']"
            :hideViewSelector="true"
            :hide-title-bar="true"
            active-view="month"
            :selected-date="selectedMonthYear"
          >
            <template #cell-content="{ cell }">
              <div
                class="d-flex flex-column justify-space-between"
                style="height: 100%"
                @click="openViewForm(cell.formattedDate)"
              >
                <div class="d-flex flex-start pl-2 font-weight-bold">
                  {{ getDate(cell.formattedDate) }}
                </div>
                <div
                  class="font-weight-bold py-1"
                  :style="customColor(cell.formattedDate)"
                  v-html="getCustomTitle(cell.formattedDate)"
                ></div>
              </div>
            </template>
          </vue-cal>
        </v-col>
      </v-row>
    </v-card>

    <!-- Schedule details modal -->
    <v-dialog v-model="showModal" max-width="600px">
      <v-card
        elevation="3"
        class="card-item d-flex pa-4 rounded-lg ma-2"
        color="grey-lighten-5"
      >
        <v-card-title
          class="text-primary font-weight-bold text-h6 d-flex align-center justify-space-between"
        >
          <span class="text-truncate"
            >Schedule(s) on
            {{ formatDate(selectedScheduleDetails[0]?.eventDate) }}</span
          ><span class="d-flex justify-end">
            <v-icon
              color="primary"
              size="15"
              class="font-weight-bold mr-n4 mt-n6"
              @click="closeModal()"
              >fas fa-times</v-icon
            >
          </span>
        </v-card-title>
        <v-card-text>
          <div v-if="selectedScheduleDetails?.length">
            <div
              v-for="schedule of selectedScheduleDetails"
              :key="schedule in selectedScheduleDetails"
            >
              <v-row>
                <v-col cols="6" sm="6" md="3" lg="3">
                  <div class="mt-2 mr-2 d-flex flex-column justify-start">
                    <b class="mb-1 text-grey justify-start">Schedule</b>
                    <span class="py-2">
                      {{ checkNullValue(schedule.fromTime) }}
                      <span v-if="schedule.toTime"
                        >- {{ schedule.toTime }}</span
                      ></span
                    >
                  </div>
                </v-col>
                <v-col cols="6" sm="6" md="3" lg="3">
                  <div class="mt-2 mr-2 d-flex flex-column justify-start">
                    <b class="mb-1 text-grey justify-start">Class</b>
                    <span class="py-2">
                      {{ checkNullValue(schedule.sectionName) }}</span
                    >
                  </div>
                </v-col>
                <v-col cols="6" sm="6" md="3" lg="3">
                  <div class="mt-2 mr-2 d-flex flex-column justify-start">
                    <b class="mb-1 text-grey justify-start">Subject</b>
                    <span class="py-2">{{
                      checkNullValue(schedule.subject)
                    }}</span>
                  </div>
                </v-col>
                <v-col cols="6" sm="6" md="3" lg="3">
                  <div class="mt-2 mr-2 d-flex flex-column justify-start">
                    <b class="mb-1 text-grey justify-start">Location</b>
                    <span class="py-2">{{
                      checkNullValue(schedule.locationName)
                    }}</span>
                  </div>
                </v-col>
              </v-row>
              <v-spacer />
            </div>
          </div>
          <div v-else>No schedules for this date</div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import VueCal from "vue-cal";
import "vue-cal/dist/vuecal.css";
import moment from "moment";
import { checkNullValue } from "@/helper";

export default {
  name: "CamuCalender",
  components: {
    VueCal,
  },
  data() {
    return {
      showModal: false,
      selectedMonthYear: new Date(),
      selectedScheduleDetails: [],
    };
  },

  props: {
    getStaffSchedulesByDateRange: {
      type: Object,
      required: true,
    },
  },

  computed: {
    formattedSelectedMonth() {
      return moment(this.selectedMonthYear).format("MMMM, YYYY");
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "";
      };
    },
    disableLeft() {
      const selectedMonth = moment(this.selectedMonthYear).startOf("month");
      const firstEventMonth = moment(
        this.getStaffSchedulesByDateRange.processedSchedules[0]?.eventDate
      ).startOf("month");

      return selectedMonth.isSameOrBefore(firstEventMonth);
    },
    disableRight() {
      const lastSchedule =
        this.getStaffSchedulesByDateRange?.processedSchedules?.slice(-1)[0];
      if (!lastSchedule || !this.selectedMonthYear) return false;
      const selectedMonth = moment(this.selectedMonthYear).startOf("month");
      const lastEventMonth = moment(lastSchedule.eventDate).startOf("month");

      return selectedMonth.isSameOrAfter(lastEventMonth);
    },
    getDate() {
      return (date) => {
        return moment(date).format("DD");
      };
    },
    getCustomTitle() {
      return (date) => {
        const matchedDays =
          this.getStaffSchedulesByDateRange?.processedSchedules?.find(
            (schedule) => schedule.eventDate == date
          );
        if (!matchedDays) return "";
        const matchedSchedule =
          this.getStaffSchedulesByDateRange?.allSchedules?.find(
            (schedule) => schedule.eventDate == date
          );
        return matchedSchedule ? "Scheduled" : "No Schedules";
      };
    },
    customColor() {
      return (date) => {
        const matchedDays =
          this.getStaffSchedulesByDateRange?.processedSchedules?.find(
            (schedule) => schedule.eventDate == date
          );
        if (!matchedDays) return "";
        const matchedSchedule =
          this.getStaffSchedulesByDateRange?.processedSchedules?.find(
            (schedule) => schedule?.eventDate == date
          );
        return {
          backgroundColor: matchedSchedule.colorCode || "",
          cursor: matchedSchedule.isScheduled ? "pointer" : "default",
        };
      };
    },
  },
  methods: {
    checkNullValue,
    changeMonth(value) {
      if (value) {
        let newDate = moment(this.selectedMonthYear).add(value, "M");
        this.selectedMonthYear = newDate.toDate();
      }
    },
    openViewForm(item) {
      const selectedScheduleDetails =
        this.getStaffSchedulesByDateRange?.allSchedules?.filter((el) =>
          el.schedules?.some((schedule) => schedule.eventDate == item)
        );
      const extractedSchedules = selectedScheduleDetails?.flatMap(
        (el) => el.schedules
      );

      if (extractedSchedules?.length > 0) {
        this.showModal = true;
        this.selectedScheduleDetails = extractedSchedules;
      }
    },
    closeModal() {
      this.showModal = false;
      this.selectedScheduleDetails = [];
    },
  },
};
</script>

<style>
.vuecal {
  max-height: 80vh;
}
.vuecal__title-bar {
  background-color: rgb(var(--v-theme-primary));
  color: white;
}
.vuecal__cell {
  border: 0.1px solid #e8eaec;
  height: 108px;
  background-color: white;
}
.vuecal__event-title {
  display: flex;
  justify-content: center;
}
.vuecal__weekdays-headings {
  background-color: rgb(var(--v-theme-hover));
}
.datepicker-calendar_view .vuejs3-datepicker__value {
  min-width: 160px;
  display: flex;
  align-items: center;
}
.datepicker-calendar_view .vuejs3-datepicker__calendar {
  right: 1px;
}
:deep(.employee-list-btn > .v-btn__content) {
  max-width: 100%;
  white-space: wrap;
}
</style>
