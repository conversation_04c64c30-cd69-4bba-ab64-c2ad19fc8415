<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Certificate Details</span
        >
        <v-spacer></v-spacer>
        <v-icon
          color="grey"
          size="25"
          @click="$emit('close-certification-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditCertificateForm">
          <v-row>
            <v-col cols="12" md="6">
              <CustomSelect
                v-if="labelList[456]?.Predefined?.toLowerCase() == 'yes'"
                v-model="certificateFormData.Certification_Name"
                :items="certificationList"
                :label="labelList[456].Field_Alias"
                :isLoading="dropdownLoading"
                item-title="Required_Certification"
                item-value="Required_Certification"
                :isRequired="
                  labelList[456].Mandatory_Field?.toLowerCase() == 'yes'
                "
                :itemSelected="certificateFormData.Certification_Name"
                :rules="[
                  required(
                    labelList[456]?.Field_Alias || 'Certificate',
                    certificateFormData.Certification_Name
                  ),
                ]"
              />
              <v-text-field
                v-else
                v-model="certificateFormData.Certification_Name"
                :rules="[
                  required(
                    labelList[456]?.Field_Alias || 'Certificate',
                    certificateFormData.Certification_Name
                  ),
                  validateWithRulesAndReturnMessages(
                    certificateFormData.Certification_Name,
                    'certificationName',
                    labelList[456]?.Field_Alias || 'Certificate'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[456]?.Field_Alias || "Certificate" }}
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-menu
                v-model="ReceivedOnMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="receivedDate"
                    v-model="formattedReceivedOn"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Received On', formattedReceivedOn)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Received On<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="certificateFormData.Received_Date"
                  :min="selectedEmpDobDate"
                  :max="currentDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="certificateFormData.Certificate_Received_From"
                :rules="[
                  required(
                    'Received From',
                    certificateFormData.Certificate_Received_From
                  ),
                  validateWithRulesAndReturnMessages(
                    certificateFormData.Certificate_Received_From,
                    'receivedFrom',
                    'Received From'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Received From<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-if="labelList[381]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="6"
            >
              <v-text-field
                v-model="certificateFormData.Ranking"
                :rules="[
                  labelList[381]?.Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        labelList[381]?.Field_Alias,
                        certificateFormData.Ranking
                      )
                    : true,
                  validateWithRulesAndReturnMessages(
                    certificateFormData.Ranking,
                    'skillCategory',
                    labelList[381]?.Field_Alias
                  ),
                ]"
                variant="solo"
                clearable
                :ref="`ranking${i}`"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  {{ labelList[381]?.Field_Alias }}
                  <span
                    v-if="
                      labelList[381]?.Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field></v-col
            >
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="this.$emit('close-certification-form')"
                  class="ma-2 pa-2"
                  variant="outlined"
                  >Cancel</v-btn
                >
                <v-btn
                  class="ma-2 pa-2"
                  :disabled="!isFormDirty"
                  color="primary"
                  @click="validateCertificateDetails"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import moment from "moment";
import { ADD_UPDATE_CERTIFICATE_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "AddEditCertificationDetails",
  components: {
    CustomSelect,
  },
  props: {
    selectedCertificationDetails: {
      type: Object,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
  },
  mixins: [validationRules],
  emits: ["refetch-career-details", "close-certification-form"],
  data() {
    return {
      certificateFormData: {
        Certification_Name: "",
        Received_Date: null,
        Certificate_Received_From: "",
        Ranking: "",
      },
      isFormDirty: false,
      //Date-picker
      formattedReceivedOn: "",
      ReceivedOnMenu: false,
      // edit
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      certificationList: [],
      dropdownLoading: false,
    };
  },

  watch: {
    "certificateFormData.Received_Date": function (val) {
      if (val) {
        this.ReceivedOnMenu = false;
        this.formattedReceivedOn = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    selectedEmpDobDate() {
      if (
        this.selectedEmployeeDob &&
        this.selectedEmployeeDob !== "0000-00-00"
      ) {
        return moment(this.selectedEmployeeDob).format("YYYY-MM-DD");
      } else return null;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedCertificationDetails &&
      Object.keys(this.selectedCertificationDetails).length > 0
    ) {
      this.certificateFormData = JSON.parse(
        JSON.stringify(this.selectedCertificationDetails)
      );
      if (this.certificateFormData.Received_Date) {
        this.formattedReceivedOn = this.formatDate(
          this.certificateFormData?.Received_Date
        );
        this.certificateFormData.Received_Date = this.certificateFormData
          .Received_Date
          ? new Date(this.certificateFormData.Received_Date)
          : null;
      }
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
    this.getDropdownDetails();
  },

  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },
    async getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;
      await vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: vm.callingFrom === "team" ? 243 : 18,
            key: ["Required_Certification"],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "required_certification") {
                vm.certificationList = item.data;
              }
            });
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "certificate dropdown",
        isListError: false,
      });
    },
    async validateCertificateDetails() {
      const { valid } = await this.$refs.addEditCertificateForm.validate();
      mixpanel.track("EmpProfile-career-certification-submit-click");
      if (valid) {
        this.updateCertificateDetails();
      }
    },

    updateCertificateDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_CERTIFICATE_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            certificationId: vm.certificateFormData.Certification_Id,
            certificationName: vm.certificateFormData.Certification_Name,
            receivedDate: moment(vm.certificateFormData.Received_Date).isValid()
              ? moment(vm.certificateFormData.Received_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            receivedFrom: vm.certificateFormData.Certificate_Received_From,
            university: vm.certificateFormData.University,
            yearOfPassing: vm.certificateFormData.Year_Of_Passing,
            percentage: vm.certificateFormData.Percentage,
            grade: vm.certificateFormData.Grade,
            ranking: vm.certificateFormData.Ranking,
            formName: vm.callingFrom === "profile" ? "My Profile" : "",
          },
          client: "apolloClientAD",
        })
        .then(() => {
          mixpanel.track("EmpProfile-career-certification-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Certificate details updated successfully"
                : "Certificate details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-career-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-career-certification-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "certificate details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
