<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="primary" size="20">fas fa-tasks</v-icon>
          </v-avatar>
          <div class="text-subtitle-1 font-weight-bold">{{ projectLabel }}</div>
        </div>
        <div class="d-flex align-center">
          <div
            v-if="
              !showEmployeeList &&
              !showAccreditationList &&
              formAccess.update &&
              projectDetails.status !== 'Closed'
            "
            class="d-flex"
          >
            <v-btn
              rounded="lg"
              variant="outlined"
              size="small"
              theme="dark"
              :disabled="fetchingAccreditations"
              class="mr-2 font-weight-bold primary"
              @click="openModal = true"
              ><span style="text-transform: initial"
                >Close the {{ projectLabelSmallCase }}</span
              ></v-btn
            >
            <v-btn
              rounded="lg"
              variant="elevated"
              size="small"
              :disabled="fetchingAccreditations"
              class="mr-2 font-weight-bold primary"
              @click="$emit('edit-project')"
              >Edit</v-btn
            >
          </div>
          <v-icon class="mx-1" color="primary" @click="closeView()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <FormTab
        :model-value="openedSubTab"
        grow
        :hide-slider="true"
        style="border-bottom: 1px solid #cfcfcf; border-radius: 0px"
      >
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            {{ tab.label }}
            <div
              v-if="isActiveSubTab(tab.value)"
              class="mt-3 mb-n4"
              style="border-bottom: 4px solid; width: 200px"
            ></div>
          </div>
        </v-tab>
      </FormTab>
      <div
        :style="
          isMobileView
            ? 'height: calc(100vh - 400px); overflow: scroll'
            : 'height: calc(100vh - 340px); overflow: scroll'
        "
      >
        <v-card-text>
          <v-alert
            v-model="showWarning"
            text
            density="compact"
            type="warning"
            class="mb-0"
            closable
          >
            {{ alertMessage }}
          </v-alert>
          <v-window v-model="openedSubTab" style="width: 100%">
            <v-window-item value="project">
              <div v-if="!showEmployeeList && !showAccreditationList">
                <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      {{ projectLabel }}
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ projectDetails.projectName }}
                    </p>
                  </v-col>

                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">Client</p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(projectDetails.clientName) }}
                    </p>
                  </v-col>

                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      {{ additionalFieldProjectLabel }} Location
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(projectDetails.locationName) }}
                    </p>
                  </v-col>

                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      {{ additionalFieldProjectLabel }} Manager
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(projectDetails.managerName) }}
                    </p>
                  </v-col>

                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">Status</p>
                    <p
                      class="text-subtitle-1 font-weight-regular"
                      :class="
                        projectDetails.status !== 'Closed'
                          ? 'green--text'
                          : 'red--text'
                      "
                    >
                      {{
                        projectDetails.status ? projectDetails.status : "Open"
                      }}
                    </p>
                  </v-col>

                  <v-col
                    v-if="projectCoverage === 'CUSTOMGROUP'"
                    cols="12"
                    sm="6"
                  >
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Custom Employee Group
                    </p>
                    <v-skeleton-loader
                      v-if="fetchingGroups"
                      type="text"
                      class="mt-3 ml-n4"
                      style="max-width: 40%"
                    ></v-skeleton-loader>
                    <p v-else class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(customGroupName) }}
                    </p>
                  </v-col>
                  <v-col
                    v-if="projectCoverage === 'CUSTOMGROUP' && customGroupName"
                    cols="12"
                    sm="6"
                  >
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Employees - {{ empListInSelectedGroup.length }}
                    </p>
                    <v-skeleton-loader
                      v-if="fetchingGroupEmployees"
                      type="text"
                      class="mt-3 ml-n4"
                      style="max-width: 40%"
                    ></v-skeleton-loader>
                    <div v-else>
                      <div
                        v-if="empListInSelectedGroup.length === 0"
                        class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                      >
                        <v-icon color="warning" size="25"
                          >fas fa-exclamation-triangle</v-icon
                        >
                        <span
                          v-if="errorInFetchEmployeesList"
                          class="pl-2 text-caption"
                          >Something went wrong while fetching the employees
                          list. Please try again.
                          <a
                            class="text-primary"
                            @click="fetchCustomGroupEmployees"
                            >Refresh
                          </a>
                        </span>
                        <span v-else class="pl-2 text-caption">
                          It seems like there are no employees associated with
                          the selected custom group. Please add some employees
                          under the selected group or try choosing an another
                          group.</span
                        >
                      </div>
                      <div v-else class="d-flex align-center mt-n3">
                        <AvatarOrderedList
                          v-if="empListInSelectedGroup.length > 0"
                          class="mt-4"
                          :ordered-list="empListInSelectedGroup"
                        ></AvatarOrderedList>
                        <v-btn
                          rounded="lg"
                          color="primary"
                          size="small"
                          class="mt-4"
                          @click="openCustomGroupEmpList()"
                        >
                          View All
                        </v-btn>
                      </div>
                    </div>
                  </v-col>

                  <v-col v-if="projectCoverage === 'Employee'" cols="12" sm="6">
                    <p class="text-subtitle-1 text-grey-darken-1">Employees</p>
                    <v-skeleton-loader
                      v-if="fetchingEmployees"
                      type="text"
                      class="mt-3 ml-n4"
                      style="max-width: 40%"
                    ></v-skeleton-loader>
                    <div v-else class="mt-2 text-subtitle-1">
                      <div
                        v-if="employeesList && employeesList.length > 0"
                        class="d-flex align-center"
                      >
                        <AvatarOrderedList
                          :ordered-list="employeesList"
                          empNameKey="employee_name"
                        ></AvatarOrderedList>
                        <v-btn
                          rounded="lg"
                          color="primary"
                          size="small"
                          class="ml-1"
                          @click="openEmployeesList(projectDetails)"
                        >
                          View All
                        </v-btn>
                      </div>
                      <p v-else class="text-subtitle-1 font-weight-regular">
                        -
                      </p>
                    </div>
                  </v-col>

                  <v-col cols="12" sm="6">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Associate Training/Accreditation
                    </p>
                    <v-skeleton-loader
                      v-if="fetchingAccreditations"
                      type="text"
                      class="mt-3 ml-n4"
                      style="max-width: 40%"
                    ></v-skeleton-loader>
                    <div
                      v-else-if="accreditationCategoryAndTypes.length > 0"
                      class="text-subtitle-1"
                    >
                      <v-btn
                        rounded="lg"
                        color="primary"
                        size="small"
                        class="mt-2"
                        @click="openAccreditationList()"
                      >
                        View All
                      </v-btn>
                    </div>
                    <p v-else class="text-subtitle-1 font-weight-regular">-</p>
                  </v-col>

                  <v-col cols="12">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Description
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(projectDetails.description) }}
                    </p>
                  </v-col>

                  <v-col v-if="moreDetailsList.length > 0" cols="12">
                    <MoreDetails
                      :more-details-list="moreDetailsList"
                      :open-close-card="openMoreDetails"
                      @on-open-close="openMoreDetails = $event"
                    ></MoreDetails>
                  </v-col>
                </v-row>
              </div>
              <div v-else>
                <div class="px-6 d-flex ma-2">
                  <v-btn rounded="lg" color="primary" @click="backToViewForm()">
                    <v-icon class="mr-1"> fas fa-chevron-left </v-icon>
                    Back
                  </v-btn>
                </div>
                <v-data-table
                  v-if="showAccreditationList"
                  :items="accreditationCategoryAndTypes"
                  :headers="accreditationHeader"
                  :search="searchedItem"
                  class="elevation-1"
                >
                  <template #top>
                    <div class="d-flex justify-center">
                      <v-text-field
                        v-model="searchedItem"
                        placeholder="Search"
                        class="mx-4"
                        clearable
                        style="max-width: 70%"
                        prepend-inner-icon="search"
                      ></v-text-field>
                    </div>
                  </template>
                </v-data-table>
                <EmployeeListCard
                  v-else
                  ref="employeesListCard"
                  :modal-title="
                    projectCoverage === 'CUSTOMGROUP'
                      ? 'Custom Group Employees'
                      : 'Employees'
                  "
                  :employees-list="selectedEmployees"
                  :showFilterSearch="true"
                  :show-filter="false"
                  :show-modal="showEmployeeList"
                  :selectable="false"
                  @close-modal="showEmployeeList = false"
                >
                </EmployeeListCard>
              </div>
            </v-window-item>
            <v-window-item value="project-activities">
              <div style="height: calc(100vh - 400px); overflow: scroll">
                <ProjectActivityCard
                  v-for="(data, index) in projectActivityList"
                  :key="data.projectActivityId"
                  :formData="data"
                  :activityList="activityList"
                  :projectId="projectDetails.projectId"
                  :openActivityEdit="openActivityEdit[index]"
                  @change-activity-edit="changeActivityEdit(index)"
                  @refetch-data="fetchActivityWithProject()"
                  :addIcon="
                    projectActivityList.length - 1 == index && !addNewData
                  "
                  class="ma-1"
                  :isAdd="false"
                  :formAccess="formAccess"
                  :addNewData="addNewData"
                  :isAnyActivityEditClosed="isAnyActivityEditClosed"
                  :projectStatus="projectDetails.status"
                  :disableActivityList="disableActivityList"
                  :fetchingActivityList="fetchingActivityList"
                  @add-new-activity="addNewActivity()"
                  @close-edit="closeEditForm()"
                ></ProjectActivityCard>
                <ProjectActivityCard
                  v-if="addNewData"
                  :formData="{}"
                  :openActivityEdit="false"
                  :activityList="activityList"
                  :formAccess="formAccess"
                  :isAdd="true"
                  :projectId="projectDetails.projectId"
                  :fetchingActivityList="fetchingActivityList"
                  @refetch-data="fetchActivityWithProject()"
                  :disableActivityList="disableActivityList"
                  @close-edit="closeEditForm()"
                  class="ma-1 mb-4"
                ></ProjectActivityCard>
                <AppFetchErrorScreen
                  v-if="
                    !projectActivityList.length && !addNewData && !isLoadingCard
                  "
                  :content="`No activities are associated to this ${projectLabelSmallCase}`"
                  icon-name="fas fa-plus"
                  :button-text="
                    projectDetails && projectDetails.status != 'Closed'
                      ? 'Add New'
                      : ''
                  "
                  @button-click="addNewActivity()"
                >
                </AppFetchErrorScreen>
              </div>
            </v-window-item>
          </v-window>
        </v-card-text>
      </div>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isLoadingCard"
        scrim="#fff"
      >
        <v-progress-circular color="primary" indeterminate size="64">
        </v-progress-circular>
      </v-overlay>
    </v-card>
    <AppWarningModal
      v-if="openModal"
      :open-modal="openModal"
      icon-name="fas fa-exclamation-triangle"
      icon-color="amber"
      :confirmationHeading="`Are you sure you want to close the ${projectLabelSmallCase}?`"
      :confirmationText="`Once a ${projectLabelSmallCase} is closed, it cannot be reopened`"
      @close-warning-modal="openModal = false"
      @accept-modal="onCloseProject()"
    ></AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue } from "@/helper";
import moment from "moment";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);
const EmployeeListCard = defineAsyncComponent(() =>
  import("@/components/helper-components/EmployeeListCard")
);
const AvatarOrderedList = defineAsyncComponent(() =>
  import("@/components/helper-components/AvatarOrderedList")
);
import ProjectActivityCard from "./ProjectActivityCard.vue";
// queries
import {
  ADD_EDIT_PROJECTS,
  RETRIEVE_PROJECT_ACTIVITIES,
} from "@/graphql/corehr/projectsQueries";

import { RETRIEVE_PROJECT_MAPPED_ACCREDITATION_DETAILS } from "@/graphql/dropDownQueries.js";
import { LIST_PROJECT_ACTIVITIES } from "@/graphql/corehr/projectActivityQueries.js";
export default {
  name: "ViewProjects",

  components: {
    MoreDetails,
    EmployeeListCard,
    AvatarOrderedList,
    ProjectActivityCard,
  },

  props: {
    projectDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    projectCoverage: {
      type: String,
      default: "",
    },
  },

  emits: [
    "close-view-form",
    "accreditation-retrieved",
    "project-closed",
    "edit-project",
  ],

  data: () => ({
    // data
    moreDetailsList: [],
    selectedEmployees: [],
    employeesList: [],
    allEmployeesList: [],
    accreditationCategoryAndTypes: [],
    empListInSelectedGroup: [],
    customGroupName: "",

    // others
    openMoreDetails: false,
    openModal: false,
    showEmployeeList: false,
    showAccreditationList: false,
    accreditationHeader: [
      {
        title: "Accreditation Category",
        key: "Accreditation_Category",
      },
      {
        title: "Accreditation Type",
        key: "Accreditation_Type",
      },
    ],
    accreditationIds: [],
    searchedItem: "",

    // loading
    fetchingGroups: false,
    isLoadingCard: false,
    fetchingEmployees: false,
    fetchingAccreditations: false,
    fetchingGroupEmployees: false,
    fetchingActivityList: false,

    // error
    alertMessage: "",
    showWarning: false,
    errorInFetchEmployeesList: false,

    // sub tabs
    openedSubTab: "project",
    projectActivityList: [],
    openActivityEdit: [],
    showValidationAlert: false,
    validationMessages: [],
    activityList: [],
    addNewData: false,
  }),

  computed: {
    subTabItems() {
      return [
        {
          label: this.projectLabel,
          value: "project",
          disable: false,
        },
        {
          label: this.projectLabel + " Activities",
          value: "project-activities",
          disable: false,
        },
      ];
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    disableActivityList() {
      return this.projectActivityList.map((item) => item.activityName);
    },
    isAnyActivityEditClosed() {
      return this.openActivityEdit.some((value) => value === false);
    },
    projectLabel() {
      return this.$store.state.projectLabel;
    },
    projectLabelSmallCase() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel ? pLabel.toLowerCase() : pLabel;
    },
    additionalFieldProjectLabel() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel === "Course" ? "" : pLabel;
    },
  },

  watch: {
    projectDetails() {
      this.callInitialApis();
    },

    showWarning(val) {
      if (val) {
        setTimeout(() => {
          this.showWarning = false;
          this.alertMessage = "";
        }, 5000);
      }
    },
    openedSubTab(val) {
      if (val === "project-activities") {
        this.fetchActivity();
        this.fetchActivityWithProject();
      }
    },
  },

  mounted() {
    this.callInitialApis();
  },

  methods: {
    checkNullValue,
    addNewActivity() {
      this.addNewData = true;
    },
    changeActivityEdit(index) {
      this.closeEditForm();
      this.openActivityEdit[index] = false;
    },
    closeEditForm() {
      this.openActivityEdit = Array(this.projectActivityList.length).fill(true);
      this.addNewData = false;
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },

    callInitialApis() {
      this.prefillMoreDetails();
      this.retrieveProjectMappedAccreditations();
      if (this.projectCoverage === "Employee") {
        this.fetchEmployeesList();
      } else if (this.projectCoverage === "CUSTOMGROUP") {
        this.retrieveCustomGroups();
        this.fetchCustomGroupEmployees();
      }
    },
    fetchActivityWithProject() {
      let vm = this;
      vm.isLoadingCard = true;
      vm.addNewData = false;
      vm.$apollo
        .query({
          query: RETRIEVE_PROJECT_ACTIVITIES,
          variables: {
            projectId: vm.projectDetails.projectId,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveProjectActivities) {
            vm.projectActivityList =
              response.data.retrieveProjectActivities.activityDetails;
            vm.openActivityEdit = Array(vm.projectActivityList.length).fill(
              true
            );
          } else {
            vm.handleListError((err = ""), "activities");
          }
          vm.isLoadingCard = false;
        })
        .catch((err) => {
          vm.isLoadingCard = false;
          vm.handleListError(err, "activities");
        });
    },
    fetchActivity() {
      let vm = this;
      vm.fetchingActivityList = true;
      vm.addNewData = false;
      vm.$apollo
        .query({
          query: LIST_PROJECT_ACTIVITIES,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listProjectActivities) {
            vm.activityList = response.data.listProjectActivities.activities;
          } else {
            vm.handleListError((err = ""), "activities");
          }
          vm.fetchingActivityList = false;
        })
        .catch((err) => {
          vm.fetchingActivityList = false;
          vm.handleListError(err, "activities");
        });
    },
    handleListError(err = "", formName) {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((validationErrors) => {
          this.validationMessages = [validationErrors];
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    // retrieve states based on country selected
    async retrieveCustomGroups() {
      if (this.projectDetails.customGroupId) {
        this.fetchingGroups = true;
        await this.$store
          .dispatch("listCustomGroupBasedOnFormName", {
            formName: "projects",
          })
          .then((groupList) => {
            const customGroupList = groupList ? groupList : [];
            let matchedGroup = customGroupList.filter(
              (el) => el.Custom_Group_Id == this.projectDetails.customGroupId
            );
            if (matchedGroup && matchedGroup.length > 0) {
              this.customGroupName = matchedGroup[0].Custom_Group_Name;
            } else this.customGroupName = "";
            this.fetchingGroups = false;
          })
          .catch(() => {
            this.fetchingGroups = false;
          });
      }
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(this.projectDetails.addedOn),
        addedByName = this.projectDetails.addedByName,
        updatedByName = this.projectDetails.updatedByName,
        updatedOn = this.formatDate(this.projectDetails.updatedOn);

      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },

    closeView() {
      this.showEmployeeList = false;
      this.$emit("close-view-form");
    },

    async fetchEmployeesList() {
      if (this.projectDetails.employeeId) {
        if (this.allEmployeesList.length === 0) {
          this.fetchingEmployees = true;
          await this.$store
            .dispatch("getAllEmployeesList", {
              formName: "Projects",
              isActiveOnly: 1,
            })
            .then((empData) => {
              this.allEmployeesList = empData;
              this.findMatchedEmpList();
              this.fetchingEmployees = false;
            })
            .catch((err) => {
              if (err === "error") {
                this.alertMessage =
                  "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
              } else {
                this.alertMessage = err;
              }
              this.showWarning = true;
              this.allEmpList = [];
              this.fetchingEmployees = false;
            });
        } else {
          this.findMatchedEmpList();
        }
      }
    },

    findMatchedEmpList() {
      let matchedEmpList = [];
      let projectEmployees = this.projectDetails.employeeId.split(",");
      for (let emp of this.allEmployeesList) {
        let empId = emp.employee_id.toString();
        if (projectEmployees.includes(empId)) {
          matchedEmpList.push(emp);
        }
      }
      this.employeesList = matchedEmpList;
    },

    // retrieve project mapped accreditations
    retrieveProjectMappedAccreditations() {
      let vm = this;
      vm.fetchingAccreditations = true;
      vm.$apollo
        .query({
          query: RETRIEVE_PROJECT_MAPPED_ACCREDITATION_DETAILS,
          variables: {
            formName: "projects",
          },
          fetchPolicy: "no-cache",
          client: "apolloClientV",
        })
        .then((response) => {
          let { errorCode, projectMappingDetails } =
            response.data.retrieveProjectAccreditationCategoryDetails;
          if (!errorCode) {
            let projectMapped = projectMappingDetails;
            let matchedCategory = projectMapped.filter(
              (el) => el.Project_Id === vm.projectDetails.projectId
            );
            let accreditationIds = [],
              validCategories = [];
            for (let accreditation of matchedCategory) {
              if (accreditation.Accreditation_Category_And_Type_Id) {
                accreditationIds.push(
                  accreditation.Accreditation_Category_And_Type_Id
                );
                validCategories.push(accreditation);
              }
            }
            vm.$emit("accreditation-retrieved", accreditationIds);
            vm.accreditationIds = accreditationIds;
            vm.accreditationCategoryAndTypes = validCategories;
          }
          vm.fetchingAccreditations = false;
        })
        .catch(() => {
          vm.fetchingAccreditations = false;
        });
    },

    openAccreditationList() {
      this.showAccreditationList = true;
    },

    // on changing the custom group
    openEmployeesList() {
      this.showEmployeeList = true;
      this.selectedEmployees = this.employeesList;
    },

    // open employees list to view the employees when the coverage is custom-group
    openCustomGroupEmpList() {
      this.selectedEmployees = this.empListInSelectedGroup;
      this.showEmployeeList = true;
    },

    async fetchCustomGroupEmployees() {
      if (this.projectDetails.customGroupId) {
        let vm = this;
        vm.errorInFetchEmployeesList = false;
        vm.fetchingGroupEmployees = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(vm.projectDetails.customGroupId),
          })
          .then((response) => {
            const employeeDetails = response;
            if (!employeeDetails || employeeDetails.length === 0) {
              vm.empListInSelectedGroup = [];
            } else {
              for (let i = 0; i < employeeDetails.length; i++) {
                employeeDetails[i].employee_name =
                  employeeDetails[i]["employeeName"];
                employeeDetails[i].designation_name =
                  employeeDetails[i]["designationName"];
                employeeDetails[i].department_name =
                  employeeDetails[i]["departmentName"];
                employeeDetails[i].user_defined_empid =
                  employeeDetails[i]["userDefinedEmpId"];
                delete employeeDetails[i].key1;
              }
              vm.empListInSelectedGroup = employeeDetails;
            }
            vm.fetchingGroupEmployees = false;
          })
          .catch(() => {
            vm.empListInSelectedGroup = [];
            vm.errorInFetchEmployeesList = true;
            vm.fetchingGroupEmployees = false;
          });
      }
    },

    onCloseProject() {
      let vm = this;
      vm.openModal = false;
      vm.isLoadingCard = true;
      let empIds = [];
      if (vm.projectDetails.employeeId && vm.projectCoverage === "Employee") {
        let employeeIds = vm.projectDetails.employeeId.split(",");
        for (let empId of employeeIds) {
          empIds.push(parseInt(empId, 10));
        }
      }
      try {
        const {
          projectId,
          projectName,
          clientName,
          managerId,
          locationId,
          description,
          customGroupId,
        } = vm.projectDetails;
        vm.$apollo
          .mutate({
            mutation: ADD_EDIT_PROJECTS,
            variables: {
              projectId: projectId ? projectId : 0,
              projectName: projectName ? projectName : "",
              clientName: clientName ? clientName : "",
              managerId: managerId ? managerId : 0,
              locationId: locationId ? locationId : 0,
              status: "Closed",
              description: description ? description : "",
              employeeId: empIds,
              customGroupId: customGroupId ? parseInt(customGroupId) : 0,
              accreditationId: vm.accreditationIds,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoadingCard = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.projectLabel + " closed successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("project-closed");
          })
          .catch(() => {
            vm.handleProjectCloseError();
          });
      } catch {
        vm.handleProjectCloseError();
      }
    },

    handleProjectCloseError() {
      this.isLoadingCard = false;
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: `Something went wrong while closing the ${projectLabelSmallCase} status. Please try after some time.`,
      };
      this.showAlert(snackbarData);
    },

    backToViewForm() {
      this.showEmployeeList = false;
      this.showAccreditationList = false;
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
