<template>
  <div>
    <div v-if="mainTabList.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabList"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :class="originalList?.length ? '' : 'mr-8'"
                :isFilter="false"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-container fluid class="leaves-card">
            <ProfileCard>
              <FormTab :model-value="openedSubTab" :hide-slider="true">
                <v-tab
                  v-for="tab in subTabItems"
                  :key="tab.value"
                  :value="tab.value"
                  :disabled="tab.disable"
                  @click="onChangeSubTabs(tab.value)"
                >
                  <div
                    :class="[
                      isActiveSubTab(tab.value)
                        ? 'text-primary font-weight-bold'
                        : 'text-grey-darken-2 font-weight-bold',
                    ]"
                  >
                    {{ tab.label }}
                    <div
                      v-if="isActiveSubTab(tab.value)"
                      class="mt-3 mb-n4"
                      style="border-bottom: 4px solid"
                      :style="
                        openedSubTab === 'carryForwardEncashment'
                          ? 'width: 200px'
                          : 'width: 150px'
                      "
                    ></div>
                  </div>
                </v-tab>
              </FormTab>
            </ProfileCard>
          </v-container>
          <v-container fluid>
            <v-window v-model="openedSubTab">
              <v-window-item value="carryForwardEncashment">
                <CarryForwardList
                  v-if="openedSubTab === 'carryForwardEncashment'"
                  :form-access="formAccess"
                  :form-id="formId"
                  :landed-form-name="landedFormName"
                  :filtered-list="itemList"
                  @send-list-data="updateList($event)"
                />
              </v-window-item>
              <v-window-item value="encashmentOverride">
                <LeaveEncashmentList
                  v-if="openedSubTab === 'encashmentOverride'"
                  :form-access="formAccess"
                  :form-id="formId"
                  :landed-form-name="landedFormName"
                  :filtered-list="itemList"
                  @send-list-data="updateList($event)"
                />
              </v-window-item>
            </v-window>
          </v-container>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
</template>
<script>
const { defineAsyncComponent } = require("vue");
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const CarryForwardList = defineAsyncComponent(() =>
  import("./carry-forward-encashment/CarryForwardList.vue")
);
const LeaveEncashmentList = defineAsyncComponent(() =>
  import("./leave-encashment/LeaveEncashmentList.vue")
);
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "LeaveRequestListSetup",
  components: {
    EmployeeDefaultFilterMenu,
    CarryForwardList,
    LeaveEncashmentList,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      originalList: [],
      itemList: [],
      showViewForm: false,
      showAddEditForm: false,
      openMoreMenu: false,
      isEdit: false,
      // Leaves
      openedSubTab: "carryForwardEncashment",
      isFilterApplied: false,
    };
  },
  computed: {
    landedFormName() {
      return this.accessRights("356")?.customFormName || "Leave Closure";
    },
    formId() {
      return 356;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(356);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    leaveOverrideCheckAccess() {
      return (
        (this.leaveOverrideAccess &&
          (this.leaveOverrideAccess.admin === "admin" ||
            this.leaveOverrideAccess.isManager)) ||
        this.leaveConfigAccess
      );
    },
    leaveOverrideAccess() {
      let formAccess = this.accessRights("277");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    leaveConfigAccess() {
      let formAccess = this.accessRights("263");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    leavePolicyFormAccess() {
      let access = this.accessRights("347");
      if (access && access.accessRights && access.accessRights["view"]) {
        return access.accessRights;
      } else {
        return false;
      }
    },
    currentTabItem() {
      let index = this.mainTabList.indexOf(this.landedFormName);
      return "tab-" + index;
    },
    mainTabList() {
      let tabs = [];
      if (this.leaveOverrideCheckAccess)
        tabs.push(this.accessRights("277")?.customFormName || "Leave Override");
      if (this.leavePolicyFormAccess)
        tabs.push(this.accessRights("347")?.customFormName || "Leave Policy");
      if (this.formAccess) tabs.push(this.landedFormName);
      return tabs;
    },
    subTabItems() {
      return [
        {
          label: "Carry Forward & Encashment",
          value: "carryForwardEncashment",
          disable: false,
        },
        {
          label: "Encashment Override",
          value: "encashmentOverride",
          disable: false,
        },
      ];
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {},
  methods: {
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    updateList(list) {
      this.originalList = list || [];
      this.itemList = list || [];
      this.resetFilter();
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
    },

    applyFilter(filter) {
      this.isFilterApplied = true;
      this.itemList = filter;
    },
    onTabChange(tabName) {
      if (tabName !== this.landedFormName) {
        this.isLoading = true;
        if (tabName === "Leave Override") {
          this.$router.push("/core-hr/time-off-management/leave-override");
        } else if (tabName === "Leave Policy") {
          this.$router.push("/core-hr/leave-policy");
        }
      }
    },
  },
};
</script>
<style scoped>
.container {
  padding: 3.7em 0em 0em 0em;
}
.leaves-card {
  padding: 0em 0em 0em 0em;
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
  .leaves-card {
    padding: 0em 0em 0em 0em;
  }
}
</style>
