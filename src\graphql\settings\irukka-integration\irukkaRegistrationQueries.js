import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const GET_INTEGRATION_STATUS = gql`
  query jobBoardIntegrationStatus($form_Id: Int!) {
    jobBoardIntegrationStatus(form_Id: $form_Id) {
      errorCode
      message
      getStatus {
        Integration_Id
        Integration_Type
        Integration_Status
        Indeed_Client_Id
        Indeed_Secret_Key
        Indeed_Source_Name
        Company_Id
        Hirer_ID
      }
    }
  }
`;

export const GET_COMPANY_AND_USER_DETAILS = gql`
  query getCompanySignUpDetails {
    getCompanySignUpDetails {
      errorCode
      message
      getPersonalDetails {
        Employee_Id
        DOB
        Emp_Email
        Mobile_Number
        Updated_By_Name
      }
      getCompanyDetails {
        Org_Name
        Org_Description
      }
    }
  }
`;

export const GET_LOCATION_DETAILS = gql`
  query getLocationDetails {
    getLocationDetails {
      errorCode
      message
      locationDetails {
        Location_Id
        Location_Name
        Pincode
        City_Id
        City_Name
        State_Id
        State_Name
        Country_Code
        Country_Name
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const ADD_UPDATE_IRUKKA_STATUS = gql`
  mutation addUpdateIrukkaStatus(
    $Integration_Id: Int!
    $Integration_Type: String!
    $Integration_Status: String!
  ) {
    addUpdateIrukkaStatus(
      Integration_Id: $Integration_Id
      Integration_Type: $Integration_Type
      Integration_Status: $Integration_Status
    ) {
      errorCode
      message
    }
  }
`;
