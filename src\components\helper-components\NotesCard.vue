<template>
  <div>
    <v-card
      class="d-flex align-start pa-2"
      :class="{ 'flex-column': windowWidth <= 600 }"
      :style="
        backgroundColor
          ? ''
          : 'background: linear-gradient(to left, #f7f793 0%, #fff9d1 100%)'
      "
      width="100%"
      :color="backgroundColor"
      elevation="0"
    >
      <div v-if="heading" class="d-flex align-center">
        <div class="mr-6">
          <v-progress-circular
            model-value="100"
            color="primary"
            :size="22"
          ></v-progress-circular>
        </div>
        <div class="text-h6 text-grey-darken-1 font-weight-bold">
          {{ heading }}
        </div>
      </div>
      <div class="d-flex align-start">
        <img
          v-if="imageName"
          :src="getFooterImage"
          style="width: 50px; height: auto"
          class="ml-n5 mr-5"
          alt="idea-bulb"
        />
      </div>
      <span class="text-caption mr-1 text-justify">
        {{ notes }}
      </span>
      <slot name="notesCardContent"></slot>
    </v-card>
  </div>
</template>

<script>
export default {
  name: "NotesCard",

  props: {
    imageName: {
      type: String,
      default: "common/idea-bulb",
    },
    notes: {
      type: String,
      default: "",
    },
    backgroundColor: {
      type: String,
      default: "",
    },
    heading: {
      type: String,
      default: "",
    },
  },

  computed: {
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getFooterImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.imageName + ".webp");
      else return require("@/assets/images/" + this.imageName + ".png");
    },
  },
};
</script>
