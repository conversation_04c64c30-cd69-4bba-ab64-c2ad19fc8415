<template>
  <div>
    <v-container fluid v-if="formAccess">
      <div v-if="listLoading" class="mt-3">
        <v-skeleton-loader
          ref="skeleton1"
          type="table-heading"
          class="mx-auto"
        ></v-skeleton-loader>
        <div v-for="i in 4" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <AppFetchErrorScreen
        v-else-if="isErrorInList"
        :content="errorContent"
        key="error-screen"
        icon-name="fas fa-redo-alt"
        image-name="common/human-error-image"
        button-text="Retry"
        @button-click="refetchList()"
      ></AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="originalList?.length === 0"
        key="no-data-screen"
        :main-title="emptyScenarioMsg"
        :isSmallImage="!isFilter"
        :image-name="!isFilter ? '' : 'common/no-records'"
      >
        <template v-if="!isFilter" #contentSlot>
          <div style="max-width: 80%">
            <v-row
              class="rounded-lg pa-5 mb-4"
              :style="!isFilter ? 'background: white' : ''"
            >
              <v-col cols="12">
                <NotesCard
                  notes="The Leave Balance module provides employees with a comprehensive overview of their leave entitlements and usage. It allows them to easily track and manage their leave details, including the total number of leave days allocated for each leave type in the current leave year, the number of leave days already utilized, and any leave days carried forward from the previous year, if applicable."
                  backgroundColor="transparent"
                  class="mb-4"
                />
                <NotesCard
                  notes="Employees can also monitor leave requests that are pending approval from their manager or admin and view their real-time available leave balance after accounting for both taken and pending leaves."
                  backgroundColor="transparent"
                  class="mb-4"
                />
              </v-col>
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <div
                  class="d-flex align-center flex-wrap"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <CustomSelect
                    v-if="callingFromMyTeam"
                    v-model="selectedEmployeeId"
                    :items="allEmployeesList"
                    :itemSelected="selectedEmployeeId"
                    :isAutoComplete="true"
                    clearable
                    variant="solo"
                    class="mt-3"
                    :loading="isFetchingEmployees"
                    label="Employee"
                    density="compact"
                    min-width="150px"
                    max-width="350px"
                    item-title="employeeData"
                    item-value="employeeId"
                    @selected-item="changeField($event)"
                  />
                  <v-btn
                    color="transparent"
                    variant="flat"
                    class="ml-2 mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="itemList?.length == 0"
        key="no-results-screen"
        main-title="There are no leaves matched for the selected filters/searches."
        image-name="common/no-records"
      >
        <template #contentSlot>
          <div style="max-width: 80%">
            <v-row class="rounded-lg pa-5 mb-4">
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <v-btn
                  variant="elevated"
                  color="primary"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  @click="resetFilter('grid')"
                >
                  <span class="primary">Reset Filter/Search </span>
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <div v-else>
        <div
          v-if="originalList?.length > 0"
          class="d-flex flex-wrap align-center"
          :class="isMobileView ? 'flex-column' : ''"
          style="justify-content: space-between"
        >
          <div
            class="d-flex align-center flex-wrap"
            :class="isMobileView ? 'justify-center' : ''"
          >
            <CustomSelect
              v-if="callingFromMyTeam"
              v-model="selectedEmployeeId"
              :items="allEmployeesList"
              :itemSelected="selectedEmployeeId"
              :isAutoComplete="true"
              clearable
              variant="solo"
              class="mt-3"
              :loading="isFetchingEmployees"
              label="Employee"
              density="compact"
              min-width="150px"
              max-width="350px"
              item-title="employeeData"
              item-value="employeeId"
              @selected-item="changeField($event)"
            />
          </div>
          <div
            class="d-flex align-center"
            :class="isMobileView ? 'justify-center' : 'justify-end'"
          >
            <v-btn
              rounded="lg"
              color="transparent"
              variant="flat"
              class="mt-1"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu class="mb-1" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                  <v-icon>fas fa-ellipsis-v</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                        ><v-icon size="15" class="pr-2">{{
                          action.icon
                        }}</v-icon
                        >{{ action.key }}</v-list-item-title
                      >
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
        <v-row>
          <v-col cols="12">
            <v-data-table
              :headers="tableHeaders"
              :items="itemList"
              :items-per-page="50"
              fixed-header
              :height="
                itemList?.length > 11 ? $store.getters.getTableHeight(270) : ''
              "
              item-value="Leave_Id"
              class="elevation-1"
              style="box-shadow: none !important"
            >
              <template #[`header.totalEligibility`]="{}">
                <v-tooltip
                  text="Total Eligibility = Current Year Leave Entitlement + Carry Over Days"
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <v-hover>
                      <template
                        v-slot:default="{ isHovering, props: hoverProps }"
                      >
                        <div class="d-flex align-center" v-bind="props">
                          <span v-bind="{ ...props, ...hoverProps }"
                            >Total Eligibility</span
                          >
                          <v-icon
                            :color="isHovering ? 'grey' : 'white'"
                            class="fas fa-arrow-up ml-2"
                            size="12"
                          />
                        </div>
                      </template>
                    </v-hover>
                  </template>
                </v-tooltip>
              </template>
              <template #[`header.currentYearTotalEligibleDays`]="{}">
                <v-tooltip
                  text="Total number of leave days allocated to an employee for the current year based on company policy."
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <v-hover>
                      <template
                        v-slot:default="{ isHovering, props: hoverProps }"
                      >
                        <div class="d-flex align-center" v-bind="props">
                          <span v-bind="{ ...props, ...hoverProps }"
                            >Current Year Leave Entitlement</span
                          >
                          <v-icon
                            :color="isHovering ? 'grey' : 'white'"
                            class="fas fa-arrow-up ml-2"
                            size="12"
                          />
                        </div>
                      </template>
                    </v-hover>
                  </template>
                </v-tooltip>
              </template>
              <template #[`header.totalCODays`]="{}">
                <v-tooltip
                  text="The unused leave days from the last leave year that have been transferred to the current year."
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <v-hover>
                      <template
                        v-slot:default="{ isHovering, props: hoverProps }"
                      >
                        <div class="d-flex align-center" v-bind="props">
                          <span v-bind="{ ...props, ...hoverProps }"
                            >Carry Over Days</span
                          >
                          <v-icon
                            :color="isHovering ? 'grey' : 'white'"
                            class="fas fa-arrow-up ml-2"
                            size="12"
                          />
                        </div>
                      </template>
                    </v-hover>
                  </template>
                </v-tooltip>
              </template>
              <template #[`header.lastCOBalance`]="{}">
                <v-tooltip
                  text="The number of leave days taken by the employee from the carry over days."
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <v-hover>
                      <template
                        v-slot:default="{ isHovering, props: hoverProps }"
                      >
                        <div class="d-flex align-center" v-bind="props">
                          <span v-bind="{ ...props, ...hoverProps }"
                            >Carry Over Balance</span
                          >
                          <v-icon
                            :color="isHovering ? 'grey' : 'white'"
                            class="fas fa-arrow-up ml-2"
                            size="12"
                          />
                        </div>
                      </template>
                    </v-hover>
                  </template>
                </v-tooltip>
              </template>
              <template #[`header.leavesTaken`]="{}">
                <v-tooltip
                  text="The total number of leave days an employee has utilized."
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <v-hover>
                      <template
                        v-slot:default="{ isHovering, props: hoverProps }"
                      >
                        <div class="d-flex align-center" v-bind="props">
                          <span v-bind="{ ...props, ...hoverProps }"
                            >Leaves Taken</span
                          >
                          <v-icon
                            :color="isHovering ? 'grey' : 'white'"
                            class="fas fa-arrow-up ml-2"
                            size="12"
                          />
                        </div>
                      </template>
                    </v-hover>
                  </template>
                </v-tooltip>
              </template>
              <template #[`header.totalAppliedLeaveDays`]="{}">
                <v-tooltip
                  text="Leave requests that are awaiting review and action from the designated approver."
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <v-hover>
                      <template
                        v-slot:default="{ isHovering, props: hoverProps }"
                      >
                        <div class="d-flex align-center" v-bind="props">
                          <span v-bind="{ ...props, ...hoverProps }"
                            >Pending Approval</span
                          >
                          <v-icon
                            :color="isHovering ? 'grey' : 'white'"
                            class="fas fa-arrow-up ml-2"
                            size="12"
                          />
                        </div>
                      </template>
                    </v-hover>
                  </template>
                </v-tooltip>
              </template>
              <template #[`header.calculatedLeaveBalance`]="{}">
                <v-tooltip
                  text="Leave Balance = Total Eligibility - Leaves Taken"
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <v-hover>
                      <template
                        v-slot:default="{ isHovering, props: hoverProps }"
                      >
                        <div class="d-flex align-center" v-bind="props">
                          <span v-bind="{ ...props, ...hoverProps }"
                            >Leave Balance</span
                          >
                          <v-icon
                            :color="isHovering ? 'grey' : 'white'"
                            class="fas fa-arrow-up ml-2"
                            size="12"
                          />
                        </div>
                      </template>
                    </v-hover>
                  </template>
                </v-tooltip>
              </template>
              <template v-slot:item="{ item }">
                <tr
                  style="z-index: 200"
                  class="data-table-tr bg-white cursor-pointer"
                  :class="[
                    isMobileView
                      ? ' v-data-table__mobile-table-row ma-0 mt-2'
                      : '',
                  ]"
                >
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-small'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Leave Type
                    </div>
                    <section class="d-flex align-center">
                      <v-tooltip
                        :text="item.leaveType"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                            v-bind="props"
                          >
                            {{ checkNullValue(item.leaveType) }}
                          </div>
                        </template>
                      </v-tooltip>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-medium'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Total Eligibility
                    </div>
                    <section
                      class="text-body-2 text-truncate d-flex align-center d-flex align-center"
                      style="max-width: 250px"
                    >
                      <span class="text-body-2 font-weight-regular">
                        <div
                          class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                        >
                          {{ checkNullValue(item.totalEligibility) }}
                        </div>
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-medium'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Current Year Leave Entitlement
                    </div>
                    <section
                      class="text-body-2 text-truncate d-flex align-center d-flex align-center"
                      style="max-width: 250px"
                    >
                      <span class="text-body-2 font-weight-regular">
                        <div
                          class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                        >
                          {{
                            checkNullValue(item.currentYearTotalEligibleDays)
                          }}
                        </div>
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Carry Over Days
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.totalCODays) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Carry Over Balance
                    </div>
                    <section
                      class="text-body-2 text-truncate"
                      style="max-width: 150px"
                    >
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.lastCOBalance) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? ' d-flex justify-space-between align-center'
                        : ' pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Leaves Taken
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.leavesTaken) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? 'font-weight-bold d-flex align-center'
                          : 'font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Pending Approval
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.totalAppliedLeaveDays) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? 'font-weight-bold d-flex align-center'
                          : 'font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Leave Balance
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.calculatedLeaveBalance) }}
                      </span>
                    </section>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </div>
    </v-container>
    <AppAccessDenied v-else />
    <AppLoading v-if="listLoading" />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
// Async Components
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import { GET_LEAVE_OVERRIDE_LIST } from "@/graphql/corehr/employeeDataQueries";

export default {
  name: "LeaveBalance",
  components: {
    NotesCard,
    CustomSelect,
  },
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    formId: {
      type: Number,
      required: true,
    },
    filteredList: {
      type: Array,
      default: () => [],
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  mixins: [FileExportMixin],
  emits: ["send-list-data"],
  data: () => ({
    originalList: [],
    itemList: [],
    isFetchingEmployees: false,
    selectedEmployeeId: null,
    allEmployeesList: [],
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
  }),
  computed: {
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    callingFromMyTeam() {
      return this.formId == 332;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    tableHeaders() {
      return [
        { title: "Leave Type", key: "leaveType" },
        { title: "Total Eligibility", key: "totalEligibility" },
        {
          title: "Current Year Leave Entitlement",
          key: "currentYearTotalEligibleDays",
        },
        { title: "Carry Over Days", key: "totalCODays" },
        { title: "Carry Over Balance", key: "lastCOBalance" },
        { title: "Leaves Taken", key: "leavesTaken" },
        { title: "Pending Approval", key: "totalAppliedLeaveDays" },
        { title: "Leave Balance", key: "calculatedLeaveBalance" },
      ];
    },

    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList?.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  watch: {
    filteredList: {
      handler(val) {
        if (val && val.length) this.itemList = val;
        else this.itemList = [];
      },
      deep: true,
    },
  },

  mounted() {
    if (this.callingFromMyTeam) {
      this.selectedEmployeeId = this.loginEmployeeId;
      this.getEmpList();
    }
    this.fetchList();
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    async getEmpList() {
      this.isFetchingEmployees = true;
      await this.$store
        .dispatch("getEmployeesList", {
          formName: "Leave Request",
          formId: parseInt(this.formId),
        })
        .then((empData) => {
          this.allEmployeesList =
            empData?.map((item) => ({
              ...item,
              employeeData: item.employeeName + " - " + item.userDefinedEmpId,
            })) || [];
          this.allEmployeesList =
            this.allEmployeesList?.filter(
              (el) => el.empStatus?.toLowerCase() === "active"
            ) || [];
          this.isFetchingEmployees = false;
        })
        .catch((err) => {
          let snackbarData = {
            isOpen: true,
            message: "",
            type: "warning",
          };
          if (err === "error") {
            snackbarData.message =
              "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
          } else {
            snackbarData.message = err;
          }
          this.selectedEmployeeId = null;
          this.showAlert(snackbarData);
          this.isFetchingEmployees = false;
        });
    },
    changeField(empId) {
      if (empId) {
        this.selectedEmployeeId = empId;
        this.fetchList();
      }
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_LEAVE_OVERRIDE_LIST,
          client: "apolloClientI",
          variables: {
            employeeId: vm.callingFromMyTeam
              ? parseInt(vm.selectedEmployeeId)
              : parseInt(vm.loginEmployeeId),
            Org_Code: vm.orgCode,
            formId: vm.formId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listLeaveOverrideEmployeeDetails &&
            response.data.listLeaveOverrideEmployeeDetails
              .employeeEligibleLeaveDetails &&
            !response.data.listLeaveOverrideEmployeeDetails.errorCode
          ) {
            let tempData =
              response.data.listLeaveOverrideEmployeeDetails
                .employeeEligibleLeaveDetails;
            tempData =
              tempData?.map((item) => {
                return {
                  ...item,
                  totalEligibility: parseFloat(
                    item.currentYearTotalEligibleDays + item.totalCODays
                  ),
                };
              }) || [];
            vm.itemList = tempData;
            vm.originalList = tempData;
            vm.$emit("send-list-data", { list: tempData });
            vm.listLoading = false;
          } else {
            vm.handleLeaveBalanceError(
              response.data.listLeaveOverrideEmployeeDetails?.errorCode || ""
            );
          }
        })
        .catch((err) => {
          vm.handleLeaveBalanceError(err);
        });
    },
    handleLeaveBalanceError(err = "") {
      this.listLoading = false;
      this.itemList = [];
      this.originalList = [];
      this.$emit("send-list-data", { list: [] });
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.fetchList();

      this.resetFilter();
    },
    resetFilter() {
      this.$emit("send-list-data", { list: this.originalList });
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      let headers = [
        { header: "Leave Type", key: "leaveType" },
        { header: "Total Eligibility", key: "totalEligibility" },
        {
          header: "Current Year Leave Entitlement",
          key: "currentYearTotalEligibleDays",
        },
        { header: "Carry Over Days", key: "totalCODays" },
        { header: "Carry Over Balance", key: "lastCOBalance" },
        { header: "Leaves Taken", key: "leavesTaken" },
        { header: "Pending Approval", key: "totalAppliedLeaveDays" },
        { header: "Leave Balance", key: "calculatedLeaveBalance" },
      ];
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: headers,
      };
      this.exportExcelFile(exportOptions);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
