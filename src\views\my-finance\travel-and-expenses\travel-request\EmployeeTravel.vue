<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        :show-bottom-sheet="!listLoading && !isAddViewEditFormOpened"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row v-show="!isAddViewEditFormOpened" justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu class="justify-end" :isFilter="false">
              </EmployeeDefaultFilterMenu>
              <FormFilter
                ref="formFilterRef"
                :items="backupMainData"
                :callingFrom="callingFrom"
                @reset-filter="resetSearchFilter()"
                @apply-filter="applyFilter($event)"
              ></FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="travel-container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            button-text="Retry"
            @button-click="refetchData()"
          >
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="
              backupMainData && backupMainData.length === 0 && !showEditForm
            "
            key="no-results-screen"
          >
            <template #contentSlot>
              <div style="max-width: 80%" class="mx-auto">
                <v-row
                  v-if="!isLoading"
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                  :class="isMobileView ? 'mt-n16' : ''"
                >
                  <v-col v-if="callingFrom === 'team'" cols="12">
                    <NotesCard
                      :notes="`The ${travelCustomizedFormName} module enables employees to submit requests for official business travel. This feature streamlines the approval process, ensuring compliance with company travel policies and budget constraints.It helps organizations manage employee travel efficiently, reduce manual processes, and maintain financial control over business trips.`"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      :notes="`Employees can enter trip details, including destination, purpose, travel dates, and estimated expenses. Requests are routed to designated approvers (managers, HR, or finance) for review and approval. Employees can input expected expenses such as airfare, accommodation, meals, and transportation.`"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col v-else cols="12">
                    <NotesCard
                      :notes="`The ${travelCustomizedFormName} module allows employees to submit and manage their business travel requests seamlessly. This feature ensures a streamlined approval process while maintaining compliance with company policies.`"
                      backgroundColor="transparent"
                      class="mb-2"
                    ></NotesCard>
                    <NotesCard
                      :notes="`Employees can submit travel requests, specifying trip details such as destination, purpose, travel dates, and estimated expenses. Requests are automatically routed to the designated approvers (managers, HR, or finance) based on predefined workflows. Employees receive automated alerts on request status updates, approvals, or rejections. Employees can view past travel requests and approvals for reference.`"
                      backgroundColor="transparent"
                      class="mb-2"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      :size="this.isMobileView ? 'small' : 'default'"
                      @click="onAdd()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Add
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="mt-1"
                      :size="this.isMobileView ? 'small' : 'default'"
                      @click="refetchData()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else class="pt-4">
            <v-row>
              <v-col v-if="!isAddViewEditFormOpened">
                <ListTravelRequests
                  :items="mainData"
                  :callingFrom="callingFrom"
                  :formAccess="formAccess"
                  :form-id="formId"
                  :travelCustomizedFormName="travelCustomizedFormName"
                  @open-view-form="onOpenViewForm($event)"
                  @refetch-data="refetchData()"
                  @open-add-form="onAdd()"
                  @reset-search-filter="resetSearchFilter()"
                  @open-edit-form="openEditForm($event)"
                ></ListTravelRequests>
              </v-col>
              <v-col v-if="isAddViewEditFormOpened" cols="12" class="pa-5">
                <ViewTravelRequest
                  v-if="showViewForm"
                  :travelRequestSelected="selectedItem"
                  :callingFrom="callingFrom"
                  :travelCustomizedFormName="travelCustomizedFormName"
                  :form-id="formId"
                  @open-edit-form="openEditForm()"
                  @close-form="closeAllForms"
                  :formAccess="formAccess"
                ></ViewTravelRequest>
                <AddEditTravelRequest
                  v-else
                  :travelRequest="selectedItem"
                  :isEdit="isEdit"
                  :callingFrom="callingFrom"
                  :travelCustomizedFormName="travelCustomizedFormName"
                  :form-id="formId"
                  @close-form="closeAllForms"
                  @refetch-data="refetchData()"
                >
                </AddEditTravelRequest>
              </v-col>
            </v-row>
          </div>

          <AppSnackBar
            v-if="showValidationAlert"
            :show-snack-bar="showValidationAlert"
            snack-bar-type="warning"
            timeOut="-1"
            @close-snack-bar="closeValidationAlert"
          >
            <template #custom-alert>
              <div
                v-for="(validationMsg, index) of validationMessages"
                :key="validationMsg + index"
                class="text-subtitle-1"
              >
                {{ validationMsg }}
              </div>
              <div class="d-flex justify-end">
                <v-btn
                  class="mt-n5 primary"
                  variant="text"
                  @click="closeValidationAlert()"
                >
                  Close
                </v-btn>
              </div>
            </template>
          </AppSnackBar>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const AddEditTravelRequest = defineAsyncComponent(() =>
  import("./AddEditTravelRequest.vue")
);
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import FormFilter from "./FormFilter.vue";
import ViewTravelRequest from "./ViewTravelRequest.vue";
import ListTravelRequests from "./ListTravelRequests.vue";
import { LIST_TRAVEL_REQUESTS } from "@/graphql/employee-self-service/travelRequestQueries.js";

export default {
  name: "EmployeeTravel",
  components: {
    EmployeeDefaultFilterMenu,
    FormFilter,
    ViewTravelRequest,
    NotesCard,
    ListTravelRequests,
    AddEditTravelRequest,
  },
  props: {
    callingFrom: {
      type: String,
      default: "employee",
    },
  },
  data() {
    return {
      isLoading: false,
      currentTabItem: "tab-0",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      isEdit: false,
      showViewForm: false,
      selectedItem: {},
      showEditForm: false,
      validationMessages: [],
      showValidationAlert: false,
      mainData: [],
      backupMainData: [],
    };
  },
  computed: {
    approvalFormAccess() {
      let formAccess = this.accessRights(184);
      if (formAccess && formAccess.accessRights["view"]) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    mainTabs() {
      let tabs = [];
      // If formAccess exists, only display the travelCustomizedFormName
      if (this.formAccess) {
        tabs.push(this.travelCustomizedFormName);
      } else {
        this.checkAccessAndRedirect();
      }
      if (this.customFormAccess(this.claimFormId)?.view) {
        tabs.push(this.claimCustomizedFormName);
      }
      // Add Approvals tab if applicable
      if (
        this.approvalFormAccess &&
        this.callingFrom === "team" &&
        this.formAccess
      ) {
        tabs.push("Approvals");
      }

      return tabs;
    },
    formId() {
      let fId = this.callingFrom === "team" ? "341" : "342";
      return parseInt(fId);
    },
    claimFormId() {
      let fId = this.callingFrom === "team" ? 338 : 339;
      return fId;
    },
    formAccess() {
      let travelRequestFormAccess = this.accessRights(this.formId);
      if (
        travelRequestFormAccess &&
        travelRequestFormAccess.accessRights &&
        travelRequestFormAccess.accessRights["view"]
      ) {
        let formAccess = travelRequestFormAccess.accessRights;
        //Include Cancel applied action
        formAccess["cancel"] = formAccess["update"] || false;
        return formAccess;
      } else {
        return false;
      }
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    customFormAccess() {
      return (formId) => {
        let travelRequestFormAccess = this.accessRights(formId);
        if (
          travelRequestFormAccess &&
          travelRequestFormAccess.accessRights &&
          travelRequestFormAccess.accessRights["view"]
        ) {
          return travelRequestFormAccess.accessRights;
        } else {
          return false;
        }
      };
    },
    travelCustomizedFormName() {
      if (this.formAccess && this.formAccess.customFormName) {
        return this.formAccess.customFormName;
      }
      return "Travel Request";
    },
    claimCustomizedFormName() {
      let formAccess = this.customFormAccess(338);
      if (formAccess && formAccess.customFormName) {
        return formAccess.customFormName;
      }
      return "Claim Request";
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isAddViewEditFormOpened() {
      return this.showViewForm || this.showEditForm;
    },
    openFormInModal() {
      if (this.isAddViewEditFormOpened && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
  },

  mounted() {
    this.fetchData();
  },
  methods: {
    checkAccessAndRedirect() {
      // If no travel request access but has claim request access, redirect to claim request
      if (!this.formAccess && this.customFormAccess(this.claimFormId)?.view) {
        if (this.callingFrom === "team") {
          this.$router.push("/my-team/travel-and-expenses/claim-request");
        } else {
          this.$router.push("/my-finance/travel-and-expenses/claim-request");
        }
      }
    },
    onTabChange(tabName) {
      if (tabName == this.claimCustomizedFormName) {
        if (this.callingFrom == "team") {
          this.$router.push("/my-team/travel-and-expenses/claim-request");
        } else {
          this.$router.push("/my-finance/travel-and-expenses/claim-request");
        }
      }
      if (tabName === "Approvals") {
        this.$router.push(
          `/approvals/approval-management?form_id=${this.formId}`
        );
      }
    },
    resetSearchFilter() {
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
      this.mainData = this.backupMainData;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    applyFilter(filterParams) {
      this.mainData = filterParams;
    },
    openEditForm(item = null) {
      if (item) {
        this.selectedItem = item;
      }
      this.isEdit = true;
      this.showViewForm = false;
      this.showEditForm = true;
    },
    closeAllForms() {
      this.isEdit = false;
      this.showEditForm = false;
      this.showViewForm = false;
    },
    onAdd() {
      this.selectedItem = null;
      this.showEditForm = true;
      this.isEdit = false;
    },
    refetchData() {
      this.errorContent = "";
      this.isErrorInList = false;
      this.resetSearchFilter();
      this.closeAllForms();
      this.fetchData();
    },
    onOpenViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    fetchData() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_TRAVEL_REQUESTS,
          client: "apolloClientAC",
          variables: {
            formId: this.formId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listEmployeeTravel) {
            let mainData =
              response.data.listEmployeeTravel.employeeTravelDetails;
            vm.mainData = mainData;
            vm.backupMainData = mainData;
            vm.listLoading = false;
          } else {
            vm.handleListError(
              (err = ""),
              this.travelCustomizedFormName?.toLowerCase()
            );
          }
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleListError(err, this.travelCustomizedFormName?.toLowerCase());
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
<style scoped>
.travel-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .travel-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
