<template>
  <v-overlay
    :model-value="overlayModel"
    @click:outside="onCloseView()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? windowWidth <= 800
              ? 'width:100vw; height: 100vh'
              : 'width:60vw; height: 100vh'
            : 'width:40vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div
            class="text-h6 text-medium ps-2 overflow-hidden"
            style="max-width: 90%"
          >
            {{
              isEdit
                ? $t("coreHr.editAccreditationCategoryAndType")
                : $t("coreHr.addAccreditationCategoryAndType")
            }}
          </div>
          <div class="d-flex align-center">
            <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </div>
        </v-card-title>
        <v-card-text class="overflow-y-auto" style="max-height: 85vh">
          <v-container>
            <v-form ref="accreditationForm">
              <v-row class="mt-5">
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="accreditationCategory"
                    variant="solo"
                    :rules="[
                      required('Accreditation Category', accreditationCategory),
                      multilingualNameNumericValidation(
                        'Accreditation Category',
                        accreditationCategory
                      ),
                      minLengthValidation(
                        'Accreditation Category',
                        accreditationCategory,
                        3
                      ),
                      maxLengthValidation(
                        'Accreditation Category',
                        accreditationCategory,
                        50
                      ),
                    ]"
                    @input="isFormDirty = true"
                  >
                    <template v-slot:label>
                      {{ $t("coreHr.accreditationCategory") }}
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="accreditationType"
                    variant="solo"
                    :rules="[
                      required('Accreditation Type', accreditationType),
                      multilingualNameNumericValidation(
                        'Accreditation Type',
                        accreditationType
                      ),
                      minLengthValidation(
                        'Accreditation Type',
                        accreditationType,
                        3
                      ),
                      maxLengthValidation(
                        'Accreditation Type',
                        accreditationType,
                        50
                      ),
                    ]"
                    @input="isFormDirty = true"
                  >
                    <template v-slot:label>
                      {{ $t("coreHr.accreditationType") }}
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <p class="v-label">
                    {{ $t("coreHr.mandatorySelfOnboarding") }}
                  </p>
                  <v-switch
                    class="ml-3"
                    density="compact"
                    color="primary"
                    v-model="mandatory"
                    :true-value="1"
                    :false-value="0"
                    @change="isFormDirty = true"
                  ></v-switch>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <p class="v-label">
                    {{ $t("coreHr.enforceDependent") }}
                  </p>
                  <v-switch
                    class="ml-3"
                    density="compact"
                    color="primary"
                    v-model="enforceDependent"
                    :true-value="1"
                    :false-value="0"
                    @change="isFormDirty = true"
                  ></v-switch>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-file-input
                    :label="$t('coreHr.document')"
                    prepend-icon
                    :model-value="fileContent"
                    :hint="$t('coreHr.fileHint')"
                    :persistent-hint="true"
                    append-inner-icon="fas fa-paperclip"
                    :rules="[checkSize]"
                    variant="solo"
                    accept="image/png, image/jpeg, image/jpg, application/pdf"
                    @update:model-value="onChangeFiles"
                    @click:clear="fileContent = null"
                  >
                  </v-file-input>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <CustomSelect
                    :label="$t('coreHr.accreditationEnforcementGroup')"
                    :item-selected="enforcedGroups"
                    :items="enforcedGroupsList"
                    itemTitle="Group_Name"
                    itemValue="Group_Id"
                    :rules="[
                      mandatory
                        ? required('Enforcement Group', enforcedGroups?.length)
                        : true,
                    ]"
                    :is-auto-complete="true"
                    :is-required="mandatory ? true : false"
                    :select-properties="{
                      multiple: true,
                      chips: true,
                      clearable: true,
                      closableChips: true,
                    }"
                    :loading="enforcedGroupsListLoading"
                    @selected-item="
                      (enforcedGroups = $event), (isFormDirty = true)
                    "
                  ></CustomSelect>
                  <v-btn
                    class="mt-n4 ml-n2"
                    color="primary"
                    variant="text"
                    size="small"
                    @click="showEnforcementGroupForm = true"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon
                    >{{ $t("coreHr.addAccreditationEnforcementGroup") }}</v-btn
                  >
                </v-col>
                <v-col cols="12" class="px-md-6 pb-0 mb-2">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ $t("coreHr.instruction") }}
                  </p>
                  <div>
                    <div
                      ref="editor"
                      class="quill-editor"
                      @keydown="isFormDirty = true"
                    ></div>
                    <p class="text-red">{{ this.instructionError }}</p>
                  </div>
                  <ul class="mt-5 text-grey-darken-2">
                    <li>
                      {{
                        $t("coreHr.instructionHint1", { here: "{\{ here }\}" })
                      }}
                    </li>
                    <li>
                      {{
                        $t("coreHr.instructionHint2", { here: "{\{ here }\}" })
                      }}
                    </li>
                  </ul>
                </v-col>
              </v-row>
            </v-form>
            <v-row class="d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  class="mt-16"
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                >
                </MoreDetails>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-sheet
          class="align-center text-center pa-2 position-absolute bottom-0"
          elevation="4"
          style="width: 100%"
        >
          <v-row justify="center">
            <v-col cols="12" class="d-flex justify-end pr-6">
              <v-btn
                rounded="lg"
                class="mr-6 primary"
                @click="onCloseView()"
                variant="outlined"
              >
                {{ $t("coreHr.cancel") }}
              </v-btn>
              <v-btn
                rounded="lg"
                class="mr-1 primary"
                @click="validateAccreditationForm()"
                variant="elevated"
                :disabled="!isFormDirty"
              >
                {{ $t("coreHr.save") }}
              </v-btn>
            </v-col>
          </v-row>
        </v-sheet>
      </v-card>
      <AppLoading v-if="isLoading"></AppLoading>
    </template>
  </v-overlay>
  <v-dialog
    :model-value="showEnforcementGroupForm"
    max-width="30%"
    @click:outside="(showEnforcementGroupForm = false), (moreTags = [])"
  >
    <v-card :min-height="windowWidth > 700 ? 350 : ''" class="pa-5">
      <div class="text-h6 mb-10 text-center">
        {{ $t("coreHr.addAccreditationEnforcementGroup") }}
      </div>
      <div class="mb-auto d-flex justify-center" style="width: 100%">
        <v-form ref="enforcementGroupForm" style="width: 70%">
          <v-text-field
            ref="skillSet"
            v-model="input"
            :label="
              moreTags.length
                ? ''
                : $t('coreHr.newAccreditationEnforcementGroup')
            "
            @update:modelValue="showAddIcon"
            variant="solo"
            :rules="[
              multilingualNameNumericValidation(
                'group',
                moreTags[moreTags.length - 1]
              ),
              required('New group', moreTags[0]),
            ]"
            @keydown.enter.prevent="addChip"
          >
            <template v-slot:default>
              <v-icon v-if="showIcon" @click="addChip" size="x-small"
                >fas fa-plus</v-icon
              >
              <v-chip
                v-for="(chip, index) in moreTags"
                append-icon="fas fa-times-circle"
                :key="index"
                class="ma-1"
                @click="removeChip(index)"
                >{{ chip }}</v-chip
              >
            </template>
          </v-text-field>
        </v-form>
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="primary mr-2"
          variant="outlined"
          @click="(showEnforcementGroupForm = false), (moreTags = [])"
        >
          <span class="primary">{{ $t("coreHr.cancel") }}</span>
        </v-btn>
        <v-btn
          type="submit"
          variant="elevated"
          class="primary"
          @click="addMoreTags()"
          ><span class="primary">{{ $t("coreHr.submit") }}</span></v-btn
        >
      </div>
    </v-card>
    <AppLoading v-if="addTagLoader"></AppLoading>
  </v-dialog>
  <AppWarningModal
    v-if="showWarningModal"
    :open-modal="showWarningModal"
    :confirmation-heading="$t('coreHr.exitFormWarning')"
    imgUrl="common/exit_form"
    @close-warning-modal="showWarningModal = false"
    @accept-modal="$emit('close-view')"
  >
  </AppWarningModal>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";

import moment from "moment";

import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";

import {
  ADD_UPDATE_ACCREDITATION_CATEGORY_TYPE,
  ADD_ACCREDITATION_ENFORCEMENT_GROUP,
} from "@/graphql/corehr/accreditationCategoryAndTypeQueries";
import { LIST_ACCREDITATION_ENFORCEMENT_GROUPS } from "@/graphql/corehr/accreditationCategoryAndTypeQueries.js";

export default {
  name: "AddEditAccreditationType",
  props: {
    overlayModel: {
      type: Boolean,
      default: false,
    },
    selectedItem: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [validationRules],
  components: { MoreDetails, CustomSelect },
  emits: ["close-view", "on-form-edit"],
  data() {
    return {
      accreditationCategory: null,
      accreditationType: null,
      mandatory: 0,
      enforceDependent: 0,
      enforcedGroups: [],
      enforcedGroupsList: [],
      enforcedGroupsListLoading: false,
      fileContent: null,
      openMoreDetails: false,
      moreDetailsList: [],
      isFileChanged: false,
      instructionError: "",
      isFormDirty: false,
      isLoading: false,
      showEnforcementGroupForm: false,
      input: "",
      showIcon: false,
      moreTags: [],
      addTagLoader: false,
      showWarningModal: false,
      quill: null,
    };
  },
  computed: {
    currentTimeStamp() {
      return moment().unix();
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    checkSize() {
      if (this.fileContent) {
        const fileSize = this.fileContent.size;
        if (fileSize > 3000000) {
          return this.$t("coreHr.fileSizeError");
        }
        return true;
      }
      return true;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat + " HH:mm:ss") : "";
        } else return "";
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initQuillEditor();
    });
    this.fetchEnforcementGroups();
    if (this.isEdit) {
      this.prefillMoreDetails();
      this.accreditationCategory = this.selectedItem.Accreditation_Category;
      this.accreditationType = this.selectedItem.Accreditation_Type;
      this.mandatory =
        this.selectedItem.Mandatory?.toLowerCase() === "yes" ? 1 : 0;
      this.enforceDependent = this.selectedItem.Enforce_Dependent;
      this.enforcedGroups = this.selectedItem.Group_Ids?.split(",").map(Number);
      this.fileContent = {
        formattedFileName: this.selectedItem.File_Name,
        name: this.selectedItem.File_Name?.split("?")[3],
      };
    }
  },
  methods: {
    onCloseView() {
      this.showWarningModal = true;
    },
    onChangeFiles(val) {
      this.isFormDirty = true;
      this.fileContent = val;
      if (val) {
        this.fileContent.formattedFileName =
          this.currentTimeStamp + "?accreditation?1?" + val.name;
        this.isFileChanged = true;
      }
    },
    showAddIcon() {
      this.showIcon = !!this.input.trim();
    },
    addChip() {
      if (this.isInputValid && this.input.trim()) {
        this.moreTags.push(this.input.trim());
        this.input = "";
        this.showIcon = false;
      }
    },
    isInputValid() {
      const rules = [
        this.alphaNumSpaceNewLineWithElevenSymbolValidation(this.input),
      ];
      return rules[0] === true ? true : false;
    },
    removeChip(index) {
      this.moreTags.splice(index, 1);
    },
    initQuillEditor() {
      try {
        this.quill = new Quill(this.$refs.editor, {
          theme: "snow",
          modules: {
            toolbar: [
              ["bold", "italic", "underline"],
              [{ list: "ordered" }, { list: "bullet" }],
              ["clean"],
            ],
          },
        });

        // Set initial font size
        this.setEditorFontSize("16px");

        // Set initial content if in edit mode
        if (this.isEdit && this.selectedItem.Instruction) {
          this.quill.root.innerHTML = this.selectedItem.Instruction;
        }

        this.hasContent = !!this.quill.getText().trim();

        // Listen for editor text change events
        this.quill.on("text-change", () => {
          this.hasContent = !!this.quill.getText().trim();
          this.isFormDirty = true;
        });
      } catch {
        this.instructionError = this.$t("coreHr.editorError");
      }
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editor.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    async validateAccreditationForm() {
      let { valid } = await this.$refs.accreditationForm.validate();
      if (this.quill.root.innerHTML != "<p><br></p>") {
        const pattern = /\{\{\s*here\s*\}\}/;

        // Test the input string against the pattern
        let validInstruction = pattern.test(this.quill.root.innerHTML);
        if (!validInstruction) {
          this.instructionError = this.$t("coreHr.instructionError");
        } else {
          this.instructionError = "";
        }
      }
      if (valid && this.instructionError === "") {
        this.addUpdateAccreditationType();
      }
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.selectedItem?.Added_On + ".000Z")
        ),
        addedByName = this.selectedItem?.Added_By_Name,
        updatedByName = this.selectedItem?.Updated_By_Name,
        updatedOn = this.formatDate(
          new Date(this.selectedItem?.Updated_On + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: this.$t("coreHr.added"),
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: this.$t("coreHr.updated"),
        });
      }
    },
    addUpdateAccreditationType() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_ACCREDITATION_CATEGORY_TYPE,
          variables: {
            Accreditation_Category_And_Type_Id: this.isEdit
              ? this.selectedItem.Accreditation_Category_And_Type_Id
              : null,
            Accreditation_Category: this.accreditationCategory,
            Accreditation_Type: this.accreditationType,
            Mandatory: this.mandatory,
            Enforce_Dependent: this.enforceDependent,
            Group_Ids: this.enforcedGroups,
            Instruction: this.quill.root.innerHTML,
            File_Name: this.fileContent?.formattedFileName || "",
          },
          client: "apolloClientBB",
        })
        .then(() => {
          if (vm.fileContent && vm.fileContent.name && vm.isFileChanged) {
            vm.uploadFileContents();
          } else {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.isEdit
                ? "Accreditation type updated successfully."
                : "Accreditation type added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("on-form-edit");
          }
        })
        .catch((err) => {
          vm.handleAddUpdateError(err);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEdit ? "updating" : "adding",
        form: "accreditation type",
        isListError: false,
      });
    },
    async uploadFileContents() {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Accreditations/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.fileContent.formattedFileName,
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            message: vm.isEdit
              ? "Accreditation type updated successfully"
              : "Accreditation type added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.$emit("on-form-edit");
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
    async addMoreTags() {
      let { valid } = await this.$refs.enforcementGroupForm.validate();
      if (valid) {
        let vm = this;
        vm.addTagLoader = true;
        vm.$apollo
          .mutate({
            mutation: ADD_ACCREDITATION_ENFORCEMENT_GROUP,
            variables: { groupNames: vm.moreTags, formId: 351 },
            client: "apolloClientBB",
          })
          .then(() => {
            vm.addTagLoader = false;
            vm.fetchEnforcementGroups();
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Accreditation Enforcement Group added successfully.",
            };
            vm.moreTags = [];
            vm.showAlert(snackbarData);
            vm.showEnforcementGroupForm = false;
          })
          .catch((err) => {
            this.handleAddTagsError(err);
          });
      }
    },
    handleAddTagsError(err = "") {
      this.addTagLoader = false;
      this.showEnforcementGroupForm = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "adding",
        form: "Accreditation Enforcement Group",
        isListError: false,
      });
    },
    fetchEnforcementGroups() {
      let vm = this;
      vm.enforcedGroupsListLoading = true;
      vm.$apollo
        .query({
          query: LIST_ACCREDITATION_ENFORCEMENT_GROUPS,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response?.data?.listAccreditationEnforcementGroups?.groups.length
          ) {
            vm.enforcedGroupsList =
              response.data.listAccreditationEnforcementGroups.groups;
          } else {
            vm.enforcedGroupsList = [];
          }
          vm.enforcedGroupsListLoading = false;
        })
        .catch(() => {
          vm.enforcedGroupsList = [];
          vm.enforcedGroupsListLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.quill-editor {
  height: 200px;
  background: white;
}

.quill-editor .ql-container {
  height: calc(200px - 42px);
  overflow-y: auto;
}

.quill-editor .ql-toolbar {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.quill-editor .ql-container {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
