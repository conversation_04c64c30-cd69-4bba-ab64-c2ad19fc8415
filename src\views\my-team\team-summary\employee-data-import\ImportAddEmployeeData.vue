<template>
  <v-container class="import-container" fluid>
    <v-row justify="center">
      <v-col cols="12" lg="11" md="12" sm="12">
        <v-card min-height="600" class="rounded-lg">
          <v-card-text>
            <div class="text-center mb-6">
              <span v-for="i in 3" :key="i">
                <v-icon color="primary" size="18" class="ml-1">
                  {{ currentStep >= i ? "fas fa-circle" : "far fa-circle" }}
                </v-icon>
              </span>
            </div>
            <BulkImportStep1
              class="mb-10"
              v-show="currentStep === 1"
              ref="bulkStep1"
              :step1-text="step1Text"
              @file-upload-success="uploadFile($event)"
              @file-upload-error="fileRemoveOrError()"
              @generate-excel="onGenerateExcel()"
              :showDownload="true"
            />
            <BulkImportStep2
              class="mb-10 pb-5"
              v-if="fileContent.length > 0 && currentStep === 2"
              ref="bulkStep2"
              :file-params="fileContent"
              :headers-selected="selectedHeaders"
              @column-mapped="mapHeaders($event)"
            />
            <BulkImportStep3
              class="mb-10"
              ref="bulkImportStep3"
              v-if="checkMatchedFields && currentStep === 3"
              :fields="generateFields"
              :json-data="excelEditorData"
              type-of-import="employeeDetails"
              :extend-validation="excelValidation"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-bottom-navigation v-model="openBottomSheet">
      <v-sheet
        class="align-center text-center"
        :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      >
        <v-row justify="center">
          <v-col
            v-if="!isLoadUploadButton"
            cols="6"
            class="pa-0 d-flex justify-start align-center pl-2"
          >
            <v-btn
              v-if="currentStep > 1"
              id="back_to_step"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              :dense="isMobileView"
              color="primary"
              @click="backToStep()"
            >
              <span><i class="fa fa-chevron-left pr-2"></i> Back</span>
            </v-btn>
            <v-btn
              id="cancel_step"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              :dense="isMobileView"
              class="ml-2"
              color="primary"
              @click="closeForm()"
            >
              Cancel
            </v-btn>
          </v-col>
          <v-col
            :cols="isLoadUploadButton ? '12' : '6'"
            class="pa-0 d-flex justify-center align-center pr-4"
            :style="windowWidth >= 1264 ? 'margin-left: -106px' : ''"
          >
            <div v-if="windowWidth > 768" class="text-end mr-2">
              <div class="mr-1 text-grey text-caption" style="width: 400px">
                {{ nextBtnHelpContent }}
              </div>
            </div>
            <v-btn
              id="next_step"
              rounded="lg"
              color="primary"
              class="mr-10"
              :disabled="!enableNextButton"
              :loading="isLoadUploadButton"
              :size="isMobileView ? 'small' : 'default'"
              :dense="isMobileView"
              @click="nextStep()"
            >
              <span>
                {{ currentStep === 3 ? "Submit" : "Next" }}
                <v-icon v-if="currentStep !== 3" class="pl-1" size="15"
                  >fa fa-chevron-right</v-icon
                >
              </span>
            </v-btn>
          </v-col>
          <v-col
            cols="12"
            v-if="windowWidth <= 768 && nextBtnHelpContent"
            class="pa-1 pr-4 d-flex align-center justify-end"
          >
            <div class="mr-1 text-grey mb-0" style="font-size: 10px">
              {{ nextBtnHelpContent }}
            </div>
          </v-col>
        </v-row>
      </v-sheet>
    </v-bottom-navigation>
    <v-dialog v-model="importConfirmation" width="50%">
      <v-card>
        <v-row>
          <v-col v-if="invalidData && invalidData.length" cols="12">
            <v-alert prominent type="warning">
              <v-row align="center">
                <v-col v-if="invalidData" class="grow">
                  <span>{{ invalidData.length }}</span>
                  out of {{ excelEditorData.length }} do not have valid records.
                  This may result in omission of those records.
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertEmployeeData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
          <v-col v-else cols="12" class="pa-3">
            <v-alert prominent type="success">
              <v-row align="center">
                <v-col class="grow">
                  Everything looks <strong>good</strong>.
                  <div class="pt-1">
                    Are you <strong>sure</strong> you want to import the
                    Employee details?
                  </div>
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertEmployeeData(finalUpdateData)"
                    >Add</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-overlay
          class="align-center justify-center"
          contained
          :model-value="isLoading"
          scrim="#fff"
        >
          <v-progress-circular
            color="primary"
            indeterminate
            size="64"
          ></v-progress-circular>
        </v-overlay>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import BulkImportStep1 from "@/views/common/bulkImport/BulkImportStep1.vue";
import BulkImportStep2 from "@/views/common/bulkImport/BulkImportStep2.vue";
import BulkImportStep3 from "@/views/common/bulkImport/BulkImportStep3.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
//Queries
import {
  LIST_EMP_PROFESSION,
  LIST_BUSINESS_UNIT,
} from "@/graphql/dropDownQueries";
import {
  RETRIEVE_PERSONAL_INFO,
  RETRIEVE_CONTACT_INFO,
} from "@/graphql/commonQueries";

export default {
  name: "ImportAddEmployeeData",
  components: {
    BulkImportStep1,
    BulkImportStep2,
    BulkImportStep3,
  },
  mixins: [FileExportMixin],
  data: () => ({
    currentStep: 1,
    fileContent: [],
    errorsCountInExcel: 0,
    matchedCount: 0,
    openBottomSheet: true,
    isLoadUploadButton: false,
    mappedFileHeader: [],
    step1Text: {
      typeofData: "employee sheet",
      text: "You have the option of using our predefined template or bring in your own employee detail sheet with the headers for import",
      heading: "Download the excel template with predefined headers",
    },
    optionValues: {
      professionName: [],
      businessUnit: [],
      departments: [],
      locations: [],
      employeeType: [],
      workSchedules: [],
      managers: [],
      serviceProvider: [],
      roles: [],
      designations: [],
      fieldForce: [],
      genderList: [],
      maritalList: [],
      languageList: [],
      taxCodeList: [],
      religionList: [],
      nationalityList: [],
      cityList: [],
      countryList: [],
      salutationList: ["Mr", "Miss", "Mrs", "Dr", "Prof", "Ms"],
      genderOrientationList: [
        "Ally",
        "Asexual",
        "Bisexual",
        "Female",
        "Gay",
        "Intersex",
        "Lesbian",
        "Male",
        "Pansexual",
        "Queer",
        "Questioning",
        "Transgender",
        "Transsexual",
      ], // Assuming 230 is the ID for Gender
      bloodGroupList: [
        "A+",
        "A-",
        "B+",
        "B-",
        "AB+",
        "AB-",
        "O+",
        "O-",
        "A1B+",
        "unknown",
      ],
      signInList: ["Mobile No.", "Email"],
      pronounList: ["He/Him", "She/Her", "They/Them"],
      toggleList: ["Yes", "No"],
    },
    excelEditorData: [],
    importConfirmation: false,
    finalExcelData: [],
    finalUpdateData: [],
    isLoading: false,
    step2HeadersData: [],
  }),

  computed: {
    fieldMappings() {
      return [
        { key: "employeeId", label: "Employee Id" },
        {
          key: "biometricIntegrationId",
          label: "Biometric Integration Id",
        },
        {
          key: "salutation",
          label: "Salutation",
          options: this.optionValues["salutationList"],
        },
        { key: "firstName", label: "First Name" },
        { key: "middleName", label: "Middle Name" },
        { key: "lastName", label: "Last Name" },
        { key: "suffix", label: "Suffix" },
        { key: "gender", label: "Gender" },
        {
          key: "pronoun",
          labelIndex: 207,
          options: this.optionValues["pronounList"],
        },
        {
          key: "genderOrientation",
          labelIndex: 208,
          options: this.optionValues["genderOrientationList"],
        },
        {
          key: "maritalStatus",
          label: "Marital Status",
          options: this.optionValues["maritalList"],
        },
        { key: "dateOfBirth", label: "Date of Birth" },
        {
          key: "bloodGroup",
          label: "Blood Group",
          options: this.optionValues["bloodGroupList"],
        },
        {
          key: "nationality",
          label: "Nationality",
          options: this.optionValues["nationalityList"],
        },
        { key: "otherNationality", label: "Other Nationality" },
        { key: "aadharCardNumber", labelIndex: 212 },
        { key: "taxIdentificationNumber", labelIndex: 213 },
        {
          key: "taxCode",
          labelIndex: 188,
          options: this.optionValues["taxCodeList"],
        },
        { key: "personalEmail", label: "Personal Email" },
        {
          key: "isManager",
          label: "Is Manager",
          options: this.optionValues["toggleList"],
        },
        {
          key: "isRecruiter",
          label: "Is Recruiter",
          options: this.optionValues["toggleList"],
        },
        {
          key: "allowUserSignIn",
          label: "Allow User Sign-In",
          options: this.optionValues["toggleList"],
        },
        {
          key: "signInOption",
          label: "Sign-In Option",
          options: this.optionValues["signInList"],
        },
        { key: "birthPlace", labelIndex: 295 },
        { key: "nickname", labelIndex: 232 },
        { key: "hobbies", label: "Hobbies" },
        {
          key: "religion",
          labelIndex: 296,
          options: this.optionValues["religionList"],
        },
        { key: "otherReligion", label: "Other Religion" },
        { key: "caste", label: "Caste" },
        { key: "ethnicRace", label: "Ethnic Race" },
        { key: "insuranceNo", labelIndex: 223 },
        { key: "nps", labelIndex: 224 },
        {
          key: "servedInMilitary",
          label: "Served in Military",
          options: this.optionValues["toggleList"],
        },
        {
          key: "disabled",
          label: "Disabled",
          options: this.optionValues["toggleList"],
        },
        {
          key: "smoker",
          label: "Smoker",
          options: this.optionValues["toggleList"],
        },
        { key: "smokerAsOf", label: "Smoker As Of" },
        {
          key: "role",
          label: "Role Access Rights",
          options: this.optionValues["roles"],
        },
        { key: "dateOfJoin", label: "Date of Join" },
        {
          key: "designation",
          label: "Designation",
          options: this.optionValues["designations"],
        },
        {
          key: "department",
          label: "Department",
          options: this.optionValues["departments"],
        },
        {
          key: "location",
          label: "Location",
          options: this.optionValues["locations"],
        },
        {
          key: "workSchedule",
          label: "Work Schedule",
          options: this.optionValues["workSchedules"],
        },
        {
          key: "employeeType",
          label: "Employee Type",
          options: this.optionValues["employeeType"],
        },
        {
          key: "companyName",
          labelIndex: 115,
          condition: () => this.fieldForce === 1,
          options: this.optionValues["serviceProvider"],
        },
        { key: "workEmail", label: "Work Email" },
        {
          key: "employeeProfessionals",
          label: "Employee Profession",
          options: this.optionValues["professionName"],
        },
        {
          key: "manager",
          label: "Manager",
          options: this.optionValues["managers"],
        },
        {
          key: "businessUnit",
          label: "Business Unit",
          options: this.optionValues["businessUnit"],
        },
        { key: "pfPolicyNo", labelIndex: 305 },
        { key: "jobCode", label: "Job Code" },
        { key: "probationDate", label: "Probation Date" },
        {
          key: "confirmed",
          label: "Confirmed",
          options: this.optionValues["toggleList"],
        },
        {
          key: "commissionBasedEmployee",
          label: "Commission Based Employee",
          options: this.optionValues["toggleList"],
        },
        {
          key: "attendanceEnforcedPayment",
          label: "Attendance Enforced Payment",
          options: this.optionValues["toggleList"],
        },
        { key: "taxWithholding", labelIndex: 307 },
        { key: "previousExperience", label: "Previous Experience" },
        { key: "permanentAddress.apartmentName", labelIndex: 236 },
        {
          key: "permanentAddress.streetName",
          label: "Permanent Address - Street Name",
        },
        { key: "permanentAddress.cityMunicipality", labelIndex: 140 },
        {
          key: "permanentAddress.stateProvince",
          label: "Permanent Address - State/Province",
        },
        {
          key: "permanentAddress.country",
          label: "Permanent Address - Country",
        },
        { key: "permanentAddress.pinCode", labelIndex: 147 },
        { key: "currentAddress.apartmentName", labelIndex: 236 },
        {
          key: "currentAddress.streetName",
          label: "Current Address - Street Name",
        },
        { key: "currentAddress.cityMunicipality", labelIndex: 140 },
        {
          key: "currentAddress.stateProvince",
          label: "Current Address - State/Province",
        },
        { key: "currentAddress.country", label: "Current Address - Country" },
        { key: "currentAddress.pinCode", labelIndex: 147 },
        {
          key: "officeAddress.sameAsLocationAddress",
          label: "Office Address - Same as Location Address",
        },
        { key: "officeAddress.apartmentName", labelIndex: 237 },
        {
          key: "officeAddress.streetName",
          label: "Office Address - Street Name",
        },
        { key: "officeAddress.cityMunicipality", labelIndex: 141 },
        {
          key: "officeAddress.stateProvince",
          label: "Office Address - State/Province",
        },
        { key: "officeAddress.country", label: "Office Address - Country" },
        { key: "officeAddress.pinCode", labelIndex: 148 },
        {
          key: "contactInformation.mobileNumberCountryCode",
          label: "Mobile Number Country Code",
        },
        {
          key: "contactInformation.mobileNumber",
          label: "Mobile Number",
        },
        {
          key: "contactInformation.emergencyContactName",
          label: "Contact Information - Emergency Contact Name",
        },
        {
          key: "contactInformation.emergencyContactNo",
          label: "Contact Information - Emergency Contact No",
        },
        {
          key: "contactInformation.emergencyContactRelation",
          label: "Contact Information - Emergency Contact Relation",
        },
        {
          key: "contactInformation.telephoneNo",
          label: "Contact Information - Telephone No",
        },
        {
          key: "contactInformation.workNo",
          label: "Contact Information - Work No",
        },
      ];
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    formEmployeeHeaders() {
      let tableHeaders = this.fieldMappings.reduce((headers, field) => {
        const label = field.label
          ? field.label
          : this.labelList[field.labelIndex]?.Field_Alias;
        const isVisible = field.label
          ? true
          : field.condition
          ? field.condition()
          : this.labelList[field.labelIndex]?.Field_Visiblity === "Yes";
        const isRequired = field.labelIndex
          ? this.labelList[field.labelIndex]?.Mandatory_Field === "Yes"
          : false;
        if (isVisible && label) {
          headers.push({
            key: field.key,
            header: label,
            required: isRequired,
            options: field.options ? field.options : null,
          });
        }
        return headers;
      }, []);
      return tableHeaders;
    },
    excelValidation() {
      return {
        Salutation: ["Mr", "Miss", "Mrs", "Dr", "Prof"],
        Gender_Orientation: [
          "Ally",
          "Asexual",
          "Bisexual",
          "Female",
          "Gay",
          "Intersex",
          "Lesbian",
          "Male",
          "Pansexual",
          "Queer",
          "Questioning",
          "Transgender",
          "Transsexual",
        ], // Assuming 230 is the ID for Gender
        Blood_Group: [
          "A+",
          "A-",
          "B+",
          "B-",
          "AB+",
          "AB-",
          "O+",
          "O-",
          "A1B+",
          "unknown",
        ],
        Sign_In_Option: ["Mobile No", "Email"],
        Pronoun: ["He/Him", "She/Her", "They/Them"],
        Employee_Professionals: this.optionValues.professionName || [],
        Manager: this.optionValues.managers || [],
        Company_Name: this.optionValues.serviceProvider || [],
        Business_Unit: this.optionValues.businessUnit || [],
        Designation: this.optionValues.designations || [],
        Gender: this.optionValues.genderList || [],
        Marital_Status: this.optionValues.maritalList || [],
        Tax_Code: this.optionValues.taxCodeList || [],
        Religion: this.optionValues.religionList || [],
        Nationality: this.optionValues.nationalityList || [],
        Permanent_Address_City_Municipality: this.optionValues.cityList || [],
        Current_Address_City_Municipality: this.optionValues.cityList || [],
        Office_Address_City_Municipality: this.optionValues.cityList || [],
        Permanent_Address_Country: this.optionValues.countryList || [],
        Current_Address_Country: this.optionValues.countryList || [],
        Office_Address_Country: this.optionValues.countryList || [],
        Role: this.optionValues.roles || [],
        Department: this.optionValues.departments || [],
        Profession: this.optionValues.professionName || [],
        Location: this.optionValues.locations || [],
        Employee_Type: this.optionValues.employeeType || [],
        Work_Schedule: this.optionValues.workSchedules || [],
        Service_Provider: this.optionValues.serviceProvider || [],
        Toggle_List: this.optionValues.toggleList || [],
      };
    },
    selectedHeaders() {
      let output = this.formEmployeeHeaders;
      output = output.map((el) => {
        return {
          title: el.header,
          value: el.header,
          props: { disabled: false },
        };
      });
      return output;
    },
    invalidData() {
      return this.$refs.bulkImportStep3.invalidData;
    },
    invalidEntries() {
      return Array.from(new Set(this.$refs.bulkImportStep3.invalidData));
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    enableNextButton() {
      if (this.currentStep === 1 && this.fileContent.length > 0) {
        return true;
      } else if (this.currentStep === 2 && this.checkMatchedFields) {
        return true;
      } else if (this.currentStep === 3) {
        this.formattedFileContent();
        return true;
      } else {
        return false;
      }
    },

    nextBtnHelpContent() {
      if (this.currentStep === 1) {
        if (this.fileContent.length === 0)
          return "Please import the data with supported file types (CSV, XLSX and XLS) to continue with the next step.";
        else return "";
      } else if (this.currentStep === 2) {
        return "The unmatched optional column(s) will not be processed in the next step.";
      } else if (this.currentStep === 3) {
        if (this.formattedFileContent.length === 0) {
          return "";
        } else if (this.errorsCountInExcel !== 0) {
          return "There seems to be some validation error(s) in your file. Please amend it before uploading.";
        } else {
          return "By clicking the 'Submit' button, you can import employee data.";
        }
      } else {
        return "";
      }
    },
    mandatoryHeader() {
      let headersObject = this.formEmployeeHeaders.map((el) => el.header);
      return headersObject;
    },

    checkMatchedFields() {
      let mandatoryHeader = this.mandatoryHeader;
      if (this.matchedCount == this.mandatoryHeader.length) {
        let mandatoryMatchedCount = 0;
        for (var i in this.mappedFileHeader) {
          if (mandatoryHeader.includes(this.mappedFileHeader[i].hrapp_header))
            mandatoryMatchedCount++;
        }
        this.addHeaders();
        return mandatoryMatchedCount === this.mandatoryHeader.length
          ? true
          : false;
      } else return false;
    },
    excelFileData() {
      return this.fileContent.filter(
        (content) => content.filter(Boolean).length > 0
      );
    },
    generateFields() {
      let output = this.formEmployeeHeaders;
      output = output.map((el) => {
        return {
          field: el.header,
          label: el.header,
          type: el.options ? "select" : "string",
          readonly: false,
          width: "200px",
          options: el.options,
        };
      });
      return output;
    },
  },
  mounted() {
    this.retrieveDropdownDetails();
    this.retrieveEmpProfessions();
    this.retrieveBusinessUnit();
    this.retrievePersonalInfo();
    this.retrieveContactInfo();
    this.getTotalDesignatons();
  },
  methods: {
    insertEmployeeData(finalUpdateData) {
      console.log(finalUpdateData);
    },
    mapHeaders(event) {
      this.matchedCount = event[0];
      this.mappedFileHeader = event[1];
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },

    retrieveDropdownDetails() {
      let vm = this;
      vm.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              locations,
              employeeType,
              workSchedules,
              managers,
              serviceProvider,
              roles,
              fieldForce,
            } = res.data.getDropDownBoxDetails;
            // Populate option values
            this.optionValues = {
              ...this.optionValues, // Preserve existing optionValues
              departments:
                departments.map((item) => item.Department_Name) || [],
              locations: locations.map((item) => item.Location_Name) || [],
              employeeType:
                employeeType.map((item) => item.Employee_Type) || [],
              workSchedules: workSchedules.map((item) => item.Title) || [],
              managers: managers.map((item) => item.Manager_Name) || [],
              serviceProvider:
                serviceProvider.map((item) => item.Service_Provider_Name) || [],
              roles: roles.map((item) => item.Roles_Name) || [],
              fieldForce: fieldForce || [],
            };
          }
        });
    },
    async getTotalDesignatons() {
      await this.$store
        .dispatch("getDesignationList", {
          offset: 1,
          limit: 1,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            let { totalRecords } = res.data.getDesignationDetails;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.apiCallCount = 0;
              this.totalApiCount = Math.ceil(totalRecords / 25000);
              for (let i = 0; i < this.totalApiCount; i++) {
                this.getDesignationList(i * 25000);
              }
            }
          }
        })
        .catch(() => {});
    },
    async getDesignationList(offset) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          offset: offset,
          limit: 25000,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.optionValues = {
              ...this.optionValues,
              designations: [
                ...this.optionValues.designations,
                ...designationResult.map((item) => item.Designation_Name),
              ],
            };
          }
        })
        .catch(() => {});
    },
    retrieveEmpProfessions() {
      let vm = this;
      vm.$apollo
        .query({
          query: LIST_EMP_PROFESSION,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listEmpProfession &&
            !response.data.listEmpProfession.errorCode
          ) {
            const professionList = response.data.listEmpProfession.professions;
            this.optionValues.professionName = Array.from(
              new Set(professionList.map((item) => item.Profession_Name))
            );
          }
        });
    },
    retrieveBusinessUnit() {
      let vm = this;
      vm.$apollo
        .query({
          query: LIST_BUSINESS_UNIT,
          client: "apolloClientI",
          variables: {
            action: "active",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBusinessUnitInDropdown &&
            !response.data.listBusinessUnitInDropdown.errorCode
          ) {
            const businessUnitList =
              response.data.listBusinessUnitInDropdown.settings;

            // Correctly map the businessUnit property
            if (businessUnitList && businessUnitList.length > 0) {
              vm.optionValues.businessUnit = Array.from(
                new Set(businessUnitList.map((item) => item.businessUnit))
              );
            }
          }
        });
    },
    retrievePersonalInfo() {
      let vm = this;
      vm.$apollo
        .query({
          query: RETRIEVE_PERSONAL_INFO,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrievePersonalInfoDropdown &&
            !response.data.retrievePersonalInfoDropdown.errorCode
          ) {
            const {
              genderList,
              maritalList,
              languageList,
              taxCodeList,
              religionList,
              nationalityList,
            } = response.data.retrievePersonalInfoDropdown;

            // Ensure lists are arrays before mapping
            this.optionValues = {
              ...this.optionValues,
              genderList: Array.isArray(JSON.parse(genderList))
                ? JSON.parse(genderList).map((item) => item.Gender)
                : [],
              maritalList: Array.isArray(JSON.parse(maritalList))
                ? JSON.parse(maritalList).map((item) => item.Marital_Status)
                : [],
              languageList: Array.isArray(JSON.parse(languageList))
                ? JSON.parse(languageList).map((item) => item.Language_Name)
                : [],
              taxCodeList: Array.isArray(JSON.parse(taxCodeList))
                ? JSON.parse(taxCodeList).map((item) => item.Tax_Code)
                : [],
              religionList: Array.isArray(JSON.parse(religionList))
                ? JSON.parse(religionList).map((item) => item.Religion)
                : [],
              nationalityList: Array.isArray(JSON.parse(nationalityList))
                ? JSON.parse(nationalityList).map((item) => item.Nationality)
                : [],
            };
          }
        });
    },
    retrieveContactInfo() {
      let vm = this;
      vm.$apollo
        .query({
          query: RETRIEVE_CONTACT_INFO,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveContactInfoDropdown &&
            !response.data.retrieveContactInfoDropdown.errorCode
          ) {
            const {
              cityList = [],
              // stateList = [],
              countryList = [],
            } = response.data.retrieveContactInfoDropdown;
            // Ensure lists are arrays before mapping
            this.optionValues = {
              ...this.optionValues,
              cityList: Array.isArray(JSON.parse(cityList))
                ? JSON.parse(cityList).map((item) => item.City_Name)
                : [],
              // cityList: Array.isArray(cityList)
              //   ? cityList.map((item) => item.City_Name)
              //   : [],
              // stateList: Array.isArray(stateList)
              //   ? stateList.map((item) => item.State_Name)
              //   : [],
              // countryList: Array.isArray(countryList)
              //   ? countryList.map((item) => item.Country_Name)
              //   : [],

              countryList: Array.isArray(JSON.parse(countryList))
                ? JSON.parse(countryList).map((item) => item.Country_Name)
                : [],
            };
          }
        });
    },

    onGenerateExcel() {
      // Function to create a new employee data object
      const createEmployeeDataObject = () => ({
        // Personal Info
        employeeId: null,
        biometricIntegrationId: null,
        salutation: null,
        firstName: null,
        middleName: null,
        lastName: null,
        suffix: null,
        gender: null,
        pronoun: null,
        genderOrientation: null,
        maritalStatus: null,
        dateOfBirth: null,
        bloodGroup: null,
        nationality: null,
        otherNationality: null,
        aadharCardNumber: null,
        taxIdentificationNumber: null,
        taxCode: null,
        personalEmail: null,
        socialSecurityNo: null,
        isManager: null,
        isRecruiter: null,
        allowUserSignIn: null,
        signInOption: null,
        birthPlace: null,
        nickname: null,
        hobbies: null,
        religion: null,
        otherReligion: null,
        caste: null,
        ethnicRace: null,
        insuranceNo: null,
        nps: null,
        servedInMilitary: null,
        disabled: null,
        smoker: null,
        smokerAsOf: null,
        // Job Info
        role: null,
        dateOfJoin: null,
        designation: null,
        department: null,
        location: null,
        workSchedule: null,
        employeeType: null,
        companyName: null,
        workEmail: null,
        employeeProfessionals: null,
        manager: null,
        businessUnit: null,
        pfPolicyNo: null,
        jobCode: null,
        probationDate: null,
        confirmed: null,
        commissionBasedEmployee: null,
        attendanceEnforcedPayment: null,
        taxWithholding: null,
        previousExperience: null,
        // Contact Info
        permanentAddress: {
          apartmentName: null,
          streetName: null,
          cityMunicipality: null,
          stateProvince: null,
          country: null,
          pinCode: null,
        },
        currentAddress: {
          apartmentName: null,
          streetName: null,
          cityMunicipality: null,
          stateProvince: null,
          country: null,
          pinCode: null,
        },
        officeAddress: {
          sameAsLocationAddress: null,
          apartmentName: null,
          streetName: null,
          cityMunicipality: null,
          stateProvince: null,
          country: null,
          pinCode: null,
        },
        contactInformation: {
          mobileNumber: null,
          emergencyContactName: null,
          emergencyContactNo: null,
          emergencyContactRelation: null,
          telephoneNo: null,
          workNo: null,
        },
      });

      // Unified Data for Export
      const employeeData = Array.from(
        { length: 100 },
        createEmployeeDataObject
      );

      // Generate Headers
      let tableHeaders = this.formEmployeeHeaders;

      // Prepare Export Options
      let exportOptions = {
        fileExportData: employeeData,
        fileName: "Employee Import(Add).xlsx",
        sheetName: "Employee Import",
        header: tableHeaders,
        requiredHeaders: [
          "Employee Id",
          "Salutation",
          "First Name",
          "Last Name",
          "Gender",
          "Marital Status",
          "Date of Birth",
          "Blood Group",
          "Nationality",
          "Other Nationality",
          "Date of Join",
          "Role Access Rights",
          "Designation",
          "Department",
          "Location",
          "Work Schedule",
          "Employee Type",
          "Company Name",
          "Employee Professionals",
          "Probation Date",
          "Permanent Address - Apartment Name",
          "Permanent Address - City/Municipality",
          "Permanent Address - State/Province",
          "Permanent Address - Country",
          "Current Address - Apartment Name",
          "Current Address - City/Municipality",
          "Current Address - State/Province",
          "Current Address - Country",
          "Office Address - Apartment Name",
          "Office Address - City/Municipality",
          "Office Address - State/Province",
          "Office Address - Country",
          "Mobile Number Country Code",
          "Mobile Number",
        ],
        // Adjust if needed for column highlighting
        columnHighlightProps: {
          type: "Employee Report Import",
          Salutation: ["Mr", "Miss", "Mrs", "Dr", "Prof"],
          Common_Toggle: ["Yes", "No"],
          Pronoun: ["He/Him", "She/Her", "They/Them"],
          Gender_Orientation: [
            "Ally",
            "Asexual",
            "Bisexual",
            "Female",
            "Gay",
            "Intersex",
            "Lesbian",
            "Male",
            "Pansexual",
            "Queer",
            "Questioning",
            "Transgender",
            "Transsexual",
          ],
          Blood_Group: [
            "A+",
            "A-",
            "B+",
            "B-",
            "AB+",
            "AB-",
            "O+",
            "O-",
            "A1B+",
            "unknown",
          ],
          Sign_In_Option: ["Mobile No", "Email"],
          Employee_Professionals: this.optionValues.professionName || [],
          Manager: this.optionValues.managers || [],
          Business_Unit: this.optionValues.businessUnit || [],
          Designation: this.optionValues.designations || [],
          Gender: this.optionValues.genderList || [],
          Marital_Status: this.optionValues.maritalList || [],
          Tax_Code: this.optionValues.taxCodeList || [],
          Religion: this.optionValues.religionList || [],
          Nationality: this.optionValues.nationalityList || [],
          Permanent_Address_City_Municipality: this.optionValues.cityList || [],
          Current_Address_City_Municipality: this.optionValues.cityList || [],
          Office_Address_City_Municipality: this.optionValues.cityList || [],
          Permanent_Address_Country: this.optionValues.countryList || [],
          Current_Address_Country: this.optionValues.countryList || [],
          Office_Address_Country: this.optionValues.countryList || [],
          Profession: this.optionValues.professionName || [],
          Department: this.optionValues.departments || [],
          Location: this.optionValues.locations || [],
          Employee_Type: this.optionValues.employeeType || [],
          Work_Schedule: this.optionValues.workSchedules || [],
          Service_Provider: this.optionValues.serviceProvider || [],
          Role: this.optionValues.roles || [],
          Company_Name: this.optionValues.serviceProvider || [],
        },
        xSplit: 3,
      };

      // Add Extra Required Fields
      let extraRequiredFields = tableHeaders
        .filter((header) => header.required)
        .map((header) => header.header);

      exportOptions.requiredHeaders = [
        ...exportOptions.requiredHeaders,
        ...extraRequiredFields,
      ];

      // Create a map for required headers for fast lookup
      const requiredHeadersMap = new Map(
        exportOptions.requiredHeaders.map((header) => [header, true])
      );

      // Sort headers array
      exportOptions.header.sort((a, b) => {
        // Check if either header is in the requiredHeaders list
        const isARequired = requiredHeadersMap.has(a.header);
        const isBRequired = requiredHeadersMap.has(b.header);

        // If both are required or both are not required, maintain original order
        if (isARequired === isBRequired) return 0;

        // Prioritize required headers
        return isARequired ? -1 : 1;
      });

      this.exportExcelFile(exportOptions);
    },

    formattedFileContent() {
      let generatedData = this.formExcelData();
      this.excelEditorData = generatedData;
    },

    formExcelData() {
      let fields = this.generateFields;
      let data = JSON.parse(JSON.stringify(this.excelFileData));
      let headersAssigned = this.step2HeadersData;
      //Getting the field of the array of objects
      let excelData = [];
      let idCounter = 1;
      // Iterate through each row of data
      for (let i = 1; i < data.length; i++) {
        let rowData = data[i];
        let rowObj = { $id: "000000" + idCounter++ };

        // Iterate through each field definition and populate the row object
        for (let j = 0; j < fields.length; j++) {
          let fieldDef = fields[j];
          let fieldName = fieldDef.field;
          // Find the index of the field in the header mappings array
          let headerIndex = headersAssigned.findIndex(
            (header) => header.hrapp_header === fieldName
          );

          // If the field is present in the header mappings array, use the corresponding value from the input data
          if (headerIndex >= 0) {
            let dataValue = rowData[headerIndex];
            if (dataValue !== null && dataValue !== undefined) {
              rowObj[fieldName] = dataValue;
            } else {
              rowObj[fieldName] = null;
            }
          } else {
            // If the field is not present in the header mappings array, use the default value for the field type
            switch (fieldDef.type) {
              case "string":
                rowObj[fieldName] = "";
                break;
              case "number":
                rowObj[fieldName] = 0;
                break;
              case "boolean":
                rowObj[fieldName] = false;
                break;
              default:
                rowObj[fieldName] = null;
                break;
            }
          }
        }
        excelData.push(rowObj);
      }
      return excelData;
    },
    uploadFile(content) {
      this.fileContent = content;
    },
    fileRemoveOrError() {
      this.fileContent = [];
    },
    handleImportError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "importing",
        form: ["personalInfo", "jobInfo", "contactInfo"],
        isListError: false,
      });
    },
    addHeaders() {
      if (this.$refs.bulkStep2 && this.$refs.bulkStep2.tableItems) {
        this.step2HeadersData = this.$refs.bulkStep2.tableItems;
      }
    },
    formBulkData(data) {
      const filteredData = data;
      this.finalExcelData = data;
      console.log("Filtered data", filteredData);
      // this.finalUpdateData = filteredData.map((item) => {
      //   const newItem = {
      //     employeeId: item["Employee_Id"],
      //     biometricIntegrationId: item["Biometric_Integration_Id"],
      //     salutation: item["Salutation"],
      //     firstName: item["First_Name"],
      //     middleName: item["Middle_Name"],
      //     lastName: item["Last_Name"],
      //     suffix: item["Suffix"],
      //     gender: item["Gender"],
      //     pronoun: item["Pronoun"],
      //     genderOrientation: item["Gender_Orientation"],
      //     maritalStatus: item["Marital_Status"],
      //     dateOfBirth: item["Date_of_Birth"],
      //     bloodGroup: item["Blood_Group"],
      //     nationality: item["Nationality"],
      //     otherNationality: item["Other_Nationality"],
      //     aadharCardNumber: item["Aadhar_Card_Number"],
      //     taxIdentificationNumber: item["Tax_Identification_Number"],
      //     taxCode: item["Tax_Code"],
      //     personalEmail: item["Personal_Email"],
      //     socialSecurityNo: item["Social_Security_No"],
      //     isManager: item["Is_Manager"],
      //     isRecruiter: item["Is_Recruiter"],
      //     allowUserSignIn: item["Allow_User_Sign_In"],
      //     signInOption: item["Sign_In_Option"],
      //     birthPlace: item["Birth_Place"],
      //     nickname: item["Nickname"],
      //     hobbies: item["Hobbies"],
      //     religion: item["Religion"],
      //     otherReligion: item["Other_Religion"],
      //     caste: item["Caste"],
      //     ethnicRace: item["Ethnic_Race"],
      //     insuranceNo: item["Insurance_No"],
      //     nps: item["NPS"],
      //     servedInMilitary: item["Served_In_Military"],
      //     disabled: item["Disabled"],
      //     smoker: item["Smoker"],
      //     smokerAsOf: item["Smoker_As_Of"],

      //     // Job Info
      //     role: item["Role"],
      //     dateOfJoin: item["Date_of_Join"],
      //     designation: item["Designation"],
      //     department: item["Department"],
      //     location: item["Location"],
      //     workSchedule: item["Work_Schedule"],
      //     employeeType: item["Employee_Type"],
      //     companyName: item["Company_Name"],
      //     workEmail: item["Work_Email"],
      //     employeeProfessionals: item["Employee_Professionals"],
      //     manager: item["Manager"],
      //     businessUnit: item["Business_Unit"],
      //     pfPolicyNo: item["PF_Policy_No"],
      //     jobCode: item["Job_Code"],
      //     probationDate: item["Probation_Date"],
      //     confirmed: item["Confirmed"],
      //     commissionBasedEmployee: item["Commission_Based_Employee"],
      //     attendanceEnforcedPayment: item["Attendance_Enforced_Payment"],
      //     taxWithholding: item["Tax_Withholding"],
      //     previousExperience: item["Previous_Experience"],

      //     // Contact Info
      //     permanentAddress: {
      //       apartmentName: item["Permanent_Address_Apartment_Name"],
      //       streetName: item["Permanent_Address_Street_Name"],
      //       cityMunicipality: item["Permanent_Address_City_Municipality"],
      //       stateProvince: item["Permanent_Address_State_Province"],
      //       country: item["Permanent_Address_Country"],
      //       pinCode: item["Permanent_Address_Pin_Code"],
      //     },
      //     currentAddress: {
      //       apartmentName: item["Current_Address_Apartment_Name"],
      //       streetName: item["Current_Address_Street_Name"],
      //       cityMunicipality: item["Current_Address_City_Municipality"],
      //       stateProvince: item["Current_Address_State_Province"],
      //       country: item["Current_Address_Country"],
      //       pinCode: item["Current_Address_Pin_Code"],
      //     },
      //     officeAddress: {
      //       sameAsLocationAddress:
      //         item["Office_Address_Same_As_Location_Address"],
      //       apartmentName: item["Office_Address_Apartment_Name"],
      //       streetName: item["Office_Address_Street_Name"],
      //       cityMunicipality: item["Office_Address_City_Municipality"],
      //       stateProvince: item["Office_Address_State_Province"],
      //       country: item["Office_Address_Country"],
      //       pinCode: item["Office_Address_Pin_Code"],
      //     },
      //     contactInformation: {
      //       mobileNumber: item["Contact_Info_Mobile_Number"],
      //       emergencyContactName: item["Contact_Info_Emergency_Contact_Name"],
      //       emergencyContactNo: item["Contact_Info_Emergency_Contact_No"],
      //       emergencyContactRelation:
      //         item["Contact_Info_Emergency_Contact_Relation"],
      //       telephoneNo: item["Contact_Info_Telephone_No"],
      //       workNo: item["Contact_Info_Work_No"],
      //     },
      //   };

      //   return newItem;
      // });

      this.importConfirmation = true;
    },

    backToStep() {
      if (this.currentStep !== 1) {
        this.currentStep--;
      }
    },
    nextStep() {
      if (this.currentStep === 3) {
        this.formBulkData(this.$refs.bulkImportStep3.filteredData);
      } else {
        this.currentStep += 1;
      }
    },
    closeForm() {
      this.$emit("close-modal");
    },
  },
};
</script>
<style>
.import-container {
  padding: 5em 2em 0em 3em;
}
.v-bottom-navigation__content {
  justify-content: space-around;
  flex-direction: column;
}
.dp__button_bottom {
  display: none;
}
</style>
