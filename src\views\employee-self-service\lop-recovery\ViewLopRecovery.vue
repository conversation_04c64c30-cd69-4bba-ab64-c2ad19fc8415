<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="primary" size="20">fas fa-hand-holding-usd </v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">LOP Recovery</div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-btn
            size="small"
            color="primary"
            variant="outlined"
            rounded="lg"
            @click="$emit('open-edit-form')"
            v-if="accessRights.update && isApprovalStatusValid"
            >Edit</v-btn
          >
          <v-icon class="mx-1" color="secondary" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card>
        <div
          :style="
            isMobileView ? 'height: calc(100vh - 200px); overflow: scroll' : ''
          "
        >
          <v-card-text>
            <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  LOP Deduction Month
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(
                      editedLopRecoveryDetails.Salary_Deduction_Month
                    )
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  LOP Recovery Processing Month
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedLopRecoveryDetails.Salary_Month) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Total Recovery Days
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(
                      editedLopRecoveryDetails.Total_Lop_Recovery_Days
                    )
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  LOP Days For Recovery
                </p>
                <v-menu transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      rounded="lg"
                      variant="outlined"
                      size="small"
                      color="primary"
                      v-bind="props"
                      >view</v-btn
                    >
                  </template>
                  <v-card
                    max-height="400px"
                    min-width="400px"
                    max-width="300px"
                  >
                    <div class="pa-2">
                      <v-row>
                        <v-list>
                          <v-list-item
                            v-for="tag in lopDaysForRecovery"
                            :key="tag"
                            class="ma-2"
                            style="pointer-events: none"
                          >
                            {{ tag }}
                          </v-list-item>
                        </v-list>
                      </v-row>
                    </div>
                  </v-card>
                </v-menu>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Total LOP Recovery Amount
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedLopRecoveryDetails.Recovery_Amount) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Status</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedLopRecoveryDetails.Approval_Status) }}
                </p>
              </v-col>
              <v-col cols="12" :class="isMobileView ? ' ml-4' : ''">
                <p class="text-subtitle-1 text-grey-darken-1">Remarks</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedLopRecoveryDetails.remark) }}
                </p>
              </v-col>
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-card-text>
        </div>
      </v-card>
    </v-card>
  </div>
</template>

<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import { checkNullValue } from "@/helper.js";
export default {
  name: "ViewLopRecovery",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    editedLopRecoveryDetails: {},
    showToolTip: false,
    isNoEmployees: false,
    isLoadingCard: false,
    errorInFetchEmployeesList: false,
    lopDaysForRecovery: [],
  }),
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.editedLopRecoveryDetails = Object.assign({}, newData);
        // Split the relevant properties into arrays
        const startDateArray = newData.Start_Date
          ? newData.Start_Date.split(",")
          : [];
        const reasonArray = newData.Reason ? newData.Reason.split(",") : [];
        const durationArray = newData.Duration
          ? newData.Duration.split(",")
          : [];

        // Create the desired output array
        this.lopDaysForRecovery = startDateArray.map((startDate, index) => {
          const duration = parseFloat(durationArray[index]);
          const reason = reasonArray[index];

          let durationLabel = "";

          if (duration === 1) {
            durationLabel = "Full Day";
          } else if (duration === 0.5) {
            durationLabel = "Half a Day";
          }

          return `${this.formatDate(startDate)} - ${reason} (${durationLabel})`;
        });
        this.prefillMoreDetails();
      },
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date, withTime = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    isApprovalStatusValid() {
      return (
        this.editedLopRecoveryDetails.Approval_Status === "Applied" ||
        this.editedLopRecoveryDetails.Approval_Status === "Approved"
      );
    },
  },

  methods: {
    checkNullValue,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const Added_On = this.formatDate(
          new Date(this.selectedItem.Added_On + ".000Z"),
          true
        ),
        Added_By = this.selectedItem.Added_By,
        Updated_By = this.selectedItem.Updated_By,
        Updated_On = this.formatDate(
          new Date(this.selectedItem.Updated_On + ".000Z"),
          true
        );
      if (Added_On && Added_By) {
        this.moreDetailsList.push({
          actionDate: Added_On,
          actionBy: Added_By,
          text: "Added",
        });
      }
      if (Updated_By && Updated_On) {
        this.moreDetailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By,
          text: "Updated",
        });
      }
    },
  },
};
</script>
