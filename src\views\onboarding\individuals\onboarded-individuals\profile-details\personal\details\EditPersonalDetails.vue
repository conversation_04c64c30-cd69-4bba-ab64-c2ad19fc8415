<template>
  <div class="pb-10">
    <VueTelInput
      v-show="false"
      :autoDefaultCountry="true"
      @country-changed="getCountryCode($event)"
    ></VueTelInput>
    <div v-if="isMounted">
      <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
        >Personal Details</span
      >
      <v-form ref="editPersonalDetailsForm" class="pa-2">
        <div>
          <v-row>
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="['Mr', 'Miss', 'Mrs', 'Dr', 'Prof', 'Ms']"
                label="Salutation"
                :isRequired="true"
                :itemSelected="editedPersonalDetails.Salutation"
                :rules="[
                  required('Salutation', editedPersonalDetails.Salutation),
                ]"
                @selected-item="onChangeCustomSelectField($event, 'Salutation')"
                ref="salutation"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.Emp_First_Name"
                :rules="[
                  required('First Name', editedPersonalDetails.Emp_First_Name),
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.Emp_First_Name,
                    'empFirstName',
                    'First Name'
                  ),
                ]"
                variant="solo"
                ref="firstName"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  First Name<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.Emp_Middle_Name"
                label="Middle Name"
                :rules="
                  checkFieldAvailability(editedPersonalDetails.Emp_Middle_Name)
                    ? [
                        validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Emp_Middle_Name,
                          'empMiddleName',
                          'Middle Name'
                        ),
                      ]
                    : [true]
                "
                ref="middleName"
                variant="solo"
                @update:model-value="onChangeFields()"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.Emp_Last_Name"
                :rules="[
                  required('Last Name', editedPersonalDetails.Emp_Last_Name),
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.Emp_Last_Name,
                    'empLastName',
                    'Last Name'
                  ),
                ]"
                variant="solo"
                ref="lastName"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  Last Name<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6"
              ><v-text-field
                label="Suffix"
                variant="solo"
                v-model="editedPersonalDetails.Suffix"
                @update:model-value="onChangeFields()"
              ></v-text-field
            ></v-col>
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="genderList"
                label="Gender"
                :isRequired="true"
                :rules="
                  editedPersonalDetails.Gender_Id == 0
                    ? [true]
                    : [required('Gender', editedPersonalDetails.Gender_Id)]
                "
                :itemSelected="editedPersonalDetails.Gender_Id"
                ref="gender"
                itemValue="genderId"
                itemTitle="gender"
                :isLoading="genderListLoading"
                :noDataText="
                  genderListLoading ? 'Loading...' : 'No data available'
                "
                @selected-item="onChangeCustomSelectField($event, 'Gender_Id')"
              ></CustomSelect>
            </v-col>
            <v-col
              cols="12"
              md="4"
              sm="6"
              v-if="labelList[338].Field_Visiblity == 'Yes'"
            >
              <CustomSelect
                :items="genderIdentityList"
                :label="labelList[338].Field_Alias"
                :isRequired="labelList[338].Mandatory_Field === 'Yes'"
                :rules="
                  labelList[338].Mandatory_Field === 'Yes'
                    ? [
                        required(
                          labelList[338].Field_Alias,
                          editedPersonalDetails.Gender_Identity_Id
                        ),
                      ]
                    : [true]
                "
                :itemSelected="editedPersonalDetails.Gender_Identity_Id"
                ref="genderIdentity"
                itemValue="Gender_Identity_Id"
                itemTitle="Gender_Identity"
                :isLoading="dropdownLoading"
                :noDataText="
                  dropdownLoading ? 'Loading...' : 'No data available'
                "
                @selected-item="
                  onChangeCustomSelectField($event, 'Gender_Identity_Id')
                "
              ></CustomSelect>
            </v-col>
            <v-col
              cols="12"
              md="4"
              sm="6"
              v-if="labelList[339].Field_Visiblity == 'Yes'"
            >
              <CustomSelect
                :items="genderExpressionList"
                :label="labelList[339].Field_Alias"
                :isRequired="labelList[339].Mandatory_Field === 'Yes'"
                :rules="
                  labelList[339].Mandatory_Field === 'Yes'
                    ? [
                        required(
                          labelList[339].Field_Alias,
                          editedPersonalDetails.Gender_Expression_Id
                        ),
                      ]
                    : [true]
                "
                :itemSelected="editedPersonalDetails.Gender_Expression_Id"
                ref="genderExpression"
                itemValue="Gender_Expression_Id"
                itemTitle="Gender_Expression"
                :isLoading="dropdownLoading"
                :noDataText="
                  dropdownLoading ? 'Loading...' : 'No data available'
                "
                @selected-item="
                  onChangeCustomSelectField($event, 'Gender_Expression_Id')
                "
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[205].Field_Visiblity == 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                :items="pronounList"
                item-title="Gender_Pronoun_Name"
                item-value="Gender_Pronoun_Name"
                :isLoading="dropdownLoading"
                :label="labelList[205].Field_Alias"
                :itemSelected="editedPersonalDetails.Pronoun"
                ref="pronoun"
                :rules="[
                  labelList[205].Mandatory_Field == 'Yes'
                    ? required(
                        labelList[205].Field_Alias,
                        editedPersonalDetails.Pronoun
                      )
                    : true,
                ]"
                :isRequired="labelList[205].Mandatory_Field === 'Yes'"
                @selected-item="onChangeCustomSelectField($event, 'pronoun')"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[206].Field_Visiblity == 'Yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                :items="genderOrientationList"
                item-title="Gender_Orientations_Name"
                item-value="Gender_Orientations_Name"
                :isLoading="dropdownLoading"
                :label="labelList[206].Field_Alias"
                :itemSelected="editedPersonalDetails.Gender_Orientations"
                ref="Gender_Orientations"
                :rules="[
                  labelList[206].Mandatory_Field == 'Yes'
                    ? required(
                        labelList[206].Field_Alias,
                        editedPersonalDetails.Gender_Orientations
                      )
                    : true,
                ]"
                :isRequired="labelList[206].Mandatory_Field === 'Yes'"
                @selected-item="
                  onChangeCustomSelectField($event, 'Gender_Orientations')
                "
              ></CustomSelect>
            </v-col>
            <!-- Marital Status -->
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="maritalStatusList"
                :label="labelList[398]?.Field_Alias"
                itemValue="Marital_Status_Id"
                itemTitle="Marital_Status"
                :isLoading="maritalStatusListLoading"
                :isRequired="true"
                :rules="[
                  required(
                    labelList[398]?.Field_Alias,
                    editedPersonalDetails.Marital_Status !== null &&
                      editedPersonalDetails.Marital_Status !== undefined
                      ? editedPersonalDetails.Marital_Status + 1
                      : 0
                  ),
                ]"
                clearable
                ref="maritalStatus"
                :itemSelected="editedPersonalDetails.Marital_Status"
                @selected-item="
                  onChangeCustomSelectField($event, 'Marital_Status')
                "
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <section class="text-body-2">
                <v-menu
                  v-model="dobMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                  ><template v-slot:activator="{ props }">
                    <v-text-field
                      ref="dob"
                      v-model="formattedDateOfBirth"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[required('Date of Birth', formattedDateOfBirth)]"
                      readonly
                      v-bind="props"
                      variant="solo"
                    >
                      <template v-slot:label>
                        Date of Birth
                        <span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-if="editedPersonalDetails.DOB"
                    v-model="editedPersonalDetails.DOB"
                    :min="new Date(minimumBirthDate)"
                    :max="new Date(maximumBirthDate)"
                  ></v-date-picker>
                  <v-date-picker
                    v-else
                    v-model="defaultDOB"
                    @update:modelValue="onChangeDefaultDOB(defaultDOB)"
                    :max="new Date(maximumBirthDate)"
                    :min="new Date(minimumBirthDate)"
                  />
                </v-menu>
              </section>
            </v-col>
            <!-- Blood Group -->
            <v-col
              v-if="labelList[400]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                :items="[
                  'A+',
                  'A-',
                  'B+',
                  'B-',
                  'AB+',
                  'AB-',
                  'O+',
                  'O-',
                  'A1B+',
                  'Unknown',
                ]"
                :label="labelList[400].Field_Alias"
                :isRequired="
                  labelList[400].Mandatory_Field.toLowerCase() == 'yes'
                "
                :rules="[
                  labelList[400].Mandatory_Field.toLowerCase() == 'yes'
                    ? required(
                        `${labelList[400].Field_Alias}`,
                        editedPersonalDetails.Blood_Group
                      )
                    : true,
                ]"
                clearable
                ref="bloodGroup"
                :itemSelected="editedPersonalDetails.Blood_Group"
                @selected-item="
                  onChangeCustomSelectField($event, 'Blood_Group')
                "
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <CustomSelect
                :items="nationalityList"
                label="Nationality"
                :isRequired="true"
                itemTitle="nationality"
                itemValue="nationalityId"
                :isAutoComplete="true"
                :rules="[
                  required('Nationality', editedPersonalDetails.Nationality_Id),
                ]"
                :isLoading="nationalityListLoading"
                :itemSelected="editedPersonalDetails.Nationality_Id"
                @selected-item="
                  onChangeCustomSelectField($event, 'Nationality_Id')
                "
                ref="nationalityDropdown"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="
                !editedPersonalDetails.Nationality_Id ||
                editedPersonalDetails.Nationality_Id == 11
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                v-model.trim="editedPersonalDetails.Nationality"
                :rules="[
                  // required(
                  //   'Other  Nationality',
                  //   editedPersonalDetails.Nationality
                  // ),
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.Nationality,
                    'nationality',
                    'Other Nationality'
                  ),
                ]"
                variant="solo"
                ref="nationality"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  Other Nationality<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-if="
                labelList['215'] && labelList['215'].Field_Visiblity === 'Yes'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                v-model.trim="editedPersonalDetails.Aadhaar_Card_Number"
                :label="labelList['215'].Field_Alias"
                :rules="[
                  labelList['215'].Mandatory_Field === 'Yes'
                    ? required(
                        `${labelList['215'].Field_Alias}`,
                        editedPersonalDetails.Aadhaar_Card_Number
                      )
                    : true,
                  alreadyExistErrMsg['Aadhaar_Card_Number'],
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.Aadhaar_Card_Number,
                    'aadhar',
                    `${labelList['215'].Field_Alias}`
                  ),
                ]"
                ref="nationalIdentityNumber"
                @update:model-value="onChangeFields('Aadhaar_Card_Number')"
                @change="
                  validateFieldAlreadyExist(
                    'Aadhaar_Card_Number',
                    `${labelList['215'].Field_Alias}`
                  )
                "
                variant="solo"
                ><template v-slot:label>
                  <span>{{ labelList[215].Field_Alias }}</span>
                  <span
                    v-if="labelList[215].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              v-if="
                labelList['216'] && labelList['216'].Field_Visiblity === 'Yes'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                v-model.trim="editedPersonalDetails.PAN"
                :label="labelList['216'].Field_Alias"
                :rules="[
                  labelList['216'].Mandatory_Field === 'Yes'
                    ? required(
                        `${labelList['216'].Field_Alias}`,
                        editedPersonalDetails.PAN
                      )
                    : true,
                  alreadyExistErrMsg['PAN'],
                  validateWithRulesAndReturnMessages(
                    editedPersonalDetails.PAN,
                    'aadhar',
                    `${labelList['216'].Field_Alias}`
                  ),
                ]"
                variant="solo"
                ref="taxIdentificationNumber"
                @update:model-value="onChangeFields('PAN')"
                @change="
                  validateFieldAlreadyExist(
                    'PAN',
                    `${labelList['216'].Field_Alias}`
                  )
                "
                ><template v-slot:label>
                  <span>{{ labelList[216].Field_Alias }}</span>
                  <span
                    v-if="labelList[216].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              v-if="
                labelList['188'] && labelList['188'].Field_Visiblity === 'Yes'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <CustomSelect
                v-if="labelList['188'].Predefined === 'Yes'"
                :items="taxCodeList"
                :label="labelList['188'].Field_Alias"
                :isRequired="labelList['188'].Mandatory_Field === 'Yes'"
                itemTitle="Tax_Description"
                itemValue="Tax_Code"
                :isAutoComplete="true"
                :isLoading="taxCodeListLoading"
                :itemSelected="editedPersonalDetails.Tax_Code"
                :rules="[
                  labelList['188'].Mandatory_Field === 'Yes'
                    ? required(
                        labelList['188'].Field_Alias,
                        editedPersonalDetails.Tax_Code
                      )
                    : true,
                ]"
                @selected-item="onChangeCustomSelectField($event, 'Tax_Code')"
                ref="taxCodeDropdown"
              ></CustomSelect>
              <v-text-field
                v-else
                v-model.trim="editedPersonalDetails.Tax_Code"
                :label="labelList['188'].Field_Alias"
                :rules="[
                  labelList['188'].Mandatory_Field === 'Yes'
                    ? required(
                        `${labelList['188'].Field_Alias}`,
                        editedPersonalDetails.Tax_Code
                      )
                    : true,
                ]"
                variant="solo"
                ref="taxCode"
                @update:model-value="onChangeFields('Tax_Code')"
                @change="
                  validateFieldAlreadyExist(
                    'Tax_Code',
                    `${labelList['188'].Field_Alias}`
                  )
                "
                ><template v-slot:label>
                  <span>{{ labelList[188].Field_Alias }}</span>
                  <span
                    v-if="labelList[188].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model.trim="editedPersonalDetails.Personal_Email"
                label="Personal Email"
                :rules="
                  checkFieldAvailability(editedPersonalDetails.Personal_Email)
                    ? [
                        validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Personal_Email,
                          'personalEmail',
                          'Personal Email'
                        ),
                      ]
                    : [true]
                "
                variant="solo"
                ref="personalEmail"
                @update:model-value="onChangeFields()"
              ></v-text-field>
            </v-col>
            <v-col
              v-if="
                labelList['219'] && labelList['219'].Field_Visiblity === 'Yes'
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                v-model.trim="editedPersonalDetails.UAN"
                :label="labelList['219'].Field_Alias"
                :rules="[
                  labelList['219'].Mandatory_Field === 'Yes'
                    ? required(
                        `${labelList['219'].Field_Alias}`,
                        editedPersonalDetails.UAN
                      )
                    : true,
                  alreadyExistErrMsg['UAN'],
                  editedPersonalDetails.UAN
                    ? validateWithRulesAndReturnMessages(
                        editedPersonalDetails.UAN,
                        'uan',
                        `${labelList['219'].Field_Alias}`
                      )
                    : true,
                ]"
                variant="solo"
                ref="uan"
                @update:model-value="onChangeFields('UAN')"
                @change="
                  validateFieldAlreadyExist(
                    'UAN',
                    `${labelList['219'].Field_Alias}`
                  )
                "
                ><template v-slot:label>
                  <span>{{ labelList[219].Field_Alias }}</span>
                  <span
                    v-if="labelList[219].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col cols="12">
              <div class="d-flex mb-n6">
                <span class="v-label pr-3 pb-5">Manager</span>
                <v-switch
                  color="primary"
                  v-model="editedPersonalDetails.Is_Manager"
                  :true-value="1"
                  :false-value="0"
                  @update:model-value="onChangeFields()"
                ></v-switch>
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex">
                <span class="v-label pr-3 pb-5">Allow user sign in</span>
                <v-switch
                  color="primary"
                  v-model="editedPersonalDetails.Allow_User_Signin"
                  :true-value="1"
                  :false-value="0"
                  @update:model-value="onChangeFields()"
                ></v-switch>
              </div>
            </v-col>

            <v-col
              v-if="
                editedPersonalDetails.Allow_User_Signin &&
                !entomoIntegrationEnabled
              "
              cols="12"
              md="4"
              sm="6"
            >
              <v-btn-toggle
                v-model="editedPersonalDetails.Enable_Sign_In_With_Mobile_No"
                rounded="lg"
                mandatory
                elevation="2"
                @update:model-value="onChangeFields(val)"
              >
                <v-btn
                  color="primary"
                  style="background-color: white; color: black"
                  >Email Address</v-btn
                >
                <v-btn
                  color="primary"
                  style="background-color: white; color: black"
                  >Mobile Number</v-btn
                ></v-btn-toggle
              >
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" md="4" sm="6">
              <div class="d-flex">
                <span class="v-label pr-3 pb-5 mb-2">More Details</span>
                <v-switch
                  color="primary"
                  v-model="showMoreDetails"
                  class="ml-9"
                  @update:model-value="onChangeFields()"
                ></v-switch>
              </div>
            </v-col>
          </v-row>
          <div v-if="showMoreDetails">
            <v-row>
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model.trim="editedPersonalDetails.Place_Of_Birth"
                  label="Place of Birth"
                  :rules="[
                    checkFieldAvailability(editedPersonalDetails.Place_Of_Birth)
                      ? validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Place_Of_Birth,
                          'placeOfBirth',
                          labelList[301].Field_Alias
                        )
                      : true,
                    labelList[301].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[301].Field_Alias,
                          editedPersonalDetails.Place_Of_Birth
                        )
                      : true,
                  ]"
                  variant="solo"
                  ref="placeOfBirth"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[301].Field_Alias }}
                    <span
                      v-if="labelList[301].Mandatory_Field == 'Yes'"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model.trim="editedPersonalDetails.Emp_Pref_First_Name"
                  :label="labelList[231].Field_Alias"
                  :rules="
                    checkFieldAvailability(
                      editedPersonalDetails.Emp_Pref_First_Name
                    )
                      ? [
                          validateWithRulesAndReturnMessages(
                            editedPersonalDetails.Emp_Pref_First_Name,
                            'knownAs',
                            'Known As'
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  ref="knownAs"
                  @update:model-value="onChangeFields()"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[379]?.Field_Visiblity?.toLowerCase() == 'yes'"
              >
                <CustomSelect
                  :items="languageList"
                  :label="labelList[379].Field_Alias"
                  :isAutoComplete="true"
                  :isLoading="languageListLoading"
                  :noDataText="
                    languageListLoading ? 'Loading...' : 'No data available'
                  "
                  :rules="[
                    labelList[379].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[379].Field_Alias,
                          editedPersonalDetails.Languages
                        )
                      : true,
                  ]"
                  :is-required="labelList[379].Mandatory_Field === 'Yes'"
                  :itemSelected="editedPersonalDetails.Languages"
                  itemValue="Lang_Id"
                  itemTitle="Language_Name"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Languages')
                  "
                  ref="languageKnown"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <v-combobox
                  v-model="hobbiesCombo"
                  label="Hobbies"
                  multiple
                  chips
                  closable-chips
                  :rules="[
                    hobbiesCombo.length > 0
                      ? validateWithRulesAndReturnMessages(
                          hobbies,
                          'hobbies',
                          'Hobbies'
                        )
                      : true,
                  ]"
                  ref="hobbies"
                  variant="solo"
                  @update:modelValue="onChangeFields"
                ></v-combobox>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[302].Field_Visiblity == 'Yes'"
              >
                <CustomSelect
                  :items="religionList"
                  :label="labelList[302].Field_Alias"
                  :isRequired="
                    labelList[302].Mandatory_Field == 'Yes' ? true : false
                  "
                  :rules="[
                    labelList[302].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[302].Field_Alias,
                          editedPersonalDetails.Religion_Id
                        )
                      : true,
                  ]"
                  itemTitle="religion"
                  itemValue="religionId"
                  :isAutoComplete="true"
                  :isLoading="religionListLoading"
                  :itemSelected="editedPersonalDetails.Religion_Id"
                  @selected-item="
                    onChangeCustomSelectField($event, 'Religion_Id')
                  "
                  ref="religionDropdown"
                ></CustomSelect>
              </v-col>
              <v-col
                v-if="
                  !editedPersonalDetails.Religion_Id ||
                  editedPersonalDetails.Religion_Id == 67
                "
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model.trim="editedPersonalDetails.Religion"
                  label="Other Religion"
                  :rules="[
                    checkFieldAvailability(editedPersonalDetails.Religion)
                      ? validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Religion,
                          'religion',
                          'Other Religion'
                        )
                      : true,
                    required('Other Religion', editedPersonalDetails.Religion),
                  ]"
                  variant="solo"
                  ref="religion"
                  @update:model-value="onChangeFields()"
                ></v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[241]?.Field_Visiblity == 'Yes'"
              >
                <v-text-field
                  v-model.trim="editedPersonalDetails.Caste"
                  :label="labelList[241].Field_Alias"
                  variant="solo"
                  :rules="[
                    labelList[241].Mandatory_Field == 'Yes'
                      ? required(
                          labelList[241].Field_Alias,
                          editedPersonalDetails.Caste
                        )
                      : true,
                    checkFieldAvailability(editedPersonalDetails.Caste)
                      ? validateWithRulesAndReturnMessages(
                          editedPersonalDetails.Caste,
                          'caste',
                          labelList[241].Field_Alias
                        )
                      : true,
                  ]"
                  ref="caste"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[241].Field_Alias }}
                    <span
                      v-if="labelList[241].Mandatory_Field == 'Yes'"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <v-text-field
                  v-model.trim="editedPersonalDetails.Ethnic_Race"
                  label="Ethnic Race"
                  variant="solo"
                  :rules="
                    checkFieldAvailability(editedPersonalDetails.Ethnic_Race)
                      ? [
                          validateWithRulesAndReturnMessages(
                            editedPersonalDetails.Ethnic_Race,
                            'ethnicRace',
                            'Ethnic Race'
                          ),
                        ]
                      : [true]
                  "
                  ref="ethnicRace"
                  @update:model-value="onChangeFields()"
                ></v-text-field>
              </v-col>
              <v-col
                v-if="
                  labelList['217'] && labelList['217'].Field_Visiblity === 'Yes'
                "
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model.trim="
                    editedPersonalDetails.Statutory_Insurance_Number
                  "
                  :label="labelList['217'].Field_Alias"
                  variant="solo"
                  :rules="[
                    labelList['217'].Mandatory_Field === 'Yes'
                      ? required(
                          `${labelList['217'].Field_Alias}`,
                          editedPersonalDetails.Statutory_Insurance_Number
                        )
                      : true,
                  ]"
                  ref="statutoryInsuranceNumber"
                  @update:model-value="onChangeFields()"
                  ><template v-slot:label>
                    <span>{{ labelList[217].Field_Alias }}</span>
                    <span
                      v-if="labelList[217].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template></v-text-field
                >
              </v-col>
              <v-col
                v-if="
                  labelList['218'] && labelList['218'].Field_Visiblity === 'Yes'
                "
                cols="12"
                md="4"
                sm="6"
              >
                <v-text-field
                  v-model.trim="editedPersonalDetails.PRAN_No"
                  :label="labelList['218'].Field_Alias"
                  variant="solo"
                  :rules="[
                    labelList['218'].Mandatory_Field === 'Yes'
                      ? required(
                          `${labelList['218'].Field_Alias}`,
                          editedPersonalDetails.PRAN_No
                        )
                      : true,
                  ]"
                  ref="pranNo"
                  @update:model-value="onChangeFields()"
                  ><template v-slot:label>
                    <span>{{ labelList[218].Field_Alias }}</span>
                    <span
                      v-if="labelList[218].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template></v-text-field
                >
              </v-col>
              <v-col
                cols="12"
                v-if="labelList[376]?.Field_Visiblity?.toLowerCase() == 'yes'"
              >
                <div class="d-flex align-center justify-space-between">
                  <div class="text-h6 text-grey-darken-1 font-weight-bold mb-4">
                    Languages Known
                  </div>
                  <v-btn
                    color="primary"
                    class=""
                    variant="text"
                    @click="addMoreLanguages()"
                  >
                    <v-icon class="mr-1" size="15">fas fa-plus</v-icon>
                    Add New
                  </v-btn>
                </div>
                <v-form :ref="'languageForm'">
                  <v-row
                    v-for="(language, index) in moreLanguages"
                    :key="index"
                  >
                    <v-col cols="12" md="4" sm="6">
                      <CustomSelect
                        :ref="`langKnown${index}`"
                        :items="languageList"
                        :label="labelList[376]?.Field_Alias"
                        :isRequired="
                          labelList[376]?.Mandatory_Field?.toLowerCase() ===
                          'yes'
                        "
                        :rules="
                          labelList[376].Mandatory_Field === 'Yes'
                            ? [
                                required(
                                  labelList[376].Field_Alias,
                                  language.Lang_Known
                                ),
                              ]
                            : [true]
                        "
                        clearable
                        :itemSelected="language.Lang_Known"
                        itemValue="Lang_Id"
                        itemTitle="Language_Name"
                        :isLoading="languageListLoading"
                        @selected-item="
                          (language.Lang_Known = $event), onChangeFields()
                        "
                      />
                    </v-col>
                    <v-col
                      cols="12"
                      md="2"
                      sm="3"
                      v-if="labelList[363].Field_Visiblity == 'Yes'"
                    >
                      <div class="text-grey-darken-1" style="font-size: 16px">
                        {{ labelList[363]?.Field_Alias }}
                      </div>
                      <v-switch
                        v-model="language.Lang_Spoken"
                        :true-value="1"
                        :false-value="0"
                        color="primary"
                        class="ml-1"
                        @update:model-value="onChangeFields()"
                      />
                    </v-col>

                    <!-- Read/Write -->
                    <v-col
                      cols="12"
                      md="2"
                      sm="3"
                      v-if="labelList[364].Field_Visiblity == 'Yes'"
                    >
                      <div class="text-grey-darken-1" style="font-size: 16px">
                        {{ labelList[364]?.Field_Alias }}
                      </div>
                      <v-switch
                        v-model="language.Lang_Read_Write"
                        :true-value="1"
                        :false-value="0"
                        color="primary"
                        class="ml-1"
                        @update:model-value="onChangeFields()"
                      />
                    </v-col>
                    <v-col
                      cols="12"
                      md="3"
                      sm="6"
                      v-if="labelList[365]?.Field_Visiblity == 'Yes'"
                    >
                      <CustomSelect
                        v-if="
                          labelList[365]?.Predefined?.toLowerCase() === 'yes'
                        "
                        :items="languageProficiencyList"
                        :label="labelList[365].Field_Alias"
                        :isAutoComplete="true"
                        :isLoading="languageListLoading"
                        :itemSelected="language.Lang_Proficiency"
                        v-model="language.Lang_Proficiency"
                        itemValue="Language_Proficiency"
                        itemTitle="Language_Proficiency"
                        :isRequired="
                          labelList[365].Mandatory_Field?.toLowerCase() ===
                          'yes'
                        "
                        class="flex-grow-1"
                        variant="solo"
                        clearable
                        :rules="[
                          labelList[365]?.Mandatory_Field?.toLowerCase() ===
                          'yes'
                            ? required(
                                labelList[365]?.Field_Alias,
                                language.Lang_Proficiency
                              )
                            : true,
                        ]"
                        :ref="`langProficiency${index}`"
                        @selected-item="
                          onChangeCustomSelectField($event, 'langProficiency')
                        "
                      />
                      <v-text-field
                        v-else
                        v-model="language.Lang_Proficiency"
                        :ref="`langProficiency${index}`"
                        clearable
                        :rules="[
                          labelList[365]?.Mandatory_Field?.toLowerCase() ===
                          'yes'
                            ? required(
                                labelList[365]?.Field_Alias,
                                language.Lang_Proficiency
                              )
                            : true,
                          validateWithRulesAndReturnMessages(
                            language.Lang_Proficiency,
                            'skillName',
                            labelList[365]?.Field_Alias
                          ),
                        ]"
                        class="flex-grow-1"
                        variant="solo"
                        @update:model-value="onChangeFields()"
                      >
                        <template v-slot:label>
                          <span>{{ labelList[365]?.Field_Alias }}</span>
                          <span
                            v-if="
                              labelList[365]?.Mandatory_Field?.toLowerCase() ===
                              'yes'
                            "
                            style="color: red"
                          >
                            *
                          </span>
                        </template>
                      </v-text-field>
                    </v-col>
                    <v-col cols="1" class="d-flex pt-5">
                      <v-icon
                        v-if="moreLanguages.length > 1"
                        size="20"
                        class="fas fa-trash"
                        color="primary"
                        @click="
                          moreLanguages.splice(index, 1), onChangeFields()
                        "
                      />
                    </v-col>
                  </v-row>
                </v-form>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <div>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    Have you ever served in the military?
                  </div>
                  <v-switch
                    color="primary"
                    v-model="editedPersonalDetails.Military_Service"
                    :true-value="1"
                    :false-value="0"
                    @update:model-value="onChangeFields()"
                  ></v-switch>
                </div>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <div>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    Do you identify as a person with a disability?
                  </div>
                  <v-switch
                    color="primary"
                    v-model="editedPersonalDetails.Physically_Challenged"
                    :true-value="1"
                    :false-value="0"
                    class="ml-1"
                    @update:model-value="onChangeFields()"
                  ></v-switch>
                </div>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <div>
                  <div class="text-grey-darken-1" style="font-size: 16px">
                    Are you a smoker?
                  </div>
                  <v-switch
                    color="primary"
                    v-model="editedPersonalDetails.Smoker"
                    :true-value="1"
                    :false-value="0"
                    @update:model-value="onChangeFields()"
                  >
                  </v-switch>
                </div>
              </v-col>
              <v-col
                v-if="editedPersonalDetails.Smoker"
                cols="12"
                md="4"
                sm="6"
              >
                <section class="text-body-2">
                  <v-menu
                    v-model="smokerMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                    ><template v-slot:activator="{ props }">
                      <v-text-field
                        ref="smokerAsOf"
                        v-model="formattedSmoker"
                        prepend-inner-icon="fas fa-calendar"
                        :rules="[required('Smoker As Of', formattedSmoker)]"
                        readonly
                        v-bind="props"
                        variant="solo"
                      >
                        <template v-slot:label>
                          Smoker As Of
                          <span style="color: red">*</span>
                        </template></v-text-field
                      >
                    </template>
                    <v-date-picker
                      v-model="editedPersonalDetails.Smokerasof"
                      :min="new Date(smokerAsOfMin)"
                      :max="new Date(currentDate)"
                    ></v-date-picker>
                  </v-menu>
                </section>
              </v-col>
            </v-row>
          </div>
        </div>
      </v-form>
    </div>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            >Cancel</v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1"
            color="primary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            Save
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_UPDATE_PERSONAL_DETAILS,
  VALIDATE_FIELD_AVAILABILITY,
} from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import {
  LIST_MARITAL_STATUS,
  LIST_NATIONALITIES,
  LIST_RELIGIONS,
  GENDER_LIST,
  TAX_CODE_LIST,
} from "@/graphql/dropDownQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import { VueTelInput } from "vue-tel-input";

export default {
  name: "EditPersonalDetails",

  mixins: [validationRules],
  emits: ["close-edit-form", "edit-updated", "close-add-form"],

  props: {
    personalDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
  },

  components: {
    CustomSelect,
    VueTelInput,
  },

  data() {
    return {
      isMounted: false,
      showMoreDetails: false,
      editedPersonalDetails: {},
      isFormDirty: false,
      //Date-picker
      formattedDateOfBirth: "",
      defaultDOB: "",
      formattedSmoker: "",
      dobMenu: false,
      smokerMenu: false,
      alreadyExistErrMsg: {
        Aadhaar_Card_Number: true,
        PAN: true,
        UAN: true,
      },
      openWarningModal: false,
      // edit
      openBottomSheet: true,
      isLoading: false,
      validationMessages: [],
      hobbiesCombo: [],
      showValidationAlert: false,
      // dropdown list
      languageList: [],
      languageListLoading: false,
      maritalStatusList: [],
      maritalStatusListLoading: false,
      nationalityList: [],
      nationalityListLoading: false,
      religionList: [],
      religionListLoading: false,
      genderList: [],
      genderListLoading: false,
      taxCodeListLoading: false,
      taxCodeList: [],
      genderOrientationList: [],
      languageProficiencyList: [],
      pronounList: [],
      defaultCountryCode: "",
      genderExpressionList: [],
      genderIdentityList: [],
      dropdownLoading: false,
      moreLanguages: [],
    };
  },

  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    smokerAsOfMin() {
      if (
        this.editedPersonalDetails.DOB &&
        this.editedPersonalDetails.DOB !== "0000-00-00"
      ) {
        return moment(this.editedPersonalDetails.DOB).format("YYYY-MM-DD");
      } else return null;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    defaultBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 30);
      minBirthDate.setDate(1);
      const minBirthDateISO = minBirthDate;
      return minBirthDateISO;
    },
    minimumBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 80);
      const minBirthDateISO = minBirthDate;
      return moment(minBirthDateISO).format("YYYY-MM-DD");
    },
    maximumBirthDate() {
      let minBirthDate = new Date();
      minBirthDate.setFullYear(minBirthDate.getFullYear() - 15);
      const minBirthDateISO = minBirthDate;
      return moment(minBirthDateISO).format("YYYY-MM-DD");
    },
    hobbies() {
      return this.hobbiesCombo && this.hobbiesCombo.length > 0
        ? this.hobbiesCombo.join(",")
        : "";
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", val);
    },
    "editedPersonalDetails.DOB": function (val) {
      if (val) {
        this.dobMenu = false;
        this.formattedDateOfBirth = this.formatDate(val);
      }
    },
    "editedPersonalDetails.Smokerasof": function (val) {
      if (val) {
        this.smokerMenu = false;
        this.formattedSmoker = this.formatDate(val);
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.editedPersonalDetails = JSON.parse(
      JSON.stringify(this.personalDetails)
    );
    if (this.editedPersonalDetails.DOB) {
      this.formattedDateOfBirth = this.formatDate(
        this.editedPersonalDetails?.DOB
      );
      this.editedPersonalDetails.DOB = this.editedPersonalDetails.DOB
        ? new Date(this.editedPersonalDetails.DOB)
        : null;
    }
    if (this.editedPersonalDetails.Smokerasof) {
      this.formattedSmoker = this.formatDate(
        this.editedPersonalDetails?.Smokerasof
      );
      this.editedPersonalDetails.Smokerasof = this.editedPersonalDetails
        .Smokerasof
        ? new Date(this.editedPersonalDetails.Smokerasof)
        : null;
    }
    this.hobbiesCombo = this.editedPersonalDetails.Hobbies
      ? this.editedPersonalDetails.Hobbies.split(",")
      : [];
    let langIds = [];
    if (this.editedPersonalDetails?.Languages?.length > 0) {
      this.moreLanguages = this.editedPersonalDetails.Languages.map((lang) => {
        return {
          Lang_Known: lang.Lang_Known,
          Lang_Read_Write: lang.Lang_Read_Write,
          Lang_Proficiency: lang.Lang_Proficiency,
          Lang_Spoken: lang.Lang_Spoken,
        };
      });
    } else {
      this.moreLanguages.push({
        Lang_Known: null,
        Lang_Read_Write: 0,
        Lang_Proficiency: "",
        Lang_Spoken: 0,
      });
    }
    for (var i = 0; i < this.editedPersonalDetails.Languages.length; i++) {
      if (this.editedPersonalDetails.Languages[i])
        langIds.push(this.editedPersonalDetails.Languages[i].Lang_Id);
    }
    this.editedPersonalDetails["Languages"] = langIds;
    this.editedPersonalDetails["Marital_Status"] =
      this.editedPersonalDetails["Marital_Status"] &&
      this.editedPersonalDetails["maritalStatusName"]
        ? parseInt(this.editedPersonalDetails["Marital_Status"])
        : null;
    this.retrieveLanguages();
    this.retrieveMaritalStatusList();
    this.retrieveNationalityList();
    this.retrieveReligionList();
    this.retrieveGender();
    this.retrieveTaxCodeList();
    this.getDropdownDetails();
    this.defaultDOB = new Date(this.defaultBirthDate);
    this.showMoreDetails = true;
    this.isMounted = true;
  },

  methods: {
    getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;
      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 178,
            key: [
              "Gender_Identity_Id",
              "Gender_Expression_Id",
              "gender_orientations",
              "gender_pronoun",
              "language_proficiency",
            ],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "gender_expression_id") {
                vm.genderExpressionList = item.data;
              } else if (item.tableKey?.toLowerCase() === "gender_pronoun") {
                vm.pronounList = item.data.map((dataItem) => ({
                  Gender_Pronoun_Id: dataItem.Gender_Pronoun_Id,
                  Gender_Pronoun_Name: dataItem.Gender_Pronoun_Name,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "gender_orientations"
              ) {
                vm.genderOrientationList = item.data.map((dataItem) => ({
                  Gender_Orientations_Id: dataItem.Gender_Orientations_Id,
                  Gender_Orientations_Name: dataItem.Gender_Orientations_Name,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "language_proficiency"
              ) {
                vm.languageProficiencyList = item.data.map((dataItem) => ({
                  Language_Proficiency_Id: dataItem.Language_Proficiency_Id,
                  Language_Proficiency: dataItem.Language_Proficiency,
                }));
              } else if (
                item.tableKey?.toLowerCase() === "gender_identity_id"
              ) {
                vm.genderIdentityList = item.data;
              }
            });
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "personal details list",
        isListError: false,
      });
    },
    onChangeDefaultDOB(value) {
      this.editedPersonalDetails.DOB = value;
    },
    getCountryCode(countryCode) {
      if (countryCode && countryCode.iso2) {
        this.defaultCountryCode = countryCode.iso2.toLowerCase();
        if (
          this.editedPersonalDetails.Nationality == null &&
          this.editedPersonalDetails.Nationality_Id == null
        ) {
          this.assignNationalityBasedOnCode();
        }
      }
    },
    checkFieldAvailability(value) {
      if (value) {
        let strValue = value.toString();
        return strValue.trim().length > 0;
      } else return false;
    },
    onChangeFields(field = "") {
      this.isFormDirty = true;
      if (field) {
        this.alreadyExistErrMsg[field] = true;
      }
    },
    onChangeCustomSelectField(value, field) {
      if (field == "Religion_Id") {
        this.editedPersonalDetails.Religion = "";
      }
      if (field == "Nationality_Id") {
        this.editedPersonalDetails.Nationality = "";
      }
      this.onChangeFields();
      this.editedPersonalDetails[field] = value;
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.$emit("close-edit-form");
      }
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.editPersonalDetailsForm.validate();
      let langValid = true;
      if (this.labelList[376].Field_Visiblity === "Yes") {
        let { valid } = await this.$refs.languageForm.validate();
        if (!valid) langValid = false;
      }
      mixpanel.track("Onboarded-candidate-personalDetails-edit-submit-click");
      if (isFormValid && isFormValid.valid && langValid) {
        if (
          this.editedPersonalDetails.Nationality_Id &&
          this.editedPersonalDetails.Nationality_Id != 11
        ) {
          let selectedNationality = this.nationalityList.filter(
            (el) =>
              el.nationalityId == this.editedPersonalDetails.Nationality_Id
          );
          this.editedPersonalDetails.Nationality =
            selectedNationality && selectedNationality[0]
              ? selectedNationality[0].nationality
              : "";
        }
        if (
          this.editedPersonalDetails.Religion_Id &&
          this.editedPersonalDetails.Religion_Id != 67
        ) {
          let selectedReligion = this.religionList.filter(
            (el) => el.religionId == this.editedPersonalDetails.Religion_Id
          );
          this.editedPersonalDetails.Religion =
            selectedReligion && selectedReligion[0]
              ? selectedReligion[0].religion
              : "";
        }
        this.updatePersonalDetails();
      } else {
        // Check the validity of each field
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "salutation",
                "gender",
                "genderOrientation",
                "pronoun",
                "maritalStatus",
                "bloodGroup",
                "languageKnown",
                "religionDropdown",
                "nationalityDropdown",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 2, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },

    updatePersonalDetails() {
      let vm = this;
      vm.isLoading = true;
      let genderValue = vm.genderList.filter(
        (el) => el.genderId == vm.editedPersonalDetails.Gender_Id
      );
      let knownLangugaes = vm.editedPersonalDetails.Languages.map((langId) => {
        return {
          Lang_Known: langId,
          Lang_Spoken: null,
          Lang_Read_Write: null,
          Lang_Proficiency: "",
        };
      });
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_PERSONAL_DETAILS,
          variables: {
            candidateId: vm.editedPersonalDetails.Candidate_Id
              ? vm.editedPersonalDetails.Candidate_Id
              : 0,
            userDefinedEmpId: "",
            biometricIntegrationId: "",
            photoPath: vm.editedPersonalDetails.Photo_Path,
            salutation: vm.editedPersonalDetails.Salutation,
            empFirstName: vm.editedPersonalDetails.Emp_First_Name,
            empMiddleName: vm.editedPersonalDetails.Emp_Middle_Name,
            empLastName: vm.editedPersonalDetails.Emp_Last_Name,
            appellation: vm.editedPersonalDetails.Suffix,
            knownAs: vm.editedPersonalDetails.Emp_Pref_First_Name,
            gender: genderValue[0].gender,
            genderId: vm.editedPersonalDetails.Gender_Id,
            dob: moment(vm.editedPersonalDetails.DOB).isValid()
              ? moment(vm.editedPersonalDetails.DOB).format("YYYY-MM-DD")
              : null,
            placeOfBirth: vm.editedPersonalDetails.Place_Of_Birth,
            maritalStatus: vm.editedPersonalDetails.Marital_Status,
            bloodGroup: vm.editedPersonalDetails.Blood_Group,
            languages:
              this.labelList[376]?.Field_Visiblity === "Yes"
                ? this.moreLanguages
                : knownLangugaes,
            militaryService: vm.editedPersonalDetails.Military_Service,
            nationality: vm.editedPersonalDetails.Nationality,
            nationalityId: vm.editedPersonalDetails.Nationality_Id
              ? vm.editedPersonalDetails.Nationality_Id
              : 11,
            religion: vm.editedPersonalDetails.Religion,
            religionId: vm.editedPersonalDetails.Religion_Id
              ? vm.editedPersonalDetails.Religion_Id
              : 67,
            caste: vm.editedPersonalDetails.Caste,
            disabled: vm.editedPersonalDetails.Physically_Challenged,
            isManager: vm.editedPersonalDetails.Is_Manager,
            personalEmail: vm.editedPersonalDetails.Personal_Email,
            smoker: vm.editedPersonalDetails.Smoker,
            smokerAsOf:
              vm.editedPersonalDetails.Smoker &&
              moment(vm.editedPersonalDetails.Smokerasof).isValid()
                ? moment(vm.editedPersonalDetails.Smokerasof).format(
                    "YYYY-MM-DD"
                  )
                : null,
            aadharNumber: vm.editedPersonalDetails.Aadhaar_Card_Number
              ? vm.editedPersonalDetails.Aadhaar_Card_Number.toString()
              : "",
            uan: vm.editedPersonalDetails.UAN,
            pan: vm.editedPersonalDetails.PAN,
            formStatus: 0,
            allowUserSignIn: vm.editedPersonalDetails.Allow_User_Signin,
            enableMobileSignIn:
              vm.editedPersonalDetails.Enable_Sign_In_With_Mobile_No,
            genderOrientations: vm.editedPersonalDetails.Gender_Orientations,
            pronoun: vm.editedPersonalDetails.Pronoun,
            taxCode: vm.editedPersonalDetails?.Tax_Code,
            statutoryInsuranceNumber: vm.editedPersonalDetails
              .Statutory_Insurance_Number
              ? vm.editedPersonalDetails.Statutory_Insurance_Number.toString()
              : "",
            pranNo: vm.editedPersonalDetails.PRAN_No
              ? vm.editedPersonalDetails.PRAN_No.toString()
              : "",
            genderIdentityId: vm.editedPersonalDetails.Gender_Identity_Id,
            genderExpressionId: vm.editedPersonalDetails.Gender_Expression_Id,
          },
          client: "apolloClientW",
        })
        .then((res) => {
          mixpanel.track(
            "Onboarded-candidate-personalDetails-edit-submit-success"
          );
          if (res && res.data && res.data.updatePersonalInfo) {
            const { errorCode, candidateId } = res.data.updatePersonalInfo;
            if (!errorCode) {
              let snackbarData = {
                isOpen: true,
                message: "Personal details updated successfully",
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.isLoading = false;
              vm.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
              vm.$emit("edit-updated", candidateId);
            } else {
              vm.handleUpdateError();
            }
          } else {
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    retrieveTaxCodeList() {
      let vm = this;
      vm.taxCodeListLoading = true;
      vm.$apollo
        .query({
          query: TAX_CODE_LIST,
          client: "apolloClientAS",
        })
        .then((data) => {
          let res = data.data;
          if (
            res.listTimekeepingCareerDetail &&
            res.listTimekeepingCareerDetail.taxDetail
          ) {
            this.taxCodeList = res.listTimekeepingCareerDetail.taxDetail;
          }
          vm.taxCodeListLoading = false;
        })
        .catch(() => {
          vm.taxCodeListLoading = false;
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-personalDetails-edit-submit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "personal details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    async retrieveLanguages() {
      this.languageListLoading = true;
      this.languageList = [];
      await this.$store
        .dispatch("listLanguages")
        .then((langList) => {
          this.languageList = langList;
          this.languageListLoading = false;
        })
        .catch(() => {
          this.languageListLoading = false;
        });
    },

    retrieveNationalityList() {
      let vm = this;
      vm.nationalityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_NATIONALITIES,
          variables: { Org_Code: this.$store.getters.orgCode },
          client: "apolloClientX",
          fetchPolicy: "no-cache",
        })
        .then((data) => {
          let res = data.data;
          if (
            res &&
            res.retrieveNationalityList &&
            res.retrieveNationalityList.nationalityData
          ) {
            this.nationalityList = res.retrieveNationalityList.nationalityData;
            if (
              this.editedPersonalDetails.Nationality_Id == null &&
              this.editedPersonalDetails.Nationality == null
            ) {
              this.assignNationalityBasedOnCode();
            }
          }
          vm.nationalityListLoading = false;
        })
        .catch(() => {
          vm.nationalityListLoading = false;
        });
    },

    assignNationalityBasedOnCode() {
      let nationalityIdList = this.nationalityList.map((ele) => {
        return ele.nationalityId;
      });
      if (this.defaultCountryCode == "in") {
        if (nationalityIdList.includes(12)) {
          this.editedPersonalDetails.Nationality_Id = 12;
          this.editedPersonalDetails.Nationality = "Indian";
        }
      } else if (this.defaultCountryCode == "ph") {
        if (nationalityIdList.includes(3)) {
          this.editedPersonalDetails.Nationality_Id = 3;
          this.editedPersonalDetails.Nationality = "Filipino";
        }
      } else if (this.defaultCountryCode == "id") {
        if (nationalityIdList.includes(5)) {
          this.editedPersonalDetails.Nationality_Id = 5;
          this.editedPersonalDetails.Nationality = "Indonesian";
        }
      } else if (this.defaultCountryCode == "th") {
        if (nationalityIdList.includes(9)) {
          this.editedPersonalDetails.Nationality_Id = 9;
          this.editedPersonalDetails.Nationality = "Thai";
        }
      }
    },

    retrieveReligionList() {
      let vm = this;
      vm.religionListLoading = true;
      vm.$apollo
        .query({
          query: LIST_RELIGIONS,
          variables: { Org_Code: this.$store.getters.orgCode },
          client: "apolloClientX",
          fetchPolicy: "no-cache",
        })
        .then((data) => {
          let res = data.data;
          if (
            res &&
            res.retrieveReligionList &&
            res.retrieveReligionList.religionData
          ) {
            this.religionList = res.retrieveReligionList.religionData;
          }
          vm.religionListLoading = false;
        })
        .catch(() => {
          vm.religionListLoading = false;
        });
    },

    retrieveMaritalStatusList() {
      let vm = this;
      vm.maritalStatusListLoading = true;
      vm.$apollo
        .query({
          query: LIST_MARITAL_STATUS,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveMaritalStatus &&
            !response.data.retrieveMaritalStatus.errorCode
          ) {
            const { maritalStatus } = response.data.retrieveMaritalStatus;
            vm.maritalStatusList = maritalStatus
              ? JSON.parse(maritalStatus)
              : [];
          }
          vm.maritalStatusListLoading = false;
        })
        .catch(() => {
          vm.maritalStatusListLoading = false;
        });
    },

    retrieveGender() {
      let vm = this;
      vm.genderListLoading = true;
      vm.$apollo
        .query({
          query: GENDER_LIST,
          client: "apolloClientX",
          variables: {
            Org_Code: this.orgCode,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveGenderList &&
            !response.data.retrieveGenderList.errorCode
          ) {
            const { genderData } = response.data.retrieveGenderList;
            vm.genderList = genderData;
          }
          vm.genderListLoading = false;
        })
        .catch(() => {
          vm.genderListLoading = false;
        });
    },

    validateFieldAlreadyExist(field, label) {
      let vm = this;
      if (
        vm.editedPersonalDetails[field] &&
        vm.editedPersonalDetails[field] !== vm.personalDetails[field]
      ) {
        vm.$apollo
          .query({
            query: VALIDATE_FIELD_AVAILABILITY,
            client: "apolloClientAC",
            variables: {
              employeeId: 0,
              columnValue: vm.editedPersonalDetails[field],
              columnName: field,
              tableName: "emp_personal_info",
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateCommonAvailability &&
              !response.data.validateCommonAvailability.errorCode
            ) {
              const { isAvailable } = response.data.validateCommonAvailability;
              if (!isAvailable) {
                vm.alreadyExistErrMsg[field] = label + " already exist";
                vm.$refs.editPersonalDetailsForm.validate();
              } else {
                vm.alreadyExistErrMsg[field] = true;
              }
            }
          })
          .catch((err) => {
            vm.alreadyExistErrMsg[field] = true;
            vm.$refs.editPersonalDetailsForm.validate();
            let fieldLabel = label.toLowerCase();
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: fieldLabel,
              isListError: false,
            });
          });
      }
    },
    async addMoreLanguages() {
      let { valid } = await this.$refs.languageForm.validate();
      if (valid) {
        this.moreLanguages.push({
          Lang_Known: null,
          Lang_Proficiency: "",
          Lang_Read_Write: 0,
          Lang_Spoken: 0,
        });
      }
    },
  },
};
</script>
