<template>
  <div class="" style="position: relative">
    <Handle type="target" :position="targetPosition"> </Handle>
    <div
      :class="
        'glow-button' + (!showMenu ? ' glow-button-active' : ' dark-button')
      "
      v-click-outside="() => (showMenu = false)"
      @click="() => (showMenu = !showMenu)"
    >
      <v-icon class="white" size="8">fas fa-plus</v-icon>
    </div>
    <v-expand-x-transition>
      <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
        <MenuItems
          v-if="showMenu"
          @handleProcessNode="handleToStartNode"
        ></MenuItems>
      </div>
    </v-expand-x-transition>
  </div>
</template>

<script>
import MenuItems from "../components/menus/MainMenu.vue";
import { Handle } from "@vue-flow/core";

export default {
  emits: ["handleToStart"],
  name: "ProcessNode",
  data() {
    return {
      showMenu: false,
    };
  },
  components: {
    MenuItems,
    Handle,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    sourcePosition: {
      type: String,
      required: true,
    },
    targetPosition: {
      type: String,
      required: true,
    },
  },
  methods: {
    handleToStartNode(type) {
      this.$emit(
        "handleToStart",
        type,
        { ...this.data, id: "node_1" },
        false,
        0
      );
    },
  },
};
</script>

<style>
.dark-button {
  opacity: 0.2 !important;
}
.glow-button {
  /* padding: 1rem; */
  height: 1.3rem;
  width: 1.3rem;
  border: none;
  border-radius: 3rem;
  background-color: #ec407a;
  color: white;
  font-size: 16px;
  cursor: pointer;
  position: relative;
  justify-content: center;
  align-items: center;
  display: flex;
  margin-top: 0rem;
}

.glow-button-active::before {
  content: "";
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  background: radial-gradient(circle, #fff, transparent 10%);
  border-radius: 20px;
  z-index: -1;
  animation: glow 1s infinite;
  transition: all ease-in-out;
}

@keyframes glow {
  0% {
    box-shadow: 0 0 0 0 #f86999;
  }

  100% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
}
</style>
