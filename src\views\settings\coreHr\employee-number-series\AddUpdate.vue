<template>
  <v-overlay
    :model-value="overlayModel"
    @click:outside="onCloseForm()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? windowWidth <= 800
              ? 'width:100vw; height: 100vh'
              : 'width:60vw; height: 100vh'
            : 'width:40vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div
            class="text-h6 text-medium ps-2 overflow-hidden"
            style="max-width: 90%"
          >
            {{
              isEdit
                ? $t("settings.editEmployeeNumberSeries")
                : $t("settings.addEmployeeNumberSeries")
            }}
          </div>
          <div class="d-flex align-center">
            <v-btn icon class="clsBtn" variant="text" @click="onCloseForm()">
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </div>
        </v-card-title>
        <v-card-text class="overflow-y-auto" style="max-height: 85vh">
          <v-container>
            <v-form ref="addUpdateForm">
              <v-row class="mt-5">
                <v-col
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6 pb-0 mb-2"
                  v-if="coverage == 1"
                >
                  <CustomSelect
                    :label="
                      labelList[115]?.Field_Alias ||
                      $t('settings.serviceProvider')
                    "
                    :items="serviceProviderList"
                    :isLoading="serviceProviderLoading"
                    :isRequired="true"
                    :isAutoComplete="true"
                    item-title="Service_Provider_Name"
                    item-value="Service_Provider_Id"
                    :itemSelected="selectedServiceProvider"
                    @selected-item="
                      (selectedServiceProvider = $event), (isFormDirty = true)
                    "
                    :rules="[
                      required(
                        labelList[115]?.Field_Alias ||
                          $t('settings.serviceProvider'),
                        selectedServiceProvider
                      ),
                    ]"
                  ></CustomSelect>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="prefix"
                    variant="solo"
                    :rules="[
                      maxLengthValidation($t('settings.prefix'), prefix, 10),
                      multilingualNameNumericValidation(
                        $t('settings.prefix'),
                        prefix
                      ),
                    ]"
                    :label="$t('settings.prefix')"
                    @update:model-value="isFormDirty = true"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="suffix"
                    variant="solo"
                    :rules="[
                      maxLengthValidation($t('settings.suffix'), suffix, 10),
                      multilingualNameNumericValidation(
                        $t('settings.suffix'),
                        suffix
                      ),
                    ]"
                    :label="$t('settings.suffix')"
                    @update:model-value="isFormDirty = true"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="noOfDigits"
                    variant="solo"
                    type="number"
                    :rules="[
                      minMaxNumberValidation(
                        $t('settings.noOfDigits'),
                        parseInt(noOfDigits),
                        1,
                        10
                      ),
                    ]"
                    :label="$t('settings.noOfDigits')"
                    @update:model-value="isFormDirty = true"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="nextNumber"
                    variant="solo"
                    type="number"
                    :rules="[
                      minMaxNumberValidation(
                        $t('settings.nextNumber'),
                        parseInt(nextNumber),
                        1,
                        100000000
                      ),
                    ]"
                    :label="$t('settings.nextNumber')"
                    @update:model-value="isFormDirty = true"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                  <div class="v-label mr-4 mb-1">
                    {{ $t("settings.status") }}
                  </div>
                  <AppToggleButton
                    :button-active-text="$t('settings.active')"
                    :button-inactive-text="$t('settings.inactive')"
                    button-active-color="#7de272"
                    button-inactive-color="red"
                    id-value="gab-analysis-based-on"
                    :current-value="status === 'Active' ? true : false"
                    @chosen-value="onChangeStatus($event)"
                  ></AppToggleButton>
                </v-col>
              </v-row>
            </v-form>
            <v-row class="d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  class="mt-16"
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                >
                </MoreDetails>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-sheet
          class="align-center text-center pa-2 position-absolute bottom-0"
          elevation="4"
          style="width: 100%"
        >
          <v-row justify="center">
            <v-col cols="12" class="d-flex justify-end pr-6">
              <v-btn
                rounded="lg"
                class="mr-6 primary"
                @click="onCloseForm()"
                variant="outlined"
              >
                {{ $t("settings.cancel") }}
              </v-btn>
              <v-btn
                rounded="lg"
                class="mr-1 primary"
                @click="validateAddUpdateForm()"
                variant="elevated"
                :disabled="!isFormDirty"
              >
                {{ $t("settings.save") }}
              </v-btn>
            </v-col>
          </v-row>
        </v-sheet>
      </v-card>
      <AppLoading v-if="isLoading"></AppLoading>
    </template>
  </v-overlay>
  <AppWarningModal
    v-if="showWarningModal"
    :open-modal="showWarningModal"
    :confirmation-heading="$t('settings.exitFormWarning')"
    imgUrl="common/exit_form"
    @close-warning-modal="showWarningModal = false"
    @accept-modal="$emit('close-form')"
  >
  </AppWarningModal>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import validationRules from "@/mixins/validationRules";
import { ADD_UPDATE_EMPLOYEE_NUMBER_SERIES } from "@/graphql/settings/core-hr/employeeNumberSeries.js";
import moment from "moment";
export default {
  name: "AddUpdateEmployeeNumberSeries",
  mixins: [validationRules],
  components: {
    MoreDetails,
    CustomSelect,
  },
  emits: ["close-form", "edit-updated"],
  props: {
    overlayModel: {
      type: Boolean,
      default: false,
    },
    selectedItem: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    coverage: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      showWarningModal: false,
      isLoading: false,
      isFormDirty: false,
      openMoreDetails: true,
      moreDetailsList: [],
      serviceProviderList: [],
      serviceProviderLoading: false,
      selectedServiceProvider: null,
      prefix: "",
      suffix: "",
      noOfDigits: "",
      nextNumber: "",
      status: "Active",
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat + " HH:mm:ss") : "";
        } else return "";
      };
    },
  },
  mounted() {
    if (this.isEdit) {
      this.selectedServiceProvider = this.selectedItem.serviceProviderId;
      this.prefix = this.selectedItem.prefix;
      this.suffix = this.selectedItem.suffix;
      this.noOfDigits = this.selectedItem.noOfDigits;
      this.nextNumber = this.selectedItem.nextNumber;
      this.status = this.selectedItem.status;
      this.prefillMoreDetails();
    }
    this.getServiceProviderList();
  },
  methods: {
    onCloseForm() {
      this.showWarningModal = true;
    },
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },
    validateAddUpdateForm() {
      this.$refs.addUpdateForm.validate().then((res) => {
        if (res.valid) {
          this.addUpdateEmployeeNumberSeries();
        }
      });
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.selectedItem.addedOn + ".000Z")
        ),
        addedByName = this.selectedItem.addedByName,
        updatedByName = this.selectedItem.updatedByName,
        updatedOn = this.formatDate(
          new Date(this.selectedItem.updatedOn + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: this.$t("settings.added"),
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: this.$t("settings.updated"),
        });
      }
    },
    getServiceProviderList() {
      this.serviceProviderLoading = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 358 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { serviceProvider } = res.data.getDropDownBoxDetails;
            this.serviceProviderList = serviceProvider;
          }
          this.serviceProviderLoading = false;
        })
        .catch(() => {
          this.serviceProviderList = [];
          this.serviceProviderLoading = false;
        });
    },
    addUpdateEmployeeNumberSeries() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_EMPLOYEE_NUMBER_SERIES,
          variables: {
            empPrefixSettingId: vm.isEdit
              ? vm.selectedItem.empPrefixSettingId
              : 0,
            serviceProviderId: vm.selectedServiceProvider
              ? vm.selectedServiceProvider
              : null,
            prefix: vm.prefix ? vm.prefix : "",
            suffix: vm.suffix ? vm.suffix : "",
            noOfDigits: vm.noOfDigits ? parseInt(vm.noOfDigits) : null,
            nextNumber: vm.nextNumber ? parseInt(vm.nextNumber) : null,
            status: vm.status ? vm.status : null,
            formId: 358,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.isEdit
              ? "Employee ID prefix setting updated successfully."
              : "Employee ID prefix setting added successfully.",
          };
          vm.showAlert(snackbarData);
          vm.$emit("edit-updated");
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEdit ? "updating" : "adding",
        form: "employee number series",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
