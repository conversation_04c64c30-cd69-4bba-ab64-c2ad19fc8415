<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-menu
              v-model="swapDate"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="swapDate"
                  v-model="formatedSwapDate"
                  prepend-inner-icon="fas fa-calendar"
                  prefix="Date: "
                  readonly
                  v-bind="props"
                  density="compact"
                >
                </v-text-field>
              </template>
              <v-date-picker v-model="selectedDate" />
            </v-menu>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="shiftType"
              density="compact"
              :items="shiftTypeList"
              :loading="shiftListLoading"
              item-title="Shift_Name"
              item-value="Shift_Id"
              label="Shift Type"
              single-line
              multiple
              chips
              closable-chips
            ></v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="status"
              density="compact"
              :items="statusList"
              label="Status"
              single-line
              multiple
              chips
              closable-chips
            ></v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import moment from "moment";
import { RETRIEVE_SHIFT_LIST } from "@/graphql/roster-management/ShiftSwapQueries.js";
export default {
  name: "ShiftSwapFilter",
  data: () => ({
    openFormFilter: false,
    // Date Picker
    swapDate: false,
    formatedSwapDate: "",
    selectedDate: null,
    status: [],
    statusList: ["Applied", "Approved", "Rejected"],
    shiftType: null,
    shiftTypeList: [],
    shiftListLoading: false,
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
  },
  watch: {
    selectedDate(val) {
      if (val) {
        this.swapDate = false;
        let dateValue = this.formatDate(val);
        this.formatedSwapDate = dateValue;
      }
    },
  },
  mounted() {
    this.fetchShiftList();
  },
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        selectedDate: this.selectedDate,
        status: this.status,
        shiftType: this.shiftType,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedDate = null;
      this.formatedSwapDate = "";
      this.status = [];
      this.shiftType = [];
    },
    fetchShiftList() {
      let vm = this;
      vm.shiftListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_SHIFT_LIST,
          client: "apolloClientC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.listShiftType &&
            data.listShiftType.shiftType &&
            data.listShiftType.shiftType.length > 0
          ) {
            vm.shiftTypeList = data.listShiftType.shiftType;
          }
          vm.shiftListLoading = false;
        })
        .catch(() => {
          vm.shiftListLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "warning",
            message: "Error while fetching shift list",
          };
          vm.showAlert(snackbarData);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
