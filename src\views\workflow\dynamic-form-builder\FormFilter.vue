<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary darken-1"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              :items="['Individual', 'Vendor']"
              label="Form For"
              :itemSelected="selectedFormFor"
              @selected-item="onChangeSelectField($event, 'selectedFormFor')"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              listWidth="max-width: 300px !important"
            ></CustomSelect>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              :items="['Active', 'Inactive']"
              label="Status"
              :itemSelected="selectedStatus"
              @selected-item="onChangeSelectField($event, 'selectedStatus')"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              listWidth="max-width: 300px !important"
            ></CustomSelect>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { defineComponent } from "vue";
export default defineComponent({
  name: "DynamicFormBuilderFormFilter",

  props: {
    itemList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  components: {
    CustomSelect,
  },
  data: () => ({
    openFormFilter: false,
    selectedStatus: ["Active"],
    selectedFormFor: [],
    filterItemList: [],
  }),

  mounted() {
    this.filterItemList = this.itemList;
    this.fnApplyFilter();
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.filterItemList;

      if (this.selectedFormFor.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedFormFor.includes(item.formFor);
        });
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.status);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedFormFor = [];
      this.selectedStatus = [];
    },
  },
});
</script>
