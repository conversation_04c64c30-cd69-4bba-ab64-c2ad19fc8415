<template>
  <v-card class="rounded-lg mt-2">
    <!-- Header Section -->
    <div
      class="d-flex align-center"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center pl-4 py-2">
        <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
          <v-icon class="text-primary" size="20">fas fa-tasks</v-icon>
        </v-avatar>

        <section style="max-width: 250px" class="text-truncate">
          <div class="text-subtitle-1 font-weight-bold d-flex align-center">
            <!-- Tooltip for gradeCode (only if length > 5) -->
            <template
              v-if="
                selectedFormData.gradeCode &&
                selectedFormData.gradeCode.length > 5
              "
            >
              <v-tooltip location="bottom" :text="selectedFormData.gradeCode">
                <template v-slot:activator="{ props }">
                  <v-card variant="flat" v-bind="props">
                    <span class="d-inline-block">
                      {{ selectedFormData.gradeCode.slice(0, 5) + "..." }}
                    </span>
                  </v-card>
                </template>
              </v-tooltip>
            </template>
            <template v-else>
              <v-card variant="flat">
                <span class="d-inline-block">{{
                  selectedFormData.gradeCode
                }}</span>
              </v-card>
            </template>

            <!-- Dash between gradeCode and grade -->
            <span
              v-if="selectedFormData.gradeCode && selectedItem.grade"
              class="text-bold mx-1"
              >-</span
            >

            <!-- Tooltip for grade (only if length > 20) -->
            <template
              v-if="selectedItem.grade && selectedItem.grade.length > 20"
            >
              <v-tooltip location="bottom" :text="selectedItem.grade">
                <template v-slot:activator="{ props }">
                  <v-card variant="flat" v-bind="props">
                    <span class="d-inline-block">
                      {{ selectedItem.grade.slice(0, 20) + "..." }}
                    </span>
                  </v-card>
                </template>
              </v-tooltip>
            </template>
            <template v-else>
              <v-card variant="flat">
                <span class="d-inline-block">{{ selectedItem.grade }}</span>
              </v-card>
            </template>
          </div>
        </section>
      </div>
      <div class="d-flex align-center">
        <!-- Edit Button -->
        <v-tooltip employeegrades="bottom">
          <template v-slot:activator="{ props }">
            <v-btn
              v-if="accessRights.update"
              v-bind="selectedItem.isAssociated ? props : ''"
              @click="$emit('open-edit-form')"
              size="small"
              color="primary"
              rounded="lg"
              >Edit</v-btn
            >
          </template>
        </v-tooltip>
        <!-- Close Button -->
        <v-icon class="mx-1" color="primary" @click="$emit('close-form')">
          fas fa-times
        </v-icon>
      </div>
    </div>

    <!-- Main Content Section -->
    <div style="height: calc(100vh - 300px); overflow: scroll">
      <v-card-text>
        <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
          <v-col
            v-if="entomoIntegrationEnabled && isEntomoSyncTypePush"
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
          >
            <p class="text-subtitle-1 text-grey-darken-1">Level</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.level) }}
            </p>
          </v-col>

          <!-- Grade Code -->
          <v-col
            v-if="
              entomoIntegrationEnabled ||
              labelList[311]?.Field_Visiblity.toLowerCase() === 'yes'
            "
            cols="12"
            sm="6"
            :class="isMobileView ? ' ml-4' : ''"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[311]?.Field_Alias || "Grade Code" }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.gradeCode) }}
            </p>
          </v-col>

          <!-- Grade Name -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Grade</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.grade }}
            </p>
          </v-col>

          <!-- Minimum Gross Annual Salary -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Minimum Gross Annual Salary
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.minAnnualSalary !== null
                  ? selectedItem.minAnnualSalary
                  : "-"
              }}
            </p>
          </v-col>

          <!-- Maximum Gross Annual Salary -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Maximum Gross Annual Salary
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.maxAnnualSalary !== null
                  ? selectedItem.maxAnnualSalary
                  : "-"
              }}
            </p>
          </v-col>

          <!-- Minimum Hourly Wage -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Minimum Hourly Wage
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.minHourWages !== null
                  ? selectedItem.minHourWages
                  : "-"
              }}
            </p>
          </v-col>

          <!-- Maximum Hourly Wage -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Maximum Hourly Wage
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.maxHourWages !== null
                  ? selectedItem.maxHourWages
                  : "-"
              }}
            </p>
          </v-col>

          <!-- Minimum Overtime Hourly Wage -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Minimum Overtime Hourly Wage
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.minOvertimeWages !== null
                  ? selectedItem.minOvertimeWages
                  : "-"
              }}
            </p>
          </v-col>

          <!-- Maximum Overtime Hourly Wage -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Maximum Overtime Hourly Wage
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.maxOvertimeWages !== null
                  ? selectedItem.maxOvertimeWages
                  : "-"
              }}
            </p>
          </v-col>

          <!-- Parent Grade -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Parent Grade</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.parentGrade) }}
            </p>
          </v-col>

          <!-- Eligible For Overtime -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Overtime Eligibility
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ mapEligibleOvertime(selectedItem.eligibleOvertime) }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Overtime Allocation
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.overTimeAllocation
                  ? selectedItem.overTimeAllocation
                  : "-"
              }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">
              Overtime Fixed Amount
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.overTimeFixedAmount !== null
                  ? selectedItem.overTimeFixedAmount
                  : "-"
              }}
            </p>
          </v-col>

          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Wage Index</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.overTimeWageIndex !== null
                  ? selectedItem.overTimeWageIndex
                  : "-"
              }}
            </p>
          </v-col>

          <!-- Description -->
          <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
            <p class="text-subtitle-1 text-grey-darken-1">Description</p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedFormData.description) }}
            </p>
          </v-col>

          <!-- Additional Details Component -->
          <v-col cols="12">
            <MoreDetails :more-details-list="prefillMoreDetails"></MoreDetails>
          </v-col>
        </v-row>
      </v-card-text>
    </div>
  </v-card>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import moment from "moment";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
export default defineComponent({
  name: "ViewEmployeesGrades",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    moreDetailsList: [],
  }),
  computed: {
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    prefillMoreDetails() {
      let detailsList = [];
      const addedOn = this.formatDate(this.selectedItem.addedOn + ".000Z"),
        addedByName = this.selectedItem.addedByName,
        updatedByName = this.selectedItem.updatedByName,
        updatedOn = this.formatDate(this.selectedItem.updatedOn + ".000Z");
      let addedDateLocal = this.convertUTCToLocal(this.selectedItem.addedOn);
      let updateDateLocal = this.convertUTCToLocal(this.selectedItem.updatedOn);

      if (addedOn && addedByName) {
        detailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
          actionDateLocal: addedDateLocal,
        });
      }
      if (updatedByName && updatedOn) {
        detailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
          actionDateLocal: updateDateLocal,
        });
      }
      return detailsList;
    },
  },

  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);
      },
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    mapEligibleOvertime(value) {
      return value === 1 ? "Yes" : "No";
    },
  },
});
</script>
