/* hide scroll */
div::-webkit-scrollbar{
  width: 0px;
  height: 0px;
}


.custom-scroll-table {
  overflow-x: scroll;
  position: relative;
  scrollbar-color: #c5c5c5 #ebebeb;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
/* space between header and data */ 
tbody:before {
  content:"@";
  display:block;
  line-height:10px;
  text-indent:-99999px;
}

/* shadow while hovering */
.v-data-table tbody tr:hover:not(.v-data-table__expanded__content) {
  box-shadow : 0px 1px 15px rgba(15, 84, 136, 0.14) !important;
}

/* select icon primary color */
.v-data-table .v-icon.v-icon {
    color: rgb(var(--v-theme-primary));
}

/* sorting icon size, alignment, color */
.v-data-table-header__sort-icon{
  font-size: small;
  padding-left: 8px;
  color: grey !important;
}

/* data table background */ 
.v-data-table {
  background: transparent !important;
}

/* table header left padding for first child*/
.v-data-table thead .v-data-table__th th:first-child {
  padding-left: 35px;
}

/* mobile alignment as card */
.v-data-table tbody .v-data-table__mobile-table-row {
  margin: 10px;
  display: flex !important;
  flex-direction: column !important;
}

/* mobile alignment - top corner */
.v-data-table tbody .v-data-table__mobile-table-row td:first-child {
  border-top-right-radius: 1em !important;
  border-top-left-radius: 1em !important;
  border-bottom-left-radius: 0em !important;
}

/* mobile alignment - bottom corner */
.v-data-table tbody .v-data-table__mobile-table-row td:last-child {
  border-top-right-radius: 0px !important;
  border-bottom-left-radius: 1em !important;
  border-bottom-right-radius: 1em !important;
}

/* table headers */
.v-table.v-table--fixed-header
> .v-table__wrapper
> table
> thead
> tr
> th {
background: rgb(var(--v-theme-header)) !important;
color: rgb(var(--v-theme-headerTextColor)) !important;
}

#mobile-header {
  display: none;
}

/* mobile screen alignments */ 
@media screen and (max-width: 600px) {
  thead{
    display: none;
  }
  .v-data-table tbody tr {
    height: auto !important;
  }
  .v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td {
    border-bottom: 0px !important;
  }
  .v-table--density-default > .v-table__wrapper > table > tbody > tr > td {
    min-height: 48px !important;
  }
  .v-data-table-footer__items-per-page > span {
    display: none;
  }
  .v-data-table-footer {
    flex-wrap: nowrap !important;
  }
  .v-data-table-footer__info {
    display: none !important
  }
  #mobile-view-td {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
  }
  #mobile-header {
    display: block;
  }
}
