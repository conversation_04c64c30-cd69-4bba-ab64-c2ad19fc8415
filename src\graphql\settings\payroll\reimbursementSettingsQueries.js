import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const GET_REIMBURSEMENT_SETTINGS = gql`
  query retrieveReimbursementSettings($calledFrom: String!) {
    retrieveReimbursementSettings(calledFrom: $calledFrom) {
      errorCode
      message
      reimbursementSettings {
        Settings_Id
        Enable_Workflow
        Workflow_Id
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;
