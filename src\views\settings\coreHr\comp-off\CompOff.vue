<template>
  <div v-if="isMounted">
    <!-- AppTopBar component is reused here which is already present -->
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="backupCompOffData.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <!-- this the component which has search component and based on prop isFilter filter component is also rendered -->

              <EmployeeDefaultFilterMenu
                v-if="backupCompOffData.length > 0"
                class="justify-end"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <FormFilter
                ref="formFilterRef"
                :items="backupCompOffData"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              >
                <template #bottom-filter-menu>
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="selectedStatus"
                      color="secondary"
                      :items="['Active', 'Inactive']"
                      label="Status"
                      multiple
                      closable-chips
                      chips
                      density="compact"
                      single-line
                      variant="solo"
                    >
                    </v-autocomplete>
                  </v-col>
                </template>
              </FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <!-- Tab body starts from here -->

    <v-container fluid class="comp-off-container">
      <v-window v-model="currentTabItem" v-if="compOffFormAccess">
        <div v-if="showCoverageButton">
          <v-row :class="isMobileView ? 'mt-6 mb-2' : 'ml-3 mb-1'">
            <v-col
              cols="12"
              class="d-flex"
              :class="isMobileView ? 'flex-column mb-2' : ''"
            >
              <div
                class="d-flex"
                :class="isMobileView ? 'flex-column mb-2 mx-auto' : ''"
              >
                <div
                  class="text-subtitle-1 text-grey-darken-1"
                  :class="isMobileView ? 'mx-auto mb-2' : 'mr-5 mt-1'"
                >
                  Comp Off Coverage
                </div>
                <div>
                  <v-btn-toggle
                    v-model="selectedCoverageType"
                    rounded="lg"
                    mandatory
                    density="compact"
                    :disabled="hasActiveStatus"
                    :class="{
                      'cursor-not-allow': hasActiveStatus,
                      'custom-box-shadow': !hasActiveStatus,
                    }"
                    @update:modelValue="onChangeCoverage(selectedCoverageType)"
                  >
                    <v-btn
                      class="text-start text-wrap"
                      color="primary"
                      style="background-color: white; color: black"
                      :size="isMobileView ? 'small' : 'default'"
                      >Organization</v-btn
                    >
                    <v-btn
                      class="text-start text-wrap"
                      color="primary"
                      style="background-color: white; color: black"
                      :size="isMobileView ? 'small' : 'default'"
                      >Custom Group</v-btn
                    ></v-btn-toggle
                  >
                </div>
              </div>
            </v-col>
          </v-row>
        </div>
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading || coverageLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="compOffData.length == 0 && emptyFilterScreen"
            image-name="common/no-records"
            main-title="There are no comp off configuration for the selected filters/searches."
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center">
                <v-btn
                  color="primary"
                  variant="elevated"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click.stop="resetFilter()"
                >
                  Reset Filter/Search
                </v-btn>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchCompOffList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="backupCompOffData.length === 0"
            key="no-results-screen"
          >
            <template #contentSlot>
              <div
                v-if="!AddEditButtonClicked"
                style="max-width: 80%"
                class="mx-auto"
              >
                <v-row
                  v-if="!isLoading"
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                  :class="isMobileView ? 'mt-n16' : ''"
                >
                  <v-col cols="12">
                    <NotesCard
                      heading="Compensatory Off Configuration"
                      imageName=""
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Administrators can efficiently manage compensatory off configurations for employees working during week-offs, holidays, or when putting in extended hours on workdays. To set this up, the software relies on several key business rules. Firstly, compensatory off balances are only acknowledged and updated post the approval of attendance. Additionally, whether an employee is entitled to a half-day or a full-day compensatory leave is based on the regular working hours or a fixed limit defined by the organization. For example, working half the regular hours on a day off might merit a half-day leave, while matching or exceeding regular hours equates to a full day."
                      backgroundColor="transparent"
                      class="mb-2"
                    ></NotesCard>
                    <NotesCard
                      notes="Moreover, if an employee accrues overtime during regular weekdays, those extra hours can be seamlessly converted into compensatory offs. This ensures that any additional effort put in by the employee is aptly rewarded, fostering a sense of fairness and appreciation. Lastly, it's pivotal to note that these compensatory off balances aren't perpetual. Based on the organization's discretion, they either lapse post the monthly payroll process or at the culmination of the calendar year. This mechanism ensures that employees utilize their compensatory offs within a defined period, maintaining a balance between work and leisure."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="In essence, configuring compensatory offs not only simplifies administrative processes but also ensures equitable treatment of employees. By adhering to these guidelines and regularly revisiting the configurations, companies can promote a transparent and motivating work environment."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>

                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="compOffFormAccess.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddEditFormOnEmptyPage()"
                    >
                      <v-icon size="15" class="pr-1 primary"
                        >fas fa-plus</v-icon
                      >
                      Configure Comp Off
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchCompOffList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else-if="showListHistory">
            <v-row>
              <v-col cols="12">
                <CompOffHistory
                  :selectedHistory="selectedItem"
                  :landedFormName="landedFormName"
                  @close-history-view="closeHistoryView()"
                />
              </v-col>
            </v-row>
          </div>
          <div v-else>
            <!-- The div below contains all action buttons -->
            <div
              v-if="!viewButtonClicked"
              class="d-flex flex-wrap align-center"
              :class="isMobileView ? 'flex-column mt-2 mb-n2' : 'mb-2'"
              style="justify-content: space-between"
            >
              <div
                class="d-flex align-center"
                :class="isMobileView ? 'justify-center' : ''"
              >
                <div
                  class="text-grey-darken-1"
                  v-if="compOffFormAccess.update"
                  :class="
                    isMobileView ? 'mx-auto mt-2' : 'text-subtitle-1  mr-5 mt-1'
                  "
                >
                  Comp Off Coverage
                </div>
                <v-tooltip v-model="showToolTip" location="right">
                  <template v-slot:activator="{ props }">
                    <v-btn-toggle
                      v-if="compOffFormAccess.update"
                      v-model="selectedCoverageType"
                      rounded="lg"
                      mandatory
                      v-bind="!hasActiveStatus ? '' : props"
                      density="compact"
                      :disabled="hasActiveStatus"
                      :class="{
                        'cursor-not-allow': hasActiveStatus,
                        'custom-box-shadow': !hasActiveStatus,
                      }"
                      @update:modelValue="
                        onChangeCoverage(selectedCoverageType)
                      "
                    >
                      <v-btn
                        class="text-start text-wrap"
                        color="primary"
                        style="background-color: white; color: black"
                        :size="isMobileView ? 'small' : 'default'"
                        >Organization</v-btn
                      >
                      <v-btn
                        class="text-start text-wrap"
                        color="primary"
                        style="background-color: white; color: black"
                        :size="isMobileView ? 'small' : 'default'"
                        >Custom Group</v-btn
                      ></v-btn-toggle
                    >
                  </template>
                  <div
                    v-if="hasActiveStatus"
                    style="width: 150px !important; height: 150px !important"
                  >
                    There are currently active comp off configurations. To
                    modify the comp off coverage, you must first inactivate
                    these configurations.
                  </div>
                </v-tooltip>
              </div>
              <div
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center' : 'justify-end'"
              >
                <v-tooltip v-model="toolTipForConf" location="right">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      v-bind="!activeWorkDayTypeCount ? '' : props"
                      prepend-icon="fas fa-plus"
                      color="primary rounded-lg"
                      :class="activeWorkDayTypeCount ? 'cursor-not-allow' : ''"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="!activeWorkDayTypeCount ? openAddForm() : ''"
                      v-if="compOffFormAccess.add"
                    >
                      <template v-slot:prepend>
                        <v-icon></v-icon>
                      </template>
                      Add Configuration
                    </v-btn>
                  </template>
                  <div
                    v-if="activeWorkDayTypeCount"
                    style="width: 120px !important; height: 100px !important"
                  >
                    All possible configurations for comp off are available..
                  </div>
                </v-tooltip>
                <v-btn
                  rounded="lg"
                  color="transparent"
                  variant="flat"
                  class="ml-2 mt-1 primary"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchCompOffList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>

                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n2 mr-n5"
                      :size="isMobileView ? 'small' : 'default'"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="exportReportFile()"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
            <v-row>
              <v-col :cols="isSmallTable && !isMobileView ? 5 : 12">
                <v-data-table
                  v-model="selectedData"
                  :headers="headers"
                  :items="compOffData"
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      compOffData,
                      true
                    )
                  "
                  class="elevation-1"
                  style="box-shadow: none !important"
                >
                  <template v-slot:item="{ item, index }">
                    <tr
                      style="z-index: 200"
                      class="data-table-tr bg-white cursor-pointer"
                      @click="openViewForm(item, index)"
                      :class="[
                        isMobileView
                          ? ' v-data-table__mobile-table-row  mt-2'
                          : '',
                      ]"
                    >
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5 font-weight-small'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1"
                        >
                          Salary Type
                        </div>
                        <section
                          style="height: 3em"
                          class="d-flex align-center"
                        >
                          <div class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Configuration_Id ===
                                  item.Configuration_Id
                              "
                              class="data-table-side-border d-flex"
                              style="height: 3em"
                            ></div>
                          </div>
                          <span
                            class="text-primary text-subtitle-1 font-weight-regular"
                          >
                            {{ item.Salary_Type }}
                          </span>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1"
                        >
                          Work Day Type
                        </div>
                        <section>
                          <span class="text-subtitle-1 font-weight-regular">
                            {{ item.Work_Day_Type }}
                          </span>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1"
                        >
                          Comp Off Threshold
                        </div>
                        <section>
                          <span class="text-subtitle-1 font-weight-regular">
                            {{
                              item.Comp_Off_Threshold
                                ? item.Comp_Off_Threshold
                                : "-"
                            }}
                          </span>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1"
                        >
                          Comp Off Expiry Type
                        </div>
                        <section>
                          <span class="text-subtitle-1 font-weight-regular">
                            {{ item.Comp_Off_Expiry_Type }}
                          </span>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1"
                        >
                          Additional Comp Off For Overtime Hours
                        </div>
                        <section>
                          <span class="text-subtitle-1 font-weight-regular">
                            {{
                              item.Comp_Off_Balance_Approval !== "Manual"
                                ? item.Comp_Off_Applicability_For_Overtime_Hours
                                : "-"
                            }}
                          </span>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1"
                        >
                          Custom Group
                        </div>
                        <section>
                          <span
                            class="text-subtitle-1 font-weight-regular"
                            :class="
                              item.Group_Name == null && !isMobileView
                                ? 'ml-10'
                                : ''
                            "
                          >
                            {{ item.Group_Name ? item.Group_Name : "-" }}
                          </span>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1"
                        >
                          Status
                        </div>
                        <section
                          class="d-flex align-center justify-space-between"
                        >
                          <div class="d-flex align-center justify-space-around">
                            <span
                              id="w-80"
                              v-if="item.Status === 'Active'"
                              class="text-green text-subtitle-1 font-weight-regular d-flex justify-center align-center"
                              >{{ item.Status }}</span
                            >
                            <span
                              id="w-80"
                              v-else
                              class="text-red text-subtitle-1 font-weight-regular d-flex justify-center align-center text-center"
                              >{{ item.Status }}</span
                            >
                          </div>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView
                            ? 'd-flex justify-space-between align-center'
                            : 'pa-2 pl-5'
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1"
                        >
                          Actions
                        </div>
                        <section
                          class="d-flex align-center justify-space-between"
                        >
                          <div class="d-flex align-center justify-space-around">
                            <ActionMenu
                              v-if="itemActions(item).length > 0"
                              @selected-action="onActions($event, item)"
                              :accessRights="checkAccess()"
                              :actions="itemActions(item)"
                              :disableActionButtons="[]"
                              :tooltipActionButtons="[]"
                              :isPresentTooltip="true"
                              iconColor="grey"
                            />
                            <div v-else>
                              <p>-</p>
                            </div>
                          </div>
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>

              <v-col
                cols="7"
                v-if="
                  viewButtonClicked && !AddEditButtonClicked && !isMobileView
                "
              >
                <ViewCompOff
                  @close-split-view="closeCompOffViewTab()"
                  @close-edit-form="closeEditForm"
                  @open-edit-form="openEditForm"
                  @update-edited-data="updateData"
                  :selectedItem="selectedItem"
                  :isEdit="isEdit"
                  :coverage="coverage"
                  :access-rights="compOffFormAccess"
                  :compOffData="backupCompOffData"
                />
              </v-col>
              <v-dialog
                class="pl-4"
                v-model="compOffView"
                v-if="
                  isMobileView && viewButtonClicked && !AddEditButtonClicked
                "
                width="900"
                @click:outside="closeCompOffViewTab()"
              >
                <ViewCompOff
                  @close-split-view="closeCompOffViewTab()"
                  @close-edit-form="closeEditForm"
                  @open-edit-form="openEditForm"
                  @update-edited-data="updateData"
                  :selectedItem="selectedItem"
                  :isEdit="isEdit"
                  :coverage="coverage"
                  :access-rights="compOffFormAccess"
                  :compOffData="backupCompOffData"
                />
              </v-dialog>

              <v-col
                cols="7"
                v-if="AddEditButtonClicked && windowWidth >= 1300"
              >
                <AddEditCompOff
                  @close-split-view="closeCompOffViewTab()"
                  @close-edit-form="closeCompOffViewTab()"
                  @save-edited-data="updateData()"
                  :selectedItem="selectedItem"
                  :isEdit="isEdit"
                  :coverage="coverage"
                  @add-data="updateData"
                  :compOffData="backupCompOffData"
                  :editedCompOffDetails="selectedItem"
                />
              </v-col>
              <v-dialog
                class="pl-4"
                v-if="windowWidth < 1300"
                v-model="AddEditButtonClicked"
                width="900"
                @click:outside="closeCompOffViewTab()"
              >
                <AddEditCompOff
                  @close-split-view="closeCompOffViewTab()"
                  @close-edit-form="closeCompOffViewTab()"
                  @add-data="updateData"
                  :selectedItem="selectedItem"
                  :isEdit="isEdit"
                  :coverage="coverage"
                  :compOffData="backupCompOffData"
                  @save-edited-data="updateData()"
                  :editedCompOffDetails="selectedItem"
                />
              </v-dialog>
            </v-row>
          </div>

          <AddEditCompOff
            :class="isMobileView ? 'mx-1' : 'mx-5'"
            :style="
              !isMobileView
                ? 'margin-top: -140px !important;'
                : 'margin-top: -80px !important;'
            "
            v-if="
              compOffData.length == 0 &&
              AddEditButtonClicked &&
              !listLoading &&
              !isMobileView
            "
            @close-split-view="closeCompOffViewTab()"
            @close-edit-form="closeCompOffViewTab()"
            @add-data="updateData"
            :selectedItem="selectedItem"
            :isEdit="isEdit"
            :coverage="coverage"
            :isListEmpty="isListEmpty"
            :compOffData="backupCompOffData"
            @save-edited-data="updateData()"
            :editedCompOffDetails="selectedItem"
          />
          <v-dialog
            :model-value="openFormInModal"
            width="900"
            v-if="
              isMobileView &&
              compOffData.length == 0 &&
              AddEditButtonClicked &&
              !listLoading
            "
          >
            <AddEditCompOff
              @close-split-view="closeCompOffViewTab()"
              @close-edit-form="closeCompOffViewTab()"
              @add-data="updateData"
              :selectedItem="selectedItem"
              :isEdit="isEdit"
              :coverage="coverage"
              @save-edited-data="updateData()"
              :isListEmpty="isListEmpty"
              :compOffData="backupCompOffData"
            />
          </v-dialog>
        </v-window-item>
      </v-window>

      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewCompOff = defineAsyncComponent(() => import("./ViewCompOff.vue"));
const AddEditCompOff = defineAsyncComponent(() =>
  import("./AddEditCompOff.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const CompOffHistory = defineAsyncComponent(() =>
  import("./comp-off-history/CompOffHistory.vue")
);
// Queries
import { RETRIEVE_COMP_OFF_RULES } from "@/graphql/settings/core-hr/compOffQueries";
import FileExportMixin from "@/mixins/FileExportMixin";
import FormFilter from "./FormFilter.vue";
import moment from "moment";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "CompOff",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ViewCompOff,
    AddEditCompOff,
    FormFilter,
    CompOffHistory,
    ActionMenu,
  },
  mixins: [FileExportMixin],
  data: () => ({
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    showToolTip: false,
    isLoading: false,
    isEdit: false,
    viewButtonClicked: false,
    compOffView: false,
    AddEditButtonClicked: false,
    selectedData: [],
    selectedItemIndex: null,
    currentTabItem: "tab-2",
    showRetryBtn: true,
    compOffData: [],
    selectedItem: null,
    selectedCoverageType: 0,
    coverage: "Organization",
    validationMessages: [],
    showValidationAlert: false,
    Coverage_Id: null,
    compOffCoverageItem: [],
    isMounted: false,
    coverageLoading: false,
    emptyFilterScreen: false,
    backupCompOffData: [],
    openMoreMenu: false,
    backupFilterData: [],
    selectedStatus: ["Active"],
    toolTipForConf: false,
    // History
    showListHistory: false,
    havingAccess: {},
  }),
  computed: {
    landedFormName() {
      return "Comp Off";
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
    showCoverageButton() {
      return (
        (this.compOffData.length === 0 &&
          this.AddEditButtonClicked &&
          !this.isMobileView &&
          !this.listLoading) ||
        (this.isListEmpty && this.isMobileView && !this.listLoading)
      );
    },
    isListEmpty() {
      return this.backupCompOffData.length == 0;
    },
    hasActiveStatus() {
      // Check if any entry in compOffData has an active status
      return this.backupCompOffData.some((entry) => entry.Status === "Active");
    },
    compOffFormAccess() {
      let accessFormName = this.landedFormName.replace(/\s/g, "-");
      accessFormName = accessFormName.toLowerCase();
      let compOffAccess = this.accessRights(accessFormName);
      if (
        compOffAccess &&
        compOffAccess.accessRights &&
        compOffAccess.accessRights["view"]
      ) {
        return compOffAccess.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    coreHRFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.coreHRFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    openFormInModal() {
      if (
        (this.AddEditButtonClicked || this.viewButtonClicked) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    isSmallTable() {
      return (
        !this.openFormInModal &&
        (this.AddEditButtonClicked || this.viewButtonClicked)
      );
    },
    headers() {
      if (this.isSmallTable) {
        return [
          {
            title: "Salary Type",
            align: "start",
            key: "Salary_Type",
          },

          {
            title: "Work Day Type",
            key: "Work_Day_Type",
          },
          {
            title: "Comp Off Threshold",
            key: "Comp_Off_Threshold",
          },
        ];
      } else {
        return [
          {
            title: "Salary Type",
            align: "start",
            key: "Salary_Type",
          },

          {
            title: "Work Day Type",
            key: "Work_Day_Type",
          },
          {
            title: "Comp Off Threshold",
            key: "Comp_Off_Threshold",
          },
          {
            title: "Comp Off Expiry Type",
            key: "Comp_Off_Expiry_Type",
          },
          {
            title: "Additional Comp Off For Overtime Hours",
            key: "Comp_Off_Applicability_For_Overtime_Hours",
          },
          {
            title: "Custom Group",
            key: "Group_Name",
          },
          {
            title: "Status",
            key: "Status",
          },
          {
            title: "Actions",
            key: "action",
            align: "center",
            sortable: false,
          },
        ];
      }
    },
    //  logic for configuration, When coverage is organization then all possible combination is 8, and if all combinations are present then we need to show tooltip message that's why this logic implemented like this
    activeWorkDayTypeCount() {
      let count = 0;
      if (this.backupCompOffData.length) {
        this.backupCompOffData.forEach((item) => {
          if (item.Custom_Group_Id === null && item.Status === "Active") {
            count += 1;
          }
        });
      }
      return count == 8;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.refetchCompOffList();
    this.retrieveCompOffCoverage();
    this.isMounted = true;
  },
  methods: {
    itemActions() {
      return ["Edit", "History"];
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action === "History") {
        this.showListHistory = true;
      }
      if (action == "Edit") {
        this.viewButtonClicked = false;
        this.AddEditButtonClicked = true;
        this.compOffView = false;
        this.isEdit = true;
      }
    },
    disableActioMenu() {
      return this.compOffFormAccess?.update ? [] : ["Edit"];
    },
    checkAccess() {
      this.havingAccess["update"] = this.compOffFormAccess?.update ? 1 : 0;
      this.havingAccess["view"] = 1;
      return this.havingAccess;
    },
    closeHistoryView() {
      this.showListHistory = false;
      this.viewButtonClicked = false;
      this.AddEditButtonClicked = false;
      this.compOffView = false;
      this.selectedItem = null;
      this.selectedItemIndex = null;
    },
    resetFilter() {
      this.compOffData = this.backupCompOffData;
      this.emptyFilterScreen = false;
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
      this.selectedStatus = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.applyFilter(this.compOffData);
    },
    applyFilter(filteredArray) {
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Status);
        });
      }
      this.compOffData = filteredArray;
      this.backupFilterData = filteredArray;
      if (this.compOffData.length == 0) {
        this.emptyFilterScreen = true;
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onApplySearch(val) {
      if (!val) {
        this.compOffData = this.backupFilterData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupFilterData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.compOffData = searchItems;
        if (this.compOffData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    formatIntoHoursAndMinutes(item) {
      let hours = Math.floor(item);
      let minutes = Math.round((item - hours) * 60);
      return `${hours} hour(s) ${minutes} min(s)`;
    },
    exportReportFile() {
      let compOffList = this.compOffData.map((item) => ({
        ...item,
      }));
      // Modify the copied array
      compOffList.forEach((item) => {
        if (
          item.Comp_Off_Applicability_For_Overtime_Hours != "Not Applicable" &&
          item.Comp_Off_Balance_Approval == "Automatic"
        ) {
          if (item.Comp_Off_Applicability_For_Overtime_Hours == "Full Day") {
            item.Minimum_OT_Hours_For_Full_Day_Comp_Off =
              this.formatIntoHoursAndMinutes(
                item.Minimum_OT_Hours_For_Full_Day_Comp_Off
              );
          } else if (
            item.Comp_Off_Applicability_For_Overtime_Hours == "Half Day"
          ) {
            item.Minimum_OT_Hours_For_Half_Day_Comp_Off =
              this.formatIntoHoursAndMinutes(
                item.Minimum_OT_Hours_For_Half_Day_Comp_Off
              );
          } else {
            item.Minimum_OT_Hours_For_Full_Day_Comp_Off =
              this.formatIntoHoursAndMinutes(
                item.Minimum_OT_Hours_For_Full_Day_Comp_Off
              );
            item.Minimum_OT_Hours_For_Half_Day_Comp_Off =
              this.formatIntoHoursAndMinutes(
                item.Minimum_OT_Hours_For_Half_Day_Comp_Off
              );
          }
        }
        if (item.Comp_Off_Threshold == "Fixed Hours") {
          item.Fixed_Regular_Hours = this.formatIntoHoursAndMinutes(
            item.Fixed_Regular_Hours
          );
        }
        item.Added_On = this.formatDate(new Date(item.Added_On + ".000Z"));
        item.Updated_On = item.Updated_On
          ? this.formatDate(new Date(item.Updated_On + ".000Z"))
          : "";
      });

      let fileName = "Comp Off";
      let exportHeaders = [
        { header: "Salary Type", key: "Salary_Type" },
        { header: "Work Day Type", key: "Work_Day_Type" },
        { header: "Custom Group", key: "Group_Name" },
        {
          header: "Comp Off Balance Accrual",
          key: "Comp_Off_Balance_Approval",
        },
        { header: "Comp Off Threshold", key: "Comp_Off_Threshold" },
        { header: "Fixed Regular Hours", key: "Fixed_Regular_Hours" },
        {
          header: "Allow Half Day Comp Off Credit",
          key: "Allow_Half_Day_Comp_Off_Credit",
        },
        { header: "Comp Off Expiry Type", key: "Comp_Off_Expiry_Type" },
        { header: "Comp Off Expiry After", key: "Comp_Off_Expiry_Days" },
        {
          header: "Additional Comp Off For Overtime Hours",
          key: "Comp_Off_Applicability_For_Overtime_Hours",
        },
        {
          header: "Minimum Overtime Hours For Full Day Comp Off",
          key: "Minimum_OT_Hours_For_Full_Day_Comp_Off",
        },
        {
          header: "Minimum Overtime Hours For Half Day Comp Off",
          key: "Minimum_OT_Hours_For_Half_Day_Comp_Off",
        },
        { header: "Comp Off Encashment", key: "Comp_Off_Encashment" },
        { header: "Encashment Mode", key: "Encashment_Mode" },
        { header: "Status", key: "Status" },
        { header: "Added By", key: "Added_By" },
        { header: "Added On", key: "Added_On" },
        { header: "Update By", key: "Updated_By" },
        { header: "Update On", key: "Updated_On" },
      ];
      let exportOptions = {
        fileExportData: compOffList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    closeEditForm() {
      this.isEdit = false;
    },
    openEditForm() {
      this.isEdit = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    updateData() {
      this.viewButtonClicked = false;
      this.compOffView = false;
      this.AddEditButtonClicked = false;
      this.refetchCompOffList();

      let snackbarData = {
        isOpen: true,
        message: this.isEdit
          ? "Comp off configuration updated successfully"
          : "Comp off configuration added successfully",
        type: "success",
      };
      this.showAlert(snackbarData);
    },

    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.coreHRFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/core-hr/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + clickedForm.url;
        }
      }
    },

    openViewForm(item, index) {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.selectedItemIndex = index;
      this.selectedItem = item;
      this.viewButtonClicked = true;
      this.compOffView = true;
      this.AddEditButtonClicked = false;
      this.isEdit = false;
    },
    openAddForm() {
      this.AddEditButtonClicked = true;
      this.viewButtonClicked = true;
      this.isEdit = false;
    },
    openAddEditFormOnEmptyPage() {
      this.AddEditButtonClicked = true;
      this.isEdit = false;
    },
    closeCompOffViewTab() {
      this.selectedItemIndex = null;
      this.selectedItem = null;
      this.viewButtonClicked = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.compOffView = false;
      this.AddEditButtonClicked = false;
    },
    fetchCompOffDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_COMP_OFF_RULES,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveCompoffRules) {
            vm.backupCompOffData =
              response.data.retrieveCompoffRules.CompoffRules;
            vm.compOffData = vm.backupCompOffData;
            if (vm.compOffData && vm.compOffData.length) {
              vm.applyFilter(vm.compOffData);
            }
          } else {
            vm.handleListError(err, "comp off");
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err, "comp off");
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form:
            formName === "coverage"
              ? "comp off coverage"
              : "comp off configuration",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    async retrieveCompOffCoverage() {
      let vm = this;
      vm.coverageLoading = true;
      if (this.compOffFormAccess) {
        await this.$store
          .dispatch("retrieveFormLevelCoverage", {
            formName: "Comp Off",
            Form_Id: 250,
          })
          .then((response) => {
            if (response) {
              vm.compOffCoverageItem = response;
              vm.coverage = vm.compOffCoverageItem[0].Coverage;
              vm.Coverage_Id = vm.compOffCoverageItem[0].Coverage_Id;
              if (vm.coverage == "Organization") {
                vm.selectedCoverageType = 0;
              } else {
                vm.selectedCoverageType = 1;
              }
            } else {
              vm.listLoading = false;
            }
            vm.coverageLoading = false;
          })
          .catch(() => {
            vm.coverageLoading = false;
            vm.listLoading = false;
          });
      }
    },
    async onChangeCoverage(value) {
      let vm = this;
      vm.listLoading = true;
      let changedCoverage;
      if (value == 1) {
        changedCoverage = "Custom Group";
      } else {
        changedCoverage = "Organization";
      }
      try {
        await vm.$store
          .dispatch("updateFormLevelCoverage", {
            Coverage: changedCoverage,
            Coverage_Id: vm.Coverage_Id,
            formName: "Comp Off",
          })
          .then(() => {
            vm.coverage = changedCoverage;
            vm.listLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Comp off coverage updated successfully.",
            };
            vm.showAlert(snackbarData);
          })
          .catch(() => {
            if (value == 1) {
              vm.selectedCoverageType = 0;
            } else {
              vm.selectedCoverageType = 1;
            }
            vm.listLoading = false;
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "comp off coverage",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    refetchCompOffList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.fetchCompOffDetails();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
  },
};
</script>

<style scoped>
.comp-off-container {
  padding: 5em 2em 0em 3em;
}
.custom-box-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .comp-off-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
