<template>
  <div>
    <v-card v-if="!isEdit" class="rounded-lg">
      <div
        v-if="!showEmployeesList"
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="primary" size="20">fas fa-hand-holding-usd </v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              {{
                formName.toLowerCase() == "compoffhistory"
                  ? "Comp Off History"
                  : "Comp Off"
              }}
            </div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-tooltip v-model="showToolTip" location="right">
            <template v-slot:activator="">
              <v-btn
                @click="$emit('open-edit-form')"
                size="small"
                color="primary"
                variant="elevated"
                rounded="lg"
                v-if="accessRights?.update"
                >Edit</v-btn
              >
            </template>
          </v-tooltip>

          <v-icon
            class="mx-1"
            color="primary"
            @click="$emit('close-split-view')"
          >
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card v-if="!showEmployeesList">
        <div
          style="overflow: scroll"
          :style="
            isMobileView
              ? 'height: calc(100vh - 200px)'
              : 'height: calc(100vh - 260px)'
          "
        >
          <v-card-text>
            <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Salary Type</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Salary_Type }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Work Day Type</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Work_Day_Type }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="editedCompOffDetails.Custom_Group_Id"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Custom Group</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Group_Name }}
                </p>
              </v-col>
              <v-col
                v-if="editedCompOffDetails.Custom_Group_Id"
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <div v-if="isLoadingCard">
                  <v-skeleton-loader
                    type="list-item-two-line"
                    class="ml-n4 mt-n2"
                    width="80%"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >Employees - {{ empListInSelectedGroup.length }}</span
                  >
                  <div
                    v-if="empListInSelectedGroup.length === 0"
                    class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  >
                    <v-icon color="warning" size="25"
                      >fas fa-exclamation-triangle</v-icon
                    >
                    <span
                      v-if="errorInFetchEmployeesList"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                      >Something went wrong while fetching the employees list.
                      Please try again.
                      <a class="text-primary" @click="fetchCustomEmployeesList"
                        >Refresh
                      </a>
                    </span>
                    <span
                      v-else-if="isNoEmployees"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                    >
                      It seems like there are no employees associated with the
                      selected custom group. Please add some employees under the
                      selected group or try choosing an another group.</span
                    >
                  </div>
                  <div v-else class="d-flex align-center">
                    <AvatarOrderedList
                      v-if="empListInSelectedGroup.length > 0"
                      class="mt-1"
                      :ordered-list="empListInSelectedGroup"
                    ></AvatarOrderedList>
                    <v-btn
                      rounded="lg"
                      color="primary"
                      size="small"
                      class="mt-1"
                      @click="openCustomGroupEmpList()"
                    >
                      View All
                    </v-btn>
                  </div>
                </div>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedCompOffDetails.Work_Day_Type !==
                  'Extra Work Hours(Weekday)'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Comp Off Threshold
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Comp_Off_Threshold }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedCompOffDetails.Work_Day_Type !==
                    'Extra Work Hours(Weekday)' &&
                  editedCompOffDetails.Comp_Off_Threshold == 'Fixed Hours'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Minimum regular hours for full day comp off
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    fixedRegularHours +
                    " hour(s) " +
                    fixedRegularMinutes +
                    " min(s)"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedCompOffDetails.Work_Day_Type !==
                  'Extra Work Hours(Weekday)'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Allow Half Day Comp Off Credit
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Allow_Half_Day_Comp_Off_Credit }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedCompOffDetails.Allow_Half_Day_Comp_Off_Credit ==
                    'Yes' &&
                  editedCompOffDetails.Comp_Off_Threshold == 'Fixed Hours'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Minimum regular hours for half a day comp off
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    fixedRegularHalfDayHours +
                    " hour(s) " +
                    fixedRegularHalfDayMinutes +
                    " min(s)"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Comp Off Expiry Type
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Comp_Off_Expiry_Type }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="editedCompOffDetails.Comp_Off_Expiry_Type == 'Fixed Days'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Comp Off Expiry After
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Comp_Off_Expiry_Days + " day(s)" }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Additional Comp Off For Overtime Hours
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    editedCompOffDetails.Comp_Off_Applicability_For_Overtime_Hours
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedCompOffDetails.Comp_Off_Applicability_For_Overtime_Hours ==
                    'Full Day' ||
                  editedCompOffDetails.Comp_Off_Applicability_For_Overtime_Hours ==
                    'Both Full Day & Half Day'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Minimum Overtime Hours For Full Day Comp Off
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ fullDayHours + " hour(s) " + fullDayMinutes + " min(s)" }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedCompOffDetails.Comp_Off_Applicability_For_Overtime_Hours ==
                    'Half Day' ||
                  editedCompOffDetails.Comp_Off_Applicability_For_Overtime_Hours ==
                    'Both Full Day & Half Day'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Minimum Overtime Hours For Half Day Comp Off
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ halfDayHours + " hour(s) " + halfDayMinutes + " min(s)" }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedCompOffDetails.Comp_Off_Applicability_For_Overtime_Hours !==
                  'Not Applicable'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Comp Off Balance Accrual
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(
                      editedCompOffDetails.Comp_Off_Balance_Approval
                    )
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Comp Off Encashment
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Comp_Off_Encashment }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="editedCompOffDetails.Comp_Off_Encashment == 'Yes'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Encashment Mode
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Encashment_Mode }}
                </p>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Status</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedCompOffDetails.Status }}
                </p>
              </v-col>
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-card-text>
        </div>
      </v-card>
      <div
        v-if="showEmployeesList"
        style="overflow: scroll"
        :style="
          isMobileView
            ? 'height: calc(100vh - 200px)'
            : 'height: calc(100vh - 200px)'
        "
      >
        <div class="d-flex ma-2">
          <v-btn
            rounded="lg"
            color="primary"
            @click="showEmployeesList = false"
          >
            <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
            Back
          </v-btn>
        </div>
        <EmployeeListCard
          v-if="showEmployeesList"
          :show-modal="showEmployeesList"
          modal-title="Custom Group Employee(s)"
          :employeesList="empListForComponent"
          :selectable="false"
          :showFilter="false"
          :showFilterSearch="true"
          :isApplyFilter="true"
          @close-modal="showEmployeesList = false"
        ></EmployeeListCard>
      </div>
    </v-card>

    <div v-if="isEdit">
      <AddEditCompOff
        :editedCompOffDetails="editedCompOffDetails"
        @close-edit-form="closeEditForm"
        @save-edited-data="saveEditedCompOffDetails"
        v-if="windowWidth > 1300"
        :isEdit="isEdit"
        :coverage="coverage"
        :compOffData="compOffData"
      />
      <v-dialog
        v-else
        class="pl-4"
        v-model="displayEdit"
        width="900"
        @close-edit-form="closeEditForm"
        @save-edited-data="saveEditedCompOffDetails"
      >
        <AddEditCompOff
          :editedCompOffDetails="editedCompOffDetails"
          @close-edit-form="closeEditForm"
          @save-edited-data="saveEditedCompOffDetails"
          :isEdit="isEdit"
          :coverage="coverage"
          :compOffData="compOffData"
        />
      </v-dialog>
    </div>
  </div>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import { defineAsyncComponent } from "vue";
const AddEditCompOff = defineAsyncComponent(() =>
  import("./AddEditCompOff.vue")
);
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import EmployeeListCard from "@/components/helper-components/EmployeeListCard.vue";
import { checkNullValue } from "@/helper.js";
export default {
  name: "ViewCompOff",
  components: {
    MoreDetails,
    AddEditCompOff,
    EmployeeListCard,
    AvatarOrderedList,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    isEdit: {
      type: Boolean,
      required: true,
    },
    isHistory: {
      type: Boolean,
      required: false,
    },
    accessRights: {
      type: Object,
      required: true,
    },
    coverage: {
      type: String,
      required: true,
    },
    compOffData: {
      type: Array,
      required: false,
    },
    formName: {
      type: String,
      required: false,
      default: "compoff",
    },
  },
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    editedCompOffDetails: {},
    displayEdit: true,
    fullDayHours: 0, // Variable for full day hours
    fullDayMinutes: 0, // Variable for full day minutes
    halfDayHours: 0, // Variable for half day hours
    halfDayMinutes: 0, // Variable for half day minutes
    fixedRegularHours: 0, // Variable for fixed regular hours
    fixedRegularMinutes: 0, // Variable for fixed regular minutes
    fixedRegularHalfDayHours: 0, // Variable for fixed regular minutes
    fixedRegularHalfDayMinutes: 0, // Variable for fixed regular minutes
    showToolTip: false,
    showEmployeesList: false,
    empListInSelectedGroup: [],
    isNoEmployees: false,
    isLoadingCard: false,
    errorInFetchEmployeesList: false,
  }),
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.editedCompOffDetails = Object.assign({}, newData);
        // Convert Minimum_OT_Hours_For_Full_Day_Comp_Off to hours and minutes
        this.fullDayHours = Math.floor(
          newData.Minimum_OT_Hours_For_Full_Day_Comp_Off
        );
        this.fullDayMinutes = Math.round(
          (newData.Minimum_OT_Hours_For_Full_Day_Comp_Off - this.fullDayHours) *
            60
        );

        // Convert Minimum_OT_Hours_For_Half_Day_Comp_Off to hours and minutes
        this.halfDayHours = Math.floor(
          newData.Minimum_OT_Hours_For_Half_Day_Comp_Off
        );
        this.halfDayMinutes = Math.round(
          (newData.Minimum_OT_Hours_For_Half_Day_Comp_Off - this.halfDayHours) *
            60
        );

        // Convert Fixed_Regular_Hours to hours and minutes
        this.fixedRegularHours = Math.floor(newData.Fixed_Regular_Hours);
        this.fixedRegularMinutes = Math.round(
          (newData.Fixed_Regular_Hours - this.fixedRegularHours) * 60
        );
        this.fixedRegularHalfDayHours = Math.floor(
          newData.Minimum_Hours_For_Half_Day_Comp_Off
        );
        this.fixedRegularHalfDayMinutes = Math.round(
          (newData.Minimum_Hours_For_Half_Day_Comp_Off -
            this.fixedRegularHalfDayHours) *
            60
        );
        this.prefillMoreDetails();
        this.fetchCustomEmployeesList();
        this.showEmployeesList = false;
      },
    },
    isEdit() {
      this.displayEdit = this.isEdit;
    },
  },
  computed: {
    isCustomGroupToolTip() {
      return (
        (this.coverage === "Organization" &&
          this.editedCompOffDetails.Custom_Group_Id !== null) ||
        (this.coverage === "Custom Group" &&
          this.editedCompOffDetails.Custom_Group_Id === null)
      );
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat + " HH:mm:ss") : "";
        }
      };
    },
  },
  mounted() {
    this.displayEdit = this.isEdit;
  },
  methods: {
    checkNullValue,
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    closeEditForm() {
      this.showEmployeesList = false;
      this.$emit("close-edit-form");
    },
    saveEditedCompOffDetails() {
      this.$emit("update-edited-data");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      let Added_On = "",
        Added_By = "",
        Updated_By = "",
        Updated_On = "";

      // to form more details array based on these values
      if (!this.isHistory) {
        Added_On = this.formatDate(
          new Date(this.selectedItem.Added_On + ".000Z")
        );
        Added_By = this.selectedItem.Added_By;
        Updated_By = this.selectedItem.Updated_By;
        Updated_On = this.formatDate(
          new Date(this.selectedItem.Updated_On + ".000Z")
        );
      } else {
        Updated_By = this.selectedItem.Added_By_Name;
        Updated_On = this.formatDate(
          new Date(this.selectedItem.Added_On + ".000Z")
        );
      }

      // Push to moreDetailsList if conditions are met
      if (Added_On && Added_By) {
        this.moreDetailsList.push({
          actionDate: Added_On,
          actionBy: Added_By,
          text: "Added",
        });
      }
      if (Updated_By && Updated_On) {
        this.moreDetailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By,
          text: "Updated",
        });
      }
    },
    // prefillMoreDetails() {
    //   this.moreDetailsList = [];
    //   // to form more details array based on this values
    //   if (this.isEdit) {
    //     const Added_On = this.formatDate(
    //         new Date(this.selectedItem.Added_On + ".000Z")
    //       ),
    //       Added_By = this.selectedItem.Added_By,
    //       Updated_By = this.selectedItem.Updated_By,
    //       Updated_On = this.formatDate(
    //         new Date(this.selectedItem.Updated_On + ".000Z")
    //       );
    //   } else {
    //     const Added_On = ""
    //       Added_By = "",
    //       Updated_By = this.selectedItem.Added_By_Name,
    //       Updated_On = this.formatDate(
    //         new Date(this.selectedItem.Added_On + ".000Z")
    //       );
    //   }
    //   if (Added_On && Added_By) {
    //     this.moreDetailsList.push({
    //       actionDate: Added_On,
    //       actionBy: Added_By,
    //       text: "Added",
    //     });
    //   }
    //   if (Updated_By && Updated_On) {
    //     this.moreDetailsList.push({
    //       actionDate: Updated_On,
    //       actionBy: Updated_By,
    //       text: "Updated",
    //     });
    //   }
    // },
    // on changing the custom group we need to fetch the employees list relevant to the selected group
    async fetchCustomEmployeesList() {
      if (this.editedCompOffDetails.Custom_Group_Id) {
        let vm = this;
        vm.isLoadingCard = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(vm.editedCompOffDetails.Custom_Group_Id),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
  },
};
</script>
