<template>
  <v-overlay
    :model-value="showRecruitmentForm"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card"
        :style="windowWidth >= 1264 ? '' : 'width: 100vw'"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">
            {{
              selectedRecruitment && selectedRecruitment.Original_Position_Id
                ? "Edit"
                : "Add"
            }}
            Approved & Forecasted Positions
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <v-form ref="recruitmentRefForm">
            <v-row class="pa-6">
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  label="Position Title"
                  ref="positionTitle"
                  :item-selected="selectedPositionTitle"
                  v-model="selectedPositionTitle"
                  item-title="Pos_full_Name"
                  item-value="Originalpos_Id"
                  :items="positionList"
                  :is-loading="positionListLoading"
                  :isRequired="true"
                  :isAutoComplete="true"
                  clearable
                  :disabled="
                    !!(
                      selectedRecruitment &&
                      selectedRecruitment.Original_Position_Id
                    ) || positionListLoading
                  "
                  :rules="[required('Position Title', selectedPositionTitle)]"
                  @update:model-value="onChangePosition()"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="selectedGroup.fullName"
                  variant="solo"
                  label="Group"
                  readonly
                  @update:model-value="onChange()"
                >
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="selectedDivision.fullName"
                  variant="solo"
                  label="Division"
                  @update:model-value="onChange()"
                  readonly
                >
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="selectedDepartment.fullName"
                  label="Department"
                  variant="solo"
                  @update:model-value="onChange()"
                  readonly
                >
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="selectedSection.fullName"
                  variant="solo"
                  label="Section"
                  @update:model-value="onChange()"
                  readonly
                >
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="selectedCostCenter"
                  variant="solo"
                  label="Cost Center"
                  @update:model-value="onChange()"
                  readonly
                >
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  v-model="selectedReasonVacancy"
                  :items="reasonVacancyList"
                  label="Reason for Replacement"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :clearable="true"
                  :rules="[
                    required('Reason for Replacement', selectedReasonVacancy),
                  ]"
                  :itemSelected="selectedReasonVacancy"
                  @selected-item="isFormDirty = true"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  v-model="selectedPositionLevel"
                  :items="positionLevelList"
                  label="Position Level"
                  item-title="Position_Level"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :clearable="true"
                  :rules="[required('Position Level', selectedPositionLevel)]"
                  :itemSelected="selectedPositionLevel"
                  item-value="Position_Level_Id"
                  :is-loading="positionLevelLoading"
                  @selected-item="isFormDirty = true"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  label="Employee Type"
                  :item-selected="selectedEmployeeType"
                  v-model="selectedEmployeeType"
                  item-title="Employee_Type"
                  item-value="EmpType_Id"
                  :items="employeeTypeList"
                  :isAutoComplete="true"
                  :isRequired="true"
                  clearable
                  ref="employeeType"
                  :rules="[required('Employee Type', selectedEmployeeType)]"
                  @update:model-value="onChange()"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="numberOfPositions"
                  ref="noOfPositions"
                  :rules="[
                    numericRequiredValidation(
                      'No of Positions',
                      numberOfPositions
                    ),
                    numericValidation('No of Positions', numberOfPositions),
                    !recruitmentSettingEnabled
                      ? selectedRecruitment &&
                        selectedRecruitment.Original_Position_Id
                        ? noOfPositionsValidation(
                            numberOfPositions,
                            selectedRecruitmentFormData?.approvedPosition,
                            selectedRecruitmentFormData?.warmBodies,
                            selectedRecruitmentFormData?.totalRecruitmentCount,
                            true,
                            selectedRecruitment.No_Of_Position
                          )
                        : noOfPositionsValidation(
                            numberOfPositions,
                            selectedRecruitmentFormData?.approvedPosition,
                            selectedRecruitmentFormData?.warmBodies,
                            selectedRecruitmentFormData?.totalRecruitmentCount,
                            false,
                            0
                          )
                      : true,
                    minMaxNumberValidation(
                      'No of Positions',
                      numberOfPositions,
                      1,
                      500
                    ),
                  ]"
                  @update:model-value="onChange()"
                  :isRequired="true"
                  variant="solo"
                  max="500"
                  maxLength="3"
                  ><template v-slot:label>
                    No of Positions
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 mt-n5">
                <p class="text-subtitle-1 text-grey-darken-1 pl-3">
                  Replacement For
                  <span style="color: red">*</span>
                </p>
                <v-text-field
                  ref="inter"
                  v-model="input"
                  @update:modelValue="showAddIcon"
                  variant="solo"
                  :rules="[
                    multilingualNameValidation(input),
                    required('Replacement For', reasonReplacement[0]),
                    maxLengthValidation('Replacement For', input, 1500),
                  ]"
                  @keydown.enter.prevent="addChip"
                >
                  <template v-slot:default>
                    <v-icon v-if="showIcon" @click="addChip" size="x-small"
                      >fas fa-plus</v-icon
                    >
                    <v-chip
                      v-for="(chip, index) in reasonReplacement"
                      append-icon="fas fa-times-circle"
                      :key="index"
                      class="ma-1"
                      @click="removeChip(index)"
                    >
                      {{ chip }}
                    </v-chip>
                  </template></v-text-field
                >
              </v-col>
              <v-col
                v-if="
                  customGroupCoverage &&
                  customGroupCoverage.toLowerCase() === 'custom group'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 mt-n5"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Recruiters Group for Notification<span style="color: red"
                    >*</span
                  >
                </p>
                <CustomSelect
                  ref="customGroup"
                  :items="dropdownCustomGroup"
                  item-title="Custom_Group_Name"
                  item-value="Custom_Group_Id"
                  variant="solo"
                  :isAutoComplete="true"
                  :rules="[
                    required(
                      'Recruiters Group for Notification',
                      selectedCustomGroup
                    ),
                  ]"
                  clearable
                  :isLoading="isCustomGroupListLoading"
                  :itemSelected="selectedCustomGroup"
                  @selected-item="selectedCustomGroup = $event"
                  @update:model-value="onChange()"
                >
                </CustomSelect>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <div class="card-actions-div">
          <v-card-actions class="d-flex align-end">
            <v-sheet class="align-center text-center" style="width: 100%">
              <v-row justify="center">
                <v-col cols="12" class="d-flex justify-end pr-6">
                  <v-btn
                    rounded="lg"
                    class="mr-6 primary"
                    @click="onCloseOverlay()"
                    variant="outlined"
                  >
                    Cancel
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    class="mr-1 primary"
                    @click="submitRecruitment()"
                    variant="elevated"
                    :disabled="!isFormDirty"
                  >
                    Submit
                  </v-btn>
                </v-col>
              </v-row>
            </v-sheet>
          </v-card-actions>
        </div>
      </v-card>
      <AppLoading v-if="listLoading"></AppLoading>
    </template>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit Approved & Forecasted Positions form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onClosePopup()"
  >
  </AppWarningModal>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  HIRING_POSITION_LIST,
  RECRUITMENT_POSITION_LIST,
  RECRUITMENT_SETTINGS,
} from "@/graphql/mpp/manPowerPlanningQueries";
import { POSITION_LEVEL_LIST } from "@/graphql/mpp/newPositionQueries";
import validationRules from "@/mixins/validationRules";
import { GET_CUSTOM_GROUP_COVERAGE } from "@/graphql/settings/irukka-integration/jobPostFormQueries.js";
export default {
  name: "AddEditRecruitmentRequestForm",
  emits: ["on-close-add-form", "open-workflow-model", "edit-form-submit"],
  mixins: [validationRules],
  props: {
    showAddForm: {
      type: Boolean,
      required: true,
    },
    selectedRecruitment: {
      type: Object,
      default: () => {},
    },
    selectedPositionParentId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      showRecruitmentForm: false,
      listLoading: false,
      positionListLoading: false,
      positionList: [],
      groupList: [],
      divisionList: [],
      sectionList: [],
      departmentList: [],
      employeeTypeList: [],
      reasonReplacement: [],
      input: "",
      reasonVacancyList: [
        "Demise",
        "End of Probationary",
        "Internal Movement",
        "Maternity Leave",
        "Others",
        "Resignation",
        "Retirement",
        "Sick Leave",
        "Termination of Employment",
      ],
      showIcon: true,
      selectedReasonVacancy: null,
      selectedPositionTitle: null,
      selectedGroup: { code: "", name: "" },
      selectedDivision: { code: "", name: "" },
      selectedDepartment: { code: "", name: "" },
      selectedSection: { code: "", name: "" },
      selectedWorkflow: { eventId: "", workflowId: "" },
      selectedRecruitmentFormData: {},
      selectedCostCenter: null,
      selectedEmployeeType: null,
      numberOfPositions: 0,
      openConfirmationPopup: false,
      isFormDirty: false,
      recruitmentLimitToCallAPI: 10000,
      totalApiCount: 0,
      apiCallCount: 0,
      positionLevelLoading: false,
      positionLevelList: [],
      selectedPositionLevel: null,
      recruitmentSettingEnabled: false,
      customGroupCoverage: null,
      dropdownCustomGroup: [],
      selectedCustomGroup: null,
      isCustomGroupListLoading: false,
    };
  },
  components: {
    CustomSelect,
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isInputValid() {
      const rules = [
        this.multilingualNameValidation(this.input),
        this.required("Replacement For", this.reasonReplacement[0]),
      ];
      return rules[0] === true ? true : false;
    },
  },
  watch: {
    showAddForm: {
      handler(val) {
        this.showRecruitmentForm = val;
        if (val) {
          this.retrieveRecruitmentSettings();
        }
      },
      immediate: true,
    },
    selectedRecruitment: {
      handler(val) {
        if (val && Object.keys(val).length) {
          this.selectedPositionTitle = val.Original_Position_Id
            ? parseInt(val.Original_Position_Id)
            : null;
          this.selectedPositionLevel = val?.Position_Level_Id || null;
          this.reasonReplacement =
            val?.Reason_For_Replacement?.split(",") || [];
          this.selectedReasonVacancy = val?.Replacement_For || null;
          this.selectedEmployeeType = val.Employee_Type
            ? parseInt(val.Employee_Type)
            : null;
          this.numberOfPositions = val.No_Of_Position || 0;
          this.selectedCustomGroup = val.Custom_Group_Id
            ? parseInt(val.Custom_Group_Id)
            : null;
          // this.retrieveRecruitmentPosition();
        } else {
          this.selectedPositionTitle = "";
          this.numberOfPositions = 0;
          this.selectedEmployeeType = null;
          this.selectedReasonVacancy = null;
          this.selectedPositionLevel = null;
          this.reasonReplacement = [];
          this.selectedCustomGroup = null;
          this.resetSelectedOptions();
        }
      },
      deep: true,
    },
    selectedPositionParentId: {
      handler() {
        this.retrieveTotalPositionTitle();
      },
      immediate: true,
    },
  },
  mounted() {
    this.retrieveEmployeeType();
    this.retrievePositionLevel();
    this.getCustomGroupCoverage();
    this.listCustomGroup();
    if (
      this.selectedRecruitment &&
      Object.keys(this.selectedRecruitment).length
    ) {
      this.selectedPositionTitle = this.selectedRecruitment.Original_Position_Id
        ? parseInt(this.selectedRecruitment.Original_Position_Id)
        : null;
      this.selectedPositionLevel =
        this.selectedRecruitment?.Position_Level_Id || null;
      this.reasonReplacement =
        this.selectedRecruitment?.Reason_For_Replacement?.split(",") || [];
      this.selectedReasonVacancy =
        this.selectedRecruitment?.Replacement_For || null;
      this.selectedEmployeeType = this.selectedRecruitment.Employee_Type
        ? parseInt(this.selectedRecruitment.Employee_Type)
        : null;
      this.numberOfPositions = this.selectedRecruitment.No_Of_Position || 0;
      this.selectedCustomGroup = this.selectedRecruitment.Custom_Group_Id
        ? parseInt(this.selectedRecruitment.Custom_Group_Id)
        : null;
      // this.retrieveRecruitmentPosition();
    }
  },
  methods: {
    onCloseOverlay() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.onClosePopup();
        this.isFormDirty = false;
      }
    },
    onClosePopup() {
      this.openConfirmationPopup = false;
      this.$emit("on-close-add-form");
      this.isFormDirty = false;
    },
    onChange() {
      this.isFormDirty = true;
    },
    showAddIcon() {
      this.showIcon = !!this.input.trim();
    },
    addChip() {
      if (this.isInputValid && this.input.trim() && this.input.length < 1500) {
        this.reasonReplacement.push(this.input.trim());
        this.input = "";
        this.showIcon = false;
        this.isFormDirty = true;
      }
    },
    removeChip(index) {
      this.reasonReplacement.splice(index, 1);
      this.isFormDirty = true;
    },
    onChangePosition() {
      if (this.selectedPositionTitle) {
        this.selectedEmployeeType = null;
        this.numberOfPositions = 0;
        this.retrieveRecruitmentPosition();
      } else {
        this.resetSelectedOptions();
      }
      this.onChange();
    },
    retrieveRecruitmentSettings() {
      this.listLoading = true;
      this.$apollo
        .query({
          query: RECRUITMENT_SETTINGS,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.listLoading = false;
          if (
            res &&
            res.data &&
            res.data.retrieveRecruitmentSettings &&
            res.data.retrieveRecruitmentSettings
              .allowHireWithoutApprovedVacancy &&
            res.data.retrieveRecruitmentSettings.warmBodiesIncludeNoticePeriod
          ) {
            const result = res.data.retrieveRecruitmentSettings;
            this.recruitmentSettingEnabled =
              result.allowHireWithoutApprovedVacancy.toLowerCase() === "yes" &&
              result.warmBodiesIncludeNoticePeriod.toLowerCase() === "yes"
                ? true
                : false;
          }
        })
        .catch((err) => {
          this.listLoading = false;
          this.recruitmentSettingEnabled = false;
          this.handleRetrieveRecruitmentErrors(err);
        });
    },
    retrievePositionLevel() {
      this.positionLevelLoading = true;
      this.$apollo
        .query({
          query: POSITION_LEVEL_LIST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.retrievePositionLevel &&
            res.data.retrievePositionLevel.positionLevelList
          ) {
            this.positionLevelList =
              res.data.retrievePositionLevel.positionLevelList;
          } else {
            this.positionLevelList = [];
          }
        })
        .catch((err) => {
          this.handleRetrieveRecruitmentErrors(err);
        })
        .finally(() => {
          this.positionLevelLoading = false;
        });
    },
    retrieveTotalPositionTitle() {
      this.positionListLoading = true;
      this.positionList = [];
      this.$apollo
        .query({
          query: HIRING_POSITION_LIST,
          client: "apolloClientAG",
          variables: {
            formId: 291,
            postionParentId: this.selectedPositionParentId || "",
            offset: 0,
            limit: this.recruitmentLimitToCallAPI,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.positionListLoading = false;
          if (
            res &&
            res.data &&
            res.data.listForecastPosition &&
            res.data.listForecastPosition.positionList
          ) {
            this.positionList = res.data.listForecastPosition.positionList;
            let { totalCountResult } = res.data.listForecastPosition;
            if (totalCountResult > 0) {
              totalCountResult = parseInt(totalCountResult);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalCountResult / this.recruitmentLimitToCallAPI
              );
              for (let i = 1; i < this.totalApiCount; i++) {
                this.retrievePositionTitle(i);
              }
              if (
                this.selectedRecruitment &&
                Object.keys(this.selectedRecruitment).length
              )
                this.retrieveRecruitmentPosition();
            }
          }
        })
        .catch((err) => {
          this.positionListLoading = false;
          this.positionList = [];
          this.handleRetrieveRecruitmentErrors(err);
        });
    },
    retrievePositionTitle(index = 1) {
      let apiOffset = parseInt(index) * this.recruitmentLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.positionListLoading = true;
      this.$apollo
        .query({
          query: HIRING_POSITION_LIST,
          client: "apolloClientAG",
          variables: {
            formId: 291,
            postionParentId: this.selectedPositionParentId || "",
            offset: apiOffset,
            limit: this.recruitmentLimitToCallAPI,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.positionListLoading = false;
          if (
            res &&
            res.data &&
            res.data.listForecastPosition &&
            res.data.listForecastPosition.positionList
          ) {
            this.positionList = [
              ...this.positionList,
              ...res.data.listForecastPosition.positionList,
            ];
            this.apiCallCount = this.apiCallCount + 1;
            if (this.totalApiCount === this.apiCallCount) {
              this.positionListLoading = false;
            }
          } else {
            this.positionListLoading = false;
          }
        })
        .catch((err) => {
          this.positionListLoading = false;
          this.positionList = [];
          this.handleRetrieveRecruitmentErrors(err);
        });
    },
    async retrieveRecruitmentPosition() {
      this.listLoading = true;
      const positionData = this.positionList.find(
        (item) => item.Originalpos_Id == this.selectedPositionTitle
      );
      console.log("length", this.positionList.length);
      console.log(
        positionData,
        "---add---",
        this.selectedPositionTitle,
        "---edit---",
        this.positionList
      );
      try {
        const {
          data: {
            listDetailsBasedOnOrgPosId: { positionDetails },
          },
        } = await this.$apollo.query({
          query: RECRUITMENT_POSITION_LIST,
          variables: {
            originalPosId: this.selectedPositionTitle?.toString() || "",
            organizationStructureId:
              parseInt(positionData?.Organization_Structure_Id) || 0,
            formId: 291,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        });

        if (positionDetails) {
          this.selectedWorkflow = {
            eventId: positionDetails.eventId,
            workflowId: positionDetails.workflowId,
          };
          this.selectedGroup = {
            code: positionDetails.groupCode,
            name: positionDetails.groupName,
            fullName: positionDetails.groupFullName,
          };
          this.selectedDivision = {
            code: positionDetails.divisionCode,
            name: positionDetails.divisionName,
            fullName: positionDetails.divisionFullName,
          };
          this.selectedDepartment = {
            code: positionDetails.deptCode,
            name: positionDetails.deptName,
            fullName: positionDetails.deptFullName,
          };
          this.selectedCostCenter = positionDetails.costCode;
          this.selectedSection = {
            code: positionDetails.sectionCode,
            name: positionDetails.sectionName,
            fullName: positionDetails.sectionFullName,
          };
          this.selectedRecruitmentFormData = positionDetails;
        } else {
          this.resetSelectedOptions();
        }
      } catch (error) {
        this.resetSelectedOptions();
        this.handleRetrieveRecruitmentErrors(error);
      } finally {
        this.listLoading = false;
      }
    },
    handleRetrieveRecruitmentErrors(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "recruitment request details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    async retrieveEmployeeType() {
      this.dropdownListFetching = true;
      try {
        const {
          data: {
            getDropDownBoxDetails: { employeeType },
          },
        } = await this.$store.dispatch("getDefaultDropdownList", {
          formId: 15,
        });
        if (employeeType) {
          this.employeeTypeList = employeeType;
        }
      } catch (error) {
        this.employeeTypeList = [];
      } finally {
        this.dropdownListFetching = false;
      }
    },

    resetSelectedOptions() {
      this.selectedGroup = { code: "", name: "" };
      this.selectedDivision = { code: "", name: "" };
      this.selectedDepartment = { code: "", name: "" };
      this.selectedCostCenter = null;
      this.selectedEmployeeType = null;
      this.numberOfPositions = 0;
      this.selectedPositionTitle = "";
      this.selectedSection = { code: "", name: "" };
    },
    async submitRecruitment() {
      const { valid } = await this.$refs.recruitmentRefForm.validate();
      if (valid) {
        this.handleSubmitRecruitment();
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleSubmitRecruitment() {
      const positionData = this.positionList.find(
        (item) => this.selectedPositionTitle === item.Originalpos_Id
      );
      const empType = this.employeeTypeList.find(
        (item) => item.EmpType_Id === this.selectedEmployeeType
      );
      let requestPayload = {
        recruitmentId:
          this.selectedRecruitment && this.selectedRecruitment.Recruitment_Id
            ? this.selectedRecruitment.Recruitment_Id
            : 0,
        status: "Waiting for approval",
        noOfPosition: parseInt(this.numberOfPositions),
      };
      if (empType) {
        requestPayload.employeeTypeName = empType.Employee_Type;
        requestPayload.employeeType = empType.EmpType_Id.toString();
      }
      if (positionData) {
        requestPayload.originalPositionId =
          positionData.Originalpos_Id.toString();
        requestPayload.positionCode = positionData.Pos_Code;
        requestPayload.positionTitle = positionData.Pos_Name;
      }
      if (this.selectedGroup && this.selectedGroup.name) {
        requestPayload.groupCode = this.selectedGroup.code;
        requestPayload.groupName = this.selectedGroup.name;
      }
      if (this.selectedDivision && this.selectedDivision.name) {
        requestPayload.divisionCode = this.selectedDivision.code;
        requestPayload.divisionName = this.selectedDivision.name;
      }
      if (this.selectedDepartment && this.selectedDepartment.name) {
        requestPayload.departmentCode = this.selectedDepartment.code;
        requestPayload.departmentName = this.selectedDepartment.name;
      }
      if (this.selectedSection && this.selectedSection.name) {
        requestPayload.sectionCode = this.selectedSection.code;
        requestPayload.sectionName = this.selectedSection.name;
      }
      if (this.selectedCostCenter) {
        requestPayload.costCode = this.selectedCostCenter;
      }
      if (this.selectedReasonVacancy && this.selectedReasonVacancy.length) {
        requestPayload.reasonForRequest = this.selectedReasonVacancy;
      }
      if (this.reasonReplacement && this.reasonReplacement.length) {
        requestPayload.reasonForReplacement = this.reasonReplacement.toString();
      }
      if (this.selectedPositionLevel) {
        requestPayload.PositionLevel = this.selectedPositionLevel;
      }
      if (this.selectedCustomGroup) {
        requestPayload.customGroupId = this.selectedCustomGroup;
      }
      if (
        this.selectedRecruitment &&
        this.selectedRecruitment.Original_Position_Id
      ) {
        this.$emit("edit-form-submit", requestPayload);
      } else {
        this.$emit("open-workflow-model", requestPayload);
      }
    },
    async getCustomGroupCoverage() {
      let vm = this;
      vm.listLoading = true;
      await vm.$apollo
        .query({
          query: GET_CUSTOM_GROUP_COVERAGE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.recruitmentSetting &&
            response.data.recruitmentSetting.settingResult &&
            response.data.recruitmentSetting.settingResult.length
          ) {
            this.customGroupCoverage =
              response.data.recruitmentSetting.settingResult[0].Coverage;
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleGetCustomGroupCoverageError(err);
        });
    },
    handleGetCustomGroupCoverageError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "custom group settings",
        isListError: false,
      });
    },
    async listCustomGroup() {
      let vm = this;
      vm.isCustomGroupListLoading = true;
      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "Job Posts",
        })
        .then((groupList) => {
          if (groupList && groupList.length) {
            this.dropdownCustomGroup = groupList;
          } else {
            this.dropdownCustomGroup = [];
          }
          this.isCustomGroupListLoading = false;
        })
        .catch(() => {
          this.isCustomGroupListLoading = false;
          this.dropdownCustomGroup = [];
        });
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100vh;
  width: 50vw;
  overflow-y: auto;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
