<template>
  <v-card class="rounded-lg"
    ><v-card-title>
      <div class="d-flex" style="width: 100%">
        <v-spacer></v-spacer>
        <v-icon color="primary" @click="closeFormRender()">fas fa-times</v-icon>
      </div>
    </v-card-title>
    <v-card-text
      :style="
        'overflow: scroll; max-height: ' + $store.getters.getTableHeight(300)
      "
      class="px-10 mt-n12"
    >
      <form
        id="fb-render"
        ref="fbRender"
        class="renderTemplate form-horizontal form-validation"
        cf-context
      ></form>
    </v-card-text>
    <div class="text-center mb-4">
      <v-btn
        v-if="conversationalId > 0 && showApproval"
        type="submit"
        color="secondary"
        variant="elevated"
        rounded="lg"
        class="mr-2"
        @click="submitForm('Draft')"
        >Save Ad Draft</v-btn
      >
      <v-btn
        v-if="showApproval"
        type="submit"
        color="primary"
        variant="elevated"
        rounded="lg"
        class="mr-2"
        @click="submitForm('Submitted')"
        >Approve</v-btn
      >
      <v-btn
        type="submit"
        :variant="showApproval ? 'outlined' : 'elevated'"
        color="primary"
        rounded="lg"
        @click="exportForm"
        >Export</v-btn
      >
    </div>
    <v-overlay
      v-if="isUpdatingForm"
      v-model="overlayModal"
      contained
      class="d-flex align-center justify-center"
    >
      <v-progress-circular color="secondary" indeterminate size="64">
      </v-progress-circular>
    </v-overlay>
  </v-card>
</template>

<script>
import { defineComponent } from "vue";
import "formBuilder/dist/form-render.min.js";
import "formBuilder";
import "jquery-validation/dist/jquery.validate.min.js";
// queries
import { UPDATE_DYNAMIC_FORM_DETAILS } from "@/graphql/workflow/approvalManagementQueries.js";
// functions
import { getErrorCodes } from "@/helper.js";
import * as html2pdf from "html2pdf.js";

export default defineComponent({
  name: "FormRender",

  props: {
    formData: {
      type: [Object, Array],
      required: true,
    },
    resignationId: {
      type: [String, Number],
      required: true,
    },
    pdfName: {
      type: String,
      default: "",
    },
    fromJobRecritment: {
      type: Boolean,
      default: false,
    },
    taskId: {
      type: [String, Number],
      required: true,
    },
    formResponseId: {
      type: [String, Number],
      required: true,
    },
    conversationalId: {
      type: [String, Number],
      required: true,
    },
    processInstanceId: {
      type: [String, Number],
      required: true,
    },
    showApproval: {
      type: Boolean,
      default: false,
    },
  },

  data: () => ({
    isUpdatingForm: false,
    overlayModal: true,
    formRenderInstance: null,
  }),

  mounted() {
    let formData = this.formData;
    this.formRenderInstance = $("#fb-render").formRender({ formData });
    if (!this.showApproval) {
      $("#fb-render :input").prop("disabled", true);
    }
  },

  computed: {
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },

  methods: {
    closeFormRender() {
      this.$emit("close-form-render");
    },
    exportForm() {
      var element = document.querySelector("#fb-render");
      var opt = {
        margin: 0.5,
        filename: this.fromJobRecritment ? `${this.pdfName}.pdf` : "Form.pdf",
        image: { type: "jpeg", quality: 1 },
        html2canvas: {
          dpi: 300,
          letterRendering: false,
          scale: 2,
        },
        jsPDF: { unit: "in", format: [12, 20], orientation: "portrait" },
      };
      html2pdf().set(opt).from(element).save();
    },
    submitForm(formStatus) {
      if (formStatus === "Draft") {
        this.updateDynamicForm(formStatus);
      } else {
        if ($("#fb-render").valid()) {
          this.updateDynamicForm(formStatus);
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: "Please provide a valid response",
          };
          this.showAlert(snackbarData);
        }
      }
    },
    updateDynamicForm(formStatus) {
      let vm = this;
      vm.isUpdatingForm = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_DYNAMIC_FORM_DETAILS,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
            },
            responseId: vm.formResponseId,
            taskId: vm.taskId,
            formData: JSON.stringify($("#fb-render").formRender("userData")),
            formStatus: formStatus,
            resignationId: vm.resignationId,
            workflowInstanceId: vm.processInstanceId,
          },
          client: "apolloClientZ",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.updateDynamicFormDetail &&
            response.data.updateDynamicFormDetail.result
          ) {
            vm.isUpdatingForm = false;
            vm.$emit("form-update-success");
          } else {
            vm.handleFormRetrieveError();
          }
        })
        .catch((err) => {
          vm.handleFormRetrieveError(err);
        });
    },
    handleFormRetrieveError(err = "") {
      this.isUpdatingForm = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "ERE0005": // Error while update dynamic form response details
            snackbarData.message =
              "Something went wrong while updating the dynamic form details. Please try after some time.";
            break;
          case "ERE0006": // Error while create dynamic form response details
            snackbarData.message =
              "Something went wrong while creating the dynamic form details. Please try after some time.";
            break;
          case "ERE0105": // Error while process the request for create or update dynamic form response details
            snackbarData.message =
              "Something went wrong while processing the request to update the dynamic form details. Please try after some time.";
            break;
          default:
            snackbarData.message =
              "Something went wrong while updating the dynamic form details. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while updating the dynamic form details. If you continue to see this issue please contact the platform administrator.";
      }
      this.showAlert(snackbarData);
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style>
@import url("../../../assets/css/dynamic-form-builder.css");
.error {
  color: red;
}
h1,
h2,
p {
  margin: 15px 0px;
}
.formbuilder-radio-group-label {
  margin: 15px 0px;
}
.radio-group,
.checkbox-group {
  margin: 10px 0px;
}
.formbuilder-checkbox {
  margin: 5px 0px;
}
.rendered-form .form-group {
  margin-bottom: 25px !important;
}
</style>
