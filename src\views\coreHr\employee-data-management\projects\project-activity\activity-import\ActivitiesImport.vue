<template>
  <v-container class="activity-container" fluid>
    <div>
      <v-row justify="center">
        <v-col cols="12" lg="11" md="12" sm="12">
          <v-card min-height="560" class="rounded-lg">
            <v-card-text>
              <div class="text-center mb-6">
                <span v-for="i in 3" :key="i">
                  <v-icon color="primary" size="18" class="ml-1">{{
                    currentStep >= i ? "fas fa-circle" : "far fa-circle"
                  }}</v-icon>
                </span>
              </div>
              <BulkImportStep1
                class="mb-10"
                v-show="currentStep === 1"
                ref="bulkStep1"
                :step1-text="step1Text"
                @file-upload-success="uploadFile($event)"
                @file-upload-error="fileRemoveOrError()"
                @generate-excel="onGenerateExcel()"
                :showDownload="true"
              >
              </BulkImportStep1>
              <BulkImportStep2
                class="mb-10 pb-5"
                v-if="fileContent.length > 0 && currentStep === 2"
                ref="bulkStep2"
                :file-params="fileContent"
                :headers-selected="selectedHeaders"
                @column-mapped="
                  matchedCount = $event[0];
                  mappedFileHeader = $event[1];
                "
              ></BulkImportStep2>
              <BulkImportStep3
                class="mb-10"
                ref="bulkImportStep3"
                v-if="checkMatchedFields && currentStep === 3"
                :fields="generateFields"
                :json-data="excelEditorData"
                type-of-import="activities"
                :extend-validation="excelValidation"
              ></BulkImportStep3>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-bottom-navigation v-model="openBottomSheet">
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
        >
          <v-row class="pa-3" justify="center">
            <v-col
              v-if="!isLoadUploadButton"
              cols="6"
              class="pa-0 d-flex justify-start align-center pl-2"
            >
              <v-btn
                v-if="currentStep > 1"
                id="back_to_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                color="primary"
                @click="backToStep()"
              >
                <span><i class="fa fa-chevron-left pr-2"></i> Back</span></v-btn
              >
              <v-btn
                id="cancel_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                class="ml-2"
                color="primary"
                @click="closeForm()"
                >Cancel</v-btn
              >
            </v-col>
            <v-col
              :cols="isLoadUploadButton ? '12' : '6'"
              class="pa-0 d-flex justify-center align-center pr-4"
              :style="windowWidth >= 1264 ? 'margin-left: -106px' : ''"
            >
              <div v-if="windowWidth > 768" class="text-end mr-2">
                <div class="mr-1 text-grey text-caption" style="width: 400px">
                  {{ nextBtnHelpContent }}
                </div>
              </div>
              <v-btn
                id="next_step"
                rounded="lg"
                color="primary"
                class="mr-10"
                :disabled="!enableNextButton"
                :loading="isLoadUploadButton"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                @click="nextStep()"
              >
                <span>
                  {{ currentStep === 3 ? "Submit" : "Next" }}
                  <v-icon v-if="currentStep !== 3" class="pl-1" size="15"
                    >fa fa-chevron-right</v-icon
                  >
                </span>
              </v-btn>
            </v-col>
            <v-col
              cols="12"
              v-if="windowWidth <= 768 && nextBtnHelpContent"
              class="pa-1 pr-4 d-flex align-center justify-end"
            >
              <div class="mr-1 text-grey mb-0" style="font-size: 10px">
                {{ nextBtnHelpContent }}
              </div>
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
    </div>
    <v-dialog v-model="importConfirmation" width="50%">
      <v-card>
        <v-row>
          <v-col v-if="invalidData && invalidData.length" cols="12">
            <v-alert prominent type="warning">
              <v-row align="center">
                <v-col v-if="invalidData" class="grow"
                  ><span>{{ invalidActivities.length }}</span>
                  out of
                  {{ excelEditorData.length }} do not have valid records.This
                  may result in omittion of those records.
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertActivityData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
          <v-col v-else cols="12" class="pa-3">
            <v-alert prominent type="success">
              <v-row align="center">
                <v-col class="grow">
                  Everything looks <strong>good</strong>.
                  <div class="pt-1">
                    Are you
                    <strong>sure</strong> you want to import the activity
                    details?
                  </div>
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertActivityData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-overlay
          class="align-center justify-center"
          contained
          :model-value="isLoading"
          scrim="#fff"
        >
          <v-progress-circular color="primary" indeterminate size="64">
          </v-progress-circular>
        </v-overlay>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import BulkImportStep1 from "@/views/common/bulkImport/BulkImportStep1.vue";
import BulkImportStep2 from "@/views/common/bulkImport/BulkImportStep2.vue";
import BulkImportStep3 from "@/views/common/bulkImport/BulkImportStep3.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
import { IMPORT_PROJECT_ACTIVITIES } from "@/graphql/corehr/projectActivityQueries.js";
export default {
  name: "ActivitiesImport",
  components: {
    BulkImportStep1,
    BulkImportStep2,
    BulkImportStep3,
  },
  mixins: [FileExportMixin],
  props: {
    backupMainList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data: () => ({
    currentStep: 1,
    fileContent: [],
    errorsCountInExcel: 0,
    matchedCount: 0,
    openBottomSheet: true,
    isLoadUploadButton: false,
    mappedFileHeader: [],
    allowanceRestrictBonus: "No",
    step1Text: {
      typeofData: "activity sheet",
      text: "You have the option of using our predefined template or bring in your own activity sheet with the headers for import",
      heading: "Download the excel template with predefined headers",
    },
    selectedImportType: 1,
    fields: [],
    optionValues: {},
    excelEditorData: [],
    importConfirmation: false,
    finalExcelData: [],
    finalUpdateData: [],
    isLoading: false,
    step2HeadersData: [],
  }),

  computed: {
    activityNameList() {
      // Use Set to filter out duplicate activityName values
      const uniqueNamesSet = new Set(
        this.backupMainList.map((item) => item.activityName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    excelValidation() {
      return {
        activity: this.activityNameList,
        billable: ["Yes", "No"],
      };
    },
    selectedHeaders() {
      //Form the selectedHeaders with fields and fieldOptions
      let output = [
        {
          title: "Activity",
          value: "Activity",
          props: {
            disabled: false,
          },
        },
        {
          title: "Billable",
          value: "Billable",
          props: {
            disabled: false,
          },
        },
        {
          title: "Activity Description",
          value: "Activity Description",
          props: {
            disabled: false,
          },
        },
      ];
      return output;
    },

    invalidData() {
      return this.$refs.bulkImportStep3.invalidData;
    },
    invalidActivities() {
      let invalidData = this.$refs.bulkImportStep3.invalidData;
      let employeeFail = Array.from(new Set(invalidData));
      return employeeFail;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    // enable next button based on current step and scenarios
    enableNextButton() {
      if (this.currentStep === 1 && this.fileContent.length > 0) {
        return true;
      } else if (this.currentStep === 2 && this.checkMatchedFields) {
        return true;
      } else if (this.currentStep === 3) {
        this.formattedFileContent();
        return true;
      } else {
        return false;
      }
    },

    // next button help content based on current step and scenarios
    nextBtnHelpContent() {
      if (this.currentStep === 1) {
        if (this.fileContent.length === 0)
          return "Please import the data with supported file types (CSV, XLSX and XLS) to continue with the next step.";
        else return "";
      } else if (this.currentStep === 2) {
        return "The unmatched optional column(s) will not be processed in the next step.";
      } else if (this.currentStep === 3) {
        if (this.formattedFileContent.length === 0) {
          return "";
        } else if (this.errorsCountInExcel !== 0) {
          return "There seems to be some validation error(s) in your file. Please amend it before uploading.";
        } else {
          return "By clicking the 'Submit' button, you can import employee data.";
        }
      } else {
        return "";
      }
    },
    mandatoryHeader() {
      let fields = ["Activity", "Billable", "Activity Description"];

      return fields;
    },
    // check mandatory fields all are matched
    checkMatchedFields() {
      let mandatoryHeader = this.mandatoryHeader;
      if (this.matchedCount === this.mandatoryHeader.length) {
        let mandatoryMatchedCount = 0;
        for (var i in this.mappedFileHeader) {
          if (mandatoryHeader.includes(this.mappedFileHeader[i].hrapp_header))
            mandatoryMatchedCount++;
        }
        this.addHeaders();
        //  if all the mandatory field are matched then return true else false
        return mandatoryMatchedCount === this.mandatoryHeader.length
          ? true
          : false;
      } else return false;
    },

    // get the data from excel file without empty values
    excelFileData() {
      return this.fileContent.filter(
        (content) => content.filter(Boolean).length > 0
      );
    },
    generateFields() {
      let formOutput = [];
      // Default Inclusion
      formOutput = [
        {
          field: "Activity",
          label: "Activity",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Billable",
          label: "Billable",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Yes", "No"],
        },
        {
          field: "Activity Description",
          label: "Activity Description",
          type: "string",
          readonly: false,
          width: "200px",
        },
      ];
      return formOutput;
    },
  },
  methods: {
    onGenerateExcel() {
      // we need to have drop-down values in excel, for that we are copying the same object 100 times for now
      const activityObject = {
        activity: null,
        isBillable: null,
        description: null,
      };
      // Create an array with 100 copies of the activityObject
      const exportData = Array.from({ length: 100 }, () => ({
        ...activityObject,
      }));
      let headers = [
        { key: "activity", header: "Activity" },
        { key: "isBillable", header: "Billable" },
        { key: "description", header: "Activity Description" },
      ];

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Activity template.xlsx",
        sheetName: "Activity sheet",
        header: headers,
        requiredHeaders: ["Activity", "Billable"],
        columnHighlightProps: {
          type: "Activity Import",
          billable: ["Yes", "No"],
        },
      };
      this.exportExcelFile(exportOptions);
    },
    formattedFileContent() {
      //With Fields form the headers
      let generatedData = this.formExcelData();
      this.excelEditorData = generatedData;
    },
    formExcelData() {
      let fields = this.generateFields;
      let data = JSON.parse(JSON.stringify(this.excelFileData));
      let headersAssigned = this.step2HeadersData;
      //Getting the field of the array of objects
      let excelData = [];
      let idCounter = 1;
      // Iterate through each row of data
      for (let i = 1; i < data.length; i++) {
        let rowData = data[i];
        let rowObj = { $id: "000000" + idCounter++ };

        // Iterate through each field definition and populate the row object
        for (let j = 0; j < fields.length; j++) {
          let fieldDef = fields[j];
          let fieldName = fieldDef.field;
          // Find the index of the field in the header mappings array
          let headerIndex = headersAssigned.findIndex(
            (header) => header.hrapp_header === fieldName
          );

          // If the field is present in the header mappings array, use the corresponding value from the input data
          if (headerIndex >= 0) {
            let dataValue = rowData[headerIndex];
            if (dataValue !== null && dataValue !== undefined) {
              rowObj[fieldName] = dataValue;
            } else {
              rowObj[fieldName] = null;
            }
          } else {
            // If the field is not present in the header mappings array, use the default value for the field type
            switch (fieldDef.type) {
              case "string":
                rowObj[fieldName] = "";
                break;
              case "number":
                rowObj[fieldName] = 0;
                break;
              case "boolean":
                rowObj[fieldName] = false;
                break;
              default:
                rowObj[fieldName] = null;
                break;
            }
          }
        }
        excelData.push(rowObj);
      }
      return excelData;
    },
    // called cancel is clicked to close form
    closeForm() {
      this.$emit("close-import-model");
    },
    // back button clicks, to subtract 1 from current step
    backToStep() {
      this.currentStep = this.currentStep - 1;
      this.allRecordsFail = false;
    },

    // next button clicks, to add 1 from current step
    nextStep() {
      if (this.currentStep === 3) {
        this.formBulkData(this.$refs.bulkImportStep3.filteredData);
      } else {
        this.currentStep += 1;
      }
    },

    addHeaders() {
      if (this.$refs.bulkStep2 && this.$refs.bulkStep2.tableItems) {
        this.step2HeadersData = this.$refs.bulkStep2.tableItems;
      }
    },

    formBulkData(data) {
      const filteredData = data;
      this.finalExcelData = data;
      this.finalUpdateData = filteredData.map((item) => {
        const newItem = {
          activityName: item["Activity"],
          isBillable: item["Billable"],
          description: item["Activity Description"],
        };
        return newItem;
      });
      this.importConfirmation = true;
    },

    // called when file uploaded in step 1
    uploadFile(event) {
      this.fileContent = event;
    },
    // called from step 1 when error while uploading or removing the file
    fileRemoveOrError() {
      this.fileContent = [];
      this.matchedCount = 0;
      this.errorsCountInExcel = 0;
    },
    insertActivityData(data) {
      if (data.length) {
        let vm = this;
        vm.isLoading = true;
        vm.$apollo
          .mutate({
            mutation: IMPORT_PROJECT_ACTIVITIES,
            client: "apolloClientJ",
            variables: {
              projectActivityData: data,
            },
          })
          .then(async (response) => {
            if (
              response &&
              response.data &&
              response.data.importProjectActivities
            ) {
              let { validationError } = response.data.importProjectActivities;
              validationError = JSON.parse(validationError);
              let excelInvalidData = this.$refs.bulkImportStep3.invalidData;
              let remainingData = [];
              let inputData = this.$refs.bulkImportStep3.editorData;
              for (let i = 0; i < inputData.length; i++) {
                if (excelInvalidData.includes(inputData[i].$id)) {
                  //Check if data is not already there in remaining data
                  if (!remainingData.includes(inputData[i])) {
                    remainingData.push(inputData[i]);
                  }
                }
              }
              let backendErrorsWithMesages = [];
              // Validation Backend Error Exists
              for (let i = 0; i < validationError.length; i++) {
                for (
                  let j = 0;
                  j < validationError[i].failedArrays.length;
                  j++
                ) {
                  for (let k = 0; k < inputData.length; k++) {
                    if (!remainingData.includes(inputData[k])) {
                      remainingData.push(inputData[k]);
                    }
                    let error = JSON.parse(JSON.stringify(inputData[k]));
                    error.Message = validationError[i].Message;
                    backendErrorsWithMesages.push(error);
                  }
                }
              }
              this.excelEditorData = remainingData;
              this.$refs.bulkImportStep3.editorData = remainingData;
              //Set Field Error
              for (let i = 0; i < backendErrorsWithMesages.length; i++) {
                let message = backendErrorsWithMesages[i].Message;
                let data = backendErrorsWithMesages[i];
                let field = {};
                if (field && message && message !== undefined) {
                  field.name = message.includes("Billable")
                    ? "Billable"
                    : message.includes("Activity Description")
                    ? "Activity Description"
                    : "Activity";
                }
                this.$refs.bulkImportStep3.setFieldError(message, data, field);
              }

              vm.importConfirmation = false;
              vm.isLoading = false;
              if (
                !excelInvalidData.length &&
                !backendErrorsWithMesages.length
              ) {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Activities imported successfully.",
                };
                vm.showAlert(snackbarData);
                vm.closeForm();
                this.$emit("refetch-data");
              }
            } else {
              vm.handleImportError();
              vm.importConfirmation = false;
              vm.closeForm();
            }
          })
          .catch((err) => {
            vm.handleImportError(err);
            vm.importConfirmation = false;
            vm.closeForm();
          });
      } else {
        this.importConfirmation = false;
        this.closeForm();
      }
    },
    handleImportError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "importing",
        form: "activity",
        isListError: false,
      });
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.activity-container {
  padding: 5em 2em 0em 3em;
}
.v-bottom-navigation__content {
  justify-content: space-around;
  flex-direction: column;
}
.dp__button_bottom {
  display: none;
}
</style>
