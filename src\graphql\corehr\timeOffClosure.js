import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_FINALIZED_EMPLOYEES = gql`
  query ListTimeoffClosure($formId: Int!, $year: Int!, $employeeId: Int) {
    listTimeoffClosure(formId: $formId, year: $year, employeeId: $employeeId) {
      errorCode
      message
      timeoffClosureDetails {
        Timeoff_Closure_Id
        Employee_Id
        Timeoff_Closure_Month
        Paid_Leave_Days
        Unpaid_Leave_Days
        Onduty_Days
        Paid_Days
        Overtime_Hours
        EmpType_Id
        Business_Working_Days
        Generated_By
        Generated_On
        User_Defined_EmpId
        Employee_Name
        Generated_By_Name
        Service_Provider_Id
        Location_Id
        Department_Id
        Manager_Id
        Designation_Id
        Designation_Name
        Department_Name
        Location_Name
        Employee_Type
        Organization_Unit_Name
      }
    }
  }
`;
// ===============
// Mutations
// ===============

export const DELETE_TIMEOFF_CLOSURE = gql`
  mutation DeleteTimeoffClosure($timeoffClosureId: Int!, $formId: Int!) {
    deleteTimeoffClosure(timeoffClosureId: $timeoffClosureId, formId: $formId) {
      errorCode
      message
    }
  }
`;
