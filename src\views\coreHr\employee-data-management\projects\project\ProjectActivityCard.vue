<template>
  <div v-if="isMounted">
    <div class="ma-1">
      <div>
        <v-form ref="activityForm">
          <v-row>
            <v-col
              :cols="
                ((addIcon && formAccess.add) || !openActivityEdit) &&
                projectStatus !== 'Closed'
                  ? 11
                  : 12
              "
            >
              <div
                class="rounded-lg pa-3 d-flex flex-column"
                style="box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1) !important"
              >
                <div
                  v-if="
                    projectStatus !== 'Closed' &&
                    actionList &&
                    actionList.length &&
                    !isAdd &&
                    !addNewData &&
                    openActivityEdit &&
                    !isAnyActivityEditClosed
                  "
                  class="ml-auto"
                >
                  <ActionMenu
                    :actions="actionList"
                    :accessRights="checkAccess()"
                    @selected-action="handleActions($event, index)"
                  ></ActionMenu>
                </div>
                <v-row :class="classForActionList">
                  <v-col
                    v-if="!openActivityEdit"
                    cols="12"
                    sm="6"
                    md="6"
                    lg="6"
                  >
                    <CustomSelect
                      :items="activityList"
                      label="Activity"
                      :itemSelected="activity"
                      itemValue="activityId"
                      itemTitle="activityName"
                      :isLoading="fetchingActivityList"
                      :isRequired="true"
                      :disabled="openActivityEdit"
                      :disabledValue="disableActivityList"
                      @selected-item="
                        onChangeCustomSelectField($event, 'activity')
                      "
                      :rules="[required('Activity', activity)]"
                      style="max-width: 100%"
                      list-width="max-width:380px"
                    ></CustomSelect>
                  </v-col>
                  <v-col v-else cols="12" sm="6" md="6" lg="6">
                    <p class="text-subtitle-1 text-grey-darken-1">Activity</p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ activityName }}
                    </p>
                  </v-col>
                  <v-col
                    v-if="!openActivityEdit"
                    cols="12"
                    sm="3"
                    md="3"
                    lg="3"
                    xl="3"
                  >
                    <div class="d-flex flex-column">
                      <span class="v-label">Billable</span>
                      <v-switch
                        color="primary"
                        class="mt-n2"
                        v-model="isBillable"
                        :true-value="'Yes'"
                        :false-value="'No'"
                        :disabled="openActivityEdit"
                        @update:model-value="isFormDirty = true"
                      ></v-switch>
                    </div>
                  </v-col>
                  <v-col v-else cols="12" sm="3" md="3" lg="3" xl="3">
                    <p class="text-subtitle-1 text-grey-darken-1">Billable</p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ isBillable }}
                    </p>
                  </v-col>
                  <v-col cols="12" sm="3" md="3" lg="3" xl="3">
                    <div>Description</div>
                    <div v-if="description">
                      <v-tooltip
                        :text="description"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div v-bind="props">
                            <v-icon color="blue" class="mt-1"
                              >far fa-comment-alt</v-icon
                            >
                          </div>
                        </template>
                      </v-tooltip>
                    </div>
                    <div v-else>-</div>
                  </v-col>
                  <v-col
                    v-if="!openActivityEdit"
                    cols="12"
                    sm="6"
                    md="6"
                    lg="6"
                    xl="6"
                  >
                    <p class="custom-label">From</p>
                    <section
                      :class="openActivityEdit ? 'cursor-not-allow' : ''"
                    >
                      <datepicker
                        :format="orgDateFormat"
                        class="custom-date-picker"
                        v-model="activityFrom"
                        :disabled="openActivityEdit"
                        :open-date="
                          new Date(
                            activityFrom && activityFrom != '0000-00-00'
                              ? activityFrom
                              : currentDate
                          )
                        "
                        :disabled-dates="{
                          to: minDate ? new Date(minDate) : '',
                          from: activityToDateValidation
                            ? new Date(activityToDateValidation)
                            : maxDate
                            ? new Date(maxDate)
                            : '',
                        }"
                        style="width: 100%"
                        :style="openActivityEdit ? 'pointer-events: none' : ''"
                        @input="onChangeFields()"
                      ></datepicker>
                    </section>
                  </v-col>
                  <v-col v-else cols="12" sm="6" md="6" lg="6" xl="6">
                    <p class="text-subtitle-1 text-grey-darken-1">From</p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{
                        activityFrom && activityFrom != "0000-00-00"
                          ? convertUTCToLocal(activityFrom).substring(0, 10)
                          : "-"
                      }}
                    </p>
                  </v-col>
                  <v-col
                    v-if="!openActivityEdit"
                    cols="12"
                    sm="6"
                    md="6"
                    lg="6"
                    xl="6"
                  >
                    <p class="custom-label">To</p>
                    <section
                      :class="openActivityEdit ? 'cursor-not-allow' : ''"
                    >
                      <datepicker
                        :format="orgDateFormat"
                        class="custom-date-picker"
                        v-model="activityTo"
                        :open-date="
                          new Date(
                            activityTo && activityTo != '0000-00-00'
                              ? activityTo
                              : currentDate
                          )
                        "
                        :disabled-dates="{
                          to: activityFromDateValidation
                            ? new Date(activityFromDateValidation)
                            : minDate
                            ? new Date(minDate)
                            : '',
                          from: maxDate ? new Date(maxDate) : '',
                        }"
                        :disabled="openActivityEdit"
                        style="width: 100%"
                        :style="openActivityEdit ? 'pointer-events: none' : ''"
                        @input="onChangeFields()"
                      ></datepicker>
                    </section>
                  </v-col>
                  <v-col v-else cols="12" sm="6" md="6" lg="6" xl="6">
                    <p class="text-subtitle-1 text-grey-darken-1">To</p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{
                        activityTo && activityTo != "0000-00-00"
                          ? convertUTCToLocal(activityTo).substring(0, 10)
                          : "-"
                      }}
                    </p>
                  </v-col>
                </v-row>
              </div>
            </v-col>
            <v-col
              v-if="
                addIcon &&
                formAccess.add &&
                projectStatus !== 'Closed' &&
                openActivityEdit
              "
              :cols="1"
              class="d-flex flex-column justify-center align-center"
            >
              <v-avatar
                v-if="addIcon && formAccess.add"
                @click="$emit('add-new-activity')"
                :size="30"
                :class="my - 3"
                style="cursor: pointer"
                color="primary"
              >
                <v-icon size="15">fas fa-plus</v-icon>
              </v-avatar>
            </v-col>
            <v-col
              v-else-if="!openActivityEdit"
              :cols="1"
              class="d-flex flex-column justify-center align-center"
            >
              <v-tooltip text="Save" location="bottom">
                <template v-slot:activator="{ props }">
                  <div v-bind="props">
                    <v-avatar
                      color="success"
                      style="cursor: pointer"
                      class="my-3"
                      @click="validateForm()"
                      :size="30"
                    >
                      <v-icon size="20">fas fa-check</v-icon>
                    </v-avatar>
                  </div>
                </template>
              </v-tooltip>

              <v-tooltip text="Cancel" location="bottom">
                <template v-slot:activator="{ props }">
                  <div v-bind="props">
                    <v-avatar
                      @click="resetToOriginalValues()"
                      color="red"
                      class="my-3"
                      style="cursor: pointer"
                      :size="30"
                    >
                      <v-icon size="20">fas fa-times</v-icon>
                    </v-avatar>
                  </div>
                </template>
              </v-tooltip>
            </v-col>
          </v-row>
        </v-form>
      </div>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isLoadingCard"
        scrim="#fff"
      >
        <v-progress-circular color="primary" indeterminate size="54">
        </v-progress-circular>
      </v-overlay>
    </div>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      :confirmation-heading="warningText"
      :icon-name="warningIconClass"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="deleteRecord()"
    >
    </AppWarningModal>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseFormModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
// Queries
import {
  ADD_UPDATE_ACTIVITIES_TO_PROJECT,
  DELETE_ACTIVITY_ASSOCIATTEDD_WITH_PROJECT,
  RETRIEVE_MIN_MAX_DATE_FOR_ACTIVITY,
} from "@/graphql/corehr/projectsQueries";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { convertUTCToLocal } from "@/helper.js";
export default {
  name: "AddEditCard",
  mixins: [validationRules],
  props: {
    formData: {
      type: Object,
      default: () => ({
        activity: "",
        isBillable: false,
        activityFrom: null,
        activityTo: null,
        description: null,
      }),
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    activityList: {
      type: Array,
      default: () => [],
    },
    projectId: {
      Type: Number,
      default: 0,
    },
    openActivityEdit: {
      type: Boolean,
      required: false,
    },
    addIcon: {
      type: Boolean,
      required: false,
    },
    disableActivityList: {
      type: Array,
      default: () => [],
    },
    isAdd: {
      type: Boolean,
      required: true,
    },
    projectStatus: {
      type: String,
      required: true,
    },
    addNewData: {
      type: Boolean,
      required: true,
    },
    isAnyActivityEditClosed: {
      type: Boolean,
      required: true,
    },
    fetchingActivityList: {
      type: Boolean,
      required: true,
    },
  },
  components: {
    Datepicker,
    CustomSelect,
    ActionMenu,
  },
  emits: [
    "change-activity-edit",
    "refetch-data",
    "close-edit",
    "add-new-activity",
  ],
  data() {
    return {
      isMounted: false,
      isLoadingCard: false,
      validationMessages: [],
      showValidationAlert: false,
      description: null,
      isBillable: "No",
      activity: "",
      activityFrom: "",
      activityTo: "",
      warningIconClass: "",
      openWarningModal: false,
      warningText: "Are you sure to delete the activity?",
      deleteItem: null,
      projectActivityId: 0,
      minDate: null,
      maxDate: null,
      isLoading: false,
      activityName: "",
      openConfirmationPopup: false,
      isFormDirty: false,
      havingAccess: {},
    };
  },
  computed: {
    projectLabel() {
      return this.$store.state.projectLabel;
    },
    projectLabelSmallCase() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel ? pLabel.toLowerCase() : pLabel;
    },
    actionList() {
      let actionList = [];
      if (this.formAccess.update) {
        actionList.push("Edit");
      }
      if (this.formAccess.delete) {
        actionList.push("Delete");
      }
      return actionList;
    },
    classForActionList() {
      if (
        this.actionList &&
        this.actionList.length &&
        !this.isMobileView &&
        this.projectStatus !== "Closed" &&
        !this.isAdd &&
        !this.addNewData &&
        this.openActivityEdit &&
        !this.isAnyActivityEditClosed
      ) {
        return "mt-n10";
      } else if (this.isMobileView) {
        return "";
      } else {
        return "";
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    activityToDateValidation() {
      if (this.activityTo && this.activityTo != "0000-00-00") {
        return new Date(this.activityTo).toISOString().substring(0, 10);
      } else {
        return "";
      }
    },
    activityFromDateValidation() {
      if (this.activityFrom && this.activityFrom != "0000-00-00") {
        return new Date(this.activityFrom).toISOString().substring(0, 10);
      } else {
        return "";
      }
    },
    currentDate() {
      const today = new Date().toISOString().substring(0, 10);
      return today;
    },
  },
  mounted() {
    this.assignModelValues();
    this.isMounted = true;
    if (!this.openActivityEdit) {
      this.getMinMaxDateForActivty();
    }
  },
  methods: {
    convertUTCToLocal,
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      this.havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      return this.havingAccess;
    },
    onCloseFormModal() {
      this.openConfirmationPopup = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.assignModelValues();
      this.$emit("close-edit");
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    handleActions(action, index) {
      if (action === "Delete") {
        this.openWarningPopup();
      } else if (action === "Edit") {
        this.changeActivityEdit(index);
      }
    },
    resetToOriginalValues() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },
    assignModelValues() {
      const {
        activityName,
        activityId,
        activityFrom,
        activityTo,
        isBillable,
        description,
        projectActivityId,
      } = this.formData;
      this.activityName = activityName ? activityName : "";
      this.activity = activityId ? activityId : null;
      this.activityFrom =
        activityFrom && activityFrom != "0000-00-00" ? activityFrom : null;
      this.activityTo =
        activityTo && activityTo != "0000-00-00" ? activityTo : null;
      this.isBillable = isBillable ? isBillable : "No";
      this.description = description ? description : null;
      this.projectActivityId = projectActivityId ? projectActivityId : 0;
    },
    changeActivityEdit(index) {
      this.$emit("change-activity-edit", index);
      this.getMinMaxDateForActivty();
    },
    onChangeCustomSelectField(val, field) {
      if (field == "activity") {
        this.activity = val;
        const selectedObject = this.activityList.find(
          (item) => item.activityId === val
        );
        if (selectedObject) {
          this.isBillable = selectedObject.isBillable;
          this.activityFrom =
            selectedObject.activityFrom &&
            selectedObject.activityFrom != "0000-00-00"
              ? selectedObject.activityFrom
              : null;
          this.activityTo =
            selectedObject.activityTo &&
            selectedObject.activityTo != "0000-00-00"
              ? selectedObject.activityTo
              : null;
          this.description = selectedObject.description;
        }
      }
      this.isFormDirty = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async validateForm() {
      const { valid } = await this.$refs.activityForm.validate();
      if (valid) {
        this.addUpdateData();
      }
    },
    addUpdateData() {
      let vm = this;
      vm.isLoadingCard = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_ACTIVITIES_TO_PROJECT,
            variables: {
              projectActivityId: vm.projectActivityId,
              projectId: vm.projectId,
              activityId: vm.activity,
              activityFrom:
                vm.activityFrom && vm.activityFrom != "0000-00-00"
                  ? moment(vm.activityFrom).format("YYYY-MM-DD")
                  : "",
              activityTo:
                vm.activityTo && vm.activityTo != "0000-00-00"
                  ? moment(vm.activityTo).format("YYYY-MM-DD")
                  : "",
              description: vm.description,
              isBillable: vm.isBillable,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoadingCard = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: !this.isAdd
                ? this.projectLabel + " activity updated successfully."
                : this.projectLabel + " activity added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.isLoadingCard = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: !this.isAdd ? "updating" : "adding",
          form: this.projectLabelSmallCase + " activity",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.deleteItem = null;
    },
    openWarningPopup(item) {
      this.$emit("close-edit");
      if (item === null) {
        this.warningIconClass = "fas fa-trash";
        this.openWarningModal = false;
        return;
      }
      this.warningIconClass = "fas fa-trash";
      this.openWarningModal = true;
      this.deleteItem = this.projectActivityId;
    },
    //function to delete  record
    deleteRecord() {
      this.openWarningModal = false;
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_ACTIVITY_ASSOCIATTEDD_WITH_PROJECT,
            variables: {
              projectActivityId: vm.deleteItem ? parseInt(vm.deleteItem) : 0,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: this.projectLabel + " activity deleted successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((error) => {
            vm.handleDeleteError(error);
          });
      } catch {
        vm.handleDeleteError();
      }
    },
    handleDeleteError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: this.projectLabelSmallCase + " activity",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    getMinMaxDateForActivty() {
      let vm = this;
      vm.isLoadingCard = true;
      vm.$apollo
        .query({
          query: RETRIEVE_MIN_MAX_DATE_FOR_ACTIVITY,
          variables: {
            projectActivityId: vm.projectActivityId,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveMinMaxDateForActivity) {
            let minMaxDate =
              response.data.retrieveMinMaxDateForActivity.activityData[0];
            if (minMaxDate) {
              vm.minDate = minMaxDate.minDate;
              vm.maxDate = minMaxDate.maxDate;
            }
            vm.isLoadingCard = false;
          } else {
            vm.handleMinMaxRetrieveError((err = ""), "activities");
          }
        })
        .catch((err) => {
          vm.handleMinMaxRetrieveError(err, "activities");
        });
    },
    handleMinMaxRetrieveError(err = "", formName) {
      this.isLoadingCard = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((validationErrors) => {
          this.validationMessages = [validationErrors];
          this.showValidationAlert = true;
        });
    },
  },
};
</script>
<style scoped>
.vuejs3-datepicker__value {
  box-shadow: none !important;
}
</style>
