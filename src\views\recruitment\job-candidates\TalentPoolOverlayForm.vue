<template>
  <div class="text-center">
    <v-overlay
      v-model="overlay"
      class="d-flex justify-end overlay-content-parent"
      @click:outside="onClickClose()"
      persistent
      style="z-index: 1000"
    >
      <template v-slot:default>
        <div class="overlay-card">
          <div
            class="d-flex align-center text-h6 text-medium-emphasis pa-2 bg-primary"
            style="width: 100%"
          >
            <v-icon
              v-if="displayCustomEmail"
              @click="
                displayCustomEmail
                  ? (displayCustomEmail = false)
                  : $emit('close-overlay')
              "
              size="17"
              class="mx-2"
              >fas fa-chevron-left</v-icon
            >
            <span class="ml-2"> Move to Talent Pool </span>
            <v-spacer></v-spacer>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="onClickClose()"
            ></v-btn>
          </div>
          <div class="overlay-body">
            <v-form v-if="!displayCustomEmail" ref="talentPoolForm">
              <v-row class="px-sm-4 px-md-6 pt-sm-4">
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6 d-flex"
                >
                  <CustomSelect
                    :items="itemTalentList"
                    label="Talent Pool"
                    itemValue="talentPoolId"
                    itemTitle="talentPool"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :is-loading="talentPoolListLoading"
                    :rules="[required('Talent Pool', selectedTalentPoolId)]"
                    :itemSelected="selectedTalentPoolId"
                    @selected-item="selectedTalentPoolId = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                  <!-- Add button on the right -->
                  <v-btn
                    v-if="talentPoolFormAccess.add"
                    variant="elevated"
                    class="primary mt-3 ml-4"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="openAddTalentPoolField = true"
                  >
                    <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                    <span class="primary">Add</span>
                  </v-btn>
                  <v-btn
                    color="transparent"
                    class="ml-4 mt-3"
                    variant="flat"
                    @click="fetchTalentPoolList"
                  >
                    <v-icon size="20">fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
                <!-- Column for v-text-field taking up most of the width -->
                <v-col
                  cols="9"
                  v-if="openAddTalentPoolField"
                  class="pl-sm-4 pl-md-6"
                >
                  <v-form ref="TalentPoolAddForm" @submit.prevent="">
                    <v-text-field
                      v-model="talentPool"
                      variant="solo"
                      class="mr-2"
                      :isRequired="true"
                      :rules="[
                        required('Talent Pool', talentPool),
                        validateWithRulesAndReturnMessages(
                          talentPool,
                          'talentPool',
                          'Talent Pool'
                        ),
                      ]"
                      ><template v-slot:label
                        ><span>Add new talent pool</span>
                        <span class="ml-1" style="color: red">*</span></template
                      ></v-text-field
                    >
                  </v-form>
                </v-col>
                <!-- Column for the icons -->
                <v-col
                  v-if="openAddTalentPoolField"
                  cols="3"
                  class="d-flex justify-end align-center"
                >
                  <!-- Icons for Save and Close -->
                  <v-btn
                    icon="fas fa-check"
                    variant="text"
                    @click="validateTalentPoolForm()"
                    color="primary mb-3"
                    size="small"
                  ></v-btn>
                  <v-btn
                    icon="fas fa-times"
                    variant="text"
                    @click="openAddTalentPoolField = false"
                    color="primary mb-3"
                    size="small"
                  ></v-btn>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <CustomSelect
                    :items="archiveReasonList"
                    v-model="archiveReason"
                    label="Reason for Archiving"
                    itemValue="Reason_Id"
                    itemTitle="Reason"
                    ref="archiveReason"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :is-loading="isArchiveReasonLoading"
                    :rules="[required('Reason for Archiving', archiveReason)]"
                    :itemSelected="archiveReason"
                    @selected-item="archiveReason = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <v-switch
                    v-model="notifyCandidate"
                    label="Notify Candidate"
                    :false-value="false"
                    :true-value="true"
                    color="primary"
                    hide-details
                  ></v-switch>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                  v-if="notifyCandidate == true"
                >
                  <CustomSelect
                    :items="notificationTimeList"
                    v-model="notificationTime"
                    label="Time"
                    itemValue="notificationTime"
                    itemTitle="notificationTime"
                    ref="notificationTime"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :rules="[required('Notification Time', notificationTime)]"
                    :itemSelected="notificationTime"
                    @selected-item="notificationTime = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                  v-if="notifyCandidate && emailTemplateList?.length"
                >
                  <CustomSelect
                    :items="emailTemplateList"
                    v-model="selectedEmailTemplate"
                    label="Email Template"
                    itemValue="Template_Id"
                    itemTitle="Template_Name"
                    ref="selectedEmailTemplate"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :rules="[required('Email Template', selectedEmailTemplate)]"
                    :itemSelected="selectedEmailTemplate"
                    @selected-item="selectedEmailTemplate = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                >
                  <!-- Archive Comment -->
                  <v-textarea
                    v-model="archiveComment"
                    rows="2"
                    row-height="10"
                    color="primary"
                    hide-details="auto"
                    variant="solo"
                    label="Comment"
                    counter="500"
                    :rules="[
                      archiveComment
                        ? validateWithRulesAndReturnMessages(
                            archiveComment,
                            'archiveComment',
                            'Comment'
                          )
                        : true,
                    ]"
                    @update:model-value="deductFormChange()"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
            <CustomEmail
              v-else
              ref="customEmail"
              :formId="16"
              :templateEmail="[candidateDetails.Personal_Email]"
              :templateData="templateData"
              typeOfSchedule="noncalendar"
              :typeOfTemplate="typeOfTemplate"
              :emailTemplateList="emailTemplateList"
              :selectedEmailTemplate="selectedEmailTemplate"
              :selectedCandidateId="candidateId"
              :noCustomTemplate="noCustomTemplate"
              :notificationTimeNow="
                notificationTime && notificationTime.toLowerCase() === 'now'
                  ? true
                  : false
              "
              :submitText="
                notificationTime && notificationTime.toLowerCase() === 'now'
                  ? 'Send Email'
                  : 'Submit'
              "
              @custom-email-cancel="closeCustomEmail()"
            ></CustomEmail>
          </div>
          <v-card class="overlay-footer" elevation="16">
            <v-btn
              class="mr-5"
              variant="outlined"
              @click="
                displayCustomEmail
                  ? (this.displayCustomEmail = false)
                  : onClickClose()
              "
              rounded="lg"
              >Cancel</v-btn
            >
            <v-btn
              color="primary"
              variant="elevated"
              rounded="lg"
              @click="validateMoveToTalentPoolForm()"
            >
              {{
                displayCustomEmail
                  ? "Send Email"
                  : notifyCandidate
                  ? "Preview Email"
                  : "Submit"
              }}
            </v-btn>
          </v-card>
        </div>
        <AppLoading v-if="isLoading"></AppLoading>
      </template>
    </v-overlay>
  </div>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  ></AppWarningModal>
</template>
<script>
import { checkNullValue } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import {
  GET_TALENT_POOL_LIST,
  ADD_CANDIDATE_TO_TALENT_POOL,
  ADD_UPDATE_TALENT_POOL,
  GET_ARCHIVE_REASONS_LIST,
} from "@/graphql/recruitment/recruitmentQueries.js";
import CustomEmail from "@/views/common/customEmail/CustomEmailComponent.vue";
import moment from "moment";
import {
  recruitmentEmailTemplates,
  replacementTags,
} from "@/views/common/customEmail/recruitmentEmailTemplates";
import { LIST_CUSTOM_EMAIL_TEMPLATES } from "@/graphql/settings/email-template/emailTemplateQueries.js";

export default {
  name: "TalentPoolOverlayForm",
  components: {
    CustomSelect,
    CustomEmail,
  },
  props: {
    candidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    candidateId: {
      type: Number,
      required: true,
    },
    jobTitle: {
      default: "",
      type: String,
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    originalStatusList: {
      type: Array,
      required: true,
    },
    isCandidateDeleted: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [validationRules],
  emits: ["close-talent-pool-overlay-form", "refetch-data"],

  data: () => ({
    isFormDirty: false,
    showConfirmation: false,
    overlay: true,
    isLoading: false,
    notificationTime: null,
    notifyCandidate: false,
    archiveReason: null,
    archiveComment: "",
    openCustomEmail: false,
    itemTalentList: [],
    selectedTalentPoolId: null,
    archiveStatusId: null,
    buttonText: "Submit",
    notificationTimeList: [
      "Now",
      "After 2 Hours",
      "After 8 Hours",
      "After 24 Hours",
      "After 48 Hours",
    ],
    talentPool: "",
    openAddTalentPoolField: false,
    talentPoolListLoading: false,
    isArchiveReasonLoading: false,
    archiveReasonList: [],
    typeOfTemplate: "ArchiveApplication",
    htmlContent: null,
    emailTemplateList: [],
    selectedEmailTemplate: null,
    noCustomTemplate: false,
    archiveReasonValue: "",
    displayCustomEmail: false,
    openConfirmationPopup: false,
  }),

  mounted() {
    this.fetchTalentPoolList();
    this.getArchivedSatusId();
    this.fetchArchiveReasonList();
  },

  watch: {
    notifyCandidate(val) {
      if (val) {
        this.buttonText = "Next";
        this.fetchEmailTemplates();
      } else {
        this.buttonText = "Submit";
      }
    },
    archiveReason(val) {
      this.archiveReasonValue = this.getReasonById(this.archiveReasonList, val);
    },
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDetails() {
      return this.$store.state.orgDetails;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    loginEmployeeDetails() {
      return this.$store.state.orgDetails.userDetails;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    talentPoolFormAccess() {
      let formAccess = this.accessRights("297");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  methods: {
    checkNullValue,
    deductFormChange() {
      this.isFormDirty = true;
    },

    async validateMoveToTalentPoolForm() {
      this.htmlContent = null;
      if (!this.displayCustomEmail) {
        let { valid } = await this.$refs.talentPoolForm.validate();
        if (valid) {
          if (this.notifyCandidate) {
            this.templateData = {
              Company_Name: this.companyName,
              Candidate_Name: [
                this.candidateDetails.First_Name,
                this.candidateDetails.Middle_Name,
                this.candidateDetails.Last_Name,
              ]
                .filter((name) => name)
                .join(" "),
              Recruiter_Name: this.loginEmployeeDetails.employeeFullName,
              Designation: this.loginEmployeeDetails.designationName,
              Archive_Reason: this.archiveReasonValue,
              Job_Post_Name: this.candidateDetails.Job_Post_Name,
            };
            if (recruitmentEmailTemplates[this.typeOfTemplate]) {
              this.htmlContent = this.replaceTags(
                recruitmentEmailTemplates[this.typeOfTemplate].body,
                replacementTags,
                this.templateData
              );
            }
            this.displayCustomEmail = true;
          } else {
            this.moveCandidateToTalentPool();
          }
        }
      } else {
        let customEmailRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        if (customEmailRef) {
          this.htmlContent = customEmailRef.htmlContent;
          let noPlaceholderFound = customEmailRef.noPlaceholderFound;
          if (noPlaceholderFound) {
            let snackbarData = {
              isOpen: true,
              message:
                "Some placeholders are not replaced, kindly replace or remove them before proceeding.",
              type: "warning",
            };
            this.showAlert(snackbarData);
            customEmailRef.noPlaceholderFound = false;
          } else {
            const { valid } =
              await customEmailRef.$refs.customEmailForm.validate();
            if (valid && customEmailRef.isContentPresent) {
              this.moveCandidateToTalentPool(customEmailRef);
            }
          }
        }
      }
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeWindow(isSuccess) {
      this.$emit("close-talent-pool-overlay-form", isSuccess);
      this.overlay = true;
    },
    closeCustomEmail() {
      this.templateData = "";
      this.templateType = "";
      this.comment = "";
      this.selectedItem = null;
      this.openCustomEmail = false;
      this.closeWindow();
    },
    onClickClose() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.closeWindow();
      }
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.closeWindow();
    },
    fetchTalentPoolList() {
      let vm = this;
      vm.talentPoolListLoading = true;
      vm.$apollo
        .query({
          query: GET_TALENT_POOL_LIST,
          client: "apolloClientAY",
          variables: {
            formId: 297,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getTalentPoolList &&
            !response.data.getTalentPoolList.errorCode
          ) {
            vm.itemTalentList =
              response.data.getTalentPoolList.talentPoolListData;
            vm.originalList = vm.itemTalentList;
            vm.talentPoolListLoading = false;
          } else {
            vm.itemTalentList = [];
            vm.selectedTalentPoolId = null;
            vm.talentPoolListLoading = false;
          }
        })
        .catch((err) => {
          vm.talentPoolListLoading = false;
          vm.handleTalentPoolListError(err);
        });
    },
    handleTalentPoolListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "talent pool list",
        isListError: false,
      });
    },
    async moveCandidateToTalentPool(customEmailRef = null) {
      let vm = this;
      vm.isLoading = true;
      try {
        const formmatedNotificationTime = this.getFormattedDate(
          vm.notificationTime
        );
        const response = await vm.$apollo.mutate({
          mutation: ADD_CANDIDATE_TO_TALENT_POOL,
          client: "apolloClientBF",
          variables: {
            candidateId: parseInt(vm.candidateId),
            talentPoolId: vm.selectedTalentPoolId,
            archiveStatusId: this.archiveStatusId,
            archiveReasonId: vm.archiveReason,
            notificationTime: formmatedNotificationTime,
            archiveComment: vm.archiveComment,
            mailContent:
              vm.notifyCandidate &&
              vm.notificationTime &&
              vm.notificationTime.toLowerCase() === "now"
                ? null
                : vm.htmlContent,
          },
          fetchPolicy: "no-cache",
        });

        if (
          response &&
          response.data &&
          response.data.addCandidateToTalentPool
        ) {
          const { errorCode, validationError } =
            response.data.addCandidateToTalentPool;

          if (!errorCode && !validationError) {
            // Show success message
            vm.showAlert({
              isOpen: true,
              type: "success",
              message: "Job candidate added to talent pool successfully.",
            });

            // Sending the custom email if needed
            if (customEmailRef) {
              try {
                await customEmailRef.validateCustomEmailForm();
              } catch (error) {
                vm.handleMoveCandidateToTalentPoolError(error);
              }
            }
            vm.closeWindow();
            vm.$emit("refetch-data");
          }
          vm.isLoading = false;
        }
      } catch (error) {
        vm.isLoading = false;
        this.overlay = true;
        vm.handleMoveCandidateToTalentPoolError(error);
      }
    },
    handleMoveCandidateToTalentPoolError(err = "") {
      this.$emit("refetch-data");
      this.isLoading = false;
      this.trackingStatusLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "adding",
        form: "candidate to talent pool",
        isListError: false,
      });
    },
    getArchivedSatusId() {
      let archivedItem = this.originalStatusList.find(
        (item) => item.Status?.toLowerCase() === "rejected"
      );
      this.archiveStatusId = archivedItem ? archivedItem.Id : null;
    },
    getFormattedDate(selectedItem) {
      if (!selectedItem) {
        return null;
      }
      let date = moment().utc();

      switch (selectedItem) {
        case "Now":
          date = null;
          break;
        case "After 2 Hours":
          date = date.add(2, "hours");
          break;
        case "After 8 Hours":
          date = date.add(8, "hours");
          break;
        case "After 24 Hours":
          date = date.add(24, "hours");
          break;
        case "After 48 Hours":
          date = date.add(48, "hours");
          break;
        default:
          return "Invalid selection";
      }
      return date ? date.format("YYYY-MM-DD HH:mm:ss") : null;
    },
    async validateTalentPoolForm() {
      const { valid } = await this.$refs.TalentPoolAddForm.validate();

      if (valid) {
        this.addTalentPool();
      }
    },
    async addTalentPool() {
      let vm = this;
      try {
        vm.isLoading = true;
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_TALENT_POOL,
            client: "apolloClientAV",
            fetchPolicy: "no-cache",
            variables: {
              talentPoolId: 0,
              talentPool: vm.talentPool.trim(),
            },
          })
          .then((response) => {
            if (
              response &&
              response.data &&
              response.data.addUpdateTalentPool
            ) {
              const { errorCode, validationError } =
                response.data.addUpdateTalentPool;
              if (!errorCode && !validationError) {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Talent pool added successfully.",
                };
                vm.showAlert(snackbarData);
                vm.openAddTalentPoolField = false;

                // Resetting form state
                vm.fetchTalentPoolList();
                vm.isLoading = false;
                vm.talentPool = "";
              } else {
                vm.isLoading = false;
                vm.handleAddEditError();
              }
            } else {
              vm.isLoading = false;
              vm.handleAddEditError();
            }
          })
          .catch((error) => {
            vm.isLoading = false;
            vm.handleAddEditError(error);
          });
      } catch (error) {
        vm.handleAddEditError(error);
      }
    },
    handleAddEditError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "adding",
        form: "talentPool",
        isListError: false,
      });
    },
    fetchArchiveReasonList() {
      let vm = this;
      vm.isArchiveReasonLoading = true;
      vm.$apollo
        .query({
          query: GET_ARCHIVE_REASONS_LIST,
          client: "apolloClientAY",
          variables: {
            formId: 16,
            stageId: this.candidateDetails?.Hiring_Stage_Id,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getArchiveReasonsList &&
            response.data.getArchiveReasonsList.archiveReasonList &&
            !response.data.getArchiveReasonsList.archiveReasonList.errorCode
          ) {
            vm.archiveReasonList =
              response.data.getArchiveReasonsList.archiveReasonList;
            vm.isArchiveReasonLoading = false;
          } else {
            vm.archiveReasonList = [];
            vm.isArchiveReasonLoading = false;
          }
        })
        .catch((err) => {
          vm.isArchiveReasonLoading = false;
          vm.handleFetchArchiveReasonListError(err);
        });
    },
    handleFetchArchiveReasonListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "archive reason list",
        isListError: false,
      });
    },
    replaceTags(template, replacementTags, templateData) {
      for (const [tag, replacement] of Object.entries(replacementTags)) {
        if (
          template.includes(tag) &&
          templateData.hasOwnProperty(replacement)
        ) {
          // Check if tag exists in template and replacement data exists
          template = template.replace(
            new RegExp("\\" + tag, "g"),
            templateData[replacement]
          );
        }
      }
      return template;
    },
    fetchEmailTemplates() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: LIST_CUSTOM_EMAIL_TEMPLATES,
          variables: {
            formId: 16,
            categoryId: 8,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listCustomEmailTemplates &&
            response.data.listCustomEmailTemplates.emailTemplates
          ) {
            vm.emailTemplateList =
              response.data.listCustomEmailTemplates.emailTemplates;
            vm.noCustomTemplate =
              vm.emailTemplateList?.length === 0 ? true : false;
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.isLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "email templates",
        isListError: false,
      });
    },
    getReasonById(reasons, reasonId) {
      const result = reasons.find((item) => item.Reason_Id === reasonId);
      return result ? result.Reason : "";
    },
  },
};
</script>

<style scoped>
.overlay {
  height: 100% !important;
}

.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
