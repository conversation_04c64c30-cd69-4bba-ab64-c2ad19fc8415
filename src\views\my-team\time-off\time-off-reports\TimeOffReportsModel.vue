<template>
  <div>
    <v-dialog
      v-model="showModel"
      :width="isMobileView ? '90%' : '60%'"
      persistent
      scrollable
      style="z-index: 1000"
    >
      <v-card class="rounded-lg" :min-height="windowWidth > 700 ? 400 : ''">
        <div class="d-flex justify-end">
          <v-icon color="primary" class="pr-4 pt-4" @click="closeAllForms()"
            >fas fa-times</v-icon
          >
        </div>
        <v-card-text>
          <div
            class="d-flex align-center flex-column font-weight-bold modal-heading text-primary"
          >
            {{ checkNullValue(selectedItem?.Rep_Title) }}
          </div>
          <v-form ref="reportForm">
            <v-row v-if="filters?.length" class="pt-2">
              <v-col
                cols="12"
                xs="12"
                md="4"
                v-for="(item, index) in filters"
                :key="index"
              >
                <!-- Date Range -->
                <div v-if="item.fieldType?.toLowerCase() == `dpicker`">
                  <p class="text-caption mt-n2">
                    {{ item.labels }}
                    <span v-if="item.isRequired" style="color: red">*</span>
                  </p>
                  <v-btn
                    class="bg-white date-btn"
                    :class="{ 'mobile-date-btn': isMobileView }"
                    :style="isMobileView ? 'width: 100%' : 'width: max-content'"
                    rounded="lg"
                    @click="$refs['datePicker' + index].fp?.open()"
                  >
                    <v-icon color="primary" size="14"
                      >fas fa-calendar-alt</v-icon
                    >
                    <span class="text-caption px-1 pt-1">Date:</span>
                    <flat-pickr
                      :ref="'datePicker' + index"
                      v-model="item.selectedValue"
                      :config="flatPickerOptions"
                      placeholder="Select Date Range"
                      clearable
                      class="ml-2 mt-1 date-range-picker-custom-bg"
                      :class="{ 'mobile-date-picker': isMobileView }"
                      :style="{
                        outline: '0px',
                        color: 'var(--v-primary-base)',
                        width: isMobileView ? '100%' : '190px',
                        maxWidth: isMobileView ? '150px' : '190px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }"
                    />
                  </v-btn>
                </div>
                <!-- Month Picker -->
                <datepicker
                  v-else-if="item.fieldType?.toLowerCase() == `mpicker`"
                  :ref="'monthPicker' + index"
                  :format="'MMMM, yyyy'"
                  :placeholder="item.labels"
                  v-model="item.selectedValue"
                  clearable
                  maximum-view="year"
                  minimum-view="month"
                  :class="isMobileView ? 'mb-2' : ''"
                  :style="{
                    width: '100%',
                    height: '30px',
                  }"
                />
                <!-- text Fileds -->
                <v-text-field
                  v-else-if="
                    item.labels?.toLowerCase() == `employee name` ||
                    item.labels?.toLowerCase() == `manager name` ||
                    item.labels?.toLowerCase() == `name`
                  "
                  ref="textFields"
                  v-model="item.selectedValue"
                  :rules="[
                    item.isRequired
                      ? required(item.labels, item.selectedValue)
                      : true,
                  ]"
                  variant="solo"
                  density="comfortable"
                  clearable
                  ><template v-slot:label>
                    {{ item.labels }}
                    <span v-if="item.isRequired" style="color: red">*</span>
                  </template>
                </v-text-field>
                <!-- Number Fileds -->
                <v-text-field
                  v-else-if="item.fieldType?.toLowerCase() == `amount`"
                  ref="numberFields"
                  v-model="item.selectedValue"
                  variant="solo"
                  density="comfortable"
                  type="number"
                  clearable
                  ><template v-slot:label>
                    {{ item.labels }}
                    <span v-if="item.isRequired" style="color: red">*</span>
                  </template>
                </v-text-field>
                <!-- All Dropdowns with special handling for departments -->
                <CustomSelect
                  v-else
                  v-model="item.selectedValue"
                  :items="
                    item.fieldType?.toLowerCase().includes('department')
                      ? formatDepartmentItems(item.listItems)
                      : item.listItems
                  "
                  item-title="value"
                  item-value="id"
                  :isAutoComplete="true"
                  :itemSelected="item.selectedValue"
                  :label="item.labels"
                  variant="solo"
                  density="comfortable"
                  clearable
                  :isRequired="item.isRequired"
                  :rules="
                    item.isRequired
                      ? [required(item.labels, item.selectedValue)]
                      : [true]
                  "
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <div class="text-center pb-10">
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            @click="validateReport()"
          >
            <v-icon size="15" class="pr-2 fas fa-file-export" /> Export
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <app-loading v-if="isLoading" />
  </div>
</template>
<script>
import { checkNullValue } from "@/helper";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import Datepicker from "vuejs3-datepicker";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
export default {
  name: "TimeOffReportsModel",
  data() {
    return {
      showModel: true,
      isLoading: false,
      dropdownLoading: false,
      filters: [],
      selectedFilter: null,
      selectedItem: {},
    };
  },
  components: {
    CustomSelect,
    flatPickr,
    Datepicker,
  },
  mixins: [FileExportMixin, validationRules],
  props: {
    reportData: {
      type: Object,
      required: true,
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
      };
    },
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    this.getreportDetails(this.reportData);
  },
  methods: {
    async getreportDetails(report = "") {
      let vm = this;
      if (Object.keys(report || {}).length == 0) {
        vm.closeAllForms();
        return;
      }
      vm.isLoading = true;
      try {
        const apiObj = {
          url: vm.baseUrl + "reports/hr-reports/get-report-filter-details",
          type: "POST",
          dataType: "json",
          data: {
            reportId: [report?.Rep_Id || 0],
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          vm.selectedItem = response?.reportDetails[0] || {};
          vm.prefillDetails();
        } else {
          let snackbarData = {
            isOpen: true,
            type: response?.type || "warning",
            message:
              response?.msg ||
              "Something went wrong. Please try after some time.",
          };
          vm.showAlert(snackbarData);
          vm.closeAllForms();
        }
      } catch (err) {
        vm.showAlert({
          isOpen: true,
          type: "warning",
          message: err || "Something went wrong. Please try again later.",
        });
        vm.closeAllForms();
      } finally {
        vm.isLoading = false;
      }
    },
    prefillDetails() {
      if (this.selectedItem && Object.keys(this.selectedItem || {}).length) {
        const filterLabels = this.selectedItem.Rep_Filter?.split("-");
        const filterTables = this.selectedItem.Filter_Table?.split(",") || [];

        this.filters =
          filterLabels?.map((labels, index) => {
            // Check if the label is Service Provider and replace with alias if available
            let labelText = labels || "";
            if (labelText.toLowerCase() === "service provider") {
              labelText =
                this.labelList[115]?.Field_Alias || "Service Provider";
            }

            // Determine if the field is required
            let isFieldRequired = false;

            // Make Employee Name required when Rep_Title is "Employee Step Increment"
            if (
              labelText.toLowerCase() === "employee name" &&
              this.selectedItem?.Rep_Title === "Employee Step Increment"
            ) {
              isFieldRequired = true;
            }

            // Check if there's a default filter value for this label
            let defaultValue = null;
            if (this.selectedItem.Default_Filter && labelText) {
              // Check if this label exists in Default_Filter
              const defaultFilterKey = Object.keys(
                this.selectedItem.Default_Filter
              ).find((key) => key.toLowerCase() === labelText.toLowerCase());

              if (defaultFilterKey) {
                const defaultFilterValue =
                  this.selectedItem.Default_Filter[defaultFilterKey];

                // Handle date picker fields
                if (
                  filterTables[index]?.toLowerCase() === "dpicker" &&
                  defaultFilterValue &&
                  defaultFilterValue.from_date &&
                  defaultFilterValue.to_date
                ) {
                  // Get org date format
                  let orgDateFormat =
                    this.$store.state.orgDetails.orgDateFormat;

                  // Format dates according to org format
                  const fromDate = moment(defaultFilterValue.from_date).format(
                    orgDateFormat
                  );
                  const toDate = moment(defaultFilterValue.to_date).format(
                    orgDateFormat
                  );

                  // Create the date range string
                  defaultValue = `${fromDate} to ${toDate}`;
                } else {
                  // For other field types, just use the value directly
                  defaultValue = defaultFilterValue;
                }
              }
            }

            return {
              labels: labelText,
              fieldType: filterTables[index] || null,
              listItems: this.getListItemsFromFilterData(index), // add list items
              selectedValue: defaultValue,
              isRequired: isFieldRequired,
            };
          }) || [];
      }
    },
    // Build hierarchical structure for departments
    buildHierarchy(departments) {
      if (!departments || !Array.isArray(departments)) {
        return [];
      }

      const departmentMap = {};
      const hierarchy = [];

      // Convert items to department structure if needed
      const deptItems = [];

      // First pass: Create department objects
      departments.forEach((item) => {
        if (!item) return;

        // Handle case where item.value is an object with Department structure
        if (
          item.value &&
          typeof item.value === "object" &&
          item.value.Department_Id
        ) {
          // This is a department with nested structure
          const dept = {
            Department_Id: item.value.Department_Id,
            Department_Name: item.value.Department_Name || "",
            Parent_Type_Id: 0, // Default as root
            originalItem: item,
            hasChildren:
              item.value.Child &&
              Array.isArray(item.value.Child) &&
              item.value.Child.length > 0,
          };

          deptItems.push(dept);

          // Process children if they exist
          if (dept.hasChildren && item.value.Child) {
            item.value.Child.forEach((childItem) => {
              if (childItem && childItem.Department_Id) {
                deptItems.push({
                  Department_Id: childItem.Department_Id,
                  Department_Name: childItem.Department_Name || "",
                  Parent_Type_Id: dept.Department_Id, // Set parent ID to connect to parent
                  originalItem: {
                    id: childItem.Department_Id,
                    value: childItem,
                  },
                  hasChildren:
                    childItem.Child &&
                    Array.isArray(childItem.Child) &&
                    childItem.Child.length > 0,
                });

                // Process second level children if they exist
                if (
                  childItem.Child &&
                  Array.isArray(childItem.Child) &&
                  childItem.Child.length > 0
                ) {
                  childItem.Child.forEach((grandChild) => {
                    if (grandChild && grandChild.Department_Id) {
                      deptItems.push({
                        Department_Id: grandChild.Department_Id,
                        Department_Name: grandChild.Department_Name || "",
                        Parent_Type_Id: childItem.Department_Id,
                        originalItem: {
                          id: grandChild.Department_Id,
                          value: grandChild,
                        },
                        hasChildren: false,
                      });
                    }
                  });
                }
              }
            });
          }
        } else {
          // Default case - simple string value
          deptItems.push({
            Department_Id: item.id || "",
            Department_Name: typeof item.value === "string" ? item.value : "",
            Parent_Type_Id: 0,
            originalItem: item,
            hasChildren: false,
          });
        }
      });

      // Map all departments by their Department_Id
      deptItems.forEach((dept) => {
        if (dept && dept.Department_Id) {
          departmentMap[dept.Department_Id] = { ...dept, children: [] };
        }
      });

      // Organize departments into a hierarchical structure
      deptItems.forEach((dept) => {
        if (!dept || !dept.Department_Id) return;

        if (!dept.Parent_Type_Id || dept.Parent_Type_Id === 0) {
          // If it's a root department, add it to the hierarchy
          hierarchy.push(departmentMap[dept.Department_Id]);
        } else if (departmentMap[dept.Parent_Type_Id]) {
          // Else, add it to its parent's children array if parent exists
          departmentMap[dept.Parent_Type_Id].children.push(
            departmentMap[dept.Department_Id]
          );
        } else {
          // If parent doesn't exist, add as root
          hierarchy.push(departmentMap[dept.Department_Id]);
        }
      });

      return hierarchy;
    },

    // Flatten hierarchical structure with indentation levels
    flattenTree(items, level = 0) {
      if (!items || !Array.isArray(items)) {
        return [];
      }

      let flattened = [];
      items.forEach((item) => {
        if (!item) return;

        // Add indentation based on the level of hierarchy
        const originalItem = item.originalItem || {};

        // For department items, we need to format them differently
        if (item.Department_Name) {
          // Create a formatted display name with indentation
          const displayText = "  ".repeat(level) + (item.Department_Name || "");

          // If this is from a nested structure with Department_Id
          if (item.originalItem?.value?.Department_Id) {
            flattened.push({
              id: item.Department_Id || "",
              // Use the Department_Name as the value for display
              value: displayText,
              level: level,
              originalItem: originalItem,
            });
          } else {
            // For regular items
            flattened.push({
              id: item.Department_Id || "",
              value: displayText,
              level: level,
              originalItem: originalItem,
            });
          }
        } else {
          // Default case for non-department items
          flattened.push({
            id: item.Department_Id || "",
            value: item.Department_Name || "",
            level: level,
            originalItem: originalItem,
          });
        }

        // Recursively add children with increased indentation
        if (item.children && item.children.length > 0) {
          flattened = flattened.concat(
            this.flattenTree(item.children, level + 1)
          );
        }
      });
      return flattened;
    },

    // Format department items for the dropdown
    formatDepartmentItems(items) {
      // Return empty array if items is null or undefined
      if (!items || !Array.isArray(items)) {
        return [];
      }

      // Build hierarchy and flatten it for display
      const hierarchy = this.buildHierarchy(items);
      return this.flattenTree(hierarchy);
    },
    getListItemsFromFilterData(index) {
      const filterData = this.selectedItem?.Filter_Data;
      if (
        filterData &&
        filterData[index] &&
        Object.keys(filterData[index] || {}).length
      ) {
        return Object.entries(filterData[index] || {}).map(([key, value]) => ({
          id: key || "",
          value: value || "",
        }));
      }
      return [];
    },
    async validateReport() {
      const { valid } = await this.$refs.reportForm.validate();
      if (valid) {
        this.exportReports();
      }
    },
    exportReports() {
      let filterArray =
        this.filters?.map((el) => {
          if (
            el &&
            el.fieldType?.toLowerCase() === "dpicker" &&
            el.selectedValue
          ) {
            let dateParts = el.selectedValue.split(" to ");
            if (dateParts.length === 2) {
              // Get the organization date format
              let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;

              // Parse dates using moment with the correct format
              let startDate = moment(dateParts[0], orgDateFormat).format(
                "YYYY-MM-DD"
              );
              let endDate = moment(dateParts[1], orgDateFormat).format(
                "YYYY-MM-DD"
              );
              return `${startDate}&${endDate}`;
            }
          } else if (el && el.fieldType?.toLowerCase() === "mpicker") {
            return el.selectedValue
              ? moment(el.selectedValue).format("YYYY-MM")
              : "";
          } else {
            return el.selectedValue || "";
          }
        }) || [];

      // Convert to a comma-separated string
      let filterString = filterArray?.join(",") || "";
      this.exportReportFile(
        "",
        this.selectedItem?.Rep_Title || "",
        "employees",
        filterString,
        "",
        ""
      );
    },
    async exportReportFile(
      loanId,
      linkValue,
      modName,
      filterArray,
      checkedColumns = "",
      filterGroupBy = ""
    ) {
      this.isLoading = true;
      try {
        const directDownloadReports = [
          "Tds",
          "Payment Register",
          "Insurance Statement",
          "Ssnit Tier 1",
          "Ssnit Tier 2",
          "Provident Fund Detailed Report",
          "Reimbursement Allowances",
          "Hourly Wage Payslip",
          "Monthly Master Report",
          "Hourly Master Report",
          "Esi Monthly",
          "Esi Hourly",
          "Esic Monthly",
          "Esic Hourly",
          "Attendance Summary Hourly",
          "Attendance Summary Monthly",
          "Employee Utilization",
          "Additional Wage Summary",
          "Uan Based Ecr",
          "Uan Based Ecr Hourly",
          "Eft Monthly",
          "Eft Hourly",
          "Loan Amortization",
          "Attendance Shortage",
          "Employee Wise Expenses",
          "Reimbursement",
          "Pay Bill",
          "Monthly Payslip Comprehensive",
          "Monthly Salary",
          "Employee Status",
          "Lop Recovery",
          "Employee Step Increment",
        ];

        const exportUrl = {
          type: "POST",
          async: false,
          dataType: "json",
          url: `${this.baseUrl}reports/hr-reports/export-csv/disGrid/s/loanId/${loanId}/linkValue/${linkValue}/_modName/${modName}/filterArray/${filterArray}/checkedColumns/${checkedColumns}/filterGroupBy/${filterGroupBy}`,
        };

        if (directDownloadReports.includes(linkValue)) {
          // Direct download for specific reports
          window.open(exportUrl.url, "_blank");
        } else {
          const result = await this.$store.dispatch(
            "triggerControllerFunction",
            exportUrl
          );
          if (
            result &&
            Object.keys(result || {}).length &&
            result.fileExportData?.length
          ) {
            let exportOptions = {
              header: result.fileHeader,
              sheetName: result.reportTitle,
              fileExportData: result.fileExportData,
              fileName: result.reportTitle,
              organizationName: result.organizationName,
              reportTitle: result.reportTitle,
              createdBy: result.createdBy,
              createdOn: result.createdOn,
              firstHeaderData: result.firstHeaderData,
              firstHeaderDetails: result.firstHeaderDetails,
              footerData: result.footerData,
              headerReference: result.headerReference,
              headerFormula: result.headerFormula,
            };
            this.exportExcelFile(exportOptions);
          } else
            this.showAlert({
              isOpen: true,
              message: `There are no employees for ${result?.reportTitle} report`,
              type: "warning",
            });
        }
      } catch (err) {
        this.showAlert({
          isOpen: true,
          message: err || "Something went wrong. Please contact system admin",
          type: "warning",
        });
      } finally {
        this.isLoading = false;
        this.closeAllForms();
      }
    },
    closeAllForms() {
      this.showModel = false;
      this.$emit("close-model");
    },
    checkNullValue,
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style scoped>
.modal-heading {
  font-size: 1.3em;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__value) {
  min-width: 160px;
  display: flex;
  align-items: center;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__calendar) {
  right: 1px;
}
/* Responsive flat-pickr styling */
.date-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  transition: all 0.3s ease;
  max-width: 100%;
  overflow: hidden;
}

.mobile-date-btn {
  padding: 6px 8px;
  font-size: 0.9rem;
  width: 100% !important;
}

/* Mobile specific styles for flat-pickr */
.mobile-date-picker {
  width: 150px !important;
  font-size: 0.9rem !important;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Media queries for responsive design */
@media (max-width: 600px) {
  .date-btn {
    width: 100% !important;
    justify-content: flex-start;
  }
}
</style>
