<template>
  <v-overlay
    :model-value="showAddEditForm"
    @click:outside="onCloseEditForm()"
    persistent
    class="d-flex justify-end"
    style="z-index: 1000"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:50vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">
            {{ isEdit ? "Edit" : "Add" }}
            {{ landedFormName }}
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseEditForm()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <div class="px-5 py-6">
            <v-form ref="addEditFormValidator">
              <v-row>
                <v-col v-if="!isManualEnterForAddress" cols="12">
                  <VCard>
                    <vue-google-autocomplete
                      id="pMap"
                      ref="pGoogleVal"
                      classname="form-control pa-5 google-auto-complete-address-field"
                      placeholder="Please type your address"
                      v-on:placechanged="setAddress"
                      :enable-geolocation="true"
                      :fields="['address_components', 'geometry']"
                    />
                  </VCard>
                </v-col>
                <v-col cols="12">
                  <div>
                    <v-checkbox-btn
                      v-model="isManualEnterForAddress"
                      color="primary"
                      false-icon="far fa-square"
                      true-icon="fas fa-check-square"
                      indeterminate-icon="fas fa-minus-circle"
                      class="mt-1"
                      @change="isFormDirty = true"
                    >
                      <template v-slot:label>
                        <div class="text-grey-darken-1" style="font-size: 16px">
                          No autocomplete address found
                        </div>
                      </template>
                    </v-checkbox-btn>
                  </div>
                </v-col>
                <v-col cols="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="selectedDesignationCity"
                    variant="solo"
                    :rules="[
                      required('Destination City', selectedDesignationCity),
                      validateWithRulesAndReturnMessages(
                        selectedDesignationCity,
                        'city',
                        'Destination City'
                      ),
                    ]"
                    :disabled="!isManualEnterForAddress"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      Destination City<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </v-col>

                <v-col cols="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="selectedDesignationCountry"
                    variant="solo"
                    :rules="[
                      required(
                        'Destination Country',
                        selectedDesignationCountry
                      ),
                      validateWithRulesAndReturnMessages(
                        selectedDesignationCountry,
                        'country',
                        'Destination Country'
                      ),
                    ]"
                    :disabled="!isManualEnterForAddress"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      <span>Destination Country</span>
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>

                <v-col cols="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="selectedAirTicketCategory"
                    variant="solo"
                    ref="ticketClaim"
                    :rules="[
                      required(
                        'Air Ticket Category',
                        selectedAirTicketCategory
                      ),
                      validateWithRulesAndReturnMessages(
                        selectedAirTicketCategory,
                        'airTicketingCategory',
                        'Air Ticket Category'
                      ),
                    ]"
                    clearable
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      <span>Air Ticket Category</span>
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Air Fare Entitlement for Infant -->
                <v-col cols="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="selectedInfantAmount"
                    variant="solo"
                    type="number"
                    min="0"
                    max="2000000000"
                    :rules="[
                      minMaxNumberValidation(
                        'Air Fare Entitlement for Infant',
                        parseInt(selectedInfantAmount),
                        0,
                        2000000000
                      ),
                      validateDecimal(selectedInfantAmount),
                    ]"
                    @keypress="validateNumericInput($event)"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      Air Fare Entitlement for Infant
                      <span v-if="payrollCurrency">
                        (in {{ payrollCurrency }})</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Air Fare Entitlement for Child -->
                <v-col cols="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="selectedChildAmount"
                    variant="solo"
                    type="number"
                    min="0"
                    max="2000000000"
                    :rules="[
                      minMaxNumberValidation(
                        'Air Fare Entitlement for Child',
                        parseInt(selectedChildAmount),
                        0,
                        2000000000
                      ),
                      validateDecimal(selectedChildAmount),
                    ]"
                    @keypress="validateNumericInput($event)"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      Air Fare Entitlement for Child
                      <span v-if="payrollCurrency">
                        (in {{ payrollCurrency }})</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Air Fare Entitlement for Adult -->
                <v-col cols="6" class="px-md-6 pb-0 mb-2">
                  <v-text-field
                    v-model="selectedAdultAmount"
                    variant="solo"
                    type="number"
                    min="1"
                    max="2000000000"
                    :rules="[
                      required(
                        'Air Fare Entitlement for Adult',
                        selectedAdultAmount
                      ),
                      minMaxNumberValidation(
                        'Air Fare Entitlement for Adult',
                        parseInt(selectedAdultAmount),
                        1,
                        2000000000
                      ),
                      validateDecimal(selectedAdultAmount),
                    ]"
                    @keypress="validateNumericInput($event)"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      Air Fare Entitlement for Adult
                      <span v-if="payrollCurrency">
                        (in {{ payrollCurrency }})</span
                      >
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>

                <v-col cols="6" class="px-md-6 pb-0 mb-2 d-flex align-center">
                  <div class="v-label mr-4">
                    <span>Status</span>
                  </div>
                  <AppToggleButton
                    button-active-text="Active"
                    button-inactive-text="InActive"
                    button-active-color="#7de272"
                    button-inactive-color="red"
                    :isDisableToggle="isEdit ? false : true"
                    id-value="gab-analysis-based-on"
                    :current-value="
                      selectedStatus?.toLowerCase() === 'active' ? true : false
                    "
                    @chosen-value="onChangeStatus($event)"
                    @update:model-value="isFormDirty = true"
                  />
                </v-col>
              </v-row>
            </v-form>
            <div class="card-actions-div">
              <v-sheet class="align-center text-center" style="width: 100%">
                <v-col cols="12" class="d-flex justify-end pr-4">
                  <v-btn
                    rounded="lg"
                    class="primary mr-6"
                    variant="outlined"
                    @click="onCloseEditForm()"
                  >
                    Cancel
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    :disabled="!isFormDirty"
                    class="mr-1 primary"
                    variant="elevated"
                    @click="validateForm()"
                  >
                    Save
                  </v-btn>
                </v-col>
              </v-sheet>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </template>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  />
  <AppLoading v-if="isLoading" />
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import VueGoogleAutocomplete from "vue-google-autocomplete";
import { ADD_UPDATE_AIR_TICKET_SETTINGS } from "@/graphql/corehr/payrollDataManagement.js";
export default {
  name: "AddEditAitTicketPolicy",
  components: {
    VueGoogleAutocomplete,
  },
  mixins: [validationRules],

  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    originalList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      showAddEditForm: true,
      // add/update
      isManualEnterForAddress: false,
      selectedDesignationCity: "",
      selectedDesignationCountry: "",
      selectedAirTicketCategory: "",
      selectedInfantAmount: null,
      selectedChildAmount: null,
      selectedAdultAmount: null,
      selectedStatus: this.isEdit ? "" : "Active",
      isFormDirty: false,
      // loading/error/other
      isLoading: false,
      isDeptHeaderLoding: false,
      openConfirmationPopup: false,
      validationMessages: [],
      showValidationAlert: false,
      selectedItem: null,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
  },
  watch: {
    isManualEnterForAddress() {
      if (this.$refs.pGoogleVal) this.$refs.pGoogleVal.clear();
      this.isFormDirty = true;
    },
  },
  mounted() {
    if (
      this.editFormData &&
      Object.keys(this.editFormData)?.length &&
      this.isEdit
    ) {
      this.isManualEnterForAddress = true;
      this.selectedDesignationCity = this.editFormData.Destination_City;
      this.selectedDesignationCountry = this.editFormData.Destination_Country;
      this.selectedAirTicketCategory = this.editFormData.Air_Ticketing_Category;
      this.selectedAdultAmount = this.editFormData?.Adult_Amount;
      this.selectedInfantAmount = this.editFormData?.Infant_Amount;
      this.selectedChildAmount = this.editFormData?.Child_Amount;
      this.selectedStatus = this.editFormData.Status;
    }
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.showAddEditForm = false;
      this.$emit("close-form");
    },

    // change the mode of performance management
    onChangeStatus(value) {
      this.selectedStatus = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },
    validateNumericInput(event) {
      const char = String.fromCharCode(event.keyCode);

      // Allow only numbers and a single decimal point
      if (!/[\d.]/.test(char)) {
        event.preventDefault();
        return;
      }

      // Prevent multiple decimal points
      if (char === "." && event.target.value.includes(".")) {
        event.preventDefault();
      }
    },
    validateDecimal(value) {
      if (!value) return true; // Allow empty values if not required
      const regex = /^\d+(\.\d{1,2})?$/; // Allows up to 2 decimal places
      return regex.test(value)
        ? true
        : "Provide Amount up to 2 decimal places.";
    },

    async validateForm() {
      const { valid } = await this.$refs.addEditFormValidator.validate();
      if (valid) {
        this.addUpdateFormData();
      }
    },
    setAddress(addressData) {
      this.resetAddress();
      this.selectedDesignationCity = addressData.locality;
      this.selectedDesignationCountry = addressData.country;
      this.isManualEnterForAddress = true;
    },
    resetAddress() {
      this.selectedDesignationCity = "";
      this.selectedDesignationCountry = "";
    },

    addUpdateFormData() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_AIR_TICKET_SETTINGS,
          variables: {
            formId: 319,
            airTicketSettingId: vm.isEdit
              ? vm.editFormData.Air_Ticket_Setting_Id
              : 0,
            city: vm.selectedDesignationCity,
            country: vm.selectedDesignationCountry,
            airTicketingCategory: vm.selectedAirTicketCategory,
            infantAmount: vm.selectedInfantAmount
              ? parseFloat(vm.selectedInfantAmount)
              : null,
            childAmount: vm.selectedChildAmount
              ? parseFloat(vm.selectedChildAmount)
              : null,
            adultAmount: vm.selectedAdultAmount
              ? parseFloat(vm.selectedAdultAmount)
              : null,
            status: vm.isEdit ? vm.selectedStatus : "Active",
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.isEdit
              ? vm.landedFormName + " updated successfully."
              : vm.landedFormName + " added successfully.",
          };
          vm.showAlert(snackbarData);
          vm.$emit("edit-updated");
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.landedFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>

<style scoped>
#integration-form > .v-overlay__content {
  height: 100%;
  width: 50%;
}
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.google-auto-complete-address-field {
  width: 100% !important;
  border: none !important;
}
.google-auto-complete-address-field:focus {
  outline: none !important;
}
</style>
