<template>
  <div>
    <div v-if="itemList.length > 0">
      <div
        class="d-flex flex-wrap align-center my-3"
        :class="isMobileView ? 'flex-column' : ''"
        style="justify-content: space-between"
      >
        <div
          class="d-flex align-center"
          :class="isMobileView ? 'justify-center' : ''"
        >
          <v-btn
            rounded="lg"
            style="pointer-events: none"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            >Settled Employees:
            <span class="text-primary">{{ settledEmployees }}</span></v-btn
          >
          <v-btn
            rounded="lg"
            class="ml-2"
            style="pointer-events: none"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            >Employees To Be Settled:
            <span class="text-primary">{{ notSettledEmployees }}</span></v-btn
          >
        </div>
        <div
          class="d-flex align-center"
          :class="isMobileView ? 'justify-center' : 'justify-end'"
        >
          <v-btn
            v-if="restrictAccess && accessRights.add"
            color="primary"
            rounded="lg"
            class="mt-1"
            @click="getResignedEmployeesList()"
            :size="isMobileView ? 'small' : 'default'"
          >
            <v-icon size="15" class="pr-1">fas fa-plus</v-icon>Initiate
            Settlement</v-btn
          >
          <v-btn
            color="white"
            rounded="lg"
            class="ml-1 mt-1"
            :size="isMobileView ? 'small' : 'default'"
            @click="$emit('refetch-list')"
            ><v-icon size="15">fas fa-redo-alt</v-icon></v-btn
          >
          <v-menu v-model="openMoreMenu" transition="scale-transition">
            <template v-slot:activator="{ props }">
              <v-btn variant="plain" class="mt-1 ml-n2 mr-n5" v-bind="props">
                <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                <v-icon v-else>fas fa-caret-up</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="action in moreActions"
                :key="action.key"
                @click="onMoreAction(action.key)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <v-list-item-title
                      v-bind="props"
                      class="pa-3"
                      :class="{
                        'pink-lighten-5': isHovering,
                      }"
                      ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                      >{{ action.key }}</v-list-item-title
                    >
                  </template>
                </v-hover>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <v-data-table
        :headers="tableHeaders"
        :items="itemList"
        class="elevation-1"
        fixed-header
        :height="
          $store.getters.getTableHeightBasedOnScreenSize(290, itemList, true)
        "
        style="box-shadow: none !important"
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
      >
        <template v-slot:item="{ item }">
          <tr
            @click="onSelectItem(item)"
            class="data-table-tr bg-white cursor-pointer"
            :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
          >
            <td :class="isMobileView ? 'd-flex justify-space-between' : ''">
              <div v-if="isMobileView" class="font-weight-bold mt-2">
                Employee
              </div>
              <section class="d-flex align-center">
                <div class="text-body-2 text-primary font-weight-medium">
                  {{ item.employeeName ? item.employeeName : "-" }}
                  <div v-if="item.userDefinedEmpId" class="text-grey">
                    {{ item.userDefinedEmpId }}
                  </div>
                </div>
              </section>
            </td>
            <td :class="isMobileView ? 'd-flex justify-space-between' : ''">
              <div v-if="isMobileView" class="font-weight-bold mt-2">
                Settlement Initiated Date
              </div>
              <section class="text-body-2 text-primary">
                {{
                  item.settlementDate ? formatDate(item.settlementDate) : "-"
                }}
              </section>
            </td>
            <td :class="isMobileView ? 'd-flex justify-space-between' : ''">
              <div v-if="isMobileView" class="font-weight-bold">Net Pay</div>
              <section class="text-primary text-body-2">
                {{
                  item.netPay || parseInt(item.netPay) === 0 ? item.netPay : "-"
                }}
              </section>
            </td>
            <td :class="isMobileView ? 'd-flex justify-space-between' : ''">
              <div v-if="isMobileView" class="font-weight-bold">Status</div>
              <section class="text-body-2 font-weight-medium text-primary">
                {{ item.settlementStatus ? item.settlementStatus : "-" }}
              </section>
            </td>
            <td
              v-if="restrictAccess"
              :class="
                isMobileView ? 'd-flex justify-space-between align-center' : ''
              "
              :style="isMobileView ? '' : 'max-width: 150px'"
            >
              <div v-if="isMobileView" class="font-weight-bold">Actions</div>
              <section class="d-flex align-center">
                <v-tooltip text="Approve" location="bottom">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      v-if="
                        item.settlementStatus === 'Pending Approval' &&
                        accessRights.update
                      "
                      v-bind="props"
                      color="green"
                      class="mr-2"
                      size="30"
                      @click.stop="approveSettlement(item)"
                      >far fa-check-circle
                    </v-icon>
                  </template>
                </v-tooltip>

                <v-tooltip
                  text="Unlock to make changes to the full & final settlement"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <span
                      v-if="
                        item.settlementStatus === 'Yet to be settled' &&
                        accessRights.update
                      "
                      v-bind="props"
                      @click.stop="reopenSettlement(item)"
                      class="rounded-border text-amber mr-2"
                    >
                      <v-icon color="amber" size="15">fas fa-lock</v-icon>
                    </span>
                  </template>
                </v-tooltip>

                <v-tooltip text="Delete" location="bottom">
                  <template v-slot:activator="{ props }">
                    <span
                      v-if="
                        item.settlementStatus !== 'Settled' &&
                        accessRights.delete
                      "
                      v-bind="props"
                    >
                      <v-avatar
                        color="red"
                        size="30"
                        @click.stop="onDelete(item)"
                      >
                        <v-icon color="white" size="15"
                          >fas fa-trash-alt</v-icon
                        ></v-avatar
                      >
                    </span>
                  </template>
                </v-tooltip>
                <div
                  v-if="item.settlementStatus === 'Settled'"
                  class="text-body-2 text-primary pl-2"
                >
                  For amending the settlement details, delete the
                  <a
                    @click.stop="
                      {
                      }
                    "
                    class="text-primary"
                    :href="baseUrl + 'payroll/salary-payslip'"
                    >payslip</a
                  >
                  for the selected payroll month
                </div>
              </section>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
    <AppFetchErrorScreen
      v-else
      key="no-results-screen"
      :main-title="emptyScenarioMsg"
      :isSmallImage="originalList.length === 0"
      :image-name="originalList.length === 0 ? '' : 'common/no-records'"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row
            :style="originalList.length === 0 ? 'background: white' : ''"
            class="rounded-lg pa-5 mb-4"
          >
            <v-col v-if="originalList.length === 0" cols="12">
              <NotesCard
                notes="Calculate and reconcile financial obligations: Determine and
                calculate the employee's pending salaries, benefits, allowances,
                incentives, and any outstanding dues based on their employment terms
                and period worked. Take into account factors such as unpaid leave,
                pro-rata calculations for partial months, and deductions, if
                applicable"
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
              <NotesCard
                notes="Process payment and provide documentation: 
                Settle the determined amounts by processing the payment for all pending dues, 
                including the final salary, remaining leave encashment, pending bonuses or commissions, 
                unpaid reimbursements, and any other outstanding payments owed to the employee. 
                Provide the departing employee with a comprehensive statement detailing the settlement 
                components and maintain proper documentation of the transaction for record-keeping purposes."
                backgroundColor="transparent"
                class="mb-2"
              ></NotesCard>
            </v-col>
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                v-if="originalList.length === 0 && restrictAccess"
                variant="elevated"
                color="primary"
                class="ml-4 mt-1"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="getResignedEmployeesList()"
              >
                <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                Initiate Settlement
              </v-btn>
              <v-btn
                v-if="originalList.length === 0"
                color="white"
                rounded="lg"
                class="ml-2 mt-1"
                :size="isMobileView ? 'small' : 'default'"
                @click="$emit('refetch-list')"
                ><v-icon>fas fa-redo-alt</v-icon></v-btn
              >
              <v-btn
                v-if="originalList.length > 0"
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="$emit('reset-filter')"
              >
                Reset Filter/Search
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="deleteSettlement()"
    >
    </AppWarningModal>
    <EmployeesListModal
      v-if="showEmpListModal"
      :show-modal="showEmpListModal"
      :employeesList="employeesList"
      :showFilterSearch="true"
      :isApplyFilter="true"
      extraColumnKey="exit_date"
      extraColumnText="Exit Date"
      :isExtraColumnAsDate="true"
      @on-select-employees="onSelectEmployees($event)"
      @close-modal="showEmpListModal = false"
    ></EmployeesListModal>
  </div>
</template>

<script>
import moment from "moment";
import { defineComponent, defineAsyncComponent } from "vue";
// queries
import {
  LIST_RESIGNED_EMPLOYEES,
  DELETE_SETTLEMENT,
  REOPEN_SETTLEMENT,
  ADD_SETTLEMENT,
} from "@/graphql/payroll/fullAndFinalSettlementQueries.js";
// components
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import { getErrorCodes } from "@/helper.js";
import FileExportMixin from "@/mixins/FileExportMixin";

export default defineComponent({
  name: "ListSettlements",

  components: { EmployeesListModal, NotesCard },

  mixins: [FileExportMixin],

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    originalList: {
      type: Array,
      required: true,
      default: () => [],
    },
    selectedPayrollMonth: {
      type: Number,
      required: true,
    },
    selectedPayrollYear: {
      type: Number,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
    restrictAccess: {
      type: Number,
      required: true,
    },
    isInitialCall: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      itemList: [],
      openMoreMenu: false,

      // loader
      isLoading: false,

      // delete
      openWarningModal: false,
      selectedEmpForDelete: {},

      // add/emp list
      showEmpListModal: false,
      employeesList: [],
    };
  },

  watch: {
    items(val) {
      this.itemList = val;
      this.onApplySearch(this.searchValue);
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
    originalList(list) {
      if (list.length > 0 && this.isInitialCall) {
        this.getResignedEmployeesList("default");
      }
    },
  },

  mounted() {
    if (this.items.length) {
      this.itemList = this.items;
      this.onApplySearch();
    }
    if (this.originalList.length > 0 && this.isInitialCall) {
      this.getResignedEmployeesList("default");
    }
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // search in shares list
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = "There are no settlements for the selected filters/searches.";
      }
      return msgText;
    },
    tableHeaders() {
      let headers = [];
      headers.push(
        { title: "Employee", key: "employeeName" },
        { title: "Settlement Initiated Date", key: "settlementDate" },
        { title: "Net Pay", key: "netPay" },
        { title: "Status", key: "settlementStatus" }
      );
      if (
        this.restrictAccess &&
        (this.accessRights.update || this.accessRights.delete)
      ) {
        headers.push({ title: "Actions", key: "action" });
      }
      return headers;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return moment(date).format(orgDateFormat);
      };
    },
    settledEmployees() {
      let empList = this.itemList.filter(
        (el) => el.settlementStatus === "Settled"
      );
      return empList.length;
    },
    notSettledEmployees() {
      let empList = this.itemList.filter(
        (el) => el.settlementStatus !== "Settled"
      );
      return empList.length;
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  methods: {
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.items;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.items;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    approveSettlement(item) {
      this.$emit("approve-settlement", item);
    },
    onSelectItem(item) {
      this.$emit("on-select-item", item);
    },
    initiateSettlement() {},

    onDelete(item) {
      this.selectedEmpForDelete = item;
      this.openWarningModal = true;
    },

    deleteSettlement() {
      let vm = this;
      vm.openWarningModal = false;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_SETTLEMENT,
          variables: {
            employeeId: [vm.selectedEmpForDelete.employeeId],
            salaryMonth: vm.selectedPayrollMonth,
            salaryYear: vm.selectedPayrollYear,
          },
          client: "apolloClientAB",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Full & final settlement deleted successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-list");
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    onCloseWarningModal() {
      this.selectedEmpForDelete = {};
      this.openWarningModal = false;
    },

    reopenSettlement(item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: REOPEN_SETTLEMENT,
          variables: {
            employeeId: [item.employeeId],
          },
          client: "apolloClientAB",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Full & final settlement reopened successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-list");
        })
        .catch((err) => {
          vm.handleReopenError(err);
        });
    },

    sendMail() {},

    getResignedEmployeesList(calledFrom = "") {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: LIST_RESIGNED_EMPLOYEES,
          variables: {
            salaryMonth: vm.selectedPayrollMonth,
            salaryYear: vm.selectedPayrollYear,
          },
          client: "apolloClientAA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listResignedEmployees
          ) {
            const { employeeDetails } = response.data.listResignedEmployees;
            if (employeeDetails && employeeDetails.length > 0) {
              vm.employeesList = employeeDetails;
              vm.showEmpListModal = true;
            } else if (calledFrom !== "default") {
              let snackbarData = {
                isOpen: true,
                message:
                  "There are no resigned employees for the selected payroll month to initiate the full and final settlement.",
                type: "warning",
              };
              vm.showAlert(snackbarData);
            }
            vm.$emit("resigned-employees-fetched");
            vm.isLoading = false;
          } else {
            vm.handleListResignedEmployeesError();
          }
        })
        .catch((err) => {
          vm.handleListResignedEmployeesError(err);
        });
    },

    onSelectEmployees(employees) {
      let vm = this;
      vm.showEmpListModal = false;
      vm.isLoading = true;
      let employeesToBeAdd = [];
      for (let emp of employees) {
        employeesToBeAdd.push({
          employeeId: emp.employee_id,
          noticeDate: emp.notice_date,
          resignationDate: emp.exit_date,
        });
      }
      vm.$apollo
        .mutate({
          mutation: ADD_SETTLEMENT,
          variables: {
            employeeDetails: employeesToBeAdd,
            salaryMonth: vm.selectedPayrollMonth,
            salaryYear: vm.selectedPayrollYear,
          },
          client: "apolloClientAB",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Full & final settlement added successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-list");
        })
        .catch((err) => {
          vm.handleAddError(err);
        });
    },

    handleListResignedEmployeesError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "PFF0010": // Login employee is not allowed to initiate the full and final settlement.
          case "_DB0114": // This employee does not have admin or manager access.
            snackbarData.message =
              "Sorry, you don't have access rights to view the resigned employees. Please contact the HR administrator.";
            break;
          case "_EC0007": // invalid inputs
            snackbarData.message =
              "Please retry as the input request was not received as expected. If you encounter this message repeatedly, kindly reach out to the platform administrator.";
            break;
          case "SGE0105": // Organization details does not exists.
            snackbarData.message =
              "Something went wrong while fetching the organization details when listing the resigned employees. Please try after some time.";
            break;
          case "SGE0112": // Error while getting the restrict financial access for manager flag.
            snackbarData.message =
              "Something went wrong while getting the restrict financial access when listing the resigned employees. Please try after some time.";
            break;
          case "PFF0002": // Error while retrieving the resigned employees.
          case "DB0016": // Error while retrieving Employee details.
          case "_UH0001": // unhandled error
          default:
            snackbarData.message =
              "Something went wrong while retrieving the resigned employees. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while retrieving the resigned employees. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    handleDeleteError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "PFF0010": // Login employee is not allowed to initiate the full and final settlement.
          case "_DB0114": // This employee does not have admin or manager access.
          case "_DB0103": // This employee do not have delete access rights
            snackbarData.message =
              "Sorry, you don't have access rights to delete the settlement. Please contact the HR administrator.";
            break;
          case "_EC0007": // invalid inputs
            snackbarData.message =
              "Please retry as the input request was not received as expected. If you encounter this message repeatedly, kindly reach out to the platform administrator.";
            break;
          case "_EC0006": // Record(s) are already deleted in the same or some other user session.
            snackbarData.message =
              "Unable to delete the settlement as it was deleted already in the same or some other user session";
            this.$emit("refetch-list");
            break;
          case "PFF0004": // Login employee is not eligible to add or update or delete the full and final settlement.
            snackbarData.message =
              "You are unable to delete your own record. Please contact the platform administrator for further assistance.";
            break;
          case "SGE0105": // Organization details does not exists.
            snackbarData.message =
              "Something went wrong while fetching the organization details when deleting the settlement details. Please try after some time.";
            break;
          case "SGE0112": // Error while getting the restrict financial access for manager flag.
            snackbarData.message =
              "Something went wrong while getting the restrict financial access when deleting the settlement details. Please try after some time.";
            break;
          case "PFF0005": // Error while retrieving the full and final settlement employee.
          case "PFF0102": // Error while processing the request to delete the full and final settlement.
          case "_UH0001": // unhandled error
          default:
            snackbarData.message =
              "Something went wrong while deleting the settlement. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while deleting the settlement. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    handleReopenError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "PFF0010": // Login employee is not allowed to initiate the full and final settlement.
          case "_DB0114": // This employee does not have admin or manager access.
            snackbarData.message =
              "Sorry, you don't have access rights to reopen the settlement. Please contact the HR administrator.";
            break;
          case "SGE0105": // Organization details does not exists.
            snackbarData.message =
              "Something went wrong while fetching the organization details when reopening the settlement. Please try after some time.";
            break;
          case "SGE0112": // Error while getting the restrict financial access for manager flag.
            snackbarData.message =
              "Something went wrong while getting the restrict financial access when reopening the settlement. Please try after some time.";
            break;
          case "PFF0007": // Error while reopening the full and final settlement.
          case "PFF0104": // Error while processing the request to reopen the full and final settlement.
          case "_UH0001": // unhandled error
          default:
            snackbarData.message =
              "Something went wrong while reopening the settlement. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while reopening the settlement. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    handleAddError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "PFF0010": // Login employee is not allowed to initiate the full and final settlement.
          case "_DB0114": // This employee does not have admin or manager access.
          case "_DB0101": // This employee do not have add access rights
            snackbarData.message =
              "Sorry, you don't have access rights to add the settlement. Please contact the HR administrator.";
            break;
          case "IVE0000": // Invalid input request.
          case "IVE0302": // Employee details are required.
          case "_EC0007": // invalid inputs
            snackbarData.message =
              "Please retry as the input request was not received as expected. If you encounter this message repeatedly, kindly reach out to the platform administrator.";
            break;
          case "_EC0009": // Record(s) are already added in the same or some other user session.
            snackbarData.message =
              "Unable to add the settlement for the employee as it was added already in the same or some other user session";
            this.$emit("refetch-list");
            break;
          case "PFF0106": // Error in retrieving the employee earnings and deductions.
            snackbarData.message =
              "Something went wrong while retrieving the employee earnings and deductions when adding the settlement. Please try after some time.";
            break;
          case "SGE0105": // Organization details does not exists.
            snackbarData.message =
              "Something went wrong while fetching the organization details when adding the settlement details. Please try after some time.";
            break;
          case "SGE0112": // Error while getting the restrict financial access for manager flag.
            snackbarData.message =
              "Something went wrong while getting the restrict financial access when adding the settlement details. Please try after some time.";
            break;
          case "PFF0006": // Error while adding the full and final settlement employee.
          case "PFF0103": // Error while processing the request to add the full and final settlement.
          case "_UH0001": // unhandled error
          default:
            snackbarData.message =
              "Something went wrong while adding the settlement. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while adding the settlement. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
    },

    exportReportFile() {
      let settlementList = this.itemList;
      settlementList = settlementList.map((item) => ({
        ...item,
        settlementDateFormatted: item.settlementDate
          ? this.formatDate(item.settlementDate)
          : "",
        addedOnFormatted: item.addedOn ? this.formatDate(item.addedOn) : "",
        updatedOnFormatted: item.updatedOn
          ? this.formatDate(item.updatedOn)
          : "",
      }));
      let exportOptions = {
        fileExportData: settlementList,
        fileName: "Full and Final Settlement",
        sheetName: "Full and Final Settlement",
        header: [
          { header: "Employee Id", key: "userDefinedEmpId" },
          { header: "Employee Name", key: "employeeName" },
          {
            header: "Settlement Initiated Date",
            key: "settlementDateFormatted",
          },
          { header: "Net Pay", key: "netPay" },
          { header: "Status", key: "settlementStatus" },
          { header: "Added By", key: "addedBy" },
          { header: "Added On", key: "addedOnFormatted" },
          { header: "Updated By", key: "updatedBy" },
          { header: "Updated On", key: "updatedOnFormatted" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
  },
});
</script>

<style>
.rounded-border {
  width: 32px;
  height: 32px;
  text-align: center;
  border-radius: 50%;
  border: 3px solid;
}
</style>
