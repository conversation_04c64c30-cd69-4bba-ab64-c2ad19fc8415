<template>
  <v-overlay
    :model-value="showViewForm"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:50vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">View {{ landedFormName }}</div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <div v-if="!showAccreditationList">
            <div class="d-flex justify-end align-center">
              <v-btn
                v-if="formAccess?.update"
                @click="onOpenEditForm()"
                class="mr-3 mt-3 text-primary"
                variant="text"
                rounded="lg"
              >
                <v-icon class="mr-1" size="15">fas fa-edit</v-icon>Edit
              </v-btn>
            </div>
            <div class="px-6 py-2">
              <v-row>
                <v-col
                  v-if="
                    labelList[426]?.Field_Visiblity?.toLowerCase() === 'yes'
                  "
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6 pb-0 my-3"
                >
                  <div class="text-subtitle-1 text-grey-darken-1">
                    {{ labelList[426].Field_Alias }}
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedItem.Job_Role_Code) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <div class="text-subtitle-1 text-grey-darken-1">Job Role</div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedItem.Job_Role) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Associate Training/Accreditation
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      <v-btn
                        v-if="selectedItem.accreditations?.length > 0"
                        rounded="lg"
                        color="primary"
                        @click="openAccreditationList()"
                      >
                        View All
                      </v-btn>
                      <p v-else>-</p>
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Designation
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ constructDesignations(selectedItem.designations) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <div class="text-subtitle-1 text-grey-darken-1">Status</div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedItem.Status) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Description
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedItem.Description) }}
                    </section>
                  </div>
                </v-col>
              </v-row>
              <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  /> </v-col
              ></v-row>
            </div>
          </div>
          <div v-else class="px-6 ma-2">
            <v-btn color="primary" @click="showAccreditationList = false">
              <v-icon color="" class="mr-1 fas fa-chevron-left" />
              Back
            </v-btn>
            <div>
              <v-data-table
                :items="accreditationCategoryAndTypes"
                :headers="accreditationHeader"
                hide-default-footer
                :search="searchedItem"
                :items-per-page="20"
                :height="
                  $store.getters.getTableHeightBasedOnScreenSize(
                    290,
                    accreditationCategoryAndTypes
                  )
                "
                class="elevation-1"
              >
                <template #top>
                  <div class="d-flex justify-center">
                    <v-text-field
                      v-model="searchedItem"
                      placeholder="Search"
                      class="mx-4"
                      clearable
                      style="max-width: 70%"
                      prepend-inner-icon="search"
                    ></v-text-field>
                  </div>
                </template>
                <template v-slot:item="{ item }">
                  <tr
                    class="data-table-tr bg-white cursor-pointer"
                    :class="
                      isMobileView ? ' v-data-table__mobile-table-row' : ''
                    "
                  >
                    <td
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        Accrediation Category
                      </div>
                      <v-tooltip
                        :text="item.Accreditation_Category"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            v-bind="
                              item.Accreditation_Category.length > 30
                                ? props
                                : ''
                            "
                            :style="
                              !isMobileView
                                ? 'max-width: 500px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ checkNullValue(item.Accreditation_Category) }}
                          </section>
                        </template>
                      </v-tooltip>
                    </td>
                    <td
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        Accrediation Type
                      </div>
                      <v-tooltip
                        :text="item.Accreditation_Type"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            v-bind="
                              item.Accreditation_Type.length > 30 ? props : ''
                            "
                            :style="
                              !isMobileView
                                ? 'max-width: 500px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ checkNullValue(item.Accreditation_Type) }}
                          </section>
                        </template>
                      </v-tooltip>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </template>
  </v-overlay>
</template>

<script>
import { checkNullValue, convertUTCToLocal } from "@/helper";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
export default {
  name: "ViewJobRoles",
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      selectedFormValue: [],
      showViewForm: true,
      showAccreditationList: false,
      searchedItem: "",
      accreditationCategoryAndTypes: [],
    };
  },
  props: {
    selectedItem: {
      type: Object,
      default: () => {},
    },
    landedFormName: {
      type: String,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    accreditationHeader() {
      return [
        {
          title: "Accreditation Category",
          key: "Accreditation_Category",
          align: "start",
        },
        {
          title: "Accreditation Type",
          key: "Accreditation_Type",
        },
      ];
    },
  },
  components: {
    MoreDetails,
  },
  mounted() {
    this.prefillMoreDetails();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    openAccreditationList() {
      this.showAccreditationList = true;
      this.accreditationCategoryAndTypes = this.selectedItem.accreditations;
    },
    constructDesignations(designations) {
      if (designations?.length) {
        return designations.map((el) => `${el.Designation_Name}`).join(", ");
      } else {
        return "-";
      }
    },
    onOpenEditForm() {
      this.showViewForm = false;
      this.$emit("open-edit-form");
    },
    onCloseOverlay() {
      this.showViewForm = false;
      this.$emit("close-form");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.selectedItem.Added_On),
        addedByName = this.selectedItem.Added_By_Name,
        updatedByName = this.selectedItem.Updated_By_Name,
        updatedOn = this.convertUTCToLocal(this.selectedItem.Updated_On);

      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
