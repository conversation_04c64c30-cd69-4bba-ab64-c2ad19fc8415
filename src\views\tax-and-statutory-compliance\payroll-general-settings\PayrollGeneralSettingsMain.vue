<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>
    <v-container fluid class="payroll-setting-container">
      <v-window v-if="formAccess" v-model="currentTabItem">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchData()"
          >
          </AppFetchErrorScreen>
          <div v-else>
            <v-card
              class="py-9 rounded-lg fill-height"
              :class="isMobileView ? '' : 'px-5'"
              elevation="5"
            >
              <v-card-text>
                <v-row>
                  <v-col v-if="!isEdit && !listLoading" cols="12">
                    <ViewPayrollGeneralSettings
                      :editFormData="payrollGeneralSettingsData"
                      @open-edit="openEditForm()"
                      :accessFormName="accessFormName"
                      :formAccess="formAccess"
                      :getFieldAlias="labelList"
                    ></ViewPayrollGeneralSettings>
                  </v-col>
                  <v-col v-if="isEdit && !listLoading" cols="12">
                    <EditPayrollGeneralSettings
                      :editFormData="payrollGeneralSettingsData"
                      @refetch-data="refetchData()"
                      @close-form="closeEditForm()"
                      :accessFormName="accessFormName"
                      :getFieldAlias="labelList"
                    >
                    </EditPayrollGeneralSettings>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
// components
import ViewPayrollGeneralSettings from "./ViewPayrollGeneralSettings";
import EditPayrollGeneralSettings from "./EditPayrollGeneralSettings.vue";
// Queries
import { RETRIEVE_PAYROLL_GENERAL_SETTINGS } from "@/graphql/tax-and-statutory-compliance/payrollGeneralSettings";
export default {
  name: "GeneralSettings",
  components: {
    ViewPayrollGeneralSettings,
    EditPayrollGeneralSettings,
  },
  data() {
    return {
      isLoading: false,
      currentTabItem: "tab-0",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      payrollGeneralSettingsData: {},
      isEdit: false,
      slabList: [],
    };
  },
  computed: {
    formAccess() {
      let payrollGenralConfigAccess = this.accessRights("261");
      if (
        payrollGenralConfigAccess &&
        payrollGenralConfigAccess.accessRights &&
        payrollGenralConfigAccess.accessRights["view"]
      ) {
        return payrollGenralConfigAccess.accessRights;
      } else return false;
    },
    accessFormName() {
      let payrollGenralConfigAccess = this.accessRights("261");
      if (
        payrollGenralConfigAccess &&
        payrollGenralConfigAccess.customFormName
      ) {
        return payrollGenralConfigAccess.customFormName;
      } else return "General";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    salaryTemplateAccess() {
      let formAccess = this.accessRights(206);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    mainTabs() {
      let tabs = [this.accessFormName];
      if (this.salaryTemplateAccess && this.salaryTemplateAccess.view) {
        tabs.push("Salary Template");
      }
      return tabs;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("General settings error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  mounted() {
    this.fetchPayrollGeneralSettingsDetails();
  },
  methods: {
    onTabChange(tabName) {
      if (tabName.toLowerCase() === "salary template") {
        this.$router.push("/settings/payroll/salary-template");
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    openEditForm() {
      this.isEdit = true;
    },
    closeEditForm() {
      this.isEdit = false;
    },
    refetchData() {
      this.closeEditForm();
      this.fetchPayrollGeneralSettingsDetails();
    },
    fetchPayrollGeneralSettingsDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_PAYROLL_GENERAL_SETTINGS,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrievePayrollGeneralSettings
              .payrollGeneralSettingsData
          ) {
            let payrollGeneralSettingsData =
              response.data.retrievePayrollGeneralSettings
                .payrollGeneralSettingsData;

            vm.payrollGeneralSettingsData = payrollGeneralSettingsData[0];
            vm.listLoading = false;
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.payroll-setting-container {
  padding: 5em 3em 0em 3em;
}
@media screen and (max-width: 805px) {
  .payroll-setting-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
