<template>
  <div>
    <v-card
      v-if="!alwaysOpen"
      class="ma-1 pa-1 text-center bg-hover"
      @click="openMoreDetails = !openMoreDetails"
    >
      <span class="text-primary font-weight-bold bg-hover">{{
        $t("settings.moreDetails")
      }}</span>
      <v-icon v-if="!openMoreDetails" color="primary">fas fa-angle-down</v-icon>
      <v-icon v-else color="primary">fas fa-angle-up</v-icon>
    </v-card>
    <v-expand-transition v-if="alwaysOpen || openMoreDetails">
      <v-card class="mt-n2 ma-1 pa-4 mb-1 bg-hover">
        <v-timeline density="comfortable">
          <v-timeline-item
            v-for="(moreDetail, index) in moreDetailsList"
            :key="moreDetail.actionDate + index"
            dot-color="primary"
            size="x-small"
          >
            <span>{{ moreDetail.actionDate }}</span>
            <span
              v-if="moreDetail.text"
              class="text-primary pl-1 font-weight-bold"
            >
              - {{ moreDetail.text }}
              {{ moreDetail.notDisplayby === 1 ? "" : "by" }}</span
            >
            <span class="pl-2 text-primary font-weight-bold">{{
              moreDetail.actionBy
            }}</span>
            <span
              v-if="moreDetail.actionById"
              class="text-primary pl-1 font-weight-bold"
            >
              - E-ID: {{ moreDetail.actionById }}</span
            >
          </v-timeline-item>
        </v-timeline>
      </v-card>
    </v-expand-transition>
  </div>
</template>
<script>
export default {
  name: "MoreDetails",
  props: {
    moreDetailsList: {
      type: Array,
      required: true,
    },
    openCloseCard: {
      type: Boolean,
      required: true,
    },
    alwaysOpen: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({
    openMoreDetails: false,
  }),
  watch: {
    openCloseCard(val) {
      if (!val) {
        this.openMoreDetails = false;
      }
    },
    openMoreDetails(val) {
      if (val) {
        this.$emit("on-open-close", true);
      }
    },
  },
};
</script>
