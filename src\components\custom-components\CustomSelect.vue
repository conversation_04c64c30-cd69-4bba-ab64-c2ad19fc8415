<template>
  <v-select
    v-if="!isAutoComplete"
    v-model="selectedItem"
    :items="items"
    :label="label"
    :placeholder="placeholder"
    :variant="variantType"
    :multiple="selectProperties.multiple"
    :chips="selectProperties.chips"
    :closable-chips="selectProperties.closableChips"
    :clearable="selectProperties.clearable"
    :item-title="itemTitle"
    :item-value="itemValue"
    :loading="isLoading"
    :rules="rules"
    :disabled="disabled"
    :no-data-text="isLoading ? 'Loading...' : 'No data available'"
    :hint="hint"
    :density="density"
    :prefix="prefix"
    ref="customSelect"
    @update:modelValue="onChangeCustomSelect()"
    @update:focused="isFocused = !isFocused"
    :style="cStyle"
  >
    <template v-if="label" v-slot:label>
      <span class="custom-label-color"
        >{{ label }}<span v-if="isRequired" style="color: red">*</span></span
      >
    </template>
    <template v-if="messageColor" v-slot:message="{ message }">
      <span :style="{ color: messageColor }">{{ message }}</span>
    </template>
    <template v-slot:item="{ item, props }">
      <div
        class="py-1 px-2"
        v-bind="props"
        :style="{
          pointerEvents: disabledValue.includes(item.title) ? 'none' : 'auto',
        }"
      >
        <v-hover>
          <template v-slot:default="{ isHovering, props }">
            <div
              v-bind="props"
              class="pa-3 rounded-lg"
              :class="{
                'bg-hover': isHovering && !showConfigure.includes(item.title),
                'bg-primary': isSelected(item.value),
                'bg-grey-lighten-4':
                  !(isHovering && !showConfigure.includes(item.title)) &&
                  !this.isSelected(item.value),
                'cursor-pointer': !showConfigure.includes(item.title),
              }"
              :style="listWidth"
            >
              <v-list-item
                :disabled="disabledValue.includes(item.title)"
                :style="
                  disableBreak
                    ? ''
                    : { wordBreak: 'break-all', maxWidth: dropdownWidth }
                "
              >
                <div class="d-flex justify-space-between">
                  <div>
                    {{ item.title }}
                    <div v-if="subText" class="py-1 text-grey">
                      <span v-if="subTextTitle">{{ subTextTitle }}:</span
                      >{{ item.raw[subText] }}
                    </div>
                  </div>
                  <div
                    v-if="showConfigure.includes(item.title)"
                    class="text-blue cursor-pointer"
                    @click="onClickConfigure(item.title)"
                  >
                    configure
                  </div>
                </div>
              </v-list-item>
            </div>
          </template>
        </v-hover>
      </div>
    </template>
    <template v-if="appendIcon && isFocused" v-slot:append>
      <v-icon size="20" color="primary" @click="$emit('append-icon-clicked')">
        {{ appendIcon }}
      </v-icon>
    </template>
  </v-select>
  <v-autocomplete
    v-else
    v-model="selectedItem"
    :items="items"
    :label="label"
    :placeholder="placeholder"
    :variant="variantType"
    :multiple="selectProperties.multiple"
    :item-title="itemTitle"
    :item-value="itemValue"
    :chips="selectProperties.chips"
    :closable-chips="selectProperties.closableChips"
    :clearable="selectProperties.clearable"
    :loading="isLoading"
    :rules="rules"
    :disabled="disabled"
    :no-data-text="isLoading ? 'Loading...' : 'No data available'"
    :hint="hint"
    :density="density"
    :prefix="prefix"
    ref="customSelect"
    :clear-on-select="true"
    @update:modelValue="onChangeCustomSelect()"
    @update:focused="onFieldFocus"
    @update:search="$emit('update-search-value', $event)"
  >
    <template v-if="label" v-slot:label>
      <span class="custom-label-color"
        >{{ label }}<span v-if="isRequired" style="color: red">*</span></span
      >
    </template>
    <template v-slot:item="{ item, props }">
      <div
        class="py-1 px-2"
        v-bind="props"
        :style="{
          pointerEvents: disabledValue.includes(item.title) ? 'none' : 'auto',
        }"
      >
        <v-hover>
          <template v-slot:default="{ isHovering, props }">
            <div
              v-bind="props"
              class="pa-3 rounded-lg cursor-pointer"
              :class="
                isHovering
                  ? 'bg-hover'
                  : isSelected(item.value)
                  ? 'bg-primary'
                  : 'bg-grey-lighten-4'
              "
              :style="listWidth"
            >
              <v-list-item
                :disabled="disabledValue.includes(item.title)"
                :style="{ wordBreak: 'break-all', maxWidth: dropdownWidth }"
              >
                {{ item.title }}

                <div v-if="subText" class="py-1 text-grey">
                  <span v-if="subTextTitle">{{ subTextTitle }}:</span
                  >{{ item.raw[subText] }}
                </div>
                <div v-if="showTooltip">
                  <span
                    v-if="tooltipText"
                    class="text-red"
                    :class="isSelected(item.value) ? 'text-hover' : ''"
                    style="font-size: 12px"
                  >
                    {{ tooltipText }}
                  </span>
                </div>
              </v-list-item>
            </div>
          </template>
        </v-hover>
      </div>
    </template>
    <template v-if="appendIcon && isFocused" v-slot:append>
      <v-icon size="20" color="primary" @click="onAppendClick()">
        {{ appendIcon }}
      </v-icon>
    </template>
  </v-autocomplete>
</template>
<script>
export default {
  name: "CustomSelect",
  props: {
    items: {
      type: [Object, Array],
      required: true,
    },
    label: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "",
    },
    variantType: {
      type: String,
      default: "solo",
    },
    tooltipText: {
      type: String,
      default: "",
    },
    showTooltip: {
      type: Boolean,
      default: false,
    },
    itemSelected: {
      required: true,
    },
    selectProperties: {
      type: Object,
      default: function () {
        return {
          multiple: false,
          chips: false,
          clearable: false,
          closableChips: false,
        };
      },
    },
    isAutoComplete: {
      type: Boolean,
      default: false,
    },
    itemValue: {
      type: String,
      default: "",
    },
    itemTitle: {
      type: String,
      default: "",
    },
    isLoading: {
      type: [Boolean, Number],
      default: false,
    },
    disableBreak: {
      type: Boolean,
      default: false,
    },
    rules: {
      type: Array,
      default: function () {
        return [true];
      },
    },
    listWidth: {
      type: String,
      default: "",
    },
    isRequired: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: [Boolean, Number],
      default: false,
    },
    subText: {
      type: String,
      default: "",
    },
    subTextTitle: {
      type: String,
      default: "",
    },
    hint: {
      type: String,
      default: "",
    },
    disabledValue: {
      type: Array,
      default: function () {
        return [];
      },
    },
    appendIcon: {
      type: String,
      default: "",
    },
    appendInner: {
      type: Boolean,
      default: false,
    },
    autoSelectFirst: {
      type: Boolean,
      default: false,
    },
    messageColor: {
      type: String,
      default: null,
    },
    cStyle: {
      type: String,
      default: "",
    },
    density: {
      type: String,
      default: "default",
    },
    prefix: {
      type: String,
      default: "",
    },
    showConfigure: {
      type: Array,
      default: () => [],
    },
  },
  emits: [
    "append-icon-clicked",
    "selected-item",
    "update-search-value",
    "configure-clicked",
  ],
  data() {
    return {
      selectedItem: null,
      isFocused: false,
      dropdownWidth: "100%",
    };
  },
  computed: {
    isSelected() {
      return (value) => {
        if (this.selectProperties.multiple) {
          return this.selectedItem && this.selectedItem.includes(value);
        } else return value === this.selectedItem;
      };
    },
  },
  mounted() {
    this.assignSelectedItem();
    let prefixWidth = this.prefix ? 200 : 100;
    this.dropdownWidth = `${
      this.$refs.customSelect.$el.offsetWidth + prefixWidth
    }px`;
  },
  watch: {
    itemSelected() {
      this.assignSelectedItem();
    },
    isLoading(val) {
      if (val) {
        this.selectedItem = null;
      }
      this.assignSelectedItem();
    },
    // Here is the code for selecting all the values in dropdown/this code only works when we will have select all field in drop down
    selectedItem(newValue) {
      if (Array.isArray(newValue)) {
        if (newValue && newValue.includes("select-all")) {
          // User selected "Select All," so set selectedItems to all items Ids excluding disabled values
          // this code will work even if you don't have any disabled values
          this.selectedItem = this.items
            .filter(
              (option) =>
                option.itemId !== "select-all" &&
                this.disabledValue &&
                !this.disabledValue.some(
                  (disabled) => disabled === option.itemTitle
                )
            )
            .map((option) => option.itemId);
          this.onChangeCustomSelect();
        }
      }
    },
  },
  methods: {
    onChangeCustomSelect() {
      if (
        this.showConfigure.includes(this.selectedItem) &&
        !this.isAutoComplete
      ) {
        this.selectedItem = null;
      }
      this.$emit("selected-item", this.selectedItem);
    },
    assignSelectedItem() {
      if (!this.isLoading) {
        this.selectedItem = this.itemSelected;
      }
    },
    onFieldFocus() {
      if (this.isFocused) {
        setTimeout(() => {
          this.isFocused = !this.isFocused;
        }, 2000);
      } else {
        this.isFocused = !this.isFocused;
      }
    },
    onClickConfigure(type) {
      this.$emit("configure-clicked", type);
    },
    onAppendClick() {
      this.$emit("append-icon-clicked");
    },
    onFocusCustomSelect() {
      this.$refs.customSelect.focus();
    },
    showField(value) {
      this.$emit(value, true);
    },
  },
};
</script>
<style scoped>
.custom-label-color {
  color: #2c2c2c !important;
}
</style>
