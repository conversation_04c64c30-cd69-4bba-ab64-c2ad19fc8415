<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="primary" size="20">fas fa-file-alt</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">Activity</div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-btn
            v-if="formAccess.update"
            @click="$emit('open-edit-form')"
            size="small"
            color="primary"
            variant="elevated"
            rounded="lg"
            >Edit</v-btn
          >
          <v-icon class="mx-1" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card>
        <div
          :style="
            isMobileView ? 'height: calc(100vh - 260px); overflow: scroll' : ''
          "
        >
          <v-card-text>
            <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Activity</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.activityName) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Billable</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.isBillable) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Activity Description
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.description) }}
                </p>
              </v-col>
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-card-text>
        </div>
      </v-card>
    </v-card>
  </div>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
export default {
  name: "ViewProjectActivities",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    editedData: {},
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.editedData = Object.assign({}, newData);
        this.prefillMoreDetails();
      },
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.selectedItem.addedOn),
        addedByName = this.selectedItem.addedByName,
        updatedByName = this.selectedItem.updatedByName,
        updatedOn = this.convertUTCToLocal(this.selectedItem.updatedOn);
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>
