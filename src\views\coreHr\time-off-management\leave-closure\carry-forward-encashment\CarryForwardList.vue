<template>
  <div>
    <v-container fluid v-if="formAccess">
      <div v-if="listLoading" class="mt-3">
        <v-skeleton-loader
          ref="skeleton1"
          type="table-heading"
          class="mx-auto"
        ></v-skeleton-loader>
        <div v-for="i in 4" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <AppFetchErrorScreen
        v-else-if="isErrorInList"
        :content="errorContent"
        key="error-screen"
        icon-name="fas fa-redo-alt"
        image-name="common/human-error-image"
        button-text="Retry"
        @button-click="refetchList()"
      ></AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="originalList?.length === 0"
        key="no-data-screen"
        :main-title="emptyScenarioMsg"
        :isSmallImage="!isFilter"
        :image-name="!isFilter ? '' : 'common/no-records'"
      >
        <template v-if="!isFilter" #contentSlot>
          <div style="max-width: 80%">
            <v-row
              class="rounded-lg pa-5 mb-4"
              :style="!isFilter ? 'background: white' : ''"
            >
              <v-col cols="12">
                <NotesCard
                  notes=""
                  backgroundColor="transparent"
                  class="mb-4"
                />
              </v-col>
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <div
                  class="d-flex align-center flex-wrap"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <CustomSelect
                    v-model="selectedLeaveName"
                    :items="leavesList"
                    :itemSelected="selectedLeaveName"
                    :isAutoComplete="true"
                    variant="solo"
                    class="mt-3"
                    label="Leave Name"
                    density="compact"
                    min-width="150px"
                    :max-width="isMobileView ? `200px` : `300px`"
                    @selected-item="onChangeLeaveName($event)"
                  />
                  <v-btn
                    color="transparent"
                    variant="flat"
                    class="ml-2 mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="itemList?.length == 0"
        key="no-results-screen"
        main-title="There are no records matched for the selected filters/searches."
        image-name="common/no-records"
      >
        <template #contentSlot>
          <div style="max-width: 80%">
            <v-row class="rounded-lg pa-5 mb-4">
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <v-btn
                  variant="elevated"
                  color="primary"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  @click="resetFilter('grid')"
                >
                  <span class="primary">Reset Filter/Search </span>
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <div v-else>
        <div
          v-if="originalList?.length > 0"
          class="d-flex flex-wrap align-center"
          :class="isMobileView ? 'flex-column' : ''"
          style="justify-content: space-between"
        >
          <div
            class="d-flex align-center flex-wrap"
            :class="isMobileView ? 'justify-center' : ''"
          >
            <CustomSelect
              v-model="selectedLeaveName"
              :items="leavesList"
              :itemSelected="selectedLeaveName"
              :isAutoComplete="true"
              variant="solo"
              class="mt-3"
              label="Leave Name"
              density="compact"
              min-width="150px"
              max-width="500px"
              @selected-item="onChangeLeaveName($event)"
            />
          </div>

          <div
            class="d-flex align-center"
            :class="isMobileView ? 'justify-center' : 'justify-end'"
          >
            <v-btn
              rounded="lg"
              color="transparent"
              variant="flat"
              class="mt-1"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu class="mb-1" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                  <v-icon>fas fa-ellipsis-v</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                        ><v-icon color="primary" size="15" class="pr-2">{{
                          action.icon
                        }}</v-icon
                        >{{ action.key }}</v-list-item-title
                      >
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
        <v-row>
          <v-col cols="12">
            <v-data-table
              :headers="tableHeaders"
              :items="itemList"
              :items-per-page="50"
              fixed-header
              :height="
                itemList?.length > 11 ? $store.getters.getTableHeight(270) : ''
              "
              item-value="count"
              class="elevation-1"
              style="box-shadow: none !important"
            >
              <template v-slot:item="{ item }">
                <tr
                  style="z-index: 200"
                  class="data-table-tr bg-white cursor-pointer"
                  :class="[
                    isMobileView
                      ? ' v-data-table__mobile-table-row ma-0 mt-2'
                      : '',
                  ]"
                >
                  <td
                    v-for="(header, index) in tableHeaders"
                    :key="index"
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-small'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      {{ checkNullValue(header.title) }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        v-if="
                          !isMobileView &&
                          selectedItem &&
                          selectedItem.Leave_Id === item.Leave_Id &&
                          index === 0
                        "
                        class="data-table-side-border d-flex"
                      />
                      <v-tooltip
                        v-if="item[header.key]?.toString().length > 25"
                        :text="item[header.key]"
                        location="top"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :class="{ 'text-primary': index === 0 }"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                            v-bind="props"
                          >
                            {{ checkNullValue(item[header.key]) }}
                            <div
                              v-if="index === 0 && item?.User_Defined_EmpId"
                              class="text-grey"
                            >
                              {{ checkNullValue(item.User_Defined_EmpId) }}
                            </div>
                          </div>
                        </template>
                      </v-tooltip>
                      <div
                        v-else
                        class="text-subtitle-1 font-weight-regular text-truncate"
                        :class="{
                          'text-primary': index === 0,
                          'text-left': true,
                        }"
                      >
                        {{ checkNullValue(item[header.key]) }}
                        <div
                          v-if="index === 0 && item?.User_Defined_EmpId"
                          class="text-grey"
                        >
                          {{ checkNullValue(item.User_Defined_EmpId) }}
                        </div>
                      </div>
                    </section>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </div>
    </v-container>
    <AppAccessDenied v-else />
    <AppLoading v-if="listLoading" />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
// Async Components
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "CarryForwardList",
  components: {
    NotesCard,
    CustomSelect,
  },
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    formId: {
      type: Number,
      required: true,
    },
    filteredList: {
      type: Array,
      default: () => [],
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  mixins: [FileExportMixin],
  emits: ["send-list-data"],
  data: () => ({
    originalList: [],
    itemList: [],
    selectedLeaveName: 27,
    selectedItem: null,
    isFilter: false,
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
  }),
  computed: {
    leavesList() {
      let list = [];
      return list;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    tableHeaders() {
      let headers = [
        {
          title: "Employee",
          key: "EmployeeName",
          align: "start",
        },
        { title: "Leave Name", key: "Leave_Name" },
        {
          title: "Current Year Leave Balance",
          key: "Current_Year_Leave_Balance",
        },
        { title: "Carry Forward Limit", key: "CarryOver_Limit" },
        { title: "Leaves Forward", key: "Leaves_Forward" },
        { title: "Previous Year CO Balance", key: "Last_CO_Balance" },
        { title: "Total Carry Over Balance", key: "Total_Carry_Over_Balance" },
      ];
      return headers;
    },

    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList?.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  watch: {
    filteredList: {
      handler(val) {
        if (val && val.length) this.itemList = val;
        else this.itemList = [];
      },
      deep: true,
    },
  },

  mounted() {
    this.fetchList();
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    onChangeLeaveName(leave) {
      if (leave) {
        this.selectedLeaveName = leave;
        this.refetchList();
      }
    },
    async fetchList() {
      let vm = this;
      try {
        vm.listLoading = true;
        vm.isErrorInList = false;
        vm.errorContent = "";
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/leaves/carry-over/coleavetype/" +
            vm.selectedLeaveName,
          type: "POST",
          dataType: "json",
          data: {},
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response) {
          vm.itemList = response?.aaData || [];
          vm.originalList = response?.aaData || [];
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please try after some time.",
          };
          vm.itemList = [];
          vm.originalList = [];
          vm.showAlert(snackbarData);
        }
        vm.$emit("send-list-data", vm.originalList);
      } catch (err) {
        vm.isErrorInList = true;
        vm.errorContent = err;
        vm.$emit("send-list-data", []);
        vm.showAlert({
          isOpen: true,
          type: "warning",
          message: err || "Something went wrong. Please try again later.",
        });
      } finally {
        vm.listLoading = false;
      }
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.fetchList();

      this.resetFilter();
    },
    resetFilter() {
      this.$emit("send-list-data", this.originalList);
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        Date_Of_Join: el.Date_Of_Join ? this.formatDate(el.Date_Of_Join) : "",
      }));

      // Create headers based on tableHeaders
      let headers = [
        {
          header: "Employee Id",
          key: "User_Defined_EmpId",
        },
      ];
      // Map tableHeaders to export headers format
      this.tableHeaders.forEach((header) => {
        headers.push({
          header: header.title,
          key: header.key,
        });
      });

      // Insert "Date Of Join" at the 3rd position (index 2)
      headers.splice(2, 0, {
        header: "Date Of Join",
        key: "Date_Of_Join",
      });

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Leave Closure",
        sheetName: "Leave Closure",
        header: headers,
      };
      this.exportExcelFile(exportOptions);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
