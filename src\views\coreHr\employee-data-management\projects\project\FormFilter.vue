<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary darken-1"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              :items="clients"
              label="Client"
              :isAutoComplete="true"
              :itemSelected="selectedClient"
              @selected-item="onChangeSelectField($event, 'selectedClient')"
              :is-auto-complete="true"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              listWidth="max-width: 300px !important"
            ></CustomSelect>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              :items="projectManagers"
              :label="`${additionalFieldProjectLabel} Manager`"
              :isAutoComplete="true"
              :itemSelected="selectedProjectManager"
              @selected-item="
                onChangeSelectField($event, 'selectedProjectManager')
              "
              :is-auto-complete="true"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              listWidth="max-width: 300px !important"
            ></CustomSelect>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              :items="projectLocations"
              label="Location"
              :isAutoComplete="true"
              :itemSelected="selectedProjectLocation"
              @selected-item="
                onChangeSelectField($event, 'selectedProjectLocation')
              "
              :is-auto-complete="true"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              listWidth="max-width: 300px !important"
            ></CustomSelect>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              :items="['Open', 'Closed']"
              label="Status"
              :isAutoComplete="true"
              :itemSelected="selectedStatus"
              @selected-item="onChangeSelectField($event, 'selectedStatus')"
              :is-auto-complete="true"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              listWidth="max-width: 300px !important"
            ></CustomSelect>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn rounded="lg" variant="outlined" @click="resetFilterValues()">
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { defineComponent } from "vue";
export default defineComponent({
  name: "ProjectFormFilter",

  props: {
    itemList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  components: {
    CustomSelect,
  },
  data: () => ({
    openFormFilter: false,
    selectedProjectManager: [],
    selectedClient: [],
    selectedProjectLocation: [],
    selectedStatus: [],
  }),

  computed: {
    additionalFieldProjectLabel() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel === "Course" ? "" : pLabel;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    clients() {
      const filteredItemList = this.itemList.filter(
        (item) => item.clientName && item.clientName.trim() !== ""
      );
      // Use Set to filter out duplicate clientName values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.clientName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    projectManagers() {
      const filteredItemList = this.itemList.filter(
        (item) => item.clientName && item.managerName.trim() !== ""
      );
      // Use Set to filter out duplicate managerName values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.managerName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    projectLocations() {
      const filteredItemList = this.itemList.filter(
        (item) => item.locationName && item.locationName.trim() !== ""
      );
      // Use Set to filter out duplicate locationName values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.locationName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
  },

  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.itemList;

      if (this.selectedClient.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedClient.includes(item.clientName);
        });
      }
      if (this.selectedProjectManager.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedProjectManager.includes(item.managerName);
        });
      }
      if (this.selectedProjectLocation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedProjectLocation.includes(item.locationName);
        });
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.status);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedClient = [];
      this.selectedProjectManager = [];
      this.selectedProjectLocation = [];
      this.selectedStatus = [];
    },
  },
});
</script>
