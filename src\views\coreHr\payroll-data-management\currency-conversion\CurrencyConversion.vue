<template>
  <div v-if="isMounted">
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row
            justify="center"
            v-if="backupCurrencyConversionData.length > 0"
            class="mr-4"
          >
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                v-if="backupCurrencyConversionData.length > 0"
                class="justify-end"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <FormFilter
                ref="formFilterRef"
                :items="backupCurrencyConversionData"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              >
              </FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="currency-conversion-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-container fluid class="currency-conversion-card">
            <ProfileCard>
              <FormTab :model-value="openedSubTab" :hide-slider="true">
                <v-tab
                  v-for="tab in subTabItems"
                  :key="tab.value"
                  :value="tab.value"
                  :disabled="tab.disable"
                  @click="onChangeSubTabs(tab.value)"
                >
                  <div
                    :class="[
                      isActiveSubTab(tab.value)
                        ? 'text-primary font-weight-bold'
                        : 'text-grey-darken-2 font-weight-bold',
                    ]"
                  >
                    {{ tab.label }}
                    <div
                      v-if="isActiveSubTab(tab.value)"
                      class="mt-3 mb-n4"
                      style="border-bottom: 4px solid; width: 150px"
                    ></div>
                  </div>
                </v-tab>
              </FormTab>
            </ProfileCard>
          </v-container>
          <v-container fluid class="currency-conversion-data">
            <v-window v-model="openedSubTab">
              <v-window-item value="Currency Conversion">
                <div>
                  <div v-if="listLoading" class="mt-3">
                    <v-skeleton-loader
                      ref="skeleton1"
                      type="table-heading"
                      class="mx-auto"
                    ></v-skeleton-loader>
                    <div v-for="i in 3" :key="i" class="mt-4">
                      <v-skeleton-loader
                        ref="skeleton2"
                        type="list-item-avatar"
                        class="mx-auto"
                      ></v-skeleton-loader>
                    </div>
                  </div>
                  <div v-else-if="isErrorInList">
                    <AppFetchErrorScreen
                      :content="errorContent"
                      icon-name="fas fa-redo-alt"
                      image-name="common/human-error-image"
                      :button-text="showRetryBtn ? 'Retry' : ''"
                      @button-click="refetchCurrencyConversionList()"
                    >
                    </AppFetchErrorScreen>
                  </div>
                  <div
                    v-else-if="
                      !listLoading && backupCurrencyConversionData?.length == 0
                    "
                  >
                    <AppFetchErrorScreen key="no-results-screen">
                      <template #contentSlot>
                        <div style="max-width: 80%">
                          <v-row
                            v-if="!isLoading"
                            style="background: white"
                            class="rounded-lg pa-5 mb-4"
                          >
                            <v-col cols="12">
                              <NotesCard
                                notes="Currency conversion allows organizations to manage claim reimbursements involving multiple currencies efficiently and accurately. This ensures that when employees incur expenses in foreign currencies, the claims are seamlessly converted to the organization's base currency. Admins can configure this feature either manually or by setting up automated exchange rate integrations, based on the organization’s financial policies."
                                backgroundColor="transparent"
                                class="mb-2"
                              ></NotesCard>
                              <NotesCard
                                notes="Once the currency conversion is configured, it is applied seamlessly to all employee claims involving foreign currencies. During submission, employees select the currency in which the expense was incurred, and the system automatically calculates and displays the converted amount in the base currency."
                                backgroundColor="transparent"
                                class="mb-4"
                              ></NotesCard>
                            </v-col>

                            <v-col
                              cols="12"
                              class="d-flex align-center justify-center mb-4"
                            >
                              <div v-if="formAccess && formAccess.add">
                                <v-btn
                                  @click="addButtonClicked()"
                                  class="px-6 seconary mr-2"
                                  variant="elevated"
                                >
                                  <v-icon size="15" class="pr-1"
                                    >fas fa-plus</v-icon
                                  ><span class="primary"
                                    >Add Currency Conversion</span
                                  >
                                </v-btn>
                              </div>
                              <v-btn
                                color="transparent"
                                variant="flat"
                                rounded="lg"
                                :size="isMobileView ? 'small' : 'default'"
                                @click="refetchCurrencyConversionList()"
                              >
                                <v-icon class="pr-1" color="grey"
                                  >fas fa-redo-alt</v-icon
                                >
                              </v-btn>
                            </v-col>
                          </v-row>
                        </div>
                      </template>
                    </AppFetchErrorScreen>
                  </div>
                  <div
                    v-else-if="
                      currencyConversionData?.length == 0 &&
                      !listLoading &&
                      emptyFilterScreen
                    "
                  >
                    <AppFetchErrorScreen
                      image-name="common/no-records"
                      main-title="There are no currency conversion records for the selected filters/searches."
                    >
                      <template #contentSlot>
                        <div class="d-flex mb-2 flex-wrap justify-center">
                          <v-btn
                            color="primary"
                            variant="elevated"
                            class="ml-4 mt-1"
                            rounded="lg"
                            :size="isMobileView ? 'small' : 'default'"
                            @click.stop="resetFilter()"
                          >
                            Reset Filter/Search
                          </v-btn>
                        </div>
                      </template>
                    </AppFetchErrorScreen>
                  </div>
                  <div v-else>
                    <div
                      v-if="currencyConversionData.length > 0"
                      class="d-flex flex-wrap align-center my-3"
                      :class="isMobileView ? 'flex-column' : ''"
                      style="justify-content: flex-end"
                    >
                      <div
                        v-if="formAccess && formAccess.add"
                        class="d-flex align-center flex-wrap"
                        :class="isMobileView ? 'justify-center' : 'justify-end'"
                      >
                        <v-btn
                          @click="addButtonClicked()"
                          class="primary"
                          variant="elevated"
                          :size="isMobileView ? 'small' : 'default'"
                        >
                          <v-icon size="15" class="pr-1 primary"
                            >fas fa-plus</v-icon
                          >
                          <span class="primary"
                            >Add Currency Conversion</span
                          ></v-btn
                        >
                        <v-btn
                          color="transparent"
                          variant="flat"
                          rounded="lg"
                          :size="isMobileView ? 'small' : 'default'"
                          @click="refetchCurrencyConversionList()"
                        >
                          <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
                        </v-btn>
                        <v-menu class="mb-1 mt-1" transition="scale-transition">
                          <template v-slot:activator="{ props }">
                            <v-btn variant="plain" v-bind="props">
                              <v-icon>fas fa-ellipsis-v</v-icon>
                            </v-btn>
                          </template>
                          <v-list>
                            <v-list-item
                              v-for="action in moreActions"
                              :key="action.key"
                              @click="onMoreAction(action.key)"
                            >
                              <v-hover>
                                <template
                                  v-slot:default="{ isHovering, props }"
                                >
                                  <v-list-item-title
                                    v-bind="props"
                                    class="pa-3"
                                    :class="{
                                      hover: isHovering,
                                    }"
                                    ><v-icon size="15" class="pr-2">{{
                                      action.icon
                                    }}</v-icon
                                    >{{ action.key }}</v-list-item-title
                                  >
                                </template>
                              </v-hover>
                            </v-list-item>
                          </v-list>
                        </v-menu>
                      </div>
                    </div>
                    <v-data-table
                      v-if="currencyConversionData.length > 0"
                      :headers="headers"
                      :items="currencyConversionData"
                      :items-per-page="50"
                      :items-per-page-options="[
                        { value: 50, title: '50' },
                        { value: 100, title: '100' },
                        {
                          value: -1,
                          title: '$vuetify.dataFooter.itemsPerPageAll',
                        },
                      ]"
                      fixed-header
                      :height="
                        $store.getters.getTableHeightBasedOnScreenSize(
                          290,
                          currencyConversionData
                        )
                      "
                      :sort-by="[{ key: 'claimCurrencyName', order: 'asc' }]"
                      class="elevation-1"
                      style="box-shadow: none !important"
                    >
                      <template v-slot:item="{ item, index }">
                        <tr
                          style="z-index: 200; cursor: pointer"
                          class="data-table-tr bg-white cursor-pointer"
                          @click="viewCurrencyConversionDetails(item, index)"
                          :class="[
                            isMobileView
                              ? ' v-data-table__mobile-table-row ma-0 mt-2'
                              : '',
                          ]"
                        >
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5 font-weight-small'
                            "
                            :style="isMobileView ? '' : 'max-width: 100px'"
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Claim Currency
                            </div>
                            <v-tooltip
                              :text="item.claimCurrencyName"
                              location="top"
                            >
                              <template v-slot:activator="{ props }">
                                <section
                                  :class="isMobileView ? '' : 'text-truncate'"
                                >
                                  <span
                                    class="text-primary text-body-2 font-weight-regular"
                                    v-bind="props"
                                  >
                                    {{ checkNullValue(item.claimCurrencyName) }}
                                  </span>
                                  <div
                                    v-if="item.User_Defined_EmpId"
                                    class="text-grey"
                                    v-bind="props"
                                  >
                                    {{
                                      checkNullValue(item.User_Defined_EmpId)
                                    }}
                                  </div>
                                </section>
                              </template>
                            </v-tooltip>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5'
                            "
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Payroll Currency
                            </div>
                            <section>
                              <span class="text-body-2 font-weight-regular">
                                {{ checkNullValue(item.payrollCurrencyName) }}
                              </span>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5 font-weight-small'
                            "
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Conversion Type
                            </div>
                            <section>
                              <span class="text-body-2 font-weight-regular">
                                {{ checkNullValue(item.conversionType) }}
                              </span>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5 font-weight-small'
                            "
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Conversion
                            </div>
                            <section>
                              <span class="text-body-2 font-weight-regular">
                                {{ checkNullValue(item.conversionValue) }}
                              </span>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2 pl-5 font-weight-small'
                            "
                          >
                            <div id="mobile-header" class="font-weight-bold">
                              Status
                            </div>
                            <section class="text-body-2 text-primary">
                              <!-- Toggle button for Active/Inactive -->
                              <AppToggleButton
                                button-active-text="Active"
                                button-inactive-text="InActive"
                                button-active-color="#7de272"
                                button-inactive-color="red"
                                id-value="gab-analysis-based-on"
                                :current-value="item.status === 'Active'"
                                @chosen-value="
                                  (status) => updateStatus(status, item)
                                "
                                :isDisableToggle="!formAccess.update"
                                :tooltipContent="
                                  formAccess.update
                                    ? ''
                                    : `Sorry, you don't have access rights to update the status`
                                "
                              ></AppToggleButton>
                            </section>
                          </td>
                          <td
                            :class="
                              isMobileView
                                ? 'd-flex justify-space-between align-center'
                                : 'pa-2'
                            "
                            @click.stop=""
                          >
                            <div
                              v-if="isMobileView"
                              :class="
                                isMobileView
                                  ? ' font-weight-bold d-flex align-center'
                                  : ' font-weight-bold mt-2 d-flex align-center'
                              "
                            >
                              Actions
                            </div>
                            <section>
                              <div class="d-flex justify-center">
                                <ActionMenu
                                  v-if="getActions(item)"
                                  @selected-action="onActions($event, item)"
                                  :actions="getActions(item)"
                                  :access-rights="checkAccess()"
                                  :isPresentTooltip="true"
                                  iconColor="grey"
                                ></ActionMenu>
                                <section
                                  class="text-body-2 font-weight-medium"
                                  v-else
                                >
                                  -
                                </section>
                              </div>
                            </section>
                          </td>
                        </tr>
                      </template>
                    </v-data-table>
                  </div>
                </div>
              </v-window-item>
            </v-window>
          </v-container>
        </v-window-item>
      </v-window>

      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <CurrencyConversionForm
      v-if="openCurrencyConversionForm"
      :selectedCurrencyConversionData="selectedCurrencyConversionRecord"
      :isEditForm="isEditForm"
      @on-close-add-form="closeCurrencyConversionForm()"
      @refetch-list="refetchCurrencyConversionList($event)"
    />
    <ViewCurrencyConversion
      :selectedCurrencyConversionData="selectedCurrencyConversionRecord"
      :enable-view="showViewForm"
      @close-view-details="closeView()"
      @edit-currency-conversion-record="editCurrencyConversion()"
    />
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const CurrencyConversionForm = defineAsyncComponent(() =>
  import("./AddUpdateCurrencyConversion.vue")
);
const ViewCurrencyConversion = defineAsyncComponent(() =>
  import("./ViewCurrencyConversion.vue")
);
import {
  LIST_CURRENCY_CONVERSION,
  ADD_UPDATE_CURRENCY_CONVERSION,
} from "@/graphql/corehr/payrollDataManagement.js";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
import FormFilter from "./FormFilter.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import AppToggleButton from "@/components/base-components/AppToggleButton.vue";

export default {
  name: "CurrencyConversion",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    FormFilter,
    ActionMenu,
    CurrencyConversionForm,
    ViewCurrencyConversion,
    AppToggleButton,
  },
  mixins: [FileExportMixin, validationRules],
  data: () => ({
    openedSubTab: "Currency Conversion",
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    isLoading: false,
    isEdit: false,
    currentTabItem: "",
    showRetryBtn: true,
    currencyConversionData: [],
    validationMessages: [],
    showValidationAlert: false,
    isMounted: false,
    emptyFilterScreen: false,
    backupCurrencyConversionData: [],
    backupFilterData: [],
    havingAccess: {},
    openCurrencyConversionForm: false,
    showViewForm: false,
    selectedCurrencyConversionRecord: {},
    isEditForm: false,
  }),
  computed: {
    landedFormName() {
      let form = this.accessRights(354);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Travel and Expenses";
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    coreHrPayrollDataManagementFormAcess() {
      return this.$store.getters.coreHrPayrollDataManagementFormAcess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.coreHrPayrollDataManagementFormAcess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          formAccess[access].havingAccess = true;
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("355");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    headers() {
      return [
        {
          title: "Claim Currency",
          align: "start",
          key: "claimCurrencyName",
        },

        {
          title: "Payroll Currency",
          key: "payrollCurrencyName",
        },
        {
          title: "Conversion Type",
          key: "conversionType",
        },
        {
          title: "Conversion",
          key: "conversionValue",
        },
        {
          title: "Status",
          key: "status",
        },
        {
          title: "Actions",
          key: "action",
          align: "center",
          sortable: false,
        },
      ];
    },
    myTeamTimeOffFormAccess() {
      return this.$store.getters.myTeamTimeOffFormAccess;
    },
    subTabItems() {
      let initialTabs = [
        {
          label: "Currency Conversion",
          value: "Currency Conversion",
          disable: false,
        },
      ];
      return initialTabs;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    enablePendingApprovals() {
      return (
        this.backupCurrencyConversionData?.some(
          (el) =>
            !el.Process_Instance_Id &&
            (el.Approval_Status?.toLowerCase() === "applied" ||
              el.Approval_Status?.toLowerCase() === "cancel applied")
        ) || false
      );
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.listCurrencyConversionDetails();
    this.openedSubTab = "Currency Conversion";
    this.isMounted = true;
  },
  methods: {
    checkNullValue,
    getActions() {
      const optionMenu = [];
      optionMenu.push("Edit");
      return optionMenu;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
      // For future use
      // if (val.toLowerCase() == "sub tab name") {
      //   this.$router.push("/my-team/core-hr/sub-tab-url");
      // }
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    addButtonClicked() {
      this.isEditForm = false;
      this.openCurrencyConversionForm = true;
      this.selectedCurrencyConversionRecord = {};
    },
    onActions(type, item) {
      if (type && type.toLowerCase() === "edit") {
        this.editCurrencyConversion(item);
      }
    },
    checkAccess() {
      this.havingAccess["update"] = this.formAccess?.update ? 1 : 0;
      return this.havingAccess;
    },
    resetFilter() {
      this.currencyConversionData = this.backupCurrencyConversionData;
      this.emptyFilterScreen = false;
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.applyFilter(this.currencyConversionData);
    },
    applyFilter(filteredArray) {
      this.currencyConversionData = filteredArray;
      this.backupFilterData = filteredArray;
      if (this.currencyConversionData.length == 0) {
        this.emptyFilterScreen = true;
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onApplySearch(val) {
      if (!val) {
        this.currencyConversionData = this.backupFilterData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupFilterData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.currencyConversionData = searchItems;
        if (this.currencyConversionData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    getStatusClass(status) {
      if (status === "Approved") {
        return "text-green";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    formatIntoHoursAndMinutes(item) {
      let hours = Math.floor(item);
      let minutes = Math.round((item - hours) * 60);
      return `${hours} hour(s) ${minutes} min(s)`;
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
    },

    exportReportFile() {
      let currencyConversionList = this.currencyConversionData.map((item) => ({
        ...item,
      }));
      let fileName = "Currency Conversion";
      let exportHeaders = [
        { header: "Claim Currency", key: "claimCurrencyName" },
        { header: "Payroll Currency", key: "payrollCurrencyName" },
        { header: "Conversion Type", key: "conversionType" },
        { header: "Conversion", key: "conversionValue" },
        { header: "Status", key: "status" },
        { header: "Added By", key: "addedByName" },
        { header: "Added On", key: "addedOn" },
        { header: "Updated By", key: "updatedByName" },
        { header: "Updated On", key: "updatedOn" },
      ];
      let exportOptions = {
        fileExportData: currencyConversionList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    closeEditForm() {
      this.isEdit = false;
    },
    openEditForm() {
      this.isEdit = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.coreHrPayrollDataManagementFormAcess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },
    updateStatus(locationStatus, item) {
      // Extract the boolean value from the array
      const newStatus = Array.isArray(locationStatus)
        ? locationStatus[1]
        : locationStatus;

      // Check if the new status is different from the current status
      const currentStatus = item.status.toLowerCase() === "active";

      if (currentStatus === newStatus) {
        // If the status hasn't changed, do nothing
        return;
      }
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_CURRENCY_CONVERSION,
          variables: {
            conversionId: item.conversionId,
            claimCurrencyId: item.claimCurrencyId,
            conversionType: item.conversionType,
            conversionValue: item.conversionValue
              ? parseFloat(item.conversionValue)
              : 0,
            status: locationStatus[1] ? "Active" : "InActive",
            formId: 355,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          if (res && res.data && res.data.addUpdateCurrencyConversion) {
            const { errorCode } = res.data.addUpdateCurrencyConversion;
            if (!errorCode) {
              let snackbarData = {
                isOpen: true,
                message: `Currency Conversion status updated successfully`,
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.listLoading = false;
              vm.listCurrencyConversionDetails();
            } else {
              vm.handleUpdateError();
            }
          } else {
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },
    handleUpdateError(err = "") {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "currency conversion status",
        isListError: false,
      });
    },
    listCurrencyConversionDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_CURRENCY_CONVERSION,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            formId: 355,
          },
        })
        .then((response) => {
          if (response.data?.listCurrencyConversion?.data) {
            vm.backupCurrencyConversionData =
              response.data.listCurrencyConversion.data;
            vm.currencyConversionData = vm.backupCurrencyConversionData;
            vm.currencyConversionData.status = "Active";
            if (vm.currencyConversionData && vm.currencyConversionData.length) {
              vm.applyFilter(vm.currencyConversionData);
            }
          } else {
            vm.handleListError();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    refetchCurrencyConversionList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.openCurrencyConversionForm = false;
      this.listCurrencyConversionDetails();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
    },
    closeCurrencyConversionForm() {
      this.openCurrencyConversionForm = false;
    },
    closeView() {
      this.showViewForm = false;
    },
    viewCurrencyConversionDetails(item) {
      this.showViewForm = true;
      this.selectedCurrencyConversionRecord = item;
    },
    editCurrencyConversion(item = {}) {
      this.openCurrencyConversionForm = true;
      this.isEditForm = true;
      this.showViewForm = false;
      if (Object.keys(item).length) {
        this.selectedCurrencyConversionRecord = item;
      }
    },
  },
};
</script>

<style scoped>
.currency-conversion-container {
  padding: 3.7em 0em 0em 0em;
}
.custom-box-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.currency-conversion-card {
  padding: 0em 0em 0em 0em;
}

.currency-conversion-data {
  padding-left: 2em;
  padding-right: 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .currency-conversion-container {
    padding: 4em 1em 0em 1em;
  }
  .currency-conversion-card {
    padding: 0em 0em 0em 0em;
  }
  .currency-conversion-data {
    padding: 0em 0em 0em 0em;
  }
}
</style>
