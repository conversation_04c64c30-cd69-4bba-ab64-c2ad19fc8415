<template>
  <v-container class="employee-type-container" fluid>
    <div>
      <v-row justify="center">
        <v-col cols="12" lg="11" md="12" sm="12">
          <v-card min-height="560" class="rounded-lg">
            <v-card-text>
              <div class="text-center mb-6">
                <span v-for="i in 3" :key="i">
                  <v-icon color="primary" size="18" class="ml-1">{{
                    currentStep >= i ? "fas fa-circle" : "far fa-circle"
                  }}</v-icon>
                </span>
              </div>
              <BulkImportStep1
                class="mb-10"
                v-show="currentStep === 1"
                ref="bulkStep1"
                :step1-text="step1Text"
                @file-upload-success="uploadFile($event)"
                @file-upload-error="fileRemoveOrError()"
                @generate-excel="onGenerateExcel()"
                :showDownload="true"
              />
              <BulkImportStep2
                class="mb-10 pb-5"
                v-if="fileContent.length > 0 && currentStep === 2"
                ref="bulkStep2"
                :file-params="fileContent"
                :headers-selected="selectedHeaders"
                @column-mapped="
                  matchedCount = $event[0];
                  mappedFileHeader = $event[1];
                "
              />
              <BulkImportStep3
                class="mb-10"
                ref="bulkImportStep3"
                v-if="checkMatchedFields && currentStep === 3"
                :fields="generateFields"
                :json-data="excelEditorData"
                type-of-import="employeetype"
                :extend-validation="excelValidation"
              />
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-bottom-navigation v-model="openBottomSheet">
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
        >
          <v-row class="pa-3" justify="center">
            <v-col
              v-if="!isLoadUploadButton"
              cols="6"
              class="pa-0 d-flex justify-start align-center pl-2"
            >
              <v-btn
                v-if="currentStep > 1"
                id="back_to_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                color="primary"
                @click="backToStep()"
              >
                <span><i class="fa fa-chevron-left pr-2"></i> Back</span>
              </v-btn>
              <v-btn
                id="cancel_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                class="ml-2"
                color="primary"
                @click="closeForm()"
                >Cancel</v-btn
              >
            </v-col>
            <v-col
              :cols="isLoadUploadButton ? '12' : '6'"
              class="pa-0 d-flex justify-center align-center pr-4"
              :style="windowWidth >= 1264 ? 'margin-left: -106px' : ''"
            >
              <div v-if="windowWidth > 768" class="text-end mr-2">
                <div class="mr-1 text-grey text-caption" style="width: 400px">
                  {{ nextBtnHelpContent }}
                </div>
              </div>
              <v-btn
                id="next_step"
                rounded="lg"
                color="primary"
                class="mr-10"
                :disabled="!enableNextButton"
                :loading="isLoadUploadButton"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                @click="nextStep()"
              >
                <span>
                  {{ currentStep === 3 ? "Submit" : "Next" }}
                  <v-icon v-if="currentStep !== 3" class="pl-1" size="15"
                    >fa fa-chevron-right</v-icon
                  >
                </span>
              </v-btn>
            </v-col>
            <v-col
              cols="12"
              v-if="windowWidth <= 768 && nextBtnHelpContent"
              class="pa-1 pr-4 d-flex align-center justify-end"
            >
              <div class="mr-1 text-grey mb-0" style="font-size: 10px">
                {{ nextBtnHelpContent }}
              </div>
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
    </div>
    <v-dialog v-model="importConfirmation" width="50%">
      <v-card>
        <v-row>
          <v-col v-if="invalidData && invalidData.length" cols="12">
            <v-alert prominent type="warning">
              <v-row align="center">
                <v-col v-if="invalidData" class="grow"
                  ><span>{{ invalidEmployeeType.length }}</span>
                  out of
                  {{ excelEditorData.length }} do not have valid records. This
                  may result in omission of those records.
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertEmployeeTypeData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
          <v-col v-else cols="12" class="pa-3">
            <v-alert prominent type="success">
              <v-row align="center">
                <v-col class="grow">
                  Everything looks <strong>good</strong>.
                  <div class="pt-1">
                    Are you
                    <strong>sure</strong> you want to import the Employee Type
                    details?
                  </div>
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertEmployeeTypeData(finalUpdateData)"
                    >Add</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-overlay
          class="align-center justify-center"
          contained
          :model-value="isLoading"
          scrim="#fff"
        >
          <v-progress-circular color="primary" indeterminate size="64">
          </v-progress-circular>
        </v-overlay>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import BulkImportStep1 from "@/views/common/bulkImport/BulkImportStep1.vue";
import BulkImportStep2 from "@/views/common/bulkImport/BulkImportStep2.vue";
import BulkImportStep3 from "@/views/common/bulkImport/BulkImportStep3.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
// import { IMPORT_LOCATIONS } from "@/graphql/organisation/location/OrganisationQueries.js"; // update with correct import
// import { ADD_EDIT_LIST_LOCATION_DETAILS } from "@/graphql/organisation/location/OrganisationQueries.js";
// import {
//   // LIST_LOCATION_DETAILS,
//   GET_TIME_ZONE_LIST,
// } from "@/graphql/organisation/location/OrganisationQueries.js";
// import { LIST_CITIES_NO_AUTH } from "@/graphql/dropDownQueries.js";

export default {
  name: "EmployeesTypeImport",
  components: {
    BulkImportStep1,
    BulkImportStep2,
    BulkImportStep3,
  },
  mixins: [FileExportMixin],
  props: {
    backupMainList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data: () => ({
    currentStep: 1,
    fileContent: [],
    errorsCountInExcel: 0,
    matchedCount: 0,
    openBottomSheet: true,
    isLoadUploadButton: false,
    mappedFileHeader: [],
    step1Text: {
      typeofData: "employees type sheet",
      text: "You have the option of using our predefined template or bring in your own Employee Type sheet with the headers for import",
      heading: "Download the excel template with predefined headers",
    },
    selectedImportType: 1,
    fields: [],
    // optionValues: {
    //   cityName: [],
    //   stateName: [],
    //   countryName: [],
    //   zoneId: [],
    // },
    excelEditorData: [],
    importConfirmation: false,
    finalExcelData: [],
    finalUpdateData: [],
    isLoading: false,
    step2HeadersData: [],
  }),

  computed: {
    employeesTypeList() {
      // Use Set to filter out duplicate employeeType values
      const uniqueNamesSet = new Set(
        this.backupMainList.map((item) => item.employeeType)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    excelValidation() {
      return {
        employeetype: this.employeesTypeList,
        Work_Schedule: ["Employee Level", "Shift Roster"],
        Salary_Calculation_Days: [
          "All Days of Salary Month",
          "Average Days in a Month",
          "Business Working Days",
          "Fixed Days of Salary Month",
        ],
        Comp_Off_Calculation_Days: [
          "All Days of Salary Month",
          "Average Days in a Month",
          "Business Working Days",
          "Fixed Days of Salary Month",
        ],
        Approve_Dashboard_Attendance: [
          "Automatically",
          "Automatically for Selected Workplace",
          "Manually",
        ],
        Processed_Biometric_Attendance_Record_Status: [
          "Auto Approval",
          "Manual Approval",
        ],
        Work_Place: [
          "Campaign",
          "Field",
          "Meeting",
          "Office",
          "On Duty",
          "Other Field Work",
          "Sales Closing",
          "Training/Induction",
          "Transit",
          "Work From Home",
        ],
        Status: ["Active", "InActive"],
        Eligible_For_Benefits: ["Yes", "No"],
        Holiday_Eligibility: ["Yes", "No"],
        Display_Duration_In_Hours_And_Minutes: ["Yes", "No"],
        Exclude_Break_Hours: ["Yes", "No"],
        Enable_Work_Place: ["Yes", "No"],
        // Country_Name: this.optionValues.countryName || [],
      };
    },
    selectedHeaders() {
      let output = [
        {
          title: "Employee Type",
          value: "Employee_Type",
          props: { disabled: false },
        },
        {
          title: "Work Schedule",
          value: "Work_Schedule",
          props: { disabled: false },
        },
        {
          title: "Salary Calculation Days",
          value: "Salary_Calculation_Days",
          props: { disabled: false },
        },
        {
          title: "Comp Off Calculation Days",
          value: "Comp_Off_Calculation_Days",
          props: { disabled: false },
        },
        {
          title: "Eligible For Benefits",
          value: "Eligible_For_Benefits",
          props: { disabled: false },
        },
        {
          title: "Holiday Eligibility",
          value: "Holiday_Eligibility",
          props: { disabled: false },
        },
        {
          title: "Display Duration In Hours And Minutes",
          value: "Display_Duration_In_Hours_And_Minutes",
          props: { disabled: false },
        },
        {
          title: "Exclude Break Hours",
          value: "Exclude_Break_Hours",
          props: { disabled: false },
        },
        {
          title: "Processed Biometric Attendance Record Status",
          value: "Processed_Biometric_Attendance_Record_Status",
          props: { disabled: false },
        },
        {
          title: "Enable Work Place",
          value: "Enable_Work_Place",
          props: { disabled: false },
        },
        {
          title: "Approve Dashboard Attendance",
          value: "Approve_Dashboard_Attendance",
          props: { disabled: false },
        },
        {
          title: "Work Place",
          value: "Work_Place",
          props: { disabled: false },
        },
        { title: "Status", value: "Status", props: { disabled: false } },
        {
          title: "Description",
          value: "Description",
          props: { disabled: false },
        },
      ];
      return output;
    },
    invalidData() {
      return this.$refs.bulkImportStep3.invalidData;
    },
    invalidEmployeeType() {
      let invalidData = this.$refs.bulkImportStep3.invalidData;
      let employeeTypeFail = Array.from(new Set(invalidData));
      return employeeTypeFail;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    enableNextButton() {
      if (this.currentStep === 1 && this.fileContent.length > 0) {
        return true;
      } else if (this.currentStep === 2 && this.checkMatchedFields) {
        return true;
      } else if (this.currentStep === 3) {
        this.formattedFileContent();
        return true;
      } else {
        return false;
      }
    },

    nextBtnHelpContent() {
      if (this.currentStep === 1) {
        if (this.fileContent.length === 0)
          return "Please import the data with supported file types (CSV, XLSX and XLS) to continue with the next step.";
        else return "";
      } else if (this.currentStep === 2) {
        return "The unmatched optional column(s) will not be processed in the next step.";
      } else if (this.currentStep === 3) {
        if (this.formattedFileContent.length === 0) {
          return "";
        } else if (this.errorsCountInExcel !== 0) {
          return "There seems to be some validation error(s) in your file. Please amend it before uploading.";
        } else {
          return "By clicking the 'Submit' button, you can import employees type data.";
        }
      } else {
        return "";
      }
    },
    mandatoryHeader() {
      let fields = [
        "Employee Type",
        "Work Schedule",
        "Salary Calculation Days",
        "Comp Off Calculation Days",
      ];
      return fields;
    },
    checkMatchedFields() {
      let mandatoryHeader = this.mandatoryHeader;
      if (this.matchedCount === this.mandatoryHeader.length) {
        let mandatoryMatchedCount = 0;
        for (var i in this.mappedFileHeader) {
          if (mandatoryHeader.includes(this.mappedFileHeader[i].hrapp_header))
            mandatoryMatchedCount++;
        }
        this.addHeaders();
        return mandatoryMatchedCount === this.mandatoryHeader.length
          ? true
          : false;
      } else return false;
    },
    excelFileData() {
      return this.fileContent.filter(
        (content) => content.filter(Boolean).length > 0
      );
    },
    generateFields() {
      let formOutput = [
        {
          field: "Employee Type",
          label: "Employee Type",
          type: "select",
          readonly: false,
          width: "200px",
        },
        {
          field: "Work Schedule",
          label: "Work Schedule",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Employee Level", "Shift Roster"],
        },
        {
          field: "Salary Calculation Days",
          label: "Salary Calculation Days",
          type: "select",
          readonly: false,
          width: "200px",
          options: [
            "All Days of Salary Month",
            "Average Days in a Month",
            "Business Working Days",
            "Fixed Days of Salary Month",
          ],
        },
        {
          field: "Comp Off Calculation Days",
          label: "Comp Off Calculation Days",
          type: "select",
          readonly: false,
          width: "200px",
          options: [
            "All Days of Salary Month",
            "Average Days in a Month",
            "Business Working Days",
            "Fixed Days of Salary Month",
          ],
        },
        {
          field: "Eligible For Benefits",
          label: "Eligible For Benefits",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Yes", "No"],
        },
        {
          field: "Holiday Eligibility",
          label: "Holiday Eligibility",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Yes", "No"],
        },
        {
          field: "Display Duration In Hours And Minutes",
          label: "Display Duration In Hours And Minutes",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Yes", "No"],
        },
        {
          field: "Exclude Break Hours",
          label: "Exclude Break Hours",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Yes", "No"],
        },
        {
          field: "Processed Biometric Attendance Record Status",
          label: "Processed Biometric Attendance Record Status",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Auto Approval", "Manual Approval"],
        },
        {
          field: "Enable Work Place",
          label: "Enable Work Place",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Yes", "No"],
        },
        {
          field: "Approve Dashboard Attendance",
          label: "Approve Dashboard Attendance",
          type: "select",
          readonly: false,
          width: "200px",
          options: [
            "Automatically",
            "Automatically for Selected Workplace",
            "Manually",
          ],
        },
        {
          field: "Work Place",
          label: "Work Place",
          type: "select",
          readonly: false,
          width: "200px",
          options: [
            "Campaign",
            "Field",
            "Meeting",
            "Office",
            "On Duty",
            "Other Field Work",
            "Sales Closing",
            "Training/Induction",
            "Transit",
            "Work From Home",
          ],
        },
        {
          field: "Status",
          label: "Status",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Active", "InActive"],
        },
        {
          field: "Description",
          label: "Description",
          type: "string",
          readonly: false,
          width: "200px",
        },
      ];
      return formOutput;
    },
  },
  // mounted() {
  //   // mixpanel.init(Config.mixPanelToken, {
  //   //   debug: true,
  //   //   track_pageview: true,
  //   //   persistence: "localStorage",
  //   // });
  //   // mixpanel.identify(this.mixPanelId);
  //   this.fetchList();
  // },
  methods: {
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    // async fetchList() {
    //   try {
    //     // Fetch employees type details
    //     const locationResponse = await this.$apollo.query({
    //       query: LIST_CITIES_NO_AUTH,
    //       client: "apolloClientAS",
    //       fetchPolicy: "no-cache",
    //       variables: {
    //         limit: 0,
    //         offset: 0,
    //       },
    //     });

    //     // Debug: Log employees type response
    //     console.log("Location Response:", locationResponse);

    //     const locationDetails =
    //       locationResponse.data.getCityListWithState.cityDetails;

    //     // Populate option values
    //     this.optionValues.countryName = Array.from(
    //       new Set(locationDetails.map((item) => item.Country_Name))
    //     );
    //     this.optionValues.cityName = Array.from(
    //       new Set(locationDetails.map((item) => item.City_Name))
    //     );
    //     this.optionValues.stateName = Array.from(
    //       new Set(locationDetails.map((item) => item.State_Name))
    //     );

    //     // Fetch time zone details
    //     console.log("Fetching Time Zone Details..."); // Debug log before query
    //     const timeZoneResponse = await this.$apollo.query({
    //       query: GET_TIME_ZONE_LIST,
    //       client: "apolloClientAS",
    //       fetchPolicy: "no-cache",
    //     });

    //     // Debug: Log time zone response
    //     console.log("Time Zone Response:", timeZoneResponse);

    //     if (
    //       timeZoneResponse &&
    //       timeZoneResponse.data &&
    //       timeZoneResponse.data.getTimezoneList
    //     ) {
    //       const timeZoneDetails =
    //         timeZoneResponse.data.getTimezoneList.timeZoneDetails;

    //       // Debug: Check the structure of timeZoneDetails
    //       console.log("Time Zone Details:", timeZoneDetails);

    //       this.optionValues.zoneId = Array.from(
    //         new Set(timeZoneDetails.map((item) => item.TimeZone_Id))
    //       );
    //     } else {
    //       console.error("No time zone data found:", timeZoneResponse);
    //     }
    //   } catch (error) {
    //     console.error("Error fetching time zone data:", error);
    //   }
    // },
    // async fetchList() {
    //   try {
    //     // Fetch employee type details
    //     const employeeTypeResponse = await this.$apollo.query({
    //       query: LIST_EMPLOYEE_TYPES_NO_AUTH, // Replace with the appropriate query
    //       client: "apolloClientAS",
    //       fetchPolicy: "no-cache",
    //       variables: {
    //         limit: 0,
    //         offset: 0,
    //       },
    //     });

    //     // Debug: Log employee type response
    //     console.log("Employee Type Response:", employeeTypeResponse);

    //     const employeeTypeDetails =
    //       employeeTypeResponse.data.getEmployeeTypeList.employeeTypeDetails;

    //     // Populate option values based on employee type details
    //     this.optionValues.workSchedule = Array.from(
    //       new Set(employeeTypeDetails.map((item) => item.Work_Schedule))
    //     );
    //     this.optionValues.eligibleForBenefits = Array.from(
    //       new Set(employeeTypeDetails.map((item) => item.Eligible_For_Benefits))
    //     );
    //     this.optionValues.holidayEligibility = Array.from(
    //       new Set(employeeTypeDetails.map((item) => item.Holiday_Eligibility))
    //     );
    //     this.optionValues.salaryCalculationDays = Array.from(
    //       new Set(
    //         employeeTypeDetails.map((item) => item.Salary_Calculation_Days)
    //       )
    //     );
    //     this.optionValues.compOffCalculationDays = Array.from(
    //       new Set(
    //         employeeTypeDetails.map((item) => item.Comp_Off_Calculation_Days)
    //       )
    //     );
    //     this.optionValues.displayDurationInHoursAndMinutes = Array.from(
    //       new Set(
    //         employeeTypeDetails.map(
    //           (item) => item.Display_Duration_In_Hours_And_Minutes
    //         )
    //       )
    //     );
    //     this.optionValues.excludeBreakHours = Array.from(
    //       new Set(employeeTypeDetails.map((item) => item.Exclude_Break_Hours))
    //     );
    //     this.optionValues.processedBiometricAttendanceRecordStatus = Array.from(
    //       new Set(
    //         employeeTypeDetails.map(
    //           (item) => item.Processed_Biometric_Attendance_Record_Status
    //         )
    //       )
    //     );
    //     this.optionValues.enableWorkPlace = Array.from(
    //       new Set(employeeTypeDetails.map((item) => item.Enable_Work_Place))
    //     );
    //     this.optionValues.approveDashboardAttendance = Array.from(
    //       new Set(
    //         employeeTypeDetails.map((item) => item.Approve_Dashboard_Attendance)
    //       )
    //     );
    //     this.optionValues.workPlace = Array.from(
    //       new Set(employeeTypeDetails.map((item) => item.Work_Place))
    //     );
    //     this.optionValues.status = Array.from(
    //       new Set(employeeTypeDetails.map((item) => item.Status))
    //     );

    //     // Fetch additional details if needed, for example, Time Zone or other related data
    //     // For this example, we assume no additional data is required, but you can follow
    //     // a similar structure as shown in the location data fetching.

    //     // If additional queries are needed, you can follow the pattern below:
    //     /*
    // const additionalResponse = await this.$apollo.query({
    //   query: GET_ADDITIONAL_DATA, // Replace with the appropriate query
    //   client: "apolloClientAS",
    //   fetchPolicy: "no-cache",
    // });

    // // Debug: Log additional response
    // console.log("Additional Response:", additionalResponse);

    // if (additionalResponse && additionalResponse.data && additionalResponse.data.someField) {
    //   const additionalDetails = additionalResponse.data.someField;

    //   this.optionValues.additionalField = Array.from(
    //     new Set(additionalDetails.map((item) => item.someProperty))
    //   );
    // } else {
    //   console.error("No additional data found:", additionalResponse);
    // }
    // */
    //   } catch (error) {
    //     console.error("Error fetching employee type data:", error);
    //   }
    // },

    onGenerateExcel() {
      const employeeTypeObject = {
        employeeType: null,
        eligibleForBenefits: null,
        holidayEligibility: null,
        workSchedule: null,
        salaryCalculationDays: null,
        compOffCalculationDays: null,
        displayDurationInHoursAndMinutes: null,
        excludeBreakHours: null,
        processedBiometricAttendanceRecordStatus: null,
        enableWorkPlace: null,
        approveDashboardAttendance: null,
        workPlace: null,
        status: null,
        description: null,
      };

      const exportData = Array.from({ length: 100 }, () => ({
        ...employeeTypeObject,
      }));

      let headers = [
        { key: "employeeType", header: "Employee Type" },
        { key: "eligibleForBenefits", header: "Eligible For Benefits" },
        { key: "holidayEligibility", header: "Holiday Eligibility" },
        { key: "workSchedule", header: "Work Schedule" },
        { key: "salaryCalculationDays", header: "Salary Calculation Days" },
        { key: "compOffCalculationDays", header: "Comp Off Calculation Days" },
        {
          key: "displayDurationInHoursAndMinutes",
          header: "Display Duration In Hours And Minutes",
        },
        { key: "excludeBreakHours", header: "Exclude Break Hours" },
        {
          key: "processedBiometricAttendanceRecordStatus",
          header: "Processed Biometric Attendance Record Status",
        },
        { key: "enableWorkPlace", header: "Enable Work Place" },
        {
          key: "approveDashboardAttendance",
          header: "Approve Dashboard Attendance",
        },
        { key: "workPlace", header: "Work Place" },
        { key: "status", header: "Status" },
        { key: "description", header: "Description" },
      ];

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Employee Type template.xlsx",
        sheetName: "Employee Type sheet",
        header: headers,
        requiredHeaders: [
          "Employee Type",
          "Work Schedule",
          "Salary Calculation Days",
          "Comp Off Calculation Days",
          "Eligible For Benefits",
          "Holiday Eligibility",
          "Display Duration In Hours And Minutes",
          "Exclude Break Hours",
          "Processed Biometric Attendance Record Status",
          "Enable Work Place",
          "Approve Dashboard Attendance",
          "Work Place",
          "Status",
        ],
        columnHighlightProps: {
          type: "Employee Type Import",
          Work_Schedule: ["Employee Level", "Shift Roster"],
          Salary_Calculation_Days: [
            "All Days of Salary Month",
            "Average Days in a Month",
            "Business Working Days",
            "Fixed Days of Salary Month",
          ],
          Comp_Off_Calculation_Days: [
            "All Days of Salary Month",
            "Average Days in a Month",
            "Business Working Days",
            "Fixed Days of Salary Month",
          ],
          Approve_Dashboard_Attendance: [
            "Automatically",
            "Automatically for Selected Workplace",
            "Manually",
          ],
          Processed_Biometric_Attendance_Record_Status: [
            "Auto Approval",
            "Manual Approval",
          ],
          Work_Place: [
            "Campaign",
            "Field",
            "Meeting",
            "Office",
            "On Duty",
            "Other Field Work",
            "Sales Closing",
            "Training/Induction",
            "Transit",
            "Work From Home",
          ],
          Status: ["Active", "InActive"],
          Eligible_For_Benefits: ["Yes", "No"],
          Holiday_Eligibility: ["Yes", "No"],
          Display_Duration_In_Hours_And_Minutes: ["Yes", "No"],
          Exclude_Break_Hours: ["Yes", "No"],
          Enable_Work_Place: ["Yes", "No"],
        },
      };

      this.exportExcelFile(exportOptions);
    },
    formattedFileContent() {
      let generatedData = this.formExcelData();
      this.excelEditorData = generatedData;
    },
    formExcelData() {
      let fields = this.generateFields;
      let data = JSON.parse(JSON.stringify(this.excelFileData));
      let headersAssigned = this.step2HeadersData;
      //Getting the field of the array of objects
      let excelData = [];
      let idCounter = 1;
      // Iterate through each row of data
      for (let i = 1; i < data.length; i++) {
        let rowData = data[i];
        let rowObj = { $id: "000000" + idCounter++ };

        // Iterate through each field definition and populate the row object
        for (let j = 0; j < fields.length; j++) {
          let fieldDef = fields[j];
          let fieldName = fieldDef.field;
          // Find the index of the field in the header mappings array
          let headerIndex = headersAssigned.findIndex(
            (header) => header.hrapp_header === fieldName
          );

          // If the field is present in the header mappings array, use the corresponding value from the input data
          if (headerIndex >= 0) {
            let dataValue = rowData[headerIndex];
            if (dataValue !== null && dataValue !== undefined) {
              rowObj[fieldName] = dataValue;
            } else {
              rowObj[fieldName] = null;
            }
          } else {
            // If the field is not present in the header mappings array, use the default value for the field type
            switch (fieldDef.type) {
              case "string":
                rowObj[fieldName] = "";
                break;
              case "number":
                rowObj[fieldName] = 0;
                break;
              case "boolean":
                rowObj[fieldName] = false;
                break;
              default:
                rowObj[fieldName] = null;
                break;
            }
          }
        }
        excelData.push(rowObj);
      }
      return excelData;
    },
    uploadFile(content) {
      this.fileContent = content;
    },
    fileRemoveOrError() {
      this.fileContent = [];
    },
    // Will be used in future
    // insertEmployeeTypeData(data) {
    //   if (data.length) {
    //     let vm = this;
    //     vm.isLoading = true;
    //     vm.$apollo
    //       .mutate({
    //         mutation: ADD_EDIT_LIST_EMPLOYEE_TYPE_DETAILS, // Use the appropriate mutation for employee types
    //         client: "apolloClientJ",
    //         variables: {
    //           employeeTypeData: data, // Adjust the variable name as needed
    //         },
    //       })
    //       .then(async (response) => {
    //         if (response && response.data && response.data.importEmployeeType) {
    //           let { validationError } = response.data.importEmployeeType;
    //           validationError = JSON.parse(validationError);
    //           let excelInvalidData = this.$refs.bulkImportStep3.invalidData;
    //           let remainingData = [];
    //           let inputData = this.$refs.bulkImportStep3.editorData;

    //           for (let i = 0; i < inputData.length; i++) {
    //             if (excelInvalidData.includes(inputData[i].$id)) {
    //               if (!remainingData.includes(inputData[i])) {
    //                 remainingData.push(inputData[i]);
    //               }
    //             }
    //           }

    //           let backendErrorsWithMessages = [];
    //           // Validation Backend Error Exists
    //           for (let i = 0; i < validationError.length; i++) {
    //             for (
    //               let j = 0;
    //               j < validationError[i].failedArrays.length;
    //               j++
    //             ) {
    //               for (let k = 0; k < inputData.length; k++) {
    //                 if (!remainingData.includes(inputData[k])) {
    //                   remainingData.push(inputData[k]);
    //                 }
    //                 let error = JSON.parse(JSON.stringify(inputData[k]));
    //                 error.Message = validationError[i].Message;
    //                 backendErrorsWithMessages.push(error);
    //               }
    //             }
    //           }

    //           this.excelEditorData = remainingData;
    //           this.$refs.bulkImportStep3.editorData = remainingData;

    //           // Set Field Error
    //           for (let i = 0; i < backendErrorsWithMessages.length; i++) {
    //             let message = backendErrorsWithMessages[i].Message;
    //             let data = backendErrorsWithMessages[i];
    //             let field = {};
    //             if (field && message && message !== undefined) {
    //               field.name = message.includes("Work Schedule")
    //                 ? "Work Schedule"
    //                 : message.includes("Eligible For Benefits")
    //                 ? "Eligible For Benefits"
    //                 : message.includes("Holiday Eligibility")
    //                 ? "Holiday Eligibility"
    //                 : message.includes("Salary Calculation Days")
    //                 ? "Salary Calculation Days"
    //                 : message.includes("Comp Off Calculation Days")
    //                 ? "Comp Off Calculation Days"
    //                 : message.includes("Display Duration In Hours And Minutes")
    //                 ? "Display Duration In Hours And Minutes"
    //                 : message.includes("Exclude Break Hours")
    //                 ? "Exclude Break Hours"
    //                 : message.includes(
    //                     "Processed Biometric Attendance Record Status"
    //                   )
    //                 ? "Processed Biometric Attendance Record Status"
    //                 : message.includes("Enable Work Place")
    //                 ? "Enable Work Place"
    //                 : message.includes("Approve Dashboard Attendance")
    //                 ? "Approve Dashboard Attendance"
    //                 : message.includes("Work Place")
    //                 ? "Work Place"
    //                 : message.includes("Status")
    //                 ? "Status"
    //                 : "Employee Type";
    //             }
    //             this.$refs.bulkImportStep3.setFieldError(message, data, field);
    //           }

    //           vm.importConfirmation = false;
    //           vm.isLoading = false;
    //           if (
    //             !excelInvalidData.length &&
    //             !backendErrorsWithMessages.length
    //           ) {
    //             let snackbarData = {
    //               isOpen: true,
    //               type: "success",
    //               message: "Employee types imported successfully.",
    //             };
    //             vm.showAlert(snackbarData);
    //             vm.closeForm();
    //             this.$emit("refetch-data");
    //           }
    //         } else {
    //           vm.handleImportError();
    //           vm.importConfirmation = false;
    //           vm.closeForm();
    //         }
    //       })
    //       .catch((err) => {
    //         vm.handleImportError(err);
    //         vm.importConfirmation = false;
    //         vm.closeForm();
    //       });
    //   } else {
    //     this.importConfirmation = false;
    //     this.closeForm();
    //   }
    // },

    handleImportError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "importing",
        form: "employees type",
        isListError: false,
      });
    },
    addHeaders() {
      if (this.$refs.bulkStep2 && this.$refs.bulkStep2.tableItems) {
        this.step2HeadersData = this.$refs.bulkStep2.tableItems;
      }
    },
    formBulkData(data) {
      const filteredData = data;
      this.finalExcelData = data;

      this.finalUpdateData = filteredData.map((item) => {
        const newItem = {
          employeeType: item["Employee_Type"],
          eligibleForBenefits: item["Eligible_For_Benefits"],
          holidayEligibility: item["Holiday_Eligibility"],
          workSchedule: item["Work_Schedule"],
          salaryCalculationDays: item["Salary_Calculation_Days"],
          compOffCalculationDays: item["Comp_Off_Calculation_Days"],
          displayDurationInHoursAndMinutes:
            item["Display_Duration_In_Hours_And_Minutes"],
          excludeBreakHours: item["Exclude_Break_Hours"],
          processedBiometricAttendanceRecordStatus:
            item["Processed_Biometric_Attendance_Record_Status"],
          enableWorkPlace: item["Enable_Work_Place"],
          approveDashboardAttendance: item["Approve_Dashboard_Attendance"],
          workPlace: item["Work_Place"],
          status: item["Status"],
          description: item["Description"],
        };
        return newItem;
      });

      this.importConfirmation = true;
    },
    backToStep() {
      if (this.currentStep !== 1) {
        this.currentStep--;
      }
    },
    // nextStep() {
    //   if (this.enableNextButton) {
    //     if (this.currentStep === 1 && this.errorsCountInExcel === 0) {
    //       this.currentStep++;
    //     } else if (this.currentStep === 2 && this.errorsCountInExcel === 0) {
    //       this.currentStep++;
    //     }
    //   }
    // },
    nextStep() {
      if (this.currentStep === 3) {
        this.formBulkData(this.$refs.bulkImportStep3.filteredData);
      } else {
        this.currentStep += 1;
      }
    },
    closeForm() {
      this.$emit("close-import-model");
    },
  },
  // watch: {
  //   currentStep() {
  //     this.onGenerateExcel();
  //   },
  // },
};
</script>
<style>
.employee-type-container {
  padding: 5em 2em 0em 3em;
}
.v-bottom-navigation__content {
  justify-content: space-around;
  flex-direction: column;
}
.dp__button_bottom {
  display: none;
}
</style>
