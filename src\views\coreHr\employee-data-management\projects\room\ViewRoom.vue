<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="primary" size="20">fas fa-person-booth</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              {{
                formAccess.customFormName ? formAccess.customFormName : "Room"
              }}
            </div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-btn
            v-if="formAccess.update"
            @click="$emit('open-edit-form')"
            size="small"
            color="primary"
            variant="elevated"
            rounded="lg"
            >Edit</v-btn
          >
          <v-icon class="mx-1" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card>
        <div
          :style="
            isMobileView ? 'height: calc(100vh - 260px); overflow: scroll' : ''
          "
        >
          <v-card-text>
            <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
              <v-col cols="12" :class="isMobileView ? ' ml-4' : ''">
                <p class="text-subtitle-1 text-grey-darken-1">Room No</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.Room_No) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Description</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedData.Description) }}
                </p>
              </v-col>
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-card-text>
        </div>
      </v-card>
    </v-card>
  </div>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
export default {
  name: "ViewRoom",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    editedData: {},
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.editedData = Object.assign({}, newData);
        this.prefillMoreDetails();
      },
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.selectedItem.Added_On),
        addedByName = this.selectedItem.Added_By_Name,
        updatedByName = this.selectedItem.Updated_By_Name,
        updatedOn = this.convertUTCToLocal(this.selectedItem.Updated_On);
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>
