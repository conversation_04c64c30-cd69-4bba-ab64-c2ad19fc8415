<template>
  <div>
    <AppTopBarTab
      v-if="mainTabs.length > 0"
      :tabs-list="mainTabs"
      :show-bottom-sheet="false"
    ></AppTopBarTab>
    <v-container fluid class="form-container">
      <v-window v-model="currentTabItem" class="overflow-visible">
        <v-window-item value="tab-0" class="overflow-visible">
          <v-card class="pa-6 my-6 rounded-lg overflow-visible">
            <v-card-text class="pa-6 card-blue-background">
              <v-form ref="formGeneratorForm">
                <v-row>
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Report Title
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="8" lg="9">
                    <CustomSelect
                      v-if="payrollCountry == 'th'"
                      :items="thaiReportList"
                      label
                      :isAutoComplete="true"
                      :itemSelected="selectedThaiReport"
                      :rules="[required('Report', selectedThaiReport)]"
                      style="max-width: 300px"
                      @selected-item="
                        onChangeValue($event, 'selectedThaiReport')
                      "
                    ></CustomSelect>
                    <CustomSelect
                      v-else-if="payrollCountry == 'id'"
                      :items="indonesiaReportList"
                      label
                      :isAutoComplete="true"
                      :itemSelected="selectedIndonesiaReport"
                      :rules="[required('Report', selectedIndonesiaReport)]"
                      style="max-width: 300px"
                      @selected-item="
                        onChangeValue($event, 'selectedIndonesiaReport')
                      "
                    ></CustomSelect>
                    <CustomSelect
                      v-else
                      :items="reportList"
                      label
                      ref="report"
                      :isAutoComplete="true"
                      :itemSelected="selectedReport"
                      :rules="[required('Report', selectedReport)]"
                      style="max-width: 300px"
                      @selected-item="onChangeValue($event, 'selectedReport')"
                    ></CustomSelect>
                  </v-col>
                </v-row>
                <v-row
                  v-if="
                    selectedIndonesiaReport == 'BPJSTK Mutation' ||
                    selectedIndonesiaReport == 'BPJSTK Pension' ||
                    selectedIndonesiaReport == 'Yearly Tax Report'
                  "
                >
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Assessment Year
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="4" lg="3">
                    <CustomSelect
                      :items="assessmentYearList"
                      label
                      :item-selected="selectedYear"
                      :rules="[required('Assessment Year', selectedYear)]"
                      style="max-width: 300px"
                      @selected-item="selectedYear = $event"
                    ></CustomSelect>
                  </v-col>
                </v-row>
                <v-row v-if="selectedIndonesiaReport == `BPJSTK Exit`">
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Resigned Month
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="4" lg="3">
                    <datepicker
                      :format="'MMMM, yyyy'"
                      v-model="selectedMonthYear"
                      maximum-view="year"
                      minimum-view="month"
                      style="width: 80%"
                      :disabled-dates="{
                        from: new Date(),
                        preventDisableDateSelection: true,
                      }"
                    ></datepicker>
                    <div class="mt-2 ml-3">
                      <p style="color: #b00020; font-size: 12px">
                        {{ dateErrorMsg }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
                <v-row
                  v-if="
                    selectedReport ==
                      `BIR Form No. 1604-C - Annual Information Return of Income Taxes Withheld on Compensation` ||
                    selectedReport ==
                      `BIR Form No. 2316 - Certificate of Compensation Payment / Tax Withheld For Compensation Payment With or Without Tax Withheld` ||
                    selectedReport ==
                      `SSS - R5 SOCIAL SECURITY SYSTEM CONTRIBUTION COLLECTION LIST` ||
                    selectedThaiReport == 'With Holding Tax Certificate' ||
                    selectedThaiReport == 'PND 91'
                  "
                >
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Assessment Year
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="4" lg="3">
                    <datepicker
                      v-model="selectedYear"
                      maximum-view="year"
                      minimum-view="year"
                      style="width: 80%"
                      :disabled-dates="{
                        from: new Date(),
                        preventDisableDateSelection: true,
                      }"
                    ></datepicker>
                    <div class="mt-2 ml-3">
                      <p style="color: #b00020; font-size: 12px">
                        {{ dateErrorMsg }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
                <v-row
                  v-if="
                    selectedReport ==
                      `BIR Form No. 2316 - Certificate of Compensation Payment / Tax Withheld For Compensation Payment With or Without Tax Withheld` ||
                    selectedThaiReport == 'With Holding Tax Certificate'
                  "
                >
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Employee
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="8" lg="9">
                    <CustomSelect
                      :items="allEmployeesList"
                      label
                      :isAutoComplete="true"
                      :itemSelected="selectedEmployeeId"
                      :isLoading="fetchingEmployees"
                      :rules="[required('Employee', selectedEmployeeId)]"
                      itemValue="employee_id"
                      itemTitle="empNameId"
                      style="max-width: 300px"
                      @selected-item="selectedEmployeeId = $event"
                    ></CustomSelect>
                  </v-col>
                </v-row>
                <v-row
                  v-if="
                    selectedReport ==
                      `PHILHEALTH - RF1 EMPLOYER’S REMITTANCE REPORT` ||
                    selectedReport ==
                      `HDMF - MEMBER'S CONTRIBUTION REMITTANCE FORM (MCRF)` ||
                    selectedIndonesiaReport == 'BPJSTK Contribution' ||
                    selectedIndonesiaReport == 'Employee BPJSTK' ||
                    selectedIndonesiaReport == 'Monthly Tax Report' ||
                    selectedThaiReport == 'WHT PND 1' ||
                    selectedThaiReport?.toLowerCase() ==
                      'social security contribution' ||
                    selectedThaiReport == 'PND 53' ||
                    selectedThaiReport == 'PND 3' ||
                    selectedThaiReport == 'PND 1 (Monthly)'
                  "
                >
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Payroll Month
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="4" lg="3">
                    <datepicker
                      :format="'MMMM, yyyy'"
                      v-model="selectedMonthYear"
                      maximum-view="year"
                      minimum-view="month"
                      style="width: 80%"
                      :disabled-dates="{
                        from: new Date(),
                        preventDisableDateSelection: true,
                      }"
                    ></datepicker>
                    <div class="mt-2 ml-3">
                      <p style="color: #b00020; font-size: 12px">
                        {{ dateErrorMsg }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
                <v-row
                  v-if="
                    selectedReport ==
                    'SSS - R3 SOCIAL SECURITY SYSTEM EMPLOYER CONTRIBUTIONS PAYMENT RETURN'
                  "
                >
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Quarter Ending Month
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="4" lg="3">
                    <datepicker
                      :format="'MMMM, yyyy'"
                      v-model="selectedEndingMonthYear"
                      maximum-view="year"
                      minimum-view="month"
                      style="width: 80%"
                      :disabled-dates="{
                        from: new Date(),
                        preventDisableDateSelection: true,
                      }"
                    ></datepicker>
                    <div class="mt-2 ml-3">
                      <p style="color: #b00020; font-size: 12px">
                        {{ dateErrorMsg }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
                <v-row
                  v-if="
                    fieldForce &&
                    (selectedThaiReport == 'WHT PND 1' ||
                      selectedThaiReport == 'With Holding Tax Certificate' ||
                      selectedThaiReport == 'PND 53' ||
                      selectedThaiReport == 'PND 3' ||
                      selectedThaiReport == 'PND 91' ||
                      selectedThaiReport == 'PND 1 (Monthly)')
                  "
                >
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Service Provider
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="4" lg="3">
                    <CustomSelect
                      :items="serviceProviderList"
                      item-title="Service_Provider_Name"
                      item-value="Service_Provider_Id"
                      label
                      :isAutoComplete="true"
                      :itemSelected="selectedServiceProvider"
                      :rules="[
                        required('Service Provider', selectedServiceProvider),
                      ]"
                      style="max-width: 300px"
                      @selected-item="
                        onChangeValue($event, 'selectedServiceProvider')
                      "
                    ></CustomSelect>
                  </v-col>
                </v-row>
              </v-form>
            </v-card-text>
            <v-card-actions class="d-flex justify-center">
              <v-btn
                rounded="lg"
                variant="elevated"
                color="primary"
                @click="retrievePDFDetails"
                >Generate Report</v-btn
              >
            </v-card-actions>
          </v-card>
        </v-window-item>
      </v-window>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import moment from "moment";
import validationRules from "@/mixins/validationRules";
import {
  GET_MEMBER_CONTRIBUTION,
  GET_CERTIFICATE_OF_PAYMENT_REPORT,
  GET_REMITTANCEREPORT,
  GET_R3_CONTRIBUTION_REPORT,
  GET_R5_CONTRIBUTION_REPORT,
  GET_ANNUAL_INFORMATION_RETURN_REPORT,
  RETRIEVE_EXCEL_DETAILS,
  GET_WITHOLDING_TAX_CERTIFICATE,
  GET_WITHOLDING_TAX_PND,
  GENERATE_THAILAND_REPORT,
  GET_PND3_REPORT,
  GET_PND53_REPORT,
  GET_PND1_REPORT,
} from "@/graphql/reports/formGeneratorQueries.js";
import FileGeneratorMixins from "@/mixins/FileGeneratorMixins";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";
import Datepicker from "vuejs3-datepicker";
import FileExportMixin from "@/mixins/FileExportMixin";
import axios from "axios";
import numberToWords from "number-to-words";

export default defineComponent({
  name: "EmployeeProfile",
  components: { CustomSelect, Datepicker },
  mixins: [validationRules, FileExportMixin, FileGeneratorMixins],
  data() {
    return {
      currentTabItem: "tab-0",
      reportList: [],
      thaiReportList: [
        "Social Security Contribution",
        "PND 1 (Monthly)",
        "PND 3",
        "PND 53",
        // "PND 91",
        // "SSO 1-10",
        // "PVF",
        // "PND 3",
        // "WHT 50 TAWI PND3",
        // "PND 1 KOR",
        // "WHT 50 TAWI PND1 KOR",
        // "KOR TOR 20",
        "WHT PND 1",
        "With Holding Tax Certificate",
      ],
      indonesiaReportList: [
        "BPJSTK Contribution",
        "BPJSTK Mutation",
        "BPJSTK Pension",
        "BPJSTK Exit",
        "Employee BPJSTK",
        "Monthly Tax Report",
        "Yearly Tax Report",
      ],
      selectedThaiReport: "",
      selectedIndonesiaReport: "",
      selectedReport: "",
      yearList: [2025, 2024, 2023, 2022],
      selectedYear: "",
      monthYearList: [],
      quarterMonthYearList: [],
      selectedMonthYear: "",
      selectedEndingMonthYear: "",
      allEmployeesList: [],
      selectedEmployeeId: null,
      fetchingEmployees: false,
      isLoading: false,
      // pdf
      preFillDetails: {},
      pdfFile: "",
      // payroll country
      payrollCountry: "",
      selectedMonthYearModal: false,
      formattedMonthYear: "",
      dateErrorMsg: "",
      selectedServiceProvider: null,
      fetchingServiceProvider: false,
    };
  },

  computed: {
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    disabledDates() {
      return [new Date("April 01")];
    },
    mainTabs() {
      return ["Compliance Form Generation"];
    },
    loginEmployeeId() {
      return this.$store.state.orgDetails.userDetails.userDefinedEmployeeId;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    assessmentYearList() {
      let date = new Date();
      return [date.getFullYear(), date.getFullYear() - 1];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  watch: {
    selectedMonthYear(val) {
      if (val) {
        this.dateErrorMsg = "";
      }
    },
    selectedEndingMonthYear(val) {
      if (val) {
        let month = moment(val).month();
        if ((month + 1) % 3 == 0) {
          this.dateErrorMsg = "";
        } else {
          this.dateErrorMsg =
            "Please select valid months (march, june, september, december)";
        }
      }
    },
    selectedYear(val) {
      if (val) {
        this.dateErrorMsg = "";
      }
    },
  },

  mounted() {
    this.updateOrgData();
    this.payrollCountry = this.$route.query.payroll_country;

    // Get the current year
    const currentYear = "2023";

    // Initialize an empty array to hold the month-year combinations
    let monthYearCombo = [],
      quarterMonthCombo = [];

    // Loop through each month of the year (0 to 11 for Jan to Dec)
    for (let month = 0; month < 12; month++) {
      // Create a moment object for the first day of the month
      let monthMoment = moment({ year: currentYear, month: month, day: 1 });

      // Push the formatted object into the array
      monthYearCombo.push({
        value: `${month + 1},${currentYear}`,
        text: monthMoment.format("MMM, YYYY"),
      });
      if (month === 2 || month === 5 || month === 8 || month === 11) {
        quarterMonthCombo.push({
          value: `${month + 1},${currentYear}`,
          text: monthMoment.format("MMM, YYYY"),
        });
      }
    }
    this.monthYearList = monthYearCombo;
    this.quarterMonthYearList = quarterMonthCombo;
    this.fetchAllEmployeesList();
    if (this.fieldForce) {
      this.getServiceProviderList();
    }
  },

  errorCaptured() {
    return false;
  },

  methods: {
    retrieveTaxReport(variables) {
      let vm = this;
      let reportTitle = vm.selectedIndonesiaReport.trim().replace(/\s+/g, "");
      let payslipMonth = variables.month;
      let payslipYear = variables.year;
      let url =
        vm.baseUrl +
        "payroll/salary-payslip/get-indonesia-tax-report/reportTitle/" +
        reportTitle +
        "/payslipMonth/" +
        payslipMonth +
        "/payslipYear/" +
        payslipYear;
      const apiObj = {
        url: url,
        type: "GET",
        data: null,
        dataType: "json",
      };
      vm.isLoading = true;
      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((response) => {
          if (response && response.success) {
            response =
              response.data && response.data.length ? response.data : [];
          } else {
            response = [];
          }
          let exportOptions = {
            fileExportData: response,
            fileName: vm.selectedIndonesiaReport,
            sheetName: vm.selectedIndonesiaReport,
            header: vm.formMonthlyYearlyTaxReportHeader(response),
          };

          vm.exportExcelFile(exportOptions);
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleRetrieveError(err);
        });
    },
    async retrieveMonthlyYearlyTaxReport(variables) {
      let vm = this;
      vm.isLoading = true;
      try {
        let reportTitle = vm.selectedIndonesiaReport.trim().replace(/\s+/g, "");
        let payslipMonth = variables.month;
        let payslipYear = variables.year;

        let url =
          this.baseUrl +
          "payroll/salary-payslip/get-indonesia-tax-report/reportTitle/" +
          reportTitle +
          "/payslipMonth/" +
          payslipMonth +
          "/payslipYear/" +
          payslipYear;

        let response = await axios.get(url);

        if (response && response.data && response.data.success) {
          response =
            response.data && response.data.data ? response.data.data : [];
        } else {
          response = [];
        }

        let exportOptions = {
          fileExportData: response,
          fileName: vm.selectedIndonesiaReport,
          sheetName: vm.selectedIndonesiaReport,
          header: vm.formMonthlyYearlyTaxReportHeader(response),
        };

        vm.exportExcelFile(exportOptions);
        vm.isLoading = false;
      } catch (err) {
        vm.handleRetrieveError(err);
      }
    },
    formMonthlyYearlyTaxReportHeader(data) {
      let header = [];
      if (data && data.length > 0) {
        //Replace _ with space
        header = Object.keys(data[0]).map((key) => {
          return {
            key: key,
            header: key.replace(/_/g, " "),
          };
        });
      }
      return header;
    },
    validateDate() {
      if (
        this.selectedReport ===
          "HDMF - MEMBER'S CONTRIBUTION REMITTANCE FORM (MCRF)" ||
        this.selectedReport ===
          "PHILHEALTH - RF1 EMPLOYER’S REMITTANCE REPORT" ||
        this.selectedIndonesiaReport === "BPJSTK Contribution" ||
        this.selectedIndonesiaReport === "Employee BPJSTK" ||
        this.selectedIndonesiaReport === "Monthly Tax Report" ||
        this.selectedThaiReport == "WHT PND 1" ||
        this.selectedThaiReport?.toLowerCase() ===
          "social security contribution" ||
        this.selectedThaiReport?.toLowerCase() === "pnd 1 (monthly)"
      ) {
        if (this.selectedMonthYear !== "") {
          return true;
        } else {
          this.dateErrorMsg = "Payroll month is required";
          return false;
        }
      } else if (
        this.selectedReport ===
          "BIR Form No. 2316 - Certificate of Compensation Payment / Tax Withheld For Compensation Payment With or Without Tax Withheld" ||
        this.selectedReport ===
          "SSS - R5 SOCIAL SECURITY SYSTEM CONTRIBUTION COLLECTION LIST" ||
        this.selectedReport ===
          "BIR Form No. 1604-C - Annual Information Return of Income Taxes Withheld on Compensation" ||
        this.selectedIndonesiaReport == "BPJSTK Mutation" ||
        this.selectedIndonesiaReport == "BPJSTK Pension" ||
        this.selectedIndonesiaReport == "Yearly Tax Report"
      ) {
        if (this.selectedYear !== "") {
          return true;
        } else {
          this.dateErrorMsg = "Assessment year is required";
          return false;
        }
      } else if (
        this.selectedReport ===
        "SSS - R3 SOCIAL SECURITY SYSTEM EMPLOYER CONTRIBUTIONS PAYMENT RETURN"
      ) {
        this.dateErrorMsg = "Quarter ending month is required";
        if (this.selectedEndingMonthYear !== "") {
          let month = moment(this.selectedEndingMonthYear).month();
          if ((month + 1) % 3 === 0) {
            this.dateErrorMsg = "";
            return true;
          } else {
            this.dateErrorMsg =
              "Please select valid months (march, june, september, december)";
            return false;
          }
        } else {
          this.dateErrorMsg = "Quarter ending month is required";
          return false;
        }
      } else if (this.selectedIndonesiaReport == `BPJSTK Exit`) {
        if (this.selectedMonthYear !== "") {
          return true;
        } else {
          this.dateErrorMsg = "Resigned year is required";
          return false;
        }
      } else {
        return true;
      }
    },
    updateOrgData() {
      if (this.orgCode === "govph") {
        this.reportList = [
          "PHILHEALTH - RF1 EMPLOYER’S REMITTANCE REPORT",
          "HDMF - MEMBER'S CONTRIBUTION REMITTANCE FORM (MCRF)",
          "BIR Form No. 1604-C - Annual Information Return of Income Taxes Withheld on Compensation",
          "BIR Form No. 2316 - Certificate of Compensation Payment / Tax Withheld For Compensation Payment With or Without Tax Withheld",
          "GSIS MEMBER’S REQUEST FORM (MRF)",
          "SELF-DECLARATION OF SURVIVING SPOUSE (on Non-Marriage / Non-Cohabitation)",
          "ANNUAL PENSIONERS INFORMATION REVALIDATION (APIR) FORM",
          "RESTRUCTURING PROGRAM FOR SERVICE LOANS (GSIS-RPSL)",
          "GSIS PENSIONER’S REQUEST FORM (PRF)",
        ];
      } else {
        this.reportList = [
          "PHILHEALTH - RF1 EMPLOYER’S REMITTANCE REPORT",
          "HDMF - MEMBER'S CONTRIBUTION REMITTANCE FORM (MCRF)",
          "SSS - R5 SOCIAL SECURITY SYSTEM CONTRIBUTION COLLECTION LIST",
          "SSS - R3 SOCIAL SECURITY SYSTEM EMPLOYER CONTRIBUTIONS PAYMENT RETURN",
          "BIR Form No. 1604-C - Annual Information Return of Income Taxes Withheld on Compensation",
          "BIR Form No. 2316 - Certificate of Compensation Payment / Tax Withheld For Compensation Payment With or Without Tax Withheld",
        ];
      }
    },
    onChangeValue(value, field) {
      this[field] = value;
      this.dateErrorMsg = "";
    },
    async retrievePDFDetails() {
      let isFormValid = await this.$refs.formGeneratorForm.validate();
      if (isFormValid && isFormValid.valid) {
        isFormValid.valid = this.validateDate();
      }
      if (isFormValid && isFormValid.valid) {
        if (
          !this.selectedIndonesiaReport &&
          !this.selectedIndonesiaReport.length
        ) {
          if (
            this.selectedThaiReport &&
            this.selectedThaiReport !== "WHT PND 1" &&
            this.selectedThaiReport !== "With Holding Tax Certificate" &&
            this.selectedThaiReport !== "Social Security Contribution" &&
            this.selectedThaiReport?.toLowerCase() !== "pnd 53" &&
            this.selectedThaiReport?.toLowerCase() !== "pnd 3" &&
            this.selectedThaiReport?.toLowerCase() !== "pnd 1 (monthly)"
          ) {
            this.preparePdf();
          } else if (
            this.selectedThaiReport?.toLowerCase() ===
            "social security contribution"
          ) {
            const variables = {
              payrollMonthYear: moment(this.selectedMonthYear).format("M,YYYY"),
              reportType: this.selectedThaiReport,
            };
            this.generateThailandReport(variables);
          } else {
            let apiMethod = "",
              apiQuery = "",
              apiVariables = {};
            // based on report type selected define API method, query and variables
            if (
              this.selectedReport ==
              "HDMF - MEMBER'S CONTRIBUTION REMITTANCE FORM (MCRF)"
            ) {
              apiMethod = "getMemberContributionReport";
              apiQuery = GET_MEMBER_CONTRIBUTION;
              apiVariables = {
                payRollMonth: moment(this.selectedMonthYear).format("M,YYYY"),
              };
              this.pdfFile = "memberContribution";
            } else if (
              this.selectedReport ==
              "BIR Form No. 2316 - Certificate of Compensation Payment / Tax Withheld For Compensation Payment With or Without Tax Withheld"
            ) {
              apiMethod = "getCertificateOfPaymentReport";
              apiQuery = GET_CERTIFICATE_OF_PAYMENT_REPORT;
              apiVariables = {
                employeeId: parseInt(this.selectedEmployeeId),
                assessmentYear: parseInt(
                  moment(this.selectedYear).format("yyyy")
                ),
              };
              this.pdfFile = "BIR2316";
            } else if (
              this.selectedReport ==
              "PHILHEALTH - RF1 EMPLOYER’S REMITTANCE REPORT"
            ) {
              apiMethod = "getEmployerRemittanceReport";
              apiQuery = GET_REMITTANCEREPORT;
              apiVariables = {
                payRollMonth: moment(this.selectedMonthYear).format("M,YYYY"),
              };
              this.pdfFile = "rF1Philhealth";
            } else if (
              this.selectedReport ==
              "SSS - R3 SOCIAL SECURITY SYSTEM EMPLOYER CONTRIBUTIONS PAYMENT RETURN"
            ) {
              apiMethod = "getR3MonthlyContributionReport";
              apiQuery = GET_R3_CONTRIBUTION_REPORT;
              apiVariables = {
                quarterEndMonth: moment(this.selectedEndingMonthYear).format(
                  "M,YYYY"
                ),
              };
              this.pdfFile = "sssFormR-3";
            } else if (
              this.selectedReport ==
              "SSS - R5 SOCIAL SECURITY SYSTEM CONTRIBUTION COLLECTION LIST"
            ) {
              apiMethod = "getR5ContributionReport";
              apiQuery = GET_R5_CONTRIBUTION_REPORT;
              apiVariables = {
                assessmentYear: parseInt(
                  moment(this.selectedYear).format("yyyy")
                ),
              };
              this.pdfFile = "sssR5Form";
            } else if (
              this.selectedReport ==
              "BIR Form No. 1604-C - Annual Information Return of Income Taxes Withheld on Compensation"
            ) {
              apiMethod = "getAnnualInformationReturnReport";
              apiQuery = GET_ANNUAL_INFORMATION_RETURN_REPORT;
              apiVariables = {
                assessmentYear: parseInt(
                  moment(this.selectedYear).format("yyyy")
                ),
              };
              this.pdfFile = "1604-C Jan 2018 Final";
            } else if (this.selectedThaiReport == "WHT PND 1") {
              apiMethod = "getPND1Report";
              apiQuery = GET_WITHOLDING_TAX_PND;
              apiVariables = {
                payRollMonth: this.selectedMonthYear
                  ? moment(this.selectedMonthYear).format("M,YYYY")
                  : null,
                serviceProviderId: this.selectedServiceProvider,
              };
              this.pdfFile = "WTH_PND1";
            } else if (
              this.selectedThaiReport == "With Holding Tax Certificate"
            ) {
              apiMethod = "getWithHoldTaxCertificate";
              apiQuery = GET_WITHOLDING_TAX_CERTIFICATE;
              apiVariables = {
                employeeId: this.selectedEmployeeId,
                assessmentYear: this.selectedYear
                  ? parseInt(moment(this.selectedYear).format("YYYY"))
                  : null,
                serviceProviderId: this.selectedServiceProvider,
              };
              this.pdfFile = "WTC";
            } else if (this.selectedThaiReport?.toLowerCase() === "pnd 53") {
              apiMethod = "getPND53Report";
              apiQuery = GET_PND53_REPORT;
              apiVariables = {
                payRollMonth: moment(this.selectedMonthYear).isValid()
                  ? moment(this.selectedMonthYear).format("M,YYYY")
                  : null,
                serviceProviderId: this.selectedServiceProvider,
              };
            } else if (this.selectedThaiReport?.toLowerCase() === "pnd 3") {
              apiMethod = "getPND3Report";
              apiQuery = GET_PND3_REPORT;
              apiVariables = {
                payRollMonth: moment(this.selectedMonthYear).isValid()
                  ? moment(this.selectedMonthYear).format("M,YYYY")
                  : null,
                serviceProviderId: this.selectedServiceProvider,
              };
            } else if (
              this.selectedThaiReport?.toLowerCase() === "pnd 1 (monthly)"
            ) {
              apiMethod = "getPND1MonthlyReport";
              apiQuery = GET_PND1_REPORT;
              apiVariables = {
                payRollMonth: moment(this.selectedMonthYear).isValid()
                  ? moment(this.selectedMonthYear).format("M,YYYY")
                  : null,
                serviceProviderId: this.selectedServiceProvider,
              };
              this.pdfFile = "PND1 Monthly";
            }
            this.retrieveFormTemplate(apiQuery, apiMethod, apiVariables);
          }
        } else if (
          this.selectedIndonesiaReport &&
          this.selectedIndonesiaReport.length
        ) {
          let variables = {};
          if (
            this.selectedIndonesiaReport == "BPJSTK Mutation" ||
            this.selectedIndonesiaReport == "BPJSTK Pension"
          ) {
            variables = {
              reportType: this.selectedIndonesiaReport,
              month: null,
              year: this.selectedYear,
            };
            this.retrieveExcelDetails(variables);
          } else if (
            this.selectedIndonesiaReport == "Yearly Tax Report" ||
            this.selectedIndonesiaReport == "Monthly Tax Report"
          ) {
            variables = {
              reportType: this.selectedIndonesiaReport,
              month: moment(this.selectedMonthYear).format("M,YYYY"),
              year: parseInt(moment(this.selectedMonthYear).format("yyyy")),
            };
            this.retrieveTaxReport(variables);
          } else {
            variables = {
              reportType: this.selectedIndonesiaReport,
              month: parseInt(moment(this.selectedMonthYear).format("M")),
              year: parseInt(moment(this.selectedMonthYear).format("yyyy")),
            };
            this.retrieveExcelDetails(variables);
          }
        } else {
          this.preparePdf();
        }
      }
    },
    generateThailandReport(variables) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GENERATE_THAILAND_REPORT,
          client: "apolloClientAA",
          fetchPolicy: "no-cache",
          variables: variables,
        })
        .then((res) => {
          const { generateThailandReports } = res.data;
          if (generateThailandReports) {
            const reportSummary = JSON.parse(
              generateThailandReports.reportSummary
            );
            const reportDetails = JSON.parse(
              generateThailandReports.reportDetails
            );
            if (this.selectedThaiReport == "Social Security Contribution") {
              const month = moment(this.selectedMonthYear).format("MM");
              const year = moment(this.selectedMonthYear).format("YYYY");
              this.downloadReportDocument(
                reportSummary,
                reportDetails,
                `SSC_${month}_${year}`
              );
            } else this.downloadReportDocument(reportSummary, reportDetails);
          } else {
            vm.handleRetrieveError();
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.handleRetrieveError(err);
        });
    },
    preparePdf() {
      if (
        [
          "GSIS MEMBER’S REQUEST FORM (MRF)",
          "SELF-DECLARATION OF SURVIVING SPOUSE (on Non-Marriage / Non-Cohabitation)",
          "ANNUAL PENSIONERS INFORMATION REVALIDATION (APIR) FORM",
          "RESTRUCTURING PROGRAM FOR SERVICE LOANS (GSIS-RPSL)",
          "GSIS PENSIONER’S REQUEST FORM (PRF)",
        ].includes(this.selectedReport)
      ) {
        if (this.selectedReport == "GSIS MEMBER’S REQUEST FORM (MRF)") {
          this.pdfFile = "MRF_Codified";
        } else if (
          this.selectedReport ==
          "SELF-DECLARATION OF SURVIVING SPOUSE (on Non-Marriage / Non-Cohabitation)"
        ) {
          this.pdfFile = "2SELF-DECLARATION";
        } else if (
          this.selectedReport ==
          "ANNUAL PENSIONERS INFORMATION REVALIDATION (APIR) FORM"
        ) {
          this.pdfFile = "20240508-APIR-Form";
        } else if (
          this.selectedReport ==
          "RESTRUCTURING PROGRAM FOR SERVICE LOANS (GSIS-RPSL)"
        ) {
          this.pdfFile = "20240508-RPSL";
        } else if (
          this.selectedReport == "GSIS PENSIONER’S REQUEST FORM (PRF)"
        ) {
          this.pdfFile =
            "FM-GSIS-OPS-CPR-01_Pensioner's Request Form_Rev3_10May2024";
        }
        this.fillPdf([]);
      } else if (this.selectedThaiReport) {
        if (this.selectedThaiReport == "PND 1") {
          this.pdfFile = "PND1(Monthly)";
        } else if (this.selectedThaiReport == "PND 3") {
          this.pdfFile = "PND3(Monthly)";
        } else if (this.selectedThaiReport == "SSO 1-10") {
          this.pdfFile = "SSO 1-10(Monthly)";
        } else if (this.selectedThaiReport == "PVF") {
          this.pdfFile = "PVF(Monthly)";
        } else if (this.selectedThaiReport == "WHT 50 TAWI PND3") {
          this.pdfFile = "WHT 50 TAWI PND3(Monthly)";
        } else if (this.selectedThaiReport == "PND 1 KOR") {
          this.pdfFile = "WHT PND1 KOR(Annually)";
        } else if (this.selectedThaiReport == "WHT 50 TAWI PND1 KOR") {
          this.pdfFile = "WHT PND1 KOR(Annually)";
        } else if (this.selectedThaiReport == "KOR TOR 20") {
          this.pdfFile = "KOR TOR 20(Annually)";
        } else if (this.selectedThaiReport == "PND 91") {
          this.pdfFile = "PND91";
          this.formPND91();
        }
        this.fillPdf([]);
      }
    },
    retrieveExcelDetails(variables) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EXCEL_DETAILS,
          client: "apolloClientAT",
          fetchPolicy: "no-cache",
          variables: variables,
        })
        .then((res) => {
          let { generateIndonesiaReports } = res.data;
          if (generateIndonesiaReports) {
            let exportOptions = {
              fileExportData: JSON.parse(generateIndonesiaReports.reportData),
              fileName: this.selectedIndonesiaReport,
              sheetName: this.selectedIndonesiaReport,
              header: JSON.parse(generateIndonesiaReports.headers),
            };
            vm.isLoading = false;
            vm.exportExcelFile(exportOptions);
          } else {
            vm.handleRetrieveError();
          }
        })
        .catch((err) => {
          vm.handleRetrieveError(err);
        });
    },
    retrieveFormTemplate(apiQuery, apiMethod, apiVariables) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: apiQuery,
          client: "apolloClientAT",
          fetchPolicy: "no-cache",
          variables: apiVariables,
        })
        .then((response) => {
          if (
            response.data &&
            response.data[apiMethod] &&
            !response.data[apiMethod].error
          ) {
            let details = response.data[apiMethod];
            vm.preFillDetails = details;
            vm.formPdfArrayBasedOnReportTypeChosen();
          } else {
            vm.handleRetrieveError();
          }
        })
        .catch((err) => {
          vm.handleRetrieveError(err);
        });
    },
    formPdfArrayBasedOnReportTypeChosen() {
      if (
        this.selectedReport ==
        "HDMF - MEMBER'S CONTRIBUTION REMITTANCE FORM (MCRF)"
      ) {
        this.formMembersContributionRemittanceForm();
      } else if (
        this.selectedReport ==
        "BIR Form No. 2316 - Certificate of Compensation Payment / Tax Withheld For Compensation Payment With or Without Tax Withheld"
      ) {
        this.formBIR2316();
      } else if (
        this.selectedReport == "PHILHEALTH - RF1 EMPLOYER’S REMITTANCE REPORT"
      ) {
        this.formEmployerRemittanceReport();
      } else if (
        this.selectedReport ==
        "SSS - R3 SOCIAL SECURITY SYSTEM EMPLOYER CONTRIBUTIONS PAYMENT RETURN"
      ) {
        this.formEmployerR3Report();
      } else if (
        this.selectedReport ==
        "SSS - R5 SOCIAL SECURITY SYSTEM CONTRIBUTION COLLECTION LIST"
      ) {
        this.formR5ContributionReport();
      } else if (
        this.selectedReport ==
        "BIR Form No. 1604-C - Annual Information Return of Income Taxes Withheld on Compensation"
      ) {
        this.formBir1604();
      } else if (this.selectedThaiReport == "WHT PND 1") {
        this.formWhtPnd1();
      } else if (this.selectedThaiReport == "With Holding Tax Certificate") {
        this.formWithHoldingTaxCertificate();
      } else if (this.selectedThaiReport?.toLowerCase() === "pnd 53") {
        const details = JSON.parse(this.preFillDetails.employeeDetails);
        const month = moment(this.selectedMonthYear).format("MM");
        const year = moment(this.selectedMonthYear).format("YYYY");
        this.downloadReportDocument(null, details, `PND53_${month}_${year}`);
        this.isLoading = false;
      } else if (this.selectedThaiReport?.toLowerCase() === "pnd 3") {
        const details = JSON.parse(this.preFillDetails.pnd3Details);
        const month = moment(this.selectedMonthYear).format("MM");
        const year = moment(this.selectedMonthYear).format("YYYY");
        this.downloadReportDocument(null, [details], `PND3_${month}_${year}`);
        this.isLoading = false;
      } else if (this.selectedThaiReport?.toLowerCase() === "pnd 1 (monthly)") {
        this.formPND1Monthly();
      }
    },
    formEmployerR3Report() {
      const empType = this.preFillDetails?.typeOfEmployer?.toLowerCase();
      let pdfArray = [
        {
          x: 193,
          y: 192,
          isImage: false,
          value: this.preFillDetails.employerName,
          page: 1,
        },
        {
          x: 46,
          y: 166,
          isImage: false,
          value: this.preFillDetails.mobileNo,
          page: 1,
        },
        {
          x: 193,
          y: 166,
          isImage: false,
          value:
            this.preFillDetails.maillingAddress.street1 +
            "," +
            this.preFillDetails.maillingAddress.street2 +
            "," +
            this.preFillDetails.maillingAddress.cityName +
            "," +
            this.preFillDetails.maillingAddress.stateName +
            "," +
            this.preFillDetails.maillingAddress.countryName +
            "," +
            this.preFillDetails.maillingAddress.pincode,
          page: 1,
        },
        {
          x: empType.includes("regular") ? 617 : 688,
          y: 164,
          isImage: true,
          value: "",
          page: 1,
        },
        {
          x: 385,
          y: -137,
          isImage: false,
          value: this.preFillDetails.totalOrgShareAmount1,
          page: 1,
        },
        {
          x: 448,
          y: -137,
          isImage: false,
          value: this.preFillDetails.totalOrgShareAmount2,
          page: 1,
        },
        {
          x: 510,
          y: -137,
          isImage: false,
          value: this.preFillDetails.totalOrgShareAmount3,
          page: 1,
        },
        {
          x: 573,
          y: -137,
          isImage: false,
          value: this.preFillDetails.totalEmpShareAmount1,
          page: 1,
        },
        {
          x: 607,
          y: -137,
          isImage: false,
          value: this.preFillDetails.totalEmpShareAmount2,
          page: 1,
        },
        {
          x: 640,
          y: -137,
          isImage: false,
          value: this.preFillDetails.totalEmpShareAmount3,
          page: 1,
        },
      ];
      if (
        this.preFillDetails.employeeDetails &&
        this.preFillDetails.employeeDetails.length > 0
      ) {
        this.preFillDetails.employeeDetails
          .slice(0, 15)
          .forEach((item, index) => {
            const YAxis = 110 - index * 16;
            const empName = `${item?.firstName ? item?.firstName : ""}  ${
              item?.middleName ? item?.middleName : ""
            }  ${item?.lastName ? item?.lastName : ""}`;
            pdfArray.push(
              {
                x: 165,
                y: YAxis,
                isImage: false,
                value: empName,
                page: 1,
              },
              {
                x: 674,
                y: YAxis,
                isImage: false,
                value: item?.separationDate
                  ? item?.separationDate?.slice(5, 7).split("").join("  ")
                  : "",
                page: 1,
              },
              {
                x: 697,
                y: YAxis,
                isImage: false,
                value: item?.separationDate
                  ? item?.separationDate?.slice(8, 10).split("").join("  ")
                  : "",
                page: 1,
              },
              {
                x: 719,
                y: YAxis,
                isImage: false,
                value: item?.separationDate
                  ? item?.separationDate?.slice(0, 4).split("").join("  ")
                  : "",
                page: 1,
              },
              {
                x: 385,
                y: YAxis,
                isImage: false,
                value: Math.round(item?.orgShareAmount1)
                  .toString()
                  .split("")
                  .join("  "),
                page: 1,
              },
              {
                x: 448,
                y: YAxis,
                isImage: false,
                value: Math.round(item?.orgShareAmount2)
                  .toString()
                  .split("")
                  .join("  "),
                page: 1,
              },
              {
                x: 510,
                y: YAxis,
                isImage: false,
                value: Math.round(item?.orgShareAmount3)
                  .toString()
                  .split("")
                  .join("  "),
                page: 1,
              },
              {
                x: 573,
                y: YAxis,
                isImage: false,
                value: Math.round(item?.empShareAmount1)
                  .toString()
                  .slice(0, 3)
                  .split("")
                  .join("  "),
                page: 1,
              },
              {
                x: 607,
                y: YAxis,
                isImage: false,
                value: Math.round(item?.empShareAmount2)
                  .toString()
                  .slice(0, 3)
                  .split("")
                  .join("  "),
                page: 1,
              },
              {
                x: 640,
                y: YAxis,
                isImage: false,
                value: Math.round(item?.empShareAmount3)
                  .toString()
                  .slice(0, 3)
                  .split("")
                  .join("  "),
                page: 1,
              }
            );
          });
      }
      this.fillPdf(pdfArray);
    },

    formEmployerRemittanceReport() {
      const empType = this.preFillDetails.employerType?.toLowerCase();
      const reportType = this.preFillDetails.reportType?.toLowerCase();
      let pdfArray = [
        {
          x: 200,
          y: 160,
          isImage: false,
          value: this.preFillDetails.employerName,
          page: 1,
        },
        {
          x: 200,
          y: 147,
          isImage: false,
          value:
            this.preFillDetails.maillingAddress.street1 +
            "," +
            this.preFillDetails.maillingAddress.street2,
          page: 1,
        },
        {
          x: 200,
          y: 135,
          isImage: false,
          value:
            this.preFillDetails.maillingAddress.cityName +
            "," +
            this.preFillDetails.maillingAddress.stateName +
            "," +
            this.preFillDetails.maillingAddress.countryName +
            "," +
            this.preFillDetails.maillingAddress.pincode,
          page: 1,
        },
        {
          x: 651,
          y: -149,
          isImage: false,
          value: this.preFillDetails.personalShareTotal,
          page: 1,
        },
        {
          x: 681,
          y: -149,
          isImage: false,
          value: this.preFillDetails.employerShareTotal,
          page: 1,
        },
        {
          x: 140,
          y: 122,
          isImage: false,
          value: this.preFillDetails.mobileNo,
          page: 1,
        },
        {
          x: 550,
          y: empType.includes("private")
            ? 142
            : empType.includes("government")
            ? 132
            : 122,
          isImage: true,
          value: "",
          page: 1,
        },
        {
          x: 620,
          y: reportType.includes("regular")
            ? 142
            : reportType.includes("addition")
            ? 132
            : 122,
          isImage: true,
          value: "",
          page: 1,
        },
        {
          x: 600,
          y: 192,
          isImage: false,
          value: this.preFillDetails.dateReceived?.slice(0, 10),
          page: 1,
        },
      ];
      this.preFillDetails.employerTinNo
        ? this.preFillDetails.employerTinNo
            .split("")
            .forEach((character, index) => {
              const XAxis =
                index < 3
                  ? 162 + index * 18
                  : index < 6
                  ? 166 + index * 18
                  : 170 + index * 18;
              pdfArray.push({
                x: XAxis,
                y: 176,
                isImage: false,
                value: character,
                page: 1,
              });
            })
        : "";
      if (
        this.preFillDetails.employeeDetails &&
        this.preFillDetails.employeeDetails.length > 0
      ) {
        this.preFillDetails.employeeDetails
          .slice(0, 10)
          .forEach((item, index) => {
            const YAxis = 53 - index * 20;
            pdfArray.push(
              {
                x: 76,
                y: YAxis,
                isImage: false,
                value: item.identificationNumber
                  ? item.identificationNumber.split("").join("  ")
                  : "",
                page: 1,
              },
              {
                x: 295,
                y: YAxis,
                isImage: false,
                value: item.firstName ? item.firstName : "",
                page: 1,
              },
              {
                x: 200,
                y: YAxis,
                isImage: false,
                value: item.lastName ? item.lastName : "",
                page: 1,
              },
              {
                x: 380,
                y: YAxis,
                isImage: false,
                value: item?.middleName ? item.middleName : "",
                page: 1,
              },
              {
                x: 541,
                y: YAxis,
                isImage: false,
                value: item.dateOfBirth ? item.dateOfBirth.slice(0, 4) : "",
                page: 1,
              },
              {
                x: 501,
                y: YAxis,
                isImage: false,
                value: item?.dateOfBirth ? item.dateOfBirth.slice(5, 7) : "",
                page: 1,
              },
              {
                x: 521,
                y: YAxis,
                isImage: false,
                value: item?.dateOfBirth ? item.dateOfBirth.slice(8, 10) : "",
                page: 1,
              },
              {
                x: 581,
                y: YAxis,
                isImage: false,
                value: item?.gender
                  ? item.gender.slice(0, 1).toUpperCase()
                  : "",
                page: 1,
              },
              {
                x: 650,
                y: YAxis,
                isImage: false,
                value: item.personalShare ? item.personalShare : "",
                page: 1,
              },
              {
                x: 680,
                y: YAxis,
                isImage: false,
                value: item.employerShare ? item.employerShare : "",
                page: 1,
              }
            );
          });
      }
      this.fillPdf(pdfArray);
    },
    formMembersContributionRemittanceForm() {
      let pdfArray = [
        {
          x: 470,
          y: 320,
          isImage: false,
          value: this.loginEmployeeId,
          page: 1,
        },
        {
          x: 30,
          y: 270,
          isImage: false,
          value: this.preFillDetails.employerName,
          page: 1,
        },
        {
          x: 200,
          y: 210,
          isImage: false,
          value: this.preFillDetails.maillingAddress.cityName,
          page: 1,
        },
        {
          x: 350,
          y: 210,
          isImage: false,
          value: this.preFillDetails.maillingAddress.countryName,
          page: 1,
        },
        {
          x: 535,
          y: 210,
          isImage: false,
          value: this.preFillDetails.maillingAddress.pincode,
          page: 1,
        },
        {
          x: 30,
          y: 210,
          isImage: false,
          value: this.preFillDetails.maillingAddress.stateName,
          page: 1,
        },
        {
          x: 350,
          y: 236,
          isImage: false,
          value: this.preFillDetails.maillingAddress.street1,
          page: 1,
        },
        {
          x: 535,
          y: 236,
          isImage: false,
          value: this.preFillDetails.maillingAddress.street2,
          page: 1,
        },
        {
          x: 473,
          y: -238,
          isImage: false,
          value: this.preFillDetails.personalShareTotal,
          page: 1,
        },
        {
          x: 507,
          y: -238,
          isImage: false,
          value: this.preFillDetails.employerShareTotal,
          page: 1,
        },
      ];
      if (
        this.preFillDetails.employeeDetails &&
        this.preFillDetails.employeeDetails.length > 0
      ) {
        if (this.preFillDetails.employeeDetails[0]) {
          pdfArray.push(
            {
              x: 157,
              y: 158,
              isImage: false,
              value: this.preFillDetails.employeeDetails[0]?.firstName
                ? this.preFillDetails.employeeDetails[0].firstName
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 227,
              y: 158,
              isImage: false,
              value: this.preFillDetails.employeeDetails[0]?.lastName
                ? this.preFillDetails.employeeDetails[0].lastName
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 24,
              y: 158,
              isImage: false,
              value: this.preFillDetails.employeeDetails[0].rtnNumber
                ? this.preFillDetails.employeeDetails[0].rtnNumber
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 467,
              y: 158,
              isImage: false,
              value: this.preFillDetails.employeeDetails[0].employerShare
                ? this.preFillDetails.employeeDetails[0].employerShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 501,
              y: 158,
              isImage: false,
              value: this.preFillDetails.employeeDetails[0].personalShare
                ? this.preFillDetails.employeeDetails[0].personalShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 534,
              y: 158,
              isImage: false,
              value:
                this.preFillDetails.employeeDetails[0].employerShare &&
                this.preFillDetails.employeeDetails[0].personalShare
                  ? (
                      parseFloat(
                        this.preFillDetails.employeeDetails[0].employerShare
                      ) +
                      parseFloat(
                        this.preFillDetails.employeeDetails[0].personalShare
                      )
                    ).toFixed(2)
                  : "",
              page: 1,
              size: 9,
            }
          );
        }
        if (this.preFillDetails.employeeDetails[1]) {
          pdfArray.push(
            {
              x: 157,
              y: 145,
              isImage: false,
              value: this.preFillDetails.employeeDetails[1].firstName,
              page: 1,
              size: 9,
            },
            {
              x: 227,
              y: 145,
              isImage: false,
              value: this.preFillDetails.employeeDetails[1].lastName,
              page: 1,
              size: 9,
            },
            {
              x: 30,
              y: 145,
              isImage: false,
              value: this.preFillDetails.employeeDetails[1].rtnNumber
                ? this.preFillDetails.employeeDetails[1].rtnNumber
                : "",
              page: 1,
            },
            {
              x: 467,
              y: 145,
              isImage: false,
              value: this.preFillDetails.employeeDetails[1].employerShare
                ? this.preFillDetails.employeeDetails[1].employerShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 501,
              y: 145,
              isImage: false,
              value: this.preFillDetails.employeeDetails[1].personalShare
                ? this.preFillDetails.employeeDetails[1].personalShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 534,
              y: 145,
              isImage: false,
              value: this.preFillDetails.employeeDetails[1].employerShare
                ? (
                    parseFloat(
                      this.preFillDetails.employeeDetails[1].employerShare
                    ) +
                    parseFloat(
                      this.preFillDetails.employeeDetails[1].personalShare
                    )
                  ).toFixed(2)
                : "",
              page: 1,
              size: 9,
            }
          );
        }
        if (this.preFillDetails.employeeDetails[2]) {
          pdfArray.push(
            {
              x: 157,
              y: 132,
              isImage: false,
              value: this.preFillDetails.employeeDetails[2].firstName,
              page: 1,
              size: 9,
            },
            {
              x: 227,
              y: 132,
              isImage: false,
              value: this.preFillDetails.employeeDetails[2].lastName,
              page: 1,
              size: 9,
            },
            {
              x: 30,
              y: 132,
              isImage: false,
              value: this.preFillDetails.employeeDetails[2].rtnNumber
                ? this.preFillDetails.employeeDetails[2].rtnNumber
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 467,
              y: 132,
              isImage: false,
              value: this.preFillDetails.employeeDetails[2].employerShare
                ? this.preFillDetails.employeeDetails[2].employerShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 501,
              y: 132,
              isImage: false,
              value: this.preFillDetails.employeeDetails[2].personalShare
                ? this.preFillDetails.employeeDetails[2].personalShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 534,
              y: 132,
              isImage: false,
              value: (
                parseFloat(
                  this.preFillDetails.employeeDetails[2].employerShare
                ) +
                parseFloat(this.preFillDetails.employeeDetails[2].personalShare)
              ).toFixed(2),
              page: 1,
              size: 9,
            }
          );
        }
        if (this.preFillDetails.employeeDetails[3]) {
          pdfArray.push(
            {
              x: 157,
              y: 119,
              isImage: false,
              value: this.preFillDetails.employeeDetails[3].firstName,
              page: 1,
              size: 9,
            },
            {
              x: 227,
              y: 119,
              isImage: false,
              value: this.preFillDetails.employeeDetails[3].lastName,
              page: 1,
              size: 9,
            },
            {
              x: 30,
              y: 119,
              isImage: false,
              value: this.preFillDetails.employeeDetails[3].rtnNumber
                ? this.preFillDetails.employeeDetails[3].rtnNumber
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 467,
              y: 119,
              isImage: false,
              value: this.preFillDetails.employeeDetails[3].employerShare
                ? this.preFillDetails.employeeDetails[3].employerShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 501,
              y: 119,
              isImage: false,
              value: this.preFillDetails.employeeDetails[3].personalShare
                ? this.preFillDetails.employeeDetails[3].personalShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 534,
              y: 119,
              isImage: false,
              value: (
                parseFloat(
                  this.preFillDetails.employeeDetails[3].employerShare
                ) +
                parseFloat(this.preFillDetails.employeeDetails[3].personalShare)
              ).toFixed(2),
              page: 1,
              size: 9,
            }
          );
        }
        if (this.preFillDetails.employeeDetails[4]) {
          pdfArray.push(
            {
              x: 157,
              y: 106,
              isImage: false,
              value: this.preFillDetails.employeeDetails[4].firstName,
              page: 1,
              size: 9,
            },
            {
              x: 227,
              y: 106,
              isImage: false,
              value: this.preFillDetails.employeeDetails[4].lastName,
              page: 1,
              size: 9,
            },
            {
              x: 30,
              y: 106,
              isImage: false,
              value: this.preFillDetails.employeeDetails[4].rtnNumber
                ? this.preFillDetails.employeeDetails[4].rtnNumber
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 467,
              y: 106,
              isImage: false,
              value: this.preFillDetails.employeeDetails[4].employerShare
                ? this.preFillDetails.employeeDetails[4].employerShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 501,
              y: 106,
              isImage: false,
              value: this.preFillDetails.employeeDetails[4].personalShare
                ? this.preFillDetails.employeeDetails[4].personalShare
                : "",
              page: 1,
              size: 9,
            },
            {
              x: 534,
              y: 106,
              isImage: false,
              value: (
                parseFloat(
                  this.preFillDetails.employeeDetails[4].employerShare
                ) +
                parseFloat(this.preFillDetails.employeeDetails[4].personalShare)
              ).toFixed(2),
              page: 1,
              size: 9,
            }
          );
        }
      }
      this.fillPdf(pdfArray);
    },
    formBIR2316() {
      let day = "",
        month = "",
        year = "";
      if (this.preFillDetails.dateofBirth) {
        let dob = moment(this.preFillDetails.dateofBirth);
        day = dob.format("DD") + "";
        month = dob.format("MM") + "";
        year = dob.format("YYYY") + "";
      }
      let name = this.preFillDetails.lastName
        ? this.preFillDetails.lastName + " "
        : "";
      name += this.preFillDetails.firstName
        ? this.preFillDetails.firstName + " "
        : "";
      name += this.preFillDetails.middleName
        ? this.preFillDetails.middleName + " "
        : "";
      let empTaxId = [];
      if (this.preFillDetails && this.preFillDetails.employerIdentificationNo) {
        empTaxId = this.preFillDetails.employerIdentificationNo.split("");
      }
      let pdfArray = [
        {
          x: 133,
          y: 405,
          isImage: false,
          value: this.preFillDetails.assessmentYear + "",
          page: 1,
        },
        {
          x: 133,
          y: 373,
          isImage: false,
          value: this.preFillDetails.taxPayerIdentificationNo + "",
          page: 1,
        },
        {
          x: 52,
          y: 348,
          isImage: false,
          value: name,
          page: 1,
        },
        {
          x: 52,
          y: 241,
          isImage: false,
          value: month ? month?.split("")[0] : "",
          page: 1,
        },
        {
          x: 64,
          y: 241,
          isImage: false,
          value: month ? month?.split("")[1] : "",
          page: 1,
        },
        {
          x: 77,
          y: 241,
          isImage: false,
          value: day ? day?.split("")[0] : "",
          page: 1,
        },
        {
          x: 90,
          y: 241,
          isImage: false,
          value: day ? day?.split("")[1] : "",
          page: 1,
        },
        {
          x: 103,
          y: 241,
          isImage: false,
          value: year ? year?.split("")[0] : "",
          page: 1,
        },
        {
          x: 116,
          y: 241,
          isImage: false,
          value: year ? year?.split("")[1] : "",
          page: 1,
        },
        {
          x: 131,
          y: 241,
          isImage: false,
          value: year ? year?.split("")[2] : "",
          page: 1,
        },
        {
          x: 145,
          y: 241,
          isImage: false,
          value: year ? year?.split("")[3] : "",
          page: 1,
        },
        {
          x: 208,
          y: 241,
          isImage: false,
          value:
            this.preFillDetails.mobileCountryCode +
            this.preFillDetails.mobileNo +
            "",
          page: 1,
        },
        {
          x: 133,
          y: 60,
          isImage: false,
          value: empTaxId[0] ? empTaxId[0] : "",
          page: 1,
        },
        {
          x: 145,
          y: 60,
          isImage: false,
          value: empTaxId[1] ? empTaxId[1] : "",
          page: 1,
        },
        {
          x: 155,
          y: 60,
          isImage: false,
          value: empTaxId[2] ? empTaxId[2] : "",
          page: 1,
        },
        {
          x: 180,
          y: 60,
          isImage: false,
          value: empTaxId[3] ? empTaxId[3] : "",
          page: 1,
        },
        {
          x: 190,
          y: 60,
          isImage: false,
          value: empTaxId[4] ? empTaxId[4] : "",
          page: 1,
        },
        {
          x: 200,
          y: 60,
          isImage: false,
          value: empTaxId[5] ? empTaxId[5] : "",
          page: 1,
        },
        {
          x: 225,
          y: 60,
          isImage: false,
          value: empTaxId[6] ? empTaxId[6] : "",
          page: 1,
        },
        {
          x: 235,
          y: 60,
          isImage: false,
          value: empTaxId[7] ? empTaxId[7] : "",
          page: 1,
        },
        {
          x: 245,
          y: 60,
          isImage: false,
          value: empTaxId[8] ? empTaxId[8] : "",
          page: 1,
        },
        {
          x: 270,
          y: 60,
          isImage: false,
          value: empTaxId[9] ? empTaxId[9] : "",
          page: 1,
        },
        {
          x: 280,
          y: 60,
          isImage: false,
          value: empTaxId[10] ? empTaxId[10] : "",
          page: 1,
        },
        {
          x: 290,
          y: 60,
          isImage: false,
          value: empTaxId[11] ? empTaxId[11] : "",
          page: 1,
        },
        {
          x: 300,
          y: 60,
          isImage: false,
          value: empTaxId[12] ? empTaxId[12] : "",
          page: 1,
        },
        {
          x: 52,
          y: 35,
          isImage: false,
          value: this.preFillDetails.employerName + "",
          page: 1,
        },

        {
          x: 265,
          y: 8,
          isImage: false,
          value: this.preFillDetails.maillingAddress.pincode + "",
          page: 1,
        },
        {
          x: 186,
          y: -188,
          isImage: false,
          value: this.preFillDetails.totalExemptions + "",
          page: 1,
        },
        {
          x: 186,
          y: -203,
          isImage: false,
          value: this.preFillDetails.premiumPaidHealth + "",
          page: 1,
        },
        {
          x: 186,
          y: -238,
          isImage: false,
          value: this.preFillDetails.taxDue + "",
          page: 1,
        },
        {
          x: 186,
          y: -273,
          isImage: false,
          value: this.preFillDetails.bPreviousEmployer + "",
          page: 1,
        },
        {
          x: 445,
          y: 136,
          isImage: false,
          value: this.preFillDetails.salariesAndCompensation + "",
          page: 1,
        },
        {
          x: 445,
          y: 315,
          isImage: false,
          value: this.preFillDetails.holidayPayMWE + "",
          page: 1,
        },
        {
          x: 445,
          y: 292,
          isImage: false,
          value: this.preFillDetails.overtimePayMWE + "",
          page: 1,
        },
        {
          x: 445,
          y: 250,
          isImage: false,
          value: this.preFillDetails.hazardPayMWE + "",
          page: 1,
        },
        {
          x: 445,
          y: 228,
          isImage: false,
          value: this.preFillDetails.monthPay + "",
          page: 1,
        },
        {
          x: 445,
          y: 203,
          isImage: false,
          value: this.preFillDetails.deMinimisBenefits + "",
          page: 1,
        },
        {
          x: 445,
          y: 55,
          isImage: false,
          value: this.preFillDetails.basicSalary + "",
          page: 1,
        },
        {
          x: 445,
          y: 33,
          isImage: false,
          value: this.preFillDetails.representation + "",
          page: 1,
        },
        {
          x: 445,
          y: 11,
          isImage: false,
          value: this.preFillDetails.transportaion + "",
          page: 1,
        },
        {
          x: 445,
          y: -8,
          isImage: false,
          value: this.preFillDetails.costLivingAllowance + "",
          page: 1,
        },
        {
          x: 445,
          y: -28,
          isImage: false,
          value: this.preFillDetails.fixedHousingAllowance + "",
          page: 1,
        },
        {
          x: 445,
          y: -102,
          isImage: false,
          value: this.preFillDetails.commission + "",
          page: 1,
        },
        {
          x: 445,
          y: -125,
          isImage: false,
          value: this.preFillDetails.profitSharing + "",
          page: 1,
        },
        {
          x: 445,
          y: -150,
          isImage: false,
          value: this.preFillDetails.feesIncludingDirector + "",
          page: 1,
        },
        {
          x: 445,
          y: -172,
          isImage: false,
          value: this.preFillDetails.taxableMonthPay + "",
          page: 1,
        },
        {
          x: 445,
          y: -195,
          isImage: false,
          value: this.preFillDetails.hazardPay + "",
          page: 1,
        },
        {
          x: 445,
          y: -220,
          isImage: false,
          value: this.preFillDetails.overtimePay + "",
          page: 1,
        },
        {
          x: 186,
          y: -125,
          isImage: false,
          value: this.preFillDetails.totalNonTaxable
            ? this.preFillDetails.totalNonTaxable + ""
            : 0,
          page: 1,
        },
        {
          x: 186,
          y: -110,
          isImage: false,
          value: this.preFillDetails.presentEmployerGrossIncome
            ? this.preFillDetails.presentEmployerGrossIncome + ""
            : 0,
          page: 1,
        },
        {
          x: 186,
          y: -140,
          isImage: false,
          value: this.preFillDetails.presentEmployerTaxableIncome
            ? this.preFillDetails.presentEmployerTaxableIncome + ""
            : 0,
          page: 1,
        },
        {
          x: 186,
          y: -155,
          isImage: false,
          value: this.preFillDetails.previousEmployerTaxableIncome
            ? this.preFillDetails.previousEmployerTaxableIncome + ""
            : 0,
          page: 1,
        },
        {
          x: 186,
          y: -172,
          isImage: false,
          value: this.preFillDetails.otherBenefitsGrossTaxableIncome
            ? this.preFillDetails.otherBenefitsGrossTaxableIncome + ""
            : 0,
          page: 1,
        },
      ];
      if (
        this.preFillDetails.permenantAddress &&
        this.preFillDetails.permenantAddress.length > 0
      ) {
        pdfArray.push(
          {
            x: 52,
            y: 320,
            isImage: false,
            value:
              this.preFillDetails.permenantAddress.apartmentName +
              // this.preFillDetails.permenantAddress.streetName +
              // this.preFillDetails.permenantAddress.city +
              // this.preFillDetails.permenantAddress.state +
              this.preFillDetails.permenantAddress.country +
              "",
            page: 1,
          },
          {
            x: 265,
            y: 320,
            isImage: false,
            value: this.preFillDetails.permenantAddress.pincode + "",
            page: 1,
          }
        );
      }
      if (
        this.preFillDetails.maillingAddress &&
        this.preFillDetails.maillingAddress.length > 0
      ) {
        pdfArray.push(
          {
            x: 52,
            y: 8,
            isImage: false,
            value:
              this.preFillDetails.maillingAddress.street1 +
              this.preFillDetails.maillingAddress.street2 +
              this.preFillDetails.maillingAddress.cityName +
              this.preFillDetails.maillingAddress.stateName +
              this.preFillDetails.maillingAddress.countryName +
              "",
            page: 1,
          },
          {
            x: 265,
            y: 8,
            isImage: false,
            value: this.preFillDetails.maillingAddress.pincode + "",
            page: 1,
          }
        );
      }
      this.fillPdf(pdfArray);
    },
    formR5ContributionReport() {
      let pdfArray = [
        {
          x: 218,
          y: 239,
          isImage: false,
          value: this.preFillDetails.employerName,
          page: 1,
        },
        {
          x: 412,
          y: 185,
          isImage: false,
          value: this.preFillDetails.employerTinNo
            ? this.preFillDetails.employerTinNo
            : "",
          page: 1,
        },
        {
          x: 140,
          y: -44,
          isImage: false,
          value: this.preFillDetails.orgShareAmountTotal
            ? this.preFillDetails.orgShareAmountTotal
            : "",
          page: 1,
        },
        {
          x: 230,
          y: -44,
          isImage: false,
          value: this.preFillDetails.employerShareTotal
            ? this.preFillDetails.employerShareTotal
            : "",
          page: 1,
        },
      ];
      if (this.preFillDetails.maillingAddress) {
        pdfArray.push(
          {
            x: 25,
            y: 213,
            isImage: false,
            value:
              this.preFillDetails.maillingAddress.street1 +
              "," +
              this.preFillDetails.maillingAddress.street2,
            page: 1,
          },
          {
            x: 25,
            y: 185,
            isImage: false,
            value:
              this.preFillDetails.maillingAddress.cityName +
              "," +
              this.preFillDetails.maillingAddress.stateName
                ? this.preFillDetails.maillingAddress.stateName
                : "",
            page: 1,
          }
        );
      }
      if (
        this.preFillDetails.maillingAddress &&
        this.preFillDetails.maillingAddress.pincode
      ) {
        let pincode = this.preFillDetails.maillingAddress.pincode.split("");
        pincode.map((ele, index) => {
          pdfArray.push({
            x: 354 + index * 15,
            y: 185,
            isImage: false,
            value: ele,
            page: 1,
          });
        });
      }
      if (
        this.preFillDetails.maillingAddress &&
        this.preFillDetails.maillingAddress.phone
      ) {
        let phoneNumber = this.preFillDetails.maillingAddress.phone.split("");
        phoneNumber.map((ele, index) => {
          pdfArray.push({
            x: 173 + index * 15,
            y: 159,
            isImage: false,
            value: ele,
            page: 1,
          });
        });
      }
      if (
        this.preFillDetails.paymentDetails &&
        this.preFillDetails.paymentDetails.length > 0
      ) {
        this.preFillDetails.paymentDetails.slice(0, 10).forEach((item) => {
          const YAxis = 125 - item?.salaryMonth?.split(",")[0] * 13;
          pdfArray.push(
            {
              x: 89,
              y: YAxis,
              isImage: false,
              value: item.salaryMonth.split(",")[1],
              page: 1,
            },
            {
              x: 139,
              y: YAxis,
              isImage: false,
              value: item.orgShareAmount,
              page: 1,
            },
            {
              x: 230,
              y: YAxis,
              isImage: false,
              value: item.empShareAmount,
              page: 1,
            },
            {
              x: 330,
              y: YAxis,
              isImage: false,
              value: item.orgShareAmount + item.empShareAmount,
              page: 1,
            }
          );
        });
      }

      this.fillPdf(pdfArray);
    },
    formBir1604() {
      let pdfArray = [
        {
          x: 240,
          y: 297,
          isImage: false,
          value: this.preFillDetails.identificationNumber
            ? this.preFillDetails.identificationNumber
            : "",
          page: 1,
        },
        {
          x: this.preFillDetails.amendedReturn == "Yes" ? 310 : 360,
          y: 336,
          isImage: true,
          value: this.preFillDetails.amendedReturn
            ? this.preFillDetails.amendedReturn
            : "",
          page: 1,
        },
        {
          x: 560,
          y: 336,
          isImage: false,
          value: this.preFillDetails.numberOfSheetAttach,
          page: 1,
        },
        {
          x: this.preFillDetails.withholdingAgent == "Yes" ? 496 : 545,
          y: 197,
          isImage: true,
          value: this.preFillDetails.withholdingAgent,
          page: 1,
        },
        {
          x: this.preFillDetails.yearEndAdjustment == "Yes" ? 218 : 264,
          y: 140,
          isImage: true,
          value: this.preFillDetails.yearEndAdjustment,
          page: 1,
        },
      ];
      if (this.preFillDetails.maillingAddress) {
        pdfArray.push(
          {
            x: 28,
            y: 233,
            isImage: false,
            value:
              this.preFillDetails.maillingAddress.street1 +
              "," +
              this.preFillDetails.maillingAddress.street2 +
              "," +
              this.preFillDetails.maillingAddress.cityName,
            page: 1,
          },
          {
            x: 28,
            y: 213,
            isImage: false,
            value:
              this.preFillDetails.maillingAddress.stateName +
              "," +
              this.preFillDetails.maillingAddress.countryName,
            page: 1,
          }
        );
      }
      if (this.preFillDetails?.agentName) {
        let name = this.preFillDetails?.agentName?.split("");
        name?.map((ele, index) => {
          pdfArray.push({
            x: 28 + index * 16,
            y: 265,
            isImage: false,
            value: ele,
            page: 1,
          });
        });
      }
      if (
        this.preFillDetails.maillingAddress &&
        this.preFillDetails.maillingAddress?.phone
      ) {
        let phoneNumber =
          this.preFillDetails?.maillingAddress?.phone?.split("");
        phoneNumber?.map((ele, index) => {
          pdfArray.push({
            x: 26 + index * 15,
            y: 163,
            isImage: false,
            value: ele,
            page: 1,
          });
        });
      }
      if (
        this.preFillDetails.paymentDetails &&
        this.preFillDetails.paymentDetails.length > 0
      ) {
        this.preFillDetails.paymentDetails
          .slice(0, 10)
          .forEach((item, index) => {
            // const YAxis = item.paymentDate
            //   ? 125 - parseInt(item.paymentDate.split("-")[1]) * 14
            //   : 70 - index * 13;   // if paymentDate is available we can use this method
            const YAxis = 70 - index * 13;
            pdfArray.push(
              {
                x: 129,
                y: YAxis,
                isImage: false,
                value: item?.paymentDate
                  ? moment(item?.paymentDate).format("MM/DD/YYYY")
                  : "",
                page: 1,
              },
              {
                x: 240,
                y: YAxis,
                isImage: false,
                value: item.bankName ? item.bankName : "",
                page: 1,
              },
              {
                x: 390,
                y: YAxis,
                isImage: false,
                value: item.documentNo ? item.documentNo : "",
                page: 1,
              },
              {
                x: 500,
                y: YAxis,
                isImage: false,
                value: item.totalAmount ? item.totalAmount : "",
                page: 1,
              }
            );
          });
      }
      this.fillPdf(pdfArray);
    },
    formWhtPnd1() {
      let month_x, month_y;
      if (this.selectedMonthYear) {
        let month = moment(this.selectedMonthYear).format("MMMM");
        const monthMapping = {
          January: { x: 300, y: 190 },
          February: { x: 300, y: 173 },
          March: { x: 300, y: 156 },
          April: { x: 360, y: 190 },
          May: { x: 360, y: 173 },
          June: { x: 360, y: 156 },
          July: { x: 410, y: 190 },
          August: { x: 410, y: 173 },
          September: { x: 410, y: 156 },
          October: { x: 475, y: 190 },
          November: { x: 475, y: 173 },
          December: { x: 475, y: 156 },
        };
        month_x = monthMapping[month].x;
        month_y = monthMapping[month].y;
      }
      const details = this.preFillDetails?.data || {};
      const fields = [
        "generalSalariesAndWages",
        "approvedSalariesWithholdingTax",
        "terminationPaymentIncome",
        "residentIncomeSection40_2",
        "nonResidentIncomeSection40_2",
      ];
      let total_person = fields.reduce((total, field) => {
        return (
          total + (details?.summaryHoldingTaxDetails[field]?.employeeCount || 0)
        );
      }, 0);
      let total_income = fields.reduce((total, field) => {
        return (
          total +
          (details?.summaryHoldingTaxDetails[field]?.totalAmountOfIncome || 0)
        );
      }, 0);
      let total_tax = fields.reduce((total, field) => {
        return (
          total +
          (details?.summaryHoldingTaxDetails[field]?.amountWithHoldingTax || 0)
        );
      }, 0);
      let pdfArray = [
        {
          x: 290,
          y: 283,
          isImage: true, //ordinary filling
          value: "",
          page: 1,
        },
        {
          x: 28,
          y: 210,
          isImage: false,
          value: details?.maillingAddress?.taxAgent
            ? details.maillingAddress.taxAgent
            : "",
          page: 1,
        },
        {
          x: 59,
          y: 158,
          isImage: false,
          value: details?.maillingAddress?.street1
            ? details.maillingAddress.street1
            : "",
          page: 1,
        },
        {
          x: 185,
          y: 158,
          isImage: false,
          value: details?.maillingAddress?.street2
            ? details.maillingAddress.street2
            : "",
          page: 1,
        },
        {
          x: 190,
          y: 141,
          isImage: false,
          value: details?.maillingAddress?.cityName
            ? details.maillingAddress.cityName
            : "",
          page: 1,
        },
        {
          x: 55,
          y: 125,
          isImage: false,
          value: details?.maillingAddress?.stateName
            ? details.maillingAddress.stateName
            : "",
          page: 1,
        },
        {
          x: 40,
          y: 110,
          isImage: false,
          value: details?.maillingAddress?.mobileNo
            ? details.maillingAddress.mobileNo
            : "",
          page: 1,
        },
        {
          x: 517,
          y: 227,
          isImage: false,
          value: this.selectedMonthYear
            ? moment(this.selectedMonthYear).add(543, "year").format("YYYY")
            : "",
          page: 1,
        },
        {
          x: month_x,
          y: month_y,
          isImage: true,
          value: "",
          width: 10,
          height: 10,
          page: 1,
        },
        {
          x: 58,
          y: 54,
          isImage: false,
          value: details?.taxDetails?.paymentDate
            ? details.taxDetails.paymentDate
            : "",
          page: 1,
        },
        {
          x: 202,
          y: 54,
          isImage: false,
          value: details?.taxDetails?.Receipt_No
            ? details.taxDetails.Receipt_No
            : "",
          page: 1,
        },
        {
          x: 340,
          y: 54,
          isImage: false,
          value:
            details?.taxDetails?.totalAmountOfIncome >= 0
              ? details.taxDetails.totalAmountOfIncome
              : "",
          page: 1,
        },
        {
          x: 365,
          y: 38,
          isImage: false,
          value: details?.taxDetails?.totalAmountOfIncomeInWords
            ? details.taxDetails.totalAmountOfIncomeInWords
            : "",
          page: 1,
        },
        {
          x: 323,
          y: -120,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.generalSalariesAndWages
              ?.employeeCount >= 0
              ? details.summaryHoldingTaxDetails.generalSalariesAndWages
                  .employeeCount
              : "",
          page: 1,
        },
        {
          x: 376,
          y: -120,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.generalSalariesAndWages
              ?.totalAmountOfIncome >= 0
              ? details.summaryHoldingTaxDetails.generalSalariesAndWages
                  .totalAmountOfIncome
              : "",
          page: 1,
        },
        {
          x: 460,
          y: -120,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.generalSalariesAndWages
              ?.amountWithHoldingTax >= 0
              ? details.summaryHoldingTaxDetails.generalSalariesAndWages
                  .amountWithHoldingTax
              : "",
          page: 1,
        },
        {
          x: 323,
          y: -138,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.approvedSalariesWithholdingTax
              ?.employeeCount >= 0
              ? details.summaryHoldingTaxDetails.approvedSalariesWithholdingTax
                  .employeeCount
              : "",
          page: 1,
        },
        {
          x: 376,
          y: -138,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.approvedSalariesWithholdingTax
              ?.totalAmountOfIncome >= 0
              ? details.summaryHoldingTaxDetails.approvedSalariesWithholdingTax
                  .totalAmountOfIncome
              : "",
          page: 1,
        },
        {
          x: 460,
          y: -138,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.approvedSalariesWithholdingTax
              ?.amountWithHoldingTax >= 0
              ? details.summaryHoldingTaxDetails.approvedSalariesWithholdingTax
                  .amountWithHoldingTax
              : "",
          page: 1,
        },
        {
          x: 323,
          y: -190,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.terminationPaymentIncome
              ?.employeeCount >= 0
              ? details.summaryHoldingTaxDetails.terminationPaymentIncome
                  .employeeCount
              : "",
          page: 1,
        },
        {
          x: 376,
          y: -190,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.terminationPaymentIncome
              ?.totalAmountOfIncome >= 0
              ? details.summaryHoldingTaxDetails.terminationPaymentIncome
                  .totalAmountOfIncome
              : "",
          page: 1,
        },
        {
          x: 460,
          y: -190,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.terminationPaymentIncome
              ?.amountWithHoldingTax >= 0
              ? details.summaryHoldingTaxDetails.terminationPaymentIncome
                  .amountWithHoldingTax
              : "",
          page: 1,
        },
        {
          x: 323,
          y: -225,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.residentIncomeSection40_2
              ?.employeeCount >= 0
              ? details.summaryHoldingTaxDetails.residentIncomeSection40_2
                  .employeeCount
              : "",
          page: 1,
        },
        {
          x: 376,
          y: -225,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.residentIncomeSection40_2
              ?.totalAmountOfIncome >= 0
              ? details.summaryHoldingTaxDetails.residentIncomeSection40_2
                  .totalAmountOfIncome
              : "",
          page: 1,
        },
        {
          x: 460,
          y: -225,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.residentIncomeSection40_2
              ?.amountWithHoldingTax >= 0
              ? details.summaryHoldingTaxDetails.residentIncomeSection40_2
                  .amountWithHoldingTax
              : "",
          page: 1,
        },
        {
          x: 323,
          y: -240,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.residentIncomeSection40_2
              ?.employeeCount >= 0
              ? details.summaryHoldingTaxDetails.residentIncomeSection40_2
                  .employeeCount
              : "",
          page: 1,
        },
        {
          x: 376,
          y: -240,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.residentIncomeSection40_2
              ?.totalAmountOfIncome >= 0
              ? details.summaryHoldingTaxDetails.residentIncomeSection40_2
                  .totalAmountOfIncome
              : "",
          page: 1,
        },
        {
          x: 460,
          y: -240,
          isImage: false,
          value:
            details?.summaryHoldingTaxDetails?.nonResidentIncomeSection40_2
              ?.amountWithHoldingTax >= 0
              ? details.summaryHoldingTaxDetails.nonResidentIncomeSection40_2
                  .amountWithHoldingTax
              : "",
          page: 1,
        },
        {
          x: 323,
          y: -258,
          isImage: false,
          value: total_person,
          page: 1,
        },
        {
          x: 376,
          y: -258,
          isImage: false,
          value: total_income,
          page: 1,
        },
        {
          x: 460,
          y: -258,
          isImage: false,
          value: total_tax,
          page: 1,
        },
        {
          x: 460,
          y: -293,
          isImage: false,
          value: total_tax,
          page: 1,
        },
        {
          x: 250,
          y: -310,
          isImage: false,
          value: total_tax >= 0 ? numberToWords.toWords(total_tax) : "",
          page: 1,
        },
        {
          x: 215,
          y: -365,
          isImage: false,
          value: moment().format("DD"),
          page: 1,
        },
        {
          x: 273,
          y: -365,
          isImage: false,
          value: moment().format("MM"),
          page: 1,
        },
        {
          x: 370,
          y: -365,
          isImage: false,
          value: moment().add(543, "year").format("YYYY"),
          page: 1,
        },
      ];
      if (details?.maillingAddress?.tan?.length) {
        pdfArray.push(
          {
            x: 148,
            y: 259,
            isImage: false,
            value: details.maillingAddress.tan[0],
            page: 1,
            size: 8,
          },
          {
            x: 161,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[1],
            page: 1,
            size: 8,
          },
          {
            x: 168,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[2],
            page: 1,
            size: 8,
          },
          {
            x: 175,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[3],
            page: 1,
            size: 8,
          },
          {
            x: 182,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[4],
            page: 1,
            size: 8,
          },
          {
            x: 195,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[5],
            page: 1,
            size: 8,
          },
          {
            x: 202,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[6],
            page: 1,
            size: 8,
          },
          {
            x: 209,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[7],
            page: 1,
            size: 8,
          },
          {
            x: 216,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[8],
            page: 1,
            size: 8,
          },
          {
            x: 229,
            y: 258,
            isImage: false,
            value: details.maillingAddress.tan[9],
            page: 1,
            size: 8,
          }
        );
      }
      if (details?.maillingAddress?.pincode?.length) {
        pdfArray.push(
          {
            x: 208,
            y: 124,
            isImage: false,
            value: details.maillingAddress.pincode[0],
            page: 1,
            size: 8,
          },
          {
            x: 215,
            y: 124,
            isImage: false,
            value: details.maillingAddress.pincode[1]
              ? details.maillingAddress.pincode[1]
              : "",
            page: 1,
            size: 8,
          },
          {
            x: 223,
            y: 124,
            isImage: false,
            value: details.maillingAddress.pincode[2]
              ? details.maillingAddress.pincode[2]
              : "",
            page: 1,
            size: 8,
          },
          {
            x: 231,
            y: 124,
            isImage: false,
            value: details.maillingAddress.pincode[3]
              ? details.maillingAddress.pincode[3]
              : "",
            page: 1,
            size: 8,
          },
          {
            x: 239,
            y: 124,
            isImage: false,
            value: details.maillingAddress.pincode[4]
              ? details.maillingAddress.pincode[4]
              : "",
            page: 1,
            size: 8,
          },
          {
            x: 247,
            y: 124,
            isImage: false,
            value: details.maillingAddress.pincode[5]
              ? details.maillingAddress.pincode[5]
              : "",
            page: 1,
            size: 8,
          }
        );
      }
      this.fillPdf(pdfArray);
    },
    formWithHoldingTaxCertificate() {
      let tax_payer_address = [
        this.preFillDetails?.employeeDetails?.address?.apartmentName,
        this.preFillDetails?.employeeDetails?.address?.streetName,
        this.preFillDetails?.employeeDetails?.address?.city,
        this.preFillDetails?.employeeDetails?.address?.state,
        this.preFillDetails?.employeeDetails?.address?.country,
        this.preFillDetails?.employeeDetails?.address?.pincode,
      ]
        .filter((item) => item)
        .join(", ");
      let tax_agent_address = [
        this.preFillDetails?.mailingCompanyAddress?.street1,
        this.preFillDetails?.mailingCompanyAddress?.street2,
        this.preFillDetails?.mailingCompanyAddress?.cityName,
        this.preFillDetails?.mailingCompanyAddress?.stateName,
        this.preFillDetails?.mailingCompanyAddress?.countryName,
        this.preFillDetails?.mailingCompanyAddress?.pincode,
      ]
        .filter((item) => item)
        .join(", ");
      let details = this.preFillDetails?.taxWithHoldingDetails || {};
      let fields = [
        "salaryWagePensionSection40_1",
        "commissionsSection40_2",
        "royaltiesSection40_3",
        "interestSection40_4_a",
        "incomeSubjectToWithholdingSection3Tredecim",
        "others",
      ];
      let total_amount_paid = fields.reduce((total, field) => {
        return total + (details[field]?.amountPaid || 0);
      }, 0);
      let total_tax_withheld = fields.reduce((total, field) => {
        return total + (details[field]?.taxWithHeld || 0);
      }, 0);
      let pdfArray = [
        {
          x: 103,
          y: 330,
          isImage: false,
          value: this.preFillDetails?.mailingCompanyAddress?.taxAgent,
          page: 1,
          size: 10,
        },
        {
          x: 113,
          y: 313,
          isImage: false,
          value: tax_agent_address,
          page: 1,
          size: 9,
        },
        {
          x: 103,
          y: 285,
          isImage: false,
          value: this.preFillDetails?.employeeDetails?.name,
          page: 1,
          size: 9,
        },
        {
          x: 113,
          y: 267,
          isImage: false,
          value: tax_payer_address,
          page: 1,
          size: 9,
        },
        // {
        //   x: 132,
        //   y: 246,
        //   isImage: false,
        //   value: "1234",
        //   page: 1,
        //   size: 9,
        // },
        {
          x: 354,
          y: 207,
          isImage: false,
          value: "",
          page: 1,
          size: 9,
        },
        {
          x: 404,
          y: 207,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails
              ?.salaryWagePensionSection40_1?.amountPaid >= 0
              ? this.preFillDetails?.taxWithHoldingDetails
                  .salaryWagePensionSection40_1?.amountPaid
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 466,
          y: 207,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails
              ?.salaryWagePensionSection40_1?.taxWithHeld >= 0
              ? this.preFillDetails?.taxWithHoldingDetails
                  .salaryWagePensionSection40_1?.taxWithHeld
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 404,
          y: 194,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails?.commissionsSection40_2
              ?.amountPaid >= 0
              ? this.preFillDetails?.taxWithHoldingDetails
                  .commissionsSection40_2?.amountPaid
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 466,
          y: 194,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails?.commissionsSection40_2
              ?.taxWithHeld >= 0
              ? this.preFillDetails?.taxWithHoldingDetails
                  .commissionsSection40_2?.taxWithHeld
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 404,
          y: 181,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails?.royaltiesSection40_3
              ?.amountPaid >= 0
              ? this.preFillDetails?.taxWithHoldingDetails.royaltiesSection40_3
                  ?.amountPaid
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 466,
          y: 181,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails?.royaltiesSection40_3
              ?.taxWithHeld >= 0
              ? this.preFillDetails?.taxWithHoldingDetails.royaltiesSection40_3
                  ?.taxWithHeld
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 404,
          y: 168,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails?.interestSection40_4_a
              ?.amountPaid >= 0
              ? this.preFillDetails?.taxWithHoldingDetails.interestSection40_4_a
                  ?.amountPaid
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 466,
          y: 168,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails?.interestSection40_4_a
              ?.taxWithHeld >= 0
              ? this.preFillDetails?.taxWithHoldingDetails.interestSection40_4_a
                  ?.taxWithHeld
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 404,
          y: -110,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails
              ?.incomeSubjectToWithholdingSection3Tredecim?.amountPaid >= 0
              ? this.preFillDetails?.taxWithHoldingDetails
                  .incomeSubjectToWithholdingSection3Tredecim?.amountPaid
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 466,
          y: -110,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails
              ?.incomeSubjectToWithholdingSection3Tredecim?.taxWithHeld >= 0
              ? this.preFillDetails?.taxWithHoldingDetails
                  .incomeSubjectToWithholdingSection3Tredecim?.taxWithHeld
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 404,
          y: -202,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails?.others?.amountPaid >= 0
              ? this.preFillDetails?.taxWithHoldingDetails.others?.amountPaid
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 466,
          y: -202,
          isImage: false,
          value:
            this.preFillDetails?.taxWithHoldingDetails?.others?.taxWithHeld >= 0
              ? this.preFillDetails?.taxWithHoldingDetails.others?.taxWithHeld
              : "",
          page: 1,
          size: 9,
        },
        {
          x: 404,
          y: -235,
          isImage: false,
          value: total_amount_paid,
          page: 1,
          size: 9,
        },
        {
          x: 466,
          y: -235,
          isImage: false,
          value: total_tax_withheld,
          page: 1,
          size: 9,
        },
        {
          x: 200,
          y: -253,
          isImage: false,
          value: numberToWords.toWords(total_tax_withheld),
          page: 1,
          size: 9,
        },
        {
          x: 355,
          y: -370,
          isImage: false,
          value: moment().format("DD"),
          page: 1,
          size: 9,
        },
        {
          x: 385,
          y: -370,
          isImage: false,
          value: moment().format("MM"),
          page: 1,
          size: 9,
        },
        {
          x: 420,
          y: -370,
          isImage: false,
          value: moment().format("YYYY"),
          page: 1,
          size: 9,
        },
      ];
      if (
        this.preFillDetails?.employeeDetails?.personalIdentificationNo?.length
      ) {
        pdfArray.push(
          {
            x: 340,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[0],
            page: 1,
            size: 9,
          },
          {
            x: 358,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[1] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[1]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 368,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[2] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[2]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 378,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[3] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[3]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 388,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[4] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[4]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 406,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[5] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[5]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 416,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[6] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[6]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 424,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[7] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[7]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 434,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[8] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[8]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 444,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.personalIdentificationNo[9] >=
              0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[9]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 462,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails
                .personalIdentificationNo[10] >= 0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[10]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 470,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails
                .personalIdentificationNo[11] >= 0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[11]
                : "",
            page: 1,
            size: 9,
          },
          {
            x: 489,
            y: 297,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails
                .personalIdentificationNo[12] >= 0
                ? this.preFillDetails.employeeDetails
                    .personalIdentificationNo[12]
                : "",
            page: 1,
            size: 9,
          }
        );
      }
      if (this.preFillDetails?.employeeDetails?.tan?.length) {
        pdfArray.push(
          {
            x: 380,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[0] >= 0
                ? this.preFillDetails.employeeDetails.tan[0]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 400, //7
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[1] >= 0
                ? this.preFillDetails.employeeDetails.tan[1]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 409,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[2] >= 0
                ? this.preFillDetails.employeeDetails.tan[2]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 418,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[3] >= 0
                ? this.preFillDetails.employeeDetails.tan[3]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 429,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[4] >= 0
                ? this.preFillDetails.employeeDetails.tan[4]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 449,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[5] >= 0
                ? this.preFillDetails.employeeDetails.tan[5]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 459,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[6] >= 0
                ? this.preFillDetails.employeeDetails.tan[6]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 469,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[7] >= 0
                ? this.preFillDetails.employeeDetails.tan[7]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 479,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[8] >= 0
                ? this.preFillDetails.employeeDetails.tan[8]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 499,
            y: 279,
            isImage: false,
            value:
              this.preFillDetails.employeeDetails.tan[9] >= 0
                ? this.preFillDetails.employeeDetails.tan[9]
                : "",
            page: 1,
            size: 8,
          }
        );
      }
      if (this.preFillDetails?.mailingCompanyAddress?.tan?.length) {
        pdfArray.push(
          {
            x: 380,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[0] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[0]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 400, //7
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[1] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[1]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 409,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[2] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[2]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 418,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[3] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[3]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 429,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[4] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[4]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 449,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[5] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[5]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 459,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[6] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[6]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 469,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[7] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[7]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 479,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[8] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[8]
                : "",
            page: 1,
            size: 8,
          },
          {
            x: 499,
            y: 326,
            isImage: false,
            value:
              this.preFillDetails.mailingCompanyAddress.tan[9] >= 0
                ? this.preFillDetails.mailingCompanyAddress.tan[9]
                : "",
            page: 1,
            size: 8,
          }
        );
      }
      this.fillPdf(pdfArray);
    },
    formPND1Monthly() {
      let reportData = this.preFillDetails.data;
      let pdfArray = [
        {
          x: 375,
          y: 242,
          isImage: false,
          value: reportData?.personalIdentificationNo || "",
          page: 1,
        },
        {
          x: 643,
          y: 242,
          isImage: false,
          value: reportData?.taxpayerIdentificationNo || "",
          page: 1,
        },
        {
          x: 690,
          y: 196,
          isImage: false,
          value: reportData?.branchNo || "",
          page: 1,
        },
        {
          x: 620,
          y: -140,
          isImage: false,
          value: reportData?.totalAmountOfTaxWithHeld
            ? reportData.totalAmountOfTaxWithHeld.toFixed(2)
            : "0", // Total tax withheld
          page: 1,
        },
        {
          x: 523,
          y: -140,
          isImage: false,
          value: reportData?.totalPaidAmount
            ? reportData.totalPaidAmount.toFixed(2)
            : "0",
          page: 1,
        },
        {
          x: 540,
          y: -176,
          isImage: false,
          value: reportData?.payerResponsibleName || "",
          page: 1,
        },
        {
          x: 550,
          y: -206,
          isImage: false,
          value: reportData?.position || "",
          page: 1,
        },
        {
          x: 570,
          y: -223,
          isImage: false,
          value:
            reportData?.fillingDate && moment(reportData.fillingDate).isValid()
              ? moment(reportData.fillingDate).format("DD")
              : "",
          page: 1,
        },
        {
          x: 615,
          y: -223,
          isImage: false,
          value:
            reportData?.fillingDate && moment(reportData.fillingDate).isValid()
              ? moment(reportData.fillingDate).format("MM")
              : "",
          page: 1,
        },
        {
          x: 678,
          y: -223,
          isImage: false,
          value:
            reportData?.fillingDate && moment(reportData.fillingDate).isValid()
              ? moment(reportData.fillingDate).add(543, "year").format("YYYY")
              : "",
          page: 1,
        },
      ];
      if (reportData?.typeofIncome1?.toLowerCase() === "yes") {
        pdfArray.push({
          x: 68,
          y: 183,
          isImage: true,
          width: 9,
          height: 9,
          value: "",
          page: 1,
        });
      }
      if (reportData?.typeofIncome2?.toLowerCase() === "yes") {
        pdfArray.push({
          x: 68,
          y: 169,
          isImage: true,
          width: 9,
          height: 9,
          value: "", // Type of Income 2
          page: 1,
        });
      }
      if (reportData?.typeofIncome3?.toLowerCase() === "yes") {
        pdfArray.push({
          x: 335,
          y: 183,
          isImage: true,
          width: 9,
          height: 9,
          value: "", // Type of Income 3
          page: 1,
        });
      }
      if (reportData?.typeofIncome4?.toLowerCase() === "yes") {
        pdfArray.push({
          x: 335,
          y: 155,
          isImage: true,
          width: 9,
          height: 9,
          value: "", // Type of Income 4
          page: 1,
        });
      }
      if (reportData?.typeofIncome5?.toLowerCase() === "yes") {
        pdfArray.push({
          x: 335,
          y: 140,
          isImage: true,
          width: 9,
          height: 9,
          value: "", // Type of Income 5
          page: 1,
        });
      }
      if (reportData?.employeeDetails?.length > 0) {
        reportData.employeeDetails.map((item, index) => {
          pdfArray.push(
            {
              x: 75,
              y: 76 - index * 30,
              isImage: false,
              value: item.empPersonalIdentificationNo || "", // emp PIN
              size: 9,
              page: 1,
            },
            {
              x: 280,
              y: 76 - index * 30,
              isImage: false,
              value: item.empTaxIdentificationNo || "", // emp TIN
              size: 9,
              page: 1,
            },
            {
              x: 77,
              y: 65 - index * 30.5,
              isImage: false,
              value: item.employeeName || "", // emp first name
              size: 9,
              page: 1,
            },
            {
              x: 285,
              y: 65 - index * 30.5,
              isImage: false,
              value: item.surname || "", // emp last name
              size: 9,
              page: 1,
            },
            {
              x: 460,
              y: 65 - index * 30,
              isImage: false,
              value:
                reportData.paymentDate &&
                moment(reportData.paymentDate).isValid()
                  ? moment(reportData.paymentDate).format(
                      this.$store.state.orgDetails.orgDateFormat
                    )
                  : "",
              page: 1,
            },
            {
              x: 523,
              y: 65 - index * 30,
              isImage: false,
              value: item.paidAmount ? item.paidAmount.toFixed(2) : "0",
              page: 1,
            },
            {
              x: 620,
              y: 65 - index * 30,
              isImage: false,
              value: item.amountOfTaxWithHeld
                ? item.amountOfTaxWithHeld.toFixed(2)
                : "0",
              page: 1,
            },
            {
              x: 710,
              y: 65 - index * 30,
              isImage: false,
              value: item.conditions || "",
              page: 1,
            }
          );
        });
      }
      this.fillPdf(pdfArray);
    },
    // formPND91() {
    //   let taxpayerPin = "*********0123";
    //   let taxPayble = "*********";
    //   let pincode = "12345";
    //   let taxToParty = "123";
    //   let dob = moment();
    //   let maritalStatus = {
    //     single: 90,
    //     married: 143,
    //     divorced: 195,
    //     deceased: 250,
    //   };
    //   let taxPayblePoints = {
    //     0: 143,
    //     1: 151,
    //     2: 160,
    //     3: 169,
    //     4: 178,
    //     5: 187,
    //     6: 196,
    //     7: 205,
    //     8: 214,
    //     10: 226,
    //     11: 233,
    //   };
    //   let taxpayerPINPoints = {
    //     0: 129,
    //     1: 146,
    //     2: 157,
    //     3: 169,
    //     4: 180,
    //     5: 196,
    //     6: 208,
    //     7: 220,
    //     8: 231,
    //     9: 242,
    //     10: 258,
    //     11: 270,
    //     12: 287,
    //   };
    //   let taxToPartyPoints = {
    //     0: 310,
    //     1: 321,
    //     2: 331,
    //     4: 345,
    //     5: 355,
    //   };
    //   let xPoints2Page = {
    //     0: 474,
    //     1: 482,
    //     2: 491,
    //     3: 500,
    //     4: 509,
    //     5: 518,
    //     6: 527,
    //     7: 536,
    //     8: 545,
    //     10: 557,
    //     11: 564,
    //   };
    //   let page3Points = {
    //     0: 58,
    //     1: 70,
    //     2: 82,
    //     3: 94,
    //     4: 106,
    //     5: 118,
    //     6: 130,
    //     7: 141,
    //     8: 153,
    //     9: 164,
    //     10: 176,
    //     11: 188,
    //     12: 200,
    //   };
    //   let pincodePoints = { 0: 101, 1: 113, 2: 124, 3: 135, 4: 145 };
    //   let pdfArray = [
    //     {
    //       x: 80, // city
    //       y: 183,
    //       isImage: false,
    //       value: "Coimbatore",
    //       page: 1,
    //     },
    //     {
    //       x: 220, // state
    //       y: 183,
    //       isImage: false,
    //       value: "Tamil Nadu",
    //       page: 1,
    //     },
    //     {
    //       x: 180, // payer
    //       y: 265,
    //       isImage: false,
    //       value: dob.format("DD") || "",
    //       page: 1,
    //     },
    //     {
    //       x: 220, // payer
    //       y: 265,
    //       isImage: false,
    //       value: dob.format("MM") || "",
    //       page: 1,
    //     },
    //     {
    //       x: 252, // payer
    //       y: 265,
    //       isImage: false,
    //       value: dob.format("YYYY") || "",
    //       page: 1,
    //     },
    //     {
    //       x: 438, // spouse
    //       y: 265,
    //       isImage: false,
    //       value: dob.format("DD") || "",
    //       page: 1,
    //     },
    //     {
    //       x: 460, // spouse
    //       y: 265,
    //       isImage: false,
    //       value: dob.format("MM") || "",
    //       page: 1,
    //     },
    //     {
    //       x: 495, // spouse
    //       y: 265,
    //       isImage: false,
    //       value: dob.format("YYYY") || "",
    //       page: 1,
    //     },
    //     {
    //       x: 100, // payer first name
    //       y: 247,
    //       isImage: false,
    //       value: "al;adklcmd",
    //       page: 1,
    //     },
    //     {
    //       x: 230, // payer last name
    //       y: 247,
    //       isImage: false,
    //       value: "al;adklcmd",
    //       page: 1,
    //     },
    //     {
    //       x: 390, // spouse first name
    //       y: 247,
    //       isImage: false,
    //       value: "al;adklcmd",
    //       page: 1,
    //     },
    //     {
    //       x: 490, // spouse last name
    //       y: 247,
    //       isImage: false,
    //       value: "al;adklcmd",
    //       page: 1,
    //     },
    //     {
    //       x: 348, // spouse marital status 1
    //       y: 215, // 192 179 165
    //       isImage: true,
    //       value: "",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       x: 450, // spouse filing status 1
    //       y: 216, // 203 178
    //       isImage: true,
    //       value: "",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       x: 390, // spouse passport number
    //       y: 130,
    //       isImage: false,
    //       value: "12323454554",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       x: 385, // spouse nationality
    //       y: 118,
    //       isImage: false,
    //       value: "Indian",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       x: 490, // spouse country
    //       y: 118,
    //       isImage: false,
    //       value: "India",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       x: 90, // Regular Filing
    //       y: 130,
    //       isImage: true,
    //       value: "",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       x: maritalStatus["deceased"],
    //       y: 108,
    //       isImage: true,
    //       value: "",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       // contribution of tax payable to political party -- taxpayer
    //       x: 224, // No: 144  Yes: 224
    //       y: -28,
    //       isImage: true,
    //       value: "",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       // contribution of tax payable to political party -- spouse
    //       x: 224, // No: 144  Yes: 224
    //       y: -46,
    //       isImage: true,
    //       value: "",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       // contribution of tax payable to political party -- taxpayer
    //       x: 435, //political party nos
    //       y: -25,
    //       isImage: false,
    //       value: "1",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       // contribution of tax payable to political party -- taxpayer
    //       x: 444, //political party nos
    //       y: -25,
    //       isImage: false,
    //       value: "2",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       // contribution of tax payable to political party -- taxpayer
    //       x: 453, //political party nos
    //       y: -25,
    //       isImage: false,
    //       value: "3",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       // contribution of tax payable to political party -- spouse
    //       x: 435, //political party nos
    //       y: -43,
    //       isImage: false,
    //       value: "1",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       // contribution of tax payable to political party -- spouse
    //       x: 444, //political party nos
    //       y: -43,
    //       isImage: false,
    //       value: "2",
    //       size: 9,
    //       page: 1,
    //     },
    //     {
    //       // contribution of tax payable to political party -- spouse
    //       x: 453, //political party nos
    //       y: -43,
    //       isImage: false,
    //       value: "1",
    //       size: 9,
    //       page: 1,
    //     },
    //     //PAGE2 20 tickbox
    //     {
    //       x: 155, // 100
    //       y: 10,
    //       isImage: true,
    //       value: "",
    //       width: 9,
    //       height: 9,
    //       page: 2,
    //     },
    //     //PAGE2 20 tickbox
    //     {
    //       x: 78, // 134
    //       y: -103,
    //       isImage: true,
    //       value: "",
    //       width: 9,
    //       height: 9,
    //       page: 2,
    //     },
    //     //PAGE2 22 tickbox
    //     {
    //       x: 152, // 98
    //       y: -132,
    //       isImage: true,
    //       value: "",
    //       width: 9,
    //       height: 9,
    //       page: 2,
    //     },
    //     //exempted 4 tickbox
    //     {
    //       x: 58, // No: 144  Yes: 224
    //       y: -266, // -250
    //       isImage: true,
    //       value: "",
    //       width: 9,
    //       height: 9,
    //       page: 2,
    //     },
    //   ];
    //   //pincode
    //   if (pincode?.length) {
    //     let arr = pincode.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: pincodePoints[i],
    //         y: 167,
    //         isImage: false,
    //         value: n || "",
    //         page: 1,
    //       });
    //     });
    //   }
    //   // taxpayer PIN
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: taxpayerPINPoints[i],
    //         y: 293,
    //         isImage: false,
    //         value: n || "",
    //         page: 1,
    //       });
    //     });
    //   }
    //   // spouse PIN
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: taxpayerPINPoints[i] + 276,
    //         y: 293,
    //         isImage: false,
    //         value: n || "",
    //         page: 1,
    //       });
    //     });
    //   }
    //   //tax payable
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: taxPayblePoints[i],
    //           y: 20,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 1,
    //         });
    //     });
    //   }
    //   //tax overpaid
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: taxPayblePoints[i] + 301,
    //           y: 20,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 1,
    //         });
    //     });
    //   }
    //   //taxpayer tax to political party
    //   if (taxToParty?.length) {
    //     let len = 6;
    //     let tax = parseFloat(taxToParty).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: taxToPartyPoints[i],
    //           y: -25,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 1,
    //         });
    //     });
    //   }
    //   //taxpayer tax to political party -- spouse
    //   if (taxToParty?.length) {
    //     let len = 6;
    //     let tax = parseFloat(taxToParty).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: taxToPartyPoints[i],
    //           y: -43,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 1,
    //         });
    //     });
    //   }
    //   //request for tax refund
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: taxPayblePoints[i] + 311,
    //           y: -118,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 1,
    //         });
    //     });
    //   }
    //   // payer of income
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: taxpayerPINPoints[i] + 17,
    //         y: 350,
    //         isImage: false,
    //         value: n || "",
    //         page: 2,
    //       });
    //     });
    //   }
    //   // second page 1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 293,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 2
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 279,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 3
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 265,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 4
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 237,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 5
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 223,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 6
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 180,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 7
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 166,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 8
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 139,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 9
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 125,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 10
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 111,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 11
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 97,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 12
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 83,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 13
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 69,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 13.1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 340,
    //           y: 53,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 14
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 40,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 15
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: 26,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 16
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -3,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 17
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -30,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 18
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -59,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 19
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -87,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 20
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -101,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 21
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -115,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second page 22
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -129,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second exempted income 1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -185,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second exempted income 2
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -200,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second exempted income 3
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -216,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second exempted income 4
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -272,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second exempted income 5
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -310,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // second exempted income 6
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i],
    //           y: -325,
    //           isImage: false,
    //           value: n,
    //           size: 9,
    //           page: 2,
    //         });
    //     });
    //   }
    //   // taxpayer TIN
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: taxpayerPINPoints[i] - 1,
    //         y: 328,
    //         isImage: false,
    //         value: n || "",
    //         size: 10,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // spouse TIN
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: taxpayerPINPoints[i] + 259,
    //         y: 328,
    //         isImage: false,
    //         value: n || "",
    //         size: 10,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // third page 1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 302,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 2
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 290,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 3
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 276,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 3.1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 218,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   //child
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 252,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 241,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 229,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 265,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 254,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 242,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 231,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   //second children
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 192,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 180,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 205,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 193,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 182,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // third page 4.1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 146,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 4.2
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 125,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 4.3
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 106,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 4.4
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 86,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // father of taxpayer
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 143,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // mother of spouse
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 103,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // father of spouse
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 83,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // mother of taxpayer
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 123,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // third page 5
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 67,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 6
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: 55,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // father of taxpayer
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 27,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // mother of taxpayer
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 29,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // father of spouse
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i],
    //         y: 5,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // mother of spouse
    //   if (taxpayerPin?.length) {
    //     let arr = taxpayerPin.split("");
    //     arr.forEach((n, i) => {
    //       pdfArray.push({
    //         x: page3Points[i] + 195,
    //         y: 8,
    //         isImage: false,
    //         value: n || "",
    //         size: 9,
    //         page: 3,
    //       });
    //     });
    //   }
    //   // third page 7.1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -12,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 7.2
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -23,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 7.3
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -34,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 8
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -45,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 9
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -56,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 10
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -68,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 11
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -79,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 12
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -90,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 13
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -102,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 13.1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 181,
    //           y: -113,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 14
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -123,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 15
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -144,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 16
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -155,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 17
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -166,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 18
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -177,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 19
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -188,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 20
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -200,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 23.1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 181,
    //           y: -211,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 23.2
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 181,
    //           y: -222,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 23.3
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 181,
    //           y: -233,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 21
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -244,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 22
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -255,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 23
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -266,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 23.1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 181,
    //           y: -277,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 23.2
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 181,
    //           y: -288,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 24
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -297,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 25
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -308,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 26
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -320,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 27
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -331,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 27.1
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 181,
    //           y: -343,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 28
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -353,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 29
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -364,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   // third page 30
    //   if (taxPayble?.length) {
    //     let len = 12;
    //     let tax = parseFloat(taxPayble).toFixed(2) + "";
    //     tax = tax.padStart(len, " ").split("");
    //     tax.forEach((n, i) => {
    //       if (n !== ".")
    //         pdfArray.push({
    //           x: xPoints2Page[i] - 8,
    //           y: -375,
    //           isImage: false,
    //           value: n,
    //           size: 8,
    //           page: 3,
    //         });
    //     });
    //   }
    //   this.fillPdf(pdfArray);
    // },
    async fillPdf(pdfArray) {
      // Load the existing PDF template
      const existingPdfBytes = await fetch("./" + this.pdfFile + ".pdf").then(
        (res) => res.arrayBuffer()
      );
      // Load a PDFDocument from the existing PDF bytes
      const pdfDoc = await PDFDocument.load(existingPdfBytes);
      // Embed the Helvetica font
      const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
      // check image
      const pngImageBytes = await fetch("./check.png").then((res) =>
        res.arrayBuffer()
      );
      const pngImage = await pdfDoc.embedPng(pngImageBytes);
      // Get the first page of the document
      const pages = pdfDoc.getPages();
      if (pdfArray && pdfArray.length > 0) {
        for (let item of pdfArray) {
          const currentPage = pages[item.page - 1];
          const { height } = currentPage.getSize();
          if (item.isImage) {
            // Draw the PNG image
            currentPage.drawImage(pngImage, {
              x: item.x,
              y: height / 2 + item.y,
              width: item.width ? item.width : 12,
              height: item.height ? item.height : 12,
            });
          } else {
            // Draw a string of text diagonally across the first page
            currentPage.drawText("" + item.value + "", {
              x: item.x,
              y: height / 2 + item.y,
              size: item.size ? item.size : 11,
              font: helveticaFont,
              color: rgb(0, 0, 0),
            });
          }
        }
      }
      // Serialize the PDFDocument to bytes (a Uint8Array)
      const pdfBytes = await pdfDoc.save();
      // Create a Blob from the PDF bytes and generate a URL for it
      const blob = new Blob([pdfBytes], { type: "application/pdf" });
      // Create URL for the blob
      const url = window.URL.createObjectURL(blob);
      // Create link element to trigger download
      const link = document.createElement("a");
      link.href = url;
      link.download = this.pdfFile + ".pdf";
      link.click();
      // Clean up
      window.URL.revokeObjectURL(url);
      this.isLoading = false;
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleRetrieveError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "report details",
        isListError: false,
      });
    },
    async fetchAllEmployeesList() {
      this.fetchingEmployees = true;
      await this.$store
        .dispatch("getAllEmployeesList", {
          formName: "Payroll Reports",
          isActiveOnly: 1,
        })
        .then((empData) => {
          this.allEmployeesList = empData.map((item) => ({
            ...item,
            empNameId: item.employee_name + " - " + item.user_defined_empid,
          }));
          this.fetchingEmployees = false;
        })
        .catch(() => {
          this.allEmployeesList = [];
          this.fetchingEmployees = false;
        });
    },
    async getServiceProviderList() {
      this.fetchingServiceProviders = true;
      await this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { serviceProvider } = res.data.getDropDownBoxDetails;
            this.serviceProviderList = serviceProvider;
          }
          this.fetchingServiceProviders = false;
        })
        .catch(() => {
          this.serviceProviderList = [];
          this.fetchingServiceProviders = false;
        });
    },
  },
});
</script>

<style>
.form-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .form-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
