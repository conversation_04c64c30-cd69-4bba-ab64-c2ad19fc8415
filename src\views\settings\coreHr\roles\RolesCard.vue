<template>
  <v-card
    elevation="3"
    class="card-item d-flex rounded-lg ma-2 cursor-pointer"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `border-left: 7px solid ${generateRandomColor()}; height:100%;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
    @click="$emit('view-roles')"
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex align-center">
              <v-icon
                v-if="roleDetails.Type_Of_Admin.includes('Super Admin')"
                class="px-2"
                color="#1E3050"
                >fas fa-users-cog</v-icon
              >
              <v-icon
                v-else-if="roleDetails.Type_Of_Admin.includes('Admin')"
                class="px-2"
                color="#1E3050"
                >fas fa-user-cog</v-icon
              >
              <div class="mx-2 d-flex flex-column justify-start">
                <v-tooltip :text="roleDetails.Roles_Name" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="roleDetails.Roles_Name ? props : ''"
                    >
                      {{ checkNullValue(roleDetails.Roles_Name) }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n8">
        <span
          class="d-flex align-start flex-column ml-3 mt-3 mb-n6"
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Description </b>
              <v-tooltip location="bottom">
                <template v-slot:activator="{ props }">
                  <div
                    class="text-truncate py-2"
                    style="max-width: 200px"
                    v-bind="roleDetails.Description ? props : ''"
                  >
                    {{ checkNullValue(roleDetails.Description) }}
                  </div>
                </template>
                <div style="max-width: 250px !important">
                  {{ roleDetails.Description }}
                </div>
              </v-tooltip>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Default Template </b>
              <span class="py-2">
                {{ roleDetails.Is_Template ? "Yes" : "No" }}</span
              >
            </div>
            <div class="d-flex flex-column justify-start">
              <b class="mt-1 mr-2 text-grey justify-start">Added By </b>
              <span class="py-2">
                {{ checkNullValue(roleDetails.Added_By) }}</span
              >
            </div>
            <div
              v-if="roleDetails.Updated_By && roleDetails.Updated_On"
              class="d-flex flex-column justify-start"
            >
              <b class="mt-1 mr-2 text-grey justify-start">Updated By </b>
              <span class="py-2">
                {{ checkNullValue(roleDetails.Updated_By) }}</span
              >
            </div>
          </v-card-text>
        </span>
        <span
          class="d-flex align-start flex-column ml-3 mt-3 mb-n6"
          :style="
            !isMobileView
              ? 'width:50%'
              : 'width:100% ; margin-top:-28px !important;margin-bottom: 10px !important;'
          "
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex flex-column justify-start">
              <b class="mt-2 text-grey justify-start">Status </b>
              <span
                class="py-2"
                :class="
                  roleDetails.Role_Status == 'Active'
                    ? 'text-green'
                    : 'text-red'
                "
              >
                {{ checkNullValue(roleDetails.Role_Status) }}</span
              >
            </div>
            <div class="d-flex flex-column justify-start">
              <b class="mt-2 text-grey justify-start">Admin Privileges </b>
              <v-tooltip :text="roleDetails.Admin_Privileges" location="bottom">
                <template v-slot:activator="{ props }">
                  <div
                    class="text-truncate py-2"
                    style="max-width: 200px"
                    v-bind="roleDetails.Admin_Privileges ? props : ''"
                  >
                    {{
                      roleDetails.Admin_Privileges
                        ? roleDetails.Admin_Privileges
                        : "-"
                    }}
                  </div>
                </template>
              </v-tooltip>
            </div>
            <div class="d-flex flex-column justify-start">
              <b class="mt-2 text-grey justify-start">Added On </b>
              <span class="py-2"> {{ roleDetails.Added_On }}</span>
            </div>
            <div
              v-if="roleDetails.Updated_By && roleDetails.Updated_On"
              class="d-flex flex-column justify-start"
            >
              <b class="mt-2 text-grey justify-start">Updated On </b>
              <span class="py-2"> {{ roleDetails.Updated_On }}</span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="!roleDetails.Is_Template" class="ml-auto">
      <ActionMenu
        :actions="['Edit', 'Delete', 'Export']"
        @selected-action="handleActions($event)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    iconName="fas fa-trash"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="deleteRoles()"
  >
  </AppWarningModal>
</template>

<script>
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { DELETE_ROLES } from "@/graphql/settings/core-hr/rolesQueries";

export default {
  name: "RolesCard",
  props: {
    roleDetails: {
      type: Object,
      required: true,
    },
  },
  components: { ActionMenu },
  emits: ["refetch-list", "on-open-edit", "view-roles", "export-roles"],
  data() {
    return {
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      openWarningModal: false,
      havingAccess: {},
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      if (!this.roleDetails.Is_Template) {
        this.havingAccess["update"] = 1;
        this.havingAccess["delete"] = 1;
        this.havingAccess["export"] = 1;
      } else {
        this.havingAccess["update"] = 0;
        this.havingAccess["delete"] = 0;
        this.havingAccess["export"] = 0;
      }
      return this.havingAccess;
    },
    handleActions(action) {
      if (action === "Delete") {
        this.openWarningModal = true;
      } else if (action === "Export") {
        this.$emit("export-roles");
      } else {
        this.$emit("on-open-edit");
      }
    },

    deleteRoles() {
      let vm = this;
      vm.openWarningModal = false;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_ROLES,
          variables: {
            roleId: vm.roleDetails.Roles_Id,
          },
          client: "apolloClientAD",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Roles deleted successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-list");
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: "roles",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
