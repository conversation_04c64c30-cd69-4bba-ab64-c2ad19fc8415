<template>
  <v-menu
    v-model="menu"
    :close-on-content-click="false"
    transition="scale-transition"
    offset-y
    min-width="auto"
  >
    <template v-slot:activator="{ props }">
      <v-text-field
        v-model="formattedDate"
        :readonly="true"
        prepend-inner-icon="fas fa-calendar"
        :variant="variant"
        v-bind="props"
        :clearable="true"
        :density="density"
        :single-line="singleLine"
        :rules="rules"
        @click:clear="clearDate"
      >
        <template v-slot:label>
          {{ label }}
          <span v-if="isRequired" style="color: red">*</span>
        </template>
      </v-text-field>
    </template>

    <v-card class="d-flex flex-column align-center" style="max-width: 350px">
      <v-date-picker
        density="compact"
        v-model="selectedDate"
        :min="formatDate(minDate)"
        :max="formatDate(maxDate)"
        @update:model-value="updateDate"
      ></v-date-picker>

      <v-dialog v-model="timePickerDialog" width="auto">
        <template v-slot:activator="{ props }">
          <v-text-field
            v-if="isDateTime"
            v-model="selectedTime"
            label="Select Time"
            readonly
            :variant="variant"
            :density="density"
            :single-line="singleLine"
            v-bind="props"
            style="width: 30%"
          ></v-text-field>
        </template>

        <v-card>
          <v-card-title class="text-h6">Pick a Time</v-card-title>
          <v-divider></v-divider>
          <v-time-picker
            v-if="timePickerDialog"
            v-model="selectedTime"
            format="24hr"
            @update:model-value="updateTime"
          ></v-time-picker>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn text @click="timePickerDialog = false">Cancel</v-btn>
            <v-btn color="primary" text @click="timePickerDialog = false">
              OK
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-card-actions v-if="isDateTime">
        <v-spacer></v-spacer>
        <v-btn text @click="menu = false">Cancel</v-btn>
        <v-btn color="primary" text @click="saveDateTime">OK</v-btn>
      </v-card-actions>
    </v-card>
  </v-menu>
</template>

<script>
import moment from "moment";

export default {
  name: "DatePickerField",
  props: {
    modelValue: {
      type: [String, Date],
      default: null,
    },
    label: {
      type: String,
      default: "Select Date",
    },
    rules: {
      type: Array,
      default: () => [],
    },
    isRequired: {
      type: Boolean,
      default: false,
    },
    isDateTime: {
      type: Boolean,
      default: false,
    },
    minDate: {
      type: String,
      default: null,
    },
    maxDate: {
      type: String,
      default: null,
    },
    variant: {
      type: String,
      default: "solo",
    },
    density: {
      type: String,
      default: "default",
    },
    singleLine: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    if (this.modelValue && typeof this.modelValue === "string") {
      this.formattedDate = this.formatDateTime(this.modelValue);
    }
  },
  data() {
    return {
      menu: false,
      timePickerDialog: false,
      selectedDate: this.modelValue ? moment(this.modelValue).toDate() : null,
      selectedTime: this.modelValue
        ? moment(this.modelValue).format("HH:mm")
        : "00:00",
      formattedDate: this.formatDateTime(this.modelValue),
    };
  },
  computed: {
    dateFormat() {
      return this.$store.state.orgDetails?.orgDateFormat || "DD-MM-YYYY";
    },
    timeFormat() {
      return "HH:mm";
    },
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.selectedDate = moment(val, [
          "YYYY-MM-DD HH:mm",
          "YYYY-MM-DD",
        ]).toDate();
        this.selectedTime =
          moment(val, ["YYYY-MM-DD HH:mm"]).format("HH:mm") || "00:00";
      } else {
        this.selectedDate = null;
        this.selectedTime = "00:00";
      }
      this.formattedDate = this.formatDateTime(val);
    },
  },
  methods: {
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD");
    },
    formatDateTime(date) {
      if (!date) return "";
      return this.isDateTime
        ? moment(date).format(`${this.dateFormat} ${this.timeFormat}`)
        : moment(date).format(this.dateFormat);
    },
    updateDate(val) {
      this.selectedDate = val;
      if (!this.isDateTime) {
        this.saveDateTime();
      }
    },
    updateTime(val) {
      this.selectedTime = val;
    },
    saveDateTime() {
      if (!this.selectedDate) return;

      let dateTime = moment(this.selectedDate).format("YYYY-MM-DD");
      if (this.isDateTime) {
        dateTime += ` ${this.selectedTime}`;
      }

      this.formattedDate = this.formatDateTime(dateTime);
      this.$emit("update:modelValue", dateTime);
      this.menu = false;
    },
    clearDate() {
      this.selectedDate = null;
      this.selectedTime = "00:00";
      this.formattedDate = "";
      this.$emit("update:modelValue", null);
    },
  },
};
</script>
