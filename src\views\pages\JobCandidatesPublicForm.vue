<template>
  <div
    v-if="isMounted"
    class="d-flex flex-column align-center public-form-content"
  >
    <div
      v-if="jobPostStatusId && (jobPostStatusId == 6 || jobPostStatusId == 3)"
      class="d-flex fill-height align-center"
    >
      <v-card class="mx-auto my-8 pt-4 text-center rounded-lg" max-width="600">
        <v-card-title
          ><v-icon size="100" color="red" class="px-6"
            >far fa-times-circle</v-icon
          ></v-card-title
        >
        <v-card-text class="pa-6 text-h5" style="line-height: 1.5em">
          Thank you for your interest. Unfortunately, the position you're
          applying for is now closed. Please check back for future
          opportunities.
        </v-card-text>
      </v-card>
    </div>
    <div v-else class="w-100">
      <v-row>
        <v-col cols="12">
          <EditJobCandidateDetails
            v-if="!showSuccessForm"
            :candidateDetails="candidateDetails"
            :candidateIdSelected="candidateIdSelected"
            :jobPostId="jobPostId"
            @signin-candidate="retrieveJobCandidateDetails($event)"
            @edit-updated="showSuccessForm = true"
            @candidate-portal-access-enabled="isCandidateLoggedIn($event)"
            :showJobHeader="true"
          ></EditJobCandidateDetails>
        </v-col>
      </v-row>
      <v-dialog
        v-model="showSuccessForm"
        :width="presentWithoutSignin ? 600 : 400"
        persistent
      >
        <v-card class="rounded-lg" :class="isMobileView ? 'pa-4' : 'pa-10'">
          <v-card-text class="text-center d-flex flex-column align-center">
            <v-icon color="green" size="100">fas fa-check-circle</v-icon>
            <div v-if="presentWithoutSignin">
              <div class="text-h6 text-primary">
                Your application submitted successfully.
              </div>
              <div class="text-subtitle-1 font-weight-regular mt-2">
                To help you stay informed and engaged throughout your hiring
                journey, we’ve created a Candidate Experience Portal just for
                you. You can log in to view your application status, update your
                profile information, apply for new opportunities, and more.
              </div>
              <div>
                Access your portal
                <span
                  @click="redirectToCandidatePortal()"
                  class="text-decoration-underline text-primary"
                  style="cursor: pointer"
                  >here</span
                >.
              </div>
            </div>
            <div v-else>
              <div class="text-h6 text-primary">
                Your details submitted successfully!
              </div>
              <v-btn
                v-if="candidateDetails && Object.keys(candidateDetails).length"
                @click="redirectToCandidatePortal()"
                variant="text"
                class="text-h6 text-green text-decoration-underline"
              >
                View Application Status
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-dialog>
    </div>
    <AppLoading v-if="isGetJobpostDetailsLoading"></AppLoading>
  </div>
</template>

<script>
import EditJobCandidateDetails from "@/views/recruitment/job-candidates/job-candidates-details/EditJobCandidateDetails.vue";
import { GET_JOB_POST_DETAILS } from "@/graphql/workflow/approvalManagementQueries.js";
import { RETRIEVE_JOB_CANDIDATE_DETAILS } from "@/graphql/recruitment/recruitmentQueries.js";
export default {
  name: "JobCandidatesPublicForm",
  components: { EditJobCandidateDetails },
  data() {
    return {
      showSuccessForm: false,
      presentWithoutSignin: false,
      jobPostId: 0,
      isMounted: false,
      jobPostData: [],
      jobPostStatusId: null,
      isGetJobpostDetailsLoading: false,
      candidateDetails: {},
      candidateIdSelected: 0,
    };
  },
  computed: {
    domainName() {
      return this.$store.getters.domain;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    this.jobPostId = this.$route.query.jobPostId;
    this.getJobPostDetails();
    this.isMounted = true;
  },
  methods: {
    isCandidateLoggedIn(val) {
      this.presentWithoutSignin = val;
    },
    redirectToCandidatePortal() {
      this.$router.push({ path: "/candidate-portal" });
    },
    retrieveJobCandidateDetails(candidateId = 0) {
      let vm = this;
      vm.isLoading = true;
      vm.candidateIdSelected = parseInt(candidateId);
      vm.$apollo
        .query({
          query: RETRIEVE_JOB_CANDIDATE_DETAILS,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: parseInt(candidateId),
            action: "view",
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveJobCandidates &&
            !response.data.retrieveJobCandidates.errorCode
          ) {
            vm.candidateDetails =
              response.data.retrieveJobCandidates.jobCandidateDetails;
          } else {
            vm.handleRetrieveError(
              response.data.retrieveJobCandidates.errorCode
            );
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.handleRetrieveError(err);
        });
    },
    handleRetrieveError() {
      this.isLoading = false;
      var snackbarData = {
        isOpen: true,
        type: "warning",
        message: "Something went wrong while retrieving job candidate details",
      };
      this.showAlert(snackbarData);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async getJobPostDetails() {
      let vm = this;
      vm.isGetJobpostDetailsLoading = true;
      await vm.$apollo
        .query({
          query: GET_JOB_POST_DETAILS,
          variables: {
            jobPostId: parseInt(this.jobPostId),
            employeeId: 1,
            action: "view",
          },
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveJobPost &&
            response.data.retrieveJobPost.jobPostData
          ) {
            this.jobPostData = response.data.retrieveJobPost.jobPostData;
            this.jobPostStatusId = this.jobPostData.Status_Id;
          }
          vm.isGetJobpostDetailsLoading = false;
        })
        .catch((err) => {
          vm.isGetJobpostDetailsLoading = false;
          this.handleGetJobPostDetailsError(err);
        });
    },
    handleGetJobPostDetailsError() {
      this.isListError = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.public-form-content {
  height: 100%;
  background-color: white;
}
.job-candidate-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .job-candidate-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
