<template>
  <v-dialog v-model="openModal" persistent width="1200">
    <v-card>
      <v-card-title class="mb-4">
        <div class="d-flex" style="width: 100%">
          {{ heading }}
          <v-spacer></v-spacer>
          <v-icon color="primary" @click="closePreviewModal()"
            >fas fa-times</v-icon
          >
        </div>
      </v-card-title>
      <v-row>
        <v-col
          v-if="length > 1 ? true : false"
          cols="1"
          class="d-flex align-center justify-center ml-2"
        >
          <v-icon
            class="action-btn"
            :style="
              current === 0
                ? 'cursor: not-allowed !important; opacity: 0.5'
                : 'cursor: pointer !important'
            "
            @click="current === 0 ? {} : $emit('prev-document')"
          >
            fas fa-chevron-circle-left
          </v-icon>
        </v-col>
        <v-col :cols="length > 1 ? '10' : '12'">
          <v-row style="width: 100%; height: 100%; margin-left: 5px">
            <v-col
              :md="
                !isObjectEmpty(documentDetails) && visibleDetails == true
                  ? '9'
                  : '12'
              "
              sm="12"
            >
              <v-card-text
                :style="`
          max-height: calc(100vh - 200px);
          overflow: scroll;
          min-height: 400px;
        `"
              >
                <div class="mt-n4 text-center">
                  <div class="text-primary text-h6 font-weight-medium">
                    {{ formattedFileName }}
                  </div>
                  <img
                    v-if="imgSrc"
                    :src="imgSrc"
                    alt="image source"
                    style="width: 100%"
                  />
                  <div v-else-if="docxSrc" id="docx-container"></div>
                  <div v-else-if="docSrc" class="mt-5">
                    The document format cannot be previewed. To view the
                    document, please download it using the button below.
                  </div>
                  <vue-pdf-app
                    v-else-if="pdfSrc"
                    style="height: 100vh"
                    :pdf="pdfSrc"
                  ></vue-pdf-app>
                  <pre v-else-if="txtSrc">{{ fileContent }}</pre>
                </div>
              </v-card-text>
              <div class="d-flex justify-center">
                <v-btn
                  rounded="lg"
                  theme="dark"
                  variant="elevated"
                  class="font-weight-bold mb-2 primary"
                  @click="downloadFile()"
                >
                  Download
                </v-btn>
              </div>
            </v-col>
            <v-col
              v-if="
                !isObjectEmpty(documentDetails) && visibleDetails == true
                  ? true
                  : false
              "
            >
              <v-row>
                <v-col
                  v-for="(field, index) in documentDetails"
                  :key="index"
                  cols="12"
                  sm="12"
                >
                  <p
                    v-if="field.Field_Visiblity == 'Yes'"
                    class="text-subtitle-1 text-grey-darken-1"
                  >
                    {{ field.Field_Alias }}
                  </p>
                  <p
                    v-if="field.Field_Visiblity == 'Yes'"
                    class="text-subtitle-1 font-weight-regular"
                  >
                    {{ field.Field_value }}
                  </p>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col
          v-if="length > 1 ? true : false"
          cols="1"
          class="d-flex align-center justify-center ml-n4"
        >
          <v-icon
            :class="current === length - 1 ? '' : 'action-btn'"
            :style="
              current === length - 1
                ? 'cursor: not-allowed !important; opacity: 0.5'
                : 'cursor: pointer !important'
            "
            @click="current === length - 1 ? {} : $emit('next-document')"
          >
            fas fa-chevron-circle-right
          </v-icon>
        </v-col>
      </v-row>
    </v-card>
    <AppLoading v-if="isFetchingFiles"></AppLoading>
  </v-dialog>
  <a ref="downloadLink" style="display: none"></a>
</template>

<script>
import VuePdfApp from "vue3-pdf-app";
import "vue3-pdf-app/dist/icons/main.css";
import { checkNullValue } from "@/helper";
import { renderAsync } from "docx-preview";

export default {
  name: "FilePreviewModal",

  components: { VuePdfApp },

  props: {
    fileRetrieveType: {
      type: String,
      default: "documents",
    },
    fileAction: {
      type: String,
      default: "view",
    },
    heading: {
      type: String,
      default: "Attachments",
    },
    fileNamePosition: {
      type: Number,
      default: 3,
    },
    fileName: {
      type: String,
      required: true,
    },
    folderName: {
      type: String,
      required: true,
    },
    appendUnderScoreInDomain: {
      type: Boolean,
      default: false,
    },
    documentDetails: {
      type: Object,
      default: () => {
        return {};
      },
    },
    current: {
      type: Number,
      default: 0,
    },
    length: {
      type: Number,
      default: 0,
    },
    visibleDetails: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["close-preview-modal", "next-document", "prev-document"],
  data() {
    return {
      openModal: false,
      isFetchingFiles: false,
      pdfSrc: "",
      imgSrc: "",
      docxSrc: "",
      docSrc: "",
      txtSrc: "",
      downloadLink: "",
      fileContent: "",
    };
  },
  computed: {
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      let domain = this.$store.getters.domain;
      if (this.appendUnderScoreInDomain) {
        domain = domain + "_";
      }
      return domain;
    },
    formattedFileName() {
      if (this.fileName) {
        let fileNameChunks = this.fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[this.fileNamePosition]
          : "-";
      }
      return "File Name";
    },
  },

  mounted() {
    this.retrieveFileContents();
    this.openModal = true;
  },

  watch: {
    fileName() {
      this.retrieveFileContents();
    },
  },

  methods: {
    checkNullValue,
    downloadFile() {
      window.open(this.downloadLink, "_blank");
    },

    isObjectEmpty(obj) {
      return Object.keys(obj).length === 0;
    },

    closePreviewModal() {
      this.openModal = false;
      this.$emit("close-preview-modal");
    },

    async retrieveFileContents() {
      let vm = this;
      vm.downloadLink = "";
      vm.pdfSrc = "";
      vm.imgSrc = "";
      vm.isFetchingFiles = true;
      let fullFilePath =
        vm.domainName +
        "/" +
        vm.orgCode +
        "/" +
        vm.folderName +
        "/" +
        vm.fileName;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fullFilePath,
          action: vm.fileAction,
          type: vm.fileRetrieveType,
        })
        .then(async (presignedUrl) => {
          vm.downloadLink = presignedUrl;
          if (vm.fileName.includes("pdf")) {
            vm.pdfSrc = presignedUrl;
            vm.imgSrc = "";
            vm.docxSrc = "";
            vm.docSrc = "";
            vm.txtSrc = "";
          } else if (vm.fileName.includes(".docx")) {
            // Fetch the DOCX file and render it using docx-preview
            vm.docxSrc = presignedUrl;
            vm.pdfSrc = "";
            vm.imgSrc = "";
            vm.docSrc = "";
            vm.txtSrc = "";
            const response = await fetch(presignedUrl);
            const arrayBuffer = await response.arrayBuffer();
            const container = document.getElementById("docx-container");
            await renderAsync(arrayBuffer, container);
          } else if (vm.fileName.includes(".doc")) {
            vm.docSrc = presignedUrl;
            vm.docxSrc = "";
            vm.pdfSrc = "";
            vm.imgSrc = "";
            vm.txtSrc = "";
          } else if (vm.fileName.includes(".txt")) {
            vm.txtSrc = presignedUrl;
            vm.imgSrc = "";
            vm.pdfSrc = "";
            vm.docxSrc = "";
            vm.docSrc = "";
            let response = await fetch(presignedUrl);
            let content = await response.text();
            vm.fileContent = content;
          } else {
            vm.txtSrc = "";
            vm.imgSrc = presignedUrl;
            vm.pdfSrc = "";
            vm.docxSrc = "";
            vm.docSrc = "";
          }
          vm.openModal = true;
          vm.isFetchingFiles = false;
        })
        .catch(() => {
          vm.isFetchingFiles = false;
        });
    },
  },
};
</script>
<style>
.action-btn:hover {
  transform: scale(2);
  cursor: pointer;
}
</style>
