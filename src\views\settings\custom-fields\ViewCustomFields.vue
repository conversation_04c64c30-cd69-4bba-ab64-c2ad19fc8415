<template>
  <v-overlay
    :model-value="showViewForm"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:50vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">View {{ landedFormName }}</div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <div class="d-flex justify-end align-center">
            <v-btn
              v-if="formAccess?.update"
              @click="onOpenEditForm()"
              class="mr-3 mt-3 text-primary"
              variant="text"
              rounded="lg"
            >
              <v-icon class="mr-1" size="15">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <div class="px-6 py-2">
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Field Name</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Custom_Field_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Field Type</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Custom_Field_Type) }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="
                  selectedItem?.Custom_Field_Type?.toLowerCase() ===
                    'text field' ||
                  selectedItem?.Custom_Field_Type?.toLowerCase() ===
                    'text area' ||
                  selectedItem?.Custom_Field_Type?.toLowerCase() === 'number'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div
                  v-if="
                    selectedItem?.Custom_Field_Type?.toLowerCase() !== 'number'
                  "
                  class="text-subtitle-1 text-grey-darken-1"
                >
                  Minimum Characters
                </div>
                <div
                  v-if="
                    selectedItem?.Custom_Field_Type?.toLowerCase() === 'number'
                  "
                  class="text-subtitle-1 text-grey-darken-1"
                >
                  Minimum Value
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ selectedItem.Min_Validation || 0 }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="
                  selectedItem?.Custom_Field_Type?.toLowerCase() ===
                    'text field' ||
                  selectedItem?.Custom_Field_Type?.toLowerCase() ===
                    'text area' ||
                  selectedItem?.Custom_Field_Type?.toLowerCase() === 'number'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div
                  v-if="
                    selectedItem?.Custom_Field_Type?.toLowerCase() !== 'number'
                  "
                  class="text-subtitle-1 text-grey-darken-1"
                >
                  Maximum Characters
                </div>
                <div
                  v-if="
                    selectedItem?.Custom_Field_Type?.toLowerCase() === 'number'
                  "
                  class="text-subtitle-1 text-grey-darken-1"
                >
                  Maximum Value
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Max_Validation) }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="
                  selectedItem?.Custom_Field_Type?.toLowerCase() ===
                    'text field' ||
                  selectedItem?.Custom_Field_Type?.toLowerCase() === 'text area'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div class="text-subtitle-1 text-grey-darken-1">
                  Validation Rule
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Validation_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="
                  selectedItem?.Custom_Field_Type?.toLowerCase() ===
                    'single choice' ||
                  selectedItem?.Custom_Field_Type?.toLowerCase() ===
                    'multiple choice'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div class="text-subtitle-1 text-grey-darken-1">
                  Dropdown Values
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{
                      checkNullValue(
                        JSON.parse(selectedItem.Dropdown_Values)?.join(", ")
                      )
                    }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="selectedItem?.Custom_Field_Type?.toLowerCase() === 'url'"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div class="text-subtitle-1 text-grey-darken-1">URL Link</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Url_Link) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Who Can See
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{
                      checkNullValue(getAllRoleNames(selectedItem?.Roles_Id))
                    }}
                  </section>
                </div>
              </v-col>

              <!-- Prerequisite Field -->
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Prerequisite Field
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(dependentFieldInfo?.fieldName) }}
                  </section>
                </div>
              </v-col>
              <!-- Prerequisite Value Type -->
              <v-col
                v-if="dependentFieldInfo?.fieldName"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div class="text-subtitle-1 text-grey-darken-1">
                  Prerequisite Value Type
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{
                      checkNullValue(selectedItem?.Visibility_Condition?.Type)
                    }}
                  </section>
                </div>
              </v-col>

              <!-- Prerequisite Value -->
              <v-col
                v-if="
                  selectedItem?.Visibility_Condition?.Type?.toLowerCase() ===
                  'specific'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 my-3"
              >
                <div class="text-subtitle-1 text-grey-darken-1">
                  Prerequisite Value
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{
                      checkNullValue(formatDependentValue(dependentFieldInfo))
                    }}
                  </section>
                </div>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" class="my-2">
                <v-card
                  class="mx-3 pl-4 py-4"
                  style="box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1)"
                >
                  <div>
                    <!-- Dynamic form rendering -->
                    <div>
                      <v-row>
                        <v-col cols="3">Form Name</v-col>
                        <v-col cols="3">Field Visibility</v-col>
                        <v-col cols="3">Required</v-col>
                        <v-col cols="3" class="ml-n6"
                          >Integration Mapping Key</v-col
                        >
                      </v-row>
                      <!-- Render each form -->
                      <v-row
                        v-for="(form, formIndex) in selectedFormValue"
                        :key="formIndex"
                        class="align-center my-n7"
                      >
                        <v-col cols="3">{{ form.Form_Name }}</v-col>
                        <v-col cols="3">
                          <v-checkbox
                            v-model="form.Field_Visibility"
                            color="primary"
                            :readonly="true"
                          />
                        </v-col>
                        <v-col cols="3">
                          <v-checkbox
                            v-model="form.Mandatory"
                            color="primary"
                            :readonly="true"
                          />
                        </v-col>
                        <v-col cols="3">
                          {{ checkNullValue(form.Integration_Mapping_Key) }}
                        </v-col>
                      </v-row>
                    </div>
                  </div>
                </v-card>
              </v-col>
            </v-row>
            <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails> </v-col
            ></v-row>
          </div> </v-card-text></v-card></template
  ></v-overlay>
</template>

<script>
import { checkNullValue, convertUTCToLocal } from "@/helper";
import moment from "moment";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
export default {
  name: "ViewCustomFields",
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      selectedFormValue: [],
      showViewForm: true,
    };
  },
  props: {
    selectedItem: {
      type: Object,
      default: () => {},
    },
    landedFormName: {
      type: String,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
    listData: {
      type: Array,
      required: true,
    },
  },
  emits: ["close-view-form", "open-edit-form"],
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    dependentFieldInfo() {
      if (!this.selectedItem?.Visibility_Condition) return null;

      const visibilityCondition =
        typeof this.selectedItem.Visibility_Condition === "string"
          ? JSON.parse(this.selectedItem.Visibility_Condition)
          : this.selectedItem.Visibility_Condition;

      // Find the dependent field from the store or parent component
      const parentField = this.listData?.find(
        (field) => field.Custom_Field_Id === visibilityCondition.Custom_Field_Id
      );

      if (!parentField) return null;

      return {
        fieldId: visibilityCondition.Custom_Field_Id,
        fieldName: parentField.Custom_Field_Name,
        fieldType: parentField.Custom_Field_Type,
        value: visibilityCondition.Value,
        dropdownValues: parentField.Dropdown_Values,
      };
    },
  },
  components: {
    MoreDetails,
  },
  mounted() {
    this.prefillMoreDetails();
    this.updateMandatoryToBoolean(this.selectedItem);
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    onOpenEditForm() {
      this.showViewForm = false;
      this.$emit("open-edit-form");
    },
    onCloseOverlay() {
      this.showViewForm = false;
      this.$emit("close-view-form");
    },
    getAllFormNames(forms) {
      return forms.map((form) => form.Form_Name).join(", ");
    },
    getAllRoleNames(roles) {
      return roles.map((role) => role.Roles_Name).join(", ");
    },
    updateMandatoryToBoolean(value) {
      // Ensure selected FormValue is an array
      if (Array.isArray(value.FormIds)) {
        this.selectedFormValue = value.FormIds.map((form) => ({
          ...form,
          Field_Visibility: true,
          Mandatory: form.Mandatory?.toLowerCase() === "yes",
        }));
      } else this.selectedFormValue = [];
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.selectedItem.Added_On),
        addedByName = this.selectedItem.Added_By,
        updatedByName = this.selectedItem.Updated_By,
        updatedOn = this.convertUTCToLocal(this.selectedItem.Updated_On);

      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    formatDependentValue(dependentInfo) {
      if (!dependentInfo) return "";

      const { fieldType, value, dropdownValues } = dependentInfo;

      // Format based on field type
      if (fieldType?.toLowerCase() === "date") {
        return this.formatDate(value);
      }

      if (fieldType?.toLowerCase() === "single choice" && dropdownValues) {
        return value;
      }
      if (fieldType?.toLowerCase() === "multiple choice" && dropdownValues) {
        return JSON.parse(value)?.join(", ");
      }

      // For all other types (text, number, etc.)
      return value;
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
