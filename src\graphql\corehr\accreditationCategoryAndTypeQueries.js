import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_ACCREDITATION_ENFORCEMENT_GROUPS = gql`
  query listAccreditationEnforcementGroups {
    listAccreditationEnforcementGroups {
      errorCode
      message
      groups {
        Group_Id
        Group_Name
      }
    }
  }
`;
export const LIST_ACCREDITATION_CATEGORY_TYPES = gql`
  query listAccreditationCategoryAndType {
    listAccreditationCategoryAndType {
      errorCode
      message
      accreditationCategoryTypes
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_ACCREDITATION_CATEGORY_TYPE = gql`
  mutation addUpdateAccreditationCategoryType(
    $Accreditation_Category_And_Type_Id: Int
    $Accreditation_Category: String!
    $Accreditation_Type: String!
    $Mandatory: BinaryEnum!
    $Instruction: String
    $File_Name: String
    $Enforce_Dependent: BinaryEnum!
    $Group_Ids: [Int]
  ) {
    addUpdateAccreditationCategoryType(
      Accreditation_Category_And_Type_Id: $Accreditation_Category_And_Type_Id
      Accreditation_Category: $Accreditation_Category
      Accreditation_Type: $Accreditation_Type
      Mandatory: $Mandatory
      Instruction: $Instruction
      File_Name: $File_Name
      Enforce_Dependent: $Enforce_Dependent
      Group_Ids: $Group_Ids
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_ACCREDITATION_ENFORCEMENT_GROUP = gql`
  mutation addAccreditationEnforcementGroup(
    $groupNames: [String!]!
    $formId: Int!
  ) {
    addAccreditationEnforcementGroup(groupNames: $groupNames, formId: $formId) {
      errorCode
      message
    }
  }
`;
