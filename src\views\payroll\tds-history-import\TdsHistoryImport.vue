<template>
  <v-container class="tds-history-container" fluid>
    <div>
      <v-row justify="center">
        <v-col cols="12" lg="11" md="12" sm="12">
          <v-card min-height="560" class="rounded-lg">
            <v-card-text>
              <div class="text-center mb-6">
                <span v-for="i in 3" :key="i">
                  <v-icon color="secondary" size="18" class="ml-1">{{
                    currentStep >= i ? "fas fa-circle" : "far fa-circle"
                  }}</v-icon>
                </span>
              </div>
              <BulkImportStep1
                class="mb-10"
                v-show="currentStep === 1"
                ref="bulkStep1"
                :step1-text="step1Text"
                @file-upload-success="uploadFile($event)"
                @file-upload-error="fileRemoveOrError()"
                @generate-excel="onGenerateExcel()"
                showDownload="true"
              >
              </BulkImportStep1>
              <BulkImportStep2
                class="mb-10 pb-5"
                v-if="fileContent.length > 0 && currentStep === 2"
                ref="bulkStep2"
                :file-params="fileContent"
                :headers-selected="selectedHeaders"
                @column-mapped="
                  matchedCount = $event[0];
                  mappedFileHeader = $event[1];
                "
              ></BulkImportStep2>
              <BulkImportStep3
                class="mb-10"
                ref="bulkImportStep3"
                v-if="checkMatchedFields && currentStep === 3"
                :fields="generateFields"
                :json-data="excelEditorData"
                type-of-import="tdsHistoryImport"
              ></BulkImportStep3>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-bottom-navigation v-model="openBottomSheet">
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
        >
          <v-row class="pa-3" justify="center">
            <v-col
              v-if="!isLoadUploadButton"
              cols="6"
              class="pa-0 d-flex justify-start align-center pl-2"
            >
              <v-btn
                v-if="currentStep > 1"
                id="back_to_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                color="secondary"
                @click="backToStep()"
              >
                <span><i class="fa fa-chevron-left pr-2"></i> Back</span></v-btn
              >
              <v-btn
                id="cancel_step"
                variant="outlined"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                class="ml-2"
                color="primary"
                @click="closeForm()"
                >Cancel</v-btn
              >
            </v-col>
            <v-col
              :cols="isLoadUploadButton ? '12' : '6'"
              class="pa-0 d-flex justify-center align-center pr-4"
              :style="windowWidth >= 1264 ? 'margin-left: -106px' : ''"
            >
              <div v-if="windowWidth > 768" class="text-end mr-2">
                <div class="mr-1 text-grey text-caption" style="width: 400px">
                  {{ nextBtnHelpContent }}
                </div>
              </div>
              <v-btn
                id="next_step"
                rounded="lg"
                color="secondary"
                class="mr-10"
                :disabled="!enableNextButton"
                :loading="isLoadUploadButton"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                @click="nextStep()"
              >
                <span>
                  {{ currentStep === 3 ? "Submit" : "Next" }}
                  <v-icon v-if="currentStep !== 3" class="pl-1" size="15"
                    >fa fa-chevron-right</v-icon
                  >
                </span>
              </v-btn>
            </v-col>
            <v-col
              cols="12"
              v-if="windowWidth <= 768 && nextBtnHelpContent"
              class="pa-1 pr-4 d-flex align-center justify-end"
            >
              <div class="mr-1 text-grey mb-0" style="font-size: 10px">
                {{ nextBtnHelpContent }}
              </div>
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
    </div>
    <v-dialog v-model="importConfirmation" width="50%">
      <v-card>
        <v-row>
          <v-col v-if="invalidData && invalidData.length" cols="12">
            <v-alert prominent type="warning">
              <v-row align="center">
                <v-col v-if="invalidData" class="grow"
                  ><span>{{ invalidEmployees.length }}</span>
                  out of
                  {{ excelEditorData.length }} employee do not have valid
                  records.This may result in omittion of update for those
                  records
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertUpdateEmployeeData(finalUpdateData)"
                    >Update anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
          <v-col v-else cols="12" class="pa-3">
            <v-alert prominent type="success">
              <v-row align="center">
                <v-col class="grow">
                  Everything looks <strong>good</strong>.
                  <div class="pt-1">
                    Are you
                    <strong>sure</strong> you want to import the TDS history
                    details?
                  </div>
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertUpdateEmployeeData(finalUpdateData)"
                    >Update anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-overlay
          class="align-center justify-center"
          contained
          :model-value="isLoading"
          scrim="#fff"
        >
          <v-progress-circular color="secondary" indeterminate size="64">
          </v-progress-circular>
        </v-overlay>
      </v-card>
    </v-dialog>

    <v-dialog v-model="openTdsHistoryTemplate" persistent width="900">
      <TdsHistoryTemplateExport @close-dialog-form="closeDialogForm()" />
    </v-dialog>
  </v-container>
</template>
<script>
import BulkImportStep1 from "@/views/common/bulkImport/BulkImportStep1.vue";
import BulkImportStep2 from "@/views/common/bulkImport/BulkImportStep2.vue";
import BulkImportStep3 from "@/views/common/bulkImport/BulkImportStep3.vue";
import TdsHistoryTemplateExport from "./TdsHistoryFormAndExport.vue";
export default {
  name: "TdsHistoryImport",
  components: {
    BulkImportStep1,
    BulkImportStep2,
    BulkImportStep3,
    TdsHistoryTemplateExport,
  },
  data: () => ({
    currentStep: 1,
    fileContent: [],
    errorsCountInExcel: 0,
    matchedCount: 0,
    openBottomSheet: true,
    isLoadUploadButton: false,
    mappedFileHeader: [],
    step1Text: {
      typeofData: "TDS history data sheet",
      text: "You have the option of using our predefined template or bring in your own TDS history data sheet with the headers for import",
      heading: "Download the excel template with predefined headers",
    },
    selectedImportType: 1,
    fields: [],
    optionValues: {},
    excelEditorData: [],
    importConfirmation: false,
    finalExcelData: [],
    finalUpdateData: [],
    isLoading: false,
    step2HeadersData: [],
    openTdsHistoryTemplate: false,
  }),

  computed: {
    selectedHeaders() {
      let output = [
        {
          title: "Employee Id",
          value: "Employee Id",
          props: {
            disabled: false,
          },
        },
        {
          title: "Employee Name",
          value: "Employee Name",
          props: {
            disabled: false,
          },
        },
        {
          title: "History Type",
          value: "History Type",
          props: {
            disabled: false,
          },
        },
        {
          title: "From Date",
          value: "From Date",
          props: {
            disabled: false,
          },
        },
        {
          title: "To Date",
          value: "To Date",
          props: {
            disabled: false,
          },
        },
        {
          title: "Gross Salary",
          value: "Gross Salary",
          props: {
            disabled: false,
          },
        },
        {
          title: "Basic Pay",
          value: "Basic Pay",
          props: {
            disabled: false,
          },
        },
        {
          title: "Dearness Allowance",
          value: "Dearness Allowance",
          props: {
            disabled: false,
          },
        },
        {
          title: "House Rent Allowance",
          value: "House Rent Allowance",
          props: {
            disabled: false,
          },
        },
        {
          title: "Other Allowance",
          value: "Other Allowance",
          props: {
            disabled: false,
          },
        },
        {
          title: "Commission",
          value: "Commission",
          props: {
            disabled: false,
          },
        },
        {
          title: "Perquisites",
          value: "Perquisites",
          props: {
            disabled: false,
          },
        },
        {
          title: "Profits In Lieu Of Salary",
          value: "Profits In Lieu Of Salary",
          props: {
            disabled: false,
          },
        },
        {
          title: "Professional Tax",
          value: "Professional Tax",
          props: {
            disabled: false,
          },
        },
        {
          title: "Total Income Tax Paid",
          value: "Total Income Tax Paid",
          props: {
            disabled: false,
          },
        },
        {
          title: "Medical Expense",
          value: "Medical Expense",
          props: {
            disabled: false,
          },
        },
        {
          title: "Medical Insurance",
          value: "Medical Insurance",
          props: {
            disabled: false,
          },
        },
        {
          title: "Leave Encashment Exemptions",
          value: "Leave Encashment Exemptions",
          props: {
            disabled: false,
          },
        },
        {
          title: "Gratuity Amount",
          value: "Gratuity Amount",
          props: {
            disabled: false,
          },
        },
        {
          title: "VRS Exemption Amount",
          value: "VRS Exemption Amount",
          props: {
            disabled: false,
          },
        },
        {
          title: "No of times LTA claimed",
          value: "No of times LTA claimed",
          props: {
            disabled: false,
          },
        },
        {
          title: "LTA Exemption carried forward",
          value: "LTA Exemption carried forward",
          props: {
            disabled: false,
          },
        },
        {
          title: "No Of times LTA carried forward",
          value: "No Of times LTA carried forward",
          props: {
            disabled: false,
          },
        },
        {
          title: "Education cess",
          value: "Education cess",
          props: {
            disabled: false,
          },
        },
        {
          title: "Surcharge",
          value: "Surcharge",
          props: {
            disabled: false,
          },
        },

        {
          title: "Total Employee PF contribution",
          value: "Total Employee PF contribution",
          props: {
            disabled: false,
          },
        },

        {
          title: "Total Employee VPF Contribution",
          value: "Total Employee VPF Contribution",
          props: {
            disabled: false,
          },
        },

        {
          title: "Total Employer NPS Contribution",
          value: "Total Employer NPS Contribution",
          props: {
            disabled: false,
          },
        },
      ];

      return output;
    },

    invalidData() {
      return this.$refs.bulkImportStep3.invalidData;
    },
    invalidEmployees() {
      let invalidData = this.$refs.bulkImportStep3.invalidData;
      let employeeFail = Array.from(new Set(invalidData));
      return employeeFail;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    // enable next button based on current step and scenarios
    enableNextButton() {
      if (this.currentStep === 1 && this.fileContent.length > 0) {
        return true;
      } else if (this.currentStep === 2 && this.checkMatchedFields) {
        return true;
      } else if (this.currentStep === 3) {
        this.formattedFileContent();
        return true;
      } else {
        return false;
      }
    },

    // next button help content based on current step and scenarios
    nextBtnHelpContent() {
      if (this.currentStep === 1) {
        if (this.fileContent.length === 0)
          return "Please import the data with supported file types (CSV, XLSX and XLS) to continue with the next step.";
        else return "";
      } else if (this.currentStep === 2) {
        return "The unmatched optional column(s) will not be processed in the next step.";
      } else if (this.currentStep === 3) {
        if (this.formattedFileContent.length === 0) {
          return "";
        } else if (this.errorsCountInExcel !== 0) {
          return "There seems to be some validation error(s) in your file. Please amend it before uploading.";
        } else {
          return "By clicking the 'Submit' button, you can import employee data.";
        }
      } else {
        return "";
      }
    },
    mandatoryHeader() {
      let fields = [
        "Employee Id",
        "Employee Name",
        "History Type",
        "From Date",
        "To Date",
        "Gross Salary",
        "Basic Pay",
        "Dearness Allowance",
        "House Rent Allowance",
        "Other Allowance",
        "Commission",
        "Perquisites",
        "Profits In Lieu Of Salary",
        "Professional Tax",
        "Total Income Tax Paid",
        "Medical Expense",
        "Medical Insurance",
        "Leave Encashment Exemptions",
        "Gratuity Amount",
        "VRS Exemption Amount",
        "No of times LTA claimed",
        "LTA Exemption carried forward",
        "No Of times LTA carried forward",
        "Education cess",
        "Surcharge",
        "Total Employee PF contribution",
        "Total Employee VPF Contribution",
        "Total Employer NPS Contribution",
      ];

      return fields;
    },
    // check mandatory fields all are matched
    checkMatchedFields() {
      let mandatoryHeader = this.mandatoryHeader;
      if (this.matchedCount === this.mandatoryHeader.length) {
        let mandatoryMatchedCount = 0;
        for (var i in this.mappedFileHeader) {
          if (mandatoryHeader.includes(this.mappedFileHeader[i].hrapp_header))
            mandatoryMatchedCount++;
        }
        this.addHeaders();
        //  if all the mandatory field are matched then return true else false
        return mandatoryMatchedCount === this.mandatoryHeader.length
          ? true
          : false;
      } else return false;
    },

    // get the data from excel file without empty values
    excelFileData() {
      return this.fileContent.filter(
        (content) => content.filter(Boolean).length > 1
      );
    },
    generateFields() {
      let formOutput = [];
      formOutput = [
        {
          field: "Employee Id",
          label: "Employee Id",
          type: "string",
          readonly: true,
          width: "200px",
        },
        {
          field: "Employee Name",
          label: "Employee Name",
          type: "string",
          readonly: true,
          width: "200px",
        },
        {
          field: "History Type",
          label: "History Type",
          type: "string",
          readonly: true,
          width: "200px",
        },
        {
          field: "From Date",
          label: "From Date",
          type: "string",
          readonly: true,
          width: "200px",
        },
        {
          field: "To Date",
          label: "To Date",
          type: "string",
          readonly: true,
          width: "200px",
        },
        {
          field: "Gross Salary",
          label: "Gross Salary",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Basic Pay",
          label: "Basic Pay",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Dearness Allowance",
          label: "Dearness Allowance",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "House Rent Allowance",
          label: "House Rent Allowance",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Other Allowance",
          label: "Other Allowance",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Commission",
          label: "Commission",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Perquisites",
          label: "Perquisites",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Profits In Lieu Of Salary",
          label: "Profits In Lieu Of Salary",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Professional Tax",
          label: "Professional Tax",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Total Income Tax Paid",
          label: "Total Income Tax Paid",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Medical Expense",
          label: "Medical Expense",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Medical Insurance",
          label: "Medical Insurance",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Leave Encashment Exemptions",
          label: "Leave Encashment Exemptions",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Gratuity Amount",
          label: "Gratuity Amount",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "VRS Exemption Amount",
          label: "VRS Exemption Amount",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "No of times LTA claimed",
          label: "No of times LTA claimed",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "LTA Exemption carried forward",
          label: "LTA Exemption carried forward",
          type: "string",
          readonly: true,
          width: "200px",
        },
        {
          field: "No Of times LTA carried forward",
          label: "No Of times LTA carried forward",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Education cess",
          label: "Education cess",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Surcharge",
          label: "Surcharge",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Total Employee PF contribution",
          label: "Total Employee PF contribution",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Total Employee VPF Contribution",
          label: "Total Employee VPF Contribution",
          type: "number",
          readonly: true,
          width: "200px",
        },
        {
          field: "Total Employer NPS Contribution",
          label: "Total Employer NPS Contribution",
          type: "number",
          readonly: true,
          width: "200px",
        },
      ];

      return formOutput;
    },
  },
  methods: {
    // called when download clicked
    onGenerateExcel() {
      this.openTdsHistoryTemplate = true;
    },
    closeDialogForm() {
      this.openTdsHistoryTemplate = false;
    },
    formattedFileContent() {
      //With Fields form the headers
      let generatedData = this.formExcelData();
      this.excelEditorData = generatedData;
    },
    formExcelData() {
      let fields = this.generateFields;
      let data = JSON.parse(JSON.stringify(this.excelFileData));
      let headersAssigned = this.step2HeadersData;
      //Getting the field of the array of objects
      let excelData = [];
      let idCounter = 1;
      // Iterate through each row of data
      for (let i = 1; i < data.length; i++) {
        let rowData = data[i];
        let rowObj = { $id: "000000" + idCounter++ };

        // Iterate through each field definition and populate the row object
        for (let j = 0; j < fields.length; j++) {
          let fieldDef = fields[j];
          let fieldName = fieldDef.field;
          // Find the index of the field in the header mappings array
          let headerIndex = headersAssigned.findIndex(
            (header) => header.hrapp_header === fieldName
          );

          // If the field is present in the header mappings array, use the corresponding value from the input data
          if (headerIndex >= 0) {
            let dataValue = rowData[headerIndex];
            if (dataValue !== null && dataValue !== undefined) {
              rowObj[fieldName] = dataValue;
            } else {
              rowObj[fieldName] = null;
            }
          } else {
            // If the field is not present in the header mappings array, use the default value for the field type
            switch (fieldDef.type) {
              case "string":
                rowObj[fieldName] = "";
                break;
              case "number":
                rowObj[fieldName] = 0;
                break;
              case "boolean":
                rowObj[fieldName] = false;
                break;
              default:
                rowObj[fieldName] = null;
                break;
            }
          }
        }
        excelData.push(rowObj);
      }
      return excelData;
    },
    // called cancel is clicked to close form(need to write logic for this, redirect to payroll/tds-history php)
    closeForm() {
      this.currentStep = 1;
    },
    // back button clicks, to subtract 1 from current step
    backToStep() {
      this.currentStep = this.currentStep - 1;
      this.allRecordsFail = false;
    },

    // next button clicks, to add 1 from current step
    nextStep() {
      if (this.currentStep === 3) {
        this.formBulkData(this.$refs.bulkImportStep3.filteredData);
      } else {
        this.currentStep += 1;
      }
    },

    addHeaders() {
      if (this.$refs.bulkStep2 && this.$refs.bulkStep2.tableItems) {
        this.step2HeadersData = this.$refs.bulkStep2.tableItems;
      }
    },

    formBulkData(data) {
      this.finalExcelData = data;
      this.finalUpdateData = data;
      this.importConfirmation = true;
    },

    // called when file uploaded in step 1
    uploadFile(event) {
      this.fileContent = event;
    },
    // called from step 1 when error while uploading or removing the file
    fileRemoveOrError() {
      this.fileContent = [];
      this.matchedCount = 0;
      this.errorsCountInExcel = 0;
    },
    insertUpdateEmployeeData() {
      this.importConfirmation = false;
      this.isLoading = false;
      this.closeForm();
    },
  },
};
</script>
<style>
.tds-history-container {
  padding: 5em 2em 0em 3em;
}
.v-bottom-navigation__content {
  justify-content: space-around;
  flex-direction: column;
}
.dp__button_bottom {
  display: none;
}
</style>
