<template>
  <div
    v-if="experienceDetails && experienceDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-subtitle-1 text-grey pl-7"
  >
    No experience details have been added
  </div>
  <div class="ml-7 pb-4"></div>
  <v-card
    elevation="3"
    v-for="(data, index) in experienceDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg mr-4 mb-1"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `width:450px; border-left: 7px solid ${generateRandomColor()}; height:auto; `
        : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <v-row>
      <v-col
        v-if="labelList[279].Field_Visiblity == 'Yes'"
        cols="12"
        class="pa-0 pl-4 d-flex"
      >
        <v-tooltip :text="data.Prev_Company_Name" location="bottom">
          <template v-slot:activator="{ props }">
            <div
              class="text-primary font-weight-bold text-subtitle-1 text-truncate"
              :style="isMobileView ? 'max-width: 180px' : 'max-width: 350px'"
              v-bind="data.Prev_Company_Name ? props : ''"
            >
              {{ checkNullValue(data.Prev_Company_Name) }}
            </div>
          </template>
        </v-tooltip>
      </v-col>
      <v-col
        v-if="labelList[280].Field_Visiblity == 'Yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="mt-2 mr-2 d-flex flex-column justify-start text-body-1">
          <b class="mr-2 text-grey justify-start">
            {{ labelList[280].Field_Alias }}
          </b>
          <v-tooltip :text="data.Designation" location="bottom">
            <template v-slot:activator="{ props }"
              ><div
                :style="isMobileView ? 'max-width: 200px' : 'max-width:140px'"
                class="text-truncate"
                v-bind="data.Designation ? props : ''"
              >
                {{ checkNullValue(data.Designation) }}
              </div></template
            ></v-tooltip
          >
        </div>
      </v-col>
      <v-col
        v-if="labelList[281].Field_Visiblity == 'Yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="mt-2 mr-2 d-flex flex-column justify-start text-body-1">
          <b class="mr-2 text-grey justify-start">
            {{ labelList[281].Field_Alias }}</b
          >
          <span class="pb-1 pt-1">{{ formatDate(data.Start_Date) }}</span>
        </div></v-col
      >
      <v-col
        v-if="labelList[282].Field_Visiblity == 'Yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="mt-2 mr-2 d-flex flex-column justify-start text-body-1">
          <b class="mr-2 text-grey justify-start">
            {{ labelList[282].Field_Alias }}</b
          >
          <span class="pb-1 pt-1">
            {{ formatDate(data.End_Date) }}
          </span>
        </div></v-col
      >
      <v-col
        v-if="labelList[336].Field_Visiblity?.toLowerCase() === 'yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <div class="mt-2 mr-2 d-flex flex-column justify-start text-body-1">
          <b class="mr-2 text-grey justify-start">
            {{ labelList[336].Field_Alias }}
          </b>
          <v-tooltip :text="data.Prev_Company_Location" location="bottom">
            <template v-slot:activator="{ props }"
              ><div
                :style="isMobileView ? 'max-width: 200px' : 'max-width:140px'"
                class="text-truncate"
                v-bind="data.Prev_Company_Location ? props : ''"
              >
                {{ checkNullValue(data.Prev_Company_Location) }}
              </div></template
            ></v-tooltip
          >
        </div>
      </v-col>
      <v-col
        v-if="labelList[360]?.Field_Visiblity?.toLowerCase() === 'yes'"
        cols="12"
        sm="6"
        class="pa-0 pl-4"
      >
        <!-- Wrapper for Menu -->
        <div class="mt-2 d-flex flex-column justify-start text-body-1">
          <!-- Vuetify Menu -->
          <v-menu open-on-hover location="bottom" offset-y>
            <!-- Menu Activator -->
            <template v-slot:activator="{ props }">
              <div
                :style="isMobileView ? 'max-width: 200px' : 'max-width:140px'"
                class="text-truncate"
                v-bind="props"
              >
                <v-btn
                  variant="text"
                  class="text-decoration-underline ml-n4"
                  color="primary"
                >
                  Reference(s)
                </v-btn>
              </div>
            </template>

            <!-- Menu Content -->
            <v-card class="pa-3" max-width="500px">
              <div v-if="!data.References || data.References.length === 0">
                No references available
              </div>
              <div v-else>
                <div
                  v-for="(reference, index) in data.References"
                  :key="index"
                  class="mb-2"
                >
                  <!-- Reference Name -->
                  <div v-if="labelList[360]?.Field_Visiblity === 'Yes'">
                    {{ labelList[360]?.Field_Alias }}:
                    {{ checkNullValue(reference.Reference_Name) }}
                  </div>

                  <!-- Reference Email -->
                  <div v-if="labelList[361]?.Field_Visiblity === 'Yes'">
                    {{ labelList[361]?.Field_Alias }}:
                    {{ checkNullValue(reference.Reference_Email) }}
                  </div>

                  <!-- Reference Number -->
                  <div v-if="labelList[362]?.Field_Visiblity === 'Yes'">
                    {{ labelList[362]?.Field_Alias }}:
                    {{ checkNullValue(reference.Reference_Number) }}
                  </div>

                  <!-- Separator for multiple references -->
                  <v-divider
                    v-if="index < data.References.length - 1"
                    class="my-2"
                  />
                </div>
              </div>
            </v-card>
          </v-menu>
        </div>
      </v-col>
    </v-row>
  </v-card>
</template>

<script>
import { generateRandomColor, checkNullValue } from "@/helper";
import moment from "moment";

export default {
  name: "ExperienceDetails",
  props: {
    experienceDetails: {
      type: Object,
      required: true,
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date, "YYYY/MM/DD").format(orgDateFormat);
        } else return "-";
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
  },
};
</script>
