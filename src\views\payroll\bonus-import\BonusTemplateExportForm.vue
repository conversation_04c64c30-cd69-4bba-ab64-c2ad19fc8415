<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div class="d-flex align-center justify-space-between px-4 pt-4">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="secondary"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Download bonus template</span
          >
        </div>
        <v-icon @click="closeForm" color="secondary" class="mr-1">
          fas fa-times
        </v-icon>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-4'"
        style="height: calc(100vh - 500px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="bonusImportForm">
            <v-row>
              <v-col cols="12" sm="6" md="6">
                <div class="d-flex">
                  <span class="v-label pr-3 pb-5"
                    >Allowance Restrict Bonus</span
                  >
                  <v-switch
                    color="secondary"
                    class="ml-2"
                    v-model="allowanceRestrictBonus"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @input="updateAllowanceRestrictBonus()"
                  ></v-switch>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                md="6"
                v-if="allowanceRestrictBonus == 'Yes'"
              >
                <CustomSelect
                  :items="allowanceNameItem"
                  label="Allowance Name*"
                  :is-auto-complete="true"
                  :itemSelected="allowanceName"
                  @selected-item="onChangeCustomSelect($event, 'allowanceName')"
                  :rules="[required('Allowance Name', allowanceName)]"
                ></CustomSelect>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                md="6"
                v-if="allowanceRestrictBonus == 'No'"
              >
                <CustomSelect
                  :items="bonusTypeItem"
                  label="Bonus Type*"
                  :is-auto-complete="true"
                  :itemSelected="bonusType"
                  @selected-item="onChangeCustomSelect($event, 'bonusType')"
                  :rules="[required('Bonus Type', bonusType)]"
                ></CustomSelect>
              </v-col>

              <v-col cols="12" sm="6" md="6" lg="6">
                <datepicker
                  v-model="bonusFrom"
                  :minimum-view="'month'"
                  :maximum-view="'year'"
                  placeholder="Bonus From*"
                  style="min-width: 100% !important"
                  :rules="[required('Bonus from', bonusFrom)]"
                  @input="onChangeDateFields()"
                ></datepicker>
                <div
                  v-if="bonusFromErrorMsg"
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ !this.bonusFrom ? bonusFromErrorMsg : "" }}
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" lg="6">
                <datepicker
                  :format="orgDateFormat"
                  v-model="payrollMonth"
                  placeholder="Payroll Month*"
                  style="min-width: 100% !important"
                  :rules="[required('Payroll Month', bonusFrom)]"
                  @input="onChangeDateFields()"
                ></datepicker>
                <div
                  v-if="payrollMonthErrorMsg"
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ !this.payrollMonth ? payrollMonthErrorMsg : "" }}
                </div>
              </v-col>
              <v-col cols="12">
                <div class="d-flex justify-center mt-16">
                  <v-btn
                    id="bulk_sheet_download"
                    rounded="lg"
                    size="small"
                    color="secondary"
                    class="font-weight-bold"
                    @click="saveData"
                  >
                    <v-icon class="mr-2" size="16"
                      >fas fa-cloud-download-alt</v-icon
                    >
                    Download
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import Datepicker from "vuejs3-datepicker";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
//Mixins
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "BonusTemplateExport",
  mixins: [validationRules, FileExportMixin],
  components: {
    CustomSelect,
    Datepicker,
  },
  data() {
    return {
      isMounted: false,
      isLoadingDetails: false,
      allowanceRestrictBonus: "No",
      allowanceName: "",
      bonusType: "",
      bonusFrom: null,
      payrollMonth: null,
      isFormDirty: false,
      bonusFromErrorMsg: "",
      payrollMonthErrorMsg: "",
      openConfirmationPopup: false,
      allowanceRestrictBonusItem: ["Yes", "No"],
      allowanceNameItem: ["shyam", "suhan", "abhishekh"],
      bonusTypeItem: ["variable pay", "performance bonus"],
      forwardToItem: ["suhan", "abhishekh", "shanthi"],
      statusItem: ["applied", "new"],
      exportListDataForYes: [
        {
          User_Defined_EmpId: "HACHADRE43",
          Employee_Name: "shyam",
          Allowance_Restrict_Bonus: "Yes",
          Allowance_Name: "Variable Pay",
          Bonus_From: "Feb, 2022",
          Bonus_To: "Jul, 2022",
          Bonus_In_Percentage: 10.0,
          Bonus_Amount: 10000.0,
          Payroll_Month: "08/03/2022",
          Status: "Pending Approval",
          Forward_To: "suganya",
          Comment: "Comment",
        },
        {
          User_Defined_EmpId: "HRAPP05",
          Employee_Name: "suhan",
          Allowance_Restrict_Bonus: "Yes",
          Allowance_Name: "Variable Pay",
          Bonus_From: "Feb, 2022",
          Bonus_To: "Jul, 2022",
          Bonus_In_Percentage: 10.0,
          Bonus_Amount: 10000.0,
          Payroll_Month: "08/03/2022",
          Status: "Pending",
          Forward_To: "suresh",
          Comment: "Comment",
        },
        {
          User_Defined_EmpId: "HRAPP02",
          Employee_Name: "abhishekh",
          Allowance_Restrict_Bonus: "Yes",
          Allowance_Name: "Variable Pay",
          Bonus_From: "Feb, 2022",
          Bonus_To: "Jul, 2022",
          Bonus_In_Percentage: 10.0,
          Bonus_Amount: 10000.0,
          Payroll_Month: "08/03/2022",
          Status: "Pending Approval",
          Forward_To: "shanthi",
          Comment: "Comment",
        },
      ],
      exportListDataForNo: [
        {
          User_Defined_EmpId: "HACHADRE43",
          Employee_Name: "shyam",
          Allowance_Restrict_Bonus: "No",
          Bonus_Type: "variable pay",
          Bonus_From: "Feb, 2022",
          Bonus_To: "Jul, 2022",
          Bonus_In_Percentage: 10.0,
          Bonus_Amount: 10000.0,
          Payroll_Month: "08/03/2022",
          Status: "Pending Approval",
          Forward_To: "suganya",
          Comment: "Comment",
        },
        {
          User_Defined_EmpId: "HRAPP05",
          Employee_Name: "suhan",
          Allowance_Restrict_Bonus: "No",
          Bonus_Type: "variable pay",
          Bonus_From: "Feb, 2022",
          Bonus_To: "Jul, 2022",
          Bonus_In_Percentage: 10.0,
          Bonus_Amount: 10000.0,
          Payroll_Month: "08/03/2022",
          Status: "Pending",
          Forward_To: "suresh",
          Comment: "Comment",
        },
        {
          User_Defined_EmpId: "HRAPP02",
          Employee_Name: "abhishekh",
          Allowance_Restrict_Bonus: "No",
          Bonus_Type: "variable pay",
          Bonus_From: "Feb, 2022",
          Bonus_To: "Jul, 2022",
          Bonus_In_Percentage: 10.0,
          Bonus_Amount: 10000.0,
          Payroll_Month: "08/03/2022",
          Status: "Pending Approval",
          Forward_To: "shanthi",
          Comment: "Comment",
        },
      ],
    };
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    closeForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-dialog-form");
    },
    onChangeDateFields() {
      this.isFormDirty = true;
    },
    onChangeCustomSelect(val, field) {
      if (field == "allowanceName") {
        this.allowanceName = val;
      } else if (field == "bonusType") {
        this.bonusType = val;
      }
      this.isFormDirty = true;
    },
    updateAllowanceRestrictBonus() {
      this.isFormDirty = true;
    },
    async saveData() {
      const { valid } = await this.$refs.bonusImportForm.validate();
      if (valid && this.bonusFrom && this.payrollMonth) {
        this.exportReportFile();
      } else {
        if (!this.bonusFrom) {
          this.bonusFromErrorMsg = "Bonus From is required";
        }
        if (!this.payrollMonth) {
          this.payrollMonthErrorMsg = "Payroll Month is required";
        }
      }
    },
    exportReportFile() {
      let exportData;
      if (this.allowanceRestrictBonus == "Yes") {
        exportData = this.exportListDataForYes;
      } else {
        exportData = this.exportListDataForNo;
      }

      //Form Headers - This will form like [{ key: 'First_Name', header: 'First Name'}, { key: 'Second_Name', header: 'Second Name'}]
      let headers = [];
      headers.unshift(
        { key: "User_Defined_EmpId", header: "Employee Id" },
        { key: "Employee_Name", header: "Employee Name" },
        {
          key: "Allowance_Restrict_Bonus",
          header: "Allowance Restrict Bonus",
        }
      );
      if (this.allowanceRestrictBonus == "Yes") {
        headers.push({ key: "Allowance_Name", header: "Allowance Name" });
      } else {
        headers.push({ key: "Bonus_Type", header: "Bonus Type" });
      }
      headers.push(
        { key: "Bonus_From", header: "Bonus From" },
        { key: "Bonus_To", header: "Bonus To" },
        { key: "Bonus_In_Percentage", header: "Bonus In Percentage" },
        { key: "Bonus_Amount", header: "Bonus Amount" },
        {
          key: "Payroll_Month",
          header:
            "Payroll Month" +
            "(" +
            this.$store.state.orgDetails.orgDateFormat +
            ")",
        },
        { key: "Status", header: "Status" },
        { key: "Forward_To", header: "Forward To" },
        { key: "Comment", header: "Comment" }
      );

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Bonus template.xlsx",
        sheetName: "Bonus sheet",
        header: headers,
        requiredHeaders: [
          "Employee Id",
          "Employee Name",
          "Allowance Restrict Bonus",
          "Allowance Name",
          "Bonus Type",
          "Bonus From",
          "Bonus To",
          "Bonus In Percentage",
          "Bonus Amount",
          "Payroll Month" +
            "(" +
            this.$store.state.orgDetails.orgDateFormat +
            ")",
          "Status",
          "Forward To",
        ],
        // kept for future use
        // columnHighlightProps: {
        //   type: "Bonus Import",
        //   Allowance_Restrict_Bonus: this.allowanceRestrictBonusItem
        //     ? this.allowanceRestrictBonusItem
        //     : [],
        //   Allowance_Name: this.allowanceNameItem
        //     ? this.allowanceNameItem.slice(1)
        //     : [],
        //   Bonus_Type: this.bonusTypeItem ? this.bonusTypeItem : [],
        //   Forward_To: this.forwardToItem ? this.forwardToItem : [],
        //   Status: this.statusItem ? this.statusItem : [],
        // },
      };
      this.exportExcelFile(exportOptions);
      this.$emit("close-dialog-form");
    },

    // show success/error message in snackbar
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
  },
  mounted() {
    this.isFormDirty = false;
    this.isMounted = true;
  },
};
</script>
