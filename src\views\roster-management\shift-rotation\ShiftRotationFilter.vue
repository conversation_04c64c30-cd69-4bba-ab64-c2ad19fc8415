<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedName"
              color="primary"
              :items="listNames"
              label="Scheduler Name"
              multiple
              variant="solo"
              clearable
              closable-chips
              chips
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="isRepeatSchedule"
              color="primary"
              :items="listSchedule"
              label="Repeat Schedule"
              multiple
              variant="solo"
              clearable
              closable-chips
              chips
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="isEnableRoster"
              color="primary"
              :items="listRoster"
              label="Enable Roster Leave"
              multiple
              variant="solo"
              clearable
              closable-chips
              chips
              single-line
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "ShiftRotationFilter",
  data: () => ({
    openFormFilter: false,
    // Date Picker
    selectedName: null,
    isRepeatSchedule: null,
    isEnableRoster: null,
    listSchedule: ["Yes", "No"],
    listRoster: ["Yes", "No"],
  }),

  props: {
    itemList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    listNames() {
      const filteredItemList = this.itemList.filter(
        (item) => item.Scheduler_Name && item.Scheduler_Name.trim() !== ""
      );
      // Use Set to filter out duplicate employee Name values
      const uniqueNamesSet = new Set(
        filteredItemList.map((item) => item.Scheduler_Name)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
  },
  mounted() {},
  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        selectedName: this.selectedName,
        isRepeatSchedule: this.isRepeatSchedule,
        isEnableRoster: this.isEnableRoster,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedName = null;
      this.isRepeatSchedule = null;
      this.isEnableRoster = null;
    },
  },
};
</script>
