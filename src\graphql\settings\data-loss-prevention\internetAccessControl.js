import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_URL_CATEGORY = gql`
  query listUrlCategory($groupId: Int, $source: String!) {
    listUrlCategory(groupId: $groupId, source: $source) {
      errorCode
      message
      urlDetails {
        domainId
        domainName
        category
      }
      urlCategoryCount {
        productiveUrlCount
        unproductiveUrlCount
        uncategorizedUrlCount
      }
    }
  }
`;
export const GET_EMPLOYEE_LEVEL_DLP_SETTINGS = gql`
  query getEmployeeLevelDlpSettings {
    getEmployeeLevelDlpSettings {
      errorCode
      message
      dlpSettingsData
    }
  }
`;
export const GET_ORGANIZATION_LEVEL_DLP_SETTINGS = gql`
  query getOrganizationLevelDlpSettings {
    getOrganizationLevelDlpSettings {
      errorCode
      message
      dlpSettingsData
    }
  }
`;

export const GET_EMPLOYEE_LEVEL_BLOCKED_DOMAINS = gql`
  query getEmployeeLevelBlockedDomain($employeeId: Int!) {
    getEmployeeLevelBlockedDomain(employeeId: $employeeId) {
      errorCode
      message
      employeeDomainData {
        domainId
        domainName
        category
        addedForWCConf
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const UPDATE_EMPLOYEE_LEVEL_DLP_SETTINGS = gql`
  mutation updateEmployeeLevelDLPSetting($dlpSettingsData: [dlpSettingsData]) {
    updateEmployeeLevelDLPSetting(dlpSettingsData: $dlpSettingsData) {
      errorCode
      message
    }
  }
`;
export const UPDATE_ORGANIZATION_LEVEL_DLP_SETTINGS = gql`
  mutation updateOrganizationLevelDlpSettings(
    $Dlp_Settings_Id: Int!
    $enableInternetAccessControl: String
    $notifyInternetAccessViolation: String
    $internetAccessUpdateFrequency: Int
  ) {
    updateOrganizationLevelDlpSettings(
      Dlp_Settings_Id: $Dlp_Settings_Id
      enableInternetAccessControl: $enableInternetAccessControl
      notifyInternetAccessViolation: $notifyInternetAccessViolation
      internetAccessUpdateFrequency: $internetAccessUpdateFrequency
    ) {
      errorCode
      message
    }
  }
`;

export const RETRIEVE_EMP_MONITOR_SCREENSHOT_FREQUENCY_SETTINGS = gql`
  query retrieveScreenshotsFrequencySettings {
    retrieveScreenshotsFrequencySettings {
      errorCode
      message
      screenshotsFrequency {
        organizationLevelScreenshotsFrequencySettings {
          capture_screenshot
          screenshot_frequency
          no_of_screenshots_per_frequency
        }
        employeeLevelScreenshotsFrequencySettings {
          employee_id
          capture_screenshot
          screenshot_frequency
          no_of_screenshots_per_frequency
          photo_path
          user_defined_empid
          employee_name
          emp_email
        }
      }
    }
  }
`;

export const RETRIEVE_EMP_MONITOR_SCREENSHOT_BLUR_SETTINGS = gql`
  query retrieveScreenshotsBlurSettings {
    retrieveScreenshotsBlurSettings {
      errorCode
      message
      screenshotsBlur {
        organizationLevelBlurSettings {
          screenshot_blur
        }
        employeeLevelBlurSettings {
          employee_id
          screenshot_blur
          photo_path
          user_defined_empid
          employee_name
          emp_email
        }
      }
    }
  }
`;

export const RETRIEVE_ACTIVITY_GOAL_SETTINGS = gql`
  query retrieveActivityGoalSettings {
    retrieveActivityGoalSettings {
      errorCode
      message
      activityGoal {
        organizationLevelActivityGoalSettings {
          lowActivityMinimum
          lowActivityMaximum
          moderateActivityMinimum
          moderateActivityMaximum
          highActivityMinimum
          highActivityMaximum
        }
        employeeLevelActivityGoalSettings {
          employee_id
          photo_path
          user_defined_empid
          employee_name
          emp_email
          lowActivityMinimum
          lowActivityMaximum
          moderateActivityMinimum
          moderateActivityMaximum
          highActivityMinimum
          highActivityMaximum
        }
      }
    }
  }
`;

export const RETRIEVE_APPS_URLS_SETTINGS = gql`
  query retrieveAppUrlSettings {
    retrieveAppUrlSettings {
      errorCode
      message
      appUrlSettings {
        organizationLevelAppUrlSettings {
          track_app_url
        }
        employeeLevelAppUrlSettings {
          employee_id
          track_app_url
          photo_path
          user_defined_empid
          employee_name
          emp_email
        }
        applicationCategoryCount {
          productiveApplicationCount
          unproductiveApplicationCount
          uncategorizedApplicationCount
        }
        urlCategoryCount {
          productiveUrlCount
          unproductiveUrlCount
          uncategorizedUrlCount
        }
      }
    }
  }
`;
// Apps and Urls settings
export const RETRIEVE_APPS_CATEGORY_LIST = gql`
  query listAppCategory($groupId: Int, $source: String!) {
    listAppCategory(groupId: $groupId, source: $source) {
      errorCode
      message
      applicationDetails {
        applicationId
        applicationName
        category
      }
      applicationCategoryCount {
        productiveApplicationCount
        unproductiveApplicationCount
        uncategorizedApplicationCount
      }
    }
  }
`;
export const RETRIEVE_URLS_CATEGORY_LIST = gql`
  query listUrlCategory($groupId: Int, $source: String!) {
    listUrlCategory(groupId: $groupId, source: $source) {
      errorCode
      message
      urlDetails {
        domainId
        domainName
        category
      }
      urlCategoryCount {
        productiveUrlCount
        unproductiveUrlCount
        uncategorizedUrlCount
      }
    }
  }
`;

export const RETRIEVE_EMP_MONITOR_IDLE_TIME_SETTINGS = gql`
  query retrieveIdleTimeSettings {
    retrieveIdleTimeSettings {
      errorCode
      message
      idleTimeSettings {
        organizationLevelIdleTimeSettings {
          idleTime
          considerIdleNotActiveTime
          fixedDailyWorkHours
        }
        employeeLevelIdleTimeSettings {
          employeeId
          userDefinedEmpId
          employeeName
          employeeEmail
          idleTime
          considerIdleNotActiveTime
          photoPath
        }
      }
    }
  }
`;

export const RETRIEVE_SCHEDULE_AND_EFFORT_SETTINGS = gql`
  query retrieveScheduleAndEffortSettings {
    retrieveScheduleAndEffortSettings {
      errorCode
      message
      workScheduleAndEffortDetails {
        orgLevelWorkScheduleAndEffortSettings {
          workScheduleId
          workSchedule
          timeZone
          targetEffortInHoursPerWeek
        }
        employeeLevelWorkScheduleAndEffortSettings {
          workScheduleId
          workSchedule
          timeZone
          targetEffortInHoursPerWeek
          employeeId
          userDefinedEmployeeId
          employeeEmail
          employeeName
        }
      }
    }
  }
`;

export const GET_URL_DOMAIN_WHITELISTING = gql`
  query geturlDomainWhitelisting($coverage: String!) {
    geturlDomainWhitelisting(coverage: $coverage) {
      errorCode
      message
      domainDetails {
        Domain_Whitelist_Id
        Custom_Group_Id
        Whitelisted_Domain
        Category
        Added_By
        Added_On
        Updated_By
        Updated_On
      }
    }
  }
`;
export const GET_BLOCKED_DOMAINS = gql`
  query getDomainsToBeBlocked {
    getDomainsToBeBlocked {
      errorCode
      message
      blockedDomainData {
        domainId
        domainName
        category
        addedForWCConf
      }
    }
  }
`;
// ===============
// Mutation
// ===============
export const UPDATE_EMP_MONITOR_SCREENSHOT_FREQUENCY_SETTINGS = gql`
  mutation updateScreenshotFrequencySettings(
    $organizationSettings: [organizationScreenshotFrequencyInput]!
    $employeeSettings: [employeeScreenshotFrequencyInput]!
  ) {
    updateScreenshotFrequencySettings(
      organizationSettings: $organizationSettings
      employeeSettings: $employeeSettings
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_EMP_MONITOR_SCREENSHOT_BLUR_SETTINGS = gql`
  mutation updateScreenshotBlurSettings(
    $organizationSettings: [organizationBlurSettingsInput]!
    $employeeSettings: [employeeBlurSettingsInput]!
  ) {
    updateScreenshotBlurSettings(
      organizationSettings: $organizationSettings
      employeeSettings: $employeeSettings
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_ACTIVITY_GOAL_SETTINGS = gql`
  mutation updateActivityGoalSettings(
    $organizationSettings: [organizationActivityGoalInput]!
    $employeeSettings: [employeeActivityGoalInput]!
  ) {
    updateActivityGoalSettings(
      organizationSettings: $organizationSettings
      employeeSettings: $employeeSettings
    ) {
      errorCode
      message
      validationError
    }
  }
`;
export const DELETE_ORGANIZATION_LEVEL_BLOCKED_DOMAINS = gql`
  mutation deleteOrganizationLevelBlockedDomains($wCBlockedDomainsId: Int!) {
    deleteOrganizationLevelBlockedDomains(
      wCBlockedDomainsId: $wCBlockedDomainsId
    ) {
      errorCode
      message
    }
  }
`;
export const DELETE_EMPLOYEE_LEVEL_BLOCKED_DOMAINS = gql`
  mutation deleteEmployeeLevelBlockedDomains($wCBlockedDomainsId: Int!) {
    deleteEmployeeLevelBlockedDomains(wCBlockedDomainsId: $wCBlockedDomainsId) {
      errorCode
      message
    }
  }
`;

export const UPDATE_APP_CATEGORY_ORG_LEVEL = gql`
  mutation updateAppCategory($appDetails: [appCategoryInput]!) {
    updateAppCategory(appDetails: $appDetails) {
      errorCode
      message
    }
  }
`;

export const UPDATE_APP_CATEGORY_GROUP_LEVEL = gql`
  mutation updateAppCategoryBasedOnGroup(
    $groupId: Int!
    $appDetails: [appCategoryInput]!
  ) {
    updateAppCategoryBasedOnGroup(groupId: $groupId, appDetails: $appDetails) {
      errorCode
      message
    }
  }
`;

export const UPDATE_APPS_URLS_CATEGORY_SETTINGS = gql`
  mutation updateAppUrlSettings(
    $organizationSettings: [organizationAppUrlInput]!
    $employeeSettings: [employeeAppUrlInput]!
  ) {
    updateAppUrlSettings(
      organizationSettings: $organizationSettings
      employeeSettings: $employeeSettings
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_URL_CATEGORY_ORG_LEVEL = gql`
  mutation updateUrlCategory($urlDetails: [urlCategoryInput]!) {
    updateUrlCategory(urlDetails: $urlDetails) {
      errorCode
      message
    }
  }
`;

export const UPDATE_URLS_CATEGORY_GROUP_LEVEL = gql`
  mutation updateUrlCategoryBasedOnGroup(
    $groupId: Int!
    $urlDetails: [urlCategoryInput]!
  ) {
    updateUrlCategoryBasedOnGroup(groupId: $groupId, urlDetails: $urlDetails) {
      errorCode
      message
    }
  }
`;

export const UPDATE_EMP_MONITOR_IDLE_TIME_SETTINGS = gql`
  mutation updateIdleTimeSettings(
    $organizationSettings: [organizationIdleTimeSettingsInput]!
    $employeeSettings: [employeeIdleTimeSettingsInput]!
  ) {
    updateIdleTimeSettings(
      organizationSettings: $organizationSettings
      employeeSettings: $employeeSettings
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_SCHEDULE_AND_EFFORT_SETTINGS = gql`
  mutation updateWorkScheduleSettings(
    $organizationSettings: [organizationWSAndEffortInput]!
    $employeeSettings: [employeeWSAndEffortInput]!
  ) {
    updateWorkScheduleSettings(
      organizationSettings: $organizationSettings
      employeeSettings: $employeeSettings
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_DOMAIN_WHITELISTING = gql`
  mutation updateUrlDomainWhitelisting(
    $coverage: String!
    $whitelistedDomains: [whitelistedDomains]!
  ) {
    updateUrlDomainWhitelisting(
      whitelistedDomains: $whitelistedDomains
      coverage: $coverage
    ) {
      errorCode
      message
    }
  }
`;

export const DLP_BLOCKED_DOMAIN_DATA = gql`
  mutation addOrganizationLevelBlockedDomains(
    $WC_Blocked_Domain: String
    $Added_For_WC_Conf: Int!
    $dlpBlockedDomainData: [dlpBlockedDomainData]
  ) {
    addOrganizationLevelBlockedDomains(
      WC_Blocked_Domain: $WC_Blocked_Domain
      Added_For_WC_Conf: $Added_For_WC_Conf
      dlpBlockedDomainData: $dlpBlockedDomainData
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_EMPLOYEE_LEVEL_BLOCKED_DOMAIN = gql`
  mutation addEmployeeLevelBlockedDomain(
    $employeeId: Int
    $Added_For_WC_Conf: Int!
    $WC_Blocked_Domain: String
    $dlpBlockedDomainData: [dlpBlockedDomainData]
  ) {
    addEmployeeLevelBlockedDomain(
      employeeId: $employeeId
      Added_For_WC_Conf: $Added_For_WC_Conf
      WC_Blocked_Domain: $WC_Blocked_Domain
      dlpBlockedDomainData: $dlpBlockedDomainData
    ) {
      errorCode
      message
    }
  }
`;
