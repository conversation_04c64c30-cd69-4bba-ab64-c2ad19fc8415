import gql from "graphql-tag";
// ===============
// Queries
// ===============

export const LIST_JOB_ROLES = gql`
  query listJobRoles(
    $formId: Int
    $designationId: Int
    $accreditationCategoryTypeId: Int
  ) {
    listJobRoles(
      formId: $formId
      designationId: $designationId
      accreditationCategoryTypeId: $accreditationCategoryTypeId
    ) {
      errorCode
      message
      jobRoles
    }
  }
`;
// ===============
//Mutations
// ===============
export const ADD_UPDATE_JOB_ROLE = gql`
  mutation addUpdateJobRole(
    $Form_Id: Int
    $Job_Role_Id: Int
    $Job_Role: String!
    $Job_Role_Code: String
    $Designation_Ids: [Int!]!
    $Accreditation_Category_And_Type_Ids: [Int!]!
    $Status: Status!
    $Description: String
  ) {
    addUpdateJobRole(
      Form_Id: $Form_Id
      Job_Role_Id: $Job_Role_Id
      Job_Role: $Job_Role
      Job_Role_Code: $Job_Role_Code
      Designation_Ids: $Designation_Ids
      Accreditation_Category_And_Type_Ids: $Accreditation_Category_And_Type_Ids
      Status: $Status
      Description: $Description
    ) {
      errorCode
      message
    }
  }
`;
