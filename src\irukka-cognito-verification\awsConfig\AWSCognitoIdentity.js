import * as AmazonCognitoIdentity from "amazon-cognito-identity-js";
import store from "../../store";
import { CognitoUserPool } from "amazon-cognito-identity-js";
import axios from "axios";
import Config from "../../../src/config";

class AWSCognitoIdentity {
  /**
   * AWS method to Sign-in
   * @param mobileNumber
   * @param successCallback
   * @param failureCallback
   */

  async signIn(mobileNumber, successCallback, failureCallback) {
    const irukkaConfig = await fetchAWSCognitoCredentials();
    const poolData = {
      UserPoolId: irukkaConfig.userPoolId,
      ClientId: irukkaConfig.clientSecret,
      endpoint: irukkaConfig.cognitoEndpoint,
    };
    const cognitoPool = new CognitoUserPool(poolData);
    const userData = {
      Username: mobileNumber,
      Pool: cognitoPool,
    };
    const authenticationData = {
      Username: mobileNumber,
      Password: irukkaConfig.secretPassword,
    };
    const authenticationDetails =
      new AmazonCognitoIdentity.AuthenticationDetails(authenticationData);
    delete userData.Pool["storage"];
    this.cognitoUser = new AmazonCognitoIdentity.CognitoUser(userData);
    this.cognitoUser.authenticateUser(authenticationDetails, {
      onSuccess: function (result) {
        successCallback(result);
      },
      onFailure: function (err) {
        failureCallback(err);
      },
      mfaRequired: function () {},
    });
  }

  /**
   * AWS Method for verify OTP*
   * @param otp
   * @param success
   * @param failure
   */
  verify(otp, success, failure) {
    this.cognitoUser.sendMFACode(otp, {
      onSuccess: function (result) {
        success(result);
      },
      onFailure: function (err) {
        failure(err);
      },
    });
  }

  /**
   * AWS cognito method for sign-up the user*
   * @param mobileNumber
   * @param success
   * @param failure
   */
  async signUp(mobileNumber, success, failure) {
    const irukkaConfig = await fetchAWSCognitoCredentials();
    const poolData = {
      UserPoolId: irukkaConfig.userPoolId,
      ClientId: irukkaConfig.clientSecret,
      endpoint: irukkaConfig.cognitoEndpoint,
    };
    const cognitoPool = new CognitoUserPool(poolData);

    const attributeList = [
      new AmazonCognitoIdentity.CognitoUserAttribute({
        Name: "phone_number",
        Value: mobileNumber,
      }),
    ];

    cognitoPool.signUp(
      mobileNumber,
      irukkaConfig.secretPassword,
      attributeList,
      [],
      (err, result) => {
        if (err) {
          failure(err);
        } else {
          success(result);
        }
      }
    );
  }

  async confirmRegistration(mobileNumber, otp, success, failure) {
    const irukkaConfig = await fetchAWSCognitoCredentials();
    const poolData = {
      UserPoolId: irukkaConfig.userPoolId,
      ClientId: irukkaConfig.clientSecret,
      endpoint: irukkaConfig.cognitoEndpoint,
    };
    const cognitoPool = new CognitoUserPool(poolData);
    const userData = {
      Username: mobileNumber,
      Pool: cognitoPool,
    };
    this.cognitoUser = new AmazonCognitoIdentity.CognitoUser(userData);

    this.cognitoUser.confirmRegistration(otp, true, function (err, result) {
      if (err) {
        console.log(
          "Confirm Registration Error : ",
          err.message || JSON.stringify(err)
        );
        failure(err);
      } else {
        success(result);
      }
    });
  }

  resendConfirmationCode(success, failure) {
    this.cognitoUser.resendConfirmationCode((err, result) => {
      if (err) {
        console.log("Resend OTP Error : ", err.message || JSON.stringify(err));
        failure(err);
      } else {
        success(result);
      }
    });
  }

  isValidToken() {
    return this.cognitoUser.getSignInUserSession()?.isValid();
  }

  getAccessJwtToken() {
    let token = this.cognitoUser
      .getSignInUserSession()
      ?.getIdToken()
      .getJwtToken();
    return token;
  }

  refreshToken() {
    return this.cognitoUser.getSignInUserSession()?.getRefreshToken();
  }

  getUserAttributes(success, failure) {
    this.cognitoUser.getUserAttributes((err, result) => {
      if (err) {
        console.log(err.message || JSON.stringify(err));
        failure(err);
      }
      success(result);
    });
  }

  updateUserAttributes(data, success, failure) {
    const attributeList = [];
    data.forEach((attribute) => {
      const addedAttributes = new AmazonCognitoIdentity.CognitoUserAttribute({
        Name: attribute.name,
        Value: attribute.value,
      });
      attributeList.push(addedAttributes);
    });
    this.cognitoUser.updateAttributes(attributeList, function (err, result) {
      if (err) {
        console.log(err.message || JSON.stringify(err));
        failure(err);
      }
      success(result);
    });
  }

  getCognitoUser() {
    return this.cognitoUser.getSignInUserSession();
  }

  /**
   * AWS method to get the user session details*
   * @param success
   * @param failure
   */
  getSession(success, failure) {
    this.cognitoUser.getSession((err, result) => {
      if (err) {
        console.log(err.message || JSON.stringify(err));
        failure(err);
      }
      success(result);
    });
  }

  signOut() {
    this.cognitoUser.globalSignOut({
      onSuccess: function (result) {
        console.log("sign out ", result);
      },
      onFailure: function (err) {
        console.log("signout Er => ", err.message || JSON.stringify(err));
      },
    });
  }
}

async function fetchAWSCognitoCredentials() {
  try {
    const url = Config.graphql_endpoint.integrationRead;
    const refreshToken = window.$cookies.get("refreshToken");
    const token = window.$cookies.get("accessToken");
    const orgCode = store.getters.orgCode;
    let requestBody = {
      query:
        "query getAWSCognitoIdentities { getAWSCognitoIdentities{ errorCode message data { userPoolId, clientSecret, secretPassword } }  }",
    };
    const apiHeaders = {
      org_code: orgCode,
      Authorization: token,
      refresh_token: refreshToken,
    };
    const response = await axios.post(url, requestBody, {
      headers: apiHeaders,
    });
    const config = {
      clientSecret:
        response.data.data.getAWSCognitoIdentities.data.clientSecret,
      userPoolId: response.data.data.getAWSCognitoIdentities.data.userPoolId,
      cognitoEndpoint:
        "https://f7pw36yln2.execute-api.ap-south-1.amazonaws.com/uat",
      secretPassword:
        response.data.data.getAWSCognitoIdentities.data.secretPassword,
    };
    return config;
  } catch (error) {
    console.log("Error in fetchAWSCognitoCredentials () function", error);
    throw error;
  }
}

export default AWSCognitoIdentity;
