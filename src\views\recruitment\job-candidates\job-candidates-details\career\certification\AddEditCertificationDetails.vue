<template>
  <div v-if="hasData">
    <span
      class="d-flex"
      :class="
        certificateFormData.length > 0
          ? 'justify-end mt-n4'
          : 'justify-start ml-n3 mt-2 mb-n2'
      "
    >
      <v-btn color="primary" variant="text" @click="onAddNew()">
        <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add New</v-btn
      >
    </span>
    <v-form ref="addEditCertificateForm">
      <div v-for="(certificate, i) in certificateFormData" :key="'exp' + i">
        <v-row align="center">
          <v-col cols="11">
            <v-row>
              <v-col
                v-if="labelList[284]?.Field_Visiblity == 'Yes'"
                cols="12"
                md="6"
              >
                <CustomSelect
                  v-if="labelList[284].Predefined?.toLowerCase() === 'yes'"
                  :items="certificationList"
                  v-model="certificateFormData[i].Certification_Name"
                  :label="labelList[284].Field_Alias"
                  :itemSelected="certificateFormData[i].Certification_Name"
                  itemValue="Required_Certification"
                  itemTitle="Required_Certification"
                  clearable
                  @selected-item="
                    certificateFormData[i].Certification_Name = $event
                  "
                  :isAutoComplete="true"
                  :isLoading="dropdownLoading"
                  :rules="[
                    labelList[284].Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[284].Field_Alias,
                          certificateFormData[i].Certification_Name
                        )
                      : true,
                  ]"
                  :isRequired="
                    labelList[284].Mandatory_Field?.toLowerCase() === 'yes'
                  "
                />
                <v-text-field
                  v-else
                  v-model="certificateFormData[i].Certification_Name"
                  :rules="[
                    labelList[284].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[284].Field_Alias}`,
                          certificateFormData[i].Certification_Name
                        )
                      : true,
                    certificateFormData[i].Certification_Name
                      ? validateWithRulesAndReturnMessages(
                          certificateFormData[i].Certification_Name,
                          'certificationName',
                          `${labelList[284].Field_Alias}`
                        )
                      : true,
                  ]"
                  variant="solo"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    <span>{{ labelList[284].Field_Alias }}</span>
                    <span
                      v-if="labelList[284].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="labelList[285].Field_Visiblity == 'Yes'"
                cols="12"
                md="6"
                class="mt-n5"
              >
                <div class="text-caption">
                  {{ labelList[285].Field_Alias
                  }}<span
                    style="color: red"
                    v-if="labelList[285].Mandatory_Field == 'Yes'"
                    >*</span
                  >
                  <v-tooltip
                    v-if="!selectedCandidateDOB"
                    location="top"
                    text="Date of birth is required to add  certification dates."
                  >
                    <template v-slot:activator="{ props }">
                      <span v-bind="props" class="pa-1 cursor-not-allowed">
                        <v-icon
                          size="small"
                          color="info"
                          class="fas fa-info-circle"
                        ></v-icon>
                      </span>
                    </template>
                  </v-tooltip>
                </div>
                <v-menu
                  v-model="certificateFormData[i].showReceivedDate"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      :value="certificateFormData[i].receivedDateFormatted"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[
                        labelList[285].Mandatory_Field == 'Yes'
                          ? required(
                              labelList[285].Field_Alias,
                              certificateFormData[i].receivedDateFormatted
                            )
                          : true,
                      ]"
                      readonly
                      v-bind="props"
                      :disabled="!selectedCandidateDOB"
                      variant="solo"
                      ref="certificateReceivedOn"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    @update:modelValue="onDateChange(i, $event)"
                    v-model="certificateFormData[i].Received_Date"
                    :min="selectedEmpDobDate"
                    :max="currentDate"
                  />
                </v-menu>
              </v-col>
              <v-col
                v-if="labelList[286].Field_Visiblity == 'Yes'"
                cols="12"
                md="6"
              >
                <v-text-field
                  v-model="certificateFormData[i].Certificate_Received_From"
                  :rules="[
                    labelList[286].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[286].Field_Alias}`,
                          certificateFormData[i].Certificate_Received_From
                        )
                      : true,
                    certificateFormData[i].Certificate_Received_From
                      ? validateWithRulesAndReturnMessages(
                          certificateFormData[i].Certificate_Received_From,
                          'receivedFrom',
                          `${labelList[286].Field_Alias}`
                        )
                      : true,
                  ]"
                  variant="solo"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    <span>{{ labelList[286].Field_Alias }}</span>
                    <span
                      v-if="labelList[286].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    ></template
                  >
                </v-text-field>
              </v-col>
              <v-col
                v-if="labelList[337]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                md="6"
              >
                <v-text-field
                  v-model="certificateFormData[i].Ranking"
                  :rules="[
                    labelList[337]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[337]?.Field_Alias,
                          certificateFormData[i].Ranking
                        )
                      : true,
                    validateWithRulesAndReturnMessages(
                      certificateFormData[i].Ranking,
                      'skillCategory',
                      labelList[337]?.Field_Alias
                    ),
                  ]"
                  variant="solo"
                  clearable
                  :ref="`ranking${i}`"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList[337]?.Field_Alias }}
                    <span
                      v-if="
                        labelList[337]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field></v-col
              >
            </v-row>
          </v-col>
          <v-col cols="1" class="d-flex justify-center">
            <v-hover>
              <template v-slot:default="{ isHovering, props }">
                <v-icon
                  v-bind="props"
                  :color="isHovering ? 'red' : 'grey'"
                  @click="onDelate(i)"
                  >fas fa-trash</v-icon
                >
              </template>
            </v-hover></v-col
          >
        </v-row>
        <v-divider class="my-4"></v-divider>
      </div>
    </v-form>
  </div>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
export default {
  name: "AddEditCertificationDetails",
  props: {
    certificationDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
    dateFormat: {
      type: String,
      default: "",
    },
    jobPostId: {
      type: Number,
      default: 0,
    },
    isUserLogedIn: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    CustomSelect,
  },
  mixins: [validationRules],
  data() {
    return {
      certificateFormData: [],
      isFormDirty: false,
      receivedDateErrorMsg: [],
      dropdownLoading: false,
      certificationList: [],
      hasData: false,
    };
  },

  watch: {
    certificationDetails: {
      immediate: true,
      handler(newVal) {
        this.checkCandidateDetails(newVal);
      },
    },
    certificateFormData: {
      deep: true,
      handler(newVal) {
        if (newVal?.length)
          this.$emit("update-certification-details", newVal, "certificate");
      },
    },
  },
  emits: ["update-certification-details"],

  computed: {
    orgDateFormat() {
      let format = this.dateFormat
        ? this.dateFormat
        : this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    selectedEmpDobDate() {
      if (
        this.selectedCandidateDOB &&
        this.selectedCandidateDOB !== "0000-00-00"
      ) {
        return moment(this.selectedCandidateDOB).format("YYYY-MM-DD");
      } else return null;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.getDropdownDetails();
  },

  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },
    async getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientAO, apolloClientA },
      } = vm.$apolloProvider;

      const selectedClient =
        vm.jobPostId || vm.isUserLogedIn ? apolloClientAO : apolloClientA;
      await vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: selectedClient,
          payload: {
            formId: 16,
            key: ["Required_Certification"],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "required_certification") {
                vm.certificationList = item.data;
              }
            });
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Job Candidates",
        isListError: false,
      });
    },
    checkCandidateDetails(newVal) {
      this.hasData = newVal;
      if (
        this.certificationDetails &&
        Object.keys(this.certificationDetails).length > 0
      ) {
        this.certificateFormData = JSON.parse(
          JSON.stringify(this.certificationDetails)
        );
        for (let i = 0; i < this.certificateFormData.length; i++) {
          if (this.certificateFormData[i]["Received_Date"]) {
            this.certificateFormData[i]["Received_Date"] = moment(
              this.certificateFormData[i]["Received_Date"],
              "YYYY/MM/DD"
            )._d;
            this.certificateFormData[i]["receivedDateFormatted"] =
              this.convertedDate(this.certificateFormData[i].Received_Date);
            this.certificateFormData[i]["showReceivedDate"] = false;
          }
        }
      } else {
        this.certificateFormData = [];
      }
    },
    onAddNew() {
      this.certificateFormData.push({
        Certification_Name: "",
        Received_Date: null,
        Certificate_Received_From: "",
        showReceivedDate: false,
        receivedDateFormatted: "",
        Ranking: "",
      });
    },
    onDateChange(index, selectedDate) {
      this.isFormDirty = true;
      const formattedDate = this.convertedDate(selectedDate);
      this.certificateFormData[index]["receivedDateFormatted"] = formattedDate;
      this.certificateFormData[index]["showReceivedDate"] = false;
    },
    onDelate(index) {
      this.certificateFormData.splice(index, 1);
    },
    convertedDate(selectedDate) {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      const formattedDate = selectedDate
        ? moment(selectedDate).format(orgDateFormat)
        : "";
      return formattedDate;
    },
    async validateCertificateDetails() {
      if (this.certificateFormData.length > 0) {
        const { valid } = await this.$refs.addEditCertificateForm.validate();
        if (valid) {
          this.receivedDateErrorMsg = [];
          let certificateFormData = this.certificateFormData;
          for (let i = 0; i < this.certificateFormData.length; i++) {
            if (certificateFormData[i].Received_Date) {
              certificateFormData[i]["Received_Date"] = moment(
                certificateFormData[i]["Received_Date"]
              ).format("YYYY/MM/DD");
            } else {
              certificateFormData[i].Received_Date = null;
            }
          }

          return certificateFormData;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
  },
};
</script>
