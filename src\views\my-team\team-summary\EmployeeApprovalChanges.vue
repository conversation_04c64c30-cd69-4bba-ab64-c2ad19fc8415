<template>
  <div>
    <v-dialog
      :model-value="modelValue"
      fullscreen
      scrollable
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-toolbar>
          <CustomSelect
            v-model="carouselIndex"
            class="mx-2 mt-5"
            :items="sectionTitles"
            itemTitle="title"
            itemValue="value"
            label="Changes"
            :isRequired="true"
            :itemSelected="carouselIndex"
            :disableBreak="true"
          ></CustomSelect>
          <v-spacer></v-spacer>
          <v-btn
            v-if="!isApprovalHistory"
            color="success"
            @click="$emit('approve-reject', 'approve')"
            ><v-icon size="18" class="mt-1 mr-1">fas fa-check-circle</v-icon
            ><span v-if="!isMobileView">Approve</span></v-btn
          >
          <v-btn
            v-if="!isApprovalHistory"
            color="error"
            @click="$emit('approve-reject', 'reject')"
            ><v-icon size="18" class="mt-1 mr-1">fas fa-times-circle</v-icon
            ><span v-if="!isMobileView">Reject</span></v-btn
          >
          <v-btn icon @click="$emit('update:modelValue', false)">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-toolbar>
        <v-carousel
          v-model="carouselIndex"
          :hide-delimiters="!isMobileView"
          progress="primary"
          :show-arrows="!isMobileView ? 'hover' : false"
          height="90vh"
          class="pa-2"
        >
          <v-carousel-item
            v-for="(section, index) in dynamicSections"
            :key="index"
          >
            <div style="height: 100%; overflow-y: auto">
              <component
                :is="section.component"
                :ref="`section-${index}`"
                :[section.newDataProp]="section.data.New_Data"
                :[section.oldDataProp]="section.data.Old_Data"
                :isApprovalView="true"
              />
            </div>
          </v-carousel-item>
        </v-carousel>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import PersonalDetails from "../../employee-profile/profile-details/personal/details/PersonalDetails.vue";
import JobDetails from "../../employee-profile/profile-details/job/details/JobDetails.vue";
import DependentDetails from "../../employee-profile/profile-details/personal/dependent/DependentDetails.vue";
import PassportDetails from "../../employee-profile/profile-details/personal/passport/PassportDetails.vue";
import DrivingLicenseDetails from "../../employee-profile/profile-details/personal/license/DrivingLicenseDetails.vue";
import ContactInfo from "../../employee-profile/profile-details/contact/ContactInfo.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "EmployeeApprovalChanges",
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    instanceData: {
      type: Object,
      default: () => ({}),
    },
    isApprovalHistory: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    PersonalDetails,
    JobDetails,
    DependentDetails,
    PassportDetails,
    DrivingLicenseDetails,
    ContactInfo,
    CustomSelect,
  },
  data() {
    return {
      carouselIndex: 0,
      sectionTitles: [],
      dynamicSections: [],
      multipleDataSections: ["emp_dependent"],
      sectionConfig: {
        emp_personal_info: {
          component: "PersonalDetails",
          title: "Personal Details",
          newDataProp: "personalDetailsData",
          oldDataProp: "oldPersonalDetailsData",
        },
        emp_job: {
          component: "JobDetails",
          title: "Job Details",
          newDataProp: "jobDetailsData",
          oldDataProp: "oldJobDetailsData",
        },
        emp_dependent: {
          component: "DependentDetails",
          title: "Dependent Details",
          newDataProp: "dependentDetailsData",
          oldDataProp: "oldDependentDetailsData",
        },
        emp_passport: {
          component: "PassportDetails",
          title: "Passport Details",
          newDataProp: "passportDetailsData",
          oldDataProp: "oldPassportDetailsData",
        },
        emp_drivinglicense: {
          component: "DrivingLicenseDetails",
          title: "Driving License Details",
          newDataProp: "licenseDetailsData",
          oldDataProp: "oldDrivingLicenseDetailsData",
        },
        contact_details: {
          component: "ContactInfo",
          title: "Contact Details",
          newDataProp: "contactDetailsData",
          oldDataProp: "oldContactDetailsData",
        },
      },
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    this.initializeSections();
  },
  methods: {
    initializeSections() {
      if (!this.instanceData?.Request_Items?.length) return;

      this.instanceData.Request_Items.forEach((item) => {
        const tableName = item.Table_Name?.toLowerCase();
        const config = this.sectionConfig[tableName];
        if (!config) return;

        const existingSection = this.dynamicSections.find(
          (section) => section.component === config.component
        );

        const newDataParsed = safeParse(
          item.New_Data,
          this.multipleDataSections.includes(tableName) ? [] : {}
        );
        const oldDataParsed = safeParse(
          item.Old_Data,
          this.multipleDataSections.includes(tableName) ? [] : {}
        );

        if (this.multipleDataSections.includes(tableName) && existingSection) {
          const changeType = item.Change_Type?.toLowerCase();
          if (changeType === "add") {
            const lastData = newDataParsed[newDataParsed.length - 1];
            existingSection.data.New_Data.push(lastData);
          } else if (changeType === "edit") {
            const updatedId = item.Reference_Id;
            const updatedData = newDataParsed.find(
              (data) => data.Dependent_Id === updatedId
            );
            if (updatedData) {
              existingSection.data.New_Data.forEach((data) => {
                if (data.Dependent_Id === updatedId) {
                  Object.assign(data, updatedData);
                }
              });
            }
          }
        } else {
          if (!this.sectionTitles.some((t) => t.title === config.title)) {
            this.sectionTitles.push({
              title: config.title,
              value: this.dynamicSections.length,
            });
          }

          this.dynamicSections.push({
            component: config.component,
            title: config.title,
            newDataProp: config.newDataProp,
            oldDataProp: config.oldDataProp,
            data: {
              New_Data: newDataParsed,
              Old_Data: oldDataParsed,
            },
          });
        }
      });

      function safeParse(str, fallback) {
        try {
          return JSON.parse(str || "");
        } catch {
          return fallback;
        }
      }
    },
  },
};
</script>

<style scoped>
:deep(.v-window__controls .v-btn--icon) {
  opacity: 0.5;
  transition: opacity 0.2s;
  background-color: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  min-width: 0 !important;
  width: auto !important;
  height: auto !important;
  border-radius: 0 !important;
}

:deep(.v-window__controls .v-btn--icon:hover),
:deep(.v-window__controls .v-btn--icon:focus) {
  opacity: 1;
  background-color: transparent !important;
  box-shadow: none !important;
}

:deep(.v-window__controls .v-btn--icon .v-icon) {
  color: #260029 !important; /* Or your preferred color */
  font-size: 2rem; /* Optional: adjust icon size */
}
</style>
