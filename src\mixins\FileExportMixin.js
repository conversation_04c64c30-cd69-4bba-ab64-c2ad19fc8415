import { Workbook } from "exceljs";
import * as fs from "file-saver";

function addListValidation(
  worksheet,
  headerText,
  validationList,
  validationWorkSheet,
  type
) {
  // Iterate over the columns to find the column with the specified header text
  worksheet.columns.forEach(function (column) {
    if (column.header === headerText) {
      let columnLetter = column.letter;
      // If the header text is found, set data validation for the column
      column.eachCell({ includeEmpty: true }, function (cell, rowNumber) {
        if (rowNumber > 1) {
          // Skip the header row
          let validationValues = [];
          if (type != "Direct") {
            validationValues = validationList.map(
              (obj) => obj[Object.keys(obj)[1]]
            );
          } else {
            validationValues = validationList;
          }

          // drop down validation initiation
          let dropdownListValidation;
          let validationValuesLength = validationValues.length;
          // check dropdown list is greater than 0
          if (validationValuesLength > 0) {
            // if dropdown list having 20 items then form the validations from 1 row to until the length of the dropdown list (ex: $A$1:$A$20)
            dropdownListValidation =
              "$" +
              columnLetter +
              "$1:$" +
              columnLetter +
              "$" +
              validationValuesLength;
          } else {
            // when dropdownList length is zero, we have to set manual data-validation-range(ex: A1:A1) to present empty selection box in excel dropdown field or its dynamic based on items length
            dropdownListValidation = columnLetter + "1:" + columnLetter + "1";
          }

          // filling the dropdown value in each cell
          for (let l = 1; l <= validationValuesLength; l++) {
            validationWorkSheet.getCell(columnLetter + l).value =
              validationValues[l - 1];
          }

          // combine the validation range with sheet name
          let validationRule = "ValidationSheet!" + dropdownListValidation;

          cell.dataValidation = {
            type: "list",
            allowBlank: true,
            formulae: [validationRule],
            showErrorMessage: true,
            errorStyle: "error",
            error: "The value Valid",
            prompt: "Please select a valid value from the list",
          };
        }
      });
    }
  });
}

export default {
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    // export xlsx file
    exportExcelFile(fileOptions) {
      // file options that is passed to this function
      const {
        sheetName,
        header,
        fileName,
        fileExportData,
        requiredHeaders = [],
        headerRowNumber = 1,
        headerColor = "92CDDC",
        rowHighlightProps = {
          key: "",
          value: "",
        },
        columnHighlightProps = {
          type: null,
        },
        xSplit = 2,
      } = fileOptions;
      // Create workbook and worksheet
      let workbook = new Workbook();
      let worksheet = workbook.addWorksheet(sheetName);

      worksheet.columns = header;
      // assign data in each row
      if (fileExportData && fileExportData.length > 0) {
        worksheet.addRows(fileExportData);
        for (let rowIndex = 1; rowIndex <= fileExportData.length; rowIndex++) {
          const row = worksheet.getRow(rowIndex);
          row.eachCell((cell) => {
            if (rowIndex === headerRowNumber) {
              let isRequired = requiredHeaders.includes(cell.html);
              if (sheetName == "Bonus sheet") {
                if (requiredHeaders.includes(cell.html)) {
                  cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "FF1744" },
                  };
                  cell.border = {
                    top: { style: "thin" },
                    left: { style: "thin" },
                    bottom: { style: "thin" },
                    right: { style: "thin" },
                  };
                }
              } else if (sheetName == "TDS history sheet") {
                if (requiredHeaders.includes(cell.html)) {
                  cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "FF1744" },
                  };
                  cell.border = {
                    top: { style: "thin" },
                    left: { style: "thin" },
                    bottom: { style: "thin" },
                    right: { style: "thin" },
                  };
                }
              } else if (sheetName == "Activity sheet") {
                if (requiredHeaders.includes(cell.html)) {
                  cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "FF1744" },
                  };
                  cell.border = {
                    top: { style: "thin" },
                    left: { style: "thin" },
                    bottom: { style: "thin" },
                    right: { style: "thin" },
                  };
                }
              } else {
                cell.fill = {
                  type: "pattern",
                  pattern: "solid",
                  fgColor: { argb: isRequired ? "FF1744" : headerColor },
                };
                cell.alignment = { vertical: "middle", horizontal: "center" };
                cell.font = {
                  name: "Calibri",
                  size: 11,
                  bold: true,
                };
                cell.border = {
                  top: { style: "thin" },
                  left: { style: "thin" },
                  bottom: { style: "thin" },
                  right: { style: "thin" },
                };
              }
            } else {
              cell.alignment = { wrapText: true };
              const lengths = row.values.map((v) => v.toString().length);
              const maxLength = Math.max(
                ...lengths.filter((v) => typeof v === "number")
              );
              if (maxLength > 60) {
                row.height = maxLength / 3;
              }
            }
          });
        }
      }
      // freeze column and row
      worksheet.views = [
        {
          state: "frozen",
          xSplit: xSplit,
          ySplit: headerRowNumber,
        },
      ];
      // highlight row based on passed cell key & value
      if (rowHighlightProps && rowHighlightProps.key) {
        let rowKey = rowHighlightProps.key;
        fileExportData.map((val, index) => {
          let indexPlusOne = index + 2;
          if (val[rowKey] === rowHighlightProps.value) {
            worksheet.getRow(indexPlusOne).fill = {
              type: "pattern",
              pattern: "solid",
              fgColor: { argb: "ffe4c9" },
            };
            worksheet.getRow(indexPlusOne).border = {
              top: { style: "thin" },
              left: { style: "thin" },
              bottom: { style: "thin" },
              right: { style: "thin" },
            };
          }
        });
      }
      // auto width of column based on value length
      worksheet.columns.forEach((column) => {
        const lengths = column.values.map((v) => v.toString().length);
        const maxLength = Math.max(
          ...lengths.filter((v) => typeof v === "number")
        );
        column.width = maxLength > 50 ? 54 : maxLength + 4;
        column.numFmt = "@"; // @ is the code for treating cells as text
      });
      // set cell background based on column number and conditions passed
      if (columnHighlightProps.type) {
        // sheet create
        let validationWorkSheet = workbook.addWorksheet("ValidationSheet");
        if (columnHighlightProps.type === "Employee Import") {
          if (columnHighlightProps.designation.length) {
            addListValidation(
              worksheet,
              "Designation",
              columnHighlightProps.designation,
              validationWorkSheet
            );
          }
          if (columnHighlightProps.department.length) {
            addListValidation(
              worksheet,
              "Department",
              columnHighlightProps.department,
              validationWorkSheet
            );
          }
          if (columnHighlightProps.workschedule.length) {
            addListValidation(
              worksheet,
              "WorkSchedule",
              columnHighlightProps.workschedule,
              validationWorkSheet
            );
          }
          if (columnHighlightProps.employeeType.length) {
            addListValidation(
              worksheet,
              "EmployeeType",
              columnHighlightProps.employeeType,
              validationWorkSheet
            );
          }
          if (columnHighlightProps.location.length) {
            addListValidation(
              worksheet,
              "Location",
              columnHighlightProps.location,
              validationWorkSheet
            );
          }
          if (columnHighlightProps.manager.length) {
            addListValidation(
              worksheet,
              "Manager",
              columnHighlightProps.manager,
              validationWorkSheet
            );
          }
        } else if (columnHighlightProps.type === "Bonus Import") {
          if (columnHighlightProps.Allowance_Restrict_Bonus.length) {
            addListValidation(
              worksheet,
              "Allowance Restrict Bonus",
              columnHighlightProps.Allowance_Restrict_Bonus,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Allowance_Name.length) {
            addListValidation(
              worksheet,
              "Allowance Name",
              columnHighlightProps.Allowance_Name,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Bonus_Type.length) {
            addListValidation(
              worksheet,
              "Bonus Type",
              columnHighlightProps.Bonus_Type,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Forward_To.length) {
            addListValidation(
              worksheet,
              "Forward To",
              columnHighlightProps.Forward_To,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Status.length) {
            addListValidation(
              worksheet,
              "Status",
              columnHighlightProps.Status,
              validationWorkSheet,
              "Direct"
            );
          }
        } else if (columnHighlightProps.type === "Tax Relief") {
          if (columnHighlightProps.months.length) {
            addListValidation(
              worksheet,
              "Month",
              columnHighlightProps.months,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.category.length) {
            addListValidation(
              worksheet,
              "Tax Relief Category",
              columnHighlightProps.category,
              validationWorkSheet,
              "Direct"
            );
          }
        } else if (columnHighlightProps.type === "Activity Import") {
          if (columnHighlightProps.billable.length) {
            addListValidation(
              worksheet,
              "Billable",
              columnHighlightProps.billable,
              validationWorkSheet,
              "Direct"
            );
          }
        } else if (columnHighlightProps.type === "Employee Report Import") {
          if (columnHighlightProps.Gender_Orientation.length) {
            addListValidation(
              worksheet,
              this.labelList[208].Field_Alias,
              columnHighlightProps.Gender_Orientation,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Pronoun.length) {
            addListValidation(
              worksheet,
              this.labelList[207].Field_Alias,
              columnHighlightProps.Pronoun,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Designation.length) {
            addListValidation(
              worksheet,
              "Designation",
              columnHighlightProps.Designation,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Gender.length) {
            addListValidation(
              worksheet,
              "Gender",
              columnHighlightProps.Gender,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Marital_Status.length) {
            addListValidation(
              worksheet,
              "Marital Status",
              columnHighlightProps.Marital_Status,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Tax_Code.length) {
            addListValidation(
              worksheet,
              this.labelList["188"].Field_Alias,
              columnHighlightProps.Tax_Code,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Religion.length) {
            addListValidation(
              worksheet,
              this.labelList[296].Field_Alias,
              columnHighlightProps.Religion,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Business_Unit.length) {
            addListValidation(
              worksheet,
              "Business Unit",
              columnHighlightProps.Business_Unit,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Department.length) {
            addListValidation(
              worksheet,
              "Department",
              columnHighlightProps.Department,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Employee_Type.length) {
            addListValidation(
              worksheet,
              "Employee Type",
              columnHighlightProps.Employee_Type,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Work_Schedule.length) {
            addListValidation(
              worksheet,
              "Work Schedule",
              columnHighlightProps.Work_Schedule,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Profession.length) {
            addListValidation(
              worksheet,
              "Profession",
              columnHighlightProps.Profession,
              validationWorkSheet,
              "Direct"
            );
          }

          if (columnHighlightProps.Location.length) {
            addListValidation(
              worksheet,
              "Location",
              columnHighlightProps.Location,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Service_Provider.length) {
            addListValidation(
              worksheet,
              "Service Provider",
              columnHighlightProps.Service_Provider,
              validationWorkSheet,
              "Direct"
            );
          }

          if (columnHighlightProps.Role.length) {
            addListValidation(
              worksheet,
              "Role (Access Rights)",
              columnHighlightProps.Role,
              validationWorkSheet,
              "Direct"
            );
          }

          if (columnHighlightProps.Blood_Group.length) {
            addListValidation(
              worksheet,
              "Blood Group",
              columnHighlightProps.Blood_Group,
              validationWorkSheet,
              "Direct"
            );
          }

          if (columnHighlightProps.Sign_In_Option.length) {
            addListValidation(
              worksheet,
              "Sign-In Option",
              columnHighlightProps.Sign_In_Option,
              validationWorkSheet,
              "Direct"
            );
          }

          if (columnHighlightProps.Permanent_Address_City_Municipality.length) {
            addListValidation(
              worksheet,
              "City/Municipality",
              columnHighlightProps.Permanent_Address_City_Municipality,
              validationWorkSheet,
              "Direct"
            );
          }

          if (columnHighlightProps.Current_Address_City_Municipality.length) {
            addListValidation(
              worksheet,
              "Current Address - City/Municipality",
              columnHighlightProps.Current_Address_City_Municipality,
              validationWorkSheet,
              "Direct"
            );
          }

          if (columnHighlightProps.Office_Address_City_Municipality.length) {
            addListValidation(
              worksheet,
              "City/Municipality",
              columnHighlightProps.Office_Address_City_Municipality,
              validationWorkSheet,
              "Direct"
            );
          }

          if (columnHighlightProps.Manager.length) {
            addListValidation(
              worksheet,
              "Manager",
              columnHighlightProps.Manager,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Employee_Professionals.length) {
            addListValidation(
              worksheet,
              "Employee Professionals",
              columnHighlightProps.Employee_Professionals,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Company_Name.length) {
            addListValidation(
              worksheet,
              "Company Name",
              columnHighlightProps.Company_Name,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Salutation.length) {
            addListValidation(
              worksheet,
              "Salutation",
              columnHighlightProps.Salutation,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Marital_Status.length) {
            addListValidation(
              worksheet,
              "Marital Status",
              columnHighlightProps.Marital_Status,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Nationality.length) {
            addListValidation(
              worksheet,
              "Nationality",
              columnHighlightProps.Nationality,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Permanent_Address_Country.length) {
            addListValidation(
              worksheet,
              "Permanent Address - Country",
              columnHighlightProps.Permanent_Address_Country,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Current_Address_Country.length) {
            addListValidation(
              worksheet,
              "Current Address - Country",
              columnHighlightProps.Current_Address_Country,
              validationWorkSheet,
              "Direct"
            );
          }
          if (columnHighlightProps.Office_Address_Country.length) {
            addListValidation(
              worksheet,
              "Office Address - Country",
              columnHighlightProps.Office_Address_Country,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.PF_Policy_No &&
            columnHighlightProps.PF_Policy_No.length
          ) {
            addListValidation(
              worksheet,
              "PF Policy No",
              columnHighlightProps.PF_Policy_No,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.Common_Toggle &&
            columnHighlightProps.Common_Toggle.length
          ) {
            addListValidation(
              worksheet,
              "Is Manager",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              "Smoker",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              "Is Recruiter",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              "Allow User Sign-In",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              "Served in Military",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              "Disabled",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              "Confirmed",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              "Attendance Enforced Payment",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              "Commission Based Employee",
              columnHighlightProps.Common_Toggle,
              validationWorkSheet,
              "Direct"
            );
          }
        } else if (
          columnHighlightProps.type === "Department Hierarchy Import"
        ) {
          if (columnHighlightProps.status.length) {
            addListValidation(
              worksheet,
              "Status",
              columnHighlightProps.status,
              validationWorkSheet,
              "Direct"
            );
          }
        } else if (
          columnHighlightProps.type &&
          columnHighlightProps.type.toLowerCase() === "job candidate import" &&
          this.labelList
        ) {
          if (
            columnHighlightProps.genderOrientation &&
            columnHighlightProps.genderOrientation.length
          ) {
            addListValidation(
              worksheet,
              "Gender Orientation",
              columnHighlightProps.genderOrientation,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.yearOfStart &&
            columnHighlightProps.yearOfStart.length
          ) {
            addListValidation(
              worksheet,
              "Education - Year of Start",
              columnHighlightProps.yearOfStart,
              validationWorkSheet,
              "Direct"
            );
            addListValidation(
              worksheet,
              `Education - ${this.labelList["179"]?.Field_Alias}`,
              columnHighlightProps.yearOfStart,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.languageList &&
            columnHighlightProps.languageList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["325"]?.Field_Alias,
              columnHighlightProps.languageList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.languageList &&
            columnHighlightProps.languageList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["383"]?.Field_Alias,
              columnHighlightProps.languageList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.genderList &&
            columnHighlightProps.genderList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["267"]?.Field_Alias,
              columnHighlightProps.genderList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.bloodGroupList &&
            columnHighlightProps.bloodGroupList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["262"]?.Field_Alias,
              columnHighlightProps.bloodGroupList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.maritalStatusList &&
            columnHighlightProps.maritalStatusList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["270"]?.Field_Alias,
              columnHighlightProps.maritalStatusList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.nationalityList &&
            columnHighlightProps.nationalityList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["271"]?.Field_Alias,
              columnHighlightProps.nationalityList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.workPermitList &&
            columnHighlightProps.workPermitList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["265"]?.Field_Alias,
              columnHighlightProps.workPermitList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.currencyList &&
            columnHighlightProps.currencyList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["289"]?.Field_Alias,
              columnHighlightProps.currencyList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.sourceApplicationList &&
            columnHighlightProps.sourceApplicationList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["287"]?.Field_Alias,
              columnHighlightProps.sourceApplicationList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.jobPostList &&
            columnHighlightProps.jobPostList.length
          ) {
            addListValidation(
              worksheet,
              "Job Title",
              columnHighlightProps.jobPostList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.cityList &&
            columnHighlightProps.cityList.length
          ) {
            addListValidation(
              worksheet,
              this.labelList["143"]?.Field_Alias,
              columnHighlightProps.cityList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.countryList &&
            columnHighlightProps.countryList.length
          ) {
            addListValidation(
              worksheet,
              "Country",
              columnHighlightProps.countryList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.pronounList &&
            columnHighlightProps.pronounList.length
          ) {
            addListValidation(
              worksheet,
              `${this.labelList["209"]?.Field_Alias}`,
              columnHighlightProps.pronounList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.languageProficiencyList &&
            columnHighlightProps.languageProficiencyList.length
          ) {
            addListValidation(
              worksheet,
              `${this.labelList["358"]?.Field_Alias}`,
              columnHighlightProps.languageProficiencyList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.genderOrientationList &&
            columnHighlightProps.genderOrientationList.length
          ) {
            addListValidation(
              worksheet,
              `${this.labelList["210"]?.Field_Alias}`,
              columnHighlightProps.genderOrientationList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.genderIdentityList &&
            columnHighlightProps.genderIdentityList.length
          ) {
            addListValidation(
              worksheet,
              `${this.labelList["329"]?.Field_Alias}`,
              columnHighlightProps.genderIdentityList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.genderExpressionList &&
            columnHighlightProps.genderExpressionList.length
          ) {
            addListValidation(
              worksheet,
              `${this.labelList["330"]?.Field_Alias}`,
              columnHighlightProps.genderExpressionList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.certificationList &&
            columnHighlightProps.certificationList.length
          ) {
            addListValidation(
              worksheet,
              `Certification - ${this.labelList["284"]?.Field_Alias}`,
              columnHighlightProps.certificationList,
              validationWorkSheet,
              "Direct"
            );
          }
          const targetIndices = [356, 357, 331];
          targetIndices.forEach((index) => {
            if (
              columnHighlightProps.toggleButtonList &&
              columnHighlightProps.toggleButtonList.length
            ) {
              addListValidation(
                worksheet,
                `${this.labelList[index]?.Field_Alias}`,
                columnHighlightProps.toggleButtonList,
                validationWorkSheet,
                "Direct"
              );
            }
          });
          if (
            columnHighlightProps.courseList &&
            columnHighlightProps.courseList.length
          ) {
            addListValidation(
              worksheet,
              `Education - ${this.labelList["128"]?.Field_Alias}`,
              columnHighlightProps.courseList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.specializationList &&
            columnHighlightProps.specializationList.length
          ) {
            addListValidation(
              worksheet,
              `Education - ${this.labelList["174"]?.Field_Alias}`,
              columnHighlightProps.specializationList,
              validationWorkSheet,
              "Direct"
            );
          }
          if (
            columnHighlightProps.instituteList &&
            columnHighlightProps.instituteList.length
          ) {
            addListValidation(
              worksheet,
              `Education - ${this.labelList["175"]?.Field_Alias}`,
              columnHighlightProps.instituteList,
              validationWorkSheet,
              "Direct"
            );
          }
        }
        // hide the sheet
        validationWorkSheet.state = "hidden";
      }
      // generate excel file with given file name
      workbook.xlsx.writeBuffer().then((data) => {
        let blob = new Blob([data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        fs.saveAs(blob, fileName);
      });
    },

    async exportMultipleSheets(fileOptions) {
      let { fileName, sheets, data, headers } = fileOptions;

      const workbook = new Workbook();
      let excelSheets = [];
      for (let i = 0; i < sheets.length; i++) {
        excelSheets[i] = workbook.addWorksheet(sheets[i]);
        excelSheets[i].columns = headers[i];
        excelSheets[i].getRow(1).eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "92CDDC" }, // Yellow background
          };
        });
        excelSheets[i].addRows(data[i]);
      }

      // Write workbook to a file
      workbook.xlsx.writeBuffer().then((data) => {
        let blob = new Blob([data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        fs.saveAs(blob, fileName);
      });
    },

    // export CSV file
    exportCSV(fileOptions) {
      // file options that is passed to this function
      const { sheetName, header, fileName, fileExportData } = fileOptions;
      // Create workbook and worksheet
      let workbook = new Workbook();
      let worksheet = workbook.addWorksheet(sheetName);
      worksheet.columns = header;
      // assign data in each row
      if (fileExportData && fileExportData.length > 0) {
        worksheet.addRows(fileExportData);
      }
      // generate excel file with given file name
      workbook.csv.writeBuffer().then((data) => {
        let blob = new Blob([data], {
          type: "text/csv;charset=utf-8",
        });
        fs.saveAs(blob, fileName);
      });
    },
  },
};
