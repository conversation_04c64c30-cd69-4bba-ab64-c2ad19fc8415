<template>
  <div v-if="isMounted">
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    ></AppTopBarTab>
    <v-container fluid class="attendance-configuration-container">
      <div v-if="AttendanceConfigurationFormAccess">
        <div v-if="listLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          icon-name="fas fa-redo-alt"
          :button-text="showRetryBtn ? 'Retry' : ''"
          @button-click="refetchPreApprovalList()"
        >
        </AppFetchErrorScreen>
        <section v-else class="mt-8">
          <div>
            <v-card
              class="py-9 rounded-lg rounded-lg"
              :class="isMobileView ? '' : 'px-5'"
              min-height="500"
              elevation="5"
            >
              <v-card-text>
                <v-form ref="attendanceConfiguration">
                  <v-row class="d-flex justify-space-between mb-4">
                    <div class="d-flex align-center">
                      <v-progress-circular
                        model-value="100"
                        color="secondary"
                        :size="22"
                        class="mr-1"
                      ></v-progress-circular>
                      <span class="text-h6 text-grey-darken-1 font-weight-bold"
                        >Attendance Regularization</span
                      >
                    </div>

                    <v-avatar
                      v-if="!isEdit && AttendanceConfigurationFormAccess.update"
                      @click="isEdit = true"
                      :size="isMobileView ? '20' : '30'"
                      color="primary"
                      class="cursor-pointer"
                      :class="isMobileView ? 'ml-auto mt-2' : ''"
                    >
                      <v-icon color="white" :size="isMobileView ? '10' : '12'"
                        >fas fa-pencil-alt</v-icon
                      >
                    </v-avatar>

                    <div
                      v-if="isEdit"
                      class="d-flex align-center pa-1"
                      :class="isMobileView ? 'ml-auto' : ''"
                    >
                      <v-btn
                        rounded="lg"
                        variant="outlined"
                        color="secondary"
                        class="mr-2"
                        @click="closeEditForm()"
                      >
                        Cancel
                      </v-btn>
                      <div class="mt-2 mr-1">
                        <v-tooltip v-model="showToolTip" location="top">
                          <template v-slot:activator="{ props }">
                            <v-btn
                              v-bind="isFormDirty ? '' : props"
                              rounded="lg"
                              variant="elevated"
                              :color="
                                !isFormDirty ? 'grey-lighten-3' : 'primary'
                              "
                              class="mb-2"
                              @click="updateAttendanceConfiguration"
                              ><span class="px-2">Save</span></v-btn
                            >
                          </template>
                          <div v-if="!isFormDirty">
                            There are no changes to be updated
                          </div>
                        </v-tooltip>
                      </div>
                    </div>
                  </v-row>
                  <v-row>
                    <v-col
                      cols="12"
                      md="8"
                      :class="isMobileView ? 'd-flex flex-column' : 'd-flex'"
                    >
                      <span
                        class="text-subtitle-1 text-grey-darken-1"
                        :class="isEdit ? '' : 'mr-3'"
                        >Restrict attendance regularization to</span
                      >

                      <v-text-field
                        v-if="isEdit"
                        v-model.number="employeeRegularizationCutOffDays"
                        type="number"
                        variant="solo"
                        :class="isMobileView ? 'mt-2' : 'ml-2 mt-n2'"
                        :min="1"
                        :max="40"
                        :rules="[
                          numericRequiredValidation(
                            'This field',
                            employeeRegularizationCutOffDays
                          ),
                          numericValidation(
                            'This field',
                            employeeRegularizationCutOffDays
                          ),
                          minMaxNumberValidation(
                            'This field',
                            parseInt(employeeRegularizationCutOffDays),
                            1,
                            40
                          ),
                        ]"
                        style="
                          max-width: 120px !important;
                          min-width: 120px !important;
                        "
                        density="compact"
                        suffix="day(s)"
                        @update:model-value="
                          validateInput('employeeRegularizationCutOffDays')
                        "
                      ></v-text-field>

                      <span v-else class="text-subtitle-1 font-weight-regular">
                        {{
                          checkNullValue(
                            attendanceConfigurationData.employeeRegularizationCutOffDays
                          )
                        }}
                        day(s)
                      </span>
                      <span
                        v-if="advancePayroll == 'Yes'"
                        class="text-subtitle-1 text-grey-darken-1"
                        :class="isMobileView ? '' : 'ml-3'"
                        >after the attendance cut-off day.</span
                      >
                      <span
                        v-else
                        class="text-subtitle-1 text-grey-darken-1"
                        :class="isMobileView ? '' : 'ml-3'"
                        >after the last day of payroll.</span
                      >
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col
                      :class="isMobileView ? 'd-flex flex-column' : 'd-flex'"
                      cols="12"
                      md="8"
                    >
                      <span
                        class="text-subtitle-1 text-grey-darken-1"
                        :class="isEdit ? '' : 'mr-3'"
                      >
                        Restrict the number of regularization to
                      </span>

                      <v-text-field
                        v-if="isEdit"
                        v-model.number="employeeRegularizationRequestLimit"
                        :class="isMobileView ? 'mt-2' : 'ml-2 mt-n2'"
                        type="number"
                        :max="31"
                        variant="solo"
                        :rules="[
                          numericRequiredValidation(
                            'This field',
                            employeeRegularizationRequestLimit
                          ),
                          numericValidation(
                            'This field',
                            employeeRegularizationRequestLimit
                          ),
                          minMaxNumberValidation(
                            'This field',
                            parseInt(employeeRegularizationRequestLimit),
                            0,
                            31
                          ),
                        ]"
                        style="
                          max-width: 120px !important;
                          min-width: 120px !important;
                        "
                        density="compact"
                        @update:model-value="
                          validateInput('employeeRegularizationRequestLimit')
                        "
                      ></v-text-field>

                      <span v-else class="text-subtitle-1 font-weight-regular">
                        {{
                          checkNullValue(
                            attendanceConfigurationData.employeeRegularizationRequestLimit
                          )
                        }}
                      </span>
                      <span
                        class="text-subtitle-1 text-grey-darken-1"
                        :class="isMobileView ? '' : 'ml-3'"
                        >request(s) per month.</span
                      >
                      <v-tooltip v-model="show" location="top">
                        <template v-slot:activator="{ props }">
                          <v-icon
                            class="mt-2 ml-2"
                            v-bind="props"
                            size="x-small"
                            color="info"
                          >
                            fas fa-info-circle
                          </v-icon>
                        </template>
                        <div>
                          Each request allows the regularization for one day
                        </div>
                      </v-tooltip>
                    </v-col>
                  </v-row>
                  <v-row class="d-flex justify-space-between my-4">
                    <div class="d-flex align-center">
                      <v-progress-circular
                        model-value="100"
                        color="secondary"
                        :size="22"
                        class="mr-1"
                      ></v-progress-circular>
                      <span class="text-h6 text-grey-darken-1 font-weight-bold"
                        >Attendance Approval</span
                      >
                    </div>
                  </v-row>
                  <v-row>
                    <v-col
                      cols="12"
                      md="8"
                      :class="isMobileView ? 'd-flex flex-column' : 'd-flex'"
                    >
                      <span
                        class="text-subtitle-1 text-grey-darken-1"
                        :class="isEdit ? '' : 'mr-3'"
                      >
                        Restrict attendance approval for managers up to
                      </span>

                      <v-text-field
                        v-if="isEdit"
                        v-model.number="attendanceApprovalCutOffDaysForManager"
                        type="number"
                        :min="1"
                        :max="365"
                        :class="isMobileView ? 'mt-2' : 'ml-2 mt-n2'"
                        variant="solo"
                        :rules="[
                          numericRequiredValidation(
                            'This field',
                            attendanceApprovalCutOffDaysForManager
                          ),
                          numericValidation(
                            'This field',
                            attendanceApprovalCutOffDaysForManager
                          ),
                          minMaxNumberValidation(
                            'This field',
                            parseInt(attendanceApprovalCutOffDaysForManager),
                            1,
                            365
                          ),
                        ]"
                        style="
                          max-width: 120px !important;
                          min-width: 120px !important;
                        "
                        density="compact"
                        suffix="day(s)"
                        @update:model-value="
                          validateInput(
                            'attendanceApprovalCutOffDaysForManager'
                          )
                        "
                      ></v-text-field>

                      <span v-else class="text-subtitle-1 font-weight-regular">
                        {{
                          checkNullValue(
                            attendanceConfigurationData.attendanceApprovalCutOffDaysForManager
                          ) + " day(s)"
                        }}
                      </span>
                      <span
                        v-if="advancePayroll == 'Yes'"
                        class="text-subtitle-1 text-grey-darken-1"
                        :class="isMobileView ? '' : 'ml-3'"
                        >after the advance payroll cut-off day
                      </span>
                      <span
                        v-else
                        class="text-subtitle-1 text-grey-darken-1"
                        :class="isMobileView ? '' : 'ml-3'"
                        >after the last day of payroll.</span
                      >
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col
                      v-if="moreDetailsList.length > 0 && !isEdit"
                      cols="12"
                    >
                      <MoreDetails
                        :more-details-list="moreDetailsList"
                        :open-close-card="openMoreDetails"
                        @on-open-close="openMoreDetails = $event"
                      ></MoreDetails>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-card>
          </div>
          <AppWarningModal
            v-if="openConfirmationPopup"
            :open-modal="openConfirmationPopup"
            confirmation-heading="Are you sure to exit this form?"
            imgUrl="common/exit_form"
            @close-warning-modal="abortClose()"
            @accept-modal="acceptClose()"
          >
          </AppWarningModal>
        </section>
      </div>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import { getErrorCodes } from "@/helper.js";
import {
  VIEW_ATTENDANCE_CONFIGURATION,
  UPDATE_ATTENDANCE_CONFIGURATION,
} from "@/graphql/settings/core-hr/attendanceConfigurationQueries";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import { checkNullValue } from "@/helper.js";
export default {
  name: "AttendanceConfiguration",
  mixins: [validationRules],
  components: {
    MoreDetails,
  },
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      listLoading: true,
      show: false,
      currentTabItem: "tab-2",
      employeeRegularizationCutOffDays: 40,
      employeeRegularizationRequestLimit: 0,
      attendanceApprovalCutOffDaysForManager: 365,
      isLoading: false,
      isErrorInList: false,
      isEdit: false,
      showToolTip: false,
      isMounted: false,
      isFormDirty: false,
      openConfirmationPopup: false,
      attendanceConfigurationData: {},
      advancePayroll: "No",
    };
  },

  computed: {
    landedFormName() {
      return "Attendance Configuration";
    },
    AttendanceConfigurationFormAccess() {
      let accessFormName = this.landedFormName.replace(/\s/g, "-");
      accessFormName = accessFormName.toLowerCase();
      let attendanceConfigurationFormAccess = this.accessRights(accessFormName);
      if (
        attendanceConfigurationFormAccess &&
        attendanceConfigurationFormAccess.accessRights &&
        attendanceConfigurationFormAccess.accessRights["view"]
      ) {
        return attendanceConfigurationFormAccess.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    coreHRFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.coreHRFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    this.advancePayroll = this.$store.state.orgDetails.advancePayroll;
    this.isFormDirty = false;
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchAttendanceConfigurationDetails();
    this.isMounted = true;
    this.isLoading = false;
  },
  methods: {
    checkNullValue,
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values

      const updatedByName =
          this.attendanceConfigurationData.updatedByEmployeeName,
        updatedOn = this.formatDate(
          new Date(this.attendanceConfigurationData.updatedOn + ".000Z")
        );

      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.coreHRFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/core-hr/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + clickedForm.url;
        }
      }
    },
    closeEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    acceptClose() {
      this.attendanceApprovalCutOffDaysForManager =
        this.attendanceConfigurationData.attendanceApprovalCutOffDaysForManager;
      this.employeeRegularizationRequestLimit =
        this.attendanceConfigurationData.employeeRegularizationRequestLimit;
      this.employeeRegularizationCutOffDays =
        this.attendanceConfigurationData.employeeRegularizationCutOffDays;
      this.openConfirmationPopup = false;
      this.isEdit = false;
      this.isFormDirty = false;
    },

    async updateAttendanceConfiguration() {
      const { valid } = await this.$refs.attendanceConfiguration.validate();
      let vm = this;
      if (valid && this.isFormDirty) {
        vm.isLoading = true;
        try {
          vm.$apollo
            .mutate({
              mutation: UPDATE_ATTENDANCE_CONFIGURATION,
              variables: {
                employeeRegularizationCutOffDays:
                  vm.employeeRegularizationCutOffDays
                    ? parseInt(vm.employeeRegularizationCutOffDays)
                    : 40,
                employeeRegularizationRequestLimit:
                  vm.employeeRegularizationRequestLimit
                    ? vm.employeeRegularizationRequestLimit
                    : 0,
                attendanceApprovalCutOffDaysForManager:
                  vm.attendanceApprovalCutOffDaysForManager
                    ? parseInt(vm.attendanceApprovalCutOffDaysForManager)
                    : 365,
              },
              client: "apolloClientAF",
            })
            .then(() => {
              vm.isLoading = false;
              var snackbarData = {
                isOpen: true,
                type: "success",
                message: "Attendance configuration updated successfully.",
              };

              vm.isEdit = false;
              vm.isFormDirty = false;
              vm.showAlert(snackbarData);
              this.fetchAttendanceConfigurationDetails();
            })
            .catch((error) => {
              vm.handleUpdateError(error);
            });
        } catch {
          vm.handleUpdateError();
        }
      } else {
        if (!this.isFormDirty) {
          vm.isEdit = false;
        }
      }
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture

        var errorCode = getErrorCodes(err);

        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0102": // update access denied
            snackbarData.message =
              "Sorry, you don't have update access rights. Please contact the HR administrator.";
            break;
          case "SAC0002": // Error while updating the attendance configuration.
            snackbarData.message =
              "An error occurred while updating the attendance configuration.";
            break;
          case "SAC0102": // while processing the request to update the attendance configuration.
            snackbarData.message =
              "Oops! We encountered a problem while updating the attendance configuration. Please try again later.";
            break;
          case "_UH0001": // unhandled error
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0002": // Error while checking the employee access rights
          case "_DB0104": // While check access rights form not found
            break;
          default:
            snackbarData.message =
              "Something went wrong while configuring attendance regularization. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while configuring attendance regularization. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    fetchAttendanceConfigurationDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: VIEW_ATTENDANCE_CONFIGURATION,
          client: "apolloClientAE",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.viewAttendanceConfiguration) {
            vm.attendanceConfigurationData =
              response.data.viewAttendanceConfiguration.configurationDetails;
            vm.employeeRegularizationCutOffDays =
              vm.attendanceConfigurationData.employeeRegularizationCutOffDays;
            vm.employeeRegularizationRequestLimit =
              vm.attendanceConfigurationData.employeeRegularizationRequestLimit;
            vm.attendanceApprovalCutOffDaysForManager =
              vm.attendanceConfigurationData.attendanceApprovalCutOffDaysForManager;
            this.prefillMoreDetails();
          } else {
            vm.handleFormRetrieveError();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleFormRetrieveError(err);
          vm.listLoading = false;
        });
    },
    handleFormRetrieveError(err = "") {
      this.listLoading = false;

      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        var errorCode = getErrorCodes(err);

        if (errorCode) {
          switch (errorCode) {
            case "_DB0100":
              this.errorContent =
                "Sorry, you don't have access rights to view the attendance configuration details. Please contact HR administrator.";
              break;
            case "_DB0000": // technical errors
              this.errorContent =
                "It’s us! There seem to be some technical difficulties. Please try after some time.";
              break;

            case "SAC0001": // Error while retrieving the attendance configuration.
              this.errorContent =
                "Something went wrong while retrieving the attendance configuration details. If you continue to see this issue please contact the platform administrator.";
              break;
            case "SAC0101": // Error while processing the request to retrieve the attendance configuration.
              this.errorContent =
                "An error occurred while attempting to fetch the attendance data.";
              break;
            case "_UH0001": // Something went wrong! Please contact the system admin.
              break;
            default:
              this.errorContent =
                "Something went wrong while retrieving the attendance configuration details. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        } else {
          this.errorContent =
            "Something went wrong while retrieving the attendance configuration details. Please try after some time.";
        }
      } else {
        this.errorContent =
          "Something went wrong while retrieving the attendance configuration details. Please try after some time.";
      }
      this.isErrorInList = true;
    },
  },
};
</script>
<style scoped>
.attendance-configuration-container {
  padding: 5em 2em 0em 3em;
}
</style>
