<template>
  <v-dialog v-model="dialogVisible" persistent max-width="1000px">
    <v-card class="rounded-lg">
      <v-overlay
        class="align-center justify-center"
        contained
        :model-value="isLoading"
        scrim="#fff"
      >
        <v-progress-circular color="primary" indeterminate size="64">
        </v-progress-circular>
      </v-overlay>
      <div class="d-flex justify-end pa-1">
        <v-icon
          color="primary"
          class="pr-4 pt-4 font-weight-bold"
          @click="closeModal()"
        >
          fas fa-times
        </v-icon>
      </div>
      <v-card-title class="mt-n6 text-center">
        <v-icon size="15" class="mr-3" style="color: #ff247b"
          >far fa-circle</v-icon
        >
        <span>Export Data</span>
      </v-card-title>
      <v-card-text>
        <v-form ref="forms">
          <v-container>
            <v-row>
              <v-col cols="12" v-if="screen !== 'import'">
                <v-autocomplete
                  v-model="employeeDetailsMultiple"
                  :items="employeeDetailsOptions"
                  variant="underlined"
                  :rules="[
                    (v) =>
                      (v && v.length) || 'Employee details type is required',
                  ]"
                  required
                  multiple
                  chips
                  clearable
                  closable-chips
                  @update:model-value="onSelectEmployeeDetails"
                >
                  <template v-slot:label>
                    <span class="text-subtitle-1 font-weight-bold"
                      >Select the employee details to export</span
                    ><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="12" v-if="screen === 'import'">
                <v-autocomplete
                  v-model="employeeDetails"
                  :items="employeeDetailsOptions"
                  variant="underlined"
                  clearable
                  :rules="[
                    (v) =>
                      (v && v.length) || 'Employee details type is required',
                  ]"
                  required
                >
                  <template v-slot:label>
                    <span class="text-subtitle-1 font-weight-bold"
                      >Select the employee details to export</span
                    ><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="12" v-if="screen == 'import'">
                <v-autocomplete
                  v-model="fields"
                  :items="employeeDetails ? fieldOptionsArray : []"
                  item-value="value"
                  item-title="text"
                  multiple
                  chips
                  clearable
                  closable-chips
                  variant="underlined"
                  color="primary"
                  :rules="[
                    (value) =>
                      !!employeeDetails ||
                      'Employee details type is required to display the fields',
                    (value) =>
                      (value && value.length > 0) ||
                      'Please select atleast one field',
                  ]"
                  required
                  @update:modelValue="onSelectFields"
                >
                  <template v-slot:label>
                    <span
                      style="color: black"
                      class="text-subtitle-1 font-weight-bold"
                      >Select Fields</span
                    >
                    <strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="6" v-if="screen !== 'import'">
                <v-menu
                  v-model="filterDateMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="activityDate"
                      v-model="formattedSelectedDateRange"
                      prepend-inner-icon="fas fa-calendar"
                      readonly
                      label="Date of Join Range"
                      v-bind="props"
                      variant="underlined"
                    >
                      <template v-slot:label> Date of Join Range </template>
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="selectedDateRange"
                    multiple="range"
                    @update:model-value="formatDate()"
                  />
                </v-menu>
              </v-col>
              <v-col cols="6">
                <v-autocomplete
                  v-model="filterBy"
                  :items="filterOptions"
                  variant="underlined"
                  :rules="[(v) => !!v || 'Filter By is required']"
                  required
                  clearable
                >
                  <template v-slot:label>
                    <span class="text-subtitle-1 font-weight-bold"
                      >Filter By </span
                    ><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="6">
                <v-autocomplete
                  v-model="sortBy"
                  :items="sortOptions"
                  variant="underlined"
                  :rules="[(v) => !!v || 'Sort By is required']"
                  required
                  clearable
                >
                  <template v-slot:label>
                    <span class="text-subtitle-1 font-weight-bold">Sort by</span
                    ><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="6">
                <v-autocomplete
                  v-model="selectedDesignation"
                  :items="designations"
                  item-title="Designation_Name"
                  item-value="Designation_Id"
                  variant="underlined"
                  :loading="designationListLoading"
                  multiple
                  chips
                  clearable
                  closable-chips
                  placeholder="Type minimum 3 characters to list"
                  :rules="[
                    (value) =>
                      (value && value.length > 0) || 'Designation is required',
                  ]"
                  required
                  @update:modelValue="onSelectDesignation"
                  @update:search="callDesignationList($event)"
                >
                  <template v-slot:label>
                    <span class="text-subtitle-1 font-weight-bold"
                      >Designation</span
                    ><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="6">
                <v-autocomplete
                  v-model="selectedDepartment"
                  :items="departments"
                  item-title="Department_Name"
                  item-value="Department_Id"
                  variant="underlined"
                  multiple
                  chips
                  clearable
                  closable-chips
                  :rules="[
                    (value) =>
                      (value && value.length > 0) || 'Department is required',
                  ]"
                  required
                  @update:modelValue="onSelectDepartment"
                >
                  <template v-slot:label>
                    <span class="text-subtitle-1 font-weight-bold"
                      >Department</span
                    ><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col>
              <!-- <v-col cols="6">
                <v-autocomplete v-model="selectedWorkSchedule" :items="workSchedules" item-title="Title" item-value="WorkSchedule_Id" variant="underlined"
                  :rules="[(v) => !!v || 'Workschedule is required']" required>
                  <template v-slot:label>
                    <span class="text-subtitle-1">Work Schedule</span><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col> -->
              <v-col cols="6">
                <v-autocomplete
                  v-model="selectedLocation"
                  :items="locations"
                  item-title="Location_Name"
                  item-value="Location_Id"
                  variant="underlined"
                  multiple
                  chips
                  clearable
                  closable-chips
                  :rules="[
                    (value) =>
                      (value && value.length > 0) || 'Location is required',
                  ]"
                  required
                  @update:modelValue="onSelectLocation"
                >
                  <template v-slot:label>
                    <span class="text-subtitle-1 font-weight-bold"
                      >Location</span
                    ><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col>
              <!-- <v-col cols="6">
                <v-autocomplete v-model="selectedEmployeeType" :items="employeeTypes" item-title="Employee_Type" item-value="EmpType_Id" variant="underlined"
                  :rules="[(v) => !!v || 'Employee Type is required']" required>
                  <template v-slot:label>
                    <span class="text-subtitle-1">Employee Type</span><strong class="pl-1 text-red-darken-1">*</strong>
                  </template>
                </v-autocomplete>
              </v-col> -->
            </v-row>
          </v-container>
        </v-form>
        <small class="pl-5"
          ><strong class="pr-1 text-red-darken-1">*</strong>indicates required
          field</small
        >
      </v-card-text>
      <v-card-actions class="d-flex justify-center">
        <v-btn
          variant="flat"
          rounded="lg"
          color="primary"
          class="pa-3"
          @click="validateExportData()"
        >
          <v-icon class="pr-2 pb-1">fas fa-file-export</v-icon>
          Export
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
//Queries
import {
  EXPORT_EMPLOYEE_DATA,
  GET_ONBOARDING_EMPLOYEE_DATA,
} from "@/graphql/corehr/employeeDataQueries.js";
import { RETRIEVE_DYNAMIC_FORM_FIELDS } from "@/graphql/settings/general/customFieldsQuries.js";
import mixpanel from "mixpanel-browser";
import moment from "moment";
import Config from "@/config.js";
//Mixins
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";

export default {
  name: "ExportEmployeeData",

  props: {
    openDialog: {
      type: Boolean,
      default: false,
    },
    screen: {
      type: String,
      default: "",
    },
    sunfish: {
      type: String,
      default: "",
    },
    deploymentType: {
      type: String,
      default: "",
    },
    formId: {
      type: Number,
      default: 0,
    },
  },

  mixins: [FileExportMixin, validationRules],

  watch: {
    openDialog(val) {
      this.dialogVisible = val;
    },
    employeeDetails() {
      this.validateExportData(true);
    },
    selectedDateRange(val) {
      if (val.length > 1) {
        this.formattedSelectedDateRange =
          this.formatDate(val[0]) +
          " - " +
          this.formatDate(val[val.length - 1]);
        this.filterDateMenu = false;
      }
    },
  },

  data() {
    return {
      dialogVisible: false,
      isLoading: false,
      dialogTitle: "Export Employee Data",
      filterBy: "Active Employees",
      sortBy: "Employee_Id",
      sortOptions: [
        { title: "Id", value: "Employee_Id" },
        { title: "EmployeeId", value: "User_Defined_EmpId" },
        { title: "Employee Name", value: "Employee_Name" },
      ],
      exportOptions: [
        "All CSV (Comma Seperated Values)",
        "XLSX (Microsoft Excel)",
      ],
      fields: [],
      valid: false,
      exportListData: [],
      //Data
      designations: ["All"],
      departments: ["All"],
      locations: ["All"],
      workSchedules: ["All"],
      employeeTypes: ["All"],
      managers: ["All"],

      //Extra Filters
      selectedDesignation: ["All"],
      selectedDepartment: ["All"],
      selectedLocation: ["All"],
      selectedWorkSchedule: ["All"],
      selectedEmployeeType: ["All"],
      designationListLoading: false,
      filterDateMenu: false,
      selectedDateRange: [],
      formattedSelectedDateRange: "",
      employeeDetailsMultiple: [],
      customFields: [],
      employeeDetails: null,
      designationList: [],
    };
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    filterOptions() {
      return ["Active Employees"];
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    airTicketFormAccess() {
      let formAccess = this.accessRights("322");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    personalInfoHeaders() {
      let headers = [{ header: "Employee ID", key: "User_Defined_EmpId" }];
      if (this.screen === "summary") {
        headers.push({
          header: "Biometric Integration ID",
          key: "External_EmpId",
        });
      }
      headers.push(
        { header: "Salutation", key: "Salutation" },
        { header: "First Name", key: "Emp_First_Name" },
        { header: "Middle Name", key: "Emp_Middle_Name" },
        { header: "Last Name", key: "Emp_Last_Name" },
        { header: "Suffix", key: "Appellation" },
        { header: "Gender", key: "Gender" }
      );
      if (this.labelList[205] && this.labelList[205].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[205].Field_Alias,
          key: "Pronoun",
        });
      }
      if (this.labelList[206] && this.labelList[206].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[206].Field_Alias,
          key: "Gender_Orientations",
        });
      }
      headers.push(
        { header: "Marital Status", key: "Marital_Status_Name" },
        { header: "Date Of Birth", key: "DOB" },
        { header: "Blood Group", key: "Blood_Group" },
        { header: "Nationality", key: "Nationality" }
      );
      if (this.labelList[215] && this.labelList[215].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[215].Field_Alias,
          key: "Aadhaar_Card_Number",
        });
      }
      if (this.labelList[216] && this.labelList[216].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[216].Field_Alias,
          key: "PAN",
        });
      }
      if (this.labelList[188] && this.labelList[188].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[188].Field_Alias,
          key: "Tax_Code",
        });
      }
      headers.push({
        header: "Personal Email",
        key: "Personal_Email",
      });
      if (this.labelList[219] && this.labelList[219].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[219].Field_Alias,
          key: "UAN",
        });
      }
      headers.push(
        { header: "Manager", key: "Is_Manager" },
        { header: "Allow User Sign In", key: "Allow_User_Signin" },
        {
          header: "Allow Sign In using mobile no.",
          key: "Enable_Sign_In_With_Mobile_No",
        }
      );
      if (this.labelList[301] && this.labelList[301].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[301].Field_Alias,
          key: "Place_Of_Birth",
        });
      }
      if (this.labelList[231] && this.labelList[231].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[231].Field_Alias,
          key: "Nick_Name",
        });
      }
      headers.push(
        { header: "Languages Known", key: "Languages" },
        { header: "Hobbies", key: "Hobbies" },
        { header: "Ethnic Race", key: "Ethnic_Race" }
      );
      if (this.labelList[241] && this.labelList[241].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[241].Field_Alias,
          key: "Caste",
        });
      }
      if (this.labelList[302] && this.labelList[302].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[302].Field_Alias,
          key: "Religion",
        });
      }
      headers.push(
        { header: "Disability", key: "Physically_Challenged" },
        { header: "Military Service", key: "Military_Service" },
        { header: "Smoker", key: "Smoker" },
        { header: "Smoker as of", key: "Smokerasof" }
      );
      if (this.labelList[217] && this.labelList[217].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[217].Field_Alias,
          key: "Statutory_Insurance_Number",
        });
      }
      if (this.labelList[218] && this.labelList[218].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[218].Field_Alias,
          key: "PRAN_No",
        });
      }
      return headers;
    },
    drivingLicenseHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "License Number", key: "Driving_License_No" },
        { header: "Issued Date", key: "License_Issue_Date" },
        { header: "Expiry Date", key: "License_Expiry_Date" },
        { header: "Issued Authority", key: "Issuing_Authority" },
        { header: "Issued State", key: "Issuing_State" },
        { header: "Issued Country", key: "Country_Name" },
        { header: "Vehicle Type", key: "Vehicle_Type" },
      ];
    },
    dependentHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Dependent First Name", key: "Dependent_First_Name" },
        { header: "Dependent Last Name", key: "Dependent_Last_Name" },
        { header: "Relationship", key: "Relationship" },
        { header: "Gender", key: "Gender" },
        { header: "Date of Birth", key: "Dependent_DOB" },
      ];
    },
    jobHeaders() {
      let headers = [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Role(Access Rights)", key: "Roles_Name" },
        { header: "Date of Join", key: "Date_Of_Join" },
        { header: "Designation Code", key: "Designation_Code" },
        { header: "Designation", key: "Designation_Name" },
        { header: "Department Code ", key: "Department_Code" },
        { header: "Department", key: "Department_Name" },
        { header: "Location Code", key: "Location_Code" },
        { header: "Location", key: "Location_Name" },
        { header: "Employee Type Code", key: "Employee_Type_Code" },
        { header: "Employee Type", key: "Employee_Type" },
        { header: "Work Schedule", key: "Work_Schedule_Name" },
      ];

      if (this.labelList[115] && this.labelList[115].Field_Visiblity == "Yes") {
        headers.push(
          {
            header: this.labelList[115].Field_Alias + " Code",
            key: "Service_Provider_Code",
          },
          {
            header: this.labelList[115].Field_Alias,
            key: "Service_Provider_Name",
          }
        );
      }
      if (this.sunfish.toLowerCase() == "active") {
        headers.push(
          { header: "Business Unit Code", key: "Business_Unit_Code" },
          { header: "Business Unit/Cost Center", key: "Business_Unit" }
        );
      } else {
        headers.push(
          { header: "Business Unit Code", key: "Business_Unit_Code" },
          {
            header: "Business Unit/Cost Center",
            key: "Business_Unit_Name",
          }
        );
      }
      if (this.labelList[151] && this.labelList[151].Field_Visiblity == "Yes") {
        headers.push(
          {
            header: this.labelList[151].Field_Alias + " Code",
            key: "Organization_Group_Code",
          },
          {
            header: this.labelList[151].Field_Alias,
            key: "Organization_Group",
          }
        );
      }
      headers.push(
        { header: "Work Email", key: "Emp_Email" },
        { header: "Employee Profession", key: "Profession_Name" },
        { header: "Manager Id", key: "Manager_User_Defined_EmpId" },
        { header: "Manager", key: "Manager_Name" },
        {
          header: "Second Line Manager Id",
          key: "Second_Manager_User_Defined_EmpId",
        },
        { header: "Second Line Manager", key: "Second_Manager_Name" }
        // { header: "Salary", key: "Salary" }
      );
      if (this.labelList[186] && this.labelList[186].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[186].Field_Alias,
          key: "Timekeeping_Name",
        });
      }
      if (this.labelList[187] && this.labelList[187].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[187].Field_Alias,
          key: "Career_Name",
        });
      }
      if (this.sunfish.toLowerCase() == "active") {
        headers.push(
          { header: "Branch_Email", key: "Branch_Email_Address" },
          { header: "Job Title Code", key: "Job_Title_Code" },
          { header: "Last Grade Code", key: "Lst_Grade_Code" },
          { header: "Global Grade", key: "Global_Grade" },
          { header: "Organization Unit Code", key: "Organization_Unit_Code" },
          { header: "Job Family Level Code", key: "Job_Family_Level_Code" },
          { header: "Job Family Code", key: "Job_Family_Code" },
          { header: "Job Family Grade Code", key: "Job_Family_Grade_Code" },
          { header: "Group Code", key: "Pos_Group_Code" },
          { header: "Group Name", key: "Pos_Group_Name" },
          { header: "Division Code", key: "Pos_Division_Code" },
          { header: "Division Name", key: "Pos_Division_Name" },
          { header: "Section Code", key: "Pos_Section_Code" },
          { header: "Section Name", key: "Pos_Section_Name" }
        );
      }
      if (this.labelList[304] && this.labelList[304].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[304].Field_Alias,
          key: "Pf_PolicyNo",
        });
      }
      headers.push(
        { header: "Job Code", key: "Job_Code" },
        { header: "Probation Date", key: "Probation_Date" }
      );
      headers.push(
        { header: "Confirmed", key: "Confirmed" },
        { header: "Confirmation Date", key: "Confirmation_Date" },
        { header: "Commission Based Employee", key: "Commission_Employee" },
        {
          header: "Attendance Enforced Payment",
          key: "Attendance_Enforced_Payment",
        },
        { header: "Employee Status", key: "Emp_Status" },
        { header: "Previous Experience", key: "Previous_Employee_Experience" }
      );
      if (this.labelList[306] && this.labelList[306].Field_Visiblity == "Yes") {
        headers.push({
          header: this.labelList[306].Field_Alias,
          key: "TDS_Exemption",
        });
      }
      headers.push(
        { header: "Camu ID", key: "Camu_Id" },
        { header: "Global Resource ID", key: "Global_Resource_Id" }
      );
      return headers;
    },
    passportHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Passport Number", key: "Passport_No" },
        { header: "Issue Date", key: "Issue_Date" },
        { header: "Expiry Date", key: "Expiry_Date" },
        { header: "Issuing Country", key: "Country_Name" },
        { header: "Issuing Authority", key: "Issuing_Authority" },
      ];
    },
    experienceHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Company Name", key: "Prev_Company_Name" },
        { header: "Designation", key: "Designation" },
        { header: "Location", key: "Prev_Company_Location" },
        { header: "From", key: "Start_Date_Join" },
        { header: "To", key: "End_Date" },
        { header: "Duration", key: "Duration" },
      ];
    },
    contactHeaders() {
      let headers = [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        {
          header: "Permanent " + this.labelList[233].Field_Alias,
          key: "pApartment_Name",
        },
        { header: "Permanent Street", key: "pStreet_Name" },
        { header: "Permanent City", key: "pCity" },
        { header: "Permanent State/Province", key: "pState" },
        { header: "Permanent Country", key: "pCountry_Name" },
        { header: "Permanent Pincode", key: "pPincode" },
        {
          header: "Current " + this.labelList[233].Field_Alias,
          key: "cApartment_Name",
        },
        { header: "Current Street", key: "cStreet_Name" },
        { header: "Current City", key: "cCity" },
        { header: "Current State/Province", key: "cState" },
        { header: "Current Country", key: "cCountry_Name" },
        { header: "Current Pincode", key: "cPincode" },
        {
          header: "Office " + this.labelList[233].Field_Alias,
          key: "oApartment_Name",
        },
        { header: "Office Street", key: "oStreet_Name" },
        { header: "Office City", key: "oCity" },
        { header: "Office State/Province", key: "oState" },
        { header: "Office Country", key: "oCountry_Name" },
        { header: "Office Pincode", key: "oPincode" },
        { header: "Mobile Number", key: "Mobile_No" },
        { header: "Emergency Contact Name", key: "Emergency_Contact_Name" },
        { header: "Emergency Contact Number", key: "Fax_No" },
        {
          header: "Emergency Contact Relation",
          key: "Emergency_Contact_Relation",
        },
        { header: "Telephone Number", key: "Land_Line_No" },
        { header: "Work Number", key: "Work_No" },
      ];
      return headers;
    },
    educationHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Course Name", key: "Course_Name" },
        { header: "Specialization", key: "Specialization_Name" },
        { header: "University", key: "University" },
        { header: "Institute", key: "Institute_Name" },
        { header: "Year of Start", key: "Year_Of_Start" },
        { header: "Year of Passing", key: "Year_Of_Passing" },
        { header: "Percentage", key: "Percentage" },
        { header: "Grade", key: "Grade" },
      ];
    },
    certificationHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Certification Name", key: "Certification_Name" },
        { header: "Received On", key: "Received_Date" },
        {
          header: "Certification Received From",
          key: "Certificate_Received_From",
        },
      ];
    },
    trainingHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Training Name", key: "Training_Name" },
        { header: "Trainer", key: "Trainer" },
        { header: "Training Start Date", key: "Training_Start_Date" },
        { header: "Training End Date", key: "Training_End_Date" },
        { header: "Training Duration", key: "Training_Duration" },
        { header: "Training Center", key: "Center" },
      ];
    },
    awardHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Award Name", key: "Award_Name" },
        { header: "Received On", key: "Received_On" },
        { header: "Received For", key: "Received_For" },
        { header: "Received From", key: "Received_From" },
      ];
    },
    skillHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Primary Skill", key: "Primary_Skill" },
        { header: "Secondary Skill", key: "Secondary_Skill" },
        { header: "Known Skills", key: "Known_Skills" },
        { header: "Hands On", key: "Hands_On" },
      ];
    },
    bankHeaders() {
      let headers = [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Bank Name", key: "bankName" },
        { header: "Account Number", key: "Bank_Account_Number" },
      ];
      if (
        this.labelList[135] &&
        this.labelList[135].Field_Visiblity === "Yes"
      ) {
        headers.push({
          header: this.labelList[135].Field_Alias,
          key: "IFSC_Code",
        });
      }
      headers.push(
        { header: "Credit Account", key: "Credit_Account" },
        { header: "Account Type", key: "Account_Type" },
        { header: "Branch", key: "Branch_Name" }
      );
      if (
        this.labelList[235] &&
        this.labelList[235].Field_Visiblity === "Yes"
      ) {
        headers.push({
          header: this.labelList[235].Field_Alias,
          key: "Street",
        });
      }
      headers.push(
        { header: "City", key: "City" },
        { header: "State", key: "State" },
        { header: "Country", key: "Country" },
        { header: this.labelList[146].Field_Alias, key: "Zip" },
        { header: "Beneficiary Id", key: "Beneficiary_Id" },
        { header: "Status", key: "Status" }
      );
      return headers;
    },
    insuranceHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Insurance Type", key: "Insurance_Type" },
        { header: "Insurance Name", key: "Insurance_Name" },
        { header: "Insurance No/ Insurance Policy No", key: "Policy_No" },
      ];
    },
    accreditationHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        {
          header: "Professional License Category",
          key: "Accreditation_Category",
        },
        { header: this.labelList[229].Field_Alias, key: "Accreditation_Type" },
        { header: "Recieved Date", key: "Received_Date" },
        { header: "Expiry Date", key: "Expiry_Date" },
        { header: "Identifier", key: "Identifier" },
      ];
    },
    documentHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Document Name", key: "Document_Name" },
        { header: "Document Category", key: "Category_Fields" },
        { header: "Document Subtype", key: "Document_Sub_Type" },
        { header: "Document Type", key: "Document_Type" },
      ];
    },
    sunfishHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Status", key: "Status" },
        { header: "Candidate Email Status", key: "Candidate_Email_Status" },
        {
          header: "Hiring Manager Email Status",
          key: "Hiring_Manager_Email_Status",
        },
        {
          header: "Personal Info Sync Status",
          key: "Personal_Info_Sync_Status",
        },
        {
          header: "Personal Info Failure Reason",
          key: "Personal_Info_Failure_Reason",
        },
        {
          header: "Personal Info Updated On",
          key: "Personal_Info_Updated_On",
        },
        {
          header: "Additional Info Sync Status",
          key: "Additional_Info_Sync_Status",
        },
        {
          header: "Additional Info Failure Reason",
          key: "Additional_Info_Failure_Reason",
        },
        {
          header: "Additional Info Updated On",
          key: "Additional_Info_Updated_On",
        },
        {
          header: "Employee Info Sync Status",
          key: "Employee_Info_Sync_Status",
        },
        {
          header: "Employee Info Failure Reason",
          key: "Employee_Info_Failure_Reason",
        },
        {
          header: "Employee Info Updated On",
          key: "Employee_Info_Updated_On",
        },
        {
          header: "Education Info Sync Status",
          key: "Education_Info_Sync_Status",
        },
        {
          header: "Education Info Failure Reason",
          key: "Education_Info_Failure_Reason",
        },
        {
          header: "Education Info Updated On",
          key: "Education_Info_Updated_On",
        },
        {
          header: "Attachment Info Sync Status",
          key: "Attachment_Info_Sync_Status",
        },
        {
          header: "Attachment Info Failure Reason",
          key: "Attachment_Info_Failure_Reason",
        },
        {
          header: "Attachment Info Updated On",
          key: "Attachment_Info_Updated_On",
        },
        {
          header: "Updated By Employee Id",
          key: "Updated_By_User_Defined_EmpId",
        },
        {
          header: "Updated By Employee Name",
          key: "Updated_By_Employee_Name",
        },
      ];
    },
    PagtHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Status", key: "Status" },
        { header: "Candidate Email Status", key: "Candidate_Email_Status" },
        {
          header: "Hiring Manager Email Status",
          key: "Hiring_Manager_Email_Status",
        },
        {
          header: "Personal Info Sync Status",
          key: "Personal_Info_Sync_Status",
        },
        {
          header: "Personal Info Failure Reason",
          key: "Personal_Info_Failure_Reason",
        },
        {
          header: "Personal Info Updated On",
          key: "Personal_Info_Updated_On",
        },
        {
          header: "Additional Info Sync Status",
          key: "Additional_Info_Sync_Status",
        },
        {
          header: "Additional Info Failure Reason",
          key: "Additional_Info_Failure_Reason",
        },
        {
          header: "Additional Info Updated On",
          key: "Additional_Info_Updated_On",
        },
        {
          header: "Education Info Sync Status",
          key: "Education_Info_Sync_Status",
        },
        {
          header: "Education Info Failure Reason",
          key: "Education_Info_Failure_Reason",
        },
        {
          header: "Education Info Updated On",
          key: "Education_Info_Updated_On",
        },
        {
          header: "Attachment Info Sync Status",
          key: "Attachment_Info_Sync_Status",
        },
        {
          header: "Attachment Info Failure Reason",
          key: "Attachment_Info_Failure_Reason",
        },
        {
          header: "Attachment Info Updated On",
          key: "Attachment_Info_Updated_On",
        },
        {
          header: "Accreditation Info Sync Status",
          key: "Accreditation_Info_Sync_Status",
        },
        {
          header: "Accreditation Info Failure Reason",
          key: "Accreditation_Info_Failure_Reason",
        },
        {
          header: "Accreditation Info Updated On",
          key: "Accreditation_Info_Updated_On",
        },
        {
          header: "Certificate Info Sync Status",
          key: "Certificate_Info_Sync_Status",
        },
        {
          header: "Certificate Info Failure Reason",
          key: "Certificate_Info_Failure_Reason",
        },
        {
          header: "Certificate Info Updated On",
          key: "Certificate_Info_Updated_On",
        },
        {
          header: "Language Info Sync Status",
          key: "Language_Info_Sync_Status",
        },
        {
          header: "Language Info Failure Reason",
          key: "Language_Info_Failure_Reason",
        },
        {
          header: "Language Info Updated On",
          key: "Language_Info_Updated_On",
        },
        {
          header: "Previous Employment Info Sync Status",
          key: "Previous_Info_Sync_Status",
        },
        {
          header: "Previous Employment Info Failure Reason",
          key: "Previous_Info_Failure_Reason",
        },
        {
          header: "Previous Employement Info Updated On",
          key: "Previous_Info_Updated_On",
        },
        {
          header: "Updated By Employee Id",
          key: "Updated_By_User_Defined_EmpId",
        },
        {
          header: "Updated By Employee Name",
          key: "Updated_By_Employee_Name",
        },
      ];
    },
    AssetsHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Asset Name", key: "Asset_Name" },
        { header: "Serial No", key: "Serial_No" },
        { header: "Receive Date", key: "Receive_Date" },
        { header: "Return Date", key: "Return_Date" },
      ];
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    airTicketHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Place of Origin", key: "Destination" },
        { header: "Air Ticket Category", key: "Air_Ticketing_Category" },
        {
          header: `Air Ticket Entitlement (in ${this.payrollCurrency})`,
          key: "Air_Ticketing_Entitlement",
        },
        { header: "Effective From", key: "Effective_Date_Enable" },
        { header: "Effective Date", key: "Effective_Date" },
        {
          header: "Eligibility Of Ticket Claim (in Months)",
          key: "Eligibility_Of_Ticket_Claim_Months",
        },
        { header: "Air Ticket To Dependent", key: "Air_Ticket_To_Dependent" },
        { header: "Dependent Relationship", key: "Dependent_Relationship" },
        { header: "Last Availed Date", key: "Last_Availed_Date" },
        { header: "Status", key: "Status" },
      ];
    },
    airTicketSettlementHeaders() {
      return [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Destination City", key: "Destination_City" },
        { header: "Destination Country", key: "Destination_Country" },
        { header: "Air Ticket Category", key: "Air_Ticketing_Category" },
        { header: "Settlement Status", key: "Settlement_Status" },
        {
          header: `Settlement Amount ${this.payrollCurrency}`,
          key: "Settlement_Amount",
        },
        {
          header: "No Of Dependents Eligible For Ticket",
          key: "No_Of_Dependents",
        },
        { header: "Air Ticket To Dependent", key: "Air_Ticket_To_Dependent" },
        { header: "Availed Date", key: "Availed_Date" },
        {
          header: `Accrual Basis (in ${this.payrollCurrency})`,
          key: "Accrual_Basis",
        },
        { header: "Dependent Relationship", key: "Dependent_Relationship" },
        { header: "Effective Date", key: "Effective_Date" },
        { header: "Effective From", key: "Effective_Date_Enable" },
        {
          header: "Eligibility Of Ticket Claim (in Months)",
          key: "Eligibility_Of_Ticket_Claim_Months",
        },
        { header: "Payroll Month", key: "Payroll_Month" },
      ];
    },
    fieldOptions() {
      if (this.employeeDetails === "Employee Job Details") {
        return {
          All: "All",
          Emp_Email: "Email",
          WorkSchedule: "WorkSchedule",
          Designation: "Designation",
          Department: "Department",
          Location: "Location",
          Employee_Type: "EmployeeType",
          Manager: "Manager",
        };
      }
      return [];
    },
    fieldOptionsArray() {
      return Object.values(this.fieldOptions);
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    employeeDetailsOptions() {
      if (this.screen === "import") {
        return ["Employee Job Details"];
      }
      let options = [
        "All",
        "Personal",
        "License",
        "Dependent",
        "Passport",
        "Job",
        "Experience",
        "Contact",
        "Education",
        "Certifications",
        "Training",
        "Awards",
        "Skills",
        "Bank",
        "Insurance",
        "Professional License",
        "Documents",
      ];
      if (this.screen === "onboarded") {
        if (this.sunfish.toLowerCase() == "active") {
          options.push("Data Sync Status");
        }
      } else if (this.screen === "summary") {
        options.push("Assets");
        options.push("Pay Configuration");
        if (this.airTicketFormAccess) {
          options.push("Air Ticket Policy");
          options.push("Air Ticket Settlement Summary");
        }
        options.push("Custom Fields");
      }
      return options;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.getDropDownBoxDetails();
    this.getDesignationListWithLimit();
    this.fetchCustomFields();
  },
  methods: {
    onSelectFields() {
      if (this.fields.includes("All")) {
        this.fields = [
          "All",
          "Email",
          "Manager",
          "WorkSchedule",
          "Designation",
          "Department",
          "Location",
          "EmployeeType",
        ];
      }
    },
    onSelectEmployeeDetails() {
      if (this.employeeDetailsMultiple.includes("All")) {
        this.employeeDetailsMultiple = this.employeeDetailsOptions;
      }
    },
    onSelectDesignation() {
      if (this.selectedDesignation.includes("All")) {
        this.selectedDesignation = ["All"];
      }
    },
    onSelectDepartment() {
      if (this.selectedDepartment.includes("All")) {
        this.selectedDepartment = ["All"];
      }
    },
    onSelectLocation() {
      if (this.selectedLocation.includes("All")) {
        this.selectedLocation = ["All"];
      }
    },
    async validateExportData(onlyValidation) {
      mixpanel.track("MyTeam-exportWithFilter-export-btn-click");
      const { valid } = await this.$refs.forms.validate();
      if (valid && !onlyValidation && this.screen == "import") {
        this.exportEmployeeData();
      } else if (valid && !onlyValidation) {
        this.fetchOnboardingEmployeeData();
      }
    },
    async getDesignationListWithLimit() {
      this.isLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          offset: 0,
          limit: 1000,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            let { totalRecords } = res.data.getDesignationDetails;
            if (totalRecords <= 1000) {
              this.designationList =
                res.data.getDesignationDetails.designationResult;
            } else {
              this.designationList = [];
            }
          }
          this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
        });
    },
    exportEmployeeData() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: EXPORT_EMPLOYEE_DATA,
          client: "apolloClientI",
          variables: {
            typeOfEmployeeDetails: vm.employeeDetails,
            filterBy: vm.filterBy,
            location: vm.selectedLocation.includes("All")
              ? []
              : vm.selectedLocation,
            department: vm.selectedDepartment.includes("All")
              ? []
              : vm.selectedDepartment,
            designation: vm.selectedDesignation.includes("All")
              ? []
              : vm.selectedDesignation,
            sortBy: vm.sortBy,
          },
          fetchPolicy: "no-cache",
        })
        .then(async (response) => {
          mixpanel.track("MyTeam-exportWithFilter-export-success");
          if (
            response &&
            response.data &&
            response.data.exportEmployeeDetails
          ) {
            vm.exportListData = response.data.exportEmployeeDetails
              .exportEmployeeDetails
              ? response.data.exportEmployeeDetails.exportEmployeeDetails
              : [];
            vm.exportListData = JSON.parse(vm.exportListData);
            vm.isLoading = false;
            vm.formExportData();
          } else {
            vm.handleExportEmployeeError();
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.handleExportEmployeeError(err);
          vm.isLoading = false;
          vm.closeModal();
        });
    },
    fetchOnboardingEmployeeData() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_ONBOARDING_EMPLOYEE_DATA,
          client: "apolloClientI",
          variables: {
            typeOfEmployeeDetails: vm.employeeDetailsMultiple.includes("All")
              ? ["All"]
              : vm.employeeDetailsMultiple,
            filterBy: [vm.filterBy],
            location: vm.selectedLocation.includes("All")
              ? []
              : vm.selectedLocation,
            department: vm.selectedDepartment.includes("All")
              ? []
              : vm.selectedDepartment,
            designation: vm.selectedDesignation.includes("All")
              ? []
              : vm.selectedDesignation,
            sortBy: vm.sortBy,
            fromDate:
              vm.selectedDateRange && vm.selectedDateRange.length > 0
                ? vm.selectedDateRange[0]
                : null,
            toDate:
              vm.selectedDateRange && vm.selectedDateRange.length > 0
                ? vm.selectedDateRange[vm.selectedDateRange.length - 1]
                : null,
            formId: 178,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.exportEmployeeAllDetails &&
            data.exportEmployeeAllDetails.errorCode === "_EC0001"
          ) {
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message: data.exportEmployeeAllDetails.message,
            };
            vm.showAlert(snackbarData);
          } else if (
            data &&
            data.exportEmployeeAllDetails &&
            data.exportEmployeeAllDetails.exportEmployeeDetails
          ) {
            let employeeData = JSON.parse(
              data.exportEmployeeAllDetails.exportEmployeeDetails
            );
            this.formOnboardExportData(employeeData);
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.handleExportEmployeeError(err);
          vm.isLoading = false;
          vm.closeModal();
        });
    },
    getDropDownBoxDetails() {
      this.$store
        .dispatch("getDefaultDropdownList", {
          status: "",
          formId: 15,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              locations,
              employeeType,
              workSchedules,
              managers,
            } = res.data.getDropDownBoxDetails;

            this.departments = this.departments.concat(departments);
            this.locations = this.locations.concat(locations);
            this.employeeTypes = this.employeeTypes.concat(employeeType);
            this.workSchedules = this.workSchedules.concat(workSchedules);
            this.managers = this.managers.concat(managers);
            this.isLoading = false;
          } else {
            this.handleExportEmployeeError();
          }
        })
        .catch((err) => {
          this.handleExportEmployeeError(err);
        });
    },
    handleExportEmployeeError(err = "") {
      mixpanel.track("MyTeam-exportWithFilter-export-error");
      var snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "_DB0000": // technical errors
              snackbarData.message =
                "It’s us! There seem to be some technical difficulties. Please try after some time.";
              break;
            case "_UH0001": // unhandled error
            case "_DB0001": // Error while retrieving the employee access rights
            case "_DB0002": // Error while checking the employee access rights
            case "_DB0104": // While check access rights form not found
            case "EDM0103": // Error while listing employee details.
            case "EDM0003": // Error while processing the request to retrieve employee details.
            default:
              snackbarData.message =
                "Something went wrong while retrieving the employee details. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        } else {
          snackbarData.message =
            "Something went wrong while retrieving the employee details. Please try after some time.";
        }
      } else {
        snackbarData.message =
          "Something went wrong while retrieving the employee details. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    formExportData() {
      this.exportListData = this.exportListData.map((employee) => {
        let designation = this.designations.find(
          (d) => d.Designation_Id === employee.Designation_Id
        );
        let department = this.departments.find(
          (d) => d.Department_Id === employee.Department_Id
        );
        let location = this.locations.find(
          (d) => d.Location_Id === employee.Location_Id
        );
        let empType = this.employeeTypes.find(
          (d) => d.EmpType_Id === employee.EmpType_Id
        );
        let workschedule = this.workSchedules.find(
          (d) => d.WorkSchedule_Id === employee.Work_Schedule
        );
        let manager = this.managers.find(
          (d) => d.Manager_Id === employee.Manager_Id
        );
        return {
          ...employee,
          Designation: designation ? designation.Designation_Name : null,
          Department: department ? department.Department_Name : null,
          Location: location ? location.Location_Name : null,
          Employee_Type: empType ? empType.Employee_Type : null,
          WorkSchedule: workschedule ? workschedule.Title : null,
          Manager: manager ? manager.Manager_Name : null,
        };
      });
      this.exportReportFile();
    },
    fetchCustomFields() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_DYNAMIC_FORM_FIELDS,
          client: "apolloClientI",
          variables: {
            formId: parseInt(vm.formId),
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveDynamicFormFields &&
            response.data.retrieveDynamicFormFields.dynamicFields &&
            !response.data.retrieveDynamicFormFields.errorCode
          ) {
            const departList = JSON.parse(
              response.data.retrieveDynamicFormFields.dynamicFields
            );
            vm.customFields = departList;
          } else {
            vm.handleListError(
              response.data.retrieveDynamicFormFields.errorCode
            );
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleListError(err);
        });
    },
    exportReportFile() {
      let exportData = this.exportListData;
      // let effectiveDateFields = [
      //   "Designation",
      //   "Department",
      //   "WorkSchedule",
      //   "EmployeeType",
      //   "Location",
      // ];

      //Form Headers - This will form like [{ key: 'First_Name', header: 'First Name'}, { key: 'Second_Name', header: 'Second Name'}]
      let headers = [];
      if (this.fields.includes("All")) {
        headers = Object.entries(this.fieldOptions)
          .filter(([key]) => key !== "All")
          .map(([key, value]) => ({
            key,
            header: value,
          }));
      } else {
        headers = Object.entries(this.fieldOptions)
          .filter(([, value]) => this.fields.includes(value))
          .map(([key, value]) => ({ key, header: value }));
      }
      headers.unshift(
        { key: "User_Defined_EmpId", header: "Employee Id" },
        { key: "Employee_Name", header: "Employee Name" }
      );

      let exportOptions = {
        fileExportData: exportData,
        fileName: this.employeeDetails,
        sheetName: this.employeeDetails,
        header: headers,
        columnHighlightProps: {
          type: "Employee Import",
          designation: this.fields.includes("Designation")
            ? this.designationList.length > 0
              ? this.designationList
              : this.designations.slice(1)
            : [],
          department: this.fields.includes("Department")
            ? this.departments.slice(1)
            : [],
          workschedule: this.fields.includes("WorkSchedule")
            ? this.workSchedules.slice(1)
            : [],
          employeeType: this.fields.includes("EmployeeType")
            ? this.employeeTypes.slice(1)
            : [],
          location: this.fields.includes("Location")
            ? this.locations.slice(1)
            : [],
          manager: this.fields.includes("Manager")
            ? this.managers.slice(1)
            : [],
        },
      };
      this.exportExcelFile(exportOptions);
      mixpanel.track("MyTeam-exportWithFilter-exported");
      this.closeModal();
    },
    formOnboardExportData(exportData) {
      let sheets = [],
        data = [],
        headers = [];
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      if (
        this.employeeDetailsMultiple.includes("Personal") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Personal Info");
        headers.push(this.personalInfoHeaders);
        let modifiedData = exportData.personalInfoDetails.map((field) => {
          return {
            ...field,
            DOB: moment(field.DOB).isValid()
              ? moment(field.DOB).format(orgDateFormat)
              : "",
            Smokerasof: moment(field.Smokerasof).isValid()
              ? moment(field.Smokerasof).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("License") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Driving License Info");
        headers.push(this.drivingLicenseHeaders);
        let modifiedData = exportData.drivingLicenseDetails.map((field) => {
          return {
            ...field,
            License_Issue_Date: moment(field.License_Issue_Date).isValid()
              ? moment(field.License_Issue_Date).format(orgDateFormat)
              : "",
            License_Expiry_Date: moment(field.License_Expiry_Date).isValid()
              ? moment(field.License_Expiry_Date).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Dependent") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Dependent Details");
        headers.push(this.dependentHeaders);
        let dependentDetails = exportData.dependentDetails.map((dependent) => {
          return {
            ...dependent,
            Dependent_Name:
              dependent.Dependent_First_Name +
              " " +
              dependent.Dependent_Last_Name,
            Dependent_DOB: moment(dependent.Dependent_DOB).isValid()
              ? moment(dependent.Dependent_DOB).format(orgDateFormat)
              : "",
          };
        });
        data.push(dependentDetails || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Passport") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Passport Info");
        headers.push(this.passportHeaders);
        let modifiedData = exportData.passportDetails.map((field) => {
          return {
            ...field,
            Issue_Date: moment(field.Issue_Date).isValid()
              ? moment(field.Issue_Date).format(orgDateFormat)
              : "",
            Expiry_Date: moment(field.Expiry_Date).isValid()
              ? moment(field.Expiry_Date).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Job") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Job Info");
        let modifiedData = exportData.jobInfoDetails.map((field) => {
          return {
            ...field,
            Date_Of_Join: moment(field.Date_Of_Join).isValid()
              ? moment(field.Date_Of_Join).format(orgDateFormat)
              : "",
            Probation_Date: moment(field.Probation_Date).isValid()
              ? moment(field.Probation_Date).format(orgDateFormat)
              : "",
            Confirmed: field.Confirmed ? "Yes" : "No",
            Confirmation_Date: moment(field.Confirmation_Date).isValid()
              ? moment(field.Confirmation_Date).format(orgDateFormat)
              : "",
            Commission_Employee: field.Commission_Employee ? "Yes" : "No",
            Attendance_Enforced_Payment: field.Attendance_Enforced_Payment
              ? "Yes"
              : "No",
          };
          // return field;
        });
        headers.push(this.jobHeaders);
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Assets") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Asset Details");
        headers.push(this.AssetsHeaders);
        let modifiedData = exportData.assetDetails.map((field) => {
          return {
            ...field,
            Receive_Date: moment(field.Receive_Date).isValid()
              ? moment(field.Receive_Date).format(orgDateFormat)
              : "",
            Return_Date: moment(field.Return_Date).isValid()
              ? moment(field.Return_Date).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Experience") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Experience Info");
        headers.push(this.experienceHeaders);
        let modifiedData = exportData.experienceDetails.map((field) => {
          return {
            ...field,
            Start_Date_Join: moment(field.Start_Date_Join).isValid()
              ? moment(field.Start_Date_Join).format(orgDateFormat)
              : "",
            End_Date: moment(field.End_Date).isValid()
              ? moment(field.End_Date).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Contact") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Contact Details");
        headers.push(this.contactHeaders);
        data.push(exportData.contactInfoDetails || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Education") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Education Details");
        headers.push(this.educationHeaders);
        data.push(exportData.educationalInfoDetails || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Certifications") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Certification Details");
        headers.push(this.certificationHeaders);
        let modifiedData = exportData.certificateInfoDetails.map((field) => {
          return {
            ...field,
            Received_Date: moment(field.Received_Date).isValid()
              ? moment(field.Received_Date).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Training") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Training Details");
        headers.push(this.trainingHeaders);
        let modifiedData = exportData.trainingInfoDetails.map((field) => {
          return {
            ...field,
            Training_Start_Date: moment(field.Training_Start_Date).isValid()
              ? moment(field.Training_Start_Date).format(orgDateFormat)
              : "",
            Training_End_Date: moment(field.Training_End_Date).isValid()
              ? moment(field.Training_End_Date).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Awards") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Award Details");
        headers.push(this.awardHeaders);
        let modifiedData = exportData.awardDetails.map((field) => {
          return {
            ...field,
            Received_On: moment(field.Received_On).isValid()
              ? moment(field.Received_On).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Skills") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Skills");
        headers.push(this.skillHeaders);
        data.push(exportData.skillDetails || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Bank") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Bank Info");
        headers.push(this.bankHeaders);
        data.push(exportData.bankDetails || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Insurance") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Insurance Info");
        headers.push(this.insuranceHeaders);
        data.push(exportData.insuranceDetails || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Professional License") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Professional License Info");
        headers.push(this.accreditationHeaders);
        let modifiedData = exportData.accreditationDetails.map((field) => {
          return {
            ...field,
            Received_Date: moment(field.Received_Date).isValid()
              ? moment(field.Received_Date).format(orgDateFormat)
              : "",
            Expiry_Date: moment(field.Expiry_Date).isValid()
              ? moment(field.Expiry_Date).format(orgDateFormat)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Documents") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Documents");
        headers.push(this.documentHeaders);
        data.push(exportData.documentDetails || []);
      }
      if (
        (this.employeeDetailsMultiple.includes("Data Sync Status") ||
          this.employeeDetailsMultiple.includes("All")) &&
        this.sunfish?.toLowerCase() == "active"
      ) {
        sheets.push("Data Sync Status");
        if (this.deploymentType.toLowerCase() == "sunfish") {
          headers.push(this.sunfishHeaders);
          data.push(exportData.sunfishIntegrationDetails || []);
        } else {
          let modifiedData = exportData.sunfishIntegrationDetails.map(
            (item) => {
              let field = JSON.parse(item.Data_Sync_Result);
              return {
                User_Defined_EmpId: item.User_Defined_EmpId,
                Employee_Name: item.Employee_Name,
                Status: item.Status,
                Candidate_Email_Status: item.Candidate_Email_Status,
                Hiring_Manager_Email_Status: item.Hiring_Manager_Email_Status,
                Personal_Info_Sync_Status: field.Personal?.Sync_Status || "",
                Personal_Info_Failure_Reason:
                  field.Personal?.Failure_Reason || "",
                Personal_Info_Updated_On: field.Personal?.Updated_On || "",
                Additional_Info_Sync_Status:
                  field.Additional?.Sync_Status || "",
                Additional_Info_Failure_Reason:
                  field.Additional?.Failure_Reason || "",
                Additional_Info_Updated_On: field.Additional?.Updated_On || "",
                Education_Info_Sync_Status: field.Education?.Sync_Status || "",
                Education_Info_Failure_Reason:
                  field.Education?.Failure_Reason || "",
                Education_Info_Updated_On: field.Education?.Updated_On || "",
                Attachment_Info_Sync_Status:
                  field.Attachment?.Sync_Status || "",
                Attachment_Info_Failure_Reason:
                  field.Attachment?.Failure_Reason || "",
                Attachment_Info_Updated_On: field.Attachment?.Updated_On || "",
                Accreditation_Info_Sync_Status:
                  field.Accreditations?.Sync_Status || "",
                Accreditation_Info_Failure_Reason:
                  field.Accreditations?.Failure_Reason || "",
                Accreditation_Info_Updated_On:
                  field.Accreditations?.Updated_On || "",
                Certificate_Info_Sync_Status:
                  field.Certificate?.Sync_Status || "",
                Certificate_Info_Failure_Reason:
                  field.Certificate?.Failure_Reason || "",
                Certificate_Info_Updated_On:
                  field.Certificate?.Updated_On || "",
                Language_Info_Sync_Status: field.Language?.Sync_Status || "",
                Language_Info_Failure_Reason:
                  field.Language?.Failure_Reason || "",
                Language_Info_Updated_On: field.Language?.Updated_On || "",
                Previous_Info_Sync_Status:
                  field.PreviousEmployment?.Sync_Status || "",
                Previous_Info_Failure_Reason:
                  field.PreviousEmployment?.Failure_Reason || "",
                Previous_Info_Updated_On:
                  field.PreviousEmployment?.Updated_On || "",
                Updated_By_User_Defined_EmpId:
                  item.Updated_By_User_Defined_EmpId,
                Updated_By_Employee_Name: item.Updated_By_Employee_Name,
              };
            }
          );
          headers.push(this.PagtHeaders);
          data.push(modifiedData || []);
        }
      }
      if (
        (this.employeeDetailsMultiple.includes("Air Ticket Policy") ||
          this.employeeDetailsMultiple.includes("All")) &&
        this.airTicketFormAccess
      ) {
        sheets.push("Air Ticket Policy");
        headers.push(this.airTicketHeaders);
        let modifiedData = exportData.airTiketPolicy?.map((field) => {
          return {
            ...field,
            Air_Ticketing_Entitlement: `Infant-${field.Infant_Amount}, Child-${field.Child_Amount}, Adult-${field.Adult_Amount}`,
            Destination:
              field.Destination_City + " - " + field.Destination_Country,
            Dependent_Relationship: field.Dependent_Relationship
              ? JSON.parse(field.Dependent_Relationship).join(", ")
              : "",
            Effective_Date: field.Effective_Date
              ? this.formatDate(field.Effective_Date)
              : "",
            Last_Availed_Date: field.Last_Availed_Date
              ? this.formatDate(field.Last_Availed_Date)
              : "",
          };
        });
        data.push(modifiedData || []);
      }
      if (
        (this.employeeDetailsMultiple.includes(
          "Air Ticket Settlement Summary"
        ) ||
          this.employeeDetailsMultiple.includes("All")) &&
        this.airTicketFormAccess
      ) {
        sheets.push("Air Ticket Settlement Summary");
        headers.push(this.airTicketSettlementHeaders);
        let modifiedData = exportData.airTiketSettlementSummary?.map(
          (field) => {
            return {
              ...field,
              Accrual_Basis: `Infant-${field.Infant_Policy_Amount}, Child-${field.Child_Policy_Amount}, Adult-${field.Adult_Policy_Amount}`,
              Dependent_Relationship: field.Dependent_Relationship
                ? JSON.parse(field.Dependent_Relationship).join(", ")
                : "",
              Availed_Date: field.Availed_Date
                ? this.formatDate(field.Availed_Date)
                : "",
              Effective_Date: field.Effective_Date
                ? this.formatDate(field.Effective_Date)
                : "",
            };
          }
        );
        data.push(modifiedData || []);
      }
      if (
        this.employeeDetailsMultiple.includes("Custom Fields") ||
        this.employeeDetailsMultiple.includes("All")
      ) {
        sheets.push("Additional Details");
        let customHeaders = [
          {
            key: "User_Defined_EmpId",
            header: "Employee Id",
          },
          {
            key: "Employee_Name",
            header: "Employee Name",
          },
        ];
        let modifiedHeaders = [];
        exportData.customAdditional?.forEach((field) => {
          let fieldId = Object.keys(JSON.parse(field.Custom_Field_Value));
          fieldId.forEach((id) => {
            let fieldObj = this.customFields.find(
              (f) => f.Custom_Field_Id === parseInt(id)
            );

            if (fieldObj) {
              let headerObj = {
                header: fieldObj.Custom_Field_Name,
                key: fieldObj.Custom_Field_Id,
              };

              // Ensure uniqueness before pushing into modifiedHeaders
              if (!modifiedHeaders.some((h) => h.key === headerObj.key)) {
                modifiedHeaders.push(headerObj);
              }
            }
          });
        });
        customHeaders = customHeaders.concat(modifiedHeaders);
        headers.push(customHeaders);
        let modifiedData = exportData.customAdditional?.map((field) => {
          let fields = JSON.parse(field.Custom_Field_Value);
          return {
            ...field,
            ...fields,
          };
        });
        data.push(modifiedData || []);
      }
      this.exportOnboardingReportFile({
        sheets,
        data,
        headers,
        fileName:
          this.screen !== "summary"
            ? "Onboarding Employee Data"
            : "Employee Data",
      });
    },
    exportOnboardingReportFile(exportData) {
      this.exportMultipleSheets(exportData);
      this.closeModal();
    },
    closeModal() {
      this.employeeDetails = "";
      this.employeeDetailsMultiple = [];
      this.selectedDateRange = [];
      this.formattedSelectedDateRange = "";
      this.filterBy = "Active Employees";
      this.fields = [];
      this.selectedDepartment = ["All"];
      this.selectedDesignation = ["All"];
      this.selectedLocation = ["All"];
      this.sortBy = "Employee_Id";
      this.employeeDetails = "";
      mixpanel.track("MyTeam-exportWithFilter-form-closed");
      this.$emit("close-modal");
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    callDesignationList(searchString) {
      if (searchString.length >= 3) {
        this.getDesignationList(searchString);
      }
    },

    async getDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designations = this.designations.concat(designationResult);
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.designations = [];
        });
    },
  },
};
</script>
