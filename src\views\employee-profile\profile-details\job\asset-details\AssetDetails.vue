<template>
  <div ref="assetDetails">
    <div class="d-flex">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="primary"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Asset Details</span
        >
      </div>

      <span v-if="enableAdd" class="d-flex justify-end ml-auto">
        <v-btn
          color="primary"
          variant="text"
          @click="showAssetAddEditForm = true"
        >
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
        >
      </span>
    </div>

    <v-dialog
      v-if="showAssetAddEditForm"
      v-model="showAssetAddEditForm"
      width="80%"
      style="height: 100% !important"
      @click:outside="closeAddEditForm()"
    >
      <AddEditAssetDetails
        :assetDetails="selectedAssetDetails"
        :selectedEmpId="selectedEmpId"
        :selectedEmpDoj="selectedEmpDoj"
        :selectedEmpStatus="selectedEmpStatus"
        @close-asset-form="closeAddEditForm()"
        @refetch-asset-details="submitAssetFormData()"
      >
      </AddEditAssetDetails>
    </v-dialog>
    <div v-if="!isMobileView" class="d-flex">
      <v-slide-group
        class="pa-4"
        selected-class="bg-secondary"
        prev-icon="fas fa-chevron-circle-left"
        next-icon="fas fa-chevron-circle-right"
        show-arrows
      >
        <v-slide-group-item>
          <ViewAssetDetails
            :assetDetails="assetDetails"
            :formAccess="formAccess"
            :empFormUpdateAccess="empFormUpdateAccess"
            @on-open-edit="openEditForm($event)"
            @on-delete="openWarningPopUp($event)"
          ></ViewAssetDetails>
        </v-slide-group-item>
      </v-slide-group>
    </div>
    <div v-else>
      <div class="card-container">
        <ViewAssetDetails
          :assetDetails="assetDetails"
          :formAccess="formAccess"
          :empFormUpdateAccess="empFormUpdateAccess"
          @on-open-edit="openEditForm($event)"
          @on-delete="openWarningPopUp($event)"
        ></ViewAssetDetails>
      </div>
    </div>
  </div>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure you want to delete this record ?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onDelateAsset()"
  >
  </AppWarningModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
// components
const AddEditAssetDetails = defineAsyncComponent(() =>
  import("./AddEditAssetDetails.vue")
);
const ViewAssetDetails = defineAsyncComponent(() =>
  import("./ViewAssetDetails.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
export default {
  name: "JobInfo",

  components: {
    AddEditAssetDetails,
    ViewAssetDetails,
  },

  props: {
    assetDetailsData: {
      type: Array,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["refetch-job-details"],
  data() {
    return {
      assetDetails: [],
      selectedAssetDeleteRecord: {},
      showAssetAddEditForm: false,
      selectedAssetDetails: {},
      openWarningModal: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    employeeEdit() {
      return this.$store.state.orgDetails.employeeEdit;
    },
    enableAdd() {
      return (
        (this.empFormUpdateAccess && this.employeeEdit) ||
        (this.formAccess &&
          this.formAccess.add &&
          this.formAccess.admin === "admin")
      );
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.assetDetailsData && this.assetDetailsData.length > 0) {
      this.assetDetails = this.assetDetailsData;
    }
  },

  methods: {
    submitAssetFormData() {
      this.$emit("refetch-job-details");
      this.closeAddEditForm();
    },

    closeAddEditForm() {
      this.showAssetAddEditForm = false;
      mixpanel.track("EmpProfile-asset-edit-closed");
      this.selectedAssetDetails = {};
    },

    openEditForm(selectedItem) {
      this.selectedAssetDetails = selectedItem;
      mixpanel.track("EmpProfile-asset-edit-opened");
      this.showAssetAddEditForm = true;
    },

    onDelateAsset() {
      this.openWarningModal = false;
    },

    openWarningPopUp(selectedItem) {
      this.openWarningModal = true;
      this.selectedAssetDeleteRecord = selectedItem;
    },

    //this method closes the delete confirmation popup
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedAssetDeleteRecord = null;
    },
  },
};
</script>
<style scoped>
.bottom-navigation :deep() .v-bottom-navigation__content {
  background-color: white;
  justify-content: flex-start !important;
  align-items: center !important;
}
.bottom-navigation :deep() .v-bottom-navigation__content > .v-btn {
  font-size: inherit;
  height: 45px;
  max-width: 120px;
  min-width: 100px;
  font-size: 1.2rem;
  text-transform: none;
  transition: inherit;
  width: auto;
  border-radius: 0;
  margin-left: 20px !important;
}
</style>
