<template>
  <div>
    <div class="tax-alert-content">
      <div
        v-if="planCurrentStatus === 'trial-subscription-skipped-card-not-exist'"
        class="d-flex text-center"
      >
        <p
          style="padding: 0.5em; margin: 0px; word-break: break-word"
          class="font-weight-bold"
        >
          {{ trialPeriodMessage }}
          Please add the card details to renew the subscription before it
          expires.
        </p>
        <v-btn
          v-if="!isMobileView"
          id="desktop_pay_subscribe_now"
          size="small"
          rounded="lg"
          class="bg-white text-blue"
          @click="addCardDetails()"
        >
          Subscribe Now
        </v-btn>
      </div>
      <div
        v-else-if="
          planCurrentStatus === 'trial-subscription-not-expired-card-exist'
        "
      >
        <p
          style="padding: 0.5em; margin: 0px; word-break: break-word"
          class="font-weight-bold"
        >
          {{ trialPeriodMessage }}
        </p>
      </div>
      <div
        v-else-if="
          planCurrentStatus === 'trial-subscription-expired-card-not-exist'
        "
        class="text-center"
      >
        <p
          style="padding: 0.5em; margin: 0px; word-break: break-word"
          class="font-weight-bold"
        >
          Your free trial has expired. Please add the card details to renew your
          subscription.
        </p>
      </div>
      <div
        v-else-if="planCurrentStatus === 'active-subscription-expired'"
        class="text-center"
      >
        <p
          style="padding: 0.5em; margin: 0px; word-break: break-word"
          class="font-weight-bold"
        >
          Payment is failed. Please validate your card details for payment to
          succeed and avoid suspension of account and removal of user data.
        </p>
      </div>
    </div>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
export default {
  name: "TrialNotification",
  data: () => ({
    isLoading: false,
    cbInstance: "",
    checkoutStep: "",
    hostedPageId: "",
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // current status of auto-billing plan
    planCurrentStatus() {
      return this.$store.state.autoBillingPlanCurrentStatus;
    },
    // remaining trial days
    remainingTrialDays() {
      return this.$store.state.trialPeriod;
    },
    // trial message based on remaining trial period
    trialPeriodMessage() {
      if (this.remainingTrialDays > 0) {
        return `Your free trial will expire within ${this.remainingTrialDays} days(s).`;
      }
      return "Your free trial will expire soon.";
    },
    // charge bee site name based on stating/prod
    chargeBeeSiteName() {
      return this.$store.state.chargeBeeSiteName;
    },
    // auto-billing subscription details
    autoBillingDetails() {
      return this.$store.state.autoBillingDetails;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    // if chargebee script is not loaded already, if not we have to load it on header
    if (!window.Chargebee) {
      // add chargebee script in the header
      let chargeBeeScript = document.createElement("script");
      chargeBeeScript.setAttribute(
        "src",
        "https://js.chargebee.com/v2/chargebee.js"
      );
      document.head.appendChild(chargeBeeScript);
    }
  },
  methods: {
    // when add card details is clicked we need to fetch change subscription hosted page
    async addCardDetails() {
      let vm = this;
      vm.cbInstance = window.Chargebee.init({ site: this.chargeBeeSiteName });
      vm.isLoading = true;
      const { planId, subscriptionId, subscribedPlanQuantity } =
        this.autoBillingDetails;
      try {
        await vm.$store
          .dispatch("billing/changeSubscription", {
            subscriptionId: subscriptionId,
            planId: planId,
            planEmployeeCount: subscribedPlanQuantity,
            source: "addcard",
          })
          .then((hostedPageDetails) => {
            vm.openCheckout(hostedPageDetails);
            vm.isLoading = false;
          })
          .catch((error) => {
            let snackbarData = {
              isOpen: true,
              message: "",
              type: "warning",
            };
            if (error === "accessDenied") {
              snackbarData["message"] =
                "Sorry, you don't have access to add the card details. Please contact your administrator.";
            } else {
              snackbarData["message"] = error;
            }
            vm.isLoading = false;
            vm.showAlert(snackbarData);
          });
      } catch (catchError) {
        let snackbarData = {
          isOpen: true,
          message:
            "Something went wrong while processing your request. If you continue to see this problem please talk to your platform administrator.",
          type: "warning",
        };
        vm.isLoading = false;
        vm.showAlert(snackbarData);
      }
    },

    // open update card hosted page using chargebee pre-defined functions
    openCheckout(hostedPageDetails) {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      this.cbInstance.openCheckout({
        // it should return promise to handle response
        hostedPage: function () {
          return new Promise(function (resolve) {
            let hostedPage = hostedPageDetails.hosted_page;
            resolve(hostedPage);
          });
        },
        // popup was closed
        close: () => {
          // thankyou_screen - last page in checkout popup
          if (this.checkoutStep === "thankyou_screen" || this.hostedPageId) {
            window.location.href = this.baseUrl + "in/billing";
          } else {
            snackbarData.message = "Subscription hasn’t been completed";
            this.showAlert(snackbarData);
          }
        },
        // after successful payment
        success: (hostedPageId) => {
          this.$store.dispatch("getOrganizationSubscriptionDetails");
          this.hostedPageId = hostedPageId;
        },
        // current step opened
        step: (step) => {
          this.checkoutStep = step;
        },
        // error in checkout popup
        error: () => {
          let snackbarData = {
            isOpen: true,
            message: "Something went wrong. Please try after some time.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        },
      });
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
