import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const VIEW_ATTENDANCE_CONFIGURATION = gql`
  query viewAttendanceConfiguration {
    viewAttendanceConfiguration {
      errorCode
      message
      configurationDetails {
        employeeRegularizationCutOffDays
        employeeRegularizationRequestLimit
        attendanceApprovalCutOffDaysForManager
        updatedOn
        updatedByEmployeeName
      }
    }
  }
`;

// ===============
// Mutations
// ===============

export const UPDATE_ATTENDANCE_CONFIGURATION = gql`
  mutation updateAttendanceConfiguration(
    $employeeRegularizationCutOffDays: Int!
    $employeeRegularizationRequestLimit: Int!
    $attendanceApprovalCutOffDaysForManager: Int!
  ) {
    updateAttendanceConfiguration(
      employeeRegularizationCutOffDays: $employeeRegularizationCutOffDays
      employeeRegularizationRequestLimit: $employeeRegularizationRequestLimit
      attendanceApprovalCutOffDaysForManager: $attendanceApprovalCutOffDaysForManager
    ) {
      errorCode
      message
    }
  }
`;
