import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_ALL_RESIGNATION = gql`
  query getAllResignation($envelope: Envelope!, $filter: Filter!) {
    getAllResignation(envelope: $envelope, filter: $filter) {
      error {
        code
        message
      }
      result {
        resignationId
        employeeId
        userDefinedEmpId
        employeeName
        employeeGender
        employeePhotoPath
        employeeIsManager
        employeeDepartmentId
        employeeDepartmentName
        employeeDesignationId
        employeeDesignationName
        approverId
        approverName
        resignationStatus
        appliedDate
        resignationDate
        addedOn
        addedUserId
        addedUserName
        updatedOn
        updatedUserId
        updatedUserName
        workflowInstanceId
        workflowStatus
        reasonId
        esicReasonName
        relievingReasonComment
        withdrawnCancellationComment
        locationId
        locationName
        empTypeId
        employeeType
        fileName
      }
    }
  }
`;

export const GET_ALL_EMPLOYEES = gql`
  query getAllEmployees($envelope: Envelope!, $filter: EmployeeFilter!) {
    getAllEmployees(envelope: $envelope, filter: $filter) {
      error {
        code
        message
      }
      result {
        id
        idPrefix
        name
        photoPath
        designationId
        designationName
        departmentId
        departmentName
        locationName
        empTypeId
        employeeType
        isManager
        gender
        status
      }
      relievingReasonDetails {
        reasonId
        esicReasonName
      }
    }
  }
`;

export const GET_EMPLOYEE_CURRENT_RESIGNATION = gql`
  query getEmployeeCurrentResignation($envelope: Envelope!, $employeeId: Int!) {
    getEmployeeCurrentResignation(
      envelope: $envelope
      employeeId: $employeeId
    ) {
      error {
        code
        message
      }
      result {
        resignation {
          resignationId
          employeeId
          employeeName
          employeeGender
          employeePhotoPath
          employeeIsManager
          employeeDepartmentId
          employeeDepartmentName
          employeeDesignationId
          employeeDesignationName
          approverId
          approverName
          resignationStatus
          appliedDate
          resignationDate
          addedOn
          addedUserId
          addedUserName
          updatedOn
          updatedUserId
          updatedUserName
          workflowInstanceId
        }
        dynamicFormTemplate {
          templateName
          template
          conversational
        }
        dynamicFormResponse {
          formStatus
          formResponseId
          formResponse
          addedUserId
          addedUserName
          addedOn
          updatedUserId
          updatedUserName
          updatedOn
        }
        workflow {
          workflowInstanceId
          workflowTaskId
        }
      }
    }
  }
`;

export const GET_EMPLOYEE_NOTIFICATION_PERIOD_DAYS = gql`
  query getEmployeeNotificationPeriodDays(
    $envelope: Envelope!
    $employeeId: Int!
  ) {
    getEmployeeNotificationPeriodDays(
      envelope: $envelope
      employeeId: $employeeId
    ) {
      error {
        message
        code
      }
      result
    }
  }
`;

export const GET_EMPLOYEE_RESIGNATION = gql`
  query getEmployeeResignation($envelope: Envelope!, $resignationId: Int!) {
    getEmployeeResignation(envelope: $envelope, resignationId: $resignationId) {
      error {
        code
        message
      }
      result {
        resignationId
        employeeId
        employeeName
        employeeGender
        empStatus
        employeePhotoPath
        employeeIsManager
        employeeDepartmentId
        employeeDepartmentName
        employeeDesignationId
        employeeDesignationName
        approverId
        approverName
        resignationStatus
        appliedDate
        resignationDate
        addedOn
        addedUserId
        addedUserName
        updatedOn
        updatedUserId
        updatedUserName
        employeeIdWithPrefix
        reasonId
        esicReasonName
        relievingReasonComment
        withdrawnCancellationComment
        isPayslipGenerated
      }
      relievingReasonDetails {
        reasonId
        esicReasonName
      }
    }
  }
`;

// ===============
// Mutations
// ===============

export const WITHDRAWN_CANCEL_RESIGNATION = gql`
  mutation withdrawnCancelResignation(
    $envelope: Envelope!
    $resignationId: Int!
    $approvalStatus: String!
    $comment: String!
  ) {
    withdrawnCancelResignation(
      envelope: $envelope
      resignationId: $resignationId
      comment: $comment
      approvalStatus: $approvalStatus
    ) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          code
          message
        }
      }
    }
  }
`;

export const UPDATE_RESIGNATION_DATE = gql`
  mutation updateResignationDate(
    $envelope: Envelope!
    $resignationId: Int!
    $resignationDate: Date!
    $appliedDate: Date!
    $fileName: String
  ) {
    updateResignationDate(
      envelope: $envelope
      resignationId: $resignationId
      resignationDate: $resignationDate
      appliedDate: $appliedDate
      fileName: $fileName
    ) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          code
          message
        }
      }
    }
  }
`;

export const UPDATE_RESIGNATION_REASON = gql`
  mutation updateResignationReason(
    $envelope: Envelope!
    $resignationId: Int!
    $esicReason: String
    $reasonId: Int!
    $relievingReasonComment: String
  ) {
    updateResignationReason(
      envelope: $envelope
      resignationId: $resignationId
      esicReason: $esicReason
      reasonId: $reasonId
      relievingReasonComment: $relievingReasonComment
    ) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          code
          message
        }
      }
    }
  }
`;

export const CREATE_RESIGNATION = gql`
  mutation createResignation(
    $envelope: Envelope!
    $employeeId: Int!
    $resignationDate: Date!
    $appliedDate: Date!
    $esicReason: String
    $reasonId: Int!
    $relievingReasonComment: String
    $fileName: String
  ) {
    createResignation(
      envelope: $envelope
      employeeId: $employeeId
      resignationDate: $resignationDate
      appliedDate: $appliedDate
      esicReason: $esicReason
      reasonId: $reasonId
      relievingReasonComment: $relievingReasonComment
      fileName: $fileName
    ) {
      error {
        code
        message
      }
      result {
        success
        validation {
          code
          message
        }
        message
        resignation {
          resignationId
          resignationDate
          appliedDate
          resignationStatus
        }
        dynamicFormTemplate {
          templateName
          template
          conversational
        }
        workflow {
          workflowInstanceId
          workflowTaskId
        }
      }
    }
  }
`;
