@font-face {
  font-family: 'hrapp';
  src:  url('fonts/hrapp.eot?ti6stz');
  src:  url('fonts/hrapp.eot?ti6stz#iefix') format('embedded-opentype'),
    url('fonts/hrapp.ttf?ti6stz') format('truetype'),
    url('fonts/hrapp.woff?ti6stz') format('woff'),
    url('fonts/hrapp.svg?ti6stz#hrapp') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="hr-"], [class*=" hr-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'hrapp' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hr-approvals:before {
  content: "\edce";
}
.hr-approvals-approval-management:before {
  content: "\edcf";
}
.hr-my-finance-my-pay:before {
  content: "\e900";
}
.hr-roster-management-shift-swap:before {
  content: "\e901";
}
.hr-data-loss-prevention-location-intelligence:before {
  content: "\e902";
}
.hr-settings-recruitment:before {
  content: "\e903";
}
.hr-productivity-monitoring-workforce-analytics:before {
  content: "\e904";
}
.hr-benefits-esop:before {
  content: "\e905";
}
.hr-benefits:before {
  content: "\e906";
}
.hr-px:before {
  content: "\e907";
}
.hr-account-code:before {
  content: "\e908";
}
.hr-account-schema-mapping:before {
  content: "\e909";
}
.hr-account-type:before {
  content: "\e90a";
}
.hr-add:before {
  content: "\e90b";
}
.hr-address-book:before {
  content: "\e90c";
}
.hr-address-book-o:before {
  content: "\e90d";
}
.hr-address-card:before {
  content: "\e90e";
}
.hr-address-card-o:before {
  content: "\e90f";
}
.hr-adjust:before {
  content: "\e910";
}
.hr-admin-roles:before {
  content: "\e911";
}
.hr-adn:before {
  content: "\e912";
}
.hr-airport-plane:before {
  content: "\e913";
}
.hr-alert:before {
  content: "\e914";
}
.hr-alert-1:before {
  content: "\e915";
}
.hr-alert-2:before {
  content: "\e916";
}
.hr-alert-3:before {
  content: "\e917";
}
.hr-alert-circled:before {
  content: "\e918";
}
.hr-align-center:before {
  content: "\e919";
}
.hr-align-justify:before {
  content: "\e91a";
}
.hr-align-left:before {
  content: "\e91b";
}
.hr-align-right:before {
  content: "\e91c";
}
.hr-amazon:before {
  content: "\e91d";
}
.hr-ambulance:before {
  content: "\e91e";
}
.hr-american-sign-language-interpreting:before {
  content: "\e91f";
}
.hr-analytics-file-1:before {
  content: "\e920";
}
.hr-anchor:before {
  content: "\e921";
}
.hr-android:before {
  content: "\e922";
}
.hr-android-attach:before {
  content: "\e923";
}
.hr-android-bulb:before {
  content: "\e924";
}
.hr-android-done:before {
  content: "\e925";
}
.hr-android-done-all:before {
  content: "\e926";
}
.hr-angellist:before {
  content: "\e927";
}
.hr-angle-double-down:before {
  content: "\e928";
}
.hr-angle-double-left:before {
  content: "\e929";
}
.hr-angle-double-right:before {
  content: "\e92a";
}
.hr-angle-double-up:before {
  content: "\e92b";
}
.hr-angle-down:before {
  content: "\e92c";
}
.hr-angle-left:before {
  content: "\e92d";
}
.hr-angle-right:before {
  content: "\e92e";
}
.hr-angle-up:before {
  content: "\e92f";
}
.hr-apple:before {
  content: "\e930";
}
.hr-archive:before {
  content: "\e931";
}
.hr-area-chart:before {
  content: "\e932";
}
.hr-arrow-circle-down:before {
  content: "\e933";
}
.hr-arrow-circle-left:before {
  content: "\e934";
}
.hr-arrow-circle-o-down:before {
  content: "\e935";
}
.hr-arrow-circle-o-left:before {
  content: "\e936";
}
.hr-arrow-circle-o-right:before {
  content: "\e937";
}
.hr-arrow-circle-o-up:before {
  content: "\e938";
}
.hr-arrow-circle-right:before {
  content: "\e939";
}
.hr-arrow-circle-up:before {
  content: "\e93a";
}
.hr-arrow-down:before {
  content: "\e93b";
}
.hr-arrow-left:before {
  content: "\e93c";
}
.hr-arrow-right:before {
  content: "\e93d";
}
.hr-arrows:before {
  content: "\e93e";
}
.hr-arrows-alt:before {
  content: "\e93f";
}
.hr-arrows-h:before {
  content: "\e940";
}
.hr-arrows-v:before {
  content: "\e941";
}
.hr-arrow-up:before {
  content: "\e942";
}
.hr-asset-management:before {
  content: "\e943";
}
.hr-asset-management-assets:before {
  content: "\e944";
}
.hr-assistive-listening-systems:before {
  content: "\e945";
}
.hr-asterisk:before {
  content: "\e946";
}
.hr-at:before {
  content: "\e947";
}
.hr-attendance-box:before {
  content: "\e948";
}
.hr-audio-description:before {
  content: "\e949";
}
.hr-automobile:before {
  content: "\e94a";
}
.hr-backward:before {
  content: "\e94b";
}
.hr-balance-scale:before {
  content: "\e94c";
}
.hr-ban:before {
  content: "\e94d";
}
.hr-ban1:before {
  content: "\e94e";
}
.hr-bandcamp:before {
  content: "\e94f";
}
.hr-bank:before {
  content: "\e950";
}
.hr-bar-chart:before {
  content: "\e951";
}
.hr-barcode:before {
  content: "\e952";
}
.hr-bars:before {
  content: "\e953";
}
.hr-bath:before {
  content: "\e954";
}
.hr-battery:before {
  content: "\e955";
}
.hr-battery-0:before {
  content: "\e956";
}
.hr-battery-1:before {
  content: "\e957";
}
.hr-battery-2:before {
  content: "\e958";
}
.hr-battery-3:before {
  content: "\e959";
}
.hr-bed:before {
  content: "\e95a";
}
.hr-beer:before {
  content: "\e95b";
}
.hr-behance:before {
  content: "\e95c";
}
.hr-behance-square:before {
  content: "\e95d";
}
.hr-bell:before {
  content: "\e95e";
}
.hr-bell1:before {
  content: "\e95f";
}
.hr-bell-1:before {
  content: "\e960";
}
.hr-bell-o:before {
  content: "\e961";
}
.hr-bell-o1:before {
  content: "\e962";
}
.hr-bell-slash:before {
  content: "\e963";
}
.hr-bell-slash-o:before {
  content: "\e964";
}
.hr-bell-two:before {
  content: "\e965";
}
.hr-bicycle:before {
  content: "\e966";
}
.hr-billing:before {
  content: "\e967";
}
.hr-billing-billing:before {
  content: "\e968";
}
.hr-binoculars:before {
  content: "\e969";
}
.hr-birthday-cake:before {
  content: "\e96a";
}
.hr-birthday-cake1:before {
  content: "\e96b";
}
.hr-bitbucket:before {
  content: "\e96c";
}
.hr-bitbucket-square:before {
  content: "\e96d";
}
.hr-bitcoin:before {
  content: "\e96e";
}
.hr-black-tie:before {
  content: "\e96f";
}
.hr-blind:before {
  content: "\e970";
}
.hr-bluetooth:before {
  content: "\e971";
}
.hr-bluetooth-b:before {
  content: "\e972";
}
.hr-bold:before {
  content: "\e973";
}
.hr-bolt:before {
  content: "\e974";
}
.hr-bomb:before {
  content: "\e975";
}
.hr-book:before {
  content: "\e976";
}
.hr-bookmark:before {
  content: "\e977";
}
.hr-bookmark-o:before {
  content: "\e978";
}
.hr-braille:before {
  content: "\e979";
}
.hr-briefcase:before {
  content: "\e97a";
}
.hr-broadcast:before {
  content: "\e97b";
}
.hr-bug:before {
  content: "\e97c";
}
.hr-bug1:before {
  content: "\e97d";
}
.hr-building:before {
  content: "\e97e";
}
.hr-building-o:before {
  content: "\e97f";
}
.hr-bulk-copy:before {
  content: "\e980";
}
.hr-bullhorn:before {
  content: "\e981";
}
.hr-bullseye:before {
  content: "\e982";
}
.hr-bus:before {
  content: "\e983";
}
.hr-buysellads:before {
  content: "\e984";
}
.hr-cab:before {
  content: "\e985";
}
.hr-calculator:before {
  content: "\e986";
}
.hr-calculator1:before {
  content: "\e987";
}
.hr-calendar:before {
  content: "\e988";
}
.hr-calendar1:before {
  content: "\e989";
}
.hr-calendar-1:before {
  content: "\e98a";
}
.hr-calendar-alt-fill:before {
  content: "\e98b";
}
.hr-calendar-check-o:before {
  content: "\e98c";
}
.hr-calendar-minus-o:before {
  content: "\e98d";
}
.hr-calendar-o:before {
  content: "\e98e";
}
.hr-calendar-plus-o:before {
  content: "\e98f";
}
.hr-calendar-times-o:before {
  content: "\e990";
}
.hr-camera:before {
  content: "\e991";
}
.hr-camera-retro:before {
  content: "\e992";
}
.hr-cancel-circle:before {
  content: "\e993";
}
.hr-caret-down:before {
  content: "\e994";
}
.hr-caret-left:before {
  content: "\e995";
}
.hr-caret-right:before {
  content: "\e996";
}
.hr-caret-square-o-down:before {
  content: "\e997";
}
.hr-caret-square-o-left:before {
  content: "\e998";
}
.hr-caret-square-o-right:before {
  content: "\e999";
}
.hr-caret-square-o-up:before {
  content: "\e99a";
}
.hr-caret-up:before {
  content: "\e99b";
}
.hr-cart-arrow-down:before {
  content: "\e99c";
}
.hr-cart-plus:before {
  content: "\e99d";
}
.hr-cc:before {
  content: "\e99e";
}
.hr-cc-amex:before {
  content: "\e99f";
}
.hr-cc-diners-club:before {
  content: "\e9a0";
}
.hr-cc-discover:before {
  content: "\e9a1";
}
.hr-cc-jcb:before {
  content: "\e9a2";
}
.hr-cc-mastercard:before {
  content: "\e9a3";
}
.hr-cc-paypal:before {
  content: "\e9a4";
}
.hr-cc-stripe:before {
  content: "\e9a5";
}
.hr-cc-visa:before {
  content: "\e9a6";
}
.hr-certificate:before {
  content: "\e9a7";
}
.hr-chain:before {
  content: "\e9a8";
}
.hr-chain-broken:before {
  content: "\e9a9";
}
.hr-check:before {
  content: "\e9aa";
}
.hr-check1:before {
  content: "\e9ab";
}
.hr-check-all:before {
  content: "\e9ac";
}
.hr-check-circle:before {
  content: "\e9ad";
}
.hr-check-circle1:before {
  content: "\e9ae";
}
.hr-check-circle-o:before {
  content: "\e9af";
}
.hr-check-square:before {
  content: "\e9b0";
}
.hr-check-square-o:before {
  content: "\e9b1";
}
.hr-chevron-circle-down:before {
  content: "\e9b2";
}
.hr-chevron-circle-left:before {
  content: "\e9b3";
}
.hr-chevron-circle-right:before {
  content: "\e9b4";
}
.hr-chevron-circle-up:before {
  content: "\e9b5";
}
.hr-chevron-down:before {
  content: "\e9b6";
}
.hr-chevron-left:before {
  content: "\e9b7";
}
.hr-chevron-right:before {
  content: "\e9b8";
}
.hr-chevron-up:before {
  content: "\e9b9";
}
.hr-child:before {
  content: "\e9ba";
}
.hr-chrome:before {
  content: "\e9bb";
}
.hr-circle:before {
  content: "\e9bc";
}
.hr-circle-o:before {
  content: "\e9bd";
}
.hr-circle-o-notch:before {
  content: "\e9be";
}
.hr-circle-thin:before {
  content: "\e9bf";
}
.hr-clipboard:before {
  content: "\e9c0";
}
.hr-clock-o:before {
  content: "\e9c1";
}
.hr-clone:before {
  content: "\e9c2";
}
.hr-clone-roles:before {
  content: "\e9c3";
}
.hr-close:before {
  content: "\e9c4";
}
.hr-close1:before {
  content: "\e9c5";
}
.hr-close-1:before {
  content: "\e9c6";
}
.hr-close-round:before {
  content: "\e9c7";
}
.hr-cloud:before {
  content: "\e9c8";
}
.hr-cloud-download:before {
  content: "\e9c9";
}
.hr-cloud-download1:before {
  content: "\e9ca";
}
.hr-cloud-upload:before {
  content: "\e9cb";
}
.hr-cloud-upload1:before {
  content: "\e9cc";
}
.hr-cny:before {
  content: "\e9cd";
}
.hr-code:before {
  content: "\e9ce";
}
.hr-code-fork:before {
  content: "\e9cf";
}
.hr-codepen:before {
  content: "\e9d0";
}
.hr-codiepie:before {
  content: "\e9d1";
}
.hr-coffee:before {
  content: "\e9d2";
}
.hr-cog:before {
  content: "\e9d3";
}
.hr-cogs:before {
  content: "\e9d4";
}
.hr-cog-solid:before {
  content: "\e9d5";
}
.hr-columns:before {
  content: "\e9d6";
}
.hr-comment:before {
  content: "\e9d7";
}
.hr-commenting:before {
  content: "\e9d8";
}
.hr-commenting-o:before {
  content: "\e9d9";
}
.hr-comment-o:before {
  content: "\e9da";
}
.hr-comments:before {
  content: "\e9db";
}
.hr-comments-o:before {
  content: "\e9dc";
}
.hr-compass:before {
  content: "\e9dd";
}
.hr-compress:before {
  content: "\e9de";
}
.hr-connectdevelop:before {
  content: "\e9df";
}
.hr-contacs:before {
  content: "\e9e0";
}
.hr-contact:before {
  content: "\e9e1";
}
.hr-contact-2:before {
  content: "\e9e2";
}
.hr-contact-add:before {
  content: "\e9e3";
}
.hr-contact-add-2:before {
  content: "\e9e4";
}
.hr-contact-add-3:before {
  content: "\e9e5";
}
.hr-contact-big:before {
  content: "\e9e6";
}
.hr-contact-details:before {
  content: "\e9e7";
}
.hr-contacts:before {
  content: "\e9e8";
}
.hr-contao:before {
  content: "\e9e9";
}
.hr-copy:before {
  content: "\e9ea";
}
.hr-copy1:before {
  content: "\e9eb";
}
.hr-copyright:before {
  content: "\e9ec";
}
.hr-creative-commons:before {
  content: "\e9ed";
}
.hr-credit-card:before {
  content: "\e9ee";
}
.hr-credit-card-alt:before {
  content: "\e9ef";
}
.hr-crop:before {
  content: "\e9f0";
}
.hr-crosshairs:before {
  content: "\e9f1";
}
.hr-cross-mark:before {
  content: "\e9f2";
}
.hr-crown-king-streamline:before {
  content: "\e9f3";
}
.hr-css3:before {
  content: "\e9f4";
}
.hr-csv:before {
  content: "\e9f5";
}
.hr-cube:before {
  content: "\e9f6";
}
.hr-cubes:before {
  content: "\e9f7";
}
.hr-cut:before {
  content: "\e9f8";
}
.hr-cutlery:before {
  content: "\e9f9";
}
.hr-danger:before {
  content: "\e9fa";
}
.hr-dashboard:before {
  content: "\e9fb";
}
.hr-dashcube:before {
  content: "\e9fc";
}
.hr-database:before {
  content: "\e9fd";
}
.hr-database1:before {
  content: "\e9fe";
}
.hr-database-add:before {
  content: "\e9ff";
}
.hr-database-edit:before {
  content: "\ea00";
}
.hr-database-information:before {
  content: "\ea01";
}
.hr-database-remove:before {
  content: "\ea02";
}
.hr-database-run:before {
  content: "\ea03";
}
.hr-database-security:before {
  content: "\ea04";
}
.hr-deaf:before {
  content: "\ea05";
}
.hr-dedent:before {
  content: "\ea06";
}
.hr-delete:before {
  content: "\ea07";
}
.hr-delicious:before {
  content: "\ea08";
}
.hr-department:before {
  content: "\ea09";
}
.hr-desktop:before {
  content: "\ea0a";
}
.hr-deviantart:before {
  content: "\ea0b";
}
.hr-diamond:before {
  content: "\ea0c";
}
.hr-diff:before {
  content: "\ea0d";
}
.hr-digg:before {
  content: "\ea0e";
}
.hr-document-sans-accept:before {
  content: "\ea0f";
}
.hr-document-sans-add:before {
  content: "\ea10";
}
.hr-document-sans-cancel:before {
  content: "\ea11";
}
.hr-document-sans-down:before {
  content: "\ea12";
}
.hr-document-sans-edit:before {
  content: "\ea13";
}
.hr-document-sans-information:before {
  content: "\ea14";
}
.hr-document-sans-remove:before {
  content: "\ea15";
}
.hr-document-sans-run:before {
  content: "\ea16";
}
.hr-document-sans-security:before {
  content: "\ea17";
}
.hr-document-sans-settings:before {
  content: "\ea18";
}
.hr-document-sans-up:before {
  content: "\ea19";
}
.hr-document-text:before {
  content: "\ea1a";
}
.hr-document-text-add:before {
  content: "\ea1b";
}
.hr-document-text-down:before {
  content: "\ea1c";
}
.hr-document-text-information:before {
  content: "\ea1d";
}
.hr-document-text-remove:before {
  content: "\ea1e";
}
.hr-document-text-run:before {
  content: "\ea1f";
}
.hr-document-text-security:before {
  content: "\ea20";
}
.hr-document-text-up:before {
  content: "\ea21";
}
.hr-dollar:before {
  content: "\ea22";
}
.hr-donate:before {
  content: "\ea23";
}
.hr-dot-circle-o:before {
  content: "\ea24";
}
.hr-download:before {
  content: "\ea25";
}
.hr-download1:before {
  content: "\ea26";
}
.hr-download-1:before {
  content: "\ea27";
}
.hr-download-2:before {
  content: "\ea28";
}
.hr-download-accept:before {
  content: "\ea29";
}
.hr-download-cancel:before {
  content: "\ea2a";
}
.hr-download-information:before {
  content: "\ea2b";
}
.hr-dribbble:before {
  content: "\ea2c";
}
.hr-drivers-license:before {
  content: "\ea2d";
}
.hr-drivers-license-o:before {
  content: "\ea2e";
}
.hr-dropbox:before {
  content: "\ea2f";
}
.hr-drupal:before {
  content: "\ea30";
}
.hr-edge:before {
  content: "\ea31";
}
.hr-edit:before {
  content: "\ea32";
}
.hr-edit1:before {
  content: "\ea33";
}
.hr-eercast:before {
  content: "\ea34";
}
.hr-eject:before {
  content: "\ea35";
}
.hr-electric-no-off:before {
  content: "\ea36";
}
.hr-ellipsis-h:before {
  content: "\ea37";
}
.hr-ellipsis-v:before {
  content: "\ea38";
}
.hr-empire:before {
  content: "\ea39";
}
.hr-employee-awards-1:before {
  content: "\ea3a";
}
.hr-employee-clone:before {
  content: "\ea3b";
}
.hr-employee-confirmation:before {
  content: "\ea3c";
}
.hr-employee-designation:before {
  content: "\ea3d";
}
.hr-employee-probation:before {
  content: "\ea3e";
}
.hr-employees:before {
  content: "\ea3f";
}
.hr-employees-assignments:before {
  content: "\ea40";
}
.hr-employees-attendance:before {
  content: "\ea41";
}
.hr-employees-awards:before {
  content: "\ea42";
}
.hr-employees-compensatory-off:before {
  content: "\ea43";
}
.hr-employees-complaints:before {
  content: "\ea44";
}
.hr-employees-custom-employee-groups:before {
  content: "\ea45";
}
.hr-employees-designations:before {
  content: "\ea46";
}
.hr-employees-employee-bank-account:before {
  content: "\ea47";
}
.hr-employees-employees:before {
  content: "\ea48";
}
.hr-employees-employees-document-upload:before {
  content: "\ea49";
}
.hr-employees-employee-travel:before {
  content: "\ea4a";
}
.hr-employees-employee-type:before {
  content: "\ea4b";
}
.hr-employees-grades:before {
  content: "\ea4c";
}
.hr-employees-inbox:before {
  content: "\ea4d";
}
.hr-employee-skillset:before {
  content: "\ea4e";
}
.hr-employee-skillset-1:before {
  content: "\ea4f";
}
.hr-employees-leaves:before {
  content: "\ea50";
}
.hr-employees-leave-types:before {
  content: "\ea51";
}
.hr-employees-memos:before {
  content: "\ea52";
}
.hr-employees-organization-chart:before {
  content: "\ea53";
}
.hr-employees-organization-chart-fit-horizontal:before {
  content: "\ea54";
}
.hr-employees-organization-chart-fit-reset:before {
  content: "\ea55";
}
.hr-employees-organization-chart-fit-vertical:before {
  content: "\ea56";
}
.hr-employees-organization-chart-reset-hierarchy-path:before {
  content: "\ea57";
}
.hr-employees-organization-chart-view-hierarchy-path:before {
  content: "\ea58";
}
.hr-performance-management-performance-evaluation:before {
  content: "\ea59";
}
.hr-employees-resignation:before {
  content: "\ea5a";
}
.hr-employees-roles-template:before {
  content: "\ea5b";
}
.hr-employees-short-time-off:before {
  content: "\ea5c";
}
.hr-employees-skillset-assessment:before {
  content: "\ea5d";
}
.hr-employees-timesheet-hours:before {
  content: "\ea5e";
}
.hr-employee-self-service-timesheets:before {
  content: "\ea5f";
}
.hr-employees-transfer:before {
  content: "\ea60";
}
.hr-employees-warnings:before {
  content: "\ea61";
}
.hr-enterprise:before {
  content: "\ea62";
}
.hr-entrance:before {
  content: "\ea63";
}
.hr-envelope:before {
  content: "\ea64";
}
.hr-envelope-o:before {
  content: "\ea65";
}
.hr-envelope-open:before {
  content: "\ea66";
}
.hr-envelope-open-o:before {
  content: "\ea67";
}
.hr-envelope-square:before {
  content: "\ea68";
}
.hr-envira:before {
  content: "\ea69";
}
.hr-eraser:before {
  content: "\ea6a";
}
.hr-etsy:before {
  content: "\ea6b";
}
.hr-eur:before {
  content: "\ea6c";
}
.hr-exchange:before {
  content: "\ea6d";
}
.hr-exclamation:before {
  content: "\ea6e";
}
.hr-exclamation1:before {
  content: "\ea6f";
}
.hr-exclamation-1:before {
  content: "\ea70";
}
.hr-exclamation-2:before {
  content: "\ea71";
}
.hr-exclamation-3:before {
  content: "\ea72";
}
.hr-exclamation-circle:before {
  content: "\ea73";
}
.hr-exclamation-circle1:before {
  content: "\ea74";
}
.hr-exclamation-circle-1:before {
  content: "\ea75";
}
.hr-exclamation-triangle:before {
  content: "\ea76";
}
.hr-exclamation-triangle1:before {
  content: "\ea77";
}
.hr-exclamation-triangle-1:before {
  content: "\ea78";
}
.hr-exclude-break-hours:before {
  content: "\ea79";
}
.hr-expand:before {
  content: "\ea7a";
}
.hr-expand1:before {
  content: "\ea7b";
}
.hr-expeditedssl:before {
  content: "\ea7c";
}
.hr-external-link:before {
  content: "\ea7d";
}
.hr-external-link-square:before {
  content: "\ea7e";
}
.hr-eye:before {
  content: "\ea7f";
}
.hr-eyedropper:before {
  content: "\ea80";
}
.hr-eye-slash:before {
  content: "\ea81";
}
.hr-fa:before {
  content: "\ea82";
}
.hr-facebook:before {
  content: "\ea83";
}
.hr-facebook-official:before {
  content: "\ea84";
}
.hr-facebook-square:before {
  content: "\ea85";
}
.hr-fast-backward:before {
  content: "\ea86";
}
.hr-fast-forward:before {
  content: "\ea87";
}
.hr-fax:before {
  content: "\ea88";
}
.hr-feed:before {
  content: "\ea89";
}
.hr-female:before {
  content: "\ea8a";
}
.hr-female1:before {
  content: "\ea8b";
}
.hr-female-rounded-1:before {
  content: "\ea8c";
}
.hr-fighter-jet:before {
  content: "\ea8d";
}
.hr-file:before {
  content: "\ea8e";
}
.hr-file-archive-o:before {
  content: "\ea8f";
}
.hr-file-audio-o:before {
  content: "\ea90";
}
.hr-file-code-o:before {
  content: "\ea91";
}
.hr-file-excel-o:before {
  content: "\ea92";
}
.hr-file-image-o:before {
  content: "\ea93";
}
.hr-file-movie-o:before {
  content: "\ea94";
}
.hr-file-o:before {
  content: "\ea95";
}
.hr-file-pdf-o:before {
  content: "\ea96";
}
.hr-file-powerpoint-o:before {
  content: "\ea97";
}
.hr-file-submodule:before {
  content: "\ea98";
}
.hr-file-symlink-directory:before {
  content: "\ea99";
}
.hr-file-text:before {
  content: "\ea9a";
}
.hr-file-text-o:before {
  content: "\ea9b";
}
.hr-file-word-o:before {
  content: "\ea9c";
}
.hr-film:before {
  content: "\ea9d";
}
.hr-filter:before {
  content: "\ea9e";
}
.hr-financeclosure:before {
  content: "\ea9f";
}
.hr-financial-closure:before {
  content: "\eaa0";
}
.hr-fire:before {
  content: "\eaa1";
}
.hr-fire-extinguisher:before {
  content: "\eaa2";
}
.hr-firefox:before {
  content: "\eaa3";
}
.hr-first-order:before {
  content: "\eaa4";
}
.hr-flag:before {
  content: "\eaa5";
}
.hr-flag-checkered:before {
  content: "\eaa6";
}
.hr-flag-o:before {
  content: "\eaa7";
}
.hr-flask:before {
  content: "\eaa8";
}
.hr-flickr:before {
  content: "\eaa9";
}
.hr-floppy-disk:before {
  content: "\eaaa";
}
.hr-floppy-o:before {
  content: "\eaab";
}
.hr-folder:before {
  content: "\eaac";
}
.hr-folder-downloads:before {
  content: "\eaad";
}
.hr-folder-image:before {
  content: "\eaae";
}
.hr-folder-music:before {
  content: "\eaaf";
}
.hr-folder-o:before {
  content: "\eab0";
}
.hr-folder-open:before {
  content: "\eab1";
}
.hr-folder-open-o:before {
  content: "\eab2";
}
.hr-folder-sans:before {
  content: "\eab3";
}
.hr-folder-sans-accept:before {
  content: "\eab4";
}
.hr-folder-sans-add:before {
  content: "\eab5";
}
.hr-folder-sans-cancel:before {
  content: "\eab6";
}
.hr-folder-sans-down:before {
  content: "\eab7";
}
.hr-folder-sans-edit:before {
  content: "\eab8";
}
.hr-folder-sans-information:before {
  content: "\eab9";
}
.hr-folder-sans-remove:before {
  content: "\eaba";
}
.hr-folder-sans-run:before {
  content: "\eabb";
}
.hr-folder-sans-security:before {
  content: "\eabc";
}
.hr-folder-sans-settings:before {
  content: "\eabd";
}
.hr-folder-sans-up:before {
  content: "\eabe";
}
.hr-folder-text:before {
  content: "\eabf";
}
.hr-folder-video:before {
  content: "\eac0";
}
.hr-font:before {
  content: "\eac1";
}
.hr-fonticons:before {
  content: "\eac2";
}
.hr-tax-and-statutory-compliance:before {
  content: "\eac3";
}
.hr-tax-and-statutory-compliance-compliance-forms:before {
  content: "\eac4";
}
.hr-compliance-management-docusign:before {
  content: "\eac5";
}
.hr-forms-manager-document-template-engine:before {
  content: "\eac6";
}
.hr-workflow-dynamic-form-builder:before {
  content: "\eac7";
}
.hr-forms-manager-form16:before {
  content: "\eac8";
}
.hr-tax-and-statutory-compliance-form-downloads:before {
  content: "\eac9";
}
.hr-forms-manager-form-f:before {
  content: "\eaca";
}
.hr-forms-manager-form-g:before {
  content: "\eacb";
}
.hr-fort-awesome:before {
  content: "\eacc";
}
.hr-forumbee:before {
  content: "\eacd";
}
.hr-forward:before {
  content: "\eace";
}
.hr-foursquare:before {
  content: "\eacf";
}
.hr-free-code-camp:before {
  content: "\ead0";
}
.hr-friends:before {
  content: "\ead1";
}
.hr-frown-o:before {
  content: "\ead2";
}
.hr-futbol-o:before {
  content: "\ead3";
}
.hr-gamepad:before {
  content: "\ead4";
}
.hr-gavel:before {
  content: "\ead5";
}
.hr-gbp:before {
  content: "\ead6";
}
.hr-genderless:before {
  content: "\ead7";
}
.hr-get-pocket:before {
  content: "\ead8";
}
.hr-gg:before {
  content: "\ead9";
}
.hr-gg-circle:before {
  content: "\eada";
}
.hr-gift:before {
  content: "\eadb";
}
.hr-git:before {
  content: "\eadc";
}
.hr-github:before {
  content: "\eadd";
}
.hr-github-alt:before {
  content: "\eade";
}
.hr-github-square:before {
  content: "\eadf";
}
.hr-gitlab:before {
  content: "\eae0";
}
.hr-git-square:before {
  content: "\eae1";
}
.hr-gittip:before {
  content: "\eae2";
}
.hr-glass:before {
  content: "\eae3";
}
.hr-glide:before {
  content: "\eae4";
}
.hr-glide-g:before {
  content: "\eae5";
}
.hr-globe:before {
  content: "\eae6";
}
.hr-google:before {
  content: "\eae7";
}
.hr-google-plus:before {
  content: "\eae8";
}
.hr-google-plus-circle:before {
  content: "\eae9";
}
.hr-google-plus-square:before {
  content: "\eaea";
}
.hr-google-wallet:before {
  content: "\eaeb";
}
.hr-graduation-cap:before {
  content: "\eaec";
}
.hr-grav:before {
  content: "\eaed";
}
.hr-group:before {
  content: "\eaee";
}
.hr-group-full:before {
  content: "\eaef";
}
.hr-group-full-edit:before {
  content: "\eaf0";
}
.hr-group-full-security:before {
  content: "\eaf1";
}
.hr-group-half:before {
  content: "\eaf2";
}
.hr-group-half-edit:before {
  content: "\eaf3";
}
.hr-hacker-news:before {
  content: "\eaf4";
}
.hr-hand-grab-o:before {
  content: "\eaf5";
}
.hr-hand-lizard-o:before {
  content: "\eaf6";
}
.hr-hand-o-down:before {
  content: "\eaf7";
}
.hr-hand-o-left:before {
  content: "\eaf8";
}
.hr-hand-o-right:before {
  content: "\eaf9";
}
.hr-hand-o-up:before {
  content: "\eafa";
}
.hr-hand-paper-o:before {
  content: "\eafb";
}
.hr-hand-peace-o:before {
  content: "\eafc";
}
.hr-hand-pointer-o:before {
  content: "\eafd";
}
.hr-hand-scissors-o:before {
  content: "\eafe";
}
.hr-handshake-o:before {
  content: "\eaff";
}
.hr-hand-spock-o:before {
  content: "\eb00";
}
.hr-hashtag:before {
  content: "\eb01";
}
.hr-hdd-o:before {
  content: "\eb02";
}
.hr-header:before {
  content: "\eb03";
}
.hr-headphones:before {
  content: "\eb04";
}
.hr-heart:before {
  content: "\eb05";
}
.hr-heartbeat:before {
  content: "\eb06";
}
.hr-heart-o:before {
  content: "\eb07";
}
.hr-help:before {
  content: "\eb08";
}
.hr-help-contact-us:before {
  content: "\eb09";
}
.hr-help-help-topics:before {
  content: "\eb0a";
}
.hr-history:before {
  content: "\eb0b";
}
.hr-history1:before {
  content: "\eb0c";
}
.hr-home:before {
  content: "\eb0d";
}
.hr-home1:before {
  content: "\eb0e";
}
.hr-horizontal-rule:before {
  content: "\eb0f";
}
.hr-hospital-o:before {
  content: "\eb10";
}
.hr-hourglass:before {
  content: "\eb11";
}
.hr-hourglass-1:before {
  content: "\eb12";
}
.hr-hourglass-2:before {
  content: "\eb13";
}
.hr-hourglass-3:before {
  content: "\eb14";
}
.hr-hourglass-o:before {
  content: "\eb15";
}
.hr-hourly-master-report:before {
  content: "\eb16";
}
.hr-hourly-payment:before {
  content: "\eb17";
}
.hr-houzz:before {
  content: "\eb18";
}
.hr-hr-group:before {
  content: "\eb19";
}
.hr-hr-report-employee-education:before {
  content: "\eb1a";
}
.hr-hr-report-new-joinees:before {
  content: "\eb1b";
}
.hr-hr-tax-section-allowance-mapping:before {
  content: "\eb1c";
}
.hr-h-square:before {
  content: "\eb1d";
}
.hr-html5:before {
  content: "\eb1e";
}
.hr-i-cursor:before {
  content: "\eb1f";
}
.hr-id-badge:before {
  content: "\eb20";
}
.hr-ils:before {
  content: "\eb21";
}
.hr-image:before {
  content: "\eb22";
}
.hr-imdb:before {
  content: "\eb23";
}
.hr-inbox:before {
  content: "\eb24";
}
.hr-include-break-hours:before {
  content: "\eb25";
}
.hr-indent:before {
  content: "\eb26";
}
.hr-industry:before {
  content: "\eb27";
}
.hr-info:before {
  content: "\eb28";
}
.hr-info1:before {
  content: "\eb29";
}
.hr-info-circle:before {
  content: "\eb2a";
}
.hr-inr:before {
  content: "\eb2b";
}
.hr-instagram:before {
  content: "\eb2c";
}
.hr-insurance-payment-tracker:before {
  content: "\eb2d";
}
.hr-insurance-variable:before {
  content: "\eb2e";
}
.hr-integration:before {
  content: "\eb2f";
}
.hr-integration-api-dashboard:before {
  content: "\eb30";
}
.hr-integration-gvp:before {
  content: "\eb31";
}
.hr-integration-ocr:before {
  content: "\eb32";
}
.hr-internet-explorer:before {
  content: "\eb33";
}
.hr-intersex:before {
  content: "\eb34";
}
.hr-ios-refresh-outline:before {
  content: "\eb35";
}
.hr-ios-upload:before {
  content: "\eb36";
}
.hr-ios-upload-outline:before {
  content: "\eb37";
}
.hr-ioxhost:before {
  content: "\eb38";
}
.hr-issue-closed:before {
  content: "\eb39";
}
.hr-issue-reopened:before {
  content: "\eb3a";
}
.hr-italic:before {
  content: "\eb3b";
}
.hr-joomla:before {
  content: "\eb3c";
}
.hr-jsfiddle:before {
  content: "\eb3d";
}
.hr-key:before {
  content: "\eb3e";
}
.hr-key1:before {
  content: "\eb3f";
}
.hr-key-1:before {
  content: "\eb40";
}
.hr-key-2:before {
  content: "\eb41";
}
.hr-key-3:before {
  content: "\eb42";
}
.hr-key-4:before {
  content: "\eb43";
}
.hr-key-5:before {
  content: "\eb44";
}
.hr-key-6:before {
  content: "\eb45";
}
.hr-keyboard-o:before {
  content: "\eb46";
}
.hr-key-fill:before {
  content: "\eb47";
}
.hr-key-stroke:before {
  content: "\eb48";
}
.hr-krw:before {
  content: "\eb49";
}
.hr-language:before {
  content: "\eb4a";
}
.hr-laptop:before {
  content: "\eb4b";
}
.hr-lastfm:before {
  content: "\eb4c";
}
.hr-lastfm-square:before {
  content: "\eb4d";
}
.hr-leaf:before {
  content: "\eb4e";
}
.hr-leanpub:before {
  content: "\eb4f";
}
.hr-lemon-o:before {
  content: "\eb50";
}
.hr-level-down:before {
  content: "\eb51";
}
.hr-level-up:before {
  content: "\eb52";
}
.hr-life-bouy:before {
  content: "\eb53";
}
.hr-light-bulb:before {
  content: "\eb54";
}
.hr-lightbulb-o:before {
  content: "\eb55";
}
.hr-line-chart:before {
  content: "\eb56";
}
.hr-linkedin:before {
  content: "\eb57";
}
.hr-linkedin-square:before {
  content: "\eb58";
}
.hr-linode:before {
  content: "\eb59";
}
.hr-linux:before {
  content: "\eb5a";
}
.hr-list:before {
  content: "\eb5b";
}
.hr-list-add:before {
  content: "\eb5c";
}
.hr-list-alt:before {
  content: "\eb5d";
}
.hr-list-ol:before {
  content: "\eb5e";
}
.hr-list-ul:before {
  content: "\eb5f";
}
.hr-location-arrow:before {
  content: "\eb60";
}
.hr-lock:before {
  content: "\eb61";
}
.hr-lock-alt:before {
  content: "\eb62";
}
.hr-long-arrow-down:before {
  content: "\eb63";
}
.hr-long-arrow-left:before {
  content: "\eb64";
}
.hr-long-arrow-right:before {
  content: "\eb65";
}
.hr-long-arrow-up:before {
  content: "\eb66";
}
.hr-low-vision:before {
  content: "\eb67";
}
.hr-magic:before {
  content: "\eb68";
}
.hr-magic-wand:before {
  content: "\eb69";
}
.hr-magnet:before {
  content: "\eb6a";
}
.hr-mail:before {
  content: "\eb6b";
}
.hr-mail-forward:before {
  content: "\eb6c";
}
.hr-mail-read:before {
  content: "\eb6d";
}
.hr-mail-reply:before {
  content: "\eb6e";
}
.hr-mail-reply1:before {
  content: "\eb6f";
}
.hr-mail-reply-all:before {
  content: "\eb70";
}
.hr-male:before {
  content: "\eb71";
}
.hr-male1:before {
  content: "\eb72";
}
.hr-male-rounded-1:before {
  content: "\eb73";
}
.hr-male-user-1:before {
  content: "\eb74";
}
.hr-map:before {
  content: "\eb75";
}
.hr-map-marker:before {
  content: "\eb76";
}
.hr-map-o:before {
  content: "\eb77";
}
.hr-map-pin:before {
  content: "\eb78";
}
.hr-map-signs:before {
  content: "\eb79";
}
.hr-mars:before {
  content: "\eb7a";
}
.hr-mars-double:before {
  content: "\eb7b";
}
.hr-mars-stroke:before {
  content: "\eb7c";
}
.hr-mars-stroke-h:before {
  content: "\eb7d";
}
.hr-mars-stroke-v:before {
  content: "\eb7e";
}
.hr-maxcdn:before {
  content: "\eb7f";
}
.hr-meanpath:before {
  content: "\eb80";
}
.hr-medium:before {
  content: "\eb81";
}
.hr-medkit:before {
  content: "\eb82";
}
.hr-meetup:before {
  content: "\eb83";
}
.hr-meh-o:before {
  content: "\eb84";
}
.hr-menu:before {
  content: "\eb85";
}
.hr-mercury:before {
  content: "\eb86";
}
.hr-microchip:before {
  content: "\eb87";
}
.hr-microphone:before {
  content: "\eb88";
}
.hr-microphone-slash:before {
  content: "\eb89";
}
.hr-minus:before {
  content: "\eb8a";
}
.hr-minus-circle:before {
  content: "\eb8b";
}
.hr-minus-square:before {
  content: "\eb8c";
}
.hr-minus-square-o:before {
  content: "\eb8d";
}
.hr-mixcloud:before {
  content: "\eb8e";
}
.hr-mobile:before {
  content: "\eb8f";
}
.hr-modx:before {
  content: "\eb90";
}
.hr-money:before {
  content: "\eb91";
}
.hr-monthly-payment:before {
  content: "\eb92";
}
.hr-montly-master-report:before {
  content: "\eb93";
}
.hr-moon-o:before {
  content: "\eb94";
}
.hr-motorcycle:before {
  content: "\eb95";
}
.hr-mouse-pointer:before {
  content: "\eb96";
}
.hr-music:before {
  content: "\eb97";
}
.hr-neuter:before {
  content: "\eb98";
}
.hr-newspaper-o:before {
  content: "\eb99";
}
.hr-object-group:before {
  content: "\eb9a";
}
.hr-object-ungroup:before {
  content: "\eb9b";
}
.hr-odnoklassniki:before {
  content: "\eb9c";
}
.hr-odnoklassniki-square:before {
  content: "\eb9d";
}
.hr-opencart:before {
  content: "\eb9e";
}
.hr-openid:before {
  content: "\eb9f";
}
.hr-opera:before {
  content: "\eba0";
}
.hr-optin-monster:before {
  content: "\eba1";
}
.hr-optional-choice:before {
  content: "\eba2";
}
.hr-organization:before {
  content: "\eba3";
}
.hr-organization-1:before {
  content: "\eba4";
}
.hr-organization-announcements:before {
  content: "\eba5";
}
.hr-organization-data-import:before {
  content: "\eba6";
}
.hr-organization-data-import-1-1:before {
  content: "\eba7";
}
.hr-organization-department-hierarchy:before {
  content: "\eba8";
}
.hr-organization-details:before {
  content: "\eba9";
}
.hr-organization-eft-configuration:before {
  content: "\ebaa";
}
.hr-organization-eft-configuration-deregister:before {
  content: "\ebab";
}
.hr-organization-special-wages:before {
  content: "\ebac";
}
.hr-organization-holiday-types:before {
  content: "\ebad";
}
.hr-organization-locations:before {
  content: "\ebae";
}
.hr-organization-organization-policies:before {
  content: "\ebaf";
}
.hr-organization-organization-profile:before {
  content: "\ebb0";
}
.hr-organization-organization-settings:before {
  content: "\ebb1";
}
.hr-core-hr-employee-data-management:before {
  content: "\ebb2";
}
.hr-organization-projects-1:before {
  content: "\ebb3";
}
.hr-organization-system-log:before {
  content: "\ebb4";
}
.hr-organization-work-schedule:before {
  content: "\ebb5";
}
.hr-pagelines:before {
  content: "\ebb6";
}
.hr-page-pdf:before {
  content: "\ebb7";
}
.hr-paint-brush:before {
  content: "\ebb8";
}
.hr-paperclip:before {
  content: "\ebb9";
}
.hr-paper-plane:before {
  content: "\ebba";
}
.hr-paper-plane-o:before {
  content: "\ebbb";
}
.hr-paragraph:before {
  content: "\ebbc";
}
.hr-pause:before {
  content: "\ebbd";
}
.hr-pause-circle:before {
  content: "\ebbe";
}
.hr-pause-circle-o:before {
  content: "\ebbf";
}
.hr-paw:before {
  content: "\ebc0";
}
.hr-payment-tracker:before {
  content: "\ebc1";
}
.hr-paypal:before {
  content: "\ebc2";
}
.hr-payroll:before {
  content: "\ebc3";
}
.hr-payroll-2:before {
  content: "\ebc4";
}
.hr-payroll-advance-salary:before {
  content: "\ebc5";
}
.hr-payroll-allowances:before {
  content: "\ebc6";
}
.hr-payroll-bonus:before {
  content: "\ebc7";
}
.hr-payroll-commission:before {
  content: "\ebc8";
}
.hr-payroll-deductions:before {
  content: "\ebc9";
}
.hr-tax-and-statutory-compliance-nps:before {
  content: "\ebca";
}
.hr-payroll-final-settlement:before {
  content: "\ebcb";
}
.hr-tax-and-statutory-compliance-fixed-health-insurance:before {
  content: "\ebcc";
}
.hr-payroll-flexi-benefit-declaration:before {
  content: "\ebcd";
}
.hr-tax-and-statutory-compliance-gratuity:before {
  content: "\ebce";
}
.hr-payroll-gratuity-nomination:before {
  content: "\ebcf";
}
.hr-payroll-group:before {
  content: "\ebd0";
}
.hr-tax-and-statutory-compliance-insurance:before {
  content: "\ebd1";
}
.hr-tax-and-statutory-compliance-labour-welfare-fund:before {
  content: "\ebd2";
}
.hr-payroll-loan:before {
  content: "\ebd3";
}
.hr-payroll-additional-wage-claim:before {
  content: "\ebd4";
}
.hr-payroll-payout:before {
  content: "\ebd5";
}
.hr-payroll-payout-history:before {
  content: "\ebd6";
}
.hr-payroll-payslip-template:before {
  content: "\ebd7";
}
.hr-tax-and-statutory-compliance-perquisite-tracker:before {
  content: "\ebd8";
}
.hr-payroll-proof-of-investment:before {
  content: "\ebd9";
}
.hr-tax-and-statutory-compliance-provident-fund:before {
  content: "\ebda";
}
.hr-payroll-reimbursement:before {
  content: "\ebdb";
}
.hr-payroll-reimbursement-bank-statement:before {
  content: "\ebdc";
}
.hr-payroll-salary:before {
  content: "\ebdd";
}
.hr-payroll-salary-payslip:before {
  content: "\ebde";
}
.hr-payroll-salary-template:before {
  content: "\ebdf";
}
.hr-payroll-shift-allowance:before {
  content: "\ebe0";
}
.hr-tax-and-statutory-compliance-tax-declarations:before {
  content: "\ebe1";
}
.hr-payroll-tax-declarations-1:before {
  content: "\ebe2";
}
.hr-tax-and-statutory-compliance-tax-rules:before {
  content: "\ebe3";
}
.hr-tax-and-statutory-compliance-tds-history:before {
  content: "\ebe4";
}
.hr-pencil:before {
  content: "\ebe5";
}
.hr-pencil-square:before {
  content: "\ebe6";
}
.hr-percent:before {
  content: "\ebe7";
}
.hr-performance-evaluation:before {
  content: "\ebe8";
}
.hr-phone:before {
  content: "\ebe9";
}
.hr-phone-square:before {
  content: "\ebea";
}
.hr-pie-chart:before {
  content: "\ebeb";
}
.hr-pied-piper:before {
  content: "\ebec";
}
.hr-pied-piper-alt:before {
  content: "\ebed";
}
.hr-pied-piper-pp:before {
  content: "\ebee";
}
.hr-pinterest:before {
  content: "\ebef";
}
.hr-pinterest-p:before {
  content: "\ebf0";
}
.hr-pinterest-square:before {
  content: "\ebf1";
}
.hr-plane:before {
  content: "\ebf2";
}
.hr-play:before {
  content: "\ebf3";
}
.hr-play-circle:before {
  content: "\ebf4";
}
.hr-play-circle-o:before {
  content: "\ebf5";
}
.hr-plug:before {
  content: "\ebf6";
}
.hr-plus:before {
  content: "\ebf7";
}
.hr-plus-circle:before {
  content: "\ebf8";
}
.hr-plus-square:before {
  content: "\ebf9";
}
.hr-plus-square-o:before {
  content: "\ebfa";
}
.hr-podcast:before {
  content: "\ebfb";
}
.hr-power-off:before {
  content: "\ebfc";
}
.hr-print:before {
  content: "\ebfd";
}
.hr-print1:before {
  content: "\ebfe";
}
.hr-product-hunt:before {
  content: "\ebff";
}
.hr-productivity-monitoring:before {
  content: "\ec00";
}
.hr-productivity-monitoring-activity-dashboard:before {
  content: "\ec01";
}
.hr-productivity-monitoring-activity-tracker:before {
  content: "\ec02";
}
.hr-productivity-monitoring-members:before {
  content: "\ec03";
}
.hr-productivity-monitoring-reports:before {
  content: "\ec04";
}
.hr-professional-tax:before {
  content: "\ec05";
}
.hr-puzzle-piece:before {
  content: "\ec06";
}
.hr-qq:before {
  content: "\ec07";
}
.hr-qrcode:before {
  content: "\ec08";
}
.hr-question:before {
  content: "\ec09";
}
.hr-question-circle:before {
  content: "\ec0a";
}
.hr-question-circle-o:before {
  content: "\ec0b";
}
.hr-quick-menu:before {
  content: "\ec0c";
}
.hr-quora:before {
  content: "\ec0d";
}
.hr-quote-left:before {
  content: "\ec0e";
}
.hr-quote-right:before {
  content: "\ec0f";
}
.hr-ra:before {
  content: "\ec10";
}
.hr-random:before {
  content: "\ec11";
}
.hr-ravelry:before {
  content: "\ec12";
}
.hr-recruitment:before {
  content: "\ec13";
}
.hr-recruitment_clients_chat:before {
  content: "\ec14";
}
.hr-recruitment_clients_import:before {
  content: "\ec15";
}
.hr-recruitment-clients-campaign:before {
  content: "\ec16";
}
.hr-recruitment-clients-campaign1:before {
  content: "\ec17";
}
.hr-recruitment-clients-export:before {
  content: "\ec18";
}
.hr-recruitment-clients-import:before {
  content: "\ec19";
}
.hr-recruitment-clients-master:before {
  content: "\ec1a";
}
.hr-recruitment-clients-print:before {
  content: "\ec1b";
}
.hr-recruitment-dashboard:before {
  content: "\ec1c";
}
.hr-recruitment-dashboard-applied:before {
  content: "\ec1d";
}
.hr-recruitment-dashboard-rejected:before {
  content: "\ec1e";
}
.hr-recruitment-dashboard-scheduled:before {
  content: "\ec1f";
}
.hr-recruitment-dashboard-shortlisted:before {
  content: "\ec20";
}
.hr-onboarding-individuals:before {
  content: "\ec21";
}
.hr-recruitment-interview-calendar:before {
  content: "\ec22";
}
.hr-recruitment-interview-rounds-master:before {
  content: "\ec23";
}
.hr-recruitment-job-candidates:before {
  content: "\ec24";
}
.hr-recruitment-job-post-requisition:before {
  content: "\ec25";
}
.hr-recruitment-job-posts:before {
  content: "\ec26";
}
.hr-recruitment-schedule-interviews:before {
  content: "\ec27";
}
.hr-recruitment-shortlisted-candidates:before {
  content: "\ec28";
}
.hr-recycle:before {
  content: "\ec29";
}
.hr-reddit:before {
  content: "\ec2a";
}
.hr-reddit-alien:before {
  content: "\ec2b";
}
.hr-reddit-square:before {
  content: "\ec2c";
}
.hr-refresh:before {
  content: "\ec2d";
}
.hr-refresh1:before {
  content: "\ec2e";
}
.hr-registered:before {
  content: "\ec2f";
}
.hr-renren:before {
  content: "\ec30";
}
.hr-repeat:before {
  content: "\ec31";
}
.hr-repo:before {
  content: "\ec32";
}
.hr-repo-clone:before {
  content: "\ec33";
}
.hr-repo-force-push:before {
  content: "\ec34";
}
.hr-report-absentees:before {
  content: "\ec35";
}
.hr-report-assignment:before {
  content: "\ec36";
}
.hr-report-attendance-import:before {
  content: "\ec37";
}
.hr-report-attendance-muster-info:before {
  content: "\ec38";
}
.hr-report-attendance-shortage:before {
  content: "\ec39";
}
.hr-report-attendance-summary-hourly:before {
  content: "\ec3a";
}
.hr-report-attendance-summary-monthly:before {
  content: "\ec3b";
}
.hr-report-attendance-without-grace-period:before {
  content: "\ec3c";
}
.hr-report-attendance-with-out-grace-period:before {
  content: "\ec3d";
}
.hr-report-biometric-error-log:before {
  content: "\ec3e";
}
.hr-report-biometric-sync-history:before {
  content: "\ec3f";
}
.hr-report-certifications:before {
  content: "\ec40";
}
.hr-report-compensatory-off:before {
  content: "\ec41";
}
.hr-report-compensatory-off-balance:before {
  content: "\ec42";
}
.hr-report-dependents:before {
  content: "\ec43";
}
.hr-report-designations:before {
  content: "\ec44";
}
.hr-report-device-management:before {
  content: "\ec45";
}
.hr-report-educational-qualifications:before {
  content: "\ec46";
}
.hr-report-gender:before {
  content: "\ec47";
}
.hr-report-holiday-attendance:before {
  content: "\ec48";
}
.hr-report-insurance:before {
  content: "\ec49";
}
.hr-report-insurance-fixed:before {
  content: "\ec4a";
}
.hr-report-leave-history:before {
  content: "\ec4b";
}
.hr-report-license-expiry:before {
  content: "\ec4c";
}
.hr-report-loan:before {
  content: "\ec4d";
}
.hr-report-logo:before {
  content: "\ec4e";
}
.hr-report-monthly-leave-balance:before {
  content: "\ec4f";
}
.hr-report-monthly-shortage:before {
  content: "\ec50";
}
.hr-report-overtime:before {
  content: "\ec51";
}
.hr-report-project:before {
  content: "\ec52";
}
.hr-report-report:before {
  content: "\ec53";
}
.hr-reports:before {
  content: "\ec54";
}
.hr-reports-adhoc-allowance:before {
  content: "\ec55";
}
.hr-reports-custom-report:before {
  content: "\ec56";
}
.hr-reports-deferred-loan:before {
  content: "\ec57";
}
.hr-reports-ecr-hourly:before {
  content: "\ec58";
}
.hr-reports-ecr-monthly:before {
  content: "\ec59";
}
.hr-reports-eft:before {
  content: "\ec5a";
}
.hr-reports-eft-hourly:before {
  content: "\ec5b";
}
.hr-reports-eft-monthly:before {
  content: "\ec5c";
}
.hr-reports-employees-reports:before {
  content: "\ec5d";
}
.hr-reports-employee-wise-expenses:before {
  content: "\ec5e";
}
.hr-reports-esic-hourly:before {
  content: "\ec5f";
}
.hr-reports-esic-monthly:before {
  content: "\ec60";
}
.hr-reports-esi-hourly:before {
  content: "\ec61";
}
.hr-reports-esi-monthly:before {
  content: "\ec62";
}
.hr-reports-group-deductions:before {
  content: "\ec63";
}
.hr-report-short:before {
  content: "\ec64";
}
.hr-reports-hourly-master-report:before {
  content: "\ec65";
}
.hr-reports-hourly-wage-payslip:before {
  content: "\ec66";
}
.hr-reports-hr-reports:before {
  content: "\ec67";
}
.hr-reports-insurance-fixed:before {
  content: "\ec68";
}
.hr-reports-insurance-payment-tracker:before {
  content: "\ec69";
}
.hr-report-skills:before {
  content: "\ec6a";
}
.hr-reports-loan-amortization:before {
  content: "\ec6b";
}
.hr-reports-loan-balance:before {
  content: "\ec6c";
}
.hr-reports-monthly-master-report:before {
  content: "\ec6d";
}
.hr-reports-monthly-salary:before {
  content: "\ec6e";
}
.hr-reports-monthly-salary-payslip:before {
  content: "\ec6f";
}
.hr-reports-payroll-reports:before {
  content: "\ec70";
}
.hr-reports-pf-hourly:before {
  content: "\ec71";
}
.hr-reports-pf-monthly:before {
  content: "\ec72";
}
.hr-reports-pf-payment-tracker:before {
  content: "\ec73";
}
.hr-reports-professional-tax-hourly:before {
  content: "\ec74";
}
.hr-reports-professional-tax-monthly:before {
  content: "\ec75";
}
.hr-reports-recruitment-reports:before {
  content: "\ec76";
}
.hr-reports-salary-increment:before {
  content: "\ec77";
}
.hr-report-status:before {
  content: "\ec78";
}
.hr-reports-tds:before {
  content: "\ec79";
}
.hr-reports-timeline:before {
  content: "\ec7a";
}
.hr-reports-uan-based-ecr:before {
  content: "\ec7b";
}
.hr-reports-uan-based-ecr-hourly:before {
  content: "\ec7c";
}
.hr-reports-variable-insurance:before {
  content: "\ec7d";
}
.hr-reports-work-sheet:before {
  content: "\ec7e";
}
.hr-report-tax:before {
  content: "\ec7f";
}
.hr-report-taxdeduct:before {
  content: "\ec80";
}
.hr-report-trainings:before {
  content: "\ec81";
}
.hr-report-weekoff-attendance:before {
  content: "\ec82";
}
.hr-report-year-of-join:before {
  content: "\ec83";
}
.hr-reset:before {
  content: "\ec84";
}
.hr-retweet:before {
  content: "\ec85";
}
.hr-road:before {
  content: "\ec86";
}
.hr-rocket:before {
  content: "\ec87";
}
.hr-roster-management:before {
  content: "\ec88";
}
.hr-roster-management-calendar-view:before {
  content: "\ec89";
}
.hr-roster-management-custom-group:before {
  content: "\ec8a";
}
.hr-roster-management-custom-group-settings:before {
  content: "\ec8b";
}
.hr-roster-management-shift-scheduling:before {
  content: "\ec8c";
}
.hr-roster-management-shift-type:before {
  content: "\ec8d";
}
.hr-rotate-left:before {
  content: "\ec8e";
}
.hr-rouble:before {
  content: "\ec8f";
}
.hr-rss-square:before {
  content: "\ec90";
}
.hr-ruler:before {
  content: "\ec91";
}
.hr-safari:before {
  content: "\ec92";
}
.hr-scribd:before {
  content: "\ec93";
}
.hr-search:before {
  content: "\ec94";
}
.hr-search1:before {
  content: "\ec95";
}
.hr-search-minus:before {
  content: "\ec96";
}
.hr-search-plus:before {
  content: "\ec97";
}
.hr-section-investment:before {
  content: "\ec98";
}
.hr-sellsy:before {
  content: "\ec99";
}
.hr-server:before {
  content: "\ec9a";
}
.hr-settings:before {
  content: "\ec9b";
}
.hr-settings-ip-whitelisting:before {
  content: "\ec9c";
}
.hr-settings-general:before {
  content: "\ec9d";
}
.hr-settings-payroll:before {
  content: "\ec9e";
}
.hr-settings-performance-management:before {
  content: "\ec9f";
}
.hr-settings-productivity-monitoring:before {
  content: "\eca0";
}
.hr-settings-tax-and-statutory-compliance:before {
  content: "\eca1";
}
.hr-share-alt:before {
  content: "\eca2";
}
.hr-share-alt-square:before {
  content: "\eca3";
}
.hr-share-square:before {
  content: "\eca4";
}
.hr-share-square-o:before {
  content: "\eca5";
}
.hr-shield:before {
  content: "\eca6";
}
.hr-ship:before {
  content: "\eca7";
}
.hr-shirtsinbulk:before {
  content: "\eca8";
}
.hr-shopping-bag:before {
  content: "\eca9";
}
.hr-shopping-basket:before {
  content: "\ecaa";
}
.hr-shopping-cart:before {
  content: "\ecab";
}
.hr-shower:before {
  content: "\ecac";
}
.hr-shrink:before {
  content: "\ecad";
}
.hr-signal:before {
  content: "\ecae";
}
.hr-sign-in:before {
  content: "\ecaf";
}
.hr-sign-in1:before {
  content: "\ecb0";
}
.hr-sign-in-1:before {
  content: "\ecb1";
}
.hr-sign-language:before {
  content: "\ecb2";
}
.hr-signout:before {
  content: "\ecb3";
}
.hr-sign-out:before {
  content: "\ecb4";
}
.hr-sign-out1:before {
  content: "\ecb5";
}
.hr-simplybuilt:before {
  content: "\ecb6";
}
.hr-sitemap:before {
  content: "\ecb7";
}
.hr-skill-definition:before {
  content: "\ecb8";
}
.hr-skill-level-association:before {
  content: "\ecb9";
}
.hr-skyatlas:before {
  content: "\ecba";
}
.hr-skype:before {
  content: "\ecbb";
}
.hr-slack:before {
  content: "\ecbc";
}
.hr-sliders:before {
  content: "\ecbd";
}
.hr-slideshare:before {
  content: "\ecbe";
}
.hr-smile-o:before {
  content: "\ecbf";
}
.hr-snapchat:before {
  content: "\ecc0";
}
.hr-snapchat-ghost:before {
  content: "\ecc1";
}
.hr-snapchat-square:before {
  content: "\ecc2";
}
.hr-snowflake-o:before {
  content: "\ecc3";
}
.hr-sort:before {
  content: "\ecc4";
}
.hr-sort-alpha-asc:before {
  content: "\ecc5";
}
.hr-sort-alpha-desc:before {
  content: "\ecc6";
}
.hr-sort-amount-asc:before {
  content: "\ecc7";
}
.hr-sort-amount-desc:before {
  content: "\ecc8";
}
.hr-sort-asc:before {
  content: "\ecc9";
}
.hr-sort-desc:before {
  content: "\ecca";
}
.hr-sort-numeric-asc:before {
  content: "\eccb";
}
.hr-sort-numeric-desc:before {
  content: "\eccc";
}
.hr-soundcloud:before {
  content: "\eccd";
}
.hr-space-shuttle:before {
  content: "\ecce";
}
.hr-spinner:before {
  content: "\eccf";
}
.hr-spoon:before {
  content: "\ecd0";
}
.hr-spotify:before {
  content: "\ecd1";
}
.hr-square:before {
  content: "\ecd2";
}
.hr-square-o:before {
  content: "\ecd3";
}
.hr-stack-exchange:before {
  content: "\ecd4";
}
.hr-stack-overflow:before {
  content: "\ecd5";
}
.hr-star:before {
  content: "\ecd6";
}
.hr-star1:before {
  content: "\ecd7";
}
.hr-star-half:before {
  content: "\ecd8";
}
.hr-star-half-empty:before {
  content: "\ecd9";
}
.hr-star-o:before {
  content: "\ecda";
}
.hr-statistics:before {
  content: "\ecdb";
}
.hr-status-approval:before {
  content: "\ecdc";
}
.hr-status-update:before {
  content: "\ecdd";
}
.hr-steam:before {
  content: "\ecde";
}
.hr-steam-square:before {
  content: "\ecdf";
}
.hr-step-backward:before {
  content: "\ece0";
}
.hr-step-forward:before {
  content: "\ece1";
}
.hr-stethoscope:before {
  content: "\ece2";
}
.hr-sticky-note:before {
  content: "\ece3";
}
.hr-sticky-note-o:before {
  content: "\ece4";
}
.hr-stop:before {
  content: "\ece5";
}
.hr-stop1:before {
  content: "\ece6";
}
.hr-stop-circle:before {
  content: "\ece7";
}
.hr-stop-circle-o:before {
  content: "\ece8";
}
.hr-street-view:before {
  content: "\ece9";
}
.hr-strikethrough:before {
  content: "\ecea";
}
.hr-stumbleupon:before {
  content: "\eceb";
}
.hr-stumbleupon-circle:before {
  content: "\ecec";
}
.hr-subscript:before {
  content: "\eced";
}
.hr-subway:before {
  content: "\ecee";
}
.hr-suitcase:before {
  content: "\ecef";
}
.hr-sun-o:before {
  content: "\ecf0";
}
.hr-superpowers:before {
  content: "\ecf1";
}
.hr-superscript:before {
  content: "\ecf2";
}
.hr-support-ticket:before {
  content: "\ecf3";
}
.hr-sync:before {
  content: "\ecf4";
}
.hr-sync-1:before {
  content: "\ecf5";
}
.hr-table:before {
  content: "\ecf6";
}
.hr-tablet:before {
  content: "\ecf7";
}
.hr-tag:before {
  content: "\ecf8";
}
.hr-tags:before {
  content: "\ecf9";
}
.hr-tasks:before {
  content: "\ecfa";
}
.hr-tax-calculation:before {
  content: "\ecfb";
}
.hr-tax-entities:before {
  content: "\ecfc";
}
.hr-tax-exemptions:before {
  content: "\ecfd";
}
.hr-tax-rebates:before {
  content: "\ecfe";
}
.hr-tax-section:before {
  content: "\ecff";
}
.hr-tax-section-1:before {
  content: "\ed00";
}
.hr-tax-slab:before {
  content: "\ed01";
}
.hr-tax-slab-1:before {
  content: "\ed02";
}
.hr-tds-payment-tracker:before {
  content: "\ed03";
}
.hr-tds-submission:before {
  content: "\ed04";
}
.hr-telegram:before {
  content: "\ed05";
}
.hr-telescope:before {
  content: "\ed06";
}
.hr-television:before {
  content: "\ed07";
}
.hr-tencent-weibo:before {
  content: "\ed08";
}
.hr-terminal:before {
  content: "\ed09";
}
.hr-text-height:before {
  content: "\ed0a";
}
.hr-text-width:before {
  content: "\ed0b";
}
.hr-th:before {
  content: "\ed0c";
}
.hr-themeisle:before {
  content: "\ed0d";
}
.hr-thermometer:before {
  content: "\ed0e";
}
.hr-thermometer-0:before {
  content: "\ed0f";
}
.hr-thermometer-1:before {
  content: "\ed10";
}
.hr-thermometer-2:before {
  content: "\ed11";
}
.hr-thermometer-3:before {
  content: "\ed12";
}
.hr-th-large:before {
  content: "\ed13";
}
.hr-th-list:before {
  content: "\ed14";
}
.hr-thumbs-down:before {
  content: "\ed15";
}
.hr-thumbs-o-down:before {
  content: "\ed16";
}
.hr-thumbs-o-up:before {
  content: "\ed17";
}
.hr-thumbs-up:before {
  content: "\ed18";
}
.hr-thumb-tack:before {
  content: "\ed19";
}
.hr-ticket:before {
  content: "\ed1a";
}
.hr-times:before {
  content: "\ed1b";
}
.hr-times-circle:before {
  content: "\ed1c";
}
.hr-times-circle-o:before {
  content: "\ed1d";
}
.hr-timesheet:before {
  content: "\ed1e";
}
.hr-timesheet-activity:before {
  content: "\ed1f";
}
.hr-timesheet-hours:before {
  content: "\ed20";
}
.hr-times-rectangle:before {
  content: "\ed21";
}
.hr-times-rectangle-o:before {
  content: "\ed22";
}
.hr-tint:before {
  content: "\ed23";
}
.hr-toggle-off:before {
  content: "\ed24";
}
.hr-toggle-on:before {
  content: "\ed25";
}
.hr-trademark:before {
  content: "\ed26";
}
.hr-train:before {
  content: "\ed27";
}
.hr-transgender-alt:before {
  content: "\ed28";
}
.hr-trash:before {
  content: "\ed29";
}
.hr-trash-o:before {
  content: "\ed2a";
}
.hr-tree:before {
  content: "\ed2b";
}
.hr-trello:before {
  content: "\ed2c";
}
.hr-tripadvisor:before {
  content: "\ed2d";
}
.hr-trophy:before {
  content: "\ed2e";
}
.hr-truck:before {
  content: "\ed2f";
}
.hr-try:before {
  content: "\ed30";
}
.hr-tty:before {
  content: "\ed31";
}
.hr-tumblr:before {
  content: "\ed32";
}
.hr-tumblr-square:before {
  content: "\ed33";
}
.hr-twitch:before {
  content: "\ed34";
}
.hr-twitter:before {
  content: "\ed35";
}
.hr-twitter-square:before {
  content: "\ed36";
}
.hr-umbrella:before {
  content: "\ed37";
}
.hr-underline:before {
  content: "\ed38";
}
.hr-universal-access:before {
  content: "\ed39";
}
.hr-unlock:before {
  content: "\ed3a";
}
.hr-unlock-alt:before {
  content: "\ed3b";
}
.hr-upload:before {
  content: "\ed3c";
}
.hr-upload1:before {
  content: "\ed3d";
}
.hr-usb:before {
  content: "\ed3e";
}
.hr-user:before {
  content: "\ed3f";
}
.hr-user1:before {
  content: "\ed40";
}
.hr-user-1:before {
  content: "\ed41";
}
.hr-user-2:before {
  content: "\ed42";
}
.hr-user-circle:before {
  content: "\ed43";
}
.hr-user-circle-o:before {
  content: "\ed44";
}
.hr-user-close:before {
  content: "\ed45";
}
.hr-user-close-add:before {
  content: "\ed46";
}
.hr-user-close-edit:before {
  content: "\ed47";
}
.hr-user-close-information:before {
  content: "\ed48";
}
.hr-user-close-remove:before {
  content: "\ed49";
}
.hr-user-close-settings:before {
  content: "\ed4a";
}
.hr-user-full:before {
  content: "\ed4b";
}
.hr-user-full-add:before {
  content: "\ed4c";
}
.hr-user-full-edit:before {
  content: "\ed4d";
}
.hr-user-full-remove:before {
  content: "\ed4e";
}
.hr-user-full-security:before {
  content: "\ed4f";
}
.hr-user-full-settings:before {
  content: "\ed50";
}
.hr-user-half:before {
  content: "\ed51";
}
.hr-user-half-add:before {
  content: "\ed52";
}
.hr-user-half-edit:before {
  content: "\ed53";
}
.hr-user-half-information:before {
  content: "\ed54";
}
.hr-user-half-remove:before {
  content: "\ed55";
}
.hr-user-half-security:before {
  content: "\ed56";
}
.hr-user-half-settings:before {
  content: "\ed57";
}
.hr-user-md:before {
  content: "\ed58";
}
.hr-user-o:before {
  content: "\ed59";
}
.hr-user-outline:before {
  content: "\ed5a";
}
.hr-onboarding:before {
  content: "\ed5b";
}
.hr-user-profile:before {
  content: "\ed5c";
}
.hr-user-profile-edit:before {
  content: "\ed5d";
}
.hr-user-secret:before {
  content: "\ed5e";
}
.hr-user-times:before {
  content: "\ed5f";
}
.hr-venus:before {
  content: "\ed60";
}
.hr-venus1:before {
  content: "\ed61";
}
.hr-venus-double:before {
  content: "\ed62";
}
.hr-venus-double1:before {
  content: "\ed63";
}
.hr-venus-mars:before {
  content: "\ed64";
}
.hr-viacoin:before {
  content: "\ed65";
}
.hr-viadeo:before {
  content: "\ed66";
}
.hr-viadeo-square:before {
  content: "\ed67";
}
.hr-video-camera:before {
  content: "\ed68";
}
.hr-view:before {
  content: "\ed69";
}
.hr-view-disable:before {
  content: "\ed6a";
}
.hr-vimeo:before {
  content: "\ed6b";
}
.hr-vimeo-square:before {
  content: "\ed6c";
}
.hr-vine:before {
  content: "\ed6d";
}
.hr-vk:before {
  content: "\ed6e";
}
.hr-volume-control-phone:before {
  content: "\ed6f";
}
.hr-volume-down:before {
  content: "\ed70";
}
.hr-volume-off:before {
  content: "\ed71";
}
.hr-volume-up:before {
  content: "\ed72";
}
.hr-wechat:before {
  content: "\ed73";
}
.hr-weibo:before {
  content: "\ed74";
}
.hr-whatsapp:before {
  content: "\ed75";
}
.hr-wheelchair:before {
  content: "\ed76";
}
.hr-wheelchair-alt:before {
  content: "\ed77";
}
.hr-wifi:before {
  content: "\ed78";
}
.hr-wikipedia-w:before {
  content: "\ed79";
}
.hr-window-maximize:before {
  content: "\ed7a";
}
.hr-window-minimize:before {
  content: "\ed7b";
}
.hr-window-restore:before {
  content: "\ed7c";
}
.hr-windows:before {
  content: "\ed7d";
}
.hr-wordpress:before {
  content: "\ed7e";
}
.hr-workflow:before {
  content: "\ed7f";
}
.hr-workflow-approval-management:before {
  content: "\ed80";
}
.hr-workflow-task-management-approve:before {
  content: "\ed81";
}
.hr-workflow-task-management-approvedtask:before {
  content: "\ed82";
}
.hr-workflow-task-management-claim:before {
  content: "\ed83";
}
.hr-workflow-task-management-claim-override:before {
  content: "\ed84";
}
.hr-workflow-task-management-form:before {
  content: "\ed85";
}
.hr-workflow-task-management-reject:before {
  content: "\ed86";
}
.hr-workflow-task-management-surrender:before {
  content: "\ed87";
}
.hr-workflow-task-management-view:before {
  content: "\ed88";
}
.hr-workflow-task-management-waitingforapprovaltask:before {
  content: "\ed89";
}
.hr-workflow-workflow-builder:before {
  content: "\ed8a";
}
.hr-wpbeginner:before {
  content: "\ed8b";
}
.hr-wpexplorer:before {
  content: "\ed8c";
}
.hr-wpforms:before {
  content: "\ed8d";
}
.hr-wrench:before {
  content: "\ed8e";
}
.hr-x:before {
  content: "\ed8f";
}
.hr-xing:before {
  content: "\ed90";
}
.hr-xing-square:before {
  content: "\ed91";
}
.hr-yahoo:before {
  content: "\ed92";
}
.hr-y-combinator:before {
  content: "\ed93";
}
.hr-yelp:before {
  content: "\ed94";
}
.hr-yoast:before {
  content: "\ed95";
}
.hr-youtube:before {
  content: "\ed96";
}
.hr-youtube-play:before {
  content: "\ed97";
}
.hr-youtube-play1:before {
  content: "\ed98";
}
.hr-youtube-square:before {
  content: "\ed99";
}
.hr-zap:before {
  content: "\ed9a";
}
.hr-zoom-out:before {
  content: "\ed9b";
}
.hr-performance-management:before {
  content: "\ed9c";
}
.hr-performance-management-trulead:before {
  content: "\ed9d";
}
.hr-core-hr-work-schedule:before {
  content: "\ed9e";
}
.hr-core-hr:before {
  content: "\ed9f";
}
.hr-compliance-management:before {
  content: "\eda0";
}
.hr-core-hr-register-face:before {
  content: "\eda1";
}
.hr-settings-attendance-configuration:before {
  content: "\eda2";
}
.hr-data-loss-prevention-file-transfers:before {
  content: "\eda3";
}
.hr-data-loss-prevention:before {
  content: "\eda4";
}
.hr-settings-data-loss-prevention:before {
  content: "\eda5";
}
.hr-onboarding-vendors:before {
  content: "\eda6";
}
.hr-compliance-management-accreditation:before {
  content: "\eda7";
}
.hr-settings-core-hr:before {
  content: "\eda8";
}
.hr-settings-integration:before {
  content: "\eda9";
}
.hr-employee-self-service:before {
  content: "\edaa";
}
.hr-my-team:before {
  content: "\edab";
}
.hr-employee-self-service-lop-recovery:before {
  content: "\edac";
}
.hr-employee-self-service-pre-approval:before {
  content: "\edad";
}
.hr-my-team-team-summary:before {
  content: "\edae";
}
.hr-my-team-lop-recovery:before {
  content: "\edaf";
}
.hr-my-team-compensatory-off-balance:before {
  content: "\edb0";
}
.hr-employee-self-service-compensatory-off-balance:before {
  content: "\edb1";
}
.hr-my-team-timesheets:before {
  content: "\edb2";
}
.hr-core-hr-time-off-management:before {
  content: "\edb3";
}
.hr-recruitment-recruitment-dashboard:before {
  content: "\edb4";
}
.hr-my-team-exit-management:before {
  content: "\edb5";
}
.hr-core-hr-org-structure:before {
  content: "\edb6";
}
.hr-data-loss-prevention-location-intelligence1:before {
  content: "\edb7";
}
.hr-man-power-planning-table-of-organization:before {
  content: "\edb8";
}
.hr-man-power-planning-job-requisition:before {
  content: "\edb9";
}
.hr-man-power-planning-hiring-forecast:before {
  content: "\edba";
}
.hr-man-power-planning:before {
  content: "\edbb";
}
.hr-settings-man-power-planning:before {
  content: "\edbc";
}
.hr-data-loss-prevention-key-logger:before {
  content: "\edbd";
}
.hr-employee-self-service-organization-chart:before {
  content: "\edbe";
}
.hr-employee-self-service-my-profile:before {
  content: "\edbf";
}
.hr-man-power-planning-settings:before {
  content: "\edc0";
}
.hr-employee-self-service-attendance:before {
  content: "\edc1";
}
.hr-my-team-attendance:before {
  content: "\edc2";
}
.hr-recruitment-my-integration:before {
  content: "\edc3";
}
.hr-recruitment-careers:before {
  content: "\edc4";
}
.hr-payroll-payroll-reconciliation:before {
  content: "\edc5";
}
.hr-payroll-electronic-fund-transfer:before {
  content: "\edc6";
}
.hr-core-hr-payroll-data-management:before {
  content: "\edc7";
}
.hr-employee-self-service-time-off:before {
  content: "\edc8";
}
.hr-my-team-time-off:before {
  content: "\edc9";
}
.hr-my-finance-travel-and-expenses:before {
  content: "\edca";
}
.hr-my-team-travel-and-expenses:before {
  content: "\edcb";
}
.hr-my-finance:before {
  content: "\edcc";
}
.hr-payroll-payroll-management:before {
  content: "\edcd";
}
