<template>
  <v-overlay
    :model-value="overlayModel"
    @click:outside="onCloseView()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? windowWidth <= 800
              ? 'width:100vw; height: 100vh'
              : 'width:60vw; height: 100vh'
            : 'width:40vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div
            class="text-h6 text-medium ps-2 overflow-hidden"
            style="max-width: 90%"
          >
            {{ $t("coreHr.viewAccreditationCategoryAndType") }}
          </div>
          <div class="d-flex align-center">
            <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </div>
        </v-card-title>
        <v-card-text class="overflow-y-auto" style="max-height: 90vh">
          <div
            v-if="formAccess?.update"
            class="d-flex justify-end align-center"
          >
            <v-btn
              @click="onClickEdit()"
              class="mr-3 mt-3 bg-white text-primary"
              variant="text"
              rounded="lg"
            >
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon
              >{{ $t("coreHr.edit") }}</v-btn
            >
          </div>
          <v-container>
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("coreHr.accreditationCategory") }}
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Accreditation_Category) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("coreHr.accreditationType") }}
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Accreditation_Type) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("coreHr.mandatorySelfOnboarding") }}
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Mandatory) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("coreHr.enforceDependent") }}
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ selectedItem.Enforce_Dependent ? "Yes" : "No" }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("coreHr.accreditationEnforcementGroup") }}
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Group_Names) }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="selectedItem?.File_Name"
                cols="12"
                sm="6"
                lg="6"
                class="px-md-6 pb-0"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("coreHr.document") }}
                </p>
                <span
                  style="text-decoration: underline"
                  @click="openDocumentViewModel = true"
                  class="text-blue cursor-pointer mb-2"
                >
                  {{ $t("coreHr.viewDocument") }}
                </span>
              </v-col>
              <v-col cols="12" class="px-md-6 pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("coreHr.instruction") }}
                </p>
                <p
                  class="text-body-2 font-weight-regular"
                  v-html="checkNullValue(selectedItem.Instruction)"
                ></p>
              </v-col>
            </v-row>
            <v-row class="d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  class="mt-16"
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </template>
  </v-overlay>
  <FilePreviewModal
    v-if="openDocumentViewModel"
    :fileName="selectedItem.File_Name"
    folderName="Accreditations"
    fileRetrieveType="documents"
    @close-preview-modal="openDocumentViewModel = false"
  ></FilePreviewModal>
</template>
<script>
import { checkNullValue } from "@/helper";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import FilePreviewModal from "@/components/custom-components/FilePreviewModal.vue";
import moment from "moment";

export default {
  name: "ViewAccreditationType",
  props: {
    overlayModel: {
      type: Boolean,
      default: false,
    },
    selectedItem: {
      type: Object,
      default: () => {},
    },
  },
  components: { MoreDetails, FilePreviewModal },
  emits: ["close-view", "on-edit"],
  data() {
    return {
      openMoreDetails: false,
      moreDetailsList: [],
      openDocumentViewModel: false,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formAccess() {
      let formAccessRights = this.accessRights(351);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat + " HH:mm:ss") : "";
        } else return "";
      };
    },
  },
  watch: {
    selectedItem: {
      deep: true,
      handler() {
        this.prefillMoreDetails();
      },
    },
  },
  mounted() {
    this.prefillMoreDetails();
  },
  methods: {
    checkNullValue,
    onCloseView() {
      this.$emit("close-view");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      const addedOn = this.formatDate(
          new Date(this.selectedItem?.Added_On + ".000Z")
        ),
        addedByName = this.selectedItem?.Added_By_Name,
        updatedByName = this.selectedItem?.Updated_By_Name,
        updatedOn = this.formatDate(
          new Date(this.selectedItem?.Updated_On + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: this.$t("coreHr.added"),
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: this.$t("coreHr.updated"),
        });
      }
    },
    onClickEdit() {
      this.$emit("on-edit");
    },
  },
};
</script>
