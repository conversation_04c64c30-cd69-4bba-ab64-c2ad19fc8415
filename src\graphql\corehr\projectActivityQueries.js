import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_PROJECT_ACTIVITIES = gql`
  query listProjectActivities {
    listProjectActivities {
      errorCode
      message
      activities {
        activityId
        activityName
        isBillable
        description
        addedOn
        updatedOn
        updatedByName
        addedByName
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const DELETE_ACTIVITY = gql`
  mutation deleteProjectActivity($activityId: Int!) {
    deleteProjectActivity(activityId: $activityId) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_PROJECT_ACTIVITIES = gql`
  mutation addUpdateProjectActivities(
    $activityId: Int!
    $activityName: String!
    $isBillable: String!
    $description: String
  ) {
    addUpdateProjectActivities(
      activityId: $activityId
      activityName: $activityName
      isBillable: $isBillable
      description: $description
    ) {
      errorCode
      message
    }
  }
`;

export const IMPORT_PROJECT_ACTIVITIES = gql`
  mutation importProjectActivities($projectActivityData: [projectActivity]) {
    importProjectActivities(projectActivityData: $projectActivityData) {
      errorCode
      message
      validationError
    }
  }
`;
