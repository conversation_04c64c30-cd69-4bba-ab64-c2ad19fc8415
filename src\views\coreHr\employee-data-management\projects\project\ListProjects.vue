<template>
  <div>
    <div
      v-if="itemList.length > 0 && !isSmallTable"
      class="d-flex flex-wrap align-center mb-4"
      :class="isMobileView ? 'justify-center' : 'justify-end'"
    >
      <v-btn
        v-if="formAccess && formAccess.add"
        rounded="lg"
        color="primary"
        theme="dark"
        :size="isMobileView ? 'small' : 'default'"
        @click="addProjects()"
      >
        <v-icon size="13">fas fa-plus</v-icon>
        <span v-if="!isMobileView">Add New</span>
      </v-btn>
      <v-btn
        rounded="lg"
        color="transparent"
        variant="flat"
        class="mr-n1"
        :size="this.isMobileView ? 'small' : 'default'"
        @click="$emit('refetch-data')"
      >
        <v-icon>fas fa-redo-alt</v-icon>
      </v-btn>
      <div :class="isMobileView ? 'd-flex align-center' : ''">
        <v-menu v-model="openMoreMenu">
          <template #activator="{ props }">
            <v-btn color="primary" variant="text" v-bind="props">
              <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
              <v-icon v-else>fas fa-caret-up</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="action in moreActions"
              :key="action.key"
              :class="{ 'grey--text pointer-block': !action.enable }"
              @click="action.enable ? onMoreAction(action.key) : {}"
            >
              <v-hover>
                <template #default="{ hover }">
                  <v-list-item-title
                    class="pa-3"
                    :class="{
                      'pink lighten-5': hover && action.enable,
                    }"
                    ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                    >{{ action.key }}</v-list-item-title
                  >
                </template>
              </v-hover>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </div>
    <v-data-table
      id="project_table"
      :headers="tableHeaders"
      fixed-header
      :height="
        $store.getters.getTableHeightBasedOnScreenSize(320, itemList, true)
      "
      :items="itemList"
      item-key="projectId"
      :search="searchValue"
      :class="isSmallTable ? 'pt-1' : ''"
      :items-per-page="50"
      :items-per-page-options="[
        { value: 50, title: '50' },
        { value: 100, title: '100' },
        { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
      ]"
      class="mb-4"
    >
      <template #item="{ item }">
        <tr
          :id="'project_tr' + item.projectId"
          class="data-table-tr bg-white cursor-pointer"
          :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
          @click="onSelectProject(item)"
        >
          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              {{ projectLabel }}
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <div class="d-flex align-center">
                <span
                  v-show="
                    isSmallTable &&
                    windowWidth > 1264 &&
                    selectedProject &&
                    selectedProject.projectId === item.projectId
                  "
                  class="project-selected-table"
                ></span>
                <section
                  class="text-primary text-body-2 font-weight-medium pl-sm-5"
                  style="max-width: 200px"
                >
                  {{ checkNullValue(item.projectName) }}
                </section>
              </div>
            </div>
          </td>

          <td
            v-if="!isSmallTable"
            :class="isMobileView ? 'v-data-table__mobile-row' : ''"
          >
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Client
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section class="text-body-2" style="max-width: 200px">
                {{ checkNullValue(item.clientName) }}
              </section>
            </div>
          </td>

          <td
            v-if="!isSmallTable"
            :class="isMobileView ? 'v-data-table__mobile-row' : ''"
          >
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              {{ additionalFieldProjectLabel }} Location
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section class="text-body-2">
                {{ checkNullValue(item.locationName) }}
              </section>
            </div>
          </td>

          <td
            v-if="!isSmallTable"
            :class="isMobileView ? 'v-data-table__mobile-row' : ''"
          >
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              {{ additionalFieldProjectLabel }} Manager
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section class="text-body-2">
                {{ checkNullValue(item.managerName) }}
              </section>
            </div>
          </td>

          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Status
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section
                class="text-body-2 font-weight-medium d-flex justify-space-between"
                :class="item.status === 'Closed' ? 'text-red' : 'text-green'"
              >
                {{ item.status ? item.status : "Open" }}
                <span
                  class="ml-2"
                  @click.stop="
                    {
                    }
                  "
                >
                  <v-btn
                    v-if="formAccess.add"
                    class="mr-2"
                    size="small"
                    color="blue"
                    variant="outlined"
                    rounded="lg"
                    @click.stop="$emit('on-clone', item)"
                  >
                    <v-icon color="blue" class="mr-1">fas fa-clone</v-icon>
                    Clone
                  </v-btn>
                  <v-avatar
                    v-if="formAccess && formAccess.delete"
                    class="list-delete-enabled"
                    size="25"
                    color="red"
                    @click="singleDelete(item.projectId)"
                  >
                    <v-icon size="13" color="white">fas fa-trash-alt</v-icon>
                  </v-avatar>
                </span>
              </section>
            </div>
          </td>
        </tr>
      </template>
    </v-data-table>
    <AppWarningModal
      v-if="openDeleteConfirmation"
      :open-modal="openDeleteConfirmation"
      iconName="fas fa-trash"
      @close-warning-modal="closeDeleteConfirmationModal()"
      @accept-modal="deleteProject()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 primary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="loadingScreen"></AppLoading>
  </div>
</template>

<script>
import { checkNullValue, getErrorCodes } from "@/helper";
import {
  DELETE_PROJECTS,
  EXPORT_PROJECT_DETAILS_AND_ACTIVITIES,
} from "@/graphql/corehr/projectsQueries";
// mixins
import FileExportMixin from "@/mixins/FileExportMixin";
import { convertUTCToLocal } from "@/helper.js";
import moment from "moment";

export default {
  name: "ListProjects",

  mixins: [FileExportMixin],

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    isSmallTable: {
      type: Boolean,
      default: false,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    projectCoverage: {
      type: String,
      required: true,
    },
  },

  emits: [
    "refetch-data",
    "on-clone",
    "open-add-form",
    "delete-success",
    "on-grid-click",
  ],

  data: () => ({
    // table
    itemList: [],

    // others
    selectedProject: {},
    deleteProjectId: [],
    openDeleteConfirmation: false,
    loadingScreen: false,
    openMoreMenu: false,
    projectWithActivityList: [],
    showValidationAlert: false,
    validationMessages: [],
  }),

  computed: {
    formatDate() {
      return (date, dateWithTime = 1) => {
        if (dateWithTime) {
          return convertUTCToLocal(date);
        } else {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    largeTableHeaders() {
      return [
        {
          title: this.projectLabel,
          key: "projectName",
        },
        {
          title: "Client",
          key: "clientName",
        },
        {
          title: this.additionalFieldProjectLabel + " Location",
          key: "locationName",
        },
        {
          title: this.additionalFieldProjectLabel + " Manager",
          key: "managerName",
        },
        {
          title: "Status",
          key: "status",
        },
      ];
    },
    smallTableHeaders() {
      return [
        {
          title: this.projectLabel,
          key: "projectName",
        },
        {
          title: "Status",
          key: "status",
        },
      ];
    },
    tableHeaders() {
      return this.isSmallTable
        ? this.smallTableHeaders
        : this.largeTableHeaders;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // search in shares list
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    moreActions() {
      return [
        {
          key: "Export all " + this.projectLabelSmallCase,
          enable: true,
          icon: "fas fa-file-export",
        },
        {
          key: `Export ${this.projectLabelSmallCase}s with activities`,
          enable: true,
          icon: "fas fa-file-export",
        },
      ];
    },
    projectLabel() {
      return this.$store.state.projectLabel;
    },
    projectLabelSmallCase() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel ? pLabel.toLowerCase() : pLabel;
    },
    additionalFieldProjectLabel() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel === "Course" ? "" : pLabel;
    },
  },

  watch: {
    items(val) {
      this.itemList = val;
    },
    isSmallTable(val) {
      if (val === false) {
        this.selectedProject = {};
      }
    },
  },

  mounted() {
    if (this.items.length) {
      this.itemList = this.items;
    }
  },

  methods: {
    checkNullValue,

    addProjects() {
      this.$emit("open-add-form");
    },

    onMoreAction(actionType) {
      let key1 = "Export all " + this.projectLabelSmallCase,
        key2 = `Export ${this.projectLabelSmallCase}s with activities`;
      if (actionType === key1) {
        this.exportReportFile();
      } else if (actionType === key2) {
        this.fetchActivity();
      }
    },
    fetchActivity() {
      let vm = this;
      vm.loadingScreen = true;
      let projectId = this.items.map((data) => data.projectId);
      vm.$apollo
        .query({
          query: EXPORT_PROJECT_DETAILS_AND_ACTIVITIES,
          client: "apolloClientI",
          variables: {
            projectId: projectId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.exportProjectDetailsAndActivities
          ) {
            vm.projectWithActivityList = JSON.parse(
              response.data.exportProjectDetailsAndActivities
                .projectDetailsAndActivities
            );
            vm.exportProjectWithActivities();
            vm.loadingScreen = false;
          } else {
            vm.handleExportError((err = ""), "activities");
          }
        })
        .catch((err) => {
          vm.handleExportError(err, "activities");
        });
    },
    handleExportError(err = "", formName) {
      this.loadingScreen = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "exporting",
          form: formName,
          isListError: false,
        })
        .then((validationErrors) => {
          this.validationMessages = [validationErrors];
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    // export the employee attendance summary
    exportProjectWithActivities() {
      let exportData = this.projectWithActivityList;
      let updatedData = [];
      // Iterate over each project
      exportData.forEach((project) => {
        // Iterate over activities for each project
        project.activities.forEach((activity) => {
          const transformedActivity = {
            ...project, // Spread operator to include other project data
            addedOn: project.addedOn ? this.formatDate(project.addedOn) : "",
            updatedOn: project.updatedOn
              ? this.formatDate(project.updatedOn)
              : "",
            activityName: activity.activityName,
            isBillable: activity.isBillable,
            activityDescription: activity.activityDescription,
            activityFrom: activity.activityFrom
              ? this.formatDate(activity.activityFrom, 0)
              : "",
            activityTo: activity.activityTo
              ? this.formatDate(activity.activityTo, 0)
              : "",
            coverage: this.projectCoverage,
          };
          updatedData.push(transformedActivity);
        });
      });
      let exportOptions = {
        fileExportData: updatedData,
        fileName: this.projectLabel + "s with activities",
        sheetName: this.projectLabel + "s with activities",
        header: [
          { key: "projectName", header: this.projectLabel },
          { key: "clientName", header: "Client" },
          { key: "activityName", header: "Activity" },
          { key: "activityFrom", header: "Activity From" },
          { key: "activityTo", header: "Activity To" },
          { key: "coverage", header: "Coverage" },
          { key: "isBillable", header: "Billable" },
          { key: "customGroupName", header: "Custom Group" },
          {
            key: "locationName",
            header: this.additionalFieldProjectLabel + " Location",
          },
          {
            key: "managerName",
            header: this.additionalFieldProjectLabel + " Manager",
          },
          { key: "status", header: "Status" },
          { key: "description", header: "Description" },
          { key: "activityDescription", header: "Activity Description" },
          { key: "addedOn", header: "Added On" },
          { key: "addedByName", header: "Added By" },
          { key: "updatedOn", header: "Updated On" },
          { key: "updatedByName", header: "Updated By" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
    // while canceling the delete confirmation, modal will be close
    closeDeleteConfirmationModal() {
      this.deleteProjectId = null;
      this.openDeleteConfirmation = false;
    },

    singleDelete(deleteId) {
      this.deleteProjectId = deleteId;
      this.openDeleteConfirmation = true;
    },

    async deleteProject() {
      let vm = this;
      vm.openDeleteConfirmation = false;
      vm.loadingScreen = true;
      try {
        await vm.$apollo
          .mutate({
            mutation: DELETE_PROJECTS,
            variables: {
              projectId: vm.deleteProjectId,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.loadingScreen = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.projectLabel + " deleted successfully.",
            };
            vm.closeDeleteConfirmationModal();
            vm.showAlert(snackbarData);
            vm.$emit("delete-success");
          })
          .catch((deleteErr) => {
            vm.handleDeleteError(deleteErr);
          });
      } catch {
        vm.handleDeleteError();
      }
    },

    handleDeleteError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      this.closeDeleteConfirmationModal();
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0103": // no delete access
            snackbarData[
              "message"
            ] = `Sorry, you don't have access to delete the ${this.projectLabelSmallCase}. Please contact HR administrator.`;
            break;
          case "CHR0010": // Unable to delete the project as the timesheet activity exists for the project.
            snackbarData[
              "message"
            ] = `Unable to delete the ${this.projectLabelSmallCase} as it has a timesheet activity.`;
            break;
          case "CHR0011": // Unable to delete the project as the assignment exists for the project.
            snackbarData[
              "message"
            ] = `Unable to delete the ${this.projectLabelSmallCase} as it has a assignment.`;
            break;
          case "CHR0012": // Unable to delete the project as the audit assignment exists for the project.
            snackbarData[
              "message"
            ] = `Unable to delete the ${this.projectLabelSmallCase} as it has a audit assignment.`;
            break;
          case "CHR0013": // Unable to delete the project as the employee timesheet data exists for the project.
            snackbarData[
              "message"
            ] = `Unable to delete the ${this.projectLabelSmallCase} as it has a employee timesheet data.`;
            break;
          case "CHR0024": // Project details already deleted.
            snackbarData[
              "message"
            ] = `Unable to delete the ${this.projectLabelSmallCase} as it was deleted already in same or some other user session.`;
            this.$emit("delete-success");
            break;
          case "CHR0008": // Error while deleting the project details.
          case "CHR0009": // Error while processing the request to delete the project details.
            snackbarData[
              "message"
            ] = `Something went wrong while processing the request to delete the ${this.projectLabelSmallCase} details. Please try after some time.`;
            break;
          case "_UH0001": //unhandled error
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0104": // While check access rights form not found
          case "_DB0002": // Error while checking the employee access rights
          default:
            snackbarData[
              "message"
            ] = `Something went wrong while deleting the ${this.projectLabelSmallCase}. If you continue to see this issue, please contact the platform administrator.`;
            break;
        }
      } else {
        snackbarData[
          "message"
        ] = `Something went wrong while deleting the ${this.projectLabelSmallCase}. Please try after some time.`;
      }
      this.showAlert(snackbarData);
    },

    onSelectProject(item) {
      this.selectedProject = item;
      this.$emit("on-grid-click", item);
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    // export the employee attendance summary
    exportReportFile() {
      let exportData = this.itemList;
      exportData = exportData.map((el) => ({
        ...el,
        coverage: this.projectCoverage,
        addedOn: el.addedOn ? this.formatDate(el.addedOn) : "",
        updatedOn: el.updatedOn ? this.formatDate(el.updatedOn) : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.projectLabel + "s",
        sheetName: this.projectLabel + "s",
        header: [
          { key: "projectName", header: this.projectLabel },
          { key: "clientName", header: "Client" },
          { key: "coverage", header: "Coverage" },
          {
            key: "locationName",
            header: this.additionalFieldProjectLabel + " Location",
          },
          {
            key: "managerName",
            header: this.additionalFieldProjectLabel + " Manager",
          },
          { key: "status", header: "Status" },
          { key: "description", header: "Description" },
          { key: "addedOn", header: "Added On" },
          { key: "addedByName", header: "Added By" },
          { key: "updatedOn", header: "Updated On" },
          { key: "updatedByName", header: "Updated By" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>

<style scoped>
.project-selected-table {
  border-left: 7px solid rgb(var(--v-theme-primary));
  margin-left: -1.2em;
  margin-right: 7px;
  height: 4em;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
</style>
