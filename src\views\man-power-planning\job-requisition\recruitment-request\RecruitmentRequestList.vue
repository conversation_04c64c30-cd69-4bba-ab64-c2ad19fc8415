<template>
  <div v-if="formAccess && formAccess.view">
    <div v-if="listLoading || isLoading || listAPILoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>

    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      button-text="Retry"
      @button-click="refetchList()"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="originalList.length == 0"
      key="no-data-screen"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4" style="background: white">
            <v-col cols="12">
              <NotesCard
                notes="Replacement requests can be initiated for open positions that are part of the approved staffing plan."
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
              <NotesCard
                notes="If a current employee leaves or a vacancy arises, you can submit a request to fill the position using the allocated headcount from the previously approved number of positions."
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
              <NotesCard
                notes="This ensures that replacements are made efficiently without exceeding the approved staffing limits."
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
            </v-col>
            <v-col cols="12">
              <div
                class="d-flex flex-row align-center flex-wrap justify-center"
              >
                <CustomSelect
                  v-model="selectedPosition"
                  color="primary"
                  :items="groupList"
                  class="mt-5 mr-3"
                  clearable
                  :isAutoComplete="true"
                  label="Position Group"
                  placeholder="Position Group"
                  item-title="Pos_full_Name"
                  :itemSelected="selectedPosition"
                  item-value="Originalpos_Id"
                  density="compact"
                  style="max-width: 220px !important; width: 150px !important"
                  :disabled="disableGroup"
                  :isLoading="listLoading"
                  @update:model-value="updateGroup()"
                >
                </CustomSelect>
                <CustomSelect
                  v-model="selectedDivision"
                  color="primary"
                  :items="divisionList"
                  class="mt-5 mr-3"
                  clearable
                  :isAutoComplete="true"
                  label="Division"
                  placeholder="Division"
                  item-title="Pos_full_Name"
                  :itemSelected="selectedDivision"
                  item-value="Originalpos_Id"
                  density="compact"
                  style="max-width: 220px !important; width: 150px !important"
                  :disabled="disableDivision"
                  :isLoading="listLoading"
                  @update:model-value="updateDivision()"
                >
                </CustomSelect>

                <CustomSelect
                  v-model="selectedDepartment"
                  color="primary"
                  :items="departmentList"
                  class="mt-5 mr-3"
                  clearable
                  :isAutoComplete="true"
                  label="Department"
                  placeholder="Department"
                  item-title="Pos_full_Name"
                  :itemSelected="selectedDepartment"
                  item-value="Originalpos_Id"
                  density="compact"
                  :disabled="disableDepartment"
                  :isLoading="listLoading"
                  style="max-width: 220px !important; width: 150px !important"
                  @update:model-value="updateDepartment()"
                />
                <CustomSelect
                  v-model="selectedSection"
                  color="primary"
                  :items="sectionList"
                  class="mt-5 mr-3"
                  clearable
                  :isAutoComplete="true"
                  label="Section"
                  placeholder="Section"
                  item-title="Pos_full_Name"
                  :itemSelected="selectedSection"
                  item-value="Originalpos_Id"
                  density="compact"
                  :disabled="disableSection"
                  :isLoading="listLoading"
                  style="max-width: 220px !important; width: 150px !important"
                  @update:model-value="updateSection()"
                />
              </div>
            </v-col>
            <v-col
              cols="12"
              :class="
                windowWidth >= 1264
                  ? 'd-flex align-center justify-center'
                  : 'flex-wrap d-flex align-center justify-center'
              "
              class="mb-4"
            >
              <div v-if="formAccess && formAccess.add">
                <v-tooltip
                  text="Please select at least one filter to proceed."
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-card
                      v-bind="disableAddButton ? props : ''"
                      variant="text"
                    >
                      <v-btn
                        @click="addNewRecruitment()"
                        class="px-6 mr-2 primary"
                        variant="elevated"
                        size="default"
                        :disabled="disableAddButton"
                      >
                        <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                        <span> Add Approved & Forecasted Positions</span></v-btn
                      >
                    </v-card>
                  </template>
                </v-tooltip>
              </div>
              <v-tooltip location="bottom" v-else>
                <template v-slot:activator="{ props }">
                  <v-chip
                    class="mr-3"
                    v-bind="props"
                    rounded="lg"
                    color="grey"
                    size="large"
                  >
                    Add Approved & Forecasted Positions</v-chip
                  >
                </template>
                <div style="max-width: 350px !important">
                  Your don't have access to perform this action
                </div>
              </v-tooltip>
              <v-btn
                color="transparent"
                variant="flat"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="recruitmentRequestList.length === 0 && !listLoading"
      key="no-results-screen"
      main-title="There are no Approved & Forecasted Positions matched for the selected filters/searches."
      image-name="common/no-records"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4">
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="windowWidth <= 960 ? 'small' : 'default'"
                @click="resetFetchList()"
              >
                Reset Filter/Search
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <div
        v-if="recruitmentRequestList.length > 0"
        class="d-flex flex-wrap align-center my-3"
        :class="isMobileView ? 'flex-column' : ''"
        style="justify-content: space-between"
      >
        <div
          class="d-flex align-center flex-wrap"
          :class="isMobileView ? 'justify-center' : ''"
        >
          <CustomSelect
            v-model="selectedPosition"
            color="primary"
            :items="groupList"
            class="mt-5 mr-3"
            clearable
            :isAutoComplete="true"
            label="Position Group"
            placeholder="Position Group"
            item-title="Pos_full_Name"
            :itemSelected="selectedPosition"
            item-value="Originalpos_Id"
            density="compact"
            min-width="150px"
            max-width="200px"
            :disabled="disableGroup"
            :isLoading="listLoading"
            @update:model-value="updateGroup()"
          />
          <CustomSelect
            v-model="selectedDivision"
            color="primary"
            :items="divisionList"
            class="mt-5 mr-3"
            clearable
            :isAutoComplete="true"
            label="Division"
            placeholder="Division"
            item-title="Pos_full_Name"
            :itemSelected="selectedDivision"
            item-value="Originalpos_Id"
            density="compact"
            min-width="150px"
            max-width="200px"
            :disabled="disableDivision"
            :isLoading="listLoading"
            @update:model-value="updateDivision()"
          />
          <CustomSelect
            v-model="selectedDepartment"
            color="primary"
            :items="departmentList"
            class="mt-5 mr-3"
            clearable
            :isAutoComplete="true"
            label="Department"
            placeholder="Department"
            item-title="Pos_full_Name"
            :itemSelected="selectedDepartment"
            item-value="Originalpos_Id"
            density="compact"
            min-width="150px"
            max-width="200px"
            :disabled="disableDepartment"
            :isLoading="listLoading"
            @update:model-value="updateDepartment()"
          />
          <CustomSelect
            v-model="selectedSection"
            color="primary"
            :items="sectionList"
            class="mt-5 mr-3"
            clearable
            :isAutoComplete="true"
            label="Section"
            placeholder="Section"
            item-title="Pos_full_Name"
            :itemSelected="selectedSection"
            item-value="Originalpos_Id"
            density="compact"
            min-width="150px"
            max-width="200px"
            :disabled="disableSection"
            :isLoading="listLoading"
            @update:model-value="updateSection()"
          />
        </div>
        <div
          class="d-flex align-center"
          :class="isMobileView ? 'justify-center' : 'justify-end'"
        >
          <div v-if="formAccess && formAccess.add">
            <v-btn
              @click="addNewRecruitment()"
              class="px-6 mr-2 primary"
              variant="elevated"
              size="default"
            >
              <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
              <span class="primary">
                Add Approved & Forecasted Positions</span
              ></v-btn
            >
          </div>
          <v-tooltip location="bottom" v-else>
            <template v-slot:activator="{ props }">
              <v-chip
                class="mr-3"
                v-bind="props"
                rounded="lg"
                color="grey"
                size="large"
              >
                Add Approved & Forecasted Positions</v-chip
              >
            </template>
            <div style="max-width: 350px !important">
              Your don't have access to perform this action
            </div>
          </v-tooltip>
          <v-btn
            color="transparent"
            variant="flat"
            rounded="lg"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
          </v-btn>
          <v-menu class="mb-1" transition="scale-transition">
            <template v-slot:activator="{ props }">
              <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                <v-icon>fas fa-ellipsis-v</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="action in moreActions"
                :key="action.key"
                @click="onMoreAction(action.key)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <v-list-item-title
                      v-bind="props"
                      class="pa-3"
                      :class="{
                        'pink-lighten-5': isHovering,
                      }"
                      ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                      >{{ action.key }}</v-list-item-title
                    >
                  </template>
                </v-hover>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <v-data-table
        :headers="tableHeaders"
        :items="recruitmentRequestList"
        fixed-header
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
        :height="
          $store.getters.getTableHeightBasedOnScreenSize(
            290,
            recruitmentRequestList,
            true
          )
        "
        style="box-shadow: none !important"
        class="elevation-1 custom-scroll-table"
      >
        <template v-slot:item="{ item }">
          <tr
            @click="openViewForm(item)"
            class="data-table-tr bg-white cursor-pointer"
            :class="isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''"
          >
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Position
              </div>
              <section style="height: 3em" class="justify-start align-center">
                <v-tooltip
                  :text="item?.Pos_Name"
                  location="bottom"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      v-bind="props"
                      class="text-body-2 text-truncate text-start text-primary"
                      style="max-width: 200px"
                    >
                      {{ checkNullValue(item.Pos_Name) }}
                    </div>
                  </template>
                </v-tooltip>
                <div
                  class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 300px; ' : 'max-width: 200px; '
                  "
                >
                  {{ checkNullValue(item.Pos_Code) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Group Code
              </div>
              <v-tooltip
                :text="item?.Group_Name"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Group_Name ? props : {}"
                    class="text-body-2 text-truncate text-start text-primary"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Group_Name) }}
                  </div>
                </template>
              </v-tooltip>
              <v-tooltip
                :text="item?.Group_Code"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Group_Code ? props : {}"
                    class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Group_Code) }}
                  </div>
                </template></v-tooltip
              >
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Division Code
              </div>
              <v-tooltip
                :text="item?.Division_Name"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Division_Name ? props : {}"
                    class="text-body-2 text-truncate text-start text-primary"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Division_Name) }}
                  </div>
                </template>
              </v-tooltip>
              <v-tooltip
                :text="item?.Division_Code"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Division_Code ? props : {}"
                    class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Division_Code) }}
                  </div>
                </template></v-tooltip
              >
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Department Code
              </div>
              <v-tooltip
                :text="item?.Department_Name"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Department_Name ? props : {}"
                    class="text-body-2 text-truncate text-start text-primary"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Department_Name) }}
                  </div>
                </template>
              </v-tooltip>
              <v-tooltip
                :text="item?.Department_Code"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Department_Code ? props : {}"
                    class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Department_Code) }}
                  </div>
                </template></v-tooltip
              >
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Section Code
              </div>
              <v-tooltip
                :text="item?.Section_Name"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Section_Name ? props : {}"
                    class="text-body-2 text-truncate text-start text-primary"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Section_Name) }}
                  </div>
                </template>
              </v-tooltip>
              <v-tooltip
                :text="item?.Section_Code"
                location="bottom"
                max-width="300"
              >
                <template v-slot:activator="{ props }">
                  <div
                    v-bind="item?.Section_Code ? props : {}"
                    class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 100px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Section_Code) }}
                  </div>
                </template></v-tooltip
              >
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Position Level
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 300px; ' : 'max-width: 200px; '
                  "
                >
                  {{ checkNullValue(item.Position_Level) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Cost Center
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 300px; ' : 'max-width: 200px; '
                  "
                >
                  {{ checkNullValue(item.Cost_Center) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Employee Type
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 300px; ' : 'max-width: 200px; '
                  "
                >
                  {{ checkNullValue(item.Employee_Type_Name) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                No of Positions
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 300px; ' : 'max-width: 200px; '
                  "
                >
                  {{ checkNullValue(item.No_Of_Position) }}
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Status
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <div
                  class="text-subtitle-1 font-weight-regular text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 300px; ' : 'max-width: 200px; '
                  "
                >
                  <span
                    id="w-80"
                    :class="getStatusClass(item.Status)"
                    class="text-body-2 font-weight-regular d-flex justify-center align-center"
                    >{{ item.Status }}</span
                  >
                </div>
              </section>
            </td>
            <td
              class="pa-2"
              :class="
                isMobileView ? 'd-flex justify-space-between align-center' : ''
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center text-subtitle-1 text-grey-darken-1"
              >
                Actions
              </div>
              <div
                class="d-flex align-center justify-center"
                v-if="
                  enableEdit(item) &&
                  (loginEmployeeName?.toLowerCase() ===
                    item?.Added_By?.toLowerCase() ||
                    isAdmin)
                "
              >
                <ActionMenu
                  :actions="['Edit']"
                  iconColor="grey"
                  @selected-action="onActions(item)"
                  :access-rights="formAccess"
                ></ActionMenu>
              </div>
              <div v-else class="d-flex justify-center">-</div>
            </td>
          </tr>
        </template></v-data-table
      >
    </div>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
  <AppAccessDenied v-else></AppAccessDenied>
  <AddEditRecruitmentRequest
    v-if="showRecruitmentForm"
    :selectedPositionParentId="selectedPositionParentId"
    :show-add-form="showRecruitmentForm"
    :selectedRecruitment="selectedRecruitmentFormData"
    @on-close-add-form="onCloseRecruitment()"
    @open-workflow-model="openWorkflowConfirm($event)"
    @edit-form-submit="editRecruitmentRequest($event)"
  />
  <RecruitmentRequestView
    v-if="showViewForm"
    :selectedPositionData="selectedRecruitmentFormData"
    :enable-view="showViewForm"
    @close-view-details="closeView()"
    @edit-position-details="editPosition()"
  />
  <v-dialog
    v-model="showPreForm"
    class="rounded-lg overlay-custom-center"
    :style="isMobileView ? '' : 'width:50vw !important'"
  >
    <v-card class="px-8 rounded-lg">
      <v-icon
        color="primary"
        class="font-weight-bold mt-4 ml-auto"
        @click="showPreForm = false"
        >far fa-times
      </v-icon>
      <v-form ref="workflowForm">
        <v-card-title class="d-flex justify-center"
          >Choose your Workflow</v-card-title
        >
        <CustomSelect
          v-model="selectedWorkflow"
          :items="dropdownWorkflow"
          item-title="Workflow_Name"
          item-value="Workflow_Id"
          :isAutoComplete="true"
          label="Choose the approval workflow *"
          variant="underlined"
          :isLoading="workflowLoading"
          clearable
          :itemSelected="selectedWorkflow"
          :rules="[required('Workflow', selectedWorkflow)]"
          @selected-item="selectedWorkflow = $event"
        ></CustomSelect>
        <div class="d-flex justify-center pb-8">
          <v-btn
            class="px-8 primary"
            variant="elevated"
            :disabled="workflowLoading"
            @click="validateWorkflowForm"
            ><span class="primary">Start</span></v-btn
          >
        </div>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<script>
import NotesCard from "@/components/helper-components/NotesCard.vue";
import AddEditRecruitmentRequest from "./AddEditRecruitmentRequest.vue";
import { LIST_WORKFLOW } from "@/graphql/settings/irukka-integration/jobPostFormQueries";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import {
  ADD_EDIT_RECRUITMENT_REQUEST,
  LIST_RECRUITMENT_REQUEST,
  GET_WORKFLOW_APPROVAL_ENABLED,
} from "@/graphql/mpp/manPowerPlanningQueries";
import { ORG_STRUCTURE_BASED_ON_GROUP } from "@/graphql/mpp/newPositionQueries";
import { checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
import RecruitmentRequestView from "./RecruitmentRequestView.vue";

export default {
  name: "RecruitmentRequestList",
  mixins: [validationRules, FileExportMixin],
  emits: ["enable-filter", "reset-filter-value"],
  props: {
    groupList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      listLoading: false,
      listAPILoading: false,
      isLoading: false,
      isErrorInList: false,
      showRecruitmentForm: false,
      workflowLoading: false,
      showPreForm: false,
      openMoreMenu: false,
      selectedWorkflow: null,
      errorContent: "",
      recruitmentRequestList: [],
      originalList: [],
      dropdownWorkflow: [],
      recruitmentFormData: {},
      selectedRecruitmentFormData: {},
      selectedPosition: "",
      selectedPositionId: "",
      selectedPositionParentId: "",
      showViewForm: false,
      selectedDivision: "",
      selectedDepartment: "",
      selectedSection: "",
      divisionList: [],
      departmentList: [],
      sectionList: [],
      newPositionLimitToCallAPI: 10000,
      totalApiCount: 0,
      apiCallCount: 0,
      isWorkflowEnabled: null,
      allExport: false,
      parentGroup: "",
      orgLevel: "",
    };
  },
  components: {
    NotesCard,
    AddEditRecruitmentRequest,
    RecruitmentRequestView,
    CustomSelect,
    ActionMenu,
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isRecruiter() {
      return this.$store.state.isRecruiter;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    formAccess() {
      let formAccess = this.accessRights("291");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    tableHeaders() {
      let headers = [
        {
          title: "Position",
          align: "start",
          key: "Pos_Code",
        },
        { title: "Group", align: "start", key: "Group_Code" },
        { title: "Division", align: "start", key: "Division_Code" },
        { title: "Department", align: "start", key: "Department_Code" },
        { title: "Section", align: "start", key: "Section_Code" },
        { title: "Position Level", align: "start", key: "Position_Level" },
        { title: "Cost Center", align: "start", key: "Cost_Center" },
        { title: "Employee Type", align: "start", key: "Employee_Type_Name" },
        { title: "No of Positions", align: "start", key: "No_Of_Position" },
        { title: "Status", align: "start", key: "Status" },
        { title: "Actions", align: "start", sortable: false, key: "action" },
      ];
      return headers;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      if (this.isAdmin || this.isRecruiter) {
        actions.push({
          key: "Export All",
          icon: "fas fa-file-export",
        });
      }
      return actions;
    },
    disableAddButton() {
      const isInvalid = (val, match) =>
        !val || val.toString().toLowerCase() === match;

      return (
        isInvalid(this.selectedPosition, "nogroup") &&
        isInvalid(this.selectedDivision, "nodivision") &&
        isInvalid(this.selectedDepartment, "nodepartment") &&
        isInvalid(this.selectedSection, "nosection")
      );
    },
    disableSection() {
      if (!this.selectedDepartment) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (this.orgLevel?.toLowerCase() == "sec") {
        return true;
      }
      return false;
    },
    disableDepartment() {
      if (!this.selectedDivision) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
    disableDivision() {
      if (!this.selectedPosition) {
        return true;
      }
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "div" ||
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
    disableGroup() {
      if (this.isAdmin || this.isRecruiter) {
        return false;
      }
      if (
        this.orgLevel?.toLowerCase() == "grp" ||
        this.orgLevel?.toLowerCase() == "div" ||
        this.orgLevel?.toLowerCase() == "dept" ||
        this.orgLevel?.toLowerCase() == "sec"
      ) {
        return true;
      }
      return false;
    },
  },
  mounted() {
    if (this.formAccess && this.formAccess.view) {
      this.retrieveRecruitmentList("mounted");
      this.getWorkflowApproval();
    }
    this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
  },
  methods: {
    checkNullValue,
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      } else if (actionType.toLowerCase() === "export all") {
        this.allExport = true;
        this.retrieveRecruitmentList();
      }
      this.openMoreMenu = false;
    },
    openViewForm(item) {
      this.selectedRecruitmentFormData = item;
      this.showViewForm = true;
    },
    closeView() {
      this.showViewForm = false;
    },
    editPosition() {
      this.showRecruitmentForm = true;
      this.showViewForm = false;
    },
    addNewRecruitment() {
      this.showRecruitmentForm = true;
      this.selectedRecruitmentFormData = {};
    },
    onActions(item) {
      this.selectedRecruitmentFormData = item;
      this.showRecruitmentForm = true;
    },
    getStatusClass(status) {
      if (status === "Open") {
        return "text-amber-darken-4";
      } else if (status === "Closed") {
        return "text-amber";
      } else if (status === "Shortlisted") {
        return "text-purple-darken-4";
      } else if (status === "Scheduled For Interview") {
        return "text-green";
      } else if (status === "Approved") {
        return "text-brown-darken-4";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    refetchList() {
      this.retrieveRecruitmentList();
    },
    resetFetchList() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$emit("reset-filter-value");
    },
    onCloseRecruitment() {
      this.showRecruitmentForm = false;
    },
    openWorkflowConfirm(val) {
      this.recruitmentFormData = val;
      if (this.isWorkflowEnabled.toLowerCase() === "yes") {
        this.retrieveWorkflowDetails(false);
      } else {
        this.submitRecruitment();
      }
    },
    updateGroup() {
      this.selectedDivision = null;
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedPosition) {
        if (this.selectedPosition?.toString().toLowerCase() === "nogroup") {
          this.selectedPositionId = "";
        } else {
          this.selectedPositionId = this.selectedPosition;
        }
        this.retrieveCountGroupPosition("group");
      }
    },
    updateDivision() {
      this.selectedDepartment = null;
      this.selectedSection = null;
      if (this.selectedDivision) {
        this.retrieveCountGroupPosition("division");
      }
    },
    updateDepartment() {
      this.selectedSection = null;
      if (this.selectedDepartment) {
        this.retrieveCountGroupPosition("department");
      }
    },
    updateSection() {
      if (this.selectedSection) {
        this.retrieveCountGroupPosition();
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.recruitmentRequestList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.recruitmentRequestList = searchItems;
      }
    },
    retrieveCountGroupPosition(type = "") {
      this.listLoading = true;
      let groupId = this.selectPositionId() || "0";
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(groupId),
            limit: this.newPositionLimitToCallAPI,
            offset: 0,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listDetailsBasedOnGroupCode) {
            if (res.data.listDetailsBasedOnGroupCode.positionDetails) {
              const tempData =
                res.data.listDetailsBasedOnGroupCode.positionDetails;
              if (type === "mounted") {
                this.divisionList = tempData.divisionList || [];
                this.departmentList = tempData.deptList || [];
                this.sectionList = tempData.sectionList || [];
              }
              if (type === "group")
                this.divisionList = tempData.divisionList || [];
              if (type === "division")
                this.departmentList = tempData.deptList || [];
              if (type === "department")
                this.sectionList = tempData.sectionList || [];
              if (
                !this.divisionList.some(
                  (item) => item.Originalpos_Id === "nodivision"
                )
              )
                this.divisionList.unshift({
                  Pos_Name: "No Division",
                  Pos_full_Name: "No Division",
                  Originalpos_Id: "nodivision",
                });

              if (
                !this.departmentList.some(
                  (item) => item.Originalpos_Id === "nodepartment"
                )
              )
                this.departmentList.unshift({
                  Pos_Name: "No Department",
                  Pos_full_Name: "No Department",
                  Originalpos_Id: "nodepartment",
                });
              if (
                !this.sectionList.some(
                  (item) => item.Originalpos_Id === "nosection"
                )
              )
                this.sectionList.unshift({
                  Pos_Name: "No Section",
                  Pos_full_Name: "No Section",
                  Originalpos_Id: "nosection",
                });
            } else {
              this.resetTempList();
            }
            let { totalRecords } = res.data.listDetailsBasedOnGroupCode;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalRecords / this.newPositionLimitToCallAPI
              );
              for (let i = 1; i < this.totalApiCount; i++) {
                this.updateGroupPosition(i, groupId, type);
              }
              this.retrieveRecruitmentList();
            }
          }
          this.listLoading = false;
        })
        .catch((err) => {
          this.handleRetrieveRecruitmentRequestErrors(err);
          this.resetTempList();
          this.listLoading = false;
        });
    },
    handleRetrieveRecruitmentRequestErrors(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "new position request details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    updateGroupPosition(index = 1, groupId = "", type = "") {
      this.listLoading = true;
      let apiOffset = parseInt(index) * this.newPositionLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(groupId),
            limit: this.newPositionLimitToCallAPI,
            offset: apiOffset,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listDetailsBasedOnGroupCode &&
            res.data.listDetailsBasedOnGroupCode.positionDetails
          ) {
            const tempData =
              res.data.listDetailsBasedOnGroupCode.positionDetails;
            if (
              tempData.divisionList &&
              tempData.divisionList.length > 0 &&
              (type === "group" || type === "mounted")
            ) {
              this.divisionList = [
                ...this.divisionList,
                ...tempData.divisionList,
              ];
            }

            if (
              tempData.deptList &&
              tempData.deptList.length > 0 &&
              (type === "division" || type === "mounted")
            ) {
              this.departmentList = [
                ...this.departmentList,
                ...tempData.deptList,
              ];
            }
            if (
              tempData.sectionList &&
              tempData.sectionList.length > 0 &&
              (type === "department" || type === "mounted")
            ) {
              this.sectionList = [...this.sectionList, ...tempData.sectionList];
            }
            this.apiCallCount = this.apiCallCount + 1;
            if (this.totalApiCount === this.apiCallCount) {
              this.listLoading = false;
            }
          } else {
            this.resetTempList();
            this.listLoading = false;
          }
        })
        .catch((err) => {
          this.handleRetrieveRecruitmentRequestErrors(err);
          this.resetTempList();
          this.listLoading = false;
        });
    },
    resetTempList() {
      this.divisionList = [];
      this.departmentList = [];
      this.sectionList = [];
    },
    selectPositionId(type = "") {
      let code = "";
      if (
        this.selectedSection &&
        this.selectedSection.toString().toLowerCase() != "nosection"
      ) {
        if (type === "code") {
          let pos = this.sectionList.find(
            (item) => item.Originalpos_Id === this.selectedSection
          );
          code = pos ? pos.Pos_Code : "";
        } else {
          code = this.selectedSection;
        }
      } else if (
        this.selectedDepartment &&
        this.selectedDepartment.toString().toLowerCase() != "nodepartment"
      ) {
        if (type === "code") {
          let pos = this.departmentList.find(
            (item) => item.Originalpos_Id === this.selectedDepartment
          );
          code = pos ? pos.Pos_Code : "";
        } else {
          code = this.selectedDepartment.toString();
        }
      } else if (
        this.selectedDivision &&
        this.selectedDivision.toString().toLowerCase() != "nodivision"
      ) {
        if (type === "code") {
          let pos = this.divisionList.find(
            (item) => item.Originalpos_Id === this.selectedDivision
          );
          code = pos ? pos.Pos_Code : "";
        } else {
          code = this.selectedDivision.toString();
        }
      } else if (
        this.selectedPosition &&
        this.selectedPosition.toString().toLowerCase() != "nogroup"
      ) {
        if (type === "code") {
          let pos = this.groupList.find(
            (item) => item.Originalpos_Id === this.selectedPosition
          );
          code = pos ? pos.Pos_Code : "";
        } else {
          code = this.selectedPosition.toString();
        }
      }
      return code;
    },
    retrieveRecruitmentList(callingFrom = "") {
      if (!this.allExport) {
        this.listAPILoading = true;
      } else {
        this.isLoading = true;
      }
      const code = this.selectPositionId("code");

      let apiVariables = {
        formId: 291,
        postionParentCode: String(code),
        alexport: this.allExport,
      };
      if (this.selectedPosition) {
        const group = this.groupList.find(
          (item) => item.Originalpos_Id == this.selectedPosition
        );
        apiVariables.groupFilter = {
          Org_Level: "GRP",
          code: String(
            this.selectedPosition == "nogroup" ? "0" : group?.Pos_Code
          ),
        };
      }
      if (this.selectedDivision) {
        const division = this.divisionList.find(
          (item) => item.Originalpos_Id == this.selectedDivision
        );
        apiVariables.divisionFilter = {
          Org_Level: "DIV",
          code: String(
            this.selectedDivision == "nodivision" ? "0" : division?.Pos_Code
          ),
        };
      }
      if (this.selectedDepartment) {
        const department = this.departmentList.find(
          (item) => item.Originalpos_Id == this.selectedDepartment
        );
        apiVariables.departmentFilter = {
          Org_Level: "DEPT",
          code: String(
            this.selectedDepartment == "nodepartment"
              ? "0"
              : department?.Pos_Code
          ),
        };
      }
      if (this.selectedSection) {
        const section = this.sectionList.find(
          (item) => item.Originalpos_Id == this.selectedSection
        );
        apiVariables.sectionFilter = {
          Org_Level: "SEC",
          code: String(
            this.selectedSection == "nosection" ? "0" : section?.Pos_Code
          ),
        };
      }

      this.$apollo
        .query({
          query: LIST_RECRUITMENT_REQUEST,
          variables: apiVariables,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listReqruitmentRequest) {
            if (!this.allExport) {
              const tempData = res.data.listReqruitmentRequest;
              if (callingFrom === "mounted") {
                this.parentGroup = tempData?.positionParentId;
                this.orgLevel = String(tempData?.orgLevel || "").trim();
                this.selectDefaultPosition();
              }
              if (
                tempData &&
                tempData.reqruitmentRequestDetails &&
                tempData.reqruitmentRequestDetails.length > 0
              ) {
                this.recruitmentRequestList =
                  tempData.reqruitmentRequestDetails;
                this.originalList = tempData.reqruitmentRequestDetails;
                this.selectedPositionParentId = tempData.positionParentId || "";
                if (this.searchValue && this.searchValue.length > 0) {
                  this.onApplySearch(this.searchValue);
                }
                this.$emit("enable-filter", true);
              } else {
                this.$emit("enable-filter", false);
                this.recruitmentRequestList = [];
                this.originalList = [];
              }
            } else {
              let sheetUrl = res.data.listReqruitmentRequest.s3Url;
              if (sheetUrl) {
                window.open(sheetUrl, "_blank");
              }
            }
          } else {
            this.$emit("enable-filter", false);
            this.recruitmentRequestList = [];
            this.originalList = [];
          }
          this.allExport = false;
          this.isLoading = false;
        })
        .catch(() => {
          this.allExport = false;
          this.isLoading = false;
          this.$emit("enable-filter", false);
          this.recruitmentRequestList = [];
          this.originalList = [];
        })
        .finally(() => {
          this.listAPILoading = false;
        });
    },
    selectDefaultPosition() {
      if (this.orgLevel) {
        if (this.orgLevel === "GRP") {
          if (this.parentGroup) {
            this.selectedPosition = this.parentGroup;
          } else {
            this.selectedPosition = "nogroup";
          }
        } else if (this.orgLevel === "DIV") {
          this.selectedPosition = "nogroup";
          if (this.parentGroup) {
            this.selectedDivision = this.parentGroup;
          } else {
            this.selectedDivision = "nodivision";
          }
        } else if (this.orgLevel === "DEPT") {
          this.selectedPosition = "nogroup";
          this.selectedDivision = "nodivision";
          if (this.parentGroup) {
            this.selectedDepartment = this.parentGroup;
          } else {
            this.selectedDepartment = "nodepartment";
          }
        } else if (this.orgLevel === "SEC") {
          this.selectedPosition = "nogroup";
          this.selectedDivision = "nodivision";
          this.selectedDepartment = "nodepartment";
          if (this.parentGroup) {
            this.selectedSection = this.parentGroup;
          } else {
            this.selectedSection = "nosection";
          }
        }
        this.retrieveCountGroupPosition("mounted");
      } else {
        this.resetTempList();
        this.selectedPosition = "nogroup";
        this.setListWithNoFilters(true, true, true);
      }
      this.retrieveRecruitmentList();
    },
    setListWithNoFilters(
      division = false,
      department = false,
      section = false
    ) {
      if (division)
        this.divisionList.unshift({
          Pos_Name: "No Division",
          Pos_full_Name: "No Division",
          Originalpos_Id: "nodivision",
        });

      if (department)
        this.departmentList.unshift({
          Pos_Name: "No Department",
          Pos_full_Name: "No Department",
          Originalpos_Id: "nodepartment",
        });
      if (section)
        this.sectionList.unshift({
          Pos_Name: "No Section",
          Pos_full_Name: "No Section",
          Originalpos_Id: "nosection",
        });
    },
    editRecruitmentRequest(val) {
      this.recruitmentFormData = val;
      if (this.isWorkflowEnabled.toLowerCase() === "yes") {
        this.retrieveWorkflowDetails(true);
      } else {
        this.submitRecruitment();
      }
    },
    retrieveWorkflowDetails(workflowFlag) {
      let vm = this;
      this.isLoading = true;
      vm.selectedWorkflow = null;
      vm.workflowLoading = true;
      vm.dropdownWorkflow = [];
      vm.$apollo
        .query({
          query: LIST_WORKFLOW,
          variables: {
            employeeId: vm.loginEmployeeId,
            searchString: "",
            moduleId: ["10"],
            isDropDownCall: 1,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((workflowList) => {
          this.isLoading = false;
          if (
            workflowList &&
            workflowList.data &&
            workflowList.data.listWorkflow &&
            workflowList.data.listWorkflow.Workflows &&
            workflowList.data.listWorkflow.Workflows.length > 0
          ) {
            this.dropdownWorkflow = workflowList.data.listWorkflow.Workflows;
            if (
              this.selectedRecruitmentFormData &&
              this.selectedRecruitmentFormData.Workflow_Id
            ) {
              this.selectedWorkflow =
                this.selectedRecruitmentFormData.Workflow_Id;
              if (
                (this.dropdownWorkflow && this.dropdownWorkflow.length === 1) ||
                workflowFlag
              ) {
                this.submitRecruitment();
              } else {
                this.showPreForm = true;
              }
            } else {
              if (this.dropdownWorkflow && this.dropdownWorkflow.length === 1) {
                this.selectedWorkflow = this.dropdownWorkflow[0].Workflow_Id;
                this.submitRecruitment();
              } else {
                this.selectedWorkflow = this.dropdownWorkflow[0].Workflow_Id;
                this.showPreForm = true;
              }
            }
            this.workflowLoading = false;
          } else {
            this.workflowLoading = false;
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.workflowLoading = false;
          this.handleRetrieveWorkflowDetailsErrors(err);
        });
    },
    async validateWorkflowForm() {
      const { valid } = await this.$refs.workflowForm.validate();
      if (valid) {
        this.submitRecruitment();
      }
    },
    submitRecruitment() {
      let requestPayload = this.recruitmentFormData;
      this.isLoading = true;
      this.showPreForm = false;
      const workflowData = this.dropdownWorkflow.find(
        (item) => item.Workflow_Id === this.selectedWorkflow
      );
      if (workflowData) {
        requestPayload.workflowId = workflowData?.Workflow_Id;
        requestPayload.eventId = workflowData?.Event_Id;
      }
      this.$apollo
        .mutate({
          mutation: ADD_EDIT_RECRUITMENT_REQUEST,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.addUpdateReqruitmentPosition &&
            res.data.addUpdateReqruitmentPosition.message
          ) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: res.data.addUpdateReqruitmentPosition.message,
            };
            this.showRecruitmentForm = false;
            this.showAlert(snackbarData);
            this.retrieveRecruitmentList();
          } else {
            this.handleRetrieveWorkflowDetailsErrors();
          }
        })
        .catch((err) => {
          this.handleRetrieveWorkflowDetailsErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleRetrieveWorkflowDetailsErrors(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action:
            this.selectedRecruitmentFormData &&
            this.selectedRecruitmentFormData.Recruitment_Id
              ? "updating"
              : "adding",
          form: "recruitment request details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    exportReportFile() {
      let itemList = this.recruitmentRequestList.map((item) => ({
        Pos_Name: item.Pos_Name,
        Pos_Code: item.Pos_Code,
        Group_Code: item.Group_Code,
        Group_Name: item.Group_Name,
        Division_Code: item.Division_Code,
        Division_Name: item.Division_Name,
        Department_Code: item.Department_Code,
        Department_Name: item.Department_Name,
        Section_Code: item.Section_Code,
        Section_Name: item.Section_Name,
        Cost_Code: item.Cost_Code,
        Position_Level: item.Position_Level,
        Employee_Type: item.Employee_Type_Name,
        No_Of_Position: item.No_Of_Position,
        Status: item.Status,
      }));
      let fileName = "Approved & Forecasted Positions List";
      let exportHeaders = [
        {
          header: "Position Name",
          key: "Pos_Name",
        },
        {
          header: "Position Code",
          key: "Pos_Code",
        },
        {
          header: "Group Code",
          key: "Group_Code",
        },
        {
          header: "Group Name",
          key: "Group_Name",
        },
        { header: "Division Code", key: "Division_Code" },
        {
          header: "Division Name",
          key: "Division_Name",
        },
        { header: "Department Code", key: "Department_Code" },
        {
          header: "Department Name",
          key: "Department_Name",
        },
        { header: "Section Code", key: "Section_Code" },
        {
          header: "Section Name",
          key: "Section_Name",
        },
        { header: "Position Level", key: "Position_Level" },
        { header: "Cost Code", key: "Cost_Code" },
        { header: "Employee Type", key: "Employee_Type" },
        {
          header: "No of Positions",
          key: "No_Of_Position",
        },
        { header: "Status", key: "Status" },
      ];
      let exportOptions = {
        fileExportData: itemList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    async getWorkflowApproval() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_WORKFLOW_APPROVAL_ENABLED,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            formId: 291,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveWorkflowApprovalSetting &&
            response.data.retrieveWorkflowApprovalSetting.enableWorkflowApproval
          ) {
            this.isWorkflowEnabled =
              response.data.retrieveWorkflowApprovalSetting.enableWorkflowApproval;
          }
        })
        .catch(() => {
          vm.handleGetWorkflowApprovalError(err);
        });
    },
    handleGetWorkflowApprovalError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "workflow approval enable details",
        isListError: false,
      });
    },
    enableEdit(item) {
      if (item?.Status?.toLowerCase() === "rejected") {
        return false;
      }
      if (!item?.jobPostStatus) {
        return true;
      } else if (item?.jobPostStatus?.toLowerCase() === "open") {
        return false;
      } else if (item?.jobPostStatus?.toLowerCase() === "closed") {
        if (item?.candidateCount <= 0) {
          return true;
        } else {
          return false;
        }
      }
    },
  },
};
</script>
<style scoped>
.overlay-custom-center .v-overlay__content {
  display: contents !important;
}
</style>
