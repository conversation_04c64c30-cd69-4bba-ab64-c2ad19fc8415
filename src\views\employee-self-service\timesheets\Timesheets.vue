<template>
  <div>
    <AppTopBarTab
      v-if="!employeeId && mainTabs.length > 0"
      :tabs-list="mainTabs"
      :show-bottom-sheet="!listLoading"
    >
      <template #topBarContent>
        <v-row
          v-show="
            !listLoading &&
            (timesheetsListBackup.length > 0 ||
              empTimesheetsListBackup.length > 0)
          "
        >
          <v-col cols="12" class="d-flex" style="margin-left: -108px">
            <EmployeeDefaultFilterMenu class="justify-end" :isFilter="false">
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid :class="!employeeId ? 'self-timesheets-container' : ''">
      <section v-if="teamTimesheetsAccess">
        <v-window v-model="currentTabItem">
          <v-window-item value="tab-0">
            <div v-if="listLoading" class="mt-3">
              <v-skeleton-loader
                ref="skeleton1"
                type="table-heading"
                class="mx-auto"
              ></v-skeleton-loader>
              <div v-for="i in 3" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item-avatar"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </div>
            <div v-else-if="isErrorInList">
              <AppFetchErrorScreen
                image-name="common/common-error-image"
                :content="errorContent"
                icon-name="fas fa-redo-alt"
                button-text="Retry"
                :isSmallImage="true"
                @button-click="refetchAPIs()"
              >
              </AppFetchErrorScreen>
            </div>
            <v-row v-else>
              <v-col cols="12">
                <v-btn
                  v-if="
                    empTimesheetsListBackup.length > 0 && showMyTimesheetForm
                  "
                  class="mb-n2"
                  color="primary"
                  variant="text"
                  @click="showMyTimesheetForm = false"
                >
                  <v-icon class="pr-1">fas fa-angle-left fa-lg</v-icon>
                  Back</v-btn
                >
                <ListTeamTimesheets
                  v-if="!showMyTimesheetForm"
                  :items="empTimesheetsList"
                  :originalList="empTimesheetsListBackup"
                  :weekRange="weekRange"
                  :formAccess="teamTimesheetsAccess"
                  :selfService="1"
                  @on-select-item="openMyTimeSheetForm($event)"
                  @refetch-list="refetchAPIs()"
                  @on-change-week-range="onWeekChange($event)"
                  @fetch-current-week="onFetchCurrentWeek()"
                  @fetch-prev-week="onClickPrevWeek()"
                  @fetch-next-week="onClickNextWeek()"
                  @add-new-timesheet="onAddNewTimeSheet()"
                ></ListTeamTimesheets>
                <ListTimesheets
                  v-else
                  :items="timesheetsList"
                  :originalList="timesheetsListBackup"
                  :formAccess="teamTimesheetsAccess"
                  :weekRange="weekRange"
                  :empAdditionalDetails="additionalDetails"
                  :selectedEmpId="selectedEmpId"
                  :formId="formId"
                  :timeSlotEnabled="timeSlotEnabled"
                  :enforceNotes="enforceNotes"
                  :action="action"
                  :addCount="addCount"
                  :selectedEmployeeUserDefId="selectedEmployeeUserDefId"
                  :selectedEmployeeName="selectedEmployeeName"
                  @on-change-week-range="onWeekChange($event)"
                  @refetch-list="refetchAPIs()"
                  @fetch-current-week="onFetchCurrentWeek('list')"
                  @fetch-prev-week="onClickPrevWeek()"
                  @fetch-next-week="onClickNextWeek()"
                  @timesheet-added="onAddSuccess($event)"
                  @timesheet-deleted="refetchAPIs()"
                  @add-from-prev-week="refetchPrevWeekDetails()"
                  @projects-retrieved="projectList = $event"
                  @activities-retrieved="activitiesList = $event"
                  @timesheet-withdrawn="refetchAPIs()"
                  @timesheet-submitted-approval="refetchAPIs()"
                ></ListTimesheets>
              </v-col>
            </v-row>
          </v-window-item>
        </v-window>
      </section>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import {
  LIST_EMP_TIMESHEETS,
  RETRIEVE_EMPLOYEE_OTHER_DETAILS,
  RETRIEVE_TIMESHEETS_SETTINGS,
} from "@/graphql/my-team/timesheets.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import ListTimesheets from "./ListTimesheets.vue";
import moment from "moment";
import { decimalToHours, convertToDecimal } from "@/helper.js";
import ListTeamTimesheets from "../../my-team/timesheets/ListTeamTimesheets.vue";

export default defineComponent({
  name: "TimeSheets",

  components: {
    EmployeeDefaultFilterMenu,
    ListTimesheets,
    ListTeamTimesheets,
  },

  props: {
    formId: {
      type: Number,
      default: 262,
    },
    employeeId: {
      type: Number,
      default: 0,
    },
    action: {
      type: String,
      default: "",
    },
    weekRangeSelected: {
      type: String,
      default: "",
    },
    requestId: {
      type: Number,
      default: 0,
    },
    approvalStatus: {
      type: String,
      default: "",
    },
    selectedEmployeeUserDefId: {
      type: String,
      default: "",
    },
    selectedEmployeeName: {
      type: String,
      default: "",
    },
  },

  emits: ["timesheet-updated", "individual-view"],

  data() {
    return {
      currentTabItem: "tab-0",
      // list
      timesheetsList: [],
      timesheetsListBackup: [],
      empTimesheetsList: [],
      empTimesheetsListBackup: [],
      additionalDetails: {},
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      weekRange: "",
      apiFetchCount: 0,
      selectedEmpId: 0,
      timeSlotEnabled: 0,
      enforceNotes: 0,
      projectList: [],
      activitiesList: [],
      showMyTimesheetForm: false,
      selectedReqId: 0,
      selectedStatus: "",
      addCount: 0,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    teamTimesheetsAccess() {
      let formAccess = this.accessRights(this.formId);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    accessFormName() {
      let fAccess = this.accessRights(this.formId);
      if (fAccess && fAccess.customFormName) {
        return fAccess.customFormName;
      } else return "Timesheets";
    },
    mainTabs() {
      return [this.accessFormName];
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("Emp timesheets Error:", err);
    let msg =
      "Something went wrong while loading the timesheets form. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  watch: {
    apiFetchCount(count) {
      if (count == 3) {
        if (this.errorContent) {
          this.isErrorInList = true;
        }
        this.listLoading = false;
      }
    },
    showMyTimesheetForm(val) {
      this.$emit(
        "individual-view",
        this.empTimesheetsListBackup.length > 0 && val
      );
    },
    employeeId() {
      if (this.employeeId) {
        this.selectedEmpId = this.employeeId;
      } else {
        this.selectedEmpId = this.loginEmployeeId;
      }
      this.onFetchCurrentWeek();
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.employeeId) {
      this.selectedEmpId = this.employeeId;
    } else {
      this.selectedEmpId = this.loginEmployeeId;
    }
    this.onFetchCurrentWeek("initial");
  },

  methods: {
    onAddNewTimeSheet() {
      this.addCount += 1;
      this.timesheetsList = [];
      this.timesheetsListBackup = [];
      this.showMyTimesheetForm = true;
    },
    onAddSuccess(params) {
      this.$emit("timesheet-updated");
      const dataArray = params[0];
      const editedDetailsArray = params[1];
      const dayIndex = params[2];
      const isRefetch = params[3];
      if (isRefetch) {
        this.refetchAPIs();
      } else {
        // Iterate over each object in the array
        dataArray.forEach((item) => {
          // Iterate over each day in the object
          for (let dayKey in item) {
            if (dayKey.includes("Day")) {
              if (item[dayKey + "Edit"]) {
                item[dayKey].totalFormatted = item[dayKey].totalFormatted
                  ? parseFloat(convertToDecimal(item[dayKey].totalFormatted))
                  : null;
                item[dayKey].totalFormatted = decimalToHours(
                  item[dayKey].totalFormatted
                );
              }
              item[dayKey + "Edit"] = false;
              item[dayKey + "Loading"] = false;
              let editedDetailsDayIndex = "Day" + dayIndex;
              if (
                editedDetailsArray.length > 0 &&
                dayKey === editedDetailsDayIndex
              ) {
                item[dayKey].details = editedDetailsDayIndex;
                const expectedObj = {
                  Day: dayIndex,
                  Timesheet_Id: item["Timesheet_Id"],
                  detailsBytimeId: editedDetailsArray.map(
                    (item) => item.detailsBytimeId
                  ),
                  Notes: editedDetailsArray.map((item) => item.notes),
                  Start_Time: editedDetailsArray.map((item) => item.startTime),
                  End_Time: editedDetailsArray.map((item) => item.endTime),
                  totalhoursIndividual: editedDetailsArray.map((item) =>
                    parseFloat(convertToDecimal(item.totalHours))
                  ),
                };
                item[dayKey].details = expectedObj;
              }
            }
            if (item["isAdd"]) {
              if (!item.Project_Name) {
                let pNameArray = this.projectList.filter(
                  (el) => parseInt(el.projectId) == parseInt(item.Project_Id)
                );
                if (pNameArray && pNameArray.length > 0) {
                  item["Project_Name"] = pNameArray[0].projectName;
                }
              }
              if (!item.Activity_Name) {
                let aNameArray = this.activitiesList.filter(
                  (el) =>
                    parseInt(el.projectActivityId) ==
                    parseInt(item.Project_Activity_Id)
                );
                if (aNameArray && aNameArray.length > 0) {
                  item["Activity_Name"] = aNameArray[0].activityName;
                }
              }
            }
            item["clone"] = false;
            item["isAdd"] = false;
          }
        });
        this.timesheetsList = dataArray;
        this.timesheetsListBackup = dataArray;
      }
    },
    refetchAPIs(prevWeekEndDate = "", type) {
      mixpanel.track("Timesheets-list-fetch");
      this.errorContent = "";
      this.isErrorInList = false;
      this.listLoading = true;
      this.apiFetchCount = 0;
      let weekEndDate = "";
      if (!prevWeekEndDate) {
        weekEndDate =
          this.weekRange && this.weekRange.includes("to")
            ? this.weekRange.split(" to ")
            : this.weekRange;
        weekEndDate =
          weekEndDate && weekEndDate.length >= 2
            ? weekEndDate[1]
            : weekEndDate && weekEndDate.length >= 1
            ? weekEndDate[0]
            : "";
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        weekEndDate = moment(weekEndDate, orgDateFormat).format("YYYY-MM-DD");
      }
      let empView = 1;
      if (type === "initial") {
        if (this.formId == 23 && this.weekRange === this.weekRangeSelected) {
          empView = 0;
        }
        this.selectedReqId = this.requestId;
        this.selectedStatus = this.approvalStatus;
      } else {
        this.selectedReqId = 0;
        this.selectedStatus = "";
      }
      if (this.action == "approval") {
        empView = 0;
        this.selectedStatus = this.approvalStatus;
        this.apiFetchCount += 1;
      } else {
        this.getTimesheetSettings();
      }
      this.getTimesheets(weekEndDate, prevWeekEndDate, empView);
      this.getEmployeeOtherDetails(weekEndDate, prevWeekEndDate);
    },
    onFetchCurrentWeek(type) {
      if (type === "initial" && this.weekRangeSelected) {
        this.weekRange = this.weekRangeSelected;
      } else {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        const currentDate = moment(); // current date
        // Get the week start and end dates
        const weekStart = currentDate
          .clone()
          .startOf("week")
          .format(orgDateFormat);
        const weekEnd = currentDate.clone().endOf("week").format(orgDateFormat);
        this.weekRange = weekStart + " to " + weekEnd;
      }
      this.refetchAPIs("", type);
    },

    onClickPrevWeek() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      let weekEndDate =
        this.weekRange && this.weekRange.includes("to")
          ? this.weekRange.split(" to ")
          : [this.weekRange, this.weekRange];
      const startOfWeek = moment(weekEndDate[0], orgDateFormat)
        .subtract(7, "days")
        .format(orgDateFormat);
      const endOfWeek = moment(weekEndDate[1], orgDateFormat)
        .subtract(7, "days")
        .format(orgDateFormat);
      this.weekRange = startOfWeek + " to " + endOfWeek;
      this.refetchAPIs();
    },

    onClickNextWeek() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      let weekEndDate =
        this.weekRange && this.weekRange.includes("to")
          ? this.weekRange.split(" to ")
          : [this.weekRange, this.weekRange];
      const startOfWeek = moment(weekEndDate[0], orgDateFormat)
        .add(7, "days")
        .format(orgDateFormat);
      const endOfWeek = moment(weekEndDate[1], orgDateFormat)
        .add(7, "days")
        .format(orgDateFormat);
      this.weekRange = startOfWeek + " to " + endOfWeek;
      this.refetchAPIs();
    },

    refetchPrevWeekDetails() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      let weekEndDate =
        this.weekRange && this.weekRange.includes("to")
          ? this.weekRange.split(" to ")
          : this.weekRange;
      const startOfWeek = moment(weekEndDate[0], orgDateFormat);
      const prevWeekEndDate = moment(startOfWeek)
        .subtract("1", "days")
        .format("YYYY-MM-DD");
      this.refetchAPIs(prevWeekEndDate);
    },

    onWeekChange(week) {
      this.weekRange = week;
      this.refetchAPIs();
    },
    openMyTimeSheetForm(item) {
      let weekEndDate = "";
      weekEndDate =
        this.weekRange && this.weekRange.includes("to")
          ? this.weekRange.split(" to ")
          : this.weekRange;
      weekEndDate =
        weekEndDate && weekEndDate.length >= 2
          ? weekEndDate[1]
          : weekEndDate && weekEndDate.length >= 1
          ? weekEndDate[0]
          : "";
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      weekEndDate = moment(weekEndDate, orgDateFormat).format("YYYY-MM-DD");
      this.selectedReqId = item.Request_Id;
      this.selectedStatus = item.Approval_Status;
      this.apiFetchCount = 2;
      this.listLoading = true;
      this.getTimesheets(weekEndDate, "", 0);
    },
    getTimesheets(weekEndDate = "", prevWeekEndDate = "", empView = 1) {
      let vm = this;
      vm.addCount = 0;
      vm.$apollo
        .query({
          query: LIST_EMP_TIMESHEETS,
          client: "apolloClientI",
          variables: {
            selfService: vm.formId == 262 ? 1 : 0,
            weekendDate: prevWeekEndDate ? prevWeekEndDate : weekEndDate,
            employeeView: empView,
            employeeId: vm.selectedEmpId,
            requestId: vm.selectedReqId,
            status: vm.selectedStatus,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("Timesheets-list-fetch-success");
          if (
            response &&
            response.data &&
            response.data.retrieveTimeSheetProjectDetails
          ) {
            let { timesheetActivityDetails } =
              response.data.retrieveTimeSheetProjectDetails;
            timesheetActivityDetails = timesheetActivityDetails
              ? JSON.parse(timesheetActivityDetails)
              : [];
            const dataArray = timesheetActivityDetails;
            // Iterate over each object in the array
            dataArray.forEach((item) => {
              // Iterate over each day in the object
              for (let dayKey in item) {
                item["clone"] = prevWeekEndDate ? true : false;
                item["Request_Id"] = prevWeekEndDate ? 0 : item["Request_Id"];
                if (dayKey.includes("Day")) {
                  item[dayKey + "Edit"] = false;
                  item[dayKey + "Loading"] = false;
                }
                if (item[dayKey] && item[dayKey].total) {
                  item[dayKey].totalFormatted =
                    !prevWeekEndDate && item[dayKey].total
                      ? decimalToHours(item[dayKey].total)
                      : null;
                }
                if (item[dayKey] && item[dayKey].details) {
                  if (!prevWeekEndDate) {
                    // Split the comma-separated values into arrays
                    item[dayKey].details.Notes = item[dayKey].details.Notes
                      ? item[dayKey].details.Notes.split("^")
                      : [];
                    item[dayKey].details.Start_Time = item[dayKey].details
                      .Start_Time
                      ? item[dayKey].details.Start_Time.split(",")
                      : [];
                    item[dayKey].details.End_Time = item[dayKey].details
                      .End_Time
                      ? item[dayKey].details.End_Time.split(",")
                      : [];
                    item[dayKey].details.totalhoursIndividual = item[dayKey]
                      .details.totalhoursIndividual
                      ? item[dayKey].details.totalhoursIndividual.split(",")
                      : [];
                    item[dayKey].details.detailsBytimeId = item[dayKey].details
                      .detailsBytimeId
                      ? item[dayKey].details.detailsBytimeId.split(",")
                      : [];
                    item[dayKey].details.roomId = item[dayKey].details.roomIds
                      ? item[dayKey].details.roomIds.split(",")
                      : [];
                  } else {
                    // Split the comma-separated values into arrays
                    item[dayKey].details.Notes = [];
                    item[dayKey].details.Start_Time = [];
                    item[dayKey].details.End_Time = [];
                    item[dayKey].details.totalhoursIndividual = [];
                    item[dayKey].details.detailsBytimeId = [];
                  }
                }
              }
            });
            vm.apiFetchCount += 1;
            if (dataArray.length === 0 && prevWeekEndDate) {
              let snackbarData = {
                isOpen: true,
                message: "There are no activities added for the previous week",
                type: "warning",
              };
              vm.showAlert(snackbarData);
            }
            if (empView) {
              const filterBasedOnEmpId = dataArray.filter(
                (el) => el.Employee_Id == vm.selectedEmpId
              );
              if (filterBasedOnEmpId.length === 1) {
                let approvalStatus = filterBasedOnEmpId[0].Approval_Status;
                if (approvalStatus === "Rejected") {
                  vm.showMyTimesheetForm = false;
                  vm.empTimesheetsList = filterBasedOnEmpId;
                  vm.empTimesheetsListBackup = filterBasedOnEmpId;
                } else {
                  vm.selectedReqId = 0;
                  vm.selectedStatus = "";
                  vm.showMyTimesheetForm = false;
                  vm.empTimesheetsList = [];
                  vm.empTimesheetsListBackup = [];
                  vm.apiFetchCount = 2;
                  vm.listLoading = true;
                  vm.getTimesheets(weekEndDate, prevWeekEndDate, 0);
                }
              } else if (filterBasedOnEmpId.length === 0) {
                vm.timesheetsList = [];
                vm.timesheetsListBackup = [];
                vm.empTimesheetsList = [];
                vm.empTimesheetsListBackup = [];
                vm.showMyTimesheetForm = true;
              } else {
                vm.showMyTimesheetForm = false;
                vm.empTimesheetsList = filterBasedOnEmpId;
                vm.empTimesheetsListBackup = filterBasedOnEmpId;
              }
            } else {
              vm.showMyTimesheetForm = true;
              vm.timesheetsList = dataArray;
              vm.timesheetsListBackup = dataArray;
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    getEmployeeOtherDetails(weekEndDate = "", prevWeekEndDate = "") {
      let vm = this;
      vm.$apollo
        .query({
          query: RETRIEVE_EMPLOYEE_OTHER_DETAILS,
          client: "apolloClientI",
          variables: {
            weekendDate: prevWeekEndDate ? prevWeekEndDate : weekEndDate,
            employeeId: vm.selectedEmpId,
          },
        })
        .then((response) => {
          mixpanel.track("Timesheets-emp-other-details-success");
          if (
            response &&
            response.data &&
            response.data.retrieveTimesheetEmpLeaveWeekOff
          ) {
            let { employeeDetails } =
              response.data.retrieveTimesheetEmpLeaveWeekOff;
            employeeDetails = employeeDetails
              ? JSON.parse(employeeDetails)
              : [];
            if (employeeDetails.length > 0) {
              let weekEndDate =
                this.weekRange && this.weekRange.includes("to")
                  ? this.weekRange.split(" to ")
                  : [this.weekRange, this.weekRange];
              let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
              const dateRangeStart = moment(
                weekEndDate[0],
                orgDateFormat
              ).format("YYYY-MM-DD");
              const dateRangeEnd = moment(weekEndDate[1], orgDateFormat).format(
                "YYYY-MM-DD"
              );
              let mHours =
                employeeDetails[0].workHoursDetails &&
                employeeDetails[0].workHoursDetails.length > 0
                  ? employeeDetails[0].workHoursDetails[0]
                      .Regular_Hours_Per_Day +
                    employeeDetails[0].workHoursDetails[0]
                      .Overtime_Hours_Per_Day
                  : 24;
              // Initialize the expectedObject
              const expectedObject = {
                employeeId: employeeDetails[0].employeeId,
                dateOfJoin: employeeDetails[0].dateOfJoin,
                exitDate: employeeDetails[0].exitDate,
                maxHours: mHours,
              };
              let dayNumber = 0;
              // Iterate over the date range
              for (
                let currentDate = new Date(dateRangeStart);
                currentDate <= new Date(dateRangeEnd);
                currentDate.setDate(currentDate.getDate() + 1)
              ) {
                const currentDateString = currentDate
                  .toISOString()
                  .split("T")[0];

                // Find the corresponding entry in weekOffHolidayDetails
                const dayDetails =
                  employeeDetails[0].weekOffHolidayDetails.find(
                    (entry) => entry.date === currentDateString
                  );

                // Check and update the expectedObject based on the entry
                if (dayDetails) {
                  dayNumber += 1;

                  let responses = [];

                  if (dayDetails.isWeekOffDay === 1) {
                    responses.push("Week Off");
                  }
                  if (dayDetails.isHoliday === 1) {
                    responses.push("Holiday");
                  }

                  if (
                    employeeDetails[0].compOffResponse &&
                    employeeDetails[0].compOffResponse.length > 0
                  ) {
                    for (let compOff of employeeDetails[0].compOffResponse) {
                      if (currentDateString == compOff.Compensatory_Date) {
                        responses.push("Comp Off");
                      }
                    }
                  }

                  if (
                    employeeDetails[0].leaveResponse &&
                    employeeDetails[0].leaveResponse.length > 0
                  ) {
                    for (let leave of employeeDetails[0].leaveResponse) {
                      if (currentDateString == leave.date) {
                        responses.push("Leave");
                      }
                    }
                  }

                  expectedObject[`Day${dayNumber}`] = responses;
                }
              }
              expectedObject.maxHours = expectedObject.maxHours
                ? expectedObject.maxHours
                : 24;
              vm.additionalDetails = expectedObject;
            } else {
              vm.additionalDetails = {
                employeeId: vm.selectedEmpId,
                dateOfJoin: "",
                exitDate: "",
                maxHours: 24,
                Day6: [],
                Day7: [],
                Day1: [],
                Day2: [],
                Day3: [],
                Day4: [],
                Day5: [],
              };
            }
            vm.apiFetchCount += 1;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    getTimesheetSettings() {
      let vm = this;
      vm.$apollo
        .query({
          query: RETRIEVE_TIMESHEETS_SETTINGS,
          client: "apolloClientI",
          variables: {
            formId: parseInt(vm.formId),
          },
        })
        .then((response) => {
          mixpanel.track("Timesheets-settings-success");
          if (
            response &&
            response.data &&
            response.data.retrieveTimesheetSettings
          ) {
            let { timesheetSettingData } =
              response.data.retrieveTimesheetSettings;
            vm.timeSlotEnabled =
              timesheetSettingData[0].Present_Time_Slot === "Yes" ? 1 : 0;
            vm.enforceNotes =
              timesheetSettingData[0].Enforce_Note === "Yes" ? 1 : 0;
            vm.apiFetchCount += 1;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("Timesheets-list-fetch-error");
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "timesheets",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.apiFetchCount += 1;
        });
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style>
.self-timesheets-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .self-timesheets-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
