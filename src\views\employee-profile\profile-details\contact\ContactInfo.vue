<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs()"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else>
    <div v-if="showEditForm && !isApprovalView">
      <EditContactInfo
        :contactDetails="contactDetails"
        :selectedEmpStatus="selectedEmpStatus"
        :selectedEmpId="selectedEmpId"
        :actionType="actionType"
        :callingFrom="callingFrom"
        @close-edit-form="closeEditForm"
        @edit-updated="editUpdated"
      ></EditContactInfo>
    </div>

    <div v-else>
      <div v-if="!isApprovalView" class="d-flex justify-end mt-n2 mr-n2">
        <v-icon @click="refetchAPIs()" size="17" color="grey"
          >fas fa-redo-alt</v-icon
        >
      </div>
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="purple"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Permanent Address</span
            >
          </div>
          <div v-if="enableEdit && !isApprovalView">
            <v-btn @click="openEditDialog" color="primary" variant="text">
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
        </div>
        <v-row class="pa-4 ma-2 card-blue-background">
          <FieldDiff
            v-if="labelList[236]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[236]?.Field_Alias || 'Street 1'"
            :newValue="displayContactDetails.pApartment_Name"
            :oldValue="oldContactDetailsData?.pApartment_Name"
          />
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Street 2"
            :newValue="displayContactDetails.pStreet_Name"
            :oldValue="oldContactDetailsData?.pStreet_Name"
          />
          <FieldDiff
            v-if="labelList[349]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[349]?.Field_Alias || 'Barangay'"
            :newValue="displayContactDetails.pBarangay"
            :oldValue="oldContactDetailsData?.pBarangay"
          />
          <FieldDiff
            v-if="labelList[140]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[140]?.Field_Alias || 'City'"
            :newValue="displayContactDetails.pCity"
            :oldValue="oldContactDetailsData?.pCity"
          />
          <FieldDiff
            v-if="labelList[403]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[403]?.Field_Alias || 'State'"
            :newValue="displayContactDetails.pState"
            :oldValue="oldContactDetailsData?.pState"
          />
          <FieldDiff
            v-if="labelList[350]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[350]?.Field_Alias || 'Region'"
            :newValue="displayContactDetails.pRegion"
            :oldValue="oldContactDetailsData?.pRegion"
          />
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Country"
            :newValue="displayContactDetails.pCountry_Name"
            :oldValue="oldContactDetailsData?.pCountry_Name"
          />
          <FieldDiff
            v-if="labelList[147]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[147]?.Field_Alias || 'Pincode'"
            :newValue="displayContactDetails.pPincode"
            :oldValue="oldContactDetailsData?.pPincode"
          />
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="teal"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Current Address</span
            >
          </div>
        </div>
        <v-row class="pa-4 ma-2 card-blue-background">
          <FieldDiff
            v-if="labelList[236]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[236]?.Field_Alias || 'Street 1'"
            :newValue="displayContactDetails.cApartment_Name"
            :oldValue="oldContactDetailsData?.cApartment_Name"
          />
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Street 2"
            :newValue="displayContactDetails.cStreet_Name"
            :oldValue="oldContactDetailsData?.cStreet_Name"
          />
          <FieldDiff
            v-if="labelList[351]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[351]?.Field_Alias || 'Barangay'"
            :newValue="displayContactDetails.cBarangay"
            :oldValue="oldContactDetailsData?.cBarangay"
          />
          <FieldDiff
            v-if="labelList[140]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[140]?.Field_Alias || 'City'"
            :newValue="displayContactDetails.cCity"
            :oldValue="oldContactDetailsData?.cCity"
          />
          <FieldDiff
            v-if="labelList[403]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[403]?.Field_Alias || 'State'"
            :newValue="displayContactDetails.cState"
            :oldValue="oldContactDetailsData?.cState"
          />
          <FieldDiff
            v-if="labelList[352]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[352]?.Field_Alias || 'Region'"
            :newValue="displayContactDetails.cRegion"
            :oldValue="oldContactDetailsData?.cRegion"
          />
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Country"
            :newValue="displayContactDetails.cCountry_Name"
            :oldValue="oldContactDetailsData?.cCountry_Name"
          />
          <FieldDiff
            v-if="labelList[147]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[147]?.Field_Alias || 'Pincode'"
            :newValue="displayContactDetails.cPincode"
            :oldValue="oldContactDetailsData?.cPincode"
          />
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="lime"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Office Address</span
            >
          </div>
        </div>
        <v-row class="pa-4 ma-2 card-blue-background">
          <FieldDiff
            v-if="labelList[237]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[237]?.Field_Alias || 'Street 1'"
            :newValue="displayContactDetails.oApartment_Name"
            :oldValue="oldContactDetailsData?.oApartment_Name"
          />
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Street 2"
            :newValue="displayContactDetails.oStreet_Name"
            :oldValue="oldContactDetailsData?.oStreet_Name"
          />
          <FieldDiff
            v-if="labelList[353]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[353]?.Field_Alias || 'Barangay'"
            :newValue="displayContactDetails.oBarangay"
            :oldValue="oldContactDetailsData?.oBarangay"
          />
          <FieldDiff
            v-if="labelList[141]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[141]?.Field_Alias || 'City'"
            :newValue="displayContactDetails.oCity"
            :oldValue="oldContactDetailsData?.oCity"
          />
          <FieldDiff
            v-if="labelList[403]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[403]?.Field_Alias || 'State'"
            :newValue="displayContactDetails.oState"
            :oldValue="oldContactDetailsData?.oState"
          />
          <FieldDiff
            v-if="labelList[354]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[354]?.Field_Alias || 'Region'"
            :newValue="displayContactDetails.oRegion"
            :oldValue="oldContactDetailsData?.oRegion"
          />
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Country"
            :newValue="displayContactDetails.oCountry_Name"
            :oldValue="oldContactDetailsData?.oCountry_Name"
          />
          <FieldDiff
            v-if="labelList[148]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[148]?.Field_Alias || 'Pincode'"
            :newValue="displayContactDetails.oPincode"
            :oldValue="oldContactDetailsData?.oPincode"
          />
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="deep-orange"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="text-h6 text-grey-darken-1 font-weight-bold"
              >Contact Information</span
            >
          </div>
        </div>
        <v-row class="pa-4 ma-2 card-blue-background">
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Mobile Number"
            :newValue="formatMobileNumber(displayContactDetails)"
            :oldValue="formatMobileNumber(oldContactDetailsData)"
          />
          <FieldDiff
            v-if="labelList[438]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[438]?.Field_Alias || 'Emergency Contact Name'"
            :newValue="displayContactDetails.Emergency_Contact_Name"
            :oldValue="oldContactDetailsData?.Emergency_Contact_Name"
          />
          <FieldDiff
            v-if="labelList[439]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[439]?.Field_Alias || 'Fax Number'"
            :newValue="displayContactDetails.Fax_No"
            :oldValue="oldContactDetailsData?.Fax_No"
          />
          <FieldDiff
            v-if="labelList[440]?.Field_Visiblity?.toLowerCase() === 'yes'"
            :oldDataAvailable="oldContactDetailsData ? true : false"
            :label="labelList[440]?.Field_Alias || 'Emergency Contact Relation'"
            :newValue="displayContactDetails.Emergency_Contact_Relation"
            :oldValue="oldContactDetailsData?.Emergency_Contact_Relation"
          />
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Telephone Number"
            :newValue="displayContactDetails.Land_Line_No"
            :oldValue="oldContactDetailsData?.Land_Line_No"
          />
          <FieldDiff
            :oldDataAvailable="oldContactDetailsData ? true : false"
            label="Work Number"
            :newValue="displayContactDetails.Work_No"
            :oldValue="oldContactDetailsData?.Work_No"
          />
        </v-row>
      </v-card-text>
    </div>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const EditContactInfo = defineAsyncComponent(() =>
  import("./EditContactInfo.vue")
);
import FieldDiff from "@/components/custom-components/FieldDiff.vue";
import { RETRIEVE_EMP_CONTACT_INFO } from "@/graphql/employee-profile/profileQueries.js";
import { checkNullValue } from "@/helper.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "ContactInfo",
  components: {
    EditContactInfo,
    FieldDiff,
  },
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
    // Approval workflow props
    contactDetailsData: {
      type: Object,
      default: null,
    },
    oldContactDetailsData: {
      type: Object,
      default: null,
    },
    isApprovalView: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["details-retrieved", "details-updated"],
  data() {
    return {
      clickedSection: "",
      contactDetails: {
        pApartment_Name: "",
        pStreet_Name: "",
        pCity: "",
        pBarangay: "",
        pRegion: "",
        pState: "",
        pCountry: null,
        pCountry_Name: "",
        pPincode: "",
        cApartment_Name: "",
        cStreet_Name: "",
        cCity: "",
        cBarangay: "",
        cRegion: "",
        cState: "",
        cCountry: null,
        cCountry_Name: "",
        cPincode: "",
        oApartment_Name: "",
        oStreet_Name: "",
        oCity: "",
        oBarangay: "",
        oRegion: "",
        oState: "",
        oCountry: null,
        oCountry_Name: "",
        oPincode: "",
        Mobile_No_Country_Code: "",
        Mobile_No: "",
        Land_Line_No: "",
        Work_No: "",
        Fax_No: "",
        Use_Location_Address: 1,
      },
      showEditForm: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
    };
  },

  computed: {
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    displayContactDetails() {
      // Use approval data if in approval view, otherwise use component data
      return this.isApprovalView && this.contactDetailsData
        ? this.contactDetailsData
        : this.contactDetails;
    },
  },

  mounted() {
    if (!this.isApprovalView) {
      mixpanel.init(Config.mixPanelToken, {
        debug: true,
        track_pageview: true,
        persistence: "localStorage",
      });
      mixpanel.identify(this.mixPanelId);
      if (this.selectedEmpId || this.callingFrom === "profile") {
        this.getContactDetails("initial");
      }
    }
  },

  watch: {
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  methods: {
    checkNullValue,
    formatMobileNumber(contactData) {
      if (!contactData) return "";
      const countryCode = contactData.Mobile_No_Country_Code || "";
      const mobileNo = contactData.Mobile_No || "";
      return `${countryCode} ${mobileNo}`.trim();
    },
    editUpdated() {
      this.showEditForm = false;
      this.$emit("details-updated");
      this.getContactDetails("update");
    },
    openEditDialog() {
      this.showEditForm = true;
      mixpanel.track("EmpProfile-contact-edit-opened");
    },
    closeEditForm() {
      this.showEditForm = false;
      mixpanel.track("EmpProfile-contact-edit-closed");
    },
    refetchAPIs() {
      this.isErrorInList = false;
      mixpanel.track("EmpProfile-contact-refetch");
      this.getContactDetails();
    },
    getContactDetails(type) {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_CONTACT_INFO,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-contact-fetch-success");
          if (response && response.data && response.data.retrieveContactInfo) {
            const { contactDetails } = response.data.retrieveContactInfo;
            let contactData = contactDetails ? JSON.parse(contactDetails) : [];
            vm.contactDetails =
              contactData.length > 0 ? contactData[0] : vm.contactDetails;
            if (vm.contactDetails["Use_Location_Address"]) {
              vm.contactDetails["oApartment_Name"] =
                vm.contactDetails["Street1"];
              vm.contactDetails["oStreet_Name"] = vm.contactDetails["Street2"];
              vm.contactDetails["oCity"] = vm.contactDetails["City_Name"];
              vm.contactDetails["oState"] = vm.contactDetails["State_Name"];
              vm.contactDetails["oCountry_Name"] =
                vm.contactDetails["Country_Name"];
              vm.contactDetails["oCountry"] = vm.contactDetails["Country_Code"];
              vm.contactDetails["oPincode"] = vm.contactDetails["Pincode"];
            }
            vm.$emit("details-retrieved", [type, vm.contactDetails]);
            if (
              vm.actionType === "add" &&
              type === "initial" &&
              !vm.contactDetails.Mobile_No
            ) {
              vm.showEditForm = true;
              vm.listLoading = false;
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("EmpProfile-contact-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "contact details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
.text-subtitle-1 font-weight-regular {
  color: #222121 !important;
  font-size: 15px;
  margin: 12px 0px;
  overflow-wrap: break-word;
  max-width: 360px;
}
.bottom-navigation :deep() .v-bottom-navigation__content {
  background-color: white;
  justify-content: flex-start !important;
  align-items: center !important;
}
.bottom-navigation :deep() .v-bottom-navigation__content > .v-btn {
  font-size: inherit;
  height: 45px;
  max-width: 120px;
  min-width: 100px;
  font-size: 1.2rem;
  text-transform: none;
  transition: inherit;
  width: auto;
  border-radius: 0;
  margin-left: 20px !important;
}
</style>
