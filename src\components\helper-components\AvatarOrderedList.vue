<template>
  <div class="pa-0 flex flex-row justify-between items-center">
    <ul class="users-liked user-list pa-0 ma-2">
      <li v-for="(item, index) in filteredList" :key="index" class="ml-n2">
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <v-avatar
              class="text-center justify-center list"
              :class="['avatarBCColor' + (index % 5)]"
              size="35"
              v-bind="hideToolTip ? '' : props"
            >
              <span
                :class="['avatarBCColor' + (index % 5)]"
                class="text-body-1 font-weight-medium"
              >
                {{ letterAvatar(item[empNameKey]) }}
              </span>
            </v-avatar>
          </template>
          <div style="max-width: 350px !important">
            {{ item[empNameKey] }}
          </div>
        </v-tooltip>
      </li>
      <li v-if="remainingItems > 0" class="-m-2">
        <v-avatar size="35px" color="primary" class="text-white">
          +{{ remainingItems }}
        </v-avatar>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "AvatarOrderedList",
  props: {
    orderedList: {
      type: Array,
      required: true,
    },
    maximumListLength: {
      type: Number,
      default: 4,
    },
    hideToolTip: {
      type: Boolean,
      default: false,
    },
    empNameKey: {
      type: String,
      default: "employeeName",
    },
  },
  computed: {
    //show remaining items count in avatar Eg:+3
    remainingItems() {
      return this.orderedList.length - this.maximumListLength;
    },
    //From the input array we present avatar for few records remaining we present only the count
    //based on maximumListLength we present the avatar list
    filteredList() {
      return this.orderedList.slice(0, this.maximumListLength);
    },
  },
  methods: {
    letterAvatar(value) {
      if (!value) return "";
      var firstChar = value ? value.charAt(0).toUpperCase() : "";
      return firstChar;
    },
  },
};
</script>

<style lang="scss" scoped>
.user-list {
  display: flex;
  list-style-type: none;
}
.list {
  transition: 0.3s;
  &:hover {
    transform: translateY(-5px) scale(1.07);
    box-shadow: 0 14px 24px rgba(62, 57, 107, 0.2);
    z-index: 999;
  }
}
</style>
