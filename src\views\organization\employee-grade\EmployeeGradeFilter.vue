<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary darken-1"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <!-- Filter fields here -->
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedGradeCode"
              color="primary"
              :items="gradeCodeList"
              label="Grade Code"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedGradeName"
              color="primary"
              :items="gradeNameList"
              :itemSelected="selectedGradeName"
              @selected-item="onChangeSelectField($event, 'selectedGradeName')"
              label="Grade"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-text-field
              v-model="minAnnualSalary"
              label="Minimum Gross Annual Salary"
              type="number"
              variant="solo"
              @input="validateLength($event, 'minAnnualSalary')"
              :clearable="true"
            ></v-text-field>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-text-field
              v-model="maxAnnualSalary"
              label="Maximum Gross Annual Salary"
              type="number"
              variant="solo"
              :rules="[
                !maxAnnualSalary || salaryComparisonRule(maxAnnualSalary),
              ]"
              @input="validateLength($event, 'maxAnnualSalary')"
              :clearable="true"
            ></v-text-field>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedParentGrade"
              color="primary"
              :items="parentGradesList"
              :itemSelected="selectedParentGrade"
              @selected-item="
                onChangeSelectField($event, 'selectedParentGrade')
              "
              label="Parent Grade"
              variant="solo"
              multiple
              closable-chips
              chips
              clearable
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedOvertimeEligibility"
              color="primary"
              :items="overtimeEligibilityOptions"
              :itemSelected="selectedOvertimeEligibility"
              @selected-item="
                onChangeSelectField($event, 'selectedOvertimeEligibility')
              "
              label="Overtime Eligibility"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click="fnApplyFilter"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "EmployeeGradeFilter",
  props: {
    originalList: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openFormFilter: false,
      selectedGradeName: [],
      selectedGradeCode: [],
      selectedParentGrade: [],
      minAnnualSalary: null,
      maxAnnualSalary: null,
      filterItemList: [],
      gradeNameList: [],
      gradeCodeList: [],
      parentGradesList: [],
      selectedOvertimeEligibility: [],
      overtimeEligibilityOptions: ["Yes", "No"],
      lengthWarning: false,
      comparisonWarning: false,
    };
  },
  mounted() {
    this.filterItemList = this.originalList;
    this.formFilterData();
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    salaryComparisonRule() {
      return (value) => {
        const minSalary = Number(this.minAnnualSalary);
        const maxSalary = Number(this.maxAnnualSalary);
        const inputValue = Number(value);

        if (isNaN(minSalary) || isNaN(maxSalary) || isNaN(inputValue)) {
          return "Please enter valid numbers for salaries.";
        }

        return (
          maxSalary === null ||
          inputValue > minSalary ||
          "Maximum salary must be greater than minimum salary"
        );
      };
    },
  },
  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    validateLength(event, field) {
      this.lengthWarning = false;

      // Ensure the value is positive
      if (event.target.value <= 0) {
        event.target.value = "";
        this[field] = "";
      } else {
        // Limit input to 10 characters
        if (event.target.value.length > 10) {
          event.target.value = event.target.value.slice(0, 10);
          this[field] = event.target.value;
          this.lengthWarning = true;
        } else {
          this[field] = event.target.value;
        }
      }

      this.validateComparison();
    },
    validateComparison() {
      this.comparisonWarning = false;
      if (
        this.minAnnualSalary &&
        this.maxAnnualSalary &&
        this.minAnnualSalary !== null &&
        this.maxAnnualSalary !== null &&
        this.maxAnnualSalary <= this.minAnnualSalary
      ) {
        this.comparisonWarning = true;
      }
    },
    fnApplyFilter() {
      this.validateComparison();
      if (this.comparisonWarning) {
        return;
      }
      let filteredArray = this.filterItemList;
      if (
        this.selectedGradeName &&
        this.selectedGradeName.length &&
        this.selectedGradeName.length > 0
      ) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedGradeName.includes(item.grade);
        });
      }
      if (
        this.selectedGradeCode &&
        this.selectedGradeCode.length &&
        this.selectedGradeCode.length > 0
      ) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedGradeCode.includes(item.gradeCode);
        });
      }
      if (
        this.selectedParentGrade &&
        this.selectedParentGrade.length &&
        this.selectedParentGrade.length > 0
      ) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedParentGrade.includes(item.parentGrade);
        });
      }
      if (this.minAnnualSalary && this.minAnnualSalary !== null) {
        filteredArray = filteredArray.filter((item) => {
          return item.minAnnualSalary >= this.minAnnualSalary;
        });
      }
      if (this.maxAnnualSalary && this.maxAnnualSalary !== null) {
        filteredArray = filteredArray.filter((item) => {
          return item.maxAnnualSalary <= this.maxAnnualSalary;
        });
      }
      if (
        this.selectedOvertimeEligibility &&
        this.selectedOvertimeEligibility.length
      ) {
        let tempList = [];
        this.selectedOvertimeEligibility.forEach((list) => {
          if (list === "Yes") {
            tempList.push(1);
          } else {
            tempList.push(0);
          }
        });
        filteredArray = filteredArray.filter((item) => {
          return tempList.includes(item.eligibleOvertime);
        });
      }
      this.openFormFilter = false;
      this.$emit("apply-filter", filteredArray);
    },
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedGradeName = [];
      this.selectedGradeCode = [];
      this.selectedParentGrade = [];
      this.minAnnualSalary = null;
      this.maxAnnualSalary = null;
      this.lengthWarning = false;
      this.selectedOvertimeEligibility = [];
      this.comparisonWarning = false;
    },

    formFilterData() {
      const gradeNameSet = new Set();
      const gradeCodeSet = new Set();
      const parentGradeSet = new Set();

      for (let item of this.originalList) {
        if (item && item.grade) {
          gradeNameSet.add(item.grade);
        }
        if (item && item.gradeCode) {
          gradeCodeSet.add(item.gradeCode);
        }
        if (item && item.parentGrade) {
          parentGradeSet.add(item.parentGrade);
        }
      }

      this.gradeNameList = Array.from(gradeNameSet);
      this.gradeCodeList = Array.from(gradeCodeSet);
      this.parentGradesList = Array.from(parentGradeSet);
    },
  },
});
</script>
