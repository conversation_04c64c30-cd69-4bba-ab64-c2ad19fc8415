<template>
  <AppAccessDenied v-if="!formAccess"></AppAccessDenied>
  <div v-else>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row
          v-if="originalList && originalList.length > 0 && !showAddEditForm"
          justify="center"
        >
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end"
              :isFilter="false"
            ></EmployeeDefaultFilterMenu>
            <FormFilter
              v-if="!dropDownLoading"
              ref="formFilterRef"
              :status-list="candidateStatusList"
              :drop-down="dropDownDetails"
              :archive-tab="true"
              :talent-pool-tab="true"
              @reset-filter="resetFilter()"
              @apply-filter="applyFilterFromFilterComponent($event)"
            />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container fluid class="talentpool-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <!-- Skeleton Loader -->
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 4" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <!-- Error Screen -->
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            key="error-screen"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            button-text="Retry"
            @button-click="fetchTalentPoolList()"
          ></AppFetchErrorScreen>

          <!-- No Data Screen -->
          <AppFetchErrorScreen
            v-else-if="
              itemTalentList &&
              itemTalentList.length === 0 &&
              itemList &&
              itemList.length === 0
            "
            key="no-data-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  class="rounded-lg pa-5 mb-4"
                  :style="originalList.length === 0 ? 'background: white' : ''"
                >
                  <v-col cols="12">
                    <!-- Notes about talent pool benefits -->
                    <NotesCard
                      notes="Talent pools are groups of candidates that are potential fits for future job openings in your organization. They help companies stay prepared by having a list of candidates ready for hiring when needed."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="They also facilitate faster hiring processes and improve engagement with prospective candidates."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="By organizing candidates into talent pools, companies can ensure that their recruitment efforts are more strategic and efficient."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <!-- Add Talent Pool Button -->
                    <v-btn
                      v-if="
                        itemTalentList &&
                        itemTalentList.length === 0 &&
                        itemList &&
                        itemList.length === 0
                      "
                      variant="elevated"
                      class="ml-4 mt-1 secondary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddEditForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span class="primary">Add Talent Pool</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="white"
                      rounded="lg"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="fetchTalentPoolList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>

                  <!-- Expansion Panel for Add Talent Pool -->
                  <v-expansion-panels
                    v-if="isAddOperation"
                    v-model="addExpansionPanel"
                  >
                    <v-expansion-panel>
                      <v-expansion-panel-title
                        collapse-icon="mdi-minus"
                        expand-icon="mdi-plus"
                      >
                        <v-card-title
                          class="d-flex align-center justify-space-between"
                        >
                          <!-- Title: Talent Pool on the left -->
                          <span class="mr-15">Talent Pool</span>
                        </v-card-title>
                      </v-expansion-panel-title>
                      <v-expansion-panel-text>
                        <v-row>
                          <!-- Column for v-text-field taking up most of the width -->
                          <v-col cols="10">
                            <v-text-field
                              v-model="talentPool"
                              variant="solo"
                              :isRequired="true"
                              :rules="[
                                required('Talent Pool', talentPool),
                                validateWithRulesAndReturnMessages(
                                  talentPool,
                                  'talentPool',
                                  'Talent Pool'
                                ),
                              ]"
                              ref="talentPool"
                              ><template v-slot:label
                                ><span>Talent Pool</span>
                                <span class="ml-1" style="color: red"
                                  >*</span
                                ></template
                              ></v-text-field
                            >
                          </v-col>
                          <!-- Column for the icons -->
                          <v-col
                            cols="2"
                            class="d-flex justify-end align-center"
                          >
                            <!-- Icons for Save and Close -->
                            <v-btn
                              icon="fas fa-check"
                              variant="text"
                              @click="validateTalentPoolForm()"
                              color="primary mb-3"
                              size="small"
                            ></v-btn>
                            <v-btn
                              icon="fas fa-times"
                              variant="text"
                              @click="closeWindow(false)"
                              color="primary mb-3"
                              size="small"
                            ></v-btn>
                          </v-col>
                        </v-row>
                      </v-expansion-panel-text>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <!-- Talent Pool Candidate List -->
          <div v-if="showCandidateProfile">
            <JobCandidatesDetails
              :formAccess="formAccess"
              :selectedJobCandidateId="
                selectedCandidateItem ? selectedCandidateItem.Candidate_Id : 0
              "
              :selectedJobPostId="
                selectedCandidateItem ? selectedCandidateItem.Job_Post_Id : 0
              "
              :candidateChangeCount="candidateChangeCount"
              :candidateList="itemList"
              :selectedItem="selectedCandidateItem"
              :parentTabName="parentTabName"
              :isRecruiter="isRecruiter"
              @retrieve-error="closeAllForms($event)"
              @close-view-form="closeAllForms($event)"
              @on-change-candidate="onChangeCandidate($event)"
            ></JobCandidatesDetails>
          </div>
          <div v-else-if="!listLoading">
            <!-- Candidate List Table -->
            <div>
              <div
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  color="transparent"
                  class="ml-1 mt-1"
                  variant="flat"
                  @click="refetchList"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      color="transparent"
                      variant="flat"
                      class="mt-1"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
              <v-row>
                <!-- Left Side: Talent Pool List -->
                <v-col
                  :cols="isMobileView ? 12 : 3"
                  :class="isMobileView ? 'mb-4' : ''"
                >
                  <v-card class="cutom-talent-list">
                    <div>
                      <v-form ref="TalentPoolAddForm" @submit.prevent="">
                        <v-expansion-panels
                          v-if="isAddOperation"
                          v-model="addExpansionPanel"
                          :readonly="true"
                        >
                          <v-expansion-panel>
                            <v-card-title
                              class="d-flex align-center justify-space-between"
                            >
                              <!-- Talent Pool on the left -->
                              <span class="mr-15">Talent Pool</span>

                              <!-- Add button on the right -->
                              <v-btn
                                variant="elevated"
                                class="d-flex primary"
                                rounded="lg"
                                :size="isMobileView ? 'small' : 'default'"
                                @click="openAddEditForm()"
                              >
                                <v-icon size="15" class="pr-1"
                                  >fas fa-plus</v-icon
                                >
                                <span class="primary">Add</span>
                              </v-btn>
                            </v-card-title>
                            <v-expansion-panel-text>
                              <v-row>
                                <!-- Column for v-text-field taking up most of the width -->
                                <v-col cols="9">
                                  <v-text-field
                                    v-model="talentPool"
                                    variant="solo"
                                    class="mr-2"
                                    :isRequired="true"
                                    :rules="[
                                      required('Talent Pool', talentPool),
                                      validateWithRulesAndReturnMessages(
                                        talentPool,
                                        'talentPool',
                                        'Talent Pool'
                                      ),
                                    ]"
                                    ref="talentPool"
                                    ><template v-slot:label
                                      ><span>Talent Pool</span>
                                      <span class="ml-1" style="color: red"
                                        >*</span
                                      ></template
                                    ></v-text-field
                                  >
                                </v-col>
                                <!-- Column for the icons -->
                                <v-col
                                  cols="3"
                                  class="d-flex justify-end align-center"
                                >
                                  <!-- Icons for Save and Close -->
                                  <v-btn
                                    icon="fas fa-check"
                                    variant="text"
                                    @click="validateTalentPoolForm()"
                                    color="primary mb-3"
                                    size="small"
                                  ></v-btn>
                                  <v-btn
                                    icon="fas fa-times"
                                    variant="text"
                                    @click="closeWindow(false)"
                                    color="primary mb-3"
                                    size="small"
                                  ></v-btn>
                                </v-col>
                              </v-row>
                            </v-expansion-panel-text>
                          </v-expansion-panel>
                        </v-expansion-panels>
                        <v-expansion-panels
                          v-if="!isAddOperation"
                          v-model="editExpansionPanel"
                          :readonly="true"
                        >
                          <v-expansion-panel>
                            <v-card-title
                              class="d-flex align-center justify-space-between"
                            >
                              <!-- Talent Pool on the left -->
                              <span class="custom-title mr-15"
                                >Talent Pool</span
                              >

                              <!-- Add button on the right -->
                              <v-btn
                                variant="elevated"
                                class="d-flex primary"
                                rounded="lg"
                                :size="isMobileView ? 'small' : 'default'"
                                @click="openAddEditForm()"
                              >
                                <v-icon size="15" class="pr-1"
                                  >fas fa-plus</v-icon
                                >
                                <span class="primary">Add</span>
                              </v-btn>
                            </v-card-title>

                            <v-expansion-panel-text>
                              <v-row>
                                <!-- Column for v-text-field taking up most of the width -->
                                <v-col cols="9">
                                  <v-text-field
                                    v-model="talentPool"
                                    variant="solo"
                                    :isRequired="true"
                                    class="mr-2"
                                    :rules="[
                                      required('Talent Pool', talentPool),
                                      validateWithRulesAndReturnMessages(
                                        talentPool,
                                        'talentPool',
                                        'Talent Pool'
                                      ),
                                    ]"
                                    ref="talentPool"
                                    ><template v-slot:label
                                      ><span>Talent Pool</span>
                                      <span class="ml-1" style="color: red"
                                        >*</span
                                      ></template
                                    ></v-text-field
                                  >
                                </v-col>
                                <!-- Column for the icons -->
                                <v-col
                                  cols="3"
                                  class="d-flex justify-end align-center"
                                >
                                  <!-- Icons for Save and Close -->
                                  <v-btn
                                    icon="fas fa-check"
                                    variant="text"
                                    @click="validateTalentPoolForm()"
                                    color="primary mb-3"
                                    size="small"
                                  ></v-btn>
                                  <v-btn
                                    icon="fas fa-times"
                                    variant="text"
                                    @click="closeWindow(false)"
                                    color="primary mb-3"
                                    size="small"
                                  ></v-btn>
                                </v-col>
                              </v-row>
                            </v-expansion-panel-text>
                          </v-expansion-panel>
                        </v-expansion-panels>
                      </v-form>
                    </div>

                    <v-divider></v-divider>

                    <!-- Use v-list to display the talent pool list -->
                    <v-list
                      :height="
                        $store.getters.getTableHeightBasedOnScreenSize(
                          320,
                          itemTalentList
                        )
                      "
                      id="listItem"
                    >
                      <v-list-item
                        v-for="item in sortedTalentList"
                        :key="item.talentPoolId"
                        @click="handleTalentPoolClick(item.talentPoolId)"
                        class="list-item-clickable text-truncate"
                        :style="{
                          'background-color':
                            item.talentPoolId === selectedTalentPoolId
                              ? 'rgb(var(--v-theme-primary))'
                              : '',
                          color:
                            item.talentPoolId === selectedTalentPoolId
                              ? 'white'
                              : '',
                        }"
                      >
                        <!-- Use v-row to align the name and action menu horizontally -->
                        <v-row class="align-center">
                          <v-col>
                            <div style="max-width: 150px" class="text-truncate">
                              <v-tooltip
                                :text="item.talentPool"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-if="
                                      item.talentPool &&
                                      item.talentPool.length > 15
                                    "
                                    v-bind="props"
                                  >
                                    <v-list-item-title>{{
                                      item.talentPool
                                    }}</v-list-item-title>
                                  </span>
                                  <span v-else>
                                    <v-list-item-title>{{
                                      item.talentPool
                                    }}</v-list-item-title>
                                  </span>
                                </template>
                              </v-tooltip>
                            </div>
                          </v-col>
                          <v-badge
                            v-if="item.candidateCount"
                            :color="
                              item.talentPoolId === selectedTalentPoolId
                                ? 'white'
                                : 'hover'
                            "
                            text-color="primary"
                            :content="item.candidateCount"
                            overlap
                          ></v-badge>
                          <!-- Action Menu aligned to the right -->
                          <v-col class="d-flex justify-end mb-2">
                            <div>
                              <ActionMenu
                                @selected-action="onActions($event, item)"
                                :actions="['Edit', 'Delete']"
                                :access-rights="formAccess"
                                iconColor="{
                            'background-color':
                              item.talentPoolId === selectedTalentPoolId
                                ? 'rgb(var(--v-theme-primary))'
                                : '',
                            color:
                              item.talentPoolId === selectedTalentPoolId
                                ? 'white'
                                : '',
                          }"
                              ></ActionMenu>
                            </div>
                          </v-col>
                        </v-row>
                      </v-list-item>
                    </v-list>
                  </v-card>
                </v-col>

                <!-- Right Side: Candidate List Table -->
                <v-col :cols="isMobileView ? 12 : 9">
                  <!-- Your existing candidate list table here -->
                  <div v-if="itemList.length === 0">
                    <AppFetchErrorScreen
                      image-name="common/no-records"
                      main-title="There are no candidates for the selected filters/searches."
                    >
                      <template #contentSlot>
                        <div class="d-flex mb-2 flex-wrap justify-center">
                          <v-btn
                            color="primary"
                            variant="elevated"
                            class="ml-4 mt-1"
                            rounded="lg"
                            :size="isMobileView ? 'small' : 'default'"
                            @click.stop="resetFilter()"
                          >
                            Reset Filter/Search
                          </v-btn>
                        </div>
                      </template>
                    </AppFetchErrorScreen>
                  </div>
                  <div v-else>
                    <!-- Mobile Card View -->
                    <div v-if="isMobileView" class="mobile-cards-container">
                      <v-card
                        v-for="item in itemList"
                        :key="item.Candidate_Id"
                        class="candidate-card mb-4 pa-4"
                        elevation="2"
                        @click="navigateToCandidateProfile(item)"
                      >
                        <!-- Top Row with Action Menu -->
                        <v-row no-gutters class="mb-2">
                          <v-col cols="11"></v-col>
                          <v-col
                            cols="1"
                            class="d-flex justify-end align-start"
                          >
                            <ActionMenu
                              @selected-action="
                                checkCandidateBlacklist($event, item)
                              "
                              :actions="actions(item)"
                              :access-rights="formAccess"
                              @click.stop=""
                            ></ActionMenu>
                          </v-col>
                        </v-row>

                        <!-- Main Content Row -->
                        <v-row no-gutters>
                          <v-col cols="12" class="pl-2">
                            <!-- Candidate Name and Contact -->
                            <div class="candidate-header mb-3">
                              <div class="d-flex align-center mb-1">
                                <h3
                                  class="text-primary text-h6 font-weight-medium"
                                >
                                  {{
                                    item.First_Name +
                                    " " +
                                    (item.Middle_Name
                                      ? item.Middle_Name + " "
                                      : "") +
                                    item.Last_Name
                                  }}
                                </h3>
                                <v-tooltip
                                  v-if="
                                    item.Blacklisted?.toLowerCase() == 'yes'
                                  "
                                  text="This candidate has been blacklisted"
                                >
                                  <template v-slot:activator="{ props }">
                                    <v-icon
                                      size="16"
                                      class="ml-2"
                                      color="primary"
                                      v-bind="props"
                                    >
                                      fas fa-user-slash
                                    </v-icon>
                                  </template>
                                </v-tooltip>
                                <v-badge
                                  v-if="
                                    item.Duplicate_Count &&
                                    item.Duplicate_Count > 1
                                  "
                                  color="hover"
                                  text-color="white"
                                  :content="item.Duplicate_Count"
                                  class="ml-4"
                                ></v-badge>
                              </div>
                              <div class="text-body-2 text-grey-darken-1">
                                {{ checkNullValue(item.Mobile_No) }}
                              </div>
                              <div
                                v-if="item.Personal_Email"
                                class="text-body-2 text-grey-darken-1"
                              >
                                {{ item.Personal_Email }}
                              </div>
                            </div>

                            <!-- Skill Set Section -->
                            <div class="mb-2">
                              <div
                                class="text-caption text-grey-darken-2 font-weight-bold"
                              >
                                Skill Set
                              </div>
                              <div class="text-body-2 font-weight-regular">
                                {{ splitSkillSet(item.Skill_Set) }}
                              </div>
                            </div>

                            <!-- Experience Section -->
                            <div class="mb-3">
                              <div
                                class="text-caption text-grey-darken-2 font-weight-bold"
                              >
                                Experience
                              </div>
                              <div class="text-body-2 font-weight-regular">
                                {{
                                  (item.Total_Experience_In_Years &&
                                  item.Total_Experience_In_Years > 0
                                    ? item.Total_Experience_In_Years + " Years"
                                    : "") +
                                    " " +
                                    (item.Total_Experience_In_Months &&
                                    item.Total_Experience_In_Months > 0
                                      ? item.Total_Experience_In_Months +
                                        " Months"
                                      : "") || "-"
                                }}
                              </div>
                            </div>

                            <!-- Action Buttons Row -->
                            <v-row no-gutters class="align-center">
                              <v-col cols="12">
                                <v-btn
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                  @click.stop="
                                    retrieveResumeDetails(item.Resume)
                                  "
                                >
                                  <v-icon size="14" class="mr-1"
                                    >fas fa-file-pdf</v-icon
                                  >
                                  View Resume
                                </v-btn>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card>
                    </div>

                    <!-- Desktop Table View -->
                    <v-data-table
                      v-else
                      v-model="selectedCandidateRecords"
                      :headers="tableHeaders"
                      :items="itemList"
                      item-value="Candidate_Id"
                      fixed-header
                      :height="
                        $store.getters.getTableHeightBasedOnScreenSize(
                          290,
                          itemList
                        )
                      "
                      :items-per-page="50"
                      :items-per-page-options="[
                        { value: 50, title: '50' },
                        { value: 100, title: '100' },
                        {
                          value: -1,
                          title: '$vuetify.dataFooter.itemsPerPageAll',
                        },
                      ]"
                      :sort-by="[{ key: 'First_Name', order: 'asc' }]"
                    >
                      <template v-slot:item="{ item }">
                        <tr
                          @click="navigateToCandidateProfile(item)"
                          class="data-table-tr bg-white cursor-pointer"
                        >
                          <td style="width: 250px">
                            <section
                              class="text-body-2 text-truncate"
                              style="max-width: 200px"
                            >
                              <div class="text-primary d-flex align-center">
                                <v-tooltip
                                  :text="
                                    item.First_Name +
                                    ' ' +
                                    (item.Middle_Name
                                      ? item.Middle_Name + ' '
                                      : '') +
                                    item.Last_Name
                                  "
                                  location="top"
                                >
                                  <template v-slot:activator="{ props }">
                                    <section
                                      class="text-body-2 font-weight-regular text-truncate"
                                      v-bind="props"
                                    >
                                      {{
                                        item.First_Name +
                                        " " +
                                        (item.Middle_Name
                                          ? item.Middle_Name + " "
                                          : "") +
                                        item.Last_Name
                                      }}
                                    </section>
                                  </template>
                                </v-tooltip>
                                <v-tooltip
                                  text="This candidate has been blacklisted"
                                >
                                  <template v-slot:activator="{ props }">
                                    <v-icon
                                      v-if="
                                        item.Blacklisted?.toLowerCase() == 'yes'
                                      "
                                      size="13"
                                      class="ml-2"
                                      style="border-radius: 50%"
                                      v-bind="props"
                                    >
                                      fas fa-user-slash
                                    </v-icon>
                                  </template>
                                </v-tooltip>
                                <v-badge
                                  v-if="
                                    item.Duplicate_Count &&
                                    item.Duplicate_Count > 1
                                  "
                                  color="hover"
                                  text-color="primary"
                                  :content="item.Duplicate_Count"
                                  overlap
                                  style="border-radius: 50%"
                                  class="mx-4"
                                ></v-badge>
                              </div>
                              <div>
                                {{ checkNullValue(item.Mobile_No) }}
                                <div
                                  v-if="item.Personal_Email"
                                  class="text-grey"
                                >
                                  {{ item.Personal_Email }}
                                </div>
                              </div>
                            </section>
                          </td>
                          <td class="pa-2 pl-5" style="max-width: 100px">
                            <section class="text-body-2 text-truncate">
                              <v-tooltip
                                :text="splitSkillSet(item.Skill_Set)"
                                location="top"
                                max-width="400"
                              >
                                <template v-slot:activator="{ props }">
                                  <section
                                    class="text-body-2 font-weight-regular text-truncate"
                                    v-bind="
                                      item.Skill_Set &&
                                      item.Skill_Set.length > 2
                                        ? props
                                        : ''
                                    "
                                  >
                                    {{ splitSkillSet(item.Skill_Set) }}
                                  </section>
                                </template>
                              </v-tooltip>
                            </section>
                          </td>
                          <td class="pa-2 pl-5">
                            <section class="text-body-2 text-truncate">
                              <span class="text-body-2 font-weight-regular">
                                {{
                                  (item.Total_Experience_In_Years &&
                                  item.Total_Experience_In_Years > 0
                                    ? item.Total_Experience_In_Years + " Years"
                                    : "") +
                                    " " +
                                    (item.Total_Experience_In_Months &&
                                    item.Total_Experience_In_Months > 0
                                      ? item.Total_Experience_In_Months +
                                        " Months"
                                      : "") || "-"
                                }}
                              </span>
                            </section>
                          </td>
                          <td
                            @click.stop="retrieveResumeDetails(item.Resume)"
                            style="width: 150px"
                          >
                            <section
                              class="text-body-2 font-weight-medium text-truncate"
                            >
                              <a class="text-decoration-none"> View Resume </a>
                            </section>
                          </td>
                          <td
                            class="text-body-2 text-end"
                            style="width: 150px"
                            @click.stop=""
                          >
                            <ActionMenu
                              @selected-action="
                                checkCandidateBlacklist($event, item)
                              "
                              :actions="actions(item)"
                              :access-rights="formAccess"
                            ></ActionMenu>
                          </td>
                        </tr>
                      </template>
                    </v-data-table>
                  </div>
                  <div v-if="isMobileView" style="height: 100px"></div>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>

    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteTalentPool()"
    ></AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div class="d-flex justify-space-between">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <v-icon size="25" class="mr-2" @click="closeValidationAlert()">
            fas fa-times
          </v-icon>
        </div>
      </template>
    </AppSnackBar>
  </div>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="resume"
    :appendUnderScoreInDomain="true"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
  <ArchiveTalentPool
    v-if="archiveTalentPool"
    :candidateDetails="candidateDetails"
    :candidateIdSelected="candidateIdSelected"
    :candidateId="selectedCandidateId"
    :candidateName="candidateDetails.First_Name"
    :is-overlay="true"
    :currentTalentPoolId="selectedTalentPoolId"
    @refetch-talent-pool-list="fetchTalentPoolList()"
    @close-archive-candidates-window="archiveTalentPool = false"
    @form-updated="refetchList()"
  />
  <MoveCandidateToJob
    v-if="moveCandidateToJob"
    :candidateDetails="candidateDetails"
    :candidateIdSelected="candidateIdSelected"
    :candidateId="selectedCandidateId"
    :is-overlay="true"
    :candidateName="candidateDetails.First_Name"
    :currentTalentPoolId="selectedTalentPoolId"
    @close-move-candidate-window="moveCandidateToJob = false"
    @refetch-talent-pool-list="fetchTalentPoolList()"
    @form-updated="refetchList('Talent Pool was updated')"
  />
  <MoveToOtherTalentPoolForm
    v-if="openMoveToOtherTalentPoolForm"
    :candidateDetails="candidateDetails"
    :candidateIdSelected="candidateIdSelected"
    :candidateId="candidateDetails.Candidate_Id"
    :originalStatusList="originalStatusList"
    :candidateName="candidateDetails.First_Name"
    :currentTalentPoolId="selectedTalentPoolId"
    @close-talent-pool-overlay-form="openMoveToOtherTalentPoolForm = false"
    @refetch-talent-pool-list="fetchTalentPoolList()"
  >
  </MoveToOtherTalentPoolForm>
  <AppWarningModal
    v-if="openRemoveBlacklistModal"
    :open-modal="openRemoveBlacklistModal"
    confirmation-heading="Are you sure you want to remove this candidate from blacklist ?"
    icon-name=""
    @close-warning-modal="openRemoveBlacklistModal = false"
    @accept-modal="removeCandidateFromBlacklist()"
  ></AppWarningModal>
  <AppWarningModal
    v-if="blacklistCandidateWarning"
    :open-modal="blacklistCandidateWarning"
    confirmation-heading="This candidate has been blacklisted. Are you sure you want to proceed ?"
    icon-name=""
    @close-warning-modal="cancelBlacklistAction()"
    @accept-modal="onActionsCandidate(this.selectedType, this.candidateDetails)"
  ></AppWarningModal>
  <BlacklistForm
    :view-form="openBlacklistForm"
    :selectedCandidateId="selectedCandidateId"
    @close-form="openBlacklistForm = false"
    @update-form="(openBlacklistForm = false), refetchList()"
  ></BlacklistForm>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import {
  LIST_JOB_CANDIDATES,
  GET_TALENT_POOL_LIST,
  DELETE_TALENT_POOL_DETAILS,
  ADD_UPDATE_TALENT_POOL,
  CANDIDATES_DROP_DOWN,
  REMOVE_CANDIDATE_FROM_BLACKLIST,
} from "@/graphql/recruitment/recruitmentQueries.js";
import FormFilter from "../../FormFilter.vue";
import ArchiveTalentPool from "./ArchiveTalentPool.vue";
import MoveCandidateToJob from "./MoveCandidateToJob.vue";
import MoveToOtherTalentPoolForm from "./MoveToOtherTalentPoolForm.vue";
const JobCandidatesDetails = defineAsyncComponent(() =>
  import("./../../job-candidates-details/JobCandidatesDetails.vue")
);
import moment from "moment";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
const BlacklistForm = defineAsyncComponent(() =>
  import("../../BlacklistForm.vue")
);

export default {
  name: "TalentPool",
  props: {
    duplicateCandidateDetails: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    EmployeeDefaultFilterMenu,
    ActionMenu,
    NotesCard,
    ArchiveTalentPool,
    MoveCandidateToJob,
    FilePreviewModal,
    FormFilter,
    MoveToOtherTalentPoolForm,
    JobCandidatesDetails,
    BlacklistForm,
  },
  mixins: [FileExportMixin, validationRules],
  data: () => ({
    addExpansionPanel: 1,
    editExpansionPanel: 1,
    isEdit: false,
    talentPool: "",
    talentPoolId: null,
    isListError: false,
    showViewForm: false,
    showAddEditForm: false,
    openWarningModal: false,
    archiveTalentPool: false,
    moveCandidateToJob: false,
    openMoveToOtherTalentPoolForm: false,
    archiveReason: null,
    notificationTime: "",
    archiveComment: null,
    originalList: [],
    itemList: [],
    selectedItem: {},
    isFilter: false,
    errorContent: "",
    isErrorInList: false,
    hoveredItemId: null,
    isLoading: false,
    listLoading: false,
    openMoreMenu: false,
    selectedStatus: [],
    selectedCandidateRecords: [],
    resetFilterCount: 0,
    appliedFilterCount: 0,
    itemTalentList: [],
    selectedTalentPoolId: null,
    isAddOperation: false,
    selectedCandidateId: null,
    candidateDetails: {},
    showValidationAlert: false,
    emptyScenarioMsg: "",
    retrievedFileName: "",
    openModal: false,
    candidateStatusList: [],
    dropDownDetails: [],
    dropDownLoading: true,
    isFilterApplied: false,
    backupFilteredList: [],
    emptyFilterScreen: false,
    targetPosition: 0,
    selectedCandidateItem: null,
    showCandidateProfile: false,
    parentTabName: "Talent Pool",
    talentPoolClicked: false,
    candidateChangeCount: 0,
    openBlacklistForm: false,
    openRemoveBlacklistModal: false,
    blacklistCandidateWarning: false,
    selectedType: "",
  }),
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.fetchTalentPoolList();
    this.setDefaultTalentPool();
    this.retrieveDropDownDetails();
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
      if (val && val.length) {
        this.isFilterApplied = true;
      }
    },
  },
  computed: {
    actions() {
      return (item) => {
        if (item.Blacklisted?.toLowerCase() == "yes") {
          return [
            "Archive",
            "Move Candidate to Job",
            "Move to Other Talent Pool",
            "Remove from Blacklist",
          ];
        } else {
          return [
            "Archive",
            "Move Candidate to Job",
            "Move to Other Talent Pool",
            "Blacklist Candidate",
          ];
        }
      };
    },
    landedFormName() {
      let employeetype = this.accessRights("297");
      if (employeetype && employeetype.customFormName) {
        return employeetype.customFormName;
      } else return "Talent Pool";
    },
    sortedTalentList() {
      return this.itemTalentList.slice().sort((a, b) => {
        return a.talentPool.localeCompare(b.talentPool);
      });
    },
    currentTabItem() {
      let index = this.mainTabList.indexOf(this.landedFormName);
      return "tab-" + index;
    },
    userIsRecruiter() {
      return this.$store.state.isRecruiter ? "Yes" : "No";
    },
    mainTabList() {
      let tab = [];
      const addTalentPool = this.formAccess; // Check for talentFormAccess

      if (
        this.jobCandidateFormAccess &&
        this.jobCandidateFormAccess.view &&
        this.onboardingFormAccess
      ) {
        tab.push("Job Candidates", "Upcoming Interviews", "Pending Feedback");
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Archived");
        tab.push("Duplicate Candidates", "Onboarding");
      } else if (
        this.jobCandidateFormAccess &&
        this.jobCandidateFormAccess.view &&
        !this.onboardingFormAccess
      ) {
        tab.push("Job Candidates", "Upcoming Interviews", "Pending Feedback");
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Archived");
        tab.push("Duplicate Candidates");
      } else if (this.onboardingFormAccess) {
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Onboarding");
      } else {
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
      }
      return tab;
    },
    jobCandidateFormAccess() {
      let formAccess = this.accessRights("16");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    onboardingFormAccess() {
      let formAccess = this.accessRights("178");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccess() {
      let formAccess = this.accessRights("297");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        formAccess.accessRights["archive"] = formAccess.accessRights["update"];
        formAccess.accessRights["move candidate to job"] =
          formAccess.accessRights["update"];
        formAccess.accessRights["move to other talent pool"] =
          formAccess.accessRights["update"];
        formAccess.accessRights["blacklist candidate"] =
          formAccess.accessRights["update"];
        formAccess.accessRights["remove from blacklist"] =
          formAccess.accessRights["update"];
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    tableHeaders() {
      return [
        {
          title: "Candidate",
          align: "start",
          key: "First_Name",
        },
        { title: "Skill Set", key: "Skill_Set", width: "25%" },
        { title: "Experience", key: "Total_Experience_In_Years" },
        {
          title: "Resume",
          key: "Resume",
        },
        {
          title: "Actions",
          key: "action",
          align: "end",
          sortable: false,
        },
      ];
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    async validateTalentPoolForm() {
      const { valid } = await this.$refs.TalentPoolAddForm.validate();

      if (valid) {
        this.addUpdateList();
      }
    },
    async addUpdateList() {
      let vm = this;
      try {
        vm.isLoading = true;
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_TALENT_POOL,
            client: "apolloClientAV",
            fetchPolicy: "no-cache",
            variables: {
              talentPoolId: this.isAddOperation ? 0 : vm.talentPoolId,
              talentPool: vm.cleanString(vm.talentPool),
            },
          })
          .then((response) => {
            if (
              response &&
              response.data &&
              response.data.addUpdateTalentPool
            ) {
              const { errorCode, validationError } =
                response.data.addUpdateTalentPool;
              if (!errorCode && !validationError) {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: this.isAddOperation
                    ? vm.landedFormName + " added successfully."
                    : vm.landedFormName + " updated successfully.",
                };
                vm.showAlert(snackbarData);
                vm.$emit("form-updated");

                // Resetting form state
                vm.fetchTalentPoolList();
                vm.isLoading = false;
                vm.addExpansionPanel = 1;
                vm.editExpansionPanel = 1;
                vm.talentPool = "";

                // Call refetchList to update the list and candidates
                vm.refetchList("talent-pool-add/update-success");
              } else {
                vm.isLoading = false;
                vm.handleAddEditError(
                  this.isAddOperation ? "updating" : "adding"
                );
              }
            } else {
              vm.isLoading = false;
              vm.handleAddEditError(
                this.isAddOperation ? "updating" : "adding"
              );
            }
          })
          .catch((addEditError) => {
            vm.isLoading = false;
            vm.handleAddEditError(
              this.isAddOperation ? "updating" : "adding",
              addEditError
            );
          });
      } catch (e) {
        vm.handleAddEditError(this.isAddOperation ? "updating" : "adding");
      }
    },
    handleAddEditError(action, err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: action,
          form: "talentPool",
          isListError: false,
        })
        .then((validationErrors) => {
          this.validationMessages = validationErrors;
          this.showValidationAlert = true;
        });
      this.refetchList("talent-pool-add/update-failed");
    },
    onDeleteTalentPool() {
      let vm = this;
      vm.isLoading = true;
      const { talentPoolId } = vm.selectedItem;

      vm.$apollo
        .mutate({
          mutation: DELETE_TALENT_POOL_DETAILS,
          variables: {
            talentPoolId: talentPoolId,
          },
          client: "apolloClientAV",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.landedFormName + " deleted successfully.",
          };
          vm.selectedTalentPoolId = null;
          vm.showAlert(snackbarData);
          vm.fetchTalentPoolList();
          vm.openWarningModal = false;
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },
    handleDeleteError(err = "") {
      this.isLoading = false;
      this.openWarningModal = false;
      this.fetchTalentPoolList();
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "talent pool",
        isListError: false,
      });
    },
    onDelete(item) {
      this.selectedItem = item;
      this.openWarningModal = true;
    },
    onEdit(item) {
      this.selectedItem = item;
      this.addUpdateList();
    },
    onArchiveCandidate(item) {
      this.selectedItem = item;
      this.archiveTalentPool = true;
    },
    onMoveCandidateToJob(item) {
      this.selectedItem = item;
      this.moveCandidateToJob = true;
    },
    onMoveCandidateToOtherTalentPool(item) {
      this.selectedItem = item;
      this.openMoveToOtherTalentPoolForm = true;
    },
    fetchTalentPoolList() {
      let vm = this;

      vm.isLoading = true;
      vm.listLoading = true;

      vm.$apollo
        .query({
          query: GET_TALENT_POOL_LIST,
          variables: {
            formId: 297,
          },
          client: "apolloClientAY",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getTalentPoolList &&
            !response.data.getTalentPoolList.errorCode
          ) {
            vm.itemTalentList =
              response.data.getTalentPoolList.talentPoolListData;

            // Sort the talent pool list alphabetically by talentPool name
            vm.itemTalentList.sort((a, b) => {
              const nameA = a.talentPool.toUpperCase();
              const nameB = b.talentPool.toUpperCase();
              return nameA.localeCompare(nameB);
            });

            // Automatically select the first talent pool after sorting if the list is not empty
            if (vm.itemTalentList && vm.itemTalentList.length > 0) {
              const firstTalentPoolId = vm.itemTalentList[0].talentPoolId;
              if (vm.selectedTalentPoolId) {
                let talentPoolPresent = vm.isTalentPoolIdPresent(
                  vm.itemTalentList,
                  vm.selectedTalentPoolId
                );
                if (!talentPoolPresent) {
                  vm.selectedTalentPoolId = firstTalentPoolId;
                }
              } else {
                vm.selectedTalentPoolId = firstTalentPoolId;
              }

              if (this.$route.query?.candidateId) {
                const talentPoolId = this.$route.query?.talentPoolId;
                let jobCandidateId = this.$route.query?.candidateId;
                this.fetchTalentPoolCandidates(talentPoolId, jobCandidateId);
              } else {
                vm.fetchTalentPoolCandidates(vm.selectedTalentPoolId);
              }
              vm.isLoading = false;
              vm.listLoading = false;
            } else {
              vm.selectedTalentPoolId = null;
              vm.isLoading = false;
              vm.listLoading = false;
            }
          } else {
            vm.itemTalentList = [];
            vm.selectedTalentPoolId = null; // Handle empty list case
            vm.isLoading = false;
            vm.listLoading = false;
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.listLoading = false;
          vm.handleTalentPoolListError(err);
        });
    },
    handleTalentPoolListError(err = "") {
      this.isLoading = false;
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Talent Pool List",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
        });
    },

    navigateToCandidateProfile(candidateRecord) {
      this.selectedCandidateItem = candidateRecord;
      this.showCandidateProfile = true;
    },

    fetchTalentPoolCandidates(talentPoolId, jobCandidateId = null) {
      let vm = this;
      vm.listLoading = true;
      let container = null;

      vm.$apollo
        .query({
          query: LIST_JOB_CANDIDATES,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            searchString: "",
            action: "",
            isDropDownCall: 0,
            jobPostId: [],
            preferredLocation: [],
            status: [],
            candidateId: jobCandidateId ? parseInt(jobCandidateId) : null,
            talentPoolId: talentPoolId ? parseInt(talentPoolId) : 0,
            formId: 297,
          },
        })
        .then((response) => {
          const { listJobCandidates } = response.data || {};
          if (
            listJobCandidates &&
            listJobCandidates.jobCandidates &&
            !listJobCandidates.errorCode &&
            !listJobCandidates.errorCode.length
          ) {
            vm.originalList = listJobCandidates.jobCandidates;
            vm.backupFilteredList = listJobCandidates.jobCandidates;
            vm.itemList = listJobCandidates.jobCandidates;
            this.$nextTick(() => {
              container = document.getElementById("listItem");
              if (container) {
                container.scrollTop = this.targetPosition;
              }
            });

            vm.listLoading = false;
            if (this.$route.query?.candidateId) {
              const decodedCandidateId = parseInt(
                this.$route.query.candidateId
              );
              const candidateItem = vm.itemList.find(
                (data) => data.Candidate_Id === decodedCandidateId
              );

              if (candidateItem && !this.talentPoolClicked) {
                vm.navigateToCandidateProfile(candidateItem);
              }
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "talent pool candidates",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    handleTalentPoolClick(talentPoolId) {
      this.selectedTalentPoolId = talentPoolId;
      this.talentPoolClicked = true;
      const container = document.getElementById("listItem");
      if (container) {
        this.targetPosition = container.scrollTop;
      }
      this.fetchTalentPoolCandidates(talentPoolId);
    },
    setDefaultTalentPool() {
      if (this.sortedTalentList.length > 0) {
        this.selectedTalentPoolId = this.sortedTalentList[0].talentPoolId;
      }
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onApplyFilter(filteredArray) {
      let filteredList = filteredArray;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedStatus.includes(item.Candidate_Status);
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = filteredList;
    },
    applyFilterFromFilterComponent(filter) {
      let filterObj = {
        archiveReasons: filter.archiveReasons,
        preferredLocation: filter.preferredLocation,
        currentLocation: filter.currentLocation,
        source: filter.source,
        skills: filter.skills,
        gender: filter.gender,
        experience: filter.experience,
        currentCTC: filter.currentCTC,
        expectedCTC: filter.expectedCTC,
        applicantRanking: filter.applicantRanking,
        daysToJoin: filter.daysToJoin,
      };
      this.applyFilter(filterObj);
    },
    applyFilter(filter) {
      this.emptyFilterScreen = false;
      let filteredList = this.originalList;
      if (filter.jobPosts) {
        filteredList = filteredList.filter((item) => {
          return filter.jobPosts == item.Job_Post_Id;
        });
        this.jobTitleFilteredList = filteredList;
      } else {
        this.jobTitleFilteredList = filteredList;
      }
      if (filter.status && filter.status.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.status.includes(item.Status_Id);
        });
      }
      if (filter.archiveReasons && filter.archiveReasons.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.archiveReasons.includes(item.Archive_Reason);
        });
      }
      if (filter.preferredLocation && filter.preferredLocation.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.preferredLocation.includes(item.Preferred_Location);
        });
      }
      if (filter.currentLocation && filter.currentLocation.length > 0) {
        filteredList = filteredList.filter((item) => {
          return item.Current_Location
            ? item.Current_Location.toString()
                .toLowerCase()
                .includes(filter.currentLocation)
            : true;
        });
      }
      if (filter.skills && filter.skills != 0) {
        filteredList = filteredList.filter((item) => {
          if (item.Skill_Set) {
            let skillsList = item.Skill_Set || [];
            return skillsList.includes(filter.skills);
          }
        });
      }
      if (filter.source && filter.source.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.source.includes(item.Source);
        });
      }
      if (filter.gender && filter.gender.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.gender.includes(item.Gender);
        });
      }
      if (filter.experienceInYears && filter.experienceInYears.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Total_Experience_In_Years >= filter.experienceInYears[0] &&
            item.Total_Experience_In_Years <= filter.experienceInYears[1]
          );
        });
      }
      if (filter.experienceInMonths && filter.experienceInMonths.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Total_Experience_In_Months >= filter.experienceInMonths[0] &&
            item.Total_Experience_In_Months <= filter.experienceInMonths[1]
          );
        });
      }
      if (filter.currentCTC && filter.currentCTC.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Current_CTC >= filter.currentCTC[0] &&
            item.Current_CTC <= filter.currentCTC[1]
          );
        });
      }
      if (filter.expectedCTC && filter.expectedCTC.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Expected_CTC >= filter.expectedCTC[0] &&
            item.Expected_CTC <= filter.expectedCTC[1]
          );
        });
      }
      if (filter.applicantRanking && filter.applicantRanking.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Applicant_Ranking >= filter.applicantRanking[0] &&
            item.Applicant_Ranking <= filter.applicantRanking[1]
          );
        });
      }
      if (filter.daysToJoin && filter.daysToJoin.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Notice_Period >= filter.daysToJoin[0] &&
            item.Notice_Period <= filter.daysToJoin[1]
          );
        });
      }
      this.isFilterApplied = true;
      this.backupFilteredList = filteredList;
      this.itemList = filteredList;
      if (this.itemList.length === 0) {
        this.emptyFilterScreen = true;
      }
      this.onApplySearch(this.searchValue);
    },
    splitSkillSet(skill) {
      if (skill?.length) return skill.join(", ");
      else "-";
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.$store.state.empSearchValue = "";
      this.itemList = this.originalList;
      this.backupFilteredList = this.originalList;
      this.emptyFilterScreen = false;
      this.fetchTalentPoolList();
      let filterObj = {};
      this.applyFilter(filterObj);
    },
    onTabChange(tabName) {
      if (tabName === "Job Candidates") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates");
      } else if (tabName === "Duplicate Candidates") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/duplicate-candidates");
      } else if (tabName === "Onboarding") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/onboarding");
      } else if (tabName === "Upcoming Interviews") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/upcoming-interviews");
      } else if (tabName === "Pending Feedback") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/pending-feedback");
      } else if (tabName === "Archived") {
        this.isLoading = true;
        this.$router.push("/recruitment/job-candidates/archived-candidates");
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.backupFilteredList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupFilteredList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
        if (this.itemList.length === 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Candidate ID", key: "Candidate_Id" },
        { header: "First Name", key: "First_Name" },
        { header: "Middle Name", key: "Middle_Name" },
        { header: "Last Name", key: "Last_Name" },
        { header: "Mobile Number", key: "Mobile_No" },
        { header: "Personal Email", key: "Personal_Email" },
        { header: "Date of Birth", key: "DOB" },
        { header: "Skill Set", key: "Skill_Set" },
        { header: "Total Experience", key: "Total_Experience" },
        { header: "Talent Pool", key: "Talent_Pool" },
      ];

      const exportList = this.itemList.map((item) => ({
        Candidate_Id: item.Candidate_Id,
        Mobile_No: item.Mobile_No_Country_Code
          ? item.Mobile_No_Country_Code + item.Mobile_No
          : item.Mobile_No,
        Personal_Email: item.Personal_Email,
        DOB: this.formatDate(item.DOB, true),
        Skill_Set: item?.Skill_Set?.join(", ") || "",
        First_Name: item.First_Name,
        Middle_Name: item.Middle_Name,
        Last_Name: item.Last_Name,
        Total_Experience:
          (
            (item.Total_Experience_In_Years &&
            item.Total_Experience_In_Years > 0
              ? item.Total_Experience_In_Years + " Years"
              : "") +
            " " +
            (item.Total_Experience_In_Months &&
            item.Total_Experience_In_Months > 0
              ? item.Total_Experience_In_Months + " Months"
              : "")
          ).trim() || "-",
        Talent_Pool: item.Talent_Pool_Name,
      }));

      const exportOptions = {
        fileExportData: exportList,
        fileName: "Talent Pool and Candidate Report",
        sheetName: "Talent Pool and Candidate Report",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
      mixpanel.track("Talent-Pool-and-Candidate-Report-Details-exported");
    },
    panelMembers(panelMemberNames) {
      if (!panelMemberNames) return [];
      const splitNames = panelMemberNames.split(",");
      return splitNames.map((name) => ({ panelMember: name.trim() }));
    },
    formatDate(date, withoutTime) {
      let orgDateFormat = withoutTime
        ? this.$store.state.orgDetails.orgDateFormat
        : this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
      return date ? moment(date).format(orgDateFormat) : "-";
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.fetchTalentPoolList();
      this.fetchTalentPoolCandidates(this.selectedTalentPoolId);
      this.addExpansionPanel = 1;
      this.editExpansionPanel = 1;
      this.resetFilter();
    },
    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        this.onDelete(item);
      } else if (type && type.toLowerCase() === "edit") {
        this.openAddEditForm(item);
      }
    },
    checkCandidateBlacklist(type, item) {
      this.candidateDetails = item;
      this.selectedType = type;
      if (
        item &&
        item.Blacklisted?.toLowerCase() == "yes" &&
        type.toLowerCase() !== "remove from blacklist"
      ) {
        this.blacklistCandidateWarning = true;
      } else {
        this.onActionsCandidate(type, item);
      }
    },
    onActionsCandidate(type, item) {
      this.blacklistCandidateWarning = false;
      if (type && type.toLowerCase() === "archive") {
        this.selectedCandidateId = item.Candidate_Id;
        this.candidateDetails = item;
        this.onArchiveCandidate(item);
      } else if (type && type.toLowerCase() === "move candidate to job") {
        this.selectedCandidateId = item.Candidate_Id;
        this.candidateDetails = item;
        this.onMoveCandidateToJob(item);
      } else if (type && type.toLowerCase() === "move to other talent pool") {
        this.selectedCandidateId = item.Candidate_Id;
        this.candidateDetails = item;
        this.onMoveCandidateToOtherTalentPool(item);
      } else if (type && type.toLowerCase() === "blacklist candidate") {
        this.selectedCandidateId = item.Candidate_Id;
        this.openBlacklistForm = true;
      } else if (type && type.toLowerCase() === "remove from blacklist") {
        this.selectedCandidateId = item.Candidate_Id;
        this.openRemoveBlacklistModal = true;
      }
    },

    onOpenViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    closeViewForm() {
      this.selectedItem = null;
      this.showViewForm = false;
    },
    onChangeCandidate(item) {
      this.selectedCandidateItem = item;
      this.candidateChangeCount += 1;
    },

    openAddEditForm(item = {}) {
      const { talentPoolId, talentPool } = item;
      if (item && Object.keys(item).length) {
        mixpanel.track("Talent Pool add/edit form opened");
        this.isEdit = false;
        this.editExpansionPanel = 0;
        this.showViewForm = false;
        this.showAddEditForm = true;
        this.selectedItem = talentPoolId;
        if (talentPoolId) {
          this.talentPool = talentPool;
          this.isAddOperation = false;
          this.talentPoolId = talentPoolId;
        }
      } else {
        this.addExpansionPanel = 0;
        this.isAddOperation = true;
        this.talentPool = null;
      }
    },
    closeAllForms() {
      this.showCandidateProfile = false;
      this.selectedTalentPoolId = null;
      this.$router.replace({ path: this.$route.path });
      this.fetchTalentPoolList();
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedItem = null;
    },
    closeWindow(isSuccess) {
      this.addExpansionPanel = 1;
      this.editExpansionPanel = 1;
      this.talentPool = "";
      this.$emit("close-form", isSuccess);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getStatusClass(status) {
      if (status?.toLowerCase() === "shortlisted") {
        return "text-green";
      } else if (status?.toLowerCase() === "scheduled for interview") {
        return "text-amber";
      } else if (status?.toLowerCase() === "rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    retrieveResumeDetails(filepath) {
      this.retrievedFileName = filepath;
      this.openModal = true;
    },
    retrieveDropDownDetails() {
      let vm = this;
      vm.$apollo
        .query({
          query: CANDIDATES_DROP_DOWN,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getDropDownBoxDetails &&
            !response.data.getDropDownBoxDetails.errorCode.length
          ) {
            vm.dropDownDetails = response.data.getDropDownBoxDetails;
            vm.dropDownLoading = false;
          } else {
            vm.dropDownLoading = false;
          }
        })
        .catch(() => {
          vm.dropDownLoading = false;
          vm.dropDownDetails = [];
        });
    },
    cleanString(inputString) {
      return inputString?.trim().replace(/\s+/g, " ");
    },
    // Function to check if talentPoolId exists
    isTalentPoolIdPresent(talentPools, id) {
      return talentPools.some((pool) => pool.talentPoolId === id);
    },
    removeCandidateFromBlacklist() {
      let vm = this;
      vm.openRemoveBlacklistModal = false;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: REMOVE_CANDIDATE_FROM_BLACKLIST,
          variables: {
            candidateId: this.selectedCandidateId,
          },
          client: "apolloClientAH",
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidate removed from blacklist successfully",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.refetchList();
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "removing",
            form: "candidate from blacklist",
            isListError: false,
          });
        });
    },
    cancelBlacklistAction() {
      this.selectedType = "";
      this.candidateDetails = {};
      this.blacklistCandidateWarning = false;
    },
  },
};
</script>
<style scoped>
.talentpool-container {
  padding: 5em 2em 0em 3em;
}
.custom-title {
  font-size: 14px;
}

.cutom-talent-list {
  overflow-y: scroll;
  position: relative;
  scrollbar-color: #c5c5c5 #ebebeb;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Mobile Card Styles */
.mobile-cards-container {
  padding: 0 16px;
}

.candidate-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.candidate-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.candidate-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

@media screen and (max-width: 805px) {
  .talentpool-container {
    padding: 4em 1em 0em 1em;
  }

  .mobile-cards-container {
    padding: 0 8px;
  }

  .cutom-talent-list {
    max-height: 300px;
  }
}
</style>
