<template>
  <v-tabs v-model="tab" bg-color="#F9F9F9">
    <v-tab value="recruitmentTab">Recruitment</v-tab>
  </v-tabs>

  <v-window v-model="tab" v-if="formAccess">
    <v-window-item value="recruitmentTab">
      <div
        v-if="showSignUp"
        class="mt-12 d-flex justify-center align-items-center"
      >
        <SignInForm @close="showSignUp = false" @change-status="changeStatus">
        </SignInForm>
      </div>
      <div
        v-if="!showSignUp"
        class="mt-12 d-flex justify-center align-items-center"
      >
        <v-card :style="isMobileView ? '' : 'width:500px'" class="rounded-lg">
          <div class="ml-7 mt-2 d-flex align-start">
            <img
              :src="irukkaLogo"
              style="width: 50px; height: auto"
              class="ml-n5 mr-5"
              alt="idea-bulb"
            />
            <v-card-title>Irukka Integration</v-card-title>
          </div>
          <div style="background-color: rgb(247 247 247)" class="pa-1 my-2">
            <div
              bg-color="grey-lighten-2"
              class="ma-4 text-justify text-subtitle-1"
            >
              Irukka, your employment-centric social platform, simplifies job
              postings and connects with the right candidates. Ensure your
              profile's security with OTP verification; click 'Integrate Now' to
              begin
            </div>
          </div>
          <div
            v-if="formAccess && formAccess.update && idToken && companySignUpId"
            class="d-flex justify-center align-center pa-4"
          >
            <div class="text-center">
              <v-menu open-on-hover location="top">
                <template v-slot:activator="{ props }">
                  <span
                    rounded="lg"
                    v-bind="props"
                    variant="plain"
                    :ripple="false"
                    active="false"
                    class="custom-btn"
                  >
                    <AppToggleButton
                      button-active-text="Active"
                      button-inactive-text="Inactive"
                      button-active-color="#7de272"
                      button-inactive-color="red"
                      @chosen-value="addUpdateIntegrationStatus"
                      :current-value="activeOrNot === 'Active' ? true : false"
                    ></AppToggleButton>
                  </span>
                </template>
                <v-card
                  class="pa-4 text-subtitle-1 rounded-lg"
                  max-width="60vw"
                >
                  The integration process has been successfully completed. You
                  can now create new open positions for approval. Please proceed
                  to the
                  <a
                    style="cursor: pointer; color: blue"
                    @click="redirectToPage()"
                    >Job Post</a
                  >
                  page to get started on this. Once approved, these positions
                  can be posted on Irukka
                </v-card>
              </v-menu>
            </div>
          </div>
          <div v-else class="d-flex justify-center pa-4">
            <v-btn
              color="pink"
              rounded="lg"
              @click="showSignUp = true"
              v-if="formAccess && formAccess.add"
              >Integrate Now</v-btn
            >
          </div>
        </v-card>
      </div>
    </v-window-item>
  </v-window>
  <AppAccessDenied v-else></AppAccessDenied>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
import validationRules from "../../../mixins/validationRules";
import { defineAsyncComponent } from "vue";
import {
  GET_INTEGRATION_STATUS,
  ADD_UPDATE_IRUKKA_STATUS,
} from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
import { getErrorCodes } from "@/helper.js";
const SignInForm = defineAsyncComponent(() => import("./SignInForm.vue"));
//importing the irukka logo
import myImage from "../../../assets/images/common/Irukka-Logo.png";
export default {
  name: "IrukkaSignIn",
  components: {
    SignInForm,
  },
  mixins: [validationRules],

  data() {
    return {
      irukkaLogo: myImage,
      tab: null,
      errorPage: false,
      showSignUp: false,
      mobileNumber: null,
      validationMessages: [],
      showValidationAlert: false,
      show: false,
      mainTabList: ["Recruitment", "Others"],
      isClicked: false,
      counter: 0,
      mobileNoCountryCode: null,
      originalMobileNoCountryCode: "+91",
      showBottomNavigation: false,
      isTabletView: false,
      idToken: "",
      irukkaToken: "",
      errorMessage: "",
      mobileNumberProps: "",
      irukkaStatus: null,
      activeOrNot: "",
      integrationId: null,
      integrationType: "",
      irukkaRefreshToken: "",
      companySignUpId: "",
    };
  },

  computed: {
    mobileNumberValidation() {
      if (!this.mobileNumber || !this.mobileNoCountryCode) {
        return "Mobile number is required";
      } else if (this.mobileNumber && this.mobileNumberProps) {
        return this.mobileNumberProps.valid ||
          this.mobileNumberProps.valid === undefined
          ? ""
          : "Please provide a valid mobile number";
      }
      return "";
    },
    landedFormName() {
      return "Recruitment";
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    formAccess() {
      let accessFormName = this.landedFormName.replace(/\s/g, "-");
      accessFormName = accessFormName.toLowerCase();
      let formAccessRights = this.accessRights(accessFormName);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isClickCheck() {
      if (this.isClicked) {
        return false;
      } else {
        return true;
      }
    },
    isFormFilled() {
      if (this.showBottomNavigation) {
        return false;
      } else {
        return true;
      }
    },
  },
  mounted() {
    this.fetchIntegrationStatus();
    // fetch the company signUPId, that we store at the time of doing company signUp
    this.companySignUpId = window.$cookies.get("companySignupId");
    this.mobileNoCountryCode = this.originalMobileNoCountryCode;
    this.idToken = window.$cookies.get("irukkaAuthToken");
    this.irukkaRefreshToken = window.$cookies.get("irukkaRefreshToken");
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    changeStatus(idToken) {
      this.idToken = idToken;
      this.showSignUp = false;
    },
    async fetchIntegrationStatus() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 241,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            this.activeOrNot =
              response.data.jobBoardIntegrationStatus.getStatus[0].Integration_Status;
            this.irukkaStatus =
              response.data.jobBoardIntegrationStatus.getStatus[0].Integration_Status;
            this.integrationId =
              response.data.jobBoardIntegrationStatus.getStatus[0].Integration_Id;
            this.integrationType =
              response.data.jobBoardIntegrationStatus.getStatus[0].Integration_Type;

            // if irukka integration is done then directly show the sign in form.
            this.showSignUp = true;
          } else {
            this.activeOrNot = "Inactive";
          }
        })
        .catch((err) => {
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case SET0101:
            snackbarData.message =
              "Sorry, an error occurred while retrieving the Integration status . Please contact the platform administrator.";
            break;
          case SET0001:
            snackbarData.message =
              "Oops! Something went wrong while retrieving the Integration status . Please contact the platform administrator.";
            break;
          default:
            snackbarData.message =
              "Something went wrong while retrieving the Integration status details. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while fetching Integration status details. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    redirectToPage() {
      this.$router.push("/recruitment/job-posts");
    },

    addUpdateIntegrationStatus(idValue) {
      let vm = this;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_IRUKKA_STATUS,
          variables: {
            Integration_Id: this.integrationId,
            Integration_Type: this.integrationType,
            Integration_Status: idValue[1] ? "Active" : "Inactive",
          },
          client: "apolloClientAH",
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            message: "Integration status updated successfully.",
            type: "success",
          };
          this.showAlert(snackbarData);
        })
        .catch((err) => {
          vm.handleAddUpdateIntegrationStatusError(err);
        });
    },
    handleAddUpdateIntegrationStatusError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case SET0002:
            snackbarData.message =
              "Oops! Something went wrong while adding/updating the Integration status . Please contact the platform administrator.";
            break;
          case SET0102:
            snackbarData.message =
              "Something went wrong while adding/updating the Integration status . Please try after some time.";
            break;
          case SET0103:
            snackbarData.message =
              "Sorry, an error occurred while adding/updating the Integration status . Please contact the platform administrator.";
            break;
          default:
            snackbarData.message =
              "Something went wrong while adding/updating the Integration status. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while adding/updating the Integration status. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
  },
};
</script>

<style scoped>
.custom-btn {
  /* Remove default hover effect */
  transition: none !important;
  background-color: inherit !important;
  color: inherit !important;
  box-shadow: none !important;
}
.custom-btn:hover {
  transition: none !important;
  background-color: inherit !important;
  color: inherit !important;
  box-shadow: none !important;
}
.same-as-vue-tel > input:focus {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
}
</style>
