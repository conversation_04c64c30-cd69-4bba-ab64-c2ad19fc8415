<template>
  <Handle type="target" :position="targetPosition"> </Handle>
  <Handle type="source" :position="sourcePosition" />
  <div
    class="exclusive_parent"
    v-if="data.type === 'parent'"
    style="
      background-color: #ffffff;
      height: 30px;
      width: 30px;
      display: flex;
      border-radius: 5px;
      justify-content: center;
      align-items: center;
    "
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 576 512"
      height="10"
      width="10"
      fill="#0047c8"
    >
      <path
        d="M208 80c0-26.5 21.5-48 48-48h64c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48h-8v40H464c30.9 0 56 25.1 56 56v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H464c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-4.4-3.6-8-8-8H312v40h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H256c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V280H112c-4.4 0-8 3.6-8 8v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-30.9 25.1-56 56-56H264V192h-8c-26.5 0-48-21.5-48-48V80z"
      />
    </svg>
    <div
      :class="'exclusive_menu' + (showParentMenu ? 'exclusive_menu1' : '')"
      style="
        position: absolute;
        bottom: -10px;
        z-index: 99999;
        left: 50%;
        transform: translate(-50%);
      "
    >
      <div class="" style="position: relative">
        <div
          :class="
            'glow-button' +
            (!showParentMenu ? ' glow-button-active' : ' dark-button')
          "
          v-click-outside="() => (showParentMenu = false)"
          @click="() => (showParentMenu = !showParentMenu)"
        >
          <v-icon class="white" size="8">fas fa-plus</v-icon>
        </div>

        <v-expand-x-transition>
          <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
            <MenuItems
              v-if="showParentMenu"
              @handleProcessNode="handleToParentNode"
            ></MenuItems>
          </div>
        </v-expand-x-transition>
      </div>
    </div>
    <div class="close_icon" @click="confirmationModel = true">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="#ffffff"
        height="7"
        width="7"
        viewBox="0 0 384 512"
      >
        <path
          d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
        />
      </svg>
    </div>
  </div>
  <div class="" style="padding: 2.4px"></div>
  <div :class="data.addNew ? 'parallel_node' : ''" v-if="data.type === 'child'">
    <div
      style="
        position: absolute;
        bottom: -10px;
        z-index: 99999;
        left: 50%;
        transform: translate(-50%);
      "
      v-if="data.addNew"
    >
      <div class="" style="position: relative">
        <div
          :class="
            'glow-button' + (!showMenu ? ' glow-button-active' : ' dark-button')
          "
          v-click-outside="() => (showMenu = false)"
          @click="() => (showMenu = !showMenu)"
        >
          <v-icon class="white" size="8">fas fa-plus</v-icon>
        </div>
        <v-expand-x-transition>
          <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
            <MenuItems
              v-if="showMenu"
              @handleProcessNode="handleToStartNode"
            ></MenuItems>
          </div>
        </v-expand-x-transition>
      </div>
    </div>
    <div v-else>
      <div
        style="
          background-color: #ffffff;
          height: 20px;
          width: 20px;
          display: flex;
          border-radius: 5px;
          justify-content: center;
          align-items: center;
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 576 512"
          height="10"
          width="10"
          fill="#0047c8"
        >
          <path
            d="M208 80c0-26.5 21.5-48 48-48h64c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48h-8v40H464c30.9 0 56 25.1 56 56v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H464c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-4.4-3.6-8-8-8H312v40h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H256c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V280H112c-4.4 0-8 3.6-8 8v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-30.9 25.1-56 56-56H264V192h-8c-26.5 0-48-21.5-48-48V80z"
          />
        </svg>
      </div>
    </div>
  </div>
  <AppWarningModal
    v-if="confirmationModel"
    :open-modal="confirmationModel"
    iconName="fas fa-trash"
    confirmation-heading="Are you sure to delete?"
    @close-warning-modal="confirmationModel = false"
    @accept-modal="deleteNode()"
  >
  </AppWarningModal>
</template>
<script>
import { Position, Handle } from "@vue-flow/core";
import MenuItems from "../components/menus/MainMenu.vue";
export default {
  name: "AddParallelNode",
  emits: ["handleToStart", "handleToParent", "deleteNode"],
  props: {
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    sourcePosition: {
      type: String,
      required: true,
    },
    targetPosition: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      leftPosition: Position.Top,
      rightPosition: Position.Bottom,
      showMenu: false,
      addNewNode: false,
      showParentMenu: false,
      confirmationModel: false,
    };
  },
  methods: {
    handleToStartNode(type) {
      this.$emit("handleToStart", type, this.data, true, 0);
    },
    handleToParentNode(type) {
      this.$emit("handleToParent", type, this.data);
    },
    deleteNode() {
      this.confirmationModel = false;
      this.$emit("deleteNode", this.data);
    },
  },

  components: {
    MenuItems,
    Handle,
  },
};
</script>
<style>
.exclusive_node {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 5px;
  min-width: 120px;
  justify-content: flex-start;
}

.exclusive_node:hover .close_icons {
  visibility: visible;
}
.parallel_node {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 5px 10px;
}

.parallel_node::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  /* background: linear-gradient(to right, #7f00b1, #e295fa); */
  height: 4px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.parallel_text {
  font-size: 8px;
  font-weight: 300;
  font-family: "Poppins";
  text-transform: capitalize;
  margin-left: 0.2rem;
}

.exclusive_parent:hover .close_icon {
  visibility: visible;
}

.exclusive_parent .close_icon {
  right: -5px !important;
  top: -4px !important;
}
</style>
