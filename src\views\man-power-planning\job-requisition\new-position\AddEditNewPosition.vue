<template>
  <v-overlay
    :model-value="showNewPositionForm"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card"
        :style="windowWidth >= 1264 ? '' : 'width: 100vw'"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div
            class="text-h6 text-medium ps-2"
            style="max-width: 90%; text-wrap: auto"
          >
            {{
              selectedPositionData && selectedPositionData.Position_Request_Id
                ? "Edit"
                : "Add"
            }}
            New Position & Additional Headcount
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <v-form ref="newPositionRefForm">
            <v-row class="py-6 px-3">
              <v-col
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 d-flex"
                v-if="!enableCustomPosition"
              >
                <CustomSelect
                  label="Position Title"
                  :item-selected="selectedPositionTitle"
                  v-model="selectedPositionTitle"
                  item-title="Pos_full_Name"
                  item-value="Pos_Name"
                  :items="positionList"
                  :is-loading="positionListLoading"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :clearable="true"
                  :disabled="
                    !!(
                      selectedPositionData &&
                      selectedPositionData.Original_Position_Id
                    ) || positionListLoading
                  "
                  :rules="[required('Position Title', selectedPositionTitle)]"
                  @update:model-value="onChangePosition()"
                ></CustomSelect>
                <v-btn
                  rounded="lg"
                  class="ml-2 primary px-2 mt-3"
                  variant="elevated"
                  :disabled="
                    !!(
                      selectedPositionData &&
                      selectedPositionData.Original_Position_Id
                    )
                  "
                  @click="addNewPosition()"
                >
                  Add New
                </v-btn>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 d-flex" v-else>
                <v-text-field
                  v-model="tempSelectedPositionTitle"
                  :rules="[
                    required('Position Title', tempSelectedPositionTitle),
                    validateWithRulesAndReturnMessages(
                      tempSelectedPositionTitle,
                      'positionTitle',
                      'Position Title'
                    ),
                  ]"
                  ref="PositionTitle"
                  variant="solo"
                  label="Position Title"
                  @update:model-value="onChange()"
                  :disabled="
                    !!(
                      selectedPositionData &&
                      selectedPositionData.Position_Request_Id
                    )
                  "
                  ><template v-slot:label>
                    Position Title
                    <span style="color: red">*</span>
                  </template></v-text-field
                >
                <v-btn
                  rounded="lg"
                  class="ml-2 px-1 primary mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="addNewPosition()"
                  :disabled="
                    !!(
                      selectedPositionData &&
                      selectedPositionData.Position_Request_Id
                    )
                  "
                >
                  <v-icon>fas fa-times</v-icon>
                </v-btn>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-if="!enableCustomPosition"
                  v-model="selectedGroup.fullName"
                  variant="solo"
                  label="Group"
                  readonly
                  @update:model-value="onChange()"
                >
                </v-text-field>
                <CustomSelect
                  v-else
                  v-model="selectedCustomGroup"
                  :items="groupList"
                  label="Group"
                  item-title="Pos_full_Name"
                  :isAutoComplete="true"
                  :clearable="true"
                  :itemSelected="selectedCustomGroup"
                  item-value="Originalpos_Id"
                  :is-loading="customPositionLoading"
                  @selected-item="isFormDirty = true"
                  @update:model-value="updateGroup()"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-if="!enableCustomPosition"
                  v-model="selectedDivision.fullName"
                  variant="solo"
                  label="Division"
                  @update:model-value="onChange()"
                  readonly
                >
                </v-text-field>
                <CustomSelect
                  v-else
                  v-model="selectedCustomDivision"
                  :items="customDivisionList"
                  label="Division"
                  item-title="Pos_full_Name"
                  item-value="Pos_Code"
                  :isAutoComplete="true"
                  :clearable="true"
                  :is-loading="customPositionLoading"
                  :itemSelected="selectedCustomDivision"
                  @selected-item="isFormDirty = true"
                  :isLoading="listLoading"
                  :disabled="!selectedCustomGroup"
                  @update:model-value="updateDivision()"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-if="!enableCustomPosition"
                  v-model="selectedDepartment.fullName"
                  label="Department"
                  variant="solo"
                  @update:model-value="onChange()"
                  readonly
                >
                </v-text-field>
                <CustomSelect
                  v-else
                  v-model="selectedCustomDepartment"
                  :items="customDepartmentList"
                  label="Department"
                  item-title="Pos_full_Name"
                  item-value="Pos_Code"
                  :isAutoComplete="true"
                  :clearable="true"
                  :is-loading="customPositionLoading"
                  :itemSelected="selectedCustomDepartment"
                  @selected-item="isFormDirty = true"
                  :disabled="!selectedCustomDivision"
                  :isLoading="listLoading"
                  @update:model-value="updateDepartment()"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="selectedSection.fullName"
                  v-if="!enableCustomPosition"
                  variant="solo"
                  label="Section"
                  @update:model-value="onChange()"
                  readonly
                >
                </v-text-field>
                <CustomSelect
                  v-else
                  v-model="selectedCustomSection"
                  :items="customSectionList"
                  label="Section"
                  item-title="Pos_full_Name"
                  item-value="Pos_Code"
                  :isAutoComplete="true"
                  :clearable="true"
                  :is-loading="customPositionLoading"
                  :itemSelected="selectedCustomSection"
                  @selected-item="isFormDirty = true"
                  :disabled="!selectedCustomDepartment"
                  :isLoading="listLoading"
                  @update:model-value="updateSection()"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-if="!enableCustomPosition"
                  v-model="selectedCostCenter"
                  variant="solo"
                  label="Cost Center"
                  @update:model-value="onChange()"
                  readonly
                >
                </v-text-field>
                <CustomSelect
                  v-else
                  v-model="selectedCustomCostCenter"
                  :items="customCostCenterList"
                  label="Cost Center"
                  :isAutoComplete="true"
                  :clearable="true"
                  :is-loading="customPositionLoading"
                  :itemSelected="selectedCustomCostCenter"
                  @selected-item="isFormDirty = true"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  v-model="selectedReasonVacancy"
                  :items="reasonVacancyList"
                  label="Reason for Request"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :clearable="true"
                  :rules="[
                    required('Reason for Request', selectedReasonVacancy),
                  ]"
                  :itemSelected="selectedReasonVacancy"
                  @selected-item="isFormDirty = true"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  v-model="selectedPositionLevel"
                  :items="positionLevelList"
                  label="Position Level"
                  item-title="Position_Level"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :clearable="true"
                  :rules="[required('Position Level', selectedPositionLevel)]"
                  :itemSelected="selectedPositionLevel"
                  item-value="Position_Level_Id"
                  :is-loading="positionLevelLoading"
                  @selected-item="isFormDirty = true"
                />
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  label="Employee Type"
                  :item-selected="selectedEmployeeType"
                  v-model="selectedEmployeeType"
                  item-title="Employee_Type"
                  item-value="EmpType_Id"
                  :items="employeeTypeList"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :clearable="true"
                  ref="employeeType"
                  :rules="[required('Employee Type', selectedEmployeeType)]"
                  @selected-item="isFormDirty = true"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="numberOfPositions"
                  ref="noOfPositions"
                  :rules="[
                    numericRequiredValidation(
                      'No of Positions',
                      numberOfPositions
                    ),
                    numericValidation('No of Positions', numberOfPositions),
                    minMaxNumberValidation(
                      'No of Positions',
                      numberOfPositions,
                      1,
                      500
                    ),
                  ]"
                  @update:model-value="onChange()"
                  :isRequired="true"
                  variant="solo"
                  max="500"
                  maxLength="3"
                  ><template v-slot:label>
                    No of Positions
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="
                  customGroupCoverage &&
                  customGroupCoverage.toLowerCase() === 'custom group'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 my-2"
              >
                <CustomSelect
                  ref="customGroup"
                  label="Recruiters Group for Notification"
                  :items="dropdownCustomGroup"
                  item-title="Custom_Group_Name"
                  item-value="Custom_Group_Id"
                  is-required="true"
                  :isAutoComplete="true"
                  :rules="[
                    required(
                      'Recruiters Group for Notification',
                      chosenCustomGroup
                    ),
                  ]"
                  clearable
                  :isLoading="isCustomGroupListLoading"
                  :itemSelected="chosenCustomGroup"
                  @selected-item="chosenCustomGroup = $event"
                  @update:model-value="isFormDirty = true"
                >
                </CustomSelect>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <div class="card-actions-div">
          <v-card-actions class="d-flex align-end">
            <v-sheet class="align-center text-center" style="width: 100%">
              <v-row justify="center">
                <v-col cols="12" class="d-flex justify-space-between pr-6">
                  <div class="d-flex align-center">
                    <v-btn
                      v-if="
                        (selectedPositionData.Pos_Code === null ||
                          selectedPositionData.Pos_Code === undefined) &&
                        selectedPositionData?.Position_Title
                      "
                      rounded="lg"
                      class="mr-6 primary"
                      @click="gotoNextDuties()"
                      :disabled="isFormDirty"
                      variant="outlined"
                    >
                      Skip
                    </v-btn>
                  </div>
                  <div class="d-flex align-center">
                    <v-btn
                      rounded="lg"
                      class="mr-6 primary"
                      @click="onCloseOverlay()"
                      variant="outlined"
                    >
                      Cancel
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      class="mr-1 primary"
                      @click="
                        enableCustomPosition
                          ? submitCustomPosition(true)
                          : submitNewPosition()
                      "
                      variant="elevated"
                      :disabled="!isFormDirty"
                    >
                      {{
                        enableCustomPosition
                          ? this.selectedPositionData.Position_Title
                            ? "Update"
                            : "Next"
                          : "Submit"
                      }}
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
            </v-sheet>
          </v-card-actions>
        </div>
      </v-card>
      <AppLoading v-if="listLoading"></AppLoading>
      <AppWarningModal
        icon-name="fas fa-exchange-alt"
        icon-color="amber"
        v-if="openConfirmModal"
        :open-modal="openConfirmModal"
        confirmation-heading="Are you sure you want to change the position title"
        @close-warning-modal="onCancelPositionTitle()"
        @accept-modal="onChangePositionTitle()"
      ></AppWarningModal>
    </template>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit New Position & Additional Headcount request form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onClosePopup()"
  >
  </AppWarningModal>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  HIRING_POSITION_LIST,
  LIST_OF_POSITION_LIST,
  RECRUITMENT_POSITION_LIST,
} from "@/graphql/mpp/manPowerPlanningQueries";
import { GET_CUSTOM_GROUP_COVERAGE } from "@/graphql/settings/irukka-integration/jobPostFormQueries.js";
import {
  ORG_STRUCTURE_BASED_ON_GROUP,
  POSITION_LEVEL_LIST,
} from "@/graphql/mpp/newPositionQueries";
import validationRules from "@/mixins/validationRules";
export default {
  name: "AddEditNewPositionForm",
  emits: [
    "on-close-add-form",
    "goto-next-duties",
    "open-workflow-form",
    "open-job-assessment-form",
    "edit-form-submit",
  ],
  mixins: [validationRules],
  props: {
    showAddForm: {
      type: Boolean,
      required: true,
    },
    selectedNewPosition: {
      type: Object,
      required: true,
    },
    selectedPositionData: {
      type: Object,
      required: true,
    },
    selectedPositionParentId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      showNewPositionForm: false,
      listLoading: false,
      positionListLoading: false,

      //if custom title added we are enable this fields
      customPositionLoading: false,
      groupList: [],
      customDivisionList: [],
      customDepartmentList: [],
      customSectionList: [],
      customCostCenterList: [],
      selectedCustomGroup: null,
      selectedCustomDivision: null,
      selectedCustomDepartment: null,
      selectedCustomSection: null,
      selectedCustomCostCenter: null,

      //normal fields
      positionList: [],
      employeeTypeList: [],
      positionLevelList: [],
      positionLevelLoading: false,
      enableCustomPosition: false,
      tempSelectedPositionTitle: null,
      selectedPositionTitle: null,
      selectedGroup: { code: "", name: "" },
      selectedDivision: { code: "", name: "" },
      selectedDepartment: { code: "", name: "" },
      selectedSection: { code: "", name: "" },
      reasonVacancyList: ["New Branch", "New TO", "Others", "Project"],
      selectedReasonVacancy: "",
      selectedPositionLevel: null,
      selectedCostCenter: null,
      selectedEmployeeType: null,
      numberOfPositions: 0,
      openConfirmationPopup: false,
      isFormDirty: false,
      openConfirmModal: false,
      newPositionLimitToCallAPI: 10000,
      totalApiCount: 0,
      apiCallCount: 0,
      hiringPositionTotalApiCount: 0,
      hiringPositionApiCallCount: 0,
      tempOriginalStructureId: null,
      customGroupCoverage: null,
      dropdownCustomGroup: [],
      chosenCustomGroup: null,
      isCustomGroupListLoading: false,
    };
  },
  components: {
    CustomSelect,
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("290");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  watch: {
    showAddForm: {
      handler(val) {
        this.showNewPositionForm = val;
        if (val) {
          this.isFormDirty = false;
        }
      },
      immediate: true,
    },
    selectedPositionData: {
      handler(val) {
        if (val && Object.keys(val).length) {
          this.selectedPositionLevel = val?.Position_Level_Id || null;
          this.selectedReasonVacancy = val?.Reason_For_Replacement || null;
          if (val.Pos_Code === null || val.Pos_Code === undefined) {
            this.enableCustomPosition = true;
            this.tempSelectedPositionTitle = val.Position_Title;
            this.selectedCustomDepartment = val.Department_Code;
            this.selectedCustomSection = val.Section_Code;
            this.selectedCustomDivision = val.Division_Code;
            this.selectedCustomCostCenter = val.Cost_Center;
          } else {
            this.enableCustomPosition = false;
            this.tempSelectedPositionTitle = null;
            if (val.Original_Position_Id) {
              const positionData = this.positionList.find(
                (item) => val?.Original_Position_Id == item.Originalpos_Id
              );
              this.selectedPositionTitle = positionData?.Pos_Name || null;
            } else {
              this.selectedPositionTitle = null;
            }
            this.retrieveNewPosition();
          }
          this.selectedEmployeeType = val.Employee_Type
            ? parseInt(val.Employee_Type)
            : null;
          this.chosenCustomGroup = val.Custom_Group_Id
            ? parseInt(val.Custom_Group_Id)
            : null;
          this.numberOfPositions = val.No_Of_Position || 0;
        } else {
          this.enableCustomPosition = false;
          this.selectedPositionTitle = null;
          this.numberOfPositions = 0;
          this.selectedEmployeeType = null;
          this.selectedPositionLevel = null;
          this.tempSelectedPositionTitle = null;
          this.resetSelectedOptions();
          this.selectedCustomGroup = null;
          this.selectedReasonVacancy = null;
          this.selectedCustomDivision = null;
          this.selectedCustomDepartment = null;
          this.selectedCustomSection = null;
          this.selectedCustomCostCenter = null;
          this.chosenCustomGroup = null;
        }
      },
      deep: true,
    },
    selectedPositionParentId: {
      handler() {
        this.retrieveTotalPositionTitle();
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.formAccess && this.formAccess.view) {
      this.retrieveEmployeeType();
      this.retrieveGroupList();
      this.retrievePositionLevel();
      this.getCustomGroupCoverage();
      this.listCustomGroup();
    }
  },
  methods: {
    onCloseOverlay() {
      this.openConfirmationPopup = true;
    },
    gotoNextDuties() {
      this.submitCustomPosition(false);
    },
    onClosePopup() {
      this.openConfirmationPopup = false;
      this.$emit("on-close-add-form");
    },
    prefillCustomGroup(val) {
      if (val && Object.keys(val).length) {
        if (val.Pos_Code === null || val.Pos_Code === undefined) {
          this.selectedCustomGroup = val.Group_Code;
          const originalId = this.groupList.find(
            (item) => item.Pos_Code === val.Group_Code
          );
          this.selectedCustomGroup = originalId?.Originalpos_Id || 0;
          this.retrieveCountGroupPosition("group");
        }
      }
    },
    updateGroup() {
      this.resetCustomPositionList();
      this.retrieveCountGroupPosition("group");
    },
    updateDivision() {
      this.selectedCustomSection = null;
      this.selectedCustomDepartment = null;
      this.retrieveCountGroupPosition("division");
    },
    updateDepartment() {
      this.selectedCustomSection = null;
      this.retrieveCountGroupPosition("department");
    },
    updateSection() {
      this.retrieveCountGroupPosition();
    },
    onChange() {
      this.isFormDirty = true;
    },
    onChangePosition() {
      if (this.selectedPositionTitle) {
        this.isFormDirty = false;
        const positionData = this.positionList.find(
          (item) => this.selectedPositionTitle == item.Pos_Name
        );
        if (positionData && positionData.Originalpos_Id) {
          this.tempOriginalStructureId = positionData.Organization_Structure_Id;
        } else {
          this.tempOriginalStructureId = null;
        }
        if (positionData && positionData.Originalpos_Id) {
          this.retrieveNewPosition();
        } else {
          this.enableCustomPosition = true;
          this.tempSelectedPositionTitle = positionData?.Pos_Name || null;
          this.selectedPositionTitle = null;
        }
      } else {
        this.resetSelectedOptions();
        this.isFormDirty = false;
      }
      this.onChange();
    },
    addNewPosition() {
      if (this.isFormDirty) {
        this.openConfirmModal = true;
      } else {
        this.enableCustomPosition = !this.enableCustomPosition;
        this.selectedPositionLevel = null;
      }
    },
    onChangePositionTitle() {
      this.selectedPositionLevel = null;
      if (this.enableCustomPosition) {
        this.tempSelectedPositionTitle = null;
        this.resetCustomPositionList();
        this.selectedCustomGroup = null;
        this.selectedCostCenter = null;
        this.selectedEmployeeType = null;
        this.numberOfPositions = 0;
      } else {
        this.selectedPositionTitle = null;
        this.resetSelectedOptions();
      }
      this.enableCustomPosition = !this.enableCustomPosition;
      this.openConfirmModal = false;
      this.isFormDirty = false;
    },
    onCancelPositionTitle() {
      this.openConfirmModal = false;
    },
    retrievePositionLevel() {
      this.positionLevelLoading = true;
      this.$apollo
        .query({
          query: POSITION_LEVEL_LIST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.retrievePositionLevel &&
            res.data.retrievePositionLevel.positionLevelList
          ) {
            this.positionLevelList =
              res.data.retrievePositionLevel.positionLevelList;
          } else {
            this.positionLevelList = [];
          }
        })
        .catch((err) => {
          this.handleRetrieveNewPositionErrors(err);
        })
        .finally(() => {
          this.positionLevelLoading = false;
        });
    },
    retrieveTotalPositionTitle() {
      this.positionListLoading = true;
      this.positionList = [];
      this.$apollo
        .query({
          query: HIRING_POSITION_LIST,
          client: "apolloClientAG",
          variables: {
            formId: 290,
            postionParentId: this.selectedPositionParentId || "",
            offset: 0,
            limit: this.newPositionLimitToCallAPI,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.positionListLoading = false;
          if (
            res &&
            res.data &&
            res.data.listForecastPosition &&
            res.data.listForecastPosition.positionList
          ) {
            this.positionList = res.data.listForecastPosition.positionList;
            let { totalCountResult } = res.data.listForecastPosition;
            if (totalCountResult > 0) {
              totalCountResult = parseInt(totalCountResult);
              this.hiringPositionApiCallCount = 1;
              this.hiringPositionTotalApiCount = Math.ceil(
                totalCountResult / this.newPositionLimitToCallAPI
              );
              for (let i = 1; i < this.hiringPositionTotalApiCount; i++) {
                this.retrievePositionTitle(i);
              }
            }
          }
        })
        .catch((err) => {
          this.positionListLoading = false;
          this.positionList = [];
          this.handleRetrieveNewPositionErrors(err);
        });
    },
    retrievePositionTitle(index = 1) {
      let apiOffset = parseInt(index) * this.newPositionLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.positionListLoading = true;
      this.$apollo
        .query({
          query: HIRING_POSITION_LIST,
          client: "apolloClientAG",
          variables: {
            formId: 290,
            postionParentId: this.selectedPositionParentId || "",
            offset: apiOffset,
            limit: this.newPositionLimitToCallAPI,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.positionListLoading = false;
          if (
            res &&
            res.data &&
            res.data.listForecastPosition &&
            res.data.listForecastPosition.positionList
          ) {
            this.positionList = [
              ...this.positionList,
              ...res.data.listForecastPosition.positionList,
            ];
            this.hiringPositionApiCallCount =
              this.hiringPositionApiCallCount + 1;
            if (
              this.hiringPositionTotalApiCount ===
              this.hiringPositionApiCallCount
            ) {
              this.positionListLoading = false;
            }
          } else {
            this.positionListLoading = false;
          }
        })
        .catch((err) => {
          this.positionListLoading = false;
          this.positionList = [];
          this.handleRetrieveNewPositionErrors(err);
        });
    },
    retrieveGroupList() {
      this.$apollo
        .query({
          query: LIST_OF_POSITION_LIST,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            Form_Id: 290,
            conditions: [
              {
                key: "Org_Level",
                operator: "=",
                value: "GRP",
              },
            ],
          },
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.jobTitleList &&
            res.data.jobTitleList.jobTitleResult
          ) {
            let groups = res.data.jobTitleList.jobTitleResult;
            this.groupList = [
              {
                Pos_Name: "No Group",
                Pos_full_Name: "No Group",
                Originalpos_Id: "nogroup",
              },
            ].concat(groups);
            this.prefillCustomGroup(this.selectedPositionData);
          }
        });
    },
    async retrieveNewPosition() {
      this.listLoading = true;
      const positionData = this.positionList.find(
        (item) => this.selectedPositionTitle === item.Pos_Name
      );
      try {
        const {
          data: {
            listDetailsBasedOnOrgPosId: { positionDetails },
          },
        } = await this.$apollo.query({
          query: RECRUITMENT_POSITION_LIST,
          variables: {
            originalPosId: positionData?.Originalpos_Id?.toString() || "",
            organizationStructureId:
              parseInt(positionData?.Organization_Structure_Id) || 0,
            formId: 290,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        });

        if (positionDetails) {
          this.selectedWorkflow = {
            eventId: positionDetails.eventId,
            workflowId: positionDetails.workflowId,
          };
          this.selectedGroup = {
            code: positionDetails.groupCode,
            name: positionDetails.groupName,
            fullName: positionDetails.groupFullName,
          };
          this.selectedDivision = {
            code: positionDetails.divisionCode,
            name: positionDetails.divisionName,
            fullName: positionDetails.divisionFullName,
          };
          this.selectedDepartment = {
            code: positionDetails.deptCode,
            name: positionDetails.deptName,
            fullName: positionDetails.deptFullName,
          };
          this.selectedCostCenter = positionDetails.costCode;
          this.selectedSection = {
            code: positionDetails.sectionCode,
            name: positionDetails.sectionName,
            fullName: positionDetails.sectionFullName,
          };
          this.selectedPositionLevel =
            this.selectedPositionData &&
            this.selectedPositionData.Position_Level_Id
              ? this.selectedPositionData.Position_Level_Id
              : positionDetails?.positionLevelId;
        } else {
          this.resetSelectedOptions();
        }
      } catch (error) {
        this.resetSelectedOptions();
        this.handleRetrieveNewPositionErrors(error);
      } finally {
        this.listLoading = false;
      }
    },
    handleRetrieveNewPositionErrors(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "new position request details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    async retrieveEmployeeType() {
      this.dropdownListFetching = true;
      try {
        const {
          data: {
            getDropDownBoxDetails: { employeeType },
          },
        } = await this.$store.dispatch("getDefaultDropdownList", {
          formId: 15,
        });
        if (employeeType) {
          this.employeeTypeList = employeeType;
        }
      } catch (error) {
        this.employeeTypeList = [];
      } finally {
        this.dropdownListFetching = false;
      }
    },

    resetSelectedOptions() {
      this.selectedPositionTitle = null;
      this.selectedGroup = { code: "", name: "" };
      this.selectedDivision = { code: "", name: "" };
      this.selectedDepartment = { code: "", name: "" };
      this.selectedSection = { code: "", name: "" };
      this.selectedCostCenter = null;
      this.selectedEmployeeType = null;
      this.numberOfPositions = 0;
    },
    resetCustomPositionList() {
      this.selectedCustomDivision = null;
      this.selectedCustomDepartment = null;
      this.selectedCustomSection = null;
      this.selectedCustomCostCenter = null;
    },
    retrieveCountGroupPosition(type = "") {
      let groupId = "0";
      if (this.selectedCustomSection) {
        groupId =
          this.customSectionList.find(
            (item) => item.Pos_Code == this.selectedCustomSection
          )?.Originalpos_Id || "";
      } else if (
        this.selectedCustomDepartment &&
        this.selectedCustomDepartment.toLowerCase() !== "nodepartment"
      ) {
        groupId =
          this.customDepartmentList.find(
            (item) => item.Pos_Code == this.selectedCustomDepartment
          )?.Originalpos_Id || "";
      } else if (
        this.selectedCustomDivision &&
        this.selectedCustomDivision.toLowerCase() !== "nodivision"
      ) {
        groupId =
          this.customDivisionList.find(
            (item) => item.Pos_Code == this.selectedCustomDivision
          )?.Originalpos_Id || "";
      } else if (!this.selectedCustomGroup) {
        groupId = "";
      } else if (
        this.selectedCustomGroup &&
        String(this.selectedCustomGroup || "")?.toLowerCase() !== "nogroup"
      ) {
        groupId =
          this.groupList.find(
            (item) => item.Originalpos_Id == this.selectedCustomGroup
          )?.Originalpos_Id || "";
      }
      this.listLoading = true;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(groupId),
            limit: this.newPositionLimitToCallAPI,
            offset: 0,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listDetailsBasedOnGroupCode) {
            if (res.data.listDetailsBasedOnGroupCode.positionDetails) {
              const tempData =
                res.data.listDetailsBasedOnGroupCode.positionDetails;
              if (type === "group")
                this.customDivisionList = tempData.divisionList || [];
              if (type === "division")
                this.customDepartmentList = tempData.deptList || [];
              if (type === "department")
                this.customSectionList = tempData.sectionList || [];
              this.customCostCenterList = tempData.costCodeList || [];
              if (
                !this.customDivisionList.some(
                  (item) => item.Pos_Code === "nodivision"
                )
              )
                this.customDivisionList.unshift({
                  Pos_Name: "No Division",
                  Pos_Code: "nodivision",
                  Pos_full_Name: "No Division",
                  Originalpos_Id: "",
                });

              if (
                !this.customDepartmentList.some(
                  (item) => item.Pos_Code === "nodepartment"
                )
              )
                this.customDepartmentList.unshift({
                  Pos_Name: "No Department",
                  Pos_Code: "nodepartment",
                  Pos_full_Name: "No Department",
                  Originalpos_Id: "",
                });
              if (
                !this.customSectionList.some(
                  (item) => item.Pos_Code === "nosection"
                )
              )
                this.customSectionList.unshift({
                  Pos_Name: "No Section",
                  Pos_Code: "nosection",
                  Pos_full_Name: "No Section",
                  Originalpos_Id: "",
                });
            } else {
              this.resetTempList();
            }
            let { totalRecords } = res.data.listDetailsBasedOnGroupCode;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalRecords / this.newPositionLimitToCallAPI
              );
              for (let i = 1; i < this.totalApiCount; i++) {
                this.updateGroupPosition(i, String(groupId), type);
              }
            }
          }
          this.listLoading = false;
        })
        .catch((err) => {
          this.handleRetrieveNewPositionErrors(err);
          this.resetTempList();
          this.listLoading = false;
        });
    },
    updateGroupPosition(index = 1, groupId = "", type = "") {
      this.listLoading = true;
      let apiOffset = parseInt(index) * this.newPositionLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.isFormDirty = true;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(groupId),
            limit: this.newPositionLimitToCallAPI,
            offset: apiOffset,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listDetailsBasedOnGroupCode &&
            res.data.listDetailsBasedOnGroupCode.positionDetails
          ) {
            const tempData =
              res.data.listDetailsBasedOnGroupCode.positionDetails;
            if (
              tempData.divisionList &&
              tempData.divisionList.length > 0 &&
              type === "group"
            ) {
              this.customDivisionList = [
                ...this.customDivisionList,
                ...tempData.divisionList,
              ];
            }

            if (
              tempData.deptList &&
              tempData.deptList.length > 0 &&
              type === "division"
            ) {
              this.customDepartmentList = [
                ...this.customDepartmentList,
                ...tempData.deptList,
              ];
            }
            if (
              tempData.sectionList &&
              tempData.sectionList.length > 0 &&
              type === "department"
            ) {
              this.customSectionList = [
                ...this.customSectionList,
                ...tempData.sectionList,
              ];
            }
            if (tempData.costCodeList && tempData.costCodeList.length > 0) {
              this.customCostCenterList = [
                ...this.customCostCenterList,
                tempData.costCodeList,
              ];
            }
            this.apiCallCount = this.apiCallCount + 1;
            if (this.totalApiCount === this.apiCallCount) {
              this.listLoading = false;
            }
          } else {
            this.resetTempList();
            this.listLoading = false;
          }
        })
        .catch((err) => {
          this.handleRetrieveNewPositionErrors(err);
          this.resetTempList();
          this.listLoading = false;
        });
    },
    resetTempList() {
      this.customDivisionList = [];
      this.customDepartmentList = [];
      this.customSectionList = [];
      this.customCostCenterList = [];
    },
    async submitNewPosition() {
      const { valid } = await this.$refs.newPositionRefForm.validate();
      if (valid && this.positionList && this.positionList.length > 0) {
        const positionData = this.positionList.find(
          (item) => this.selectedPositionTitle === item.Pos_Name
        );
        const empType = this.employeeTypeList.find(
          (item) => item.EmpType_Id === this.selectedEmployeeType
        );
        let requestPayload = {
          positionRequestId:
            this.selectedPositionData &&
            this.selectedPositionData.Position_Request_Id
              ? this.selectedPositionData.Position_Request_Id
              : 0,
          status:
            (this.selectedPositionData && this.selectedPositionData.Status) ||
            "Waiting for approval",
          noOfPosition: parseInt(this.numberOfPositions),
          reasonForReplacement: "",
          customGroupId: this.chosenCustomGroup
            ? parseInt(this.chosenCustomGroup)
            : null,
        };
        if (empType) {
          requestPayload.employeeTypeName = empType.Employee_Type;
          requestPayload.employeeType = empType.EmpType_Id.toString();
        }
        if (positionData) {
          requestPayload.originalPositionId =
            positionData.Originalpos_Id.toString();
          requestPayload.positionCode = positionData.Pos_Code;
          requestPayload.positionTitle = positionData.Pos_Name;
          requestPayload.organizationStructureId =
            positionData.Organization_Structure_Id
              ? parseInt(positionData.Organization_Structure_Id)
              : 0;
        }
        if (this.selectedGroup && this.selectedGroup.name) {
          requestPayload.groupCode = this.selectedGroup.code;
          requestPayload.groupName = this.selectedGroup.name;
        }
        if (this.selectedDivision && this.selectedDivision.name) {
          requestPayload.divisionCode = this.selectedDivision.code;
          requestPayload.divisionName = this.selectedDivision.name;
        }
        if (this.selectedDepartment && this.selectedDepartment.name) {
          requestPayload.departmentCode = this.selectedDepartment.code;
          requestPayload.departmentName = this.selectedDepartment.name;
        }
        if (this.selectedSection && this.selectedSection.name) {
          requestPayload.sectionCode = this.selectedSection.code;
          requestPayload.sectionName = this.selectedSection.name;
        }
        if (this.selectedCostCenter) {
          requestPayload.costCenter = this.selectedCostCenter;
        }
        if (this.selectedPositionLevel) {
          requestPayload.PositionLevel = this.selectedPositionLevel;
        }
        if (this.selectedReasonVacancy && this.selectedReasonVacancy.length) {
          requestPayload.reasonForReplacement = this.selectedReasonVacancy;
        }
        this.isFormDirty = false;
        if (
          this.selectedPositionData &&
          this.selectedPositionData.Position_Request_Id
        ) {
          this.$emit("edit-form-submit", requestPayload);
        } else {
          this.$emit("open-workflow-form", requestPayload);
        }
      }
    },
    async submitCustomPosition(submitFlag) {
      const { valid } = await this.$refs.newPositionRefForm.validate();
      if (valid) {
        this.isFormDirty = false;
        const empType = this.employeeTypeList.find(
          (item) => item.EmpType_Id === this.selectedEmployeeType
        );
        let requestPayload = {
          positionRequestId:
            this.selectedPositionData &&
            this.selectedPositionData.Position_Request_Id
              ? this.selectedPositionData.Position_Request_Id
              : 0,
          status:
            (this.selectedPositionData && this.selectedPositionData.Status) ||
            "Draft",
          originalPositionId: "",
          positionCode: "",
          workflowId: null,
          eventId: "",
          positionTitle: this.tempSelectedPositionTitle,
          noOfPosition: parseInt(this.numberOfPositions),
          customGroupId: this.chosenCustomGroup
            ? parseInt(this.chosenCustomGroup)
            : null,
          divisionCode: "",
          divisionName: "",
          groupCode: "",
          groupName: "",
          departmentCode: "",
          departmentName: "",
          sectionCode: "",
          sectionName: "",
          costCenter: "",
          reasonForRequest: "",
          reasonForReplacement: "",
          groupId: "",
          divisionId: "",
          departmentId: "",
          sectionId: "",
          organizationStructureId: 0,
        };
        if (empType) {
          requestPayload.employeeTypeName = empType.Employee_Type;
          requestPayload.employeeType = empType.EmpType_Id.toString();
        }
        if (
          this.selectedCustomDivision &&
          this.selectedCustomDivision.toLowerCase() != "nodivision"
        ) {
          const resultData = this.customDivisionList.find(
            (item) => item.Pos_Code === this.selectedCustomDivision
          );
          requestPayload.divisionCode = resultData?.Pos_Code;
          requestPayload.divisionName = resultData?.Pos_Name;
          requestPayload.divisionId =
            resultData?.Originalpos_Id.toString() || "";
        }
        if (
          this.selectedCustomGroup &&
          this.selectedCustomGroup?.toString().toLowerCase() != "nogroup"
        ) {
          const resultData = this.groupList.find(
            (item) => item.Originalpos_Id === this.selectedCustomGroup
          );
          requestPayload.groupCode = resultData?.Pos_Code;
          requestPayload.groupName = resultData?.Pos_Name;
          requestPayload.groupId = this.selectedCustomGroup.toString() || "";
        }
        if (
          this.selectedCustomDepartment &&
          this.selectedCustomDepartment.toLowerCase() != "nodepartment"
        ) {
          const resultData = this.customDepartmentList.find(
            (item) => item.Pos_Code === this.selectedCustomDepartment
          );
          requestPayload.departmentCode = resultData?.Pos_Code;
          requestPayload.departmentName = resultData?.Pos_Name;
          requestPayload.departmentId =
            resultData?.Originalpos_Id.toString() || "";
        }
        if (this.selectedCustomSection) {
          const resultData = this.customSectionList.find(
            (item) => item.Pos_Code === this.selectedCustomSection
          );
          requestPayload.sectionCode = resultData?.Pos_Code;
          requestPayload.sectionName = resultData?.Pos_Name;
          requestPayload.sectionId =
            resultData?.Originalpos_Id.toString() || "";
        }
        if (this.selectedCustomCostCenter) {
          requestPayload.costCenter = this.selectedCustomCostCenter;
        }
        if (this.selectedPositionLevel) {
          requestPayload.PositionLevel = this.selectedPositionLevel;
        }
        if (this.selectedReasonVacancy && this.selectedReasonVacancy.length) {
          requestPayload.reasonForReplacement = this.selectedReasonVacancy;
        }
        const positionData = this.positionList.find(
          (item) => this.tempSelectedPositionTitle === item?.Pos_Name
        );
        if (positionData && positionData.Organization_Structure_Id) {
          requestPayload.organizationStructureId =
            positionData.Organization_Structure_Id;
        } else {
          requestPayload.organizationStructureId = 0;
        }
        if (submitFlag) {
          this.$emit("open-job-assessment-form", requestPayload);
        } else {
          this.$emit("goto-next-duties", requestPayload);
        }
      }
    },
    getCustomGroupCoverage() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_CUSTOM_GROUP_COVERAGE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.recruitmentSetting &&
            response.data.recruitmentSetting.settingResult &&
            response.data.recruitmentSetting.settingResult.length
          ) {
            this.customGroupCoverage =
              response.data.recruitmentSetting.settingResult[0].Coverage;
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleGetCustomGroupCoverageError(err);
        });
    },
    handleGetCustomGroupCoverageError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "custom group settings",
        isListError: false,
      });
    },
    listCustomGroup() {
      let vm = this;
      vm.isCustomGroupListLoading = true;
      this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "Job Posts",
        })
        .then((groupList) => {
          if (groupList && groupList.length) {
            this.dropdownCustomGroup = groupList;
          } else {
            this.dropdownCustomGroup = [];
          }
          this.isCustomGroupListLoading = false;
        })
        .catch(() => {
          this.isCustomGroupListLoading = false;
          this.dropdownCustomGroup = [];
        });
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100vh;
  width: 50vw;
  overflow-y: auto;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
