<template>
  <v-row v-if="labelList[375]?.Field_Visiblity?.toLowerCase() === 'yes'">
    <v-slide-group
      class="pa-4"
      selected-class="bg-primary"
      prev-icon="fas fa-chevron-circle-left"
      next-icon="fas fa-chevron-circle-right"
      show-arrows
    >
      <v-slide-group-item
        v-for="(langItem, index) in mergedLanguages"
        :key="index"
      >
        <v-card
          class="card-item d-flex pa-4 rounded-lg mr-4 mb-1"
          color="grey-lighten-5"
          :style="
            !isMobileView
              ? `width:450px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
              : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
          "
        >
          <v-row>
            <!-- Language Name -->
            <v-col
              v-if="labelList[375]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              class="pa-0 pl-4 d-flex"
            >
              <v-tooltip
                :text="
                  langItem.newLang?.Language_Name ||
                  langItem.oldLang?.Language_Name
                "
                location="bottom"
              >
                <template v-slot:activator="{ props }">
                  <div
                    class="text-subtitle-1 font-weight-bold d-flex align-center text-truncate"
                    v-bind="props"
                    :style="
                      isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                    "
                  >
                    <span
                      v-if="
                        langItem.oldLang &&
                        langItem.newLang &&
                        langItem.oldLang.Language_Name !==
                          langItem.newLang.Language_Name
                      "
                      class="text-decoration-line-through text-error mr-1"
                    >
                      {{ checkNullValue(langItem.oldLang?.Language_Name) }}
                    </span>
                    <span
                      v-if="langItem.newLang"
                      :class="
                        langItem.oldLang &&
                        langItem.oldLang.Language_Name !==
                          langItem.newLang.Language_Name
                          ? 'text-success'
                          : ''
                      "
                    >
                      {{ checkNullValue(langItem.newLang?.Language_Name) }}
                    </span>
                    <span
                      v-else-if="langItem.oldLang"
                      class="text-error text-decoration-line-through"
                    >
                      {{ checkNullValue(langItem.oldLang?.Language_Name) }}
                    </span>
                  </div>
                </template>
              </v-tooltip>
            </v-col>

            <!-- Lang Spoken -->
            <v-col
              v-if="labelList[369]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              sm="6"
              class="pa-0 pl-4"
            >
              <div
                class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
              >
                <b class="mr-2 text-grey">{{ labelList[369]?.Field_Alias }}</b>
                <div>
                  <span
                    v-if="
                      langItem.oldLang &&
                      langItem.newLang &&
                      langItem.oldLang.Lang_Spoken !==
                        langItem.newLang.Lang_Spoken
                    "
                    class="text-decoration-line-through text-error mr-1"
                  >
                    {{ langItem.oldLang.Lang_Spoken ? "Yes" : "No" }}
                  </span>
                  <span
                    v-if="langItem.newLang"
                    :class="
                      langItem.oldLang &&
                      langItem.oldLang.Lang_Spoken !==
                        langItem.newLang.Lang_Spoken
                        ? 'text-success'
                        : ''
                    "
                  >
                    {{ langItem.newLang.Lang_Spoken ? "Yes" : "No" }}
                  </span>
                  <span
                    v-else-if="langItem.oldLang"
                    class="text-error text-decoration-line-through"
                  >
                    {{ langItem.oldLang.Lang_Spoken ? "Yes" : "No" }}
                  </span>
                </div>
              </div>
            </v-col>

            <!-- Lang Read/Write -->
            <v-col
              v-if="labelList[370]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              sm="6"
              class="pa-0 pl-4"
            >
              <div
                class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
              >
                <b class="mr-2 text-grey">{{ labelList[370]?.Field_Alias }}</b>
                <div>
                  <span
                    v-if="
                      langItem.oldLang &&
                      langItem.newLang &&
                      langItem.oldLang.Lang_Read_Write !==
                        langItem.newLang.Lang_Read_Write
                    "
                    class="text-decoration-line-through text-error mr-1"
                  >
                    {{ langItem.oldLang.Lang_Read_Write ? "Yes" : "No" }}
                  </span>
                  <span
                    v-if="langItem.newLang"
                    :class="
                      langItem.oldLang &&
                      langItem.oldLang.Lang_Read_Write !==
                        langItem.newLang.Lang_Read_Write
                        ? 'text-success'
                        : ''
                    "
                  >
                    {{ langItem.newLang.Lang_Read_Write ? "Yes" : "No" }}
                  </span>
                  <span
                    v-else-if="langItem.oldLang"
                    class="text-error text-decoration-line-through"
                  >
                    {{ langItem.oldLang.Lang_Read_Write ? "Yes" : "No" }}
                  </span>
                </div>
              </div>
            </v-col>

            <!-- Lang Proficiency -->
            <v-col
              v-if="labelList[371]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              sm="6"
              class="pa-0 pl-4"
            >
              <div
                class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
              >
                <b class="mr-2 text-grey">{{ labelList[371]?.Field_Alias }}</b>
                <v-tooltip
                  :text="
                    langItem.newLang?.Lang_Proficiency ||
                    langItem.oldLang?.Lang_Proficiency
                  "
                  location="bottom"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <span v-bind="props">
                      <div
                        class="text-truncate"
                        :style="
                          isMobileView ? 'max-width: 200px' : 'max-width: 140px'
                        "
                      >
                        <span
                          v-if="
                            langItem.oldLang &&
                            langItem.newLang &&
                            langItem.oldLang.Lang_Proficiency !==
                              langItem.newLang.Lang_Proficiency
                          "
                          class="text-decoration-line-through text-error mr-1"
                        >
                          {{
                            checkNullValue(langItem.oldLang.Lang_Proficiency)
                          }}
                        </span>
                        <span
                          v-if="langItem.newLang"
                          :class="
                            langItem.oldLang &&
                            langItem.oldLang.Lang_Proficiency !==
                              langItem.newLang.Lang_Proficiency
                              ? 'text-success'
                              : ''
                          "
                        >
                          {{
                            checkNullValue(langItem.newLang.Lang_Proficiency)
                          }}
                        </span>
                        <span
                          v-else-if="langItem.oldLang"
                          class="text-error text-decoration-line-through"
                        >
                          {{
                            checkNullValue(langItem.oldLang.Lang_Proficiency)
                          }}
                        </span>
                      </div>
                    </span>
                  </template>
                </v-tooltip>
              </div>
            </v-col>
          </v-row>
        </v-card>
      </v-slide-group-item>
    </v-slide-group>
  </v-row>
</template>

<script>
export default {
  name: "LanguageComparison",
  props: {
    personalDetails: {
      type: Object,
      required: true,
    },
    oldPersonalDetails: {
      type: Object,
      required: true,
    },
    generateRandomColor: {
      type: Function,
      required: true,
    },
    checkNullValue: {
      type: Function,
      required: true,
    },
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    mergedLanguages() {
      const oldLangs = this.oldPersonalDetails?.Languages || [];
      const newLangs = this.personalDetails?.Languages || [];

      const idSet = new Set();

      oldLangs.forEach((lang) => {
        if (lang?.Lang_Id) idSet.add(lang.Lang_Id);
      });
      newLangs.forEach((lang) => {
        if (lang?.Lang_Id) idSet.add(lang.Lang_Id);
      });

      return Array.from(idSet).map((id) => {
        const oldLang = oldLangs.find((l) => l?.Lang_Id === id);
        const newLang = newLangs.find((l) => l?.Lang_Id === id);
        return { key: id, oldLang, newLang };
      });
    },
  },
};
</script>

<style scoped>
.text-success {
  color: green;
}
.text-error {
  color: red;
}
.text-decoration-line-through {
  text-decoration: line-through;
}
</style>
