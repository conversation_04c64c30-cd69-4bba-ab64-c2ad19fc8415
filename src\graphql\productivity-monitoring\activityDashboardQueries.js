import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_TOTAL_PRODUCTIVE_HOURS_FOR_A_MONTH = gql`
  query getTotalProductiveHoursForAMonth(
    $startDate: String!
    $endDate: String!
    $productivityType: String!
    $workScheduleId: Int
    $customGroupId: Int
  ) {
    getTotalProductiveHoursForAMonth(
      startDate: $startDate
      endDate: $endDate
      productivityType: $productivityType
      workScheduleId: $workScheduleId
      customGroupId: $customGroupId
    ) {
      errorCode
      message
      period
      totalProductiveHoursData {
        employeeId
        employeeName
        totalUserActiveTimeHours
        totalIdleAndNotAcitveHours
        totalComputerActivityTimeHours
        totalSystemUpTimeHours
        totalTimeSpentOnProductiveAppsAndUrls
      }
    }
  }
`;

export const GET_PER_DAY_PRODUCTIVE_HOURS = gql`
  query getPerDayProductiveHours(
    $startDate: String!
    $endDate: String!
    $workScheduleId: Int
    $customGroupId: Int
  ) {
    getPerDayProductiveHours(
      startDate: $startDate
      endDate: $endDate
      workScheduleId: $workScheduleId
      customGroupId: $customGroupId
    ) {
      errorCode
      message
      period
      activityDetails {
        employeeId
        employeeName
        activityData {
          activityDate
          totalActiveTimeInMinutes
          totalActiveTimeInHours
          totalIdleTimeInMinutes
          totalIdleTimeInHours
          totalComputerActivityTimeInMinutes
          totalComputerActivityTimeInHours
          totalSystemUpTimeInMinutes
          totalSystemUpTimeInHours
          totalTimeSpentOnProductiveAppsAndUrlsInMinutes
          totalTimeSpentOnProductiveAppsAndUrlsInHours
        }
      }
    }
  }
`;

export const GET_EMPLOYEE_WORK_SCHEDULE_DETAILS = gql`
  query getEmployeeWorkScheduleDetails($employeeId: Int!) {
    getEmployeeWorkScheduleDetails(employeeId: $employeeId) {
      errorCode
      message
      employeeWorkScheduleDetails {
        workScheduleId
        workSchedule
        shiftMarginStartDateTime
        shiftMarginEndDateTime
        timeZoneId
        timeZone
      }
    }
  }
`;

export const LIST_WORK_SCHEDULE = gql`
  query shiftQuery($formName: String) {
    listWorkSchedule(formName: $formName) {
      errorCode
      success
      message
      workSchedule {
        Regular_Hours_Per_Day
        WorkSchedule_Id
        WorkSchedule_Name
        Target_Effort_In_Hours_Per_Week
        Time_Zone
        TimeZone_Id
        Zone_Id
        currentDateBasedOnTimezone
      }
    }
  }
`;
