<template>
  <FormRender
    v-if="openFormRender"
    :form-data="formJsonData"
    :resignationId="resignationId"
    :taskId="taskId"
    :formResponseId="formResponseId"
    :conversationalId="conversationalId"
    :processInstanceId="processInstanceId"
    :show-approval="showApproval"
    @form-update-success="handleFormUpdateSuccess()"
    @close-form-render="closeFormRenderForm()"
  ></FormRender>
  <div v-else>
    <div v-if="itemList.length > 0">
      <div
        v-if="!isSmallTable"
        class="d-flex align-center my-3"
        :class="isMobileView ? 'flex-column' : ''"
        :style="
          isMobileView
            ? 'justify-content: center'
            : 'justify-content: space-between'
        "
      >
        <div
          class="d-flex align-center"
          :class="isMobileView ? 'justify-center' : ''"
        >
          <a
            v-if="selectedFormId === 31"
            :href="
              newLeaveFormAccess
                ? baseUrl + 'v3/my-team/leave-request'
                : baseUrl + 'employees/leaves'
            "
            style="text-decoration: none"
            ><v-btn
              color="white mt-1"
              variant="elevated"
              rounded="lg"
              class="mr-2"
              :size="isMobileView ? 'small' : 'default'"
              ><v-icon color="primary" size="14" class="pr-2"
                >fas fa-chevron-left</v-icon
              ><span>{{ isMobileView ? "" : "Back To" }} Leaves</span></v-btn
            ></a
          >
          <a
            v-if="selectedFormId === 267"
            :href="
              newReimbursementFormAccess
                ? baseUrl + 'v3/my-team/travel-and-expenses/claim-request'
                : baseUrl + 'payroll/reimbursement'
            "
            style="text-decoration: none"
            ><v-btn
              color="white mt-1"
              variant="elevated"
              rounded="lg"
              class="mr-2"
              :size="isMobileView ? 'small' : 'default'"
              ><v-icon color="primary" size="14" class="pr-2"
                >fas fa-chevron-left</v-icon
              ><span
                >{{ isMobileView ? "" : "Back To" }}
                {{
                  newReimbursementFormAccess ? "Claim Request" : "Reimbursement"
                }}</span
              ></v-btn
            ></a
          ><v-btn
            v-if="selectedFormId === 268"
            @click="vue3FormRedirection('/my-team/timesheets')"
            color="white mt-1"
            rounded="lg"
            class="mr-2"
            variant="elevated"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>{{ isMobileView ? "" : "Back To" }} Timesheets</span></v-btn
          >
          <v-btn
            v-if="selectedFormId === 34"
            color="white mt-1"
            @click="vue3FormRedirection('/my-team/exit-management')"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>{{ isMobileView ? "" : "Back To" }} Resignation</span></v-btn
          >
          <v-btn
            v-if="selectedFormId === 15"
            @click="vue3FormRedirection('/recruitment/job-posts')"
            color="white mt-1"
            rounded="lg"
            class="mr-2"
            variant="elevated"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>{{ isMobileView ? "" : "Back To" }} Job Posts</span></v-btn
          >
          <v-btn
            v-if="selectedFormId === 253"
            @click="vue3FormRedirection('/my-team/lop-recovery')"
            color="white mt-1"
            variant="elevated"
            rounded="lg"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} LOP Recovery</span
            ></v-btn
          >
          <v-btn
            v-if="
              selectedFormId === 244 ||
              selectedFormId === 245 ||
              selectedFormId === 246 ||
              selectedFormId === 301
            "
            @click="vue3FormRedirection('/my-team/pre-approval')"
            color="white mt-1"
            rounded="lg"
            class="mr-2"
            variant="elevated"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} Pre Approvals</span
            ></v-btn
          >
          <v-btn
            v-if="selectedFormId === 290"
            @click="
              vue3FormRedirection(
                '/man-power-planning/job-requisition/?formId=290'
              )
            "
            color="white mt-1"
            rounded="lg"
            class="mr-2"
            variant="elevated"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} New Position & Additional
              Headcount</span
            ></v-btn
          >
          <v-btn
            v-if="selectedFormId === 291"
            @click="vue3FormRedirection('/man-power-planning/job-requisition')"
            color="white mt-1"
            rounded="lg"
            class="mr-2"
            variant="elevated"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} Approved & Forecasted
              Positions</span
            ></v-btn
          >
          <v-btn
            v-if="selectedFormId === 18"
            @click="vue3FormRedirection('/employee-profile')"
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>{{ isMobileView ? "" : "Back To" }} My Profile</span></v-btn
          >
          <v-btn
            v-if="selectedFormId === 243"
            @click="vue3FormRedirection('/my-team/team-summary')"
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} Team Summary</span
            ></v-btn
          >
          <v-btn
            v-if="selectedFormId === 360"
            @click="
              vue3FormRedirection(
                '/core-hr/payroll-data-management/salary-info'
              )
            "
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} Salary Revision</span
            ></v-btn
          >
          <v-btn
            v-if="selectedFormId === 334"
            color="white mt-1"
            @click="vue3FormRedirection('/my-team/compensatory-off')"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
          >
            <v-icon color="primary" size="14" class="pr-2">
              fas fa-chevron-left
            </v-icon>
            <span>{{ isMobileView ? "" : "Back To" }} Compensatory Off</span>
          </v-btn>
          <v-btn
            v-if="selectedFormId === 341"
            color="white mt-1"
            @click="vue3FormRedirection('/my-team/travel-and-expenses')"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
          >
            <v-icon color="primary" size="14" class="pr-2">
              fas fa-chevron-left
            </v-icon>
            <span>{{ isMobileView ? "" : "Back To" }} Travel Request</span>
          </v-btn>
          <v-btn
            v-if="selectedFormId === 352"
            color="white mt-1"
            @click="vue3FormRedirection('/my-team/short-time-off')"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
          >
            <v-icon color="primary" size="14" class="pr-2">
              fas fa-chevron-left
            </v-icon>
            <span>{{ isMobileView ? "" : "Back To" }} Short Time Off</span>
          </v-btn>
          <v-menu
            v-model="formMenu"
            id="approval-form-filter"
            :close-on-content-click="true"
            transition="scale-transition"
            min-width="290px"
          >
            <template v-slot:activator="{ props }">
              <v-btn
                class="bg-white my-2 ml-2"
                :size="isMobileView ? 'small' : 'default'"
                rounded="lg"
                variant="elevated"
                v-bind="props"
              >
                <v-icon color="primary" size="14">fas fa-align-left</v-icon>
                <span class="text-caption px-1">Form:</span>
                <span class="text-primary font-weight-bold mr-1">
                  {{ selectedFormName }}
                </span>
                <v-icon color="primary" v-if="!formMenu"
                  >fas fa-caret-down</v-icon
                >
                <v-icon color="primary" v-if="formMenu">fas fa-caret-up</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="(form, i) in formFilters"
                :key="i + form.formId"
                class="pages-list"
              >
                <v-list-item-title
                  class="pa-2 cursor-pointer text-primary d-flex align-center justify-space-between"
                  :class="{
                    'text-primary': selectedFormId === form.formId,
                  }"
                  @click="onSelectFormFilter(form.formId)"
                >
                  {{ form.formName }}

                  <v-chip
                    v-if="
                      form.countKey && !isApprovalHistory && !isGroupApproval
                    "
                    size="small"
                    class="ml-2 bg-primary"
                    >{{ approvalCount[form.countKey] || 0 }}</v-chip
                  >
                </v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
          <div
            v-if="selectedFormId === 31 && !isApprovalHistory && !isMobileView"
            class="d-flex mt-2 ml-2"
          >
            <span class="mr-1 late-attendance-box"></span>Late Arrival
          </div>
          <div
            v-if="selectedFormId === 352 && !isApprovalHistory && !isMobileView"
            class="d-flex mt-2 ml-2"
          >
            <span class="mr-1 late-attendance-box"></span>Late Arrival
          </div>
          <div
            v-if="
              (selectedFormId === 31 || selectedFormId === 352) &&
              !isApprovalHistory &&
              !isMobileView
            "
            class="d-flex mt-2 ml-2"
          >
            <span class="mr-1 early-checkout-box"></span>Early Checkout
          </div>
          <div
            v-if="selectedFormId === 31 && !isApprovalHistory && !isMobileView"
            class="d-flex mt-2 ml-2"
          >
            <span class="mr-1 attendance-shortage-box"></span>Attendance
            Shortage
          </div>
          <div
            v-if="selectedFormId === 31 && !isApprovalHistory && !isMobileView"
            class="d-flex mt-2 ml-2"
          >
            <span class="mr-1 attendance-finalization-box"></span>Auto Loss of
            Pay
          </div>
        </div>
        <div
          class="d-flex align-center"
          :class="isMobileView ? 'flex-column' : 'justify-end'"
        >
          <div
            class="d-flex align-center flex-wrap"
            :class="isMobileView ? 'justify-center' : ''"
          >
            <v-btn
              v-if="!isApprovalHistory && !isGroupApproval"
              color="white"
              rounded="lg"
              class="ml-2 mt-1"
              @click="
                onMultiApproval(
                  'approve',
                  'hr-workflow-task-management-approve text-green'
                )
              "
              :size="isMobileView ? 'small' : 'default'"
              ><i
                class="hr-workflow-task-management-approve text-green pr-1 text-body-1"
              ></i
              >Approve</v-btn
            >
            <v-btn
              v-if="!isApprovalHistory && !isGroupApproval"
              color="white"
              class="ml-2 mt-1"
              rounded="lg"
              @click="
                onMultiApproval(
                  'reject',
                  'hr-workflow-task-management-reject text-red'
                )
              "
              :size="isMobileView ? 'small' : 'default'"
              ><i
                class="hr-workflow-task-management-reject text-red pr-1 text-body-1"
              ></i
              >Reject</v-btn
            >
            <v-btn
              v-if="isApprovalHistory"
              class="bg-white my-2 ml-2"
              :size="isMobileView ? 'small' : 'default'"
              rounded="lg"
            >
              <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
              <span class="text-caption px-1">Approval Date:</span>
              <flat-pickr
                v-model="filteredApprovalDateRange"
                :config="flatPickerOptions"
                placeholder="Select Date"
                class="ml-2 mt-1 date-range-picker-custom-bg"
                style="outline: 0px; color: var(--v-primary-base)"
                @onClose="onChangeApprovalData"
              ></flat-pickr>
            </v-btn>
            <v-btn
              rounded="lg"
              color="transparent"
              variant="flat"
              class="ml-2 mt-1"
              :size="isMobileView ? 'small' : 'default'"
              @click="$emit('refetch-list')"
              ><v-icon>fas fa-redo-alt</v-icon></v-btn
            >
            <v-menu v-model="openMoreMenu" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="mt-1 ml-n2 mr-n5" v-bind="props">
                  <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                  <v-icon v-else>fas fa-caret-up</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'pink-lighten-5': isHovering,
                        }"
                        ><v-icon size="15" class="pr-2">{{
                          action.icon
                        }}</v-icon
                        >{{ action.key }}</v-list-item-title
                      >
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
      </div>
      <v-data-table
        :headers="tableHeaders"
        :items="itemList"
        class="elevation-1"
        :show-select="
          !isMobileView &&
          !isApprovalHistory &&
          !isGroupApproval &&
          !isSmallTable
        "
        fixed-header
        :height="
          $store.getters.getTableHeightBasedOnScreenSize(250, itemList, true)
        "
        style="box-shadow: none !important"
        :items-per-page="20"
        :items-per-page-options="[
          { value: 20, title: '20' },
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
        item-value="task_id"
        :sort-by="[{ key: 'addedOn', order: 'desc' }]"
        v-model="selectedApprovalRecords"
      >
        <template v-slot:[`header.data-table-select`]="{ selectAll }">
          <v-checkbox-btn
            v-model="selectAllBox"
            color="primary"
            false-icon="far fa-circle"
            true-icon="fas fa-check-circle"
            indeterminate-icon="fas fa-minus-circle"
            class="mt-1"
            @change="selectAll(selectAllBox)"
          ></v-checkbox-btn>
        </template>
        <template v-slot:item="{ item }">
          <tr
            @click="onSelectItem(item)"
            class="data-table-tr bg-white cursor-pointer"
            :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
          >
            <td v-if="!isApprovalHistory && !isGroupApproval && !isSmallTable">
              <div class="d-flex" style="height: 4em">
                <div
                  v-if="
                    !isMobileView &&
                    !isSmallTable &&
                    item.instanceData &&
                    item.instanceData.lateAttendance
                  "
                  class="data-table-side-border late-attendance-border-color"
                ></div>
                <div
                  v-if="
                    !isMobileView &&
                    !isSmallTable &&
                    item.earlyCheckout?.toLowerCase() == 'yes'
                  "
                  class="data-table-side-border early-checkout-border-color"
                ></div>
                <div
                  v-if="
                    !isMobileView &&
                    !isSmallTable &&
                    item.attendanceShortage?.toLowerCase() == 'yes'
                  "
                  class="data-table-side-border attendance-shortage-border-color"
                ></div>
                <div
                  v-if="
                    !isMobileView &&
                    !isSmallTable &&
                    item.autoLOP?.toLowerCase() == 'yes'
                  "
                  class="data-table-side-border attendance-finalization-border-color"
                ></div>
                <v-checkbox
                  v-model="item.isSelected"
                  hide-details
                  density="compact"
                  color="primary"
                  false-icon="far fa-circle"
                  true-icon="fas fa-check-circle"
                  @click.stop="
                    {
                    }
                  "
                  @update:model-value="checkAllSelected()"
                ></v-checkbox>
              </div>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isApprovalHistory && !isOutstandingApproval"
            >
              <div v-if="isMobileView" class="font-weight-bold">Title</div>
              <section class="d-flex align-center">
                <div
                  v-if="
                    isSmallTable &&
                    !isMobileView &&
                    item.task_id === selectedItem
                  "
                  class="data-table-side-border selected-item-border-color"
                ></div>
                <section class="text-primary font-weight-medium text-body-2">
                  {{ item.description ? item.description : "-" }}
                </section>
              </section>
            </td>
            <td
              v-if="selectedFormId !== 15 && selectedFormId !== 291"
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold mt-2">
                Employee
              </div>
              <section class="d-flex align-center">
                <div
                  v-if="
                    isApprovalHistory &&
                    !isSmallTable &&
                    !isMobileView &&
                    item.taskStatus
                  "
                  class="data-table-side-border"
                  :class="
                    item.taskStatus === 'Rejected'
                      ? 'rejected-border-color'
                      : 'approved-border-color'
                  "
                ></div>
                <div
                  v-if="
                    (isApprovalHistory || isOutstandingApproval) &&
                    isSmallTable &&
                    !isMobileView &&
                    item.task_id === selectedItem
                  "
                  class="data-table-side-border selected-item-border-color"
                ></div>
                <div class="text-body-2 text-primary">
                  {{ item.employeeName ? item.employeeName : "-" }}
                  <div v-if="item.userDefinedEmpId" class="text-grey">
                    {{ item.userDefinedEmpId }}
                  </div>
                </div>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="isApprovalHistory || isOutstandingApproval"
            >
              <div v-if="isMobileView" class="font-weight-bold">Approver</div>
              <section class="d-flex align-center">
                <div
                  v-if="
                    isSmallTable &&
                    !isMobileView &&
                    selectedFormId === 15 &&
                    item.task_id === selectedItem
                  "
                  class="data-table-side-border selected-item-border-color"
                ></div>
                <section class="text-primary text-body-2">
                  {{ item.approver ? item.approver : "-" }}
                  <div v-if="item.approverUserDefinedEmpId" class="text-grey">
                    {{ item.approverUserDefinedEmpId }}
                  </div>
                </section>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 243"
            >
              <div v-if="isMobileView" class="font-weight-bold">Updated By</div>
              <section class="text-primary text-body-2">
                {{
                  item.moreDetails?.updatedBy
                    ? item.moreDetails?.updatedBy
                    : item.moreDetails?.addedBy
                }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                !isSmallTable &&
                (selectedFormId === 18 || selectedFormId === 243)
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">Changes</div>
              <section class="text-primary text-body-2">
                {{ presentEmployeeDetailsTabName(item) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 360"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Effective Month
              </div>
              <section class="text-primary text-body-2">
                {{
                  convertToMonth(item.instanceData?.salaryEffectiveMonth) || "-"
                }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 360"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Payout Month
              </div>
              <section class="text-primary text-body-2">
                {{ convertToMonth(item.instanceData?.payoutMonth) || "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 360"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Previous CTC
              </div>
              <section class="text-primary text-body-2">
                {{ item.instanceData?.previousCtc || "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 360"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Revised CTC
              </div>
              <section class="text-primary text-body-2">
                {{ item.instanceData?.annualCTC || "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 267"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Total Applied Amount
              </div>
              <section class="text-primary text-body-2">
                {{ item.totalAppliedAmount ? item.totalAppliedAmount : "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 267"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Total Approved Amount
              </div>
              <section class="text-primary text-body-2">
                {{ item.totalAmount ? item.totalAmount : "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 267"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Applied Date
              </div>
              <section class="text-primary text-body-2">
                {{ item.appliedDate ? item.appliedDate : "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 268"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Total Hours
              </div>
              <section class="text-primary text-body-2">
                {{ decimalToHours(item.totalEffort) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 268"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Weekend Date
              </div>
              <section class="text-primary text-body-2">
                {{ item.weekendDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 31"
            >
              <div v-if="isMobileView" class="font-weight-bold">Leave Type</div>
              <section class="text-primary text-body-2">
                {{ item.leaveType ? item.leaveType : "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                !isSmallTable &&
                (selectedFormId === 31 ||
                  selectedFormId === 244 ||
                  selectedFormId === 245 ||
                  selectedFormId === 246 ||
                  selectedFormId === 301)
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">Start Date</div>
              <section class="text-primary text-body-2">
                {{ item.startDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                !isSmallTable &&
                (selectedFormId === 31 ||
                  selectedFormId === 244 ||
                  selectedFormId === 245 ||
                  selectedFormId === 246 ||
                  selectedFormId === 301)
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">End Date</div>
              <section class="text-primary text-body-2">
                {{ item.endDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 253"
            >
              <div v-if="isMobileView" class="font-weight-bold">Total Days</div>
              <section class="text-primary text-body-2">
                {{
                  item.lopRecoveryProcessingMonth
                    ? item.lopRecoveryProcessingMonth
                    : "-"
                }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                !isSmallTable &&
                (selectedFormId === 31 ||
                  selectedFormId === 253 ||
                  selectedFormId === 244 ||
                  selectedFormId === 245 ||
                  selectedFormId === 246 ||
                  selectedFormId === 301)
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">Total Days</div>
              <section class="text-primary text-body-2">
                {{ item.totalDays ? item.totalDays : "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 253"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Total Amount
              </div>
              <section class="text-primary text-body-2">
                {{ item.totalAmount ? item.totalAmount : "0" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 34"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Applied Date
              </div>
              <section class="text-primary text-body-2">
                {{ item.appliedDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 34"
            >
              <div v-if="isMobileView" class="font-weight-bold">Exit Date</div>
              <section class="text-primary text-body-2">
                {{ item.exitDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 15"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Job Post Name
              </div>
              <section class="text-primary text-body-2">
                {{ item.jobTitle ? item.jobTitle : "-" }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 15 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Posting Date
              </div>
              <section class="text-primary text-body-2">
                {{ item.postingDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 15 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Closing Date
              </div>
              <section class="text-primary text-body-2">
                {{ item.closingDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 15 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">Priority</div>
              <section class="text-primary text-body-2">
                {{
                  item.priority
                    ? convertCamelCaseToPascalCase(item.priority)
                    : "-"
                }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 34"
            >
              <div v-if="isMobileView" class="font-weight-bold">Form</div>
              <div
                v-if="
                  item.form_identifier &&
                  item.form_identifier !== '0' &&
                  item.instanceData &&
                  item.instanceData.resignationId
                "
              >
                <i
                  class="hr-workflow-task-management-form text-blue text-h5 pl-2 cursor-pointer"
                  aria-hidden="true"
                  title="Form"
                  id="idFormButton"
                  @click.stop="
                    openDynamicForm(
                      item.instanceData.resignationId,
                      item.form_identifier,
                      item.task_id,
                      item.process_instance_id,
                      item.task_action
                    )
                  "
                ></i>
              </div>
              <section v-else class="text-primary text-body-2">-</section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable && selectedFormId === 34"
            >
              <div v-if="isMobileView" class="font-weight-bold">Document</div>
              <section
                v-if="item?.instanceData?.fileName"
                @click.stop="
                  retrieveFileContent(item.instanceData.fileName, [], true)
                "
                class="text-body-2 cursor-pointer"
                style="max-width: 200px"
              >
                <v-icon>fas fa-paperclip</v-icon>
                <v-chip
                  color="primary"
                  variant="elevated"
                  size="x-small"
                  class="document-chip pa-1"
                >
                  1</v-chip
                >
                <!-- No + indicator for form ID 34 since there's only one file -->
              </section>
              <section v-else class="text-primary text-body-2">-</section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                !isSmallTable &&
                (selectedFormId === 31 ||
                  selectedFormId === 244 ||
                  selectedFormId === 301)
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">Document</div>
              <section
                v-if="item.fileNames && Object.keys(item.fileNames).length > 0"
                class="text-body-2 cursor-pointer"
                @click.stop="
                  retrieveFileContent(item.fileNames[0], item.fileNames, true)
                "
                style="max-width: 200px"
              >
                <v-icon>fas fa-paperclip</v-icon>
                <v-chip
                  color="primary"
                  variant="elevated"
                  size="x-small"
                  class="document-chip pa-1"
                >
                  {{ Object.keys(item.fileNames).length }}</v-chip
                >
              </section>
              <section v-else class="text-primary text-body-2">-</section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                (selectedFormId === 291 || selectedFormId === 290) &&
                !isSmallTable
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">Position</div>
              <section style="height: 3em" class="justify-start align-center">
                <v-tooltip
                  :text="item?.positionName"
                  location="bottom"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      v-bind="props"
                      class="text-body-2 text-truncate text-start"
                      style="max-width: 200px"
                    >
                      {{ checkNullValue(item.positionName) }}
                    </div>
                  </template>
                </v-tooltip>
                <div
                  class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 300px; ' : 'max-width: 200px; '
                  "
                >
                  {{ item.positionCode }}
                </div>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                (selectedFormId === 291 || selectedFormId === 290) &&
                !isSmallTable
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">Group Code</div>
              <section class="text-primary text-body-2">
                {{ item.groupCode }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                (selectedFormId === 291 || selectedFormId === 290) &&
                !isSmallTable
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Division Code
              </div>
              <section class="text-primary text-body-2">
                {{ item.divisionCode }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="
                (selectedFormId === 291 || selectedFormId === 290) &&
                !isSmallTable
              "
            >
              <div v-if="isMobileView" class="font-weight-bold">
                No. of Positions
              </div>
              <section class="text-primary text-body-2">
                {{ item.noOfPositions }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 334 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Worked Date
              </div>
              <section class="text-primary text-body-2">
                {{ item.workedDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 334 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">Duration</div>
              <section class="text-primary text-body-2">
                {{ item.duration }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 334 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">Period</div>
              <section class="text-primary text-body-2">
                {{ item.period }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 334 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Compensatory Off Date
              </div>
              <section class="text-primary text-body-2">
                {{ item.compOffDate }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 341 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Travel Name
              </div>
              <section class="text-primary text-body-2">
                {{ item.travelName }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 341 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Travel Type
              </div>
              <section class="text-primary text-body-2">
                {{ item.travelType }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 341 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Travel Start Date
              </div>
              <section class="text-primary text-body-2">
                {{ formatDate(item.travelStartDate) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 341 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Travel End Date
              </div>
              <section class="text-primary text-body-2">
                {{ formatDate(item.travelEndDate) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 341 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Destination Country for Visa
              </div>
              <section class="text-primary text-body-2">
                {{ item.destinationCountry }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 341 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Budget Amount
              </div>
              <section class="text-primary text-body-2">
                {{ item.amount }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 352 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Request For
              </div>
              <section class="text-primary text-body-2">
                {{ item.requestFor }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 352 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Start Date & Time
              </div>
              <section class="text-primary text-body-2">
                {{ item.startDateTime }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 352 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                End Date & Time
              </div>
              <section class="text-primary text-body-2">
                {{ item.endDateTime }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="selectedFormId === 352 && !isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Total Hours
              </div>
              <section class="text-primary text-body-2">
                {{ item.totalHours }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">Progress</div>
              <div class="d-flex" style="max-width: 200px">
                <v-progress-linear
                  v-if="!isMobileView"
                  :model-value="item.approvalLevel"
                  height="20"
                  :color="
                    item.taskStatus === 'Rejected' ||
                    item.completedTaskCount == 0
                      ? 'red'
                      : item.completedTaskCount >= item.totalTaskCount
                      ? 'green'
                      : 'amber'
                  "
                  rounded="lg"
                >
                  <strong
                    :class="item.completedTaskCount == 0 ? 'text-red' : ''"
                    >{{
                      item.completedTaskCount + "/" + item.totalTaskCount
                    }}</strong
                  ></v-progress-linear
                >
                <section
                  v-else
                  class="text-body-2"
                  :class="
                    item.taskStatus === 'Rejected' ? 'text-red' : 'text-green'
                  "
                >
                  {{ item.completedTaskCount + "/" + item.totalTaskCount }}
                </section>
              </div>
              <div
                v-if="item.approvalStatus"
                class="text-center text-caption text-grey-darken-1"
              >
                {{ item.approvalStatus }}
              </div>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              v-if="!isSmallTable"
            >
              <div v-if="isMobileView" class="font-weight-bold">Actions</div>
              <div class="d-flex align-center" style="max-width: 200px">
                <div
                  v-if="!isApprovalHistory && item.task_action !== undefined"
                  class="d-flex align-center"
                >
                  <v-icon
                    v-if="selectedFormId === 243 || selectedFormId === 18"
                    class="pr-2 cursor-pointer"
                    color="amber"
                    title="View Employee Changes"
                    @click.stop="$emit('open-employee-changes', item)"
                    >fas fa-eye</v-icon
                  >
                  <v-icon
                    v-if="!isGroupApproval && selectedFormId === 268"
                    class="pr-2 mt-n1 cursor-pointer"
                    color="amber"
                    title="Return"
                    @click.stop="openConfirmationModal('return', '', item)"
                    >fas fa-reply</v-icon
                  >
                  <i
                    v-if="!isGroupApproval"
                    class="hr-workflow-task-management-approve text-green text-h5 pl-2 cursor-pointer"
                    aria-hidden="true"
                    title="Approve"
                    id="idApproveButton"
                    @click.stop="
                      item.form_identifier &&
                      item.form_identifier !== '0' &&
                      item.instanceData &&
                      item.instanceData.resignationId &&
                      selectedFormId === 34
                        ? openDynamicForm(
                            item.instanceData.resignationId,
                            item.form_identifier,
                            item.task_id,
                            item.process_instance_id,
                            item.task_action
                          )
                        : openConfirmationModal(
                            'approve',
                            'hr-workflow-task-management-approve text-green',
                            item
                          )
                    "
                  ></i>
                  <i
                    v-if="!isGroupApproval"
                    class="hr-workflow-task-management-reject text-red text-h5 pl-2 cursor-pointer"
                    aria-hidden="true"
                    title="Reject"
                    id="idRejectButton"
                    @click.stop="
                      openConfirmationModal(
                        'reject',
                        'hr-workflow-task-management-reject text-red',
                        item
                      )
                    "
                  ></i>
                  <i
                    v-if="isGroupApproval && item.status_id !== '1006'"
                    class="hr-workflow-task-management-claim text-green text-h5 pl-2 cursor-pointer"
                    aria-hidden="true"
                    title="Claim"
                    id="idClaimButton"
                    @click.stop="
                      openConfirmationModal(
                        'claim',
                        'hr-workflow-task-management-claim text-green',
                        item
                      )
                    "
                  ></i>
                  <i
                    v-if="isGroupApproval && item.status_id === '1006'"
                    class="hr-workflow-task-management-claim-override claim-override-color text-h5 pl-2 cursor-pointer"
                    aria-hidden="true"
                    title="Claim Over ride"
                    id="idClaimOverRideButton"
                    @click.stop="
                      openConfirmationModal(
                        'claim-override',
                        'hr-workflow-task-management-claim-override claim-override-color',
                        item
                      )
                    "
                  ></i>
                  <i
                    v-if="!isGroupApproval && item.custom_group_id"
                    class="hr-workflow-task-management-surrender text-orange text-h5 pl-2 cursor-pointer"
                    aria-hidden="true"
                    title="Surrender"
                    id="idSurrenderButton"
                    @click.stop="
                      openConfirmationModal(
                        'surrender',
                        'hr-workflow-task-management-surrender text-orange',
                        item
                      )
                    "
                  ></i>
                </div>
                <v-icon
                  class="ml-2 mt-n1"
                  color="blue"
                  title="Comments"
                  @click.stop="openTaskLevel(item)"
                  >far fa-comment-alt</v-icon
                >
                <v-menu
                  @update:modelValue="onMenuToggle($event, item)"
                  v-if="
                    item.moreDetails &&
                    Object.keys(item.moreDetails).length > 0 &&
                    selectedFormId !== 267 &&
                    selectedFormId !== 268 &&
                    selectedFormId !== 290 &&
                    selectedFormId !== 18 &&
                    selectedFormId !== 243 &&
                    selectedFormId !== 360
                  "
                  transition="scale-transition"
                >
                  <template v-slot:activator="{ props }">
                    <v-btn
                      rounded="lg"
                      variant="outlined"
                      size="small"
                      color="primary"
                      class="ml-2 mt-n1"
                      v-bind="props"
                      >More</v-btn
                    >
                  </template>
                  <v-card max-height="400px" max-width="500px">
                    <div
                      v-for="(data, key) of item.moreDetails"
                      :key="key + data"
                      class="pa-2 cursor-pointer text-primary"
                    >
                      <v-row>
                        <v-col cols="4">
                          {{ convertCamelCaseToPascalCase(key) }}
                        </v-col>
                        <v-col cols="1">:</v-col>
                        <v-col cols="7">
                          <div
                            v-if="key === 'jobDescription'"
                            ref="editorView"
                            class="quill-editorView"
                          ></div>
                          <span
                            v-else
                            :class="
                              key === 'lateAttendanceHours' ? 'text-red' : ''
                            "
                            style="white-space: break-spaces"
                            >{{ data }}</span
                          >
                        </v-col>
                      </v-row>
                    </div>
                  </v-card>
                </v-menu>
                <v-btn
                  v-if="
                    selectedFormId == 290 &&
                    (!item.positionCode || item.positionCode == '-')
                  "
                  rounded="lg"
                  variant="outlined"
                  size="small"
                  color="primary"
                  class="ml-2 mt-n1"
                  @click.stop="
                    redirectToPositionForm(item.instanceData.positionRequestId)
                  "
                  >More</v-btn
                >
                <v-btn
                  v-if="selectedFormId === 267 && reimbursementFormAccess"
                  rounded="lg"
                  variant="outlined"
                  size="small"
                  color="primary"
                  class="ml-2 mt-n1"
                  @click.stop="openInvoiceDetailsModal(item)"
                  >View Invoice</v-btn
                >
                <v-btn
                  v-if="selectedFormId === 268 && timesheetFormAccess"
                  rounded="lg"
                  variant="outlined"
                  size="small"
                  color="primary"
                  class="ml-2 mt-n1"
                  @click.stop="openTimesheetDetailsModal(item)"
                  >View More</v-btn
                >
                <v-btn
                  v-if="selectedFormId === 360"
                  rounded="lg"
                  variant="outlined"
                  size="small"
                  color="primary"
                  class="ml-2 mt-n1"
                  @click.stop="openSalaryRevisionModal(item)"
                  >View</v-btn
                >
              </div>
            </td>
          </tr>
        </template>
      </v-data-table>
      <AppWarningModal
        v-if="openWarningModal"
        :open-modal="openWarningModal"
        :confirmation-heading="warningText"
        :confirmation-text="warningSubText"
        :icon-class="warningIconClass"
        iconName="fas fa-reply"
        iconColor="amber"
        @close-warning-modal="onCloseWarningModal()"
        @accept-modal="onUpdateAction()"
      >
        <template
          v-if="
            actionType === 'approve' ||
            actionType === 'reject' ||
            actionType === 'return' ||
            isMultiApproval
          "
          #warningModalContent
        >
          <v-row
            v-if="
              selectedInvoiceInstanceData &&
              Object.keys(selectedInvoiceInstanceData).length > 0 &&
              selectedInvoiceInstanceData.Claim_Title &&
              selectedInvoiceInstanceData.Claim_Approved_Amount
            "
            class="py-4"
          >
            <v-col cols="6" class="pa-0">
              <p class="text-subtitle-1 text-grey-darken-1 text-right">
                Claim Pre-approval
              </p>
            </v-col>
            <v-col cols="1" class="py-0">:</v-col>
            <v-col cols="5" class="pa-0">
              <p class="text-subtitle-1 font-weight-regular">
                {{ selectedInvoiceInstanceData.Claim_Title }}
              </p>
            </v-col>
            <v-col cols="6" class="pa-0">
              <p class="text-subtitle-1 text-grey-darken-1 text-right">
                Claim Approved Amount
              </p>
            </v-col>
            <v-col cols="1" class="py-0">:</v-col>
            <v-col cols="5" class="pa-0">
              <p class="text-subtitle-1 font-weight-regular">
                {{ selectedInvoiceInstanceData.Claim_Approved_Amount }}
              </p>
            </v-col>
            <v-col cols="6" class="pa-0">
              <p class="text-subtitle-1 text-grey-darken-1 text-right">
                Reimbursed Amount
              </p>
            </v-col>
            <v-col cols="1" class="py-0">:</v-col>
            <v-col cols="5" class="pa-0">
              <p class="text-subtitle-1 font-weight-regular">
                {{ selectedInvoiceInstanceData.totalAmount }}
              </p>
            </v-col>
          </v-row>
          <v-form ref="reasonForm">
            <v-textarea
              v-model="approvalReason"
              :placeholder="
                actionType === 'reject' || actionType === 'return'
                  ? 'Reason *'
                  : enforceLeaveApprovalComment && selectedFormId === 31
                  ? 'Remarks *'
                  : 'Remarks'
              "
              variant="outlined"
              auto-grow
              rows="1"
              class="mt-4"
              :style="isMobileView ? '' : 'min-width: 300px'"
              :rules="[
                actionType === 'reject' ||
                actionType === 'return' ||
                (enforceLeaveApprovalComment && selectedFormId === 31)
                  ? statusApprovalRules.required
                  : true,
                statusApprovalRules.minCounter,
                statusApprovalRules.maxCounter,
                statusApprovalRules.vComment,
              ]"
            ></v-textarea>
          </v-form>
        </template>
      </AppWarningModal>
    </div>
    <AppFetchErrorScreen
      v-else
      key="no-results-screen"
      :main-title="emptyScenarioMsg[0]"
      :topMessage="emptyScenarioMsg.length === 2 ? emptyScenarioMsg[1] : ''"
      :image-name="
        originalList.length === 0
          ? 'workflow/empty-approval'
          : 'common/no-records'
      "
      :isSmallImage="originalList.length === 0"
    >
      <template #contentSlot>
        <div class="d-flex mb-2 flex-wrap justify-center">
          <v-menu
            v-model="formMenu"
            v-if="originalList.length === 0"
            id="approval-form-filter"
            :close-on-content-click="true"
            transition="scale-transition"
            min-width="290px"
          >
            <template v-slot:activator="{ props }">
              <v-btn
                class="bg-white mt-1"
                rounded="lg"
                variant="elevated"
                v-bind="props"
                :size="isMobileView ? 'small' : 'default'"
              >
                <v-icon color="primary" size="14">fas fa-align-left</v-icon>
                <span class="text-caption px-1">Form:</span>
                <span class="text-primary font-weight-bold mr-1">
                  {{ selectedFormName }}
                </span>
                <v-icon color="primary" v-if="!formMenu"
                  >fas fa-caret-down</v-icon
                >
                <v-icon color="primary" v-if="formMenu">fas fa-caret-up</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="(form, i) in formFilters"
                :key="i + form.formId"
                class="pages-list"
              >
                <v-list-item-title
                  class="pa-2 cursor-pointer text-primary d-flex align-center justify-space-between"
                  :class="{
                    'text-primary': selectedFormId === form.formId,
                  }"
                  @click="onSelectFormFilter(form.formId)"
                >
                  {{ form.formName }}
                  <v-chip
                    v-if="
                      form.countKey && !isApprovalHistory && !isGroupApproval
                    "
                    size="small"
                    class="ml-2 bg-primary"
                    >{{ approvalCount[form.countKey] || 0 }}</v-chip
                  >
                </v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
          <a
            v-if="originalList.length === 0 && selectedFormId === 31"
            :href="
              newLeaveFormAccess
                ? baseUrl + 'v3/my-team/leave-request'
                : baseUrl + 'employees/leaves'
            "
            style="text-decoration: none"
            ><v-btn
              color="white mt-1"
              rounded="lg"
              class="ml-2"
              variant="elevated"
              :size="isMobileView ? 'small' : 'default'"
              ><v-icon color="primary" size="14" class="pr-2"
                >fas fa-chevron-left</v-icon
              ><span>{{ isMobileView ? "" : "Back To" }} Leaves</span></v-btn
            ></a
          >
          <a
            v-if="originalList.length === 0 && selectedFormId === 267"
            :href="
              newReimbursementFormAccess
                ? baseUrl + 'v3/my-team/travel-and-expenses/claim-request'
                : baseUrl + 'payroll/reimbursement'
            "
            style="text-decoration: none"
            ><v-btn
              color="white mt-1"
              rounded="lg"
              variant="elevated"
              class="ml-2"
              :size="isMobileView ? 'small' : 'default'"
              ><v-icon color="primary" size="14" class="pr-2"
                >fas fa-chevron-left</v-icon
              ><span
                >{{ isMobileView ? "" : "Back To" }}
                {{
                  newReimbursementFormAccess ? "Claim Request" : "Reimbursement"
                }}</span
              ></v-btn
            ></a
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 268"
            @click="vue3FormRedirection('/my-team/timesheets')"
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="ml-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>{{ isMobileView ? "" : "Back To" }} Timesheets</span></v-btn
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 34"
            color="white mt-1"
            rounded="lg"
            @click="vue3FormRedirection('/my-team/exit-management')"
            class="ml-2"
            variant="elevated"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>{{ isMobileView ? "" : "Back To" }} Resignation</span></v-btn
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 15"
            @click="vue3FormRedirection('/recruitment/job-posts')"
            color="white mt-1"
            rounded="lg"
            class="mr-2"
            variant="elevated"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>{{ isMobileView ? "" : "Back To" }} Job Posts</span></v-btn
          ><v-btn
            v-if="originalList.length === 0 && selectedFormId === 253"
            @click="vue3FormRedirection('/my-team/lop-recovery')"
            color="white mt-1"
            rounded="lg"
            class="mr-2"
            variant="elevated"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} LOP Recovery</span
            ></v-btn
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 334"
            color="white mt-1"
            @click="vue3FormRedirection('/my-team/compensatory-off')"
            rounded="lg"
            variant="elevated"
            class="mx-2"
            :size="isMobileView ? 'small' : 'default'"
          >
            <v-icon color="primary" size="14" class="pr-2">
              fas fa-chevron-left
            </v-icon>
            <span>{{ isMobileView ? "" : "Back To" }} Compensatory Off</span>
          </v-btn>
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 341"
            color="white mt-1"
            @click="vue3FormRedirection('/my-team/travel-and-expenses')"
            rounded="lg"
            variant="elevated"
            class="mx-2"
            :size="isMobileView ? 'small' : 'default'"
          >
            <v-icon color="primary" size="14" class="pr-2">
              fas fa-chevron-left
            </v-icon>
            <span>{{ isMobileView ? "" : "Back To" }} Travel Request</span>
          </v-btn>
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 352"
            color="white mt-1"
            @click="vue3FormRedirection('/my-team/short-time-off')"
            rounded="lg"
            variant="elevated"
            class="mx-2"
            :size="isMobileView ? 'small' : 'default'"
          >
            <v-icon color="primary" size="14" class="pr-2">
              fas fa-chevron-left
            </v-icon>
            <span>{{ isMobileView ? "" : "Back To" }} Short Time Off</span>
          </v-btn>
          <v-btn
            v-if="
              originalList.length === 0 &&
              (selectedFormId === 244 ||
                selectedFormId === 245 ||
                selectedFormId === 246 ||
                selectedFormId === 301)
            "
            @click="vue3FormRedirection('/my-team/pre-approval')"
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} Pre Approvals</span
            ></v-btn
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 290"
            @click="
              vue3FormRedirection(
                '/man-power-planning/job-requisition/?fornId=290'
              )
            "
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} New Position & Additional
              Headcount</span
            ></v-btn
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 291"
            @click="vue3FormRedirection('/man-power-planning/job-requisition')"
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} Approved & Forecasted
              Positions</span
            ></v-btn
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 18"
            @click="vue3FormRedirection('/employee-profile')"
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span>{{ isMobileView ? "" : "Back To" }} My Profile</span></v-btn
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 243"
            @click="vue3FormRedirection('/my-team/team-summary')"
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} Team Summary</span
            ></v-btn
          >
          <v-btn
            v-if="originalList.length === 0 && selectedFormId === 360"
            @click="
              vue3FormRedirection(
                '/core-hr/payroll-data-management/salary-info'
              )
            "
            color="white mt-1"
            rounded="lg"
            variant="elevated"
            class="mr-2"
            :size="isMobileView ? 'small' : 'default'"
            ><v-icon color="primary" size="14" class="pr-2"
              >fas fa-chevron-left</v-icon
            ><span
              >{{ isMobileView ? "" : "Back To" }} Salary Revision</span
            ></v-btn
          >
          <v-btn
            v-if="isApprovalHistory && originalList.length === 0"
            class="bg-white my-2 ml-2"
            :size="isMobileView ? 'small' : 'default'"
            rounded="lg"
            variant="elevated"
          >
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            <span class="text-caption px-1">Approval Date:</span>
            <flat-pickr
              v-model="filteredApprovalDateRange"
              :config="flatPickerOptions"
              placeholder="Select Date"
              class="ml-2 date-range-picker-custom-bg"
              style="outline: 0px; color: var(--v-primary-base)"
              @onClose="onChangeApprovalData"
            ></flat-pickr>
            <v-icon
              color="primary"
              @click="$emit('reset-approval-date')"
              size="15"
              >fas fa-redo-alt</v-icon
            >
          </v-btn>
          <v-btn
            v-if="originalList.length === 0"
            rounded="lg"
            color="transparent"
            variant="flat"
            class="ml-2 mt-1"
            :size="isMobileView ? 'small' : 'default'"
            @click="$emit('refetch-list')"
            ><v-icon>fas fa-redo-alt</v-icon></v-btn
          >
          <v-btn
            v-if="originalList.length > 0"
            color="primary"
            variant="elevated"
            class="ml-4 mt-1"
            rounded="lg"
            :size="isMobileView ? 'small' : 'default'"
            @click.stop="$emit('reset-filter')"
          >
            Reset Filter/Search
          </v-btn>
        </div>
      </template>
    </AppFetchErrorScreen>
  </div>
  <v-dialog v-model="openModal" persistent width="800">
    <v-card width="800">
      <v-card-title>
        <div class="d-flex" style="width: 100%">
          Attachments
          <v-spacer></v-spacer>
          <div>
            <v-icon
              :color="fileNumber === 1 ? 'grey' : 'primary'"
              :class="fileNumber === 1 ? 'cursor-not-allow' : 'cursor-pointer'"
              @click="fileNumber === 1 ? {} : viewPrevFile()"
              >fas fa-angle-left</v-icon
            >
            <span class="text-grey">{{
              fileNumber + "/" + selectedItemFiles.length
            }}</span>
            <v-icon
              :color="
                fileNumber === selectedItemFiles.length ? 'grey' : 'primary'
              "
              :class="
                fileNumber === selectedItemFiles.length
                  ? 'cursor-not-allow'
                  : 'cursor-pointer'
              "
              @click="
                fileNumber === selectedItemFiles.length ? {} : viewNextFile()
              "
              >fas fa-angle-right</v-icon
            >
          </div>
          <v-icon color="primary" @click="openModal = false"
            >fas fa-times</v-icon
          >
        </div>
      </v-card-title>
      <v-card-text
        :style="`
          max-height: calc(100vh - 200px);
          overflow: scroll;
          min-height: 400px;
        `"
        :class="{ 'd-flex align-center justify-center': isFetchingFiles }"
      >
        <v-progress-circular
          v-if="isFetchingFiles"
          color="primary"
          indeterminate
          size="64"
        >
        </v-progress-circular>
        <div v-else class="mt-n4 text-center">
          <div class="text-primary text-h6 font-weight-medium">
            {{ formattedFileName(selectedItemFiles[fileNumber - 1]) }}
          </div>
          <img
            v-if="!isPdf"
            :src="imgSrc"
            alt="image source"
            style="width: 100%"
          />
          <vue-pdf-app
            v-else
            style="height: 100vh"
            :pdf="iframeSrc"
          ></vue-pdf-app>
        </div>
      </v-card-text>
      <div class="d-flex justify-center">
        <v-btn
          v-if="!isPdf"
          rounded="lg"
          variant="elevated"
          theme="dark"
          color="primary"
          class="font-weight-bold mb-2"
          @click="downloadImage(selectedItemFiles[fileNumber - 1])"
        >
          Download
        </v-btn>
        <v-btn
          v-else
          rounded="lg"
          theme="dark"
          variant="elevated"
          color="primary"
          class="font-weight-bold mb-2"
          @click="downloadFile()"
        >
          Download
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
  <div class="d-flex justify-center">
    <v-dialog v-model="showSignInForm" max-width="70%">
      <irukkaSignInForm
        :formType="'listApproval'"
        @change-status="changeStatus"
        @close="showSignInForm = false"
      >
      </irukkaSignInForm>
    </v-dialog>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import Config from "@/config.js";
import axios from "axios";
import * as fs from "file-saver";
// queries
import {
  GET_DYNAMIC_FORM_DETAILS,
  APPROVAL_COUNT,
} from "@/graphql/workflow/approvalManagementQueries.js";
// components
const FormRender = defineAsyncComponent(() => import("./FormRender.vue"));
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
import { RETURN_TIMESHEETS } from "@/graphql/my-team/timesheets.js";
const irukkaSignInForm = defineAsyncComponent(() =>
  import("../../settings/integration/SignInForm.vue")
);
// functions
import {
  replaceSentanceWithoutExtraSpaceAndNewline,
  decimalToHours,
  checkNullValue,
} from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import VuePdfApp from "vue3-pdf-app";
import "vue3-pdf-app/dist/icons/main.css";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import moment from "moment";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";

export default defineComponent({
  name: "ListApprovals",

  components: { FormRender, irukkaSignInForm, VuePdfApp, flatPickr },

  mixins: [FileExportMixin],

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    isSmallTable: {
      type: Boolean,
      default: false,
    },
    filterFormId: {
      type: [String, Number],
      required: true,
    },
    approvalType: {
      type: String,
      default: "",
    },
    originalList: {
      type: Array,
      required: true,
      default: () => [],
    },
    enforceLeaveApprovalComment: {
      type: Boolean,
      default: false,
    },
    leaveWorkflowEnabled: {
      type: Boolean,
      default: false,
    },
    reimbursementWorkflowEnabled: {
      type: Boolean,
      default: false,
    },
    lateAttendanceNoOnlySelected: {
      type: Boolean,
      default: false,
    },
    filteredApprovalDate: {
      type: String,
      default: "",
    },
    invoiceApproveCount: {
      type: Number,
      default: 0,
    },
    invoiceRejectCount: {
      type: Number,
      default: 0,
    },
  },

  emits: [
    "refetch-list",
    "open-task-level",
    "on-select-item",
    "reset-filter",
    "on-status-update",
    "on-change-form",
    "on-change-approval-date",
    "show-invoice-modal",
    "show-timesheet-modal",
    "reset-approval-date",
    "show-salary-revision-modal",
    "open-employee-changes",
  ],

  data() {
    return {
      itemList: [],
      selectedItem: {},
      selectAllBox: false,
      selectedApprovalRecords: [],
      // filter
      selectedFormId: 31,
      jobPostFormId: null,
      formMenu: false,
      openMoreMenu: false,
      filteredApprovalDateRange: null,

      // actions
      actionTaskId: 0,
      actionStatusId: 0,
      actionFormId: 0,
      actionGroups: null,
      openWarningModal: false,
      actionType: "",
      warningText: "",
      warningSubText: "",
      warningIconClass: "",
      totalSelectedRecords: [],
      leaveMultiApprovalRecords: {},
      processedLeaveApprovalsCount: 0,
      multiApprovalCallCount: 0,
      multiApprovalErrorCount: 0,
      isMultiApproval: false,
      approvalReason: "",
      selectedInvoiceInstanceData: {},

      // loader
      isLoading: false,

      // document presentation
      isPdf: false,
      iframeSrc: "",
      integrationData: {},
      imgSrc: "",
      openModal: false,
      selectedItemFiles: [],
      fileNumber: 1,
      isFetchingFiles: false,
      downloadLink: false,

      // form presentation
      formJsonData: {},
      openFormRender: false,
      formResponseId: null,
      resignationId: 0,
      taskId: 0,
      conversationalId: 0,
      processInstanceId: "",
      showApproval: false,
      // irukka
      irukkaIdToken: "",
      showSignInForm: false,
      irukkaStatus: "",
      validationMessages: [],
      showValidationAlert: false,
      approvalCount: [],
      currentJobDescription: "",
    };
  },

  watch: {
    invoiceApproveCount(count) {
      if (count > 0) {
        this.openConfirmationModal(
          "approve",
          "hr-workflow-task-management-approve text-green"
        );
      }
    },
    invoiceRejectCount(count) {
      if (count > 0) {
        this.openConfirmationModal(
          "reject",
          "hr-workflow-task-management-reject text-red"
        );
      }
    },
    items(val) {
      this.itemList = val;
      this.onApplySearch();
    },
    selectedApprovalRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if task_id is present in selRecords
          if (selRecords.includes(item.task_id)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
      }
    },
    multiApprovalCallCount(count) {
      if (count === this.totalSelectedRecords.length) {
        if (this.actionType === "approve") {
          let actionMsg =
            this.multiApprovalErrorCount > 0
              ? this.totalSelectedRecords.length === 1
                ? "Something went wrong while approving the record. Please try after some time"
                : "Approved Partially"
              : "Approved Successfully";
          this.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: actionMsg,
            type: this.multiApprovalErrorCount > 0 ? "warning" : "success",
          };
          this.showAlert(snackbarData);
        } else {
          let actionMsg =
            this.multiApprovalErrorCount > 0
              ? this.totalSelectedRecords.length === 1
                ? "Something went wrong while rejecting the record. Please try after some time"
                : "Rejected Partially"
              : "Rejected Successfully";
          this.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: actionMsg,
            type: this.multiApprovalErrorCount > 0 ? "warning" : "success",
          };
          this.showAlert(snackbarData);
        }
        this.$emit("on-status-update");
      } else {
        if (this.selectedFormId === 31 && this.actionType === "approve") {
          if (count === this.processedLeaveApprovalsCount) {
            this.leaveMultiApproval();
          }
        }
      }
    },
    searchValue() {
      this.onApplySearch();
    },
  },

  mounted() {
    this.selectedFormId = this.filterFormId;
    this.filteredApprovalDateRange = this.filteredApprovalDate;
    if (this.items.length) {
      this.itemList = this.items;
      this.processedItemList();
      this.onApplySearch();
    }
    this.getApprovalCount();
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // search in shares list
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    newLeaveFormAccess() {
      let formAccessRights = this.accessRights(332);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    newReimbursementFormAccess() {
      let formAccessRights = this.accessRights(338);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formFilters() {
      let formArray = [];
      if (
        this.accessRights(31)?.accessRights?.view ||
        this.accessRights(332)?.accessRights?.view
      ) {
        formArray.push({
          formName: "Leaves",
          formId: 31,
          countKey: "leaves",
        });
      }
      if (this.accessRights(34)?.accessRights?.view) {
        formArray.push({
          formName: "Resignation",
          formId: 34,
          countKey: "resignation",
        });
      }
      if (
        this.accessRights(50)?.accessRights?.view ||
        this.accessRights(338)?.accessRights?.view
      ) {
        formArray.push({
          formName: this.newReimbursementFormAccess
            ? "Claim Request"
            : "Reimbursement",
          formId: 267,
          countKey: "reimbursement",
        });
      }
      // Check access rights for each form before pushing
      if (this.accessRights(23)?.accessRights?.view) {
        formArray.push({
          formName: "Timesheets",
          formId: 268,
          countKey: "timesheet",
        });
      }

      if (this.accessRights(257)?.accessRights?.view) {
        formArray.push({
          formName: "Work from home (pre-approval)",
          formId: 244,
          countKey: "workFromHome",
        });
      }

      if (this.accessRights(257)?.accessRights?.view) {
        formArray.push({
          formName: "Work during week off (pre-approval)",
          formId: 245,
          countKey: "workDuringWeekOff",
        });
      }

      if (this.accessRights(257)?.accessRights?.view) {
        formArray.push({
          formName: "Work during holiday (pre-approval)",
          formId: 246,
          countKey: "workDuringHoliday",
        });
      }

      if (this.accessRights(257)?.accessRights?.view) {
        formArray.push({
          formName: "On Duty (pre-approval)",
          formId: 301,
          countKey: "onDutyPreApprovals",
        });
      }

      if (this.accessRights(256)?.accessRights?.view) {
        formArray.push({
          formName: "LOP Recovery",
          formId: 253,
          countKey: "lopRecovery",
        });
      }

      if (this.accessRights(15)?.accessRights?.view) {
        formArray.push({
          formName: "Job Posts",
          formId: 15,
          countKey: "jobPost",
        });
      }

      if (this.accessRights(290)?.accessRights?.view) {
        formArray.push({
          formName: "New Position & Additional Headcount",
          formId: 290,
          countKey: "newPosition",
        });
      }

      if (this.accessRights(291)?.accessRights?.view) {
        formArray.push({
          formName: "Approved & Forecasted Positions",
          formId: 291,
          countKey: "recruitmentRequest",
        });
      }

      if (
        this.accessRights(139)?.accessRights?.view ||
        this.accessRights(334)?.accessRights?.view
      ) {
        formArray.push({
          formName: "Compensatory Off",
          formId: 334,
          countKey: "compensatoryOff",
        });
      }

      if (this.accessRights(341)?.accessRights?.view) {
        formArray.push({
          formName: "Travel Request",
          formId: 341,
          countKey: "travel",
        });
      }
      if (this.accessRights(243)?.accessRights?.view) {
        formArray.push({
          formName: "Team Summary",
          formId: 243,
          countKey: "teamSummary",
        });
      }
      if (this.accessRights(18)?.accessRights?.view) {
        formArray.push({
          formName: "My Profile",
          formId: 18,
          countKey: "myProfile",
        });
      }

      if (
        this.accessRights(128)?.accessRights?.view ||
        this.accessRights(352)?.accessRights?.view
      ) {
        formArray.push({
          formName: "Short Time Off",
          formId: 352,
          countKey: "shortTimeOff",
        });
      }
      if (this.accessRights(360)?.accessRights?.view) {
        formArray.push({
          formName: "Salary Revision",
          formId: 360,
          countKey: "empSalaryRevision",
        });
      }
      return formArray;
    },
    selectedFormName() {
      let formName = this.formFilters.filter(
        (el) => el.formId === this.selectedFormId
      );
      return formName && formName.length > 0 ? formName[0].formName : "-";
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    isOutstandingApproval() {
      return this.approvalType === "Outstanding Approvals";
    },
    isGroupApproval() {
      return this.approvalType === "Group Approvals";
    },
    isApprovalHistory() {
      return this.approvalType === "Approval History";
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    statusApprovalRules() {
      let msgType = "";
      if (this.actionType === "approve") {
        msgType = "Remarks";
      } else {
        msgType = "Reason";
      }
      const pattern = /^[\w\.\,\#\+\&\/\-\(\)\:\'\n\ ]*$/;
      var rules = {
        required: (value) => !!value || `${msgType} is required.`,
        minCounter: (value) =>
          !value ||
          value.length >= 2 ||
          `${msgType} characters should be more than are equal to 2 characters`,
        maxCounter: (value) =>
          !value ||
          value.length <= 500 ||
          `${msgType} characters should not exceed 500 characters`,
        vComment: (value) => {
          !value || pattern.test(value) || `${msgType} is not valid`;
        },
      };
      return rules;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        if (this.isOutstandingApproval) {
          msgText = "There are no outstanding approvals";
        } else if (this.approvalType === "My Approvals") {
          msgText = "No approvals have been received";
        } else if (this.approvalType === "Group Approvals") {
          msgText = "There are no group approvals";
        } else {
          msgText = "There are no history of approvals";
        }
        msgText += " for the selected filters/searches.";
        msgText = [msgText];
      } else {
        if (this.isApprovalHistory) {
          msgText = ["There are no history of approvals"];
        } else {
          msgText = [
            "We'll let you know when we've got something new for you.",
            "You have caught up with all approval requests.",
          ];
        }
      }
      return msgText;
    },
    formattedFileName() {
      return (fileName) => {
        if (fileName) {
          let fileNameChunks = fileName.split("?");
          return fileNameChunks && fileNameChunks.length > 0
            ? this.selectedFormId === 34
              ? fileNameChunks[2]
              : fileNameChunks[3]
            : "-";
        }
        return "file";
      };
    },
    tableHeaders() {
      let headers = [];
      if (!this.isOutstandingApproval && !this.isApprovalHistory) {
        headers.push({
          title: "Title",
          key: "description",
        });
      }
      if (this.selectedFormId !== 15 && this.selectedFormId !== 291) {
        headers.push({
          title: "Employee",
          key: "employeeName",
        });
      }
      if (this.isOutstandingApproval || this.isApprovalHistory) {
        headers.push({
          title: "Approver",
          key: "approver",
        });
      }
      if (this.isSmallTable && this.selectedFormId === 15) {
        headers.push({ title: "Job Post Name", key: "jobTitle" });
      }
      if (this.isSmallTable) {
        return headers;
      } else {
        if (this.selectedFormId === 31) {
          headers.push(
            { title: "Leave Type", key: "leaveType" },
            { title: "Start Date", key: "startDateInDateFormat" },
            { title: "End Date", key: "endDateInDateFormat" },
            { title: "Total Days", key: "totalDays" },
            {
              title: "Document",
              key: "fileNames",
              sortable: false,
            },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 34) {
          headers.push(
            { title: "Applied Date", key: "appliedDateInDateFormat" },
            { title: "Exit Date", key: "exitDateInDateFormat" },
            { title: "Form", key: "formLink", sortable: false },
            {
              title: "Document",
              key: "fileNames",
              sortable: false,
            },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 15) {
          headers.push(
            { title: "Job Post Name", key: "jobTitle" },
            { title: "Posting Date", key: "postingDateInDateFormat" },
            { title: "Closing Date", key: "closingDateInDateFormat" },
            { title: "Priority", key: "priority" },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 253) {
          headers.push(
            {
              title: "LOP Recovery Processing Month",
              key: "lopRecoveryProcessingMonth",
            },
            { title: "Total Days", key: "totalDays" },
            { title: "Total Amount", key: "totalAmount" },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 267) {
          headers.push(
            {
              title: "Total Applied Amount",
              key: "totalAppliedAmount",
            },
            {
              title: "Total Approved Amount",
              key: "totalAmount",
            },
            { title: "Applied Date", key: "startDateInDateFormat" },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 268) {
          headers.push(
            {
              title: "Total Hours",
              key: "totalEffort",
            },
            { title: "Weekend Date", key: "startDateInDateFormat" },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 291 || this.selectedFormId === 290) {
          headers.push(
            { title: "Position", key: "positionName" },
            { title: "Group Code", key: "groupCode" },
            { title: "Division Code", key: "divisionCode" },
            { title: "No. of Positions", key: "noOfPositions" },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 334) {
          headers.push(
            { title: "Worked Date", key: "workedDate" },
            { title: "Duration", key: "duration" },
            { title: "Period", key: "period" },
            { title: "Compensatory Off Date", key: "compOffDate" },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 341) {
          headers.push(
            { title: "Travel Name", key: "travelName" },
            { title: "Travel Type", key: "travelType" },
            { title: "Travel Start Date", key: "travelStartDate" },
            { title: "Travel End Date", key: "travelEndDate" },
            {
              title: "Destination Country for Visa",
              key: "destinationCountry",
            },
            { title: "Budget Amount", key: "amount" },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 352) {
          headers.push(
            { title: "Request For", key: "requestFor" },
            { title: "Start Date & Time", key: "startDateTime" },
            { title: "End Date & Time", key: "endDateTime" },
            { title: "Total Hours", key: "totalHours" },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 18 || this.selectedFormId === 243) {
          if (this.selectedFormId === 243)
            headers.push({
              title: "Updated By",
              key: "moreDetails",
              sortable: false,
            });
          headers.push(
            { title: "Changes", key: "instanceData", sortable: false },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else if (this.selectedFormId === 360) {
          headers.push(
            { title: "Effective Month", key: "instanceData", sortable: false },
            { title: "Payout Month", key: "instanceData", sortable: false },
            { title: "Previous CTC", key: "instanceData", sortable: false },
            { title: "Revised CTC", key: "instanceData", sortable: false },
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        } else {
          headers.push(
            { title: "Start Date", key: "startDateInDateFormat" },
            { title: "End Date", key: "endDateInDateFormat" },
            { title: "Total Days", key: "totalDays" }
          );
          if (this.selectedFormId === 244 || this.selectedFormId === 301) {
            headers.push({
              title: "Document",
              key: "fileNames",
              sortable: false,
            });
          }
          headers.push(
            { title: "Progress", key: "approvalLevel", sortable: false },
            { title: "Actions", key: "actions", sortable: false }
          );
          return headers;
        }
      }
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment().format(this.$store.state.orgDetails.orgDateFormat),
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    reimbursementFormAccess() {
      let projectFormAccess = this.accessRights("50");
      let projectFormAccess2 = this.accessRights("338");
      if (
        (projectFormAccess &&
          projectFormAccess.accessRights &&
          projectFormAccess.accessRights["view"]) ||
        (projectFormAccess2 &&
          projectFormAccess2.accessRights &&
          projectFormAccess2.accessRights["view"])
      ) {
        return true;
      } else {
        return false;
      }
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    timesheetFormAccess() {
      let formAccess = this.accessRights("23");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return true;
      } else {
        return false;
      }
    },
  },

  methods: {
    decimalToHours,
    checkNullValue,
    initQuillEditor(val, jobDescription) {
      if (this.$refs.editorView?.length) {
        if (val) {
          this.quill = new Quill(this.$refs.editorView[0], {
            theme: "snow",
          });
        } else if (this.$refs.editorView[1]) {
          this.quill = new Quill(this.$refs.editorView[1], {
            theme: "snow",
          });
        }
        this.quill.root.innerHTML = jobDescription
          ? this.convertEmojiCodepointsToEmojis(jobDescription)
          : "";
        this.quill.enable(false);
      }
    },
    formatDate(date) {
      if (date && moment(date).isValid()) {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return moment(date).format(orgDateFormat);
      }
      return "-";
    },
    processedItemList() {
      this.itemList = this.itemList.map((item) => ({
        ...item,
        addedOn: item.moreDetails?.addedOn ? item.moreDetails.addedOn : null,
      }));
    },
    getApprovalCount() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: APPROVAL_COUNT,
          variables: {
            myApprovals: !this.isOutstandingApproval,
          },
          client: "apolloClientC",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listNotificationsInDashboard &&
            response.data.listNotificationsInDashboard.notificationCount
          ) {
            vm.approvalCount = JSON.parse(
              response.data.listNotificationsInDashboard.notificationList
            );
          } else {
            vm.approvalCount = [];
          }
        })
        .catch(() => {
          vm.approvalCount = [];
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "returning",
            form: "approval count",
            isListError: false,
          });
        })
        .finally(() => {
          vm.isLoading = false;
        });
    },
    onChangeApprovalData(selectedDates, dateStr) {
      if (dateStr.includes("to") && dateStr != this.filteredApprovalDate) {
        let splittedDate = dateStr.split(" to ");
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        const startMoment = moment(splittedDate[0], orgDateFormat);
        const endMoment = moment(splittedDate[1], orgDateFormat);
        const diffInDays = endMoment.diff(startMoment, "days");
        if (diffInDays <= 31) {
          this.$emit("on-change-approval-date", dateStr);
        } else {
          let snackbarData = {
            isOpen: true,
            message: "Please select a date range of 31 days or less",
            type: "warning",
          };
          this.showAlert(snackbarData);
          this.filteredApprovalDateRange = this.filteredApprovalDate;
        }
      } else {
        this.$emit("on-change-approval-date", dateStr);
      }
    },
    redirectToPositionForm(position) {
      if (position) {
        this.$router.push(
          `/man-power-planning/job-requisition?formId=290&positionRequestId=${position}`
        );
      }
    },
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    onApplySearch() {
      let val = this.searchValue;
      if (!val) {
        this.itemList = this.items;
        this.processedItemList();
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.items;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
        this.processedItemList();
      }
    },
    onSelectItem(item) {
      this.selectedInvoiceInstanceData = item.instanceData;
      this.isMultiApproval = false;
      this.actionTaskId = item.task_id;
      this.actionStatusId = item.status_id;
      this.actionFormId = item?.form_identifier;
      this.actionGroups = item.custom_group_id;
      this.selectedItem = item.task_id;
      this.$emit("on-select-item", item);
    },
    openTaskLevel(item) {
      this.$emit("open-task-level", item);
    },
    convertCamelCaseToPascalCase(text) {
      const result = text.replace(/([A-Z])/g, " $1");
      const finalResult = result.charAt(0).toUpperCase() + result.slice(1);
      return finalResult;
    },
    async onSelectFormFilter(formId) {
      // I need to implement my irukkaIdToken logic here once the form Id is 15;
      if (formId == 15) {
        this.loadAuthToken();
        await this.fetchIntegrationStatus();
        this.irukkaStatus = this.getStatusByType("irukka");
        if (this.irukkaStatus && this.irukkaStatus == "Active") {
          if (this.irukkaIdToken) {
            //do not need to show the sign-in form
            this.selectedFormId = formId;
            this.$emit("on-change-form", formId);
          } else {
            // show sign-in form
            this.showSignInForm = true;
            this.jobPostFormId = formId;
          }
        } else {
          // do not need to show the sign-in form
          this.selectedFormId = formId;
          this.$emit("on-change-form", formId);
        }
      } else {
        // do not need to show the sign-in form
        this.selectedFormId = formId;
        this.$emit("on-change-form", formId);
      }
    },
    changeStatus() {
      this.showSignInForm = false;
      this.loadAuthToken();
      this.selectedFormId = this.jobPostFormId;
      this.$emit("on-change-form", this.selectedFormId);
    },
    onMultiApproval(approvalType, approvalIconClass) {
      this.totalSelectedCount = 0;
      this.multiApprovalCallCount = 0;
      this.multiApprovalErrorCount = 0;
      this.processedLeaveApprovalsCount = 0;
      this.actionType = approvalType;
      this.totalSelectedRecords = []; // Ensure it's reset

      let selected = this.itemList.filter((el) => el.isSelected === true);
      if (selected && selected.length > 0) {
        this.isMultiApproval = true;
        let recordsHavingFromSubmission = [];
        const invalidValues = [
          null,
          undefined,
          "",
          "0",
          0,
          "none",
          "None",
          false,
          NaN,
          [],
          {},
        ];
        let validRecords = selected.filter(
          (el) =>
            (approvalType === "approve" &&
              invalidValues.includes(el.form_identifier)) ||
            approvalType === "reject"
        );
        recordsHavingFromSubmission = selected.filter(
          (el) => !invalidValues.includes(el.form_identifier)
        );
        if (validRecords.length > 20) {
          let snackbarData = {
            isOpen: true,
            message:
              approvalType === "approve"
                ? "Please do not select more than 20 records to proceed for approval"
                : "Please do not select more than 20 records to proceed for rejection",
            type: "warning",
          };
          this.showAlert(snackbarData);
          return;
        }
        if (validRecords.length > 0) {
          this.totalSelectedRecords = validRecords;
          this.warningSubText =
            validRecords.length !== selected.length
              ? validRecords.length +
                "/" +
                selected.length +
                " are eligible for " +
                approvalType +
                "."
              : "";
          this.openConfirmationModal(
            approvalType,
            approvalIconClass,
            null,
            false
          );
        } else {
          if (recordsHavingFromSubmission.length === 1) {
            this.totalSelectedRecords = recordsHavingFromSubmission;
            const {
              form_identifier,
              instanceData,
              task_id,
              process_instance_id,
              task_action,
            } = recordsHavingFromSubmission[0];
            this.openDynamicForm(
              instanceData.resignationId,
              form_identifier,
              task_id,
              process_instance_id,
              task_action
            );
          } else {
            let approveInvalidMsg =
              recordsHavingFromSubmission.length > 1
                ? "Unable to do multiple approvals when the selected record(s) are having form submission"
                : "There are no selected eligible records for approval";
            let snackbarData = {
              isOpen: true,
              message:
                approvalType === "approve"
                  ? approveInvalidMsg
                  : "There are no selected eligible records for rejection",
              type: "warning",
            };
            this.showAlert(snackbarData);
          }
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please select at least one record to proceed",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    openConfirmationModal(actionType, warningIconClass, item) {
      if (item) {
        this.selectedInvoiceInstanceData = item.instanceData;
        this.isMultiApproval = false;
        this.actionTaskId = item.task_id;
        this.actionStatusId = item.status_id;
        this.actionFormId = item?.form_identifier;
        this.actionGroups = item.custom_group_id;
      }
      this.actionType = actionType;
      this.warningText = "Are you sure to " + actionType + "?";
      if (warningIconClass) {
        this.warningIconClass = warningIconClass + " text-h1";
      } else {
        this.warningIconClass = "";
      }
      this.openWarningModal = true;
    },
    openInvoiceDetailsModal(item) {
      this.selectedInvoiceInstanceData = item.instanceData;
      this.isMultiApproval = false;
      this.actionTaskId = item.task_id;
      this.actionStatusId = item.status_id;
      this.actionFormId = item?.form_identifier;
      this.actionGroups = item.custom_group_id;
      this.$emit("show-invoice-modal", [
        item.instanceData.requestId,
        item.process_instance_id,
      ]);
    },
    openTimesheetDetailsModal(item) {
      if (item) {
        this.isMultiApproval = false;
        this.actionTaskId = item.task_id;
        this.actionStatusId = item.status_id;
        this.actionFormId = item?.form_identifier;
        this.actionGroups = item.custom_group_id;
      }
      this.$emit("show-timesheet-modal", [
        item.employeeId,
        item.instanceData.Week_Ending_Date,
        item.instanceData.Approval_Status,
      ]);
    },
    openSalaryRevisionModal(item) {
      this.isMultiApproval = false;
      this.$emit("show-salary-revision-modal", item);
    },
    returnTimesheets() {
      let vm = this;
      vm.isLoading = true;
      let comment = replaceSentanceWithoutExtraSpaceAndNewline(
        this.approvalReason
      );
      vm.$apollo
        .mutate({
          mutation: RETURN_TIMESHEETS,
          variables: {
            selfService: 0,
            requestId: vm.selectedInvoiceInstanceData.Request_Id,
            returnedComment: comment,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          vm.$emit("on-status-update");
          vm.onCloseWarningModal();
        })
        .catch(() => {
          vm.handleReturnApprovalError();
        });
    },

    handleReturnApprovalError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "returning",
        form: "timesheet approval",
        isListError: false,
      });
    },

    vue3FormRedirection(url) {
      this.$router.push(url);
    },

    async onUpdateAction() {
      if (
        (this.actionType === "reject" ||
          this.actionType === "return" ||
          (this.selectedFormId === 31 && this.enforceLeaveApprovalComment)) &&
        this.$refs.reasonForm
      ) {
        let isFormValid = await this.$refs.reasonForm.validate();
        if (isFormValid && isFormValid.valid && this.approvalReason) {
          this.fuValidateReasonForm();
        }
      } else {
        this.fuValidateReasonForm();
      }
    },
    fuValidateReasonForm() {
      if (this.isMultiApproval) {
        if (this.selectedFormId === 31 && this.actionType === "approve") {
          // make json using empId & leaveId as key and push taskId in that unique key
          this.leaveMultiApprovalRecords = this.totalSelectedRecords.reduce(
            (acc, employee) => {
              const key = `${employee.employeeId}-${employee.leaveId}`;
              if (!acc[key]) {
                acc[key] = [];
              }
              acc[key].push(employee.task_id);
              return acc;
            },
            {}
          );
          this.leaveMultiApproval();
        } else {
          for (let rec of this.totalSelectedRecords) {
            if (this.actionType === "approve") {
              this.approveTask(rec.task_id);
            } else {
              this.rejectTask(rec.task_id);
            }
          }
        }
      } else {
        if (this.actionType === "approve") {
          this.approveTask();
        } else if (this.actionType === "return") {
          this.returnTimesheets();
        } else if (this.actionType === "reject") {
          this.rejectTask();
        } else if (this.actionType === "claim-override") {
          this.claimOverrideTask();
        } else if (this.actionType === "surrender") {
          this.surrenderTask();
        } else if (this.actionType === "claim") {
          this.claimTask();
        } else {
          this.onCloseWarningModal();
        }
      }
    },
    onCloseWarningModal() {
      this.actionType = "";
      this.warningIconClass = "";
      this.warningText = "";
      this.warningSubText = "";
      this.actionTaskId = 0;
      this.actionStatusId = 0;
      this.actionFormId = 0;
      this.actionGroups = null;
      this.openWarningModal = false;
      this.approvalReason = "";
      if (this.$refs.reasonForm) {
        this.$refs.reasonForm.resetValidation();
      }
    },
    leaveMultiApproval() {
      let remainingLeaveMultiApprovalRecords = {};
      for (let leaveApprovalKey in this.leaveMultiApprovalRecords) {
        // once process is started increase one count to check one set of async approval APIs are completed or not
        this.processedLeaveApprovalsCount += 1;
        let leaveApprovalRecord =
          this.leaveMultiApprovalRecords[leaveApprovalKey];
        // when same employee with same leave type having more than one record then push that in new object to make next approval set
        if (leaveApprovalRecord.length > 1) {
          remainingLeaveMultiApprovalRecords[leaveApprovalKey] =
            leaveApprovalRecord.slice(1);
        }
        this.approveTask(leaveApprovalRecord[0]);
      }
      // assign remaining approvals in the same key for further approval process
      this.leaveMultiApprovalRecords = remainingLeaveMultiApprovalRecords;
    },
    approveTask(taskId, type = "") {
      // replace new line, multi space in the comment text-area, to single space using regex
      let comment = replaceSentanceWithoutExtraSpaceAndNewline(
        this.approvalReason
      );
      let inputVariables = {
        task_id: taskId ? taskId : this.actionTaskId,
        status: "approve",
        completed_by: this.loginEmployeeId,
        form_data: {},
        status_id: "1002",
        remarks: comment,
      };
      this.updateTask(inputVariables, type);
    },
    rejectTask(taskId) {
      // replace new line, multi space in the comment text-area, to single space using regex
      let comment = replaceSentanceWithoutExtraSpaceAndNewline(
        this.approvalReason
      );
      let inputVariables = {
        task_id: taskId ? taskId : this.actionTaskId,
        status: "reject",
        completed_by: this.loginEmployeeId,
        status_id: "1007",
        remarks: comment,
      };
      this.updateTask(inputVariables);
    },
    updateTask(inputParams, type = "") {
      let vm = this;
      try {
        vm.isLoading = true;
        const accessToken = window.$cookies.get("accessToken")
          ? window.$cookies.get("accessToken")
          : "";
        const refreshToken = window.$cookies.get("refreshToken")
          ? window.$cookies.get("refreshToken")
          : null;
        axios
          .post(Config.workflowUrl + "/task/update", inputParams, {
            headers: {
              org_code: vm.orgCode,
              employee_id: vm.loginEmployeeId,
              db_prefix: vm.domainName,
              irukka_id_token: this.irukkaIdToken,
              Authorization: accessToken,
              refresh_token: refreshToken,
              partnerid: window.$cookies.get("partnerid")
                ? window.$cookies.get("partnerid")
                : "-",
              additional_headers: JSON.stringify({
                d_code: window.$cookies.get("d_code"),
                b_code: window.$cookies.get("b_code"),
                org_code: vm.orgCode,
                user_ip: this.$store.state.userIpAddress,
                Authorization: accessToken,
                refresh_token: window.$cookies.get("refreshToken")
                  ? window.$cookies.get("refreshToken")
                  : null,
                partnerid: window.$cookies.get("partnerid")
                  ? window.$cookies.get("partnerid")
                  : "-",
              }),
            },
          })
          .then(() => {
            if (!vm.isMultiApproval) {
              vm.isLoading = false;
              let snackbarData;
              if (type === "formSubmission") {
                snackbarData = {
                  isOpen: true,
                  message:
                    "Form submission completed and approved successfully",
                  type: "success",
                };
              } else {
                snackbarData = {
                  isOpen: true,
                  message:
                    vm.actionType === "approve"
                      ? "Approved Successfully"
                      : "Rejected Successfully",
                  type: "success",
                };
              }
              vm.showAlert(snackbarData);
              vm.$emit("on-status-update");
              vm.onCloseWarningModal();
            } else {
              vm.multiApprovalCallCount += 1;
            }
          })
          .catch(function (e) {
            vm.handleUpdateTaskError(e);
          });
      } catch {
        vm.handleUpdateTaskError();
      }
    },

    handleUpdateTaskError(err = "") {
      if (!this.isMultiApproval || this.totalSelectedRecords.length === 1) {
        this.isLoading = false;
        let snackbarData = {
          isOpen: true,
          message:
            this.actionType === "approve"
              ? "Something went wrong while approving the record. Please try after some time."
              : "Something went wrong while rejecting the record. Please try after some time.",
          type: "warning",
        };
        if (err && err.response && err.response.data) {
          let errorCode = err.response.data.errorCode;
          if (errorCode) {
            switch (errorCode) {
              case "ERR-756": // for multiple scenarios
                if (err.response.data.message === "Task id not found") {
                  snackbarData.message =
                    this.actionType === "approve"
                      ? "Unable to approve the record as it was already deleted/approved/rejected in the same or some other user session."
                      : "Unable to reject the record as it was already deleted/approved/rejected in the same or some other user session.";
                } else if (
                  err.response.data.message ===
                  "Unable to update status as the employee's available leave balance is less than applied leave"
                ) {
                  snackbarData.message =
                    this.actionType === "approve"
                      ? "Unable to approve the record as the employee's available leave balance is less than applied leave."
                      : "Unable to reject the record as the employee's available leave balance is less than applied leave.";
                } else if (
                  err.response.data.message ===
                  "Error while processing the request to update the leave status. Please contact the system admin."
                ) {
                  snackbarData.message =
                    "Something went wrong while processing the request to update the leave status. Please contact the platform administrator.";
                } else if (
                  err.response.data.message ===
                  "Error while updating the workflow status. Please contact the system admin."
                ) {
                  snackbarData.message =
                    "Something went wrong while updating the workflow status. Please contact the platform administrator.";
                } else if (
                  err.response.data.message ===
                  "Error while processing the request to update the leave workflow status."
                ) {
                  snackbarData.message =
                    "Something went wrong while processing the request to update the leave workflow status. Please contact the platform administrator.";
                } else if (
                  err.response.data.message ===
                    "Leave cannot be updated as the full and final settlement is initiated or settled for the employee" ||
                  err.response.data.message ===
                    "Leave cannot be added or updated as the full and final settlement is initiated or settled for the employee.Kindly delete the F & F settlement in order to make the necessary modifications."
                ) {
                  snackbarData.message =
                    this.actionType === "approve"
                      ? "Unable to approve the record as the employee's full and final settlement is initiated or settled"
                      : "Unable to reject the record as the employee's full and final settlement is initiated or settled";
                } else if (
                  err.response.data.message ===
                  "Error while processing the request to update the lop recovery workflow status."
                ) {
                  snackbarData.message =
                    "Something went wrong while processing the request to update the lop recovery status. Please contact the platform administrator.";
                } else if (
                  err.response.data.message ===
                  "The leave record does not exist."
                ) {
                  snackbarData.message =
                    this.actionType === "approve"
                      ? "Unable to approve the record as the leave record does not exits"
                      : "Unable to reject the record as the leave record does not exits";
                } else {
                  snackbarData.message = err.response.data.message;
                }
                this.$emit("on-status-update");
                break;
              case "ERR-753": // task id not found
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as it was already deleted/approved/rejected in the same or some other user session."
                    : "Unable to reject the record as it was already deleted/approved/rejected in the same or some other user session.";
                this.$emit("on-status-update");
                break;
              case "ERR-799": // Status id not found
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as the status is invalid. If you continue to see this issue please contact the platform administrator."
                    : "Unable to reject the record as the status is invalid. If you continue to see this issue please contact the platform administrator.";
                this.$emit("on-status-update");
                break;
              case "ERR-754": // task already approved or rejected
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as it was already approved in the same or some other user session."
                    : "Unable to reject the record as it was already rejected in the same or some other user session.";
                this.$emit("on-status-update");
                break;
              case "ERR-798": // Error while executing the query
              default:
                this.actionType === "approve"
                  ? "Something went wrong while approving the record. If you continue to see this issue please contact the platform administrator."
                  : "Something went wrong while rejecting the record. If you continue to see this issue please contact the platform administrator.";
                break;
            }
          }
        }
        this.showAlert(snackbarData);
        this.onCloseWarningModal();
      } else {
        this.multiApprovalCallCount += 1;
        this.multiApprovalErrorCount += 1;
      }
    },
    claimOverrideTask() {
      this.revokeTask();
    },
    surrenderTask() {
      this.revokeTask();
    },
    revokeTask() {
      let vm = this;
      try {
        vm.isLoading = true;
        axios
          .post(
            Config.workflowUrl + "/task/revoke/" + vm.actionTaskId,
            {
              assignee: "",
              status_id: "1001",
            },
            {
              headers: {
                org_code: vm.orgCode,
                employee_id: vm.loginEmployeeId,
                db_prefix: vm.domainName,
                Authorization: window.$cookies.get("accessToken")
                  ? window.$cookies.get("accessToken")
                  : "",
                refresh_token: window.$cookies.get("refreshToken")
                  ? window.$cookies.get("refreshToken")
                  : null,
                partnerid: window.$cookies.get("partnerid")
                  ? window.$cookies.get("partnerid")
                  : "-",
                additional_headers: JSON.stringify({
                  d_code: window.$cookies.get("d_code"),
                  b_code: window.$cookies.get("b_code"),
                  org_code: vm.orgCode,
                  user_ip: this.$store.state.userIpAddress,
                  Authorization: window.$cookies.get("accessToken")
                    ? window.$cookies.get("accessToken")
                    : null,
                  refresh_token: window.$cookies.get("refreshToken")
                    ? window.$cookies.get("refreshToken")
                    : null,
                  partnerid: window.$cookies.get("partnerid")
                    ? window.$cookies.get("partnerid")
                    : "-",
                }),
              },
            }
          )
          .then(() => {
            vm.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message:
                vm.actionType === "claim-override"
                  ? "Claim overridden successfully"
                  : "Surrendered successfully",
              type: "success",
            };
            vm.$emit("on-status-update");
            vm.showAlert(snackbarData);
            vm.onCloseWarningModal();
          })
          .catch(function (e) {
            vm.handleRevokeTaskError(e);
          });
      } catch {
        vm.handleRevokeTaskError();
      }
    },
    handleRevokeTaskError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message:
          this.actionType === "claim-override"
            ? "Something went wrong while overriding the claimed record. Please try after some time."
            : "Something went wrong while surrendering. Please try after some time.",
        type: "warning",
      };
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "ERR-799": // Status id not found
              snackbarData.message =
                this.actionType === "claim-override"
                  ? "Unable to override the claimed record as the status is invalid. If you continue to see this issue please contact the platform administrator."
                  : "Unable to surrender the record as the status is invalid. If you continue to see this issue please contact the platform administrator.";
              this.$emit("on-status-update");
              break;
            case "ERR-753": // task id not found
            case "ERR-754": // task already claim-override or surrendered
              snackbarData.message =
                this.actionType === "claim-override"
                  ? "Unable to override the claimed record as it was already overridden in same or some other user session."
                  : "Unable to surrender the record as it was already surrendered in same or some other user session.";
              this.$emit("on-status-update");
              break;
            case "ERR-798": // Error while executing the query
            default:
              this.actionType === "claim-override"
                ? "Something went wrong while overriding the claimed record. If you continue to see this issue please contact the platform administrator."
                : "Something went wrong while surrendering the record. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        }
      }
      this.showAlert(snackbarData);
      this.onCloseWarningModal();
    },
    claimTask() {
      let vm = this;
      try {
        vm.isLoading = true;
        axios
          .post(
            Config.workflowUrl + "/task/claim/" + vm.actionTaskId,
            {
              assignee: vm.loginEmployeeId.toString(),
              status_id: "1006",
            },
            {
              headers: {
                org_code: vm.orgCode,
                employee_id: vm.loginEmployeeId,
                db_prefix: vm.domainName,
                Authorization: window.$cookies.get("accessToken")
                  ? window.$cookies.get("accessToken")
                  : "",
                refresh_token: window.$cookies.get("refreshToken")
                  ? window.$cookies.get("refreshToken")
                  : null,
                partnerid: window.$cookies.get("partnerid")
                  ? window.$cookies.get("partnerid")
                  : "-",
                additional_headers: JSON.stringify({
                  d_code: window.$cookies.get("d_code"),
                  b_code: window.$cookies.get("b_code"),
                  org_code: vm.orgCode,
                  user_ip: this.$store.state.userIpAddress,
                  Authorization: window.$cookies.get("accessToken")
                    ? window.$cookies.get("accessToken")
                    : null,
                  refresh_token: window.$cookies.get("refreshToken")
                    ? window.$cookies.get("refreshToken")
                    : null,
                  partnerid: window.$cookies.get("partnerid")
                    ? window.$cookies.get("partnerid")
                    : "-",
                }),
              },
            }
          )
          .then(() => {
            vm.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message: "Claimed successfully",
              type: "success",
            };
            vm.$emit("on-status-update");
            vm.showAlert(snackbarData);
            vm.onCloseWarningModal();
          })
          .catch(function (e) {
            vm.handleClaimError(e);
          });
      } catch {
        vm.handleClaimError();
      }
    },
    handleClaimError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message:
          "Something went wrong while claiming. Please try after some time.",
        type: "warning",
      };
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "ERR-799": // Status id not found
              snackbarData.message =
                "Unable to claim the record as the status is invalid. If you continue to see this issue please contact the platform administrator.";
              this.$emit("on-status-update");
              break;
            case "ERR-753": // task id not found
            case "ERR-754": // task already claimed
              snackbarData.message =
                "Unable to claim the record as it was already claimed in same or some other user session.";
              this.$emit("on-status-update");
              break;
            case "ERR-798": // Error while executing the query
            default:
              "Something went wrong while claiming the record. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        }
      }
      this.showAlert(snackbarData);
      this.onCloseWarningModal();
    },
    openDynamicForm(
      resignationId,
      formId,
      taskId,
      processInstanceId,
      taskAction
    ) {
      let vm = this;
      vm.resignationId = resignationId;
      vm.taskId = taskId;
      vm.processInstanceId = processInstanceId;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_DYNAMIC_FORM_DETAILS,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
            },
            dynamicFormId: parseInt(formId),
            workflowTaskId: taskId,
          },
          client: "apolloClientZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getWorkflowTaskDynamicFormDetails &&
            response.data.getWorkflowTaskDynamicFormDetails.result
          ) {
            const { dynamicFormTemplates, dynamicFormResponse } =
              response.data.getWorkflowTaskDynamicFormDetails.result;
            let formJson = "";
            if (dynamicFormResponse) {
              // response of the form -- empty if it is not submitted before, or the previous response is returned
              formJson = dynamicFormResponse.formResponse;
            } else {
              // form template for new data
              formJson = dynamicFormTemplates.template;
            }
            vm.formJsonData = JSON.parse(formJson);
            vm.conversationalId = dynamicFormTemplates.conversational;
            vm.formResponseId = dynamicFormResponse
              ? parseInt(dynamicFormResponse.formResponseId)
              : null;
            vm.openFormRender = true;
            vm.showApproval = taskAction ? true : false;
            vm.isLoading = false;
          } else {
            vm.handleFormRetrieveError();
          }
        })
        .catch(() => {
          vm.handleFormRetrieveError();
        });
    },
    handleFormRetrieveError() {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message:
          "Something went wrong while retrieving the form details. Please try after some time.",
      };
      this.showAlert(snackbarData);
    },
    closeFormRenderForm() {
      this.openFormRender = false;
    },
    handleFormUpdateSuccess() {
      this.openFormRender = false;
      this.approveTask(this.taskId, "formSubmission");
    },
    async retrieveFileContent(
      fileName,
      selectedItemFiles,
      isInitialCall = false
    ) {
      let vm = this;
      if (isInitialCall) {
        vm.isLoading = true;
        vm.fileNumber = 1;
        vm.isPdf = false;
        vm.iframeSrc = "";
        vm.imgSrc = "";
        vm.selectedItemFiles = [];

        // For form ID 34, we know there's only one file, so we handle it differently
        if (this.selectedFormId === 34 && fileName) {
          // Just add the single file to the array
          vm.selectedItemFiles.push(fileName);
        } else {
          // For other forms with potentially multiple files
          for (let files in selectedItemFiles) {
            vm.selectedItemFiles.push(selectedItemFiles[files]);
          }
        }
      } else {
        vm.selectedItemFiles = selectedItemFiles;
      }
      let folderName = "";
      if (this.selectedFormId === 244 || this.selectedFormId === 301) {
        folderName = "PreApproval Request";
      } else if (this.selectedFormId === 34) {
        folderName = "Resignation Documents";
      } else {
        folderName = "Leave Documents";
      }
      vm.isFetchingFiles = true;
      let uploadUrl =
        vm.domainName + "/" + vm.orgCode + "/" + folderName + "/" + fileName;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: uploadUrl,
          action: "view",
          type: "documents",
          destinationBucket: "",
          destinationFileKey: "",
        })
        .then((presignedUrl) => {
          vm.openModal = true;
          if (fileName.includes("pdf")) {
            vm.isPdf = true;
            vm.downloadLink = presignedUrl;
            // var fileFormatted = encodeURIComponent(presignedUrl);
            vm.iframeSrc = presignedUrl;
            vm.imgSrc = "";
          } else {
            vm.isPdf = false;
            vm.imgSrc = presignedUrl;
            vm.iframeSrc = "";
          }
          vm.isLoading = false;
          vm.isFetchingFiles = false;
        })
        .catch(() => {
          vm.isLoading = false;
          vm.isFetchingFiles = false;
        });
    },

    viewNextFile() {
      this.fileNumber += 1;
      let currentFileName = this.selectedItemFiles[this.fileNumber - 1];
      this.retrieveFileContent(currentFileName, this.selectedItemFiles);
    },

    viewPrevFile() {
      this.fileNumber -= 1;
      let currentFileName = this.selectedItemFiles[this.fileNumber - 1];
      this.retrieveFileContent(currentFileName, this.selectedItemFiles);
    },

    downloadImage(fileName) {
      fs.saveAs(this.imgSrc, this.formattedFileName(fileName));
    },

    downloadFile() {
      window.open(this.downloadLink, "_blank");
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
    },
    presentEmployeeDetailsTabName(item) {
      const items = item?.instanceData?.Request_Items;
      let changedData = [];
      if (items) {
        for (let i = 0; i < items.length; i++) {
          if (items[i].Table_Name?.toLowerCase() === "emp_personal_info")
            changedData.push("Personal Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_job")
            changedData.push("Job Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_dependent")
            changedData.push("Dependent Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_passport")
            changedData.push("Passport Details");
          else if (items[i].Table_Name?.toLowerCase() === "contact_details")
            changedData.push("Contact Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_drivinglicense")
            changedData.push("License Details");
          else changedData.push("-");
        }
      }
      //Remove duplicates
      changedData = [...new Set(changedData)];
      return changedData?.join(", ");
    },

    exportReportFile() {
      let approvalList = this.itemList;
      const resultArray = approvalList.map((item) => {
        const newItem = { ...item };
        for (const key in item.moreDetails) {
          newItem[key] =
            item.moreDetails[key] === "-" ? "" : item.moreDetails[key];
        }
        newItem["progressLevel"] =
          item.completedTaskCount + "/" + item.totalTaskCount;
        newItem["docCount"] =
          item.fileNames && Object.keys(item.fileNames).length > 0
            ? Object.keys(item.fileNames).length
            : "";
        return newItem;
      });
      let fileName = this.approvalType + "(" + this.selectedFormName + ")";
      let exportHeaders = [];
      if (this.selectedFormId === 15) {
        exportHeaders.push({ header: "Job Post Name", key: "jobTitle" });
      } else {
        exportHeaders.push(
          {
            header: "Employee Id",
            key: "userDefinedEmpId",
          },
          {
            header: "Employee Name",
            key: "employeeName",
          },
          {
            header: "Designation",
            key: "designation",
          },
          {
            header: "Department",
            key: "department",
          },
          {
            header: "Employee Type",
            key: "empType",
          },
          {
            header: "Location",
            key: "location",
          }
        );
      }
      if (this.isOutstandingApproval || this.isApprovalHistory) {
        exportHeaders.push({
          header: "Approver",
          key: "approver",
        });
      } else {
        exportHeaders.push({
          header: "Title",
          key: "description",
        });
      }
      if (this.selectedFormId === 31) {
        exportHeaders.push(
          { header: "Leave Type", key: "leaveType" },
          { header: "Start Date", key: "startDate" },
          { header: "End Date", key: "endDate" },
          { header: "Total Days", key: "totalDays" },
          { header: "Duration", key: "duration" },
          { header: "Leave Period", key: "leavePeriod" },
          { header: "Hours", key: "hours" },
          { header: "Reason", key: "reason" },
          { header: "Reason Type", key: "reasonType" },
          { header: "Alternate Person", key: "alternatePerson" },
          { header: "Late Arrival", key: "lateAttendance" }
        );
        if (!this.lateAttendanceNoOnlySelected) {
          exportHeaders.push({
            header: "Late Arrival Hours",
            key: "lateAttendanceHours",
          });
        }
        exportHeaders.push(
          { header: "Early Checkout", key: "earlyCheckout" },
          { header: "Early Checkout Hours", key: "earlyCheckoutHours" },
          {
            header: "Early Checkout Hours From Grace",
            key: "earlyCheckoutHoursFromGrace",
          },
          { header: "Attendance Shortage", key: "attendanceShortage" },
          { header: "Auto Loss of Pay", key: "autoLOP" }
        );
        exportHeaders.push(
          { header: "Self Apply", key: "selfApply" },
          { header: "Comment", key: "comment" },
          { header: "Leave Status", key: "leaveStatus" },
          { header: "Document", key: "docCount" },
          { header: "Progress", key: "progressLevel" }
        );
      } else if (this.selectedFormId === 34) {
        exportHeaders.push(
          { header: "Applied Date", key: "appliedDate" },
          { header: "Exit Date", key: "exitDate" },
          { header: "Progress", key: "progressLevel" },
          { header: "Reason Type", key: "reasonType" },
          { header: "Relieving Reason", key: "relievingReason" },
          { header: "Resignation Status", key: "resignationStatus" },
          {
            header: "Withdrawn/Cancellation Comment",
            key: "withdrawnCancellationComment",
          }
        );
      } else if (this.selectedFormId === 15) {
        exportHeaders.push(
          { header: "Posting Date", key: "postingDate" },
          { header: "Closing Date", key: "closingDate" },
          { header: "Priority", key: "priority" },
          { header: "Progress", key: "progressLevel" },
          { header: "Job Description", key: "jobDescription" },
          { header: "No Of Vacancies", key: "noOfVacancies" },
          { header: "Reason For Opening", key: "reasonForOpening" },
          { header: "Job Duration", key: "jobDuration" },
          { header: "Expected Joining Date", key: "expectedJoiningDate" },
          { header: "Min Work Experience", key: "minWorkExperience" },
          { header: "Max Work Experience", key: "maxWorkExperience" },
          { header: "Min Payment Frequency", key: "minPaymentFrequency" },
          { header: "Max Payment Frequency", key: "maxPaymentFrequency" },
          { header: "Agency Involved", key: "agencyInvolved" },
          { header: "Cooling Period", key: "coolingPeriod" },
          { header: "Travel Required", key: "travelRequired" }
        );
      } else if (this.selectedFormId === 253) {
        exportHeaders.push(
          {
            header: "LOP Recovery Processing Month",
            key: "lopRecoveryProcessingMonth",
          },
          { header: "Total Days", key: "totalDays" },
          { header: "Total Amount", key: "totalAmount" },
          { header: "Deduction Month", key: "deductionMonth" },
          { header: "Recovery Date", key: "recoveryDateIndividual" },
          { header: "Leave Type", key: "leavesIndividual" },
          { header: "Duration", key: "durationIndividual" },
          { header: "Period", key: "periodIndividual" },
          { header: "Reason", key: "reasonIndividual" },
          { header: "Per Day Salary", key: "perDaySalary" },
          { header: "Remarks", key: "remarks" },
          { header: "Status", key: "status" },
          { header: "Progress", key: "progressLevel" }
        );
      } else if (this.selectedFormId === 267) {
        exportHeaders.push(
          {
            header: "Total Applied Amount",
            key: "totalAppliedAmount",
          },
          {
            header: "Total Approved Amount",
            key: "totalAmount",
          },
          { header: "Applied Date", key: "appliedDate" },
          { header: "Status", key: "status" },
          { header: "Progress", key: "progressLevel" }
        );
      } else if (this.selectedFormId === 268) {
        exportHeaders.push(
          {
            header: "Total Hours",
            key: "totalEffort",
          },
          { header: "Weekend Date", key: "weekendDate" },
          { header: "Status", key: "status" },
          { header: "Progress", key: "progressLevel" }
        );
      } else if (this.selectedFormId === 352) {
        exportHeaders.push(
          { header: "Request For", key: "requestFor" },
          { header: "Reason", key: "reason" },
          { header: "Start Date & Time", key: "startDateTime" },
          { header: "End Date & Time", key: "endDateTime" },
          { header: "Total Hours", key: "totalHours" },
          { header: "Alternate Person", key: "alternatePerson" },
          { header: "Late Arrival", key: "lateAttendance" },
          { header: "Early Checkout", key: "earlyCheckout" },
          { header: "Early Checkout Hours", key: "earlyCheckoutHours" },
          { header: "Status", key: "approvalStatus" },
          { header: "Progress", key: "progressLevel" }
        );
      } else if (this.selectedFormId === 334) {
        exportHeaders.push(
          { header: "Worked Date", key: "workedDate" },
          { header: "Duration", key: "duration" },
          { header: "Period", key: "period" },
          { header: "Compensatory Off Date", key: "compOffDate" },
          { header: "Status", key: "status" },
          { header: "Reason", key: "reason" },
          { header: "Progress", key: "progressLevel" }
        );
      } else if (this.selectedFormId === 18 || this.selectedFormId === 243) {
        exportHeaders.push(
          { header: "Work Schedule", key: "workSchedule" },
          { header: "Progress", key: "progressLevel" }
        );
      } else if (this.selectedFormId === 360) {
        exportHeaders.push(
          { header: "Bussiness Unit", key: "businessUnit" },
          {
            header: `${this.labelList[115]?.Field_Alias || "Service Provider"}`,
            key: "serviceProvider",
          },
          { header: "Work Schedule", key: "workSchedule" },
          { header: "Description", key: "description" },
          { header: "Progress", key: "progressLevel" }
        );
      } else {
        exportHeaders.push(
          { header: "Start Date", key: "startDate" },
          { header: "End Date", key: "endDate" },
          { header: "Total Days", key: "totalDays" },
          { header: "Progress", key: "progressLevel" },
          { header: "Duration", key: "duration" },
          { header: "Period", key: "period" },
          { header: "Reason", key: "reason" },
          { header: "Status", key: "status" }
        );
      }
      exportHeaders.push(
        { header: "Added By", key: "addedBy" },
        { header: "Added On", key: "addedOn" }
      );
      if (this.selectedFormId !== 267 && this.selectedFormId !== 268) {
        exportHeaders.push(
          { header: "Updated By", key: "updatedBy" },
          { header: "Updated On", key: "updatedOn" }
        );
      }
      if (
        this.isApprovalHistory &&
        (this.selectedFormId === 31 || this.selectedFormId === 34)
      ) {
        exportHeaders.push({
          header: "Action Performed On",
          key: "approvedOn",
        });
      }
      if (this.selectedFormId === 253) {
        this.exportLOPApprovals(exportHeaders, resultArray);
      } else {
        let exportOptions = {
          fileExportData: resultArray,
          fileName: fileName,
          sheetName: fileName,
          header: exportHeaders,
        };
        this.exportExcelFile(exportOptions);
      }
    },

    exportLOPApprovals(headers, exportArray) {
      let originalArray = exportArray;
      let newArray = [];
      originalArray.forEach((item) => {
        let dates = item.moreDetails.recoveryDate
          ? item.moreDetails.recoveryDate.split(",").map((date) => date.trim())
          : [1];
        let durations = item.instanceData.Duration
          ? item.instanceData.Duration.split(",").map((duration) =>
              duration.trim()
            )
          : null;
        let periods = item.instanceData.Period
          ? item.instanceData.Period.split(",").map((period) => period.trim())
          : null;
        let leaves = item.instanceData.Leave_Name
          ? item.instanceData.Leave_Name.split(",").map((leave) => leave.trim())
          : null;
        let reasons = item.instanceData.Reason
          ? item.instanceData.Reason.split(",").map((reason) => reason.trim())
          : null;

        dates.forEach((date, index) => {
          newArray.push({
            ...item,
            recoveryDateIndividual: date,
            durationIndividual: durations ? durations[index] : "",
            periodIndividual: periods ? periods[index] : "",
            leavesIndividual: leaves ? leaves[index] : "",
            reasonIndividual: reasons ? reasons[index] : "",
          });
        });
      });
      let fileName = this.approvalType + "(" + this.selectedFormName + ")";
      let exportOptions = {
        fileExportData: newArray,
        fileName: fileName,
        sheetName: fileName,
        header: headers,
      };
      this.exportExcelFile(exportOptions);
    },

    loadAuthToken() {
      this.irukkaIdToken = window.$cookies.get("irukkaAuthToken");
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    async fetchIntegrationStatus() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 184,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            this.integrationData =
              response.data.jobBoardIntegrationStatus.getStatus;
          }
        })
        .catch((err) => {
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "integration status",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    onMenuToggle(val, item) {
      if (val) {
        this.currentJobDescription = item?.moreDetails?.jobDescription;
      }
      this.$nextTick(() => {
        this.initQuillEditor(val, this.currentJobDescription);
      });
    },
    convertEmojiCodepointsToEmojis(text) {
      return text.replace(/\[EMOJI:([0-9a-f-]+)\]/gi, (match, codePoints) => {
        // Split by dash if there are multiple code points
        const codePointArray = codePoints.split("-");

        // Convert each hex code point back to a character and join them
        const emoji = codePointArray
          .map((hex) => String.fromCodePoint(parseInt(hex, 16)))
          .join("");

        return emoji;
      });
    },
    getStatusByType(integrationType) {
      if (this.integrationData && this.integrationData.length) {
        // Find the object with the specified Integration_Type
        const integration = this.integrationData.find(
          (item) =>
            item.Integration_Type.toLowerCase() ===
            integrationType.toLowerCase()
        );

        // If the integration is found, return its Integration_Status
        if (integration) {
          return integration.Integration_Status;
        } else {
          return null;
        }
      } else {
        return null;
      }
    },
    convertToMonth(value) {
      if (!value) return "-";
      return moment(value, "M,YYYY").isValid()
        ? moment(value, "M,YYYY").format("MMM YYYY")
        : null;
    },
  },
});
</script>

<style scoped>
.claim-override-color {
  color: #e64a19;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 3.5em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}

.late-attendance-border-color {
  border-left: 7px solid #d4e157;
}
.early-checkout-border-color {
  border-left: 7px solid #ffa726;
}
.attendance-shortage-border-color {
  border-left: 7px solid #ef5350;
}
.attendance-finalization-border-color {
  border-left: 7px solid #64b5f6;
}

.rejected-border-color {
  border-left: 7px solid #f44336;
}
.document-chip {
  position: relative;
  top: -5px;
  left: 0px;
}
.approved-border-color {
  border-left: 7px solid #4caf50;
}

.selected-item-border-color {
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

.late-attendance-box {
  width: 20px;
  height: 20px;
  border: 1px solid #9e9d24;
  background: #d4e157;
}

.early-checkout-box {
  width: 20px;
  height: 20px;
  border: 1px solid #ff6f00;
  background: #ffa726;
}

.attendance-shortage-box {
  width: 20px;
  height: 20px;
  border: 1px solid #d32f2f;
  background: #ef5350;
}

.attendance-finalization-box {
  width: 20px;
  height: 20px;
  border: 1px solid #1976d2;
  background: #2196f3;
}

.v-selection-control__input > .v-icon {
  font-size: 20px !important;
}

/*  style to place the irukka signIn form in the middle of the page */
::v-deep.v-dialog > .v-overlay__content > .v-card {
  align-self: center !important;
}

.same-as-vue-tel > input:focus {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
}
.quill-editorView {
  height: auto;
}
::v-deep .ql-toolbar.ql-snow {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
::v-deep .ql-editor {
  padding: 0px;
}
</style>
