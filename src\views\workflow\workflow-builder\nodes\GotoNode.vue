<template>
  <Handle type="target" :position="targetPosition"> </Handle>
  <div class="goto_node">
    <div
      style="
        display: flex;
        align-items: center;
        position: relative;
        padding-bottom: 5px;
      "
    >
      <div
        style="
          background-color: #ffe47a;
          height: 16px;
          width: 18px;
          display: flex;
          border-radius: 5px;
          justify-content: center;
          align-items: center;
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          height="7"
          width="7"
          fill="#ff7f00"
        >
          <path
            d="M125.7 160H176c17.7 0 32 14.3 32 32s-14.3 32-32 32H48c-17.7 0-32-14.3-32-32V64c0-17.7 14.3-32 32-32s32 14.3 32 32v51.2L97.6 97.6c87.5-87.5 229.3-87.5 316.8 0s87.5 229.3 0 316.8s-229.3 87.5-316.8 0c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0c62.5 62.5 163.8 62.5 226.3 0s62.5-163.8 0-226.3s-163.8-62.5-226.3 0L125.7 160z"
          />
        </svg>
      </div>

      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
        "
      >
        <span class="d-flex item-center header_text text-center">Goto</span>
        <div class="close_icon" @click="confirmationModel = true">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="#ffffff"
            height="7"
            width="7"
            viewBox="0 0 384 512"
          >
            <path
              d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            />
          </svg>
        </div>
      </div>
    </div>
    <v-sheet v-if="nodeData.length">
      <div id="custom-input_goto">
        <CustomSelect
          v-model="selectedOption"
          :itemSelected="selectedOption"
          :items="nodeData"
          item-title="name"
          item-value="id"
          :isAutoComplete="true"
          @selected-item="(e) => onSelectOption(e)"
          variant="solo"
          transition="scale-transition"
          density="compact"
          listWidth="100px"
        >
        </CustomSelect>
      </div>
    </v-sheet>
  </div>
  <AppWarningModal
    v-if="confirmationModel"
    :open-modal="confirmationModel"
    iconName="fas fa-trash"
    confirmation-heading="Are you sure to delete?"
    @close-warning-modal="confirmationModel = false"
    @accept-modal="deleteNode()"
  >
  </AppWarningModal>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { Position, Handle } from "@vue-flow/core";
export default {
  name: "GotoNode",
  emits: ["handleToStart", "deleteNode", "gotoNodes"],
  props: {
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    sourcePosition: {
      type: String,
      required: true,
    },
    targetPosition: {
      type: String,
      required: true,
    },
    nodesData: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      leftPosition: Position.Top,
      rightPosition: Position.Bottom,
      showMenu: false,
      addNewNode: false,
      nodeData: [],
      selectedOption: "",
      confirmationModel: false,
      selectedNodeModal: false,
    };
  },
  methods: {
    onSelectOption(option) {
      this.selectedOption = option;
      this.$emit("gotoNodes", { id: this.data.id, goto: option });
    },
    handleToStartNode(type) {
      this.$emit("handleToStart", type, this.data, false, 0);
    },
    deleteNode() {
      this.confirmationModel = false;
      this.$emit("deleteNode", this.data);
    },
  },

  mounted() {
    if (this.nodesData && this.nodesData.length) {
      let prepareData = [];
      let tempNodes = [];
      this.nodesData.forEach((item) => {
        if (
          item.data.id !== "node_1" &&
          item.type !== "goto_node" &&
          !tempNodes.includes(item.data.id)
        ) {
          if (item.data.title !== "" && item.data.title !== undefined) {
            tempNodes.push(item.data.id);
            prepareData.push({ id: item.data.id, name: item.data.title });
          } else if (item.data?.formData?.modalTaskData?.title) {
            tempNodes.push(item.data.id);
            prepareData.push({
              id: item.data.id,
              name: item.data?.formData?.modalTaskData?.title,
            });
          }
        }
      });
      this.nodeData = prepareData;
      this.selectedOption = this.data?.nodeId ? this.data?.nodeId : "";
    }
  },
  components: {
    // MenuItems,
    CustomSelect,
    Handle,
  },
};
</script>
<style>
.goto_node {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 5px;
  min-width: 90px;
  /* box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); */
}

.goto_node:hover .close_icon {
  visibility: visible !important;
}

.goto_node::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  /* background-color: #ffa419; */
  background: linear-gradient(to right, #ff7f00, #ffcd00);
  height: 4px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.goto_text {
  font-size: 8px;
  font-weight: 300;
  font-family: "Poppins";
  text-transform: capitalize;
  margin-left: 0.2rem;
  /* padding-bottom: 0.2rem; */
}

.select_option {
  border: 1px solid #939393 !important;
  border-radius: 5px !important;
  outline: none !important;
  padding: 0px 8px;
  margin-top: 5px;
}
.select_option:focus {
  border: 1px solid #969bfa !important;
}
#custom-input_goto .v-field__field,
#custom-input_goto .v-field__input {
  max-width: 80px !important;
  padding: 0px 0px 0px 6px !important;
  min-height: 10px;
  font-size: 9px;
}
</style>
