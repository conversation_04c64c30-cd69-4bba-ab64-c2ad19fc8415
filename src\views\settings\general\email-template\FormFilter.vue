<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedEmailTemplates"
              color="primary"
              label="Template Name"
              :items="templateNameList"
              item-title="templateName"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              v-model="selectedCreatedBy"
              :items="createdByList"
              label="Created By"
              item-title="createdBy"
              density="compact"
              variant="solo"
              :isAutoComplete="true"
              :select-properties="{
                clearable: true,
                closableChips: true,
              }"
              :itemSelected="selectedCreatedBy"
              @selected-item="selectedCreatedBy = $event"
            ></CustomSelect>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <CustomSelect
              v-model="selectedCategoryName"
              label="Category"
              item-title="categoryName"
              :items="categoryList"
              density="compact"
              variant="solo"
              :isAutoComplete="true"
              :select-properties="{
                clearable: true,
                closableChips: true,
              }"
              :itemSelected="selectedCategoryName"
              @selected-item="selectedCategoryName = $event"
            ></CustomSelect>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { getCustomFieldName } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
export default defineComponent({
  name: "FormFilter",
  components: {
    CustomSelect,
  },
  data: () => ({
    openFormFilter: false,
    selectedCreatedBy: null,
    selectedCategoryName: null,
    createdByList: [],
    categoryList: [],
    selectedEmailTemplates: [],
    templateNameList: [],
    fieldForce: 0,
  }),
  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  watch: {
    items() {
      this.formFilterData();
    },
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
  },
  mounted() {
    this.fnApplyFilter();
    this.formFilterData();
  },
  methods: {
    getCustomFieldName,
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.items;

      if (this.selectedEmailTemplates?.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmailTemplates.includes(item.Template_Name);
        });
      }
      if (this.selectedCreatedBy) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCreatedBy.includes(item.Added_By_Name);
        });
      }
      if (this.selectedCategoryName) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCategoryName.includes(item.Category_Name);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    formFilterData() {
      this.categoryList = [];
      for (let item of this.items) {
        if (item && item.Template_Name) {
          this.templateNameList.push({
            templateName: item.Template_Name,
          });
        }
        if (item && item.Added_By_Name) {
          this.createdByList.push({
            createdBy: item.Added_By_Name,
          });
        }
        if (item && item.Category_Name) {
          this.categoryList.push({
            categoryName: item.Category_Name,
          });
        }
      }
      this.templateNameList = this.removeDuplicatesFromArrayOfObject(
        this.templateNameList,
        "templateName"
      );
      this.createdByList = this.removeDuplicatesFromArrayOfObject(
        this.createdByList,
        "createdBy"
      );
      this.categoryList = this.removeDuplicatesFromArrayOfObject(
        this.categoryList,
        "categoryName"
      );
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    resetAllModelValues() {
      this.selectedEmailTemplates = [];
      this.selectedCreatedBy = null;
      this.selectedCategoryName = null;
      this.openFormFilter = false;
    },
    isVacancyValueDisabled() {
      if (this.selectedOption) {
        return false;
      } else {
        this.typableValue = "";
        return true;
      }
    },
  },
});
</script>
