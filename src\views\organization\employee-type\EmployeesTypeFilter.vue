<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary darken-1"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <!-- Filter fields here -->
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedEmpTypeCode"
              color="primary"
              :items="empTypeCodeList"
              label="Employee Type Code"
              :itemSelected="selectedEmpTypeCode"
              variant="solo"
              @selected-item="
                onChangeSelectField($event, 'selectedEmpTypeCode')
              "
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedEmployeeType"
              color="primary"
              :items="employeeTypeList"
              label="Employee Type"
              :itemSelected="selectedEmployeeType"
              variant="solo"
              @selected-item="
                onChangeSelectField($event, 'selectedEmployeeType')
              "
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedWorkSchedule"
              color="primary"
              :items="workScheduleList"
              label="Work Schedule"
              :itemSelected="selectedWorkSchedule"
              variant="solo"
              @selected-item="
                onChangeSelectField($event, 'selectedWorkSchedule')
              "
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedEligibleForBenefits"
              color="primary"
              :items="eligibleForBenefitsOptions"
              label="Eligible for benefits"
              :itemSelected="selectedEligibleForBenefits"
              variant="solo"
              @selected-item="
                onChangeSelectField($event, 'selectedEligibleForBenefits')
              "
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedHolidayEligibility"
              color="primary"
              :items="holidayEligibilityOptions"
              label="Holiday Eligibility"
              :itemSelected="selectedHolidayEligibility"
              variant="solo"
              @selected-item="
                onChangeSelectField($event, 'selectedHolidayEligibility')
              "
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedSalaryCalculationDays"
              color="primary"
              :items="salaryCalculationDaysOptions"
              label="Salary Calculation Days"
              :itemSelected="selectedSalaryCalculationDays"
              variant="solo"
              @selected-item="
                onChangeSelectField($event, 'selectedSalaryCalculationDays')
              "
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="statusOptions"
              label="Status"
              :itemSelected="selectedStatus"
              variant="solo"
              @selected-item="onChangeSelectField($event, 'selectedStatus')"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click="fnApplyFilter"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "EmployeeTypeFilter",
  props: {
    originalList: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openFormFilter: false,
      selectedEmpTypeCode: [],
      selectedEmployeeType: [],
      selectedWorkSchedule: [],
      selectedEligibleForBenefits: [],
      selectedHolidayEligibility: [],
      selectedSalaryCalculationDays: [],
      selectedStatus: ["Active"],
      filterItemList: [],
      empTypeCodeList: [],
      employeeTypeList: [],
      holidayEligibilityList: [],
      excludeBreakHoursList: [],
      processedBiometricAttendanceRecordStatusList: [],
      statusList: [],
      workScheduleOptions: ["Employee Level", "Shift Roster"],
      eligibleForBenefitsOptions: ["Yes", "No"],
      holidayEligibilityOptions: ["Yes", "No"],
      statusOptions: ["Active", "InActive"],
      salaryCalculationDaysOptions: [
        "All Days of Salary Month",
        "Average Days in a Month",
        "Business Working Days",
        "Fixed Days of Salary Month",
      ],
    };
  },
  watch: {
    originalList() {
      this.formFilterData();
      this.filterItemList = this.originalList;
    },
  },
  mounted() {
    this.filterItemList = this.originalList;
    this.formFilterData();
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  methods: {
    onChangeSelectField(val, field) {
      this[field] = val;
    },
    fnApplyFilter() {
      let filteredArray = this.filterItemList;
      if (this.selectedEmployeeType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmployeeType.includes(item.Employee_Type);
        });
      }
      if (
        this.selectedEmpTypeCode &&
        this.selectedEmpTypeCode.length &&
        this.selectedEmpTypeCode.length > 0
      ) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmpTypeCode.includes(item.Employee_Type_Code);
        });
      }
      if (this.selectedWorkSchedule.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedWorkSchedule.includes(item.Work_Schedule);
        });
      }
      if (
        this.selectedEligibleForBenefits &&
        this.selectedEligibleForBenefits.length
      ) {
        let tempList = [];
        this.selectedEligibleForBenefits.forEach((list) => {
          if (list.toLowerCase() === "yes") {
            tempList.push(1);
          } else {
            tempList.push(0);
          }
        });
        filteredArray = filteredArray.filter((item) => {
          return tempList.includes(item.Benefits_Applicable);
        });
      }
      if (
        this.selectedHolidayEligibility &&
        this.selectedHolidayEligibility.length
      ) {
        let tempList = [];
        this.selectedHolidayEligibility.forEach((list) => {
          if (list.toLowerCase() === "yes") {
            tempList.push(1);
          } else {
            tempList.push(0);
          }
        });
        filteredArray = filteredArray.filter((item) => {
          return tempList.includes(item.Holiday_Eligiblity);
        });
      }
      if (this.selectedSalaryCalculationDays.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedSalaryCalculationDays.includes(
            item.Salary_Calc_Days_Flag
          );
        });
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.EmployeeType_Status);
        });
      }
      this.openFormFilter = false;
      this.$emit("apply-filter", filteredArray);
    },
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedEmpTypeCode = [];
      this.selectedEmployeeType = [];
      this.selectedWorkSchedule = [];
      this.selectedEligibleForBenefits = [];
      this.selectedWorkSchedule = [];
      this.selectedHolidayEligibility = [];
      this.selectedSalaryCalculationDays = [];
      this.selectedStatus = ["Active"];
    },
    formFilterData() {
      const empTypeCodeSet = new Set();
      const employeeTypeSet = new Set();
      const workScheduleSet = new Set();
      const eligibleForBenefitsSet = new Set();
      const holidayEligibilitySet = new Set();
      const salaryCalculationDaysSet = new Set();
      const statusSet = new Set();

      for (let item of this.originalList) {
        if (item && item.Employee_Type_Code) {
          empTypeCodeSet.add(item.Employee_Type_Code);
        }
        if (item && item.Employee_Type) {
          employeeTypeSet.add(item.Employee_Type);
        }
        if (item && item.Work_Schedule) {
          workScheduleSet.add(item.Work_Schedule);
        }
        if (item && item.Benefits_Applicable) {
          eligibleForBenefitsSet.add(item.Benefits_Applicable);
        }
        if (item && item.Holiday_Eligiblity) {
          holidayEligibilitySet.add(item.Holiday_Eligiblity);
        }
        if (item && item.Salary_Calculation_Days) {
          salaryCalculationDaysSet.add(item.Salary_Calculation_Days);
        }
        if (item && item.EmployeeType_Status) {
          statusSet.add(item.EmployeeType_Status);
        }
      }

      this.empTypeCodeList = Array.from(empTypeCodeSet);
      this.employeeTypeList = Array.from(employeeTypeSet);
      this.workScheduleList = Array.from(workScheduleSet);
      this.eligibleForBenefitsList = Array.from(eligibleForBenefitsSet);
      this.holidayEligibilityList = Array.from(holidayEligibilitySet);
      this.salaryCalculationDaysList = Array.from(salaryCalculationDaysSet);
      this.statusList = Array.from(statusSet);
    },
  },
});
</script>
