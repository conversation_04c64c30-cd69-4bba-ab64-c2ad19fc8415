<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs('error')"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else>
    <div v-if="!openedEditForm" class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs('refresh')" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <JobDetails
      ref="jobDetails"
      v-if="!openedEditForm || openedEditForm == 'jobDetails'"
      :jobDetailsData="jobDetailsData"
      :formAccess="formAccess"
      :selectedEmpStatus="selectedEmpStatus"
      :selectedEmployeeDob="selectedEmployeeDob"
      :actionType="actionType"
      :selectedEmpId="selectedEmpId"
      :selectedEmpDoj="selectedEmpDoj"
      :selectedEmployeeDetails="selectedEmployeeDetails"
      @refetch-job-details="refetchAPIs('update')"
      @edit-opened="openedEditForm = 'jobDetails'"
      @edit-closed="openedEditForm = ''"
    />
    <ExperienceDetails
      v-if="!openedEditForm"
      :experienceDetailsData="experienceDetailsData"
      :formAccess="selectedEmpStatus === 'Active' ? formAccess : false"
      :selectedEmpId="selectedEmpId"
      :selectedEmployeeDob="selectedEmployeeDob"
      :selectedEmpDoj="selectedEmpDoj"
      :actionType="actionType"
      :empFormUpdateAccess="empFormUpdateAccess"
      @refetch-job-details="refetchAPIs('update')"
    ></ExperienceDetails>
    <AssetDetails
      v-if="!openedEditForm"
      :assetDetailsData="assetDetailsData"
      :formAccess="formAccess"
      :selectedEmpId="selectedEmpId"
      :selectedEmpDoj="selectedEmpDoj"
      :actionType="actionType"
      :selectedEmpStatus="selectedEmpStatus"
      :empFormUpdateAccess="empFormUpdateAccess"
      @refetch-job-details="refetchAPIs('update')"
    ></AssetDetails>
  </div>
</template>
<script>
import JobDetails from "./details/JobDetails.vue";
import { defineAsyncComponent } from "vue";
const ExperienceDetails = defineAsyncComponent(() =>
  import("./experience-details/ExperienceDetails.vue")
);
const AssetDetails = defineAsyncComponent(() =>
  import("./asset-details/AssetDetails.vue")
);
import { RETRIEVE_EMP_JOB_INFO } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "JobInfo",
  components: {
    JobDetails,
    ExperienceDetails,
    AssetDetails,
  },
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmployeeDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    myTeamList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["details-retrieved", "details-updated"],
  data() {
    return {
      openedEditForm: "",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      jobDetailsData: [],
      experienceDetailsData: [],
      assetDetailsData: [],
      selectedEmpDoj: "",
    };
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.selectedEmpId || this.callingFrom === "profile") {
      this.getJobDetails();
    } else {
      this.openedEditForm = "jobDetails";
    }
  },
  methods: {
    refetchAPIs(type) {
      this.openedEditForm = "";
      this.isErrorInList = false;
      mixpanel.track("EmpProfile-job-refetch");
      if (type === "update") {
        this.$emit("details-updated");
      }
      this.getJobDetails(type);
    },
    getJobDetails(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_JOB_INFO,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-job-fetch-success");
          if (response && response.data && response.data.retrieveJobInfo) {
            const { jobInfoDetails, experienceDetails, assetDetails } =
              response.data.retrieveJobInfo;
            vm.jobDetailsData = jobInfoDetails
              ? JSON.parse(jobInfoDetails)
              : [];
            if (vm.jobDetailsData.length > 0) {
              vm.selectedEmpDoj = vm.jobDetailsData[0].Date_Of_Join;
              let managerId = vm.jobDetailsData[0].Manager_Id;
              let filteredEmpDetails = vm.myTeamList.filter(
                (el) => el.employeeId == managerId
              );
              if (
                filteredEmpDetails &&
                filteredEmpDetails.length > 0 &&
                (filteredEmpDetails[0].empStatus === "InActive" ||
                  parseInt(filteredEmpDetails[0].isManager) == 0 ||
                  filteredEmpDetails[0].isManager == "No")
              ) {
                vm.jobDetailsData[0].Manager_Id = managerId;
                vm.jobDetailsData[0].Manager_Name = "";
              }
            }
            vm.experienceDetailsData = experienceDetails
              ? JSON.parse(experienceDetails)
              : [];
            vm.assetDetailsData = assetDetails ? JSON.parse(assetDetails) : [];
            vm.$emit("details-retrieved", [type, vm.jobDetailsData[0]]);
            if (type == "update") {
              vm.openedEditForm = "";
            } else if (!vm.selectedEmpDoj) {
              vm.openedEditForm = "jobDetails";
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("EmpProfile-job-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "job details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
.bottom-navigation :deep() .v-bottom-navigation__content {
  background-color: white;
  justify-content: flex-start !important;
  align-items: center !important;
}
.bottom-navigation :deep() .v-bottom-navigation__content > .v-btn {
  font-size: inherit;
  height: 45px;
  max-width: 120px;
  min-width: 100px;
  font-size: 1.2rem;
  text-transform: none;
  transition: inherit;
  width: auto;
  border-radius: 0;
  margin-left: 20px !important;
}
</style>
