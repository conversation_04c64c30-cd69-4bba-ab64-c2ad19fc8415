<template>
  <div v-if="showCareerPageDetails">
    <CareerPageDetails
      :jobDetails="wholeJobPostData"
      :orgName="orgName"
      @close-details-form="closeAllForms($event)"
    />
  </div>
  <div v-else>
    <div>
      <v-row style="margin: 1% 5% 1%">
        <p class="text-h6 text-primary font-weight-bold">{{ orgName }}</p>
      </v-row>
      <v-row>
        <div
          class="align-items-center d-flex flex-column h-500 justify-content-center w-100"
          style="
            height: 50vh;
            width: 100%;
            background-image: url('https://cdn.kekastatic.net/shared/assets/images/components/careers/banner.jpg');
            background-size: cover;
            background-position: center;
            position: relative;
            text-align: center;
          "
        >
          <div
            class="text-align-center h-100 d-flex align-center justify-center ma-4"
            style="z-index: 1"
          >
            <div>
              <p class="text-h6">
                {{
                  careerPageCaption
                    ? careerPageCaption
                    : "Chart your course, explore opportunities and set sail for success with us."
                }}
              </p>
              <v-btn
                variant="elevated"
                class="mt-7 text-button"
                color="primary"
                rounded="lg"
                @click="scrollToNextSection"
              >
                <span class="d-flex align-self-center">Browse all jobs</span>
              </v-btn>
            </div>
          </div>
        </div>
      </v-row>
      <v-row style="margin-top: 2%" justify-md="center">
        <v-col cols="12" md="3" sm="12" class="sticky-column d-md-block">
          <!-- The Filter section -->
          <v-row
            style="margin: 10px"
            class="d-flex justify-center align-center"
            cols="12"
          >
            <v-card class="filterCard">
              <v-card-text>
                <v-text-field
                  v-model="searchInput"
                  placeholder="Search"
                  ref="jobSection"
                  density="compact"
                  variant="outlined"
                  prepend-inner-icon="fas fa-search"
                  clearable
                />
                <v-autocomplete
                  v-if="
                    filtersVisible?.serviceProvider &&
                    fieldForce &&
                    labelList['115'] &&
                    labelList['115'].Field_Visiblity === 'Yes'
                  "
                  v-model="selectedServiceProvider"
                  color="primary"
                  :items="serviceProviderList"
                  :label="
                    labelList['115'] && labelList['115'].Field_Alias
                      ? labelList['115'].Field_Alias
                      : 'Service Provider'
                  "
                  item-title="serviceProviderName"
                  multiple
                  closable-chips
                  chips
                  clearable
                  density="compact"
                  single-line
                  variant="solo"
                  class="autocomplete"
                ></v-autocomplete>
                <v-autocomplete
                  v-if="filtersVisible?.department"
                  v-model="selectedDepartment"
                  color="primary"
                  :items="departmentList"
                  label="Department"
                  item-title="departmentName"
                  multiple
                  closable-chips
                  chips
                  clearable
                  density="compact"
                  single-line
                  variant="solo"
                  class="autocomplete"
                ></v-autocomplete>
                <v-autocomplete
                  v-if="filtersVisible?.location"
                  v-model="selectedLocation"
                  color="primary"
                  :items="locationList"
                  label="Location"
                  item-title="locationName"
                  multiple
                  closable-chips
                  chips
                  clearable
                  density="compact"
                  single-line
                  variant="solo"
                  class="autocomplete"
                ></v-autocomplete>
              </v-card-text>
            </v-card>
          </v-row>
        </v-col>
        <v-col cols="12" md="8" sm="12">
          <!-- The List Items -->
          <div v-if="listLoading">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 4" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else>
            <v-row
              v-if="!isJobListEmpty && isFilteredListEmpty"
              class="bg-hover"
              style="margin: 2% 0 10px"
            >
              <div class="pa-5 d-flex justify-center align-center">
                No matches found in search results!
              </div>
            </v-row>
            <v-row
              v-else-if="isJobListEmpty"
              class="bg-hover"
              style="margin: 2% 0 10px"
            >
              <div class="pa-5 d-flex justify-center align-center">
                No jobs available right now!
              </div>
            </v-row>
            <v-row v-else>
              <v-col
                v-for="(department, index) in filteredDepartments"
                :key="index"
                cols="12"
                sm="10"
                ><div
                  v-if="department.departmentName && department.jobs.length > 0"
                >
                  <div
                    v-if="filtersVisible?.department"
                    class="mb-2 d-flex align-center ml-2"
                  >
                    <span
                      class="text-primary pr-2 text-body-1 font-weight-bold"
                      >{{ department.departmentName }}</span
                    >
                    -
                    <span class="bg-hover pa-1 text-body-2 rounded-lg ml-2"
                      >{{ department.jobs.length }} jobs</span
                    >
                  </div>
                  <v-row>
                    <v-col
                      v-for="job in department.jobs"
                      :key="job.Job_Post_Id"
                      cols="12"
                    >
                      <v-card
                        class="cursor-pointer job-card ml-2"
                        @click="navigateToCareersPageDetails(job)"
                      >
                        <div>
                          <v-card-title>
                            <v-row>
                              <v-col cols="11">
                                <div
                                  class="d-flex align-center justify-space-between"
                                >
                                  <div class="text-subtitle-2 mb-2 text-wrap">
                                    <strong
                                      class="job-post-title text-primary mr-3"
                                      >{{ job.Job_Post_Name }}</strong
                                    >
                                    <span class="text-grey" variant="pill">
                                      {{
                                        getPostingDateDifference(
                                          job.Posting_Date
                                        )
                                      }}
                                      days ago
                                    </span>
                                  </div>
                                </div>
                                <div
                                  class="text-grey text-body-2 d-flex align-center"
                                >
                                  {{ job.City_Name }}
                                  <div
                                    v-if="job.Experience_Level && job.City_Name"
                                    style="
                                      background: #c4c4c4;
                                      border-radius: 50%;
                                      margin: 0px 5px;
                                      height: 10px;
                                      width: 10px;
                                    "
                                  ></div>
                                  {{ job.Experience_Level }}
                                </div>
                              </v-col>
                              <v-col
                                cols="1"
                                class="d-flex align-center justify-center"
                              >
                                <v-icon
                                  class="fas fa-location-arrow fa-sm hover-icon"
                                  color="primary"
                                  size="sm"
                                />
                              </v-col>
                            </v-row>
                          </v-card-title>
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>
              </v-col>
            </v-row>
          </div>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script>
import { POSTED_JOBS } from "@/graphql/settings/irukka-integration/jobPostFormQueries.js";
import CareerPageDetails from "./CareerPageDetails.vue";
import { CUSTOM_COLOR_PICKER } from "@/graphql/commonQueries.js";
import { GET_JOB_HEADER } from "@/graphql/recruitment/recruitmentQueries.js";
export default {
  name: "CareerPage",
  components: { CareerPageDetails },
  data() {
    return {
      jobPostList: [],
      departmentList: [],
      locationList: [],
      skillsList: [],
      qualificationList: [],
      selectedDepartment: [],
      selectedLocation: [],
      serviceProviderList: [],
      selectedServiceProvider: null,
      searchInput: "",
      careerPageCaption: "",
      orgName: "",
      fieldForce: null,
      wholeJobPostData: null,
      listLoading: false,
      showCareerPageDetails: false,
      settingResult: {},
      isLoading: false,
    };
  },
  computed: {
    filtersVisible() {
      let filters = this.settingResult?.Career_Portal_Filters
        ? JSON.parse(this.settingResult.Career_Portal_Filters)
        : {};
      return filters;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    filteredDepartments() {
      let filteredPosts = this.jobPostList;
      if (this.searchInput) {
        filteredPosts = filteredPosts.filter((job) =>
          job.Job_Post_Name.toLowerCase().includes(
            this.searchInput.toLowerCase()
          )
        );
      }
      if (this.selectedDepartment.length > 0) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedDepartment.includes(job.Department_Name)
        );
      }
      if (this.selectedLocation.length > 0) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedLocation.includes(job.Location)
        );
      }
      if (
        this.selectedServiceProvider &&
        this.selectedServiceProvider.length > 0
      ) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedServiceProvider.includes(job.Service_Provider_Name)
        );
      }
      const departments = {};
      filteredPosts.forEach((job) => {
        if (!departments[job.Department_Name]) {
          departments[job.Department_Name] = {
            departmentName: job.Department_Name,
            jobs: [],
          };
        }
        departments[job.Department_Name].jobs.push(job);
      });

      return Object.values(departments);
    },
    isJobListEmpty() {
      return this.jobPostList.length === 0;
    },
    isFilteredListEmpty() {
      return this.filteredDepartments.length === 0;
    },
  },
  mounted() {
    this.fetchPostedJobs();
    this.customColorPicker();
  },
  methods: {
    navigateToCareersPageDetails(job) {
      this.wholeJobPostData = job;
      this.showCareerPageDetails = true;
    },
    closeAllForms(refetchCount) {
      this.showCareerPageDetails = false;
      if (refetchCount > 0) {
        this.fetchPostedJobs();
      }
    },
    // Custom Color Picker Api
    customColorPicker() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: CUSTOM_COLOR_PICKER,
          client: "apolloClientAO",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.customColorPicker &&
            response.data.customColorPicker.colorResult
          ) {
            const colorResult = response.data.customColorPicker.colorResult[0];
            this.careerPageCaption = colorResult.Career_Page_Caption;
            this.orgName = colorResult.Org_Name;
            this.fieldForce = colorResult.Field_Force;
          }
        })
        .catch((err) => {
          vm.handleCustomColorPicker(err);
        })
        .finally(() => {
          this.retrieveJobHeader();
        });
    },
    handleCustomColorPicker(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "career details",
        isListError: false,
      });
    },
    retrieveJobHeader() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_JOB_HEADER,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.recruitmentSetting &&
            res.data.recruitmentSetting.settingResult &&
            res.data.recruitmentSetting.settingResult.length
          ) {
            this.settingResult = res.data.recruitmentSetting.settingResult[0];
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
    fetchPostedJobs() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: POSTED_JOBS,
          variables: {
            employeeId: vm.loginEmployeeId,
            isDropDownCall: 0,
            searchString: "",
            formId: 16,
          },
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobPost &&
            response.data.listJobPost.JobpostDetails
          ) {
            this.jobPostList = response.data.listJobPost.JobpostDetails.filter(
              (job) => job.Job_Post_Status === "Open"
            ).map((job) => ({
              ...job,
              Department_Name: job.Department_Name,
              Location: job.City_Name,
              Job_Type: job.Job_Type,
              Service_Provider_Name: job.Service_Provider_Name,
            }));

            this.departmentList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.Department_Name)
                  .filter((department) => department !== null)
              ),
            ];

            this.locationList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.City_Name)
                  .filter((location) => location !== null)
              ),
            ];

            this.serviceProviderList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.Service_Provider_Name)
                  .filter((provider) => provider !== null && provider !== 0)
              ),
            ];
            this.getJobpostId();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "career details",
        isListError: false,
      });
    },
    getPostingDateDifference(postingDate) {
      if (postingDate && postingDate !== "0000-00-00") {
        const postingDateObj = new Date(postingDate);
        const currentDate = new Date();
        const differenceInTime =
          currentDate.getTime() - postingDateObj.getTime();
        const differenceInDays = differenceInTime / (1000 * 3600 * 24);
        return Math.floor(differenceInDays);
      } else {
        return "";
      }
    },
    scrollToNextSection() {
      const jobSection = this.$refs.jobSection;
      if (jobSection) {
        const sectionPosition =
          jobSection.getBoundingClientRect().top + window.scrollY;
        window.scrollTo({
          top: sectionPosition - 35,
          behavior: "smooth",
        });
      }
    },
    getJobpostId() {
      if (this.$route.query?.jobPostId) {
        const jobPostId = parseInt(this.$route.query.jobPostId);
        let jobPostRecord = this.getJobPostById(this.jobPostList, jobPostId);
        if (jobPostRecord) {
          this.navigateToCareersPageDetails(jobPostRecord);
        }
      }
    },
    getJobPostById(jobPosts, id) {
      return jobPosts.find((post) => post.Job_Post_Id === id);
    },
  },
};
</script>

<style scoped>
.filterCard {
  width: 100%;
}
.job-card {
  border-radius: 8px;
  transition: border-color 0.3s, color 0.3s;
  border: 1px solid rgb(var(--v-theme-hover));
}

.job-card:hover,
.job-post-title:hover {
  border-color: 5px solid rgb(var(--v-theme-primary));
}

.hover-icon {
  opacity: 0;
  transition: opacity 0.3s;
}

.job-card:hover .hover-icon {
  opacity: 1;
}
.sticky-column {
  position: sticky;
  top: 10px;
  height: 100vh;
  overflow-y: auto;
}
@media screen and (max-width: 1024px) {
  .filterCard {
    border: 2px solid lightgrey;
    min-width: 200px;
  }
}
@media screen and (max-width: 960px) {
  .sticky-column {
    position: relative !important;
    top: auto;
    height: auto;
    overflow-y: visible;
    width: 100%;
  }
}
</style>
