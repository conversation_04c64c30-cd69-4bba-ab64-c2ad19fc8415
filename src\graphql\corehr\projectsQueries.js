import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_PROJECTS = gql`
  query listProjectDetails($formId: Int, $employeeId: Int) {
    listProjectDetails(formId: $formId, employeeId: $employeeId) {
      errorCode
      message
      projectCoverage
      projectDetails {
        projectId
        projectName
        description
        clientName
        locationId
        locationName
        managerId
        addedOn
        updatedOn
        addedByName
        updatedByName
        managerName
        employeeId
        employeeName
        customGroupId
        customGroupName
        status
      }
    }
  }
`;
export const RETRIEVE_PROJECT_ACTIVITIES = gql`
  query retrieveProjectActivities(
    $projectId: Int!
    $formId: Int
    $weekendDate: String
  ) {
    retrieveProjectActivities(
      projectId: $projectId
      formId: $formId
      weekendDate: $weekendDate
    ) {
      errorCode
      message
      activityDetails {
        projectId
        projectActivityId
        activityId
        activityName
        description
        isBillable
        activityFrom
        activityTo
      }
    }
  }
`;
export const RETRIEVE_MIN_MAX_DATE_FOR_ACTIVITY = gql`
  query retrieveMinMaxDateForActivity($projectActivityId: Int!) {
    retrieveMinMaxDateForActivity(projectActivityId: $projectActivityId) {
      errorCode
      message
      activityData {
        minDate
        maxDate
      }
    }
  }
`;
export const GET_TIME_SHEET_COUNT_FOR_PROJECT = gql`
  query getTimesheetCountForProject($projectId: Int!) {
    getTimesheetCountForProject(projectId: $projectId) {
      errorCode
      message
      timesheetCount
    }
  }
`;
export const EXPORT_PROJECT_DETAILS_AND_ACTIVITIES = gql`
  query exportProjectDetailsAndActivities($projectId: [Int]!) {
    exportProjectDetailsAndActivities(projectId: $projectId) {
      errorCode
      message
      projectDetailsAndActivities
      projectCoverage
    }
  }
`;
export const LIST_ROOMS = gql`
  query listRooms {
    listRooms {
      errorCode
      message
      rooms {
        Room_Id
        Room_No
        Description
        Added_On
        Added_By
        Added_By_Name
        Updated_On
        Updated_By
        Updated_By_Name
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const DELETE_PROJECTS = gql`
  mutation deleteProjectDetails($projectId: Int!) {
    deleteProjectDetails(projectId: $projectId) {
      errorCode
      message
    }
  }
`;

export const ADD_EDIT_PROJECTS = gql`
  mutation addUpdateProjectDetails(
    $projectId: Int!
    $projectName: String!
    $clientName: String!
    $managerId: Int!
    $locationId: Int!
    $status: String!
    $description: String!
    $employeeId: [Int!]
    $customGroupId: Int!
    $accreditationId: [Int!]
  ) {
    addUpdateProjectDetails(
      projectId: $projectId
      projectName: $projectName
      clientName: $clientName
      managerId: $managerId
      locationId: $locationId
      status: $status
      description: $description
      employeeId: $employeeId
      customGroupId: $customGroupId
      accreditationId: $accreditationId
    ) {
      errorCode
      message
      projectId
    }
  }
`;
export const CLONE_PROJECTS = gql`
  mutation cloneProject(
    $cloneOption: String!
    $projectId: Int!
    $projectName: String!
  ) {
    cloneProject(
      cloneOption: $cloneOption
      projectId: $projectId
      projectName: $projectName
    ) {
      errorCode
      message
      projectDetails {
        Project_Id
        Project_Name
        Description
        Client_Name
        Location_Id
        Manager_Id
        Employee_Id
        Custom_Group_Id
        Accreditation_Id
        Status
      }
    }
  }
`;
export const ADD_UPDATE_ACTIVITIES_TO_PROJECT = gql`
  mutation addUpdateActivitiesToProject(
    $projectActivityId: Int!
    $projectId: Int!
    $activityId: Int!
    $isBillable: String!
    $activityFrom: String
    $activityTo: String
    $description: String
  ) {
    addUpdateActivitiesToProject(
      projectActivityId: $projectActivityId
      projectId: $projectId
      activityId: $activityId
      activityFrom: $activityFrom
      activityTo: $activityTo
      description: $description
      isBillable: $isBillable
    ) {
      errorCode
      message
    }
  }
`;
export const DELETE_ACTIVITY_ASSOCIATTEDD_WITH_PROJECT = gql`
  mutation deleteActivityAssociatedWithProject($projectActivityId: Int!) {
    deleteActivityAssociatedWithProject(projectActivityId: $projectActivityId) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_ROOM = gql`
  mutation addUpdateRoom(
    $Room_Id: Int
    $Room_No: String!
    $Description: String
  ) {
    addUpdateRoom(
      Room_Id: $Room_Id
      Room_No: $Room_No
      Description: $Description
    ) {
      errorCode
      message
    }
  }
`;
export const DELETE_ROOM = gql`
  mutation deleteRoom($Room_Id: Int!) {
    deleteRoom(Room_Id: $Room_Id) {
      errorCode
      message
    }
  }
`;
