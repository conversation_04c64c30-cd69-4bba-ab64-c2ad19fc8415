<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <!-- <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary darken-1"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times
          </v-icon>
        </div> -->
        <v-row v-if="!showViewForm" class="mr-2 mt-2 px-2">
          <!-- Updated Filter fields here -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedEmployeeId"
              color="primary"
              :items="employeeIdList"
              label="Employee Id"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedEmployeeName"
              color="primary"
              :items="employeeNameList"
              label="Employee Name"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedDesignation"
              color="primary"
              :items="designationList"
              label="Designation"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedDepartment"
              color="primary"
              :items="departmentList"
              label="Department"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedLocation"
              color="primary"
              :items="locationList"
              label="Location"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedEmployeeType"
              color="primary"
              :items="employeeTypeList"
              label="Employee Type"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedManagerName"
              color="primary"
              :items="managerNameList"
              label="Manager Name"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col>
          <!-- <v-col
            v-if="fieldforce === 1"
            :cols="windowWidth > 900 ? 4 : 12"
            sm="4"
            class="py-2"
          >
            <v-autocomplete
              v-model="selectedServiceProvider"
              color="primary"
              :items="serviceProviderOptions"
              label="Service Provider"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
        </v-row>
        <v-row v-if="showViewForm" class="mr-2 mt-2 px-2">
          <!-- Updated Filter fields here -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedLateAttendance"
              color="primary"
              :items="lateAttendanceOptions"
              label="Late Attendance"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckInWorkPlace"
              color="primary"
              :items="checkInWorkPlaceOptions"
              label="Check In Work Place"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckOutWorkPlace"
              color="primary"
              :items="checkOutWorkPlaceOptions"
              label="Check Out Work Place"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckInFormSource"
              color="primary"
              :items="checkInFormSourceOptions"
              label="Check In Form Source"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckOutFormSource"
              color="primary"
              :items="checkOutFormSourceOptions"
              label="Check Out Form Source"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckInDataSource"
              color="primary"
              :items="checkInDataSourceOptions"
              label="Check In Data Source"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedCheckOutDataSource"
              color="primary"
              :items="checkOutDataSourceOptions"
              label="Check Out Data Source"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
          <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="statusOptions"
              label="Status"
              variant="solo"
              multiple
              closable-chips
              chips
              single-line
              :loading="loadingData"
            />
          </v-col> -->
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click="fnApplyFilter"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "AttendanceFilter",
  props: {
    originalList: {
      type: Object,
      required: true,
    },
    showViewForm: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      openFormFilter: false,
      selectedEmployeeId: [],
      selectedEmployeeName: [],
      selectedAttendanceDate: null,
      selectedDesignation: [],
      selectedDepartment: [],
      selectedLocation: [],
      selectedEmployeeType: [],
      selectedManagerName: [],
      selectedLateAttendance: [],
      selectedCheckInWorkPlace: [],
      selectedCheckOutWorkPlace: [],
      selectedCheckInFormSource: [],
      selectedCheckOutFormSource: [],
      selectedCheckInDataSource: [],
      selectedCheckOutDataSource: [],
      selectedServiceProvider: [],
      selectedStatus: [],
      filterItemList: [],
      employeeIdList: [],
      employeeNameList: [],
      designationList: [],
      departmentList: [],
      locationList: [],
      employeeTypeList: [],
      managerNameList: [],
      lateAttendanceOptions: [
        "Outside grace time(Slab)",
        "Within grace time(Slab)",
        "Outside grace time",
        "Within grace time",
        "No",
      ],
      checkInWorkPlaceOptions: [],
      checkOutWorkPlaceOptions: [],
      checkInFormSourceOptions: [],
      originalCheckInFormSourceOptions: [],
      checkOutFormSourceOptions: [],
      checkInDataSourceOptions: [],
      checkOutDataSourceOptions: [],
      serviceProviderOptions: [],
      statusOptions: ["Applied", "Approved", "Draft", "Rejected", "Returned"],
    };
  },
  watch: {
    originalList() {
      // this.formFilterData();
      this.filterItemList = this.originalList;
    },
  },
  mounted() {
    this.filterItemList = this.originalList;
    // this.formFilterData();
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  methods: {
    // onChangeDateRange(selectedDates, dateStr) {
    //   this.isExceed31Days = false;
    //   if (selectedDates.length > 1) {
    //     let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
    //     const startMoment = moment(selectedDates[0], orgDateFormat);
    //     const endMoment =
    //       selectedDates.length > 1
    //         ? moment(selectedDates[1], orgDateFormat)
    //         : moment(selectedDates[0], orgDateFormat);
    //     const diffInDays = endMoment.diff(startMoment, "days") + 1; // Adding 1 to include both the start and end dates
    //     if (diffInDays <= 31) {
    //       // Parse the dates from the given format
    //       let parsedStartDate = moment(selectedDates[0], "DD/MM/YYYY");
    //       let parsedEndDate = moment(
    //         selectedDates.length > 1 ? selectedDates[1] : selectedDates[0],
    //         "DD/MM/YYYY"
    //       );

    //       // Format the dates into "YYYY-MM-DD" format
    //       this.startDate = parsedStartDate.format("YYYY-MM-DD");
    //       this.endDate = parsedEndDate.format("YYYY-MM-DD");
    //       this.fetchChartDetails();
    //       this.getWeekWiseList();
    //       this.chartType = "Active Time";
    //     } else {
    //       this.isExceed31Days = true;
    //     }
    //   } else {
    //     this.$emit("on-change-date-range", dateStr);
    //   }
    //   if (this.isExceed31Days) {
    //     this.appliedDateRange = this.currentDateRange;
    //     let snackbarData = {
    //       isOpen: true,
    //       message: "Please select a date range of less than 31 days",
    //       type: "warning",
    //     };
    //     this.showAlert(snackbarData);
    //   }
    // },
    // showAlert(snackbarData) {
    //   this.$store.commit("OPEN_SNACKBAR", snackbarData);
    // },
    // onChangeSelectField(val, field) {
    //   this[field] = val;
    // },
    // fnApplyFilter() {
    //   let filteredArray = this.filterItemList;
    //   if (this.selectedEmployeeId.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedEmployeeId.includes(item.Employee_ID);
    //     });
    //   }

    //   if (this.selectedEmployeeName.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedEmployeeName.includes(item.Employee_Name);
    //     });
    //   }
    //   if (this.selectedAttendanceDate) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return item.Attendance_Date === this.selectedAttendanceDate;
    //     });
    //   }
    //   if (this.selectedDesignation.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedDesignation.includes(item.Designation);
    //     });
    //   }
    //   if (this.selectedDepartment.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedDepartment.includes(item.Department);
    //     });
    //   }
    //   if (this.selectedLocation.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedLocation.includes(item.Location);
    //     });
    //   }
    //   if (this.selectedEmployeeType.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedEmployeeType.includes(item.Employee_Type);
    //     });
    //   }
    //   if (this.selectedManagerName.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedManagerName.includes(item.Manager_Name);
    //     });
    //   }
    //   if (this.selectedLateAttendance.length > 0) {
    //     const tempList = this.selectedLateAttendance.map((val) =>
    //       val.toLowerCase() === "yes" ? 1 : 0
    //     );
    //     filteredArray = filteredArray.filter((item) => {
    //       return tempList.includes(item.Late_Attendance);
    //     });
    //   }
    //   if (this.selectedCheckInWorkPlace.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedCheckInWorkPlace.includes(
    //         item.Check_In_Work_Place
    //       );
    //     });
    //   }
    //   if (this.selectedCheckOutWorkPlace.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedCheckOutWorkPlace.includes(
    //         item.Check_Out_Work_Place
    //       );
    //     });
    //   }
    //   if (this.selectedCheckInFormSource.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedCheckInFormSource.includes(
    //         item.Check_In_Form_Source
    //       );
    //     });
    //   }
    //   if (this.selectedCheckOutFormSource.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedCheckOutFormSource.includes(
    //         item.Check_Out_Form_Source
    //       );
    //     });
    //   }
    //   if (this.selectedCheckInDataSource.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedCheckInDataSource.includes(
    //         item.Check_In_Data_Source
    //       );
    //     });
    //   }
    //   if (this.selectedCheckOutDataSource.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedCheckOutDataSource.includes(
    //         item.Check_Out_Data_Source
    //       );
    //     });
    //   }
    //   if (this.selectedServiceProvider.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedServiceProvider.includes(item.Service_Provider);
    //     });
    //   }
    //   if (this.selectedStatus.length > 0) {
    //     filteredArray = filteredArray.filter((item) => {
    //       return this.selectedStatus.includes(item.Status);
    //     });
    //   }

    //   this.openFormFilter = false;
    //   this.$emit("apply-filter", filteredArray);
    // },
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedEmployeeId = [];
      this.selectedEmployeeName = [];
      this.selectedAttendanceDate = null;
      this.selectedDesignation = [];
      this.selectedDepartment = [];
      this.selectedLocation = [];
      this.selectedEmployeeType = [];
      this.selectedManagerName = [];
      this.selectedLateAttendance = [];
      this.selectedCheckInWorkPlace = [];
      this.selectedCheckOutWorkPlace = [];
      this.selectedCheckInFormSource = [];
      this.selectedCheckOutFormSource = [];
      this.selectedCheckInDataSource = [];
      this.selectedCheckOutDataSource = [];
      this.selectedServiceProvider = [];
      this.selectedStatus = [];
    },
    // formFilterData() {
    //   const employeeIdSet = new Set();
    //   const employeeNameSet = new Set();
    //   const designationSet = new Set();
    //   const departmentSet = new Set();
    //   const locationSet = new Set();
    //   const employeeTypeSet = new Set();
    //   const managerNameSet = new Set();
    //   const checkInWorkPlaceSet = new Set();
    //   const checkOutWorkPlaceSet = new Set();
    //   const checkInFormSourceSet = new Set();
    //   const checkOutFormSourceSet = new Set();
    //   const serviceProviderSet = new Set();
    //   const statusSet = new Set();

    //   for (let item of this.originalList) {
    //     if (item && item.Employee_Id) employeeIdSet.add(item.Employee_Id);
    //     if (item && item.Employee_Name) employeeNameSet.add(item.Employee_Name);
    //     if (item && item.Designation) designationSet.add(item.Designation);
    //     if (item && item.Department) departmentSet.add(item.Department);
    //     if (item && item.Location) locationSet.add(item.Location);
    //     if (item && item.Employee_Type) employeeTypeSet.add(item.Employee_Type);
    //     if (item && item.Manager_Name) managerNameSet.add(item.Manager_Name);
    //     if (item && item.Check_In_Work_Place)
    //       checkInWorkPlaceSet.add(item.Check_In_Work_Place);
    //     if (item && item.Check_Out_Work_Place)
    //       checkOutWorkPlaceSet.add(item.Check_Out_Work_Place);
    //     if (item && item.Check_In_Form_Source)
    //       checkInFormSourceSet.add(item.Check_In_Form_Source);
    //     if (item && item.Check_Out_Form_Source)
    //       checkOutFormSourceSet.add(item.Check_Out_Form_Source);
    //     if (item && item.Service_Provider)
    //       serviceProviderSet.add(item.Service_Provider);
    //     if (item && item.Status) statusSet.add(item.Status);
    //   }

    //   this.employeeIdList = Array.from(employeeIdSet);
    //   this.employeeNameList = Array.from(employeeNameSet);
    //   this.designationList = Array.from(designationSet);
    //   this.departmentList = Array.from(departmentSet);
    //   this.locationList = Array.from(locationSet);
    //   this.employeeTypeList = Array.from(employeeTypeSet);
    //   this.managerNameList = Array.from(managerNameSet);
    //   this.checkInWorkPlaceList = Array.from(checkInWorkPlaceSet);
    //   this.checkOutWorkPlaceList = Array.from(checkOutWorkPlaceSet);
    //   this.checkInFormSourceList = Array.from(checkInFormSourceSet);
    //   this.checkOutFormSourceList = Array.from(checkOutFormSourceSet);
    //   this.checkInDataSourceList = Array.from(checkInFormSourceSet);
    //   this.checkOutDataSourceList = Array.from(checkOutFormSourceSet);
    //   this.serviceProviderList = Array.from(serviceProviderSet);
    //   this.statusList = Array.from(statusSet);
    // },
  },
});
</script>
