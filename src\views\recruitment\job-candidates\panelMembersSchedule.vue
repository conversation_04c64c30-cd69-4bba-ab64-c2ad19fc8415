<template>
  <div class="text-center">
    <v-overlay
      v-model="overlay"
      class="d-flex justify-end schedule-parent"
      @click:outside="onClickClose()"
      persistent
      style="z-index: 2000"
    >
      <template v-slot:default>
        <div class="panel-schedule">
          <div
            class="d-flex align-center text-h6 text-medium-emphasis pa-2 bg-primary"
            style="width: 100%"
          >
            <span class="ml-2 text-white">{{ formatDate(selectedDate) }}</span>
            <v-spacer></v-spacer>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="onClickClose()"
            ></v-btn>
          </div>
          <div class="schedule-body d-flex">
            <VueCal
              :selected-date="selectedDate"
              :hideViewSelector="true"
              :split-days="daySplit"
              :disable-views="['years', 'year', 'week', 'month']"
              :events="events"
              sticky-split-labels
              :min-split-width="400"
            >
              <template #title>
                <span class="text-subtitle-1">{{
                  formatDate(selectedDate)
                }}</span>
              </template>
              <template #event="{ event }">
                <v-tooltip>
                  <template v-slot:default>
                    <div>
                      {{ event.title }}
                      <br />
                      Start: {{ formatTime(event.start) }}
                      <br />
                      End: {{ formatTime(event.end) }}
                    </div>
                  </template>
                  <template v-slot:activator="{ props }">
                    <div
                      style="height: 100%"
                      :class="event.class"
                      class="py-1"
                      v-bind="props"
                    >
                      <div>{{ event.title }}</div>
                      <div class="mt-2 font-weight-bold">
                        {{ formatTime(event.start) }} -
                        {{ formatTime(event.end) }}
                      </div>
                    </div>
                  </template>
                </v-tooltip>
              </template>
            </VueCal>
          </div>
        </div>
      </template>
    </v-overlay>
  </div>
</template>
<script>
import VueCal from "vue-cal";
import "vue-cal/dist/vuecal.css";
import moment from "moment";
export default {
  name: "PanelMembersSchedule",
  emits: ["close"],
  components: {
    VueCal,
  },
  props: {
    selectedMemebers: {
      type: Array,
      required: true,
      default: () => [],
    },
    selectedDate: {
      type: [String, Date],
      required: true,
      default: "",
    },
  },
  data() {
    return {
      overlay: true,
      focus: "",
      events: [],
      colors: [
        "blue",
        "indigo",
        "deep-purple",
        "cyan",
        "green",
        "orange",
        "grey darken-1",
      ],
      names: [
        "Meeting",
        "Holiday",
        "PTO",
        "Travel",
        "Event",
        "Birthday",
        "Conference",
        "Party",
      ],
      today: null,
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    formatTime() {
      return (time) => {
        return time ? moment(time).format("HH:mm") : "";
      };
    },
    daySplit() {
      return this.selectedMemebers.map((item) => {
        return {
          label: item.name,
        };
      });
    },
  },
  watch: {
    selectedMemebers() {
      this.events = this.selectedMemebers.map((item, index) => {
        return item.events.map((event) => {
          return {
            ...event,
            split: index + 1,
          };
        });
      });
    },
  },
  mounted() {
    this.today = new Date(this.selectedDate);
    this.events = [];
    this.selectedMemebers.map((item, index) => {
      item.events.map((event) => {
        this.events.push({
          ...event,
          split: index + 1,
        });
      });
    });
  },
  methods: {
    onClickClose() {
      this.overlay = false;
      this.$emit("close");
    },
  },
};
</script>
<style scoped>
.panel-schedule {
  height: 100%;
  width: 100%;
  background: white;
}

.schedule-parent > .v-overlay__content {
  height: 100%;
  max-width: 50%;
}

@media only screen and (max-width: 600px) {
  .schedule-parent > .v-overlay__content {
    width: 100%;
  }
}

.schedule-body {
  padding: 15px;
  height: calc(100vh - 70px);
}

:deep(.vuecal__arrow) {
  display: none;
}
</style>
