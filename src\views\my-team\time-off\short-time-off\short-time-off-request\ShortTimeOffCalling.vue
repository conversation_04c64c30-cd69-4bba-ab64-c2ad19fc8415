<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" class="mr-4">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu class="justify-end" :isFilter="false">
              </EmployeeDefaultFilterMenu>
              <FormFilter
                ref="formFilterRef"
                :items="itemList"
                :originalList="originalList"
                :formId="352"
                :callingFrom="callingFrom"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              >
              </FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <AppAccessDenied v-else />
    <ShortTimeOffList
      ref="shortTimeOffList"
      :callingFrom="callingFrom"
      :form-access="formAccess"
      :form-id="this.callingFrom === 'myTeam' ? 352 : 353"
      :landed-form-name="landedFormName"
      :filtered-list="itemList"
      @send-list-data="updateList($event)"
      @reset-filter="resetFilter"
    />
    <AppLoading v-if="listLoading" />
    <ViewShortTimeOff
      v-if="showViewForm"
      :callingFrom="callingFrom"
      :selected-item="selectedItem"
      :calling-from="showTeam"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      @open-edit-form="openAddEditForm(true, $event)"
      @close-view-form="closeAllForms()"
    />
    <AddEditShortTimeOff
      v-if="showAddEditForm"
      :selected-item="selectedItem"
      :callingFrom="callingFrom"
      :is-edit="isEdit"
      :form-id="formId"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      @edit-updated="refetchList()"
      @close-form="closeAllForms()"
    />
    <AppWarningModal
      v-if="deleteModel"
      :open-modal="deleteModel"
      confirmation-heading="Are you sure to delete the selected record?"
      icon-name="fas fa-trash"
      icon-Size="75"
      @close-warning-modal="closeAllForms()"
      @accept-modal="onDeleteShortTimeOff()"
    />
    <ApprovalFlowModal
      v-if="openApprovalModal"
      :task-id="selectedItem.Process_Instance_Id"
      @close-modal="closeAllForms()"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
// Async Components
const ViewShortTimeOff = defineAsyncComponent(() =>
  import("./ViewShortTimeOff.vue")
);
const ApprovalFlowModal = defineAsyncComponent(() =>
  import("@/components/custom-components/ApprovalFlowModal.vue")
);

import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue } from "@/helper.js";

const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const FormFilter = defineAsyncComponent(() =>
  import("./ShortTimeOffFilter.vue")
);
const ShortTimeOffList = defineAsyncComponent(() =>
  import(
    "@/views/my-team/time-off/short-time-off/short-time-off-request/ShortTimeOffList.vue"
  )
);

export default {
  name: "ShortTimeOffCalling",
  components: {
    ViewShortTimeOff,
    ApprovalFlowModal,
    EmployeeDefaultFilterMenu,
    FormFilter,
    ShortTimeOffList,
  },
  props: {
    formId: {
      type: Number,
      required: false,
      default: 352,
    },
    filteredList: {
      type: Array,
      default: () => [],
    },
    callingFrom: {
      type: String,
      default: "myTeam",
    },
  },
  mixins: [FileExportMixin],
  emits: ["send-list-data"],
  data: () => ({
    originalList: [],
    itemList: [],
    startDate: null,
    endDate: null,
    selectedPeriod: null,
    selectedMonthYear: null,
    isEdit: false,
    selectedItem: null,
    showViewForm: false,
    showAddEditForm: false,
    deleteModel: false,
    openApprovalModal: false,
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    currentTabItem: "",
  }),

  computed: {
    landedFormName() {
      return "Short Time Off";
    },
    formAccess() {
      let formAccess =
        this.callingFrom === "myTeam"
          ? this.accessRights("352")
          : this.accessRights("353");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
      };
    },
    periodList() {
      let list = [
        "Last 7 Days",
        "This Month",
        "Last Month",
        "Next 90 Days",
        "Custom",
      ];
      if (this.showTeam) list.unshift("Today", "Yesterday");
      return list;
    },
    presentPendingApprovalButton() {
      return (
        this.itemList?.some(
          (el) =>
            !el.Process_Instance_Id &&
            (el.Approval_Status?.toLowerCase() === "applied" ||
              el.Approval_Status?.toLowerCase() === "cancel applied")
        ) || false
      );
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    myTeamTimeOffFormAccess() {
      return this.$store.getters.myTeamTimeOffFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.myTeamTimeOffFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (this.approvalManagementFormAccess) {
          const shortTimeOffIndex = formAccessArray.indexOf("Short Time Off");
          const reportsLabel =
            this.accessRights(348)?.customFormName || "Reports";
          const reportsIndex = formAccessArray.indexOf(reportsLabel);

          // Remove Approvals first if already present
          formAccessArray = formAccessArray.filter(
            (item) => item !== "Approvals"
          );

          if (
            shortTimeOffIndex !== -1 &&
            reportsIndex !== -1 &&
            shortTimeOffIndex < reportsIndex
          ) {
            formAccessArray.splice(reportsIndex, 0, "Approvals");
          } else {
            formAccessArray.push("Approvals");
          }
        }
        return formAccessArray;
      }
      return [];
    },
    approvalManagementFormAccess() {
      let formAccessRights = this.accessRights(184);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    showTeam() {
      return this.callingFrom === "myTeam";
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList?.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isManager() {
      return this.$store.state.isManager;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  watch: {
    filteredList: {
      handler(item) {
        if (item && item.length) this.itemList = item;
        else this.itemList = [];
      },
      deep: true,
    },
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.getCurrentDateRange();
  },

  methods: {
    checkNullValue,
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.myTeamTimeOffFormAccess;
        let clickedForm = formAccess[tab];
        if (tab === "Approvals") {
          window.location.href =
            this.baseUrl + "v3/approvals/approval-management?form_id=352";
        } else if (clickedForm.isVue3) {
          this.$router.push("/my-team/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/my-team/" + clickedForm.url;
        }
      }
    },
    openAddEditForm(openEdit = false, item = null) {
      if (openEdit) {
        this.isEdit = true;
        // If item is provided, use it; otherwise, keep the current selectedItem
        if (item) this.selectedItem = item;
      } else {
        this.isEdit = false;
        this.selectedItem = {};
      }
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    closeAddEditForm() {
      this.showAddEditForm = false;
    },
    getCurrentDateRange() {
      // Leave Date From = Current Month
      const leaveDateFrom = moment().startOf("month").format("YYYY-MM-DD");
      const leaveDateTo = moment().endOf("month").format("YYYY-MM-DD");
      this.selectedPeriod = "This Month";
      // Set the Date Array instead of String
      this.selectedMonthYear = [
        this.formatDate(leaveDateFrom),
        this.formatDate(leaveDateTo),
      ];
      this.startDate = leaveDateFrom;
      this.endDate = leaveDateTo;
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.deleteModel = false;
      this.openApprovalModal = false;
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();

      // Refresh the list in the child component
      if (this.$refs.shortTimeOffList) {
        this.$refs.shortTimeOffList.refetchList();
      } else {
        this.resetFilter();
      }
    },

    updateList(list) {
      this.originalList = list || [];
      this.itemList = list || [];
    },

    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action === "Cancel") {
        // Delegate cancel action to ShortTimeOffList component
        if (this.$refs.shortTimeOffList) {
          this.$refs.shortTimeOffList.onActions("Cancel", item);
        } else {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: "Unable to process the cancel action. Please try again.",
          });
        }
      } else if (action === "Delete") this.deleteModel = true;
      else if (action === "Edit") this.openAddEditForm(true);
      else if (action?.toLowerCase() === "approval workflow")
        this.openApprovalModal = true;
    },

    onDeleteShortTimeOff() {
      // Call the onDeleteShortTimeOff method in the ShortTimeOffList component
      if (this.$refs.shortTimeOffList) {
        this.$refs.shortTimeOffList.onDeleteShortTimeOff();
      } else {
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: "Unable to delete the record. Please try again.",
        });
        this.closeAllForms();
      }
    },

    onCancelShortTimeOff() {
      // Call the onCancelShortTimeOff method in the ShortTimeOffList component
      if (this.$refs.shortTimeOffList) {
        this.$refs.shortTimeOffList.onCancelShortTimeOff();
      } else {
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: "Unable to cancel the record. Please try again.",
        });
        this.closeAllForms();
      }
    },

    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        Start_Date_Time: el.Start_Date_Time
          ? this.formatDate(el.Start_Date_Time)
          : "",
        End_Date_Time: el.End_Date_Time
          ? this.formatDate(el.End_Date_Time)
          : "",
        Approved_On: el.Approved_On ? this.formatDate(el.Approved_On) : "",
        Added_On: el.Added_On ? this.formatDate(el.Added_On) : "",
        Updated_On: el.Updated_On ? this.formatDate(el.Updated_On) : "",
        Late_Attendance: el.Late_Attendance ? "Yes" : "No",
        Early_Checkout: el.Early_Checkout ? "Yes" : "No",
      }));

      const headers = [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Forwarded To", key: "Forwarded_To_Name" },
        { header: "Request For", key: "Request_For" },
        { header: "Reason", key: "Reason" },
        { header: "Start Date Time", key: "Start_Date_Time" },
        { header: "End Date Time", key: "End_Date_Time" },
        { header: "Total Hours", key: "Total_Hours" },
        { header: "Contact Details", key: "Contact_Details" },
        { header: "Alternate Person", key: "Alternate_Person_Name" },
        { header: "Late Attendance", key: "Late_Attendance" },
        { header: "Early Checkout", key: "Early_Checkout" },
        { header: "Early Checkout Hours", key: "Early_Checkout_Hours" },
        { header: "Approval Status", key: "Approval_Status" },
        { header: "Approved By", key: "Approved_By_Name" },
        { header: "Approved On", key: "Approved_On" },
        { header: "Added By", key: "Added_By_Name" },
        { header: "Added On", key: "Added_On" },
        { header: "Updated By", key: "Updated_By_Name" },
        { header: "Updated On", key: "Updated_On" },
      ];

      const exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: headers,
      };

      this.exportExcelFile(exportOptions);
    },

    statusColor(status) {
      switch (status) {
        case "Applied":
          return "text-primary";
        case "Approved":
          return "text-green";
        case "Rejected":
          return "text-red";
        case "Returned":
          return "text-amber";
        case "Cancelled":
          return "text-amber";
        default:
          return "";
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onChangePeriod(period) {
      if (period === "Custom") {
        this.selectedMonthYear = null;
      } else {
        let startDate, endDate;

        switch (period) {
          case "Today":
            startDate = endDate = moment().format("YYYY-MM-DD");
            break;
          case "Yesterday":
            startDate = endDate = moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD");
            break;
          case "Last 7 Days":
            startDate = moment().subtract(6, "days").format("YYYY-MM-DD");
            endDate = moment().format("YYYY-MM-DD");
            break;
          case "This Month":
            startDate = moment().startOf("month").format("YYYY-MM-DD");
            endDate = moment().endOf("month").format("YYYY-MM-DD");
            break;
          case "Last Month":
            startDate = moment()
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD");
            endDate = moment()
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD");
            break;
          case "Next 90 Days":
            startDate = moment().format("YYYY-MM-DD");
            endDate = moment().add(90, "days").format("YYYY-MM-DD");
            break;
        }

        if (startDate && endDate) {
          this.selectedMonthYear = [
            this.formatDate(startDate),
            this.formatDate(endDate),
          ];
        }
      }
    },

    onChangeDateRange(selectedDates) {
      if (selectedDates?.length > 1) {
        // Format the start and end dates
        let leaveStartDate = moment(selectedDates[0]).format("YYYY-MM-DD");
        let leaveEndDate = moment(selectedDates[1]).format("YYYY-MM-DD");

        // Calculate the difference in days
        let dateDifference = moment(leaveEndDate).diff(
          moment(leaveStartDate),
          "days"
        );
        const differenceAllowed = this.showTeam ? 365 : 90;
        // Prevent if the range is more than difference allowed days
        if (dateDifference > differenceAllowed) {
          this.selectedMonthYear = [
            this.formatDate(this.startDate),
            this.formatDate(this.endDate),
          ];
          this.showAlert({
            isOpen: true,
            message: `The selected date range cannot exceed ${differenceAllowed} days. Please select a valid date range.`,
            type: "warning",
          });
          return;
        }
        if (
          moment(selectedDates[0]).format("YYYY-MM-DD") != this.startDate ||
          moment(selectedDates[1]).format("YYYY-MM-DD") != this.endDate
        ) {
          // Set the dates and fetch the list
          this.startDate = leaveStartDate;
          this.endDate = leaveEndDate;
        }
      }
    },
    redirectToOldShortTimeOff() {
      window.open(this.baseUrl + "employees/short-time-off", "_blank");
    },

    // Apply filter method
    applyFilter(filteredArray) {
      this.itemList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    // Reset filter method
    resetFilter() {
      this.itemList = this.originalList;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },
  },
};
</script>

<style scoped>
.shorttimeoff {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__value) {
  min-width: 160px;
  display: flex;
  align-items: center;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__calendar) {
  right: 1px;
}
</style>
