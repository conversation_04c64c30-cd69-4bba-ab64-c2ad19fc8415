<template>
  <div>
    <div v-if="itemList && itemList.length > 0">
      <v-row class="rounded-lg ma-1 pa-1" style="background: white">
        <v-col cols="12">
          <NotesCard
            notes="The creation of Compensatory Off (Comp Off) balance is directly linked to attendance data. Attendance records for week-offs and holidays must first be approved by the reporting manager. Once approved, the Comp Off balance is credited based on the configured policy guidelines."
            backgroundColor="transparent"
          />
        </v-col>
      </v-row>
      <div
        v-if="!isSmallTable"
        class="d-flex align-center my-3"
        :class="isMobileView ? 'justify-center ' : 'justify-end'"
      >
        <v-btn
          v-if="formName === 'MyTeam'"
          class="primary my-2 ml-2"
          :size="isMobileView ? 'small' : 'default'"
          rounded="lg"
        >
          <v-icon size="14">fas fa-calendar-alt</v-icon>
          <span class="text-caption px-1">Worked Date:</span>
          <flat-pickr
            v-model="appliedDateRange"
            :config="flatPickerOptions"
            placeholder="Select Date"
            class="ml-2 date-range-picker-custom-bg"
            style="outline: 0px; color: var(--v-primary-base)"
            :style="
              isMobileView
                ? 'width: 70px;  text-overflow: ellipsis;'
                : 'width: 180px'
            "
            @onClose="onChangeApprovalData"
          ></flat-pickr>
        </v-btn>
        <v-btn
          rounded="lg"
          color="transparent"
          variant="flat"
          class="ml-2 mt-1"
          :size="isMobileView ? 'small' : 'default'"
          @click="$emit('refetch-data')"
        >
          <v-icon>fas fa-redo-alt</v-icon>
        </v-btn>
        <v-menu v-model="openMoreMenu" transition="scale-transition">
          <template v-slot:activator="{ props }">
            <v-btn
              variant="plain"
              class="mt-1 ml-n2 mr-n5"
              :size="isMobileView ? 'small' : 'default'"
              v-bind="props"
            >
              <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
              <v-icon v-else>fas fa-caret-up</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="action in moreActions"
              :key="action.key"
              @click="onMoreAction(action.key)"
            >
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <v-list-item-title
                    v-bind="props"
                    class="pa-3"
                    :class="{
                      'pink-lighten-5': isHovering,
                    }"
                    ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                    >{{ action.key }}</v-list-item-title
                  >
                </template>
              </v-hover>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
      <v-row>
        <v-col :cols="12" class="mb-12">
          <v-data-table
            :headers="tableHeaders"
            :items="itemList"
            fixed-header
            :items-per-page="50"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
            ]"
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(290, itemList)
            "
            style="box-shadow: none !important"
            class="elevation-1"
          >
            <template v-slot:item="{ item }">
              <tr
                class="data-table-tr bg-white cursor-pointer"
                :class="
                  isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''
                "
                @click="onSelectItem(item)"
              >
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5 font-weight-small'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="text-subtitle-1 text-grey-darken-1"
                  >
                    Employee Name
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <div class="d-flex align-center">
                      <div
                        v-if="
                          isSmallTable &&
                          windowWidth > 1264 &&
                          selectedItem &&
                          selectedItem.Comp_Off_Balance_Id ===
                            item.Comp_Off_Balance_Id
                        "
                        class="data-table-side-border lop-border-color d-flex"
                        style="height: 3em"
                      ></div>
                    </div>
                    <span class="text-primary text-body-2 font-weight-regular">
                      <v-tooltip :text="item.Employee_Name" location="bottom">
                        <template v-slot:activator="{ props }">
                          <span
                            v-bind="
                              item.Employee_Name &&
                              item.Employee_Name.length > 20
                                ? props
                                : ''
                            "
                            >{{ item.Employee_Name }}</span
                          >
                        </template>
                      </v-tooltip>
                      <v-tooltip
                        :text="item.User_Defined_EmpId"
                        location="bottom"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            v-if="item.User_Defined_EmpId"
                            v-bind="
                              item.User_Defined_EmpId &&
                              item.User_Defined_EmpId.length > 20
                                ? props
                                : ''
                            "
                            class="text-grey"
                          >
                            {{ item.User_Defined_EmpId }}
                          </div>
                        </template>
                      </v-tooltip>
                    </span>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Worked Dates
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ formatDate(checkNullValue(item.Worked_Date)) }}
                    </span>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Expiry Dates
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ formatDate(checkNullValue(item.Expiry_Date)) }}
                    </span>
                  </section>
                </td>
                <td
                  v-if="!isSmallTable"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Total Days
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Total_Days) }}
                    </span>
                  </section>
                </td>
                <td
                  v-if="!isSmallTable"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Remaining Days
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{
                        item.Remaining_Days || item.Remaining_Days == 0
                          ? item.Remaining_Days
                          : "-"
                      }}
                    </span>
                  </section>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </div>
    <AppFetchErrorScreen
      v-else-if="itemList && itemList.length == 0 && !isSmallTable"
      key="no-results-screen"
      main-title="No matching filter/search results found"
      image-name="common/no-records"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4">
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                variant="elevated"
                color="primary"
                class="ml-4 mt-1"
                rounded="lg"
                :size="this.isMobileView ? 'small' : 'default'"
                @click="$emit('reset-search-filter')"
              >
                Reset Filter/Search
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
  </div>
</template>
<script>
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import moment from "moment";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);

export default {
  name: "ListCompOffBalance",
  components: { flatPickr, NotesCard },
  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    isSmallTable: {
      type: Boolean,
      default: false,
    },
    backupMainList: {
      type: Array,
      required: true,
      default: () => [],
    },
    formName: {
      type: String,
      required: true,
    },
    filteredDateRange: {
      type: String,
      default: "",
    },
  },
  mixins: [FileExportMixin],
  data() {
    return {
      itemList: [],
      selectedItem: {},
      openMoreMenu: false,
      appliedDateRange: null,
      isExceed61Days: false,
    };
  },
  mounted() {
    this.appliedDateRange = this.filteredDateRange;
    if (this.items && this.items.length) {
      this.itemList = this.items;
    }
  },
  watch: {
    items(val) {
      this.itemList = val;
      this.onApplySearch();
    },
    searchValue() {
      this.onApplySearch();
    },
    isSmallTable(val) {
      if (val === false) {
        this.selectedItem = {};
      }
    },
    appliedDateRange() {
      if (this.isExceed61Days) {
        this.appliedDateRange = this.filteredDateRange;
        this.isExceed61Days = false;
      }
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Employee Name",
            align: "start",
            key: "Employee_Name",
          },
          {
            title: "Worked Dates",
            key: "Worked_Date",
          },
          {
            title: "Expiry Date",
            key: "Expiry Date",
          },
        ];
      } else {
        return [
          {
            title: "Employee Name",
            align: "start",
            key: "Employee_Name",
          },
          {
            title: "Worked Dates",
            key: "Worked_Date",
          },
          {
            title: "Expiry Date",
            key: "Expiry_Date",
          },
          {
            title: "Total Days",
            key: "Total_Days",
          },
          {
            title: "Remaining Days",
            key: "Remaining_Days",
          },
        ];
      }
    },
    moreActions() {
      let moreActions = [
        {
          key: "Export view",
          icon: "fas fa-file-export",
        },
        {
          key: "Export all",
          icon: "fas fa-file-export",
        },
      ];
      if (this.formName == "MyTeam") {
        moreActions.push({
          key: "Export History",
          icon: "fas fa-file-export",
        });
      }
      return moreActions;
    },
    formatDate() {
      return (date, withTime = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment().format(this.$store.state.orgDetails.orgDateFormat),
      };
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    onSelectItem(item) {
      this.selectedItem = item;
      this.$emit("open-view-form", item);
    },
    onChangeApprovalData(selectedDates, dateStr) {
      this.isExceed61Days = false;
      if (dateStr.includes("to")) {
        if (dateStr != this.filteredDateRange) {
          let splittedDate = dateStr.split(" to ");
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          const startMoment = moment(splittedDate[0], orgDateFormat);
          const endMoment = moment(splittedDate[1], orgDateFormat);
          const diffInDays = endMoment.diff(startMoment, "days");
          if (diffInDays <= 61) {
            this.$emit("on-change-date-range", dateStr);
          } else {
            this.isExceed61Days = true;
          }
        }
      } else {
        this.$emit("on-change-date-range", dateStr);
      }
      if (this.isExceed61Days) {
        this.appliedDateRange = this.filteredDateRange;
        let snackbarData = {
          isOpen: true,
          message: "Please select a date range of less than 61 days",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    onApplySearch() {
      let val = this.searchValue;
      if (!val) {
        this.itemList = this.items;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.items;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },

    onMoreAction(actionType) {
      if (actionType === "Export view" || actionType === "Export all") {
        this.exportReportFile(actionType);
      } else if (actionType === "Export History") {
        this.$emit("open-dialog-form");
      }
      this.openMoreMenu = false;
    },
    exportReportFile(actionType) {
      let item = this.itemList.map((obj) => ({ ...obj }));
      if (actionType == "Export all") {
        item = this.backupMainList;
      } else {
        item = this.itemList;
      }
      let itemList = item.map((item) => ({
        User_Defined_EmpId: item.User_Defined_EmpId,
        Employee_Name: item.Employee_Name,
        Employee_Type: item.Employee_Type,
        Location_Name: item.Location_Name,
        Designation_Name: item.Designation_Name,
        Department_Name: item.Department_Name,
        Work_Schedule: item.Work_Schedule,
        Worked_Date: item.Worked_Date
          ? this.convertUTCToLocal(item.Worked_Date).substring(0, 10)
          : "-",
        Expiry_Date: item.Expiry_Date
          ? this.convertUTCToLocal(item.Expiry_Date).substring(0, 10)
          : "-",
        Total_Days: item.Total_Days,
        Remaining_Days: item.Remaining_Days,
        Total_Hours: item.Total_Hours,
        Source: item.Source,
        Comp_Off_Attendance_Balance: item.Comp_Off_Attendance_Balance,
        Comp_Off_Additional_Wage_Claim_Balance:
          item.Comp_Off_Additional_Wage_Claim_Balance,
        Updated_By: item.Updated_By,
        Updated_On: item.Updated_On
          ? this.convertUTCToLocal(item.Updated_On)
          : "",
      }));
      let fileName = "Comp Off Balance";
      let exportHeaders = [
        {
          header: "Employee Id",
          key: "User_Defined_EmpId",
        },
        {
          header: "Employee Name",
          key: "Employee_Name",
        },
        {
          header: "Designation",
          key: "Designation_Name",
        },

        {
          header: "Department",
          key: "Department_Name",
        },
        {
          header: "Employee Type",
          key: "Employee_Type",
        },
        {
          header: "Work Schedule",
          key: "Work_Schedule",
        },
        { header: "Location", key: "Location_Name" },
        {
          header: "Worked Date",
          key: "Worked_Date",
        },
        {
          header: "Expiry Date",
          key: "Expiry_Date",
        },
        {
          header: "Total Days",
          key: "Total_Days",
        },
        {
          header: "Remaining Days",
          key: "Remaining_Days",
        },
        {
          header: "Comp Off Eligible Hours",
          key: "Total_Hours",
        },
        {
          header: "Balance added from",
          key: "Source",
        },
        {
          header: "Compensatory Off Balance For Attendance",
          key: "Comp_Off_Attendance_Balance",
        },
        {
          header: "Compensatory Off Balance For Additional Wage Claim",
          key: "Comp_Off_Additional_Wage_Claim_Balance",
        },
        { header: "Updated By", key: "Updated_By" },
        { header: "Updated On", key: "Updated_On" },
      ];
      let exportOptions = {
        fileExportData: itemList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style scoped>
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
