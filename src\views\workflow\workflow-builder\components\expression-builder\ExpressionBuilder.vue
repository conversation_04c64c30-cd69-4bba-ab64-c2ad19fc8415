<template>
  <div
    style="max-height: 32rem; min-height: 31rem; overflow: scroll"
    class="px-2 mt-2"
  >
    <query-builder
      v-model="filter"
      :filter-fields="filter_fields"
      :color="color"
    >
    </query-builder>
  </div>
  <v-col cols="12" class="d-flex justify-end">
    <v-btn
      class="my-2 mx-4"
      color="primary"
      variant="outlined"
      rounded="md"
      @click="onClose"
      >Cancel</v-btn
    >
    <v-btn
      class="my-2 px-4"
      color="primary"
      rounded="md"
      @click="submitExpression"
      >Save</v-btn
    >
  </v-col>
</template>

<script>
import QueryBuilder from "@ampgroep/vuetify-query-builder";
import "@ampgroep/vuetify-query-builder/dist/style.css";
import Config from "@/config.js";
import axios from "axios";

export default {
  name: "ExpressionBuilder",
  emits: ["submitQueryValue", "onCloseModal"],
  components: {
    QueryBuilder,
  },
  props: {
    selectedQueryData: {
      type: Object,
      required: false,
    },
    expressionTitle: {
      type: String,
      required: true,
    },
    eventId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filter: {},
      filter_fields: [],
      color: "#e5e9ff",
    };
  },
  computed: {
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    if (this.selectedQueryData?.queryValue) {
      this.getPrepareBuilder(this.selectedQueryData.queryValue);
    }
    this.getFieldsList();
  },
  methods: {
    getPrepareBuilder(builderData) {
      this.filter = this.transformToQueryBuilderFormat(builderData);
    },
    transformToQueryBuilderFormat(data) {
      function transformRules(rules) {
        return rules.map((rule, index) => {
          if (rule.condition) {
            return {
              type: "query-builder-group",
              query: {
                logicalOperator: rule.condition,
                children: transformRules(rule.rules),
              },
              originalIndex: index,
            };
          } else {
            const operatorsMap = {
              less: "lt",
              greater: "gt",
              equal: "eq",
              not_equal: "ne",
              less_or_equal: "lte",
              greater_or_equal: "gte",
              is_not_null: "not null",
              is_null: "is null",
              like: "like",
              regexp: "regexp",
            };

            return {
              type: "query-builder-rule",
              query: {
                rule: "",
                operator: operatorsMap[rule.operator] || rule.operator,
                operand: rule.field,
                value: rule.value,
              },
              originalIndex: index,
            };
          }
        });
      }

      return {
        children: transformRules(data.rules),
        logicalOperator: data.condition,
      };
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    submitExpression() {
      if (this.expressionTitle === "") {
        let snackbarData = {
          isOpen: true,
          message: "Please enter Expression title",
          type: "warning",
        };
        window.scrollTo(0, 0);
        this.showAlert(snackbarData);
      } else {
        const transformedData = this.transformData(this.filter);
        this.$emit("submitQueryValue", transformedData);
        this.onClose();
      }
    },
    transformData(originalData) {
      const conditionData = {
        condition: originalData.logicalOperator,
        rules: originalData.children
          .map((child) => {
            if (child.type === "query-builder-rule") {
              const rule = child.query;
              const operatorsMap = {
                lt: "less",
                gt: "greater",
                eq: "equal",
                ne: "not_equal",
                lte: "less_or_equal",
                gte: "greater_or_equal",
                "not null": "is_not_null",
                "is null": "is_null",
              };
              return {
                id: rule.operand,
                field: rule.operand,
                type: "string",
                input: "text",
                operator: operatorsMap[rule.operator] || rule.operator,
                value: rule.value,
              };
            } else if (child.type === "query-builder-group") {
              return this.transformData(child.query);
            }
          })
          .filter(Boolean),
      };
      return conditionData;
    },
    onClose() {
      this.$emit("onCloseModal");
    },
    getFieldsList() {
      let vm = this;
      try {
        axios
          .get(Config.workflowUrl + "/event/parameters/" + this.eventId, {
            headers: {
              org_code: vm.orgCode,
              employee_id: vm.loginEmployeeId,
              Db_prefix: vm.domainName,
              "Content-Type":
                "application/x-www-form-urlencoded; charset=UTF-8",
              Authorization: window.$cookies.get("accessToken")
                ? window.$cookies.get("accessToken")
                : "",
            },
          })
          .then((response) => {
            if (response && response.data && response.data.keys) {
              this.filter_fields = response.data.keys;
            }
          })
          .catch(function () {});
      } catch (e) {
        /* empty */
      }
    },
  },
};
</script>
