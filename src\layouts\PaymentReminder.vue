<template>
  <div class="fill-height">
    <!-- payment red alert -->
    <v-container
      fluid
      class="main-container d-flex align-center justify-center fill-height"
    >
      <v-row class="d-flex justify-center align-center fill-height">
        <v-col cols="12" lg="10" xl="8">
          <v-card
            v-if="showPaymentExceedCard"
            class="rounded-lg pa-sm-6"
            style="border: 7px solid red"
          >
            <v-row>
              <v-col
                v-if="windowWidth > 959"
                cols="12"
                md="5"
                class="d-flex justify-center"
              >
                <img
                  :src="nonpaymentAlertImage"
                  alt="payment alert"
                  style="height: auto; width: 400"
                />
              </v-col>
              <v-col cols="12" md="7">
                <v-row class="text-h5 font-weight-bold">
                  <v-col cols="12" class="text-red">
                    Access is restricted due to non-payment
                  </v-col>
                  <v-col cols="7" class="text-grey-darken-1">
                    Active Employee(s)
                  </v-col>
                  <v-col cols="1"> : </v-col>
                  <v-col cols="4" class="text-green">
                    {{ paymentDetails.activeEmployeesCount }}
                  </v-col>
                  <v-col cols="7" class="text-grey-darken-1">
                    Inactive Employee(s)
                  </v-col>
                  <v-col cols="1"> : </v-col>
                  <v-col cols="4" class="text-red">
                    {{ paymentDetails.inactiveEmployeesCount }}
                  </v-col>
                  <v-col cols="7" class="text-grey-darken-1"> Due Date </v-col>
                  <v-col cols="1"> : </v-col>
                  <v-col cols="4" class="text-primary"> Immediate </v-col>
                  <v-col cols="7" class="text-grey-darken-1">
                    Total Amount Due
                  </v-col>
                  <v-col cols="1"> : </v-col>
                  <v-col cols="4" class="text-primary">
                    {{
                      paymentDetails.paymentAmount
                        ? paymentDetails.currency
                        : ""
                    }}
                    {{ checkNullValue(paymentDetails.paymentAmount) }}
                  </v-col>
                  <v-col cols="12">
                    <v-btn
                      v-if="paymentDetails.paymentUrl"
                      size="large"
                      color="#1ec50f"
                      rounded="lg"
                      theme="dark"
                      @click="redirectToPaymentPage()"
                    >
                      Pay Now
                    </v-btn>
                  </v-col>
                  <v-col cols="12">
                    <div
                      class="mb-n2 text-primary text-body-1 font-weight-bold"
                    >
                      We'd be happy to discuss flexible payment options if
                      settling the full amount immediately is challenging.
                      Please let us know if you'd like to explore such options
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
// import common function
import { checkNullValue } from "@/helper";

export default {
  name: "PaymentReminder",
  props: {
    showPaymentExceedCard: {
      type: Boolean,
      default: false,
    },
    paymentDetails: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    // screen size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    nonpaymentAlertImage() {
      return require("@/assets/images/layout/non-payment-alert.gif");
    },
  },

  methods: {
    checkNullValue,
    // redirect to payment url, which is from BE
    redirectToPaymentPage() {
      window.open(this.paymentDetails.paymentUrl, "_blank");
    },
  },
};
</script>
