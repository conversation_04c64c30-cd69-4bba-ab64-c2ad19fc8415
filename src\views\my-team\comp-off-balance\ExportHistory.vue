<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div class="d-flex align-center justify-space-between px-4 pt-4">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="primary"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >History Export</span
          >
        </div>
        <v-icon @click="closeForm" color="primary" class="mr-1">
          fas fa-times
        </v-icon>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-4'"
        style="height: calc(100vh - 500px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="historyExport">
            <v-row>
              <v-col cols="12">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Worked Date
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" lg="6">
                <datepicker
                  :format="orgDateFormat"
                  v-model="expiryFrom"
                  style="min-width: 100% !important"
                  :placeholder="'From Date'"
                  :disabled-dates="{
                    to: new Date(oneYearValidation),
                    from: expiryFromDateValidation
                      ? new Date(expiryFromDateValidation)
                      : '',
                  }"
                  @input="onChangeDateFields()"
                ></datepicker>
                <div
                  v-if="expiryFromErrorMsg"
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ !this.expiryFrom ? expiryFromErrorMsg : "" }}
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" lg="6">
                <datepicker
                  :format="orgDateFormat"
                  v-model="expiryTo"
                  :placeholder="'To Date'"
                  style="min-width: 100% !important"
                  :disabled-dates="{
                    to: expiryToDateValidation
                      ? new Date(expiryToDateValidation)
                      : '',
                    from: currentDate ? new Date(currentDate) : '',
                  }"
                  @input="onChangeDateFields()"
                ></datepicker>
                <div
                  v-if="expiryToErrorMsg"
                  class="text-caption"
                  style="color: #b00020"
                >
                  {{ !this.expiryTo ? expiryToErrorMsg : "" }}
                </div>
              </v-col>
              <v-col cols="12">
                <div class="d-flex justify-center mt-16">
                  <v-btn
                    id="bulk_sheet_download"
                    rounded="lg"
                    size="small"
                    class="font-weight-bold primary"
                    @click="saveData"
                  >
                    <v-icon class="mr-2" size="16">fas fa-file-export</v-icon>
                    Export
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>

<script>
import Datepicker from "vuejs3-datepicker";
//Mixins
import FileExportMixin from "@/mixins/FileExportMixin";
import moment from "moment";
// Queries
import { EXPORT_COMP_OFF_HISTORY } from "@/graphql/my-team/comp-off-balance/compOffBalanceQueries.js";
import { convertUTCToLocal } from "@/helper.js";
export default {
  name: "HistoryExport",
  mixins: [FileExportMixin],
  components: {
    Datepicker,
  },
  props: {
    accessFormName: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      isMounted: false,
      isLoadingDetails: false,
      expiryFrom: null,
      expiryTo: null,
      isFormDirty: false,
      expiryFromErrorMsg: "",
      expiryToErrorMsg: "",
      exportList: [],
    };
  },
  mounted() {
    this.isFormDirty = false;
    this.isMounted = true;
    this.expiryTo = new Date().toISOString().substring(0, 10);
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    expiryToDateValidation() {
      if (this.expiryFrom) {
        const issueDateMs = new Date(this.expiryFrom)
          .toISOString()
          .substring(0, 10);
        return issueDateMs;
      }
      return null;
    },
    currentDate() {
      const today = new Date().toISOString().substring(0, 10);
      return today;
    },
    oneYearValidation() {
      const originalDate = new Date();
      originalDate.setFullYear(originalDate.getFullYear() - 1);
      return originalDate.toISOString().substring(0, 10);
    },
    expiryFromDateValidation() {
      if (this.expiryTo) {
        const issueDateMs = new Date(this.expiryTo)
          .toISOString()
          .substring(0, 10);
        return issueDateMs;
      }
      return null;
    },
  },
  methods: {
    convertUTCToLocal,
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeForm() {
      this.$emit("close-dialog-form");
    },

    onChangeDateFields() {
      this.isFormDirty = true;
    },

    updateAllowanceRestrictBonus() {
      this.isFormDirty = true;
    },
    async saveData() {
      const { valid } = await this.$refs.historyExport.validate();
      if (valid && this.expiryFrom && this.expiryTo) {
        this.fetchExportData();
      } else {
        if (!this.expiryFrom) {
          this.expiryFromErrorMsg = "From date is required";
        }
        if (!this.expiryTo) {
          this.expiryToErrorMsg = "To date is required";
        }
      }
    },
    fetchExportData() {
      let vm = this;
      vm.isLoadingDetails = true;
      vm.$apollo
        .query({
          query: EXPORT_COMP_OFF_HISTORY,
          variables: {
            workedDateFrom: moment(vm.expiryFrom).format("YYYY-MM-DD"),
            workedDateTo: moment(vm.expiryTo).format("YYYY-MM-DD"),
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.exportCompOffHistory) {
            vm.exportList =
              response.data.exportCompOffHistory.compOffHistoryData;
            vm.exportReportFile();
            vm.isLoadingDetails = false;
          } else {
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.isLoadingDetails = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "exporting",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    exportReportFile() {
      let exportData = this.exportList;
      let itemList;

      if (exportData.length === 0) {
        // If exportData is empty, add two empty items to itemList
        let snackbarData = {
          isOpen: true,
          message:
            "No comp off history data is available for export within the specified date range.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      } else {
        // If exportData is not empty, map the data as before
        itemList = exportData.map((item) => ({
          userDefinedEmpId: item.userDefinedEmpId,
          employeeName: item.employeeName,
          designationName: item.designationName,
          departmentName: item.departmentName,
          employeeType: item.employeeType,
          locationName: item.locationName,
          workSchedule: item.workSchedule,
          workedDate: item.workedDate
            ? this.convertUTCToLocal(item.workedDate).substring(0, 10)
            : "-",
          expiryDate: item.expiryDate
            ? this.convertUTCToLocal(item.expiryDate).substring(0, 10)
            : "-",
          totalDays: item.totalDays,
          remainingDays: item.remainingDays,
          compOffEligibleHours: item.compOffEligibleHours,
          balanceAddedFrom: item.balanceAddedFrom,
          compOffBalanceForAttendance: item.compOffBalanceForAttendance,
          compOffBalanceForAdditionalWageClaim:
            item.compOffBalanceForAdditionalWageClaim,
          updatedByName: item.updatedByName,
          updatedOn: item.updatedOn
            ? this.convertUTCToLocal(item.updatedOn)
            : "",
        }));
        let headers = [
          {
            header: "Employee Id",
            key: "userDefinedEmpId",
          },
          {
            header: "Employee Name",
            key: "employeeName",
          },
          {
            header: "Designation",
            key: "designationName",
          },
          {
            header: "Department",
            key: "departmentName",
          },
          {
            header: "Employee Type",
            key: "employeeType",
          },
          {
            header: "Work Schedule",
            key: "workSchedule",
          },
          { header: "Location", key: "locationName" },

          {
            header: "Worked Date",
            key: "workedDate",
          },
          {
            header: "Expiry Date",
            key: "expiryDate",
          },
          {
            header: "Total Days",
            key: "totalDays",
          },
          {
            header: "Remaining Days",
            key: "remainingDays",
          },
          {
            header: "Comp Off Eligible Hours",
            key: "compOffEligibleHours",
          },
          {
            header: "Balance added from",
            key: "balanceAddedFrom",
          },
          {
            header: "Compensatory Off Balance For Attendance",
            key: "compOffBalanceForAttendance",
          },
          {
            header: "Compensatory Off Balance For Additional Wage Claim",
            key: "compOffBalanceForAdditionalWageClaim",
          },
          { header: "Updated By", key: "updatedByName" },
          { header: "Updated On", key: "updatedOn" },
        ];

        let exportOptions = {
          fileExportData: itemList,
          fileName: "Comp Off Balance History",
          sheetName: "Comp Off Balance History",
          header: headers,
        };
        this.exportExcelFile(exportOptions);
      }
      this.$emit("close-dialog-form");
    },
  },
};
</script>
