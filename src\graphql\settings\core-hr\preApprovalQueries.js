import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_PREAPPROVAL_SETTINGS = gql`
  query listPreApprovalSettings {
    listPreApprovalSettings {
      errorCode
      message
      preApprovalSettings {
        preApprovalConfigurationId
        preApprovalType
        coverage
        customGroupId
        customGroupName
        period
        noOfPreApprovalRequest
        restrictSandwich
        restrictSandwichFor
        advanceNotificationDays
        documentUpload
        maxDaysForDocumentUpload
        workflowId
        workflowName
        typeOfDay
        status
        maxDaysAllowedPerRequest
        addedOn
        updatedOn
        addedByName
        updatedByName
      }
    }
  }
`;

export const GET_PRE_APPROVAL_REQUESTS = gql`
  query listPreApprovalRequests($formId: Int!) {
    listPreApprovalRequests(formId: $formId) {
      errorCode
      message
      preApprovalRequests {
        preApprovalId
        preApprovalType
        duration
        period
        startDate
        endDate
        totalDays
        employeeId
        userDefinedEmpId
        Work_Schedule
        Designation_Id
        Designation_Name
        Department_Id
        EmpType_Id
        Employee_Type
        Department_Name
        Location_Name
        Location_Id
        employeeName
        fileName
        fileSize
        reason
        status
        addedOn
        updatedOn
        addedByName
        updatedByName
        approvedOn
        approvedByName
      }
    }
  }
`;
export const GET_DISABLED_DATES = gql`
  query getEmployeeWeekOffAndHolidayDetails(
    $month: Int
    $year: Int
    $employeeId: Int!
    $formId: Int!
  ) {
    getEmployeeWeekOffAndHolidayDetails(
      month: $month
      year: $year
      employeeId: $employeeId
      formId: $formId
    ) {
      errorCode
      message
      weekOffAndHolidayDetails {
        date
        isWeekOffDay
        weekOffDuration
        isHoliday
        isShiftScheduled
      }
    }
  }
`;
export const GET_PRE_APPROVAL_EMPLOYEE_LIST = gql`
  query ($formId: Int!) {
    getPreApprovalEmployeeList(formId: $formId) {
      errorCode
      message
      employeeDetails {
        workFromHome {
          employeeId
          userDefinedEmpId
          employeeName
        }
        workDuringWeekOff {
          employeeId
          userDefinedEmpId
          employeeName
        }
        workDuringHoliday {
          employeeId
          userDefinedEmpId
          employeeName
        }
        onDuty {
          employeeId
          userDefinedEmpId
          employeeName
        }
      }
    }
  }
`;

export const RETRIEVE_PRE_APPROVAL_SETTINGS = gql`
  query retrievePreApprovalSettings(
    $employeeId: Int!
    $preApprovalType: String!
    $startDate: String
    $endDate: String
    $preApprovalId: Int
    $formId: Int!
  ) {
    retrievePreApprovalSettings(
      employeeId: $employeeId
      preApprovalType: $preApprovalType
      startDate: $startDate
      endDate: $endDate
      preApprovalId: $preApprovalId
      formId: $formId
    ) {
      errorCode
      message
      preApprovalSettings {
        period
        noOfPreApprovalRequest
        restrictSandwich
        restrictSandwichFor
        advanceNotificationDays
        preApprovalsTaken
        workflowId
        documentUpload
        typeOfDay
        maxDaysForDocumentUpload
        maxDaysAllowedPerRequest
      }
    }
  }
`;
//mutate

// ===============
// Mutations
// ===============
export const ADD_UPDATE_PREAPPROVAL = gql`
  mutation addUpdatePreApprovalRequests(
    $preApprovalId: Int!
    $formId: Int!
    $preApprovalType: String!
    $employeeId: Int
    $duration: String!
    $period: String!
    $startDate: String!
    $endDate: String!
    $totalDays: Float!
    $reason: String
    $status: String!
    $fileName: String
    $fileSize: String
  ) {
    addUpdatePreApprovalRequests(
      preApprovalId: $preApprovalId
      preApprovalType: $preApprovalType
      employeeId: $employeeId
      formId: $formId
      duration: $duration
      period: $period
      startDate: $startDate
      endDate: $endDate
      totalDays: $totalDays
      reason: $reason
      status: $status
      fileName: $fileName
      fileSize: $fileSize
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_PREAPPROVAL_SETTINGS = gql`
  mutation addUpdatePreApprovalSettings(
    $preApprovalConfigurationId: Int!
    $preApprovalType: String!
    $coverage: String!
    $customGroupId: Int
    $period: String!
    $noOfPreApprovalRequest: Int!
    $restrictSandwich: String!
    $restrictSandwichFor: String
    $advanceNotificationDays: Int!
    $maxDaysForDocumentUpload: Float
    $documentUpload: String!
    $maxDaysAllowedPerRequest: Float
    $workflowId: Int!
    $status: String!
    $typeOfDay: String!
  ) {
    addUpdatePreApprovalSettings(
      preApprovalConfigurationId: $preApprovalConfigurationId
      preApprovalType: $preApprovalType
      coverage: $coverage
      customGroupId: $customGroupId
      period: $period
      noOfPreApprovalRequest: $noOfPreApprovalRequest
      restrictSandwich: $restrictSandwich
      restrictSandwichFor: $restrictSandwichFor
      advanceNotificationDays: $advanceNotificationDays
      maxDaysForDocumentUpload: $maxDaysForDocumentUpload
      documentUpload: $documentUpload
      maxDaysAllowedPerRequest: $maxDaysAllowedPerRequest
      workflowId: $workflowId
      status: $status
      typeOfDay: $typeOfDay
    ) {
      errorCode
      message
    }
  }
`;
