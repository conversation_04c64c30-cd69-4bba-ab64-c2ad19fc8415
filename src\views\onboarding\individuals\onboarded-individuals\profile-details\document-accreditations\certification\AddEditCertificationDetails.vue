<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Certificate Details</span
        >
        <v-spacer></v-spacer>
        <v-icon
          color="primary"
          size="25"
          @click="$emit('close-certification-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="primary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditCertificateForm">
          <v-row>
            <v-col cols="12" md="6">
              <CustomSelect
                v-if="labelList[456]?.Predefined?.toLowerCase() == 'yes'"
                v-model="certificateFormData.Certification_Name"
                :items="certificationList"
                :label="labelList[456].Field_Alias"
                :isLoading="dropdownLoading"
                item-title="Required_Certification"
                item-value="Required_Certification"
                :isRequired="
                  labelList[456].Mandatory_Field?.toLowerCase() == 'yes'
                "
                :itemSelected="certificateFormData.Certification_Name"
                :rules="[
                  required(
                    labelList[456]?.Field_Alias || 'Certificate',
                    certificateFormData.Certification_Name
                  ),
                ]"
              />
              <v-text-field
                v-else
                v-model="certificateFormData.Certification_Name"
                :rules="[
                  required(
                    labelList[456]?.Field_Alias || 'Certificate',
                    certificateFormData.Certification_Name
                  ),
                  validateWithRulesAndReturnMessages(
                    certificateFormData.Certification_Name,
                    'certificationName',
                    labelList[456]?.Field_Alias || 'Certificate'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[456]?.Field_Alias || "Certificate" }}
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-menu
                v-model="ReceivedOnMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="receivedDate"
                    v-model="formattedReceivedOn"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Received On', formattedReceivedOn)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Received On<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="certificateFormData.Received_Date"
                  :min="selectedEmpDobDate"
                  :max="currentDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="certificateFormData.Certificate_Received_From"
                :rules="[
                  required(
                    'Received From',
                    certificateFormData.Certificate_Received_From
                  ),
                  validateWithRulesAndReturnMessages(
                    certificateFormData.Certificate_Received_From,
                    'receivedFrom',
                    'Received From'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Received From<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              cols="12"
              md="6"
              v-if="labelList[377]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <v-text-field
                v-model="certificateFormData.Ranking"
                :rules="[
                  labelList[377].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[377].Field_Alias,
                        certificateFormData.Ranking
                      )
                    : [true],
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[377].Field_Alias }}
                  <span
                    v-if="
                      labelList[377].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-file-input
                prepend-icon=""
                :model-value="fileContent"
                append-inner-icon="fas fa-paperclip"
                variant="solo"
                label="Document"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                @update:model-value="onChangeFiles"
                @click:clear="removeFiles"
              >
              </v-file-input>
            </v-col>
            <v-col v-if="fileContent && fileContent.name" cols="12" md="6">
              <CustomSelect
                :items="documentSubTypeList"
                label="Document Sub Type"
                :itemSelected="certificateFormData.Sub_Type_Id"
                :rules="[
                  required(
                    'Document Sub Type',
                    certificateFormData.Sub_Type_Id
                  ),
                ]"
                itemValue="Document_Sub_Type_Id"
                itemTitle="Document_Sub_Type"
                :isRequired="true"
                @selected-item="
                  onChangeCustomSelectField($event, 'Sub_Type_Id')
                "
                :isAutoComplete="true"
                :isLoading="documentSubTypeListLoading"
                :noDataText="
                  documentSubTypeListLoading
                    ? 'Loading...'
                    : 'No data available'
                "
              ></CustomSelect>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="this.$emit('close-certification-form')"
                  class="ma-2 pa-2"
                  color="primary"
                  rounded="lg"
                  variant="text"
                  elevation="4"
                  >Cancel</v-btn
                >
                <v-btn
                  color="primary"
                  rounded="lg"
                  class="ma-2 pa-2"
                  :disabled="!isFormDirty"
                  @click="validateCertificateDetails"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import moment from "moment";
import { ADD_UPDATE_CERTIFICATE_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import { LIST_SUB_DOC_TYPE } from "@/graphql/dropDownQueries";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "AddEditCertificationDetails",
  components: {
    CustomSelect,
  },
  props: {
    selectedCertificationDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
  },
  mixins: [validationRules],
  emits: ["refetch-career-details", "close-certification-form"],
  data() {
    return {
      certificateFormData: {
        Certification_Name: "",
        Received_Date: null,
        Certificate_Received_From: "",
        Ranking: "",
      },
      isFormDirty: false,
      //Date-picker
      formattedReceivedOn: "",
      ReceivedOnMenu: false,
      // edit
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      // file
      fileContent: null,
      isFileChanged: false,
      documentSubTypeList: [],
      documentSubTypeListLoading: false,
      certificationList: [],
      dropdownLoading: false,
    };
  },

  watch: {
    "certificateFormData.Received_Date": function (val) {
      if (val) {
        this.ReceivedOnMenu = false;
        this.formattedReceivedOn = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    selectedEmpDobDate() {
      if (
        this.selectedCandidateDOB &&
        this.selectedCandidateDOB !== "0000-00-00"
      ) {
        return moment(this.selectedCandidateDOB).format("YYYY-MM-DD");
      } else return null;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedCertificationDetails &&
      Object.keys(this.selectedCertificationDetails).length > 0
    ) {
      this.certificateFormData = JSON.parse(
        JSON.stringify(this.selectedCertificationDetails)
      );
      if (this.certificateFormData.Received_Date) {
        this.formattedReceivedOn = this.formatDate(
          this.certificateFormData?.Received_Date
        );
        this.certificateFormData.Received_Date = this.certificateFormData
          .Received_Date
          ? new Date(this.certificateFormData.Received_Date)
          : null;
      }
      this.fileContent = {
        name: this.formattedFileName(this.certificateFormData["File_Name"]),
        size: 100,
      };
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
    this.retrieveDocumentSubType();
    this.getDropdownDetails();
  },

  methods: {
    async getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;
      await vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 178,
            key: ["Required_Certification"],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "required_certification") {
                vm.certificationList = item.data;
              }
            });
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "certificate dropdown",
        isListError: false,
      });
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.certificateFormData[field] = value;
    },

    async validateCertificateDetails() {
      const { valid } = await this.$refs.addEditCertificateForm.validate();
      mixpanel.track("Onboarded-candidate-career-certification-submit-click");
      if (valid) {
        this.updateCertificateDetails();
      }
    },

    updateCertificateDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_CERTIFICATE_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            certificationId: vm.certificateFormData.Certification_Id,
            certificationName: vm.certificateFormData.Certification_Name,
            receivedDate: moment(vm.certificateFormData.Received_Date).isValid()
              ? moment(vm.certificateFormData.Received_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            receivedFrom: vm.certificateFormData.Certificate_Received_From,
            university: vm.certificateFormData.University,
            yearOfPassing: vm.certificateFormData.Year_Of_Passing,
            percentage: vm.certificateFormData.Percentage,
            grade: vm.certificateFormData.Grade,
            fileName: vm.certificateFormData["File_Name"],
            documentSubTypeId: vm.certificateFormData.Sub_Type_Id,
            ranking: vm.certificateFormData?.Ranking,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track(
            "Onboarded-candidate-career-certification-update-success"
          );
          if (vm.certificateFormData["File_Name"] && vm.isFileChanged) {
            vm.uploadFileContents();
          } else {
            vm.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message:
                vm.formType === "edit"
                  ? "Certificate details updated successfully"
                  : "Certificate details added successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-career-details");
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-career-certification-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "certificate details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },
    onChangeFiles(value) {
      this.fileContent = value;
      mixpanel.track("Onboarded-candidate-doc-file-changed");
      if (this.fileContent && this.fileContent.name) {
        this.certificateFormData["File_Name"] =
          this.selectedCandidateId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent.name;
        this.isFileChanged = true;
        this.onChangeFields();
      }
    },
    removeFiles() {
      mixpanel.track("Onboarded-candidate-doc-file-removed");
      this.fileContent = null;
      this.certificateFormData["File_Name"] = "";
      this.onChangeFields();
    },
    retrieveDocumentSubType() {
      let vm = this;
      vm.documentSubTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_SUB_DOC_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentSubType &&
            !response.data.listDocumentSubType.errorCode
          ) {
            const { documentSubType } = response.data.listDocumentSubType;
            vm.documentSubTypeList =
              documentSubType && documentSubType.length > 0
                ? documentSubType
                : [];
            vm.documentSubTypeList = vm.documentSubTypeList.filter(
              (el) => el.Document_Type_Id == 4
            );
          }
          vm.documentSubTypeListLoading = false;
        })
        .catch(() => {
          vm.documentSubTypeListLoading = false;
        });
    },
    async uploadFileContents() {
      mixpanel.track("Onboarded-candidate-doc-file-upload-start");
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employees Document Upload/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.certificateFormData["File_Name"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Certification details updated successfully"
                : "Certification details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-career-details");
        })
        .catch(() => {
          mixpanel.track("Onboarded-candidate-doc-file-upload-error");
          vm.isLoading = false;
          vm.$emit("refetch-career-details");
        });
    },
  },
};
</script>
