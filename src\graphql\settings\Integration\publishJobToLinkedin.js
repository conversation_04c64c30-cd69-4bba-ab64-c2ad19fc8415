import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_LINKEDIN_JOB_OPTIONS = gql`
  query getDropDownForLinkedIn($industryCode: Int) {
    getDropDownForLinkedIn(industryCode: $industryCode) {
      message
      errorCode
      workPlaceType {
        Code
        Description
      }
      experienceLevel {
        Code
        Description
      }
      jobFunction {
        Code
        Description
      }
      industryCode {
        Industry_Id
        Description
        Parent_Industry_Id
      }
    }
  }
`;

export const RETRIVE_LINKEDIN_JOB_DETAILS = gql`
  query retrieveLinkedInJobPostPublishDetails($jobPostId: Int!) {
    retrieveLinkedInJobPostPublishDetails(jobPostId: $jobPostId) {
      message
      errorCode
      addedBy
      updatedBy
      Status
      integrationId
      workPlaceType {
        Code
        Description
      }
      experienceLevel {
        Code
        Description
      }
      jobFunction {
        Code
        Description
      }
      industryCategory {
        Industry_Id
        Description
      }
      industrySubCategory {
        Industry_Id
        Description
      }
    }
  }
`;

// ===============
// MUTATIONS
// ===============

export const ADD_UPDATE_LINKED_IN_JOB_DETAILS = gql`
  mutation addUpdateLinkedInJobPostPublishDetails(
    $integrationId: Int!
    $jobPostId: Int!
    $workplaceTypeCode: String!
    $experienceLevelCode: String!
    $jobFunctionCode: String!
    $industryCategoryCode: Int!
    $industrySubCategoryCode: Int
    $status: String
  ) {
    addUpdateLinkedInJobPostPublishDetails(
      integrationId: $integrationId
      jobPostId: $jobPostId
      workplaceTypeCode: $workplaceTypeCode
      experienceLevelCode: $experienceLevelCode
      jobFunctionCode: $jobFunctionCode
      industryCategoryCode: $industryCategoryCode
      industrySubCategoryCode: $industrySubCategoryCode
      status: $status
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_LINKEDIN_JOB_POST = gql`
  mutation deleteLinkedInJobPost($integrationId: Int!, $jobPostId: Int!) {
    deleteLinkedInJobPost(
      integrationId: $integrationId
      jobPostId: $jobPostId
    ) {
      errorCode
      message
      validationError
    }
  }
`;
