<template>
  <div v-if="isMounted">
    <section class="mb-16">
      <div>
        <v-card
          class="rounded-lg"
          :class="isMobileView ? '' : 'pa-4'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="insuranceType">
              <v-row
                class="rounded-lg card-height bg-grey-lighten-5"
                :class="isMobileView ? '' : 'pa-4'"
              >
                <v-col cols="12" class="d-flex justify-space-between mb-4">
                  <div class="d-flex align-center">
                    <v-progress-circular
                      model-value="100"
                      color="secondary"
                      :size="22"
                      class="mr-1"
                    ></v-progress-circular>
                    <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                      accessFormName
                    }}</span>
                  </div>
                  <div
                    class="d-flex align-center pa-1"
                    :class="isMobileView ? 'ml-auto' : ''"
                  >
                    <v-btn
                      rounded="lg"
                      variant="outlined"
                      color="primary"
                      class="mr-2"
                      @click="closeEditForm()"
                    >
                      Cancel
                    </v-btn>
                    <div class="mt-2 mr-1">
                      <v-btn
                        v-if="isFormDirty"
                        rounded="lg"
                        color="secondary"
                        class="mb-2"
                        @click="validateForm()"
                        >Save</v-btn
                      >
                      <v-tooltip v-else location="bottom">
                        <template v-slot:activator="{ props }">
                          <v-btn
                            v-bind="props"
                            rounded="lg"
                            color="grey-lighten-3"
                            class="cursor-not-allow mb-2"
                            variant="flat"
                            >Save</v-btn
                          >
                        </template>
                        <div>There are no changes to be updated</div>
                      </v-tooltip>
                    </div>
                  </div>
                </v-col>
                <v-col
                  v-if="getFieldAlias[63].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="insuranceName"
                    type="text"
                    variant="solo"
                    :rules="[
                      getFieldAlias[63].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[63].Field_Alias}`,
                            insuranceName
                          )
                        : true,
                      insuranceName
                        ? validateWithRulesAndReturnMessages(
                            insuranceName,
                            'insuranceName',
                            `${getFieldAlias[63].Field_Alias}`
                          )
                        : true,
                    ]"
                    @update:model-value="isFormDirty = true"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias[63].Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias[63].Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <v-col
                  v-if="getFieldAlias[64].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias[64].Field_Alias
                    }}</span>
                    <v-switch
                      color="secondary"
                      v-model="slabWiseInsurance"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @click="
                        onChangeIsFormDirty(
                          slabWiseInsurance,
                          'slabWiseInsurance'
                        )
                      "
                    ></v-switch>
                  </div>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[65].Field_Visiblity == 'Yes' &&
                    slabWiseInsurance == 'No'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                  class="d-flex"
                >
                  <CustomSelect
                    :items="insuranceTypeList"
                    :itemSelected="insuranceType"
                    :label="getFieldAlias[65].Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias[65].Mandatory_Field == 'Yes' ? true : false
                    "
                    item-title="itemName"
                    item-value="itemValue"
                    @selected-item="
                      onChangeIsFormDirty($event, 'insuranceType')
                    "
                    :rules="[
                      getFieldAlias[65].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[65].Field_Alias}`,
                            insuranceType
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                  ></CustomSelect>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias[66].Field_Visiblity == 'Yes' &&
                    slabWiseInsurance == 'No' &&
                    insuranceType == 'Variable'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employerSharePercentage"
                    type="number"
                    :min="0.1"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias[66].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[66].Field_Alias}`,
                            employerSharePercentage
                          )
                        : true,
                      employerSharePercentage
                        ? validateWithRulesAndReturnMessages(
                            employerSharePercentage,
                            'employerSharePercentage',
                            `${getFieldAlias[66].Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="
                      validateInput('employerSharePercentage')
                    "
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias[66].Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias[66].Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[67].Field_Visiblity == 'Yes' &&
                    slabWiseInsurance == 'No' &&
                    insuranceType == 'Variable'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employeeSharePercentage"
                    type="number"
                    :min="0.1"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias[67].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[67].Field_Alias}`,
                            employeeSharePercentage
                          )
                        : true,
                      employeeSharePercentage
                        ? validateWithRulesAndReturnMessages(
                            employeeSharePercentage,
                            'employeeSharePercentage',
                            `${getFieldAlias[67].Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="
                      validateInput('employeeSharePercentage')
                    "
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias[67].Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias[67].Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[68].Field_Visiblity == 'Yes' &&
                    slabWiseInsurance == 'No' &&
                    insuranceType == 'Fixed'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employerShareAmount"
                    type="number"
                    :min="0.1"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias[68].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[68].Field_Alias}`,
                            employerShareAmount
                          )
                        : true,
                      employerShareAmount
                        ? validateWithRulesAndReturnMessages(
                            employerShareAmount,
                            'employerShareAmount',
                            `${getFieldAlias[68].Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('employerShareAmount')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias[68].Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias[68].Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[69].Field_Visiblity == 'Yes' &&
                    slabWiseInsurance == 'No' &&
                    insuranceType == 'Fixed'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employeeShareAmount"
                    type="number"
                    :min="0.1"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias[69].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[69].Field_Alias}`,
                            employeeShareAmount
                          )
                        : true,
                      employeeShareAmount
                        ? validateWithRulesAndReturnMessages(
                            employeeShareAmount,
                            'employeeShareAmount',
                            `${getFieldAlias[69].Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('employeeShareAmount')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias[69].Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias[69].Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias[70].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias[70].Field_Alias
                    }}</span>
                    <v-switch
                      color="secondary"
                      class="ml-2"
                      v-model="autoDeclaration"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @click="
                        onChangeIsFormDirty(autoDeclaration, 'autoDeclaration')
                      "
                    ></v-switch>
                  </div>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias[71].Field_Visiblity == 'Yes' &&
                    autoDeclaration === 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <CustomSelect
                    :items="['Employee']"
                    :itemSelected="autoDeclarationApplicableFor"
                    :label="getFieldAlias[71].Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias[71].Mandatory_Field == 'Yes' ? true : false
                    "
                    @selected-item="
                      onChangeIsFormDirty(
                        $event,
                        'autoDeclarationApplicableFor'
                      )
                    "
                    :rules="[
                      getFieldAlias[71].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[71].Field_Alias}`,
                            autoDeclarationApplicableFor
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                  ></CustomSelect>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[72].Field_Visiblity == 'Yes' &&
                    autoDeclaration === 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                  class="d-flex"
                >
                  <CustomSelect
                    :items="investmentCategoryList"
                    :itemSelected="investmentCategoryId"
                    :label="getFieldAlias[72].Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias[72].Mandatory_Field == 'Yes' ? true : false
                    "
                    item-title="Investment_Category"
                    item-value="Investment_Cat_Id"
                    :is-loading="isListLoading"
                    :disabled="isListLoading"
                    @selected-item="
                      onChangeIsFormDirty($event, 'investmentCategoryId')
                    "
                    :rules="[
                      getFieldAlias[72].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[72].Field_Alias}`,
                            investmentCategoryId
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                    listWidth="max-width: 300px !important"
                  ></CustomSelect>
                  <v-btn
                    color="white"
                    rounded="lg"
                    class="ml-2 mt-2"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="getInvestmentCategoryList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[73].Field_Visiblity == 'Yes' &&
                    slabWiseInsurance == 'No'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias[73].Field_Alias
                    }}</span>
                    <v-switch
                      color="secondary"
                      class="ml-2"
                      v-model="overrideInsuranceContributionAtEmployeeLevel"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>

                <v-col
                  v-if="getFieldAlias[74].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <CustomSelect
                    :items="paymentFrequencyList"
                    :itemSelected="paymentFrequency"
                    :label="getFieldAlias[74].Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias[74].Mandatory_Field == 'Yes' ? true : false
                    "
                    @selected-item="
                      onChangeIsFormDirty($event, 'paymentFrequency')
                    "
                    :rules="[
                      getFieldAlias[74].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[74].Field_Alias}`,
                            paymentFrequency
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                  ></CustomSelect>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias[75].Field_Visiblity == 'Yes' &&
                    slabWiseInsurance == 'No'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias[75].Field_Alias
                    }}</span>
                    <v-switch
                      color="secondary"
                      class="ml-2"
                      v-model="employeeStateInsurance"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      :disabled="isEmployeeStateInsuranceActive && !isEdit"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                    <NotesCard
                      v-if="isEmployeeStateInsuranceActive && !isEdit"
                      notes="You already have an ESI record created, and for an organization you can only have one configuration."
                      imageName=""
                      class="mt-n5 mb-1"
                      style="max-width: 400px"
                    ></NotesCard>
                  </div>
                </v-col>
                <v-col
                  v-if="getFieldAlias[76].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="v-label mb-4">
                    {{ getFieldAlias[76].Field_Alias }}
                    <span
                      v-if="getFieldAlias[76].Mandatory_Field == 'Yes'"
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </div>
                  <v-textarea
                    v-model="description"
                    variant="solo"
                    auto-grow
                    rows="1"
                    :rules="[
                      getFieldAlias[76].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[76].Field_Alias}`,
                            description
                          )
                        : true,
                      description
                        ? validateWithRulesAndReturnMessages(
                            description,
                            'description',
                            `${getFieldAlias[76].Field_Alias}`
                          )
                        : true,
                    ]"
                    @update:model-value="isFormDirty = true"
                    style="max-width: 400px"
                  ></v-textarea>
                </v-col>
                <v-col
                  v-if="getFieldAlias[77].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="v-label mb-4">
                    {{ getFieldAlias[77].Field_Alias }}
                  </div>
                  <AppToggleButton
                    button-active-text="Active"
                    button-inactive-text="Inactive"
                    button-active-color="#7de272"
                    button-inactive-color="red"
                    id-value="gab-analysis-based-on"
                    :current-value="status === 'Active' ? true : false"
                    :isDisableToggle="!isEdit"
                    @chosen-value="onChangeStatus($event)"
                  ></AppToggleButton>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
      <AppWarningModal
        v-if="openConfirmationPopup"
        :open-modal="openConfirmationPopup"
        confirmation-heading="Are you sure to exit this form?"
        imgUrl="common/exit_form"
        @close-warning-modal="abortClose()"
        @accept-modal="acceptClose()"
      >
      </AppWarningModal>
    </section>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
// Queries
import { LIST_INVESTMENT_CATEGORY } from "@/graphql/tax-and-statutory-compliance/providentFundRules";
import { ADD_UPDATE_INSURANCE_RULES } from "@/graphql/tax-and-statutory-compliance/insuranceType";

export default {
  name: "AddEditInsuranceType",
  mixins: [validationRules],
  props: {
    selectedItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    getFieldAlias: {
      type: Array,
      default: () => {
        return [];
      },
    },
    isEdit: {
      type: Boolean,
      required: true,
    },
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  components: {
    CustomSelect,
    NotesCard,
  },
  data() {
    return {
      isLoading: false,
      isMounted: false,
      isFormDirty: false,
      openConfirmationPopup: false,
      insuranceTypeId: null,
      insuranceName: null,
      slabWiseInsurance: "No",
      insuranceType: null,
      employerSharePercentage: null,
      employeeSharePercentage: null,
      employerShareAmount: null,
      employeeShareAmount: null,
      autoDeclaration: "No",
      autoDeclarationApplicableFor: "Employee",
      investmentCategoryId: null,
      overrideInsuranceContributionAtEmployeeLevel: "No",
      paymentFrequency: null,
      employeeStateInsurance: "No",
      description: "",
      status: "Active",
      insuranceTypeList: ["Fixed", "Variable"],
      paymentFrequencyList: ["Monthly", "Quarterly", "Half Yearly", "Annually"],
      showValidationAlert: false,
      validationMessages: [],
      investmentCategoryList: [],
      retrievedInvestmentId: null,
      isListLoading: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isEmployeeStateInsuranceActive() {
      return this.items.some((insurance) => {
        return (
          insurance.Employee_State_Insurance === "Yes" &&
          insurance.InsuranceType_Status === "Active"
        );
      });
    },
  },
  mounted() {
    if (this.isEdit) {
      const {
        InsuranceType_Id,
        Insurance_Name,
        Slab_Wise_Insurance,
        Insurance_Type,
        Employer_Share_Percentage,
        Employee_Share_Percentage,
        Employer_Share_Amount,
        Employee_Share_Amount,
        Auto_Declaration,
        Auto_Declaration_Applicable_For,
        Section_Investment_Category_Id,
        Override_Insurance_Contribution_At_Employee_Level,
        Payment_Frequency,
        Employee_State_Insurance,
        Description,
        InsuranceType_Status,
      } = this.selectedItem;
      this.insuranceTypeId = InsuranceType_Id ? InsuranceType_Id : null;

      this.insuranceName =
        this.getFieldAlias[63].Field_Visiblity == "Yes" && Insurance_Name
          ? Insurance_Name
          : null;

      this.slabWiseInsurance =
        this.getFieldAlias[64].Field_Visiblity == "Yes" && Slab_Wise_Insurance
          ? Slab_Wise_Insurance
          : "No";

      this.insuranceType =
        this.getFieldAlias[65].Field_Visiblity == "Yes" && Insurance_Type
          ? Insurance_Type
          : null;

      this.employerSharePercentage =
        this.getFieldAlias[66].Field_Visiblity == "Yes" &&
        Employer_Share_Percentage
          ? Employer_Share_Percentage
          : null;

      this.employeeSharePercentage =
        this.getFieldAlias[67].Field_Visiblity == "Yes" &&
        Employee_Share_Percentage
          ? Employee_Share_Percentage
          : null;

      this.employerShareAmount =
        this.getFieldAlias[68].Field_Visiblity == "Yes" && Employer_Share_Amount
          ? Employer_Share_Amount
          : null;

      this.employeeShareAmount =
        this.getFieldAlias[69].Field_Visiblity == "Yes" && Employee_Share_Amount
          ? Employee_Share_Amount
          : null;

      this.autoDeclaration =
        this.getFieldAlias[70].Field_Visiblity == "Yes" && Auto_Declaration
          ? Auto_Declaration
          : "No";

      this.autoDeclarationApplicableFor =
        this.getFieldAlias[71].Field_Visiblity == "Yes" &&
        Auto_Declaration_Applicable_For
          ? Auto_Declaration_Applicable_For
          : null;

      this.retrievedInvestmentId =
        this.getFieldAlias[72].Field_Visiblity == "Yes" &&
        Section_Investment_Category_Id
          ? Section_Investment_Category_Id
          : null;

      this.overrideInsuranceContributionAtEmployeeLevel =
        this.getFieldAlias[73].Field_Visiblity == "Yes" &&
        Override_Insurance_Contribution_At_Employee_Level
          ? Override_Insurance_Contribution_At_Employee_Level
          : "No";

      this.paymentFrequency =
        this.getFieldAlias[74].Field_Visiblity == "Yes" && Payment_Frequency
          ? Payment_Frequency
          : null;

      this.employeeStateInsurance =
        this.getFieldAlias[75].Field_Visiblity == "Yes" &&
        Employee_State_Insurance
          ? Employee_State_Insurance
          : "No";

      this.description =
        this.getFieldAlias[76].Field_Visiblity == "Yes" && Description
          ? Description
          : "";

      this.status =
        this.getFieldAlias[77].Field_Visiblity == "Yes" && InsuranceType_Status
          ? InsuranceType_Status
          : "No";

      this.getInvestmentCategoryList("Edit");
    }
    this.isMounted = true;
  },
  methods: {
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
    },
    onChangeIsFormDirty(val, field) {
      if (field == "paymentFrequency") {
        this.paymentFrequency = val;
      } else if (field == "insuranceType") {
        this.insuranceType = val;
        this.employeeSharePercentage = null;
        this.employerSharePercentage = null;
        this.employeeShareAmount = null;
        this.employerShareAmount = null;
      } else if (field == "autoDeclarationApplicableFor") {
        this.autoDeclarationApplicableFor = val;
      } else if (field == "investmentCategoryId") {
        this.investmentCategoryId = val;
      } else if (field == "autoDeclaration") {
        this.investmentCategoryId = null;
        this.getInvestmentCategoryList();
      } else if (field == "slabWiseInsurance") {
        this.insuranceType = null;
        this.employeeSharePercentage = null;
        this.employerSharePercentage = null;
        this.employeeShareAmount = null;
        this.employerShareAmount = null;
        this.overrideInsuranceContributionAtEmployeeLevel = "No";
      }
      this.isFormDirty = true;
    },
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "Inactive";
      this.isFormDirty = true;
    },
    async validateForm() {
      const { valid } = await this.$refs.insuranceType.validate();
      if (valid) {
        this.updateData();
      }
    },
    updateData() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_INSURANCE_RULES,
            variables: {
              insuranceTypeId: vm.insuranceTypeId
                ? parseInt(vm.insuranceTypeId)
                : 0,
              insuranceName: vm.insuranceName ? vm.insuranceName : null,
              slabWiseInsurance: vm.slabWiseInsurance
                ? vm.slabWiseInsurance
                : "No",
              insuranceType: vm.insuranceType ? vm.insuranceType : null,
              employerSharePercentage: vm.employerSharePercentage
                ? parseFloat(vm.employerSharePercentage)
                : null,
              employeeSharePercentage: vm.employeeSharePercentage
                ? parseFloat(vm.employeeSharePercentage)
                : null,
              employerShareAmount: vm.employerShareAmount
                ? parseFloat(vm.employerShareAmount)
                : null,
              employeeShareAmount: vm.employeeShareAmount
                ? parseFloat(vm.employeeShareAmount)
                : null,
              autoDeclaration: vm.autoDeclaration ? vm.autoDeclaration : "No",
              autoDeclarationApplicableFor:
                vm.autoDeclaration == "Yes"
                  ? vm.autoDeclarationApplicableFor
                  : null,
              sectionInvestmentCategoryId:
                vm.autoDeclaration == "Yes"
                  ? parseInt(vm.investmentCategoryId)
                  : null,
              overrideInsuranceContributionAtEmployeeLevel:
                vm.overrideInsuranceContributionAtEmployeeLevel
                  ? vm.overrideInsuranceContributionAtEmployeeLevel
                  : "No",
              paymentFrequency: vm.paymentFrequency
                ? vm.paymentFrequency
                : null,
              employeeStateInsurance: vm.employeeStateInsurance
                ? vm.employeeStateInsurance
                : "No",
              description: vm.description,
              insuranceTypeStatus: vm.status ? vm.status : "Inactive",
            },
            client: "apolloClientAK",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `${this.accessFormName} updated successfully.`,
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((error) => {
            vm.handleUpdateError(error);
          });
      } catch {
        vm.handleUpdateError((err = ""));
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: this.accessFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    closeEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
      this.isFormDirty = false;
    },
    getInvestmentCategoryList(action) {
      let vm = this;
      vm.isListLoading = true;
      vm.$apollo
        .query({
          query: LIST_INVESTMENT_CATEGORY,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listInvestmentCategory) {
            vm.investmentCategoryList =
              response.data.listInvestmentCategory.investmentCategoriesData;
            if (action == "Edit") {
              vm.investmentCategoryId = vm.retrievedInvestmentId;
            }
          } else {
            vm.handleListError((err = ""), "Investment Category");
          }
          vm.isListLoading = false;
        })
        .catch((err) => {
          vm.isListLoading = false;
          vm.handleListError(err, "Investment Category");
        });
    },
    handleListError(err = "", formName) {
      this.isListLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: formName,
        isListError: false,
      });
    },
  },
};
</script>
