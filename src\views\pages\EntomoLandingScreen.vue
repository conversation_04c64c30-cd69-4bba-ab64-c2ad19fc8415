<template>
  <div>
    <AppFetchErrorScreen
      v-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      button-text="Retry"
      @button-click="getUserSigninDetails"
    >
    </AppFetchErrorScreen>

    <AppLoading v-else></AppLoading>
  </div>
</template>

<script>
import { GET_ENTOMO_USER_DETAILS } from "@/graphql/layout/unAuthenticateLayoutQueries.js";

export default {
  name: "EntomoLandingScreen",

  data: () => ({
    decryptedUserName: "",
    isErrorInList: false,
    errorContent: "",
  }),

  computed: {
    // returns baseurl of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  mounted() {
    let queryParameters = this.$route.query;
    // if query params exist
    if (queryParameters && queryParameters.username) {
      this.decryptedUserName = decodeURIComponent(queryParameters.username);
      this.getUserSigninDetails();
    } else {
      this.handleListError();
    }
  },

  methods: {
    getUserSigninDetails() {
      let vm = this;
      vm.isErrorInList = false;
      try {
        vm.$apollo
          .query({
            query: GET_ENTOMO_USER_DETAILS,
            client: "apolloClientH",
            fetchPolicy: "no-cache",
            variables: {
              userName: vm.decryptedUserName,
            },
          })
          .then((authResponse) => {
            const { authDetails } = authResponse.data.getEntomoUserAuthDetails;
            if (authDetails) {
              let parsedAuthResponse = JSON.parse(authDetails);
              const {
                xAuthToken,
                // accessTokenExpiry,
                userId,
                xRefreshToken,
                xRefreshTokenExpiry,
                partnerId,
                partnerBaseUrl,
              } = parsedAuthResponse;
              /** Get the current date. Example: Tue Feb 16 2021 17:52:13 GMT+0530 (India Standard Time) */
              let currentDateTime = new Date();
              /** Add 1 day to the current date. Example: Wed Feb 17 2021 17:52:13 GMT+0530 (India Standard Time) */
              currentDateTime.setDate(currentDateTime.getDate() + 1);
              /** Set the 12AM for the next date following the current date. Example: Wed Feb 17 2021 00:00:00 GMT+0530 (India Standard Time) */
              let nextDayDateTime = new Date(
                currentDateTime.getFullYear(),
                currentDateTime.getMonth(),
                currentDateTime.getDate(),
                0,
                0,
                0,
                0
              );
              /** Add 90 days to the next date following the current date. Example: Tue May 18 2021 00:00:00 GMT+0530 (India Standard Time) */
              nextDayDateTime.setTime(
                nextDayDateTime.getTime() + 90 * 86400 * 1000
              );
              /** The refresh token expiry day is 15. The refresh token and empUId - expiry date will be calculated
               * by considering the next day following the current date as the start date. */
              let refreshTokenExpiryTime = nextDayDateTime;

              /** For the empUid and refreshToken, the actual expiry date time is converted to the UTC date-time and set as the expiry date-time 
                  in the cookie. Example expiry date time: Tue, 18 May 2021 18:30:00 GMT (Refers the IST, May 18, 2021 12 AM). */
              let refreshTokenExpiryTimeStr =
                refreshTokenExpiryTime.toUTCString();

              // have to change this in future once firebase user id we have added in headers
              window.$cookies.set(
                "accessToken",
                xAuthToken,
                refreshTokenExpiryTimeStr
              );

              window.$cookies.set(
                "refreshToken",
                xRefreshToken,
                xRefreshTokenExpiry
              );
              window.$cookies.set("empUid", userId, xRefreshTokenExpiry);
              window.$cookies.set("partnerid", partnerId, xRefreshTokenExpiry);
              window.$cookies.set(
                "partnerBaseUrl",
                partnerBaseUrl,
                xRefreshTokenExpiry
              );
              let queryParameters = this.$route.query;
              if (queryParameters && queryParameters.username) {
                window.$cookies.set(
                  "entomoUserName",
                  encodeURIComponent(queryParameters.username),
                  xRefreshTokenExpiry
                );
              }
              vm.redirectToDashboard();
            } else {
              vm.handleListError();
            }
          })
          .catch((err) => {
            vm.handleListError(err);
          });
      } catch (err) {
        vm.handleListError();
      }
    },

    async redirectToDashboard() {
      await this.$store.dispatch("getPlanType").then((dashboardType) => {
        this.$store.commit("UPDATE_PLAN_DASHBOARD_TYPE", dashboardType);
        // if the plan is EMPLOYEEMONITORING, then we have to redirect to activity-dashboard
        if (dashboardType === "EMPLOYEEMONITORINGDASHBOARD") {
          window.location.href =
            this.baseUrl + "in/productivity-monitoring/activity-dashboard";
        }
        // if the plan is RECRUITMENTDASHBOARD, then we have to redirect to recruitment-dashboard
        else if (dashboardType === "RECRUITMENTDASHBOARD") {
          this.$router.push("/recruitment/dashboard");
        } else {
          // otherwise, then we have to redirect to dashboard
          window.location.href = this.baseUrl + "in/";
        }
      });
    },

    handleListError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "authenticating",
          form: "entomo",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
