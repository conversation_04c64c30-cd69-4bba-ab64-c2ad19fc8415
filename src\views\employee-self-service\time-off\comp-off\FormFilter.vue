<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedWorkedDate"
              :items="workedDateList"
              item-title="workedDate"
              label="Worked Date"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedDuration"
              :items="durationList"
              label="Duration"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedCompOffDate"
              :items="compOffDateList"
              item-title="compOffDate"
              label="Comp Off Date"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <!-- <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedCompOffApplicability"
              color="primary"
              :items="compOffApplicabilityList"
              label="Manager Name"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col> -->
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="statusList"
              label="Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
export default defineComponent({
  name: "FormFilter",

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },

  data: () => ({
    openFormFilter: false,
    durationList: ["Full Day", "Half Day"],
    selectedDuration: [],
    statusList: [
      "Applied",
      "Approved",
      "Cancel Applied",
      "Cancelled",
      "Rejected",
      "Returned",
    ],
    selectedStatus: [],
    compOffDateList: [],
    selectedCompOffDate: [],
    workedDateList: [],
    selectedWorkedDate: [],
    dropdownListFetching: false,
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.fnApplyFilter();
    this.formFilterData();
  },
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.items;

      if (this.selectedDuration.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDuration.includes(item.Duration);
        });
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Approval_Status);
        });
      }
      if (this.selectedCompOffDate.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCompOffDate.includes(item.Compensatory_Date);
        });
      }
      if (this.selectedWorkedDate.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedWorkedDate.includes(item.Compensated_Date);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    formFilterData() {
      for (let item of this.items) {
        if (item && item.Employee_Name) {
          this.workedDateList.push({
            workedDate: item.Compensated_Date,
          });
          this.compOffDateList.push({
            compOffDate: item.Compensatory_Date,
          });
        }
      }
      this.workedDateList = this.removeDuplicatesFromArrayOfObject(
        this.workedDateList,
        "workedDate"
      );
      this.compOffDateList = this.removeDuplicatesFromArrayOfObject(
        this.compOffDateList,
        "compOffDate"
      );
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedDuration = [];
      this.selectedStatus = [];
      this.selectedCompOffDate = [];
      this.selectedWorkedDate = [];
      this.openFormFilter = false;
    },
  },
});
</script>
