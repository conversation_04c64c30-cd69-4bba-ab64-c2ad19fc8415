<template>
  <div>
    <v-card class="rounded-lg">
      <div
        v-if="!showEmployeesList"
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="primary" size="20">fas fa-file-alt</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">LOP Recovery</div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-tooltip v-model="showToolTip" location="right">
            <template v-slot:activator="{ props }">
              <v-btn
                v-bind="isCustomGroupToolTip ? props : ''"
                @click="isCustomGroupToolTip ? null : $emit('open-edit-form')"
                size="small"
                color="primary"
                variant="elevated"
                rounded="lg"
                v-if="accessRights.update"
                :class="isCustomGroupToolTip ? 'cursor-not-allow' : ''"
                >Edit</v-btn
              >
            </template>
            <div style="width: 200px !important; height: 150px !important">
              LOP recovery configuration is set to
              {{
                coverage === "Organization" ? "Custom Group" : "Organization"
              }}, conflicting with the platform's {{ coverage }} level setting.
              Please adjust the platform to
              {{
                coverage === "Organization" ? "Custom Group" : "Organization"
              }}
              or establish a new configuration.
            </div>
          </v-tooltip>
          <v-icon class="mx-1" color="primary" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card v-if="!showEmployeesList">
        <div
          :style="
            isMobileView ? 'height: calc(100vh - 200px); overflow: scroll' : ''
          "
        >
          <v-card-text>
            <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="editedLopRecoveryDetails.Custom_Group_Id"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Custom Group</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedLopRecoveryDetails.Group_Name) }}
                </p>
              </v-col>
              <v-col
                v-if="editedLopRecoveryDetails.Custom_Group_Id"
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <div v-if="isLoadingCard">
                  <v-skeleton-loader
                    type="list-item-two-line"
                    class="ml-n4 mt-n2"
                    width="80%"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >Employees - {{ empListInSelectedGroup.length }}</span
                  >
                  <div
                    v-if="empListInSelectedGroup.length === 0"
                    class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  >
                    <v-icon color="warning" size="25"
                      >fas fa-exclamation-triangle</v-icon
                    >
                    <span
                      v-if="errorInFetchEmployeesList"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                      >Something went wrong while fetching the employees list.
                      Please try again.
                      <a class="text-primary" @click="fetchCustomEmployeesList"
                        >Refresh
                      </a>
                    </span>
                    <span
                      v-else-if="isNoEmployees"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                    >
                      It seems like there are no employees associated with the
                      selected custom group. Please add some employees under the
                      selected group or try choosing an another group.</span
                    >
                  </div>
                  <div v-else class="d-flex align-center">
                    <AvatarOrderedList
                      v-if="empListInSelectedGroup.length > 0"
                      class="mt-1"
                      :ordered-list="empListInSelectedGroup"
                    ></AvatarOrderedList>
                    <v-btn
                      rounded="lg"
                      color="primary"
                      size="small"
                      class="mt-1"
                      @click="openCustomGroupEmpList()"
                    >
                      View All
                    </v-btn>
                  </div>
                </div>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Workflow Name</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedLopRecoveryDetails.Workflow_Name) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  LOP Recovery Applicable For Auto Initiated LOP
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(editedLopRecoveryDetails.Auto_LOP_Applicable)
                  }}
                </p>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  LOP Recovery Applicable For Attendance Shortage
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(
                      editedLopRecoveryDetails.Attendance_Shortage_Applicable
                    )
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  LOP Recovery Applicable For Late Attendance
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(
                      editedLopRecoveryDetails.Late_Attendance_Applicable
                    )
                  }}
                </p>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Status</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(
                      editedLopRecoveryDetails.Configuration_Status
                    )
                  }}
                </p>
              </v-col>
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-card-text>
        </div>
      </v-card>
      <div
        v-if="showEmployeesList"
        style="overflow: scroll"
        :style="
          isMobileView
            ? 'height: calc(100vh - 200px)'
            : 'height: calc(100vh - 200px)'
        "
      >
        <div class="d-flex ma-2">
          <v-btn
            rounded="lg"
            color="primary"
            @click="showEmployeesList = false"
          >
            <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
            Back
          </v-btn>
        </div>
        <EmployeeListCard
          v-if="showEmployeesList"
          :show-modal="showEmployeesList"
          modal-title="Custom Group Employee(s)"
          :employeesList="empListForComponent"
          :selectable="false"
          :showFilter="false"
          :showFilterSearch="true"
          :isApplyFilter="true"
          @close-modal="showEmployeesList = false"
        ></EmployeeListCard>
      </div>
    </v-card>
  </div>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import EmployeeListCard from "@/components/helper-components/EmployeeListCard.vue";
import { convertUTCToLocal } from "@/helper.js";
import { checkNullValue } from "@/helper.js";
export default {
  name: "ViewLopRecovery",
  components: {
    MoreDetails,
    EmployeeListCard,
    AvatarOrderedList,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
    coverage: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    editedLopRecoveryDetails: {},
    showToolTip: false,
    showEmployeesList: false,
    empListInSelectedGroup: [],
    isNoEmployees: false,
    isLoadingCard: false,
    errorInFetchEmployeesList: false,
    empListForComponent: [],
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isCustomGroupToolTip() {
      return (
        (this.coverage === "Organization" &&
          this.editedLopRecoveryDetails.Custom_Group_Id !== null) ||
        (this.coverage === "Custom Group" &&
          this.editedLopRecoveryDetails.Custom_Group_Id === null)
      );
    },
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.editedLopRecoveryDetails = Object.assign({}, newData);
        this.prefillMoreDetails();
        this.fetchCustomEmployeesList();
      },
    },
  },
  methods: {
    convertUTCToLocal,
    checkNullValue,
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const Added_On = this.convertUTCToLocal(this.selectedItem.Added_On),
        Added_By = this.selectedItem.Added_By,
        Updated_By = this.selectedItem.Updated_By,
        Updated_On = this.convertUTCToLocal(this.selectedItem.Updated_On);
      if (Added_On && Added_By) {
        this.moreDetailsList.push({
          actionDate: Added_On,
          actionBy: Added_By,
          text: "Added",
        });
      }
      if (Updated_By && Updated_On) {
        this.moreDetailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By,
          text: "Updated",
        });
      }
    },
    // on changing the custom group we need to fetch the employees list relevant to the selected group
    async fetchCustomEmployeesList() {
      if (this.editedLopRecoveryDetails.Custom_Group_Id) {
        let vm = this;
        vm.isLoadingCard = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(
              vm.editedLopRecoveryDetails.Custom_Group_Id
            ),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
  },
};
</script>
