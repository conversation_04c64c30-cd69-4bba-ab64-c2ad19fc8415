import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const LIST_EMPLOYEE_TYPES = gql`
  query listEmployeeTypes {
    listEmployeeTypes {
      errorCode
      message
      employeeTypes
    }
  }
`;

export const LIST_WORK_PLACES = gql`
  query ListWorkPlaces($status: String, $employeeId: Int) {
    listWorkPlaces(status: $status, employeeId: $employeeId) {
      errorCode
      message
      workPlaces
      employeeWorkPlace
    }
  }
`;

export const ADD_UPDATE_EMPLOYEE_TYPE = gql`
  mutation addUpdateEmployeeType(
    $EmpType_Id: Int
    $Employee_Type: String!
    $Benefits_Applicable: BinaryEnum!
    $Holiday_Eligiblity: BinaryEnum!
    $Salary_Calc_Days: Int!
    $Fixed_Days: Int
    $Comp_Off_Days: Int!
    $Comp_Off_Fixed_Days: Int
    $Display_Total_Hours_In_Minutes: BinaryEnum!
    $Exclude_Break_Hours: BinaryEnum!
    $Work_Schedule: String!
    $Enable_Work_Place: BinaryEnum!
    $Attendance_Process_Status: String!
    $Approve_Dashboard_Attendance: String!
    $EmployeeType_Status: Status!
    $Description: String
    $Employee_Type_Code: String
    $Work_Place_Id: [PositiveInt]
    $Auto_Approval_Work_Place_Id: [PositiveInt]
    $Level: PositiveInt
  ) {
    addUpdateEmployeeType(
      EmpType_Id: $EmpType_Id
      Employee_Type: $Employee_Type
      Benefits_Applicable: $Benefits_Applicable
      Holiday_Eligiblity: $Holiday_Eligiblity
      Salary_Calc_Days: $Salary_Calc_Days
      Fixed_Days: $Fixed_Days
      Comp_Off_Days: $Comp_Off_Days
      Comp_Off_Fixed_Days: $Comp_Off_Fixed_Days
      Display_Total_Hours_In_Minutes: $Display_Total_Hours_In_Minutes
      Exclude_Break_Hours: $Exclude_Break_Hours
      Work_Schedule: $Work_Schedule
      Enable_Work_Place: $Enable_Work_Place
      Attendance_Process_Status: $Attendance_Process_Status
      Approve_Dashboard_Attendance: $Approve_Dashboard_Attendance
      EmployeeType_Status: $EmployeeType_Status
      Description: $Description
      Employee_Type_Code: $Employee_Type_Code
      Work_Place_Id: $Work_Place_Id
      Auto_Approval_Work_Place_Id: $Auto_Approval_Work_Place_Id
      Level: $Level
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_EMPLOYEE_TYPE = gql`
  mutation deleteEmployeeType($EmpType_Id: PositiveInt!) {
    deleteEmployeeType(EmpType_Id: $EmpType_Id) {
      errorCode
      message
    }
  }
`;
