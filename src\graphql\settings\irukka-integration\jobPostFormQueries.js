import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const GET_COURSE_LIST = gql`
  query getCourseList {
    getCourseList {
      errorCode
      message
      Courses {
        Course_Id
        Course_Name
      }
    }
  }
`;

export const GET_USER_LIST = gql`
  query getUsers {
    getUsers {
      errorCode
      message
      users {
        UserId
        UserName
        userDefinedEmployeeId
      }
    }
  }
`;

export const POSTED_JOBS = gql`
  query postedJobs(
    $formId: Int
    $searchString: String
    $designation: Int
    $location: [Int]
    $functionalArea: Int
    $jobType: Int
    $closingDate: Date
    $status: Int
    $skills: [Int]
    $qualification: [Int]
    $employeeId: Int
    $isDropDownCall: Int!
  ) {
    listJobPost(
      formId: $formId
      searchString: $searchString
      designation: $designation
      location: $location
      functionalArea: $functionalArea
      jobType: $jobType
      closingDate: $closingDate
      status: $status
      skills: $skills
      qualification: $qualification
      employeeId: $employeeId
      isDropDownCall: $isDropDownCall
    ) {
      errorCode
      message
      JobpostDetails {
        Job_Post_Id
        Job_Post_Name
        Client_Name
        Posting_Date
        Added_By
        Job_Post_Status
        Status_Id
        Event_Id
        No_Of_Vacancies
        Closing_Date
        Interview_Status
        Service_Provider_Id
        Number_Of_Applicants
        Department_Name
        State_Name
        City_Name
        Country_Name
        Job_Type
        Job_Description
        Experience_Level
        Service_Provider_Id
        Service_Provider_Name
        Service_Provider_Code
      }
    }
  }
`;

export const LIST_JOB_TITLE = gql`
  query jobTitleList($Form_Id: Int!) {
    jobTitleList(Form_Id: $Form_Id) {
      errorCode
      message
      jobTitleResult {
        Organization_Structure_Id
        Pos_Name
      }
    }
  }
`;

export const GET_CUSTOM_GROUP_COVERAGE = gql`
  query recruitmentSetting {
    recruitmentSetting {
      errorCode
      message
      settingResult {
        Coverage
        Setting_Id
        Centralised_Recruitment
        Show_Job_Rounds_Tab
      }
    }
  }
`;

export const ADD_UPDATE_JOB_POST_ROUNDS = gql`
  mutation addUpdateJobPostRounds($jobPostId: Int!, $roundIds: [Int!]) {
    addUpdateJobPostRounds(jobPostId: $jobPostId, roundIds: $roundIds) {
      errorCode
      message
      validationError
    }
  }
`;

export const RECRUITMENT_SETTINGS = gql`
  query recruitmentSetting {
    recruitmentSetting {
      errorCode
      message
      settingResult {
        Coverage
        Setting_Id
        Candidate_Portal_Login_Access
        Centralised_Recruitment
        Equal_opportunity_stmt
        MPP_Integration
      }
    }
  }
`;

export const RETRIEVE_POSITION_JOB_SUMMARY = gql`
  query retrievePositionJobSummary(
    $originalPositionId: String!
    $formId: Int!
    $source: String
    $mppPositionType: String
    $positionRequestId: Int
  ) {
    retrievePositionJobSummary(
      originalPositionId: $originalPositionId
      formId: $formId
      source: $source
      mppPositionType: $mppPositionType
      positionRequestId: $positionRequestId
    ) {
      errorCode
      message
      vacancyAvailable
      summary {
        Job_Description
        Career_Band
        Job_Summary
        Skills_Competencies
        Education
        Experience
        Working_Conditions
        Working_Relationship_Internal
        Working_Relationship_External
        Minimum_Requirements
        Company_Id
      }
    }
  }
`;

//mutate

// ===============
// Mutations
// ===============
export const ADD_JOB_POST = gql`
  mutation myMutation(
    $mppPositionType: String
    $positionRequestId: Int
    $clientId: Int
    $jobPostName: String
    $functionalArea: Int!
    $designation: Int
    $minWorkExperience: Float
    $maxWorkExperience: Float
    $jobType: Int!
    $paymentType: Int
    $currency: Int
    $minPaymentFrequency: Float
    $maxPaymentFrequency: Float
    $noOfVacancies: Int!
    $jobDescription: String!
    $postingDate: Date!
    $closingDate: Date
    $status: Int
    $reasonForOpening: String
    $expectedJoiningDate: Date
    $priority: String
    $travelRequired: Int
    $agencyInvolved: String
    $jobDuration: Int
    $requiredCertificationId: Int
    $jobPostWorkflow: Int
    $addedBy: Int!
    $keySkills: [Int!]
    $workAuthorization: [Int]
    $otherWorkAuthorization: String
    $jobLocations: [Int]
    $rounds: [Int!]
    $replacementFor: [Int]
    $qualification: [Int!]
    $coolingPeriod: Float
    $Address: String
    $organizationGroupId: Int
    $No_Of_Male_Vacancies: Int
    $No_Of_Female_Vacancies: Int
    $Category_Id: Int
    $Category_Name: String
    $Subcategory_Id: Int
    $Subcategory_Name: String
    $Industry_Id: Int
    $Industry_Name: String
    $Resource_Id_Male: Int
    $Resource_Id_Female: Int
    $Skill_Set: [String!]
    $Latitude: String
    $Longitude: String
    $Pincode: String
    $otherReasonForOpening: String
    $Event_Id: String
    $serviceProviderId: Int
    $jobLocationType: String
    $WorkPlaceType: String
    $customGroupId: Int
    $experienceLevel: String
    $countryCode: String
    $stateId: Int
    $cityId: Int
    $organizationStructureId: Int
    $hiringManagerId: Int
    $hiringManagerName: String
    $payType: String
    $customFieldInputs: customFieldInputs
  ) {
    addJobPost(
      mppPositionType: $mppPositionType
      positionRequestId: $positionRequestId
      clientId: $clientId
      jobPostName: $jobPostName
      functionalArea: $functionalArea
      designation: $designation
      minWorkExperience: $minWorkExperience
      maxWorkExperience: $maxWorkExperience
      jobType: $jobType
      paymentType: $paymentType
      currency: $currency
      minPaymentFrequency: $minPaymentFrequency
      maxPaymentFrequency: $maxPaymentFrequency
      noOfVacancies: $noOfVacancies
      jobDescription: $jobDescription
      postingDate: $postingDate
      closingDate: $closingDate
      status: $status
      reasonForOpening: $reasonForOpening
      expectedJoiningDate: $expectedJoiningDate
      priority: $priority
      travelRequired: $travelRequired
      agencyInvolved: $agencyInvolved
      jobDuration: $jobDuration
      requiredCertificationId: $requiredCertificationId
      jobPostWorkflow: $jobPostWorkflow
      keySkills: $keySkills
      workAuthorization: $workAuthorization
      otherWorkAuthorization: $otherWorkAuthorization
      jobLocations: $jobLocations
      rounds: $rounds
      replacementFor: $replacementFor
      qualification: $qualification
      coolingPeriod: $coolingPeriod
      Address: $Address
      organizationGroupId: $organizationGroupId
      No_Of_Male_Vacancies: $No_Of_Male_Vacancies
      No_Of_Female_Vacancies: $No_Of_Female_Vacancies
      Category_Id: $Category_Id
      Category_Name: $Category_Name
      Subcategory_Id: $Subcategory_Id
      Subcategory_Name: $Subcategory_Name
      Industry_Id: $Industry_Id
      Industry_Name: $Industry_Name
      Resource_Id_Male: $Resource_Id_Male
      Resource_Id_Female: $Resource_Id_Female
      Skill_Set: $Skill_Set
      Latitude: $Latitude
      Longitude: $Longitude
      Pincode: $Pincode
      otherReasonForOpening: $otherReasonForOpening
      Event_Id: $Event_Id
      addedBy: $addedBy
      serviceProviderId: $serviceProviderId
      jobLocationType: $jobLocationType
      WorkPlaceType: $WorkPlaceType
      customGroupId: $customGroupId
      experienceLevel: $experienceLevel
      countryCode: $countryCode
      stateId: $stateId
      cityId: $cityId
      organizationStructureId: $organizationStructureId
      hiringManagerId: $hiringManagerId
      hiringManagerName: $hiringManagerName
      payType: $payType
      customFieldInputs: $customFieldInputs
    ) {
      errorCode
      message
      validationError
      data
    }
  }
`;

export const UPDATE_JOB_POST = gql`
  mutation myMutation(
    $jobPostId: Int!
    $clientId: Int
    $jobPostName: String!
    $functionalArea: Int!
    $designation: Int!
    $minWorkExperience: Float
    $maxWorkExperience: Float
    $jobType: Int!
    $paymentType: Int!
    $currency: Int
    $minPaymentFrequency: Float
    $maxPaymentFrequency: Float
    $noOfVacancies: Int!
    $jobDescription: String!
    $postingDate: Date!
    $closingDate: Date
    $status: Int
    $reasonForOpening: String
    $expectedJoiningDate: Date
    $priority: String
    $travelRequired: Int
    $agencyInvolved: String
    $jobDuration: Int
    $jobPostWorkflow: Int!
    $keySkills: [Int!]
    $workAuthorization: [Int]
    $otherWorkAuthorization: String
    $jobLocations: [Int!]
    $rounds: [Int!]
    $replacementFor: [Int!]
    $qualification: [Int!]
    $coolingPeriod: Float
    $Address: String
    $No_Of_Male_Vacancies: Int
    $No_Of_Female_Vacancies: Int
    $Category_Id: Int
    $Category_Name: String
    $Subcategory_Id: Int
    $Subcategory_Name: String
    $Industry_Id: Int
    $requiredCertificationId: Int
    $Industry_Name: String
    $Resource_Id_Male: Int
    $Resource_Id_Female: Int
    $Skill_Set: [String!]
    $Latitude: String
    $Longitude: String
    $Pincode: String
    $otherReasonForOpening: String
    $updatedBy: Int!
  ) {
    editJobPost(
      jobPostId: $jobPostId
      clientId: $clientId
      jobPostName: $jobPostName
      functionalArea: $functionalArea
      designation: $designation
      minWorkExperience: $minWorkExperience
      maxWorkExperience: $maxWorkExperience
      jobType: $jobType
      paymentType: $paymentType
      currency: $currency
      minPaymentFrequency: $minPaymentFrequency
      maxPaymentFrequency: $maxPaymentFrequency
      noOfVacancies: $noOfVacancies
      jobDescription: $jobDescription
      postingDate: $postingDate
      closingDate: $closingDate
      status: $status
      reasonForOpening: $reasonForOpening
      expectedJoiningDate: $expectedJoiningDate
      priority: $priority
      travelRequired: $travelRequired
      agencyInvolved: $agencyInvolved
      jobDuration: $jobDuration
      requiredCertificationId: $requiredCertificationId
      jobPostWorkflow: $jobPostWorkflow
      keySkills: $keySkills
      workAuthorization: $workAuthorization
      otherWorkAuthorization: $otherWorkAuthorization
      jobLocations: $jobLocations
      rounds: $rounds
      replacementFor: $replacementFor
      qualification: $qualification
      coolingPeriod: $coolingPeriod
      Address: $Address
      No_Of_Male_Vacancies: $No_Of_Male_Vacancies
      No_Of_Female_Vacancies: $No_Of_Female_Vacancies
      Category_Id: $Category_Id
      Category_Name: $Category_Name
      Subcategory_Id: $Subcategory_Id
      Subcategory_Name: $Subcategory_Name
      Industry_Id: $Industry_Id
      Industry_Name: $Industry_Name
      Resource_Id_Male: $Resource_Id_Male
      Resource_Id_Female: $Resource_Id_Female
      Skill_Set: $Skill_Set
      Latitude: $Latitude
      Longitude: $Longitude
      Pincode: $Pincode
      otherReasonForOpening: $otherReasonForOpening
      updatedBy: $updatedBy
    ) {
      errorCode
      message
      validationError
    }
  }
`;

export const DELETE_JOB_POST = gql`
  mutation myMutation($jobPostId: Int!, $employeeId: Int!) {
    deleteJobPost(jobPostId: $jobPostId, employeeId: $employeeId) {
      errorCode
      message
      validationError
    }
  }
`;

export const CLOSE_JOB_POST = gql`
  mutation closeJobPost($Job_Post_Id: Int!, $Status: Int!) {
    closeJobPost(Job_Post_Id: $Job_Post_Id, Status: $Status) {
      errorCode
      message
    }
  }
`;

export const LIST_WORKFLOW = gql`
  mutation MyMutation(
    $employeeId: Int!
    $searchString: String!
    $moduleId: [String!]
    $isDropDownCall: Int!
  ) {
    listWorkflow(
      employeeId: $employeeId
      searchString: $searchString
      moduleId: $moduleId
      isDropDownCall: $isDropDownCall
    ) {
      errorCode
      message
      Workflows {
        Workflow_Id
        Workflow_Name
        Event_Id
        Workflow_Unique_Id
        Workflow_Module_Id
        Description
        Default_Workflow
        Workflow_Image
        Form_Id
        Form_Name
        Module_Id
        Module_Name
        Designations {
          Designation_Id
          Designation_Name
        }
      }
    }
  }
`;

export const UPDATE_POSITION_JOB_SUMMARY = gql`
  mutation updatePositionJobSummary(
    $formId: Int!
    $jobDescription: String!
    $approvedPosition: Int!
    $warmBodies: Int!
    $originalPositionId: String!
  ) {
    updatePositionJobSummary(
      formId: $formId
      jobDescription: $jobDescription
      approvedPosition: $approvedPosition
      warmBodies: $warmBodies
      originalPositionId: $originalPositionId
    ) {
      errorCode
      message
      validationError
    }
  }
`;
