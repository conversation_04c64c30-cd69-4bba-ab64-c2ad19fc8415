import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const RETRIEVE_DYNAMIC_FORM_FIELDS = gql`
  query retrieveDynamicFormFields($formId: Int) {
    retrieveDynamicFormFields(formId: $formId) {
      errorCode
      message
      dynamicFields
    }
  }
`;
export const RETRIEVE_DYNAMIC_FIELD_VALUES = gql`
  query retrieveDynamicFieldValues($formId: Int!, $primaryId: Int!) {
    retrieveDynamicFieldValues(formId: $formId, primaryId: $primaryId) {
      errorCode
      message
      dynamicFieldValues
    }
  }
`;

// ===============
// Mutation
// ===============
export const ADD_UPDATE_CUSTOM_FIELD = gql`
  mutation addUpdateCustomField(
    $Custom_Field_Id: Int
    $Custom_Field_Name: String!
    $Custom_Field_Type: String!
    $Min_Validation: Int
    $Max_Validation: Int
    $Validation_Id: Int
    $Url_Link: String
    $Dropdown_Values: [String]
    $Roles_Id: [Int]
    $Form_Id_Associated: [Form_Id_Associated]
    $Visibility_Condition: Visibility_Condition
  ) {
    addUpdateCustomField(
      Custom_Field_Id: $Custom_Field_Id
      Custom_Field_Name: $Custom_Field_Name
      Custom_Field_Type: $Custom_Field_Type
      Min_Validation: $Min_Validation
      Max_Validation: $Max_Validation
      Validation_Id: $Validation_Id
      Url_Link: $Url_Link
      Dropdown_Values: $Dropdown_Values
      Roles_Id: $Roles_Id
      Form_Id_Associated: $Form_Id_Associated
      Visibility_Condition: $Visibility_Condition
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_CUSTOM_FIELD_VALUES = gql`
  mutation addUpdateCustomFieldValues(
    $Primary_Id: Int!
    $Form_Id: Int!
    $Field_Value: String!
  ) {
    addUpdateCustomFieldValues(
      Primary_Id: $Primary_Id
      Form_Id: $Form_Id
      Field_Value: $Field_Value
    ) {
      errorCode
      message
    }
  }
`;
