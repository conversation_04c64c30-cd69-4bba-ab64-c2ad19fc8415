import { createI18n } from "vue-i18n";
import coreHr from "./core-hr";
import productivityMonitoring from "./productivity-monitoring";
import dataLossPrevention from "./data-loss-prevention";
import settings from "./settings";
import authLayout from "./auth-layout";

// Merge by language
const messages = {
  en: {
    coreHr: coreHr.en,
    productivityMonitoring: productivityMonitoring.en,
    dataLossPrevention: dataLossPrevention.en,
    settings: settings.en,
    authLayout: authLayout.en,
    searchPlaceholder: "Search",
  },
  fr: {
    coreHr: coreHr.fr,
    productivityMonitoring: productivityMonitoring.fr,
    dataLossPrevention: dataLossPrevention.fr,
    settings: settings.fr,
    searchPlaceholder: "Rechercher",
  },
  ja: {
    coreHr: coreHr.ja,
    productivityMonitoring: productivityMonitoring.ja,
    dataLossPrevention: dataLossPrevention.ja,
    settings: settings.ja,
    searchPlaceholder: "検索",
  },
  sp: {
    coreHr: coreHr.sp,
    productivityMonitoring: productivityMonitoring.sp,
    dataLossPrevention: dataLossPrevention.sp,
    settings: settings.sp,
    authLayout: authLayout.sp,
    searchPlaceholder: "Buscar",
  },
};

const i18n = createI18n({
  locale: localStorage.getItem("language") || "en",
  fallbackLocale: "en",
  messages,
});

export default i18n;
