<template>
  <div>
    <AppTopBarTab
      v-if="mainTabs.length > 0"
      :tabs-list="mainTabs"
      :show-bottom-sheet="false"
    ></AppTopBarTab>
    <v-container fluid class="form-container">
      <v-window
        v-if="formAccess?.view"
        v-model="currentTabItem"
        class="overflow-visible"
      >
        <v-window-item value="tab-0" class="overflow-visible">
          <v-card class="pa-6 my-6 rounded-lg overflow-visible">
            <v-card-text class="pa-6 card-blue-background">
              <v-form ref="formGeneratorForm">
                <v-row>
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Report Title
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="8" lg="9">
                    <CustomSelect
                      :items="reports"
                      label
                      :isAutoComplete="true"
                      :itemSelected="selectedReport"
                      :rules="[required('Report', selectedReport)]"
                      style="max-width: 300px"
                      @selected-item="onChangeValue($event, 'selectedReport')"
                    ></CustomSelect>
                  </v-col>
                </v-row>
                <v-row
                  v-if="selectedReport?.toLowerCase() === 'pct (kbank payroll)'"
                >
                  <v-col cols="12" md="4" lg="3">
                    <span class="text-subtitle-1 font-weight-medium">
                      Payroll Month
                      <span class="text-red">*</span>
                    </span>
                  </v-col>
                  <v-col cols="12" md="4" lg="3">
                    <datepicker
                      :format="'MMMM, yyyy'"
                      v-model="selectedMonthYear"
                      maximum-view="year"
                      minimum-view="month"
                      style="width: 80%"
                      :disabled-dates="{
                        from: new Date(),
                        preventDisableDateSelection: true,
                      }"
                    ></datepicker>
                    <div class="mt-2 ml-3">
                      <p style="color: #b00020; font-size: 12px">
                        {{ dateErrorMsg }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
              </v-form>
            </v-card-text>
            <v-card-actions class="d-flex justify-center">
              <v-btn
                rounded="lg"
                variant="elevated"
                color="primary"
                @click="retrieveDocumentDetails"
                >Generate Report</v-btn
              >
            </v-card-actions>
          </v-card>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>
<script>
import Datepicker from "vuejs3-datepicker";
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import FileGeneratorMixins from "@/mixins/FileGeneratorMixins";
import moment from "moment";

import { GET_PCT_KBANK_PAYROLL } from "@/graphql/payroll/electronicFundTransferQueries";

export default {
  name: "ElectronicFundTransfer",
  components: { CustomSelect, Datepicker },
  mixins: [validationRules, FileGeneratorMixins],
  data() {
    return {
      //loading
      isLoading: false,

      //variables
      dateErrorMsg: "",
      currentTabItem: "tab-0",
      payrollCountry: "",
      selectedReport: "",
      selectedMonthYear: "",
      preFillDetails: {},

      //lists
      thaiReports: ["PCT (KBank Payroll)"],
      mainTabs: ["Electronic Fund Transfer"],
    };
  },
  computed: {
    reports() {
      let list = [];
      if (this.payrollCountry?.toLowerCase() == "th") {
        return this.thaiReports;
      }
      return list;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("electronic-fund-transfer");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  mounted() {
    this.payrollCountry = this.$route.query.payroll_country;
  },
  methods: {
    onChangeValue(value, field) {
      this[field] = value;
      this.dateErrorMsg = "";
      this.selectedMonthYear = "";
    },
    validateDate() {
      if (
        this.selectedReport &&
        this.selectedReport.toLowerCase() === "pct (kbank payroll)"
      ) {
        if (this.selectedMonthYear !== "") {
          this.dateErrorMsg = "";
          return true;
        } else {
          this.dateErrorMsg = "Payroll month is required";
          return false;
        }
      }
    },
    async retrieveDocumentDetails() {
      const { valid } = await this.$refs.formGeneratorForm.validate();
      let dateValid = this.validateDate();
      if (valid && dateValid) {
        let apiMethod = "",
          apiQuery = "",
          apiVariables = {},
          client = "";
        if (this.selectedReport?.toLowerCase() === "pct (kbank payroll)") {
          apiMethod = "retrieveBankDetailsReport";
          apiQuery = GET_PCT_KBANK_PAYROLL;
          apiVariables = {
            payrollMonthYear:
              this.selectedMonthYear &&
              this.selectedMonthYear !== "Invalid Date"
                ? moment(this.selectedMonthYear).format("M,YYYY")
                : "",
            reportType: this.selectedReport,
          };
          client = "apolloClientAA";
        }
        this.getDocumentDetails(apiQuery, apiMethod, apiVariables, client);
      }
    },
    getDocumentDetails(apiQuery, apiMethod, apiVariables, client) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: apiQuery,
          client: client,
          fetchPolicy: "no-cache",
          variables: apiVariables,
        })
        .then((response) => {
          if (
            response.data &&
            response.data[apiMethod] &&
            !response.data[apiMethod].error
          ) {
            let details = response.data[apiMethod];
            vm.preFillDetails = details;
            vm.generateDocumentBasedOnReportTypeChosen();
          } else {
            vm.handleRetrieveError();
          }
        })
        .catch((err) => {
          vm.handleRetrieveError(err);
        });
    },
    handleRetrieveError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "report details",
        isListError: false,
      });
    },
    generateDocumentBasedOnReportTypeChosen() {
      if (this.selectedReport?.toLowerCase() === "pct (kbank payroll)") {
        const details = JSON.parse(this.preFillDetails.bankDetails);
        const summary = JSON.parse(this.preFillDetails.bankDetailsSummary);
        const month = moment(this.selectedMonthYear).format("MM");
        const year = moment(this.selectedMonthYear).format("YYYY");
        this.downloadReportDocument(
          summary,
          details,
          `KBank_PCT_${month}_${year}`
        );
        this.isLoading = false;
      }
    },
  },
};
</script>
<style scoped>
.form-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .form-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
