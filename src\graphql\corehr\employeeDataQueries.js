import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_EMPLOYEE_DATA_MANAGEMENT = gql`
  query listEmployeeDetails {
    listEmployeeDetails {
      errorCode
      message
      listEmployeeDetails {
        Emp_First_Name
        Emp_Middle_Name
        Emp_Last_Name
        Employee_Id
        User_Defined_EmpId
        Emp_Email
        Emp_Status
        Manager_Id
        Mobile_No
      }
    }
  }
`;

export const EXPORT_EMPLOYEE_DATA = gql`
  query exportEmployeeDetails(
    $typeOfEmployeeDetails: String!
    $filterBy: String!
    $location: [Int]!
    $department: [Int]!
    $designation: [Int]!
    $sortBy: String
  ) {
    exportEmployeeDetails(
      typeOfEmployeeDetails: $typeOfEmployeeDetails
      filterBy: $filterBy
      location: $location
      department: $department
      designation: $designation
      sortBy: $sortBy
    ) {
      errorCode
      message
      exportEmployeeDetails
    }
  }
`;

export const GET_EFFECTIVE_DATES = gql`
  query getEffectiveDate($employeeIds: [String]) {
    getEffectiveDate(employeeIds: $employeeIds) {
      errorCode
      message
      getEffectiveDate {
        Employee_Id
        Effective_Date
        Min_Date
        Max_Date
        Designation_Id_End_Date
        Department_Id_End_Date
        Location_Id_End_Date
        Work_Schedule_End_Date
        Manager_Id_End_Date
        EmpType_Id_End_Date
        Business_Unit_Id_End_Date
        Date_Of_Join
      }
    }
  }
`;

export const GET_ONBOARDING_EMPLOYEE_DATA = gql`
  query exportEmployeeAllDetails(
    $typeOfEmployeeDetails: [String!]!
    $filterBy: [String!]!
    $location: [Int]!
    $department: [Int]!
    $designation: [Int]!
    $sortBy: String
    $fromDate: Date
    $toDate: Date
    $formId: Int!
  ) {
    exportEmployeeAllDetails(
      typeOfEmployeeDetails: $typeOfEmployeeDetails
      filterBy: $filterBy
      location: $location
      department: $department
      designation: $designation
      sortBy: $sortBy
      fromDate: $fromDate
      toDate: $toDate
      formId: $formId
    ) {
      errorCode
      message
      exportEmployeeDetails
    }
  }
`;

// Mutations

export const IMPORT_EMPLOYEE_DATA = gql`
  mutation importEmployeeData(
    $employeeData: String!
    $typeOfImport: String!
    $typeOfEmployeeDetails: String!
    $isAsync: Int!
  ) {
    importEmployeeData(
      employeeData: $employeeData
      typeOfImport: $typeOfImport
      typeOfEmployeeDetails: $typeOfEmployeeDetails
      isAsync: $isAsync
    ) {
      errorCode
      message
      validationFails {
        fieldName
        field
        reason
      }
    }
  }
`;
export const GET_LEAVE_OVERRIDE_LIST = gql`
  query listLeaveOverrideEmployeeDetails($employeeId: Int, $formId: Int) {
    listLeaveOverrideEmployeeDetails(employeeId: $employeeId, formId: $formId) {
      errorCode
      message
      employeeEligibleLeaveDetails {
        employeeId
        userDefinedEmpId
        employeeName
        departmentName
        locationName
        designationName
        leaveTypeId
        leaveType
        carryOver
        carryOverAccumulationLimit
        eligibleLeaveId
        currentYearTotalEligibleDays
        totalCODays
        lastCOBalance
        leavesTaken
        encashedDays
        totalAppliedLeaveDays
        calculatedLeaveBalance
        encashmentProcessedClosurePending
        leaveOverrideReason
      }
    }
  }
`;
export const GET_LEAVE_OVERRIDE_HISTORY = gql`
  query retrieveLeaveOverrideHistory($employeeId: Int!, $leaveTypeId: Int!) {
    retrieveLeaveOverrideHistory(
      employeeId: $employeeId
      leaveTypeId: $leaveTypeId
    ) {
      errorCode
      message
      historyDetails {
        userDefinedEmpId
        employeeName
        leaveType
        currentYearTotalEligibleDays
        carryOver
        totalCODays
        lastCOBalance
        leavesTaken
        updatedOn
        updatedBy
      }
    }
  }
`;

export const UPDATE_LEAVE_OVERRIDE = gql`
  mutation overrideEmployeeLeaves($overrideDetails: [overrideInputs]!) {
    overrideEmployeeLeaves(overrideDetails: $overrideDetails) {
      errorCode
      message
    }
  }
`;
