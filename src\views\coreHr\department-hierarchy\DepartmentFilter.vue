<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="secondary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <v-col
            v-if="labelList[314] && labelList[314].Field_Visiblity === 'Yes'"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedDepartmentcode"
              :items="departmentCode"
              item-title="departmentCode"
              item-value="departmentCode"
              color="primary"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
              @selected-item="
                onChangeSelectField($event, 'selectedDepartmentcode')
              "
              ><template v-slot:label>
                {{ labelList["314"].Field_Alias }}
              </template></v-autocomplete
            >
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedDepartments"
              :items="listDepartments"
              label="Department Name"
              item-value="departments"
              item-title="departments"
              @selected-item="
                onChangeSelectField($event, 'selectedDepartments')
              "
              color="primary"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedHeaders"
              :items="listHeaders"
              item-value="headers"
              item-title="headers"
              label="Department Header"
              @selected-item="onChangeSelectField($event, 'selectedHeaders')"
              color="primary"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedParents"
              :items="listParents"
              item-value="parents"
              item-title="parents"
              label="Parent Department"
              @selected-item="onChangeSelectField($event, 'selectedParents')"
              color="primary"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedBonusType"
              :items="listBonusTypes"
              label="Bonus Type"
              item-value="bonusTypes"
              item-title="bonusTypes"
              @selected-item="onChangeSelectField($event, 'selectedBonusType')"
              color="primary"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              :items="listStatus"
              label="Status"
              itemValue="status"
              itemTitle="status"
              color="primary"
              multiple
              clearable
              closable-chips
              chips
              density="compact"
              single-line
              @selected-item="onChangeSelectField($event, 'selectedStatus')"
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "DepartmentFilter",
  data: () => ({
    openFormFilter: false,
    //Dropdown values
    departmentCode: [],
    listDepartments: [],
    listHeaders: [],
    listParents: [],
    listBonusTypes: [],
    listStatus: [],
    // selected Values
    selectedDepartmentcode: [],
    selectedDepartments: [],
    selectedHeaders: [],
    selectedParents: [],
    selectedBonusType: [],
    selectedStatus: ["Active"],
  }),

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.fnApplyFilter();
    this.formFilterData();
  },
  methods: {
    formFilterData() {
      for (let item of this.items) {
        if (item && item.Department_Code) {
          this.departmentCode.push({
            departmentCode: item.Department_Code,
          });
        }
        if (item && item.Department_Name) {
          this.listDepartments.push({
            departments: item.Department_Name,
          });
        }
        if (item && item.Organization_Type) {
          this.listHeaders.push({
            headers: item.Organization_Type,
          });
        }
        if (item && item.Parent_Name) {
          this.listParents.push({
            parents: item.Parent_Name,
          });
        }
        if (item && item.Bonus_Type) {
          this.listBonusTypes.push({
            bonusTypes: item.Bonus_Type,
          });
        }
        if (item && item.Department_Status) {
          this.listStatus.push({
            status: item.Department_Status,
          });
        }
      }
      this.departmentCode = this.removeDuplicatesFromArrayOfObject(
        this.departmentCode,
        "departmentCode"
      );
      this.listDepartments = this.removeDuplicatesFromArrayOfObject(
        this.listDepartments,
        "departments"
      );
      this.listHeaders = this.removeDuplicatesFromArrayOfObject(
        this.listHeaders,
        "headers"
      );
      this.listParents = this.removeDuplicatesFromArrayOfObject(
        this.listParents,
        "parents"
      );
      this.listBonusTypes = this.removeDuplicatesFromArrayOfObject(
        this.listBonusTypes,
        "bonusTypes"
      );
      this.listStatus = this.removeDuplicatesFromArrayOfObject(
        this.listStatus,
        "status"
      );
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        DepartmentCode: this.selectedDepartmentcode,
        Departments: this.selectedDepartments,
        DepartmentHeader: this.selectedHeaders,
        ParentDepartment: this.selectedParents,
        BonusType: this.selectedBonusType,
        Status: this.selectedStatus,
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues;
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      (this.selectedDepartmentcode = []), (this.selectedDepartments = []);
      this.selectedHeaders = [];
      this.selectedParents = [];
      this.selectedBonusType = [];
      this.selectedStatus = [];
    },
  },
};
</script>
