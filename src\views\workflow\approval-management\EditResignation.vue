<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="40" color="hover" variant="elevated">
            <i class="primary hr-workflow-approval-management text-h6"></i>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              {{ approvalDetails.description }}
            </div>
          </div>
        </div>
        <div class="pa-3">
          <v-btn
            color="primary"
            variant="elevated"
            class="mr-4"
            rounded="lg"
            size="small"
            :disabled="!isReasonsChanged && !isDateChanged"
            @click.stop="onUpdate()"
          >
            Save
          </v-btn>
          <v-icon color="primary" @click="onCloseEditForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card-text>
        <v-row
          class="px-sm-8 px-md-12 mt-3 mb-6"
          :style="
            'overflow: scroll; height: ' + $store.getters.getTableHeight(350)
          "
        >
          <v-col cols="12">
            <v-card
              v-if="isUpdatingForm"
              class="pa-2 d-flex align-center justify-center"
              elevation="0"
              min-height="250"
              width="100%"
            >
              <v-progress-circular color="secondary" indeterminate size="64">
              </v-progress-circular>
            </v-card>
            <div v-else>
              <v-row>
                <v-col cols="12" sm="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 font-weight-bold">
                    Applied Date
                  </div>
                  <div class="value-text">
                    <section class="text-body-2">
                      <datepicker
                        :format="orgDateFormat"
                        v-model="appliedDate"
                        style="width: 100%"
                        :disabled-dates="{
                          to: resignationAppliedDateMin,
                          from: new Date(exitDate),
                        }"
                        @input="onChangeDateFields()"
                      ></datepicker>
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 font-weight-bold">Exit Date</div>
                  <div class="value-text">
                    <section class="text-body-2">
                      <datepicker
                        :format="orgDateFormat"
                        v-model="exitDate"
                        style="width: 100%"
                        :disabled-dates="{
                          to: new Date(appliedDate),
                        }"
                        @input="onChangeDateFields()"
                      ></datepicker>
                    </section>
                  </div>
                </v-col>
              </v-row>
              <v-form ref="commentReasonForm">
                <v-row class="mt-4">
                  <v-col cols="12" sm="6" class="px-md-6 pb-0">
                    <div class="text-subtitle-1 font-weight-bold">
                      Relieving Reason
                    </div>
                    <div class="value-text">
                      <section class="text-body-2">
                        <v-select
                          v-model="relievingReasonId"
                          color="primary"
                          :items="relievingReasonList"
                          density="compact"
                          item-title="esicReasonName"
                          item-value="reasonId"
                          :loading="isFetchingResignationDetails"
                          :rules="[requiredRules]"
                          @update:model-value="onChangeReasonFields()"
                        >
                        </v-select>
                      </section>
                    </div>
                  </v-col>
                  <v-col cols="12" sm="6" class="px-md-6 pb-0">
                    <div class="text-subtitle-1 font-weight-bold">Comment</div>
                    <div class="value-text">
                      <section class="text-body-2">
                        <v-textarea
                          v-model="comment"
                          variant="outlined"
                          auto-grow
                          rows="1"
                          class="mt-4"
                          :rules="[
                            commentRules.minCounter,
                            commentRules.maxCounter,
                            commentRules.vComment,
                          ]"
                          @update:model-value="onChangeReasonFields()"
                        ></v-textarea>
                      </section>
                    </div>
                  </v-col>
                </v-row>
              </v-form>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
// queries
import {
  UPDATE_RESIGNATION_DATES,
  GET_RESIGNATION_DETAILS,
  UPDATE_RESIGNATION_RELIEVING_REASON_COMMENT,
} from "@/graphql/workflow/approvalManagementQueries.js";
// functions
import { replaceSentanceWithoutExtraSpaceAndNewline } from "@/helper";

export default defineComponent({
  name: "EditResignation",

  components: {
    Datepicker,
  },

  props: {
    approvalDetails: {
      type: Object,
      required: true,
    },
    resignationAppliedDateMin: {
      type: Date,
      required: true,
    },
  },

  data: () => ({
    isUpdatingForm: false,
    appliedDate: "",
    exitDate: "",
    isFormDirty: false,
    openConfirmationPopup: false,
    relievingReasonId: null,
    relievingReasonList: [],
    isFetchingResignationDetails: true,
    comment: "",
    isDateChanged: false,
    isReasonsChanged: false,
    updateAPiCallCount: 0,
    isErrorInDateUpdate: false,
    isErrorInReasonUpdate: false,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    commentRules() {
      var rules = {
        minCounter: (value) =>
          value.length >= 2 ||
          `Comment characters should be more than are equal to 2 characters`,
        maxCounter: (value) =>
          value.length <= 500 ||
          `Comment characters should not exceed 500 characters`,
        vComment: (value) => {
          const pattern = /^[\w\.\,\#\+\&\/\-\(\)\:\'\n\ ]*$/;
          return pattern.test(value) || `Comment is not valid`;
        },
      };
      return rules;
    },
    requiredRules() {
      return (value) => !!value || `Relieving reason is required.`;
    },
  },

  mounted() {
    const { appliedDate, exitDate, resignationId, relievingReasonComment } =
      this.approvalDetails.instanceData;
    this.appliedDate = appliedDate;
    this.exitDate = exitDate;
    this.resignationId = resignationId;
    this.comment = relievingReasonComment;
    this.fetchResignationDetails();
  },

  watch: {
    relievingReasonId() {
      this.onChangeReasonFields();
    },
    updateAPiCallCount(count) {
      if (count >= 2) {
        if (this.isErrorInDateUpdate && this.isErrorInReasonUpdate) {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message:
              "Something went wrong while updating the resignation details. Please try after some time.",
          };
          this.showAlert(snackbarData);
        } else if (this.isErrorInDateUpdate) {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: this.isReasonsChanged
              ? "Resignation reason updated successfully. But unable to update the resignation dates. Please try after some time."
              : "Something went wrong while updating the resignation details. Please try after some time.",
          };
          this.showAlert(snackbarData);
          this.$emit("edit-update-success");
        } else if (this.isErrorInReasonUpdate) {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: this.isDateChanged
              ? "Resignation dates updated successfully. But unable to update the resignation reason. Please try after some time."
              : "Something went wrong while updating the resignation details. Please try after some time.",
          };
          this.showAlert(snackbarData);
          this.$emit("edit-update-success");
        } else {
          let snackbarData = {
            isOpen: true,
            type: "success",
            message: "Resignation details updated successfully",
          };
          this.showAlert(snackbarData);
          this.$emit("edit-update-success");
        }
      }
    },
  },

  methods: {
    abortClose() {
      this.openConfirmationPopup = false;
    },

    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },

    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-edit-form");
    },

    onChangeDateFields() {
      this.isFormDirty = true;
      this.isDateChanged = true;
    },

    onChangeReasonFields() {
      this.isFormDirty = true;
      this.isReasonsChanged = true;
    },

    fetchResignationDetails() {
      let vm = this;
      vm.isFetchingResignationDetails = true;
      vm.$apollo
        .query({
          query: GET_RESIGNATION_DETAILS,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
            },
            resignationId: vm.resignationId,
          },
          client: "apolloClientZ",
        })
        .then((response) => {
          if (response.data && response.data.getEmployeeResignation) {
            vm.relievingReasonList =
              response.data.getEmployeeResignation.relievingReasonDetails;
            vm.relievingReasonId = vm.approvalDetails.instanceData.reasonId;
          }
          vm.isFetchingResignationDetails = false;
        })
        .catch(() => {
          vm.isFetchingResignationDetails = false;
        });
    },

    async onUpdate() {
      this.updateAPiCallCount = 0;
      this.isErrorInDateUpdate = false;
      this.isErrorInReasonUpdate = false;
      if (this.isReasonsChanged) {
        let isFormValid = await this.$refs.commentReasonForm.validate();
        if (isFormValid && isFormValid.valid) {
          this.onUpdateResignationReasonDetails();
        }
      } else {
        this.updateAPiCallCount += 1;
      }
      if (this.isDateChanged) {
        this.onUpdateResignationDateDetails();
      } else {
        this.updateAPiCallCount += 1;
      }
    },

    onUpdateResignationDateDetails() {
      let vm = this;
      vm.isUpdatingForm = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_RESIGNATION_DATES,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
            },
            appliedDate: moment(vm.appliedDate).format("YYYY-MM-DD"),
            resignationDate: moment(vm.exitDate).format("YYYY-MM-DD"),
            resignationId: vm.resignationId,
          },
          client: "apolloClientZ",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.updateResignationDate &&
            response.data.updateResignationDate.result
          ) {
            vm.isUpdatingForm = false;
            vm.updateAPiCallCount += 1;
          } else {
            vm.handleResignationDateUpdateError();
          }
        })
        .catch(() => {
          vm.handleResignationDateUpdateError();
        });
    },

    onUpdateResignationReasonDetails() {
      let vm = this;
      vm.isUpdatingForm = true;
      let reasonName = vm.relievingReasonList.filter(
        (el) => el.reasonId === vm.relievingReasonId
      );
      reasonName =
        reasonName && reasonName.length > 0 ? reasonName[0].esicReasonName : "";
      let commentValue = replaceSentanceWithoutExtraSpaceAndNewline(vm.comment);
      vm.$apollo
        .mutate({
          mutation: UPDATE_RESIGNATION_RELIEVING_REASON_COMMENT,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
            },
            resignationId: vm.resignationId,
            esicReason: reasonName,
            reasonId: vm.relievingReasonId,
            relievingReasonComment: commentValue,
          },
          client: "apolloClientZ",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.updateResignationReason &&
            response.data.updateResignationReason.result
          ) {
            vm.isUpdatingForm = false;
            vm.updateAPiCallCount += 1;
          } else {
            vm.handleResignationReasonUpdateError();
          }
        })
        .catch(() => {
          vm.handleResignationReasonUpdateError();
        });
    },

    handleResignationDateUpdateError() {
      this.isUpdatingForm = false;
      this.isErrorInDateUpdate = true;
      this.updateAPiCallCount += 1;
    },

    handleResignationReasonUpdateError() {
      this.isUpdatingForm = false;
      this.isErrorInReasonUpdate = true;
      this.updateAPiCallCount += 1;
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
