<template>
  <v-card v-if="!isLoading" class="rounded-lg">
    <div
      class="d-flex align-center"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center pl-4 py-2">
        <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
          <v-icon class="primary" size="20">fas fa-user-tag</v-icon>
        </v-avatar>
        <div class="text-subtitle-1 font-weight-bold">
          {{
            formActionType === "view" ||
            formActionType === "associate" ||
            formActionType === "empView"
              ? rolesName
              : "Roles"
          }}
        </div>
      </div>
      <div class="d-flex align-center">
        <v-btn
          v-if="formActionType === 'view' && !isDefaultTemplate"
          @click="openEditForm()"
          size="small"
          color="primary"
          variant="elevated"
          rounded="lg"
          >Edit</v-btn
        >
        <AppToggleButton
          v-if="formActionType === 'edit' || formActionType === 'add'"
          button-active-text="Active"
          button-inactive-text="Inactive"
          button-active-color="#7de272"
          button-inactive-color="red"
          :current-value="status === 'Active' ? true : false"
          :isDisableToggle="!rolesId"
          @chosen-value="onChangeStatus($event)"
        ></AppToggleButton>
        <v-icon class="mx-1" color="primary" @click="closeEditForm">
          fas fa-times
        </v-icon>
      </div>
    </div>
    <div style="height: calc(100vh - 250px); overflow: scroll" class="mb-4">
      <v-card-text>
        <v-row class="px-sm-8">
          <v-col
            cols="12"
            v-if="
              formActionType === 'view' ||
              formActionType === 'empView' ||
              isDefaultTemplate ||
              formActionType === 'associate'
            "
          >
            <v-row>
              <v-col cols="12" sm="4">
                <p class="text-subtitle-1 text-grey-darken-1">Status</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(status) }}
                </p>
              </v-col>
              <v-col cols="12" sm="8">
                <p class="text-subtitle-1 text-grey-darken-1">Description</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(description) }}
                </p>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" v-else>
            <v-form ref="rolesForm">
              <v-row>
                <v-col cols="12" sm="6" md="6" lg="4" class="pb-0">
                  <v-text-field
                    v-model="rolesName"
                    variant="solo"
                    :rules="[
                      required('Name', rolesName),
                      validateWithRulesAndReturnMessages(
                        rolesName,
                        'businessUnit',
                        'Name'
                      ),
                    ]"
                    style="max-width: 300px"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      Name<span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>

                <v-col cols="12" sm="6" md="6" lg="4" class="pb-0">
                  <CustomSelect
                    :items="rolesList"
                    :itemSelected="selectedCopyRoleId"
                    itemValue="Roles_Id"
                    itemTitle="Roles_Name"
                    style="max-width: 300px"
                    label="Copy Roles From"
                    @selected-item="onCopyRoles($event)"
                  ></CustomSelect>
                </v-col>

                <v-col cols="12" sm="6" md="6" lg="4" class="pb-0">
                  <v-textarea
                    v-model="description"
                    variant="solo"
                    auto-grow
                    rows="1"
                    label="Description"
                    :rules="
                      description
                        ? [
                            validateWithRulesAndReturnMessages(
                              description,
                              'description',
                              'Description'
                            ),
                          ]
                        : []
                    "
                    @update:model-value="isFormDirty = true"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-col>
          <v-col cols="12">
            <div
              v-if="
                formActionType !== 'view' &&
                formActionType !== 'associate' &&
                formActionType !== 'empView'
              "
              class="d-flex justify-center align-center mt-n7"
            >
              Select All
              <v-checkbox
                v-model="selectAll"
                :true-value="1"
                :false-value="0"
                color="primary"
                style="max-width: 100px; height: 55px"
                @update:model-value="onSelectAll()"
              ></v-checkbox>
            </div>
            <v-card
              class="d-flex pa-1"
              elevation="2"
              style="justify-content: space-between"
            >
              <div
                style="width: 97%"
                :style="showModulePanel ? '' : 'overflow:hidden'"
                class="pa-1"
                :class="showModulePanel ? 'text-center' : 'd-flex'"
              >
                <v-btn
                  v-for="module in modules"
                  :key="module.Module_Id"
                  :value="module.Module_Id"
                  :color="
                    selectedModuleId === module.Module_Id
                      ? 'primary'
                      : 'grey-lighten-3'
                  "
                  :variant="
                    selectedModuleId === module.Module_Id ? 'elevated' : 'flat'
                  "
                  size="small"
                  class="mr-1 mb-1"
                  @click="onChangeModule(module.Module_Id)"
                >
                  {{ module.Module_Name }}</v-btn
                >
              </div>
              <v-icon
                class="pl-2 pt-2"
                v-if="showModulePanel"
                @click="showModulePanel = false"
                >fas fa-chevron-up</v-icon
              >
              <v-icon class="pl-2 pt-2" v-else @click="showModulePanel = true"
                >fas fa-chevron-down</v-icon
              >
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-card elevation="2" class="pa-1">
              <v-row align="center" class="pl-2">
                <v-col cols="3"> </v-col>
                <v-col cols="9" class="d-flex">
                  <div
                    v-for="(
                      category, key
                    ) of selectedModuleAccessRightsCategory[selectedModuleId]"
                    :key="key + category + selectedModuleId"
                    style="width: 20%"
                    :style="
                      formActionType !== 'view' &&
                      formActionType !== 'associate' &&
                      formActionType !== 'empView' &&
                      selectedModuleId !== 10
                        ? 'height: 60px'
                        : ''
                    "
                  >
                    {{ key.replace(/^Role_/g, "").replace(/_/g, " ") }}

                    <v-checkbox
                      v-if="
                        formActionType !== 'view' &&
                        formActionType !== 'associate' &&
                        formActionType !== 'empView' &&
                        selectedModuleId !== 10
                      "
                      v-model="
                        selectedModuleAccessRightsCategory[selectedModuleId][
                          key
                        ]
                      "
                      :true-value="1"
                      :false-value="0"
                      color="primary"
                      @update:model-value="
                        checkAllBasedOnAccessAndForm(
                          key,
                          selectedModuleAccessRightsCategory[selectedModuleId][
                            key
                          ]
                        )
                      "
                    ></v-checkbox>
                  </div>
                </v-col>
              </v-row>
              <div
                :style="`overflow: scroll;height: calc(100vh - ${formHeight})`"
              >
                <div
                  v-for="form in forms"
                  :key="form.Form_Id + form.Module_Id"
                  class="pl-2"
                >
                  <div v-if="form.Module_Id === selectedModuleId">
                    <v-row
                      align="center"
                      v-if="
                        (form.Form_Name === 'Service Provider Admin' &&
                          fieldForce) ||
                        form.Form_Name !== 'Service Provider Admin'
                      "
                    >
                      <v-col cols="3">
                        <div class="d-flex">
                          {{ form.Form_Name }}
                          <v-tooltip
                            v-if="form.Form_Name.includes('Admin')"
                            :text="adminFormTooltips(form.Form_Name)"
                            location="right"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div v-bind="props">
                                <v-icon color="blue" class="ml-1" size="18"
                                  >fas fa-info-circle</v-icon
                                >
                              </div>
                            </template>
                          </v-tooltip>
                        </div>
                      </v-col>
                      <v-col
                        cols="9"
                        class="d-flex"
                        :style="
                          formActionType === 'view' ||
                          formActionType === 'associate' ||
                          formActionType === 'empView'
                            ? 'pointer-events: none'
                            : ''
                        "
                      >
                        <div
                          v-for="(formRights, key) of accessRights[
                            form.Form_Id + '-' + form.Module_Id
                          ]"
                          :key="formRights + key + form.Form_Id"
                          style="width: 20%; height: 40px"
                        >
                          <v-checkbox
                            v-model="
                              accessRights[form.Form_Id + '-' + form.Module_Id][
                                key
                              ]
                            "
                            :true-value="1"
                            :false-value="0"
                            color="primary"
                            :readonly="
                              formActionType === 'view' ||
                              formActionType === 'associate' ||
                              formActionType === 'empView'
                            "
                            @update:model-value="
                              onChangeFormAccess(
                                form.Form_Name,
                                form.Form_Id + '-' + form.Module_Id,
                                key
                              )
                            "
                          ></v-checkbox></div
                      ></v-col>
                    </v-row>
                    <div
                      v-for="subForm in subForms"
                      :key="subForm.Form_Id + subForm.Module_Id"
                    >
                      <div
                        v-if="form.Form_Id === subForm.Sub_Form"
                        class="text-grey-darken-2"
                      >
                        <v-row align="center">
                          <v-col cols="3">
                            <div>+ {{ subForm.Form_Name }}</div>
                          </v-col>
                          <v-col
                            cols="9"
                            class="d-flex"
                            :style="
                              formActionType === 'view' ||
                              formActionType === 'associate' ||
                              formActionType === 'empView'
                                ? 'pointer-events: none'
                                : ''
                            "
                          >
                            <div
                              v-for="(formRights, key) of accessRights[
                                subForm.Form_Id + '-' + subForm.Module_Id
                              ]"
                              :key="formRights + key + subForm.Form_Id"
                              style="width: 20%; height: 40px"
                            >
                              <v-checkbox
                                v-model="
                                  accessRights[
                                    subForm.Form_Id + '-' + subForm.Module_Id
                                  ][key]
                                "
                                :true-value="1"
                                :false-value="0"
                                color="primary"
                                :readonly="
                                  formActionType === 'view' ||
                                  formActionType === 'associate' ||
                                  formActionType === 'empView'
                                "
                                @update:model-value="
                                  onChangeSubFormAccess(
                                    subForm.Form_Id + '-' + subForm.Module_Id,
                                    key,
                                    subForm.Sub_Form
                                  )
                                "
                              ></v-checkbox></div
                          ></v-col>
                        </v-row>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </div>
  </v-card>
  <v-bottom-navigation v-if="!isLoading && openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="
              formActionType === 'associate'
                ? associateRoles()
                : validateAddUpdateForm()
            "
          >
            <span class="primary">{{
              formActionType === "associate" ? "Assign" : "Save"
            }}</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    iconName="fas fa-copy"
    iconColor="amber"
    :confirmationHeading="warningText"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onChangeRoleAccess()"
  >
  </AppWarningModal>
</template>

<script>
import {
  ADD_UPDATE_ROLES,
  RETRIEVE_ROLE_ACCESS_RIGHTS,
  ASSOCIATE_ROLES_TO_EMPLOYEES,
} from "@/graphql/settings/core-hr/rolesQueries.js";
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { checkNullValue } from "@/helper.js";

export default {
  name: "RoleAccessRights",
  mixins: [validationRules],
  components: { CustomSelect },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    actionType: {
      type: String,
      default: "view",
    },
    rolesList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    employeeIds: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  emits: ["close-form", "refetch-list"],
  data: () => ({
    formActionType: "",
    selectedModuleId: 2,
    modules: [],
    forms: [],
    subForms: [],
    accessRights: [],
    formAccessRights: [],
    subFormAccessRights: [],
    selectedModuleAccessRightsCategory: [],
    isLoading: true,
    roleAccessRightsData: {},
    selectAll: 0,
    selectedCopyRoleName: "",
    warningText: "",
    openWarningModal: false,
    defaultTemplateRoleId: null,
    defaultTemplateName: "",
    hiddenForms: [
      "Work from home (pre-approval)",
      "Work during week off (pre-approval)",
      "Work during holiday (pre-approval)",
      "On Duty Settings",
      "Salary Details",
      "Fixed Insurance",
      "Variable Insurance",
    ],
    // form
    rolesId: 0,
    rolesName: "",
    description: "",
    status: "Active",
    isDefaultTemplate: false,
    openBottomSheet: false,
    isFormDirty: false,
    selectedCopyRoleId: null,
    atleastOneRolesEnabled: false,
    showModulePanel: false,
  }),

  mounted() {
    this.formActionType = this.actionType;
    const { Role_Status, Roles_Id, Roles_Name, Description, Is_Template } =
      this.selectedItem;
    this.rolesId = Roles_Id ? Roles_Id : 0;
    this.rolesName = Roles_Name;
    this.description = Description;
    this.status = Role_Status ? Role_Status : "Active";
    this.isDefaultTemplate = Is_Template ? 1 : 0;
    this.fetchRoleAccessRights();
  },

  watch: {
    formActionType(val) {
      if (val !== "view" && val !== "empView") {
        this.openBottomSheet = true;
        if (val === "associate") {
          this.isFormDirty = true;
        }
      }
    },
    accessRights() {
      this.onCheckAllSelected();
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    formHeight() {
      if (this.formActionType === "edit" || this.formActionType === "add") {
        if (this.showModulePanel) {
          return "450px";
        } else return "420px";
      } else {
        if (this.showModulePanel) {
          return "420px";
        } else return "400px";
      }
    },

    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
  },

  methods: {
    checkNullValue,
    openEditForm() {
      this.formActionType = "edit";
    },
    closeEditForm() {
      this.openBottomSheet = false;
      this.$emit("close-form");
    },
    onChangeModule(moduleId) {
      this.selectedModuleId = moduleId;
    },

    // change the mode of performance management
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "Inactive";
      this.isFormDirty = true;
    },

    onCopyRoles(value) {
      this.selectedCopyRoleId = value;
      this.selectedCopyRoleName = "";
      this.warningText = "Are you sure to copy the roles?";
      this.openWarningModal = true;
    },

    onChangeRoleAccess() {
      this.openWarningModal = false;
      this.isFormDirty = true;
      this.fetchRoleAccessRights(
        this.selectedCopyRoleId,
        this.selectedCopyRoleName
      );
    },

    onCloseWarningModal() {
      this.openWarningModal = false;
      this.isLoading = true;
      this.formModuleFormsAccessRightsData();
    },

    onChangeFormAccess(formName, formRightsKey, accessKey) {
      // checking user is enabling the admin access if yes then we need to prompt to fetch default admin template values
      if (formName) {
        let fName = formName + "(T)";
        let defaultTemplateAccess = this.rolesList.filter(
          (el) => el.Roles_Name === fName && el.Is_Template
        );
        if (defaultTemplateAccess.length > 0) {
          this.selectedCopyRoleId = defaultTemplateAccess[0].Roles_Id;
          this.selectedCopyRoleName = formName;
          this.warningText = "Are you sure to copy the selected admin roles?";
          this.openWarningModal = true;
        } else {
          this.onFormOrSubFormAccessChange(formRightsKey, accessKey);
        }
      } else {
        this.onFormOrSubFormAccessChange(formRightsKey, accessKey);
      }
    },

    onChangeSubFormAccess(formRightsKey, accessKey, mainFormIdOfSubForm) {
      this.onFormOrSubFormAccessChange(
        formRightsKey,
        accessKey,
        mainFormIdOfSubForm
      );
    },

    onFormOrSubFormAccessChange(
      formRightsKey,
      accessKey,
      mainFormIdOfSubForm = null
    ) {
      // when view access is disabled then we need to disable other access for that form
      if (
        accessKey === "Role_View" &&
        !this.accessRights[formRightsKey].Role_View
      ) {
        for (let aKey in this.accessRights[formRightsKey]) {
          this.accessRights[formRightsKey][aKey] = 0;
        }
      } else {
        let mId = formRightsKey.split("-");
        mId = mId[1];
        // when any form access is enabled except view then we need to enable view form access as well
        if (
          parseInt(mId) !== 10 &&
          this.accessRights[formRightsKey][accessKey]
        ) {
          this.accessRights[formRightsKey].Role_View = 1;
        }
      }
      let mId = this.selectedModuleId;
      for (let allRights in this.selectedModuleAccessRightsCategory[mId]) {
        this.selectedModuleAccessRightsCategory[mId][allRights] = 1;
      }
      let subFormsBasedOnMainForm = [];
      if (mainFormIdOfSubForm) {
        subFormsBasedOnMainForm = this.subForms.filter(
          (item) => item.Sub_Form === mainFormIdOfSubForm
        );
      }
      // when any of the access rights is changed need to check all access are enabled under some category to enable check-all btn
      for (let aRights in this.accessRights) {
        let moduleFormSplit = aRights.split("-");
        let moduleId = moduleFormSplit[1];
        let formId = moduleFormSplit[0];
        if (parseInt(moduleId) === this.selectedModuleId) {
          for (let aRightsKey in this.accessRights[aRights]) {
            if (!this.accessRights[aRights][aRightsKey]) {
              this.selectedModuleAccessRightsCategory[mId][aRightsKey] = 0;
            }
            // when any one sub form has access then enable it's main form access as well
            if (
              mainFormIdOfSubForm &&
              parseInt(mainFormIdOfSubForm) === parseInt(formId) &&
              this.accessRights[formRightsKey][accessKey] == 1
            ) {
              let isAnyOneSubFormHasAccess = 0;
              for (let subForm of subFormsBasedOnMainForm) {
                let subFormId = subForm.Form_Id;
                if (
                  this.accessRights[subFormId + "-" + moduleId][aRightsKey] &&
                  !isAnyOneSubFormHasAccess
                ) {
                  isAnyOneSubFormHasAccess = 1;
                }
              }
              if (isAnyOneSubFormHasAccess) {
                this.accessRights[mainFormIdOfSubForm + "-" + moduleId][
                  aRightsKey
                ] = 1;
              } else {
                this.accessRights[mainFormIdOfSubForm + "-" + moduleId][
                  aRightsKey
                ] = 0;
              }
            }
          }
        }
      }
      this.onCheckAllSelected();
      this.isFormDirty = true;
    },

    onCheckAllSelected() {
      // check all the form is having access for all the categories
      for (let access in this.accessRights) {
        let moduleId = access.split("-");
        moduleId = moduleId[1];
        // no need to check admin module access
        if (parseInt(moduleId) !== 10) {
          let checkAllSelected = 1;
          for (let accessKey in this.accessRights[access]) {
            if (!this.accessRights[access][accessKey]) {
              checkAllSelected = 0;
              break;
            }
          }
          this.selectAll = checkAllSelected;
          if (!checkAllSelected) {
            break;
          }
        }
      }
    },

    fetchRoleAccessRights(rolesId, tempName) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_ROLE_ACCESS_RIGHTS,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            Roles_Id: rolesId ? rolesId : vm.selectedItem.Roles_Id,
            Template_Name: tempName ? tempName : "",
            formId: vm.formActionType === "empView" ? 243 : 0,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveRolesAccessRights &&
            !response.data.retrieveRolesAccessRights.errorCode
          ) {
            let { rolesData } = response.data.retrieveRolesAccessRights;
            vm.roleAccessRightsData = rolesData;
            vm.formModuleFormsAccessRightsData();
          } else {
            vm.handleRetrieveRoleAccessRightsError();
          }
        })
        .catch((err) => {
          vm.handleRetrieveRoleAccessRightsError(err);
        });
    },

    handleRetrieveRoleAccessRightsError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "role access rights",
        isListError: false,
      });
      this.$emit("retrieve-error");
    },

    formModuleFormsAccessRightsData() {
      const { Modules, Forms, AccessRights } = this.roleAccessRightsData;
      // forms & sub-forms combination
      let formsAndSubForms = JSON.parse(Forms);
      // only forms
      this.forms = formsAndSubForms.filter(
        (el) => !el.Sub_Form && !this.hiddenForms.includes(el.Form_Name)
      );
      // only sub-forms
      this.subForms = formsAndSubForms.filter(
        (el) => el.Sub_Form && !this.hiddenForms.includes(el.Form_Name)
      );
      // access rights
      let parsedFormAccess = JSON.parse(AccessRights);
      // store formId as key for access rights to get access based on id
      const formIdAccess = {};
      for (const key in parsedFormAccess) {
        if (parsedFormAccess.hasOwnProperty(key)) {
          const formId = parsedFormAccess[key].Form_Id;
          let objectItem = parsedFormAccess[key];
          delete objectItem["Form_Id"];
          delete objectItem["Roles_Id"];
          formIdAccess[formId] = objectItem;
        }
      }
      // need to categorize the access based on module
      let result = {};
      formsAndSubForms.forEach((item) => {
        let formId = item["Form_Id"];
        let moduleId = item.Module_Id;
        let accessKey = formId + "-" + moduleId;
        if (
          formIdAccess[formId] &&
          Object.keys(formIdAccess[formId]).length > 0
        ) {
          result[accessKey] = formIdAccess[formId];
        } else {
          result[accessKey] = {
            Role_View: 0,
            Role_Add: 0,
            Role_Update: 0,
            Role_Delete: 0,
            Role_Optional_Choice: 0,
            Role_Hr_Group: 0,
            Role_Payroll_Group: 0,
          };
        }
        // employees
        if (moduleId === 4) {
          delete result[accessKey]["Role_Payroll_Group"];
        }
        // payroll & forms manager
        else if (moduleId === 5 || moduleId === 9 || moduleId === 26) {
          delete result[accessKey]["Role_Hr_Group"];
        }
        // admin roles
        else if (moduleId === 10) {
          result[accessKey] = {
            Role_Update: result[accessKey]["Role_Update"],
            Role_Optional_Choice: result[accessKey]["Role_Optional_Choice"],
          };
        }
        // organization, reports & admin roles
        else if (moduleId === 2 || moduleId === 7) {
          delete result[accessKey]["Role_Hr_Group"];
          delete result[accessKey]["Role_Payroll_Group"];
        }
        // help & dlp
        else if (moduleId === 8 || moduleId === 22) {
          result[accessKey] = {
            Role_View: result[accessKey]["Role_View"],
          };
        }
        // for all other forms
        else {
          delete result[accessKey]["Role_Optional_Choice"];
          delete result[accessKey]["Role_Hr_Group"];
          delete result[accessKey]["Role_Payroll_Group"];
        }
      });
      this.accessRights = result;
      this.modules = JSON.parse(Modules);
      let selModuleDetails = this.modules.filter(
        (el) => el.Module_Id == this.selectedModuleId
      );
      if (!selModuleDetails || selModuleDetails.length == 0) {
        this.selectedModuleId = this.modules[0].Module_Id;
      }
      let categoryList = {};
      // based on module we need to control access categories (view, add, update, delete, opt choice, hr group, payroll grpup)
      for (let module of this.modules) {
        let moduleId = module.Module_Id;
        // employees
        if (moduleId === 4) {
          categoryList[moduleId] = {
            Role_View: 1,
            Role_Add: 1,
            Role_Update: 1,
            Role_Delete: 1,
            Role_Optional_Choice: 1,
            Role_Hr_Group: 1,
          };
        }
        // payroll & forms manager & tax and statutory compliance
        else if (moduleId === 5 || moduleId === 9 || moduleId === 26) {
          categoryList[moduleId] = {
            Role_View: 1,
            Role_Add: 1,
            Role_Update: 1,
            Role_Delete: 1,
            Role_Optional_Choice: 1,
            Role_Payroll_Group: 1,
          };
        }
        // admin roles
        else if (moduleId === 10) {
          categoryList[moduleId] = {
            Role_Update: 1,
            Role_Optional_Choice: 1,
          };
        }
        // organization, reports & admin roles
        else if (moduleId === 2 || moduleId === 7) {
          categoryList[moduleId] = {
            Role_View: 1,
            Role_Add: 1,
            Role_Update: 1,
            Role_Delete: 1,
            Role_Optional_Choice: 1,
          };
        }
        // help & dlp
        else if (moduleId === 8 || moduleId === 22) {
          categoryList[moduleId] = {
            Role_View: 1,
          };
        }
        // for all other forms
        else {
          categoryList[moduleId] = {
            Role_View: 1,
            Role_Add: 1,
            Role_Update: 1,
            Role_Delete: 1,
          };
        }
        // when all the access are enabled under particular category then we need to enable the check-all checkbox for particular category
        for (let aRights in this.accessRights) {
          let mId = aRights.split("-");
          mId = mId[1];
          if (parseInt(mId) === moduleId) {
            for (let cat in categoryList[moduleId]) {
              if (
                !this.accessRights[aRights][cat] &&
                categoryList[moduleId][cat]
              ) {
                categoryList[moduleId][cat] = 0;
              }
            }
          }
        }
      }
      this.selectedModuleAccessRightsCategory = categoryList;
      this.isLoading = false;
    },

    checkAllBasedOnAccessAndForm(accessTypeKey, value) {
      for (let rights in this.accessRights) {
        let moduleId = rights.split("-");
        moduleId = moduleId[1];
        if (parseInt(moduleId) === this.selectedModuleId) {
          this.accessRights[rights][accessTypeKey] = value;
          if (accessTypeKey === "Role_View" && !value) {
            this.accessRights[rights]["Role_Add"] = value;
            this.accessRights[rights]["Role_Update"] = value;
            this.accessRights[rights]["Role_Delete"] = value;
            this.accessRights[rights]["Role_Optional_Choice"] = value;
            this.selectedModuleAccessRightsCategory[moduleId]["Role_Add"] =
              value;
            this.selectedModuleAccessRightsCategory[moduleId]["Role_Update"] =
              value;
            this.selectedModuleAccessRightsCategory[moduleId]["Role_Delete"] =
              value;
            this.selectedModuleAccessRightsCategory[moduleId][
              "Role_Optional_Choice"
            ] = value;
          }
        }
      }
      this.isFormDirty = true;
    },

    onSelectAll() {
      for (let rights in this.accessRights) {
        let moduleId = rights.split("-");
        moduleId = moduleId[1];
        if (parseInt(moduleId) !== 10) {
          for (let accessTypeKey in this.accessRights[rights]) {
            this.accessRights[rights][accessTypeKey] = this.selectAll;
          }
        }
      }

      for (let mId in this.selectedModuleAccessRightsCategory) {
        if (parseInt(mId) !== 10) {
          for (let accessTypeKey in this.selectedModuleAccessRightsCategory[
            mId
          ]) {
            this.selectedModuleAccessRightsCategory[mId][accessTypeKey] =
              this.selectAll;
          }
        }
      }
      this.isFormDirty = true;
    },

    async validateAddUpdateForm() {
      const { valid } = await this.$refs.rolesForm.validate();
      if (valid) {
        let accessArray = [];
        this.atleastOneRolesEnabled = false;
        for (let rights in this.accessRights) {
          let formId = rights.split("-");
          formId = formId[0];
          const {
            Role_View,
            Role_Add,
            Role_Update,
            Role_Delete,
            Role_Optional_Choice,
            Role_Hr_Group,
            Role_Payroll_Group,
          } = this.accessRights[rights];
          if (
            !this.atleastOneRolesEnabled &&
            (Role_View ||
              Role_Add ||
              Role_Update ||
              Role_Delete ||
              Role_Optional_Choice ||
              Role_Hr_Group ||
              Role_Payroll_Group)
          ) {
            this.atleastOneRolesEnabled = true;
          }
          let accessObj = {
            Form_Id: parseInt(formId),
            Roles_Id: this.rolesId,
            Role_View: Role_View ? Role_View : 0,
            Role_Add: Role_Add ? Role_Add : 0,
            Role_Update: Role_Update ? Role_Update : 0,
            Role_Delete: Role_Delete ? Role_Delete : 0,
            Role_Optional_Choice: Role_Optional_Choice
              ? Role_Optional_Choice
              : 0,
            Role_Hr_Group: Role_Hr_Group ? Role_Hr_Group : 0,
            Role_Payroll_Group: Role_Payroll_Group ? Role_Payroll_Group : 0,
          };
          accessArray.push(accessObj);
        }
        if (this.atleastOneRolesEnabled) {
          this.addUpdateRoles(accessArray);
        } else {
          let snackbarData = {
            isOpen: true,
            message: "At least one role access should be enabled",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      }
    },

    addUpdateRoles(accessArray) {
      let vm = this;
      vm.isLoading = true;
      let actionType = vm.rolesId ? "edit" : "add";
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_ROLES,
          variables: {
            roleId: vm.rolesId,
            roleName: vm.rolesName,
            description: vm.description,
            roleStatus: vm.status,
            accessRights: JSON.stringify(accessArray),
          },
          client: "apolloClientAD",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              actionType === "edit"
                ? "Roles updated successfully."
                : "Roles added successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-list");
        })
        .catch((err) => {
          vm.handleAddUpdateError(err, actionType);
        });
    },

    handleAddUpdateError(err = "", actionType) {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: actionType === "edit" ? "updating" : "adding",
          form: "roles",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    associateRoles() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ASSOCIATE_ROLES_TO_EMPLOYEES,
          variables: {
            roleId: vm.rolesId,
            employeeIds: vm.employeeIds,
          },
          client: "apolloClientAD",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Roles associated successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-list");
        })
        .catch((err) => {
          vm.handleAssociateError(err);
        });
    },

    handleAssociateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "associating",
        form: "roles",
        isListError: false,
      });
    },

    adminFormTooltips(adminForm) {
      if (adminForm === "Admin") {
        return "Have access to all modules in the system except roles and ESOP";
      } else if (adminForm === "Super Admin") {
        return "Have access to all modules(except ESOP) in the system along with ability to assign roles for others";
      } else if (adminForm === "Employee Admin") {
        return "Can view other employee details even he/she is not their manager";
      } else if (adminForm === "Payroll Admin") {
        return "Can view other employees payroll details even he/she is not their manager";
      } else if (adminForm === "Benefits Admin") {
        return "Can allocate equity to employees";
      } else if (adminForm === "Roster Admin") {
        return "Can view other employees shift details even he/she is not their manager";
      } else if (adminForm === "Productivity Monitoring Admin") {
        return "Can view other employees productivity monitoring details";
      } else if (adminForm === "Service Provider Admin") {
        return "Can view service provider level employee details in the employee and payroll module";
      }
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
