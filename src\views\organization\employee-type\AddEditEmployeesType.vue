<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">Employee Type</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mr-1">
            <v-tooltip bottom>
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="isFormDirty ? '' : props"
                  rounded="lg"
                  class="mb-2 secondary"
                  variant="elevated"
                  type="submit"
                  @click="isFormDirty ? validateEmployeeTypeForm() : {}"
                >
                  <span class="px-2 primary">Save</span>
                </v-btn>
              </template>
              <div v-if="!isFormDirty">There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon class="mt-n2" color="primary" @click="closeAddForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card-text style="height: calc(100vh - 300px); overflow: scroll">
        <v-form ref="EmployeeTypeAddForm" @submit.prevent="[]">
          <v-row class="px-sm-4 px-md-6 pt-sm-4">
            <!-- Level -->
            <v-col
              v-if="entomoIntegrationEnabled && isEntomoSyncTypePush"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="selectedLevel"
                variant="solo"
                :rules="[
                  required(`Level`, selectedLevel),
                  selectedLevel
                    ? validateWithRulesAndReturnMessages(
                        selectedLevel,
                        'level',
                        'Level',
                        true
                      )
                    : true,
                ]"
                type="number"
                style="max-width: 300px"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  Level
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <!-- Employee Code -->
            <v-col
              v-if="entomoIntegrationEnabled"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="Employee_Type_Code"
                ref="Employee_Type_Code"
                variant="solo"
                :rules="[
                  required(
                    labelList[315]?.Field_Alias || 'Employee Code',
                    Employee_Type_Code
                  ),
                  Employee_Type_Code
                    ? validateWithRulesAndReturnMessages(
                        Employee_Type_Code,
                        'Employee_Type_Code',
                        labelList[315]?.Field_Alias || 'Employee Code'
                      )
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">{{
                      labelList[315]?.Field_Alias || "Employee Code"
                    }}</span>
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-else-if="
                labelList[315] &&
                labelList[315].Field_Visiblity.toLowerCase() === 'yes'
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="Employee_Type_Code"
                ref="Employee_Type_Code"
                variant="solo"
                :rules="[
                  labelList[315].Mandatory_Field.toLowerCase() === 'yes'
                    ? required(
                        `${labelList[315].Field_Alias}`,
                        Employee_Type_Code
                      )
                    : true,
                  Employee_Type_Code
                    ? validateWithRulesAndReturnMessages(
                        Employee_Type_Code,
                        'Employee_Type_Code',
                        labelList[315].Field_Alias
                      )
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">{{
                      labelList[315].Field_Alias
                    }}</span>
                    <span
                      v-if="
                        labelList[315].Mandatory_Field.toLowerCase() === 'yes'
                      "
                      class="ml-1"
                      style="color: red"
                      >*</span
                    >
                  </div>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="employeeType"
                variant="solo"
                ref="employeeType"
                :rules="[
                  required('Employee Type', employeeType),
                  employeeTypeFirstCharacterValidation(employeeType),
                  validateWithRulesAndReturnMessages(
                    employeeType,
                    'Employee_Type',
                    'Employee Type'
                  ),
                  minMaxStringValidation('Employee Type', employeeType, 1, 250),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">Employee Type</span>
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-autocomplete
                :items="workScheduleList"
                v-model="workSchedule"
                label="Work Schedule"
                itemValue="text"
                itemTitle="text"
                ref="workSchedule"
                variant="solo"
                :isAutoComplete="true"
                :itemSelected="workSchedule"
                :isRequired="true"
                :rules="[required('Work Schedule', workSchedule)]"
                @selected-item="workSchedule = $event"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:prepend-inner>
                  <v-tooltip bottom width="400">
                    <template v-slot:activator="{ on, attrs, props }">
                      <v-icon
                        class="ml-1"
                        size="small"
                        color="blue"
                        v-bind="(attrs, props)"
                        v-on="on"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <span
                      ><p>
                        Work schedule allows you to choose whether you have to
                        add shift for all working days and consider the shift or
                        to consider default work schedule.
                      </p>
                    </span>
                  </v-tooltip>
                </template>
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">Work Schedule</span>
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
                <template v-slot:item="{ item, props }">
                  <div class="py-1 px-2" v-bind="props">
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <div
                          v-bind="props"
                          class="pa-3 rounded-lg text-truncate cursor-pointer"
                          :class="
                            isHovering
                              ? 'bg-hover'
                              : workSchedule == item.value
                              ? 'bg-primary'
                              : 'bg-grey-lighten-4'
                          "
                          :style="listWidth"
                        >
                          <v-list-item>
                            {{ item.value }}
                          </v-list-item>
                        </div>
                      </template>
                    </v-hover>
                  </div>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-autocomplete
                :items="salaryCalculationDaysList"
                v-model="salaryCalculationDays"
                label="Salary Calculation Days"
                item-value="value"
                item-title="text"
                ref="salaryCalculationDays"
                variant="solo"
                :itemSelected="salaryCalculationDays"
                :isAutoComplete="true"
                :isRequired="true"
                :rules="[
                  required('Salary Calculation Days', salaryCalculationDays),
                ]"
                @selected-item="salaryCalculationDays = $event"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:prepend-inner>
                  <v-tooltip bottom width="400">
                    <template v-slot:activator="{ on, attrs, props }">
                      <v-icon
                        class="ml-1"
                        size="small"
                        color="blue"
                        v-bind="(attrs, props)"
                        v-on="on"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <span
                      ><p>
                        Salary calculation days allows the user to choose the
                        number of days for which the salary is calculated. The
                        salary can be calculated for all days in a year or it
                        can be for the business working days or for an average
                        number of days.
                      </p>
                    </span>
                  </v-tooltip>
                </template>
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">Salary Calculation Days</span>
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
                <template v-slot:item="{ item, props }">
                  <div class="py-1 px-2" v-bind="props">
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <div
                          v-bind="props"
                          class="pa-3 rounded-lg text-truncate cursor-pointer"
                          :class="
                            isHovering
                              ? 'bg-hover'
                              : salaryCalculationDays == item.value
                              ? 'bg-primary'
                              : 'bg-grey-lighten-4'
                          "
                          :style="listWidth"
                        >
                          <v-list-item>
                            {{ item.title }}
                          </v-list-item>
                        </div>
                      </template>
                    </v-hover>
                  </div>
                </template>
              </v-autocomplete>
            </v-col>

            <v-col
              v-if="salaryCalculationDays === '3'"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
            >
              <v-text-field
                v-model="fixedDays"
                variant="solo"
                ref="fixedDays"
                type="number"
                :rules="[
                  required('Fixed Days', fixedDays),
                  validateWithRulesAndReturnMessages(
                    fixedDays,
                    'Fixed_Days',
                    'Fixed Days',
                    true
                  ),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">Fixed Days</span>
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-autocomplete
                :items="compOffCalculationDaysList"
                v-model="compOffCalculationDays"
                label="Comp Off Calculation Days"
                item-value="value"
                item-title="text"
                ref="compOffCalculationDays"
                variant="solo"
                :isAutoComplete="true"
                :itemSelected="compOffCalculationDays"
                :isRequired="true"
                :rules="[
                  required('Comp Off Calculation Days', compOffCalculationDays),
                ]"
                @selected-item="compOffCalculationDays = $event"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:prepend-inner>
                  <v-tooltip bottom width="400">
                    <template v-slot:activator="{ on, attrs, props }">
                      <v-icon
                        class="ml-1"
                        size="small"
                        color="blue"
                        v-bind="(attrs, props)"
                        v-on="on"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <span
                      ><p>
                        Comp off calculation days allows the user to choose the
                        number of days for which the comp off is calculated. The
                        comp off can be calculated for all days in a year or it
                        can be for the business working days or for an average
                        number of days.
                      </p>
                    </span>
                  </v-tooltip>
                </template>
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">Comp Off Calculation Days</span>
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
                <template v-slot:item="{ item, props }">
                  <div class="py-1 px-2" v-bind="props">
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <div
                          v-bind="props"
                          class="pa-3 rounded-lg text-truncate cursor-pointer"
                          :class="
                            isHovering
                              ? 'bg-hover'
                              : compOffCalculationDays == item.value
                              ? 'bg-primary'
                              : 'bg-grey-lighten-4'
                          "
                          :style="listWidth"
                        >
                          <v-list-item>
                            {{ item.title }}
                          </v-list-item>
                        </div>
                      </template>
                    </v-hover>
                  </div>
                </template>
              </v-autocomplete>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-row class="d-flex align-center">
                <v-col class="d-flex align-center">
                  <p class="text-subtitle-1 text-grey-darken-1 m-0">
                    Eligible For Benefits
                  </p>
                </v-col>
                <v-col class="d-flex justify-end">
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="eligibleForBenefits"
                    true-value="Yes"
                    false-value="No"
                    @update:model-value="deductFormChange()"
                  ></v-switch>
                </v-col>
              </v-row>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-row class="d-flex align-center">
                <v-col class="d-flex align-center">
                  <p class="text-subtitle-1 text-grey-darken-1 m-0">
                    Holiday Eligibility
                  </p>
                </v-col>
                <v-col class="d-flex justify-end">
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="holidayEligibility"
                    true-value="Yes"
                    false-value="No"
                    @update:model-value="deductFormChange()"
                  ></v-switch>
                </v-col>
              </v-row>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-row class="d-flex align-center">
                <v-col class="d-flex align-center">
                  <p class="text-subtitle-1 text-grey-darken-1 m-0">
                    Display Duration In Hours And Minutes
                  </p>
                </v-col>
                <v-col class="d-flex justify-end">
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="displayDurationInHoursAndMinutes"
                    true-value="Yes"
                    false-value="No"
                    @update:model-value="deductFormChange()"
                  ></v-switch>
                </v-col>
              </v-row>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-row class="d-flex align-center">
                <v-col class="d-flex align-center">
                  <p class="text-subtitle-1 text-grey-darken-1 m-0">
                    Exclude Break Hours
                  </p>
                </v-col>
                <v-col class="d-flex justify-end">
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="excludeBreakHours"
                    true-value="Yes"
                    false-value="No"
                    @update:model-value="deductFormChange()"
                  ></v-switch>
                </v-col>
              </v-row>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-autocomplete
                :items="processedBiometricAttendanceRecordStatusList"
                v-model="processedBiometricAttendanceRecordStatus"
                label="Processed Biometric Attendance Record Status"
                itemValue="text"
                itemTitle="text"
                ref="processedBiometricAttendanceRecordStatus"
                variant="solo"
                :isAutoComplete="true"
                :itemSelected="processedBiometricAttendanceRecordStatus"
                :isRequired="true"
                :rules="[
                  required(
                    'Processed Biometric Attendance Record Status',
                    processedBiometricAttendanceRecordStatus
                  ),
                ]"
                @selected-item="
                  processedBiometricAttendanceRecordStatus = $event
                "
                @update:model-value="deductFormChange()"
              >
                <template v-slot:prepend-inner>
                  <v-tooltip bottom width="400">
                    <template v-slot:activator="{ on, attrs, props }">
                      <v-icon
                        class="ml-1"
                        size="small"
                        color="blue"
                        v-bind="(attrs, props)"
                        v-on="on"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <span>
                      <p>
                        Processed Biometric Attendance Record Status allows the
                        admin to choose whether biometric attendance data
                        required further approval or it can be considered a
                        trusted source and approved during the data processing.
                      </p>
                    </span>
                  </v-tooltip>
                </template>
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate"
                      >Processed Biometric Attendance Record Status</span
                    >
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
                <template v-slot:item="{ item, props }">
                  <div class="py-1 px-2" v-bind="props">
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <div
                          v-bind="props"
                          class="pa-3 rounded-lg text-truncate cursor-pointer"
                          :class="
                            isHovering
                              ? 'bg-hover'
                              : processedBiometricAttendanceRecordStatus ==
                                item.value
                              ? 'bg-primary'
                              : 'bg-grey-lighten-4'
                          "
                          :style="listWidth"
                        >
                          <v-list-item>
                            {{ item.value }}
                          </v-list-item>
                        </div>
                      </template>
                    </v-hover>
                  </div>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-row class="d-flex align-center">
                <v-col class="d-flex align-center">
                  <p class="text-subtitle-1 text-grey-darken-1 m-0">
                    Enable Work Place
                  </p>
                  <v-tooltip bottom width="400">
                    <template v-slot:activator="{ on, attrs, props }">
                      <v-icon
                        class="ml-1"
                        size="small"
                        color="blue"
                        v-bind="(attrs, props)"
                        v-on="on"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <span>
                      <p>
                        Enable work place flag allows you to choose the work
                        place every time when user adds the attendance.
                      </p>
                    </span>
                  </v-tooltip>
                  <template v-slot:label>
                    <div class="d-flex">
                      <span class="text-truncate">Enable Work Place</span>
                      <span class="ml-1" style="color: red">*</span>
                    </div>
                  </template>
                </v-col>
                <v-col class="d-flex justify-end">
                  <v-switch
                    color="primary"
                    class="mt-4"
                    v-model="enableWorkPlace"
                    true-value="Yes"
                    false-value="No"
                    @click="disableWorkPlace"
                    @update:model-value="deductFormChange()"
                  ></v-switch>
                </v-col>
              </v-row>
            </v-col>

            <v-col
              v-if="
                enableWorkPlace === 'Yes' &&
                workPlacesList &&
                workPlacesList.length
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
            >
              <CustomSelect
                :items="workPlacesList"
                v-model="workPlace"
                label="Work Place"
                itemValue="Work_Place_Id"
                itemTitle="Work_Place"
                ref="workPlace"
                variant="solo"
                :itemSelected="workPlace"
                :isRequired="true"
                :rules="[requiredArray('Work Place', workPlace)]"
                @selected-item="workPlace = $event"
                @update:model-value="deductFormChange()"
                :selectProperties="{
                  multiple: true,
                  chips: true,
                  clearable: true,
                  closableChips: true,
                }"
              >
              </CustomSelect>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-autocomplete
                :items="filteredApproveDashboardAttendanceList"
                v-model="approveDashboardAttendance"
                label="Approve Dashboard Attendance"
                itemValue="text"
                itemTitle="text"
                ref="approveDashboardAttendance"
                variant="solo"
                :isAutoComplete="true"
                :itemSelected="approveDashboardAttendance"
                :isRequired="true"
                :rules="[
                  required(
                    'Approve Dashboard Attendance',
                    approveDashboardAttendance
                  ),
                ]"
                @selected-item="approveDashboardAttendance = $event"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate"
                      >Approve Dashboard Attendance</span
                    >
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
                <template v-slot:item="{ item, props }">
                  <div class="py-1 px-2" v-bind="props">
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <div
                          v-bind="props"
                          class="pa-3 rounded-lg text-truncate cursor-pointer"
                          :class="
                            isHovering
                              ? 'bg-hover'
                              : approveDashboardAttendance == item.value
                              ? 'bg-primary'
                              : 'bg-grey-lighten-4'
                          "
                          :style="listWidth"
                        >
                          <v-list-item>
                            {{ item.title }}
                          </v-list-item>
                        </div>
                      </template>
                    </v-hover>
                  </div>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col
              v-if="
                approveDashboardAttendance &&
                approveDashboardAttendance.length &&
                approveDashboardAttendance.toLowerCase().trim() ===
                  'automatically for selected work place'
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
            >
              <v-autocomplete
                :items="selectedWorkPlaceList"
                v-model="autoApprovalWorkPlace"
                label="Auto Approval Work Place"
                itemValue="Work_Place_Id"
                itemTitle="Work_Place"
                ref="autoApprovalWorkPlace"
                variant="solo"
                multiple
                chips
                closable-chips
                clearable
                :isRequired="true"
                :rules="[requiredWorkPlace]"
                @selected-item="autoApprovalWorkPlace = $event"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <div class="d-flex">
                    <span class="text-truncate">Auto Approval Work Place</span>
                    <span class="ml-1" style="color: red">*</span>
                  </div>
                </template>
                <template v-slot:item="{ item, props }">
                  <div class="py-1 px-2" v-bind="props">
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <div
                          v-bind="props"
                          class="pa-3 rounded-lg text-truncate cursor-pointer"
                          :class="
                            isHovering
                              ? 'bg-hover'
                              : autoApprovalWorkPlace &&
                                autoApprovalWorkPlace.includes(item.value)
                              ? 'bg-primary'
                              : 'bg-grey-lighten-4'
                          "
                        >
                          <v-list-item>
                            {{ item.title }}
                          </v-list-item>
                        </div>
                      </template>
                    </v-hover>
                  </div>
                </template>
              </v-autocomplete>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="12"
              md="6"
              xl="12"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-textarea
                v-model="description"
                rows="2"
                row-height="10"
                hide-details="auto"
                variant="solo"
                label="Description"
                counter="600"
                :rules="[
                  description
                    ? validateWithRulesAndReturnMessages(
                        description,
                        'description',
                        'Description'
                      )
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isLoadingCard"
        scrim="#fff"
      >
        <v-progress-circular color="secondary" indeterminate size="54">
        </v-progress-circular>
      </v-overlay>
    </v-card>
    <AppWarningModal
      v-if="showConfirmation"
      :open-modal="showConfirmation"
      imgUrl="common/exit_form"
      confirmation-heading="Are you sure to exit this form?"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 secondary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import {
  LIST_WORK_PLACES,
  ADD_UPDATE_EMPLOYEE_TYPE,
} from "@/graphql/organisation/employeetype/employeeTypeQueries";
import mixpanel from "mixpanel-browser";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "AddEditEmployeesType",
  mixins: [validationRules],
  components: {
    CustomSelect,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "form-updated"],
  data: () => ({
    //others
    isFormDirty: false,
    showConfirmation: false,
    // Form data
    empTypeId: 0,
    selectedLevel: null,
    Employee_Type_Code: null,
    employeeType: "",
    selectedItem: null,
    eligibleForBenefits: "No",
    holidayEligibility: "Yes",
    workSchedule: null,
    salaryCalculationDays: null,
    compOffCalculationDays: null,
    displayDurationInHoursAndMinutes: "No",
    excludeBreakHours: "No",
    processedBiometricAttendanceRecordStatus: null,
    enableWorkPlace: "No",
    approveDashboardAttendance: null,
    autoApprovalWorkPlace: null,
    workPlace: null,
    fixedDays: null,
    Status: null,
    description: "",

    // Dropdown options
    workScheduleList: [
      { value: 1, text: "Employee Level" },
      { value: 2, text: "Shift Roster" },
    ],
    salaryCalculationDaysList: [
      {
        value: "1",
        text: "All Days of Salary Month",
      },
      {
        value: "2",
        text: "Average Days in a Month",
      },
      { value: "0", text: "Business Working Days" },
      {
        value: "3",
        text: "Fixed Days of Salary Month",
      },
    ],
    compOffCalculationDaysList: [
      { value: "1", text: "All Days of Salary Month" },
      { value: "2", text: "Average Days in a Month" },
      { value: "0", text: "Business Working Days" },
      {
        value: "3",
        text: "Fixed Days of Salary Month",
      },
    ],
    approveDashboardAttendanceList: [
      { value: 1, text: "Automatically" },
      {
        value: 2,
        text: "Automatically For Selected Work Place",
      },
      { value: 3, text: "Manually" },
    ],
    processedBiometricAttendanceRecordStatusList: [
      { value: 1, text: "Auto Approval" },
      { value: 2, text: "Manual Approval" },
    ],
    workPlacesList: [],
    originalWorkPlacesList: [],
    statusList: [
      { value: 1, text: "Active" },
      { value: 2, text: "InActive" },
    ],

    // Loading and validation states
    isLoadingCard: false,
    validationMessages: [],
    showValidationAlert: false,
  }),

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    filteredApproveDashboardAttendanceList() {
      if (this.enableWorkPlace === "Yes") {
        return this.approveDashboardAttendanceList;
      } else if (
        this.approveDashboardAttendanceList &&
        this.approveDashboardAttendanceList.length
      ) {
        return this.approveDashboardAttendanceList.filter(
          (item) =>
            item.text.toLowerCase() !== "automatically for selected work place"
        );
      } else {
        return [];
      }
    },
    selectProperties() {
      return {
        multiple: false,
        chips: false,
        clearable: true,
        closableChips: false,
      };
    },
    selectedWorkPlaceList() {
      return this.workPlacesList.filter((item) => {
        return this.workPlace && this.workPlace.includes(item.Work_Place_Id);
      });
    },
    isSelected() {
      return (value) => {
        if (this.selectProperties.multiple) {
          return this.selectedItem && this.selectedItem.includes(value);
        } else return value === this.selectedItem;
      };
    },
  },

  mounted() {
    if (this.isEdit) {
      const {
        EmpType_Id,
        Employee_Type_Code,
        Employee_Type,
        Benefits_Applicable,
        Holiday_Eligiblity,
        Work_Schedule,
        Salary_Calc_Days,
        Comp_Off_Days,
        Display_Total_Hours_In_Minutes,
        Exclude_Break_Hours,
        Attendance_Process_Status,
        Enable_Work_Place,
        Approve_Dashboard_Attendance,
        Work_Place_Id,
        Fixed_Days,
        Auto_Approval_Work_Place_Id,
        EmployeeType_Status,
        Description,
        Level,
      } = this.editFormData;
      this.selectedLevel = Level;
      this.empTypeId = EmpType_Id ? parseInt(EmpType_Id) : 0;
      this.Employee_Type_Code = Employee_Type_Code ? Employee_Type_Code : null;
      this.employeeType = Employee_Type ? Employee_Type : "";
      this.eligibleForBenefits = Benefits_Applicable === 1 ? "Yes" : "No";
      this.holidayEligibility = Holiday_Eligiblity === 1 ? "Yes" : "No";
      this.workSchedule = Work_Schedule ? Work_Schedule : "";
      this.salaryCalculationDays = Salary_Calc_Days
        ? Salary_Calc_Days.toString()
        : "0";
      this.fixedDays = Fixed_Days ? parseInt(Fixed_Days) : 0;
      this.compOffCalculationDays = Comp_Off_Days
        ? Comp_Off_Days.toString()
        : "0";
      this.displayDurationInHoursAndMinutes =
        Display_Total_Hours_In_Minutes === 1 ? "Yes" : "No";
      this.excludeBreakHours = Exclude_Break_Hours === 1 ? "Yes" : "No";
      this.processedBiometricAttendanceRecordStatus =
        Attendance_Process_Status || "";
      this.enableWorkPlace = Enable_Work_Place === 1 ? "Yes" : "No";
      this.approveDashboardAttendance = Approve_Dashboard_Attendance
        ? Approve_Dashboard_Attendance
        : "";
      this.autoApprovalWorkPlace = Auto_Approval_Work_Place_Id
        ? Auto_Approval_Work_Place_Id.split(`,`).map(Number)
        : null;
      this.workPlace = Work_Place_Id
        ? Work_Place_Id.split(`,`).map(Number)
        : null;
      this.Status = EmployeeType_Status ? EmployeeType_Status : "";
      this.description = Description ? Description : "";
    }
    this.fetchWorkPlaces();
  },

  methods: {
    requiredWorkPlace(value) {
      return (value && value.length > 0) || "Work Place is required";
    },
    disableWorkPlace() {
      if (this.enableWorkPlace) {
        this.approveDashboardAttendance = null;
      }
    },
    convertYesToBinary(value) {
      return value === "Yes" ? 1 : 0;
    },
    async validateEmployeeTypeForm() {
      // Validate the form fields
      const { valid } = await this.$refs.EmployeeTypeAddForm.validate();

      if (valid) {
        // Submit the form if all fields are valid
        this.addupdateList();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });

        // Handle and log invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "Employee_Type_Code",
                "employeeType",
                "workSchedule",
                "salaryCalculationDays",
                "compOffCalculationDays",
                "processedBiometricAttendanceRecordStatus",
                "approveDashboardAttendance",
                "autoApprovalWorkPlace",
                "workPlace",
              ];

              // If the field is a v-autocomplete component, call the custom focus method
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect
                  ? fieldRef.onFocusCustomSelect()
                  : fieldRef.focus();
              } else {
                fieldRef.focus();
              }

              // Scroll to the first invalid field
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: window.scrollY + rect.top * 0.4,
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },

    async addupdateList() {
      let isFormValid = await this.$refs.EmployeeTypeAddForm.validate();
      let vm = this;
      let isUpdate = !!vm.empTypeId;
      if (isFormValid && isFormValid.valid) {
        try {
          vm.isLoadingCard = true;
          vm.$apollo
            .mutate({
              mutation: ADD_UPDATE_EMPLOYEE_TYPE,
              client: "apolloClientBB",
              fetchPolicy: "no-cache",
              variables: {
                EmpType_Id: vm.empTypeId,
                Level: vm.selectedLevel ? parseInt(vm.selectedLevel) : null,
                Employee_Type_Code:
                  vm.Employee_Type_Code === null || vm.Employee_Type_Code === ""
                    ? null
                    : vm.Employee_Type_Code,
                Employee_Type: vm.employeeType,
                Benefits_Applicable: vm.convertYesToBinary(
                  vm.eligibleForBenefits
                ),
                Holiday_Eligiblity: vm.convertYesToBinary(
                  vm.holidayEligibility
                ),
                Work_Schedule: vm.workSchedule || null,
                Salary_Calc_Days: parseInt(vm.salaryCalculationDays),
                Comp_Off_Days: parseInt(vm.compOffCalculationDays),
                Fixed_Days: parseInt(vm.fixedDays),
                Display_Total_Hours_In_Minutes: vm.convertYesToBinary(
                  vm.displayDurationInHoursAndMinutes
                ),
                Exclude_Break_Hours: vm.convertYesToBinary(
                  vm.excludeBreakHours
                ),
                Attendance_Process_Status:
                  vm.processedBiometricAttendanceRecordStatus || null,
                Enable_Work_Place: vm.convertYesToBinary(vm.enableWorkPlace),
                Approve_Dashboard_Attendance:
                  vm.approveDashboardAttendance || null,
                Work_Place_Id:
                  vm.enableWorkPlace.toLowerCase() === "yes"
                    ? vm.workPlace
                    : null,
                Auto_Approval_Work_Place_Id:
                  vm.approveDashboardAttendance.toLowerCase() ===
                  "automatically for selected work place"
                    ? vm.autoApprovalWorkPlace
                    : null,
                EmployeeType_Status: vm.Status || "Active",
                Description: vm.description || null,
              },
            })
            .then((response) => {
              if (
                response &&
                response.data &&
                response.data.addUpdateEmployeeType
              ) {
                const { errorCode, validationError } =
                  response.data.addUpdateEmployeeType;
                if (!errorCode && !validationError) {
                  let snackbarData = {
                    isOpen: true,
                    type: "success",
                    message: isUpdate
                      ? vm.landedFormName + " updated successfully."
                      : vm.landedFormName + " added successfully.",
                  };
                  vm.showAlert(snackbarData);
                  vm.$emit("form-updated");
                  vm.isLoadingCard = false;
                } else {
                  vm.handleAddEditError(isUpdate ? "updating" : "adding");
                }
              } else {
                vm.handleAddEditError(isUpdate ? "updating" : "adding");
              }
            })
            .catch((addEditError) => {
              vm.handleAddEditError(
                isUpdate ? "updating" : "adding",
                addEditError
              );
            });
        } catch (e) {
          vm.handleAddEditError(isUpdate ? "updating" : "adding");
        }
      }
    },
    handleAddEditError(action, err = "") {
      this.isLoadingCard = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: action,
          form: "employeeType",
          isListError: false,
        })
        .then((validationErrors) => {
          this.validationMessages = validationErrors;
          this.showValidationAlert = true;
        });
    },

    fetchWorkPlaces() {
      let vm = this;
      let status = this.workPlaceStatus || "Active";
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_WORK_PLACES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
          variables: {
            status: status,
          },
        })
        .then((response) => {
          if (response.data && response.data.listWorkPlaces) {
            const responseData = JSON.parse(
              response.data.listWorkPlaces.workPlaces
            );
            vm.workPlacesList = responseData.map((item) => ({
              ...item,
              Work_Place: item.Work_Place || "Unknown Work Place",
            }));
            vm.originalWorkPlacesList = responseData;
            vm.listLoading = false;
            mixpanel.track("Work places list retrieved");
          } else {
            vm.handleWorkPlacesError();
          }
        })
        .catch((err) => {
          vm.handleWorkPlacesError(err);
        });
    },

    handleWorkPlacesError(err = "") {
      mixpanel.track("Work places error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Work Places",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    employeeTypeFirstCharacterValidation(value) {
      if (value && value.startsWith(" ")) {
        return "The first character of Employee Type cannot be a space.";
      }
      return true;
    },
    deductFormChange() {
      this.isFormDirty = true;
    },

    closeAddForm() {
      if (this.isFormDirty) {
        this.showConfirmation = true;
      } else {
        this.closeForm();
      }
    },
    abortClose() {
      this.showConfirmation = false;
    },

    acceptClose() {
      this.showConfirmation = false;
      this.closeForm();
    },

    closeForm() {
      this.isFormDirty = false;
      this.$emit("close-form");
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
