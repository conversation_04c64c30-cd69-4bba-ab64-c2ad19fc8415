import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_SPECIAL_WAGES = gql`
  query retrieveSpecialWages {
    retrieveSpecialWages {
      errorCode
      message
      SpecialWagesConfiguration {
        Custom_Group_Id
        Configuration_Id
        Group_Name
        Attendance_Required
        Wage_Factor
        Salary_Type
        Special_Work_Days
        Status
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_SPECIAL_WAGES = gql`
  mutation addUpdateSpecialWages(
    $Configuration_Id: Int!
    $CustomGroup_Id: Int
    $Salary_Type: String!
    $Attendance_Required: String
    $Wage_Factor: Float
    $Special_Work_Days: String!
    $Status: String!
  ) {
    addUpdateSpecialWages(
      Configuration_Id: $Configuration_Id
      CustomGroup_Id: $CustomGroup_Id
      Attendance_Required: $Attendance_Required
      Wage_Factor: $Wage_Factor
      Salary_Type: $Salary_Type
      Special_Work_Days: $Special_Work_Days
      Status: $Status
    ) {
      errorCode
      message
    }
  }
`;
