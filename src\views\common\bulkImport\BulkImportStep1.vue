<template>
  <div>
    <v-row class="my-auto mx-auto" justify="center" align="center">
      <v-col cols="12" md="11" sm="12" xs="12">
        <v-row>
          <v-col
            cols="12"
            sm="12"
            md="12"
            lg="6"
            xlg="6"
            class="step-content-col"
          >
            <v-row>
              <v-col cols="12" class="pa-0 mb-2">
                <div class="d-flex">
                  <v-avatar v-if="!isMobileView" color="light-blue-lighten-4">
                    <v-icon
                      style="
                        --fa-primary-color: #5c98ff;
                        --fa-secondary-color: #5c98ff;
                      "
                      >fas fa-cloud-download-alt</v-icon
                    >
                  </v-avatar>
                  <div class="text-primary font-weight-bold text-h6 pl-sm-2">
                    {{ step1Text.heading }}
                  </div>
                </div>
                <div
                  :style="isMobileView ? '' : 'border-left: 2px dotted #77a1ff'"
                  class="ml-sm-5"
                >
                  <div class="text-primary-lighten-4 mt-2 pl-sm-8">
                    {{ step1Text.text }}
                    <div v-if="showDownload" class="mt-1">
                      <v-btn
                        id="bulk_sheet_download"
                        rounded="lg"
                        size="small"
                        color="secondary"
                        class="font-weight-bold"
                        @click="generateExcel()"
                      >
                        <v-icon class="mr-2" size="16"
                          >fas fa-cloud-download-alt</v-icon
                        >
                        Download
                      </v-btn>
                    </div>
                  </div>
                </div>
                <v-icon
                  v-if="!isMobileView"
                  color="cyan-lighten-1"
                  class="mt-n2 ml-1"
                  style="padding-left: 11px"
                  >fas fa-chevron-down</v-icon
                >
              </v-col>
              <v-col cols="12" class="pa-0 mb-2">
                <div class="d-flex">
                  <v-avatar v-if="!isMobileView" color="#b5ffb0">
                    <v-icon color="grey-lighten"
                      >fas fa-cloud-upload-alt</v-icon
                    >
                  </v-avatar>
                  <div class="text-primary font-weight-bold text-h6 pl-sm-2">
                    Map the imported template headers with
                    {{ organizationName }} properties for import
                  </div>
                </div>
                <div
                  :style="isMobileView ? '' : 'border-left: 2px dotted #77a1ff'"
                  class="ml-sm-5"
                >
                  <div class="text-primary-lighten-4 mt-2 pl-sm-8">
                    Map the {{ organizationName }} properties as required for
                    import. Do not worry about the data as you can still edit
                    them online like how you do in excel
                  </div>
                </div>
                <v-icon
                  v-if="!isMobileView"
                  color="cyan-lighten-1"
                  class="mt-n2 ml-1"
                  style="padding-left: 11px"
                  >fas fa-chevron-down</v-icon
                >
              </v-col>
              <v-col cols="12" class="pa-0">
                <div class="d-flex">
                  <v-avatar v-if="!isMobileView" color="#ade7ff">
                    <v-icon color="cyan">fas fa-edit</v-icon>
                  </v-avatar>
                  <div class="text-primary font-weight-bold text-h6 pl-sm-2">
                    Review, validate and adjust before processing in online
                    excel interface
                  </div>
                </div>
                <div class="ml-sm-5">
                  <div class="text-primary-lighten-4 mt-2 pl-sm-8">
                    You can review the data and amend as required before
                    validation and processing
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-col>
          <v-col
            cols="12"
            sm="12"
            md="12"
            lg="6"
            xlg="6"
            class="step-content-col"
            :class="!fileContent ? 'mt-10' : ''"
          >
            <v-row justify="center">
              <v-col :cols="isMobileView ? '12' : '10'">
                <file-pond
                  v-if="!fileContent"
                  ref="vueFilePond"
                  name="test"
                  label-idle="<div class='custom-file-upload-label-icon'>
                                                                  <i class='fas fa-cloud-upload-alt upload-icon' style='color: grey;font-size: 70px;'></i>
                                                                  </div>
                                                                  <div class='custom-file-pond-label-header'>
                                                                          <span class='custom-file-pond-label-sub-header1'>Choose a file </span> 	&nbsp; 
                                                                          <span class='custom-file-pond-label-sub-header2'> or drag it here.</span>
                                                                  </div>
                                                                  <div class='custom-label-less-than-450' style='margin-top: 40px'>
                                                                      <div style='text-align: center;'><span class='custom-file-pond-label-sub-header1'>Choose a file</span> or drag it here.</div>
                                                                      <div style='text-align: center;'>*All .Csv, .Xlsx, And .Xls File Types Are Supported.</div>
                                                                  </div>
                                                                  <div class='custom-file-pond-label-content'>*All .Csv, .Xlsx, And .Xls File Types Are Supported.</div>"
                  accepted-file-types="text/csv, application/csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                  @addfile="getFileContent"
                  @removefile="removeFileContent"
                />
                <v-card
                  v-else
                  class="rounded-lg common-box-shadow"
                  color="cyan-lighten-5"
                >
                  <v-card-title class="d-flex justify-end">
                    <v-icon class="mt-n1" size="20" @click="removeFileContent"
                      >fas fa-times</v-icon
                    >
                  </v-card-title>
                  <v-card-text
                    class="d-flex justify-center align-center text-center mx-auto flex-column mb-4 mt-n4 pa-5"
                  >
                    <v-img
                      width="20%"
                      height="auto"
                      :src="filePreviewImage"
                      alt="shares file preview"
                      class="mx-auto mb-4"
                    />
                    <div class="text-h6 mt-3" style="max-width: 300px">
                      {{ fileProperties[0] }}
                    </div>
                    <div class="mt-2">{{ fileProperties[1] }}</div>
                    <v-btn
                      dense
                      size="small"
                      rounded="lg"
                      class="mt-4"
                      color="grey-lighten-2"
                      @click="removeFileContent"
                      >Remove and upload again</v-btn
                    >
                    <br />
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </div>
</template>
<script>
import * as XLSX from "xlsx";
import vueFilePond from "vue-filepond";
import "filepond/dist/filepond.min.css";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";

// Create component
const FilePond = vueFilePond(
  FilePondPluginFileValidateType,
  FilePondPluginImagePreview
);

export default {
  name: "BulkImportStep1",
  components: {
    FilePond,
  },
  props: {
    step1Text: {
      type: Object,
      default() {
        return {
          type: "data",
          heading: "Download the excel template with predefined headers",
          text: "You have the option of using our predefined template or bring in your own data sheet with the headers for import",
        };
      },
    },
    showDownload: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({
    fileContent: null,
    fileProperties: [],
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    filePreviewImage() {
      if (this.isBrowserSupportWebp) {
        return require("@/assets/images/common/file-preview.webp");
      } else {
        return require("@/assets/images/common/file-preview.png");
      }
    },
    organizationName() {
      let domain = this.$store.getters.domain;
      //on receiving organization name update app title
      if (domain === "cannyhr") {
        return "CANNYHR";
      } else {
        return "HRAPP";
      }
    },
  },
  methods: {
    generateExcel() {
      this.$emit("generate-excel");
    },
    // called when file is added on vue-filepond plugin
    getFileContent(error, file) {
      // error: returned from plugin, if error, then we don't process file reade
      if (!error) {
        // define fire reader
        var reader = new FileReader();
        // on-loading file
        reader.onload = (e) => {
          let uploadedFile = file.file,
            fileSize = uploadedFile.size / 1000;
          this.fileProperties = [uploadedFile.name, fileSize + "kb"];

          /* Parse data */
          const binaryToString = e.target.result;
          // reading binary file value using XLSX
          const workBook = XLSX.read(binaryToString, {
            type: "binary",
          });

          /* Get first worksheet */
          const workSheetName = workBook.SheetNames[0];
          const workSheet = workBook.Sheets[workSheetName];

          /* Convert array of arrays */
          const fileContent = XLSX.utils.sheet_to_json(workSheet, {
            header: 1,
            raw: false,
          });
          this.fileContent = fileContent;
          this.checkFileUploadStatus(fileContent, workSheet);
        };
        reader.onerror = function (ex) {
          if (ex) this.$emit("file-upload-error");
        };
        reader.readAsBinaryString(file.file);
      } else {
        this.fileContent = null;
        this.$emit("file-upload-error");
      }
    },
    checkFileUploadStatus(fileContent, workSheet) {
      var XL_row_object = XLSX.utils.sheet_to_row_object_array(workSheet);
      if (XL_row_object.length > 0 && fileContent[0].length > 0) {
        this.$emit("file-upload-success", fileContent);
      } else {
        this.$emit("file-upload-error");
        this.showAlert(XL_row_object, fileContent);
      }
    },
    // on file remove, handled in plugin itself
    removeFileContent() {
      this.fileContent = null;
      this.$emit("file-upload-error");
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style lang="css" scoped>
@import "../../../assets/css/customFilePondDocUploader.css";
</style>
