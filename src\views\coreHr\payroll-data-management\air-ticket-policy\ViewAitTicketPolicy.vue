<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="closeEditForm()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:50vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">
              View {{ landedFormName }}
            </div>
            <v-btn icon class="clsBtn" variant="text" @click="closeEditForm()">
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </v-card-title>

          <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
            <div class="d-flex justify-end align-center">
              <v-btn
                v-if="accessRights?.update"
                @click="$emit('open-edit-form')"
                class="mr-3 mt-3 text-primary"
                variant="text"
                rounded="lg"
              >
                <v-icon class="mr-1" size="15">fas fa-edit</v-icon>Edit
              </v-btn>
            </div>
            <div class="px-6 py-2">
              <v-row>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Destination City
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Destination_City) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Destination Country
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Destination_Country) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Air Ticket Category
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Air_Ticketing_Category) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">Status</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedItem.Status) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Air Fare Entitlement for Infant
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    <span v-if="payrollCurrency && selectedItem.Infant_Amount">
                      {{ payrollCurrency }}</span
                    >
                    {{ checkNullValue(selectedItem.Infant_Amount) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Air Fare Entitlement for Child
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    <span v-if="payrollCurrency && selectedItem.Child_Amount">
                      {{ payrollCurrency }}</span
                    >
                    {{ checkNullValue(selectedItem.Child_Amount) }}
                  </p>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Air Fare Entitlement for Adult
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    <span v-if="payrollCurrency"> {{ payrollCurrency }}</span>
                    {{ checkNullValue(selectedItem.Adult_Amount) }}
                  </p>
                </v-col>
              </v-row>
              <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  /> </v-col
              ></v-row>
            </div>
          </v-card-text>
        </v-card>
      </template>
    </v-overlay>
  </div>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "ViewAitTicketPolicy",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    showViewForm: true,
    moreDetailsList: [],
    openMoreDetails: true,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
  },
  mounted() {
    this.prefillMoreDetails();
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    closeEditForm() {
      this.showViewForm = false;
      this.$emit("close-form");
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.selectedItem.Added_On),
        addedByName = this.selectedItem.Added_By,
        updatedByName = this.selectedItem.Updated_By,
        updatedOn = this.convertUTCToLocal(this.selectedItem.Updated_On);
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
