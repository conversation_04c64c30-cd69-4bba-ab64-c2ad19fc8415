<template>
  <v-sheet
    class="align-center text-center floating-card"
    :class="windowWidth > 450 ? 'd-flex pa-6' : 'pa-2'"
    height="60px"
    width="100%"
  >
    <div v-if="windowWidth <= 450 && changesCount > 0" class="pb-3">
      {{ $t("settings.changedSettings", { count: changesCount }) }}
    </div>
    <v-btn
      id="bottom_sheet_cancel"
      variant="outlined"
      elevation="2"
      @click="$emit('cancel')"
      >{{ $t("settings.cancel") }}</v-btn
    >
    <v-btn
      id="bottom_sheet_save"
      class="ml-2"
      color="primary"
      variant="elevated"
      :loading="isLoading"
      :disabled="disableSaveBtn"
      @click="$emit('save')"
      >{{ $t("settings.save") }}</v-btn
    >
    <span v-if="windowWidth > 450 && changesCount > 0" class="ml-2">{{
      $t("settings.changedSettings", { count: changesCount })
    }}</span>
  </v-sheet>
</template>
<script>
export default {
  name: "BottomSheetPopup",
  props: {
    changesCount: {
      type: Number,
      default: 0,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    openAlert: {
      type: Boolean,
      required: true,
    },
    disableSaveBtn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      openBottomSheet: false,
    };
  },
  computed: {
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.openBottomSheet = this.openAlert;
  },
};
</script>
<style>
.floating-card {
  position: fixed;
  bottom: 0;
  z-index: 10;
  /* Optional: Add some animation */
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}
</style>
