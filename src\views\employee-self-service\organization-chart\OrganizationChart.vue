<template>
  <div v-if="mainTabs.length > 0">
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :showBottomSheet="organizationList.length > 0"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row justify="center" v-if="organizationList.length > 0">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isDefaultFilter="false"
            >
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
  </div>

  <v-container fluid class="roles-configuration-container">
    <v-window v-model="currentTabItem" v-if="isSuperAdmin">
      <v-window-item :value="currentTabItem">
        <div v-if="listLoading" class="mt-12">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <v-row class="d-flex">
            <v-col
              xlg="3"
              lg="4"
              md="6"
              sm="6"
              v-for="i in 3"
              :key="i"
              class="mt-4"
            >
              <v-skeleton-loader
                ref="skeleton2"
                type="article"
                class="mx-auto"
              ></v-skeleton-loader>
            </v-col>
          </v-row>
        </div>

        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          :button-text="showRetryBtn ? 'Retry' : ''"
          @button-click="fetchList()"
        >
        </AppFetchErrorScreen>

        <AppFetchErrorScreen
          v-else-if="organizationList.length === 0"
          key="no-results-screen"
          :isSmallImage="true"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                <v-col v-if="organizationList.length === 0" cols="12">
                  <NotesCard
                    notes="There are no workflows in the list, so let's create a new one."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                </v-col>
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                  v-if="isSuperAdmin"
                >
                  <v-btn
                    v-if="organizationList.length === 0"
                    variant="elevated"
                    rounded="lg"
                    class="ml-2 mt-1 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="fetchList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>

        <div v-else class="mt-12">
          <div v-if="organizationList.length > 0 && isSuperAdmin">
            <v-row class="mb-12">
              <D3Chart :orgList="organizationList" />
            </v-row>
          </div>
          <AppFetchErrorScreen
            v-else
            key="no-results-screen"
            main-title="There is no roles matched for the selected filters/searches."
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter()"
                    >
                      Reset Filter/Search
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
        </div>
      </v-window-item>
    </v-window>
    <AppAccessDenied v-else></AppAccessDenied>
  </v-container>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { RETRIEVE_ORGANIZATION_LIST } from "@/graphql/employee-self-service/lopRecoveryQueries";
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
import D3Chart from "./D3Chart.vue";
export default {
  name: "OrganizationChart",
  data() {
    return {
      originalOrganizationList: [],
      organizationList: [],
      isLoading: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      currentTabItem: "0",
      mainTabs: ["Organization Chart"],
    };
  },
  components: {
    D3Chart,
    NotesCard,
    EmployeeDefaultFilterMenu,
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("173");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
  },
  mounted() {
    this.getOrganizationList();
  },
  methods: {
    getOrganizationList() {
      this.isLoading = true;
      this.$apollo
        .query({
          query: RETRIEVE_ORGANIZATION_LIST,
          variables: {
            loggedInUserId: String(this.loginEmployeeId),
            orgCode: this.orgCode,
          },
          client: "apolloClientAU",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.getOrganizationChart &&
            res.data.getOrganizationChart.result &&
            res.data.getOrganizationChart.result.children
          ) {
            const orgData = res.data.getOrganizationChart.result.children;
            this.originalOrganizationList = orgData;
            let orgList = [];
            orgList.push({
              name: res.data.getOrganizationChart.result.orgDetail.orgName,
              id: "110",
              org: true,
              parentId: "",
            });
            orgData.forEach((item) => {
              orgList.push({
                name: item.name,
                gender: item.gender,
                title: item.title,
                id: item.id,
                parentId: item.relationship,
                className: item.className,
                isManager: item.isManager,
              });
            });
            this.organizationList = orgList;
          }
          this.isLoading = false;
        });
    },
  },
};
</script>
