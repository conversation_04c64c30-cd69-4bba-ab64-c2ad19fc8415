<template>
  <div>
    <!-- AppTopBar component is reused here which is already present -->
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="specialWagesBackup.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <!-- this the component which has search component and based on prop isFilter filter component is also rendered -->

              <EmployeeDefaultFilterMenu
                v-if="specialWagesBackup.length > 0"
                class="justify-end"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <FormFilter
                ref="formFilterRef"
                :items="specialWagesBackup"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              >
                <template #bottom-filter-menu>
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="selectedStatus"
                      color="primary"
                      :items="['Active', 'Inactive']"
                      label="Status"
                      multiple
                      closable-chips
                      chips
                      density="compact"
                      single-line
                      variant="solo"
                    >
                    </v-autocomplete>
                  </v-col>
                </template>
              </FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <!-- Tab body starts from here -->

    <v-container fluid class="special-wages-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-row
          v-if="showCoverageButton"
          :class="isMobileView ? 'mt-6 mb-2' : 'ml-3 mb-1'"
        >
          <v-col
            cols="12"
            class="d-flex"
            :class="isMobileView ? 'flex-column mb-2' : ''"
          >
            <div
              class="d-flex mt-8"
              :class="isMobileView ? 'flex-column mb-2 mx-auto' : ''"
            >
              <div
                class="text-subtitle-1 text-grey-darken-1"
                :class="isMobileView ? 'mx-auto mb-2' : 'mr-5 mt-1'"
              >
                Special Wage Coverage
              </div>
              <div>
                <v-btn-toggle
                  v-model="selectedCoverageType"
                  rounded="lg"
                  mandatory
                  density="comfortable"
                  :disabled="hasActiveStatus"
                  :class="{
                    'cursor-not-allow': hasActiveStatus,
                    'custom-box-shadow': !hasActiveStatus,
                  }"
                  @update:modelValue="onChangeCoverage(selectedCoverageType)"
                >
                  <v-btn
                    class="text-start text-wrap"
                    color="primary"
                    style="background-color: white; color: black"
                    >Organization</v-btn
                  >
                  <v-btn
                    class="text-start text-wrap"
                    color="primary"
                    style="background-color: white; color: black"
                    >Custom Group</v-btn
                  ></v-btn-toggle
                >
              </div>
            </div>
          </v-col>
        </v-row>
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading || coverageLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="specialWagesData.length == 0 && emptyFilterScreen"
            image-name="common/no-records"
            main-title="There are no special wage configuration for the selected filters/searches."
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center">
                <v-btn
                  color="primary"
                  variant="elevated"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click.stop="resetFilter()"
                >
                  Reset Filter/Search
                </v-btn>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="specialWagesBackup.length === 0 && !showAddEditForm"
            key="no-results-screen"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%" class="mx-auto">
                <v-row
                  v-if="!isLoading"
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                  :class="isMobileView ? 'mt-n16' : ''"
                >
                  <v-col cols="12">
                    <NotesCard
                      heading="Special Wages Configuration for Weekends and Holidays"
                      imageName=""
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Modern HRMS solutions prioritize flexibility and adaptability, ensuring employees are compensated fairly for their dedication. One such feature is the ability to award special wages to those working on weekends or official holidays. Administrators can effortlessly navigate to the wage configurations section, set up a wage multiplier for such days (e.g., 1.5x for 150% of regular pay), and designate the official holidays in the system's calendar. It's also possible to fine-tune these settings to be applicable only for specific departments or roles."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Once activated, the system will automatically account for these special wage settings when computing pay for work logged on these specific days. Accurate hour logging is paramount to ensure that the compensation is correctly calculated. If any pay-related issues arise, administrators should first verify the date and multiplier settings. This feature is not just a nod to compliance with labor regulations, but also an acknowledgment of an employee's extra effort. For any queries or support, always refer to the system's help resources or support team."
                      backgroundColor="transparent"
                      class="mb-2"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="formAccess.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddSpecialWages()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Configure Special Wage
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      class="ml-2 mt-1 primary"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="fetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <!-- The div below contains all action buttons -->

              <div
                v-if="!isSmallTable"
                class="d-flex flex-wrap align-center"
                :class="isMobileView ? 'flex-column mt-3 mb-n4' : 'my-3'"
                style="justify-content: space-between"
              >
                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <div
                    class="text-grey-darken-1"
                    :class="
                      isMobileView
                        ? 'mx-auto mt-2'
                        : 'text-subtitle-1  mr-5 mt-1'
                    "
                  >
                    Special Wage Coverage
                  </div>
                  <v-tooltip v-model="showToolTip" location="right">
                    <template v-slot:activator="{ props }">
                      <v-btn-toggle
                        v-model="selectedCoverageType"
                        rounded="lg"
                        mandatory
                        v-bind="!hasActiveStatus ? '' : props"
                        density="compact"
                        :disabled="hasActiveStatus"
                        :class="{
                          'cursor-not-allow': hasActiveStatus,
                          'custom-box-shadow': !hasActiveStatus,
                        }"
                        @update:modelValue="
                          onChangeCoverage(selectedCoverageType)
                        "
                      >
                        <v-btn
                          class="text-start text-wrap"
                          color="primary"
                          style="background-color: white; color: black"
                          :size="isMobileView ? 'small' : 'default'"
                          >Organization</v-btn
                        >
                        <v-btn
                          class="text-start text-wrap"
                          color="primary"
                          style="background-color: white; color: black"
                          :size="isMobileView ? 'small' : 'default'"
                          >Custom Group</v-btn
                        ></v-btn-toggle
                      >
                    </template>
                    <div
                      v-if="hasActiveStatus"
                      style="width: 150px !important; height: 150px !important"
                    >
                      There are currently active special wage configurations. To
                      modify the special wage coverage, you must first
                      inactivate these configurations.
                    </div>
                  </v-tooltip>
                </div>
                <div
                  class="d-flex align-center my-3"
                  :class="isMobileView ? 'justify-center' : 'justify-end'"
                >
                  <v-tooltip v-model="toolTipForConf" location="right">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        v-bind="!activeWorkDayTypeCount ? '' : props"
                        prepend-icon="fas fa-plus"
                        color="primary rounded-lg"
                        :size="isMobileView ? 'small' : 'default'"
                        :class="
                          activeWorkDayTypeCount ? 'cursor-not-allow' : ''
                        "
                        @click="
                          !activeWorkDayTypeCount ? onAddSpecialWages() : ''
                        "
                        v-if="formAccess.add"
                      >
                        <template v-slot:prepend>
                          <v-icon></v-icon>
                        </template>
                        Add Configuration
                      </v-btn>
                    </template>
                    <div
                      v-if="activeWorkDayTypeCount"
                      style="width: 120px !important; height: 100px !important"
                    >
                      All possible configurations for special wages are
                      available..
                    </div>
                  </v-tooltip>
                  <v-btn
                    rounded="lg"
                    class="ml-2 mt-1 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="fetchList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                  <v-menu v-model="openMoreMenu" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="plain"
                        class="mt-1 ml-n2 mr-n5"
                        :size="isMobileView ? 'small' : 'default'"
                        v-bind="props"
                      >
                        <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                        <v-icon v-else>fas fa-caret-up</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action.key"
                        @click="exportReportFile()"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="15" class="pr-2">{{
                                action.icon
                              }}</v-icon
                              >{{ action.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>
              <v-row>
                <v-col
                  v-if="specialWagesData.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    v-model="selectedData"
                    :headers="headers"
                    :items="specialWagesData"
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        specialWagesData,
                        true
                      )
                    "
                    class="elevation-1"
                    style="box-shadow: none !important"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        style="z-index: 200"
                        class="data-table-tr bg-white cursor-pointer"
                        @click="openViewForm(item)"
                        :class="[
                          isMobileView
                            ? ' v-data-table__mobile-table-row ma-0 mt-2'
                            : '',
                        ]"
                      >
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5 font-weight-small'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center"
                            :class="
                              isMobileView
                                ? 'text-subtitle-1 text-grey-darken-1'
                                : 'mt-2 font-weight-bold'
                            "
                          >
                            Salary Type
                          </div>
                          <section
                            style="height: 3em"
                            class="d-flex align-center"
                          >
                            <div class="d-flex align-center">
                              <div
                                v-if="
                                  isSmallTable &&
                                  !isMobileView &&
                                  selectedItem &&
                                  selectedItem.Configuration_Id ===
                                    item.Configuration_Id
                                "
                                class="data-table-side-border d-flex"
                                style="height: 3em"
                              ></div>
                            </div>
                            <span
                              class="text-primary text-subtitle-1 font-weight-regular"
                            >
                              {{ item.Salary_Type }}
                            </span>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5 font-weight-medium'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center"
                            :class="
                              isMobileView
                                ? 'text-subtitle-1 text-grey-darken-1'
                                : 'mt-2 font-weight-bold'
                            "
                          >
                            Special Work Days
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{ item.Special_Work_Days }}
                            </span>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center"
                            :class="
                              isMobileView
                                ? 'text-subtitle-1 text-grey-darken-1'
                                : 'mt-2 font-weight-bold'
                            "
                          >
                            Attendance Required
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{
                                item.Salary_Type == "Monthly"
                                  ? "-"
                                  : item.Attendance_Required
                              }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center"
                            :class="
                              isMobileView
                                ? 'text-subtitle-1 text-grey-darken-1'
                                : 'mt-2 font-weight-bold'
                            "
                          >
                            Wage Index
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{
                                item.Wage_Factor || item.Wage_Factor == 0
                                  ? item.Wage_Factor
                                  : "-"
                              }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Custom Group
                          </div>
                          <section>
                            <span
                              class="text-subtitle-1 font-weight-regular"
                              :class="
                                item.Group_Name == null && !isMobileView
                                  ? 'ml-10'
                                  : ''
                              "
                            >
                              {{ item.Group_Name ? item.Group_Name : "-" }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center"
                            :class="
                              isMobileView
                                ? 'text-subtitle-1 text-grey-darken-1'
                                : 'mt-2 font-weight-bold'
                            "
                          >
                            Status
                          </div>
                          <section
                            class="d-flex align-center justify-space-between"
                          >
                            <div
                              class="d-flex align-center justify-space-around"
                            >
                              <span
                                id="w-80"
                                v-if="item.Status === 'Active'"
                                class="text-green text-subtitle-1 font-weight-regular d-flex justify-center align-center"
                                >{{ item.Status }}</span
                              >
                              <span
                                id="w-80"
                                v-else
                                class="text-red text-subtitle-1 font-weight-regular d-flex justify-center align-center text-center"
                                >{{ item.Status }}</span
                              >
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewSpecialWages
                    :selectedItem="selectedItem"
                    :coverage="coverage"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>

                <v-col
                  :cols="specialWagesData.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264 && !listLoading"
                >
                  <AddEditSpecialWages
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :coverage="coverage"
                    :isListEmpty="isListEmpty"
                    :specialWagesData="specialWagesData"
                    @close-form="closeAllForms()"
                    @edit-updated="refetchList()"
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>

      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditSpecialWages
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :coverage="coverage"
        :isListEmpty="isListEmpty"
        :specialWagesData="specialWagesData"
        @close-form="closeAllForms()"
        @edit-updated="refetchList()"
      />
      <ViewSpecialWages
        v-if="showViewForm"
        :selectedItem="selectedItem"
        :coverage="coverage"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);

const ViewSpecialWages = defineAsyncComponent(() =>
  import("./ViewSpecialWages.vue")
);
const AddEditSpecialWages = defineAsyncComponent(() =>
  import("./AddEditSpecialWages.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
// Queries
import { RETRIEVE_SPECIAL_WAGES } from "@/graphql/settings/core-hr/specialWagesQueries.js";
import FormFilter from "./FormFilter.vue";
import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "SpecialWages",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ViewSpecialWages,
    AddEditSpecialWages,
    FormFilter,
  },
  mixins: [FileExportMixin],
  data: () => ({
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    showToolTip: false,
    toolTipForConf: false,
    isLoading: false,
    isEdit: false,
    showViewForm: false,
    specialWagesView: false,
    showAddEditForm: false,
    tab: null,
    showSelect: false,

    selectedData: [],
    currentTabItem: "tab-3",
    showRetryBtn: true,
    specialWagesData: [],
    selectedItem: null,
    selectedCoverageType: 0,
    coverage: "Organization",
    validationMessages: [],
    showValidationAlert: false,
    Coverage_Id: null,
    specialWagesCoverageItem: [],
    coverageLoading: false,
    specialWagesBackup: [],
    selectedStatus: ["Active"],
    backupFilterData: [],
    openMoreMenu: false,
    emptyFilterScreen: false,
  }),
  computed: {
    landedFormName() {
      return "Special Wages";
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
    formatDate() {
      return (date, withTime = true) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    showCoverageButton() {
      return (
        (this.specialWagesBackup.length === 0 &&
          this.showAddEditForm &&
          !this.isMobileView &&
          !this.listLoading) ||
        (this.isListEmpty && this.isMobileView && !this.listLoading)
      );
    },
    hasActiveStatus() {
      // Check if any entry in specialWagesData has an active Status
      return this.specialWagesBackup.some((entry) => entry.Status === "Active");
    },
    isListEmpty() {
      return this.specialWagesBackup.length == 0;
    },
    formAccess() {
      let accessFormName = this.landedFormName.replace(/\s/g, "-");
      accessFormName = accessFormName.toLowerCase();
      let specialWagesAccess = this.accessRights(accessFormName);
      if (
        specialWagesAccess &&
        specialWagesAccess.accessRights &&
        specialWagesAccess.accessRights["view"]
      ) {
        return specialWagesAccess.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    coreHRFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.coreHRFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    headers() {
      if (this.isSmallTable) {
        return [
          {
            title: "Salary Type",
            align: "start",
            key: "Salary_Type",
          },

          {
            title: "Special Work Days",
            key: "Special_Work_Days",
          },
          {
            title: "Attendance Required",
            key: "Attendance_Required",
          },
        ];
      } else {
        return [
          {
            title: "Salary Type",
            align: "start",
            key: "Salary_Type",
          },

          {
            title: "Special Work Days",
            key: "Special_Work_Days",
          },
          {
            title: "Attendance Required",
            key: "Attendance_Required",
          },
          {
            title: "Wage Index",
            key: "Wage_Factor",
          },
          {
            title: "Custom Group",
            key: "Group_Name",
          },
          {
            title: "Status",
            key: "Status",
          },
        ];
      }
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    activeWorkDayTypeCount() {
      let count = 0;
      if (this.specialWagesBackup.length) {
        this.specialWagesBackup.forEach((item) => {
          if (item.Custom_Group_Id === null && item.Status === "Active") {
            count += 1;
          }
        });
      }
      return count == 8;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.retrieveSpecialWagesCoverage();
    this.fetchList();
  },
  methods: {
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onAddSpecialWages() {
      this.isEdit = false;
      this.showAddEditForm = true;
      this.showViewForm = false;
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.coreHRFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/core-hr/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + clickedForm.url;
        }
      }
      this.$store.state.empSearchValue = "";
    },
    resetFilter() {
      this.specialWagesData = this.specialWagesBackup;
      this.emptyFilterScreen = false;
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
      this.selectedStatus = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.applyFilter(this.specialWagesData);
    },
    applyFilter(filteredArray) {
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Status);
        });
      }
      this.specialWagesData = filteredArray;
      this.backupFilterData = filteredArray;
      if (this.specialWagesData.length == 0) {
        this.emptyFilterScreen = true;
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onApplySearch(val) {
      if (!val) {
        this.specialWagesData = this.backupFilterData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupFilterData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.specialWagesData = searchItems;
        if (this.specialWagesData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    exportReportFile() {
      let specialWagesList = this.specialWagesData.map((item) => ({
        Salary_Type: item.Salary_Type,
        Special_Work_Days: item.Special_Work_Days,
        Attendance_Required: item.Attendance_Required,
        Wage_Factor: item.Wage_Factor,
        Group_Name: item.Group_Name,
        Status: item.Status,
        Added_By: item.Added_By,
        Added_On: this.formatDate(new Date(item.Added_On + ".000Z")),
        Updated_By: item.Updated_By,
        Updated_On: item.Updated_On
          ? this.formatDate(new Date(item.Updated_On + ".000Z"))
          : "",
      }));
      let fileName = "Special Wages";
      let exportHeaders = [
        { header: "Salary Type", key: "Salary_Type" },
        { header: "Special Work Days", key: "Special_Work_Days" },
        {
          header: "Attendance Required",
          key: "Attendance_Required",
        },
        {
          header: "Wage Index",
          key: "Wage_Factor",
        },
        { header: "Custom Group", key: "Group_Name" },
        { header: "Status", key: "Status" },
        { header: "Added By", key: "Added_By" },
        { header: "Added On", key: "Added_On" },
        { header: "Update By", key: "Updated_By" },
        { header: "Update On", key: "Updated_On" },
      ];
      let exportOptions = {
        fileExportData: specialWagesList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_SPECIAL_WAGES,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveSpecialWages) {
            vm.specialWagesData =
              response.data.retrieveSpecialWages.SpecialWagesConfiguration;
            vm.specialWagesBackup = vm.specialWagesData;
            if (vm.specialWagesData && vm.specialWagesData.length) {
              vm.applyFilter(vm.specialWagesData);
            }
            vm.listLoading = false;
          } else {
            vm.handleListError((err = ""), "special wages");
          }
        })
        .catch((err) => {
          vm.handleListError(err, "special wages");
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form:
            formName === "coverage"
              ? "special wage coverage"
              : "special wage configuration",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    async retrieveSpecialWagesCoverage() {
      let vm = this;
      vm.coverageLoading = true;
      if (this.formAccess) {
        await this.$store
          .dispatch("retrieveFormLevelCoverage", {
            formName: "Special Wages",
            Form_Id: 85,
          })
          .then((response) => {
            if (response) {
              vm.specialWagesCoverageItem = response;
              vm.coverage = vm.specialWagesCoverageItem[0].Coverage;
              vm.Coverage_Id = vm.specialWagesCoverageItem[0].Coverage_Id;
              if (vm.coverage == "Organization") {
                vm.selectedCoverageType = 0;
              } else {
                vm.selectedCoverageType = 1;
              }
            } else {
              vm.listLoading = false;
            }
            vm.coverageLoading = false;
          })
          .catch(() => {
            vm.coverageLoading = false;
            vm.listLoading = false;
          });
      }
    },
    async onChangeCoverage(value) {
      let vm = this;
      vm.listLoading = true;
      let changedCoverage;
      if (value == 1) {
        changedCoverage = "Custom Group";
      } else {
        changedCoverage = "Organization";
      }
      try {
        await vm.$store
          .dispatch("updateFormLevelCoverage", {
            Coverage: changedCoverage,
            Coverage_Id: vm.Coverage_Id,
            formName: "Special Wages",
          })
          .then(() => {
            vm.coverage = changedCoverage;
            vm.listLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Special wage coverage updated successfully.",
            };
            vm.showAlert(snackbarData);
          })
          .catch(() => {
            if (value == 1) {
              vm.selectedCoverageType = 0;
            } else {
              vm.selectedCoverageType = 1;
            }
            vm.listLoading = false;
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "special wage coverage",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },
  },
};
</script>

<style scoped>
.custom-box-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.special-wages-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .special-wages-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
