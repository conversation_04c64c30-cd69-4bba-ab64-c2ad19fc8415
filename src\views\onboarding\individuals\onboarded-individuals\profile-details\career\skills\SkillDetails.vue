<template>
  <v-card class="rounded-lg" elevation="0">
    <div v-if="openBottomSheet">
      <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
        >Skill Details</span
      >
      <v-form ref="addEditSkillsForm" class="pa-2">
        <v-row>
          <v-col cols="12" md="4" sm="6">
            <v-combobox
              v-model="primarySkills"
              label="Primary Skills"
              multiple
              chips
              closable-chips
              :rules="[
                primarySkills.length > 0
                  ? validateWithRulesAndReturnMessages(
                      pSkills,
                      'skills',
                      'Primary Skills'
                    )
                  : true,
              ]"
              variant="solo"
              @update:modelValue="onChangeFields"
            ></v-combobox>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <v-combobox
              v-model="secondarySkills"
              label="Secondary Skills"
              multiple
              chips
              closable-chips
              :rules="[
                secondarySkills.length > 0
                  ? validateWithRulesAndReturnMessages(
                      sSkills,
                      'skills',
                      'Secondary Skills'
                    )
                  : true,
              ]"
              variant="solo"
              @update:modelValue="onChangeFields"
            ></v-combobox>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <v-combobox
              v-model="knownSkills"
              label="Known Skills"
              multiple
              chips
              closable-chips
              :rules="[
                knownSkills.length > 0
                  ? validateWithRulesAndReturnMessages(
                      kSkills,
                      'skills',
                      'Known Skills'
                    )
                  : true,
              ]"
              variant="solo"
              @update:modelValue="onChangeFields"
            ></v-combobox>
          </v-col>
        </v-row>
      </v-form>
    </div>
    <div v-else>
      <div class="d-flex">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="orange"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold">
            Skill Details
          </span>
        </div>
        <span v-if="enableEdit" class="d-flex justify-end ml-auto">
          <v-btn color="primary" variant="text" @click="openEditForm">
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit</v-btn
          >
        </span>
      </div>

      <v-row class="pa-4 ma-2 card-blue-background">
        <v-col cols="12" lg="3" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Primary Skills</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(skillsFormData.Primary) }}
          </p>
        </v-col>
        <v-col cols="12" lg="3" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Secondary Skills</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(skillsFormData.Skills) }}
          </p>
        </v-col>
        <v-col cols="12" lg="3" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Known Skills</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(skillsFormData.Proficiency) }}
          </p>
        </v-col>
      </v-row>
    </div>
  </v-card>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import { checkNullValue } from "@/helper";
import validationRules from "@/mixins/validationRules.js";
import { ADD_UPDATE_SKILL_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "SkillDetails",

  props: {
    skillDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },

  mixins: [validationRules],

  emits: ["refetch-career-details", "edit-opened", "edit-closed"],

  data() {
    return {
      skillsFormData: {},
      editedSkillDetails: {},
      primarySkills: [],
      secondarySkills: [],
      knownSkills: [],
      openWarningModal: false,
      // edit
      isFormDirty: false,
      openBottomSheet: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    pSkills() {
      return this.primarySkills && this.primarySkills.length > 0
        ? this.primarySkills.join(", ")
        : "";
    },
    sSkills() {
      return this.secondarySkills && this.secondarySkills.length > 0
        ? this.secondarySkills.join(", ")
        : "";
    },
    kSkills() {
      return this.knownSkills && this.knownSkills.length > 0
        ? this.knownSkills.join(", ")
        : "";
    },
    enableEdit() {
      return this.formAccess && this.formAccess.update;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", val);
    },
    openBottomSheet(val) {
      let editFormOpened = this.$store.state.onboarding.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "onboarding/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.skillDetails && this.skillDetails.length > 0) {
      this.skillsFormData = JSON.parse(JSON.stringify(this.skillDetails[0]));
    }
  },

  methods: {
    checkNullValue,
    formatSkillValues() {
      this.primarySkills = this.editedSkillDetails.Primary
        ? this.editedSkillDetails.Primary.split(", ")
        : [];
      this.secondarySkills = this.editedSkillDetails.Skills
        ? this.editedSkillDetails.Skills.split(", ")
        : [];
      this.knownSkills = this.editedSkillDetails.Proficiency
        ? this.editedSkillDetails.Proficiency.split(", ")
        : [];
    },
    onChangeFields() {
      this.isFormDirty = true;
    },

    openEditForm() {
      this.editedSkillDetails = this.skillsFormData;
      this.formatSkillValues();
      this.openBottomSheet = true;
      this.$emit("edit-opened");
      mixpanel.track("Onboarded-candidate-career-skills-edit-opened");
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.isFormDirty = false;
        this.primarySkills = [];
        this.secondarySkills = [];
        this.knownSkills = [];
        this.openBottomSheet = false;
        this.editedSkillDetails = {};

        mixpanel.track("Onboarded-candidate-career-skills-edit-closed");
        this.$emit("edit-closed");
      }
    },

    async validateEditForm() {
      const { valid } = await this.$refs.addEditSkillsForm.validate();

      mixpanel.track("Onboarded-candidate-career-skills-submit-click");
      if (valid) {
        this.updateSkillsDetails();
      }
    },

    updateSkillsDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_SKILL_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            primarySkill: vm.pSkills,
            secondarySkill: vm.sSkills,
            knownSkills: vm.kSkills,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-career-skills-edit-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Skills updated successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.isFormDirty = false;
          vm.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
          vm.closeEditForm();
          vm.$emit("refetch-career-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-career-skills-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "skills",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.chips-container {
  position: relative;
}
</style>
