<template>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppAccessDenied v-else-if="isAccessDenied"></AppAccessDenied>
</template>

<script>
export default {
  name: "TimeOffSetup",

  data() {
    return {
      isLoading: true,
      isAccessDenied: false,
    };
  },

  computed: {
    // returns baseurl of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
  },

  mounted() {
    let { isAnyOneFormHaveAccess, formAccess } =
      this.$store.getters.myTeamTimeOffFormAccess;
    if (isAnyOneFormHaveAccess) {
      for (let access in formAccess) {
        if (formAccess[access].havingAccess) {
          this.redirectToRelevantForm(formAccess[access]);
          break;
        }
      }
    } else {
      this.isAccessDenied = true;
      this.isLoading = false;
    }
  },

  methods: {
    redirectToRelevantForm(formData) {
      if (formData.isVue3) {
        this.$router.push("/my-team/" + formData.url);
      } else {
        window.location.href = this.baseUrl + "in/my-team/" + formData.url;
      }
    },
  },
};
</script>
