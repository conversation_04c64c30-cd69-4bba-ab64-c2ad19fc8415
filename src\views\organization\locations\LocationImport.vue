<template>
  <v-container class="location-container" fluid>
    <div>
      <v-row justify="center">
        <v-col cols="12" lg="11" md="12" sm="12">
          <v-card min-height="560" class="rounded-lg">
            <v-card-text>
              <div class="text-center mb-6">
                <span v-for="i in 3" :key="i">
                  <v-icon color="primary" size="18" class="ml-1">{{
                    currentStep >= i ? "fas fa-circle" : "far fa-circle"
                  }}</v-icon>
                </span>
              </div>
              <BulkImportStep1
                class="mb-10"
                v-show="currentStep === 1"
                ref="bulkStep1"
                :step1-text="step1Text"
                @file-upload-success="uploadFile($event)"
                @file-upload-error="fileRemoveOrError()"
                @generate-excel="onGenerateExcel()"
                :showDownload="true"
              />
              <BulkImportStep2
                class="mb-10 pb-5"
                v-if="fileContent.length > 0 && currentStep === 2"
                ref="bulkStep2"
                :file-params="fileContent"
                :headers-selected="selectedHeaders"
                @column-mapped="
                  matchedCount = $event[0];
                  mappedFileHeader = $event[1];
                "
              />
              <BulkImportStep3
                class="mb-10"
                ref="bulkImportStep3"
                v-if="checkMatchedFields && currentStep === 3"
                :fields="generateFields"
                :json-data="excelEditorData"
                type-of-import="locations"
                :extend-validation="excelValidation"
              />
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-bottom-navigation v-model="openBottomSheet">
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
        >
          <v-row class="pa-3" justify="center">
            <v-col
              v-if="!isLoadUploadButton"
              cols="6"
              class="pa-0 d-flex justify-start align-center pl-2"
            >
              <v-btn
                v-if="currentStep > 1"
                id="back_to_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                color="primary"
                @click="backToStep()"
              >
                <span><i class="fa fa-chevron-left pr-2"></i> Back</span>
              </v-btn>
              <v-btn
                id="cancel_step"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                class="ml-2"
                color="primary"
                @click="closeForm()"
                >Cancel</v-btn
              >
            </v-col>
            <v-col
              :cols="isLoadUploadButton ? '12' : '6'"
              class="pa-0 d-flex justify-center align-center pr-4"
              :style="windowWidth >= 1264 ? 'margin-left: -106px' : ''"
            >
              <div v-if="windowWidth > 768" class="text-end mr-2">
                <div class="mr-1 text-grey text-caption" style="width: 400px">
                  {{ nextBtnHelpContent }}
                </div>
              </div>
              <v-btn
                id="next_step"
                rounded="lg"
                color="primary"
                class="mr-10"
                :disabled="!enableNextButton"
                :loading="isLoadUploadButton"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                @click="nextStep()"
              >
                <span>
                  {{ currentStep === 3 ? "Submit" : "Next" }}
                  <v-icon v-if="currentStep !== 3" class="pl-1" size="15"
                    >fa fa-chevron-right</v-icon
                  >
                </span>
              </v-btn>
            </v-col>
            <v-col
              cols="12"
              v-if="windowWidth <= 768 && nextBtnHelpContent"
              class="pa-1 pr-4 d-flex align-center justify-end"
            >
              <div class="mr-1 text-grey mb-0" style="font-size: 10px">
                {{ nextBtnHelpContent }}
              </div>
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
    </div>
    <v-dialog v-model="importConfirmation" width="50%">
      <v-card>
        <v-row>
          <v-col v-if="invalidData && invalidData.length" cols="12">
            <v-alert prominent type="warning">
              <v-row align="center">
                <v-col v-if="invalidData" class="grow"
                  ><span>{{ invalidLocations.length }}</span>
                  out of
                  {{ excelEditorData.length }} do not have valid records. This
                  may result in omission of those records.
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertLocationData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
          <v-col v-else cols="12" class="pa-3">
            <v-alert prominent type="success">
              <v-row align="center">
                <v-col class="grow">
                  Everything looks <strong>good</strong>.
                  <div class="pt-1">
                    Are you
                    <strong>sure</strong> you want to import the Locations
                    details?
                  </div>
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertLocationData(finalUpdateData)"
                    >Add</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-overlay
          class="align-center justify-center"
          contained
          :model-value="isLoading"
          scrim="#fff"
        >
          <v-progress-circular color="primary" indeterminate size="64">
          </v-progress-circular>
        </v-overlay>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import BulkImportStep1 from "@/views/common/bulkImport/BulkImportStep1.vue";
import BulkImportStep2 from "@/views/common/bulkImport/BulkImportStep2.vue";
import BulkImportStep3 from "@/views/common/bulkImport/BulkImportStep3.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
// import { IMPORT_LOCATIONS } from "@/graphql/organisation/location/OrganisationQueries.js"; // update with correct import
import { ADD_EDIT_LIST_LOCATION_DETAILS } from "@/graphql/organisation/location/OrganisationQueries.js";
import {
  // LIST_LOCATION_DETAILS,
  GET_TIME_ZONE_LIST,
} from "@/graphql/organisation/location/OrganisationQueries.js";
import { LIST_CITIES_NO_AUTH } from "@/graphql/dropDownQueries.js";

export default {
  name: "LocationImport",
  components: {
    BulkImportStep1,
    BulkImportStep2,
    BulkImportStep3,
  },
  mixins: [FileExportMixin],
  props: {
    backupMainList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data: () => ({
    currentStep: 1,
    fileContent: [],
    errorsCountInExcel: 0,
    matchedCount: 0,
    openBottomSheet: true,
    isLoadUploadButton: false,
    mappedFileHeader: [],
    step1Text: {
      typeofData: "location sheet",
      text: "You have the option of using our predefined template or bring in your own location sheet with the headers for import",
      heading: "Download the excel template with predefined headers",
    },
    selectedImportType: 1,
    fields: [],
    optionValues: {
      countryName: [],
      stateName: [],
      cityName: [],
      zoneId: [],
    },
    excelEditorData: [],
    importConfirmation: false,
    finalExcelData: [],
    finalUpdateData: [],
    isLoading: false,
    step2HeadersData: [],
  }),

  computed: {
    locationList() {
      // Use Set to filter out duplicate locationName values
      const uniqueNamesSet = new Set(
        this.backupMainList.map((item) => item.locationName)
      );
      // Convert Set back to an array
      return Array.from(uniqueNamesSet);
    },
    excelValidation() {
      return {
        location: this.locationList,
        Location_Type: [
          "FieldOffice",
          "MainBranch",
          "SalesOffice",
          "SubBranch",
        ],
        Country_Name: this.optionValues.countryName || [],
        State_Name: this.optionValues.stateName || [],
        City_Name: this.optionValues.cityName || [],
        TimeZone_Id: this.optionValues.zoneId || [],
        Location_Status: ["Active", "InActive"],
      };
    },
    selectedHeaders() {
      let output = [
        {
          title: "Location Name",
          value: "Location_Name",
          props: { disabled: false },
        },
        {
          title: "Location Type",
          value: "Location_Type",
          props: { disabled: false },
        },
        {
          title: "Location Code",
          value: "Location_Code",
          props: { disabled: false },
        },
        { title: "Street1", value: "Street1", props: { disabled: false } },
        { title: "Street2", value: "Street2", props: { disabled: false } },
        { title: "Country", value: "Country_Name", props: { disabled: false } },
        { title: "State", value: "State_Name", props: { disabled: false } },
        { title: "City", value: "City_Name", props: { disabled: false } },
        { title: "PinCode", value: "pincode", props: { disabled: false } },
        {
          title: "TimeZone",
          value: "TimeZone_Id",
          props: { disabled: false },
        },
        {
          title: "Currency Symbol",
          value: "Currency_Symbol",
          props: { disabled: false },
        },
        {
          title: "Contact Number",
          value: "Phone_Number",
          props: { disabled: false },
        },
        {
          title: "Status",
          value: "Status",
          props: { disabled: false },
        },
        {
          title: "Description",
          value: "Description",
          props: { disabled: false },
        },
      ];
      return output;
    },

    invalidData() {
      return this.$refs.bulkImportStep3.invalidData;
    },
    invalidLocations() {
      let invalidData = this.$refs.bulkImportStep3.invalidData;
      let locationFail = Array.from(new Set(invalidData));
      return locationFail;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    enableNextButton() {
      if (this.currentStep === 1 && this.fileContent.length > 0) {
        return true;
      } else if (this.currentStep === 2 && this.checkMatchedFields) {
        return true;
      } else if (this.currentStep === 3) {
        this.formattedFileContent();
        return true;
      } else {
        return false;
      }
    },

    nextBtnHelpContent() {
      if (this.currentStep === 1) {
        if (this.fileContent.length === 0)
          return "Please import the data with supported file types (CSV, XLSX and XLS) to continue with the next step.";
        else return "";
      } else if (this.currentStep === 2) {
        return "The unmatched optional column(s) will not be processed in the next step.";
      } else if (this.currentStep === 3) {
        if (this.formattedFileContent.length === 0) {
          return "";
        } else if (this.errorsCountInExcel !== 0) {
          return "There seems to be some validation error(s) in your file. Please amend it before uploading.";
        } else {
          return "By clicking the 'Submit' button, you can import location data.";
        }
      } else {
        return "";
      }
    },
    mandatoryHeader() {
      let fields = [
        "Location Name",
        "Location Type",
        "Location Code",
        "Street1",
        "Street2",
        "Country",
        "State",
        "City",
        "PinCode",
        "TimeZone",
        "Currency Symbol",
        "Contact Number",
        "Status",
        "Description",
      ];
      return fields;
    },
    checkMatchedFields() {
      let mandatoryHeader = this.mandatoryHeader;
      if (this.matchedCount === this.mandatoryHeader.length) {
        let mandatoryMatchedCount = 0;
        for (var i in this.mappedFileHeader) {
          if (mandatoryHeader.includes(this.mappedFileHeader[i].hrapp_header))
            mandatoryMatchedCount++;
        }
        this.addHeaders();
        return mandatoryMatchedCount === this.mandatoryHeader.length
          ? true
          : false;
      } else return false;
    },
    excelFileData() {
      return this.fileContent.filter(
        (content) => content.filter(Boolean).length > 0
      );
    },
    generateFields() {
      let formOutput = [
        {
          field: "Location Name",
          label: "Location Name",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Location Type",
          label: "Location Type",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["FieldOffice", "MainBranch", "SalesOffice", "SubBranch"],
        },
        {
          field: "Location Code",
          label: "Location Code",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Street1",
          label: "Street1",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Street2",
          label: "Street2",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Country",
          label: "Country",
          type: "select",
          readonly: false,
          width: "200px",
          options: this.optionValues.countryName || [],
        },
        {
          field: "State",
          label: "State",
          type: "select",
          readonly: false,
          width: "200px",
          options: this.optionValues.stateName || [],
        },
        {
          field: "City",
          label: "City",
          type: "select",
          readonly: false,
          width: "200px",
          options: this.optionValues.cityName || [],
        },
        {
          field: "PinCode",
          label: "PinCode",
          type: "number",
          readonly: false,
          width: "200px",
        },
        {
          field: "TimeZone",
          label: "TimeZone",
          type: "select",
          readonly: false,
          width: "200px",
          options: this.optionValues.zoneId || [],
        },
        {
          field: "Currency Symbol",
          label: "Currency Symbol",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Contact Number",
          label: "Contact Number",
          type: "string",
          readonly: false,
          width: "200px",
        },
        {
          field: "Status",
          label: "Status",
          type: "select",
          readonly: false,
          width: "200px",
          options: ["Active", "InActive"],
        },
        {
          field: "Description",
          label: "Description",
          type: "string",
          readonly: false,
          width: "200px",
        },
      ];
      return formOutput;
    },
  },
  mounted() {
    // mixpanel.init(Config.mixPanelToken, {
    //   debug: true,
    //   track_pageview: true,
    //   persistence: "localStorage",
    // });
    // mixpanel.identify(this.mixPanelId);
    this.fetchList();
  },
  methods: {
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    async fetchList() {
      try {
        // Fetch location details
        const locationResponse = await this.$apollo.query({
          query: LIST_CITIES_NO_AUTH,
          client: "apolloClientAS",
          fetchPolicy: "no-cache",
          variables: {
            limit: 0,
            offset: 0,
          },
        });

        // Debug: Log location response
        console.log("Location Response:", locationResponse);

        const locationDetails =
          locationResponse.data.getCityListWithState.cityDetails;

        // Populate option values
        this.optionValues.countryName = Array.from(
          new Set(locationDetails.map((item) => item.Country_Name))
        );
        this.optionValues.stateName = Array.from(
          new Set(locationDetails.map((item) => item.State_Name))
        );
        this.optionValues.cityName = Array.from(
          new Set(locationDetails.map((item) => item.City_Name))
        );

        // Fetch time zone details
        console.log("Fetching Time Zone Details..."); // Debug log before query
        const timeZoneResponse = await this.$apollo.query({
          query: GET_TIME_ZONE_LIST,
          client: "apolloClientAS",
          fetchPolicy: "no-cache",
        });

        // Debug: Log time zone response
        console.log("Time Zone Response:", timeZoneResponse);

        if (
          timeZoneResponse &&
          timeZoneResponse.data &&
          timeZoneResponse.data.getTimezoneList
        ) {
          const timeZoneDetails =
            timeZoneResponse.data.getTimezoneList.timeZoneDetails;

          // Debug: Check the structure of timeZoneDetails
          console.log("Time Zone Details:", timeZoneDetails);

          this.optionValues.zoneId = Array.from(
            new Set(timeZoneDetails.map((item) => item.TimeZone_Id))
          );
        } else {
          console.error("No time zone data found:", timeZoneResponse);
        }
      } catch (error) {
        console.error("Error fetching time zone data:", error);
      }
    },

    onGenerateExcel() {
      const locationObject = {
        locationName: null,
        locationCode: null,
        locationType: null,
        Street1: null,
        Street2: null,
        country: null,
        state: null,
        city: null,
        currencySymbol: null,
        phoneNumber: null,
        description: null,
        pincode: null,
        timeZone: null,
        Status: null,
      };
      const exportData = Array.from({ length: 100 }, () => ({
        ...locationObject,
      }));

      let headers = [
        { key: "locationName", header: "Location Name" },
        { key: "locationType", header: "Location Type" },
        { key: "locationCode", header: "Location Code" },
        { key: "Street1", header: "Street1" },
        { key: "Street2", header: "Street2" },
        { key: "country", header: "Country" },
        { key: "state", header: "State" },
        { key: "city", header: "City" },
        { key: "pincode", header: "PinCode" },
        { key: "timeZone", header: "TimeZone" },
        { key: "currencySymbol", header: "Currency Symbol" },
        { key: "phoneNumber", header: "Contact Number" },
        { key: "Status", header: "Status" },
        { key: "description", header: "Description" },
      ];

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Location template.xlsx",
        sheetName: "Location sheet",
        header: headers,
        requiredHeaders: [
          "Location Name",
          "Location Type",
          "Location Code",
          "Country",
          "State",
          "City",
          "PinCode",
          "TimeZone",
          "Status",
        ],
        columnHighlightProps: {
          type: "Location Import",
          Location_Type: [
            "FieldOffice",
            "MainBranch",
            "SalesOffice",
            "SubBranch",
          ],
          Country_Name: this.optionValues.countryName || [],
          State_Name: this.optionValues.stateName || [],
          City_Name: this.optionValues.cityName || [],
          TimeZone_Id: this.optionValues.zoneId || [],
          Status: ["Active", "InActive"],
        },
      };
      exportOptions.fileExportData = exportOptions.fileExportData.map(
        (item, index) => ({
          ...item,
          timeZone:
            this.timeZoneData && this.timeZoneData.length > 0
              ? this.timeZoneData[index % this.timeZoneData.length]
                  ?.timeZoneName || ""
              : "",
        })
      );

      this.exportExcelFile(exportOptions);
    },
    formattedFileContent() {
      let generatedData = this.formExcelData();
      this.excelEditorData = generatedData;
    },
    formExcelData() {
      let fields = this.generateFields;
      let data = JSON.parse(JSON.stringify(this.excelFileData));
      let headersAssigned = this.step2HeadersData;
      //Getting the field of the array of objects
      let excelData = [];
      let idCounter = 1;
      // Iterate through each row of data
      for (let i = 1; i < data.length; i++) {
        let rowData = data[i];
        let rowObj = { $id: "000000" + idCounter++ };

        // Iterate through each field definition and populate the row object
        for (let j = 0; j < fields.length; j++) {
          let fieldDef = fields[j];
          let fieldName = fieldDef.field;
          // Find the index of the field in the header mappings array
          let headerIndex = headersAssigned.findIndex(
            (header) => header.hrapp_header === fieldName
          );

          // If the field is present in the header mappings array, use the corresponding value from the input data
          if (headerIndex >= 0) {
            let dataValue = rowData[headerIndex];
            if (dataValue !== null && dataValue !== undefined) {
              rowObj[fieldName] = dataValue;
            } else {
              rowObj[fieldName] = null;
            }
          } else {
            // If the field is not present in the header mappings array, use the default value for the field type
            switch (fieldDef.type) {
              case "string":
                rowObj[fieldName] = "";
                break;
              case "number":
                rowObj[fieldName] = 0;
                break;
              case "boolean":
                rowObj[fieldName] = false;
                break;
              default:
                rowObj[fieldName] = null;
                break;
            }
          }
        }
        excelData.push(rowObj);
      }
      return excelData;
    },
    uploadFile(content) {
      this.fileContent = content;
    },
    fileRemoveOrError() {
      this.fileContent = [];
    },
    insertLocationData(data) {
      if (data.length) {
        let vm = this;
        vm.isLoading = true;
        vm.$apollo
          .mutate({
            mutation: ADD_EDIT_LIST_LOCATION_DETAILS,
            client: "apolloClientJ",
            variables: {
              locationData: data,
            },
          })
          .then(async (response) => {
            if (response && response.data && response.data.importLocations) {
              let { validationError } = response.data.importLocations;
              validationError = JSON.parse(validationError);
              let excelInvalidData = this.$refs.bulkImportStep3.invalidData;
              let remainingData = [];
              let inputData = this.$refs.bulkImportStep3.editorData;

              for (let i = 0; i < inputData.length; i++) {
                if (excelInvalidData.includes(inputData[i].$id)) {
                  // Check if data is not already there in remaining data
                  if (!remainingData.includes(inputData[i])) {
                    remainingData.push(inputData[i]);
                  }
                }
              }

              let backendErrorsWithMessages = [];
              // Validation Backend Error Exists
              for (let i = 0; i < validationError.length; i++) {
                for (
                  let j = 0;
                  j < validationError[i].failedArrays.length;
                  j++
                ) {
                  for (let k = 0; k < inputData.length; k++) {
                    if (!remainingData.includes(inputData[k])) {
                      remainingData.push(inputData[k]);
                    }
                    let error = JSON.parse(JSON.stringify(inputData[k]));
                    error.Message = validationError[i].Message;
                    backendErrorsWithMessages.push(error);
                  }
                }
              }

              this.excelEditorData = remainingData;
              this.$refs.bulkImportStep3.editorData = remainingData;
              // Set Field Error
              for (let i = 0; i < backendErrorsWithMessages.length; i++) {
                let message = backendErrorsWithMessages[i].Message;
                let data = backendErrorsWithMessages[i];
                let field = {};
                if (field && message && message !== undefined) {
                  field.name = message.includes("Location Type")
                    ? "Location Type"
                    : message.includes("Country")
                    ? "Country"
                    : message.includes("State")
                    ? "State"
                    : message.includes("City")
                    ? "City"
                    : message.includes("TimeZone")
                    ? "TimeZone"
                    : message.includes("Status")
                    ? "Status"
                    : "Location";
                }
                this.$refs.bulkImportStep3.setFieldError(message, data, field);
              }

              vm.importConfirmation = false;
              vm.isLoading = false;
              if (
                !excelInvalidData.length &&
                !backendErrorsWithMessages.length
              ) {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Locations imported successfully.",
                };
                vm.showAlert(snackbarData);
                vm.closeForm();
                this.$emit("refetch-data");
              }
            } else {
              vm.handleImportError();
              vm.importConfirmation = false;
              vm.closeForm();
            }
          })
          .catch((err) => {
            vm.handleImportError(err);
            vm.importConfirmation = false;
            vm.closeForm();
          });
      } else {
        this.importConfirmation = false;
        this.closeForm();
      }
    },
    handleImportError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "importing",
        form: "location",
        isListError: false,
      });
    },
    addHeaders() {
      if (this.$refs.bulkStep2 && this.$refs.bulkStep2.tableItems) {
        this.step2HeadersData = this.$refs.bulkStep2.tableItems;
      }
    },
    formBulkData(data) {
      const filteredData = data;
      this.finalExcelData = data;

      this.finalUpdateData = filteredData.map((item) => {
        const newItem = {
          locationName: item["Location_Name"],
          locationCode: item["Location_Code"],
          locationType: item["Location_Type"],
          street1: item["Street1"],
          street2: item["Street2"],
          cityName: item["City_Name"],
          stateName: item["State_Name"],
          countryName: item["Country_Name"],
          pinCode: item["Pincode"],
          zoneId: item["TimeZone_Id"],
          currencySymbol: item["Currency_Symbol"],
          phoneNumber: item["Phone_Number"],
          Status: item["Status"],
          description: item["Description"],
        };
        return newItem;
      });

      this.importConfirmation = true;
    },

    // async insertLocationData() {
    //   this.isLoading = true;
    //   let fields = this.generateFields;
    //   let finalData = [];
    //   this.finalUpdateData.forEach((data) => {
    //     let temp = {};
    //     fields.forEach((field) => {
    //       temp[field.field] = data[field.label];
    //     });
    //     finalData.push(temp);
    //   });
    //   await this.$apollo
    //     .mutate({
    //       mutation: IMPORT_LOCATIONS,
    //       variables: { locationBulkInsert: finalData },
    //     })
    //     .then((data) => {
    //       if (data.data.bulkLocationInsert.Status == "SUCCESS") {
    //         this.$toast.success(data.data.bulkLocationInsert.message, {
    //           duration: 8000,
    //           keepOnHover: true,
    //         });
    //         this.isLoading = false;
    //         this.importConfirmation = true;
    //       }
    //     })
    //     .catch((error) => {
    //       this.isLoading = false;
    //     });
    // },
    backToStep() {
      if (this.currentStep !== 1) {
        this.currentStep--;
      }
    },
    // nextStep() {
    //   if (this.enableNextButton) {
    //     if (this.currentStep === 1 && this.errorsCountInExcel === 0) {
    //       this.currentStep++;
    //     } else if (this.currentStep === 2 && this.errorsCountInExcel === 0) {
    //       this.currentStep++;
    //     }
    //   }
    // },
    nextStep() {
      if (this.currentStep === 3) {
        this.formBulkData(this.$refs.bulkImportStep3.filteredData);
      } else {
        this.currentStep += 1;
      }
    },
    closeForm() {
      this.$emit("close-import-model");
    },
  },
  // watch: {
  //   currentStep() {
  //     this.onGenerateExcel();
  //   },
  // },
};
</script>
<style>
.location-container {
  padding: 5em 2em 0em 3em;
}
.v-bottom-navigation__content {
  justify-content: space-around;
  flex-direction: column;
}
.dp__button_bottom {
  display: none;
}
</style>
