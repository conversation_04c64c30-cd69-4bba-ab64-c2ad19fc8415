<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row justify="center">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isFilter="false"
            />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container v-if="formAccess?.view" fluid class="container">
      <div>
        <div v-if="listLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          key="error-screen"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          button-text="Retry"
          @button-click="refetchList()"
        ></AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="originalList.length === 0"
          key="no-data-screen"
          :main-title="emptyScenarioMsg"
          :isSmallImage="!isFilter"
          :image-name="!isFilter ? '' : 'common/no-records'"
        >
          <template v-if="!isFilter" #contentSlot>
            <div style="max-width: 80%">
              <v-row
                class="rounded-lg pa-5 mb-4"
                :style="!isFilter ? 'background: white' : ''"
              >
                <v-col cols="12">
                  <NotesCard
                    notes="The Air Ticket Settlement Summary outlines all transactions related to employee air ticket benefits, capturing details such as benefit settlement and its status. This summary includes key information like employee name, employee ID, ticket category, destination, travel route, claim amount, date of settlement or encashment, and the settlement status. It helps in tracking the benefits, ensuring compliance with company policies and labor regulations."
                    backgroundColor="transparent"
                    class="mb-4"
                  />
                  <NotesCard
                    notes="For accurate financial reporting, the summary also reflects adjustments made during employee exits, contract terminations, or cases of policy exceptions (e.g., emergency travel). Regular updates to the settlement summary ensure transparency, proper budget allocation, and ease of auditing for both HR and finance teams."
                    backgroundColor="transparent"
                    class="mb-4"
                  />
                </v-col>
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <datepicker
                    :format="'MMMM, yyyy'"
                    v-model="selectedMonthYear"
                    maximum-view="year"
                    minimum-view="month"
                    style="width: 220px"
                    :disabled-dates="getDisabledDates"
                    @input="fetchList()"
                  />
                  <v-btn
                    color="transparent"
                    variant="flat"
                    class="ml-2 mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="itemList.length == 0"
          key="no-results-screen"
          main-title="There are no employees matched for the selected filters/searches."
          image-name="common/no-records"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4">
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    variant="elevated"
                    color="primary"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    @click="resetFilter('grid')"
                  >
                    <span class="primary">Reset Filter/Search </span>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div v-else>
          <v-row>
            <v-col
              md="12"
              cols="12"
              xs="12"
              sm="12"
              class="d-flex mr-2 align-center"
              :class="{
                'flex-column': isMobileView,
                'justify-center': windowWidth < 1264,
                'justify-space-between': windowWidth >= 1264,
              }"
              style="flex-wrap: wrap"
              ><datepicker
                :format="'MMMM, yyyy'"
                v-model="selectedMonthYear"
                maximum-view="year"
                minimum-view="month"
                style="width: 220px"
                :disabled-dates="getDisabledDates"
                @input="fetchList()"
              />
              <div
                class="d-flex mr-2 align-center"
                :class="{
                  'flex-column': isMobileView,
                  'justify-center': windowWidth < 1264,
                  'justify-end': windowWidth >= 1264,
                }"
              >
                <v-btn
                  color="transparent"
                  variant="flat"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu class="mb-1" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                      <v-icon>fas fa-ellipsis-v</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <v-data-table
                :headers="headers"
                :items="itemList"
                :items-per-page="50"
                :group-by="Employee_Id"
                fixed-header
                :height="
                  itemList.length > 11 ? $store.getters.getTableHeight(270) : ''
                "
                :sort-by="[{ key: 'Employee_Id', order: 'asc' }]"
                class="elevation-1"
                style="box-shadow: none !important"
              >
                <template v-slot:item="{ item }">
                  <tr
                    style="z-index: 200"
                    @click="onOpenViewForm(item)"
                    class="data-table-tr bg-white cursor-pointer"
                    :class="[
                      isMobileView
                        ? ' v-data-table__mobile-table-row ma-0 mt-2'
                        : '',
                    ]"
                  >
                    <td
                      style="max-width: 150px"
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Employee Name
                      </div>
                      <section class="d-flex align-center">
                        <div
                          v-if="
                            showViewForm &&
                            !isMobileView &&
                            selectedItem &&
                            selectedItem.Employee_Id === item.Employee_Id
                          "
                          class="data-table-side-border d-flex"
                        ></div>
                        <v-tooltip
                          :text="item.Employee_Name"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 250px; '
                                  : 'max-width: 200px; '
                              "
                              v-bind="props"
                            >
                              {{ checkNullValue(item.Employee_Name) }}
                              <div
                                v-if="item?.User_Defined_EmpId"
                                class="text-grey"
                              >
                                {{ checkNullValue(item.User_Defined_EmpId) }}
                              </div>
                            </div>
                          </template>
                        </v-tooltip>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Destination City
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Destination_City) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Availed Date
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ formatDate(item.Availed_Date) }}
                        </span>
                      </section>
                    </td>
                    <!-- Large View - Accrual Basis for Infant -->
                    <td
                      v-if="!isMobileView"
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <v-tooltip
                        :text="String(item.Infant_Policy_Amount)"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            v-bind="
                              item.Infant_Policy_Amount?.length > 100
                                ? props
                                : ''
                            "
                            style="max-width: 100px"
                          >
                            {{ checkNullValue(item.Infant_Policy_Amount) }}
                          </section>
                        </template>
                      </v-tooltip>
                    </td>
                    <!-- Large View - Accrual Basis for Child -->
                    <td
                      v-if="!isMobileView"
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <v-tooltip
                        :text="String(item.Child_Policy_Amount)"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            v-bind="
                              item.Child_Policy_Amount?.length > 100
                                ? props
                                : ''
                            "
                            style="max-width: 100px"
                          >
                            {{ checkNullValue(item.Child_Policy_Amount) }}
                          </section>
                        </template>
                      </v-tooltip>
                    </td>
                    <!-- Large View - Accrual Basis for Adult -->
                    <td
                      v-if="!isMobileView"
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <v-tooltip
                        :text="String(item.Adult_Policy_Amount)"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            v-bind="
                              item.Adult_Policy_Amount?.length > 100
                                ? props
                                : ''
                            "
                            style="max-width: 100px"
                          >
                            {{ checkNullValue(item.Adult_Policy_Amount) }}
                          </section>
                        </template>
                      </v-tooltip>
                    </td>
                    <!-- Mobile View - Accrual Basis for Infant -->
                    <td
                      v-if="isMobileView"
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="font-weight-bold d-flex align-center"
                      >
                        Accrual Basis for Infant
                        <span v-if="payrollCurrency">
                          (in {{ payrollCurrency }})</span
                        >
                      </div>
                      <v-tooltip
                        :text="String(item.Infant_Policy_Amount)"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            v-bind="
                              item.Infant_Policy_Amount?.length > 20
                                ? props
                                : ''
                            "
                            style="max-width: 250px"
                          >
                            {{ checkNullValue(item.Infant_Policy_Amount) }}
                          </section>
                        </template>
                      </v-tooltip>
                    </td>
                    <!-- Mobile View - Accrual Basis for Child -->
                    <td
                      v-if="isMobileView"
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="font-weight-bold d-flex align-center"
                      >
                        Accrual Basis for Child
                        <span v-if="payrollCurrency">
                          (in {{ payrollCurrency }})</span
                        >
                      </div>
                      <v-tooltip
                        :text="String(item.Child_Policy_Amount)"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            v-bind="
                              item.Child_Policy_Amount?.length > 20 ? props : ''
                            "
                            style="max-width: 250px"
                          >
                            {{ checkNullValue(item.Child_Policy_Amount) }}
                          </section>
                        </template>
                      </v-tooltip>
                    </td>
                    <!-- Mobile View - Accrual Basis for Adult -->
                    <td
                      v-if="isMobileView"
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="font-weight-bold d-flex align-center"
                      >
                        Accrual Basis for Adult
                        <span v-if="payrollCurrency">
                          (in {{ payrollCurrency }})</span
                        >
                      </div>
                      <v-tooltip
                        :text="String(item.Adult_Policy_Amount)"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            v-bind="
                              item.Adult_Policy_Amount?.length > 20 ? props : ''
                            "
                            style="max-width: 250px"
                          >
                            {{ checkNullValue(item.Adult_Policy_Amount) }}
                          </section>
                        </template>
                      </v-tooltip>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Settlement Amount
                        <span v-if="payrollCurrency">{{
                          payrollCurrency
                        }}</span>
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item?.Settlement_Amount) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        No. of Tickets
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ parseInt(item.No_Of_Dependents) + 1 }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Status
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Settlement_Status) }}
                        </span>
                      </section>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </div>
      </div>
    </v-container>
    <AppAccessDenied v-else />
  </div>
  <AppLoading v-if="listLoading" />
  <ViewSettlementSummary
    v-if="showViewForm"
    :selectedItem="selectedItem"
    :landedFormName="landedFormName"
    @close-form="closeAllForms()"
  />

  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import moment from "moment";
import Datepicker from "vuejs3-datepicker";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
import FileExportMixin from "@/mixins/FileExportMixin";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewSettlementSummary = defineAsyncComponent(() =>
  import("./ViewSettlementSummary.vue")
);
import { RETRIEVE_AIR_TICKET_SETTLEMENT_SUMMARY } from "@/graphql/corehr/payrollDataManagement.js";
export default {
  name: "AirTicketSettlementSummary",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ViewSettlementSummary,
    Datepicker,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // tab
    currentTabItem: "",
    // table
    originalList: [],
    itemList: [],
    selectedMonthYear: new Date(),
    isFilter: false,
    selectedItem: null,
    showViewForm: false,
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    // export
    openMoreMenu: false,
  }),
  computed: {
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    landedFormName() {
      let formName = this.accessIdRights("321");
      if (formName?.customFormName && formName.customFormName !== "") {
        return formName.customFormName;
      } else return "Air Ticketing Settlement Summary";
    },
    coreHrPayrollDataManagementFormAcess() {
      return this.$store.getters.coreHrPayrollDataManagementFormAcess;
    },
    getDisabledDates() {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth();
      const endOfCurrentMonth = new Date(
        currentYear,
        currentMonth,
        new Date().getDate()
      );

      return {
        from: endOfCurrentMonth,
        preventDisableDateSelection: true,
      };
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.coreHrPayrollDataManagementFormAcess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    formAccess() {
      let formAccess = this.accessIdRights("321");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    headers() {
      let headers = [
        { title: "Employee Name", key: "Employee_Name", align: "start" },
        {
          title: "Destination City",
          key: "Destination_City",
        },
        {
          title: "Availed Date",
          key: "Availed_Date",
        },
      ];
      if (!this.isMobileView)
        headers.push({
          title: `Accrual Basis${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          align: "center",
          children: [
            { title: "Infant", value: "Infant_Policy_Amount", sortable: true },
            { title: "Child", value: "Child_Policy_Amount", sortable: true },
            { title: "Adult", value: "Adult_Policy_Amount", sortable: true },
          ],
        });
      else
        headers.push(
          {
            title: `Accrual Basis for Infant${
              this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
            }`,
            key: "Infant_Policy_Amount",
          },
          {
            title: `Accrual Basis for Child${
              this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
            }`,
            key: "Child_Policy_Amount",
          },
          {
            title: `Accrual Basis for Adult${
              this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
            }`,
            key: "Adult_Policy_Amount",
          }
        );
      headers.push(
        {
          title: `Settlement Amount${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Settlement_Amount",
        },
        { title: "No.of Tickets", key: "No_Of_Dependents" },
        { title: "Status", key: "Settlement_Status" }
      );

      return headers;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.isFilter) {
        msgText = "There are no employees for the selected filters/searches";
      }
      return msgText;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.itemList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    // reset filter
    resetFilter() {
      this.itemList = this.originalList;
      this.isFilter = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.listLoading = true;
        const { formAccess } = this.coreHrPayrollDataManagementFormAcess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },
    onOpenViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    closeAllForms() {
      this.selectedItem = null;
      this.showViewForm = false;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    // Retrieving the list of Invited Individuals
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      let month = moment(vm.selectedMonthYear).isValid()
        ? moment(vm.selectedMonthYear).format("M")
        : null;
      let year = moment(vm.selectedMonthYear).isValid()
        ? moment(vm.selectedMonthYear).format("YYYY")
        : null;
      vm.$apollo
        .query({
          query: RETRIEVE_AIR_TICKET_SETTLEMENT_SUMMARY,
          client: "apolloClientI",
          variables: {
            formId: 321,
            month: parseInt(month),
            year: parseInt(year),
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveAirTicketSettlementSummary &&
            response.data.retrieveAirTicketSettlementSummary
              .settlementSummary &&
            !response.data.retrieveAirTicketSettlementSummary.errorCode
          ) {
            const tempData =
              response.data.retrieveAirTicketSettlementSummary
                .settlementSummary;
            vm.itemList = tempData;
            vm.originalList = tempData;
            vm.onApplySearch();
            vm.listLoading = false;
          } else {
            vm.handleListError(
              response.data.retrieveAirTicketSettlementSummary?.errorCode || ""
            );
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.fetchList();
      this.resetFilter();
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        Infant_Policy_Amount:
          this.payrollCurrency + " " + el.Infant_Policy_Amount,
        Child_Policy_Amount:
          this.payrollCurrency + " " + el.Child_Policy_Amount,
        Adult_Policy_Amount:
          this.payrollCurrency + " " + el.Adult_Policy_Amount,
        Settlement_Amount: this.payrollCurrency + " " + el.Settlement_Amount,
        No_Of_Dependents: el.No_Of_Dependents + 1,
        Dependent_Relationship:
          JSON.parse(el?.Dependent_Relationship)?.join(", ") || "",
        Availed_Date: el.Availed_Date ? this.formatDate(el.Availed_Date) : "",
        Effective_Date: el.Effective_Date
          ? this.formatDate(el.Effective_Date)
          : "",
      }));

      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: [
          { key: "User_Defined_EmpId", header: "Employee ID" },
          { key: "Employee_Name", header: "Employee Name" },
          { key: "Destination_City", header: "Destination City" },
          { key: "Destination_Country", header: "Destination Country" },
          {
            key: "Infant_Policy_Amount",
            header: "Accrual Basis for Infant",
          },
          {
            key: "Child_Policy_Amount",
            header: "Accrual Basis for Child",
          },
          {
            key: "Adult_Policy_Amount",
            header: "Accrual Basis for Adult",
          },
          { key: "Settlement_Amount", header: "Settlement Amount" },
          { key: "No_Of_Dependents", header: "No. of Tickets" },
          { key: "Air_Ticketing_Category", header: "Air Ticket Category" },
          { key: "Availed_Date", header: "Availed Date" },
          { key: "Payroll_Month", header: "Payroll Month" },
          { key: "Effective_Date_Enable", header: "Effective Date Type" },
          { key: "Effective_Date", header: "Effective Date" },
          {
            key: "Eligibility_Of_Ticket_Claim_Months",
            header: "Eligibility Of Ticket Claim(in Months)",
          },
          { key: "Air_Ticket_To_Dependent", header: "Air Ticket To Dependent" },
          { key: "Dependent_Relationship", header: "Dependent Relationship" },
          { key: "Settlement_Status", header: "Status" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
