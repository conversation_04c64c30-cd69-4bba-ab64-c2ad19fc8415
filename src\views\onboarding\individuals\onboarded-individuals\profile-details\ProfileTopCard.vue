<template>
  <div v-if="showTopCard">
    <div v-if="listLoading">
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else-if="!isErrorInList">
      <ProfileCard>
        <div class="d-flex justify-end mb-n8 mt-2 mr-1">
          <v-icon @click="refetchAPIs()" size="17" color="grey"
            >fas fa-redo-alt</v-icon
          >
        </div>
        <v-row>
          <v-col class="d-flex" cols="12" md="9" lg="9">
            <v-row class="d-flex align-center">
              <v-col
                class="d-flex align-center"
                cols="3"
                xs="4"
                sm="4"
                md="3"
                lg="3"
              >
                <div class="d-flex align-center ml-3">
                  <v-icon @click="goBackToList()" color="primary" size="x-large"
                    >fas fa-angle-left fa-lg</v-icon
                  >
                </div>
                <v-skeleton-loader
                  v-if="isFetchingProfilePic"
                  ref="skeleton3"
                  type="avatar"
                ></v-skeleton-loader>
                <ProfileAvatar
                  v-else
                  :avatarSize="120"
                  :profile-image-path="profileImage"
                  :fileName="photoPath"
                  :empStatus="candidateStatus"
                  :employeeId="candidateDetails.candidateId"
                  :allowUpdate="enableEdit"
                  @profile-uploaded="updateProfilePath($event)"
                  @profile-uploading="isLoading = $event"
                ></ProfileAvatar>
              </v-col>
              <v-col cols="4" xs="8" sm="7" md="4" lg="4">
                <ProfileDetails
                  v-if="!isMobileView"
                  class="mx-5 d-flex flex-column justify-center"
                  :employeeName="candidateDetails.employeeName"
                  :empDepartment="candidateDetails.departmentName"
                  :empDesignation="candidateDetails.designationName"
                ></ProfileDetails>
              </v-col>
              <v-col cols="5" xs="12" sm="12" md="5" lg="5">
                <ContactDetails
                  class="mx-5 d-flex flex-column justify-center"
                  :employeeName="candidateDetails.employeeName"
                  :empEmailAddress="candidateDetails.empEmail"
                  :empMobileNo="candidateDetails.mobileNo"
                  :empMobileNoCode="candidateDetails.mobileNoCountryCode"
                  :address="candidateDetails.address"
                  :isEdit="false"
                ></ContactDetails>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="3" lg="3">
            <div class="d-flex flex-column align-md-end pt-8 pr-6">
              <v-btn
                color="secondary"
                rounded="lg"
                variant="outlined"
                class="mb-4"
                size="small"
                style="width: 200px"
                @click="onChangeCandidate()"
                ><span class="text-primary">Change Candidate</span></v-btn
              >
              <div class="d-flex justify-center">
                <CustomSelect
                  :items="candidateStatusList"
                  label="Candidate Status"
                  density="comfortable"
                  style="width: 200px"
                  :isAutoComplete="true"
                  :itemSelected="candidateStatus"
                  :disabledValue="disabledStatus"
                  :showTooltip="showTooltip"
                  :tooltipText="tooltipText"
                  @selected-item="onChangeRoundStatus($event)"
                ></CustomSelect>
              </div>
            </div>
          </v-col>
        </v-row>
      </ProfileCard>
    </div>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <EmployeesListModal
    v-if="showEmpListModal"
    :show-modal="showEmpListModal"
    :employeesList="candidateList"
    :showFilterSearch="true"
    modalTitle="Candidate(s)"
    selectStrategy="single"
    employeeIdKey="Candidate_Id"
    userDefinedEmpIdKey=""
    employeeNameLabel="Candidate Name"
    employeeNameKey="candidateName"
    deptNameKey="departmentName"
    designationKey="designationName"
    :isApplyFilter="true"
    departmentIdKey="departmentId"
    designationIdKey="designationId"
    locationIdKey="locationId"
    empTypeIdKey="empTypeId"
    workScheduleIdKey="workSchedule"
    @on-select-employee="onSelectCandidate($event)"
    @close-modal="showEmpListModal = false"
  ></EmployeesListModal>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    :confirmation-heading="modalText"
    @close-warning-modal="cancelConfirmation()"
    @accept-modal="validateEmpMail()"
  ></AppWarningModal>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <CustomEmail
    v-if="showCustomEmail"
    ref="customEmail"
    :formId="formId"
    :isOverlay="true"
    :typeOfTemplate="templateType"
    typeOfSchedule="noncalendar"
    :selectedCandidateId="selectedCandidateId"
    :template-email="
      selectedCandidateDetails.Personal_Email
        ? [selectedCandidateDetails.Personal_Email]
        : []
    "
    :template-data="templateData"
    @custom-email-sent="onSendCustomEmail()"
    @custom-email-cancel="onCloseCustomEmail()"
  />
</template>

<script>
import { defineAsyncComponent } from "vue";
import ProfileAvatar from "@/components/custom-components/ProfileAvatar.vue";
import ProfileDetails from "@/components/custom-components/ProfileDetails.vue";
import ContactDetails from "@/components/custom-components/ContactDetails.vue";
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal")
);
import {
  RETRIEVE_EMP_PROFILE_CARD_DETAILS,
  UPDATE_PROFILE_PICTURE_PATH,
  UPDATE_CANDIDATE_STATUS,
} from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import CustomEmail from "../../../../common/customEmail/CustomEmail.vue";
export default {
  name: "ProfileTopCard",
  components: {
    ProfileAvatar,
    ProfileDetails,
    ContactDetails,
    EmployeesListModal,
    CustomSelect,
    CustomEmail,
  },
  props: {
    selectedCandidateId: {
      type: [String, Number],
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    updateCount: {
      type: Number,
      default: 0,
    },
    candidateList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    selectedCandidateStatus: {
      type: String,
      default: "",
    },
    selectedCandidateDetails: {
      type: [Array, Object],
      required: true,
    },
  },
  emits: [
    "close-profile",
    "on-change-candidate",
    "form-submitted",
    "status-updated",
  ],
  data() {
    return {
      tab: null,
      profileImage: "",
      candidateDetails: {
        candidateId: 0,
        employeeName: "",
        userDefinedEmpId: "",
        departmentName: "",
        designationName: "",
        empEmail: "",
        mobileNo: "",
        mobileNoCountryCode: "",
        empStatus: "Unverified",
        address: "",
      },
      candidateStatus: "",
      openWarningModal: false,
      listLoading: false,
      isErrorInList: false,
      photoPath: "",
      isFetchingProfilePic: false,
      isLoading: false,
      showTopCard: false,
      showEmpListModal: false,
      validationMessages: [],
      showValidationAlert: false,
      showCustomEmail: false,
      templateData: {},
      templateType: "",
      showTooltip: false,
      tooltipText: "",
      candidateStatusList: [
        "Unverified",
        "Verified",
        "Returned",
        "Onboarded",
        "Rejected",
        "Candidate Withdrawn",
      ],
      formId: 178,
    };
  },
  computed: {
    // to check the device is mobile based on window size
    modalText() {
      if (this.candidateStatus === "Returned") {
        return "Returning the candidate form will automatically extend the expiry of the self-onboarding link, allowing the candidate to update the required details. In the next steps, you can add your remarks to an email and preview the message before sending it to the candidate.";
      }
      return `Are you sure want to change the candidate status to ${this.candidateStatus}? Once changed, candidate details cannot be updated`;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return this.formAccess && (this.formAccess.update || this.formAccess.add);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccessForCandidateVerification() {
      let formAccess = this.accessRights("274");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForCandidateRejection() {
      let formAccess = this.accessRights("279");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForCandidateWithdraw() {
      let formAccess = this.accessRights("302");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    loginEmployeeUser() {
      return (
        this.$store.state.userDetails.employeeFirstName +
        " " +
        this.$store.state.userDetails.employeeLastName
      );
    },
    addressLine1() {
      let organization = {};
      if (this.fieldForce) {
        organization = this.$store.state.orgDetails.serviceProvider;
      } else {
        organization = this.$store.state.orgDetails.organization;
      }
      let line1 = [];
      if (organization.street1) {
        line1.push(organization.street1);
      }
      if (organization.street2) {
        line1.push(organization.street2);
      }
      return line1.length > 0 ? line1.join(",") : "";
    },
    addressLine2() {
      const { organization } = this.$store.state.orgDetails;
      let line2 = [];
      if (organization.city) {
        line2.push(organization.city);
      }
      if (organization.state) {
        line2.push(organization.state);
      }
      if (organization.country) {
        line2.push(organization.country);
      }
      if (organization.pincode) {
        line2.push(organization.pincode);
      }
      return line2.length > 0 ? line2.join(",") : "";
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    addUpdateAccess() {
      let individualFormAccess = this.accessRights("178");
      if (
        individualFormAccess &&
        individualFormAccess.accessRights &&
        individualFormAccess.accessRights["view"]
      ) {
        return individualFormAccess.accessRights;
      } else {
        return false;
      }
    },
    disabledStatus() {
      let disabledArray = ["Onboarded"];
      if (
        this.selectedCandidateDetails.Personal_Email === "" ||
        this.selectedCandidateDetails.Personal_Email === null ||
        (this.selectedCandidateDetails.Candidate_Status &&
          ["migrated", "verified"].includes(
            this.selectedCandidateDetails.Candidate_Status.toLowerCase()
          ))
      ) {
        disabledArray.push("Verified", "Returned", "Rejected", "Unverified");
      }
      if (
        this.candidateStatus &&
        this.candidateStatus.toLowerCase() === "verified"
      ) {
        if (
          this.addUpdateAccess &&
          !this.addUpdateAccess.add &&
          !this.addUpdateAccess.update
        ) {
          disabledArray.push("Convert to Employee", "Unverified");
        }
        if (
          !this.formAccessForCandidateWithdraw ||
          (!this.formAccessForCandidateWithdraw.update &&
            !this.formAccessForCandidateWithdraw.add)
        ) {
          disabledArray.push("Candidate Withdrawn");
        }
      }
      if (
        this.candidateStatus &&
        this.candidateStatus.toLowerCase() === "candidate withdrawn"
      ) {
        disabledArray.push("Verified", "Candidate Withdrawn");
      }
      if (
        this.candidateStatus &&
        this.candidateStatus.toLowerCase() === "rejected"
      ) {
        disabledArray.push(
          "Verified",
          "Candidate Withdrawn",
          "Returned",
          "Unverified"
        );
      }
      if (
        this.candidateStatus &&
        this.candidateStatus.toLowerCase() === "unverified"
      ) {
        if (
          !this.formAccessForCandidateVerification ||
          (!this.formAccessForCandidateVerification.update &&
            !this.formAccessForCandidateVerification.add)
        ) {
          disabledArray.push("Verified", "Returned");
        }
        if (
          !this.formAccessForCandidateRejection ||
          (!this.formAccessForCandidateRejection.update &&
            !this.formAccessForCandidateRejection.add)
        ) {
          disabledArray.push("Rejected");
        }
        if (
          !this.formAccessForCandidateWithdraw ||
          (!this.formAccessForCandidateWithdraw.update &&
            !this.formAccessForCandidateWithdraw.add)
        ) {
          disabledArray.push("Candidate Withdrawn");
        }
      } else {
        if (
          !this.formAccessForCandidateVerification ||
          (!this.formAccessForCandidateVerification.update &&
            !this.formAccessForCandidateVerification.add)
        ) {
          disabledArray.push("Verified", "Returned");
        }
        if (
          !this.formAccessForCandidateRejection ||
          (!this.formAccessForCandidateRejection.update &&
            !this.formAccessForCandidateRejection.add)
        ) {
          disabledArray.push("Rejected");
        }
        if (
          !this.formAccessForCandidateWithdraw ||
          (!this.formAccessForCandidateWithdraw.update &&
            !this.formAccessForCandidateWithdraw.add)
        ) {
          disabledArray.push("Candidate Withdrawn");
        }
      }
      return disabledArray;
    },
  },

  watch: {
    selectedCandidateId(id) {
      if (id) {
        this.getPersonalDetails();
      }
    },
    updateCount(val) {
      if (val > 0) {
        this.getPersonalDetails();
      }
    },
    selectedCandidateStatus(val) {
      this.candidateStatus = val;
    },
    selectedCandidateDetails(val) {
      if (val.Personal_Email === "" || val.Personal_Email === null) {
        this.tooltipText =
          "Candidate requires personal email address for you to perform this action";
        this.showTooltip = true;
      } else {
        this.tooltipText = "";
        this.showTooltip = false;
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);

    if (this.selectedCandidateStatus === "Withdrawn") {
      this.candidateStatus = "Candidate Withdrawn";
    } else {
      this.candidateStatus = this.selectedCandidateStatus;
    }
    this.getPersonalDetails();
  },

  methods: {
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    onSelectCandidate(candidate) {
      this.$emit("on-change-candidate", candidate);
      this.showEmpListModal = false;
    },
    onChangeCandidate() {
      this.showEmpListModal = true;
    },
    refetchAPIs() {
      this.isErrorInList = false;
      mixpanel.track("Onboarded-candidate-topCard-refetch");
      this.getPersonalDetails();
    },
    async retrieveProfilePhoto() {
      let vm = this;
      vm.isFetchingProfilePic = true;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName:
            "hrapp_upload/" + vm.orgCode + "_tmp/images/" + vm.photoPath,
          action: "getrawdata",
          type: "profile",
          destinationBucket: "",
          destinationFileKey: "",
        })
        .then((presignedUrl) => {
          mixpanel.track(
            "Onboarded-candidate-topCard-profileImg-fetch-success"
          );
          vm.profileImage = presignedUrl;
          vm.isFetchingProfilePic = false;
        })
        .catch(() => {
          vm.isFetchingProfilePic = false;
          mixpanel.track("Onboarded-candidate-topCard-profileImg-fetch-error");
        });
    },
    getPersonalDetails() {
      let vm = this;
      vm.showTopCard = true;
      vm.listLoading = true;
      vm.profileImage = "";
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_PROFILE_CARD_DETAILS,
          client: "apolloClientV",
          variables: {
            candidateId: vm.selectedCandidateId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("Onboarded-candidate-topCard-fetch-success");
          if (response && response.data && response.data.retrieveMyProfile) {
            const { employeeProfile } = response.data.retrieveMyProfile;
            const {
              street,
              city,
              apartmentName,
              state,
              pinCode,
              country,
              photoPath,
              useLocationAddress,
              locationStreet1,
              locationStreet2,
              locationCity,
              locationState,
              locationPinCode,
              locationCountry,
            } = employeeProfile;
            let allAddress = [];
            if (useLocationAddress) {
              if (locationStreet1) allAddress.push(locationStreet1);
              if (locationStreet2) allAddress.push(locationStreet2);
              if (locationCity) allAddress.push(locationCity);
              if (state) allAddress.push(locationState);
              if (locationCountry) allAddress.push(locationCountry);
              if (locationPinCode) allAddress.push(locationPinCode);
            } else {
              if (apartmentName) allAddress.push(apartmentName);
              if (street) allAddress.push(street);
              if (city) allAddress.push(city);
              if (state) allAddress.push(state);
              if (country) allAddress.push(country);
              if (pinCode) allAddress.push(pinCode);
            }
            allAddress = allAddress.join(", ");
            vm.candidateDetails = employeeProfile;
            vm.candidateDetails["address"] = allAddress;
            vm.photoPath = photoPath;
            if (photoPath) {
              vm.retrieveProfilePhoto();
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("Onboarded-candidate-topCard-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "profile details",
          isListError: true,
        })
        .then(() => {
          this.isErrorInList = true;
        });
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    updateProfilePath(newPhotoPath) {
      let vm = this;
      mixpanel.track("Onboarded-candidate-topCard-profileImg-upload-start");
      if (vm.photoPath !== newPhotoPath) {
        vm.photoPath = newPhotoPath;
        vm.isLoading = true;
        vm.$apollo
          .mutate({
            mutation: UPDATE_PROFILE_PICTURE_PATH,
            variables: {
              candidateId: vm.selectedCandidateId,
              photoPath: newPhotoPath,
            },
            client: "apolloClientW",
          })
          .then(() => {
            mixpanel.track(
              "Onboarded-candidate-topCard-profileImg-upload-success"
            );
            vm.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message: `Profile picture ${
                newPhotoPath ? "updated" : "deleted"
              } successfully`,
              type: "success",
            };
            vm.showAlert(snackbarData);
          })
          .catch((err) => {
            vm.handleUpdateError(err, newPhotoPath);
          });
      } else {
        mixpanel.track("Onboarded-candidate-topCard-profileImg-upload-success");
        let snackbarData = {
          isOpen: true,
          message: `Profile picture ${
            newPhotoPath ? "updated" : "deleted"
          } successfully`,
          type: "success",
        };
        vm.showAlert(snackbarData);
      }
    },

    handleUpdateError(err = "", filePath) {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: filePath ? "updating" : "deleting",
        form: "profile picture",
        isListError: false,
      });
      mixpanel.track("Onboarded-candidate-topCard-profileImg-upload-error");
    },

    goBackToList() {
      mixpanel.track("Onboarded-candidate-topCard-backTo-team-list");
      this.$emit("close-profile");
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_OPENED_COUNT", "0-false");
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_CHANGED", false);
    },

    onChangeRoundStatus(value) {
      this.candidateStatus = value;
      if (value == "Returned") {
        this.formId = 274;
        let templateData = {
          Recruiter_Name: this.loginEmployeeUser,
          Candidate_Name: this.selectedCandidateDetails.candidateName,
          Url: this.selectedCandidateDetails.url,
          Pin: this.selectedCandidateDetails.pin,
        };
        this.templateData = templateData;
        this.templateType = "candidateFinishToDraft";
      } else if (value == "Rejected") {
        this.formId = 279;
        let templateData = {
          Recruiter_Name: this.loginEmployeeUser,
          Candidate_Name: this.selectedCandidateDetails.candidateName,
          Company_Name: this.companyName,
          Company_Address_1: this.addressLine1,
          Company_Address_2: this.addressLine2,
        };
        this.templateData = templateData;
        this.templateType = "rejectedCandidateOnboarded";
      } else if (value == "Candidate Withdrawn") {
        this.formId = 302;
        let templateData = {
          Recruiter_Name: this.loginEmployeeUser,
          Candidate_Name: this.selectedCandidateDetails.candidateName,
          Company_Name: this.companyName,
          Job_Post_Name: this.selectedCandidateDetails.designationName,
        };
        this.templateData = templateData;
        this.templateType = "candidateWithdrawn";
      }
      this.openWarningModal = true;
    },

    confirmUpdate() {
      this.openWarningModal = false;
      this.updateStatus();
    },

    cancelConfirmation() {
      this.openWarningModal = false;
      this.candidateStatus = this.selectedCandidateStatus;
    },
    onCloseCustomEmail() {
      this.showCustomEmail = false;
      this.candidateStatus = this.selectedCandidateStatus;
    },
    validateEmpMail() {
      this.openWarningModal = false;
      if (
        (this.candidateStatus === "Returned" ||
          this.candidateStatus === "Rejected" ||
          this.candidateStatus === "Candidate Withdrawn") &&
        this.selectedCandidateDetails &&
        this.selectedCandidateDetails.Personal_Email
      ) {
        this.showCustomEmail = true;
      } else {
        this.updateStatus();
      }
    },
    onSendCustomEmail() {
      this.showCustomEmail = false;
      this.updateStatus();
    },

    updateStatus() {
      let vm = this;
      vm.isLoading = true;
      let formId;
      if (
        vm.candidateStatus.toLowerCase() === "verified" ||
        vm.candidateStatus.toLowerCase() === "returned"
      ) {
        formId = 274;
      } else if (vm.candidateStatus.toLowerCase() === "rejected") {
        formId = 279;
      } else if (vm.candidateStatus.toLowerCase() === "candidate withdrawn") {
        formId = 302;
      } else {
        formId = 178;
      }
      vm.$apollo
        .mutate({
          mutation: UPDATE_CANDIDATE_STATUS,
          client: "apolloClientW",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: vm.selectedCandidateId,
            formId: formId,
            status:
              vm.candidateStatus.toLowerCase() === "candidate withdrawn"
                ? "Withdrawn"
                : vm.candidateStatus,
          },
        })
        .then(() => {
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidate status updated successfully",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          // if (
          //   vm.candidateStatus === "Returned" &&
          //   vm.selectedCandidateDetails.empEmail
          // ) {
          //   vm.showCustomEmail = true;
          // }
          vm.$emit("status-updated", vm.candidateStatus);
        })
        .catch((err) => {
          vm.handleStatusUpdateError(err);
        });
    },
    handleStatusUpdateError(err = "") {
      this.isLoading = false;
      this.candidateStatus = this.selectedCandidateStatus;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "candidate status",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
  },
};
</script>
