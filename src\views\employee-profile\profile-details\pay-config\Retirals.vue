<template>
  <div>
    <div class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="primary"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Retirals</span
        >
      </div>
      <div v-if="!showEditForm && formAccess && formAccess.update && allowEdit">
        <v-btn @click="openEditForm" color="primary" variant="text">
          <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
        </v-btn>
      </div>
    </div>
    <div v-if="showEditForm">
      <v-form ref="retiralObserver">
        <!-- VPF -->
        <v-row class="pa-4 ma-2">
          <v-col
            v-if="labelList['93'] && labelList['93'].Field_Visiblity === 'Yes'"
            cols="12"
            md="4"
            sm="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["93"].Field_Alias }}
            </p>
            <v-tooltip
              text="Since the payslip has already been generated, updates to this field are unavailable. If any changes are required, please delete the Salary Payslip and Full & Final Settlement."
            >
              <template v-slot:activator="{ props }">
                <v-card
                  width="max-content"
                  variant="plain"
                  v-bind="fieldsEditable ? '' : props"
                  :class="fieldsEditable ? '' : 'cursor-not-allowed'"
                >
                  <v-switch
                    color="primary"
                    v-model="editRetiralsData.Eligible_For_Pension"
                    :disabled="validationData.providentFund || !fieldsEditable"
                    :true-value="1"
                    :false-value="0"
                    @update:model-value="onChangeFields"
                  ></v-switch>
                </v-card>
              </template>
            </v-tooltip>
          </v-col>
          <v-col
            v-if="labelList['97'] && labelList['97'].Field_Visiblity === 'Yes'"
            cols="12"
            md="4"
            sm="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["97"].Field_Alias }}
            </p>
            <v-tooltip
              text="Since the payslip has already been generated, updates to this field are unavailable. If any changes are required, please delete the Salary Payslip and Full & Final Settlement."
            >
              <template v-slot:activator="{ props }">
                <v-card
                  width="max-content"
                  variant="plain"
                  v-bind="fieldsEditable ? '' : props"
                  :class="fieldsEditable ? '' : 'cursor-not-allowed'"
                >
                  <v-switch
                    color="primary"
                    v-model="editRetiralsData.Eligible_For_Vpf"
                    :disabled="!fieldsEditable"
                    :true-value="1"
                    :false-value="0"
                    @update:model-value="onChangeFields"
                  ></v-switch>
                </v-card>
              </template>
            </v-tooltip>
          </v-col>
          <v-col
            v-if="
              editRetiralsData.Eligible_For_Vpf &&
              labelList['98'] &&
              labelList['98'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="4"
            sm="6"
          >
            <CustomSelect
              :itemSelected="editRetiralsData.Vpf_Type"
              :items="vpfTypes"
              :label="labelList['98'].Field_Alias"
              :isRequired="
                labelList['98'].Mandatory_Field === 'Yes' ? true : false
              "
              :rules="[
                labelList['98'].Mandatory_Field === 'Yes'
                  ? required(
                      labelList['98'].Field_Alias,
                      editRetiralsData.Vpf_Type
                    )
                  : true,
              ]"
              @selected-item="onChangeCustomSelectField($event, 'Vpf_Type')"
            ></CustomSelect>
          </v-col>
          <v-col
            v-if="
              editRetiralsData.Eligible_For_Vpf &&
              editRetiralsData.Vpf_Type === 'Percentage' &&
              labelList['99'] &&
              labelList['99'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="4"
            sm="6"
          >
            <v-text-field
              v-model="editRetiralsData.Vpf_Employee_Share"
              :rules="[
                labelList['99'].Mandatory_Field === 'Yes'
                  ? numericRequiredValidation(
                      labelList['99'].Field_Alias,
                      editRetiralsData.Vpf_Employee_Share
                    )
                  : true,
                validateWithRulesAndReturnMessages(
                  editRetiralsData.Vpf_Employee_Share,
                  'vpfEmployeeShare',
                  labelList['99'].Field_Alias,
                  true
                ),
              ]"
              variant="solo"
              type="number"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["99"].Field_Alias
                }}<span
                  v-if="labelList['99'].Mandatory_Field === 'Yes'"
                  style="color: red"
                  >*</span
                >
              </template>
            </v-text-field>
          </v-col>
          <v-col
            v-if="
              editRetiralsData.Eligible_For_Vpf &&
              editRetiralsData.Vpf_Type === 'Amount' &&
              labelList['100'] &&
              labelList['100'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="4"
            sm="6"
          >
            <v-text-field
              v-model="editRetiralsData.Vpf_Employee_Share_Amount"
              :rules="[
                labelList['100'].Mandatory_Field === 'Yes'
                  ? numericRequiredValidation(
                      labelList['100'].Field_Alias,
                      editRetiralsData.Vpf_Employee_Share_Amount
                    )
                  : true,
                validateWithRulesAndReturnMessages(
                  editRetiralsData.Vpf_Employee_Share_Amount,
                  'vpfEmployeeShareAmount',
                  labelList['100'].Field_Alias,
                  true
                ),
              ]"
              type="number"
              variant="solo"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["100"].Field_Alias
                }}<span
                  v-if="labelList['100'].Mandatory_Field === 'Yes'"
                  style="color: red"
                  >*</span
                >
              </template></v-text-field
            >
          </v-col>
        </v-row>
        <!-- CPS -->
        <v-row
          class="pa-4 ma-2"
          v-if="labelList['409']?.Field_Visiblity?.toLowerCase() === 'yes'"
        >
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList['409']?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["409"].Field_Alias }}
            </p>
            <v-switch
              color="primary"
              v-model="
                editRetiralsData.Eligible_For_Contribution_Pension_Scheme
              "
              :true-value="'Yes'"
              :false-value="'No'"
              @update:model-value="onChangeFields"
            ></v-switch>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['410']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.CPS_Number"
              variant="solo"
              :rules="[
                editRetiralsData.CPS_Number
                  ? validateWithRulesAndReturnMessages(
                      editRetiralsData.CPS_Number,
                      'cpsNumber',
                      labelList['410'].Field_Alias,
                      false
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["410"].Field_Alias }}
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['411']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                'yes'
            "
          >
            <CustomSelect
              :itemSelected="editRetiralsData.CPS_Type"
              :items="vpfTypes"
              :label="labelList['411'].Field_Alias"
              :isRequired="true"
              :rules="[
                required(
                  labelList['411'].Field_Alias,
                  editRetiralsData.CPS_Type
                ),
              ]"
              @selected-item="onChangeCustomSelectField($event, 'CPS_Type')"
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['414']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.CPS_Type?.toLowerCase() === 'percentage' &&
              editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.CPS_Employee_Share"
              type="number"
              variant="solo"
              :rules="[
                required(
                  labelList['414'].Field_Alias,
                  editRetiralsData.CPS_Employee_Share
                ),
                editRetiralsData.CPS_Employee_Share &&
                payrollCountry?.toLowerCase() !== 'th'
                  ? validateWithRulesAndReturnMessages(
                      editRetiralsData.CPS_Employee_Share,
                      'cpsEmployeeShare',
                      labelList['414'].Field_Alias,
                      true
                    )
                  : true,
                payrollCountry?.toLowerCase() === 'th'
                  ? minMaxNumberValidation(
                      labelList['414'].Field_Alias,
                      editRetiralsData.CPS_Employee_Share,
                      0.5,
                      15
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["414"].Field_Alias }}
                <span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['415']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.CPS_Type?.toLowerCase() === 'percentage' &&
              editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.CPS_Employer_Share"
              type="number"
              variant="solo"
              :disabled="true"
              :rules="[
                required(
                  labelList['415'].Field_Alias,
                  editRetiralsData.CPS_Employer_Share
                ),
                editRetiralsData.CPS_Employer_Share &&
                payrollCountry?.toLowerCase() !== 'th'
                  ? validateWithRulesAndReturnMessages(
                      editRetiralsData.CPS_Employer_Share,
                      'cpsEmployerShare',
                      labelList['415'].Field_Alias,
                      true
                    )
                  : true,
                payrollCountry?.toLowerCase() === 'th'
                  ? minMaxNumberValidation(
                      labelList['415'].Field_Alias,
                      editRetiralsData.CPS_Employer_Share,
                      0.5,
                      15
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["415"].Field_Alias }}
                <span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['412']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.CPS_Type?.toLowerCase() === 'amount' &&
              editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.CPS_Employee_Share_Amount"
              type="number"
              variant="solo"
              :rules="[
                required(
                  labelList['412'].Field_Alias,
                  editRetiralsData.CPS_Employee_Share_Amount
                ),
                editRetiralsData.CPS_Employee_Share_Amount &&
                payrollCountry?.toLowerCase() !== 'th'
                  ? validateWithRulesAndReturnMessages(
                      editRetiralsData.CPS_Employee_Share_Amount,
                      'cpsEmployeeShareAmount',
                      labelList['412'].Field_Alias,
                      true
                    )
                  : true,
                payrollCountry?.toLowerCase() === 'th'
                  ? minMaxNumberValidation(
                      labelList['412'].Field_Alias,
                      editRetiralsData.CPS_Employee_Share_Amount,
                      0.5,
                      9999999999998
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["412"].Field_Alias }}
                <span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['413']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.CPS_Type?.toLowerCase() === 'amount' &&
              editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.CPS_Employer_Share_Amount"
              type="number"
              variant="solo"
              :disabled="true"
              :rules="[
                required(
                  labelList['413'].Field_Alias,
                  editRetiralsData.CPS_Employer_Share_Amount
                ),
                editRetiralsData.CPS_Employer_Share_Amount &&
                payrollCountry?.toLowerCase() !== 'th'
                  ? validateWithRulesAndReturnMessages(
                      editRetiralsData.CPS_Employer_Share_Amount,
                      'cpsEmployerShareAmount',
                      labelList['413'].Field_Alias,
                      true
                    )
                  : true,
                payrollCountry?.toLowerCase() === 'th'
                  ? minMaxNumberValidation(
                      labelList['413'].Field_Alias,
                      editRetiralsData.CPS_Employer_Share_Amount,
                      0.5,
                      9999999999998
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["413"].Field_Alias }}
                <span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
        </v-row>
        <!-- TPF -->
        <v-row
          class="pa-4 ma-2"
          v-if="labelList['404']?.Field_Visiblity?.toLowerCase() === 'yes'"
        >
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList['404']?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["404"].Field_Alias }}
            </p>
            <v-switch
              color="primary"
              v-model="editRetiralsData.Eligible_For_Teacher_Provident_Fund"
              :true-value="'Yes'"
              :false-value="'No'"
              @update:model-value="onChangeFields"
            ></v-switch>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['405']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.TPF_Number"
              variant="solo"
              :rules="[
                editRetiralsData.TPF_Number
                  ? validateWithRulesAndReturnMessages(
                      editRetiralsData.TPF_Number,
                      'tpfNumber',
                      labelList['405'].Field_Alias,
                      false
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["405"].Field_Alias }}
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['406']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
                'yes'
            "
          >
            <CustomSelect
              :itemSelected="editRetiralsData.TPF_Type"
              :items="vpfTypes"
              :isRequired="true"
              :label="labelList['406'].Field_Alias"
              :rules="[
                required(
                  labelList['406'].Field_Alias,
                  editRetiralsData.TPF_Type
                ),
              ]"
              @selected-item="onChangeCustomSelectField($event, 'TPF_Type')"
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['407']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.TPF_Type?.toLowerCase() === 'amount' &&
              editRetiralsData.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.TPF_Employee_Share_Amount"
              type="number"
              variant="solo"
              :rules="[
                validateWithRulesAndReturnMessages(
                  editRetiralsData.TPF_Employee_Share_Amount,
                  'tpfEmployeeShareAmount',
                  labelList['407'].Field_Alias,
                  true
                ),
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["407"].Field_Alias }}
                <span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['408']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.TPF_Type?.toLowerCase() === 'percentage' &&
              editRetiralsData.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.TPF_Employee_Share"
              type="number"
              variant="solo"
              :rules="[
                validateWithRulesAndReturnMessages(
                  editRetiralsData.TPF_Employee_Share,
                  'tpfEmployeeShare',
                  labelList['408'].Field_Alias,
                  true
                ),
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["408"].Field_Alias }}
                <span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
        </v-row>
        <!-- SPF -->
        <v-row class="pa-4 ma-2">
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList['416']?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["416"].Field_Alias }}
            </p>
            <v-switch
              color="primary"
              v-model="editRetiralsData.Eligible_For_Special_Provident_Fund"
              :true-value="'Yes'"
              :false-value="'No'"
              @update:model-value="onChangeFields"
            ></v-switch>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['417']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.Eligible_For_Special_Provident_Fund?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.SPF_Number"
              variant="solo"
              :rules="[
                editRetiralsData.SPF_Number
                  ? validateWithRulesAndReturnMessages(
                      editRetiralsData.SPF_Number,
                      'spfNumber',
                      labelList['417'].Field_Alias,
                      false
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["417"].Field_Alias }}
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['418']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.Eligible_For_Special_Provident_Fund?.toLowerCase() ===
                'yes'
            "
          >
            <v-text-field
              v-model="editRetiralsData.SPF_Employee_Share"
              type="number"
              variant="solo"
              :rules="[
                required(
                  labelList['418'].Field_Alias,
                  editRetiralsData.SPF_Employee_Share
                ),
                editRetiralsData.SPF_Employee_Share
                  ? validateWithRulesAndReturnMessages(
                      editRetiralsData.SPF_Employee_Share,
                      'spfEmployeeShare',
                      labelList['418'].Field_Alias,
                      true
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields"
            >
              <template v-slot:label>
                {{ labelList["418"].Field_Alias }}
                <span class="text-red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="
              labelList['419']?.Field_Visiblity?.toLowerCase() === 'yes' &&
              editRetiralsData.Eligible_For_Special_Provident_Fund?.toLowerCase() ===
                'yes'
            "
          >
            <datepicker
              :format="'MMMM, yyyy'"
              v-model="editRetiralsData.SPF_End_Month"
              maximum-view="year"
              minimum-view="month"
              class="datepicker-employee_attendance"
              :disabled-dates="getDisabledDates"
              @input="onChangeFields"
            ></datepicker>
          </v-col>
        </v-row>
      </v-form>
    </div>
    <div v-else>
      <v-row class="pa-4 ma-2 card-blue-background">
        <v-col
          v-if="labelList['93'] && labelList['93'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["93"].Field_Alias }}
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                Employees enrolled as EPF members on or before September 1,
                2014, and employees enrolled after September 1, 2014 receiving
                less than ₹15,000 will have to contribute to the Employee
                Pension Scheme mandatorily. Employees receiving more than
                ₹15,000 need not contribute to EPS and their entire PF
                contribution will go into their Provident Fund account.
              </div>
            </v-tooltip>
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ viewRetiralsData.Eligible_For_Pension ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col
          v-if="labelList['97'] && labelList['97'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["97"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ viewRetiralsData.Eligible_For_Vpf ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col
          v-if="
            labelList['98'] &&
            labelList['98'].Field_Visiblity === 'Yes' &&
            viewRetiralsData.Eligible_For_Vpf
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["98"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.Vpf_Type) }}
          </p>
        </v-col>
        <v-col
          v-if="
            viewRetiralsData.Vpf_Type === 'Percentage' &&
            viewRetiralsData.Eligible_For_Vpf &&
            labelList['99'] &&
            labelList['99'].Field_Visiblity === 'Yes'
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["99"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.Vpf_Employee_Share) }}
          </p>
        </v-col>
        <v-col
          v-if="
            viewRetiralsData.Vpf_Type === 'Amount' &&
            viewRetiralsData.Eligible_For_Vpf &&
            labelList['100'] &&
            labelList['100'].Field_Visiblity === 'Yes'
          "
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["100"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.Vpf_Employee_Share_Amount) }}
          </p>
        </v-col>
      </v-row>
      <!-- CPS -->
      <v-row
        class="pa-4 ma-2 card-blue-background"
        v-if="labelList['409']?.Field_Visiblity?.toLowerCase() == 'yes'"
      >
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList['409']?.Field_Visiblity?.toLowerCase() == 'yes'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["409"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              checkNullValue(
                viewRetiralsData.Eligible_For_Contribution_Pension_Scheme
              )
            }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['410']?.Field_Visiblity?.toLowerCase() == 'yes' &&
            viewRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["410"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.CPS_Number) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['411']?.Field_Visiblity?.toLowerCase() === 'yes' &&
            viewRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["411"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.CPS_Type) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            viewRetiralsData.CPS_Type === 'Percentage' &&
            labelList['414']?.Field_Visiblity?.toLowerCase() === 'yes' &&
            viewRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["414"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.CPS_Employee_Share) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            viewRetiralsData.CPS_Type === 'Percentage' &&
            labelList['415']?.Field_Visiblity?.toLowerCase() === 'yes' &&
            viewRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["415"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.CPS_Employer_Share) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            viewRetiralsData.CPS_Type === 'Amount' &&
            labelList['412']?.Field_Visiblity?.toLowerCase() === 'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["412"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.CPS_Employee_Share_Amount) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            viewRetiralsData.CPS_Type === 'Amount' &&
            labelList['413']?.Field_Visiblity?.toLowerCase() === 'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["413"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.CPS_Employer_Share_Amount) }}
          </p>
        </v-col>
      </v-row>
      <!-- TPS -->
      <v-row
        class="pa-4 ma-2 card-blue-background"
        v-if="labelList['404']?.Field_Visiblity?.toLowerCase() == 'yes'"
      >
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList['404']?.Field_Visiblity?.toLowerCase() == 'yes'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["404"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              checkNullValue(
                viewRetiralsData.Eligible_For_Teacher_Provident_Fund
              )
            }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['405']?.Field_Visiblity?.toLowerCase() == 'yes' &&
            viewRetiralsData?.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["405"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.TPF_Number) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['406']?.Field_Visiblity?.toLowerCase() == 'yes' &&
            viewRetiralsData?.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["406"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.TPF_Type) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['407']?.Field_Visiblity?.toLowerCase() == 'yes' &&
            viewRetiralsData?.TPF_Type?.toLowerCase() === 'amount'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["407"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.TPF_Employee_Share_Amount) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['408']?.Field_Visiblity?.toLowerCase() == 'yes' &&
            viewRetiralsData?.TPF_Type?.toLowerCase() === 'percentage'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["408"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.TPF_Employee_Share) }}
          </p>
        </v-col>
      </v-row>
      <!-- SPF -->
      <v-row
        class="pa-4 ma-2 card-blue-background"
        v-if="labelList['416']?.Field_Visiblity?.toLowerCase() == 'yes'"
      >
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList['416']?.Field_Visiblity?.toLowerCase() == 'yes'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["416"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              checkNullValue(
                viewRetiralsData.Eligible_For_Special_Provident_Fund
              )
            }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['417']?.Field_Visiblity?.toLowerCase() == 'yes' &&
            viewRetiralsData?.Eligible_For_Special_Provident_Fund?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["417"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.SPF_Number) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['418']?.Field_Visiblity?.toLowerCase() == 'yes' &&
            viewRetiralsData?.Eligible_For_Special_Provident_Fund?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["418"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(viewRetiralsData.SPF_Employee_Share) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="
            labelList['419']?.Field_Visiblity?.toLowerCase() == 'yes' &&
            viewRetiralsData?.Eligible_For_Special_Provident_Fund?.toLowerCase() ===
              'yes'
          "
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList["419"].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ formatDate(viewRetiralsData.SPF_End_Month) }}
          </p>
        </v-col>
      </v-row>
    </div>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateRetirals()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import { UPDATE_RETIRALS_CONFIG } from "@/graphql/employee-profile/profileQueries.js";
import { checkNullValue } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import mixpanel from "mixpanel-browser";
import moment from "moment";
import Config from "@/config.js";
import Datepicker from "vuejs3-datepicker";

export default {
  name: "RetiralsComponent",
  components: { CustomSelect, Datepicker },
  mixins: [validationRules],
  props: {
    retiralsData: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      required: true,
    },
    allowEdit: {
      type: Boolean,
      required: true,
    },
    validationData: {
      type: Object,
      required: true,
    },
    labelList: {
      type: Array,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
  },

  emits: ["update-success", "edit-form-opened"],

  data: () => ({
    viewRetiralsData: {},
    editRetiralsData: {},
    vpfTypes: ["Percentage", "Amount"],
    showEditForm: false,
    isFormDirty: false,
    isLoading: false,
    openMoreDetails: false,
    validationMessages: [],
    showValidationAlert: false,
    openWarningModal: false,
    openBottomSheet: false,
    fieldsEditable: true,
  }),
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    payrollCountry() {
      return this.$store.state.payrollCountry;
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          return moment(date).format("MMMM, YYYY");
        } else {
          return "-";
        }
      };
    },
    getDisabledDates() {
      let date = moment().add(1, "month").startOf("month").format("YYYY-MM-DD");
      return {
        to: new Date(date),
      };
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.viewRetiralsData = this.retiralsData;
    this.fieldsEditable =
      this.selectedEmpStatus == "Active" ||
      this.validationData.salaryNotGenerated;
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    openBottomSheet(val) {
      this.$emit("edit-form-opened", val);
    },
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
    "editRetiralsData.CPS_Employee_Share": {
      handler(val) {
        this.editRetiralsData.CPS_Employer_Share = val;
      },
      immediate: true,
    },
    "editRetiralsData.CPS_Employee_Share_Amount": {
      handler(val) {
        this.editRetiralsData.CPS_Employer_Share_Amount = val;
      },
      immediate: true,
    },
  },

  methods: {
    checkNullValue,

    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.editRetiralsData[field] = value;
    },

    openEditForm() {
      mixpanel.track("EmpProfile-payConfig-retirals-edit-opened");
      this.editRetiralsData = JSON.parse(JSON.stringify(this.viewRetiralsData));
      this.showEditForm = true;
      this.openBottomSheet = true;
    },

    closeEditForm() {
      mixpanel.track("EmpProfile-payConfig-retirals-edit-closed");
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.showEditForm = false;
      }
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    async validateRetirals() {
      const { valid } = await this.$refs.retiralObserver.validate();
      mixpanel.track("EmpProfile-payConfig-retirals-submit-cliked");
      if (valid) {
        this.updateRetirals();
      }
    },

    updateRetirals() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_RETIRALS_CONFIG,
          variables: {
            employeeId: vm.selectedEmpId,
            eligibleForPension: vm.editRetiralsData.Eligible_For_Pension
              ? vm.editRetiralsData.Eligible_For_Pension
              : 0,
            eligibleForVpf: vm.editRetiralsData.Eligible_For_Vpf
              ? vm.editRetiralsData.Eligible_For_Vpf
              : 0,
            vpfType: vm.editRetiralsData.Eligible_For_Vpf
              ? vm.editRetiralsData.Vpf_Type
              : "",
            vpfEmployeeShare:
              vm.editRetiralsData.Vpf_Type === "Percentage" &&
              vm.editRetiralsData.Vpf_Employee_Share
                ? parseFloat(vm.editRetiralsData.Vpf_Employee_Share)
                : null,
            vpfEmployeeShareAmount:
              vm.editRetiralsData.Vpf_Type === "Amount" &&
              vm.editRetiralsData.Vpf_Employee_Share_Amount
                ? parseFloat(vm.editRetiralsData.Vpf_Employee_Share_Amount)
                : null,
            eligibleForPensionScheme:
              vm.editRetiralsData.Eligible_For_Contribution_Pension_Scheme,
            cpsType:
              vm.editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
              "yes"
                ? vm.editRetiralsData.CPS_Type
                : "",
            cpsEmployeeShareAmount:
              vm.editRetiralsData.CPS_Type === "Amount" &&
              vm.editRetiralsData.CPS_Employee_Share_Amount &&
              vm.editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                "yes"
                ? parseFloat(vm.editRetiralsData.CPS_Employee_Share_Amount)
                : null,
            cpsEmployerShareAmount:
              vm.editRetiralsData.CPS_Type === "Amount" &&
              vm.editRetiralsData.CPS_Employer_Share_Amount &&
              vm.editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                "yes"
                ? parseFloat(vm.editRetiralsData.CPS_Employer_Share_Amount)
                : null,
            cpsEmployeeShare:
              vm.editRetiralsData.CPS_Type === "Percentage" &&
              vm.editRetiralsData.CPS_Employee_Share &&
              vm.editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                "yes"
                ? parseFloat(vm.editRetiralsData.CPS_Employee_Share)
                : null,
            cpsEmployerShare:
              vm.editRetiralsData.CPS_Type === "Percentage" &&
              vm.editRetiralsData.CPS_Employer_Share &&
              vm.editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
                "yes"
                ? parseFloat(vm.editRetiralsData.CPS_Employer_Share)
                : null,
            eligibleForTeacherProvidentFund:
              vm.editRetiralsData.Eligible_For_Teacher_Provident_Fund,
            tpfNumber:
              vm.editRetiralsData.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
              "yes"
                ? vm.editRetiralsData.TPF_Number
                : "",
            tpfType:
              vm.editRetiralsData.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
              "yes"
                ? vm.editRetiralsData.TPF_Type
                : "",
            tpfEmployeeShareAmount:
              vm.editRetiralsData.TPF_Type === "Amount" &&
              vm.editRetiralsData.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
                "yes"
                ? parseFloat(vm.editRetiralsData.TPF_Employee_Share_Amount)
                : null,
            tpfEmployeeShare:
              vm.editRetiralsData.TPF_Type === "Percentage" &&
              vm.editRetiralsData.Eligible_For_Teacher_Provident_Fund?.toLowerCase() ===
                "yes"
                ? parseFloat(vm.editRetiralsData.TPF_Employee_Share)
                : null,
            cpsNumber:
              vm.editRetiralsData.Eligible_For_Contribution_Pension_Scheme?.toLowerCase() ===
              "yes"
                ? vm.editRetiralsData.CPS_Number
                : "",
            eligibleForSpecialProvidentFund:
              vm.editRetiralsData.Eligible_For_Special_Provident_Fund,
            spfNumber:
              vm.editRetiralsData.Eligible_For_Special_Provident_Fund?.toLowerCase() ==
              "yes"
                ? vm.editRetiralsData.SPF_Number
                : "",
            spfEmployeeShare:
              vm.editRetiralsData.Eligible_For_Special_Provident_Fund?.toLowerCase() ==
              "yes"
                ? parseFloat(vm.editRetiralsData.SPF_Employee_Share)
                : null,
            spfEndMonth:
              moment(vm.editRetiralsData.SPF_End_Month).isValid() &&
              vm.editRetiralsData.Eligible_For_Special_Provident_Fund?.toLowerCase() ==
                "yes"
                ? moment(vm.editRetiralsData.SPF_End_Month).format("M,YYYY")
                : "",
          },
          client: "apolloClientAD",
        })
        .then(() => {
          mixpanel.track("EmpProfile-payConfig-retirals-edit-success");
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Retirals updated successfully.",
          };
          vm.showAlert(snackbarData);
          vm.openBottomSheet = false;
          vm.showEditForm = false;
          vm.isLoading = false;
          vm.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
          vm.$store.commit(
            "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
            "0-false"
          );
          vm.$emit("update-success");
        })
        .catch((updateError) => {
          vm.handleUpdateError(updateError);
        });
    },

    handleUpdateError(err) {
      mixpanel.track("EmpProfile-payConfig-retirals-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "retirals",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
