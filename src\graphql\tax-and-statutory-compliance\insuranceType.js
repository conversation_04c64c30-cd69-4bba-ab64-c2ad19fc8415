import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_INSURANCE_RULES = gql`
  query retrieveInsuranceRules {
    retrieveInsuranceRules {
      errorCode
      message
      insuranceRulesData
      slabWiseData
    }
  }
`;

// ===============
// Mutations
// ===============

export const ADD_UPDATE_INSURANCE_RULES = gql`
  mutation addUpdateInsuranceRules(
    $insuranceTypeId: Int!
    $insuranceName: String!
    $slabWiseInsurance: String!
    $insuranceType: String
    $employerSharePercentage: Float
    $employeeSharePercentage: Float
    $employerShareAmount: Float
    $employeeShareAmount: Float
    $autoDeclaration: String
    $autoDeclarationApplicableFor: String
    $sectionInvestmentCategoryId: Int
    $overrideInsuranceContributionAtEmployeeLevel: String
    $paymentFrequency: String!
    $employeeStateInsurance: String
    $description: String
    $insuranceTypeStatus: String!
  ) {
    addUpdateInsuranceRules(
      insuranceTypeId: $insuranceTypeId
      insuranceName: $insuranceName
      slabWiseInsurance: $slabWiseInsurance
      insuranceType: $insuranceType
      employerSharePercentage: $employerSharePercentage
      employeeSharePercentage: $employeeSharePercentage
      employerShareAmount: $employerShareAmount
      employeeShareAmount: $employeeShareAmount
      autoDeclaration: $autoDeclaration
      autoDeclarationApplicableFor: $autoDeclarationApplicableFor
      sectionInvestmentCategoryId: $sectionInvestmentCategoryId
      overrideInsuranceContributionAtEmployeeLevel: $overrideInsuranceContributionAtEmployeeLevel
      paymentFrequency: $paymentFrequency
      employeeStateInsurance: $employeeStateInsurance
      description: $description
      insuranceTypeStatus: $insuranceTypeStatus
    ) {
      errorCode
      message
    }
  }
`;
