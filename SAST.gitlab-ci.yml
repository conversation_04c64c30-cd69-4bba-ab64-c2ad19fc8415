# Read more about this feature here: https://docs.gitlab.com/ee/user/application_security/sast/
#
# Configure the scanning tool through the environment variables.
# List of the variables: https://gitlab.com/gitlab-org/security-products/sast#settings
# How to set: https://docs.gitlab.com/ee/ci/yaml/#variables

sast:
    stage: sast
    image: docker:stable
    variables:
        DOCKER_DRIVER: overlay2
        DOCKER_TLS_CERTDIR: ''
    allow_failure: true
    services:
        - docker:stable-dind
    script:
        - export SAST_VERSION=${SP_VERSION:-$(echo "$CI_SERVER_VERSION" | sed 's/^\([0-9]*\)\.\([0-9]*\).*/\1-\2-stable/')}
        - |
            if ! docker info &>/dev/null; then
              if [ -z "$DOCKER_HOST" -a "$KUBERNETES_PORT" ]; then
                export DOCKER_HOST='tcp://localhost:2375'
              fi
            fi
        - | # this is required to avoid undesirable reset of Docker image ENV variables being set on build stage
            function propagate_env_vars() {
              CURRENT_ENV=$(printenv)

              for VAR_NAME; do
                echo $CURRENT_ENV | grep "${VAR_NAME}=" > /dev/null && echo "--env $VAR_NAME "
              done
            }
        - |
            docker run \
              $(propagate_env_vars \
                SAST_BANDIT_EXCLUDED_PATHS \
                SAST_ANALYZER_IMAGES \
                SAST_ANALYZER_IMAGE_PREFIX \
                SAST_ANALYZER_IMAGE_TAG \
                SAST_DEFAULT_ANALYZERS \
                SAST_PULL_ANALYZER_IMAGES \
                SAST_BRAKEMAN_LEVEL \
                SAST_FLAWFINDER_LEVEL \
                SAST_GITLEAKS_ENTROPY_LEVEL \
                SAST_GOSEC_LEVEL \
                SAST_EXCLUDED_PATHS \
                SAST_DOCKER_CLIENT_NEGOTIATION_TIMEOUT \
                SAST_PULL_ANALYZER_IMAGE_TIMEOUT \
                SAST_RUN_ANALYZER_TIMEOUT \
                SAST_JAVA_VERSION \
                ANT_HOME \
                ANT_PATH \
                GRADLE_PATH \
                JAVA_OPTS \
                JAVA_PATH \
                JAVA_8_VERSION \
                JAVA_11_VERSION \
                MAVEN_CLI_OPTS \
                MAVEN_PATH \
                MAVEN_REPO_PATH \
                SBT_PATH \
                FAIL_NEVER \
              ) \
              --volume "$PWD:/code" \
              --volume /var/run/docker.sock:/var/run/docker.sock \
              "registry.gitlab.com/gitlab-org/security-products/sast:$SAST_VERSION" /app/bin/run /code
    artifacts:
        reports:
            sast: gl-sast-report.json
    dependencies: []
    only:
        refs:
            - branches
        variables:
            - $GITLAB_FEATURES =~ /\bsast\b/
    except:
        variables:
            - $SAST_DISABLED
