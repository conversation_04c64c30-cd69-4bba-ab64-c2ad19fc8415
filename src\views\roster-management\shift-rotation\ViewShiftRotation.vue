<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="text-primary" size="20">fas fa-tasks</v-icon>
          </v-avatar>
          <div class="text-subtitle-1 font-weight-bold">Shift Rotation</div>
        </div>
        <div class="d-flex align-center">
          <v-icon class="mx-1" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <FormTab
        :model-value="openedSubTab"
        grow
        :hide-slider="true"
        style="border-bottom: 1px solid #cfcfcf; border-radius: 0px"
      >
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            {{ tab.label }}
            <div
              v-if="isActiveSubTab(tab.value)"
              class="mt-3 mb-n4"
              :class="[
                isActiveSubTab(tab.value)
                  ? 'text-primary font-weight-bold'
                  : 'text-grey-darken-2 font-weight-bold',
              ]"
              style="border-bottom: 4px solid; width: 200px"
            ></div>
          </div>
        </v-tab>
      </FormTab>
      <div
        :style="
          isMobileView
            ? 'height: calc(100vh - 400px); overflow: scroll'
            : 'height: 430px; overflow: scroll;'
        "
      >
        <v-card-text>
          <v-window v-model="openedSubTab" style="width: 100%">
            <v-window-item value="shift-rotation">
              <div>
                <v-row class="px-sm-8 px-md-12 mt-2">
                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Scheduler Name
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(selectedItem.Scheduler_Name) }}
                    </p>
                  </v-col>

                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Repeat Schedule
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(selectedItem.Repeat_Schedule) }}
                    </p>
                  </v-col>

                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Repeat Count
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(selectedItem.Repeat_Count) }}
                    </p>
                  </v-col>

                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Enable Roster Leave
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(selectedItem.Enable_Roster_Leave) }}
                    </p>
                  </v-col>
                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Leave Entitlement Per Roster Day
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{
                        checkNullValue(
                          selectedItem.Leave_Entitlement_Per_Roster_Day
                        )
                      }}
                    </p>
                  </v-col>
                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Leave Replenishment Period
                    </p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{
                        checkNullValue(selectedItem.Leave_Replenishment_Period)
                      }}
                    </p>
                  </v-col>
                  <v-col cols="12" sm="6" :class="isMobileView ? ' ml-4' : ''">
                    <p class="text-subtitle-1 text-grey-darken-1">Leave Type</p>
                    <p class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(selectedItem.Leave_Name) }}
                    </p>
                  </v-col>

                  <v-col v-if="moreDetailsList.length > 0" cols="12">
                    <MoreDetails
                      :more-details-list="moreDetailsList"
                      :open-close-card="openMoreDetails"
                      @on-open-close="openMoreDetails = $event"
                    ></MoreDetails>
                  </v-col>
                </v-row>
              </div>
            </v-window-item>
            <v-window-item value="rotation-bywork-schedule">
              <div style="height: calc(100vh - 100px); overflow: scroll">
                <div
                  v-for="(schedule, index) in ShiftRotationList"
                  :key="index"
                  class="rounded-lg pa-3 ma-1 mb-4 d-flex flex-column"
                  style="box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1) !important"
                >
                  <v-row>
                    <v-col
                      cols="12"
                      sm="6"
                      :class="isMobileView ? ' ml-4' : ''"
                    >
                      <p class="text-subtitle-1 text-grey-darken-1">
                        Shift Type
                      </p>
                      <p class="text-subtitle-1 font-weight-regular">
                        {{ checkNullValue(schedule.Work_Schedule_Name) }}
                      </p>
                    </v-col>
                    <v-col
                      cols="12"
                      sm="6"
                      :class="isMobileView ? ' ml-4' : ''"
                    >
                      <p class="text-subtitle-1 text-grey-darken-1">
                        Applicable Period
                      </p>
                      <p class="text-subtitle-1 font-weight-regular">
                        {{
                          checkNullValue(
                            schedule.Applicable_Period +
                              " " +
                              (schedule.Period_Unit.toLowerCase() === "week"
                                ? "week(s)"
                                : "day(s)")
                          )
                        }}
                      </p>
                    </v-col>
                    <v-col
                      cols="12"
                      sm="6"
                      :class="isMobileView ? ' ml-4' : ''"
                    >
                      <p class="text-subtitle-1 text-grey-darken-1">
                        Shift Order Level
                      </p>
                      <p class="text-subtitle-1 font-weight-regular">
                        {{ checkNullValue(schedule.Rotation_Level) }}
                      </p>
                    </v-col>
                  </v-row>
                </div>
              </div>
            </v-window-item>
          </v-window>
        </v-card-text>
      </div>
    </v-card>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);

export default {
  name: "ViewShiftRotation",
  components: {
    MoreDetails,
  },

  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  emits: ["close-form"],

  data: () => ({
    // data
    moreDetailsList: [],
    openMoreDetails: false,
    // sub tabs
    openedSubTab: "shift-rotation",
    showValidationAlert: false,
    validationMessages: [],
  }),

  computed: {
    ShiftRotationList() {
      const tempData = this.selectedItem;
      return tempData.Shift_Rotation_Schedule.sort((a, b) => {
        return a.Rotation_Level - b.Rotation_Level;
      });
    },
    subTabItems() {
      return [
        {
          label: "Shift Rotation",
          value: "shift-rotation",
          disable: true,
        },
        {
          label: "Rotation By Work Schedule",
          value: "rotation-bywork-schedule",
          disable: false,
        },
      ];
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  mounted() {
    this.prefillMoreDetails();
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.selectedItem.Added_On),
        addedByName = this.selectedItem.Added_By_Name,
        updatedByName = this.selectedItem.Updated_By_Name,
        updatedOn = this.convertUTCToLocal(this.selectedItem.Updated_On);

      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },

    closeView() {
      this.$emit("close-form");
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
