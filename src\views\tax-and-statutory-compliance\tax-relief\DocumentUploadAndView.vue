<template>
  <v-dialog
    v-model="openDocUploadViewModal"
    @click:outside="closeModal()"
    max-width="800px"
  >
    <v-card class="rounded-lg pa-4">
      <div>
        <v-card-title class="d-flex">
          <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
            >Document Preview/Upload</span
          >
          <v-spacer></v-spacer>
          <v-btn
            v-if="
              fileAction === 'view' &&
              fileNames.length > 0 &&
              !isMobileView &&
              !restrictAddEditDocument
            "
            rounded="lg"
            size="small"
            color="primary"
            variant="elevated"
            class="mr-2"
            @click="addNewDocument()"
            ><v-icon>fas fa-plus</v-icon>Add New</v-btn
          >
          <v-icon color="grey" size="25" @click="closeModal()"
            >fas fa-times</v-icon
          >
        </v-card-title>
        <v-card-text
          v-if="fileAction === 'view'"
          style="min-height: 300px"
          :class="{ 'd-flex align-center justify-center': isFetchingFiles }"
        >
          <v-btn
            v-if="
              !isFetchingFiles &&
              fileNames.length > 0 &&
              isMobileView &&
              !restrictAddEditDocument
            "
            rounded="lg"
            size="small"
            color="primary"
            variant="elevated"
            class="mt-n4 mb-2"
            @click="addNewDocument()"
            ><v-icon>fas fa-plus</v-icon>Add New</v-btn
          >
          <v-progress-circular
            v-if="isFetchingFiles"
            color="secondary"
            indeterminate
            size="64"
          >
          </v-progress-circular>
          <div v-else-if="fileNames.length > 0" class="text-center">
            <v-row justify="center">
              <v-col cols="12" sm="10">
                <div
                  class="d-flex align-center mt-n2 mb-3"
                  style="justify-content: space-around"
                >
                  <v-icon
                    :color="fileNumber === 1 ? 'grey' : 'primary'"
                    :class="
                      fileNumber === 1 ? 'cursor-not-allow' : 'cursor-pointer'
                    "
                    size="25"
                    @click="fileNumber === 1 ? {} : viewPrevFile()"
                    >fas fa-angle-left</v-icon
                  >
                  <div
                    class="d-flex text-h6 font-weight-medium"
                    style="max-width: 500px; word-break: break-all"
                  >
                    {{ formattedFileName(fileNames[fileNumber - 1]) }}
                  </div>
                  <v-icon
                    :color="
                      fileNumber === fileNames.length ? 'grey' : 'primary'
                    "
                    size="25"
                    :class="
                      fileNumber === fileNames.length
                        ? 'cursor-not-allow'
                        : 'cursor-pointer'
                    "
                    @click="
                      fileNumber === fileNames.length ? {} : viewNextFile()
                    "
                    >fas fa-angle-right</v-icon
                  >
                </div>
              </v-col>
              <v-col cols="12" sm="2" v-if="fileAction === 'view'">
                <div class="d-flex justify-end">
                  <v-tooltip text="Delete" location="bottom">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        v-if="!restrictAddEditDocument"
                        rounded="lg"
                        color="red"
                        variant="outlined"
                        class="mr-2"
                        size="small"
                        v-bind="props"
                        @click="removeDocument()"
                        ><v-icon>fas fa-trash-alt</v-icon></v-btn
                      >
                    </template>
                  </v-tooltip>
                  <v-tooltip text="Download" location="bottom">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        rounded="lg"
                        theme="dark"
                        color="blue"
                        variant="outlined"
                        size="small"
                        v-bind="props"
                        @click="downloadFile()"
                      >
                        <v-icon>fas fa-file-download</v-icon>
                      </v-btn>
                    </template>
                  </v-tooltip>
                </div>
              </v-col>
            </v-row>
            <div
              :style="`
                max-height: calc(80vh - 200px);
                overflow: scroll;
              `"
            >
              <img
                v-if="imgSrc"
                :src="imgSrc"
                alt="image source"
                style="width: 100%"
              />
              <vue-pdf-app
                v-else-if="iframeSrc"
                style="height: 100vh"
                :pdf="iframeSrc"
              ></vue-pdf-app>
            </div>
            <div class="d-flex text-h6 justify-center">
              <div>{{ fileNumber }}</div>
              /
              <div>{{ fileNames.length }}</div>
            </div>
          </div>
          <div
            v-else
            class="d-flex flex-column justify-center align-center"
            style="height: 200px"
          >
            <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
              >No Documents are uploaded</span
            >
            <v-btn
              v-if="!restrictAddEditDocument"
              rounded="lg"
              size="small"
              color="primary"
              variant="elevated"
              class="mr-2"
              @click="addNewDocument()"
              ><v-icon class="primary">fas fa-plus</v-icon>Add New</v-btn
            >
          </div>
        </v-card-text>
        <v-card-text v-else>
          <v-row>
            <v-col cols="12">
              <v-file-input
                prepend-icon=""
                show-size
                :model-value="fileContents"
                append-inner-icon="fas fa-paperclip"
                label="Document"
                variant="solo"
                multiple
                chips
                closable-chips
                :error-messages="fileContentErrorMsg"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                @update:modelValue="onChangeFiles"
                @click:clear="removeFiles"
              ></v-file-input>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="cancelAddForm()"
                  variant="outlined"
                  class="ma-2 pa-2 secondary"
                  color="grey"
                >
                  Cancel
                </v-btn>
                <v-btn
                  :disabled="!isFormDirty"
                  class="ma-2 pa-1"
                  variant="elevated"
                  color="primary"
                  @click="validateDocUploadForm()"
                >
                  Upload
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
        <div v-if="fileAction === 'view'" class="d-flex justify-center">
          <v-btn
            @click="closeModal()"
            variant="outlined"
            class="ma-2 pa-2"
            color="secondary"
          >
            Cancel
          </v-btn>
          <v-btn
            class="ma-2 pa-1"
            color="primary"
            variant="elevated"
            :disabled="!isFileRemovedOrUploaded"
            @click="saveDocument()"
          >
            Save
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import moment from "moment";
import VuePdfApp from "vue3-pdf-app";
import "vue3-pdf-app/dist/icons/main.css";

export default {
  name: "DocumentUploadAndView",

  components: { VuePdfApp },

  emits: ["close-doc-upload-modal", "document-uploaded"],

  props: {
    employeeId: {
      type: Number,
      required: true,
    },
    uploadedFiles: {
      type: Array,
      default: function () {
        return [];
      },
    },
    restrictAddEditDocument: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {
      openDocUploadViewModal: true,
      fileContents: [],
      fileNames: [],
      newFileNames: [],
      fileUploadCount: 0,
      isLoading: false,
      isFormDirty: false,
      fileAction: "",
      isFileRemovedOrUploaded: false,
      fileContentErrorMsg: "",
      // document presentation
      iframeSrc: "",
      imgSrc: "",
      fileNumber: 1,
      isFetchingFiles: false,
      downloadLink: "",
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    formattedFileName() {
      return (fileName) => {
        if (fileName) {
          let fileNameChunks = fileName.split("?");
          return fileNameChunks && fileNameChunks.length > 0
            ? fileNameChunks[2]
            : "-";
        }
        return "file";
      };
    },
  },

  watch: {
    fileUploadCount(val) {
      if (val === this.fileContents.length) {
        this.isFileRemovedOrUploaded = true;
        this.fileNames = this.fileNames.concat(this.newFileNames);
        this.fileNumber = this.fileNames.length;
        this.fileContents = [];
        this.fileAction = "view";
        this.isLoading = false;
        this.retrieveFileContent();
      }
    },
  },

  mounted() {
    if (this.uploadedFiles.length == 0) {
      this.fileAction = "add";
    } else {
      this.fileAction = "view";
      for (let file of this.uploadedFiles) {
        let fileName =
          file && Object.keys(file).length > 0 && file.File_Name
            ? file.File_Name
            : file;
        this.fileNames.push(fileName);
      }
      this.retrieveFileContent();
    }
  },

  methods: {
    closeModal() {
      this.openDocUploadViewModal = false;
      this.$emit("close-doc-upload-modal");
    },
    saveDocument() {
      this.openDocUploadViewModal = false;
      this.$emit("document-uploaded", this.fileNames);
    },
    cancelAddForm() {
      if (this.fileNames.length === 0) {
        this.closeModal();
      } else {
        this.fileContents = [];
        this.fileNumber = 1;
        this.fileAction = "view";
      }
    },
    onChangeFiles(value) {
      this.fileContents = this.fileContents.concat(value);
      this.newFileNames = [];
      for (let file of this.fileContents) {
        this.newFileNames.push(
          this.employeeId + "?" + this.currentTimeStamp + "?" + file.name
        );
      }
      this.validateFiles();
      this.isFormDirty = true;
    },
    removeFiles() {
      this.validateFiles();
      this.isFormDirty = true;
      this.newFileNames = [];
      this.fileContents = null;
    },
    validateFiles() {
      if (this.fileContents.length > 0) {
        this.fileContentErrorMsg = "";
      } else {
        this.fileContentErrorMsg = "Document is required";
      }
    },
    validateDocUploadForm() {
      this.validateFiles();
      if (this.fileContents.length > 0) {
        this.uploadDocument();
      }
    },

    viewPrevFile() {
      this.fileNumber -= 1;
      this.retrieveFileContent();
    },

    viewNextFile() {
      this.fileNumber += 1;
      this.retrieveFileContent();
    },

    downloadFile() {
      window.open(this.downloadLink, "_blank");
    },

    removeDocument() {
      let removeIndex = this.fileNumber - 1;
      this.fileNames.splice(removeIndex, 1);
      this.fileNumber = this.fileNumber === 1 ? 1 : this.fileNumber - 1;
      this.isFileRemovedOrUploaded = true;
      this.retrieveFileContent();
    },

    addNewDocument() {
      this.fileAction = "add";
    },

    async retrieveFileContent() {
      let vm = this;
      vm.imgSrc = "";
      vm.iframeSrc = "";
      vm.downloadLink = "";
      let fileName = vm.fileNames[vm.fileNumber - 1];
      if (fileName) {
        vm.isFetchingFiles = true;
        let uploadUrl =
          vm.domainName +
          "/" +
          vm.orgCode +
          "/" +
          "Tax Relief" +
          "/" +
          fileName;
        await vm.$store
          .dispatch("s3FileUploadRetrieveAction", {
            fileName: uploadUrl,
            action: "view",
            type: "documents",
            destinationBucket: "",
            destinationFileKey: "",
          })
          .then((presignedUrl) => {
            if (fileName.includes("pdf")) {
              vm.iframeSrc = presignedUrl;
            } else {
              vm.imgSrc = presignedUrl;
            }
            vm.downloadLink = presignedUrl;
            vm.isFetchingFiles = false;
          })
          .catch(() => {
            vm.isFetchingFiles = false;
            let snackbarData = {
              isOpen: true,
              message:
                "Something went wrong while retrieving the file contents. Please try after some time.",
              type: "warning",
            };
            vm.showAlert(snackbarData);
          });
      }
    },

    uploadDocument() {
      this.isLoading = true;
      this.fileUploadCount = 0;
      for (let i = 0; i < this.fileContents.length; i++) {
        this.uploadFileContents(this.fileContents[i], this.newFileNames[i]);
      }
    },

    async uploadFileContents(file, fileName) {
      let vm = this;
      let fileUploadUrl = this.domainName + "/" + this.orgCode + "/Tax Relief/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + fileName,
          action: "upload",
          type: "documents",
          fileContent: file,
        })
        .then(() => {
          vm.fileUploadCount += 1;
        })
        .catch(() => {
          vm.fileUploadCount += 1;
        });
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
