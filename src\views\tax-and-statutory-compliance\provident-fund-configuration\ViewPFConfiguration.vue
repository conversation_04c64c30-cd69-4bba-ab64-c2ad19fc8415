<template>
  <div v-if="isMounted">
    <section :class="isMobileView ? 'mt-8' : 'mt-4'">
      <div>
        <v-card
          class="py-9 rounded-lg"
          :class="isMobileView ? '' : 'px-5'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="providentFund">
              <v-row class="d-flex justify-space-between mb-4">
                <div class="d-flex align-center">
                  <v-progress-circular
                    model-value="100"
                    color="secondary"
                    :size="22"
                    class="mr-1"
                  ></v-progress-circular>
                  <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                    accessFormName
                  }}</span>
                </div>
                <v-avatar
                  v-if="isSuperAdmin && formAccess.update"
                  @click="$emit('open-edit')"
                  :size="30"
                  color="secondary"
                  class="cursor-pointer"
                  :class="isMobileView ? 'ml-auto mt-2' : ''"
                >
                  <v-icon color="white" :size="12">fas fa-pencil-alt</v-icon>
                </v-avatar>
              </v-row>
              <v-row>
                <v-col
                  v-if="getFieldAlias(37).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(37).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.EPF_Number) }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(38).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(38).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(getEmployeeContributionMessage) }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(39).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(39).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(getEmployerContributionMessage) }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(40).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(40).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      editFormData.PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit
                    }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(41).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(41).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ editFormData.Employer_Contribution_Part_Of_CTC }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(42).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(42).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ editFormData.Admin_Charge_Part_Of_CTC }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(43).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(43).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Edli_Charge_Part_Of_CTC) }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(44).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(44).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      editFormData.Override_PF_Contribution_Rate_At_Employee_Level
                    }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(45).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(45).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(editFormData.Pro_Rate_Restricted_PF_Wage)
                    }}
                  </p>
                </v-col>
                <v-col
                  v-if="getFieldAlias(46).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(46).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        editFormData.Consider_All_Salary_Components_For_LOP
                      )
                    }}
                  </p>
                </v-col>
              </v-row>
              <v-row>
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
    </section>
  </div>

  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { convertUTCToLocal, checkNullValue } from "@/helper.js";
export default {
  name: "ViewPFConfiguration",
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    labelList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    formAccess: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    MoreDetails,
  },
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      isLoading: false,
      isMounted: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    getEmployeeContributionMessage() {
      return this.formEmployeeContributionMessage(
        this.editFormData.Employee_Contribution_Rate,
        this.editFormData.Employee_Share
          ? this.editFormData.Employee_Share
          : null,
        this.editFormData.Restricted_PF_Wage_Amount
          ? this.editFormData.Restricted_PF_Wage_Amount
          : null
      );
    },
    getEmployerContributionMessage() {
      return this.formEmployerContributionMessage(
        this.editFormData.Employer_Contribution_Rate,
        this.editFormData.Employer_Share
          ? this.editFormData.Employer_Share
          : null,
        this.editFormData.Restricted_PF_Wage_Amount
          ? this.editFormData.Restricted_PF_Wage_Amount
          : null
      );
    },
  },
  watch: {
    editFormData: {
      immediate: true,
      handler() {
        this.prefillMoreDetails();
      },
    },
  },
  mounted() {
    this.prefillMoreDetails();
    this.isMounted = true;
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    getFieldAlias(fieldId) {
      return this.labelList.find((field) => field.Field_Id === fieldId) || {};
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const updatedByName = this.editFormData.Updated_By_Name,
        updatedOn = this.convertUTCToLocal(this.editFormData.Updated_On);

      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    formEmployeeContributionMessage(contributionRate, share, wageAmount) {
      if (
        contributionRate === "Restricted" &&
        share !== null &&
        wageAmount !== null
      ) {
        return `Restrict wage contribution to ${wageAmount}`;
      } else if (contributionRate === "Actual" && share !== null) {
        return `${share}% of actual wage`;
      } else {
        return "-";
      }
    },
    formEmployerContributionMessage(contributionRate, share, wageAmount) {
      if (
        contributionRate === "Restricted" &&
        share !== null &&
        wageAmount !== null
      ) {
        return `Restrict wage contribution to ${wageAmount}`;
      } else if (contributionRate === "Actual" && share !== null) {
        return `${share}% of actual wage`;
      } else {
        return "-";
      }
    },
  },
};
</script>
