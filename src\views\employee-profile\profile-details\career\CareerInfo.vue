<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs()"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else>
    <div v-if="!hideOtherDetails" class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs()" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <div id="skillDiv" class="mt-4">
      <SkillDetails
        :skillDetails="skillDetails"
        :selectedEmpId="selectedEmpId"
        :formAccess="formAccess"
        :empFormUpdateAccess="empFormUpdateAccess"
        :callingFrom="callingFrom"
        @edit-opened="hideOtherDetails = true"
        @edit-closed="hideOtherDetails = false"
        @refetch-career-details="handleUpdateSuccess()"
      ></SkillDetails>
    </div>
    <div v-if="!hideOtherDetails">
      <div id="educationDiv" class="mt-4">
        <div class="d-flex">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="blue-grey"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold">
              Education Details
            </span>
          </div>
          <span v-if="enableAdd" class="d-flex justify-end ml-auto">
            <v-btn
              color="primary"
              variant="text"
              @click="showAddEditEducationForm = true"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
            >
          </span>
        </div>
        <v-dialog
          v-model="showAddEditEducationForm"
          max-width="70%"
          @click:outside="closeEditForm"
        >
          <AddEditEducationDetails
            :selectedEducationDetails="selectedEducationDetails"
            :selectedEmpId="selectedEmpId"
            :selectedEmployeeDob="selectedEmployeeDob"
            :callingFrom="callingFrom"
            @close-education-form="closeEditForm"
            @refetch-career-details="handleUpdateSuccess"
          >
          </AddEditEducationDetails>
        </v-dialog>
        <div v-if="!isMobileView" class="d-flex">
          <v-slide-group
            class="px-4"
            selected-class="bg-secondary"
            prev-icon="fas fa-chevron-circle-left"
            next-icon="fas fa-chevron-circle-right"
            show-arrows
          >
            <v-slide-group-item>
              <ViewEducationDetails
                :educationDetails="educationDetails"
                :formAccess="formAccess"
                :empFormUpdateAccess="empFormUpdateAccess"
                @on-open-edit="openEditForm($event)"
                @on-delete="onShowDeleteConfirmation($event)"
              />
            </v-slide-group-item>
          </v-slide-group>
        </div>
        <div v-else>
          <div class="card-container">
            <ViewEducationDetails
              :educationDetails="educationDetails"
              :formAccess="formAccess"
              :empFormUpdateAccess="empFormUpdateAccess"
              @on-open-edit="openEditForm($event)"
              @on-delete="onShowDeleteConfirmation($event)"
            />
          </div>
        </div>
      </div>
      <div id="certificationDiv" class="mt-4">
        <div class="d-flex">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="brown"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
              >Certification Details</span
            >
          </div>
          <span v-if="enableAdd" class="d-flex justify-end ml-auto">
            <v-btn
              color="primary"
              variant="text"
              @click="showAddEditCertificationForm = true"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
            >
          </span>
        </div>
        <v-dialog
          v-model="showAddEditCertificationForm"
          max-width="70%"
          @click:outside="closeEditForm"
        >
          <AddEditCertificationDetails
            :selectedCertificationDetails="selectedCertificationDetails"
            :selectedEmpId="selectedEmpId"
            :selectedEmployeeDob="selectedEmployeeDob"
            :callingFrom="callingFrom"
            @close-certification-form="closeEditForm"
            @refetch-career-details="handleUpdateSuccess"
          >
          </AddEditCertificationDetails>
        </v-dialog>
        <div v-if="!isMobileView" class="d-flex">
          <v-slide-group
            class="px-4"
            selected-class="bg-secondary"
            prev-icon="fas fa-chevron-circle-left"
            next-icon="fas fa-chevron-circle-right"
            show-arrows
          >
            <v-slide-group-item>
              <ViewCertificationDetails
                :certificationDetails="certificationDetails"
                :formAccess="formAccess"
                :empFormUpdateAccess="empFormUpdateAccess"
                @on-open-edit="openEditForm($event)"
                @on-delete="onShowDeleteConfirmation($event)"
              />
            </v-slide-group-item>
          </v-slide-group>
        </div>
        <div v-else>
          <div class="card-container">
            <ViewCertificationDetails
              :certificationDetails="certificationDetails"
              :formAccess="formAccess"
              :empFormUpdateAccess="empFormUpdateAccess"
              @on-open-edit="openEditForm($event)"
              @on-delete="onShowDeleteConfirmation($event)"
            />
          </div>
        </div>
      </div>
      <div id="trainingDiv" class="mt-4">
        <div class="d-flex">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="light-green"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
              >Training Details</span
            >
          </div>
          <span v-if="enableAdd" class="d-flex justify-end ml-auto">
            <v-btn
              color="primary"
              variant="text"
              @click="showAddEditTrainingForm = true"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
            >
          </span>
        </div>
        <v-dialog
          v-model="showAddEditTrainingForm"
          max-width="70%"
          @click:outside="closeEditForm"
        >
          <AddEditTrainingDetails
            :selectedTrainingDetails="selectedTrainingDetails"
            :selectedEmpId="selectedEmpId"
            :selectedEmployeeDob="selectedEmployeeDob"
            :callingFrom="callingFrom"
            @close-training-form="closeEditForm"
            @refetch-career-details="handleUpdateSuccess"
          >
          </AddEditTrainingDetails>
        </v-dialog>
        <div v-if="!isMobileView" class="d-flex">
          <v-slide-group
            class="px-4"
            selected-class="bg-success"
            prev-icon="fas fa-chevron-circle-left"
            next-icon="fas fa-chevron-circle-right"
            show-arrows
          >
            <v-slide-group-item>
              <ViewTrainingDetails
                :trainingDetails="trainingDetails"
                :formAccess="formAccess"
                :empFormUpdateAccess="empFormUpdateAccess"
                @on-open-edit="openEditForm($event)"
                @on-delete="onShowDeleteConfirmation($event)"
              />
            </v-slide-group-item>
          </v-slide-group>
        </div>
        <div v-else class="card-container">
          <ViewTrainingDetails
            :trainingDetails="trainingDetails"
            :formAccess="formAccess"
            :empFormUpdateAccess="empFormUpdateAccess"
            @on-open-edit="openEditForm($event)"
            @on-delete="onShowDeleteConfirmation($event)"
          />
        </div>
      </div>
      <div id="awardDiv" class="mt-4 pb-8">
        <div class="d-flex">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="light-blue"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
              >Award Details</span
            >
          </div>
          <span v-if="enableAdd" class="d-flex justify-end ml-auto">
            <v-btn
              color="primary"
              variant="text"
              @click="showAddEditAwardForm = true"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
            >
          </span>
        </div>
        <v-dialog
          v-model="showAddEditAwardForm"
          max-width="70%"
          @click:outside="closeEditForm"
        >
          <AddEditAwardDetails
            :selectedAwardDetails="selectedAwardDetails"
            :selectedEmpId="selectedEmpId"
            :selectedEmployeeDob="selectedEmployeeDob"
            :callingFrom="callingFrom"
            @close-award-form="closeEditForm"
            @refetch-career-details="handleUpdateSuccess"
          >
          </AddEditAwardDetails>
        </v-dialog>
        <div v-if="!isMobileView" class="d-flex">
          <v-slide-group
            class="px-4"
            selected-class="bg-secondary"
            prev-icon="fas fa-chevron-circle-left"
            next-icon="fas fa-chevron-circle-right"
            show-arrows
          >
            <v-slide-group-item>
              <ViewAwardsDetails
                :awardDetails="awardDetails"
                :formAccess="formAccess"
                :empFormUpdateAccess="empFormUpdateAccess"
                @on-open-edit="openEditForm($event)"
                @on-delete="onShowDeleteConfirmation($event)"
              />
            </v-slide-group-item>
          </v-slide-group>
        </div>
        <div v-else>
          <div class="card-container">
            <ViewAwardsDetails
              :awardDetails="awardDetails"
              :formAccess="formAccess"
              :empFormUpdateAccess="empFormUpdateAccess"
              @on-open-edit="openEditForm($event)"
              @on-delete="onShowDeleteConfirmation($event)"
            />
          </div>
        </div>
      </div>
    </div>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      confirmation-heading="Are you sure you want to delete this record ?"
      icon-name="fas fa-trash-alt"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteSelectedCareerDetails()"
    ></AppWarningModal>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import ViewEducationDetails from "./education/ViewEducationDetails";
import ViewCertificationDetails from "./certification/ViewCertificationDetails";
import ViewTrainingDetails from "./training/ViewTrainingDetails";
import ViewAwardsDetails from "./awards/ViewAwardsDetails.vue";
import SkillDetails from "./skills/SkillDetails.vue";
// components
const AddEditEducationDetails = defineAsyncComponent(() =>
  import("./education/AddEditEducationDetails.vue")
);
const AddEditCertificationDetails = defineAsyncComponent(() =>
  import("./certification/AddEditCertificationDetails.vue")
);
const AddEditTrainingDetails = defineAsyncComponent(() =>
  import("./training/AddEditTrainingDetails.vue")
);
const AddEditAwardDetails = defineAsyncComponent(() =>
  import("./awards/AddEditAwardDetails.vue")
);
import { RETRIEVE_EMP_CAREER_INFO } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "CareerInfo",
  components: {
    ViewEducationDetails,
    ViewCertificationDetails,
    ViewTrainingDetails,
    SkillDetails,
    ViewAwardsDetails,
    AddEditEducationDetails,
    AddEditCertificationDetails,
    AddEditTrainingDetails,
    AddEditAwardDetails,
  },
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["details-retrieved"],
  data: () => ({
    // edit/add
    showAddEditEducationForm: false,
    showAddEditCertificationForm: false,
    showAddEditTrainingForm: false,
    showAddEditAwardForm: false,
    selectedEducationDetails: {},
    selectedCertificationDetails: {},
    selectedTrainingDetails: {},
    selectedAwardDetails: {},
    // view
    educationDetails: [],
    certificationDetails: [],
    trainingDetails: [],
    awardDetails: [],
    skillDetails: [
      {
        Primary_Skill: "",
        Secondary_Skill: "",
        Known_Skills: "",
        Hands_On: "",
      },
    ],
    // delete
    selectedEducationDelateRecord: null,
    selectedCertificationDelateRecord: null,
    selectedTrainingDelateRecord: null,
    selectedAwardDelateRecord: null,
    openWarningModal: false,
    // others
    hideOtherDetails: false,
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    enableAdd() {
      return (
        this.empFormUpdateAccess || (this.formAccess && this.formAccess.add)
      );
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.selectedEmpId || this.callingFrom === "profile") {
      this.getCareerDetails();
    }
  },
  methods: {
    openEditForm(params) {
      mixpanel.track("EmpProfile-career-edit-opened");
      let selectedItem = params[0],
        typeofSkill = params[1];
      // Set the form data to the selected card's data for editing
      if (typeofSkill == "education") {
        this.selectedEducationDetails = selectedItem;
        this.showAddEditEducationForm = true;
      } else if (typeofSkill == "certification") {
        this.selectedCertificationDetails = selectedItem;
        this.showAddEditCertificationForm = true;
      } else if (typeofSkill == "training") {
        this.selectedTrainingDetails = selectedItem;
        this.showAddEditTrainingForm = true;
      } else if (typeofSkill == "award") {
        this.selectedAwardDetails = selectedItem;
        this.showAddEditAwardForm = true;
      }
    },
    closeEditForm() {
      mixpanel.track("EmpProfile-career-edit-closed");
      this.selectedEducationDetails = {};
      this.selectedCertificationDetails = {};
      this.selectedTrainingDetails = {};
      this.selectedAwardDetails = {};
      this.hideOtherDetails = false;
      this.showAddEditEducationForm = false;
      this.showAddEditCertificationForm = false;
      this.showAddEditTrainingForm = false;
      this.showAddEditAwardForm = false;
    },
    // this method opens the delete confirmation popup
    onShowDeleteConfirmation(params) {
      this.openWarningModal = true;
      let selectedItem = params[0],
        typeofSkill = params[1];
      // Set the form data to the selected card's data for editing
      if (typeofSkill == "education") {
        this.selectedEducationDelateRecord = selectedItem;
      } else if (typeofSkill == "certification") {
        this.selectedCertificationDelateRecord = selectedItem;
      } else if (typeofSkill == "training") {
        this.selectedTrainingDelateRecord = selectedItem;
      } else if (typeofSkill == "award") {
        this.selectedAwardDelateRecord = selectedItem;
      }
    },
    onDeleteSelectedCareerDetails() {
      this.onCloseWarningModal();
    },
    //this method closes the delete confirmation popup
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedEducationDelateRecord = null;
      this.selectedCertificationDelateRecord = null;
      this.selectedTrainingDelateRecord = null;
      this.selectedAwardDelateRecord = null;
    },
    handleUpdateSuccess() {
      this.closeEditForm();
      this.refetchAPIs("update");
    },
    refetchAPIs(type = "") {
      this.isErrorInList = false;
      mixpanel.track("EmpProfile-career-refetch");
      this.getCareerDetails(type);
    },
    getCareerDetails(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_CAREER_INFO,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-career-fetch-success");
          if (response && response.data && response.data.retrieveCareerInfo) {
            const {
              educationalInfoDetails,
              certificateInfoDetails,
              trainingInfoDetails,
              awardDetails,
              skillDetails,
            } = response.data.retrieveCareerInfo;
            vm.educationDetails = educationalInfoDetails
              ? JSON.parse(educationalInfoDetails)
              : [];
            vm.certificationDetails = certificateInfoDetails
              ? JSON.parse(certificateInfoDetails)
              : [];
            vm.trainingDetails = trainingInfoDetails
              ? JSON.parse(trainingInfoDetails)
              : [];
            vm.awardDetails = awardDetails ? JSON.parse(awardDetails) : [];
            vm.skillDetails = skillDetails
              ? JSON.parse(skillDetails)
              : vm.skillDetails;
            vm.$emit("details-retrieved", type);
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("EmpProfile-career-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "career details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  /* The grid-gap property adds a 10-pixel gap between the cards. */
  grid-gap: 10px;
}
@media (max-width: 600px) {
  .card-container {
    grid-template-columns: 1fr;
  }
}
</style>
