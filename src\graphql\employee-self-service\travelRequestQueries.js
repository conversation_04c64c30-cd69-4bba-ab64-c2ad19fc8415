import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_TRAVEL_REQUESTS = gql`
  query ListEmployeeTravel($employeeId: Int, $formId: Int!) {
    listEmployeeTravel(employeeId: $employeeId, formId: $formId) {
      errorCode
      message
      employeeTravelDetails {
        requestId
        employeeId
        tripName
        travelType
        travelStartDate
        travelEndDate
        businessPurpose
        tripType
        budgetAmount
        destinationCountry
        visaRequired
        addedOn
        mealPreference
        seatPreference
        updatedOn
        userDefinedEmpId
        employeeName
        addedByName
        updatedByName
        status
      }
    }
  }
`;

export const RETRIEVE_TRAVEL_REQUEST = gql`
  query RetrieveEmployeeTravel($requestId: Int!, $formId: Int!) {
    retrieveEmployeeTravel(requestId: $requestId, formId: $formId) {
      errorCode
      message
      travelDetails {
        tripId
        userDefinedEmpId
        employeeName
        employeeId
        tripName
        destinationCountry
        visaRequired
        travelType
        travelStartDate
        travelEndDate
        tripType
        mealPreference
        seatPreference
        status
        businessPurpose
        budgetAmount
        updatedByName
        updatedOn
        processInstanceId
        addedOn
        addedByName

        flightDetails {
          departFrom
          arriveAt
          departureDate
          airlinePreference
          returnDate
          departureTimePreference
          arrivalTimePreference
          entityDescription: description
          selfBooking
        }

        hotelStays {
          location
          checkInDatetime
          checkOutDatetime
          hotelPreference
          entityDescription: description
          selfBooking
        }

        trainDetails {
          departFrom
          arriveAt
          departureDate
          timePreference
          entityDescription: description
          selfBooking
        }

        busDetails {
          departFrom
          arriveAt
          departureDate
          timePreference
          entityDescription: description
          selfBooking
        }

        carDetails {
          departFrom
          arriveAt
          pickUpDateTime
          dropDateTime
          carType
          driverNeeded
          entityDescription: description
          selfBooking
        }
      }
    }
  }
`;

export const RETRIEVE_EMP_PASSPORT_INFO = gql`
  query retrievePersonalInfo($employeeId: Int!) {
    retrievePersonalInfo(employeeId: $employeeId) {
      errorCode
      message
      passportDetails
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_TRAVEL_REQUEST = gql`
  mutation AddUpdateEmployeeTravel(
    $requestId: Int
    $formId: Int!
    $employeeId: Int!
    $tripName: String!
    $travelType: String!
    $businessPurpose: String
    $status: String!
    $tripType: String!
    $budgetAmount: Float!
    $destinationCountry: String
    $visaRequired: Int!
    $mealPreference: String
    $seatPreference: String
    $travelStartDate: String!
    $travelEndDate: String!
    $flightDetails: [FlightDetailInput]
    $hotelStays: [HotelStayInput]
    $trainDetails: [TrainDetailInput]
    $busDetails: [BusDetailInput]
    $carDetails: [CarDetailInput]
  ) {
    addUpdateEmployeeTravel(
      requestId: $requestId
      formId: $formId
      employeeId: $employeeId
      tripName: $tripName
      travelType: $travelType
      businessPurpose: $businessPurpose
      status: $status
      tripType: $tripType
      budgetAmount: $budgetAmount
      destinationCountry: $destinationCountry
      visaRequired: $visaRequired
      mealPreference: $mealPreference
      seatPreference: $seatPreference
      travelStartDate: $travelStartDate
      travelEndDate: $travelEndDate
      flightDetails: $flightDetails
      hotelStays: $hotelStays
      trainDetails: $trainDetails
      busDetails: $busDetails
      carDetails: $carDetails
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_TRAVEL_REQUEST_STATUS = gql`
  mutation employeeTravelStatus(
    $requestId: Int!
    $status: String!
    $formId: Int!
  ) {
    employeeTravelStatus(
      requestId: $requestId
      status: $status
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;
