<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="onCloseView()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:25vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">View Currency Conversion</div>
            <div class="d-flex align-center">
              <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
                <v-icon>fas fa-times</v-icon>
              </v-btn>
            </div>
          </v-card-title>

          <v-card-text class="overflow-y-auto" style="max-height: 90vh">
            <div
              v-if="formAccess?.update"
              class="d-flex justify-end align-center"
            >
              <v-btn
                @click="onEditPosition()"
                class="mr-3 mt-3 bg-white text-primary"
                variant="text"
                rounded="lg"
              >
                <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
              </v-btn>
            </div>
            <div class="px-6">
              <v-row>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Claim Currency
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{
                        checkNullValue(
                          selectedCurrencyConversionData.claimCurrencyName
                        )
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Payroll Currency
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{
                        checkNullValue(
                          selectedCurrencyConversionData.payrollCurrencyName
                        )
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Conversion Type
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{
                        checkNullValue(
                          selectedCurrencyConversionData.conversionType
                        )
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Conversion
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{
                        checkNullValue(
                          selectedCurrencyConversionData.conversionValue
                        )
                      }}
                    </section>
                  </div>
                </v-col>
              </v-row>
              <v-row class="d-flex justify-center">
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails> </v-col
              ></v-row>
            </div> </v-card-text></v-card></template
    ></v-overlay>
  </div>
</template>

<script>
import { checkNullValue, convertUTCToLocal } from "@/helper";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
export default {
  name: "ViewCurrencyConversion",
  emits: ["close-view-details", "edit-currency-conversion-record"],
  props: {
    selectedCurrencyConversionData: {
      type: Object,
      required: true,
    },
    enableView: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      showViewForm: false,
      moreDetailsList: [],
      openMoreDetails: true,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formAccess() {
      let formAccess = this.accessRights("355");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formattedFileName() {
      return (fileName) => {
        if (fileName) {
          return fileName.split("?")[3];
        }
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {},
  watch: {
    enableView(val) {
      this.showViewForm = val;
      this.prefillMoreDetails();
    },
  },
  components: {
    MoreDetails,
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    onCloseView() {
      this.showViewForm = false;
      this.$emit("close-view-details");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      const addedOn = this.convertUTCToLocal(
          this.selectedCurrencyConversionData.addedOn
        ),
        addedByName = this.selectedCurrencyConversionData.addedByName,
        updatedByName = this.selectedCurrencyConversionData.updatedByName,
        updatedOn = this.convertUTCToLocal(
          this.selectedCurrencyConversionData.updatedOn
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    onEditPosition() {
      this.showViewForm = false;
      this.$emit("edit-currency-conversion-record");
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
:deep(.ql-toolbar.ql-snow) {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
:deep(.ql-editor) {
  font-family: Roboto, sans-serif !important;
}
</style>
