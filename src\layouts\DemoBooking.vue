<template>
  <v-container
    fluid
    class="main-container ml-sm-n6 d-flex align-center justify-center"
  >
    <v-row class="d-flex justify-center align-center">
      <v-col cols="12" lg="10" xl="8">
        <v-row justify="center">
          <v-col
            cols="12"
            class="text-center text-h4 text-primary font-weight-bold"
          >
            We're delighted to have you onboard.
          </v-col>
          <v-col
            v-if="windowWidth > 756"
            cols="12"
            sm="3"
            class="d-flex justify-center"
          >
            <img
              :src="demoBookingLeftImg"
              alt="payment alert"
              style="width: 90%; height: auto"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="d-flex justify-center flex-column align-center"
            style="max-width: 350px"
          >
            <img
              :src="demoBookingImg"
              alt="payment alert"
              style="width: 320; height: 200"
            />
            <div class="mt-4 text-h4 text-lightblue font-weight-bold">
              Get 1-on-1 help
            </div>
            <div
              class="text-body-1 text-primary text-lighten-2 font-weight-bold text-center mt-4"
              style="max-width: 400px"
            >
              Book a free demo today and get personalized support as you setup
              HRAPP for your crew. We'll help you every step of the way.
            </div>
            <div class="mt-6">
              <RingWaveAnimation
                display-text="Book A Demo"
                :bg-color-codes="animatedWaveLineColor"
                :is-button="true"
                @on-button-click="bookDemo()"
              >
              </RingWaveAnimation>
            </div>
          </v-col>
          <v-col
            v-if="windowWidth > 756"
            cols="12"
            sm="3"
            class="d-flex justify-center"
          >
            <img
              :src="demoBookingRightImg"
              alt="payment alert"
              style="width: 90%; height: auto"
            />
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import RingWaveAnimation from "@/components/helper-components/RingWaveAnimation";

export default {
  name: "DemoBooking",

  components: {
    RingWaveAnimation,
  },

  data: () => ({
    animatedWaveLineColor: ["#03A9F4", "#4FC3F7", "#81D4FA"],
  }),

  computed: {
    //screen size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    demoBookingImg() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/demo-booking.webp");
      else return require("@/assets/images/layout/demo-booking.png");
    },
    demoBookingLeftImg() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/demo-booking-left-info.webp");
      else return require("@/assets/images/layout/demo-booking-left-info.png");
    },
    demoBookingRightImg() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/demo-booking-right-info.webp");
      else return require("@/assets/images/layout/demo-booking-right-info.png");
    },
  },
  methods: {
    bookDemo() {
      window.open("https://meetings.hubspot.com/chandra3", "_blank");
    },
  },
};
</script>
