<template>
  <div>
    <div v-if="mainTabList.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabList"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :class="openedSubTab == 'activities' ? '' : 'mr-8'"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <div v-if="openedSubTab == 'activities'">
                <FormFilter
                  ref="formFilterRef"
                  :projectActivitiesBackupList="projectActivitiesBackupList"
                  @reset-filter="resetSearchFilter()"
                  @apply-filter="applyFilter($event)"
                >
                </FormFilter>
              </div>
              <ProjectFormFilter
                v-else-if="openedSubTab == 'projects'"
                :itemList="projectsBackupList"
                @reset-filter="resetFilterCount += 1"
                @apply-filter="applyProjectFilter($event)"
              ></ProjectFormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="projects-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <ProfileCard class="mt-5">
            <FormTab :model-value="openedSubTab" :hide-slider="true">
              <v-tab
                v-for="tab in subTabItems"
                :key="tab.value"
                :value="tab.value"
                :disabled="tab.disable"
                @click="onChangeSubTabs(tab.value)"
              >
                <div
                  :class="[
                    isActiveSubTab(tab.value)
                      ? 'text-primary font-weight-bold'
                      : 'text-grey-darken-2 font-weight-bold',
                  ]"
                >
                  {{ tab.label }}
                  <div
                    v-if="isActiveSubTab(tab.value)"
                    class="mt-3 mb-n4"
                    style="border-bottom: 4px solid; width: 150px"
                  ></div>
                </div>
              </v-tab>
            </FormTab>
          </ProfileCard>
          <v-row>
            <v-col cols="12">
              <v-window v-model="openedSubTab" style="width: 100%">
                <v-window-item value="projects">
                  <Projects
                    v-if="openedSubTab == 'projects'"
                    :formAccess="formAccess"
                    :applyFilterCount="applyFilterCount"
                    :resetFilterCount="resetFilterCount"
                    :projectsFilteredList="projectsFilteredList"
                    @projects-retrieved="projectsBackupList = $event"
                    @reset-project-filter="resetFilterFields()"
                  ></Projects>
                </v-window-item>
                <v-window-item value="activities">
                  <ProjectActivity
                    v-if="openedSubTab == 'activities'"
                    :formAccess="formAccess"
                    :mainList="projectActivitiesList"
                    :backupMainList="projectActivitiesBackupList"
                    @assign-data="onAssignData"
                    @reset-search-filter="resetSearchFilter"
                  ></ProjectActivity>
                </v-window-item>
                <v-window-item value="room">
                  <Room
                    v-if="openedSubTab == 'room'"
                    :mainList="roomList"
                    :backupMainList="roomBackupList"
                    @assign-data="onAssignRoomData"
                    @reset-search-filter="resetSearchFilter"
                  ></Room>
                </v-window-item>
              </v-window>
            </v-col>
          </v-row>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import Projects from "./project/Projects.vue";
import Room from "./room/Room.vue";
import ProjectActivity from "./project-activity/ProjectActivity.vue";
import FormFilter from "./project-activity/FormFilter.vue";
import ProjectFormFilter from "./project/FormFilter.vue";

export default {
  name: "ProjectsMain",

  components: {
    EmployeeDefaultFilterMenu,
    Projects,
    ProjectActivity,
    FormFilter,
    ProjectFormFilter,
    Room,
  },

  data() {
    return {
      // tab
      currentTabItem: "tab-0",
      openedSubTab: "projects",
      isLoading: false,
      projectActivitiesBackupList: [],
      projectActivitiesList: [],
      roomList: [],
      roomBackupList: [],
      projectsBackupList: [],
      projectsFilteredList: [],
      applyFilterCount: 0,
      resetFilterCount: 0,
    };
  },

  computed: {
    subTabItems() {
      let initialTabs = [
        {
          label: this.projectLabel,
          value: "projects",
          disable: false,
        },
        {
          label: "Activities",
          value: "activities",
          disable: false,
        },
      ];
      let roomForm = this.accessRights("280");
      if (roomForm && roomForm.accessRights && roomForm.accessRights["view"]) {
        initialTabs.push({
          label: roomForm.customFormName || "Room",
          value: "room",
          disable: false,
        });
      }
      return initialTabs;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    accessFormName() {
      let projectForm = this.accessRights("3");
      if (projectForm && projectForm.customFormName) {
        return projectForm.customFormName;
      } else return this.projectLabel;
    },
    formAccess() {
      let projectFormAccess = this.accessRights("3");
      if (
        projectFormAccess &&
        projectFormAccess.accessRights &&
        projectFormAccess.accessRights["view"]
      ) {
        return projectFormAccess.accessRights;
      } else {
        return false;
      }
    },
    empDataSetupFormAccess() {
      return this.$store.getters.empDataSetupFormAccess;
    },
    mainTabList() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.empDataSetupFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.accessFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    projectLabel() {
      return this.$store.state.projectLabel;
    },
    projectLabelSmallCase() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel ? pLabel.toLowerCase() : pLabel;
    },
  },

  mounted() {
    this.currentTabItem =
      "tab-" + this.mainTabList.indexOf(this.accessFormName);
  },

  errorCaptured(err) {
    console.error(this.projectLabel + " err", err);
    let snackbarData = {
      isOpen: true,
      message: `Something went wrong while loading the ${this.projectLabelSmallCase} form. Please try after some time.`,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  methods: {
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    onTabChange(tabName) {
      if (tabName !== this.accessFormName) {
        this.isLoading = true;
        const { formAccess } = this.empDataSetupFormAccess;
        let clickedForm = formAccess[tabName];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },
    onAssignData(data) {
      this.projectActivitiesList = data[0];
      this.projectActivitiesBackupList = data[1];
    },
    onAssignRoomData(data) {
      this.roomList = data[0];
      this.roomBackupList = data[1];
    },
    resetSearchFilter() {
      this.projectActivitiesList = this.projectActivitiesBackupList;
      this.roomList = this.roomBackupList;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.resetFilterFields();
    },
    resetFilterFields() {
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
    },
    applyFilter(filteredArray) {
      if (!filteredArray[1]) {
        this.projectActivitiesList = this.projectActivitiesBackupList;
      } else {
        this.projectActivitiesList = filteredArray[0];
      }
    },
    applyProjectFilter(filteredArray) {
      this.applyFilterCount += 1;
      this.projectsFilteredList = filteredArray;
    },
    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style>
.projects-container {
  padding: 4em 2em 0em 3em;
}
.v-dialog {
  box-shadow: none;
}
@media screen and (max-width: 805px) {
  .projects-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
