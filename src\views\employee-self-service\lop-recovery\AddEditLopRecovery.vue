<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl text-white"
            style="background-color: rgb(var(--v-theme-primary)) !important"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">LOP Recovery</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              color="secondary"
              class="mb-2"
              @click="validateLopRecoveryForm"
              >Apply</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  color="grey-lighten-3"
                  class="cursor-not-allow"
                  variant="flat"
                  >Apply</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" color="secondary" class="mr-1">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="height: calc(100vh - 260px); overflow: scroll"
      >
        <v-card-text>
          <NotesCard
            v-if="noteMessage"
            notes="Payslip is not generated for the deduction month. So LOP recovery cannot be applied."
            class="mb-4"
          ></NotesCard>
          <NotesCard
            v-if="noteMessageForLopDays"
            notes="There are no LOP days available for recovery during the selected LOP deduction month."
            :class="noteMessage ? 'mb-4 mt-4' : 'mb-4'"
          ></NotesCard>
          <v-form ref="lopRecoveryForm">
            <v-row>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex align-center">
                  <CustomSelect
                    :items="lopDeductionMonthList"
                    :itemSelected="lopDeductionMonth"
                    label="LOP Deduction Month"
                    :is-auto-complete="true"
                    :isRequired="true"
                    :disabled="isEdit || listLopRecoveryDeductionLoading"
                    @selected-item="
                      onChangeIsFormDirty($event, 'lopDeductionMonth')
                    "
                    :rules="[
                      required('LOP Deduction Month', lopDeductionMonth),
                    ]"
                    style="max-width: 300px"
                    :is-loading="listLopRecoveryDeductionLoading"
                  ></CustomSelect>
                  <v-tooltip v-model="showTooltip" location="top">
                    <template v-slot:activator="{ props }">
                      <v-icon
                        class="mt-n3 ml-2"
                        v-bind="props"
                        size="small"
                        color="info"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <div
                      style="width: 100px !important; height: 100px !important"
                    >
                      Payroll month in which the LOP deduction was done.
                    </div>
                  </v-tooltip>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="formName == 'MyTeam'"
              >
                <CustomSelect
                  :items="empNameList"
                  label="Employee Name"
                  :isRequired="true"
                  :itemSelected="selectedEmployeeId"
                  @selected-item="
                    onChangeIsFormDirty($event, 'selectedEmployeeId')
                  "
                  :is-auto-complete="true"
                  :selectProperties="{
                    chips: true,
                    closableChips: true,
                    clearable: true,
                  }"
                  item-title="employeeName"
                  item-value="employeeId"
                  :disabled="
                    !lopDeductionMonth || employeeListLoading || isEdit
                  "
                  :is-loading="employeeListLoading"
                  :rules="[required('Employees', selectedEmployeeId)]"
                  style="max-width: 300px"
                  listWidth="max-width: 300px !important"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  label="LOP Days For Recovery"
                  :isRequired="true"
                  :disabled="
                    disableLopDaysRecoveryList || listLopEmployeeLoading
                  "
                  :is-loading="listLopEmployeeLoading"
                  :items="lopDaysForRecoveryList"
                  :itemSelected="selectedLOPDaysForRecovery"
                  @selected-item="
                    onChangeIsFormDirty($event, 'selectedLOPDaysForRecovery')
                  "
                  :is-auto-complete="true"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    closableChips: true,
                    clearable: true,
                  }"
                  item-title="itemTitle"
                  item-value="itemId"
                  :disabledValue="disabledLopDaysForRecovery"
                  :rules="[
                    required(
                      'LOP Days For Recovery',
                      selectedLOPDaysForRecovery
                    ),
                  ]"
                  listWidth="max-width: 100% !important"
                ></CustomSelect>
                <div
                  v-if="errorInLopDaysList"
                  class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                >
                  <v-icon color="warning" size="25"
                    >fas fa-exclamation-triangle</v-icon
                  >
                  <span class="pl-2 text-subtitle-1 font-weight-regular"
                    >{{ errorMessageForLopDays }}
                  </span>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="
                  selectedLOPDaysForRecovery &&
                  selectedLOPDaysForRecovery.length
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Total LOP Recovery Amount
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    totalRecoveryAmount == 0.0
                      ? recoveryAmount
                      : totalRecoveryAmount
                  }}
                </p>
                <NotesCard
                  class="mt-1"
                  imageName=""
                  notes="The total LOP recovery amount is determined by multiplying the number of LOP days for recovery by the per-day LOP deduction amount."
                >
                </NotesCard>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex align-center">
                  <CustomSelect
                    :items="lopRecoveryProcessingMonthList"
                    label="LOP Recovery Processing Month"
                    :isRequired="true"
                    :itemSelected="lOPRecoveryProcessingMonth"
                    @selected-item="
                      onChangeIsFormDirty($event, 'lOPRecoveryProcessingMonth')
                    "
                    :is-auto-complete="true"
                    :disabled="disableLOPProcessingMonth"
                    :rules="[
                      required(
                        'LOP Recovery Processing Month',
                        lOPRecoveryProcessingMonth
                      ),
                    ]"
                    :is-loading="listLopRecoveryPaymentLoading"
                    style="max-width: 300px"
                  ></CustomSelect>
                  <v-tooltip v-model="payrollMonthToolTip" location="top">
                    <template v-slot:activator="{ props }">
                      <v-icon
                        class="mt-n3 ml-2"
                        v-bind="props"
                        size="small"
                        color="info"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                    <div
                      style="width: 150px !important; height: 100px !important"
                    >
                      Payroll month in which you want the LOP recovery amount to
                      be processed.
                    </div>
                  </v-tooltip>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="statusList"
                  label="Status"
                  :isRequired="true"
                  :itemSelected="approvalStatus"
                  @selected-item="
                    onChangeIsFormDirty($event, 'Approval Status')
                  "
                  :is-auto-complete="true"
                  :rules="[required('Status', approvalStatus)]"
                  :disabled="true"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" class="px-md-6 pb-0 mb-2">
                <div class="v-label mb-4">Remarks</div>
                <v-textarea
                  v-model="remark"
                  variant="solo"
                  auto-grow
                  rows="1"
                  :rules="[
                    alphaNumSpCDotHySlashNewLine(remark),
                    minMaxStringValidation('remarks', remark, 3, 500),
                  ]"
                  @update:model-value="isFormDirty = true"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import moment from "moment";
// Queries
import {
  LIST_EMPLOYEE_LOP_LEAVES,
  ADD_UPDATE_LOP_REQUEST,
  CALCULATE_RECOVERY_AMOUNT,
  LIST_LOP_RECOVERY_DEDUCTION_PAYROLL_MONTH,
  LIST_LOP_RECOVERY_PAYMENT_MONTH,
  LIST_LOP_EMPLOYEES,
} from "@/graphql/employee-self-service/lopRecoveryQueries.js";

export default {
  name: "AddEditLopRecovery",
  mixins: [validationRules],
  components: {
    CustomSelect,
    NotesCard,
  },
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isListEmpty: {
      type: Boolean,
      default: false,
    },
    formName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      lopDeductionMonth: "",
      isMounted: false,
      isLoadingDetails: false,
      openConfirmationPopup: false,
      isFormDirty: false,
      showToolTip: false,
      validationMessages: [],
      showValidationAlert: false,

      approvalStatus: "Applied",
      statusList: ["Applied", "Approved", "Paid", "Rejected"],
      recoveryAmount: null,
      perDayLOPRecoveryAmount: null,
      remark: "",
      perDayAmount: null,
      lopDeductionMonthList: [],
      lopRecoveryProcessingMonthList: [],
      lopDaysForRecoveryList: [],
      selectedLOPDaysForRecovery: [],
      payrollMonthToolTip: false,
      showTooltip: false,
      lOPRecoveryProcessingMonth: "",
      employeeList: [],
      employeeLopList: [],
      lopRecoveryID: 0,
      listLopEmployeeLoading: false,
      lopPerDaySalaryLoading: false,
      listLopRecoveryDeductionLoading: false,
      listLopRecoveryPaymentLoading: false,
      selectedEmployeeId: null,
      empNameList: [],
      selectedEmpDetails: {},
      employeeListLoading: false,
      noteMessage: false,
      noteMessageForLopDays: false,
      errorInLopDaysList: false,
      errorMessageForLopDays: "",
      storeLOPDaysForRecovery: [],
      disabledLopDaysForRecovery: [],
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // This function is use to calculate amount to be given the user
    totalRecoveryAmount() {
      let total = 0;
      const amountData = this.lopDaysForRecoveryList.filter(
        (item) =>
          this.selectedLOPDaysForRecovery &&
          this.selectedLOPDaysForRecovery.includes(item.itemId)
      );
      amountData.forEach((value) => {
        const parts =
          value && value.itemTitle ? value.itemTitle.split(" - ") : [];
        if (parts.length === 2 || parts.length === 3) {
          const duration = parts[1].includes("Half a Day") ? 0.5 : 1;
          total += duration * this.perDayAmount;
        }
      });
      return total.toFixed(2);
    },
    disableLopDaysRecoveryList() {
      if (this.formName === "MyTeam") {
        if (this.selectedEmployeeId === null) {
          return true;
        }
      }
      return (
        this.listLopEmployeeLoading ||
        this.noteMessage ||
        this.noteMessageForLopDays ||
        this.errorInLopDaysList ||
        !this.lopDeductionMonth
      );
    },
    disableLOPProcessingMonth() {
      if (!this.selectedLOPDaysForRecovery) {
        return true;
      }
      if (this.selectedLOPDaysForRecovery.length === 0) {
        return true;
      }
      if (!this.lopDeductionMonth) {
        return true;
      }
      return false;
    },
    formatDate() {
      return (date, withTime = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    if (this.isEdit) {
      const {
        LOP_Recovery_Id,
        Salary_Month,
        Recovery_Amount,
        Approval_Status,
        remark,
        Salary_Deduction_Month,
        Leave_Id,
        Employee_Id,
      } = this.editFormData;
      this.lopRecoveryID = LOP_Recovery_Id ? LOP_Recovery_Id : 0;
      this.lOPRecoveryProcessingMonth = Salary_Month ? Salary_Month : "";
      this.recoveryAmount = Recovery_Amount;

      this.approvalStatus = Approval_Status ? Approval_Status : "Applied";
      this.remark = remark ? remark : "";
      this.lopDeductionMonth = Salary_Deduction_Month
        ? Salary_Deduction_Month
        : "";
      this.selectedEmployeeId = parseInt(Employee_Id);

      this.storeLOPDaysForRecovery = Leave_Id ? Leave_Id.split(",") : [];
      this.getListLopRecoveryDeductionMonth();
      this.listLopEmployee();
    }
    this.isFormDirty = false;
    this.isMounted = true;
    if (!this.isEdit) {
      this.getListLopRecoveryDeductionMonth();
    }
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },
    formLopDaysForRecovery(newData) {
      // Split the relevant properties into arrays
      const startDateArray =
        newData && newData.Start_Date ? newData.Start_Date.split(",") : [];
      const reasonArray =
        newData && newData.Reason ? newData.Reason.split(",") : [];
      const durationArray =
        newData && newData.Duration ? newData.Duration.split(",") : [];
      const leaveIds =
        newData && newData.Leave_Id ? newData.Leave_Id.split(",") : [];
      // Create the desired output array
      let lopDaysForRecovery = startDateArray.map((startDate, index) => {
        const duration = parseFloat(durationArray[index]);
        const reason = reasonArray[index];
        const leaveId = parseFloat(leaveIds[index]);
        let durationLabel = "";

        if (duration === 1) {
          durationLabel = "Full Day";
        } else if (duration === 0.5) {
          durationLabel = "Half a Day";
        }
        return {
          itemTitle: `${this.formatDate(
            startDate
          )} - ${reason} (${durationLabel})`,
          itemId: leaveId.toString(),
        };
      });

      return lopDaysForRecovery;
    },
    async onChangeIsFormDirty(val, field) {
      if (field == "lopDeductionMonth") {
        this.lopDeductionMonth = val;
        this.resetAllModelValues();
        if (val != null) {
          this.listLopEmployee();
        }
      } else if (field == "selectedEmployeeId") {
        this.selectedEmployeeId = val;
        this.listLopEmployeeLoading = false;
        this.lopDaysForRecoveryList = [];
        this.selectedLOPDaysForRecovery = null;
        this.errorInLopDaysList = false;
        this.errorMessageForLopDays = "";
        if (val != null) {
          this.listEmployeeLopLeaves();
        }
      } else if (field == "selectedLOPDaysForRecovery") {
        this.selectedLOPDaysForRecovery = val && val.length > 0 ? val : null;
      } else if (field == "lOPRecoveryProcessingMonth") {
        this.lOPRecoveryProcessingMonth = val;
      } else if (field == "Approval Status") {
        this.Approval_Status = val;
      }
      this.isFormDirty = true;
    },
    async validateLopRecoveryForm() {
      const { valid } = await this.$refs.lopRecoveryForm.validate();
      if (valid) {
        this.addUpdateLopRecovery();
      }
    },
    resetAllModelValues() {
      this.employeeLopList = [];
      this.lopRecoveryProcessingMonthList = [];
      this.employeeList = [];
      this.empNameList = [];
      this.perDayAmount = null;
      this.lopDaysForRecoveryList = [];
      this.selectedEmployeeId = null;
      this.selectedLOPDaysForRecovery = null;
      this.lOPRecoveryProcessingMonth = null;
      this.listLopEmployeeLoading = false;
      this.errorInLopDaysList = false;
      this.noteMessage = false;
      this.errorMessageForLopDays = "";
      this.disabledLopDaysForRecovery = [];
      this.noteMessageForLopDays = false;
    },
    leaveDetails() {
      const leaveDetails = [];
      this.employeeLopList.forEach((item) => {
        const leaveIds = item && item.Leave_Id ? item.Leave_Id.split(",") : [];
        const durations = item && item.Duration ? item.Duration.split(",") : [];
        const reasons = item && item.Reason ? item.Reason.split(",") : [];
        const leavePeriods =
          item && item.Leave_Period ? item.Leave_Period.split(",") : [];
        const startDates =
          item && item.Start_Date ? item.Start_Date.split(",") : [];
        const leaveName =
          item && item.Leave_Name ? item.Leave_Name.split(",") : [];

        leaveIds.forEach((leaveId, index) => {
          if (this.selectedLOPDaysForRecovery.includes(leaveId)) {
            leaveDetails.push({
              leaveId: parseInt(leaveId),
              recoveryAmount: Number(
                (this.perDayAmount * (durations[index] || 0)).toFixed(2)
              ),
              leaveName: leaveName[index],
              Reason: reasons[index],
              Duration: parseFloat(durations[index] || 0),
              period: leavePeriods[index] || null,
              startDate: startDates[index],
            });
          }
        });
      });
      return leaveDetails;
    },
    addUpdateLopRecovery() {
      let vm = this;
      vm.isLoadingDetails = true;
      let selectedSalaryMonth = moment(
        vm.lOPRecoveryProcessingMonth,
        "MMMM, YYYY"
      ).format("M,YYYY");
      let selectedLopDeductionMonth = moment(
        vm.lopDeductionMonth,
        "MMMM, YYYY"
      ).format("M,YYYY");
      let leaveDetails = this.leaveDetails();
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_LOP_REQUEST,
            variables: {
              lopRecoveryID: vm.lopRecoveryID ? parseInt(vm.lopRecoveryID) : 0,
              selfService: vm.formName == "MyTeam" ? 0 : 1,
              salaryMonth: selectedSalaryMonth ? selectedSalaryMonth : "",
              leaveDetails: leaveDetails,
              LopDeductedSalaryMonth: selectedLopDeductionMonth,
              salaryPaymentMonth: selectedSalaryMonth
                ? selectedSalaryMonth
                : "",
              employeeId:
                vm.formName == "MyTeam"
                  ? parseInt(vm.selectedEmployeeId)
                  : vm.loginEmployeeId,
              remark: vm.remark ? vm.remark : null,
            },
            client: "apolloClientAD",
          })
          .then(() => {
            vm.isLoadingDetails = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.isEdit
                ? "LOP recovery updated successfully."
                : "LOP recovery added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("edit-updated");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    listEmployeeLopLeaves() {
      let vm = this;
      vm.listLopEmployeeLoading = true;
      let year = moment(vm.lopDeductionMonth, "MMMM, YYYY").format("M,YYYY");
      vm.employeeLopList = [];
      vm.$apollo
        .query({
          query: LIST_EMPLOYEE_LOP_LEAVES,
          variables: {
            salaryMonthYear: year,
            lopRecoveryId: vm.lopRecoveryID,
            employeeId:
              vm.formName == "MyTeam"
                ? parseInt(vm.selectedEmployeeId)
                : vm.loginEmployeeId,
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response && response.data.listEmployeeLopLeaves) {
            vm.employeeLopList =
              response.data.listEmployeeLopLeaves.leaveDetails;
            if (vm.employeeLopList && vm.employeeLopList.length) {
              this.noteMessageForLopDays = false;
              vm.lopDaysForRecoveryList = vm.formLopDaysForRecoveryArray(
                vm.employeeLopList
              );
              vm.lopDaysForRecoveryList.unshift({
                itemTitle: "Select All",
                itemId: "select-all",
              });
              vm.getLopPerDaySalary();
            } else {
              this.noteMessageForLopDays = true;
            }
          }
          if (vm.isEdit) {
            this.selectedLOPDaysForRecovery = this.storeLOPDaysForRecovery;
          }
          vm.listLopEmployeeLoading = false;

          vm.errorInLopDaysList = false;

          vm.errorMessageForLopDays = "";
        })
        .catch((err) => {
          vm.errorInLopDaysList = true;
          vm.errorMessageForLopDays = err.message;
          vm.listLopEmployeeLoading = false;
        });
    },

    listLopEmployee() {
      let vm = this;
      vm.employeeListLoading = true;
      let year = moment(vm.lopDeductionMonth, "MMMM, YYYY").format("M,YYYY");
      vm.employeeList = [];
      vm.$apollo
        .query({
          query: LIST_LOP_EMPLOYEES,
          variables: {
            selfService: vm.formName == "MyTeam" ? 0 : 1,
            salaryMonthYear: year,
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response && response.data.listLopEmployees) {
            vm.employeeList =
              response.data.listLopEmployees.lopEmployeesDetails;

            if (vm.employeeList && vm.employeeList.length) {
              vm.noteMessage = false;
              this.empNameList = this.employeeList.map((employee) => ({
                employeeName: `${employee.Employee_Name} - ${employee.userDefinedEmpId}`,
                employeeId: parseInt(employee.Employee_Id),
              }));
              if (vm.isEdit) {
                vm.listEmployeeLopLeaves();
              } else if (vm.formName == "EmployeeSelfService") {
                vm.listEmployeeLopLeaves();
              }
            } else {
              vm.noteMessage = true;
            }
          }
          vm.employeeListLoading = false;
        })
        .catch(() => {
          vm.employeeListLoading = false;
        });
    },
    formLopDaysForRecoveryArray(newData) {
      let lopDaysForRecovery = newData.map((item) => {
        const startDates =
          item && item.Start_Date ? item.Start_Date.split(",") : [];
        const reasons = item && item.Reason ? item.Reason.split(",") : [];
        const durations = item && item.Duration ? item.Duration.split(",") : [];
        const leaveIds = item && item.Leave_Id ? item.Leave_Id.split(",") : [];
        const claimStatuses =
          item && item.Claim_Status ? item.Claim_Status.split(",") : [];
        let formattedEntries = [];

        for (let i = 0; i < startDates.length; i++) {
          const duration = parseFloat(durations[i]);
          const reason = reasons[i];
          const leaveId = parseFloat(leaveIds[i]);
          const claimStatus = claimStatuses[i];
          let durationLabel = "";

          if (duration === 1) {
            durationLabel = "Full Day";
          } else if (duration === 0.5) {
            durationLabel = "Half a Day";
          }

          if (claimStatus === "Claimed") {
            this.disabledLopDaysForRecovery.push(
              `${this.formatDate(
                startDates[i]
              )} - ${reason} (${durationLabel}) - Claimed`
            );
            formattedEntries.push({
              itemTitle: `${this.formatDate(
                startDates[i]
              )} - ${reason} (${durationLabel}) - Claimed`,
              itemId: leaveId.toString(),
            });
          } else {
            formattedEntries.push({
              itemTitle: `${this.formatDate(
                startDates[i]
              )} - ${reason} (${durationLabel})`,
              itemId: leaveId.toString(),
            });
          }
        }

        return formattedEntries;
      });

      return lopDaysForRecovery.flat();
    },
    handleAddUpdateError(err = "") {
      this.isLoadingDetails = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: "LOP recovery",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    getLopPerDaySalary() {
      let vm = this;
      vm.lopPerDaySalaryLoading = true;
      vm.perDayAmount = null;
      let year = moment(vm.lopDeductionMonth, "MMMM, YYYY").format("M,YYYY");
      vm.$apollo
        .query({
          query: CALCULATE_RECOVERY_AMOUNT,
          variables: {
            employeeId:
              vm.formName == "MyTeam"
                ? parseInt(vm.selectedEmployeeId)
                : vm.loginEmployeeId,
            salaryMonth: year,
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response && response.data.calculatePerDaySalary) {
            vm.perDayAmount = response.data.calculatePerDaySalary.perDaySalary;
            vm.getListLopRecoveryPaymentMonth();
          }
          vm.lopPerDaySalaryLoading = false;
        })
        .catch(() => {
          vm.lopPerDaySalaryLoading = false;
        });
    },
    getListLopRecoveryDeductionMonth() {
      let vm = this;
      vm.listLopRecoveryDeductionLoading = true;
      vm.lopDeductionMonthList = [];
      vm.$apollo
        .query({
          query: LIST_LOP_RECOVERY_DEDUCTION_PAYROLL_MONTH,
          variables: {
            employeeId: vm.loginEmployeeId,
            selfService: vm.formName == "MyTeam" ? 0 : 1,
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response && response.data.listLopRecoveryDeductionPayrollMonth) {
            vm.lopDeductionMonthList =
              response.data.listLopRecoveryDeductionPayrollMonth.payRollMonthDetails;
            vm.lopDeductionMonthList = vm.monthYearFormat(
              vm.lopDeductionMonthList
            );
          }
          vm.listLopRecoveryDeductionLoading = false;
        })
        .catch(() => {
          vm.listLopRecoveryDeductionLoading = false;
        });
    },
    getListLopRecoveryPaymentMonth() {
      let vm = this;
      vm.listLopRecoveryPaymentLoading = true;
      vm.lopRecoveryProcessingMonthList = [];
      let year = moment(vm.lopDeductionMonth, "MMMM, YYYY").format("M,YYYY");
      vm.$apollo
        .query({
          query: LIST_LOP_RECOVERY_PAYMENT_MONTH,
          variables: {
            employeeId:
              vm.formName == "MyTeam"
                ? parseInt(vm.selectedEmployeeId)
                : vm.loginEmployeeId,
            selfService: vm.formName == "MyTeam" ? 0 : 1,
            salaryDeductionMonth: year,
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response && response.data.listLopRecoveryPaymentMonth) {
            vm.lopRecoveryProcessingMonthList =
              response.data.listLopRecoveryPaymentMonth.payConsiderationMonthDetails;
            vm.lopRecoveryProcessingMonthList = vm.monthYearFormat(
              vm.lopRecoveryProcessingMonthList
            );
          }
          vm.listLopRecoveryPaymentLoading = false;
        })
        .catch(() => {
          vm.listLopRecoveryPaymentLoading = false;
        });
    },
    // function to convert "M,YYYY" in to "MMMM, YYYY" for eg."9,2023" into "September, 2023"
    monthYearFormat(data) {
      return data.map((inputDate) =>
        moment(inputDate, "M,YYYY").format("MMMM, YYYY")
      );
    },
  },
};
</script>
