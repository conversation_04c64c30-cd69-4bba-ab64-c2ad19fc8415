<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
      </AppTopBarTab>
    </div>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <AppFetchErrorScreen
            v-if="isErrorInList && !isLoading"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="getSalaryDetails()"
          >
          </AppFetchErrorScreen>
          <v-row v-else>
            <v-col cols="12" sm="6" md="4" lg="3">
              <v-card height="100px" class="pa-5 d-flex align-center">
                <div v-if="detailsLoading" style="width: 100%">
                  <v-skeleton-loader
                    class="mx-auto"
                    type="paragraph"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <p class="text-grey-darken-1 text-body-1">
                    Current Compensation
                  </p>
                  <p class="text-h6">
                    {{ payrollCurrency }}
                    {{ totalYearlyValue(salaryDetails[0]) }} / Annum
                  </p>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" sm="6" md="4" lg="3">
              <v-card height="100px" class="pa-5 d-flex align-center">
                <div v-if="detailsLoading" style="width: 100%">
                  <v-skeleton-loader
                    class="mx-auto"
                    type="paragraph"
                  ></v-skeleton-loader>
                </div>
                <div
                  v-else
                  class="d-flex align-center justify-space-between"
                  style="width: 100%"
                >
                  <div class="text-h4">Payroll</div>
                  <div>
                    <span class="text-grey-darken-1 text-body-1"
                      >Pay Cycle</span
                    >
                    <p class="text-h6">Monthly</p>
                  </div>
                  <div></div>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" md="10">
              <v-card class="pa-4">
                <div v-if="detailsLoading">
                  <v-skeleton-loader
                    class="mx-auto"
                    type="heading"
                  ></v-skeleton-loader>
                </div>
                <div v-else class="text-sm-h4 text-h5 text-primary mb-4">
                  Salary Timeline
                </div>
                <div v-if="detailsLoading">
                  <v-skeleton-loader
                    class="mx-auto"
                    type="article"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <v-card
                    v-for="(salary, index) in salaryDetails"
                    :key="index"
                    class="mb-4"
                  >
                    <v-card-title
                      class="d-flex align-center justify-start"
                      :class="windowWidth < 550 ? 'flex-column' : ''"
                    >
                      <v-progress-circular
                        model-value="100"
                        :color="salary.color"
                        :size="22"
                        class="mr-1"
                      ></v-progress-circular>
                      <div class="mr-4 text-h6">Salary Revision</div>
                      <div class="d-flex align-center">
                        <div
                          class="text-grey-darken-1 text-subtitle-1 text-sm-h6 mr-4"
                        >
                          Effective
                          {{ formattedEffectiveDate(salary.effectiveDate) }}
                        </div>
                        <div
                          v-if="index === 0"
                          class="bg-primary text-white text-body-2 text-sm-body-1 px-2 rounded-lg"
                        >
                          Current
                        </div>
                      </div>
                    </v-card-title>
                    <v-card-text>
                      <v-container
                        class="mt-4"
                        :class="windowWidth < 365 ? 'px-0' : ''"
                      >
                        <v-row>
                          <v-col
                            :cols="windowWidth < 365 ? '6' : '5'"
                            sm="3"
                            md="3"
                            lg="2"
                            :class="windowWidth < 380 ? 'pr-0' : ''"
                            class="pr-0"
                          >
                            <div
                              class="text-grey-darken-1 text-body-2 text-sm-body-1"
                            >
                              Regular Salary
                            </div>
                            <div class="text-subtitle-1 text-sm-h6">
                              {{ payrollCurrency }}
                              {{ totalYearlyValue(salary) }}
                            </div>
                          </v-col>
                          <v-col cols="1" class="d-flex align-center px-0"
                            >=</v-col
                          >
                          <v-col
                            cols="5"
                            sm="3"
                            md="3"
                            lg="2"
                            :class="windowWidth < 380 ? 'pr-0' : ''"
                            class="pl-0"
                          >
                            <div
                              class="text-grey-darken-1 text-body-2 text-sm-body-1"
                            >
                              Total Salary
                            </div>
                            <div class="text-subtitle-1 text-sm-h6">
                              {{ payrollCurrency }}
                              {{ totalYearlyValue(salary) }}
                            </div>
                          </v-col>
                        </v-row>
                        <v-row class="mt-10">
                          <v-col
                            cols="6"
                            sm="4"
                            md="4"
                            xl="2"
                            class="py-0 text-grey-darken-1 text-body-2 text-sm-body-1"
                            >Salary Per Month</v-col
                          >
                          <v-col
                            cols="6"
                            sm="4"
                            md="4"
                            xl="2"
                            class="py-0 text-grey-darken-1 text-body-2 text-sm-body-1"
                            >Effective From</v-col
                          >
                        </v-row>
                        <v-row>
                          <v-col
                            cols="6"
                            sm="4"
                            md="4"
                            xl="2"
                            class="py-0 text-subtitle-1 text-sm-h6"
                            >{{ payrollCurrency }}
                            {{ totalValue(salary) }}</v-col
                          >
                          <v-col
                            cols="6"
                            sm="4"
                            md="4"
                            xl="2"
                            class="py-0 text-subtitle-1 text-sm-h6"
                            >{{
                              formattedEffectiveDate(salary.effectiveDate)
                            }}</v-col
                          >
                          <v-col
                            cols="6"
                            sm="4"
                            md="4"
                            xl="3"
                            class="py-0 text-decoration-underline cursor-pointer text-subtitle-1 text-sm-h6 text-primary"
                            @click="onClickViewSalaryBreakup(salary)"
                            >Salary Breakup
                          </v-col>
                        </v-row>
                      </v-container>
                    </v-card-text>
                  </v-card>
                </div>
              </v-card>
            </v-col>
          </v-row>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <SalaryBreakup
    v-if="viewSalaryBreakup"
    :show-breakup="viewSalaryBreakup"
    :revision-data="selectedSalaryDetails"
    :salary-details="salaryDropdown"
    @close-form="onCloseSalaryBreakup()"
  ></SalaryBreakup>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { GET_MY_SALARY } from "@/graphql/my-finance/mySalary";
import { generateRandomColor } from "@/helper";
import moment from "moment";
const SalaryBreakup = defineAsyncComponent(() => import("./SalaryBreakup.vue"));
export default {
  name: "MySalary",
  components: {
    SalaryBreakup,
  },
  data() {
    return {
      currentTabItem: "",
      viewSalaryBreakup: false,
      salaryDetails: [],
      selectedSalaryDetails: null,
      detailsLoading: false,
      isErrorInList: false,
      errorContent: "",
      allForms: [346, 345],
      salaryDropdown: [],
    };
  },
  computed: {
    mainTabs() {
      let tabs = [];
      tabs = this.allVisibleTabs.map((tab) => {
        return tab.formName;
      });
      return tabs;
    },
    allVisibleTabs() {
      let tabs = [];
      tabs = this.allForms
        .map((tab) => {
          let form = this.accessRights(tab);
          if (
            form?.accessRights?.view ||
            form.customFormName === this.landedFormName
          )
            return { formName: form.customFormName, formId: form.formId };
        })
        .filter((tab) => tab);
      return tabs;
    },
    landedFormName() {
      return this.accessRights(346)?.customFormName;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(346);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formattedEffectiveDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          return moment(date).format("MMM DD, YYYY");
        }
        return "-";
      };
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    totalValue() {
      return (salary) => {
        if (salary?.revisionItems?.length)
          return salary.revisionItems.reduce(
            (sum, item) =>
              sum +
              (item.itemName.toLowerCase() === "full_inc"
                ? 0
                : parseInt(item.value)),
            0
          );
        return 0;
      };
    },
    totalYearlyValue() {
      return (salary) => {
        if (salary?.revisionItems?.length) return this.totalValue(salary) * 12;
        return 0;
      };
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.getSalaryDetails();
    this.retrieveSalaryDetails();
  },
  methods: {
    generateRandomColor,
    onClickViewSalaryBreakup(salary) {
      this.selectedSalaryDetails = salary;
      this.viewSalaryBreakup = true;
    },
    onCloseSalaryBreakup() {
      this.viewSalaryBreakup = false;
      this.selectedSalaryDetails = null;
    },
    retrieveSalaryDetails() {
      let vm = this;
      vm.isLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;

      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            key: "hmc_salary_pay_master",
          },
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.retrieveDropdownDetails &&
            data.retrieveDropdownDetails.dropdownDetails
          ) {
            const tempData = JSON.parse(
              data.retrieveDropdownDetails.dropdownDetails
            );
            if (tempData && tempData.length) {
              vm.salaryDropdown = tempData[0].data || [];
            } else {
              vm.salaryDropdown = [];
            }
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: error,
            action: "retrieving",
            form: "salary details",
            isListError: false,
          });
        });
    },
    async getSalaryDetails() {
      let vm = this;
      vm.detailsLoading = true;
      this.errorContent = "";
      this.isErrorInList = false;
      vm.$apollo
        .mutate({
          mutation: GET_MY_SALARY,
          client: "apolloClientAH",
          variables: {
            formId: 346,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.generateSalaryInformation &&
            response.data.generateSalaryInformation.data
          ) {
            let data = JSON.parse(response.data.generateSalaryInformation.data);
            if (data.result) {
              let result = data.result.data.json;
              const remarksData = result?.remarks?.data;
              if (remarksData && remarksData.length > 0) {
                const sortedSalaryDetails = [...remarksData].sort(
                  (a, b) =>
                    new Date(b.effectiveDate) - new Date(a.effectiveDate)
                );
                this.salaryDetails = [...sortedSalaryDetails];
                this.salaryDetails.forEach((salary) => {
                  salary.color = this.generateRandomColor();
                });
              } else {
                this.isErrorInList = true;
                this.errorContent =
                  result?.remarks?.message + " Please try after some time." ||
                  "Something went wrong.";
                let snackbarData = {
                  isOpen: true,
                  message:
                    result?.remarks?.message + " - " + result?.remarks?.code ||
                    "Something went wrong.",
                  type: "warning",
                };
                vm.showAlert(snackbarData);
              }
            } else {
              this.salaryDetails = {};
              this.isErrorInList = true;
              this.errorContent =
                data.error?.json?.message || "Something went wrong.";
              let snackbarData = {
                isOpen: true,
                message: data?.error?.json?.message || "Something went wrong.",
                type: "warning",
              };
              vm.showAlert(snackbarData);
            }
          }
          vm.detailsLoading = false;
        })
        .catch((err) => {
          this.salaryDetails = {};
          vm.detailsLoading = false;
          vm.$store
            .dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: "salary",
              isListError: true,
            })
            .then((errorMessages) => {
              this.errorContent =
                errorMessages || "Something went wrong. Please try again.";
              this.isErrorInList = true;
            });
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onTabChange(tabName) {
      let form = this.allVisibleTabs.find((tab) => tab.formName === tabName);
      if (form && form.formId) {
        if (form.formId == 345) {
          this.$router.push("/my-finance/my-pay/my-payslip");
        }
      }
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
