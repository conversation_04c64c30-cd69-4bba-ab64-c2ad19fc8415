<template>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppAccessDenied v-else-if="isAccessDenied"></AppAccessDenied>
</template>

<script>
export default {
  name: "MyPay",
  data() {
    return {
      isLoading: true,
      isAccessDenied: false,
      forms: [
        {
          formId: 346,
          formName: "My Salary",
          formUrl: "/my-finance/my-pay/my-salary",
        },
        {
          formId: 345,
          formName: "My Payslip",
          formUrl: "/my-finance/my-pay/my-payslip",
        },
      ],
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      return (formId) => {
        let formAccessRights = this.accessRights(formId);
        if (
          formAccessRights &&
          formAccessRights.accessRights &&
          formAccessRights.accessRights["view"]
        ) {
          return formAccessRights.accessRights;
        } else return false;
      };
    },
  },
  mounted() {
    let redirected = false;
    for (let form of this.forms) {
      if (this.formAccess(form.formId)?.view) {
        this.$router.push(form.formUrl);
        redirected = true;
        break;
      }
    }
    if (!redirected) {
      this.isAccessDenied = true;
      this.isLoading = false;
    }
  },
};
</script>
