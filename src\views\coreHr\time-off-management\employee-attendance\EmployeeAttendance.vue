<template>
  <div>
    <!-- Main content area -->
    <v-container fluid class="attendance-log">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <!-- Loading skeleton or error screens -->
          <div v-if="listLoading" class="mt-3">
            <!-- Skeleton loaders -->
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <!-- Error screens for fetching data -->
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList('Attendance error refetch')"
          >
          </AppFetchErrorScreen>

          <!-- No matching attendance found -->
          <AppFetchErrorScreen
            v-else-if="itemLogList.length === 0 && originalLogList.length"
            :main-title="'There are no Attendance records matched for the selected filters/searches.'"
            image-name="common/no-records"
          >
            <template v-slot:contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <!-- Button to reset filter/search -->
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <!-- Content area for attendance -->
          <div v-else>
            <!-- Buttons for actions like add, refetch, and more actions -->
            <div
              v-if="!isSmallTable"
              class="d-flex flex-wrap align-center"
              :class="isMobileView ? 'flex-column' : 'flex-row'"
              style="justify-content: space-between"
            >
              <v-btn
                v-if="callingFrom?.toLowerCase() === 'team'"
                variant="text"
                class="d-flex align-center pa-0"
                rounded="lg"
                dense
                @click="goBackToList()"
              >
                <template class="d-flex align-center">
                  <v-icon color="primary">fas fa-angle-left fa-lg</v-icon>
                  <span class="text-primary text-decoration-underline"
                    >Back</span
                  >
                </template>
              </v-btn>
              <v-spacer></v-spacer>
              <v-menu
                v-if="callingFrom?.toLowerCase() === 'team'"
                id="activitytracker_my_activity_date_picker"
                v-model="employeeListMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
              >
                <template v-slot:activator="{ props: activatorProps }">
                  <v-btn
                    class="bg-white my-2 ml-2"
                    :class="{
                      'employee-list-btn': isMobileView,
                    }"
                    rounded="lg"
                    dense
                    v-bind="activatorProps"
                  >
                    <template v-slot:prepend>
                      <v-icon color="primary" class="mr-1" size="17">
                        fas fa-user-alt
                      </v-icon>
                    </template>
                    <span
                      style="max-width: 300px"
                      class="text-primary font-weight-bold text-truncate"
                    >
                      {{ employeeData }}
                    </span>
                    <template v-slot:append>
                      <v-icon color="primary" class="ml-1" size="17">
                        {{
                          employeeListMenu
                            ? "fas fa-caret-up"
                            : "fas fa-caret-down"
                        }}
                      </v-icon>
                    </template>
                  </v-btn>
                </template>
                <div
                  ref="employeeListContainer"
                  style="
                    min-height: 100px;
                    max-height: 300px;
                    overflow-y: scroll;
                    background-color: white;
                  "
                  class="white pa-2 pt-0"
                >
                  <div
                    style="
                      position: sticky;
                      top: 0;
                      background-color: white;
                      height: 40px;
                    "
                  >
                    <v-text-field
                      v-model="searchEmployee"
                      density="compact"
                      variant="underlined"
                      hide-details
                      @update:model-value="onSearchEmployee($event)"
                    >
                      <template v-slot:prepend-inner>
                        <v-icon>fas fa-search</v-icon>
                      </template>
                    </v-text-field>
                  </div>
                  <div v-if="allEmployeesList && allEmployeesList.length > 0">
                    <div
                      v-for="employee in allEmployeesList"
                      :key="employee.employeeId"
                      :ref="
                        employee.employeeId === selectedEmployee
                          ? 'selectedEmployeeRef'
                          : null
                      "
                      @click="onChangeEmployee(employee)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <div
                            v-bind="props"
                            class="pa-2 my-2 rounded-lg cursor-pointer"
                            :class="{
                              'bg-hover':
                                isHovering &&
                                selectedEmployee !== employee.employeeId,
                              'bg-primary text-white':
                                selectedEmployee === employee.employeeId,
                              'bg-grey-lighten-4 text-primary':
                                !isHovering &&
                                selectedEmployee !== employee.employeeId,
                            }"
                          >
                            <div
                              class="text-body-2 text-break"
                              style="max-width: 300px"
                            >
                              {{ employee.employeeData }}
                            </div>
                          </div>
                        </template>
                      </v-hover>
                    </div>
                  </div>
                  <div
                    v-else
                    style="height: 100px"
                    class="text-grey rounded-lg d-flex justify-center align-center"
                  >
                    No data available
                  </div>
                </div>
              </v-menu>
              <v-btn class="bg-white my-2 ml-2" rounded="lg">
                <template v-slot:prepend>
                  <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                </template>
                {{ formattedSelectedMonth }}
                <v-menu
                  activator="parent"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                >
                  <Datepicker
                    v-model="selectedMonthYear"
                    :inline="true"
                    :format="'MMMM, yyyy'"
                    maximum-view="year"
                    minimum-view="month"
                    :open-date="selectedMonthYear"
                    :disabled-dates="disabledDates"
                    @update:modelValue="selectedMonthYear = $event"
                  />
                </v-menu>
              </v-btn>

              <div
                v-if="!isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </div>
            </div>

            <!-- No results screen -->
            <AppFetchErrorScreen
              v-if="originalLogList.length === 0 && !showAddEditForm"
              key="no-results-screen"
              :main-title="emptyScenarioMsg"
              :isSmallImage="originalLogList.length === 0"
              :image-name="
                originalLogList.length === 0 ? '' : 'common/no-records'
              "
            >
              <template #contentSlot>
                <div style="max-width: 80%">
                  <v-row
                    :style="
                      originalLogList.length === 0 ? 'background: white' : ''
                    "
                    class="rounded-lg pa-5 mb-4"
                  >
                    <v-col
                      v-if="
                        originalLogList.length === 0 &&
                        !itemEmployeeList.User_Defined_EmpId &&
                        !itemEmployeeList.Employee_Name
                      "
                      cols="12"
                    >
                      <NotesCard
                        notes=""
                        backgroundColor="transparent"
                        class="mb-4"
                      >
                        <template v-slot:notesCardContent>
                          <div>
                            Job information for this employee is incomplete.
                            Please provide the necessary details, including the
                            work schedule and employment type, to enable
                            attendance tracking. You can update the employee's
                            information in the Team Summary section. Click
                            <a :href="`${baseUrl}v3/my-team/team-summary`"
                              >here</a
                            >
                            to update.
                          </div>
                        </template>
                      </NotesCard>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>

            <!-- Data table for displaying Attendance Log -->
            <v-row :class="{ 'mt-5': isSmallTable }">
              <v-col
                v-if="originalLogList.length > 0 && itemLogList.length"
                class="mb-12 position-relative"
                :cols="isSmallTable && windowWidth >= 1264 ? 6 : 12"
              >
                <v-progress-linear
                  v-if="loading"
                  indeterminate
                  color="primary"
                ></v-progress-linear>
                <v-data-table
                  v-model="selectedAttendanceRecords"
                  :headers="tableHeaders"
                  :items="itemLogList"
                  item-value="Employee_Id"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemLogList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[50, 100, -1]"
                  hide-default-footer
                >
                  <template #[`header.Gross_Hours`]="{}">
                    <v-tooltip
                      text="The total hours worked, including regular hours, break hours and overtime."
                      location="top"
                      max-width="300"
                    >
                      <template v-slot:activator="{ props }">
                        <v-hover>
                          <template
                            v-slot:default="{ isHovering, props: hoverProps }"
                          >
                            <div class="d-flex align-center" v-bind="props">
                              <span v-bind="{ ...props, ...hoverProps }"
                                >Gross hours</span
                              >
                              <v-icon
                                :color="isHovering ? 'grey' : 'white'"
                                class="fas fa-arrow-up ml-2"
                                size="12"
                              />
                            </div>
                          </template>
                        </v-hover>
                      </template>
                    </v-tooltip>
                  </template>
                  <template #[`header.Effective_Hours`]="{}">
                    <v-tooltip
                      text="The total hours worked, excluding break hours and overtime."
                      location="top"
                      max-width="300"
                    >
                      <template v-slot:activator="{ props }">
                        <v-hover>
                          <template
                            v-slot:default="{ isHovering, props: hoverProps }"
                          >
                            <div class="d-flex align-center" v-bind="props">
                              <span v-bind="{ ...props, ...hoverProps }"
                                >Effective hours</span
                              >
                              <v-icon
                                :color="isHovering ? 'grey' : 'white'"
                                class="fas fa-arrow-up ml-2"
                                size="12"
                              />
                            </div>
                          </template>
                        </v-hover>
                      </template>
                    </v-tooltip>
                  </template>
                  <template v-slot:item="{ item }">
                    <v-menu
                      v-model="item.showMenu"
                      :close-on-content-click="true"
                      transition="scale-transition"
                      location="right"
                      offset-y
                    >
                      <template v-slot:activator="{ props }">
                        <tr
                          @click="showDetailsForm(item)"
                          class="data-table-tr bg-white cursor-pointer"
                          :class="
                            isMobileView
                              ? 'v-data-table__mobile-table-row ma-0 mt-2'
                              : ''
                          "
                          v-bind="props"
                        >
                          <td id="mobile-view-td" width="300px">
                            <div id="mobile-header" class="font-weight-bold">
                              Date
                            </div>
                            <section class="text-body-2 text-primary">
                              {{ checkNullValue(item.Display_Punching_Date) }}
                              <span
                                v-if="
                                  item?.workscheduleHolidayInputs?.Twodays_Flag
                                "
                              >
                                <sup>+1</sup></span
                              >
                              <span v-if="item.workscheduleHolidayInputs">
                                <v-tooltip text="Week Off">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      v-if="
                                        item?.workscheduleHolidayInputs?.Week_Off_Exist?.toLowerCase() ===
                                        'yes'
                                      "
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      WO</v-btn
                                    ></template
                                  ></v-tooltip
                                >
                                <v-tooltip text="Holiday">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      v-if="
                                        item?.workscheduleHolidayInputs?.Holiday_Exist?.toLowerCase() ===
                                        'yes'
                                      "
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      HO</v-btn
                                    >
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject?.leaveDetails?.length
                                "
                              >
                                <v-tooltip text="Leave">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      LE
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject
                                    ?.compensatoryOffDetails?.length
                                "
                              >
                                <v-tooltip text="Compensatory Off">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      CO
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span v-if="item?.Comp_Off_Attendance_Balance">
                                <v-tooltip text="Compensatory Off Balance">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      COB
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject?.shortTimeOffDetails
                                    ?.length
                                "
                              >
                                <v-tooltip text="Short Time Off">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      SO
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject?.onDutyLeaveDetails
                                    ?.length
                                "
                              >
                                <v-tooltip text="On Duty">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      OD
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  (item?.Missed_Status?.toLowerCase() || '') ===
                                  'missed swipe'
                                "
                              >
                                <v-tooltip text="Missed Swipe">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      MS
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject
                                    ?.onDutyshortTimeOffDetails?.length
                                "
                              >
                                <v-tooltip text="On Duty Hours">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      ODH
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                            </section>
                          </td>
                          <td>
                            <div
                              v-if="!isMobileView"
                              id="mobile-header"
                              class="font-weight-bold"
                            >
                              Attendance Visual
                            </div>
                            <section
                              class="d-flex text-primary timeline-container nowrap mt-2"
                            >
                              <AttendanceTimeline
                                :attendanceRecords="formTimelineData(item)"
                                :message="item.Message"
                              ></AttendanceTimeline>
                              <v-icon
                                v-if="
                                  item.details[0] &&
                                  item.details[0].Checkin_Latitude
                                "
                                color="primary mt-4"
                                class="fas fa-map-marker-alt"
                                size="15"
                                @click="showMap(item, $event)"
                              ></v-icon>
                            </section>
                          </td>
                          <td v-if="!isSmallTable" id="mobile-view-td">
                            <div id="mobile-header" class="font-weight-bold">
                              Effective Hours
                            </div>
                            <section class="text-body-2 text-primary">
                              <template
                                v-if="
                                  !item.Effective_Hours &&
                                  !item.Gross_Hours &&
                                  !item.Arrival
                                "
                              >
                                {{
                                  getMessageForLeaves(item) ||
                                  checkNullValue(item.Message)
                                }}
                              </template>
                              <template v-else>
                                {{
                                  formatTime(item.Effective_Hours)
                                    ? formatTime(item.Effective_Hours)
                                    : ""
                                }}
                              </template>
                            </section>
                          </td>
                          <td v-if="!isSmallTable" id="mobile-view-td">
                            <div id="mobile-header" class="font-weight-bold">
                              Gross Hours
                            </div>
                            <section class="text-body-2 text-primary">
                              {{
                                formatTime(item.Gross_Hours)
                                  ? formatTime(item.Gross_Hours)
                                  : ""
                              }}
                            </section>
                          </td>
                          <td v-if="!isSmallTable" id="mobile-view-td">
                            <div id="mobile-header" class="font-weight-bold">
                              Arrival
                            </div>
                            <section class="text-body-2 text-primary">
                              <template
                                v-if="item.Arrival?.toLowerCase() === 'late'"
                              >
                                {{ item.Arrival ? item.Arrival : "" }} -
                                {{
                                  formatTime(item.Late_By)
                                    ? formatTime(item.Late_By)
                                    : ""
                                }}
                              </template>
                              <template
                                v-if="item.Arrival?.toLowerCase() !== 'late'"
                              >
                                {{ item.Arrival ? item.Arrival : "" }}
                              </template>
                            </section>
                          </td>
                          <td
                            v-if="!isSmallTable"
                            class="text-body-2 text-center"
                            id="mobile-view-td"
                          >
                            <div
                              v-if="isMobileView"
                              id="mobile-header"
                              class="font-weight-bold d-flex justify-left align-left"
                            >
                              Actions
                            </div>
                            <section class="d-flex justify-end align-center">
                              <ActionMenu
                                @selected-action="onActions($event, item)"
                                :actions="['Add', 'Delete', 'View']"
                                :access-rights="formAccess"
                              />
                            </section>
                          </td>
                        </tr>
                      </template>

                      <v-card
                        v-if="
                          (item.showMenu &&
                            !showViewForm &&
                            !showAddEditForm &&
                            !item.Message) ||
                          (item?.Message?.toLowerCase() !==
                            'shift not scheduled' &&
                            item?.Message?.toLowerCase() !==
                              'full day week-off')
                        "
                        class="ml-10"
                        style="max-width: 250px"
                      >
                        <v-card-title>
                          <span class="d-flex align-center">
                            <h5
                              class="mr-2 mb-0 text-truncate"
                              style="max-width: 200px"
                            >
                              {{ item?.workscheduleHolidayInputs?.Title }}
                              ({{ item?.Display_Punching_Date }})
                            </h5>

                            <v-icon
                              v-if="formAccess && item.details?.length"
                              color="primary"
                              class="far fa-eye"
                              @click="viewOverlayForm(item)"
                              size="20"
                            ></v-icon>
                          </span>

                          <div class="text-subtitle-1 mt-1">
                            Shift Time :
                            {{
                              this.convertToShiftHourCard(
                                item?.workscheduleHolidayInputs?.Regular_From
                              )
                            }}
                            -
                            {{
                              this.convertToShiftHourCard(
                                item?.workscheduleHolidayInputs?.Regular_To
                              )
                            }}
                            <span
                              v-if="
                                item?.workscheduleHolidayInputs?.Twodays_Flag
                              "
                            >
                              <sup>+1</sup></span
                            >
                          </div>
                          <div class="text-subtitle-1 mt-1">
                            Shift Margin :
                            {{
                              this.convertToShiftHourCard(
                                item?.workscheduleHolidayInputs
                                  ?.Consideration_From
                              )
                            }}
                            -
                            {{
                              this.convertToShiftHourCard(
                                item?.workscheduleHolidayInputs
                                  ?.Consideration_To
                              )
                            }}
                          </div>
                        </v-card-title>
                        <v-divider></v-divider>
                        <v-card-text
                          v-if="item?.details && item.details.length > 0"
                        >
                          <h3 class="mb-2">Attendance Entries</h3>
                          <v-list dense>
                            <v-list-item
                              v-for="(logItem, index) in item.details"
                              :key="index"
                            >
                              <div class="mb-2">
                                <v-list-item-title>
                                  <v-icon
                                    color="success mr-1"
                                    class="fas fa-arrow-up"
                                    size="15"
                                    style="transform: rotate(45deg)"
                                  ></v-icon>
                                  <span class="success mr-5">{{
                                    getHourAndMinutes(
                                      logItem.Actual_Punch_In_Time
                                    )
                                  }}</span>
                                  <v-icon
                                    v-if="
                                      isTimeValid(logItem.Actual_PunchOut_Time)
                                    "
                                    color="red mr-1"
                                    class="fas fa-arrow-down"
                                    size="15"
                                    style="transform: rotate(45deg)"
                                  ></v-icon>
                                  <span
                                    v-if="
                                      isTimeValid(logItem.Actual_PunchOut_Time)
                                    "
                                    class="error"
                                    >{{
                                      getHourAndMinutes(
                                        logItem.Actual_PunchOut_Time
                                      )
                                    }}</span
                                  >
                                </v-list-item-title>
                              </div>
                            </v-list-item>
                          </v-list>
                        </v-card-text>
                      </v-card>
                    </v-menu>
                  </template>
                </v-data-table>
              </v-col>

              <v-col
                :cols="originalLogList.length === 0 ? 12 : 6"
                v-if="isSmallTable && windowWidth >= 1264"
                ><AddEditAttendance
                  v-if="showAddEditForm"
                  :isEdit="isEdit"
                  :editFormData="selectedLogItem"
                  :itemLogList="itemLogList"
                  :itemEmployeeList="itemEmployeeList"
                  :openViewOverlayForm="false"
                  :selectedEmployee="selectedEmployee"
                  :access-rights="formAccess"
                  :landedFormName="landedFormName"
                  :work-schedule-details="workScheduleDetails"
                  @close-form="closeAllForms()"
                  @form-updated="refetchList('Attendance was added/updated')"
                />
                <v-menu
                  v-if="showDetailsCard"
                  transition="scale-transition"
                  offset-y
                  :close-on-content-click="false"
                >
                  <template v-slot:activator="{ on }">
                    <div v-on="on"></div>
                  </template>
                  <v-card
                    max-height="400px"
                    min-width="400px"
                    max-width="500px"
                  >
                    <v-container>
                      <v-row>
                        <v-col cols="4"><strong>Shift Start:</strong></v-col>
                        <v-col cols="8">{{ shiftStart || "N/A" }}</v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="4"><strong>Shift End:</strong></v-col>
                        <v-col cols="8">{{ shiftEnd || "N/A" }}</v-col>
                      </v-row>
                      <v-divider></v-divider>
                      <div
                        v-for="(detail, index) in details"
                        :key="index"
                        class="pa-2"
                      >
                        <v-row>
                          <v-col cols="4"
                            ><strong>Punch In Time:</strong></v-col
                          >
                          <v-col cols="8">{{
                            detail.Display_PunchIn_Time || "N/A"
                          }}</v-col>
                        </v-row>
                        <v-row>
                          <v-col cols="4"
                            ><strong>Punch Out Time:</strong></v-col
                          >
                          <v-col cols="8">{{
                            detail.Display_PunchOut_Time || "N/A"
                          }}</v-col>
                        </v-row>
                      </div>
                    </v-container>
                  </v-card>
                </v-menu>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <v-dialog
      v-if="openFormInModal"
      :model-value="openFormInModal"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditAttendance
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedLogItem"
        :itemLogList="itemLogList"
        :itemEmployeeList="itemEmployeeList"
        :openViewOverlayForm="false"
        :selectedEmployee="selectedEmployee"
        :access-rights="formAccess"
        :landedFormName="landedFormName"
        :callingFrom="callingFrom"
        :work-schedule-details="workScheduleDetails"
        @close-form="closeAllForms()"
        @form-updated="refetchList('Attendance was added/updated')"
      ></AddEditAttendance>
      <ViewAttendance
        v-if="openViewOverlayForm"
        :isEdit="isEdit"
        :selectedEmployee="selectedEmployee"
        :itemEmployeeList="itemEmployeeList"
        :formAccess="formAccess"
        :landedFormName="landedFormName"
        :selectedLogItem="selectedLogItem"
        :employeeData="employeeData"
        :lastSalaryDate="lastSalaryDate"
        :isBeforeLastSalary="isBeforeLastSalary"
        @close-view-attendance-window="openViewOverlayForm = false"
        @refetch-data="refetchList()"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
        @on-delete-entry="onDeleteEntry($event)"
        @on-edit-entry="onEditEntry($event)"
        @on-approve-entry="onApproveEntry($event)"
        @on-reject-entry="onRejectEntry($event)"
      ></ViewAttendance>
    </v-dialog>
    <CopyToOthersOverlayForm
      v-if="openCopyToOthersOverlayForm"
      :candidateDetails="candidateDetails"
      :candidateIdSelected="candidateIdSelected"
      :candidateId="candidateDetails.Candidate_Id"
      :originalStatusList="originalStatusList"
      :candidateName="candidateDetails.First_Name"
      @close-archive-candidates-window="openCopyToOthersOverlayForm = false"
      @refetch-data="refetchList()"
    >
    </CopyToOthersOverlayForm>
    <ViewAttendance
      v-if="openViewOverlayForm && !openFormInModal"
      :isEdit="isEdit"
      :selectedEmployee="selectedEmployee"
      :formAccess="formAccess"
      :itemEmployeeList="itemEmployeeList"
      :landedFormName="landedFormName"
      :selectedLogItem="selectedLogItem"
      :employeeData="employeeData"
      :lastSalaryDate="lastSalaryDate"
      :isBeforeLastSalary="isBeforeLastSalary"
      @close-view-attendance-window="openViewOverlayForm = false"
      @refetch-data="refetchList()"
      @close-form="closeAllForms()"
      @open-edit-form="openEditForm()"
      @on-delete-entry="onDeleteEntry($event)"
      @on-edit-entry="onEditEntry($event)"
      @on-approve-entry="onApproveEntry($event)"
      @on-reject-entry="onRejectEntry($event)"
    ></ViewAttendance>
    <AttendanceMap
      v-if="openMapDialog"
      :openMapDialog="openMapDialog"
      :selectedEmployee="selectedEmployee"
      :access-rights="formAccess"
      :landedFormName="landedFormName"
      :selectedLogItem="selectedLogItem"
      :employeeData="employeeData"
      @close-map-modal="openMapDialog = false"
    />
    <AppLoading v-if="isLoading"></AppLoading
    ><AppWarningModal
      v-if="conformationModel || rejectionModel"
      :open-modal="conformationModel || rejectionModel"
      :confirmation-heading="
        conformationModel
          ? 'Are you sure you want to approve the records?'
          : 'Are you sure you want to reject the records?'
      "
      :icon-name="
        conformationModel ? 'fas fa-check-circle' : 'fas fa-times-circle'
      "
      :icon-color="conformationModel ? 'success' : 'red'"
      :icon-Size="75"
      @close-warning-modal="
        conformationModel ? closeConfirmationModal() : closeRejectionModal()
      "
      @accept-modal="
        conformationModel
          ? onMultiApproval(
              'approve',
              'hr-workflow-task-management-approve text-green'
            )
          : onMultiApproval(
              'reject',
              'hr-workflow-task-management-reject text-red'
            )
      "
    />

    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteAttendanceLog()"
    ></AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const ViewAttendance = defineAsyncComponent(() =>
  import("./ViewAttendance.vue")
);
const AddEditAttendance = defineAsyncComponent(() =>
  import("./AddEditAttendance.vue")
);
// const FormFilter = defineAsyncComponent(() => import("./AttendanceFilter.vue"));
// Async Components
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
import Datepicker from "vuejs3-datepicker";
import mixpanel from "mixpanel-browser";
import moment from "moment";
import Config from "@/config.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import CopyToOthersOverlayForm from "./CopyToOthers.vue";
import { LIST_ATTENDANCE } from "@/graphql/my-team/attendance.js";
import AttendanceTimeline from "@/components/custom-components/AttendanceTimeline.vue";
import AttendanceMap from "./AttendanceMap.vue";

export default {
  name: "EmployeeAttendance",
  components: {
    NotesCard,
    ViewAttendance,
    AddEditAttendance,
    ActionMenu,
    AttendanceTimeline,
    CopyToOthersOverlayForm,
    AttendanceMap,
    Datepicker,
  },
  props: {
    selectedItem: {
      type: Object,
    },
    callingFrom: {
      type: String,
      default: "employee",
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  mixins: [FileExportMixin],
  emits: ["close-view-form"],
  data: () => ({
    showDetailsCard: false,
    loading: false,
    listLoading: false,
    itemLogList: [],
    originalLogList: [],
    isErrorInList: false,
    errorContent: "",
    openWarningModal: false,
    openMoreMenu: false,
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    selectedLogItem: null,
    showViewForm: false,
    showTabViewForm: false,
    currentTabItem: "",
    editFormData: {},
    isImportModel: false,
    openMoreDetails: false,
    resetFilterCount: 0,
    selectedMonthYear: new Date(),
    searchData: "",
    selectedAttendanceRecords: [],
    allEmployeesList: [],
    itemEmployeeList: [],
    selectedEmployee: 0,
    conformationModel: false,
    rejectionModel: false,
    isFetchingEmployees: false,
    openCopyToOthersOverlayForm: false,
    openViewOverlayForm: false,
    candidateDetails: {},
    originalStatusList: [],
    selectedSingleLogItem: null,
    timeEntries: [],
    openMapDialog: false,
    appliedDate: null,
    employeeListMenu: false,
    searchEmployee: "",
    allEmployeesBackupList: [],
    selectedStartDate: new Date(),
    selectedEndDate: new Date(),
  }),
  computed: {
    formattedSelectedMonth() {
      return moment(this.selectedMonthYear).format("MMMM, YYYY");
    },
    disabledDates() {
      return {
        from: this.selectedEndDate,
        to: this.selectedStartDate,
      };
    },
    employeeData() {
      const employee = this.allEmployeesList.find(
        (item) => item.employeeId === this.selectedEmployee
      );
      return employee ? employee.employeeData : null;
    },
    lastSalaryDate() {
      const employee = this.allEmployeesList.find(
        (item) => item.employeeId === this.selectedEmployee
      );
      return employee ? employee.lastSalaryDate : null;
    },
    isBeforeLastSalary() {
      return (
        this.selectedLogItem &&
        moment(this.selectedLogItem?.Attendance_Date).isBefore(
          moment(this.lastSalaryDate)
        )
      );
    },
    landedFormName() {
      let attendance = this.accessRights("304");
      if (attendance && attendance.customFormName) {
        return attendance.customFormName;
      } else return "Attendance";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formId() {
      let fId = this.callingFrom === "team" ? "304" : "305";
      return parseInt(fId);
    },
    formAccess() {
      let formAccessRights = this.accessRights(this.formId);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        // Add "copy to others" action inheriting from "update" action
        formAccessRights.accessRights["copy to others"] =
          formAccessRights.accessRights["update"];

        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    isSmallTable() {
      return this.showViewForm || this.showAddEditForm;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Date",
            key: "Display_Punching_Date",
            align: "start",
          },
          {
            title: "Attendance Visual",
            key: "AttendanceVisual",
            align: "center",
          },
        ];
      } else {
        return [
          {
            title: "Date",
            key: "Display_Punching_Date",
            align: "start",
          },
          {
            title: "Attendance Visual",
            key: "AttendanceVisual",
            align: "center",
          },
          {
            title: "Effective Hours",
            key: "Effective_Hours",
          },
          {
            title: "Gross Hours",
            key: "Gross_Hours",
          },
          {
            title: "Arrival",
            key: "Late_By",
          },
          {
            title: "Actions",
            align: "end",
            sortable: false,
          },
        ];
      }
    },

    emptyScenarioMsg() {
      return this.originalLogList.length
        ? "There are no Attendance for the selected filters/searches."
        : "";
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    openFormInModal() {
      if (this.isSmallTable && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
    isTimeValid() {
      return (time) => {
        return moment(time).isValid();
      };
    },
    getMessageForLeaves() {
      return (item) => {
        let msg = [];
        if (item?.leaveCompOffObject?.totalLeaveDuration == 1) {
          msg.push("Full day Leave");
        }
        if (item?.leaveCompOffObject?.totalCompOffDuration == 1) {
          msg.push("Full day Comp Off");
        }
        if (item?.leaveCompOffObject?.totalLeaveDuration == 0.5) {
          msg.push(
            `${
              item.leaveCompOffObject.leaveDetails[0]?.Leave_Period ||
              "Half day"
            } Leave`
          );
        }
        if (item?.leaveCompOffObject?.totalCompOffDuration == 0.5) {
          msg.push(
            `${
              item.leaveCompOffObject.compensatoryOffDetails[0]?.Period ||
              "Half day"
            } Comp Off`
          );
        }
        if (item?.leaveCompOffObject?.totalLeaveDuration == 0.25) {
          msg.push(
            `${
              item.leaveCompOffObject.leaveDetails[0]?.Leave_Period ||
              "Quarter day"
            } Leave`
          );
        }
        if (item?.leaveCompOffObject?.totalCompOffDuration == 0.25) {
          msg.push(
            `${
              item.leaveCompOffObject.compensatoryOffDetails[0]?.Period ||
              "Quarter day"
            } Comp Off`
          );
        }
        return msg.join(", ");
      };
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);

    this.currentTabItem = "Attendance Log";

    // Determine the selected employee based on the route path
    if (this.callingFrom?.toLowerCase() === "employee") {
      this.selectedEmployee = this.loginEmployeeId; // Self-service case
    } else {
      this.selectedEmployee = this.selectedItem?.employeeId || null; // Default case
    }
    // Fetch the employee list
    this.getEmpList();
    this.showDetailsCard = true;
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    selectedMonthYear() {
      this.fetchList(this.selectedEmployee);
    },
    employeeListMenu(val) {
      if (val) {
        this.$nextTick(() => {
          const selectedElement = this.$refs.selectedEmployeeRef?.[0];
          const container = this.$refs.employeeListContainer;

          if (selectedElement && container) {
            container.scrollTo({
              top: selectedElement.offsetTop - container.offsetTop - 40,
            });
          }
        });
      }
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    getTooltipMessage(item) {
      const message = item?.Message?.toLowerCase();
      const isBeforeLastSalary = moment(item?.Attendance_Date).isBefore(
        moment(this.lastSalaryDate)
      );
      if (isBeforeLastSalary) {
        return "This action cannot be performed on this record once the payslip is generated.";
      } else if (
        item.leaveCompOffObject?.totalCompOffDuration +
          item.leaveCompOffObject?.totalLeaveDuration >=
        1
      ) {
        return "This action cannot be performed on this record once the leave/comp-off is approved.";
      } else if (message === "no time entries logged") {
        return "You cannot perform this action on this record with no time entries logged.";
      } else if (message === "full day week-off") {
        return "You cannot perform this action on this record for a full day week-off.";
      } else if (message === "shift not scheduled") {
        return "This action cannot be performed on an unscheduled shift record.";
      } else if (message === "attendance entries yet to be processed") {
        return "You cannot perform this action on this record with attendance entries yet to be processed.";
      }
      return "";
    },
    showMap(item, e) {
      this.selectedLogItem = item;
      this.openMapDialog = true;
      e.stopPropagation();
    },
    getHourAndMinutes(timeString) {
      // Ensure the timeString is valid
      if (!timeString || typeof timeString !== "string") return null;

      // Split the timeString by ':' and return the first two parts
      const [hours, minutes] = timeString.split(":");
      return `${hours}:${minutes}`;
    },
    convertToShiftHourCard(dateTime) {
      if (!dateTime) return null;

      const timePart = dateTime.split(" ")[1];
      if (!timePart) return null;

      const [hours, minutes] = timePart.split(":").map(Number);

      // Format hours and minutes to always have two digits
      const formattedHours = hours < 10 ? `0${hours}` : hours;
      const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

      return `${formattedHours}:${formattedMinutes}`;
    },
    formatTime(timeString) {
      if (!timeString) return "";

      const [hours, minutes] = timeString.split(":").map(Number); // Convert hours and minutes to numbers
      const formattedTime = `${hours} Hrs ${minutes} Mins`;

      return formattedTime;
    },
    onEditEntry(item) {
      this.openViewOverlayForm = false;
      this.isEdit = true;
      this.selectedLogItem = { details: item };
      this.showAddEditForm = true;
    },
    onApproveEntry(item) {
      this.selectedLogItem = item;
      this.openViewOverlayForm = false;
      this.conformationModel = true;
      this.rejectionModel = false;
    },
    onRejectEntry(item) {
      this.selectedLogItem = item;
      this.openViewOverlayForm = false;
      this.rejectionModel = true;
      this.conformationModel = false;
    },
    closeConfirmationModal() {
      this.conformationModel = false;
    },
    closeRejectionModal() {
      this.rejectionModel = false;
    },
    onDeleteEntry(item) {
      this.selectedSingleLogItem = item;
      this.openViewOverlayForm = false;
      this.openWarningModal = true;
    },
    showDetailsForm(item) {
      if (item.showMenu) {
        item.showMenu = false;
      } else {
        this.$nextTick(() => {
          item.showMenu = true;
        });
      }
    },
    closeDetailsCard() {
      this.showDetailsCard = false;
    },
    convertToShiftHour(dateTime) {
      if (!dateTime) return null;

      const timePart = dateTime.split(" ")[1];
      if (!timePart) return null;

      const [hours, minutes] = timePart.split(":").map(Number);

      return hours + minutes / 60;
    },
    convertToHour(dateTime) {
      if (!dateTime) return null;

      const timePart = dateTime.split(" ")[0]; // Extract the time part
      if (!timePart) return null;

      const [hours, minutes] = timePart.split(":").map(Number); // Split time and convert to numbers
      return hours + minutes / 60; // Convert to decimal hours
    },
    formTimelineData(item) {
      if (!item || typeof item !== "object") {
        return [
          {
            id: null,
            date: null,
            shiftStart: null,
            shiftEnd: null,
            segments: [],
          },
        ];
      }

      const workSchedule = item.workscheduleHolidayInputs || {};
      let details = item.details || [];
      details = details.filter(
        (detail) => detail?.Approval_Status?.toLowerCase() !== "rejected"
      );
      const segments = details.map((detail) => ({
        id: detail?.Attendance_Id || null,
        start: this.convertToHour(detail?.Display_PunchIn_Time) || 0,
        end: detail?.PunchOut_Date
          ? this.convertToHour(detail?.Display_PunchOut_Time)
          : null,
        showTooltip: true,
      }));
      const safeSegments = segments.length
        ? segments
        : [
            {
              id: null,
              start: 0,
              end: 0,
              showTooltip: false,
            },
          ];

      const attendanceRecord = {
        id: item.User_Defined_EmpId || null,
        date: item.Attendance_Date || null,
        shiftStart: this.convertToShiftHour(workSchedule?.Regular_From) || 0,
        shiftEnd: this.convertToShiftHour(workSchedule?.Regular_To) || 0,
        segments: [...safeSegments],
        considerationCheckin: Math.max(
          this.convertToShiftHour(workSchedule?.Consideration_From)
            ? this.convertToShiftHour(workSchedule?.Consideration_From) - 1
            : 0,
          0
        ),
        considerationCheckout: this.convertToShiftHour(
          workSchedule?.Consideration_To
        )
          ? this.convertToShiftHour(workSchedule?.Consideration_To)
          : 0,
      };
      return [attendanceRecord];
    },
    onSearchEmployee(emp) {
      if (emp) {
        let filterList = this.allEmployeesBackupList.filter((item) => {
          if (item.employeeData.toLowerCase().includes(emp)) return item;
        });
        this.allEmployeesList = [...filterList];
      } else {
        this.allEmployeesList = [...this.allEmployeesBackupList];
      }
    },
    onChangeEmployee(emp) {
      this.employeeListMenu = false;
      this.allEmployeesList = [...this.allEmployeesBackupList];
      this.searchEmployee = "";
      this.selectedEmployee = emp.employeeId;
      this.employeeListMenu = false;
      if (emp) {
        this.fetchList(emp.employeeId);
      }
    },

    async onMultiApproval(selectedAction) {
      let vm = this;
      vm.isLoading = true;
      let attendanceIds = [];
      if (vm.selectedLogItem) {
        attendanceIds = [vm.selectedLogItem.Attendance_Id];
      }
      const newStatus =
        selectedAction.toLowerCase() === "approve" ? "Approved" : "Rejected";

      const formData = {
        attendanceIdArr: attendanceIds,
        status: newStatus,
        comments:
          selectedAction.toLowerCase() === "approve"
            ? "Approved successfully."
            : "Rejected due to issues.",
        isAction: "StatusUpdate",
      };

      this.isLoading = true;
      const apiObj = {
        url: vm.baseUrl + "employees/attendance/status-multi-approval/",
        type: "POST",
        dataType: "json",
        data: formData,
      };

      try {
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );

        if (response?.success) {
          this.showAlert({
            isOpen: true,
            type: "success",
            message: "Attendance status updated successfully.",
          });

          this.conformationModel = false;
          this.rejectionModel = false;
          this.refetchList("Attendance status updated");
        } else {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please try after some time.",
          });
        }
      } catch (err) {
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: err?.response?.data?.msg
            ? err.response.data.msg
            : "An error occurred while updating the attendance status. Please try again later.",
        });
      } finally {
        this.isLoading = false;
      }
    },
    async getEmpList() {
      let formId = this.formId;
      this.isFetchingEmployees = true;
      this.isLoading = true;

      await this.$store
        .dispatch("getEmployeesList", {
          formName: "Attendance",
          formId: formId,
          flag: "payslipreq",
        })
        .then((empData) => {
          this.allEmployeesList = empData.map((item) => ({
            ...item,
            employeeData: item.employeeName + " - " + item.userDefinedEmpId,
            lastSalaryDate: item.Last_SalaryDate,
          }));
          this.allEmployeesBackupList = [...this.allEmployeesList];
          if (this.$route.path === "/employee-self-service/attendance") {
            this.selectedEmployee = this.loginEmployeeId; // Self-service case
          } else {
            this.selectedEmployee = this.selectedItem?.employeeId || null; // Default case
          }
          let currentEmployee = this.allEmployeesList.find(
            (item) => item.employeeId === this.selectedEmployee
          );
          let dateOfJoin = moment(currentEmployee?.dateOfJoin).isValid()
            ? moment(currentEmployee.dateOfJoin)
            : "";
          let resignationDate = moment(
            currentEmployee?.resignationDate
          ).isValid()
            ? moment(currentEmployee.resignationDate)
            : "";
          let previousYear = moment().subtract(1, "year").startOf("year");
          let nextMonth = moment().add(1, "months").endOf("month");
          this.selectedStartDate =
            dateOfJoin && dateOfJoin.isAfter(previousYear)
              ? dateOfJoin.clone().startOf("month").toDate()
              : previousYear.toDate();
          if (!resignationDate) {
            this.selectedEndDate = nextMonth.toDate();
          } else {
            this.selectedEndDate = resignationDate.isAfter(nextMonth)
              ? nextMonth.toDate()
              : resignationDate.toDate();
          }

          if (resignationDate) {
            this.selectedMonthYear = moment().isAfter(resignationDate)
              ? resignationDate.toDate()
              : moment().toDate();
          } else {
            this.selectedMonthYear = moment().toDate();
          }
        })
        .catch((err) => {
          let snackbarData = {
            isOpen: true,
            message: "",
            type: "warning",
          };
          if (err === "error") {
            snackbarData.message =
              "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
          } else {
            snackbarData.message = err;
          }
          this.showAlert(snackbarData);
          this.selectedMonthYear = moment().toDate();
        })
        .finally(() => {
          // Ensure these flags are reset regardless of success or failure
          this.isFetchingEmployees = false;
          this.isLoading = false;
          this.fetchList(this.selectedEmployee);
        });
    },
    fetchList(empId) {
      let vm = this;
      let formId = vm.formId;
      vm.listLoading = true;
      let date = moment(vm.selectedMonthYear);
      let month = parseInt(date.format("M"));
      let year = parseInt(date.format("YYYY"));

      vm.$apollo
        .query({
          query: LIST_ATTENDANCE,
          client: "apolloClientAC",
          variables: {
            employeeId: empId,
            formId: formId,
            attendanceMonth: month,
            attendanceYear: year,
            flag: "fromatlogs",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listAttendance &&
            (response.data.listAttendance.attendanceDetails ||
              response.data.listAttendance.employeeDetails)
          ) {
            let attendanceDetails =
              response.data.listAttendance.attendanceDetails;
            let employeeDetails = response.data.listAttendance.employeeDetails;

            vm.itemLogList = attendanceDetails;
            vm.itemEmployeeList = employeeDetails;
            vm.originalLogList = attendanceDetails;

            vm.listLoading = false;

            mixpanel.track("Attendance data retrieved");
          } else {
            vm.handleAttendanceError();
          }
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleAttendanceError(err);
        });
    },

    handleAttendanceError(err = "") {
      mixpanel.track("Attendance error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Attendance",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    onDeleteAttendanceLog() {
      let vm = this;
      vm.isLoading = true;
      let attendanceIds = null;
      if (vm.selectedSingleLogItem) {
        attendanceIds = [vm.selectedSingleLogItem.Attendance_Id];
      } else if (vm.selectedLogItem) {
        attendanceIds = vm.selectedLogItem.details.map(
          (item) => item.Attendance_Id
        );
      }
      const apiObj = {
        url: vm.baseUrl + "employees/attendance/delete-attendance/",
        type: "POST",
        async: false,
        dataType: "json",
        data: {
          attendanceId: attendanceIds,
          attendanceImportExist: 0,
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.success) {
            vm.isLoading = false;

            let snackbarData = {
              isOpen: true,
              type: "success",
              message: res.msg
                ? res.msg
                : "Attendance record deleted successfully.",
            };
            vm.showAlert(snackbarData);
            vm.refetchList("Attendance record deleted");
          } else {
            vm.isLoading = false;
            vm.openWarningModal = false;
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message:
                res?.msg && res?.msg?.length
                  ? res?.msg
                  : "Something went wrong while deleting the attendance record. Please try after some time.",
            };
            vm.showAlert(snackbarData);
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleDeleteError(err);
        });
    },
    handleDeleteError(err = "") {
      this.openWarningModal = false;

      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "delete",
        form: this.landedFormName,
        isListError: false,
      });
    },
    onApplySearch(val) {
      if (!val) {
        this.itemLogList = this.originalLogList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.itemLogList;

        searchItems = searchItems.filter((item) => {
          let newObj = {
            Display_Punching_Date: item.Display_Punching_Date,
            Effective_Hours: item.Effective_Hours,
            Gross_Hours: item.Gross_Hours,
            Arrival: item.Arrival,
          };

          return Object.keys(newObj).some((k) => {
            if (newObj[k]) {
              let fieldValue = newObj[k].toString().toLowerCase();
              return fieldValue.includes(searchValue);
            }
            return false;
          });
        });

        this.itemLogList = searchItems;
      }
    },
    applyFilter(filteredArray) {
      this.itemLogList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    resetFilter() {
      this.itemLogList = this.originalLogList;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef?.resetAllModelValues();
    },

    openEditForm() {
      mixpanel.track("Attendance Log edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showTabViewForm = false;
      this.showAddEditForm = true;
      this.openViewOverlayForm = false;
    },

    openViewForm() {
      mixpanel.track("Attendance Log view form opened");
      this.showTabViewForm = true;
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.openViewOverlayForm = false;
    },

    openAddForm(item) {
      mixpanel.track("Attendance Log add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showTabViewForm = false;
      this.showAddEditForm = true;
      this.openViewOverlayForm = false;
      this.selectedLogItem = item;
      this.workScheduleDetails = item.workscheduleHolidayInputs;
    },

    closeAllForms() {
      mixpanel.track("Attendance Log all forms closed");
      this.showAddEditForm = false;
      this.openViewOverlayForm = false;
      this.showViewForm = false;
      this.showTabViewForm = false;
      this.selectedLogItem = null;
      this.isEdit = false;
      this.openWarningModal = false;
    },
    goBackToList() {
      this.$emit("close-view-form");
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();

      if (this.selectedEmployee) {
        this.fetchList(this.selectedEmployee);
      } else {
        this.selectedEmployee = this.loginEmployeeId;
        this.fetchList(this.loginEmployeeId);
      }

      this.resetFilter();
    },

    openStatusApprovalModal() {
      this.isImportModel = true;
    },
    closeStatusApprovalModal() {
      this.isImportModel = false;
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        mixpanel.track("Employee-Type-export-click");
        this.exportReportFile();
      } else if (actionType === "Status Approval") {
        this.openStatusApprovalModal();
      }
      this.openMoreMenu = false;
    },
    onActions(type, item) {
      const msg = item?.Message?.toLowerCase();
      const isBeforeLastSalary = moment(item?.Attendance_Date).isBefore(
        moment(this.lastSalaryDate)
      );
      if (msg === "shift not scheduled") {
        this.presentWarning(item);
      } else if (
        msg === "no time entries logged" ||
        msg === "full day week-off" ||
        msg === "attendance entries yet to be processed"
      ) {
        if (isBeforeLastSalary) {
          this.presentWarning(item);
        } else if (
          item.leaveCompOffObject?.totalCompOffDuration +
            item.leaveCompOffObject?.totalLeaveDuration >=
          1
        ) {
          this.presentWarning(item);
        } else if (
          type?.toLowerCase() === "view" ||
          type?.toLowerCase() === "delete"
        ) {
          this.presentWarning(item);
        } else if (type?.toLowerCase() === "add") {
          this.openAddForm(item);
        }
      } else {
        if (
          isBeforeLastSalary &&
          (type?.toLowerCase() === "add" || type?.toLowerCase() === "delete")
        ) {
          this.presentWarning(item);
        } else if (type && type.toLowerCase() === "delete") {
          this.onDelete(item);
        } else if (type && type.toLowerCase() === "add") {
          this.openAddForm(item);
        } else if (type && type.toLowerCase() === "edit") {
          this.onEdit(item);
        } else if (type && type.toLowerCase() === "view") {
          this.viewOverlayForm(item);
        } else if (type && type.toLowerCase() === "copy to others") {
          this.selectedCandidateId = item.Candidate_Id;
          this.candidateDetails = item;
          this.copyToOthers(item);
        }
      }
    },

    presentWarning(item) {
      let snackbarData = {
        isOpen: true,
        message:
          this.getTooltipMessage(item) ||
          "This action cannot be performed on this record.",
        type: "warning",
      };
      this.showAlert(snackbarData);
    },

    onEdit(item) {
      this.selectedLogItem = item;
      this.workScheduleDetails = item.workscheduleHolidayInputs;
      this.openEditForm();
    },
    onDelete(item) {
      this.selectedLogItem = item;
      this.openViewOverlayForm = false;
      this.openWarningModal = true;
    },
    onView(item) {
      this.selectedLogItem = item;
      this.openViewForm();
    },
    onBulkCopy(item) {
      this.selectedLogItem = item;
      this.openBulkCopyModal = true;
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedLogItem = null;
    },
    exportReportFile() {
      const selectedEmployee = this.allEmployeesList.find(
        (e) => e.employeeId === this.selectedEmployee
      );

      const exportHeaders = [
        { header: "Employee Id", key: "employeeId" },
        { header: "Employee Name", key: "employeeName" },
        { header: "Check In Date", key: "checkInDate" },
        { header: "Check In Time", key: "checkInTime" },
        { header: "Work Schedule", key: "workSchedule" },
        { header: "Effective Hours", key: "effectiveHours" },
        { header: "Break Hours", key: "breakHours" },
        { header: "Actual Check In Time", key: "actualCheckInTime" },
        { header: "Check In Work Place", key: "checkInWorkPlace" },
        { header: "Check Out Date", key: "checkOutDate" },
        { header: "Check Out Time", key: "checkOutTime" },
        { header: "Actual Check Out Time", key: "actualCheckOutTime" },
        { header: "Check Out Work Place", key: "checkOutWorkPlace" },
        { header: "Gross Hours", key: "totalHours" },
        { header: "Late Arrival", key: "lateAttendance" },
        { header: "Late Arrival Hours", key: "lateAttendanceHours" },
        { header: "Actual Hours", key: "actualTotalHours" },
        { header: "Approval Status", key: "approvalStatus" },
        { header: "Check In Geo Coordinates", key: "checkInGeoCoordinates" },
        { header: "Check Out Geo Coordinates", key: "checkOutGeoCoordinates" },
        { header: "Check In Data Source", key: "checkInDataSource" },
        { header: "Check Out Data Source", key: "checkOutDataSource" },
        { header: "Check In Form Source", key: "checkInFormSource" },
        { header: "Check Out Form Source", key: "checkOutFormSource" },
        { header: "Reporting Manager", key: "reportingManager" },
        { header: "Approval Request Forwarded to", key: "forwardTo" },
        { header: "Auto Short Time off(Permissions)", key: "shortTimeOff" },
        { header: "Approved By", key: "approvedBy" },
        { header: "Approved On", key: "approvedOn" },
        { header: "Added By", key: "addedByName" },
        { header: "Added On", key: "addedOn" },
        { header: "Updated By", key: "updatedByName" },
        { header: "Updated On", key: "updatedOn" },
      ];

      const exportList = this.itemLogList
        .map((item) => {
          const { details, workscheduleHolidayInputs } = item;
          const detail = details && details.length > 0 ? details[0] : null;

          if (detail) {
            const {
              Attendance_Id,
              Approver_Name,
              PunchIn_Date,
              Display_PunchIn_Time,
              Actual_Punch_In_Time,
              Checkin_Work_Place,
              PunchOut_Date,
              Display_PunchOut_Time,
              Actual_PunchOut_Time,
              Checkout_Work_Place,
              Display_Late_Attendance,
              Late_Attendance_Time,
              Actual_Total_Hours,
              Approval_Status,
              Checkin_Longitude,
              Checkin_Latitude,
              Checkout_Longitude,
              Checkout_Latitude,
              Checkin_Data_Source,
              Checkout_Data_Source,
              Checkin_Form_Source,
              Checkout_Form_Source,
              Approved_By_Name,
              Approved_On,
              Added_By_Name,
              Added_On,
              Updated_By_Name,
              Updated_On,
            } = detail;

            return {
              attendanceId: Attendance_Id || "",
              employeeId: selectedEmployee?.employeeId || "",
              employeeName: selectedEmployee?.employeeName || "",
              reportingManager: selectedEmployee?.reportingManager || "",
              forwardTo: Approver_Name || "",
              checkInDate: PunchIn_Date || "",
              checkInTime: Display_PunchIn_Time || "",
              effectiveHours:
                parseFloat(selectedEmployee?.Effective_Hours) || 0,
              breakHours: parseFloat(selectedEmployee?.Break_Hours) || 0,
              totalHours: parseFloat(selectedEmployee?.Gross_Hours) || 0,
              actualCheckInTime: Actual_Punch_In_Time || "",
              checkInWorkPlace: Checkin_Work_Place || "",
              checkOutDate: PunchOut_Date || "",
              checkOutTime: Display_PunchOut_Time || "",
              actualCheckOutTime: Actual_PunchOut_Time || "",
              checkOutWorkPlace: Checkout_Work_Place || "",
              lateAttendance: Display_Late_Attendance || "",
              lateAttendanceHours: Late_Attendance_Time || "",
              actualTotalHours: parseFloat(Actual_Total_Hours) || 0,
              approvalStatus: Approval_Status || "",
              checkInGeoCoordinates:
                `${Checkin_Longitude},${Checkin_Latitude}` || "",
              checkOutGeoCoordinates:
                `${Checkout_Longitude},${Checkout_Latitude}` || "",
              checkInDataSource: Checkin_Data_Source || "",
              checkOutDataSource: Checkout_Data_Source || "",
              checkInFormSource: Checkin_Form_Source || "",
              checkOutFormSource: Checkout_Form_Source || "",
              approvedBy: Approved_By_Name || "",
              approvedOn: moment(Approved_On).isValid()
                ? this.convertUTCToLocal(Approved_On)
                : null,
              addedByName: Added_By_Name || "",
              addedOn: moment(Added_On).isValid()
                ? this.convertUTCToLocal(Added_On)
                : null,
              updatedByName: Updated_By_Name || "",
              updatedOn: moment(Updated_On).isValid()
                ? this.convertUTCToLocal(Updated_On)
                : null,
              workSchedule: workscheduleHolidayInputs?.Title || "",
            };
          }
          return null;
        })
        .filter((entry) => entry !== null);

      const exportOptions = {
        fileExportData: exportList,
        fileName: "Attendance Report",
        sheetName: "Attendance Details",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
      mixpanel.track("Attendance-Details-exported");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    copyToOthers(item) {
      this.selectedLogItem = item;
      this.openCopyToOthersOverlayForm = true;
    },
    viewOverlayForm(item) {
      this.selectedLogItem = item;
      this.workScheduleDetails = item.workscheduleHolidayInputs;
      this.openViewOverlayForm = true;
      item.showMenu = false;
    },
  },
};
</script>

<style scoped>
.attendance-log {
  padding: 0em 2em 0em 2em;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

.notification-bar {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-badge {
  background-color: #ff6f61;
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  font-weight: bold;
  margin-right: 10px;
}

.v-data-table >>> tbody tr {
  cursor: pointer;
}

.v-data-table >>> tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.selected-row {
  background-color: rgba(0, 0, 0, 0.08) !important;
}

.relative-cell {
  position: relative;
}
.no-height-cell {
  max-height: 0;
  min-height: 0;
  height: 0;
  overflow: hidden;
  padding: 0; /* Optional: Remove padding if necessary */
}
.timeline-container {
  flex-grow: 1; /* Make AttendanceTimeline take up available space */
}

@media screen and (max-width: 805px) {
  .attendance-log {
    padding: 2em 1em 0em 1em;
  }
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__value) {
  min-width: 160px;
  display: flex;
  align-items: center;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__calendar) {
  right: 1px;
}
:deep(.employee-list-btn > .v-btn__content) {
  max-width: 100%;
  white-space: wrap;
}
</style>
