import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_INVITED_INDIVIDUALS = gql`
  query listInvitedIndividuals {
    listInvitedIndividuals {
      errorCode
      message
      listIndividuals
    }
  }
`;
export const LIST_CANDIDATE_INVITED_INDIVIDUALS = gql`
  query listCandidateInvitedIndividuals {
    listCandidateInvitedIndividuals {
      errorCode
      message
      listIndividuals
    }
  }
`;
export const RESEND_CANDIDATE_INVITE = gql`
  query triggerInviteCandidate($candidates: [Int]!, $formId: Int!) {
    triggerInviteCandidate(candidates: $candidates, formId: $formId) {
      errorCode
      message
    }
  }
`;
export const RETRIEVE_SECONDLINE_MANAGER = gql`
  query retrieveManagerDetails($employeeId: Int) {
    retrieveManagerDetails(employeeId: $employeeId) {
      errorCode
      message
      managerId
      managerName
    }
  }
`;
export const RETRIEVE_LOCATION_BASED_ON_DESIGNATION = gql`
  query getLocationByDesignation($formId: Int!, $designationId: Int!) {
    getLocationByDesignation(formId: $formId, designationId: $designationId) {
      errorCode
      message
      locationId
    }
  }
`;
export const RETRIEVE_DOCUMENTS_TAG = gql`
  query listdocumentEnforcementGroup {
    listdocumentEnforcementGroup {
      errorCode
      message
      groupIds {
        Group_Id
        Group_Name
      }
    }
  }
`;
export const LIST_JOB_ROLES = gql`
  query listJobRoles(
    $formId: Int
    $designationId: Int
    $accreditationCategoryTypeId: Int
  ) {
    listJobRoles(
      formId: $formId
      designationId: $designationId
      accreditationCategoryTypeId: $accreditationCategoryTypeId
    ) {
      errorCode
      message
      jobRoles
    }
  }
`;
// ===============
// Mutations
// ===============
export const ADD_CANDIDATE_INVITE_ONBOARDING = gql`
  mutation addCandidateInviteOnboard(
    $Expire_Value: Int!
    $Expire_Type: Int!
    $Department_Id: Int!
    $Designation_Id: Int!
    $Location_Id: Int!
    $Date_Of_Join: String!
    $Job_Code: String!
    $Probation_Date: String
    $EmpType_Id: Int!
    $Manager_Id: Int!
    $Work_Schedule: Int!
    $Service_Provider_Id: Int!
    $Business_Unit_Id: Int!
    $Name: String!
    $MailTo: String!
    $candidateId: Int!
    $organizationGroupId: Int
    $groupIds: [Int]
    $accreditationGroupIds: [Int]
  ) {
    addCandidateInviteOnboard(
      Expire_Value: $Expire_Value
      Expire_Type: $Expire_Type
      Department_Id: $Department_Id
      Designation_Id: $Designation_Id
      Location_Id: $Location_Id
      Date_Of_Join: $Date_Of_Join
      Job_Code: $Job_Code
      Probation_Date: $Probation_Date
      EmpType_Id: $EmpType_Id
      Manager_Id: $Manager_Id
      Work_Schedule: $Work_Schedule
      Service_Provider_Id: $Service_Provider_Id
      Business_Unit_Id: $Business_Unit_Id
      Name: $Name
      MailTo: $MailTo
      candidateId: $candidateId
      organizationGroupId: $organizationGroupId
      groupIds: $groupIds
      accreditationGroupIds: $accreditationGroupIds
    ) {
      errorCode
      message
      __typename
    }
    __typename
  }
`;
