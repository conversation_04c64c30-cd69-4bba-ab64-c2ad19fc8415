<template>
  <v-menu
    location="bottom"
    :open-on-hover="!isMobileView"
    min-width="150"
    z-index="10000"
    :close-on-content-click="false"
  >
    <template v-slot:activator="{ props }">
      <v-badge
        bordered
        class="mr-sm-4 ml-2"
        color="primary"
        :content="notificationCount"
        v-bind="props"
      >
        <div style="height: 30px">
          <v-icon
            class="mt-2"
            color="primary"
            style="font-size: 20px; cursor: pointer"
          >
            fas fa-bell
          </v-icon>
        </div>
      </v-badge>
    </template>
    <v-list
      ><div v-if="notificationLoading || leaveNotificationLoading">loading</div>
      <div v-else>
        <div v-if="errorInNotification" class="pa-4 mt-4 d-flex flex-column">
          <img
            style="width: 40%; height: 50%"
            :src="getNotificationImage"
            alt="notification error image"
            class="mx-auto"
          />
          <div class="pa-3 text-center">
            {{ $t("authLayout.technicalDifficulties") }}
          </div>
          <v-btn
            id="refresh_notifications"
            size="small"
            dense
            rounded="lg"
            color="secondary"
            class="mx-auto d-flex mt-2"
            @click="fnRefetchNotifications()"
          >
            <v-icon class="mr-1" style="font-size: 14px">refresh</v-icon>
            <span class="font-weight-bold" style="font-size: 10px">{{
              $t("authLayout.refresh")
            }}</span>
          </v-btn>
        </div>
        <div v-else-if="parseInt(notificationCount) > 0">
          <div>
            <v-icon color="secondary" size="20">trip_origin</v-icon>
            <span class="text-body-1 text-primary font-weight-bold">{{
              $t("authLayout.notificationCount", { count: notificationCount })
            }}</span>
          </div>
          <div style="overflow-x: hidden; max-height: 300px" class="px-3">
            <ListActionCard
              v-for="(notification, j) in notificationList"
              :key="j + '-notification'"
              :card-property="cardProperty[j % 2]"
              :title="notification.content ? notification.content : ''"
              :list-index="j"
              :is-clickable="true"
              :icon-name="notification.icon ? notification.icon : 'fas fa-bell'"
              style="margin-top: 10px"
              called-from="notification"
              @action-triggered="fnRedirectActions($event)"
            ></ListActionCard>
          </div>
        </div>
        <div v-else class="pa-4 mt-4 d-flex flex-column">
          <div class="pa-3 text-center text-primary font-weight-bold">
            {{ $t("authLayout.caughtUpNotifications") }}
          </div>
          <img
            style="width: 40%; height: 50%"
            :src="getNotificationImage"
            alt="notification empty image"
            class="mx-auto my-4"
          />
          <div class="pa-3 mt-4 text-center">
            {{ $t("authLayout.newContentNotification") }}
          </div>
        </div>
      </div>
    </v-list>
  </v-menu>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
// components
const ListActionCard = defineAsyncComponent(() =>
  import("@/components/helper-components/ListActionCard")
);
// queries
import { LIST_NOTIFICATIONS } from "@/graphql/layout/layoutQueries";
import {
  GET_EMPLOYEE_GROUP_IDS,
  GET_SERVICE_PROVIDER_EMPLOYEES,
} from "@/graphql/workflow/approvalManagementQueries.js";

import Config from "../config.js";
import axios from "axios";

export default defineComponent({
  name: "LayoutNotification",
  components: { ListActionCard },

  data: () => ({
    errorInNotification: false,
    otherNotificationList: [],
    leaveNotificationList: [],
    notificationCount: "0",
    cardProperty: [
      {
        style: "background: #FFFFFF",
        bg: "primary",
        color: "primary",
      },
      {
        style: "background: #FFFFFF",
        bg: "blue-grey-lighten-4",
        color: "blue-grey-lighten-2",
      },
    ],
    leaveNotificationLoading: false,
    enableWorkflow: "No",
    reimbursementWorkflowEnabled: "No",
    groupId: [],
    serviceProviderEmployees: ["-"],
    apiCallCount: 0,
  }),

  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    // get image url based on scenarios
    getNotificationImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/notification-icon.webp");
      else return require("@/assets/images/layout/notification-icon.png");
    },

    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // returns baseurl of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    // loading when the notification is fetch
    notificationLoading() {
      return this.$apollo.queries.listNotificationsInDashboard.loading;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    isServiceProviderAdmin() {
      let formAccess = this.accessRights("approval-management");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights.admin === "admin"
      ) {
        let serviceProviderAdminAccess = this.accessRights(
          "service-provider-admin"
        );
        if (
          serviceProviderAdminAccess &&
          serviceProviderAdminAccess.accessRights &&
          serviceProviderAdminAccess.accessRights.update
        ) {
          return true;
        }
      }
      return false;
    },
    statusApprovalCount() {
      return this.$store.state.currentApprovalCount;
    },
    notificationList() {
      return this.otherNotificationList.concat(this.leaveNotificationList);
    },
  },
  apollo: {
    // list notifications query
    listNotificationsInDashboard: {
      query: LIST_NOTIFICATIONS,
      client: "apolloClientC",

      result({ data }) {
        try {
          // check data is empty or not (when error, data is returned as empty)
          if (Object.keys(data).length !== 0) {
            if (data.listNotificationsInDashboard) {
              const {
                leaveWorkflowEnabled,
                reimbursementWorkflowEnabled,
                notificationList,
                notificationCount,
              } = data.listNotificationsInDashboard;
              this.enableWorkflow = leaveWorkflowEnabled;
              this.reimbursementWorkflowEnabled = reimbursementWorkflowEnabled;
              let notificationDetails = notificationList
                ? JSON.parse(notificationList)
                : {};
              this.notificationCount = notificationCount.toString();
              let list = Object.keys(notificationDetails);
              let formattedList = []; // form notification structure.
              let notificationRedirection = "Yes";
              let salaryMonth = "";
              let salaryYear = "";
              let params =
                "salaryMonth=" +
                salaryMonth +
                "&salaryYear=" +
                salaryYear +
                "&notificationRedirection=" +
                notificationRedirection;
              let encodedParams = btoa(params);
              for (var i in list) {
                if (list[i] !== "Leaves" || this.enableWorkflow === "No") {
                  switch (
                    list[i] // Only count is returned form backend. UI need to form icon, url and content(based on count)
                  ) {
                    case "attendance":
                      formattedList.push({
                        url: this.baseUrl + "employees/attendance",
                        icon: "fas fa-user-clock",
                        content: this.$t("authLayout.attendanceApproval", {
                          count: notificationDetails["attendance"],
                        }),
                      });
                      break;
                    case "compensatoryOff":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=334",
                        icon: "far fa-clock",
                        content: this.$t("authLayout.compensatoryOffApproval", {
                          count: notificationDetails["compensatoryOff"],
                        }),
                      });
                      break;
                    case "leaves":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=31",
                        icon: "far fa-clock",
                        content: this.$t("authLayout.leaveApproval", {
                          count: notificationDetails["leaves"],
                        }),
                      });
                      break;
                    case "resignation":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=34",
                        icon: "fas fa-user-minus",
                        content: this.$t("authLayout.resignationApproval", {
                          count: notificationDetails["resignation"],
                        }),
                      });
                      break;
                    case "shortTimeOff":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=352",
                        icon: "far fa-clock",
                        content: this.$t("authLayout.shortTimeOffApproval", {
                          count: notificationDetails["shortTimeOff"],
                        }),
                      });
                      break;
                    case "timesheet":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=268",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.timesheetApproval", {
                          count: notificationDetails["timesheet"],
                        }),
                      });
                      break;
                    case "lopRecovery":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=253",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.lopRecoveryApproval", {
                          count: notificationDetails["lopRecovery"],
                        }),
                      });
                      break;
                    case "onDutyPreApprovals":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=301",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.onDutyPreApprovals", {
                          count: notificationDetails["onDutyPreApprovals"],
                        }),
                      });
                      break;
                    case "workFromHome":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=244",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.workFromHomeApproval", {
                          count: notificationDetails["workFromHome"],
                        }),
                      });
                      break;
                    case "workDuringWeekOff":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=245",
                        icon: "far fa-calendar-alt",
                        content: this.$t(
                          "authLayout.workDuringWeekOffApproval",
                          {
                            count: notificationDetails["workDuringWeekOff"],
                          }
                        ),
                      });
                      break;
                    case "workDuringHoliday":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=246",
                        icon: "far fa-calendar-alt",
                        content: this.$t(
                          "authLayout.workDuringHolidayApproval",
                          {
                            count: notificationDetails["workDuringHoliday"],
                          }
                        ),
                      });
                      break;
                    case "jobPost":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=15",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.jobPostApproval", {
                          count: notificationDetails["jobPost"],
                        }),
                      });
                      break;
                    case "newPosition":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=290",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.newPositionApproval", {
                          count: notificationDetails["newPosition"],
                        }),
                      });
                      break;
                    case "recruitmentRequest":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=291",
                        icon: "far fa-calendar-alt",
                        content: this.$t(
                          "authLayout.recruitmentRequestApproval",
                          {
                            count: notificationDetails["recruitmentRequest"],
                          }
                        ),
                      });
                      break;
                    case "myProfile":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=18",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.myprofileApproval", {
                          count: notificationDetails["myProfile"],
                        }),
                      });
                      break;
                    case "teamSummary":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=243",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.teamsummaryApproval", {
                          count: notificationDetails["teamSummary"],
                        }),
                      });
                      break;
                    case "empSalaryRevision":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          "v3/approvals/approval-management?form_id=360",
                        icon: "far fa-calendar-alt",
                        content: this.$t("authLayout.salaryRevisionApproval", {
                          count: notificationDetails["empSalaryRevision"],
                        }),
                      });
                      break;
                    case "transfer":
                      formattedList.push({
                        url: this.baseUrl + "employees/transfer",
                        icon: "fas fa-exchange-alt",
                        content: this.$t("authLayout.transferApproval", {
                          count: notificationDetails["transfer"],
                        }),
                      });
                      break;
                    case "travel":
                      formattedList.push({
                        url: this.baseUrl + "employees/employee-travel",
                        icon: "fas fa-car-side",
                        content: this.$t("authLayout.travelApproval", {
                          count: notificationDetails["travel"],
                        }),
                      });
                      break;
                    case "advanceSalary":
                      formattedList.push({
                        url: this.baseUrl + "payroll/advance-salary",
                        icon: "fas fa-money-check-alt",
                        content: this.$t("authLayout.advanceSalaryApproval", {
                          count: notificationDetails["advanceSalary"],
                        }),
                      });
                      break;
                    case "bonus":
                      formattedList.push({
                        url: this.baseUrl + "payroll/bonus",
                        icon: "fas fa-money-bill-wave",
                        content: this.$t("authLayout.bonusApproval", {
                          count: notificationDetails["bonus"],
                        }),
                      });
                      break;
                    case "commission":
                      formattedList.push({
                        url: this.baseUrl + "payroll/commission",
                        icon: "fas fa-hand-holding-usd",
                        content: this.$t("authLayout.commissionApproval", {
                          count: notificationDetails["commission"],
                        }),
                      });
                      break;
                    case "deduction":
                      formattedList.push({
                        url: this.baseUrl + "payroll/deductions",
                        icon: "fas fa-cut",
                        content: this.$t("authLayout.deductionApproval", {
                          count: notificationDetails["deduction"],
                        }),
                      });
                      break;
                    case "financialClosure":
                      formattedList.push({
                        url: this.baseUrl + "payroll/tax-rules",
                        icon: "fas fa-money-check-alt",
                        content: this.$t("authLayout.financialClosure"),
                      });
                      break;
                    case "loan":
                      formattedList.push({
                        url: this.baseUrl + "payroll/loan",
                        icon: "fas fa-landmark",
                        content: this.$t("authLayout.loanApproval", {
                          count: notificationDetails["loan"],
                        }),
                      });
                      break;
                    case "deferredLoan":
                      formattedList.push({
                        url: this.baseUrl + "payroll/loan",
                        icon: "fas fa-landmark",
                        content: this.$t("authLayout.deferredLoanApproval", {
                          count: notificationDetails["deferredLoan"],
                        }),
                      });
                      break;
                    case "shiftAllowance":
                      formattedList.push({
                        url: this.baseUrl + "payroll/shift-allowance",
                        icon: "fas fa-money-bill",
                        content: this.$t("authLayout.shiftAllowanceApproval", {
                          count: notificationDetails["shiftAllowance"],
                        }),
                      });
                      break;
                    case "taxDeclaration":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          `payroll/tax-declarations?data=${encodedParams}`,
                        icon: "far fa-chart-bar",
                        content: this.$t("authLayout.taxDeclarationApproval", {
                          count: notificationDetails["taxDeclaration"],
                        }),
                      });
                      break;
                    case "taxDeclarationUpload":
                      formattedList.push({
                        url:
                          this.baseUrl +
                          `payroll/tax-declarations?data=${encodedParams}`,
                        icon: "fas fa-upload",
                        content: this.$t("authLayout.taxDeclarationUpload", {
                          count: notificationDetails["taxDeclarationUpload"],
                        }),
                      });
                      break;
                    case "recruitment":
                      formattedList.push({
                        url: this.baseUrl + "v3/recruitment/job-candidates",
                        icon: "fas fa-user-plus",
                        content: this.$t("authLayout.recruitmentApproval", {
                          count: notificationDetails["recruitment"],
                        }),
                      });
                      break;
                    case "housePropertyRecord":
                      formattedList.push({
                        url: this.baseUrl + "payroll/proof-of-investment",
                        icon: "fas fa-home",
                        content: this.$t(
                          "authLayout.housePropertyRecordApproval",
                          { count: notificationDetails["housePropertyRecord"] }
                        ),
                      });
                      break;
                    case "housePropertyUpload":
                      formattedList.push({
                        url: this.baseUrl + "payroll/income-under-section24",
                        icon: "fas fa-cloud-upload-alt",
                        content: this.$t("authLayout.housePropertyUpload", {
                          count: notificationDetails["housePropertyUpload"],
                        }),
                      });
                      break;
                    case "reimbursement":
                      formattedList.push({
                        url:
                          this.reimbursementWorkflowEnabled?.toLowerCase() ===
                          "yes"
                            ? this.baseUrl +
                              "v3/approvals/approval-management?form_id=267"
                            : this.baseUrl +
                              `payroll/reimbursement?data=${encodedParams}`,
                        icon: "far fa-credit-card",
                        content: this.$t("authLayout.reimbursementApproval", {
                          count: notificationDetails["reimbursement"],
                        }),
                      });
                      break;
                    default:
                      break;
                  }
                }
              }
              this.otherNotificationList = formattedList;
              if (this.enableWorkflow === "Yes") {
                this.fetchEnabledWorkflowNotification();
              }
            }
            this.errorInNotification = false;
          } else {
            // if any error occurs, data is returned empty object
            this.errorInNotification = true;
          }
        } catch {
          this.errorInNotification = true;
        }
      },
      // Error handling
      error() {
        this.errorInNotification = true; // we didn't check any rights for notifications. So other errors are not handled here
      },
    },
  },

  watch: {
    // As we migrateed the leave approval in new form , the below isused for old approval flow
    // apiCallCount(count) {
    //   if (count === 2) {
    //     this.fetchLeaveNotification();
    //   }
    // },
    // statusApprovalCount() {
    //   this.fetchLeaveNotification();
    // },
  },

  methods: {
    // refresh notification list when click refresh button(displayed when error occurs while listing notifications)
    fnRefetchNotifications() {
      this.otherNotificationList = [];
      this.errorInNotification = false;
      this.$apollo.queries.listNotificationsInDashboard.refetch();
    },

    // function called when any of the list action card is clicked
    fnRedirectActions(index) {
      let redirectionUrl = this.notificationList[index].url;
      window.location.href = redirectionUrl;
    },

    fetchEnabledWorkflowNotification() {
      // this.leaveNotificationLoading = true;
      this.apiCallCount = 0;
      if (this.isServiceProviderAdmin) {
        this.fetchGroupIds();
        this.fetchServiceProviderEmployees();
      } else {
        this.apiCallCount++;
        this.fetchGroupIds();
      }
    },

    fetchGroupIds() {
      let vm = this;
      vm.$apollo
        .query({
          query: GET_EMPLOYEE_GROUP_IDS,
          variables: {
            employeeId: vm.loginEmployeeId,
          },
          client: "apolloClientA",
        })
        .then((response) => {
          if (response.data) {
            this.groupId = response.data.getEmployeeGroupIds.GroupIdArray;
            this.apiCallCount++;
          } else {
            this.apiCallCount++;
          }
        })
        .catch(() => {
          this.apiCallCount++;
        });
    },

    fetchServiceProviderEmployees() {
      let vm = this;
      vm.$apollo
        .query({
          query: GET_SERVICE_PROVIDER_EMPLOYEES,
          variables: {
            employeeId: vm.loginEmployeeId,
          },
          client: "apolloClientA",
        })
        .then((response) => {
          if (response.data) {
            this.serviceProviderEmployees =
              response.data.getServiceProviderEmployees.employeeId.length > 0
                ? response.data.getServiceProviderEmployees.employeeId
                : ["-"];
            this.apiCallCount++;
          } else {
            this.apiCallCount++;
          }
        })
        .catch(() => {
          this.apiCallCount++;
        });
    },

    fetchLeaveNotification(formId = 31) {
      this.leaveNotificationList = [];
      var employeeId = localStorage.getItem("LoginEmpId");
      let myTaskFilter = [
        { key: "assignee", value: [employeeId], operator: "in" },
      ];
      if (this.isServiceProviderAdmin) {
        myTaskFilter.push({
          key: "custom_group_id",
          value: this.groupId.length > 0 ? this.groupId : ["-"],
          operator: "in",
          Employee_Id: this.serviceProviderEmployees,
        });
      } else {
        myTaskFilter.push({
          key: "custom_group_id",
          value: this.groupId.length > 0 ? this.groupId : ["-"],
          operator: "in",
        });
      }
      axios
        .post(
          Config.workflowUrl + "/workflow/userTaskCount",
          {
            filter: myTaskFilter,
            form_id: formId,
          },
          {
            headers: {
              org_code: this.orgCode,
              employee_id: this.loginEmployeeId,
              db_prefix: this.domainName,
              Authorization: window.$cookies.get("accessToken")
                ? window.$cookies.get("accessToken")
                : "",
              refresh_token: window.$cookies.get("refreshToken")
                ? window.$cookies.get("refreshToken")
                : null,
              partnerid: window.$cookies.get("partnerid")
                ? window.$cookies.get("partnerid")
                : "-",
            },
          }
        )
        .then((response) => {
          if (response.data) {
            let taskCountRes = JSON.parse(response.data.message);
            let groupCount = taskCountRes.custom_group_id
              ? taskCountRes.custom_group_id.taskcount
              : 0;
            let assigneeCount = taskCountRes.assignee
              ? taskCountRes.assignee.taskcount
              : 0;
            let totalCount = assigneeCount + groupCount;
            if (totalCount > 0) {
              let levNotification = [
                {
                  url:
                    this.baseUrl +
                    "v3/approvals/approval-management?form_id=31",
                  icon: "far fa-clock",
                  content: this.$t("authLayout.leaveApproval", {
                    count: totalCount,
                  }),
                },
              ];
              this.leaveNotificationList = levNotification;
              let notificationCnt = parseInt(this.notificationCount) + 1;
              this.notificationCount = notificationCnt.toString();
            }
          }
          this.leaveNotificationLoading = false;
        })
        .catch(() => {
          this.leaveNotificationLoading = false;
        });
    },
  },
});
</script>
