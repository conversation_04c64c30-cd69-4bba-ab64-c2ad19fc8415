<template>
  <div class="d-flex align-center justify-space-between">
    <div class="d-flex align-center">
      <v-progress-circular
        model-value="100"
        color="purple"
        :size="18"
        class="mr-1"
      ></v-progress-circular>
      <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
        >Contact Details</span
      >
    </div>
  </div>
  <v-row class="pa-4 ma-2 card-blue-background">
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Street 1</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.pApartment_Name) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Street 2</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.pStreet_Name) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[332]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[332]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.pBarangay) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">City</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.pCity) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">State/Province</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.pState) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[333]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[333]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.pRegion) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Country</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Country_Name) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[150].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.pPincode) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Mobile Number</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{
          candidateDetails.Mobile_No_Country_Code
            ? candidateDetails.Mobile_No
              ? candidateDetails.Mobile_No_Country_Code +
                " " +
                candidateDetails.Mobile_No
              : "-"
            : checkNullValue(candidateDetails.Mobile_No)
        }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Email Address</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Personal_Email) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[334]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[334]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Emergency_Contact_Name) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[335]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[335]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails?.Fax_No) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[359]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[359]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Emergency_Contact_Relation) }}
      </p>
    </v-col>
  </v-row>
</template>
<script>
import { checkNullValue } from "@/helper.js";

export default {
  name: "ContactInfo",

  props: {
    candidateDetails: {
      type: Object,
      required: true,
    },
  },

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  methods: {
    checkNullValue,
  },
};
</script>
