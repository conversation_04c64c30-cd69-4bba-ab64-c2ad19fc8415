<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem">
        <v-window-item :value="currentTabItem">
          <div class="mt-10">
            <v-row>
              <v-col
                v-if="originalList.length > 0"
                :cols="isSmallTable ? 5 : 12"
                class="mb-12"
              >
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Employee
                        </div>
                        <section class="d-flex align-center">
                          <div
                            v-if="
                              isSmallTable &&
                              !isMobileView &&
                              selectedItem &&
                              selectedItem.EmployeeId === item.EmployeeId
                            "
                            class="data-table-side-border d-flex"
                          ></div>
                          <div>
                            <div
                              class="text-body-2 font-weight-medium text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              {{ item.Employee }}
                            </div>
                            <div
                              class="text-subtitle-2 font-weight-regular text-grey"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              {{ item.EmployeeId }}
                            </div>
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Destination
                        </div>
                        <section class="d-flex align-center">
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.Destination }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Air Ticket Category
                        </div>
                        <section class="d-flex align-center">
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.AirTicketCategory }}
                          </div>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Eligibility Of Ticket Claim In Months
                        </div>
                        <section class="d-flex align-center">
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.EligibilityOfTicketClaimInMonths }}
                          </div>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Accrual Basis
                        </div>
                        <section class="d-flex align-center">
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.AccrualBasis }}
                          </div>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Status
                        </div>
                        <section class="d-flex align-center">
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.Status }}
                          </div>
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
              <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                <air-fair-view-form
                  :selectedFormData="selectedItem"
                  @close-form="closeAllForms()"
                ></air-fair-view-form>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
    </v-container>
  </div>
  <v-dialog
    :model-value="openFormInModal"
    class="pl-4"
    width="900"
    @click:outside="closeAllForms()"
  >
    <air-fair-view-form
      v-if="showViewForm"
      :selectedFormData="selectedItem"
      @close-form="closeAllForms()"
    ></air-fair-view-form>
  </v-dialog>
</template>
<script>
import { defineAsyncComponent } from "vue";

const AirFairViewForm = defineAsyncComponent(() => import("./AirFairViewForm"));
export default {
  name: "AirFairClaim",
  components: { AirFairViewForm },
  data() {
    return {
      currentTabItem: "",
      originalList: [
        {
          EmployeeId: "001",
          Employee: "James Parker ",
          JoiningDate: "03-08-2022",
          Destination: "Dammam",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "5500",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "1",
          LastAvailedDate: "01/08/2015",
        },
        {
          EmployeeId: "2",
          Employee: "Davidson P",
          JoiningDate: "07-12-2023",
          Destination: "Dusseldorf",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "12520",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "0",
          EligibilityInYears: "1",
          LastAvailedDate: "06/05/2019",
        },
        {
          EmployeeId: "003",
          Employee: "Princy sa",
          JoiningDate: "05-09-2023",
          Destination: "London",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "24",
          AccrualBasis: "7250",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "1",
          LastAvailedDate: "07/09/2014",
        },
        {
          EmployeeId: "005",
          Employee: "Jasmine M",
          JoiningDate: "11-03-2018",
          Destination: "Mauitius",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "5900",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "1",
          EligibilityInYears: "2",
          LastAvailedDate: "09/10/2021",
        },
        {
          EmployeeId: "004",
          Employee: "Reyna R",
          JoiningDate: "20-10-2017",
          Destination: "London",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "6500",
          Status: "Active",
          AirTicketToDependent: "No",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "3",
          LastAvailedDate: "03/04/2016",
        },
        {
          EmployeeId: "008",
          Employee: "Crisanto R",
          JoiningDate: "06-05-2021",
          Destination: "Amman",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "9800",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "0",
          EligibilityInYears: "2",
          LastAvailedDate: "05/11/2020",
        },
        {
          EmployeeId: "009",
          Employee: "Francis J",
          JoiningDate: "11-05-2022",
          Destination: "Rio De Janeiro",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "24",
          AccrualBasis: "6850",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "1",
          LastAvailedDate: "15/08/2018",
        },
        {
          EmployeeId: "21",
          Employee: "Anushri ",
          JoiningDate: "06-07-2023",
          Destination: "Sydney",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "5850",
          Status: "Active",
          AirTicketToDependent: "No",
          NoOfDependentEligibleForTicket: "1",
          EligibilityInYears: "1",
          LastAvailedDate: "30/12/2022",
        },
        {
          EmployeeId: "30",
          Employee: "William D'souza",
          JoiningDate: "20-08-2020",
          Destination: "Zurich",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "11860",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "0",
          EligibilityInYears: "3",
          LastAvailedDate: "25/06/2023",
        },
        {
          EmployeeId: "31",
          Employee: "Charles D 'souza",
          JoiningDate: "23-08-2020",
          Destination: "Bali",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "6800",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "1",
          EligibilityInYears: "3",
          LastAvailedDate: "19/07/2021",
        },
        {
          EmployeeId: "37",
          Employee: "Martina V",
          JoiningDate: "23-07-2024",
          Destination: "Belgium",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "8400",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "1",
          LastAvailedDate: "25/03/2024",
        },
      ],
      itemList: [
        {
          EmployeeId: "001",
          Employee: "James Parker ",
          JoiningDate: "03-08-2022",
          Destination: "Dammam",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "5500",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "1",
          LastAvailedDate: "01/08/2015",
        },
        {
          EmployeeId: "2",
          Employee: "Davidson P",
          JoiningDate: "07-12-2023",
          Destination: "Dusseldorf",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "12520",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "0",
          EligibilityInYears: "1",
          LastAvailedDate: "06/05/2019",
        },
        {
          EmployeeId: "003",
          Employee: "Princy sa",
          JoiningDate: "05-09-2023",
          Destination: "London",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "24",
          AccrualBasis: "7250",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "1",
          LastAvailedDate: "07/09/2014",
        },
        {
          EmployeeId: "005",
          Employee: "Jasmine M",
          JoiningDate: "11-03-2018",
          Destination: "Mauitius",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "5900",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "1",
          EligibilityInYears: "2",
          LastAvailedDate: "09/10/2021",
        },
        {
          EmployeeId: "004",
          Employee: "Reyna R",
          JoiningDate: "20-10-2017",
          Destination: "London",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "6500",
          Status: "Active",
          AirTicketToDependent: "No",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "3",
          LastAvailedDate: "03/04/2016",
        },
        {
          EmployeeId: "008",
          Employee: "Crisanto R",
          JoiningDate: "06-05-2021",
          Destination: "Amman",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "9800",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "0",
          EligibilityInYears: "2",
          LastAvailedDate: "05/11/2020",
        },
        {
          EmployeeId: "009",
          Employee: "Francis J",
          JoiningDate: "11-05-2022",
          Destination: "Rio De Janeiro",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "24",
          AccrualBasis: "6850",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "1",
          LastAvailedDate: "15/08/2018",
        },
        {
          EmployeeId: "21",
          Employee: "Anushri ",
          JoiningDate: "06-07-2023",
          Destination: "Sydney",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "5850",
          Status: "Active",
          AirTicketToDependent: "No",
          NoOfDependentEligibleForTicket: "1",
          EligibilityInYears: "1",
          LastAvailedDate: "30/12/2022",
        },
        {
          EmployeeId: "30",
          Employee: "William D'souza",
          JoiningDate: "20-08-2020",
          Destination: "Zurich",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "11860",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "0",
          EligibilityInYears: "3",
          LastAvailedDate: "25/06/2023",
        },
        {
          EmployeeId: "31",
          Employee: "Charles D 'souza",
          JoiningDate: "23-08-2020",
          Destination: "Bali",
          AirTicketCategory: "Class 2",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "6800",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "1",
          EligibilityInYears: "3",
          LastAvailedDate: "19/07/2021",
        },
        {
          EmployeeId: "37",
          Employee: "Martina V",
          JoiningDate: "23-07-2024",
          Destination: "Belgium",
          AirTicketCategory: "Class 1",
          EligibilityOfTicketClaimInMonths: "12",
          AccrualBasis: "8400",
          Status: "Active",
          AirTicketToDependent: "Yes",
          NoOfDependentEligibleForTicket: "2",
          EligibilityInYears: "1",
          LastAvailedDate: "25/03/2024",
        },
      ],
      showViewForm: false,
      selectedItem: null,
    };
  },
  computed: {
    mainTabs() {
      return ["My ESOP", "Uniform Monitoring", "Air Ticket Claim"];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    isSmallTable() {
      return !this.openFormInModal && this.showViewForm;
    },
    openFormInModal() {
      if (this.showViewForm && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Employee",
            align: "start",
            key: "employee",
          },
          {
            title: "Destination",
            key: "Destination",
          },
          {
            title: "Air Ticket Category",
            key: "AirTicketCategory",
          },
        ];
      } else {
        return [
          {
            title: "Employee",
            align: "start",
            key: "employee",
          },
          {
            title: "Destination",
            key: "Destination",
          },
          {
            title: "Air Ticket Category",
            key: "AirTicketCategory",
          },
          {
            title: "Eligibility Of Ticket Claim In Months",
            key: "EligibilityOfTicketClaimInMonths",
          },
          {
            title: "Accrual Basis",
            key: "AccrualBasis",
          },
          {
            title: "Status",
            key: "Status",
          },
        ];
      }
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf("Air Ticket Claim");
  },
  methods: {
    onTabChange(item) {
      if (item?.toLowerCase() === "my esop") {
        window.location.href = this.baseUrl + "in/benefits/esop";
      } else if (item?.toLowerCase() === "uniform monitoring") {
        this.$router.push("/my-team/uniform-monitoring");
      }
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    closeAllForms() {
      this.showViewForm = false;
      this.selectedItem = null;
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
