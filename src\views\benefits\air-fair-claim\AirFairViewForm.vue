<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" color="hover" size="35" variant="elevated">
            <v-icon class="text-primary" size="20">
              fas fa-plane-departure
            </v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">Air Ticket Claim</div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-icon class="mx-1" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>

      <div
        :style="
          isMobileView
            ? 'height: calc(100vh - 100px); overflow: scroll'
            : 'min-height: 400px'
        "
      >
        <v-card-text>
          <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Employee Id</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.EmployeeId) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Employee Name</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Employee) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Destination</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Destination) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                Air Ticket Category
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.AirTicketCategory) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                Eligibility Of Ticket Claim In Months
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{
                  checkNullValue(
                    selectedFormData.EligibilityOfTicketClaimInMonths
                  )
                }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Accrual Basis</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.AccrualBasis) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Status</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Status) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Joining Date</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.JoiningDate) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                Air Ticket To Dependent
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.AirTicketToDependent) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                No Of Dependent Eligible For Ticket
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{
                  checkNullValue(
                    selectedFormData.NoOfDependentEligibleForTicket
                  )
                }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                Eligibility In Years
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.EligibilityInYears) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                Last Availed Date
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.LastAvailedDate) }}
              </p>
            </v-col>
          </v-row>
        </v-card-text>
      </div>
    </v-card>
  </div>
</template>
<script>
import { checkNullValue } from "@/helper";
export default {
  name: "AirFairViewForm",
  props: {
    selectedFormData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  methods: {
    checkNullValue,
  },
};
</script>
