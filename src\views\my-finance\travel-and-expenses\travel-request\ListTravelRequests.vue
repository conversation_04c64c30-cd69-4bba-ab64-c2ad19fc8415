<template>
  <div>
    <div v-if="itemList.length > 0">
      <div
        class="d-flex align-center my-3"
        :class="isMobileView ? 'justify-center ' : 'justify-end'"
      >
        <v-btn
          prepend-icon="fas fa-plus"
          color="primary rounded-lg"
          @click="$emit('open-add-form')"
          v-if="formAccess.add"
        >
          Add
        </v-btn>
        <v-btn
          rounded="lg"
          color="transparent"
          variant="flat"
          class="ml-3"
          @click="$emit('refetch-data')"
        >
          <v-icon>fas fa-redo-alt</v-icon>
        </v-btn>
        <v-menu v-model="openMoreMenu" transition="scale-transition">
          <template v-slot:activator="{ props }">
            <v-btn
              variant="plain"
              class="ml-n2 mr-n5"
              :size="isMobileView ? 'small' : 'default'"
              v-bind="props"
            >
              <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
              <v-icon v-else>fas fa-caret-up</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="action in moreActions"
              :key="action.key"
              @click="onMoreAction(action.key)"
            >
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <v-list-item-title
                    v-bind="props"
                    class="pa-3"
                    :class="{
                      'pink-lighten-5': isHovering,
                    }"
                    ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                    >{{ action.key }}</v-list-item-title
                  >
                </template>
              </v-hover>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
      <v-row>
        <v-col :cols="12">
          <v-data-table
            :headers="tableHeaders"
            :items="itemList"
            fixed-header
            :items-per-page="50"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
            ]"
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(270, itemList)
            "
            style="box-shadow: none !important"
            class="elevation-1"
          >
            <template v-slot:item="{ item }">
              <tr
                class="data-table-tr bg-white cursor-pointer"
                :class="
                  isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''
                "
                @click="onSelectTravelRequest(item)"
              >
                <td
                  v-if="callingFrom !== 'employee'"
                  :class="isMobileView ? 'd-flex justify-space-between' : ''"
                  :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                >
                  <div
                    v-if="isMobileView"
                    class="text-subtitle-1 text-grey-darken-1"
                  >
                    Employee
                  </div>
                  <section class="d-flex align-center">
                    <div
                      class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                      :style="
                        !isMobileView
                          ? 'max-width: 200px; '
                          : 'max-width: 100px; '
                      "
                    >
                      <v-tooltip
                        :text="
                          item.employeeName + ' - ' + item.userDefinedEmpId
                        "
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                            v-bind="props"
                          >
                            {{ checkNullValue(item.employeeName) }}
                            <div
                              v-if="item?.userDefinedEmpId"
                              class="text-grey"
                            >
                              {{ checkNullValue(item.userDefinedEmpId) }}
                            </div>
                          </div>
                        </template>
                      </v-tooltip>
                    </div>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Travel Title
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <v-tooltip
                      :text="item.tripName"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="props"
                        >
                          {{ checkNullValue(item.tripName) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Travel Type
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <v-tooltip
                      :text="item.travelType"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="
                            item.travelType && item.travelType.length > 40
                              ? props
                              : ''
                          "
                        >
                          {{ checkNullValue(item.travelType) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Travel Start Date
                  </div>
                  <section style="height: 3em" class="d-flex align-center my-1">
                    <div
                      class="text-subtitle-1 font-weight-regular text-primary"
                      :style="
                        !isMobileView
                          ? 'max-width: 300px; '
                          : 'max-width: 200px; '
                      "
                    >
                      <div>
                        <div class="text-primary font-weight-regular">
                          {{ formatDate(item.travelStartDate) }}
                        </div>
                      </div>
                    </div>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Travel End Date
                  </div>
                  <section style="height: 3em" class="d-flex align-center my-1">
                    <div
                      class="text-subtitle-1 font-weight-regular text-primary"
                      :style="
                        !isMobileView
                          ? 'max-width: 300px; '
                          : 'max-width: 200px; '
                      "
                    >
                      <div>
                        <div class="text-primary font-weight-regular">
                          {{ formatDate(item.travelEndDate) }}
                        </div>
                      </div>
                    </div>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Destination Country for Visa
                  </div>
                  <section style="height: 3em" class="d-flex align-center">
                    <v-tooltip
                      :text="item.destinationCountry"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="
                            item.destinationCountry &&
                            item.destinationCountry.length > 40
                              ? props
                              : ''
                          "
                        >
                          {{ checkNullValue(item.destinationCountry) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Budget Amount (in {{ payrollCurrency }})
                  </div>
                  <section>
                    <v-tooltip
                      :text="item.budgetAmount?.toString()"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="
                            item.budgetAmount && item.budgetAmount.length > 50
                              ? props
                              : ''
                          "
                        >
                          {{ checkNullValue(item.budgetAmount) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Status
                  </div>
                  <section
                    class="text-subtitle-1 font-weight-regular text-truncate"
                    :class="statusColor(item.status)"
                    :style="
                      !isMobileView
                        ? 'max-width: 500px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.status) }}
                  </section>
                </td>
                <td
                  class="text-body-2"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 '
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Actions
                  </div>
                  <section
                    class="text-subtitle-1 font-weight-regular text-truncate"
                    v-if="allowedActions(item.status)"
                    :style="
                      !isMobileView
                        ? 'max-width: 500px; '
                        : 'max-width: 200px; padding-left: 8em'
                    "
                  >
                    <ActionMenu
                      :actions="allowedActions(item.status)"
                      iconColor="grey"
                      @selected-action="onActions($event, item)"
                      :access-rights="formAccess"
                    ></ActionMenu>
                  </section>
                  <div class="ml-10 font-weight-bold" v-else>-</div>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </div>
    <AppFetchErrorScreen
      v-else-if="itemList.length == 0"
      key="no-results-screen"
      main-title="No matching filter/search results found"
      image-name="common/no-records"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4">
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="this.isMobileView ? 'small' : 'default'"
                @click="$emit('reset-search-filter')"
              >
                Reset Filter/Search
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      :confirmation-heading="warningText"
      :icon-name="warningIconClass"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="updateStatus()"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>
<script>
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { UPDATE_TRAVEL_REQUEST_STATUS } from "@/graphql/employee-self-service/travelRequestQueries";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "ListTravelRequests",
  components: {
    ActionMenu,
  },
  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    formAccess: {
      type: Object,
      required: true,
    },
    formId: {
      type: Number,
      required: true,
    },
    callingFrom: {
      type: String,
      default: "employee",
    },
    travelCustomizedFormName: {
      type: String,
      default: "Travel Request",
    },
  },
  mixins: [FileExportMixin],
  data() {
    return {
      itemList: [],
      warningIconClass: "",
      openWarningModal: false,
      warningText: "Are you sure to cancel the travel request?",
      selectedItem: null,
      selectedTravelRequest: {},
      isLoading: false,
      openMoreMenu: false,
    };
  },
  mounted() {
    if (this.items.length) {
      this.itemList = this.items;
    }
  },
  watch: {
    items(val) {
      this.itemList = val;
      this.onApplySearch();
    },
    searchValue() {
      this.onApplySearch();
    },
  },
  computed: {
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      let headers = [
        {
          title: "Travel Title",
          key: "tripName",
        },
        {
          title: "Travel Type",
          key: "travelType",
        },
        {
          title: "Travel Start Date",
          key: "travelStartDate",
        },
        {
          title: "Travel End Date",
          key: "travelEndDate",
        },
        {
          title: "Destination Country for Visa",
          key: "destinationCountry",
        },
        {
          title: `Budget Amount (in ${this.payrollCurrency})`,
          key: "budgetAmount",
        },
        {
          title: "Status",
          key: "status",
        },
        { title: "Actions", key: "action", sortable: false },
      ];
      if (this.callingFrom !== "employee") {
        headers.unshift({
          title: "Employee",
          align: "start",
          key: "employeeName",
        });
      }
      return headers;
    },
    moreActions() {
      let moreActions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];

      if (this.formAccess.add) {
        moreActions.push({
          key: "Import",
          icon: "fas fa-file-import",
        });
      }

      return moreActions;
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    formatDate(date) {
      if (date && moment(date).isValid()) {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return moment(date).format(orgDateFormat);
      }
      return "-";
    },
    updateTravelRequestStatus(requestId, status) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_TRAVEL_REQUEST_STATUS,
          variables: {
            requestId: requestId,
            status: status,
            formId: this.formId,
          },
          client: "apolloClientAD",
        })
        .then(() => {
          vm.showAlert({
            isOpen: true,
            message: `${this.travelCustomizedFormName} status updated successfully`,
            type: "success",
          });
          vm.isLoading = false;
          vm.$emit("refetch-data");
        })
        .catch((error) => {
          vm.handleStatusUpdateError(error);
        });
    },
    handleStatusUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: this.travelCustomizedFormName + " status",
        isListError: false,
      });
      this.$emit("refetch-data");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    allowedActions(status) {
      status = status.toLowerCase();
      if (
        status === "applied" ||
        status === "approved" ||
        status === "rejected" ||
        status === "cancelled"
      ) {
        switch (status) {
          case "applied":
            return ["Edit", "Cancel"];
          case "approved":
            return ["Cancel"];
          case "rejected":
            return ["Edit"];
          case "cancelled":
            return ["Edit"];
        }
      }
      return false;
    },
    statusColor(status) {
      switch (status) {
        case "Applied":
          return "text-primary";
        case "Approved":
          return "text-green";
        case "Rejected":
          return "text-red";
        case "Cancelled":
          return "text-amber";
        case "Cancel Applied":
          return "text-warning";
        default:
          return "";
      }
    },
    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        this.openWarningPopup(item, "fas fa-trash");
      } else if (type && type.toLowerCase() === "edit") {
        this.$emit("open-edit-form", item);
      } else if (type && type.toLowerCase() === "cancel") {
        this.openWarningPopup(item, "far fa-times-circle");
      }
    },
    onSelectTravelRequest(item) {
      this.selectedTravelRequest = item;
      this.$emit("open-view-form", item);
    },
    onApplySearch() {
      let val = this.searchValue;
      if (!val) {
        this.itemList = this.items;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.items;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.selectedItem = null;
    },
    openWarningPopup(item, warningIcon) {
      if (item === null) {
        this.warningIconClass = warningIcon;
        this.openWarningModal = false;
        return;
      }
      this.warningIconClass = warningIcon;
      this.openWarningModal = true;
      this.selectedItem = item;
    },
    updateStatus() {
      this.openWarningModal = false;
      this.updateTravelRequestStatus(
        this.selectedItem.requestId,
        this.selectedItem.status?.toLowerCase() === "approved"
          ? "Cancel Applied"
          : "Cancelled"
      );
      this.selectedItem = null;
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      } else if (actionType === "Import") {
        this.$emit("open-import-form");
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let item = this.itemList;
      let itemList = item.map((item) => ({
        tripName: item.tripName,
        travelType: item.travelType,
        businessPurpose: item.businessPurpose,
        tripType: item.tripType,
        budgetAmount: item.budgetAmount,
        destinationCountry: item.destinationCountry,
        travelStartDate: this.formatDate(item.travelStartDate),
        travelEndDate: this.formatDate(item.travelEndDate),
        visaRequired: item.visaRequired ? "Yes" : "No",
        mealPreference: item.mealPreference,
        seatPreference: item.seatPreference,
        addedOn: item.addedOn ? this.convertUTCToLocal(item.addedOn) : "",
        addedBy: item.addedByName,
        updatedOn: item.updatedOn ? this.convertUTCToLocal(item.updatedOn) : "",
        userDefinedEmpId: item.userDefinedEmpId,
        employeeName: item.employeeName,
        updatedByName: item.updatedByName,
        status: item.status,
      }));

      let fileName = "Travel Request";
      let exportHeaders = [
        { header: "Employee Id", key: "userDefinedEmpId" },
        { header: "Employee", key: "employeeName" },
        { header: "Travel Title", key: "tripName" },
        { header: "Travel Start Date", key: "travelStartDate" },
        { header: "Travel End Date", key: "travelEndDate" },
        { header: "Destination Country for Visa", key: "destinationCountry" },
        { header: "Travel Type", key: "travelType" },
        { header: "Visa Required", key: "visaRequired" },
        { header: "Meal Preference", key: "mealPreference" },
        { header: "Seat Preference", key: "seatPreference" },
        {
          header: `Budget Amount (in ${this.payrollCurrency})`,
          key: "budgetAmount",
        },
        { header: "Business Purpose", key: "businessPurpose" },
        { header: "Status", key: "status" },
        { header: "Added By", key: "addedBy" },
        { header: "Added On", key: "addedOn" },
        { header: "Updated By", key: "updatedByName" },
        { header: "Updated On", key: "updatedOn" },
      ];

      let exportOptions = {
        fileExportData: itemList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
