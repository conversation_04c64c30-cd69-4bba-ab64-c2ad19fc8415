<template>
  <div ref="dependentDetails" class="my-4">
    <div class="d-flex">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="yellow"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Dependent Details</span
        >
      </div>

      <span v-if="enableAdd" class="d-flex justify-end ml-auto">
        <v-btn
          color="primary"
          variant="text"
          @click="showAddEditDependentForm = true"
        >
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
        >
      </span>
    </div>
    <v-dialog
      v-model="showAddEditDependentForm"
      width="80%"
      style="height: 100% !important"
      @click:outside="closeAddEditForm()"
    >
      <AddEditDependentDetails
        :dependentDetails="selectedDependentDetails"
        :selectedCandidateId="selectedCandidateId"
        :selectedEmpMaritalStatus="selectedEmpMaritalStatus"
        @close-dependent-form="closeAddEditForm()"
        @refetch-personal-details="submitDependentFormData()"
      >
      </AddEditDependentDetails>
    </v-dialog>
    <div v-if="!isMobileView" class="d-flex">
      <v-slide-group
        class="pa-4"
        selected-class="bg-primary"
        prev-icon="fas fa-chevron-circle-left"
        next-icon="fas fa-chevron-circle-right"
        show-arrows
      >
        <v-slide-group-item>
          <ViewDependentDetails
            :dependentDetails="dependentDetails"
            :formAccess="formAccess"
            @on-open-edit="openEditForm($event)"
            @on-delete="openWarningPopUp($event)"
          />
        </v-slide-group-item>
      </v-slide-group>
    </div>
    <div v-else>
      <div class="card-container">
        <ViewDependentDetails
          :dependentDetails="dependentDetails"
          :formAccess="formAccess"
          @on-open-edit="openEditForm($event)"
          @on-delete="openWarningPopUp($event)"
        ></ViewDependentDetails>
      </div>
    </div>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      confirmation-heading="Are you sure you want to delete this record ?"
      icon-name="fas fa-trash-alt"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDelate()"
    >
    </AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
// components
const AddEditDependentDetails = defineAsyncComponent(() =>
  import("./AddEditDependentDetails")
);
const ViewDependentDetails = defineAsyncComponent(() =>
  import("./ViewDependentDetails.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "DependentDetails",
  components: {
    AddEditDependentDetails,
    ViewDependentDetails,
  },
  props: {
    dependentDetailsData: {
      type: [Array, Object],
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedEmpMaritalStatus: {
      type: [String, Number],
      required: true,
    },
  },
  emits: ["refetch-personal-details"],
  data() {
    return {
      dependentDetails: [],
      selectedDependentDeleteRecord: null,
      showAddEditDependentForm: false,
      selectedDependentDetails: {},
      openWarningModal: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    enableAdd() {
      return this.formAccess && this.formAccess.add;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.dependentDetails = this.dependentDetailsData;
  },

  methods: {
    submitDependentFormData() {
      this.$emit("refetch-personal-details");
      this.closeAddEditForm();
    },

    closeAddEditForm() {
      this.showAddEditDependentForm = false;
      this.selectedDependentDetails = {};
      mixpanel.track("Onboarded-candidate-dependent-add-edit-closed");
    },

    openEditForm(selectedItem) {
      this.selectedDependentDetails = selectedItem;
      this.showAddEditDependentForm = true;
      mixpanel.track("Onboarded-candidate-dependent-edit-opened");
    },

    onDelate() {
      this.openWarningModal = false;
    },

    openWarningPopUp(selectedItem) {
      this.openWarningModal = true;
      this.selectedDependentDeleteRecord = selectedItem;
    },

    //this method closes the delete confirmation popup
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedDependentDeleteRecord = null;
    },
  },
};
</script>
