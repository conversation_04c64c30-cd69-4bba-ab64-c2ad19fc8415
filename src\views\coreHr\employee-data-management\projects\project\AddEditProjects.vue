<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1">{{ projectLabel }}</span>
        </div>
        <div class="d-flex pa-1">
          <v-icon color="primary" @click="closeAddForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <FormTab
        :model-value="openedSubTab"
        grow
        :hide-slider="true"
        style="border-bottom: 1px solid #cfcfcf; border-radius: 0px"
      >
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            {{ tab.label }}
            <div
              v-if="isActiveSubTab(tab.value)"
              class="mt-3 mb-n4"
              style="border-bottom: 4px solid; width: 200px"
            ></div>
          </div>
        </v-tab>
      </FormTab>
      <div>
        <v-card-text>
          <v-window v-model="openedSubTab" style="width: 100%">
            <v-window-item value="project">
              <v-form v-show="!showEmployeesList" ref="projectForm">
                <v-row
                  class="px-sm-4 px-md-6 pt-sm-4"
                  style="height: calc(100vh - 400px); overflow: scroll"
                >
                  <v-col
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <div class="d-flex">
                      <v-text-field
                        v-model="projectName"
                        color="primary"
                        style="max-width: 80%"
                        maxlength="50"
                        variant="solo"
                        :rules="[
                          required(projectLabel, projectName),
                          validateWithRulesAndReturnMessages(
                            projectName,
                            'projectName',
                            projectLabel
                          ),
                        ]"
                        :disabled="timeSheetCount > 0"
                        @update:model-value="deductFormChange()"
                      >
                        <template v-slot:label>
                          <span>{{ projectLabel }}</span>
                          <span class="ml-1" style="color: red">*</span>
                        </template>
                      </v-text-field>
                      <v-tooltip location="top">
                        <template v-slot:activator="{ props }">
                          <v-icon
                            class="ml-1 mt-4"
                            v-bind="props"
                            size="small"
                            color="info"
                            v-if="timeSheetCount > 0"
                          >
                            fas fa-info-circle
                          </v-icon>
                        </template>
                        <div style="width: 200px !important">
                          Editing the {{ projectLabelSmallCase }} name is
                          restricted as there are associated time sheets
                          approved for the {{ projectLabelSmallCase }}.
                        </div>
                      </v-tooltip>
                    </div>
                  </v-col>

                  <v-col
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <div class="d-flex">
                      <v-text-field
                        v-model="clientName"
                        color="primary"
                        label="Client"
                        style="max-width: 80%"
                        maxlength="100"
                        variant="solo"
                        :rules="[
                          clientName
                            ? validateWithRulesAndReturnMessages(
                                clientName,
                                'client',
                                'Client'
                              )
                            : true,
                        ]"
                        :disabled="timeSheetCount > 0"
                        @update:model-value="deductFormChange()"
                      ></v-text-field>
                      <v-tooltip location="top">
                        <template v-slot:activator="{ props }">
                          <v-icon
                            class="ml-1 mt-4"
                            v-bind="props"
                            size="small"
                            color="info"
                            v-if="timeSheetCount > 0"
                          >
                            fas fa-info-circle
                          </v-icon>
                        </template>
                        <div style="width: 200px !important">
                          Editing the client's name is restricted as there are
                          associated time sheets approved for this
                          {{ projectLabelSmallCase }}.
                        </div>
                      </v-tooltip>
                    </div>
                  </v-col>

                  <v-col
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <CustomSelect
                      :items="locationList"
                      :label="`${additionalFieldProjectLabel} Location`"
                      itemValue="Location_Id"
                      itemTitle="Location_Name"
                      :isLoading="fetchingLocations"
                      :itemSelected="projectLocation"
                      :isAutoComplete="true"
                      style="max-width: 80%"
                      @selected-item="
                        onChangeCustomSelectField($event, 'projectLocation')
                      "
                    ></CustomSelect>
                  </v-col>

                  <v-col
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <CustomSelect
                      :items="allEmployeesList"
                      :label="`${additionalFieldProjectLabel} Manager`"
                      itemValue="employee_id"
                      itemTitle="empNameId"
                      :isLoading="fetchingEmployees"
                      :itemSelected="projectManager"
                      :isAutoComplete="true"
                      style="max-width: 80%"
                      @selected-item="
                        onChangeCustomSelectField($event, 'projectManager')
                      "
                    ></CustomSelect>
                  </v-col>
                  <v-col
                    v-if="projectCoverage === 'Employee'"
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <div
                      class="text-subtitle-1 text-grey-darken-1"
                      :class="{ 'red--text': employeeEmptyErrMsg }"
                    >
                      Employees
                      <span v-if="selectedEmployees.length > 0">
                        - {{ selectedEmployees.length }}</span
                      >
                      <span class="text-red">*</span>
                    </div>
                    <div class="d-flex align-center">
                      <AvatarOrderedList
                        v-if="selectedEmployees.length > 0"
                        class="mt-4"
                        :ordered-list="selectedEmployees"
                        empNameKey="employee_name"
                      ></AvatarOrderedList>
                      <v-btn
                        rounded="lg"
                        variant="elevated"
                        size="small"
                        class="mt-4 primary"
                        :loading="fetchingEmployees"
                        @click="openEmployeesList()"
                      >
                        <v-icon class="primary">fas fa-plus</v-icon
                        ><span class="primary">Add</span>
                      </v-btn>
                    </div>
                    <div
                      v-if="employeeEmptyErrMsg"
                      class="text-caption mt-2 text-red"
                    >
                      {{ employeeEmptyErrMsg }}
                    </div>
                  </v-col>

                  <v-col
                    v-if="projectCoverage === 'CUSTOMGROUP'"
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <CustomSelect
                      :items="customGroupList"
                      label="Custom Employee Group"
                      item-title="Custom_Group_Name"
                      item-value="Custom_Group_Id"
                      :isLoading="fetchingGroups"
                      :itemSelected="selectedCustomGroupId"
                      :isAutoComplete="true"
                      style="max-width: 80%"
                      :isRequired="true"
                      :rules="[
                        required(
                          'Custom Employee Group',
                          selectedCustomGroupId
                        ),
                      ]"
                      appendIcon="fas fa-redo-alt"
                      @selected-item="
                        onChangeCustomSelectField(
                          $event,
                          'selectedCustomGroupId',
                          'customGroup'
                        )
                      "
                      @append-icon-clicked="retrieveCustomGroups()"
                    ></CustomSelect>
                    <v-btn
                      variant="text"
                      class="mt-n2 primary"
                      :href="baseUrl + 'in/core-hr/custom-employee-groups'"
                      target="_blank"
                    >
                      <span class="ml-n5">
                        <v-icon class="primary">fas fa-plus</v-icon>
                        <span class="primary">Add Custom Group</span>
                      </span>
                    </v-btn>
                    <div
                      v-if="customGroupList.length === 0 && !fetchingGroups"
                      class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                    >
                      <v-icon color="warning" size="25"
                        >fas fa-exclamation-triangle</v-icon
                      >
                      <span
                        v-if="errorInFetchCustomEmployeeGroup"
                        class="pl-2 text-caption"
                        >Unable to fetch Custom Employee Group. Please refresh
                        and try again.
                        <a class="text-primary" @click="retrieveCustomGroups()"
                          >Refresh
                        </a>
                      </span>
                      <span v-else class="pl-2 text-caption"
                        >You do not seem to have configured Custom Employee
                        Group. Please
                        <a
                          class="text-primary"
                          :href="baseUrl + 'in/core-hr/custom-employee-groups'"
                          >create custom employee group
                        </a>
                        {{ projectLabelSmallCase }}s.</span
                      >
                    </div>
                  </v-col>
                  <v-col
                    v-if="
                      projectCoverage === 'CUSTOMGROUP' && selectedCustomGroupId
                    "
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Employees - {{ empListInSelectedGroup.length }}
                    </p>
                    <v-skeleton-loader
                      v-if="fetchingGroupEmployees"
                      type="text"
                      class="mt-3 ml-n4"
                      style="max-width: 40%"
                    ></v-skeleton-loader>
                    <div v-else>
                      <div
                        v-if="empListInSelectedGroup.length === 0"
                        class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                      >
                        <v-icon color="warning" size="25"
                          >fas fa-exclamation-triangle</v-icon
                        >
                        <span
                          v-if="errorInFetchEmployeesList"
                          class="pl-2 text-caption"
                          >Something went wrong while fetching the employees
                          list. Please try again.
                          <a
                            class="text-primary"
                            @click="fetchCustomEmployeesList"
                            >Refresh
                          </a>
                        </span>
                        <span v-else class="pl-2 text-caption">
                          It seems like there are no employees associated with
                          the selected custom group. Please add some employees
                          under the selected group or try choosing an another
                          group.</span
                        >
                      </div>
                      <div v-else class="d-flex align-center mt-n3">
                        <AvatarOrderedList
                          v-if="empListInSelectedGroup.length > 0"
                          class="mt-4"
                          :ordered-list="empListInSelectedGroup"
                        ></AvatarOrderedList>
                        <v-btn
                          rounded="lg"
                          variant="elevated"
                          size="small"
                          class="mt-4 primary"
                          @click="openCustomGroupEmpList()"
                        >
                          View All
                        </v-btn>
                      </div>
                    </div>
                  </v-col>

                  <v-col
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <CustomSelect
                      :items="accreditationCategoryAndTypeList"
                      label="Accreditations Category & Type"
                      :itemSelected="accreditationCategoryType"
                      itemValue="accreditationCategoryAndTypeId"
                      itemTitle="accreditationCategory"
                      subText="accreditationType"
                      :isAutoComplete="true"
                      :isLoading="fetchingAccreditations"
                      :selectProperties="{
                        multiple: true,
                        chips: true,
                        clearable: true,
                        closableChips: true,
                      }"
                      @selected-item="
                        onChangeCustomSelectField(
                          $event,
                          'accreditationCategoryType'
                        )
                      "
                      style="max-width: 80%"
                    ></CustomSelect>
                  </v-col>

                  <v-col
                    v-if="isEdit"
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <p class="text-subtitle-1 text-grey-darken-1">Status</p>
                    <AppToggleButton
                      class="mt-4"
                      button-active-text="Open"
                      button-inactive-text="Close"
                      button-active-color="#7de272"
                      button-inactive-color="red"
                      id-value="gab-analysis-based-on"
                      :current-value="projectStatus === 'Open' ? true : false"
                      @chosen-value="onChangeStatus($event)"
                      style="max-width: 80%"
                    ></AppToggleButton>
                  </v-col>

                  <v-col
                    cols="12"
                    sm="12"
                    lg="6"
                    md="6"
                    xl="6"
                    class="pl-sm-4 pl-md-6"
                  >
                    <v-textarea
                      v-model="description"
                      rows="2"
                      row-height="8"
                      color="primary"
                      maxlength="600"
                      hide-details="auto"
                      variant="solo"
                      label="Description"
                      :rules="[
                        validateWithRulesAndReturnMessages(
                          description,
                          'description',
                          'Description'
                        ),
                      ]"
                      @update:model-value="deductFormChange()"
                    ></v-textarea>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <v-btn
                      v-if="formAccess.add || formAccess.update"
                      :disabled="!isFormDirty"
                      class="primary"
                      style="width: 100%"
                      @click="validateAddUpdateForm()"
                      >Save & Continue</v-btn
                    >
                  </v-col>
                </v-row>
              </v-form>
              <div v-if="showEmployeesList">
                <div class="px-6 d-flex ma-2">
                  <v-btn
                    rounded="lg"
                    class="primary"
                    @click="showEmployeesList = false"
                  >
                    <v-icon class="mr-1 primary"> fas fa-chevron-left </v-icon>
                    Back
                  </v-btn>
                </div>
                <EmployeeListCard
                  ref="employeesListCard"
                  :modal-title="
                    projectCoverage === 'CUSTOMGROUP'
                      ? 'Custom Group Employees'
                      : 'Employees'
                  "
                  :employees-list="empListForComponent"
                  :show-modal="showEmployeesList"
                  :submit-button-text="
                    projectCoverage === 'Employee' ? 'Select' : ''
                  "
                  :selected-item="selectedEmployees"
                  :showFilterSearch="true"
                  :show-filter="false"
                  @close-modal="showEmployeesList = false"
                  :selectable="projectCoverage === 'CUSTOMGROUP' ? false : true"
                  @apply-filter="onApplyFilterInEmpList($event)"
                  @on-select-employees="onSelectEmployee($event)"
                >
                </EmployeeListCard>
              </div>
            </v-window-item>
            <v-window-item value="project-activities">
              <div style="height: calc(100vh - 400px); overflow: scroll">
                <ProjectActivityCard
                  v-for="(data, index) in projectActivityList"
                  :key="data.projectActivityId"
                  :formData="data"
                  :activityList="activityList"
                  :projectId="projectId"
                  :openActivityEdit="openActivityEdit[index]"
                  @change-activity-edit="changeActivityEdit(index)"
                  @refetch-data="fetchActivityWithProject()"
                  :addIcon="
                    projectActivityList.length - 1 == index && !addNewData
                  "
                  class="ma-1"
                  :isAdd="false"
                  :formAccess="formAccess"
                  :addNewData="addNewData"
                  :projectStatus="projectStatus"
                  :disableActivityList="disableActivityList"
                  :fetchingActivityList="fetchingActivityList"
                  @add-new-activity="addNewActivity()"
                  @close-edit="closeEditForm()"
                ></ProjectActivityCard>
                <ProjectActivityCard
                  v-if="!projectActivityList.length || addNewData"
                  :formData="{}"
                  :openActivityEdit="false"
                  :activityList="activityList"
                  :formAccess="formAccess"
                  :isAdd="true"
                  :projectId="projectId"
                  @refetch-data="fetchActivityWithProject()"
                  :disableActivityList="disableActivityList"
                  @close-edit="closeEditForm()"
                  class="ma-1 mb-4"
                  :fetchingActivityList="fetchingActivityList"
                ></ProjectActivityCard>
              </div>

              <v-row>
                <v-col cols="12">
                  <v-btn
                    class="primary"
                    style="width: 100%"
                    @click="addNewActivity()"
                    ><span class="primary">Add New</span></v-btn
                  >
                </v-col>
              </v-row>
            </v-window-item>
          </v-window>
        </v-card-text>
      </div>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isLoadingCard"
        scrim="#fff"
      >
        <v-progress-circular color="primary" indeterminate size="54">
        </v-progress-circular>
      </v-overlay>
    </v-card>
    <AppWarningModal
      v-if="showConfirmation"
      :open-modal="showConfirmation"
      imgUrl="common/exit_form"
      confirmation-heading="Are you sure to exit this form?"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 primary"
            variant="text"
            @click="closeValidationAlert()"
          >
            <span class="primary">Close</span>
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { getErrorCodesWithValidation, handleNetworkErrors } from "@/helper";
const AvatarOrderedList = defineAsyncComponent(() =>
  import("@/components/helper-components/AvatarOrderedList")
);
const EmployeeListCard = defineAsyncComponent(() =>
  import("@/components/helper-components/EmployeeListCard")
);
import {
  RETRIEVE_ACCREDITATION_TYPE,
  RETRIEVE_DROPDOWN_DATA,
} from "@/graphql/dropDownQueries.js";
import {
  ADD_EDIT_PROJECTS,
  RETRIEVE_PROJECT_ACTIVITIES,
  GET_TIME_SHEET_COUNT_FOR_PROJECT,
} from "@/graphql/corehr/projectsQueries";
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import ProjectActivityCard from "./ProjectActivityCard.vue";
import { LIST_PROJECT_ACTIVITIES } from "@/graphql/corehr/projectActivityQueries.js";
export default {
  name: "AddEditProjects",

  components: {
    AvatarOrderedList,
    EmployeeListCard,
    CustomSelect,
    ProjectActivityCard,
  },

  mixins: [validationRules],

  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    projectDetails: {
      type: Object,
      default: () => {
        return {};
      },
    },
    projectCoverage: {
      type: String,
      default: "",
    },
    projectMappedAccreditations: {
      type: Array,
      default: () => {
        return [];
      },
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    openCloneModal: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["refetch-projects", "close-add-edit-form", "add-update-success"],

  data: () => ({
    projectActivityList: [],
    // others
    showConfirmation: false,
    showEmployeesList: false,
    isFormDirty: false,
    isProjectUpdated: false,

    // data
    projectId: 0,
    projectName: "",
    clientName: "",
    description: "",
    projectLocation: null,
    projectManager: null,
    accreditationCategoryType: [],
    selectedCustomGroupId: null,
    projectStatus: "Open",
    selectedEmployees: [],

    // lists
    locationList: [],
    allEmployeesList: [],
    customGroupList: [],
    accreditationCategoryAndTypeList: [],
    empListInSelectedGroup: [],
    empListForComponent: [],

    // loading
    fetchingEmployees: false,
    isLoadingCard: false,
    fetchingGroups: false,
    fetchingGroupEmployees: false,
    fetchingTimeSheetCount: false,
    fetchingActivityList: false,

    // errors
    errorInFetchCustomEmployeeGroup: false,
    errorInFetchEmployeesList: false,
    employeeEmptyErrMsg: "",
    validationMessages: [],
    showValidationAlert: false,

    // sub tabs
    openedSubTab: "project",
    activityList: [],
    activity: "",
    openActivityEdit: [],
    addNewData: false,
    timeSheetCount: 0,
  }),

  computed: {
    subTabItems() {
      return [
        {
          label: this.projectLabel,
          value: "project",
          disable: false,
        },
        {
          label: this.projectLabel + " Activities",
          value: "project-activities",
          disable: true,
        },
      ];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    fetchingAccreditations() {
      return this.$apollo.queries.retrieveAccreditationAndType.loading;
    },
    fetchingLocations() {
      return this.$apollo.queries.getDropDownBoxDetails.loading;
    },
    disableActivityList() {
      return this.projectActivityList.map((item) => item.activityName);
    },
    projectLabel() {
      return this.$store.state.projectLabel;
    },
    projectLabelSmallCase() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel ? pLabel.toLowerCase() : pLabel;
    },
    additionalFieldProjectLabel() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel === "Course" ? "" : pLabel;
    },
  },

  watch: {
    openedSubTab(val) {
      if (val === "project-activities") {
        this.fetchActivity();
        this.fetchActivityWithProject();
      }
    },
  },

  apollo: {
    // list dropdown details
    getDropDownBoxDetails: {
      query: RETRIEVE_DROPDOWN_DATA,
      client: "apolloClientA",
      result({ data }) {
        if (data && Object.keys(data).length) {
          if (data && data.getDropDownBoxDetails) {
            this.locationList = data.getDropDownBoxDetails.locations;
          }
        }
      },
    },

    retrieveAccreditationAndType: {
      query: RETRIEVE_ACCREDITATION_TYPE,
      client: "apolloClientV",
      variables() {
        return {
          urlHash: "",
        };
      },
      result({ data }) {
        if (data && Object.keys(data).length) {
          if (data && data.retrieveAccreditationAndType) {
            this.accreditationCategoryAndTypeList =
              data.retrieveAccreditationAndType.accreditationAndType;
          }
        }
      },
    },
  },

  mounted() {
    if (this.isEdit) {
      this.subTabItems[1].disable = false;
      const {
        projectName,
        clientName,
        description,
        locationId,
        managerId,
        customGroupId,
        status,
        accreditationId,
        projectId,
      } = this.projectDetails;
      this.projectId = projectId ? parseInt(projectId) : 0;
      this.projectName = projectName ? projectName : "";
      this.clientName = clientName ? clientName : "";
      this.description = description ? description : "";
      this.projectLocation = locationId ? locationId : null;
      this.projectManager = managerId ? managerId : null;
      this.projectStatus = status ? status : "Open";
      this.accreditationCategoryType = this.projectMappedAccreditations
        ? this.projectMappedAccreditations
        : [];
      if (this.openCloneModal) {
        this.accreditationCategoryType = accreditationId;
      }
      this.selectedCustomGroupId = customGroupId
        ? parseInt(customGroupId)
        : null;
      this.getTimeSheetCount();
    }
    if (this.projectCoverage === "CUSTOMGROUP") {
      this.retrieveCustomGroups();
      this.fetchCustomEmployeesList();
    }
    this.fetchAllEmployeesList();
  },

  methods: {
    addNewActivity() {
      this.addNewData = true;
    },
    changeActivityEdit(index) {
      this.closeEditForm();
      this.openActivityEdit[index] = false;
    },
    closeEditForm() {
      this.openActivityEdit = Array(this.projectActivityList.length).fill(true);
      this.addNewData = false;
    },
    fetchActivity() {
      let vm = this;
      vm.fetchingActivityList = true;
      vm.$apollo
        .query({
          query: LIST_PROJECT_ACTIVITIES,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listProjectActivities) {
            vm.activityList = response.data.listProjectActivities.activities;
          } else {
            vm.handleListError((err = ""), "activities");
          }
          vm.fetchingActivityList = false;
        })
        .catch((err) => {
          vm.fetchingActivityList = false;
          vm.handleListError(err, "activities");
        });
    },
    fetchActivityWithProject() {
      let vm = this;
      vm.isLoadingCard = true;
      vm.addNewData = false;
      vm.$apollo
        .query({
          query: RETRIEVE_PROJECT_ACTIVITIES,
          variables: {
            projectId: vm.projectId,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveProjectActivities) {
            vm.projectActivityList =
              response.data.retrieveProjectActivities.activityDetails;
            this.openActivityEdit = Array(this.projectActivityList.length).fill(
              true
            );
          } else {
            vm.handleListError((err = ""), "activities");
          }
          vm.isLoadingCard = false;
        })
        .catch((err) => {
          vm.isLoadingCard = false;
          vm.handleListError(err, "activities");
        });
    },
    getTimeSheetCount() {
      let vm = this;
      vm.fetchingTimeSheetCount = true;
      vm.$apollo
        .query({
          query: GET_TIME_SHEET_COUNT_FOR_PROJECT,
          variables: {
            projectId: vm.projectId,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getTimesheetCountForProject) {
            vm.timeSheetCount =
              response.data.getTimesheetCountForProject.timesheetCount;
          }
          vm.fetchingTimeSheetCount = false;
        })
        .catch(() => {
          vm.fetchingTimeSheetCount = false;
        });
    },
    handleListError(err = "", formName) {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((validationErrors) => {
          this.validationMessages = [validationErrors];
          this.showValidationAlert = true;
        });
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    // retrieve states based on country selected
    async retrieveCustomGroups() {
      this.fetchingGroups = true;
      this.errorInFetchCustomEmployeeGroup = false;
      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "projects",
        })
        .then((groupList) => {
          this.customGroupList = groupList ? groupList : [];
          this.errorInFetchCustomEmployeeGroup = false;
          this.fetchingGroups = false;
        })
        .catch(() => {
          this.errorInFetchCustomEmployeeGroup = true;
          this.customGroupList = [];
          this.fetchingGroups = false;
        });
    },

    async fetchAllEmployeesList() {
      this.fetchingEmployees = true;
      await this.$store
        .dispatch("getAllEmployeesList", {
          formName: "Projects",
          isActiveOnly: 1,
        })
        .then((empData) => {
          this.allEmployeesList = empData.map((item) => ({
            ...item,
            empNameId: item.employee_name + " - " + item.user_defined_empid,
          }));
          this.fetchingEmployees = false;
          this.findMatchedEmpList();
        })
        .catch(() => {
          this.allEmployeesList = [];
          this.fetchingEmployees = false;
        });
    },

    findMatchedEmpList() {
      if (
        this.projectDetails.employeeId &&
        this.projectCoverage === "Employee"
      ) {
        let matchedEmpList = [];
        let projectEmployees = this.projectDetails.employeeId.split(",");
        for (let emp of this.allEmployeesList) {
          let empId = emp.employee_id.toString();
          if (projectEmployees.includes(empId)) {
            matchedEmpList.push(emp);
          }
        }
        this.selectedEmployees = matchedEmpList;
      }
    },

    // open employees list when the coverage is Employees
    openEmployeesList() {
      this.empListForComponent = this.allEmployeesList;
      this.showEmployeesList = true;
    },

    // when the employees are selected in the employees list card
    onSelectEmployee(employees) {
      this.employeeEmptyErrMsg = "";
      this.selectedEmployees = employees;
      this.isFormDirty = true;
      this.showEmployeesList = false;
    },

    // open employees list to view the employees when the coverage is custom-group
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },

    onChangeCustomSelectField(value, field, label) {
      this.deductFormChange();
      this[field] = value;
      if (label === "customGroup") {
        this.fetchCustomEmployeesList();
      }
    },

    // called when on change function of all fields in this form
    deductFormChange() {
      this.isFormDirty = true;
    },

    // on changing the custom group we need to fetch the employees list relevant to the selected group
    async fetchCustomEmployeesList() {
      if (this.selectedCustomGroupId) {
        let vm = this;
        vm.fetchingGroupEmployees = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(this.selectedCustomGroupId),
          })
          .then((response) => {
            const employeeDetails = response;
            if (!employeeDetails || employeeDetails.length === 0) {
              vm.empListInSelectedGroup = [];
            } else {
              for (let i = 0; i < employeeDetails.length; i++) {
                employeeDetails[i].employee_name =
                  employeeDetails[i]["employeeName"];
                employeeDetails[i].designation_name =
                  employeeDetails[i]["designationName"];
                employeeDetails[i].department_name =
                  employeeDetails[i]["departmentName"];
                employeeDetails[i].user_defined_empid =
                  employeeDetails[i]["userDefinedEmpId"];
                delete employeeDetails[i].key1;
              }
              vm.empListInSelectedGroup = employeeDetails;
            }
            vm.fetchingGroupEmployees = false;
            vm.errorInFetchEmployeesList = false;
          })
          .catch(() => {
            vm.fetchingGroupEmployees = false;
            vm.empListInSelectedGroup = [];
            vm.errorInFetchEmployeesList = true;
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },

    // before closing the form we have ask close confirmation from user
    closeAddForm() {
      if (this.isFormDirty) {
        this.showConfirmation = true;
      } else {
        this.closeForm();
      }
    },

    // when aborting close in confirmation screen we have to keep the user in the same page
    abortClose() {
      this.showConfirmation = false;
    },

    // when accepting close in confirmation screen, we have to close the form
    acceptClose() {
      this.showConfirmation = false;
      this.closeForm();
    },

    closeForm() {
      this.isFormDirty = false;
      if (this.openedSubTab === "project-activities") {
        this.$emit("refetch-projects");
      } else {
        this.$emit("close-add-edit-form", this.openCloneModal);
      }
    },

    // change the mode of performance management
    onChangeStatus(value) {
      this.isFormDirty = true;
      this.projectStatus = value[1] ? "Open" : "Closed";
    },

    onApplyFilterInEmpList(filters) {
      let filteredEmployees = [];
      let employees =
        this.projectCoverage === "Employee"
          ? this.allEmployeesList
          : this.empListInSelectedGroup;
      for (let employee of employees) {
        if (
          (filters[1].length === 0 ||
            filters[1].includes(employee.Designation_Id)) &&
          (filters[0].length === 0 ||
            filters[0].includes(employee.Department_Id)) &&
          (filters[3].length === 0 ||
            filters[3].includes(employee.Location_Id)) &&
          (filters[2].length === 0 ||
            filters[2].includes(employee.EmpType_Id)) &&
          (filters[4].length === 0 ||
            filters[4].includes(employee.Work_Schedule))
        ) {
          filteredEmployees.push(employee);
        }
      }
      this.empListForComponent = filteredEmployees;
    },

    async validateAddUpdateForm() {
      let isFormValid = await this.$refs.projectForm.validate();
      if (
        isFormValid &&
        isFormValid.valid &&
        (this.projectCoverage !== "Employee" ||
          this.selectedEmployees.length > 0)
      ) {
        this.addUpdateProject();
      } else if (this.selectedEmployees.length === 0) {
        this.employeeEmptyErrMsg = "Employees are required";
      }
    },

    addUpdateProject() {
      let vm = this;
      vm.isLoadingCard = true;
      let empIds = [];
      if (vm.projectCoverage === "Employee") {
        for (let emp of vm.selectedEmployees) {
          empIds.push(emp.employee_id);
        }
      }
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_EDIT_PROJECTS,
            variables: {
              projectId: vm.projectId ? vm.projectId : 0,
              projectName: vm.projectName,
              clientName: vm.clientName ? vm.clientName : "",
              managerId: vm.projectManager ? parseInt(vm.projectManager) : 0,
              locationId: vm.projectLocation ? parseInt(vm.projectLocation) : 0,
              status: vm.projectStatus ? vm.projectStatus : "Open",
              description: vm.description ? vm.description : "",
              employeeId: empIds,
              customGroupId: vm.selectedCustomGroupId
                ? parseInt(vm.selectedCustomGroupId)
                : 0,
              accreditationId: vm.accreditationCategoryType,
            },
            client: "apolloClientJ",
          })
          .then((response) => {
            if (
              response &&
              response.data &&
              response.data.addUpdateProjectDetails
            ) {
              const { errorCode, projectId } =
                response.data.addUpdateProjectDetails;
              if (!errorCode) {
                var snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: vm.isEdit
                    ? this.projectLabel + " details updated successfully."
                    : this.projectLabel + " details added successfully.",
                };
                vm.projectId = projectId ? parseInt(projectId) : 0;
                vm.showAlert(snackbarData);
                vm.isProjectUpdated = true;
                vm.openedSubTab = "project-activities";
                vm.subTabItems[1].disable = false;
                vm.isLoadingCard = false;
              } else {
                vm.handleProjectAddEditError();
              }
            } else {
              vm.handleProjectAddEditError();
            }
          })
          .catch((addEditError) => {
            vm.handleProjectAddEditError(addEditError);
          });
      } catch {
        vm.handleProjectAddEditError();
      }
    },

    handleProjectAddEditError(err = "") {
      this.isLoadingCard = false;
      this.validationMessages = [];
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      let eAction = this.isEdit
        ? `updating the ${this.projectLabelSmallCase}s`
        : `creating the ${this.projectLabelSmallCase}s`;
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodesWithValidation(err);
        if (errorCode) {
          switch (errorCode[0]) {
            case "_DB0000": // technical errors
              snackbarData.message =
                "It’s us! There seem to be some technical difficulties. Please try after some time.";
              break;
            case "_DB0101": // add access denied
              snackbarData.message = `Sorry, you don't have access rights to add ${this.projectLabelSmallCase}s. Please contact HR administrator`;
              break;
            case "_DB0102": // update access denied
              snackbarData.message = `Sorry, you don't have rights to update ${this.projectLabelSmallCase}s. Please contact HR administrator`;
              break;
            // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
            case "BAD_USER_INPUT":
              var validErrs = errorCode[1];
              var errArray = validErrs ? Object.keys(validErrs) : [];
              for (var i in errArray) {
                // project, client, description
                if (
                  errArray[i] === "IVE0279" ||
                  errArray[i] === "IVE0282" ||
                  errArray[i] === "IVE0283"
                ) {
                  this.validationMessages.push(validErrs[errArray[i]]);
                }
              }
              if (this.validationMessages.length === 0) {
                // IVE0278: projectId, IVE0284: status, IVE0285: employeeId, IVE0286: customGroupId, IVE0095: managerId, IVE0181: locationId
                snackbarData[
                  "message"
                ] = `Something went wrong while validating the inputs when ${eAction}. Please try after some time - ${errorCode[0]}`;
              } else {
                snackbarData.isOpen = false;
                this.showValidationAlert = true;
              }
              break;
            case "CHR0015": //Error while inserting the project additional details
              snackbarData.message = `Something went wrong while inserting the ${this.projectLabelSmallCase} additional details when adding. Please try after some time.`;
              break;
            case "CHR0016": // Error deleting the project additional details
              snackbarData.message = `Something went wrong while deleting the ${this.projectLabelSmallCase} additional details when updating. Please try after some time.`;
              break;
            case "CHR0025": //Error while checking the project name exists
              snackbarData.message = `Something went wrong while processing the request to checking the ${this.projectLabelSmallCase} name exists when ${eAction} . Please try after some time.`;
              break;
            case "CHR0014": // Error while processing the request to add the project details
            case "CHR0017": // Error while processing the request to update the project details
            case "CHR0018": // Error while processing the request to add/ update the project details
              snackbarData.message = `Something went wrong while processing the request to ${eAction} . Please try after some time - ${errorCode[0]}`;
              break;
            case "_UH0001": // unhandled error
            case "_DB0001": // Error while retrieving the employee access rights
            case "_DB0002": // Error while checking the employee access rights
            case "_DB0104": // While check access rights form not found
            default:
              snackbarData.message = `Something went wrong while ${eAction} . If you continue to see this issue, please contact the platform administrator  - ${errorCode[0]}`;
              break;
          }
        } else {
          snackbarData.message = `Something went wrong while ${eAction} . Please try after some time.`;
        }
      } else if (err && err.networkError) {
        snackbarData.message = handleNetworkErrors(err);
      } else {
        snackbarData.message = `Something went wrong while ${eAction} . Please try after some time.`;
      }
      this.showAlert(snackbarData);
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
