<template>
  <div class="rounded-lg ma-1" color="#FDFEFF">
    <div v-if="!showEditForm">
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="blue"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Air Ticket Policy</span
          >
        </div>
        <div
          v-if="
            formAccess?.add &&
            airTicketData &&
            Object.keys(airTicketData)?.length === 0 &&
            callingFrom === `team`
          "
        >
          <v-btn @click="openAddDialog" color="primary" variant="text">
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon>Add
          </v-btn>
        </div>
        <div v-else-if="enableEdit">
          <v-btn @click="openEditDialog" color="primary" variant="text">
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
          </v-btn>
        </div>
      </div>
      <div
        v-if="airTicketData && Object.keys(airTicketData)?.length === 0"
        class="d-flex align-center justify-start fill-height text-h6 text-grey pa-4"
      >
        No Air Ticket Policy have been added
      </div>
      <v-row v-else class="pa-4 ma-2 card-blue-background">
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Place of Origin</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(airTicketData.Destination_City) }}
            <span v-if="airTicketData.Destination_Country"
              >- {{ airTicketData.Destination_Country }}</span
            >
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Air Ticket Category</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(airTicketData.Air_Ticketing_Category) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Air Fare Entitlement
            <span v-if="payrollCurrency" class="mr-1"
              >(in {{ payrollCurrency }})</span
            >
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            <span
              >Infant -
              {{
                airTicketData?.Infant_Amount ? airTicketData?.Infant_Amount : 0
              }},</span
            >
            <span class="ml-1"
              >Child -
              {{
                airTicketData?.Child_Amount ? airTicketData?.Child_Amount : 0
              }},</span
            >
            <span class="m-1">
              Adult -
              {{ checkNullValue(airTicketData?.Adult_Amount) }}</span
            >
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Effective From</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(airTicketData.Effective_Date_Enable) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Effective Date</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ formatDate(airTicketData.Effective_Date) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Eligibility of Ticket Claim (in Months)
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              checkNullValue(airTicketData.Eligibility_Of_Ticket_Claim_Months)
            }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Air Ticket to Dependent
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(airTicketData.Air_Ticket_To_Dependent) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Dependent Relationship
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              checkNullValue(
                JSON.parse(airTicketData?.Dependent_Relationship)?.join(", ")
              )
            }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Status</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(airTicketData.Status) }}
          </p>
        </v-col>
      </v-row>
    </div>
    <div v-else>
      <AirTicketPolicyEdit
        :selectedEmpId="selectedEmpId"
        :isEdit="isEdit"
        :airTicketDetails="airTicketDetails"
        :actionType="actionType"
        :callingFrom="callingFrom"
        @close-edit-form="closeEditForm()"
        @edit-updated="editUpdated()"
      />
    </div>
  </div>
</template>
<script>
import { checkNullValue } from "@/helper";
import moment from "moment";
import AirTicketPolicyEdit from "./AirTicketPolicyEdit.vue";
export default {
  name: "AirTicketPolicyView",
  components: { AirTicketPolicyEdit },
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    airTicketDetails: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showEditForm: false,
      airTicketData: {},
      isEdit: false,
    };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.formAccess &&
        this.formAccess.update &&
        this.formAccess.admin === "admin" &&
        this.callingFrom === "team"
      );
    },
  },

  mounted() {
    this.airTicketData = this.airTicketDetails?.length
      ? this.airTicketDetails[0]
      : {};
  },

  methods: {
    checkNullValue,
    editUpdated() {
      this.showEditForm = false;
      this.isEdit = false;
      this.$emit("fetch-api");
    },
    openAddDialog() {
      this.isEdit = false;
      this.showEditForm = true;
    },
    openEditDialog() {
      this.isEdit = true;
      this.showEditForm = true;
    },
    closeEditForm() {
      this.isEdit = false;
      this.showEditForm = false;
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
