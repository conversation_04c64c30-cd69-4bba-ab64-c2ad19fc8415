<template>
  <div
    v-if="dependentDetails && dependentDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No dependent details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in dependentDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:400px; border-left: 7px solid ${generateRandomColor()};height:auto`
        : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip
                  :text="
                    formDependentName(
                      data.Dependent_First_Name,
                      data.Dependent_Last_Name
                    )
                  "
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="
                        formDependentName(
                          data.Dependent_First_Name,
                          data.Dependent_Last_Name
                        )
                          ? props
                          : ''
                      "
                    >
                      {{
                        checkNullValue(
                          formDependentName(
                            data.Dependent_First_Name,
                            data.Dependent_Last_Name
                          )
                        )
                      }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="d-flex flex-column">
        <div class="card-columns w-100 mt-n6">
          <span
            :style="!isMobileView ? 'width:67%' : 'width:100%'"
            class="d-flex align-start flex-column"
          >
            <v-card-text class="text-body-1 font-weight-regular">
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Relationship </b>
                <span class="pb-1 pt-1">
                  <div
                    :style="
                      isMobileView ? 'max-width: 200px' : 'max-width:140px'
                    "
                    class="text-truncate"
                  >
                    {{ checkNullValue(data.Relationship) }}
                  </div></span
                >
              </div>
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Gender </b>
                <span class="pb-1 pt-1">{{ checkNullValue(data.Gender) }}</span>
              </div>
            </v-card-text>
          </span>
          <span
            :style="
              !isMobileView
                ? 'width:50%'
                : 'margin-top:-40px !important;margin-bottom: 10px !important;width:100%'
            "
            class="d-flex align-start flex-column"
          >
            <v-card-text class="text-body-1 font-weight-regular">
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Date of Birth </b>
                <span class="pb-1 pt-1">{{
                  formatDate(data.Dependent_DOB)
                }}</span>
              </div>
            </v-card-text>
          </span>
        </div>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "DependentDetails",
  data() {
    return { havingAccess: {} };
  },
  props: {
    dependentDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  components: { ActionMenu },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return this.formAccess && this.formAccess.update;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.dependentDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    formDependentName(firstName, LastName) {
      if (firstName || LastName) {
        return firstName + " " + LastName;
      } else return "";
    },
  },
};
</script>
