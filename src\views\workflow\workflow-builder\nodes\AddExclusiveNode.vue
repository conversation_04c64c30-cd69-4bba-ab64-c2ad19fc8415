<template>
  <Handle type="target" :position="targetPosition"> </Handle>
  <Handle type="source" :position="sourcePosition" />
  <div
    class="exclusive_parent"
    v-if="data.type === 'parent'"
    style="
      background-color: #ffffff;
      height: 30px;
      width: 30px;
      display: flex;
      border-radius: 5px;
      justify-content: center;
      align-items: center;
    "
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 512 512"
      height="10"
      width="10"
      fill="#0047c8"
    >
      <path
        d="M403.8 34.4c12-5 25.7-2.2 34.9 6.9l64 64c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-64 64c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6V160H352c-10.1 0-19.6 4.7-25.6 12.8L284 229.3 244 176l31.2-41.6C293.3 110.2 321.8 96 352 96h32V64c0-12.9 7.8-24.6 19.8-29.6zM164 282.7L204 336l-31.2 41.6C154.7 401.8 126.2 416 96 416H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H96c10.1 0 19.6-4.7 25.6-12.8L164 282.7zm274.6 188c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6V416H352c-30.2 0-58.7-14.2-76.8-38.4L121.6 172.8c-6-8.1-15.5-12.8-25.6-12.8H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H96c30.2 0 58.7 14.2 76.8 38.4L326.4 339.2c6 8.1 15.5 12.8 25.6 12.8h32V320c0-12.9 7.8-24.6 19.8-29.6s25.7-2.2 34.9 6.9l64 64c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-64 64z"
      />
    </svg>
    <div
      :class="'exclusive_menu' + (showParentMenu ? 'exclusive_menu1' : '')"
      style="
        position: absolute;
        bottom: -10px;
        z-index: 99999;
        left: 50%;
        transform: translate(-50%);
      "
    >
      <div class="" style="position: relative">
        <div
          :class="
            'glow-button' +
            (!showParentMenu ? ' glow-button-active' : ' dark-button')
          "
          v-click-outside="() => (showParentMenu = false)"
          @click="handleToParentNode"
        >
          <v-icon class="white" size="8">fas fa-plus</v-icon>
        </div>

        <v-expand-x-transition>
          <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
            <MenuItems
              v-if="showParentMenu"
              @handleProcessNode="handleToParentNode"
            ></MenuItems>
          </div>
        </v-expand-x-transition>
      </div>
    </div>
    <div class="close_icon" @click="confirmationModel = true">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="#ffffff"
        height="7"
        width="7"
        viewBox="0 0 384 512"
      >
        <path
          d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
        />
      </svg>
    </div>
  </div>
  <div class="exclusive_node" v-if="data.type === 'child'">
    <div
      style="
        display: flex;
        align-items: center;
        position: relative;
        padding-bottom: 5px;
      "
    >
      <div
        style="
          background-color: #0047c81a;
          height: 20px;
          width: 20px;
          display: flex;
          border-radius: 5px;
          justify-content: center;
          align-items: center;
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          height="10"
          width="10"
          fill="#0d25ff"
        >
          <path
            d="M403.8 34.4c12-5 25.7-2.2 34.9 6.9l64 64c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-64 64c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6V160H352c-10.1 0-19.6 4.7-25.6 12.8L284 229.3 244 176l31.2-41.6C293.3 110.2 321.8 96 352 96h32V64c0-12.9 7.8-24.6 19.8-29.6zM164 282.7L204 336l-31.2 41.6C154.7 401.8 126.2 416 96 416H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H96c10.1 0 19.6-4.7 25.6-12.8L164 282.7zm274.6 188c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6V416H352c-30.2 0-58.7-14.2-76.8-38.4L121.6 172.8c-6-8.1-15.5-12.8-25.6-12.8H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H96c30.2 0 58.7 14.2 76.8 38.4L326.4 339.2c6 8.1 15.5 12.8 25.6 12.8h32V320c0-12.9 7.8-24.6 19.8-29.6s25.7-2.2 34.9 6.9l64 64c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-64 64z"
          />
        </svg>
      </div>
      <v-text-field
        v-model="title"
        placeholder="Exclusive Gateway"
        class="header_text px-1 py-0 workflow_input"
        label=""
        hide-details
        variant="underlined"
        single-line
        min-width="160"
        density="compact"
        @blur="onChangedTitle"
      >
      </v-text-field>
      <div class="close_icons" @click="confirmationModel = true">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="#ffffff"
          height="7"
          width="7"
          viewBox="0 0 384 512"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
          />
        </svg>
      </div>
    </div>
    <v-divider></v-divider>
    <v-sheet class="d-flex align-center justify-end py-2">
      <v-btn
        size="x-small"
        class="text-none"
        color="primary"
        text="Submit"
        variant="elevated"
        rounded="md"
        @click="$emit('openQueryBuilder', data.id)"
      >
        <span class="d-flex item-center header_text">Expression</span>
      </v-btn>
    </v-sheet>
    <div
      style="
        position: absolute;
        bottom: -10px;
        z-index: 99999;
        left: 50%;
        transform: translate(-50%);
      "
      v-if="data.addNew"
    >
      <div class="" style="position: relative">
        <div
          :class="
            'glow-button' + (!showMenu ? ' glow-button-active' : ' dark-button')
          "
          v-click-outside="() => (showMenu = false)"
          @click="() => (showMenu = !showMenu)"
        >
          <v-icon class="white" size="8">fas fa-plus</v-icon>
        </div>
        <v-expand-x-transition>
          <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
            <MenuItems
              v-if="showMenu"
              @handleProcessNode="handleToStartNode"
            ></MenuItems>
          </div>
        </v-expand-x-transition>
      </div>
    </div>
  </div>
  <AppWarningModal
    v-if="confirmationModel"
    :open-modal="confirmationModel"
    iconName="fas fa-trash"
    confirmation-heading="Are you sure to delete?"
    @close-warning-modal="confirmationModel = false"
    @accept-modal="deleteNode()"
  >
  </AppWarningModal>
</template>
<script>
import { Position, Handle } from "@vue-flow/core";
import MenuItems from "../components/menus/MainMenu.vue";
export default {
  emits: ["handleToStart", "updateNodeTitle", "deleteNode", "openQueryBuilder"],
  name: "AddExclusiveNode",
  props: {
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    sourcePosition: {
      type: String,
      required: true,
    },
    targetPosition: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      leftPosition: Position.Top,
      rightPosition: Position.Bottom,
      showMenu: false,
      showParentMenu: false,
      addNewNode: false,
      selectedOption: "option1",
      dialog: false,
      conditionDialog: false,
      showModal: false,
      confirmationModel: false,
      title: "",
    };
  },
  components: {
    MenuItems,
    Handle,
  },
  methods: {
    handleToStartNode(type) {
      this.$emit("handleToStart", type, this.data, false, 0);
    },
    handleToParentNode() {
      this.$emit(
        "handleToStart",
        "addChildExclusiveGateway",
        this.data,
        true,
        0
      );
    },
    deleteNode() {
      this.confirmationModel = false;
      this.$emit("deleteNode", this.data);
    },
    onChangedTitle() {
      this.$emit("updateNodeTitle", {
        title: this.title,
        nodeId: this.data.id,
      });
    },
  },
  watch: {
    data: {
      immediate: true,
      handler(val) {
        this.title = val.title;
      },
    },
  },
};
</script>
<style>
.exclusive_node {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 5px;
  min-width: 120px;
  justify-content: flex-start;
}

.exclusive_node:hover .close_icons {
  visibility: visible;
}

.exclusive_node::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  /* background-color: #0081ff; */
  background: linear-gradient(to right, #00879b, #00d6fa);
  height: 4px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.exclusive_menu {
  display: none;
}

.exclusive_parent:hover .exclusive_menu,
.exclusive_menu1 {
  display: block !important;
}

.node-inputs .v-field__input {
  padding: 0px !important;
  min-height: 20px;
}
</style>
