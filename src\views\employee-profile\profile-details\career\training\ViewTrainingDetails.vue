<template>
  <div
    v-if="trainingDetails && trainingDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No training details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in trainingDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:550px; max-width:550px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip :text="data.Training_Name" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="data.Training_Name ? props : ''"
                    >
                      {{ checkNullValue(data.Training_Name) }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n8">
        <span
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Trainer </b>
              <span class="py-1">{{ checkNullValue(data.Trainer) }}</span>
            </div>
            <div class="mt-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Start Date </b>
              <span class="py-1">{{
                formatDate(data.Training_Start_Date)
              }}</span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Training Duration </b>
              <span class="py-1">{{
                convertMonthToYearMonthsDays(data.Training_Duration)
              }}</span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-40px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Center </b>
              <span class="py-1">{{ checkNullValue(data.Center) }}</span>
            </div>
            <div class="mt-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">End Date </b>
              <span class="py-1">{{ formatDate(data.Training_End_Date) }}</span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import {
  generateRandomColor,
  checkNullValue,
  convertMonthToYearMonthsDays,
} from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewTrainingDetails",
  components: { ActionMenu },
  props: {
    trainingDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return { havingAccess: {} };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    convertMonthToYearMonthsDays,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.trainingDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", [selectedActionItem, "training"]);
      } else {
        this.$emit("on-open-edit", [selectedActionItem, "training"]);
      }
    },
  },
};
</script>
