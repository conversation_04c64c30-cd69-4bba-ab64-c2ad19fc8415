import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_REIMBURSEMENT_LIST = gql`
  query listReimbursement(
    $employeeId: Int
    $formId: Int!
    $reimbursementMonth: Int!
    $reimbursementYear: Int!
  ) {
    listReimbursement(
      employeeId: $employeeId
      formId: $formId
      reimbursementMonth: $reimbursementMonth
      reimbursementYear: $reimbursementYear
    ) {
      errorCode
      message
      reimbursementDetails {
        Request_Id
        Employee_Id
        Approver_Id
        Payroll_Mid
        Added_By
        Added_On
        Submission_Date
        Approved_On
        Approval_Status
        Total_Amount
        Request_Count
        User_Defined_EmpId
        Actual_Total_Amount
        Employee_Name
        Approver_Name
        Added_By_Name
        Approved_By_Name
        Payroll_Mid_Name
        Audit_Request_Id
        Actual_Total_Amount
        Audit_Id
        Service_Provider_Id
        Organization_Unit_Name
        Process_Instance_Id
        comments {
          Comment_Id
          Emp_Comment
          Approval_Status
          Type
        }
      }
    }
  }
`;
export const GET_CURRENCY_LIST = gql`
  query listCurrencyConversion($formId: Int!, $status: String) {
    listCurrencyConversion(formId: $formId, status: $status) {
      errorCode
      message
      data {
        conversionId
        claimCurrencyId
        claimCurrencyCode
        claimCurrencyName
        payrollCurrencyCode
        payrollCurrencyName
        conversionType
        conversionValue
        addedOn
        updatedOn
        addedByName
        updatedByName
      }
    }
  }
`;
export const GET_REIMBURSEMENT_INFO = gql`
  query RetrieveReimbursement($formId: Int!, $requestId: Int!) {
    retrieveReimbursement(formId: $formId, requestId: $requestId) {
      errorCode
      message
      ReimbursementInfo {
        Request_Id
        Approver_Id
        Claim_Title
        Amount
        Line_Item_Id
        Other_Expense
        Invoice_No
        Reimbursement_Mode
        Description
        Mob_Invoice_Date
        Invoice_Amount
        Actual_Amount
        Deduction_Amount
        Invoice_Date
        Expense
        Expense_Type
        Expense_Type_Id
        Allowance_Type
        Allowance_Type_Name
        Allowance_Type_Id
        File_Details
        Claim_Currency_Code
        Claim_Currency_Amount
      }
    }
  }
`;
