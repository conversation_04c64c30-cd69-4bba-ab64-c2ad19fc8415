<template>
  <div v-if="itemList.length > 0">
    <v-data-table
      v-model="selectedEmpRecords"
      :headers="tableHeaders"
      :items="itemList"
      :items-per-page="50"
      :show-select="false"
      :sort-by="[{ key: 'employeeId', order: 'asc' }]"
      :height="
        $store.getters.getTableHeightBasedOnScreenSize(
          isSmallTable ? 220 : 290,
          itemList,
          true
        )
      "
      :items-per-page-options="[
        { value: 50, title: '50' },
        { value: 100, title: '100' },
        { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
      ]"
      fixed-header
      item-value="employeeId"
    >
      <template v-slot:item="{ item }">
        <tr
          @click="onSelectItem(item)"
          class="data-table-tr bg-white cursor-pointer"
          :class="
            isMobileView ? 'v-data-table__mobile-table-row ma-0 mt-2' : ''
          "
        >
          <td
            id="mobile-view-td pl-0"
            :style="
              item.employeeId === selectedEmployeeId && isSmallTable
                ? 'padding-left: 0px'
                : ''
            "
          >
            <div id="mobile-header" class="font-weight-bold mt-2">Employee</div>
            <section
              style="max-width: 200px"
              class="text-truncate d-flex align-center pl-0"
            >
              <div
                v-if="item.employeeId === selectedEmployeeId && isSmallTable"
                class="data-table-side-border selected-item-border-color"
              ></div>
              <span class="text-primary text-body-2 font-weight-medium">
                <v-tooltip :text="item.employeeName" location="bottom">
                  <template v-slot:activator="{ props }">
                    <span
                      v-bind="
                        item.employeeName && item.employeeName.length > 20
                          ? props
                          : ''
                      "
                      >{{ item.employeeName }}</span
                    >
                  </template>
                </v-tooltip>
                <v-tooltip
                  :text="String(item.userDefinedEmpId)"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      v-if="item.employeeId"
                      v-bind="
                        item.employeeId && item.employeeId.length > 20
                          ? props
                          : ''
                      "
                      class="text-grey"
                    >
                      {{ item.userDefinedEmpId }}
                    </div>
                  </template>
                </v-tooltip>
              </span>
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Gender</div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.gender) }}
            </section>
          </td>
          <td id="mobile-view-td" v-if="!isSmallTable">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Uniform Type
            </div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.uniformType) }}
            </section>
          </td>
          <td id="mobile-view-td" v-if="!isSmallTable">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Uniform Size
            </div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.uniformSize) }}
            </section>
          </td>
          <td id="mobile-view-td" v-if="!isSmallTable">
            <div id="mobile-header" class="font-weight-bold mt-2">Status</div>
            <section
              class="text-body-2 font-weight-bold"
              :class="
                item.uniformStatus == 'Returned'
                  ? 'text-purple'
                  : item.uniformStatus === 'Delivered'
                  ? 'text-green'
                  : 'text-red'
              "
            >
              {{ checkNullValue(item.uniformStatus) }}
            </section>
          </td>
        </tr>
      </template>
    </v-data-table>
  </div>

  <AppFetchErrorScreen
    v-else
    key="no-results-screen"
    main-title="There are no uniform monitoring matched for the selected employee."
    image-name="common/no-records"
  >
    <template #contentSlot>
      <div style="max-width: 80%">
        <v-row class="rounded-lg pa-5 mb-4">
          <v-col cols="12" class="d-flex align-center justify-center mb-4">
            <v-btn
              color="primary"
              variant="elevated"
              class="ml-4 mt-1"
              rounded="lg"
              :size="windowWidth <= 960 ? 'small' : 'default'"
              @click="$emit('refetch-list')"
            >
              Refresh
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </template>
  </AppFetchErrorScreen>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineComponent } from "vue";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";

export default defineComponent({
  name: "UniformMonitoringList",

  emits: ["on-select-item", "show-history", "refetch-list"],
  mixins: [FileExportMixin],

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    originalList: {
      type: Array,
      required: true,
      default: () => [],
    },
    formAccess: {
      type: Object,
      required: true,
    },
    isPayrollAdmin: {
      type: Boolean,
      default: false,
    },
    isSmallTable: {
      type: Boolean,
      required: true,
    },
  },

  data: () => ({
    // list
    itemList: [],
    openMoreMenu: false,
    openExportMenu: false,
    selectAllBox: false,
    selectedEmpRecords: [],
    // role
    selectedEmpIds: [],
    selectedRoleId: null,
    selectedRole: {},
    rolesList: [],
    showRolesAssociation: false,
    openRolesSelectionPopup: false,
    // delete/warning
    openWarningModal: false,
    selectedActionItem: null,
    selectedEmployee: "",
    selectedEmployeeId: "",

    // clone
    openCloneModal: false,

    resetFilterCount: 0,
    applyFilterCount: 0,
    isLoading: false,
  }),
  computed: {
    //the value searched in searchbox will be stored in vuex store

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },
    dojHeader() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      return "Date of Join (" + orgDateFormat + ")";
    },
    tableHeaders() {
      let headers = [];
      if (this.isSmallTable) {
        headers = [
          {
            title: "Employee",
            key: "employeeName",
          },
          {
            title: "Uniform Type",
            key: "uniformType",
          },
        ];
      } else {
        headers = [
          {
            title: "Employee",
            key: "employeeName",
          },
          {
            title: "Gender",
            key: "gender",
          },
          {
            title: "Uniform Type",
            key: "uniformType",
          },
          { title: "Uniform Size", key: "size" },
          { title: "Status", key: "status" },
        ];
      }
      return headers;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return true;
      } else return false;
    },
    selectedItems() {
      let selected = this.itemList.filter((el) => el.isSelected === true);
      return selected && selected.length > 0 ? selected : [];
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },

  mounted() {
    if (this.items.length) {
      this.itemList = this.items;
      this.itemList = this.itemList.map((item) => ({
        ...item,
        isSelected: false,
      }));
    }
  },

  watch: {
    selectedEmpRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.employeeId)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
      }
    },
    items(val) {
      this.itemList = val;
    },
  },

  methods: {
    checkNullValue,
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    onSelectItem(item) {
      this.selectedEmployeeId = item.employeeId;
      this.$emit("on-select-item", item);
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },

    onAssociateSuccess() {
      this.showRolesAssociation = false;
      this.selectAllBox = false;
      this.itemList = this.itemList.map((item) => {
        item["isSelected"] = false;
        return item;
      });
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
<style>
.data-table-side-border {
  /* margin-left: -1.1em; */
  margin-right: 10px;
  min-height: 3.5em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
.selected-item-border-color {
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
