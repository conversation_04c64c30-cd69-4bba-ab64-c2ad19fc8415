<template>
  <div>
    <div class="d-flex justify-space-between align-center">
      <v-row>
        <v-col cols="12" class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="red"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
            >Bank Details</span
          >
        </v-col>
      </v-row>
      <v-dialog
        transition="dialog-bottom-transition"
        v-model="showAddEditBankForm"
        width="70%"
      >
        <template v-slot:activator="{ props }">
          <v-btn
            v-if="formAccess && formAccess.add && bankDetailsData.length === 0"
            v-bind="props"
            color="primary"
            variant="text"
            @click="openAddForm()"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
          >
        </template>
        <AddEditBankDetails
          :selectedBankDetails="selectedBankDetails"
          :selectedCandidateId="selectedCandidateId"
          @close-bank-form="closeAddEditForm"
          @refetch-other-details="onUpdateAddSuccess"
        />
      </v-dialog>
    </div>
    <div class="d-flex justify center" v-if="!isMobileView">
      <v-slide-group
        class="px-4"
        selected-class="bg-primary"
        prev-icon="fas fa-chevron-circle-left"
        next-icon="fas fa-chevron-circle-right"
        show-arrows
      >
        <v-slide-group-item>
          <ViewBankDetails
            :bankDetails="bankDetails"
            :formAccess="formAccess"
            @on-open-edit="openEditForm"
            @on-delete="showDeleteConfirmationModal"
          />
        </v-slide-group-item>
      </v-slide-group>
    </div>
    <div v-else class="d-flex flex-column mt-6 align-center justify-center">
      <ViewBankDetails
        :bankDetails="bankDetails"
        :formAccess="formAccess"
        @on-open-edit="openEditForm"
        @on-delete="showDeleteConfirmationModal"
      />
    </div>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const AddEditBankDetails = defineAsyncComponent(() =>
  import("./AddEditBankDetails.vue")
);
import ViewBankDetails from "./ViewBankDetails.vue";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "OtherDetails",
  components: { ViewBankDetails, AddEditBankDetails },
  props: {
    bankDetailsData: {
      type: Array,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
  },
  emits: ["refetch-other-details"],
  data() {
    return {
      bankDetails: [],
      selectedBankDetails: {},
      showAddEditBankForm: false,
      showDeleteConfirmationModal: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.bankDetailsData && this.bankDetailsData.length > 0) {
      this.bankDetails = this.bankDetailsData;
    }
  },
  methods: {
    onUpdateAddSuccess() {
      this.closeAddEditForm();
      this.$emit("refetch-other-details");
    },
    closeAddEditForm() {
      mixpanel.track("Onboarded-candidate-other-bank-add-edit-closed");
      this.showAddEditBankForm = false;
      this.selectedBankDetails = {};
    },
    openAddForm() {
      mixpanel.track("Onboarded-candidate-other-bank-add-opened");
      this.selectedBankDetails = {};
      this.showAddEditBankForm = true;
    },
    openEditForm(selectedItem) {
      mixpanel.track("Onboarded-candidate-other-bank-edit-opened");
      this.selectedBankDetails = selectedItem;
      this.showAddEditBankForm = true;
    },
  },
};
</script>
<style scoped>
.text-subtitle-1 font-weight-regular {
  color: #222121 !important;
  font-size: 15px;
  margin: 12px 0px;
  overflow-wrap: break-word;
  max-width: 360px;
}
.bottom-navigation :deep() .v-bottom-navigation__content {
  background-color: white;
  justify-content: flex-start !important;
  align-items: center !important;
}
.bottom-navigation :deep() .v-bottom-navigation__content > .v-btn {
  font-size: inherit;
  height: 45px;
  max-width: 120px;
  min-width: 100px;
  font-size: 1.2rem;
  text-transform: none;
  transition: inherit;
  width: auto;
  border-radius: 0;
  margin-left: 20px !important;
}
</style>
