<template>
  <v-dialog v-model="openModal" width="1000" @click:outside="onCloseModal()">
    <v-card class="rounded-lg">
      <div class="d-flex justify-end">
        <v-icon color="primary" size="20" class="ma-4" @click="onCloseModal()">
          fas fa-times
        </v-icon>
      </div>
      <v-card-title class="mt-n4">
        <div class="text-primary text-center font-weight-medium">
          {{ modalTitle }}
        </div>
      </v-card-title>
      <v-card-text>
        <v-row v-if="showFilterSearch" justify="center">
          <v-col cols="10">
            <v-text-field
              v-model="searchInput"
              density="compact"
              placeholder="Search"
              clearable
              variant="outlined"
              prepend-inner-icon="fas fa-search"
            >
            </v-text-field>
          </v-col>
          <v-col v-if="showFilter" cols="2" md="1">
            <EmployeeDefaultFilterMenu
              menu-position="left"
              :is-search="false"
              class="pl-4"
              :list-items="empListItemsOriginal"
              :isApplyFilter="isApplyFilter"
              :departmentIdKey="departmentIdKey"
              :designationIdKey="designationIdKey"
              :locationIdKey="locationIdKey"
              :empTypeIdKey="empTypeIdKey"
              :workScheduleIdKey="workScheduleIdKey"
              :rolesIdKey="rolesIdKey"
              :showRoles="showRoles"
              :showServiceProvider="showServiceProvider"
              @reset-emp-filter="onResetFilter()"
              @applied-filter="onApplyFilter($event)"
            ></EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
        <v-data-table
          v-if="empListItems.length > 0"
          v-model="selectedEmpRecords"
          id="gridView"
          :headers="tableHeaders"
          :show-select="selectStrategy !== 'single' && !isMobileView"
          :select-strategy="selectStrategy"
          :items="empListItems"
          :items-per-page="50"
          fixed-header
          :height="
            $store.getters.getTableHeightBasedOnScreenSize(400, empListItems)
          "
          :item-value="employeeIdKey"
          :items-per-page-options="[
            { value: 50, title: '50' },
            { value: 100, title: '100' },
            { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
          ]"
        >
          <template v-slot:[`header.data-table-select`]="{ selectAll }">
            <v-checkbox-btn
              v-model="selectAllBox"
              color="primary"
              false-icon="far fa-circle"
              true-icon="fas fa-check-circle"
              indeterminate-icon="fas fa-minus-circle"
              class="mt-1"
              @change="selectAll(selectAllBox)"
            ></v-checkbox-btn>
          </template>
          <template #item="{ item }">
            <tr
              class="data-table-tr bg-white cursor-pointer"
              :class="
                isMobileView
                  ? 'v-data-table__mobile-table-row'
                  : selectStrategy === 'single'
                  ? 'cursor-pointer'
                  : ''
              "
              @click="selectStrategy === 'single' ? onSelectEmployee(item) : {}"
            >
              <td
                v-if="selectStrategy !== 'single' && !isMobileView"
                :class="isMobileView ? 'v-data-table__mobile-row' : ''"
              >
                <div class="d-flex justify-center align-center">
                  <v-checkbox-btn
                    v-model="item.isSelected"
                    color="primary"
                    false-icon="far fa-circle"
                    true-icon="fas fa-check-circle"
                    class="mt-n2 ml-n2"
                    @click.stop="
                      {
                      }
                    "
                    @change="checkAllSelected()"
                  ></v-checkbox-btn>
                </div>
              </td>

              <td
                v-if="userDefinedEmpIdKey"
                :class="
                  isMobileView
                    ? 'v-data-table__mobile-row d-flex align-center justify-center'
                    : ''
                "
              >
                <div
                  v-if="isMobileView"
                  class="v-data-table__mobile-row__header"
                >
                  {{ employeeIdLabel }}
                </div>
                <div
                  :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
                >
                  <section class="text-primary text-body-2">
                    {{
                      item[userDefinedEmpIdKey]
                        ? item[userDefinedEmpIdKey]
                        : "-"
                    }}
                  </section>
                </div>
              </td>

              <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
                <div
                  v-if="isMobileView"
                  class="v-data-table__mobile-row__header"
                >
                  {{ employeeNameLabel }}
                </div>
                <div
                  :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
                >
                  <section class="text-primary text-body-2">
                    {{ item[employeeNameKey] ? item[employeeNameKey] : "-" }}
                  </section>
                </div>
              </td>

              <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
                <div
                  v-if="isMobileView"
                  class="v-data-table__mobile-row__header"
                >
                  {{ designationNameLabel }}
                </div>
                <div
                  :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
                >
                  <section class="text-primary text-body-2">
                    {{ item[designationKey] ? item[designationKey] : "-" }}
                  </section>
                </div>
              </td>

              <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
                <div
                  v-if="isMobileView"
                  class="v-data-table__mobile-row__header"
                >
                  {{ deptNameLabel }}
                </div>
                <div
                  :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
                >
                  <section class="text-primary text-body-2">
                    {{ item[deptNameKey] ? item[deptNameKey] : "-" }}
                  </section>
                </div>
              </td>

              <td
                v-if="extraColumnKey && extraColumnText"
                :class="isMobileView ? 'v-data-table__mobile-row' : ''"
              >
                <div
                  v-if="isMobileView"
                  class="v-data-table__mobile-row__header"
                >
                  {{ extraColumnText }}
                </div>
                <div
                  :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
                >
                  <section class="text-primary text-body-2">
                    {{
                      item[extraColumnKey]
                        ? isExtraColumnAsDate
                          ? formatDate(item[extraColumnKey])
                          : item[extraColumnKey]
                        : "-"
                    }}
                  </section>
                </div>
              </td>
            </tr>
          </template>
        </v-data-table>

        <AppFetchErrorScreen
          v-else
          key="no-results-screen"
          main-title="No matching search results found"
          image-name="common/no-records"
        ></AppFetchErrorScreen>
      </v-card-text>
      <div
        v-if="selectStrategy !== 'single'"
        class="d-flex justify-center mb-4"
      >
        <v-btn
          rounded="lg"
          class="primary"
          variant="elevated"
          min-width="100"
          :disabled="selectedEmployees.length === 0"
          @click="onAddEmployees()"
        >
          {{ submitButtonText }}
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import moment from "moment";
import EmployeeDefaultFilterMenu from "./EmployeeDefaultFilterMenu.vue";

export default {
  name: "EmployeesListModal",
  components: {
    EmployeeDefaultFilterMenu,
  },
  props: {
    selectStrategy: {
      type: String,
      default: "page",
    },
    showModal: {
      type: Boolean,
      required: true,
    },
    modalTitle: {
      type: String,
      default: "Employee(s)",
    },
    employeesList: {
      type: Array,
      required: true,
    },
    showFilterSearch: {
      type: Boolean,
      default: false,
    },
    showFilter: {
      type: Boolean,
      default: true,
    },
    submitButtonText: {
      type: String,
      default: "Select",
    },
    // grid
    employeeIdKey: {
      type: String,
      default: "employee_id",
    },
    userDefinedEmpIdKey: {
      type: String,
      default: "user_defined_empid",
    },
    employeeIdLabel: {
      type: String,
      default: "Employee ID",
    },
    employeeNameLabel: {
      type: String,
      default: "Employee Name",
    },
    employeeNameKey: {
      type: String,
      default: "employee_name",
    },
    deptNameLabel: {
      type: String,
      default: "Department",
    },
    deptNameKey: {
      type: String,
      default: "department_name",
    },
    designationNameLabel: {
      type: String,
      default: "Designation",
    },
    designationKey: {
      type: String,
      default: "designation_name",
    },
    extraColumnKey: {
      type: String,
      default: "",
    },
    extraColumnText: {
      type: String,
      default: "",
    },
    isExtraColumnAsDate: {
      type: Boolean,
      default: false,
    },
    // filter
    isApplyFilter: {
      type: Boolean,
      default: false,
    },
    departmentIdKey: {
      type: String,
      default: "Department_Id",
    },
    designationIdKey: {
      type: String,
      default: "Designation_Id",
    },
    empTypeIdKey: {
      type: String,
      default: "EmpType_Id",
    },
    locationIdKey: {
      type: String,
      default: "Location_Id",
    },
    workScheduleIdKey: {
      type: String,
      default: "Work_Schedule",
    },
    rolesIdKey: {
      type: String,
      default: "Roles_Id",
    },
    showRoles: {
      type: Boolean,
      default: false,
    },
    showServiceProvider: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      openModal: false,
      searchInput: "",
      empListItems: [],
      empListItemsOriginal: [],
      selectAllBox: false,
      selectedEmpRecords: [],
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getNoRecordImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/common/no-records.webp");
      else return require("@/assets/images/common/no-records.png");
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    tableHeaders() {
      let header1 = [];
      if (this.userDefinedEmpIdKey) {
        header1.push({
          title: this.employeeIdLabel,
          key: this.userDefinedEmpIdKey,
        });
      }
      let headers = [
        {
          title: this.employeeNameLabel,
          key: this.employeeNameKey,
        },
        {
          title: this.designationNameLabel,
          key: this.designationKey,
        },
        {
          title: this.deptNameLabel,
          key: this.deptNameKey,
        },
      ];
      headers = header1.concat(headers);
      if (this.extraColumnKey && this.extraColumnText) {
        headers.push({
          title: this.extraColumnText,
          key: this.extraColumnKey,
        });
      }
      return headers;
    },
    selectedEmployees() {
      let selected = this.empListItemsOriginal.filter(
        (el) => el.isSelected === true
      );
      return selected && selected.length > 0 ? selected : [];
    },
    formatDate() {
      return (date) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return moment(date).format(orgDateFormat);
      };
    },
  },

  mounted() {
    let employees = this.employeesList;
    employees = employees.map((item) => ({
      ...item,
      isSelected: false,
    }));
    this.empListItems = employees;
    this.empListItemsOriginal = employees;
    this.openModal = this.showModal;
  },

  watch: {
    selectedEmpRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through empListItems
        for (const item of this.empListItems) {
          // Check if employeeIdKey is present in selRecords
          if (selRecords.includes(item[this.employeeIdKey])) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through empListItems
        for (const item of this.empListItems) {
          item.isSelected = false;
        }
      }
    },
    searchInput(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkAllSelected() {
      let selectedItems = this.empListItemsOriginal.filter(
        (el) => el.isSelected
      );
      this.selectAllBox =
        selectedItems.length === this.empListItemsOriginal.length;
    },
    onApplySearch(val) {
      if (!val) {
        this.empListItems = this.empListItemsOriginal;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.employeesList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.empListItems = searchItems;
      }
    },
    onApplyFilter(filteredArray) {
      this.searchInput = "";
      if (this.isApplyFilter) {
        this.empListItems = filteredArray;
      } else {
        this.$emit("on-apply-filter");
      }
    },
    onAddEmployees() {
      this.$emit("on-select-employees", this.selectedEmployees);
    },
    onSelectEmployee(item) {
      this.$emit("on-select-employee", item);
    },
    onResetFilter() {
      this.searchInput = "";
      this.empListItems = this.empListItemsOriginal;
    },
    onCloseModal() {
      this.openModal = false;
      this.$emit("close-modal");
    },
  },
};
</script>

<style>
.v-selection-control__input > .v-icon {
  font-size: 18px !important;
}
</style>
