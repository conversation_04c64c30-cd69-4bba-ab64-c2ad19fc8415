<template>
  <div v-if="listLoading" class="mt-3 mx-5">
    <v-skeleton-loader
      ref="skeleton1"
      type="table-heading"
      class="mx-auto"
    ></v-skeleton-loader>
    <div v-for="i in 3" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div
    :class="isMobileView ? 'd-flex flex-column align-center' : 'd-flex'"
    class="mr-4"
    v-else
  >
    <v-icon
      color="primary"
      size="30"
      dense
      @click="$emit('close-form')"
      class="mt-1"
      >fas fa-angle-left fa-lg
    </v-icon>
    <div style="width: 100%">
      <div
        class="d-flex"
        :class="isMobileView ? 'flex-column align-center' : 'align-start'"
      >
        <v-menu
          v-if="formId === 338"
          id="activitytracker_my_activity_date_picker"
          v-model="employeeListMenu"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
        >
          <template v-slot:activator="{ props: activatorProps }">
            <v-btn
              class="bg-white mr-2"
              :class="{
                'employee-list-btn': isMobileView,
              }"
              rounded="lg"
              dense
              v-bind="isEdit ? {} : activatorProps"
              :readonly="isEdit"
            >
              <template v-slot:prepend>
                <v-icon color="primary" class="mr-1" size="17">
                  fas fa-user-alt
                </v-icon>
              </template>
              <span
                style="max-width: 300px"
                class="text-primary font-weight-bold text-truncate"
              >
                {{ employeeData }}
              </span>
              <template v-slot:append>
                <v-icon v-if="!isEdit" color="primary" class="ml-1" size="17">
                  {{
                    employeeListMenu ? "fas fa-caret-up" : "fas fa-caret-down"
                  }}
                </v-icon>
              </template>
            </v-btn>
          </template>
          <div
            ref="employeeListContainer"
            style="
              min-height: 100px;
              max-height: 300px;
              overflow-y: scroll;
              background-color: white;
            "
            class="white pa-2 pt-0"
          >
            <div
              style="
                position: sticky;
                top: 0;
                background-color: white;
                height: 40px;
              "
            >
              <v-text-field
                v-model="searchEmployee"
                density="compact"
                variant="underlined"
                hide-details
                @update:model-value="onSearchEmployee($event)"
              >
                <template v-slot:prepend-inner>
                  <v-icon>fas fa-search</v-icon>
                </template>
              </v-text-field>
            </div>
            <div v-if="allEmployeesList && allEmployeesList.length > 0">
              <div
                v-for="employee in allEmployeesList"
                :key="employee.employeeId"
                :ref="
                  employee.employeeId === selectedEmployee
                    ? 'selectedEmployeeRef'
                    : null
                "
                @click="onChangeEmployee(employee)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <div
                      v-bind="props"
                      class="pa-2 my-2 rounded-lg cursor-pointer"
                      :class="{
                        'bg-hover':
                          isHovering &&
                          selectedEmployee !== employee.employeeId,
                        'bg-primary text-white':
                          selectedEmployee === employee.employeeId,
                        'bg-grey-lighten-4 text-primary':
                          !isHovering &&
                          selectedEmployee !== employee.employeeId,
                      }"
                    >
                      <div
                        class="text-body-2 text-break"
                        style="max-width: 300px"
                      >
                        {{ employee.employeeData }}
                      </div>
                    </div>
                  </template>
                </v-hover>
              </div>
            </div>
            <div
              v-else
              style="height: 100px"
              class="text-grey rounded-lg d-flex justify-center align-center"
            >
              No data available
            </div>
          </div>
        </v-menu>
        <v-menu
          v-model="claimDateMenu"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
        >
          <template v-slot:activator="{ props }">
            <v-btn
              class="bg-white"
              :class="isMobileView ? 'my-2' : ''"
              rounded="lg"
              v-bind="isEdit ? {} : props"
            >
              <template v-slot:prepend>
                <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
              </template>
              {{ formattedSelectedMonth }}
            </v-btn>
          </template>
          <Datepicker
            v-model="selectedMonthYear"
            :inline="true"
            :format="'MMMM, yyyy'"
            maximum-view="year"
            minimum-view="month"
            :disabled-dates="{ from: maxDate, to: minDate }"
            @changed-month="claimDateMenu = false"
            @update:modelValue="selectedMonthYear = $event"
          />
        </v-menu>
        <v-spacer></v-spacer>
        <v-menu
          v-if="
            requestId && (!isEdit || approvalStatus?.toLowerCase() === 'draft')
          "
          v-model="openStatusMenu"
          transition="scale-transition"
        >
          <template v-slot:activator="{ props }">
            <v-btn v-bind="props" rounded="lg">
              Status:
              <span>{{ approvalStatus ? " " + approvalStatus : " " }}</span>
              <v-icon class="pl-3" size="14" v-if="openStatusMenu"
                >fas fa-chevron-up</v-icon
              >
              <v-icon class="pl-3" size="14" v-else>fas fa-chevron-down</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item @click="onActions('submit for approval')">
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <v-list-item-title
                    v-bind="props"
                    class="pa-3"
                    :class="{
                      'bg-hover': isHovering,
                    }"
                    >Submit For Approval</v-list-item-title
                  >
                </template>
              </v-hover>
            </v-list-item>
          </v-list>
        </v-menu>
        <v-btn
          color="transparent"
          class="mt-1 ml-2"
          variant="flat"
          :size="isMobileView ? 'small' : 'default'"
          @click="getReimbursementInfo()"
        >
          <v-icon>fas fa-redo-alt</v-icon>
        </v-btn>
      </div>
      <v-form ref="claimPurposeForm">
        <div
          class="mt-3"
          :class="isMobileView ? 'd-flex justify-center' : ''"
          v-if="labelList[452]?.Field_Visiblity?.toLowerCase() == 'yes'"
        >
          <v-text-field
            v-model="claimTitle"
            density="comfortable"
            :disabled="claimPurposeDisbled"
            variant="solo"
            style="max-width: 250px"
            :rules="[
              labelList[452].Mandatory_Field?.toLowerCase() == 'yes'
                ? required(
                    labelList[452].Field_Alias || 'Claim Purpose',
                    claimTitle
                  )
                : true,
              multilingualNameNumericValidation(
                labelList[452].Field_Alias || 'Claim Purpose',
                claimTitle
              ),
              minMaxStringValidation(
                labelList[452].Field_Alias || 'Claim Purpose',
                claimTitle,
                3,
                100
              ),
            ]"
            @update:model-value="isFormDirty = true"
          >
            <template v-slot:label>
              {{ labelList[452].Field_Alias || "Claim Purpose" }}
              <span
                v-if="labelList[452].Mandatory_Field?.toLowerCase() == 'yes'"
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </div>
      </v-form>

      <div
        v-if="!isMobileView"
        :style="
          'overflow: scroll; height: ' + $store.getters.getTableHeight(240)
        "
      >
        <v-form ref="reimbursementForm">
          <table>
            <thead>
              <tr>
                <th
                  class="text-black font-weight-regular"
                  :class="fontsize"
                  style="background: #e1e1e1"
                  v-for="header in listHeaders"
                  :key="header"
                >
                  {{ header.title }}
                  <span v-if="header.isRequired" class="text-red">*</span>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in reimbursementList"
                :key="index"
                style="height: 50px; border-bottom: 1px solid #e7e7e4"
              >
                <td class="text-body-2" style="width: 150px">
                  <custom-select
                    :ref="'reimbursementType' + index"
                    v-if="item.isAdd"
                    variantType="outlined"
                    density="comfortable"
                    :item-selected="item.reimbursementType"
                    :items="reimbursementTypeList"
                    :rules="[
                      required('Reimbursement Type', item.reimbursementType),
                    ]"
                    @selected-item="
                      (item.reimbursementType = $event), (isFormDirty = true)
                    "
                  ></custom-select>
                  <p v-else class="text-body-2 font-weight-regular">
                    {{ checkNullValue(item.reimbursementType) }}
                  </p>
                </td>
                <td
                  v-if="item.reimbursementType !== 'Allowance'"
                  class="text-body-2"
                  style="width: 130px"
                >
                  <custom-select
                    :ref="'expenseType' + index"
                    v-if="item.isAdd"
                    variantType="outlined"
                    density="comfortable"
                    :is-loading="expenseTypeLoading"
                    :item-selected="item.expenseType"
                    :items="filterExpenseTypeList(index)"
                    :hint="getExpenseRule(item.expenseType)"
                    :persistent-hint="true"
                    item-title="Expense_Name"
                    item-value="Expense_Id"
                    :rules="[
                      required(
                        'Expense Type',
                        item.expenseType !== null &&
                          item.expenseType !== undefined
                      ),
                    ]"
                    @selected-item="onChangeExpenseType($event, index)"
                  ></custom-select>
                  <p
                    v-else
                    class="text-body-2 font-weight-regular text-truncate"
                  >
                    <v-tooltip :text="item.otherExpense" location="top">
                      <template v-slot:activator="{ props }">
                        <span
                          v-bind="item.otherExpense?.length > 30 ? props : {}"
                        >
                          {{
                            item.expenseType === 0
                              ? checkNullValue(item.otherExpense)
                              : checkNullValue(item.expanseTypeName)
                          }}</span
                        >
                      </template>
                    </v-tooltip>
                  </p>
                  <v-text-field
                    :ref="'otherExpense' + index"
                    v-if="item.isAdd && item.expenseType === 0"
                    density="comfortable"
                    label="Other Expense"
                    v-model="item.otherExpense"
                    variant="outlined"
                    :rules="[
                      required('Other Expense', item.otherExpense),
                      minLengthValidation(
                        'Other Expense',
                        item.otherExpense,
                        5
                      ),
                      maxLengthValidation(
                        'Other Expense',
                        item.otherExpense,
                        500
                      ),
                      alphaNumSpCDotHySlashValidation(
                        'Other Expense',
                        item.otherExpense
                      ),
                      firstLetterSpace('Other Expense', item.otherExpense),
                    ]"
                    @update:model-value="isFormDirty = true"
                  >
                  </v-text-field>
                </td>
                <td
                  v-if="item.reimbursementType === 'Allowance'"
                  class="text-body-2"
                  style="width: 130px"
                >
                  <custom-select
                    :ref="'allowanceType' + index"
                    v-if="item.isAdd"
                    variantType="outlined"
                    density="comfortable"
                    :is-loading="allowanceTypeLoading"
                    :item-selected="item.allowanceType"
                    :items="allowanceTypeList"
                    item-title="Allowance_Name"
                    item-value="Allowance_Type_Id"
                    :rules="[required('Allowance Type', item.allowanceType)]"
                    @selected-item="
                      (item.allowanceType = $event), (isFormDirty = true)
                    "
                  ></custom-select>
                  <p v-else class="text-body-2 font-weight-regular">
                    {{ checkNullValue(item.allowanceTypeName) }}
                  </p>
                </td>
                <td class="text-body-2" style="width: 150px">
                  <v-file-input
                    :ref="'documents' + index"
                    v-if="item.isAdd"
                    density="comfortable"
                    prepend-icon
                    :model-value="item.documents"
                    accept=".png, .jpg, .jpeg, .pdf"
                    hint="Max Size: 3MB."
                    :persistent-hint="true"
                    multiple
                    :rules="[
                      checkSize(item),
                      documentRequired(item)
                        ? required('Documents', item.documents.length)
                        : true,
                    ]"
                    variant="outlined"
                    chips
                    clearable
                    @update:model-value="onUploadFile($event, item, index)"
                    @click:clear="item.documents = []"
                  >
                    <template v-slot:prepend-inner>
                      <v-icon>fas fa-paperclip</v-icon>
                    </template>
                    <template v-slot:selection="{}">
                      <v-chip
                        v-for="(file, docIndex) in item.documents"
                        :key="file.name + '_' + docIndex"
                        draggable
                        small
                        style="max-width: 100%"
                        closable
                        @click:close="onCloseChip(index, docIndex)"
                      >
                        <span class="text-truncate">
                          {{ file.name }}
                        </span>
                      </v-chip>
                    </template>
                  </v-file-input>
                  <div
                    v-else-if="item.documents && item.documents.length > 0"
                    class="text-body-2 font-weight-regular"
                  >
                    <p
                      v-for="(file, index) in item.documents"
                      :key="index"
                      @click="viewReimbursementDocuments(file.formattedName)"
                      class="text-blue text-decoration-underline cursor-pointer text-truncate"
                    >
                      {{
                        file.name?.length > 15
                          ? file.name.slice(0, 12) + "..."
                          : file.name
                      }}
                    </p>
                  </div>
                  <div v-else class="text-body-2 font-weight-regular">-</div>
                </td>
                <td class="text-body-2" style="width: 150px">
                  <v-text-field
                    :ref="'invoiceNo' + index"
                    v-if="item.isAdd"
                    density="comfortable"
                    v-model="item.invoiceNo"
                    variant="outlined"
                    :rules="[
                      required('Invoice/Bill Number', item.invoiceNo),
                      minLengthValidation(
                        'Invoice/Bill Number',
                        item.invoiceNo,
                        1
                      ),
                      maxLengthValidation(
                        'Invoice/Bill Number',
                        item.invoiceNo,
                        20
                      ),
                      isAllZeros('Invoice/Bill Number', item.invoiceNo),
                      alphaNumericHypSlashHash(item.invoiceNo),
                    ]"
                    @update:model-value="isFormDirty = true"
                  >
                  </v-text-field>
                  <p v-else class="text-body-2 font-weight-regular">
                    {{ checkNullValue(item.invoiceNo) }}
                  </p>
                </td>
                <td class="text-body-2" style="width: 130px">
                  <v-menu
                    v-if="item.isAdd"
                    v-model="item.invoiceDateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        density="comfortable"
                        :ref="'activityDate' + index"
                        v-model="item.formatedInvoiceDate"
                        readonly
                        v-bind="props"
                        variant="outlined"
                        :rules="[
                          required('Invoice Date', item.formatedInvoiceDate),
                          validateFutureDate(item.invoiceDate),
                        ]"
                      >
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="item.invoiceDate"
                      :max="item.maxInvoiceDate"
                      :min="item.minInvoiceDate"
                      @update:modelValue="onUpdateInvoiceDate(index)"
                    />
                  </v-menu>
                  <p v-else class="text-body-2 font-weight-regular">
                    {{ checkNullValue(item.formatedInvoiceDate) }}
                  </p>
                </td>
                <td
                  class="text-body-2"
                  style="width: 150px"
                  v-if="currencyCodes.length"
                >
                  <v-text-field
                    :ref="'expenseAmount' + index"
                    v-if="item.isAdd"
                    density="comfortable"
                    v-model="item.expenseAmount"
                    variant="outlined"
                    type="number"
                    class="pl-0"
                    :rules="[
                      required('Expense Amount', item.expenseAmount),
                      minNumberValidation(
                        'Expense Amount',
                        item.expenseAmount,
                        1
                      ),
                      maxValue(
                        'Expense Amount',
                        item.expenseAmount,
                        item.expenseMaxAmount
                      ),
                      twoDecimalPrecisionValidation(item.expenseAmount),
                    ]"
                    @update:model-value="onUpdateExpenseAmount(index)"
                  >
                    <template #prepend-inner>
                      <v-menu
                        v-model="item.currencyMenu"
                        offset-y
                        :close-on-content-click="false"
                        transition="scale-transition"
                        max-height="250"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            v-bind="props"
                            class="d-flex align-center px-0"
                            style="cursor: pointer"
                          >
                            <span>{{ currentCurrency(item.currency) }}</span>
                            <v-icon size="15" class="ml-1">
                              {{
                                item.currencyMenu
                                  ? "fas fa-caret-up"
                                  : "fas fa-caret-down"
                              }}
                            </v-icon>
                          </div>
                        </template>

                        <v-list dense>
                          <v-list-item
                            v-for="(code, i) in currencyCodes"
                            :key="i"
                            @click="selectCode(code, index)"
                          >
                            <v-hover>
                              <template v-slot:default="{ isHovering, props }">
                                <v-list-item-title
                                  v-bind="props"
                                  class="pa-2 mb-1 rounded-lg cursor-pointer"
                                  :class="{
                                    'bg-hover': isHovering,
                                    'bg-grey-lighten-4 text-primary':
                                      !isHovering,
                                  }"
                                  >{{
                                    code.claimCurrencyName +
                                    " - " +
                                    code.claimCurrencyCode
                                  }}</v-list-item-title
                                >
                              </template>
                            </v-hover>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </template>
                  </v-text-field>
                  <p v-else class="text-body-2 font-weight-regular">
                    {{
                      currentCurrency(item.currency) +
                      " " +
                      checkNullValue(item.expenseAmount)
                    }}
                  </p>
                </td>
                <td class="text-body-2" style="width: 120px">
                  <v-text-field
                    :ref="'claimAmount' + index"
                    v-if="item.isAdd && !currencyCodes.length"
                    density="comfortable"
                    v-model="item.claimAmount"
                    variant="outlined"
                    type="number"
                    class="pl-0"
                    :rules="
                      currencyCodes.length
                        ? [true]
                        : [
                            required('Claim Amount', item.claimAmount),
                            minNumberValidation(
                              'Claim Amount',
                              item.claimAmount,
                              1
                            ),
                            maxValue(
                              'Claim Amount',
                              item.claimAmount,
                              item.claimMaxAmount
                            ),
                            twoDecimalPrecisionValidation(item.claimAmount),
                          ]
                    "
                    @update:model-value="onUpdateClaimAmount(index)"
                  >
                  </v-text-field>
                  <p v-else class="text-body-2 font-weight-regular">
                    {{
                      this.payrollCurrency && item.claimAmount
                        ? this.payrollCurrency + " " + item.claimAmount
                        : checkNullValue(item.claimAmount)
                    }}
                  </p>
                </td>
                <td class="text-body-2" style="width: 30px">
                  <v-menu activator="parent" :close-on-content-click="false">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon="fas fa-comment"
                        variant="plain"
                        color="primary"
                        v-bind="props"
                      >
                      </v-btn>
                      <p
                        class="text-caption text-red-darken-4"
                        v-if="
                          item.isAdd &&
                          item.comment != '' &&
                          (item.comment.length < 5 ||
                            item.comment?.length > 250)
                        "
                      >
                        Comment should be between 5 to 250 characters
                      </p>
                      <p
                        class="text-caption text-red-darken-4"
                        v-if="
                          item.isAdd &&
                          commentId?.Mandatory_Field === 'Yes' &&
                          item.comment === ''
                        "
                      >
                        Comment is required
                      </p>
                    </template>
                    <v-card class="pa-3" max-width="300px">
                      <v-textarea
                        v-if="item.isAdd"
                        v-model="item.comment"
                        rows="2"
                        variant="outlined"
                        auto-grow
                        :rules="[
                          minLengthValidation('Comment', item.comment, 5),
                          maxLengthValidation('Comment', item.comment, 250),
                          multilingualNameNumericValidation(
                            'Comment',
                            item.comment
                          ),
                        ]"
                        @update:model-value="isFormDirty = true"
                      ></v-textarea>
                      <p v-else class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.comment) }}
                      </p>
                    </v-card>
                  </v-menu>
                </td>
                <td class="text-body-2" style="width: 30px">
                  <v-icon
                    v-if="
                      item.isAdd && !item.lineId && reimbursementList.length > 1
                    "
                    class="mx-5"
                    color="red"
                    @click="
                      reimbursementList.splice(index, 1), (isFormDirty = false)
                    "
                    >fas fa-trash-alt</v-icon
                  >
                  <ActionMenu
                    v-if="
                      actionsList.length &&
                      !item.isAdd &&
                      (approvalStatus?.toLowerCase() === 'draft' ||
                        approvalStatus?.toLowerCase() === 'pending approval')
                    "
                    @selected-action="onActions($event, item)"
                    :actions="actionsList"
                    :access-rights="formAccess"
                  />
                </td>
              </tr>
            </tbody>
            <tbody class="text-black font-weight-regular" style="height: 50px">
              <tr>
                <td style="background: #e1e1e1">Total</td>
                <td style="background: #e1e1e1"></td>
                <td style="background: #e1e1e1"></td>
                <td style="background: #e1e1e1"></td>
                <td style="background: #e1e1e1"></td>
                <td
                  v-if="currencyCodes.length"
                  style="background: #e1e1e1"
                ></td>
                <td style="background: #e1e1e1">{{ totalClaim }}</td>
                <td style="background: #e1e1e1"></td>
                <td style="background: #e1e1e1"></td>
              </tr>
            </tbody>
            <tbody>
              <tr style="height: 50px">
                <td
                  style="background-color: transparent"
                  colspan="7"
                  class="pl-0"
                >
                  <v-btn
                    v-if="isFormDirty"
                    variant="elevated"
                    color="primary"
                    density="comfortable"
                    class="mt-2 mr-3"
                    @click="handleClickSave"
                  >
                    <span class="text-body-2">Save as Draft</span>
                  </v-btn>
                  <v-btn
                    v-if="
                      !isEdit ||
                      approvalStatus?.toLowerCase() === 'draft' ||
                      approvalStatus?.toLowerCase() === 'pending approval'
                    "
                    variant="elevated"
                    color="primary"
                    density="comfortable"
                    class="mt-2 mr-3"
                    @click="handleAddNew"
                    ><v-icon class="pr-2" size="10">fas fa-plus</v-icon>
                    <span class="text-body-2">Add New</span>
                  </v-btn>
                  <v-btn
                    v-if="
                      requestId &&
                      (!isEdit || approvalStatus?.toLowerCase() === 'draft')
                    "
                    variant="elevated"
                    color="primary"
                    density="comfortable"
                    class="mt-2"
                    @click="onActions('submit for approval')"
                  >
                    Submit for Approval
                  </v-btn>
                </td>
              </tr>
            </tbody>
          </table>
        </v-form>
      </div>
      <div v-else class="pa-4">
        <v-form ref="reimbursementForm">
          <v-container>
            <v-row
              v-for="(item, index) in reimbursementList"
              :key="index"
              style="border-bottom: 1px solid #e7e7e4"
              class="bg-white"
            >
              <v-col
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
              >
                <p>Reimbursement Type <span class="text-red">*</span></p>
              </v-col>
              <v-col cols="6" class="text-body-2">
                <custom-select
                  :ref="'reimbursementType' + index"
                  v-if="item.isAdd"
                  variantType="outlined"
                  density="comfortable"
                  :item-selected="item.reimbursementType"
                  :items="reimbursementTypeList"
                  :rules="[
                    required('Reimbursement Type', item.reimbursementType),
                  ]"
                  @selected-item="
                    (item.reimbursementType = $event), (isFormDirty = true)
                  "
                ></custom-select>
                <p v-else class="text-body-2 font-weight-regular">
                  {{ checkNullValue(item.reimbursementType) }}
                </p>
              </v-col>
              <v-col
                v-if="item.reimbursementType !== 'Allowance'"
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
              >
                <p>Expense Type <span class="text-red">*</span></p>
              </v-col>
              <v-col
                cols="6"
                v-if="item.reimbursementType !== 'Allowance'"
                class="text-body-2"
              >
                <custom-select
                  :ref="'expenseType' + index"
                  v-if="item.isAdd"
                  variantType="outlined"
                  density="comfortable"
                  :is-loading="expenseTypeLoading"
                  :item-selected="item.expenseType"
                  :items="expenseTypeList"
                  item-title="Expense_Name"
                  item-value="Expense_Id"
                  :hint="getExpenseRule(item.expenseType)"
                  :persistent-hint="true"
                  :rules="[
                    required(
                      'Expense Type',
                      item.expenseType !== null &&
                        item.expenseType !== undefined
                    ),
                  ]"
                  @selected-item="onChangeExpenseType($event, index)"
                ></custom-select>
                <p v-else class="text-body-2 font-weight-regular text-truncate">
                  {{
                    item.expenseType === 0
                      ? checkNullValue(item.otherExpense)
                      : checkNullValue(item.expanseTypeName)
                  }}
                </p>
                <v-text-field
                  :ref="'otherExpense' + index"
                  v-if="item.isAdd && item.expenseType === 0"
                  density="comfortable"
                  v-model="item.otherExpense"
                  variant="outlined"
                  :rules="[
                    required('Other Expense', item.otherExpense),
                    minLengthValidation('Other Expense', item.otherExpense, 5),
                    maxLengthValidation(
                      'Other Expense',
                      item.otherExpense,
                      500
                    ),
                    alphaNumSpCDotHySlashValidation(
                      'Other Expense',
                      item.otherExpense
                    ),
                    firstLetterSpace('Other Expense', item.otherExpense),
                  ]"
                  @update:model-value="isFormDirty = true"
                >
                </v-text-field>
              </v-col>
              <v-col
                v-if="item.reimbursementType === 'Allowance'"
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
              >
                <p>Allowance Type <span class="text-red">*</span></p>
              </v-col>
              <v-col
                cols="6"
                v-if="item.reimbursementType === 'Allowance'"
                class="text-body-2"
              >
                <custom-select
                  :ref="'allowanceType' + index"
                  v-if="item.isAdd"
                  variantType="outlined"
                  density="comfortable"
                  :is-loading="allowanceTypeLoading"
                  :item-selected="item.allowanceType"
                  :items="allowanceTypeList"
                  item-title="Allowance_Name"
                  item-value="Allowance_Type_Id"
                  :rules="[required('Allowance Type', item.allowanceType)]"
                  @selected-item="
                    (item.allowanceType = $event), (isFormDirty = true)
                  "
                ></custom-select>
                <p v-else class="text-body-2 font-weight-regular">
                  {{ checkNullValue(item.allowanceTypeName) }}
                </p>
              </v-col>
              <v-col
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
              >
                <p>Receipts<span class="text-red">*</span></p>
              </v-col>
              <v-col class="text-body-2" cols="6">
                <v-file-input
                  :ref="'documents' + index"
                  v-if="item.isAdd"
                  density="comfortable"
                  prepend-icon
                  :model-value="item.documents"
                  accept=".png, .jpg, .jpeg, .pdf"
                  hint="Max Size: 3MB."
                  :persistent-hint="true"
                  multiple
                  :rules="[
                    documentRequired(item)
                      ? required('Documents', item.documents.length)
                      : true,
                    checkSize(item),
                  ]"
                  variant="outlined"
                  chips
                  clearable
                  @update:model-value="onUploadFile($event, item, index)"
                  @click:clear="item.documents = []"
                >
                  <template v-slot:prepend-inner>
                    <v-icon>fas fa-paperclip</v-icon>
                  </template>
                  <template v-slot:selection="{}">
                    <v-chip
                      v-for="(file, index) in item.documents"
                      :key="file.name + '_' + index"
                      draggable
                      small
                      closable
                      style="max-width: 100%"
                      @click:close="item.documents.splice(index, 1)"
                    >
                      <span class="text-truncate">
                        {{ file.name }}
                      </span>
                    </v-chip>
                  </template>
                </v-file-input>
                <div
                  class="text-body-2 font-weight-regular"
                  v-else-if="item.documents && item.documents.length > 0"
                >
                  <p
                    v-for="(file, index) in item.documents"
                    :key="index"
                    @click="viewReimbursementDocuments(file.formattedName)"
                    class="text-blue text-decoration-underline cursor-pointer text-truncate"
                  >
                    {{ file.name }}
                  </p>
                </div>
                <div v-else class="text-body-2 font-weight-regular">-</div>
              </v-col>
              <v-col
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
              >
                <p>Invoice/Bill Number<span class="text-red">*</span></p>
              </v-col>
              <v-col class="text-body-2" cols="6">
                <v-text-field
                  :ref="'invoiceNo'"
                  v-if="item.isAdd"
                  density="comfortable"
                  v-model="item.invoiceNo"
                  variant="outlined"
                  :rules="[
                    required('Invoice/Bill Number', item.invoiceNo),
                    minLengthValidation(
                      'Invoice/Bill Number',
                      item.invoiceNo,
                      1
                    ),
                    maxLengthValidation(
                      'Invoice/Bill Number',
                      item.invoiceNo,
                      20
                    ),
                    isAllZeros('Invoice/Bill Number', item.invoiceNo),
                    alphaNumericHypSlashHash(item.invoiceNo),
                  ]"
                  @update:model-value="isFormDirty = true"
                >
                </v-text-field>
                <p v-else class="text-body-2 font-weight-regular">
                  {{ checkNullValue(item.invoiceNo) }}
                </p>
              </v-col>
              <v-col
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
              >
                <p>Invoice/Bill Date<span class="text-red">*</span></p>
              </v-col>
              <v-col class="text-body-2" cols="6">
                <v-menu
                  v-if="item.isAdd"
                  v-model="item.invoiceDateMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      density="comfortable"
                      :ref="'activityDate' + index"
                      v-model="item.formatedInvoiceDate"
                      readonly
                      v-bind="props"
                      variant="outlined"
                      :rules="[
                        required('Invoice Date', item.formatedInvoiceDate),
                        validateFutureDate(item.invoiceDate),
                      ]"
                    >
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="item.invoiceDate"
                    :max="item.maxInvoiceDate"
                    :min="item.minInvoiceDate"
                    @update:modelValue="onUpdateInvoiceDate(index)"
                  />
                </v-menu>
                <p v-else class="text-body-2 font-weight-regular">
                  {{ checkNullValue(item.formatedInvoiceDate) }}
                </p>
              </v-col>
              <v-col
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                v-if="currencyCodes.length"
              >
                <p>Expense Amount<span class="text-red">*</span></p>
              </v-col>
              <v-col class="text-body-2" cols="6" v-if="currencyCodes.length">
                <v-text-field
                  :ref="'expenseAmount' + index"
                  v-if="item.isAdd"
                  density="comfortable"
                  v-model="item.expenseAmount"
                  variant="outlined"
                  type="number"
                  class="pl-0"
                  :rules="[
                    required('Expense Amount', item.expenseAmount),
                    maxValue(
                      'Expense Amount',
                      item.expenseAmount,
                      item.expenseMaxAmount
                    ),
                    twoDecimalPrecisionValidation(item.expenseAmount),
                  ]"
                  @update:model-value="onUpdateExpenseAmount(index)"
                >
                  <template #prepend-inner>
                    <v-menu
                      v-model="item.currencyMenu"
                      offset-y
                      :close-on-content-click="false"
                      transition="scale-transition"
                      max-height="250"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          v-bind="props"
                          class="d-flex align-center px-0"
                          style="cursor: pointer"
                        >
                          <span>{{ currentCurrency(item.currency) }}</span>
                          <v-icon size="15" class="ml-1">
                            {{
                              item.currencyMenu
                                ? "fas fa-caret-up"
                                : "fas fa-caret-down"
                            }}
                          </v-icon>
                        </div>
                      </template>

                      <v-list dense>
                        <v-list-item
                          v-for="(code, i) in currencyCodes"
                          :key="i"
                          @click="selectCode(code, index)"
                        >
                          <v-hover>
                            <template v-slot:default="{ isHovering, props }">
                              <v-list-item-title
                                v-bind="props"
                                class="pa-2 mb-1 rounded-lg cursor-pointer"
                                :class="{
                                  'bg-hover': isHovering,
                                  'bg-grey-lighten-4 text-primary': !isHovering,
                                }"
                                >{{
                                  code.claimCurrencyName +
                                  " - " +
                                  code.claimCurrencyCode
                                }}</v-list-item-title
                              >
                            </template>
                          </v-hover>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </template>
                </v-text-field>
                <p v-else class="text-body-2 font-weight-regular">
                  {{ checkNullValue(item.expenseAmount) }}
                </p>
              </v-col>
              <v-col
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
              >
                <p>
                  Claim Amount<span
                    class="text-red"
                    v-if="!currencyCodes.length"
                    >*</span
                  >
                </p>
              </v-col>
              <v-col class="text-body-2" cols="6">
                <v-text-field
                  :ref="'claimAmount' + index"
                  v-if="item.isAdd && !currencyCodes.length"
                  density="comfortable"
                  v-model="item.claimAmount"
                  variant="outlined"
                  type="number"
                  class="pl-0"
                  :rules="
                    currencyCodes.length
                      ? [true]
                      : [
                          required('Claim Amount', item.claimAmount),
                          minNumberValidation(
                            'Claim Amount',
                            item.claimAmount,
                            1
                          ),
                          maxValue(
                            'Claim Amount',
                            item.claimAmount,
                            item.claimMaxAmount
                          ),
                          twoDecimalPrecisionValidation(item.claimAmount),
                        ]
                  "
                  @update:model-value="onUpdateClaimAmount(index)"
                >
                </v-text-field>
                <p v-else class="text-body-2 font-weight-regular">
                  {{ checkNullValue(item.claimAmount) }}
                </p>
              </v-col>
              <v-col
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
                >Comment</v-col
              >
              <v-col class="text-body-2" cols="6">
                <v-menu activator="parent" :close-on-content-click="false">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon="fas fa-comment"
                      variant="plain"
                      color="primary"
                      v-bind="props"
                    >
                    </v-btn>
                    <p
                      class="text-caption text-red-darken-2"
                      v-if="
                        item.isAdd &&
                        item.comment &&
                        item.comment.length <= 5 &&
                        item.comment?.length >= 250
                      "
                    >
                      Comment should be between 5 to 250 characters
                    </p>
                    <p
                      class="text-caption text-red-darken-2"
                      v-if="
                        item.isAdd &&
                        commentId?.Mandatory_Field === 'Yes' &&
                        item.comment === ''
                      "
                    >
                      Comment is required
                    </p>
                  </template>
                  <v-card class="pa-3">
                    <v-textarea
                      v-if="item.isAdd"
                      v-model="item.comment"
                      rows="2"
                      variant="outlined"
                      auto-grow
                      :rules="[
                        minLengthValidation('Comment', item.comment, 5),
                        maxLengthValidation('Comment', item.comment, 250),
                        multilingualNameNumericValidation(
                          'Comment',
                          item.comment
                        ),
                      ]"
                      @update:model-value="isFormDirty = true"
                    ></v-textarea>
                    <p v-else class="text-body-2 font-weight-regular">
                      {{ checkNullValue(item.comment) }}
                    </p>
                  </v-card>
                </v-menu>
              </v-col>
              <v-col
                cols="6"
                class="text-black font-weight-regular d-flex align-center"
                style=""
                >Actions</v-col
              >
              <v-col class="text-body-2" cols="6">
                <v-icon
                  v-if="
                    item.isAdd && !item.lineId && reimbursementList.length > 1
                  "
                  class="mx-5"
                  color="red"
                  @click="
                    reimbursementList.splice(index, 1), (isFormDirty = false)
                  "
                  >fas fa-trash-alt</v-icon
                >
                <ActionMenu
                  v-if="
                    actionsList.length &&
                    !item.isAdd &&
                    (approvalStatus?.toLowerCase() === 'draft' ||
                      approvalStatus?.toLowerCase() === 'pending approval')
                  "
                  @selected-action="onActions($event, item)"
                  :actions="actionsList"
                  :access-rights="formAccess"
                />
              </v-col>
            </v-row>
            <v-row class="bg-grey-lighten-2 text-black mt-4">
              <v-col cols="6">Total</v-col>
              <v-col cols="6" class="d-flex justify-end">{{
                totalClaim
              }}</v-col>
            </v-row>
            <div
              style="height: 50px"
              class="my-4 mb-16 d-flex"
              :class="isMobileView ? 'flex-column align-center' : ''"
            >
              <v-btn
                v-if="isFormDirty"
                variant="elevated"
                color="primary"
                density="comfortable"
                class="mt-2 mr-3"
                @click="handleClickSave"
              >
                <span class="text-body-2">Save as Draft</span>
              </v-btn>
              <v-btn
                v-if="
                  !isEdit ||
                  approvalStatus?.toLowerCase() === 'draft' ||
                  approvalStatus?.toLowerCase() === 'pending approval'
                "
                variant="elevated"
                color="primary"
                density="comfortable"
                class="mt-2 mr-3"
                @click="handleAddNew"
                ><v-icon class="pr-2" size="10">fas fa-plus</v-icon>
                <span class="text-body-2">Add New</span>
              </v-btn>
              <v-btn
                v-if="
                  requestId &&
                  (!isEdit || approvalStatus?.toLowerCase() === 'draft')
                "
                variant="elevated"
                color="primary"
                class="mt-2"
                density="comfortable"
                @click="onActions('submit for approval')"
              >
                Submit for Approval
              </v-btn>
            </div>
          </v-container>
        </v-form>
      </div>
    </div>
  </div>
  <AppWarningModal
    v-if="openDeleteConfirmation"
    :open-modal="openDeleteConfirmation"
    confirmation-heading="Are you sure to delete this record?"
    iconName="fas fa-trash"
    @close-warning-modal="
      (openDeleteConfirmation = false), (selectedLineItem = null)
    "
    @accept-modal="deleteLineItem()"
  ></AppWarningModal>
  <file-preview-modal
    v-if="showReimbursementDocuments"
    :fileName="reimbursementFileName"
    :folderName="'Reimbursement'"
    @close-preview-modal="showReimbursementDocuments = false"
  ></file-preview-modal>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
import validationRules from "@/mixins/validationRules";
import { checkNullValue } from "@/helper";
import {
  GET_REIMBURSEMENT_INFO,
  GET_CURRENCY_LIST,
} from "@/graphql/employee-self-service/reimbursement";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
export default {
  name: "AddEditReimbursement",
  emits: ["close-form", "refetch-list"],
  mixins: [validationRules],
  components: { Datepicker, CustomSelect, ActionMenu, FilePreviewModal },
  props: {
    formId: {
      type: Number,
      default: 339,
    },
    selectedEmployeeId: {
      type: Number,
      required: true,
    },
    selectedItem: {
      type: Object,
      default: null,
    },
    employeesList: {
      type: Array,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  data() {
    return {
      employeeListMenu: false,
      reimbursementTypeList: ["Expense"],
      expenseTypeList: [],
      allowanceTypeList: [],
      allEmployeesList: [],
      selectedEmployee: null,
      allEmployeesBackupList: [],
      searchEmployee: "",
      selectedMonthYear: new Date(),
      reimbursementList: [],
      expenseTypeLoading: false,
      allowanceTypeLoading: false,
      isLoading: false,
      requestId: 0,
      minDate: "",
      maxDate: "",
      isFormDirty: false,
      openDeleteConfirmation: false,
      selectedLineItem: null,
      openStatusMenu: false,
      statusMenuBottom: false,
      listLoading: false,
      isFileChanged: false,
      showReimbursementDocuments: false,
      reimbursementFileName: "",
      approvalStatus: "",
      claimDateMenu: false,
      updateTimeout: null,
      currencyCodes: [],
      claimTitle: "",
    };
  },
  computed: {
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    fontsize() {
      let windowSize = this.$store.state.windowWidth;
      if (windowSize > 1520) {
        return "text-subtitle-1";
      } else if (windowSize > 1342) {
        return "text-subtitle-2";
      } else if (windowSize > 1105) {
        return "text-caption";
      } else if (windowSize > 600) {
        return "small-caption";
      }
      return "";
    },
    filterExpenseTypeList() {
      return (index) => {
        // For first item, show all expense types
        if (
          index === 0 &&
          (!this.requestId || this.reimbursementList.length === 1)
        ) {
          return this.expenseTypeList;
        }

        // For subsequent items, filter by workflow ID of first selected expense type
        const firstItemWorkflowId = this.expenseTypeList.find(
          (item) => item.Expense_Id === this.reimbursementList[0].expenseType
        )?.Workflow_Id;

        return firstItemWorkflowId
          ? this.expenseTypeList.filter(
              (item) => item.Workflow_Id === firstItemWorkflowId
            )
          : []; // Return empty array if no workflow ID found to prevent invalid selections
      };
    },
    listHeaders() {
      let array = [
        { title: "Reimbursement Type", isRequired: true },
        { title: "Expense Type", isRequired: true },
        { title: "Receipts", isRequired: true },
        { title: "Invoice/Bill Number", isRequired: true },
        { title: "Invoice/Bill Date", isRequired: true },
        { title: "Claim Amount", isRequired: !this.currencyCodes.length },
        {
          title: "Comment",
          isRequired: this.commentId?.Mandatory_Field === "Yes",
        },
        { title: "Actions", isRequired: false },
      ];
      if (this.currencyCodes.length) {
        array.splice(5, 0, { title: "Expense Amount", isRequired: true });
      }
      return array;
    },
    actionsList() {
      let actions = [];
      if (this.formAccess?.delete) {
        actions.push("Delete");
      }
      if (this.formAccess?.update) {
        actions.push("Edit");
      }
      return actions;
    },
    totalClaim() {
      let sum = this.reimbursementList.reduce(
        (sum, claim) =>
          sum + (claim.claimAmount ? parseFloat(claim.claimAmount) : 0),
        0
      );
      if (sum) return sum.toFixed(2);
      return 0;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    employeeData() {
      const employee = this.allEmployeesList.find(
        (item) => item.employeeId === this.selectedEmployee
      );
      return employee ? employee.employeeData : null;
    },
    formattedSelectedMonth() {
      return moment(this.selectedMonthYear).format("MMMM, YYYY");
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        }
        return "";
      };
    },
    commentId() {
      let id = this.formId == 338 ? 433 : 434;
      let customObj = this.labelList[id];
      return customObj;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    checkSize() {
      return (item) => {
        if (item.documents?.length) {
          for (let doc of item.documents) {
            if (doc.size > 3000000) {
              return "The file size should be less than 3 MB.";
            }
          }
          return true;
        }
        return true;
      };
    },
    currentTimeStamp() {
      return moment().unix();
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    isAllZeros() {
      return (field, value) => {
        return value.length > 0 && [...value].every((char) => char === "0")
          ? "Invalid " + field
          : true;
      };
    },
    validateFutureDate() {
      return (date) => {
        return date && new Date(date) > new Date()
          ? "Date should not be in future"
          : true;
      };
    },
    getExpenseRule() {
      return (expenseType) => {
        if (expenseType && expenseType > 0) {
          let rule = this.expenseTypeList.find(
            (item) => item.Expense_Id === expenseType
          );
          return [this.payrollCurrency, rule?.Max_Amount, rule?.Unit]
            .filter((item) => item)
            .join(" ");
        }
        return "";
      };
    },
    claimPurposeDisbled() {
      if (this.reimbursementList?.length) {
        let anyOneIsEdit = false;
        this.reimbursementList.forEach((item) => {
          if (item.isAdd) {
            anyOneIsEdit = true;
          }
        });
        return !anyOneIsEdit;
      }
      return true;
    },
    currentCurrency() {
      return (currency) => {
        return (
          this.currencyCodes.find((item) => item.claimCurrencyCode === currency)
            ?.claimCurrencyCode || this.payrollCurrency
        );
      };
    },
    documentRequired() {
      return (item) => {
        if (item.expenseType) {
          let expense = this.expenseTypeList.find(
            (item) => item.Expense_Id === item.expenseType
          );
          return expense?.Document_Upload?.toLowerCase() === "yes"
            ? true
            : false;
        }
        return false;
      };
    },
  },
  watch: {
    selectedEmployeeId(val) {
      if (val) this.selectedEmployee = val;
    },
    selectedItem(val) {
      if (val) {
        this.getReimbursementInfo();
      }
    },
    employeesList: {
      handler: function (val) {
        if (val?.length) {
          this.allEmployeesList = [...val];
          this.allEmployeesBackupList = [...val];
        }
      },
      deep: true,
      immediate: true,
    },
    selectedEmployee(val) {
      if (val) {
        this.getExpenseTypeList();
        this.getAllowanceTypeList();
      }
    },
  },
  mounted() {
    this.selectedEmployee = this.selectedEmployeeId;
    this.getCurrencyList();
    if (!this.isEdit) {
      this.reimbursementList = [
        {
          isAdd: true,
          reimbursementType: "Expense",
          expenseType: null,
          otherExpense: "",
          allowanceType: null,
          invoiceNo: "",
          invoiceDate: null,
          expenseAmount: null,
          claimAmount: null,
          comment: "",
          invoiceDateMenu: false,
          formatedInvoiceDate: "",
          documents: [],
          oldDocuments: [],
          expenseMaxAmount: 9999999999999.99,
          claimMaxAmount: 9999999999999.99,
          maxInvoiceDate: null,
          minInvoiceDate: null,
          currency: this.payrollCurrency,
          currencyMenu: false,
        },
      ];
    } else {
      this.selectedMonthYear = this.selectedItem.Submission_Date;
      this.requestId = this.selectedItem.Request_Id;
      this.approvalStatus = this.selectedItem.Approval_Status;
      this.getReimbursementInfo();
    }
  },
  methods: {
    checkNullValue,
    onSearchEmployee(emp) {
      if (emp) {
        let filterList = this.allEmployeesBackupList.filter((item) => {
          if (item.employeeData.toLowerCase().includes(emp)) return item;
        });
        this.allEmployeesList = [...filterList];
      } else {
        this.allEmployeesList = [...this.allEmployeesBackupList];
      }
    },
    async getMaxExpenseAmount(index) {
      let currencyCode = this.reimbursementList[index].currency;
      let currentCurrency = this.currencyCodes.find(
        (item) => item.claimCurrencyCode === currencyCode
      );
      if (currentCurrency) {
        if (currentCurrency.conversionType?.toLowerCase() == "manual") {
          let conversionRate = currentCurrency.conversionValue;
          let claimAmount = (
            this.reimbursementList[index].expenseAmount * conversionRate
          ).toFixed(2);
          this.reimbursementList[index].claimAmount = claimAmount;
        } else {
          let response = await fetch(
            `https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/${this.payrollCurrency.toLowerCase()}.json`
          );
          let data = await response.json();
          let conversionRate =
            data[this.payrollCurrency.toLowerCase()][
              currentCurrency.claimCurrencyCode.toLowerCase()
            ];
          let claimAmount = (
            this.reimbursementList[index].expenseAmount / conversionRate
          ).toFixed(2);
          this.reimbursementList[index].claimAmount = claimAmount;
        }
      } else {
        this.reimbursementList[index].claimAmount =
          this.reimbursementList[index].expenseAmount;
      }
    },
    onUpdateExpenseAmount(index) {
      this.isFormDirty = true;
      clearTimeout(this.updateTimeout);
      this.updateTimeout = setTimeout(() => {
        this.getMaxExpenseAmount(index);
      }, 500);
    },
    onChangeEmployee(emp) {
      this.employeeListMenu = false;
      this.allEmployeesList = [...this.allEmployeesBackupList];
      this.searchEmployee = "";
      this.selectedEmployee = emp.employeeId;
      this.requestId = null;
      this.$refs.reimbursementForm.resetValidation();
      this.reimbursementList = [
        {
          isAdd: true,
          reimbursementType: "Expense",
          expenseType: null,
          otherExpense: "",
          allowanceType: null,
          invoiceNo: "",
          invoiceDate: null,
          expenseAmount: null,
          claimAmount: null,
          comment: "",
          invoiceDateMenu: false,
          formatedInvoiceDate: "",
          documents: [],
          oldDocuments: [],
          expenseMaxAmount: 9999999999999.99,
          claimMaxAmount: 9999999999999.99,
          maxInvoiceDate: null,
          minInvoiceDate: null,
          currency: this.payrollCurrency,
          currencyMenu: false,
        },
      ];
    },
    selectCode(code, index) {
      this.isFormDirty = true;
      this.reimbursementList[index].expenseAmount = null;
      this.reimbursementList[index].claimAmount = null;
      this.reimbursementList[index].currency = code.claimCurrencyCode;
      this.reimbursementList[index].currencyMenu = false;
      this.setExpenseMaxAmount(index);
    },
    viewReimbursementDocuments(fileName) {
      this.reimbursementFileName = fileName;
      this.showReimbursementDocuments = true;
    },
    onUpdateInvoiceDate(index) {
      this.isFormDirty = true;
      this.reimbursementList[index].invoiceDateMenu = false;
      this.reimbursementList[index].formatedInvoiceDate = this.formatDate(
        this.reimbursementList[index].invoiceDate
      );
      this.getClaimMaxAmount(index);
    },
    onCloseChip(index, docIndex) {
      this.reimbursementList[index].documents.splice(docIndex, 1);
      this.isFormDirty = true;
      this.isFileChanged = true;
    },
    onActions(type, item) {
      if (type && type.toLowerCase() == "edit") {
        if (this.formAccess.update) {
          item.isAdd = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else if (type && type.toLowerCase() == "delete") {
        if (this.formAccess.delete) {
          this.selectedLineItem = item;
          this.openDeleteConfirmation = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else if (type && type.toLowerCase() == "submit for approval") {
        if (this.formAccess.update) {
          this.submitForApproval();
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      }
    },
    async handleAddNew() {
      if (this.formAccess.add) {
        let { valid } = await this.$refs.reimbursementForm.validate();
        let { valid: claimPurposeValid } =
          await this.$refs.claimPurposeForm.validate();
        const isCommentMandatory = this.commentId?.Mandatory_Field === "Yes";
        const allCommentsValid = this.reimbursementList.every(
          (obj) =>
            (!isCommentMandatory && obj.comment === "") ||
            (obj.comment &&
              obj.comment.length >= 5 &&
              obj.comment.length <= 250)
        );
        if (valid && allCommentsValid && claimPurposeValid) {
          this.reimbursementList.push({
            isAdd: true,
            reimbursementType: "Expense",
            expenseType: null,
            otherExpense: "",
            invoiceNo: "",
            invoiceDate: null,
            expenseAmount: null,
            claimAmount: null,
            maxInvoiceDate: null,
            minInvoiceDate: null,
            comment: "",
            invoiceDateMenu: false,
            formatedInvoiceDate: "",
            documents: [],
            oldDocuments: [],
            expenseMaxAmount: 9999999999999.99,
            claimMaxAmount: 9999999999999.99,
            currency: this.payrollCurrency,
            currencyMenu: false,
          });
        } else {
          const invalidFields = [];
          Object.keys(this.$refs).forEach((refName) => {
            const field = this.$refs[refName];
            if (field && field[0] && field[0].rules) {
              let allTrue = field[0].rules.every((value) => value === true);
              if (field[0].rules.length > 0 && !allTrue) {
                invalidFields.push(refName);
              }
            }
          });
          if (invalidFields.length > 0) {
            const firstErrorField = invalidFields[0];
            this.$nextTick(() => {
              const fieldRef = this.$refs[firstErrorField];
              if (fieldRef && fieldRef[0] && fieldRef[0].$el) {
                const rect = fieldRef[0].$el.getBoundingClientRect();
                window.scrollTo({
                  top: window.scrollY + rect.top - 250, // Adjust as needed
                  behavior: "smooth",
                });
              }
            });
          }
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "You don't have access to perform this action.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    onUploadFile(event, item, index) {
      this.isFormDirty = true;
      this.isFileChanged = true;
      if (event) {
        for (let doc of event) {
          doc.formattedName =
            this.selectedEmployee +
            "?" +
            this.currentTimeStamp +
            "?1?" +
            doc.name;
        }
      }
      this.reimbursementList[index].documents = [...item.documents, ...event];
    },
    async getExpenseTypeList() {
      let vm = this;
      vm.expenseTypeLoading = true;
      try {
        let apiObj = {
          url:
            vm.baseUrl +
            "payroll/reimbursement/get-expense-types/employeeId/" +
            vm.selectedEmployee +
            "/formId/" +
            vm.formId,
          type: "POST",
          dataType: "json",
        };
        let response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response) {
          vm.expenseTypeList = response
            .map((item) => {
              if (item.Expense_Title?.toLowerCase() !== "none of the above") {
                return {
                  Expense_Id: parseInt(item.Expense_Id),
                  Expense_Name: item.Expense_Title,
                  Unit: item.Unit,
                  Max_Amount: item.Max_Amount,
                  Workflow_Id: item.Workflow_Id,
                  Document_Upload: item.Document_Upload,
                };
              }
            })
            .filter((item) => item);
        } else {
          vm.expenseTypeList = [];
        }
      } catch (error) {
        vm.expenseTypeList = [];
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message: "Something went wrong while retrieving expense types",
        };
        vm.showAlert(snackbarData);
      } finally {
        vm.expenseTypeLoading = false;
      }
    },
    getCurrencyList() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_CURRENCY_LIST,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            formId: this.formId,
            status: "Active",
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listCurrencyConversion &&
            response.data.listCurrencyConversion.data
          ) {
            let codes = response.data.listCurrencyConversion.data;
            if (codes && codes.length) {
              codes.splice(0, 0, {
                claimCurrencyCode: codes[0].payrollCurrencyCode,
                claimCurrencyName: codes[0].payrollCurrencyName,
              });
            }
            vm.currencyCodes = codes;
          } else {
            vm.currencyCodes = [];
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
          vm.currencyCodes = [];
        });
    },
    async getAllowanceTypeList() {
      let vm = this;
      vm.allowanceTypeLoading = true;
      vm.reimbursementTypeList = ["Expense"];
      try {
        let apiObj = {
          url:
            vm.baseUrl +
            "default/employee-info/list-approver-details/employeeId/" +
            vm.selectedEmployee +
            "/formName/Reimbursement",
          type: "POST",
          dataType: "json",
        };
        let response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response) {
          if (response.allowances?.length) {
            vm.allowanceTypeList = response.allowances;
            vm.reimbursementTypeList.push("Allowance");
          }
          let orgDateFormat = vm.$store.state.orgDetails.orgDateFormat;
          this.maxDate = moment(response.cutoffDate, orgDateFormat).toDate();
          this.minDate = moment(
            response.previousCutoffDate,
            orgDateFormat
          ).toDate();
        } else {
          vm.allowanceTypeList = [];
          vm.reimbursementTypeList = ["Expense"];
        }
      } catch (error) {
        vm.allowanceTypeList = [];
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message: "Something went wrong while retrieving alllowance types",
        };
        vm.showAlert(snackbarData);
      } finally {
        vm.allowanceTypeLoading = false;
      }
    },
    onUpdateClaimAmount(index) {
      this.isFormDirty = true;
      clearTimeout(this.updateTimeout);
      this.updateTimeout = setTimeout(() => {
        this.getClaimMaxAmount(index);
      }, 500);
    },
    async getClaimMaxAmount(index) {
      let vm = this;
      let lineId = 0;
      let expanseId = this.reimbursementList[index].expenseType || "";
      let invoiceDate =
        moment(this.reimbursementList[index].invoiceDate).format(
          "YYYY-MM-DD"
        ) || "";
      let considerationDate =
        moment(vm.selectedMonthYear).format("YYYY-MM-DD") || "";
      if (expanseId) {
        try {
          let apiObj = {
            url:
              vm.baseUrl +
              "payroll/reimbursement/expense-maxamount/expenseId/" +
              expanseId +
              "/employeeId/" +
              vm.selectedEmployee +
              "/invoiceDate/" +
              invoiceDate +
              "/lineId/" +
              lineId +
              "/considerationDate/" +
              considerationDate,
            type: "POST",
            dataType: "json",
          };
          let response = await this.$store.dispatch(
            "triggerControllerFunction",
            apiObj
          );
          if (response) {
            this.reimbursementList[index].claimMaxAmount =
              response.amount || 9999999999999.99;
            this.setExpenseMaxAmount(index);
            if (response.isInvoiceDateRestrict) {
              this.reimbursementList[index].maxInvoiceDate = moment(
                this.maxDate
              ).format("YYYY-MM-DD");
              this.reimbursementList[index].minInvoiceDate = moment(
                this.minDate
              ).format("YYYY-MM-DD");
            } else {
              this.reimbursementList[index].minInvoiceDate = moment(
                response.dateOfJoin
              ).toDate();
              this.reimbursementList[index].maxInvoiceDate = moment().toDate();
            }
          } else {
            this.reimbursementList[index].claimMaxAmount = 9999999999999.99;
            this.setExpenseMaxAmount(index);
          }
        } catch (error) {
          this.reimbursementList[index].claimMaxAmount = 9999999999999.99;
          this.setExpenseMaxAmount(index);
        }
      } else {
        this.reimbursementList[index].claimMaxAmount = 9999999999999.99;
        this.setExpenseMaxAmount(index);
      }
      if (!this.currencyCodes.length) {
        this.$nextTick(() => {
          this.$refs["claimAmount" + index][0].validate();
        });
      }
    },
    async setExpenseMaxAmount(index) {
      if (
        this.reimbursementList[index].currency !== this.payrollCurrency &&
        this.currencyCodes.length
      ) {
        let currencyCode = this.currencyCodes.find(
          (item) =>
            item.claimCurrencyCode === this.reimbursementList[index].currency
        );
        let amount = this.reimbursementList[index].claimMaxAmount;
        if (
          currencyCode.conversionType?.toLowerCase() === "manual" &&
          currencyCode.conversionValue
        ) {
          amount = (
            this.reimbursementList[index].claimMaxAmount /
            currencyCode.conversionValue
          ).toFixed(2);
        } else {
          let currentCurrency = this.currencyCodes.find(
            (item) =>
              item.claimCurrencyCode === this.reimbursementList[index].currency
          );
          let response = await fetch(
            `https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/${this.payrollCurrency.toLowerCase()}.json`
          );
          let data = await response.json();
          let conversionRate =
            data[this.payrollCurrency.toLowerCase()][
              currentCurrency.claimCurrencyCode.toLowerCase()
            ];
          amount = (
            this.reimbursementList[index].claimMaxAmount * conversionRate
          ).toFixed(2);
        }
        this.reimbursementList[index].expenseMaxAmount = amount;
      } else {
        this.reimbursementList[index].expenseMaxAmount =
          this.reimbursementList[index].claimMaxAmount;
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async handleClickSave() {
      let { valid } = await this.$refs.reimbursementForm.validate();
      let { valid: claimPurposeValid } =
        await this.$refs.claimPurposeForm.validate();
      const isCommentMandatory = this.commentId?.Mandatory_Field === "Yes";
      const allCommentsValid = this.reimbursementList.every(
        (obj) =>
          (!isCommentMandatory && obj.comment === "") ||
          (obj.comment && obj.comment.length >= 5 && obj.comment.length <= 250)
      );
      if (valid && allCommentsValid && claimPurposeValid) {
        this.addUpdateReimbursement();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field[0] && field[0].rules) {
            let allTrue = field[0].rules.every((value) => value === true);
            if (field[0].rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef && fieldRef[0] && fieldRef[0].$el) {
              const rect = fieldRef[0].$el.getBoundingClientRect();
              window.scrollTo({
                top: window.scrollY + rect.top - 250, // Adjust as needed
                behavior: "smooth",
              });
            }
          });
        }
      }
    },
    onChangeExpenseType(event, index) {
      this.isFormDirty = true;
      this.reimbursementList[index].expenseType = event;
      this.reimbursementList[index].expanseTypeName = this.expenseTypeList.find(
        (item) => item.Expense_Id == event
      ).Expense_Name;
      this.getClaimMaxAmount(index);
    },
    async addUpdateReimbursement() {
      let vm = this;
      vm.isLoading = true;
      let countEdit = vm.reimbursementList.filter((item) => item.isAdd);
      let count = 0;
      for (let [index, item] of vm.reimbursementList.entries()) {
        if (item.isAdd) {
          count++;
          try {
            let apiObj = {
              url: vm.baseUrl + "payroll/reimbursement/update-reimbursement",
              type: "POST",
              data: {
                requestId: vm.requestId || 0,
                lineId: item.lineId || 0,
                employeeId: vm.selectedEmployee,
                reimbursementMode: item.reimbursementType || "",
                allowanceTypeId:
                  item.reimbursementType == "Allowance"
                    ? parseInt(item.allowanceType)
                    : 0,
                status: "Pending Approval",
                invoiceNo: item.invoiceNo,
                invoiceDate: moment(item.invoiceDate).isValid()
                  ? moment(item.invoiceDate).format("YYYY-MM-DD")
                  : "",
                claimMonth: moment(vm.selectedMonthYear).isValid()
                  ? moment(vm.selectedMonthYear).format("M,YYYY")
                  : "",
                title:
                  item.reimbursementType == "Expense" ? item.expenseType : 0,
                expenseTypeId:
                  item.reimbursementType == "Expense" ? item.expenseType : 0,
                otherExpense:
                  item.expenseType === 0 && item.otherExpense
                    ? item.otherExpense
                    : "",
                description: item.comment || "",
                invoiceAmount: item.claimAmount || 0,
                expenseAmount: item.expenseAmount || 0,
                currency: item.currency,
                mailUsr: false,
                claimTitle: this.claimTitle,
              },
              dataType: "json",
            };
            let response = await this.$store.dispatch(
              "triggerControllerFunction",
              apiObj
            );
            if (response?.success) {
              vm.reimbursementList[index].isAdd = false;
              vm.reimbursementList[index].lineId = response.Line_Item_Id;
              vm.requestId = response.Request_Id;

              await vm.handleReimbursementDocuments(
                index,
                response.Line_Item_Id
              );

              if (count == countEdit.length) {
                vm.isFormDirty = false;
                vm.isLoading = false;
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: response.msg,
                };
                vm.showAlert(snackbarData);
                vm.getReimbursementInfo();
                vm.approvalStatus = "Draft";
              }
            } else {
              vm.isLoading = false;
              vm.isFormDirty = false;
              let snackbarData = {
                isOpen: true,
                type: "warning",
                message:
                  response.msg ||
                  "Something went wrong while adding claim request. Please try after some time.",
              };
              vm.showAlert(snackbarData);
            }
          } catch (error) {
            vm.isLoading = false;
            vm.isFormDirty = false;
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message: "Something went wrong while adding reimbursement",
            };
            vm.showAlert(snackbarData);
          }
        }
      }
      vm.isLoading = false;
    },
    async handleReimbursementDocuments(index, lineId) {
      const oldDocs = this.reimbursementList[index]?.oldDocuments || [];
      const newDocs = this.reimbursementList[index]?.documents || [];
      const oldDocsMap = new Map(
        oldDocs.map((doc) => [doc.formattedName, doc])
      );
      const newDocsMap = new Map(
        newDocs.map((doc) => [doc.formattedName, doc])
      );
      // 🔹 Detect New or Changed Documents
      const docsToBeUploaded = newDocs.filter((doc) => {
        const oldDoc = oldDocsMap.get(doc.formattedName);
        return (
          !oldDoc ||
          oldDoc.size !== doc.size ||
          oldDoc.lastModified !== doc.lastModified
        );
      });
      if (docsToBeUploaded.length && this.isFileChanged) {
        await Promise.all(
          docsToBeUploaded.map((doc) => this.uploadFileContents(doc))
        );
        await this.uploadFileNames(index);
      }
      const docsToBeDeleted = oldDocs.filter(
        (doc) => !newDocsMap.has(doc.formattedName)
      );
      if (docsToBeDeleted.length && this.isFileChanged) {
        await Promise.all(
          docsToBeDeleted.map(async (doc) => {
            await this.deleteDucumentsFile(doc.formattedName);
            await this.deleteReimbursementDocument(lineId, doc.formattedName);
          })
        );
      }
    },
    async uploadFileContents(file) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Reimbursement/";
      try {
        await vm.$store.dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + file.formattedName,
          action: "upload",
          type: "documents",
          fileContent: file,
        });
      } catch (error) {
        let snackbarData = {
          isOpen: true,
          message: error,
          type: "warning",
        };
        vm.showAlert(snackbarData);
      }
    },
    async uploadFileNames(index) {
      let vm = this;
      let documentList = vm.reimbursementList[index].documents
        .filter((item) => item.size)
        .map((item) => {
          return {
            Name: item.formattedName,
            Size: item.size,
          };
        });
      try {
        let apiObj = {
          url:
            vm.baseUrl +
            "payroll/reimbursement/update-reimbursement-upload-files",
          type: "POST",
          dataType: "json",
          data: {
            lineItemId: vm.reimbursementList[index].lineId,
            reimbursementFiles: documentList,
          },
        };
        let response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          if (
            response.uploadReimbursementFiles.uploadReimbursementFiles !==
            documentList.length
          ) {
            let snackbarData = {
              isOpen: true,
              message: "Only few files were uploaded successfully",
              type: "warning",
            };
            vm.showAlert(snackbarData);
          }
        }
      } catch (error) {
        let snackbarData = {
          isOpen: true,
          warning: "warning",
          message: "Something went wrong while updating document names",
        };
        vm.showAlert(snackbarData);
      }
    },
    getReimbursementInfo() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_REIMBURSEMENT_INFO,
          fetchPolicy: "no-cache",
          client: "apolloClientAC",
          variables: {
            formId: vm.formId,
            requestId: parseInt(vm.requestId),
          },
        })
        .then((response) => {
          if (
            response?.data?.retrieveReimbursement?.ReimbursementInfo?.length
          ) {
            vm.claimTitle =
              response.data.retrieveReimbursement.ReimbursementInfo[0].Claim_Title;
            let listDetails =
              response.data.retrieveReimbursement.ReimbursementInfo.map(
                (item) => {
                  let documents = item.File_Details?.split(",").filter(
                    (item) => item
                  );
                  documents = documents.map((doc) => {
                    return {
                      name: doc.split("|")[0].split("?")[3],
                      formattedName: doc.split("|")[0],
                    };
                  });
                  return {
                    isAdd: false,
                    lineId: item.Line_Item_Id,
                    reimbursementType: item.Reimbursement_Mode,
                    expenseType: item.Expense_Type_Id,
                    expanseTypeName: item.Expense_Type,
                    otherExpense: item.Other_Expense,
                    allowanceType: item.Allowance_Type_Id?.toString(),
                    allowanceTypeName: item.Allowance_Type_Name,
                    invoiceNo: item.Invoice_No,
                    invoiceDate: new Date(item.Mob_Invoice_Date),
                    claimAmount: item.Actual_Amount,
                    expenseAmount: item.Claim_Currency_Amount,
                    comment: item.Description || "",
                    invoiceDateMenu: false,
                    formatedInvoiceDate: this.formatDate(item.Mob_Invoice_Date),
                    documents: documents,
                    oldDocuments: JSON.parse(JSON.stringify(documents)),
                    expenseMaxAmount: 9999999999999.99,
                    claimMaxAmount: 9999999999999.99,
                    currency: item.Claim_Currency_Code,
                    currencyMenu: false,
                  };
                }
              );
            vm.reimbursementList = listDetails;
          } else {
            vm.reimbursementList = [];
            vm.$emit("refetch-list");
          }
          vm.listLoading = false;
        })
        .catch((error) => {
          vm.listLoading = false;
          vm.handleReimbursementInfoError(error);
        });
    },
    handleReimbursementInfoError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "reimbursement",
        isListError: false,
      });
    },
    async deleteLineItem() {
      let vm = this;
      vm.isLoading = true;
      vm.openDeleteConfirmation = false;
      for (let doc of vm.selectedLineItem.documents) {
        await vm.deleteDucumentsFile(doc.formattedName);
      }
      try {
        let apiObj = {
          url:
            vm.baseUrl +
            "payroll/reimbursement/delete-reimbursement-request/lineItemId/" +
            vm.selectedLineItem.lineId +
            "/requestId/" +
            vm.requestId,
          type: "POST",
          dataType: "json",
        };
        let response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          let snackbarData = {
            isOpen: true,
            message: "Record deleted successfully.",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.getReimbursementInfo();
        } else {
          let snackbarData = {
            isOpen: true,
            message: response?.msg,
            type: "warning",
          };
          vm.showAlert(snackbarData);
        }
      } catch (error) {
        let snackbarData = {
          isOpen: true,
          message: "Something went wrong while deleting the record.",
          type: "warning",
        };
        vm.showAlert(snackbarData);
      } finally {
        vm.isLoading = false;
        vm.selectedLineItem = null;
      }
    },
    async deleteReimbursementDocument(lineId, fileName) {
      let vm = this;
      try {
        let apiObj = {
          url:
            vm.baseUrl +
            "payroll/reimbursement/delete-reimbursement-upload-files/line_Item_Id/" +
            lineId,
          type: "POST",
          dataType: "json",
          data: {
            reimbursementFileName: fileName,
          },
        };
        await this.$store.dispatch("triggerControllerFunction", apiObj);
      } catch (error) {
        let snackbarData = {
          isOpen: true,
          message: "Something went wrong while deleting the record document.",
          type: "warning",
        };
        vm.showAlert(snackbarData);
      }
    },
    async deleteDucumentsFile(files) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Reimbursement/";
      await vm.$store.dispatch("deletes3File", {
        fileName: fileUploadUrl + files,
        type: "documents",
      });
    },
    async submitForApproval() {
      let vm = this;
      vm.isLoading = true;
      try {
        let apiObj = {
          url: vm.baseUrl + "/payroll/reimbursement/update-reimbursement",
          type: "POST",
          data: {
            requestId: vm.requestId,
            employeeId: vm.selectedEmployee,
            approverId: null,
            status: "Draft",
            saveAsDraft: 0,
            travelId: 0,
            SubmissionDate: vm.maxDate,
            mailUsr: true,
          },
          dataType: "json",
        };
        let response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          let snackbarData = {
            isOpen: true,
            message: response.msg,
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-list");
        } else {
          let snackbarData = {
            isOpen: true,
            message: response.msg,
            type: "warning",
          };
          vm.showAlert(snackbarData);
        }
      } catch (error) {
        let snackbarData = {
          isOpen: true,
          message: "Something went wrong while submitting for approval.",
          type: "warning",
        };
        vm.showAlert(snackbarData);
      } finally {
        vm.isLoading = false;
      }
    },
  },
};
</script>
<style scoped>
:deep(.employee-list-btn > .v-btn__content) {
  max-width: 100%;
  white-space: wrap;
}
:deep(.v-chip__content) {
  max-width: 85%;
  white-space: wrap;
}
table {
  border-collapse: collapse;
  width: 100%;
}

th {
  text-align: left;
  padding: 8px;
}

td {
  text-align: left;
  padding: 8px;
  background-color: #ffffff;
}

th:first-child {
  position: sticky;
  left: 0;
  border: 0px;
}

th:last-child {
  border: 0px;
}

thead th {
  position: sticky;
  top: 0;
  z-index: 2000;
}
.small-caption {
  font-size: 0.625rem;
  font-weight: 400;
  line-height: 1.667;
  letter-spacing: 0.0333333333em;
}
@media screen and (max-width: 600px) {
  thead {
    display: contents !important;
  }

  thead th {
    position: relative;
  }

  th:first-child {
    position: relative;
  }
}
</style>
