<template>
  <div class="pa-5">
    <v-row class="d-flex justify-center">
      <v-col cols="12" xs="12" sm="12" md="8" class="d-flex align-center">
        <div class="rounded-lg bg-white pa-6">
          <v-row v-if="!isViewForm">
            <v-col class="d-flex justify-end">
              <v-btn
                v-if="formAccess && formAccess.update"
                @click="onChangeEdit()"
                class="primary"
                variant="text"
              >
                <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
              </v-btn>
            </v-col>
            <v-col cols="12" class="d-flex align-center mb-4">
              <div
                v-if="windowWidth > 600"
                class="bg-hover pa-3 rounded-pill"
                style="height: 45px; width: 45px"
              >
                <v-icon color="primary" size="20">fas fa-unlock-alt</v-icon>
              </div>
              <div class="pl-3 text-h6 text-grey-darken-1 font-weight-bold">
                This section allows you to manage and release the hiring
                forecast for Hiring Managers. Adjust the settings as needed.
              </div>
            </v-col>
            <v-col cols="12" class="d-flex align-center py-6 flex-wrap d-flex">
              <div class="pr-2">Allow hiring forecast until</div>
              <div class="font-weight-bold">{{ formattedEndMonthDate }}.</div>
            </v-col>
            <v-col cols="12" class="d-flex align-center py-6 flex-wrap d-flex">
              <div class="pr-2">Last month of hiring forecast</div>
              <div class="font-weight-bold">{{ formattedMonth?.month }}.</div>
            </v-col>
            <v-col cols="12" class="align-center py-6 flex-wrap d-flex">
              <div class="pr-2">
                Select the role of the users who should receive the notification
                for hiring forecast update
              </div>
              <div
                class="d-flex"
                v-if="formattedRoles && formattedRoles.length"
              >
                <div
                  class="font-weight-bold"
                  v-for="(roles, index) in formattedRoles"
                  :key="index"
                >
                  {{
                    `${roles.Roles_Name}${
                      index !== formattedRoles.length - 1 ? "," : "."
                    }`
                  }}
                </div>
              </div>
            </v-col>
          </v-row>
          <v-row v-else>
            <v-col cols="12" class="d-flex align-center my-4">
              <div
                v-if="windowWidth > 600"
                class="bg-hover pa-3 rounded-pill"
                style="height: 45px; width: 45px"
              >
                <v-icon color="primary" :size="20">fas fa-key</v-icon>
              </div>
              <div class="pl-3 text-h6 text-grey-darken-1 font-weight-bold">
                This section allows you to manage and release the hiring
                forecast for Hiring Managers. Adjust the settings as needed.
              </div>
            </v-col>
            <v-form ref="hiringForecastForm">
              <v-col
                cols="12"
                class="d-flex align-center py-6"
                :class="windowWidth > 600 ? '' : 'flex-wrap'"
              >
                <div class="pr-4">Allow hiring forecast until</div>
                <v-menu
                  v-model="endMonthDateMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="End Date"
                      style="max-width: 300px"
                      v-model="formattedEndMonthDate"
                      prepend-inner-icon="fas fa-calendar"
                      v-bind="props"
                      variant="solo"
                      :rules="[
                        required('End Date', formattedEndMonthDate),
                        currentDateComparison(
                          'Release date',
                          selectedEndMonthDate
                        ),
                      ]"
                      readonly
                    >
                      <template v-slot:label>
                        End date <span style="color: red">*</span></template
                      ></v-text-field
                    >
                  </template>
                  <v-date-picker
                    :min="minDateFormat"
                    v-model="utcEndMonthDate"
                    @update:model-value="onChangeEndDate($event)"
                  />
                </v-menu>
              </v-col>
              <v-col cols="12" class="d-flex align-center py-6 flex-wrap">
                <div class="pr-4">Last month of hiring forecast</div>
                <CustomSelect
                  label="End month"
                  :isRequired="true"
                  :rules="[required('End month', selectedEndMonth)]"
                  :isAutoComplete="true"
                  style="
                    max-width: 200px !important;
                    min-width: 200px !important;
                  "
                  v-model="selectedEndMonth"
                  :disabled="totalNumberHiringForecast > 0"
                  :item-selected="selectedEndMonth"
                  :items="monthList"
                  item-title="month"
                  item-value="id"
                  @selected-item="onChange('endMonth')"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                class="d-flex align-center py-3"
                :class="windowWidth > 600 ? '' : 'flex-column'"
              >
                <div :class="windowWidth > 600 ? 'pr-4' : 'pr-1'">
                  Select the role of the users who should receive the
                  notification for hiring forecast update
                </div>
                <CustomSelect
                  v-model="selectedRole"
                  :items="rolesList"
                  label="Role"
                  :itemSelected="selectedRole"
                  @update:modelValue="onChange($event, 'role')"
                  :is-auto-complete="true"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    closableChips: true,
                    clearable: true,
                  }"
                  item-title="Roles_Name"
                  item-value="Roles_Id"
                  style="width: 300px !important"
                ></CustomSelect>
              </v-col>
            </v-form>
            <v-col cols="12" class="d-flex justify-end">
              <v-btn
                class="my-2 mx-4"
                color="primary"
                variant="outlined"
                rounded="md"
                @click="onClose"
                >Cancel</v-btn
              >
              <v-btn
                class="my-2 px-4"
                color="primary"
                rounded="md"
                :disabled="!isFormDirty"
                @click="submitForm"
                >Next</v-btn
              >
            </v-col>
          </v-row>
        </div>
      </v-col>
    </v-row>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <CustomEmail
    v-if="showCustomEmail"
    ref="customEmail"
    :formId="285"
    :isOverlay="true"
    typeOfTemplate="hiringForecastSettings"
    typeOfSchedule="noncalendar"
    :template-email="[]"
    :emailFullData="selectedRolesEmailList"
    :additional-email="customEmailList"
    :template-data="templateData"
    submitText="Notify"
    :closeableChips="true"
    @custom-email-sent="onSendCustomEmail()"
    @without-custom-email="onCloseCustomEmail()"
    @custom-email-cancel="this.showCustomEmail = false"
  />
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_AND_UPDATE_FORECAST_SETTINGS,
  RETRIEVE_ROLES_EMAIL_LIST,
} from "@/graphql/mpp/manPowerPlanningQueries";
import { convertUTCToLocal } from "@/helper";
import validationRules from "@/mixins/validationRules";
import CustomEmail from "@/views/common/customEmail/CustomEmail.vue";
import moment from "moment";

export default {
  name: "AddEditHiringForecast",
  emits: ["on-close-form", "refresh-list"],
  props: {
    rolesList: {
      type: Object,
      default: () => {
        return {};
      },
    },
    rolesLoader: {
      type: Boolean,
      default: false,
    },
    editFormAccess: {
      type: Boolean,
      required: false,
    },
    hireForecastData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    totalNumberHiringForecast: {
      type: Number,
      default: 0,
    },
  },
  mixins: [validationRules],
  data() {
    return {
      selectedEndMonth: "",
      formattedEndMonthDate: "",
      formattedMonthDate: null,
      selectedEndMonthDate: null,
      endMonthDateMenu: null,
      isFormDirty: false,
      isViewForm: false,
      showCustomEmail: false,
      isLoading: false,
      selectedRole: null,
      selectedRolesEmailList: [],
      customEmailList: [],
      monthList: moment
        .months()
        .map((month, index) => ({ id: index + 1, month: month })),
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date != "0000-00-00") {
          let formattedDate = this.convertUTCToLocal(date);
          return formattedDate.split(" ")[0];
        } else return "-";
      };
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formattedMonth() {
      if (this.selectedEndMonth) {
        return this.monthList.find((list) => list.id === this.selectedEndMonth);
      }
      return null;
    },
    minDateFormat() {
      return moment().format("YYYY-MM-DD");
    },
    formattedRoles() {
      if (
        this.selectedRole &&
        this.selectedRole.length &&
        this.rolesList &&
        this.rolesList.length
      ) {
        return this.rolesList.filter((list) =>
          this.selectedRole.includes(list.Roles_Id)
        );
      }
      return null;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    loginEmployeeName() {
      return (
        this.$store.state.userDetails.employeeFirstName +
        " " +
        this.$store.state.userDetails.employeeLastName
      );
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    addressLine1() {
      let organization = {};
      if (this.fieldForce) {
        organization = this.$store.state.orgDetails.serviceProvider;
      } else {
        organization = this.$store.state.orgDetails.organization;
      }
      let line1 = [];
      if (organization.street1) {
        line1.push(organization.street1);
      }
      if (organization.street2) {
        line1.push(organization.street2);
      }
      return line1.length > 0 ? line1.join(",") : "";
    },
    addressLine2() {
      const { organization } = this.$store.state.orgDetails;
      let line2 = [];
      if (organization.city) {
        line2.push(organization.city);
      }
      if (organization.state) {
        line2.push(organization.state);
      }
      if (organization.country) {
        line2.push(organization.country);
      }
      if (organization.pincode) {
        line2.push(organization.pincode);
      }
      return line2.length > 0 ? line2.join(",") : "";
    },
    templateData() {
      let templateData = {
        Date: this.formattedEndMonthDate,
        Url: this.baseUrl + "v3/man-power-planning/hiring-forecast",
        Candidate_Name: this.loginEmployeeName,
        Company_Name: this.companyName,
        Company_Address_1: this.addressLine1,
        Company_Address_2: this.addressLine2,
      };
      return templateData;
    },
    utcEndMonthDate() {
      if (this.selectedEndMonthDate) {
        let date = this.convertUTCToLocal(this.selectedEndMonthDate);
        return moment(date, "DD/MM/YYYY HH:mm:ss").toDate();
      }
      return null;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  watch: {
    selectedEndMonthDate(val) {
      if (val) {
        this.endMonthDateMenu = false;
        this.formattedEndMonthDate = this.formatDate(val);
        this.formattedMonthDate = new Date(val);
        let currentTime = new Date();
        this.formattedMonthDate.setHours(currentTime.getHours());
        this.formattedMonthDate.setMinutes(currentTime.getMinutes());
        this.formattedMonthDate.setSeconds(currentTime.getSeconds());
        this.formattedMonthDate.setMilliseconds(currentTime.getMilliseconds());
      }
    },
    hireForecastData(val) {
      if (val) {
        this.setFormHiringForecast();
      }
    },
  },
  mounted() {
    this.isViewForm = this.editFormAccess;
    this.setFormHiringForecast();
  },
  components: {
    CustomSelect,
    CustomEmail,
  },
  methods: {
    convertUTCToLocal,
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeEndDate(val) {
      this.isFormDirty = true;
      this.selectedEndMonthDate = val;
    },
    onChange(value, field) {
      if (field === "role") {
        if (value.length == 0) {
          this.selectedRole = null;
        }
      }
      this.isFormDirty = true;
    },
    setFormHiringForecast() {
      if (
        this.hireForecastData &&
        Object.keys(this.hireForecastData) &&
        Object.keys(this.hireForecastData).length
      ) {
        this.selectedEndMonth = this.hireForecastData.End_Month;
        if (
          this.hireForecastData?.Roles_Ids &&
          this.hireForecastData?.Roles_Ids.length
        ) {
          const roleIds = this.hireForecastData?.Roles_Ids?.split(",");
          this.selectedRole = roleIds.map((item) => parseInt(item));
        }
        if (this.hireForecastData.Release_Date) {
          this.selectedEndMonthDate = this.hireForecastData.Release_Date;
        }
      } else {
        this.selectedEndMonth = "";
        this.selectedRole = null;
        this.selectedEndMonthDate = null;
      }
    },
    onClose() {
      if (!this.editFormAccess) this.isViewForm = false;
      else this.$emit("on-close-form");
      this.setFormHiringForecast();
      this.isFormDirty = false;
    },
    onChangeEdit() {
      this.isViewForm = true;
    },
    onSendCustomEmail() {
      this.showCustomEmail = false;
      this.addEditHiringSettings();
    },
    onCloseCustomEmail() {
      this.showCustomEmail = false;
      this.addEditHiringSettings();
    },
    async submitForm() {
      const { valid } = await this.$refs.hiringForecastForm.validate();
      if (valid) {
        this.retrieveRolesEmailList();
      }
    },
    addEditHiringSettings() {
      this.isLoading = true;
      let requestPayload = {
        forecastSettingsId: 0,
        releaseDate: moment(this.formattedMonthDate).utc().format(),
        endMonth: this.selectedEndMonth,
      };
      if (this.selectedRole && this.selectedRole.length) {
        requestPayload.roleIds = this.selectedRole.map((list) =>
          parseInt(list)
        );
      }
      if (
        this.hireForecastData &&
        Object.keys(this.hireForecastData) &&
        Object.keys(this.hireForecastData).length
      ) {
        requestPayload.forecastSettingsId =
          this.hireForecastData.Forecast_Settings_Id;
      }
      this.$apollo
        .mutate({
          mutation: ADD_AND_UPDATE_FORECAST_SETTINGS,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          this.isLoading = false;
          if (
            res &&
            res.data &&
            res.data.addUpdateForeCastSettings &&
            res.data.addUpdateForeCastSettings.message
          ) {
            this.$emit("refresh-list");
            this.onClose();
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: res.data.addUpdateForeCastSettings.message,
            };
            this.showAlert(snackbarData);
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.addEditErrorHandle(err);
        });
    },
    retrieveRolesEmailList() {
      this.isLoading = true;
      this.$apollo
        .query({
          query: RETRIEVE_ROLES_EMAIL_LIST,
          client: "apolloClientAG",
          variables: {
            roleIds:
              this.selectedRole && this.selectedRole.length
                ? this.selectedRole?.map((list) => parseInt(list))
                : [],
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.isLoading = false;
          if (res && res.data && res.data.retrieveEmployeeRoleEmail) {
            const tempEmailList = res.data.retrieveEmployeeRoleEmail;
            this.showCustomEmail = true;
            if (tempEmailList.emailList && tempEmailList.emailList.length) {
              this.selectedRolesEmailList = tempEmailList.emailList.map(
                (item) => {
                  return {
                    ...item,
                    Emp_Detail: `${item?.Employee_Name} ${
                      item?.User_Defined_EmpId
                        ? " - " + item.User_Defined_EmpId
                        : ""
                    }`,
                  };
                }
              );
            } else {
              this.selectedRolesEmailList = [];
            }
            if (
              tempEmailList.nonRoleEmailList &&
              tempEmailList.nonRoleEmailList.length
            ) {
              this.customEmailList = tempEmailList.nonRoleEmailList.map(
                (item) => {
                  return {
                    ...item,
                    Emp_Detail: `${item?.Employee_Name} ${
                      item?.User_Defined_EmpId
                        ? " - " + item.User_Defined_EmpId
                        : ""
                    }`,
                  };
                }
              );
            }
          }
        })
        .catch((err) => {
          this.selectedRolesEmailList = [];
          this.isLoading = false;
          this.handleRetrieveError(err);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleRetrieveError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "Hiring Forecast",
        isListError: false,
      });
    },
    addEditErrorHandle(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "Hiring Forecast details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
  },
};
</script>
