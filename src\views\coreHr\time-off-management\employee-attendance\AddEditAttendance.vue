<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">Attendance</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mr-1">
            <v-tooltip bottom>
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="isFormDirty ? {} : props"
                  rounded="lg"
                  class="mb-2 secondary"
                  variant="elevated"
                  type="submit"
                  @click="isFormDirty ? validateAttendanceForm() : {}"
                >
                  <span class="px-2 primary">Submit</span>
                </v-btn>
              </template>
              <div v-if="!isFormDirty">There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon class="mt-n2" color="primary" @click="closeAddForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card-text style="height: calc(100vh - 300px); overflow: scroll">
        <v-form ref="AttendanceAddForm" @submit.prevent="[]">
          <v-row class="px-sm-4 px-md-6 pt-sm-4">
            <v-col
              v-if="
                formAccess &&
                $route.path !== '/employee-self-service/attendance'
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                v-model="employeeId"
                ref="employeeId"
                :isAutoComplete="true"
                :isRequired="true"
                :rules="[required('Employee Name', employeeId)]"
                :items="allEmployeesList"
                :itemSelected="employeeId"
                item-title="employeeData"
                item-value="employeeId"
                variant="solo"
                label="Employee Name"
                :disabled="true"
                @selected-item="employeeId = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>

            <!-- Check In Date -->
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-menu
                v-model="checkInDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="formattedCheckInDate"
                    v-model="formattedCheckInDate"
                    :value="displayFormattedDate(formattedCheckInDate)"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Check In Date', formattedCheckInDate)]"
                    :disabled="isEdit ? formAccess.update : formAccess.add"
                    readonly
                    v-bind="props"
                    variant="solo"
                    @selected-item="formattedCheckInDate = $event"
                    @update:model-value="deductFormChange()"
                  >
                    <template v-slot:label>
                      Check In Date
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </template>

                <!-- Date Picker -->
                <v-date-picker
                  v-model="selectedCheckInDate"
                  :min="
                    splitConsiderationDate(workScheduleInput.Consideration_From)
                  "
                  :max="
                    splitConsiderationDate(workScheduleInput.Consideration_To)
                  "
                  @update:model-value="deductFormChange()"
                ></v-date-picker>
              </v-menu>
            </v-col>

            <!-- Check In Time -->
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-menu
                v-model="checkInTimeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="formattedCheckInTime"
                    v-model="formattedCheckInTime"
                    prepend-inner-icon="fas fa-clock"
                    :rules="[required('Check In Time', formattedCheckInTime)]"
                    readonly
                    :disabled="formattedCheckInDate ? false : true"
                    v-bind="props"
                    variant="solo"
                    @selected-item="formattedCheckInTime = $event"
                    @update:model-value="deductFormChange()"
                  >
                    <template v-slot:label>
                      Check In Time
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </template>

                <v-time-picker
                  v-model="selectedCheckInTime"
                  :min="checkInMinTimeLimit(workScheduleInput)"
                  :max="checkInMaxTimeLimit(workScheduleInput)"
                  @update:minute="checkInTimeMenu = false"
                  format="24hr"
                  @update:model-value="deductFormChange()"
                ></v-time-picker>
              </v-menu>
            </v-col>

            <!-- Check In Work Place -->
            <v-col
              v-if="
                this.formattedCheckInTime &&
                this.formattedCheckInDate &&
                this.enableWorkPlace === 1
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                :items="workPlacesList"
                :isLoading="workPlaceLoading"
                v-model="checkInWorkPlace"
                label="Check In Work Place"
                itemValue="Work_Place_Id"
                itemTitle="Work_Place"
                ref="checkInWorkPlace"
                :isRequired="true"
                variant="solo"
                :rules="[required('Check In Work Place', checkInWorkPlace)]"
                :isAutoComplete="true"
                @selected-item="checkInWorkPlace = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>

            <!-- Check Out Date -->
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-menu
                v-model="checkOutDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="formattedCheckOutDate"
                    v-model="formattedCheckOutDate"
                    :value="displayFormattedDate(formattedCheckOutDate)"
                    prepend-inner-icon="fas fa-calendar"
                    readonly
                    v-bind="props"
                    :rules="[validateDates(formattedCheckOutDate)]"
                    clearable
                    variant="solo"
                    @selected-item="formattedCheckOutDate = $event"
                    @update:model-value="checkOutDateCleared()"
                  >
                    <template v-slot:label> Check Out Date </template>
                  </v-text-field>
                </template>

                <!-- Date Picker -->
                <v-date-picker
                  v-model="selectedCheckOutDate"
                  :month="getMonth"
                  :year="getYear"
                  :min="formattedCheckInDate"
                  :max="
                    splitConsiderationDate(workScheduleInput.Consideration_To)
                  "
                  @update:model-value="deductFormChange()"
                ></v-date-picker>
              </v-menu>
            </v-col>

            <!-- Check Out Time -->
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-menu
                v-model="checkOutTimeMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="formattedCheckOutTime"
                    v-model="formattedCheckOutTime"
                    prepend-inner-icon="fas fa-clock"
                    readonly
                    :disabled="formattedCheckOutDate ? false : true"
                    v-bind="props"
                    variant="solo"
                    @selected-item="formattedCheckOutTime = $event"
                    :rules="[
                      validateCheckOutTime(formattedCheckOutTime),
                      selectedCheckOutDate
                        ? required('Check Out Time', formattedCheckOutTime)
                        : true,
                    ]"
                    @update:model-value="deductFormChange()"
                  >
                    <template v-slot:label> Check Out Time </template>
                  </v-text-field>
                </template>

                <!-- Time Picker -->
                <v-time-picker
                  v-model="selectedCheckOutTime"
                  :min="checkOutMinTimeLimit()"
                  :max="checkOutMaxTimeLimit(workScheduleInput)"
                  @update:minute="checkOutTimeMenu = false"
                  format="24hr"
                  @update:model-value="deductFormChange()"
                ></v-time-picker>
              </v-menu>
            </v-col>

            <!-- Check Out Work Place -->
            <v-col
              v-if="
                this.formattedCheckOutTime &&
                this.formattedCheckOutDate &&
                this.enableWorkPlace === 1
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                :items="workPlacesList"
                :isLoading="workPlaceLoading"
                v-model="checkOutWorkPlace"
                label="Check Out Work Place"
                itemValue="Work_Place_Id"
                itemTitle="Work_Place"
                ref="checkOutWorkPlace"
                variant="solo"
                :isRequired="true"
                :rules="[required('Check Out Work Place', checkOutWorkPlace)]"
                :isAutoComplete="true"
                @selected-item="checkOutWorkPlace = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>

            <!-- Add Comment -->
            <v-col cols="12" class="pl-sm-4 pl-md-6" style="height: 100px">
              <v-textarea
                v-model="comment"
                rows="4"
                hide-details="auto"
                variant="solo"
                label="Add Comment"
                counter="500"
                :rules="[minMaxStringValidation('Comment', comment, 5, 500)]"
                @selected-item="comment = $event"
                @update:model-value="deductFormChange()"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isLoadingCard"
        scrim="#fff"
      >
        <v-progress-circular color="secondary" indeterminate size="54">
        </v-progress-circular>
      </v-overlay>
    </v-card>
    <AppWarningModal
      v-if="showConfirmation"
      :open-modal="showConfirmation"
      imgUrl="common/exit_form"
      confirmation-heading="Are you sure to exit this form?"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 secondary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import mixpanel from "mixpanel-browser";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import moment from "moment";
import { LIST_WORK_PLACES } from "@/graphql/organisation/employeetype/employeeTypeQueries";

export default {
  name: "AddEditAttendance",
  mixins: [validationRules],
  components: {
    CustomSelect,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    itemLogList: {
      type: Array, // Represents attendance details
      default: () => [],
    },
    selectedEmployee: {
      type: [Object, Number],
      default: null,
    },
    formAccess: {
      type: Object,
      default: () => {
        return {};
      },
    },
    landedFormName: {
      type: String,
      required: true,
    },
    openViewOverlayForm: {
      type: Boolean,
      default: false,
    },
    workScheduleDetails: {
      type: Object,
      required: true,
    },
  },

  emits: ["close-form", "form-updated"],
  data: () => ({
    //others
    isFormDirty: false,
    showConfirmation: false,
    // Form data
    selectedItem: null,
    selectedCheckInDate: null,
    formattedCheckInDate: "",
    formattedCheckInTime: "",
    checkInDateMenu: false,
    checkInTimeMenu: false,
    selectedCheckOutDate: null,
    formattedCheckOut: "",
    comment: "",
    selectedCheckInTime: null,
    selectedCheckOutTime: null,
    formattedCheckOutDate: "",
    formattedCheckOutTime: "", // Updated variable for Check-Out Time
    checkOutDateMenu: false, // Added for Check-Out Date menu
    checkOutTimeMenu: false, // Added for Check-Out Time menu
    workPlacesList: [],
    originalWorkPlacesList: [],
    allEmployeesList: [],
    managers: [],
    checkInWorkPlace: null,
    checkOutWorkPlace: null,
    employeeId: null,
    attendanceId: null,
    approvalStatus: "Draft",
    punchType: "Punch_In",
    excludeBreakHours: 0,
    callingFrom: "team",
    enableWorkPlace: 0,

    // Loading and validation states
    isLoadingCard: false,
    validationMessages: [],
    showValidationAlert: false,
    workScheduleInput: null,
    isInitialized: false,
  }),

  computed: {
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formId() {
      let fId = this.callingFrom === "team" ? "304" : "305";
      return parseInt(fId);
    },
    getMonth() {
      if (this.selectedCheckInDate) {
        return moment(this.selectedCheckInDate).format("MM") - 1;
      } else {
        return moment().format("MM") - 1;
      }
    },
    getYear() {
      if (this.selectedCheckInDate) {
        return moment(this.selectedCheckInDate).format("YYYY");
      } else {
        return moment().format("YYYY");
      }
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formatDate() {
      return (date) => {
        return moment(date, "YYYY-MM-DD", true).isValid()
          ? moment(date).format("YYYY-MM-DD")
          : false;
      };
    },

    selectProperties() {
      return {
        multiple: false,
        chips: false,
        clearable: true,
        closableChips: false,
      };
    },
    isSelected() {
      return (value) => {
        if (this.selectProperties.multiple) {
          return this.selectedItem && this.selectedItem.includes(value);
        } else return value === this.selectedItem;
      };
    },
    formatTime() {
      return (time) => {
        if (time && time !== "Invalid Time") {
          // Use the appropriate format returned by the time picker
          const orgTimeFormat =
            this.$store.state.orgDetails.orgTimeFormat || "HH:mm"; // Default to 24-hour format
          return moment(time, "HH:mm").format(orgTimeFormat); // Adjust parsing to match picker
        }
        return "";
      };
    },
  },
  watch: {
    selectedEmployee(newVal) {
      const selectedEmp = this.allEmployeesList.find(
        (emp) => emp.employeeId === newVal
      );
      this.enableWorkPlace = selectedEmp?.enableWorkPlace || 0;
    },
    selectedCheckInDate(val) {
      if (this.isInitialized) {
        if (val) {
          // this.retrieveProbationDate();
          this.checkInDateMenu = false;
          let dateValue = this.formatDate(val);
          this.formattedCheckInDate = dateValue;
          //Update the check in time and check out time as null to avoid validation error
          this.selectedCheckInTime = null;
          this.selectedCheckOutDate = null;
        }
      }
    },
    selectedCheckInTime(val) {
      if (this.isInitialized) {
        if (val) {
          let timeValue = this.formatTime(val);
          this.formattedCheckInTime = timeValue; // Fallback to val if formatting fails
        } else {
          this.formattedCheckInTime = null;
        }
      }
    },
    selectedCheckOutDate(val) {
      if (this.isInitialized) {
        this.checkOutDateMenu = false; // Updated menu reference
        if (val) {
          let dateValue = this.formatDate(val);
          this.formattedCheckOutDate = dateValue; // Updated variable reference
        } else {
          this.formattedCheckOutDate = null;
        }
        this.selectedCheckOutTime = null;
      }
    },
    selectedCheckOutTime(val) {
      if (this.isInitialized) {
        if (val) {
          this.formattedCheckOutTime = this.formatTime(val); // Added formatting logic
        } else {
          this.formattedCheckOutTime = null;
        }
      }
    },
    editFormData() {
      if (this.isEdit && this.editFormData) {
        let { Presentational_Name, Employee_Name, details } = this.editFormData;
        details = details && details.length > 0 ? details[0] : details;
        let Employee_Id = this.selectedEmployee;

        if (details) {
          const {
            Attendance_Id,
            Total_Hours,
            PunchIn_Date,
            Display_PunchIn_Time,
            Checkin_Work_Place_Id,
            PunchOut_Date,
            Display_PunchOut_Time,
            Checkout_Work_Place_Id,
            Display_Late_Attendance,
            Late_Attendance_Time,
            Actual_Total_Hours,
            Checkin_Data_Source,
            Checkout_Data_Source,
            Checkin_Form_Source,
            Checkout_Form_Source,
            comments,
          } = details;

          this.attendanceId = Attendance_Id ? parseInt(Attendance_Id) : null;
          this.employeeId = Employee_Id ? parseInt(Employee_Id) : null;
          this.presentationalName = Presentational_Name || "";
          this.employeeName = Employee_Name || "";
          this.formattedCheckInDate = PunchIn_Date || "";
          this.formattedCheckInTime =
            PunchIn_Date && Display_PunchIn_Time
              ? moment(Display_PunchIn_Time, "HH:mm:ss").format("HH:mm")
              : "";
          this.checkInWorkPlace = Checkin_Work_Place_Id || null;
          this.formattedCheckOutDate = PunchOut_Date || "";
          this.formattedCheckOutTime =
            PunchOut_Date && Display_PunchOut_Time
              ? moment(Display_PunchOut_Time, "HH:mm:ss").format("HH:mm")
              : "";
          this.checkOutWorkPlace = Checkout_Work_Place_Id || null;
          this.totalHours = Total_Hours || 0;
          this.lateAttendance = Display_Late_Attendance || "";
          this.lateAttendanceHours = Late_Attendance_Time || 0;
          this.actualTotalHours = Actual_Total_Hours || 0;
          this.checkInDataSource = Checkin_Data_Source || "";
          this.checkOutDataSource = Checkout_Data_Source || "";
          this.checkInFormSource = Checkin_Form_Source || "";
          this.checkOutFormSource = Checkout_Form_Source || "";
          this.comment =
            comments && comments.length > 0 ? comments[0]?.Emp_Comment : null;
          this.selectedCheckInDate = PunchIn_Date
            ? new Date(PunchIn_Date)
            : null;
          this.selectedCheckInTime =
            PunchIn_Date && Display_PunchIn_Time ? Display_PunchIn_Time : null;
          this.selectedCheckOutDate = PunchOut_Date
            ? new Date(PunchOut_Date)
            : null;
          this.selectedCheckOutTime =
            PunchOut_Date && Display_PunchOut_Time
              ? Display_PunchOut_Time
              : null;
        }
      }
    },
  },
  mounted() {
    if (this.isEdit && this.editFormData) {
      let { Presentational_Name, Employee_Name, details } = this.editFormData;
      details = details && details.length > 0 ? details[0] : details;
      let Employee_Id = this.selectedEmployee;

      if (details) {
        const {
          Attendance_Id,
          Total_Hours,
          PunchIn_Date,
          Display_PunchIn_Time,
          Checkin_Work_Place_Id,
          PunchOut_Date,
          Display_PunchOut_Time,
          Checkout_Work_Place_Id,
          Display_Late_Attendance,
          Late_Attendance_Time,
          Actual_Total_Hours,
          Checkin_Data_Source,
          Checkout_Data_Source,
          Checkin_Form_Source,
          Checkout_Form_Source,
          comments,
        } = details;

        this.attendanceId = Attendance_Id ? parseInt(Attendance_Id) : null;
        this.employeeId = Employee_Id ? parseInt(Employee_Id) : null;
        this.presentationalName = Presentational_Name || "";
        this.employeeName = Employee_Name || "";
        this.formattedCheckInDate = PunchIn_Date || "";
        this.formattedCheckInTime =
          PunchIn_Date && Display_PunchIn_Time
            ? moment(Display_PunchIn_Time, "HH:mm:ss").format("HH:mm")
            : "";
        this.checkInWorkPlace = Checkin_Work_Place_Id || null;
        this.formattedCheckOutDate = PunchOut_Date || "";
        this.formattedCheckOutTime =
          PunchOut_Date && Display_PunchOut_Time
            ? moment(Display_PunchOut_Time, "HH:mm:ss").format("HH:mm")
            : "";
        this.checkOutWorkPlace = Checkout_Work_Place_Id || null;
        this.totalHours = Total_Hours || 0;
        this.lateAttendance = Display_Late_Attendance || "";
        this.lateAttendanceHours = Late_Attendance_Time || 0;
        this.actualTotalHours = Actual_Total_Hours || 0;
        this.checkInDataSource = Checkin_Data_Source || "";
        this.checkOutDataSource = Checkout_Data_Source || "";
        this.checkInFormSource = Checkin_Form_Source || "";
        this.checkOutFormSource = Checkout_Form_Source || "";
        this.comment =
          comments && comments.length > 0 ? comments[0]?.Emp_Comment : null;
        this.selectedCheckInDate = PunchIn_Date ? new Date(PunchIn_Date) : null;
        this.selectedCheckInTime =
          PunchIn_Date && Display_PunchIn_Time ? Display_PunchIn_Time : null;
        this.selectedCheckOutDate = PunchOut_Date
          ? new Date(PunchOut_Date)
          : null;
        this.selectedCheckOutTime =
          PunchOut_Date && Display_PunchOut_Time ? Display_PunchOut_Time : null;
      }
    }

    //Get the work schedule details from the editFormData
    if (this.workScheduleDetails) {
      this.workScheduleInput = this.workScheduleDetails;
    }

    // Mark initialization as complete
    this.$nextTick(() => {
      this.isInitialized = true;
    });

    //Prefill the check in date and time if add
    if (!this.isEdit) {
      this.selectedCheckInDate = new Date(
        this.workScheduleInput.Consideration_From.trim().split(" ")[0]
      );
      this.formattedCheckInDate =
        this.workScheduleInput.Consideration_From.trim().split(" ")[0];
    }

    this.fetchWorkPlaces();
    this.getEmpList();
    this.retrieveDropdownDetails();
    if (!this.isEdit) {
      this.employeeId = this.selectedEmployee;
    }
  },
  methods: {
    displayFormattedDate(date) {
      if (date && moment(date).isValid()) {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return date ? moment(date).format(orgDateFormat) : "";
      }
    },
    async checkOutDateCleared() {
      this.selectedCheckOutDate = null;
      this.deductFormChange();
      //Clear validation
      await this.$refs?.AttendanceAddForm?.validate();
    },
    //setting minimum time limit in time picker based on consideration date and time
    checkInMinTimeLimit(attendance) {
      if (attendance) {
        let considerationFrom = attendance.Consideration_From.trim().split(" ");
        let considerationTo = attendance.Consideration_To.trim().split(" ");
        if (
          this.formattedCheckInDate === considerationFrom[0] &&
          this.formattedCheckInDate === considerationTo[0]
        ) {
          return considerationFrom[1];
        } else if (this.formattedCheckInDate === considerationFrom[0]) {
          return considerationFrom[1];
        } else {
          return "";
        }
      }
    },

    //setting maximum time limit in time picker based on consideration date and time
    checkInMaxTimeLimit(attendance) {
      if (attendance) {
        let considerationFrom = attendance.Consideration_From.trim().split(" ");
        let considerationTo = attendance.Consideration_To.trim().split(" ");

        //for current date missed check in max time should be current time.
        if (considerationFrom[0] === moment().format("YYYY-MM-DD")) {
          let checkInMaxLimit = moment().format("kk:mm:ss"); //24hrs fomat
          return checkInMaxLimit;
        } else {
          if (
            this.formattedCheckInDate === considerationFrom[0] &&
            this.formattedCheckInDate === considerationTo[0]
          ) {
            return considerationTo[1];
          } else if (this.formattedCheckInDate === considerationTo[0]) {
            return considerationTo[1];
          } else {
            return "";
          }
        }
      }
    },

    //setting minimum time limit in time picker based on consideration date and time
    checkOutMinTimeLimit() {
      if (this.formattedCheckOutDate === this.formattedCheckInDate) {
        return this.selectedCheckInTime;
      } else {
        return "";
      }
    },

    //setting maximum time limit in time picker based on consideration date and time
    checkOutMaxTimeLimit(attendance) {
      if (attendance) {
        let considerationTo = attendance.Consideration_To.trim().split(" ");
        if (this.formattedCheckOutDate === considerationTo[0]) {
          return considerationTo[1];
        } else {
          return "";
        }
      }
    },
    splitConsiderationDate(consideration_date) {
      if (consideration_date) {
        let dateLimit = consideration_date.trim().split(" ");
        return dateLimit[0];
      } else return "";
    },
    validateDates(value) {
      if (!this.formattedCheckInDate || !value) {
        return true; // Skip validation if either date is missing
      }

      const checkInDate = moment(this.formattedCheckInDate);
      const checkOutDate = moment(value);

      // Calculate the difference in days
      const differenceInDays =
        (checkOutDate - checkInDate) / (1000 * 60 * 60 * 24);

      if (differenceInDays < 0 || differenceInDays > 1) {
        return "Should be between 24 hours.";
      }

      return true;
    },
    validateCheckOutTime(value) {
      if (
        !this.formattedCheckInDate ||
        !this.formattedCheckOutDate ||
        !this.formattedCheckInTime ||
        !value
      ) {
        return true; // Skip validation if any value is missing
      }

      const checkInDate = new Date(this.formattedCheckInDate);
      const checkOutDate = new Date(this.formattedCheckOutDate);

      if (checkInDate.getTime() === checkOutDate.getTime()) {
        // Parse check-in and check-out times
        const [checkInHour, checkInMinute] = this.formattedCheckInTime
          .split(":")
          .map(Number);
        const [checkOutHour, checkOutMinute] = value.split(":").map(Number);

        const checkInTime = new Date(checkInDate);
        checkInTime.setHours(checkInHour, checkInMinute);

        const checkOutTime = new Date(checkOutDate);
        checkOutTime.setHours(checkOutHour, checkOutMinute);

        if (checkOutTime <= checkInTime) {
          return "Check Out Time must be greater than Check In Time when the dates are the same.";
        }
      }

      return true;
    },

    async addUpdateList() {
      let vm = this;
      vm.isLoading = true;
      vm.isLoadingCard = true;

      let isFormValid = await vm.$refs.AttendanceAddForm?.validate();
      if (isFormValid && isFormValid.valid) {
        let isUpdate = !!vm.attendanceId;
        try {
          const apiObj = {
            url: `${
              vm.baseUrl
            }employees/attendance/update-attendance/attendanceId/${
              isUpdate ? vm.attendanceId : 0
            }/istheme/Attendance`,
            type: "POST",
            dataType: "json",
            data: {
              attendanceId: isUpdate ? vm.attendanceId : 0,
              employeeId: vm.employeeId || null,
              punchInDate: this.formatDate(vm.formattedCheckInDate) || null,
              punchInTime: vm.formattedCheckInTime || null,
              punchInWorkPlace: vm.checkInWorkPlace || null,
              punchOutDate: this.formatDate(vm.formattedCheckOutDate) || null,
              punchOutTime: vm.formattedCheckOutTime || null,
              punchOutWorkPlace: vm.checkOutWorkPlace || null,
              formSource: "Attendance",
              punchType: vm.punchType || "Punch_In",
              excludeBrkHrs: vm.excludeBreakHours || 0,
              status: vm.approvalStatus || "Draft",
              comment: vm.comment || "",
              lateAttendance: vm.lateAttendance || 0,
              latitude: vm.latitude || null,
              longitude: vm.longitude || null,
              Checkin_Data_Source: vm.checkInDataSource || "Web",
              Checkout_Data_Source: vm.checkOutDataSource || "Web",
            },
          };

          const response = await vm.$store.dispatch(
            "triggerControllerFunction",
            apiObj
          );
          if (response?.success) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: isUpdate
                ? "Attendance record updated successfully."
                : "Attendance record added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("form-updated");
          } else {
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message: response?.msg
                ? response.msg
                : "Something went wrong. Please try after some time.",
            };
            vm.showAlert(snackbarData);
          }
        } catch (err) {
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "manager list",
            isListError: false,
          });
        } finally {
          vm.isLoadingCard = false;
          vm.isLoading = false;
        }
      }
    },

    async validateAttendanceForm() {
      // Validate the form fields
      const { valid } = await this.$refs.AttendanceAddForm.validate();

      if (valid) {
        // Submit the form if all fields are valid
        this.addUpdateList();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            const allValid = field.rules.every((rule) =>
              typeof rule === "function" ? rule() === true : rule === true
            );
            if (!allValid) {
              invalidFields.push(refName);
            }
          }
        });

        // Handle and log invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              // Define fields with custom focus logic (e.g., v-autocomplete)
              const selectFields = [
                "employeeID",
                "forwardTo",
                "formattedCheckInDate",
                "formattedCheckInTime",
                "formattedCheckOutDate",
                "formattedCheckOutTime",
                "checkInWorkPlace",
                "checkOutWorkPlace",
                "comment",
              ];

              // Handle custom focus logic
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect
                  ? fieldRef.onFocusCustomSelect()
                  : fieldRef.focus();
              } else {
                fieldRef.focus();
              }

              // Smooth scroll to the first invalid field
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: window.scrollY + rect.top + rect.height * 0.5,
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },

    retrieveDropdownDetails() {
      this.dropdownListFetching = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { managers } = res.data.getDropDownBoxDetails;

            this.managers = managers.map((manager) => ({
              ...manager,
              managerData:
                manager.Manager_Name +
                " - " +
                manager.Manager_User_Defined_EmpId,
            }));
          }
          this.dropdownListFetching = false;
        })
        .catch((err) => {
          this.dropdownListFetching = false;
          this.handleFetchListError(err);
        });
    },
    handleFetchListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "manager list",
        isListError: false,
      });
    },
    async getEmpList() {
      let formId = this.formId;
      this.isFetchingEmployees = true;
      this.listLoading = true;

      try {
        const empData = await this.$store.dispatch("getEmployeesList", {
          formName: "Attendance",
          formId: formId,
          flag: "payslipreq",
        });

        if (!empData || empData.length === 0) {
          this.allEmployeesList = [];
          return;
        }

        this.allEmployeesList = empData.map((item) => ({
          ...item,
          employeeData: `${item.employeeName} - ${item.userDefinedEmpId}`,
          enableWorkPlace: parseInt(item.Enable_Work_Place, 10) || 0,
        }));
        this.updateSelectedEmployee();
      } catch (err) {
        const snackbarData = {
          isOpen: true,
          message:
            err === "error"
              ? "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator."
              : err,
          type: "warning",
        };
        this.showAlert(snackbarData);
      } finally {
        this.isFetchingEmployees = false;
        this.listLoading = false;
      }
    },
    updateSelectedEmployee() {
      const selectedEmp = this.allEmployeesList.find(
        (emp) => emp.employeeId === this.selectedEmployee
      );
      this.enableWorkPlace = selectedEmp?.enableWorkPlace || 0;
    },
    fetchWorkPlaces() {
      let vm = this;
      let status = this.workPlaceStatus || "Active";
      vm.listLoading = true;
      vm.workPlaceLoading = true;
      vm.$apollo
        .query({
          query: LIST_WORK_PLACES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
          variables: {
            status: status,
            employeeId: vm.selectedEmployee,
          },
        })
        .then((response) => {
          if (response.data && response.data.listWorkPlaces) {
            const responseData = JSON.parse(
              response.data.listWorkPlaces.employeeWorkPlace
            );
            vm.workPlacesList = responseData.map((item) => ({
              ...item,
              Work_Place: item.Work_Place || "Unknown Work Place",
            }));
            vm.originalWorkPlacesList = responseData;
            vm.listLoading = false;
            mixpanel.track("Work places list retrieved");
          } else {
            vm.handleWorkPlacesError();
          }
          vm.workPlaceLoading = false;
        })
        .catch((err) => {
          vm.workPlaceLoading = false;
          vm.handleWorkPlacesError(err);
        });
    },

    handleWorkPlacesError(err = "") {
      mixpanel.track("Work places error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Work Places",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    deductFormChange() {
      this.isFormDirty = true;
    },

    closeAddForm() {
      if (this.isFormDirty) {
        this.showConfirmation = true;
      } else {
        this.closeForm();
      }
    },
    abortClose() {
      this.showConfirmation = false;
    },

    acceptClose() {
      this.showConfirmation = false;
      this.closeForm();
    },

    closeForm() {
      this.isFormDirty = false;
      this.$emit("close-form");
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
