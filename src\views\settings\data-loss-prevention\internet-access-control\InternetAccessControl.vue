<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row justify="center" v-if="mainList.length > 0">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              v-if="mainList.length > 0"
              class="justify-end mr-6"
              :list-items="mainList"
              :isApplyFilter="false"
              :isFilter="false"
            >
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="internet-access-control">
      <v-window v-if="formAccess" v-model="currentTabItem">
        <v-window-item :value="currentTabItem">
          <AppFetchErrorScreen
            v-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? $t('settings.retry') : ''"
            @button-click="refetchData()"
          >
          </AppFetchErrorScreen>
          <div v-else>
            <v-row>
              <v-col cols="12" class="mt-6">
                <ListInternetAccessControl
                  v-if="!isLoading"
                  :listLoading="listLoading"
                  :items="mainList"
                  :backupMainList="backupMainList"
                  :organizationDetails="organizationDetails"
                  :backupOrganizationDetails="backupOrganizationDetails"
                  :formAccess="formAccess"
                  :accessFormName="$t('settings.internetAccessControl')"
                  @refetch-data="refetchData()"
                  @refetch-list-data="refetchListData()"
                  @reset-search-filter="resetFilter()"
                ></ListInternetAccessControl>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
import ListInternetAccessControl from "./ListInternetAccessControl.vue";
import mixpanel from "mixpanel-browser";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
import {
  GET_ORGANIZATION_LEVEL_DLP_SETTINGS,
  GET_EMPLOYEE_LEVEL_DLP_SETTINGS,
} from "@/graphql/settings/data-loss-prevention/internetAccessControl.js";
export default {
  name: "InternetAccessControl",
  components: {
    ListInternetAccessControl,
    EmployeeDefaultFilterMenu,
  },
  data() {
    return {
      isLoading: false,
      currentTabItem: "tab-0",
      listLoading: true,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      mainList: [],
      backupMainList: [],
      organizationDetails: {},
      backupOrganizationDetails: {},
    };
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    formAccess() {
      let fAccess = this.accessRights("265");
      if (
        fAccess &&
        fAccess.accessRights &&
        fAccess.accessRights["view"] &&
        fAccess.accessRights["admin"] === "admin"
      ) {
        return fAccess.accessRights;
      } else return false;
    },
    accessFormName() {
      return this.$t("settings.internetAccessControl");
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    dataLossPreventionFormAccess() {
      let fAccess = this.accessRights("230");
      if (fAccess && fAccess.accessRights && fAccess.accessRights["view"]) {
        return fAccess.accessRights;
      } else return false;
    },
    dataLossPreventionFormName() {
      let fAccess = this.accessRights("230");
      if (fAccess && fAccess.customFormName) {
        return fAccess.customFormName;
      } else return this.$t("settings.dataLossPrevention");
    },
    dataLossFormAccess() {
      return this.$store.getters.lossPreventionFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.dataLossFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access of formAccess) {
          if (access.havingAccess || access.formId === 265) {
            formAccessArray.push(this.$t(access.displayFormName));
          }
        }
        return formAccessArray;
      }
      return [];
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  errorCaptured(err, vm, info) {
    let url = window.location.href;
    let msg = this.$t("settings.accessDeniedMessage");
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },
  mounted() {
    mixpanel.init("6df21e89c6a0f6b1bc345ae98b6ef36e", {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.accessFormName);
    this.refetchData();
  },
  methods: {
    onTabChange(tab) {
      mixpanel.track("Internet access control form tab changed");
      if (tab !== this.accessFormName) {
        this.isLoading = true;
        const { formAccess } = this.dataLossFormAccess;
        let clickedTab = formAccess.find(
          (form) => this.$t(form.displayFormName) === tab
        );
        if (clickedTab.isVue3) {
          this.$router.push("/settings/" + clickedTab.url);
        } else {
          window.location.href = this.baseUrl + "in/settings/" + clickedTab.url;
        }
      }
    },
    resetFilter() {
      this.mainList = [...this.backupMainList];
      this.organizationDetails = this.backupOrganizationDetails;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    async refetchData() {
      this.errorContent = "";
      this.isErrorInList = false;
      mixpanel.track("internetAccessControl-dlpSettings-organization-refetch");
      await Promise.all([
        this.OrganizationLevelDlpSettings(),
        this.getEmployeeLevelDlpSettings(),
      ]);
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    async refetchListData() {
      this.errorContent = "";
      this.isErrorInList = false;
      mixpanel.track("internetAccessControl-employee-dlpSettings-refetch");
      await this.getEmployeeLevelDlpSettings();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    getEmployeeLevelDlpSettings() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_EMPLOYEE_LEVEL_DLP_SETTINGS,
          client: "apolloClientK",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track(
            "internetAccessControl-dlpSettings-employee-fetch-success"
          );
          if (response.data && response.data.getEmployeeLevelDlpSettings) {
            let mainList = JSON.parse(
              response.data.getEmployeeLevelDlpSettings.dlpSettingsData
            );
            vm.mainList = mainList;
            vm.backupMainList = JSON.parse(JSON.stringify(mainList));
            vm.listLoading = false;
          } else {
            mixpanel.track(
              "internetAccessControl-dlpSettings-employee-fetch-error"
            );
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    OrganizationLevelDlpSettings() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_ORGANIZATION_LEVEL_DLP_SETTINGS,
          client: "apolloClientK",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track(
            "internetAccessControl-dlpSettings-organization-fetch-success"
          );
          if (response.data && response.data.getOrganizationLevelDlpSettings) {
            let organizationDetails = JSON.parse(
              response.data.getOrganizationLevelDlpSettings.dlpSettingsData
            );
            vm.backupOrganizationDetails = organizationDetails[0];
            vm.organizationDetails = organizationDetails[0];
            vm.isLoading = false;
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          mixpanel.track(
            "internetAccessControl-dlpSettings-organization-fetch-error"
          );
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.internet-access-control {
  padding: 5em 3em 0em 3em;
}
@media screen and (max-width: 805px) {
  .internet-access-control {
    padding: 4em 1em 0em 1em;
  }
}
</style>
