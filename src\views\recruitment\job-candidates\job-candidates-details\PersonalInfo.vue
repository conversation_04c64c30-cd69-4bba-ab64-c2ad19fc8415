<template>
  <div class="d-flex align-center justify-space-between">
    <div class="d-flex align-center">
      <v-progress-circular
        model-value="100"
        color="blue"
        :size="18"
        class="mr-1"
      ></v-progress-circular>
      <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
        >Personal Details</span
      >
    </div>
  </div>
  <v-row class="pa-4 ma-2 card-blue-background">
    <v-col v-if="!previewScreen" cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Resume</p>
      <p class="text-subtitle-1 font-weight-regular">
        <span
          @click="retrieveResumeDetails(candidateDetails.Resume)"
          class="text-blue cursor-pointer text-wrap"
          :style="
            candidateDetails.Resume
              ? 'text-decoration: underline; max-width: 20px'
              : ''
          "
        >
          {{ formattedFileName(candidateDetails.Resume) }}
        </span>
      </p>
    </v-col>
    <!-- <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Salutation</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Salutation) }}
      </p>
    </v-col> -->
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">First Name</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Candidate_First_Name) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Middle Name</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Candidate_Middle_Name) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Last Name</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Candidate_Last_Name) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[328]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[328]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Emp_Pref_First_Name) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Date of Birth</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ formatDate(candidateDetails.DOB) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Gender</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Gender) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[329]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[329]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Gender_Identity) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[330]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[330]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Gender_Expression) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[331]?.Field_Visiblity?.toLowerCase() === 'yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[331]?.Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ candidateDetails.Physically_Challenged ? "Yes" : "No" }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[209].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[209].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Pronoun) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[210].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[210].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Gender_Orientations) }}
      </p>
    </v-col>

    <v-col
      cols="12"
      md="4"
      sm="6"
      v-if="labelList[325].Field_Visiblity == 'Yes'"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[325].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{
          candidateDetails.Lang_Known && candidateDetails.Lang_Known.length > 0
            ? formLanguagesNames(candidateDetails.Lang_Known)
            : "-"
        }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[262].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[262].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Blood_Group) }}
      </p>
    </v-col>
    <v-col cols="12" md="4" sm="6">
      <p class="text-subtitle-1 text-grey-darken-1">Marital Status</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Marital_Status_Name) }}
      </p>
    </v-col>
    <v-col
      v-if="candidateDetails && candidateDetails.Nationality_Id"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">Nationality</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Nationality_Table_Name) }}
      </p>
    </v-col>
    <v-col
      v-else-if="candidateDetails && candidateDetails.Nationality"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">Nationality</p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Nationality) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[264].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[264].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{
          candidateDetails.Candidate_Dependent &&
          candidateDetails.Candidate_Dependent.length > 0
            ? formFatherMotherName(
                candidateDetails.Candidate_Dependent,
                "Father"
              )
            : "-"
        }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[263].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[263].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{
          candidateDetails.Candidate_Dependent &&
          candidateDetails.Candidate_Dependent.length > 0
            ? formFatherMotherName(
                candidateDetails.Candidate_Dependent,
                "Mother"
              )
            : "-"
        }}
      </p>
    </v-col>
    <v-col
      v-if="labelList['220'] && labelList['220'].Field_Visiblity === 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[220].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.National_Identification_Number) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[265].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[265].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{
          candidateDetails.Work_Permit &&
          candidateDetails.Work_Permit.length > 0
            ? formWorkPermits(candidateDetails.Work_Permit)
            : "-"
        }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[266].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[266].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Other_Work_Permit) }}
      </p>
    </v-col>
    <v-col
      v-if="labelList[287].Field_Visiblity == 'Yes'"
      cols="12"
      md="4"
      sm="6"
    >
      <p class="text-subtitle-1 text-grey-darken-1">
        {{ labelList[287].Field_Alias }}
      </p>
      <p class="text-subtitle-1 font-weight-regular">
        {{ checkNullValue(candidateDetails.Source) }}
      </p>
    </v-col>
  </v-row>
  <div
    class="d-flex align-center justify-space-between mb-2"
    v-if="labelList[383]?.Field_Visiblity?.toLowerCase() === 'yes'"
  >
    <div class="d-flex align-center">
      <v-progress-circular
        model-value="100"
        color="danger"
        :size="18"
        class="mx-2"
      ></v-progress-circular>
      <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
        >Language(s) Known</span
      >
    </div>
  </div>
  <v-row v-if="labelList[383]?.Field_Visiblity?.toLowerCase() === 'yes'">
    <v-slide-group
      class="pa-4"
      selected-class="bg-primary"
      prev-icon="fas fa-chevron-circle-left"
      next-icon="fas fa-chevron-circle-right"
      show-arrows
    >
      <v-slide-group-item>
        <v-card
          v-for="(lang, index) in candidateDetails.Lang_Known"
          :key="index"
          class="card-item d-flex pa-4 rounded-lg mr-4 mb-1"
          color="grey-lighten-5"
          :style="
            !isMobileView
              ? `width:450px; border-left: 7px solid ${generateRandomColor()}; height:auto; `
              : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
          "
        >
          <v-row>
            <v-col
              v-if="labelList[383]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              class="pa-0 pl-4 d-flex"
            >
              <v-tooltip :text="lang?.Language_Name" location="bottom">
                <template v-slot:activator="{ props }">
                  <div
                    class="text-primary font-weight-bold text-subtitle-1 text-truncate"
                    :style="
                      isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                    "
                    v-bind="lang?.Language_Name ? props : ''"
                  >
                    {{ checkNullValue(lang?.Language_Name) }}
                  </div>
                </template>
              </v-tooltip>
            </v-col>
            <v-col
              v-if="labelList[356]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              sm="6"
              class="pa-0 pl-4"
            >
              <div
                class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
                style="max-content"
              >
                <b class="mr-2 text-grey justify-start">
                  {{ labelList[356]?.Field_Alias }}
                </b>
                <span class="pb-1 pt-1">{{
                  lang?.Lang_Spoken ? "Yes" : "No"
                }}</span>
              </div>
            </v-col>
            <v-col
              v-if="labelList[357]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              sm="6"
              class="pa-0 pl-4"
            >
              <div
                class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
                style="max-content"
              >
                <b class="mr-2 text-grey justify-start">
                  {{ labelList[357]?.Field_Alias }}
                </b>
                <span class="pb-1 pt-1">{{
                  lang?.Lang_Read_Write ? "Yes" : "No"
                }}</span>
              </div>
            </v-col>
            <v-col
              v-if="labelList[358]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              sm="6"
              class="pa-0 pl-4"
            >
              <div
                class="mt-2 mr-2 d-flex flex-column justify-start text-body-1"
                style="max-content"
              >
                <b class="mr-2 text-grey justify-start">
                  {{ labelList[358]?.Field_Alias }} </b
                ><v-tooltip
                  :text="lang?.Lang_Proficiency"
                  location="bottom"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <span class="pb-1 pt-1" v-bind="props">
                      <div
                        :style="
                          isMobileView ? 'max-width: 200px' : 'max-width:140px'
                        "
                        class="text-truncate"
                      >
                        {{ checkNullValue(lang?.Lang_Proficiency) }}
                      </div></span
                    >
                  </template>
                </v-tooltip>
              </div>
            </v-col>
          </v-row>
        </v-card>
      </v-slide-group-item></v-slide-group
    >
  </v-row>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="resume"
    :appendUnderScoreInDomain="true"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue, generateRandomColor } from "@/helper";
import moment from "moment";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

export default {
  name: "PersonalDetails",
  components: { FilePreviewModal },
  props: {
    candidateDetails: {
      type: [Array, Object],
      required: true,
    },
    previewScreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      openModal: false,
      retrievedFileName: "",
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date, "YYYY/MM/DD").format(orgDateFormat);
        } else return "-";
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  methods: {
    checkNullValue,
    generateRandomColor,
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File";
      }
      return "-";
    },
    formLanguagesNames(languages) {
      return languages.map((el) => el.Language_Name).join(", ");
    },
    formFatherMotherName(details, relation) {
      let motherName = details.filter((el) => el.Relationship === relation);
      return motherName && motherName.length > 0
        ? motherName[0].Dependent_First_Name
        : "-";
    },
    formWorkPermits(permits) {
      let wPermits = [];
      for (var i = 0; i < permits.length; i++) {
        wPermits.push(permits[i].Work_Permit);
      }
      return wPermits.join(", ");
    },
    retrieveResumeDetails(filepath) {
      this.retrievedFileName = filepath;
      this.openModal = true;
    },
  },
};
</script>
