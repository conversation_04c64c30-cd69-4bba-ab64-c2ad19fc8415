import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_LOCATION_TRACKING_LIST = gql`
  query getofficeIpAddress {
    getofficeIpAddress {
      errorCode
      message
      officeIpAddressData {
        officeIpAddressId
        workLocationName
        ipRange
        status
        addedOn
        updatedOn
        addedBy
        updatedBy
      }
      orgLevelofficeIpAddressData {
        url
        status
        updatedBy
        updatedOn
      }
    }
  }
`;

// ===============
// Mutation
// ===============

export const ADD_UPDATE_WORK_LOCATION = gql`
  mutation addUpdateOfficeIpAddress(
    $officeIpAddressId: Int!
    $workLocationName: String!
    $ipRange: String!
    $status: String!
  ) {
    addUpdateOfficeIpAddress(
      officeIpAddressId: $officeIpAddressId
      workLocationName: $workLocationName
      ipRange: $ipRange
      status: $status
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_LOCATION_TRACKING_SETTING = gql`
  mutation updateOrganizationLocationSettings(
    $orgLocationTrackingId: Int!
    $status: String!
  ) {
    updateOrganizationLocationSettings(
      orgLocationTrackingId: $orgLocationTrackingId
      status: $status
    ) {
      errorCode
      message
    }
  }
`;
