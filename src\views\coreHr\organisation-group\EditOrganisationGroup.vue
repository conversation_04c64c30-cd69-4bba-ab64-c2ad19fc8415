<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold"> {{ landedFormName }}</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              variant="elevated"
              class="mb-2 primary"
              @click="validateOrganizationForm"
              >Save</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Save</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" class="mr-1"> fas fa-times </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="height: calc(100vh - 260px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="organizationGroupForm">
            <v-row>
              <!-- Level -->
              <v-col
                v-if="entomoIntegrationEnabled && isEntomoSyncTypePush"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="selectedLevel"
                  variant="solo"
                  :rules="[
                    required(`Level`, selectedLevel),
                    selectedLevel
                      ? validateWithRulesAndReturnMessages(
                          selectedLevel,
                          'level',
                          'Level',
                          true
                        )
                      : true,
                  ]"
                  type="number"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    Level
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>

              <!-- Organization Group Code -->
              <v-col
                v-if="entomoIntegrationEnabled"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="organizationGroupCode"
                  variant="solo"
                  :rules="[
                    required(
                      labelList[317]?.Field_Alias || `Organization Group Code`,
                      organizationGroupCode
                    ),
                    organizationGroupCode
                      ? validateWithRulesAndReturnMessages(
                          organizationGroupCode,
                          'Department_Code',
                          labelList[317]?.Field_Alias ||
                            `Organization Group Code`
                        )
                      : true,
                  ]"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    <div class="d-flex">
                      <span class="text-truncate">{{
                        labelList[317]?.Field_Alias || `Organization Group Code`
                      }}</span>
                      <span style="color: red" class="ml-1"> *</span>
                    </div>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-else-if="
                  labelList[317] &&
                  labelList[317].Field_Visiblity.toLowerCase() === 'yes'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="organizationGroupCode"
                  variant="solo"
                  :rules="[
                    labelList[317].Mandatory_Field.toLowerCase() === 'yes'
                      ? required(
                          `${labelList[317].Field_Alias}`,
                          organizationGroupCode
                        )
                      : true,
                    organizationGroupCode
                      ? validateWithRulesAndReturnMessages(
                          organizationGroupCode,
                          'Department_Code',
                          labelList[317].Field_Alias
                        )
                      : true,
                  ]"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    <div class="d-flex">
                      <span class="text-truncate">{{
                        labelList[317].Field_Alias
                      }}</span>
                      <span
                        v-if="
                          labelList[317].Mandatory_Field.toLowerCase() === 'yes'
                        "
                        style="color: red"
                        class="ml-1"
                      >
                        *</span
                      >
                    </div>
                  </template>
                </v-text-field>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-text-field
                  v-model="organizationGroup"
                  variant="solo"
                  :rules="[
                    required(landedFormName, organizationGroup),
                    validateWithRulesAndReturnMessages(
                      organizationGroup,
                      'organizationGroup',
                      landedFormName
                    ),
                  ]"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    <span>Organization Group</span>
                    <span style="color: red"> *</span>
                  </template>
                </v-text-field>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2 d-flex align-center"
              >
                <div class="v-label mr-4">Status</div>
                <AppToggleButton
                  button-active-text="Active"
                  button-inactive-text="InActive"
                  button-active-color="#7de272"
                  button-inactive-color="red"
                  id-value="gab-analysis-based-on"
                  :current-value="status === 'Active' ? true : false"
                  @chosen-value="onChangeStatus($event)"
                ></AppToggleButton>
              </v-col>

              <v-col cols="12" class="px-md-6 pb-0 mb-2">
                <v-textarea
                  v-model="description"
                  variant="solo"
                  auto-grow
                  rows="1"
                  :rules="[
                    validateWithRulesAndReturnMessages(
                      description,
                      'description',
                      'Description'
                    ),
                  ]"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    Description<span style="color: red">*</span>
                  </template>
                </v-textarea>
              </v-col>
              <v-col
                v-if="moreDetailsList && moreDetailsList.length > 0"
                cols="12"
              >
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
      <div>
        <v-row class="px-sm-8 px-md-10 mt-2 mb-2">
          <!-- <-- v-if="moreDetailsList.length > 0" -->
        </v-row>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import { ADD_UPDATE_ORGANIZATION_GROUP } from "@/graphql/corehr/organisationGroupQueries";

export default {
  name: "EditOrganizationGroup",
  components: {
    MoreDetails,
  },
  mixins: [validationRules],

  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    originalList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    landedFormName: {
      type: String,
      required: true,
    },
    maxOrgCode: {
      type: Number,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      // add/update
      selectedLevel: null,
      organizationGroupCode: "",
      organizationGroup: "",
      status: "Active",
      description: "",
      isFormDirty: false,
      addedBy: "Sanket",
      addedOn: "12/04/24",
      updatedBy: "sanket",
      updatedOn: "20/07/24",
      moreDetailsList: [],
      openMoreDetails: true,
      // loading/error/other
      isLoading: false,
      openConfirmationPopup: false,
      validationMessages: [],
      showValidationAlert: false,
    };
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
  },
  mounted() {
    if (this.editFormData) {
      this.organizationGroupCode = this.editFormData.organizationGroupCode;
      this.organizationGroup = this.editFormData.organizationGroup;
      this.description = this.editFormData.description;
      this.selectedLevel = this.editFormData.level;
    } else {
      this.organizationGroupCode = null;
    }
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);
        this.prefillMoreDetails();
      },
    },
    editFormData() {
      this.organizationGroupCode = this.editFormData.organizationGroupCode;
      this.organizationGroup = this.editFormData.organizationGroup;
      this.description = this.editFormData.description;
    },
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },

    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },

    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },

    // change the mode of performance management
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },

    async validateOrganizationForm() {
      const { valid } = await this.$refs.organizationGroupForm.validate();
      if (valid) {
        if (this.checkIfAlreadyPresent()) {
          this.addUpdateOrganizationGroup();
        } else {
          var snackbarData = {
            isOpen: true,
            type: "error",
            message: "Business Unit Code should be unique",
          };
          this.showAlert(snackbarData);
        }
      }
    },
    checkIfAlreadyPresent() {
      if (this.isEdit) {
        let newlist = this.originalList.filter(
          (ele) =>
            ele.organizationGroupId != this.editFormData.organizationGroupId
        );
        for (let org of newlist) {
          if (org.organizationGroupCode == this.organizationGroupCode) {
            return false;
          }
        }
        return true;
      } else {
        for (let org of this.originalList) {
          if (org.organizationGroupCode == this.organizationGroupCode) {
            return false;
          }
        }
        return true;
      }
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.editFormData?.addedOn + ".000Z")
        ),
        addedByName = this.editFormData?.addedByName,
        updatedByName = this.editFormData?.updatedByName,
        updatedOn = this.formatDate(
          new Date(this.editFormData?.updatedOn + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    addUpdateOrganizationGroup() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_ORGANIZATION_GROUP,
          variables: {
            organizationGroupId: this.isEdit
              ? this.editFormData.organizationGroupId
              : 0,
            organizationGroupCode: this.organizationGroupCode
              ? this.organizationGroupCode.toString()
              : null,
            organizationGroup: this.organizationGroup,
            description: this.description,
            level: this.selectedLevel ? parseInt(this.selectedLevel) : null,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.isEdit
              ? this.landedFormName + " configuration updated successfully."
              : this.landedFormName + " configuration added successfully.",
          };
          vm.showAlert(snackbarData);
          vm.$emit("edit-updated");
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.landedFormName.toLowerCase(),
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
  },
};
</script>
