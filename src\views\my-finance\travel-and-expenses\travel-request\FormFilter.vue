<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="secondary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col
            v-if="callingFrom === 'team'"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedEmpName"
              color="secondary"
              :items="empNameList"
              label="Employee"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
              item-title="employeeName"
              item-value="employeeId"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedTravelType"
              color="secondary"
              :items="travelType"
              label="Travel Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedTravelRequestStatus"
              color="secondary"
              :items="preApprovalStatusList"
              label="Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter('manual')"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "FormFilter",

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    callingFrom: {
      type: String,
      default: "employee",
    },
  },

  data: () => ({
    openFormFilter: false,
    empNameList: [],
    selectedEmpName: [],
    travelType: ["Domestic", "International"],
    selectedTravelType: [],
    preApprovalStatusList: [
      "Applied",
      "Approved",
      "Cancel Applied",
      "Cancelled",
      "Rejected",
    ],
    selectedTravelRequestStatus: [],
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  watch: {
    items() {
      this.formFilterData();
    },
  },

  mounted() {
    this.formFilterData();
  },

  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.items;

      if (this.selectedEmpName.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmpName.includes(item.userDefinedEmpId);
        });
      }

      if (this.selectedTravelType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedTravelType.includes(item.travelType);
        });
      }
      if (this.selectedTravelRequestStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedTravelRequestStatus.includes(item.status);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },

    // reset filter
    resetFilterValues() {
      this.selectedEmpName = [];
      this.selectedEmpId = [];
      this.selectedTravelType = [];
      this.selectedStartDate = "";
      this.selectedEndDate = "";
      this.selectedTravelRequestStatus = [];
      this.openFormFilter = false;
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedEmpName = [];
      this.selectedEmpId = [];
      this.selectedTravelType = [];
      this.selectedStartDate = "";
      this.selectedEndDate = "";
      this.selectedTravelRequestStatus = [];
      this.openFormFilter = false;
    },
    formFilterData() {
      for (let item of this.items) {
        if (item && (item.employeeName || item.userDefinedEmpId)) {
          this.empNameList.push({
            employeeName: item.employeeName + " - " + item.userDefinedEmpId,
            employeeId: item.userDefinedEmpId,
          });
        }
      }
      this.empNameList = this.removeDuplicatesFromArrayOfObject(
        this.empNameList,
        "employeeId"
      );
    },

    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
  },
});
</script>
