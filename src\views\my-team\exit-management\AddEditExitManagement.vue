<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold"> Resignation </span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="
                isFormDirty ||
                (selectedReasonForResignation &&
                  selectedResignationDate &&
                  selectedExitDate)
              "
              rounded="lg"
              class="mb-2 primary"
              variant="elevated"
              @click="validateResignationForm"
              >Save</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Save</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" color="primary" class="mr-1">
            fas fa-times
          </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="height: calc(100vh - 260px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="resignationForm">
            <v-row class="mt-n8">
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div>
                  <p class="text-subtitle-2 text-grey-darken-1">
                    <strong>Employee Id</strong>
                  </p>

                  <p class="text-subtitle-3 font-weight-regular">
                    {{
                      checkNullValue(
                        isEdit
                          ? editFormData?.userDefinedEmpId
                          : addFormEmployeeDetails?.idPrefix
                      )
                    }}
                  </p>
                </div>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div>
                  <p class="text-subtitle-2 text-grey-darken-1">
                    <strong>Employee Name</strong>
                  </p>

                  <p class="text-subtitle-3 font-weight-regular">
                    {{ checkNullValue(employeeName) }}
                  </p>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 ml-n1">
                <v-menu
                  v-model="resignationMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="formattedResignation"
                      v-model="formattedResignation"
                      prepend-inner-icon="fas fa-calendar"
                      readonly
                      density="comfortable"
                      :disabled="isDisabledApply"
                      :rules="[
                        required('Date of Resignation', formattedResignation),
                      ]"
                      v-bind="props"
                      :clearable="true"
                      variant="solo"
                      ><template v-slot:label>
                        Date of Resignation
                        <span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-model="selectedResignationDate"
                    :min="minResignationDate"
                    @update:model-value="isFormDirty = true"
                  ></v-date-picker> </v-menu
              ></v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <v-menu
                  v-model="exitDateMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="formattedExitDate"
                      v-model="formattedExitDate"
                      prepend-inner-icon="fas fa-calendar"
                      readonly
                      v-bind="props"
                      density="comfortable"
                      :disabled="isDisabledExit"
                      :rules="[required('Date of Exit', formattedExitDate)]"
                      :clearable="true"
                      variant="solo"
                      ><template v-slot:label>
                        Date of Exit
                        <span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-model="selectedExitDate"
                    :min="minExitDate"
                    @update:model-value="changeExitDate($event)"
                  />
                </v-menu>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div>
                  <p class="text-subtitle-2 text-grey-darken-1">
                    <strong>Notice Period</strong>
                  </p>
                  <p
                    v-if="noticeperiod"
                    class="text-subtitle-3 font-weight-regular"
                  >
                    {{ parseInt(noticeperiod) }} day(s)
                  </p>
                  <p v-else class="text-subtitle-3 font-weight-regular">-</p>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 ml-n1">
                <CustomSelect
                  label="Reason for Resignation"
                  :items="reasonItems"
                  :itemSelected="selectedReasonForResignation"
                  :rules="[
                    required(
                      'Reason for Resignation',
                      selectedReasonForResignation
                    ),
                  ]"
                  item-title="esicReasonName"
                  item-value="esicReasonName"
                  density="comfortable"
                  :isLoading="isLoading"
                  :is-required="true"
                  :clearable="true"
                  :isAutoComplete="true"
                  variant="solo"
                  @selected-item="selectedReasonForResignation = $event"
                  @update:model-value="isFormDirty = true"
                />
              </v-col>
              <v-col v-if="isEdit" cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div>
                  <p class="text-subtitle-2 text-grey-darken-1">
                    <strong>Status</strong>
                  </p>

                  <p class="text-subtitle-3 font-weight-regular">
                    {{ checkNullValue(resignationStatus) }}
                  </p>
                </div>
              </v-col>
              <v-col
                v-if="labelList[461]?.Field_Visiblity?.toLowerCase() === 'yes'"
                cols="12"
                class="px-md-6 pb-0 ml-n2"
              >
                <v-file-input
                  ref="documentUpload"
                  prepend-icon=""
                  clearable
                  chips
                  :model-value="selectedDocument"
                  append-inner-icon="fas fa-paperclip"
                  variant="solo"
                  :rules="[
                    labelList[461].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList[461].Field_Alias || `Documents`,
                          selectedDocument
                        )
                      : true,
                    fileTypeRule(selectedDocument),
                  ]"
                  :persistent-hint="true"
                  hint="Max Size: 3MB. Allowed formats: .jpg, .jpeg, .png, .pdf, .doc, .docx"
                  accept=".jpg, .jpeg, .png, .pdf, .doc, .docx"
                  @update:model-value="onUploadFile($event)"
                  @click:clear="selectedDocument = null"
                >
                  <template v-slot:label>
                    {{ labelList[461].Field_Alias || "Documents"
                    }}<span
                      v-if="
                        labelList[461].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-file-input>
              </v-col>
              <v-col cols="12" class="px-md-6 pb-0 ml-n2">
                <v-textarea
                  v-model="selectedComments"
                  variant="solo"
                  auto-grow
                  label="Comments"
                  rows="4"
                  :rules="[
                    minLengthValidation('Comments', selectedComments, 5),
                    maxLengthValidation('Comments', selectedComments, 600),
                    validateWithRulesAndReturnMessages(
                      selectedComments,
                      'description',
                      'Comments'
                    ),
                  ]"
                  @update:model-value="isFormDirty = true"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppWarningModal
      v-if="cancelModel"
      :open-modal="cancelModel"
      confirmation-heading=""
      :acceptButtonDisable="checkCancelButton"
      @close-warning-modal="$emit('close-form')"
      @accept-modal="(isFormDirty = true), (cancelModel = false)"
    >
      <template v-slot:warningModalContent>
        <v-row>
          <v-col cols="12" class="text-left">
            <div style="background-color: white" class="rounded-lg pa-4">
              <NotesCard
                notes="The employee is currently inactive. Kindly update the employee's work
                email in the team summary to ensure they receive application access."
                backgroundColor="transparent"
              />
            </div>
            <div
              v-if="appTrackingMode?.length"
              style="background-color: white"
              class="rounded-lg pa-4"
            >
              <NotesCard
                notes="The productivity monitoring member record status cannot be reverted because the asset has been disassociated."
                backgroundColor="transparent"
              />
            </div>
          </v-col>
        </v-row>
      </template>
    </AppWarningModal>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 primary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import axios from "axios";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import moment from "moment";
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import {
  GET_EMPLOYEE_RESIGNATION,
  GET_EMPLOYEE_NOTIFICATION_PERIOD_DAYS,
  CREATE_RESIGNATION,
  UPDATE_RESIGNATION_DATE,
  UPDATE_RESIGNATION_REASON,
} from "@/graphql/my-team/exitManagement.js";
import { checkNullValue } from "@/helper.js";
export default {
  name: "AddEditExitManagement",
  mixins: [validationRules],
  components: { CustomSelect, NotesCard },
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    addFormEmployeeDetails: {
      type: Object,
      default: () => {
        return {};
      },
    },
    resignationList: {
      type: [Object, Array],
      required: true,
    },
  },
  emits: ["close-form", "edit-updated", "added-new-record"],
  data() {
    return {
      // add/update
      selectedReasonForResignation: null,
      noticeperiod: null,
      reasonItems: [],
      selectedComments: null,
      selectedDocument: null,
      employeeName: "",
      // Date Picker
      resignationMenu: false,
      exitDateMenu: false,
      formattedResignation: "",
      formattedExitDate: "",
      selectedResignationDate: null,
      selectedExitDate: null,
      isFormDirty: false,
      // loading/error/other
      isLoading: false,
      openConfirmationPopup: false,
      cancelModel: false,
      validationMessages: [],
      showValidationAlert: false,
      ResignationAxios: null,
    };
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    appTrackingMode() {
      return this.$store.state.appTrackingMode;
    },
    isDisabledApply() {
      if (!this.isEdit) {
        return false;
      }
      return (
        this.ResignationAxios?.payslipGenerated === 1 &&
        this.ResignationAxios?.fullAndFinalSettlementInitiated === 1
      );
    },
    isDisabledExit() {
      if (!this.isEdit) {
        return false;
      }
      if (
        this.ResignationAxios?.payslipGenerated === 1 &&
        this.ResignationAxios?.fullAndFinalSettlementInitiated === 1
      ) {
        return true;
      }
      return !this.selectedResignationDate;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    minResignationDate() {
      if (
        this.ResignationAxios?.resignationDateOverride &&
        this.ResignationAxios?.payslipGenerated &&
        this.ResignationAxios?.salaryDate &&
        this.isEdit
      ) {
        const salaryDate = moment(
          this.ResignationAxios.salaryDate,
          "DD/MM/YYYY"
        ).subtract(1, "days");
        return salaryDate.format("YYYY-MM-DD");
      } else if (this.ResignationAxios && this.ResignationAxios.salaryDate) {
        const salaryDate = moment(
          this.ResignationAxios.salaryDate,
          "DD/MM/YYYY"
        );
        return salaryDate.format("YYYY-MM-DD");
      } else return null;
    },
    minExitDate() {
      return moment(this.selectedResignationDate).format("YYYY-MM-DD");
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
  },
  watch: {
    selectedResignationDate(val) {
      if (val) {
        this.resignationMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedResignation = dateValue;
        if (this.formattedExitDate) {
          // Convert both formatted dates to Date objects
          const resignationDate = new Date(this.formattedResignation);
          const exitDate = new Date(this.formattedExitDate);

          if (resignationDate < exitDate) {
            this.calculateNoticePeriodDate();
          } else {
            this.selectedExitDate = null;
            this.formattedExitDate = "";
            this.noticeperiod = null;
          }
        }
      }
    },
    selectedExitDate(val) {
      if (val) {
        this.exitDateMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedExitDate = dateValue;
        if (this.formattedResignation) {
          this.calculateNoticePeriodDate();
        }
      }
    },
  },
  mounted() {
    this.retriveRelievingReasonDetails();
    if (this.isEdit) {
      const {
        employeeName,
        employeeId,
        resignationStatus,
        appliedDate,
        resignationDate,
        relievingReasonComment,
        fileName,
      } = this.editFormData;
      this.employeeName = employeeName ? employeeName : null;
      this.employeeId = employeeId ? employeeId : null;
      this.resignationStatus = resignationStatus ? resignationStatus : null;
      this.selectedResignationDate = appliedDate ? new Date(appliedDate) : null;
      this.selectedExitDate = resignationDate
        ? new Date(resignationDate)
        : null;
      this.selectedComments = relievingReasonComment
        ? relievingReasonComment
        : null;
      if (fileName) {
        this.selectedDocument = {
          name: fileName.split("?")[2] || "", // Extract filename from string
          formattedName: fileName, // Keep the full path
        };
      }
    } else {
      this.getEmployeeNotificationPeriodDays();
      this.employeeId = this.addFormEmployeeDetails?.id;
      this.employeeName = this.addFormEmployeeDetails?.name;
    }
    this.fetchResignationAjax();
  },
  methods: {
    checkNullValue,
    onUploadFile(file) {
      if (!file) return;

      this.isFormDirty = true;
      this.selectedDocument = file;

      const timestamp = moment().unix();

      if (!this.selectedDocument.formattedName) {
        this.selectedDocument.formattedName =
          (this.isEdit
            ? this.editFormData?.employeeId
            : this.addFormEmployeeDetails?.id) +
          "?" +
          timestamp +
          "?" +
          this.selectedDocument.name;
      }
    },
    calculateRetriveNoticePeriod(noticePeriod) {
      if (
        this.selectedResignationDate &&
        this.selectedExitDate &&
        this.isEdit
      ) {
        // Clone dates to avoid modifying the original ones
        const resignationDate = new Date(this.selectedResignationDate);
        resignationDate.setHours(0, 0, 0, 0); // Reset time
        const exitDate = new Date(this.selectedExitDate);
        exitDate.setHours(0, 0, 0, 0); // Reset time
        this.noticeperiod = (exitDate - resignationDate) / (1000 * 3600 * 24);
      } else {
        if (noticePeriod) {
          this.selectedResignationDate = new Date();
          const resignationDate = this.selectedResignationDate;
          resignationDate.setHours(0, 0, 0, 0);

          const exitDate = new Date(moment().add(noticePeriod, "days"));

          this.selectedExitDate = exitDate;
          exitDate.setHours(0, 0, 0, 0);
          this.noticeperiod = noticePeriod
            ? noticePeriod
            : (exitDate - resignationDate) / (1000 * 3600 * 24);
        }
      }
    },
    checkIfAlreadyPresent() {
      if (this.isEdit) {
        return true;
      } else {
        // Filter records matching the employeeId from addFormEmployeeDetails
        const filteredList = this.resignationList.filter(
          (record) => record.employeeId === this.addFormEmployeeDetails?.id
        );

        // Check the resignationStatus in matched records
        for (let record of filteredList) {
          if (record && record.resignationStatus.toLowerCase() === "applied") {
            return false;
          }
        }
        return true;
      }
    },
    changeExitDate() {
      if (
        new Date(this.editFormData.resignationDate) < new Date() &&
        this.editFormData.resignationStatus.toLowerCase() === "approved"
      ) {
        this.cancelModel = true;
      } else {
        this.isFormDirty = true;
      }
    },
    async fetchResignationAjax() {
      try {
        this.isLoading = true;
        let employeeId = this.editFormData?.employeeId
          ? this.editFormData?.employeeId
          : parseInt(this.addFormEmployeeDetails.id);
        let url =
          this.baseUrl +
          "default/employee-info/list-approver-details/employeeId/" +
          +employeeId +
          "/formName/Resignation/loginEmployeeId/" +
          this.loginEmployeeId;

        const response = await axios.get(url);
        this.ResignationAxios = response.data;
        this.isLoading = false;
      } catch (error) {
        this.handleAxiosError(error);
        this.isLoading = false;
      }
    },
    handleAxiosError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },

    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },

    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },

    // change the mode of performance management
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },

    async validateResignationForm() {
      const { valid } = await this.$refs.resignationForm.validate();
      if (valid) {
        if (this.checkIfAlreadyPresent()) {
          if (!this.isEdit) {
            try {
              this.isLoading = true;
              // Upload Document file
              if (this.selectedDocument) {
                await this.uploadDocumentFile(this.selectedDocument);
              }
              // If not in edit mode, add a new resignation record
              this.addResignationRecord();
            } catch (error) {
              this.isLoading = false;
              this.showAlert({
                isOpen: true,
                type: "warning",
                message:
                  "Something went wrong while uploading documents. Please try again.",
              });
            }
          } else {
            // If in edit mode, determine what has changed
            const hasDateChanged =
              moment(this.selectedExitDate).format("YYYY-MM-DD") !==
                this.editFormData.resignationDate ||
              moment(this.selectedResignationDate).format("YYYY-MM-DD") !==
                this.editFormData.appliedDate ||
              (this.selectedDocument?.formattedName || "") !==
                (this.editFormData.fileName || "");
            const hasReasonOrCommentsChanged =
              (this.selectedReasonForResignation || "") !==
                (this.editFormData?.esicReasonName || "") ||
              (this.selectedComments || "") !==
                (this.editFormData?.relievingReasonComment || "");

            // Call the respective API only if the relevant fields have changed
            if (hasDateChanged) {
              try {
                this.isLoading = true;
                // Upload new document if it exists
                if (this.selectedDocument?.size) {
                  await this.uploadDocumentFile(this.selectedDocument);
                }
                this.updateResignationDate();
              } catch (error) {
                this.isLoading = false;
                this.showAlert({
                  isOpen: true,
                  type: "warning",
                  message:
                    "Something went wrong while uploading documents. Please try again.",
                });
              }
            }

            if (hasReasonOrCommentsChanged) {
              this.updateResignationReason();
            }
          }
        } else {
          // Show warning if a similar resignation record already exists
          var snackbarData = {
            isOpen: true,
            type: "warning",
            message: "Resignation should be unique",
          };
          this.showAlert(snackbarData);
        }
      }
    },

    changeField(field, value) {
      this[field] = value;
    },

    calculateNoticePeriodDate() {
      if (this.selectedResignationDate && this.selectedExitDate) {
        const resignationDate = new Date(this.selectedResignationDate);
        resignationDate.setHours(0, 0, 0, 0); // Reset time
        const exitDate = new Date(this.selectedExitDate);
        exitDate.setHours(0, 0, 0, 0); // Reset time
        this.noticeperiod = (exitDate - resignationDate) / (1000 * 3600 * 24);
      } else {
        this.selectedResignationDate = null;
        this.selectedExitDate = null;
      }
    },

    addResignationRecord() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: CREATE_RESIGNATION,
            variables: {
              envelope: {
                loggedInUserId: vm.loginEmployeeId,
                orgCode: vm.orgCode,
                formId: 34,
              },
              employeeId: vm.isEdit
                ? vm.employeeId
                : vm.addFormEmployeeDetails?.id,
              esicReason: vm.selectedReasonForResignation,
              reasonId: vm.getReasonIdByName(vm.selectedReasonForResignation),
              relievingReasonComment: vm.selectedComments
                ? vm.selectedComments
                : "",
              appliedDate: moment(vm.selectedResignationDate).format(
                "YYYY/MM/DD"
              ),
              resignationDate: moment(vm.selectedExitDate).format("YYYY/MM/DD"),
              fileName: vm.selectedDocument?.formattedName || null,
            },
            client: "apolloClientZ",
          })
          .then((response) => {
            vm.isLoading = false;
            if (!response.data.createResignation.error) {
              let createResignationResponse = response.data.createResignation
                .result
                ? response.data.createResignation.result
                : {};
              var snackbarData = {
                isOpen: true,
                type: "success",
                message: "Resignation added successfully.",
              };
              vm.showAlert(snackbarData);
              vm.$emit("added-new-record", createResignationResponse);
            } else {
              snackbarData = {
                isOpen: true,
                type: "warning",
                message: response.data.createResignation.error.message,
              };
              vm.showAlert(snackbarData);
            }
          })
          .catch((err) => {
            vm.handleAddRecordError(err);
          });
      } catch (err) {
        vm.handleAddRecordError(err);
      }
    },
    handleAddRecordError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEdit ? "updating" : "adding",
        form: "Resignation",
        isListError: false,
      });
    },

    async updateResignationDate() {
      let vm = this;
      vm.isLoading = true;
      try {
        // Delete from S3 Bucket before updating if file has changed
        if (
          vm.editFormData.fileName &&
          vm.selectedDocument?.formattedName !== vm.editFormData.fileName
        ) {
          await vm.deleteDucumentsFile(vm.editFormData.fileName);
        }

        await vm.$apollo
          .mutate({
            mutation: UPDATE_RESIGNATION_DATE,
            variables: {
              envelope: {
                loggedInUserId: vm.loginEmployeeId,
                orgCode: vm.orgCode,
                formId: 34,
              },
              resignationId: vm.editFormData?.resignationId
                ? vm.editFormData?.resignationId
                : vm.addFormEmployeeDetails?.id,
              appliedDate: moment(vm.selectedResignationDate).format(
                "YYYY/MM/DD"
              ),
              resignationDate: moment(vm.selectedExitDate).format("YYYY/MM/DD"),
              fileName: vm.selectedDocument?.formattedName || "",
            },
            client: "apolloClientZ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Resignation dates updated successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("edit-updated");
          })
          .catch((err) => {
            vm.handleUpdateDateError(err);
          });
      } catch (err) {
        vm.handleUpdateDateError(err);
      }
    },
    handleUpdateDateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEdit ? "updating" : "adding",
        form: "Resignation",
        isListError: false,
      });
    },

    getReasonIdByName(esicReasonName) {
      const reasonItem = this.reasonItems.find(
        (item) => item.esicReasonName === esicReasonName
      );
      return reasonItem ? reasonItem.reasonId : null;
    },
    updateResignationReason() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_RESIGNATION_REASON,
            variables: {
              envelope: {
                loggedInUserId: vm.loginEmployeeId,
                orgCode: vm.orgCode,
                formId: 34,
              },
              esicReason: vm.selectedReasonForResignation,
              reasonId: vm.getReasonIdByName(vm.selectedReasonForResignation),
              relievingReasonComment: vm.selectedComments,
              resignationId: this.editFormData?.resignationId
                ? this.editFormData?.resignationId
                : this.addFormEmployeeDetails?.id,
            },
            client: "apolloClientZ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.isEdit
                ? "Resignation reason updated successfully."
                : "Resignation reason added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("edit-updated");
          })
          .catch((err) => {
            vm.handleUpdateReasonError(err);
          });
      } catch (err) {
        vm.handleUpdateReasonError(err);
      }
    },
    handleUpdateReasonError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEdit ? "updating" : "adding",
        form: "Resignation",
        isListError: false,
      });
    },
    async uploadDocumentFile(file) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Resignation Documents/";

      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + file.formattedName,
          action: "upload",
          type: "documents",
          fileContent: file,
        })
        .catch((error) => {
          throw error;
        });
    },
    async deleteDucumentsFile(filePath) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Resignation Documents/";
      try {
        await vm.$store.dispatch("deletes3File", {
          fileName: fileUploadUrl + filePath,
          type: "documents",
        });
      } catch (error) {
        vm.showAlert({
          isOpen: true,
          type: "warning",
          message: "File delete failed: " + error,
        });
      }
    },

    getEmployeeNotificationPeriodDays() {
      let vm = this;
      vm.isNoticePeriod = true;
      vm.$apollo
        .query({
          query: GET_EMPLOYEE_NOTIFICATION_PERIOD_DAYS,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: this.loginEmployeeId,
              orgCode: this.orgCode,
              formId: 34,
            },
            employeeId: this.addFormEmployeeDetails?.id,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getEmployeeNotificationPeriodDays &&
            !response.data.getEmployeeNotificationPeriodDays.error
          ) {
            vm.noticeperiod =
              response.data.getEmployeeNotificationPeriodDays.result;
            if (vm.noticeperiod) {
              vm.calculateRetriveNoticePeriod(vm.noticeperiod);
            }
            vm.isNoticePeriod = false;
          } else {
            this.isLoaisNoticePeriodding = false;
            vm.handleNoticePeriodError();
          }
        })
        .catch((err) => {
          this.isNoticePeriod = false;
          vm.handleNoticePeriodError(err);
        });
    },
    handleNoticePeriodError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },

    async retriveRelievingReasonDetails() {
      let vm = this;
      vm.isLoading = true;
      await vm.$apollo
        .query({
          query: GET_EMPLOYEE_RESIGNATION,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: this.loginEmployeeId,
              orgCode: this.orgCode,
              formId: 34,
            },
            resignationId: this.editFormData?.resignationId
              ? this.editFormData?.resignationId
              : this.addFormEmployeeDetails?.id,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getEmployeeResignation &&
            !response.data.getEmployeeResignation.error
          ) {
            vm.reasonItems =
              response.data.getEmployeeResignation.relievingReasonDetails;
            vm.resignationId =
              response.data.getEmployeeResignation.result?.resignationId;
            if (vm.isEdit) {
              vm.selectedReasonForResignation =
                response.data.getEmployeeResignation.result?.esicReasonName;
            } else {
              vm.selectedReasonForResignation =
                vm.reasonItems.find((item) => item.reasonId == 3)
                  ?.esicReasonName || null;
            }
            vm.isLoading = false;
          } else {
            this.isLoading = false;
            vm.handleListError();
          }
        })
        .catch((err) => {
          this.isLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
