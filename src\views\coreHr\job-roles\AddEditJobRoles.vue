<template>
  <v-overlay
    :model-value="showAddEditForm"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:50vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">
            {{ isEdit ? "Edit" : "Add" }}
            {{ landedFormName }}
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <div class="px-5 py-6">
            <v-form ref="addEditFormValidator">
              <v-row>
                <!-- Job Role Code -->
                <v-col
                  v-if="
                    labelList[426]?.Field_Visiblity?.toLowerCase() === 'yes'
                  "
                  cols="12"
                  sm="6"
                  md="6"
                  class="px-md-6"
                >
                  <v-text-field
                    variant="solo"
                    ref="jobRoleCode"
                    v-model="selectedJobRoleCode"
                    clearable
                    :rules="[
                      labelList[426].Mandatory_Field?.toLowerCase() === 'yes'
                        ? required(
                            labelList[426]?.Field_Alias,
                            selectedJobRoleCode
                          )
                        : true,
                      selectedJobRoleCode
                        ? validateWithRulesAndReturnMessages(
                            selectedJobRoleCode,
                            'Job_Role_Code',
                            labelList[426]?.Field_Alias
                          )
                        : true,
                    ]"
                    @update:model-value="
                      onChangeFieldType($event, selectedJobRole)
                    "
                  >
                    <template v-slot:label>
                      {{ labelList[426].Field_Alias }}
                      <span
                        v-if="
                          labelList[426].Mandatory_Field?.toLowerCase() ===
                          'yes'
                        "
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Job Role -->
                <v-col cols="12" sm="6" md="6" class="px-md-6">
                  <v-text-field
                    variant="solo"
                    ref="jobRole"
                    v-model="selectedJobRole"
                    clearable
                    :rules="[
                      required('Job Role', selectedJobRole),
                      validateWithRulesAndReturnMessages(
                        selectedJobRole,
                        'Job_Role',
                        'Job Role'
                      ),
                    ]"
                    @update:model-value="
                      onChangeFieldType($event, selectedJobRole)
                    "
                  >
                    <template v-slot:label>
                      Job Role
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Associate Training/Accreditation -->
                <v-col cols="12" sm="6" md="6" class="px-md-6">
                  <CustomSelect
                    ref="fieldType"
                    v-model="selectedAccreditation"
                    :items="accreditationCategoryList"
                    itemTitle="accreditationType"
                    subText="accreditationCategory"
                    itemValue="accreditationCategoryAndTypeId"
                    :loading="dropdownLoading"
                    :rules="[
                      required(
                        'Associate Training/Accreditation',
                        selectedAccreditation?.length
                      ),
                    ]"
                    label="Associate Training/Accreditation"
                    :select-properties="{
                      multiple: true,
                      chips: true,
                      clearable: true,
                      closableChips: true,
                    }"
                    :is-required="true"
                    variant="solo"
                    :isAutoComplete="true"
                    :itemSelected="selectedAccreditation"
                    @selected-item="
                      onChangeFieldType($event, selectedAccreditation)
                    "
                  />
                </v-col>
                <!-- Designation -->
                <v-col cols="12" sm="6" md="6" class="px-md-6">
                  <CustomSelect
                    ref="selectedDesignation"
                    v-model="selectedDesignation"
                    :isLoading="designationListLoading"
                    :rules="[
                      required('Designation', selectedDesignation?.length),
                    ]"
                    :items="designationList"
                    item-title="Designation_Name"
                    item-value="Designation_Id"
                    label="Designation"
                    :isRequired="true"
                    variant="solo"
                    placeholder="Type minimum 3 characters to list"
                    :no-data-text="noDataText"
                    :isAutoComplete="true"
                    :itemSelected="selectedDesignation"
                    :select-properties="{
                      multiple: true,
                      chips: true,
                      clearable: true,
                      closableChips: true,
                    }"
                    @selected-item="
                      onChangeFieldType($event, selectedDesignation)
                    "
                    @update-search-value="callDesignationList($event)"
                  />
                  <v-btn
                    class="mt-n4 ml-n2"
                    color="primary"
                    variant="text"
                    size="small"
                    @click="openRedirectingForm('in/core-hr/designations')"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
                    Designation</v-btn
                  >
                </v-col>
                <!-- Status -->
                <v-col cols="12" sm="6" md="6" class="px-md-6">
                  <div class="v-label mr-4">
                    <span>Status</span>
                  </div>
                  <AppToggleButton
                    button-active-text="Active"
                    button-inactive-text="InActive"
                    button-active-color="#7de272"
                    button-inactive-color="red"
                    id-value="gab-analysis-based-on"
                    :isDisableToggle="!isEdit"
                    :current-value="selectedStatus === 'Active' ? true : false"
                    @chosen-value="onChangeStatus($event)"
                    @update:model-value="isFormDirty = true"
                  />
                </v-col>
                <!-- Description -->
                <v-col cols="12" class="px-md-6"
                  ><v-textarea
                    v-model="selectedDescription"
                    variant="solo"
                    auto-grow
                    label="Description"
                    rows="2"
                    :rules="[
                      selectedDescription
                        ? validateWithRulesAndReturnMessages(
                            selectedDescription,
                            'departmentDescription',
                            'Description'
                          )
                        : true,
                    ]"
                    @update:model-value="
                      onChangeFieldType($event, selectedDescription)
                    "
                /></v-col>
              </v-row>
            </v-form>
            <div class="card-actions-div">
              <v-sheet class="align-center text-center" style="width: 100%">
                <v-row justify="center">
                  <v-col cols="12" class="d-flex justify-end pr-4">
                    <v-btn
                      rounded="lg"
                      class="mr-6"
                      variant="outlined"
                      @click="$emit('close-form')"
                    >
                      Cancel
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      class="mr-1 primary"
                      variant="elevated"
                      :disabled="isLoading"
                      @click="validateCustomFields()"
                    >
                      Save
                    </v-btn>
                  </v-col>
                </v-row>
              </v-sheet>
            </div>
            <AppLoading v-if="isLoading" />
            <AppSnackBar
              v-if="showValidationAlert"
              :show-snack-bar="showValidationAlert"
              snack-bar-type="warning"
              timeOut="-1"
              @close-snack-bar="closeValidationAlert"
            >
              <template #custom-alert>
                <div
                  v-for="(validationMsg, index) of validationMessages"
                  :key="validationMsg + index"
                  class="text-subtitle-1"
                >
                  {{ validationMsg }}
                </div>
                <div class="d-flex justify-end">
                  <v-btn
                    class="mt-n5 secondary"
                    variant="text"
                    @click="closeValidationAlert()"
                  >
                    Close
                  </v-btn>
                </div>
              </template>
            </AppSnackBar>
          </div>
        </v-card-text>
      </v-card>
    </template>
  </v-overlay>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { ADD_UPDATE_JOB_ROLE } from "@/graphql/corehr/jobRolesQueries.js";
import { RETRIEVE_ACCREDITATION_TYPE } from "@/graphql/dropDownQueries.js";
export default {
  name: "AddEditJobRoles",
  data() {
    return {
      showAddEditForm: true,
      isLoading: false,
      showValidationAlert: false,
      validationMessages: [],
      isFormDirty: false,
      // Fields
      selectedJobRoleCode: null,
      selectedJobRole: null,
      selectedDesignation: null,
      selectedAccreditation: null,
      selectedStatus: this.isEdit ? "" : "Active",
      selectedDescription: null,
      // Lists & Loders
      accreditationCategoryList: [],
      designationList: [],
      designationListLoading: false,
      dropdownLoading: false,
      searchString: "",
    };
  },
  components: {
    CustomSelect,
  },
  mixins: [validationRules],
  emits: ["close-form", "edit-updated"],
  props: {
    selectedItem: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  computed: {
    noDataText() {
      if (this.designationListLoading) {
        return "Loading...";
      } else if (
        !this.designationListLoading &&
        this.designationList.length == 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else if (
        this.selectedDesignation?.length > 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
  },
  mounted() {
    this.fetchAccreditations();
    if (this.isEdit) this.prefillDetails();
  },
  methods: {
    onCloseOverlay() {
      this.showAddEditForm = false;
      this.$emit("close-form");
    },
    prefillDetails() {
      this.selectedJobRoleCode = this.selectedItem.Job_Role_Code;
      this.selectedJobRole = this.selectedItem.Job_Role;
      this.selectedDesignation = this.selectedItem.Designation_Ids;
      this.selectedAccreditation =
        this.selectedItem.Accreditation_Category_And_Type_Ids;
      this.selectedStatus = this.selectedItem.Status;
      this.selectedDescription = this.selectedItem.Description;
      this.designationList = this.selectedItem.designations;
    },
    callDesignationList(searchString) {
      this.searchString = searchString;
      if (searchString.length >= 3) {
        this.getDesignationList(searchString);
      }
    },

    async getDesignationList(searchString) {
      this.designationListLoading = true;
      let choosenDesignation = [];
      if (this.designationList?.length)
        choosenDesignation = this.designationList?.filter((designation) =>
          this.selectedDesignation.includes(designation.Designation_Id)
        );
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designationList = designationResult.concat(choosenDesignation);
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.designationList = [];
        });
    },
    fetchAccreditations() {
      let vm = this;
      vm.dropdownLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_ACCREDITATION_TYPE,
          client: "apolloClientV",
          variables: {
            urlHash: "",
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveAccreditationAndType &&
            res.data.retrieveAccreditationAndType.accreditationAndType &&
            !res.data.retrieveAccreditationAndType.errorCode
          ) {
            vm.accreditationCategoryList =
              res.data.retrieveAccreditationAndType.accreditationAndType || [];
          } else {
            let err = res.data.retrieveAccreditationAndType?.errorCode || "";
            vm.handleAccrediations(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleAccrediations(err);
        });
    },
    handleAccrediations(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: this.landedFormName,
        isListError: false,
      });
    },
    openRedirectingForm(path) {
      const redirectionPath = `${this.baseUrl}${path}`;
      window.open(redirectionPath, "_blank");
    },
    onChangeStatus(value) {
      this.selectedStatus = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },
    onChangeFieldType() {
      this.isFormDirty = true;
    },
    async validateCustomFields() {
      // checking whether the fields of form are valid or not
      const { valid } = await this.$refs.addEditFormValidator.validate();
      // submit the form only if all the fields are filled
      if (valid) {
        this.addUpdateJobRoles();
      }
    },
    addUpdateJobRoles() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_JOB_ROLE,
          client: "apolloClientBB",
          variables: {
            Form_Id: 329,
            Job_Role_Id: vm.isEdit ? vm.selectedItem.Job_Role_Id : 0,
            Job_Role: vm.selectedJobRole,
            Job_Role_Code: vm.selectedJobRoleCode,
            Designation_Ids: vm.selectedDesignation || [],
            Accreditation_Category_And_Type_Ids: vm.selectedAccreditation || [],
            Status: vm.isEdit ? vm.selectedStatus : "Active",
            Description: vm.selectedDescription || null,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.addUpdateJobRole &&
            !response.data.addUpdateJobRole.errorCode
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: response.data.addUpdateJobRole?.message,
            };
            vm.showAddEditForm = false;
            vm.showAlert(snackbarData);
            vm.$emit("edit-updated");
          } else {
            vm.handleAddUpdateErrors(
              response.data.addUpdateJobRole?.errorCode || ""
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.handleAddUpdateErrors(error);
        });
    },
    handleAddUpdateErrors(err = "") {
      // Check if the error contains GraphQL errors and extensions
      if (
        err &&
        err.graphQLErrors &&
        err.graphQLErrors[0] &&
        err.graphQLErrors[0].extensions
      ) {
        const graphQLError = err.graphQLErrors[0];

        // Handle associated forms error (if any)
        const associatedForms = graphQLError.extensions.associatedForms;
        if (associatedForms && associatedForms.length > 0) {
          let errorMessage =
            "Operation failed as the following forms are associated with this job roles:\n\n";
          associatedForms.forEach((form, index) => {
            // Add a comma for all but the last item
            if (index === associatedForms.length - 1) {
              errorMessage += ` ${form}.`; // Add a period for the last item
            } else {
              errorMessage += ` ${form},\n`; // Add a comma and newline for other items
            }
          });
          errorMessage +=
            "\nPlease ensure the forms are reassigned or removed before proceeding.";

          // Show alert with the associated forms
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: errorMessage,
          });
          return; // Exit the function after handling associated forms error
        }
      }
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.landedFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
<style scoped>
#integration-form > .v-overlay__content {
  height: 100%;
  width: 50%;
}
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
