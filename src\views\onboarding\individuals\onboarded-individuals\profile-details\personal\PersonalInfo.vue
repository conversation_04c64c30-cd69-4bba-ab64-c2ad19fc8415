<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs('error')"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else-if="isMounted">
    <PersonalDetails
      v-if="!openedEditForm || openedEditForm === 'personalDetails'"
      ref="personalDetails"
      :personalDetailsData="personalDetailsData"
      :formAccess="formAccess"
      :selectedCandidateId="candidateIdSelected"
      @refetch-personal-details="refetchAPIs('update', $event)"
      @edit-opened="openedEditForm = 'personalDetails'"
      @edit-closed="openedEditForm = ''"
      @close-add-form="$emit('close-add-form')"
    />
    <DependentDetails
      v-if="!openedEditForm"
      ref="dependentDetails"
      :dependentDetailsData="dependentDetailsData"
      :selectedCandidateId="candidateIdSelected"
      :formAccess="formAccess"
      :selectedEmpMaritalStatus="selectedEmpMaritalStatus"
      @refetch-personal-details="refetchAPIs('update')"
    />
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
// components
const PersonalDetails = defineAsyncComponent(() =>
  import("./details/PersonalDetails.vue")
);
const DependentDetails = defineAsyncComponent(() =>
  import("./dependent/DependentDetails.vue")
);
import { RETRIEVE_EMP_PERSONAL_INFO } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default defineComponent({
  name: "PersonalInfo",
  components: {
    PersonalDetails,
    DependentDetails,
  },
  props: {
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    refetchCount: {
      type: Number,
      default: 0,
    },
  },
  emits: [
    "selected-emp-dob",
    "close-add-form",
    "details-retrieved",
    "selected-candidate-id",
    "details-updated",
    "opened-edit-form",
  ],
  data() {
    return {
      openedEditForm: "",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      personalDetailsData: [],
      dependentDetailsData: [],
      selectedCandidateDOB: "",
      isLoading: false,
      candidateIdSelected: 0,
      isMounted: false,
      isUpdated: false,
      selectedEmpMaritalStatus: 0,
    };
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  watch: {
    openedEditForm(val) {
      this.$emit("opened-edit-form", val);
    },
    refetchCount(count) {
      if (count > 0) {
        this.candidateIdSelected = this.selectedCandidateId;
        this.refetchAPIs("refresh");
      }
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.candidateIdSelected = this.selectedCandidateId;
    this.getPersonalDetails();
    this.isMounted = true;
  },
  methods: {
    refetchAPIs(type, candidateId) {
      if (candidateId) {
        this.candidateIdSelected = candidateId;
        this.$emit("selected-candidate-id", candidateId);
      }
      this.openedEditForm = "";
      this.isErrorInList = false;
      mixpanel.track("Onboarded-candidate-personalDetails-refetch");
      if (type === "update") {
        this.$emit("details-updated");
      }
      this.getPersonalDetails(type);
    },
    getPersonalDetails(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_PERSONAL_INFO,
          client: "apolloClientV",
          variables: {
            candidateId: vm.candidateIdSelected,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("Onboarded-candidate-personalDetails-fetch-success");
          if (response && response.data && response.data.retrievePersonalInfo) {
            const { personalInfoDetails, dependentDetails } =
              response.data.retrievePersonalInfo;
            vm.personalDetailsData = personalInfoDetails
              ? [JSON.parse(personalInfoDetails)]
              : [];
            if (vm.personalDetailsData && vm.personalDetailsData.length > 0) {
              vm.selectedCandidateDOB = vm.personalDetailsData[0].DOB;
              vm.$emit("selected-emp-dob", vm.selectedCandidateDOB);
              vm.selectedEmpMaritalStatus =
                vm.personalDetailsData[0].Marital_Status;
            }
            vm.dependentDetailsData = dependentDetails
              ? JSON.parse(dependentDetails)
              : [];
            vm.$emit("details-retrieved", [
              type,
              vm.personalDetailsData && vm.personalDetailsData.length > 0
                ? vm.personalDetailsData[0]
                : {},
            ]);
            if (type === "update") {
              vm.openedEditForm = "";
            } else if (!this.selectedCandidateId) {
              vm.openedEditForm = "personalDetails";
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("Onboarded-candidate-personalDetails-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "personal details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
});
</script>
