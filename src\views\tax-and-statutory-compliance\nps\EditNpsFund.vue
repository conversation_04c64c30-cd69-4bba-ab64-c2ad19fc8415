<template>
  <div v-if="isMounted">
    <section>
      <div>
        <v-card
          class="py-9 rounded-lg"
          :class="isMobileView ? '' : 'px-5'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="npsFund">
              <v-row class="d-flex justify-space-between mb-4">
                <div class="d-flex align-center">
                  <v-progress-circular
                    model-value="100"
                    color="secondary"
                    :size="22"
                    class="mr-1"
                  ></v-progress-circular>
                  <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                    accessFormName
                  }}</span>
                </div>
                <div
                  class="d-flex align-center pa-1"
                  :class="isMobileView ? 'ml-auto' : ''"
                >
                  <v-btn
                    rounded="lg"
                    variant="outlined"
                    color="primary"
                    class="mr-2"
                    @click="closeEditForm()"
                  >
                    Cancel
                  </v-btn>
                  <div class="mt-2 mr-1">
                    <v-btn
                      v-if="isFormDirty"
                      rounded="lg"
                      color="secondary"
                      class="mb-2"
                      @click="validateLNpsFundForm()"
                      >Save</v-btn
                    >
                    <v-tooltip v-else location="bottom">
                      <template v-slot:activator="{ props }">
                        <v-btn
                          v-bind="props"
                          rounded="lg"
                          color="grey-lighten-3"
                          class="cursor-not-allow mb-2"
                          variant="flat"
                          >Save</v-btn
                        >
                      </template>
                      <div>There are no changes to be updated</div>
                    </v-tooltip>
                  </div>
                </div>
              </v-row>
              <v-row>
                <v-col
                  v-if="getFieldAlias[52].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex">
                    <span class="v-label pr-3 pb-5">{{
                      getFieldAlias[52].Field_Alias
                    }}</span>
                    <v-switch
                      color="secondary"
                      class="ml-2"
                      v-model="autoDeclaration"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias[53].Field_Visiblity == 'Yes' &&
                    autoDeclaration === 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                  class="d-flex"
                >
                  <CustomSelect
                    :items="investmentCategoryList"
                    :itemSelected="investmentCategoryId"
                    :label="getFieldAlias[53].Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias[53].Mandatory_Field == 'Yes' ? true : false
                    "
                    item-title="Investment_Category"
                    item-value="Investment_Cat_Id"
                    :is-loading="isListLoading"
                    :disabled="isListLoading"
                    @selected-item="
                      onChangeIsFormDirty($event, 'investmentCategoryId')
                    "
                    :rules="[
                      getFieldAlias[53].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[53].Field_Alias}`,
                            investmentCategoryId
                          )
                        : true,
                    ]"
                    listWidth="max-width: 300px !important"
                    style="max-width: 300px"
                  ></CustomSelect>
                  <v-btn
                    color="white"
                    rounded="lg"
                    class="ml-2 mt-2"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="getInvestmentCategoryList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
                <v-col
                  v-if="getFieldAlias[54].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="npsDeductionPercentage"
                    type="number"
                    :min="0"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias[54].Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias[54].Field_Alias}`,
                            npsDeductionPercentage
                          )
                        : true,

                      npsDeductionPercentage
                        ? validateWithRulesAndReturnMessages(
                            npsDeductionPercentage,
                            'npsDeductionPercentage',
                            `${getFieldAlias[54].Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="
                      validateInput('npsDeductionPercentage')
                    "
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias[54].Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias[54].Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias[55].Field_Visiblity == 'Yes' &&
                    autoDeclaration === 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <CustomSelect
                    :items="['Employee Share']"
                    :itemSelected="autoDeclarationApplicableFor"
                    :label="getFieldAlias[55].Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias[55].Mandatory_Field == 'Yes' ? true : false
                    "
                    @selected-item="
                      onChangeIsFormDirty(
                        $event,
                        'autoDeclarationApplicableFor'
                      )
                    "
                    :rules="[
                      getFieldAlias[55].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[55].Field_Alias}`,
                            autoDeclarationApplicableFor
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                  ></CustomSelect>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
      <AppWarningModal
        v-if="openConfirmationPopup"
        :open-modal="openConfirmationPopup"
        confirmation-heading="Are you sure to exit this form?"
        imgUrl="common/exit_form"
        @close-warning-modal="abortClose()"
        @accept-modal="acceptClose()"
      >
      </AppWarningModal>
    </section>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
// Queries
import { UPDATE_NPS_FUND_RULES } from "@/graphql/tax-and-statutory-compliance/npsRules";
import { LIST_INVESTMENT_CATEGORY } from "@/graphql/tax-and-statutory-compliance/providentFundRules";

export default {
  name: "EditNpsFund",
  mixins: [validationRules],
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    getFieldAlias: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    CustomSelect,
  },
  data() {
    return {
      isLoading: false,
      isMounted: false,
      isFormDirty: false,
      openConfirmationPopup: false,
      npsDeductionPercentage: null,
      employerShare: null,
      autoDeclarationApplicableFor: "Employee Share",
      autoDeclaration: "Yes",
      investmentCategoryId: null,
      showValidationAlert: false,
      validationMessages: [],
      investmentCategoryList: [],
      retrievedInvestmentId: null,
      isListLoading: false,
      npsRulesId: 0,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    const {
      Auto_Declaration,
      Auto_Declaration_Applicable_For,
      Investment_Category_Id,
      NPS_Deduction_Percentage,
      Nps_Rules_Id,
    } = this.editFormData;
    this.npsRulesId = Nps_Rules_Id ? Nps_Rules_Id : 1;
    this.autoDeclaration =
      this.getFieldAlias[52].Field_Visiblity == "Yes" && Auto_Declaration
        ? Auto_Declaration
        : "No";
    this.autoDeclarationApplicableFor =
      this.getFieldAlias[55].Field_Visiblity == "Yes" &&
      Auto_Declaration_Applicable_For
        ? Auto_Declaration_Applicable_For
        : null;
    this.getInvestmentCategoryList("Edit", Investment_Category_Id);
    this.npsDeductionPercentage =
      this.getFieldAlias[54].Field_Visiblity == "Yes" &&
      NPS_Deduction_Percentage
        ? parseFloat(NPS_Deduction_Percentage)
        : null;

    this.isMounted = true;
  },
  watch: {
    autoDeclaration(val) {
      if (val === "No") {
        this.investmentCategoryId = null;
      }
    },
  },
  methods: {
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
    },
    onChangeIsFormDirty(val, field) {
      if (field == "autoDeclarationApplicableFor") {
        this.autoDeclarationApplicableFor = val;
      } else if (field == "investmentCategoryId") {
        this.investmentCategoryId = val;
      }
      this.isFormDirty = true;
    },
    async validateLNpsFundForm() {
      const { valid } = await this.$refs.npsFund.validate();
      if (valid) {
        this.updateNpsFund();
      }
    },
    updateNpsFund() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_NPS_FUND_RULES,
            variables: {
              npsRulesId: vm.npsRulesId ? vm.npsRulesId : 1,
              autoDeclaration: vm.autoDeclaration ? vm.autoDeclaration : "No",
              autoDeclarationApplicableFor:
                vm.autoDeclaration == "Yes"
                  ? vm.autoDeclarationApplicableFor
                  : null,
              npsDeductionPercentage: vm.npsDeductionPercentage
                ? parseFloat(vm.npsDeductionPercentage)
                : null,
              investmentCategoryId:
                vm.autoDeclaration == "Yes" ? vm.investmentCategoryId : null,
            },
            client: "apolloClientAK",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `${this.accessFormName} updated successfully.`,
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((error) => {
            vm.handleUpdateError(error);
          });
      } catch {
        vm.handleUpdateError();
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: this.accessFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    closeEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
      this.isFormDirty = false;
    },
    getInvestmentCategoryList(action, Investment_Category_Id = null) {
      let vm = this;
      vm.isListLoading = true;
      vm.$apollo
        .query({
          query: LIST_INVESTMENT_CATEGORY,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listInvestmentCategory) {
            vm.investmentCategoryList =
              response.data.listInvestmentCategory.investmentCategoriesData;
            if (action == "Edit") {
              vm.investmentCategoryId = Investment_Category_Id;
            }
            if (vm.investmentCategoryList.length <= 0) {
              vm.investmentCategoryId = null;
              vm.retrievedInvestmentId = null;
            }
          } else {
            vm.handleListError((err = ""), this.accessFormName);
          }
          vm.isListLoading = false;
        })
        .catch((err) => {
          vm.isListLoading = false;
          // Form Name will be going to change dynamically
          vm.handleListError(err, "Investment Category List");
        });
    },
    handleListError(err = "", field_name) {
      this.isListLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: field_name,
        isListError: false,
      });
    },
  },
};
</script>
