<template>
  <div v-if="isMounted">
    <section :class="isMobileView ? 'mt-8' : 'mt-4'">
      <div>
        <v-card
          class="py-9 rounded-lg"
          :class="isMobileView ? '' : 'px-5'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="providentFund">
              <v-row class="d-flex justify-space-between mb-4">
                <div class="d-flex align-center">
                  <v-progress-circular
                    model-value="100"
                    color="secondary"
                    :size="22"
                    class="mr-1"
                  ></v-progress-circular>
                  <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                    accessFormName
                  }}</span>
                </div>
                <div
                  class="d-flex align-center pa-1"
                  :class="isMobileView ? 'ml-auto' : ''"
                >
                  <v-btn
                    rounded="lg"
                    variant="outlined"
                    color="primary"
                    class="mr-2"
                    @click="closeEditForm()"
                  >
                    Cancel
                  </v-btn>
                  <div class="mt-2 mr-1">
                    <v-btn
                      v-if="isFormDirty"
                      rounded="lg"
                      color="secondary"
                      class="mb-2"
                      @click="validateLProvidentFundForm()"
                      >Save</v-btn
                    >
                    <v-tooltip v-else location="bottom">
                      <template v-slot:activator="{ props }">
                        <v-btn
                          v-bind="props"
                          rounded="lg"
                          color="grey-lighten-3"
                          class="cursor-not-allow mb-2"
                          variant="flat"
                          >Save</v-btn
                        >
                      </template>
                      <div>There are no changes to be updated</div>
                    </v-tooltip>
                  </div>
                </div>
              </v-row>
              <v-row>
                <v-col
                  v-if="getFieldAlias(28).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="restrictPFWageAmount"
                    type="number"
                    :min="1"
                    :max="9999999.99"
                    variant="solo"
                    :rules="[
                      getFieldAlias(28).Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias(28).Field_Alias}`,
                            restrictPFWageAmount
                          )
                        : true,
                      restrictPFWageAmount
                        ? validateWithRulesAndReturnMessages(
                            restrictPFWageAmount,
                            'Restricted_PF_Wage_Amount',
                            `${getFieldAlias(28).Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('restrictPFWageAmount')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(28).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(28).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(29).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employeeShare"
                    type="number"
                    :min="0"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias(29).Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias(29).Field_Alias}`,
                            employeeShare
                          )
                        : true,

                      employeeShare
                        ? validateWithRulesAndReturnMessages(
                            employeeShare,
                            'Employee_Share',
                            `${getFieldAlias(29).Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('employeeShare')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(29).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(29).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(30).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employerShare"
                    type="number"
                    :min="0"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias(30).Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias(30).Field_Alias}`,
                            employerShare
                          )
                        : true,

                      employerShare
                        ? validateWithRulesAndReturnMessages(
                            employerShare,
                            'Employer_Share',
                            `${getFieldAlias(30).Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('employerShare')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(30).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(30).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(31).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employeePensionScheme"
                    type="number"
                    :min="0"
                    :max="employerShare"
                    variant="solo"
                    :rules="[
                      getFieldAlias(31).Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias(31).Field_Alias}`,
                            employeePensionScheme
                          )
                        : true,
                      employeePensionScheme
                        ? twoDecimalPrecisionValidation(employeePensionScheme)
                        : true,
                      employeePensionScheme
                        ? minMaxNumberValidation(
                            `${getFieldAlias(31).Field_Alias}`,
                            parseFloat(employeePensionScheme),
                            0,
                            parseFloat(employerShare)
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('employeePensionScheme')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(31).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(31).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(32).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employeesProvidentFund"
                    type="number"
                    :min="0"
                    :max="employerShare - employeePensionScheme"
                    variant="solo"
                    :rules="[
                      getFieldAlias(32).Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias(32).Field_Alias}`,
                            employeesProvidentFund
                          )
                        : true,
                      employeesProvidentFund
                        ? twoDecimalPrecisionValidation(employeesProvidentFund)
                        : true,
                      employeesProvidentFund ? equalNumberValidation : true,
                    ]"
                    @update:model-value="
                      validateInput('employeesProvidentFund')
                    "
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(32).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(32).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(33).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="adminCharge"
                    type="number"
                    :min="0.1"
                    :max="0.5"
                    variant="solo"
                    :rules="[
                      getFieldAlias(33).Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias(33).Field_Alias}`,
                            adminCharge
                          )
                        : true,

                      adminCharge
                        ? validateWithRulesAndReturnMessages(
                            adminCharge,
                            'Admin_Charge',
                            `${getFieldAlias(33).Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('adminCharge')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(33).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(33).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(34).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="adminChargeMaximumAmount"
                    type="number"
                    :min="1"
                    :max="500"
                    variant="solo"
                    :rules="[
                      getFieldAlias(34).Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias(34).Field_Alias}`,
                            adminChargeMaximumAmount
                          )
                        : true,

                      adminChargeMaximumAmount
                        ? validateWithRulesAndReturnMessages(
                            adminChargeMaximumAmount,
                            'Admin_Charge_Max_Amount',
                            `${getFieldAlias(34).Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="
                      validateInput('adminChargeMaximumAmount')
                    "
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(34).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(34).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(35).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="edliConfigurationByEmployer"
                    type="number"
                    :min="0.1"
                    :max="0.5"
                    variant="solo"
                    :rules="[
                      getFieldAlias(36).Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias(35).Field_Alias}`,
                            edliConfigurationByEmployer
                          )
                        : true,

                      edliConfigurationByEmployer
                        ? validateWithRulesAndReturnMessages(
                            edliConfigurationByEmployer,
                            'EDLI_Charge',
                            `${getFieldAlias(35).Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="
                      validateInput('edliConfigurationByEmployer')
                    "
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(35).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(35).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(36).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="edliConfigurationMaximumAmount"
                    type="number"
                    :min="0.1"
                    :max="75"
                    variant="solo"
                    :rules="[
                      getFieldAlias(36).Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias(36).Field_Alias}`,
                            edliConfigurationMaximumAmount
                          )
                        : true,
                      edliConfigurationMaximumAmount
                        ? validateWithRulesAndReturnMessages(
                            edliConfigurationMaximumAmount,
                            'EDLI_Charge_Max_Amount',
                            `${getFieldAlias(36).Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="
                      validateInput('edliConfigurationMaximumAmount')
                    "
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(36).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(36).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <v-col
                  v-if="getFieldAlias(47).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex">
                    <span class="v-label pr-3 pb-5">{{
                      getFieldAlias(47).Field_Alias
                    }}</span>
                    <v-switch
                      color="secondary"
                      class="ml-2"
                      v-model="autoDeclaration"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="
                        onChangeIsFormDirty(autoDeclaration, 'autoDeclaration')
                      "
                    ></v-switch>
                  </div>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias(48).Field_Visiblity == 'Yes' &&
                    autoDeclaration === 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                  class="d-flex"
                >
                  <CustomSelect
                    :items="investmentCategoryList"
                    :itemSelected="investmentCategoryId"
                    :label="getFieldAlias(48).Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias(48).Mandatory_Field == 'Yes' ? true : false
                    "
                    item-title="Investment_Category"
                    item-value="Investment_Cat_Id"
                    :is-loading="isListLoading"
                    :disabled="isListLoading"
                    @selected-item="
                      onChangeIsFormDirty($event, 'investmentCategoryId')
                    "
                    :rules="[
                      getFieldAlias(48).Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias(48).Field_Alias}`,
                            investmentCategoryId
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                    listWidth="max-width: 300px !important"
                  ></CustomSelect>
                  <v-btn
                    color="white"
                    rounded="lg"
                    class="ml-2 mt-2"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="getInvestmentCategoryList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias(49).Field_Visiblity == 'Yes' &&
                    autoDeclaration === 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <CustomSelect
                    :items="['Employee Share']"
                    :itemSelected="autoDeclarationApplicableFor"
                    :label="getFieldAlias(49).Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias(49).Mandatory_Field == 'Yes' ? true : false
                    "
                    @selected-item="
                      onChangeIsFormDirty(
                        $event,
                        'autoDeclarationApplicableFor'
                      )
                    "
                    :rules="[
                      getFieldAlias(49).Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias(49).Field_Alias}`,
                            autoDeclarationApplicableFor
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                  ></CustomSelect>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
      <AppWarningModal
        v-if="openConfirmationPopup"
        :open-modal="openConfirmationPopup"
        confirmation-heading="Are you sure to exit this form?"
        imgUrl="common/exit_form"
        @close-warning-modal="abortClose()"
        @accept-modal="acceptClose()"
      >
      </AppWarningModal>
    </section>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import { convertUTCToLocal, checkNullValue } from "@/helper.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
// Queries
import {
  UPDATE_PROVIDENT_FUND_RULES,
  LIST_INVESTMENT_CATEGORY,
} from "@/graphql/tax-and-statutory-compliance/providentFundRules";

export default {
  name: "EditProvidentFund",
  mixins: [validationRules],
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    labelList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    CustomSelect,
  },
  data() {
    return {
      isLoading: false,
      isMounted: false,
      isFormDirty: false,
      openConfirmationPopup: false,
      restrictPFWageAmount: null,
      employeeShare: null,
      employerShare: null,
      employeesProvidentFund: null,
      employeePensionScheme: null,
      adminCharge: null,
      adminChargeMaximumAmount: null,
      edliConfigurationByEmployer: null,
      edliConfigurationMaximumAmount: null,
      autoDeclarationApplicableFor: "Employee Share",
      autoDeclaration: "No",
      investmentCategoryId: null,
      showValidationAlert: false,
      validationMessages: [],
      investmentCategoryList: [],
      retrievedInvestmentId: null,
      isListLoading: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    equalNumberValidation() {
      const tolerance = 0.0;
      const sum =
        parseFloat(this.employeePensionScheme) +
        parseFloat(this.employeesProvidentFund);
      const employerShare = parseFloat(this.employerShare);

      if (Math.abs(sum - employerShare) > tolerance) {
        return `Sum of ${this.getFieldAlias(31).Field_Alias} and ${
          this.getFieldAlias(32).Field_Alias
        } contribution from employer should be approximately equal to ${
          this.getFieldAlias(30).Field_Alias
        } of ${employerShare} %`;
      }
      return true;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    const {
      Restricted_PF_Wage_Amount,
      Employee_Share,
      Employer_Share,
      EPF_Account_Share,
      Employees_Pension_Scheme,
      Admin_Charge,
      Admin_Charge_Max_Amount,
      EDLI_Charge,
      EDLI_Charge_Max_Amount,
      Auto_Declaration,
      Auto_Declaration_Applicable_For,
      Investment_Cat_Id,
    } = this.editFormData;
    this.restrictPFWageAmount =
      this.getFieldAlias(28).Field_Visiblity == "Yes" &&
      Restricted_PF_Wage_Amount
        ? Restricted_PF_Wage_Amount
        : null;
    this.employeeShare =
      this.getFieldAlias(29).Field_Visiblity == "Yes" && Employee_Share
        ? Employee_Share
        : null;
    this.employerShare =
      this.getFieldAlias(30).Field_Visiblity == "Yes" && Employer_Share
        ? Employer_Share
        : null;
    this.employeePensionScheme =
      this.getFieldAlias(31).Field_Visiblity == "Yes" &&
      Employees_Pension_Scheme
        ? Employees_Pension_Scheme
        : null;
    this.employeesProvidentFund =
      this.getFieldAlias(32).Field_Visiblity == "Yes" && EPF_Account_Share
        ? EPF_Account_Share
        : null;

    this.adminCharge =
      this.getFieldAlias(33).Field_Visiblity == "Yes" && Admin_Charge
        ? Admin_Charge
        : null;
    this.adminChargeMaximumAmount =
      this.getFieldAlias(34).Field_Visiblity == "Yes" && Admin_Charge_Max_Amount
        ? Admin_Charge_Max_Amount
        : null;
    this.edliConfigurationByEmployer =
      this.getFieldAlias(35).Field_Visiblity == "Yes" && EDLI_Charge
        ? EDLI_Charge
        : null;
    this.edliConfigurationMaximumAmount =
      this.getFieldAlias(36).Field_Visiblity == "Yes" && EDLI_Charge_Max_Amount
        ? EDLI_Charge_Max_Amount
        : null;
    this.autoDeclaration =
      this.getFieldAlias(47).Field_Visiblity == "Yes" && Auto_Declaration
        ? Auto_Declaration
        : "No";
    this.autoDeclarationApplicableFor =
      this.getFieldAlias(49).Field_Visiblity == "Yes" &&
      Auto_Declaration_Applicable_For
        ? Auto_Declaration_Applicable_For
        : null;
    this.retrievedInvestmentId =
      this.getFieldAlias(49).Field_Visiblity == "Yes" && Investment_Cat_Id
        ? Investment_Cat_Id
        : null;
    this.getInvestmentCategoryList("Edit");
    this.isMounted = true;
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    getFieldAlias(fieldId) {
      return this.labelList.find((field) => field.Field_Id === fieldId) || {};
    },
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
    },
    onChangeIsFormDirty(val, field) {
      if (field == "autoDeclarationApplicableFor") {
        this.autoDeclarationApplicableFor = val;
      } else if (field == "investmentCategoryId") {
        this.investmentCategoryId = val;
      } else if (field == "autoDeclaration") {
        this.investmentCategoryId = null;
      }
      this.isFormDirty = true;
    },
    async validateLProvidentFundForm() {
      const { valid } = await this.$refs.providentFund.validate();
      if (valid) {
        this.updateProvidentFund();
      }
    },
    updateProvidentFund() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_PROVIDENT_FUND_RULES,
            variables: {
              Restricted_PF_Wage_Amount: vm.restrictPFWageAmount
                ? parseFloat(vm.restrictPFWageAmount)
                : null,
              Employee_Share: vm.employeeShare
                ? parseFloat(vm.employeeShare)
                : null,
              Employer_Share: vm.employerShare
                ? parseFloat(vm.employerShare)
                : null,
              Employees_Pension_Scheme: vm.employeePensionScheme
                ? parseFloat(vm.employeePensionScheme)
                : null,
              EPF_Account_Share: vm.employeesProvidentFund
                ? parseFloat(vm.employeesProvidentFund)
                : null,

              Admin_Charge: vm.adminCharge ? parseFloat(vm.adminCharge) : null,
              Admin_Charge_Max_Amount: vm.adminChargeMaximumAmount
                ? parseFloat(vm.adminChargeMaximumAmount)
                : null,
              EDLI_Charge: vm.edliConfigurationByEmployer
                ? parseFloat(vm.edliConfigurationByEmployer)
                : null,
              EDLI_Charge_Max_Amount: vm.edliConfigurationMaximumAmount
                ? parseFloat(vm.edliConfigurationMaximumAmount)
                : null,
              Auto_Declaration: vm.autoDeclaration ? vm.autoDeclaration : "No",
              Auto_Declaration_Applicable_For:
                vm.autoDeclaration == "Yes"
                  ? vm.autoDeclarationApplicableFor
                  : null,
              Investment_Cat_Id:
                vm.autoDeclaration == "Yes"
                  ? parseInt(vm.investmentCategoryId)
                  : null,
            },
            client: "apolloClientAK",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `${this.accessFormName} updated successfully.`,
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((error) => {
            vm.handleUpdateError(error);
          });
      } catch {
        vm.handleUpdateError((err = ""));
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: this.accessFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    closeEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
      this.isFormDirty = false;
    },
    getInvestmentCategoryList(action) {
      let vm = this;
      vm.isListLoading = true;
      vm.$apollo
        .query({
          query: LIST_INVESTMENT_CATEGORY,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listInvestmentCategory) {
            vm.investmentCategoryList =
              response.data.listInvestmentCategory.investmentCategoriesData;
            if (action == "Edit") {
              vm.investmentCategoryId = vm.retrievedInvestmentId;
            }
          } else {
            vm.handleListError((err = ""), this.accessFormName);
          }
          vm.isListLoading = false;
        })
        .catch((err) => {
          vm.isListLoading = false;
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.isListLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: formName,
        isListError: false,
      });
    },
  },
};
</script>
