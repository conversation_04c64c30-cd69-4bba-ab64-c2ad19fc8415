<template>
  <div>
    <v-row justify="center" class="mt-4 mb-4">
      <div class="d-flex justify-center mb-8">
        <span class="font-weight-bold text-h6 pr-4 pt-2">
          Import action type
        </span>
        <span>
          <v-btn-toggle
            v-model="selectedImportType"
            rounded="lg"
            mandatory
            base-color="secondary"
            color="primary"
          >
            <v-btn disabled>New add</v-btn>
            <v-btn>Update</v-btn>
          </v-btn-toggle>
        </span>
      </div>
      <ImportAddEmployeeData
        v-if="selectedImportType === 0"
        @close-modal="onCloseModal"
      ></ImportAddEmployeeData>
      <ImportUpdateEmployeeData
        v-else
        @close-modal="onCloseModal"
      ></ImportUpdateEmployeeData>
    </v-row>
  </div>
</template>
<script>
import ImportAddEmployeeData from "@/views/my-team/team-summary/employee-data-import/ImportAddEmployeeData.vue";
import ImportUpdateEmployeeData from "@/views/my-team/team-summary/employee-data-import/ImportUpdateEmployeeData.vue";
export default {
  name: "ImportEmployeeData",
  components: {
    ImportAddEmployeeData,
    ImportUpdateEmployeeData,
  },
  data: () => ({
    selectedImportType: 1,
  }),
  methods: {
    onCloseModal() {
      this.$emit("close-modal");
    },
  },
};
</script>
