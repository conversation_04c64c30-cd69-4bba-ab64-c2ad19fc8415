<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
      >
        <template #topBarContent>
          <v-row
            v-if="originalList.length > 0 && !showAddEditForm && !showViewForm"
            justify="center"
          >
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="interview-round-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList('Interview Round error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="Introducing our Interview Round Management  – a centralized tool for streamlining interview rounds within your organization's hiring process. Recruiters can easily configure new rounds or update existing ones with  rounds, max score, passing score, and description."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="With secure access permissions tied to specific actions, only authorized personnel can add, edit, view, or delete interview rounds, ensuring data integrity and confidentiality. This module provides a clear overview of each round, empowering recruiters to make informed decisions and collaborate efficiently to refine the interview process for better outcomes."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 secondary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span class="primary">Add New</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      <span>Reset Filter/Search</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      variant="flat"
                      rounded="lg"
                      class="mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <div
                v-if="!isSmallTable"
                class="d-flex align-center my-3 flex-wrap"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <div class="Skills">
                  <CustomSelect
                    :items="skillsList"
                    item-title="Skill_Name"
                    item-value="Skill_Name"
                    label="Skill"
                    clearable
                    density="compact"
                    class="align-self-center"
                    variant="solo"
                    max-width="500px"
                    :isAutoComplete="true"
                    :itemSelected="selectedSkill"
                    @selected-item="onSelectSkill"
                  />
                </div>
                <v-btn
                  v-if="formAccess.add"
                  prepend-icon="fas fa-plus"
                  variant="elevated"
                  class="mx-1 secondary"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="openAddForm"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  <span class="primary">Add New</span>
                </v-btn>
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                  ><v-icon>fas fa-redo-alt</v-icon></v-btn
                >
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  class="mb-12"
                  :cols="isSmallTable && windowWidth >= 1264 ? 6 : 12"
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView
                            ? 'v-data-table__mobile-table-row ma-0 mt-2'
                            : ''
                        "
                      >
                        <td id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Interview Round
                          </div>
                          <section class="d-flex align-center">
                            <div class="d-flex align-center">
                              <div
                                v-if="
                                  isSmallTable &&
                                  windowWidth > 1264 &&
                                  selectedItem &&
                                  selectedItem.Round_Id === item.Round_Id
                                "
                                class="data-table-side-border"
                              ></div>
                            </div>
                            <v-tooltip
                              :text="item.Round_Name"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-body-2 font-weight-medium text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Round_Name.length > 50 ? props : ''
                                  "
                                >
                                  {{ item.Round_Name }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td v-if="!isSmallTable" id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Max Score Per Skill
                          </div>
                          <section class="text-body-2 text-primary">
                            {{
                              item.Max_Score_Per_Skill
                                ? item.Max_Score_Per_Skill
                                : 0
                            }}
                          </section>
                        </td>
                        <td id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Passing Score
                          </div>
                          <section class="text-body-2 text-primary">
                            {{ item.Passing_Score ? item.Passing_Score : 0 }} /
                            {{ item.Total_Score ? item.Total_Score : 0 }}
                          </section>
                        </td>
                        <td v-if="!isSmallTable" id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Min Panel Members
                          </div>
                          <section class="text-body-2 text-primary">
                            {{
                              item.Minimum_Panel_Members
                                ? item.Minimum_Panel_Members
                                : 0
                            }}
                          </section>
                        </td>
                        <td v-if="!isSmallTable" id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Max Panel Members
                          </div>
                          <section class="text-body-2 text-primary">
                            {{
                              item.Maximum_Panel_members
                                ? item.Maximum_Panel_members
                                : 0
                            }}
                          </section>
                        </td>
                        <td v-if="!isSmallTable" id="mobile-view-td">
                          <div id="mobile-header" class="font-weight-bold">
                            Max Candidates
                          </div>
                          <section class="text-body-2 text-primary">
                            {{
                              item.Maximum_Candidates
                                ? item.Maximum_Candidates
                                : 0
                            }}
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          class="text-body-2 text-end"
                          style="width: 150px"
                        >
                          <ActionMenu
                            @selected-action="onActions($event, item)"
                            :actions="['Edit', 'Delete']"
                            :access-rights="
                              item.Check_FeedBack === 'Yes'
                                ? { ...formAccess, update: 0, delete: 0 }
                                : item.Check_FeedBack === 'No' &&
                                  item.isAssociated
                                ? { ...formAccess, delete: 0 }
                                : formAccess
                            "
                            :disableActionButtons="
                              item.Check_FeedBack === 'Yes'
                                ? ['Edit', 'Delete']
                                : item.Check_FeedBack === 'No' &&
                                  item.isAssociated
                                ? ['Delete']
                                : []
                            "
                            iconColor="grey"
                            tooltipMessage="`You cannot update this interview round as it's already scheduled in
            interview.`"
                          ></ActionMenu>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
                <v-col
                  :cols="originalList.length === 0 ? 12 : 6"
                  v-if="isSmallTable && windowWidth >= 1264"
                >
                  <AddEditInterviewRounds
                    v-if="showAddEditForm"
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @form-updated="
                      refetchList('Interview round was added/updated')
                    "
                  ></AddEditInterviewRounds>
                  <ViewInterviewRounds
                    v-else
                    :selectedItem="selectedItem"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  ></ViewInterviewRounds>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <v-dialog
      v-if="openFormInModal"
      :model-value="openFormInModal"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditInterviewRounds
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :formAccess="formAccess"
        @close-form="closeAllForms()"
        @form-updated="refetchList('Interview round was added/updated')"
      ></AddEditInterviewRounds>
      <ViewInterviewRounds
        v-else
        :selectedItem="selectedItem"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      ></ViewInterviewRounds>
    </v-dialog>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteRecord()"
    ></AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewInterviewRounds = defineAsyncComponent(() =>
  import("./ViewInterviewRounds.vue")
);
const AddEditInterviewRounds = defineAsyncComponent(() =>
  import("./AddEditInterviewRounds.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
// Queries
import {
  LIST_INTERVIEW_ROUNDS,
  DELETE_INTERVIEW_ROUNDS,
} from "@/graphql/recruitment/interviewRoundMasterQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { GET_SKILL_LIST_FOR_INTERVIEW } from "@/graphql/recruitment/interviewRoundMasterQueries.js";

export default {
  name: "InterviewRoundMaster",
  components: {
    EmployeeDefaultFilterMenu,
    AddEditInterviewRounds,
    ViewInterviewRounds,
    NotesCard,
    CustomSelect,
    ActionMenu,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    listLoading: false,
    itemList: [],
    originalList: [],
    selectedSkill: null,
    skillsList: [],
    isErrorInList: false,
    errorContent: "",
    openWarningModal: false,
    openMoreMenu: false,
    // add/update
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
  }),
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("182");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    landedFormName() {
      let fAccess = this.accessRights("182");
      if (fAccess && fAccess.customFormName) {
        return fAccess.customFormName;
      } else return "Timesheets";
    },
    mainTabs() {
      if (this.formAccess) {
        return [this.landedFormName];
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    isSmallTable() {
      return this.showViewForm || this.showAddEditForm;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Interview Round",
            align: "start",
            key: "Round_Name",
          },
          {
            title: "Passing Score",
            key: "Passing_Score",
          },
        ];
      } else {
        return [
          {
            title: "Interview Round",
            align: "start",
            key: "Round_Name",
          },
          {
            title: "Max Score Per Skill",
            key: "Max_Score_Per_Skill",
          },
          {
            title: "Passing Score",
            key: "Passing_Score",
          },
          {
            title: "Min Panel Members",
            key: "Minimum_Panel_Members",
          },
          {
            title: "Max Panel Members",
            key: "Maximum_Panel_members",
          },
          {
            title: "Max Candidates",
            key: "Maximum_Candidates",
          },
          {
            title: "Action",
            key: "action",
            align: "end",
            sortable: false,
          },
        ];
      }
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText =
          "There are no interview rounds for the selected filters/searches.";
      }
      return msgText;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    openFormInModal() {
      if (this.isSmallTable && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
    this.fetchSkills();
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("Interview rounds Error:", err);
    let msg =
      "Something went wrong while loading the interview round. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        this.onDelete(item);
      } else {
        this.onEdit(item);
      }
    },

    onEdit(item) {
      this.selectedItem = item;
      this.openEditForm();
    },

    onDelete(item) {
      mixpanel.track("Interview round Delete Triggered");
      this.openWarningModal = true;
      this.selectedItem = item;
    },

    // function close the warning modal
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedItem = null;
    },

    onDeleteRecord() {
      let vm = this;
      vm.isLoading = true;
      const { Round_Id } = this.selectedItem;
      vm.$apollo
        .mutate({
          mutation: DELETE_INTERVIEW_ROUNDS,
          variables: {
            roundId: Round_Id,
            employeeId: vm.loginEmployeeId,
          },
          client: "apolloClientA",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Interview round deleted successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.refetchList("Interview round deleted");
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      mixpanel.track("Interview-round-delete-error");
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "interview round",
        isListError: false,
      });
      this.openWarningModal = false;
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          let newObj = {
            Round_Name: item.Round_Name,
            Max_Score_Per_Skill: item.Max_Score_Per_Skill,
            Passing_Score: item.Passing_Score,
            Minimum_Panel_Members: item.Minimum_Panel_Members,
            Maximum_Panel_members: item.Maximum_Panel_members,
            Maximum_Candidates: item.Maximum_Candidates,
          };
          return Object.keys(newObj).some((k) => {
            if (
              item[k] &&
              item[k].toString().toLowerCase().includes(searchValue)
            ) {
              return true; // Include if search value is found in the current key's value
            } else {
              return false;
            }
          });
        });
        this.itemList = searchItems;
      }
    },
    onSelectSkill(skill) {
      this.selectedSkill = skill;
      this.ApplyFilter(skill);
    },
    // Retreiving Skills Api
    fetchSkills() {
      let vm = this;
      vm.$apollo
        .query({
          query: GET_SKILL_LIST_FOR_INTERVIEW,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getSkillListForInterview &&
            response.data.getSkillListForInterview.skillResult &&
            !response.data.getSkillListForInterview.errorCode
          ) {
            vm.skillsList = response.data.getSkillListForInterview.skillResult;
          } else {
            vm.handleSkillsError();
          }
        })
        .catch((err) => {
          vm.handleSkillsError(err);
        });
    },
    handleSkillsError(err = "") {
      mixpanel.track("Skills error in list API");
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "skills",
        isListError: false,
      });
    },
    //Filter
    ApplyFilter(selectedSkill) {
      if (!selectedSkill) {
        this.resetFilter();
        return;
      }
      const skillName = selectedSkill;
      this.itemList = this.originalList.filter((item) => {
        if (item.SkillsCategory && item.SkillsCategory.length) {
          const parsedSkillsCategory = JSON.parse(item.SkillsCategory);
          return parsedSkillsCategory.some((category) =>
            category.Skills.some((skill) => skill.Skill_Name === skillName)
          );
        }
        return false;
      });
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = this.originalList;
    },

    openEditForm() {
      mixpanel.track("Interview round edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item) {
      mixpanel.track("Interview round view form opened");
      this.selectedItem = item;
      this.showViewForm = true;
    },

    openAddForm() {
      mixpanel.track("Interview round add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      mixpanel.track("Interview round all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.openWarningModal = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_INTERVIEW_ROUNDS,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            searchString: "",
            isDropDownCall: 0,
            employeeId: vm.loginEmployeeId,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listRounds &&
            !response.data.listRounds.errorCode
          ) {
            vm.itemList = response.data.listRounds.roundsDetails;
            vm.originalList = response.data.listRounds.roundsDetails;
            vm.listLoading = false;
            vm.onApplySearch();
            vm.selectedSkill = null;
            mixpanel.track("Interview round list retrieved");
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      mixpanel.track("Interview round error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "interview rounds",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },

    onMoreAction(actionType) {
      if (actionType === "Export") {
        mixpanel.track("Interview-rounds-export-click");
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportHeaders = [];
      for (let header of this.tableHeaders) {
        if (header.key != "action") {
          exportHeaders.push({
            header: header.title,
            key:
              header.key === "Passing_Score"
                ? "passingScoreWithTotal"
                : header.key,
          });
        }
      }
      let additionalHeaders = [
        {
          header: "Job Category",
          key: "jobCategory",
        },
        {
          header: "Skills",
          key: "skills",
        },
        {
          header: "Skill Category",
          key: "skillCategory",
        },
        {
          header: "Questions",
          key: "questions",
        },
        {
          header: "Description",
          key: "Description",
        },
      ];
      exportHeaders = exportHeaders.concat(additionalHeaders);

      // Map through itemList and extract Skill_Category and Skill_Name
      let exportList = this.itemList.map((item) => {
        let skillsData = [];
        let questionsData = [];
        let jobCategories = [];
        let skillCategories = [];
        let skillNames = [];

        if (item.SkillsCategory && item.SkillsCategory.length) {
          const parsedSkillsCategory = JSON.parse(item?.SkillsCategory);
          if (parsedSkillsCategory && parsedSkillsCategory.length) {
            parsedSkillsCategory.forEach((category) => {
              if (category.Skill_Category) {
                jobCategories.push(category.Skill_Category);
              }
              if (category.Skill_Category) {
                skillCategories.push(category.Skill_Category); // Extract Skill Category
              }
              category.Skills.forEach((skill) => {
                if (skill.Skill_Name) {
                  skillsData.push(skill.Skill_Name); // Extract Skill Name
                }
                if (skill.Questions) {
                  questionsData.push(skill.Questions);
                }
              });
            });
          }
        }

        return {
          ...item,
          passingScoreWithTotal: item.Passing_Score + "/" + item.Total_Score,
          jobCategory: jobCategories.join("\n"),
          skills: skillsData.join("\n"),
          questions: questionsData.join("\n"),
          skillCategory: skillCategories.join("\n"), // Added Skill Category values
          skillName: skillNames.join("\n"), // Added Skill Name values
        };
      });

      let exportOptions = {
        fileExportData: exportList,
        fileName: "Interview Rounds",
        sheetName: "Interview Rounds",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
      mixpanel.track("Interview-rounds-exported");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.interview-round-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
.Skills {
  min-width: 150px;
  margin-right: 20px;
  padding-top: 25px;
}
@media screen and (max-width: 805px) {
  .interview-round-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
