<template>
  <div>
    <v-container fluid class="logger-container">
      <div v-if="formAccess && formAccess.view">
        <div v-if="isLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          />
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            />
          </div>
        </div>

        <v-row v-else>
          <v-col
            md="12"
            cols="12"
            xs="12"
            sm="12"
            class="d-flex mr-2 align-center justify-end"
            style="flex-wrap: wrap"
          >
            <div
              class="d-flex mr-2 align-center"
              :class="{
                'flex-column': isMobileView,
                'justify-center': windowWidth < 1264,
                'justify-end': windowWidth >= 1264,
              }"
            >
              <div>
                <v-menu
                  v-model="activityDate"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="activityDate"
                      v-model="formatedActivityDate"
                      prepend-inner-icon="fas fa-calendar"
                      :label="$t('dataLossPrevention.date')"
                      readonly
                      v-bind="props"
                      variant="solo"
                      class="mr-3 mt-5"
                      density="compact"
                      style="min-width: 150px"
                    />
                  </template>
                  <v-date-picker
                    v-model="selectedDate"
                    :max="maxDate"
                    :min="minDate"
                  />
                </v-menu>
              </div>
              <div class="mr-3">
                <CustomSelect
                  density="compact"
                  :label="$t('dataLossPrevention.workSchedule')"
                  :items="workScheduleList"
                  v-model="workSchedule"
                  item-title="WorkSchedule_Name"
                  item-value="WorkSchedule_Id"
                  sub-text="Time_Zone"
                  :is-loading="workScheduleLoder"
                  min-width="150px"
                  :item-selected="workSchedule"
                  hide-details
                  :readonly="true"
                />
              </div>
              <div class="mr-3 mt-5">
                <CustomSelect
                  density="compact"
                  variant="solo"
                  :label="$t('dataLossPrevention.timezone')"
                  :items="[]"
                  v-model="employeeTimeZone"
                  min-width="150px"
                  :item-selected="employeeTimeZone"
                  :readonly="true"
                  @selected-item="employeeTimeZone"
                />
              </div>
              <v-btn rounded="lg" variant="text" @click="fetchEmployeeList()">
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    rounded="lg"
                    class="ml-n1 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action"
                    :disabled="appUrlData.length == 0"
                    @click="onMoreAction(action)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            hover: isHovering,
                          }"
                        >
                          <v-tooltip :text="action.message">
                            <template v-slot:activator="{ props }">
                              <div v-bind="action.message ? props : ''">
                                <v-icon size="15" class="pr-2">{{
                                  action.icon
                                }}</v-icon>
                                {{ action.key }}
                              </div>
                            </template>
                          </v-tooltip>
                        </v-list-item-title>
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </v-col>

          <v-col cols="12">
            <EmployeeTrackingDetails
              :formId="formId"
              :selectedEmployeeId="loginEmployeeId"
              listAppsUrlsType="apps"
              source="myTeamActivity"
              listType="logger"
              :isFromEmployeeLogin="isFromEmployeeLogin"
              :timezone="employeeTimeZone"
              :selectedDate="selectedDate"
              :selectedWorkSchedule="workSchedule"
              @app-url-data="appUrlActivityData($event)"
            />
          </v-col>
        </v-row>
      </div>
      <AppAccessDenied v-else />
    </v-container>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
const EmployeeTrackingDetails = defineAsyncComponent(() =>
  import("./EmployeeTrackingDetails.vue")
);
import { convertUTCToLocal } from "@/helper";
import { GET_EMPLOYEE_WORK_SCHEDULE_DETAILS } from "@/graphql/productivity-monitoring/activityDashboardQueries";
import { LIST_WORK_SCHEDULE } from "@/graphql/productivity-monitoring/activityDashboardQueries";

export default {
  name: "MyKeyLogs",
  components: {
    EmployeeTrackingDetails,
    CustomSelect,
  },
  mixins: [FileExportMixin],
  data: () => ({
    workScheduleLoder: false,
    openMoreMenu: false,
    isLoading: false,
    selectedDate: null,
    activityDate: false,
    formatedActivityDate: "",
    workSchedule: null,
    workScheduleList: [],
    appUrlData: [],
    isFromEmployeeLogin: false,
  }),
  props: {
    formAccess: {
      type: Object,
      required: true,
    },
    searchData: {
      type: String,
      default: "",
    },
    formId: {
      type: Number,
      required: true,
    },
  },
  emits: ["my-key-loader"],
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    moreActions() {
      return [
        {
          key: this.$t("dataLossPrevention.export"),
          icon: "fas fa-file-export",
        },
      ];
    },
    minDate() {
      return moment()
        .subtract(2, "months")
        .startOf("month")
        .format("YYYY-MM-DD");
    },
    maxDate() {
      return moment().format("YYYY-MM-DD");
    },
    allWorkScheduleOption() {
      return {
        WorkSchedule_Name: this.$t("dataLossPrevention.all"),
        WorkSchedule_Id: 0,
        Time_Zone: "",
      };
    },
  },
  watch: {
    isLoading() {
      this.$emit("my-key-loader", this.isLoading);
    },
    selectedDate(val) {
      if (val) {
        this.activityDate = false;
        let dateValue = this.formatDate(val);
        this.formatedActivityDate = dateValue;
      }
    },
  },
  created() {
    this.fetchEmployeeWorkDetails();
    this.fetchWorkScheduleList();
  },
  mounted() {
    this.selectedDate = new Date();
  },
  methods: {
    convertUTCToLocal,
    fetchEmployeeList() {
      this.isFromEmployeeLogin = true;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      setTimeout(() => {
        this.isFromEmployeeLogin = false;
      }, 100);
    },

    onMoreAction(action) {
      this.openMoreMenu = false;
      if (action.key == this.$t("dataLossPrevention.export")) {
        this.exportAppsURLs();
      }
    },
    appUrlActivityData(data) {
      if (!data) return;
      else this.appUrlData = data;
    },
    presentWithoutSecs(datetimeStr) {
      if (!datetimeStr) return "";
      const [date, time] = datetimeStr.split(" ");
      const [hours, minutes] = time.split(":");
      return `${date} ${hours}:${minutes}`;
    },
    exportAppsURLs() {
      let exportData = JSON.parse(JSON.stringify(this.appUrlData));
      exportData = exportData.map((el) => ({
        ...el,
        activityStartDateTime: el.activityStartDateTime
          ? this.presentWithoutSecs(
              this.convertUTCToLocal(el.activityStartDateTime)
            )
          : "",
        activityEndDateTime: el.activityEndDateTime
          ? this.presentWithoutSecs(
              this.convertUTCToLocal(el.activityEndDateTime)
            )
          : "",
      }));
      let exportHeaders = [
        {
          header: this.$t("dataLossPrevention.applicationName"),
          key: "Application_Name",
        },
        {
          header: this.$t("dataLossPrevention.applicationTitle"),
          key: "App_Title",
        },
        { header: this.$t("dataLossPrevention.keyLogs"), key: "keyStrokes" },
        {
          header: this.$t("dataLossPrevention.activityStartTime"),
          key: "activityStartDateTime",
        },
        {
          header: this.$t("dataLossPrevention.activityEndTime"),
          key: "activityEndDateTime",
        },
      ];
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.$t("dataLossPrevention.keyLogs"),
        sheetName: this.$t("dataLossPrevention.keyLogs"),
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    fetchEmployeeWorkDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_EMPLOYEE_WORK_SCHEDULE_DETAILS,
          client: "apolloClientK",
          variables: { employeeId: vm.loginEmployeeId },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getEmployeeWorkScheduleDetails &&
            response.data.getEmployeeWorkScheduleDetails
              .employeeWorkScheduleDetails
          ) {
            vm.employeeTimeZone =
              response.data.getEmployeeWorkScheduleDetails.employeeWorkScheduleDetails.timeZone;
            vm.workSchedule =
              response.data.getEmployeeWorkScheduleDetails.employeeWorkScheduleDetails.workScheduleId;
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleWorkDetailsError(err);
        });
    },
    handleWorkDetailsError(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: this.$t("dataLossPrevention.keyLogs"),
        isListError: false,
      });
    },
    fetchWorkScheduleList() {
      let vm = this;
      vm.workScheduleLoder = true;
      vm.$apollo
        .query({
          query: LIST_WORK_SCHEDULE,
          client: "apolloClientC",
          variables: {
            formName: "productivitymonitoring",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          const { errorCode, workSchedule } = response.data.listWorkSchedule;
          if (!errorCode && workSchedule) {
            let WSList = [vm.allWorkScheduleOption];
            vm.workScheduleList = WSList.concat(workSchedule);
            vm.workScheduleLoder = false;
          } else {
            vm.workScheduleLoder = false;
          }
        })
        .catch((err) => {
          vm.workScheduleLoder = false;
          vm.handleWorkSchduleError(err);
        });
    },
    handleWorkSchduleError(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: this.$t("dataLossPrevention.keyLogs"),
        isListError: false,
      });
    },
  },
};
</script>

<style scoped></style>
