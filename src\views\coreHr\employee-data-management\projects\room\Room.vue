<template>
  <div>
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      @button-click="refetchData()"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="backupMainList && backupMainList.length === 0 && !showEditForm"
      key="no-results-screen"
    >
      <template #contentSlot>
        <div style="max-width: 80%" class="mx-auto">
          <v-row
            v-if="!isLoading"
            style="background: white"
            class="rounded-lg pa-5 mb-4"
            :class="isMobileView ? 'mt-n16' : ''"
          >
            <v-col cols="12">
              <NotesCard
                :notes="`The ${roomCustomizedFormName} form provides a centralized platform to manage all ${roomCustomizedFormName.toLowerCase()}-related information, ensuring that ${roomCustomizedFormName.toLowerCase()} assignments and usage are organized and efficient.`"
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
              <NotesCard
                :notes="`By adding and maintaining accurate room records, you enable seamless ${roomCustomizedFormName.toLowerCase()} allocation across various forms, such as Timesheets, improving resource planning and utilization.`"
                backgroundColor="transparent"
                class="mb-2"
              ></NotesCard>
              <NotesCard
                :notes="`Easily track and update ${roomCustomizedFormName.toLowerCase()} details to ensure that all associated records reflect the most current information, facilitating smoother operations and coordination.`"
                backgroundColor="transparent"
                class="mb-2"
              ></NotesCard>
            </v-col>
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                v-if="formAccess.add"
                variant="elevated"
                class="ml-4 mt-1 primary"
                rounded="lg"
                :size="this.isMobileView ? 'small' : 'default'"
                @click="onAdd()"
              >
                <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                Add
              </v-btn>
              <v-btn
                rounded="lg"
                color="transparent"
                variant="flat"
                class="mt-1"
                :size="this.isMobileView ? 'small' : 'default'"
                @click="refetchData()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else class="pt-4">
      <v-row>
        <v-col :cols="isAddViewEditFormOpened && windowWidth >= 1264 ? 5 : 12">
          <ListRooms
            :items="mainList"
            :backupMainList="backupMainList"
            :is-small-table="isAddViewEditFormOpened"
            :formAccess="formAccess"
            @open-view-form="onOpenViewForm($event)"
            @refetch-data="refetchData()"
            @open-add-form="onAdd()"
            @delete="deleteRecord($event)"
            @reset-search-filter="resetSearchFilter()"
            @open-edit-form="openEditForm($event)"
          ></ListRooms>
        </v-col>
        <v-col
          v-if="isAddViewEditFormOpened && windowWidth >= 1264"
          :cols="backupMainList && backupMainList.length === 0 ? 12 : 7"
        >
          <ViewRoom
            v-if="showViewForm"
            @open-edit-form="openEditForm()"
            @close-form="closeAllForms"
            :selectedItem="selectedItem"
            :formAccess="formAccess"
          ></ViewRoom>
          <AddEditRoom
            v-else
            :selectedItem="selectedItem"
            :isEdit="isEdit"
            :formAccess="formAccess"
            @close-form="closeAllForms"
            @refetch-data="refetchData()"
          >
          </AddEditRoom>
        </v-col>
      </v-row>
    </div>

    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 primary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>

    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <ViewRoom
        v-if="showViewForm"
        @open-edit-form="openEditForm()"
        @close-form="closeAllForms()"
        :selectedItem="selectedItem"
        :formAccess="formAccess"
      />
      <AddEditRoom
        v-if="showEditForm"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        @close-form="closeAllForms()"
        @refetch-data="refetchData()"
        :formAccess="formAccess"
      />
    </v-dialog>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
//Vue
import { defineAsyncComponent } from "vue";
//Components
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
//Pages
const AddEditRoom = defineAsyncComponent(() => import("./AddEditRoom.vue"));
import ViewRoom from "./ViewRoom.vue";
import ListRooms from "./ListRooms.vue";
// Queries
import { LIST_ROOMS, DELETE_ROOM } from "@/graphql/corehr/projectsQueries.js";

export default {
  name: "RoomMain",
  components: {
    ViewRoom,
    NotesCard,
    ListRooms,
    AddEditRoom,
  },
  props: {
    mainList: {
      type: Array,
      default: () => [],
      required: true,
    },
    backupMainList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  emits: ["reset-search-filter", "assign-data"],
  data() {
    return {
      isLoading: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      isEdit: false,
      showViewForm: false,
      selectedItem: {},
      showEditForm: false,
      validationMessages: [],
      showValidationAlert: false,
      mainData: [],
      backupMainData: [],
    };
  },
  computed: {
    formAccess() {
      let roomFormAccess = this.accessRights("280");
      if (
        roomFormAccess &&
        roomFormAccess.accessRights &&
        roomFormAccess.accessRights["view"]
      ) {
        return roomFormAccess.accessRights;
      } else {
        return false;
      }
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    roomCustomizedFormName() {
      if (this.formAccess && this.formAccess.customFormName) {
        return this.formAccess.customFormName;
      }
      return "Room";
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isAddViewEditFormOpened() {
      return this.showViewForm || this.showEditForm;
    },
    openFormInModal() {
      if (this.isAddViewEditFormOpened && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
  },

  mounted() {
    this.fetchData();
  },
  methods: {
    resetSearchFilter() {
      this.$emit("reset-search-filter");
    },
    openEditForm(item = null) {
      if (item) {
        this.selectedItem = item;
      }
      this.isEdit = true;
      this.showViewForm = false;
      this.showEditForm = true;
    },
    closeAllForms() {
      this.isEdit = false;
      this.showEditForm = false;
      this.showViewForm = false;
    },
    onAdd() {
      this.selectedItem = null;
      this.showEditForm = true;
      this.isEdit = false;
    },
    refetchData() {
      this.errorContent = "";
      this.isErrorInList = false;
      this.resetSearchFilter();
      this.closeAllForms();
      this.fetchData();
    },
    onOpenViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    fetchData() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_ROOMS,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listRooms) {
            let mainData = response.data.listRooms.rooms;
            vm.mainData = mainData;
            vm.backupMainData = mainData;
            this.$emit("assign-data", [vm.mainData, vm.backupMainData]);
            vm.listLoading = false;
          } else {
            vm.handleListError((err = ""), "room");
          }
        })
        .catch((err) => {
          vm.handleListError(err, "room");
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    //function to delete  record
    deleteRecord(deleteItem) {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_ROOM,
            variables: {
              Room_Id: deleteItem.Room_Id ? parseInt(deleteItem.Room_Id) : 0,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Room deleted successfully.",
            };
            vm.showAlert(snackbarData);
            vm.refetchData();
          })
          .catch((error) => {
            vm.handleDeleteError(error);
          });
      } catch (error) {
        vm.handleDeleteError(error);
      }
    },
    handleDeleteError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: "room",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
