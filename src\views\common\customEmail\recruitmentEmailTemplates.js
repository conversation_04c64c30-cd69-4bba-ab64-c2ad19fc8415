export const recruitmentEmailTemplates = {
  candidateCalendarLinkInterview: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Hi <strong>[Applicant's Name]</strong>,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. I got your profile from <strong>[Source]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>Please let me know your preferred schedule through the link below: </p>
    <a href="[Calendar Link]">Calendar Link</a>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateCalendarLinkInterviewNoSource: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Hi <strong>[Applicant's Name]</strong>,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>Please let me know your preferred schedule through the link below: </p>
    <a href="[Calendar Link]">Calendar Link</a>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateCalendarLinkInterviewBulk: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Hi Candidate,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. I got your profile from <strong>[Source]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>Please let me know your preferred schedule through the link below: </p>
    <a href="[Calendar Link]">Calendar Link</a>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateCalendarLinkInterviewBulkNoSource: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Hi Candidate,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>Please let me know your preferred schedule through the link below: </p>
    <a href="[Calendar Link]">Calendar Link</a>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateOnlineInterview: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Hi <strong>[Applicant's Name]</strong>,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. I got your profile from <strong>[Source]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>We want to invite you for an online interview on <strong>[Interview Schedule] [Start Time] to [End Time] (24hr Format)</strong></p>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateOnlineInterviewNoSource: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Hi <strong>[Applicant's Name]</strong>,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>We want to invite you for an online interview on <strong>[Interview Schedule] [Start Time] to [End Time] (24hr Format)</strong></p>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateOnlineInterviewWithMeeting: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Hi <strong>[Applicant's Name]</strong>,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. I got your profile from <strong>[Source]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>We want to invite you for an online interview on <strong>[Interview Schedule] [Start Time] to [End Time] (24hr Format)</strong></p>
    <p>Meeting Link: <a href="[Meeting Link]">Meeting Link</a></p>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateOnlineInterviewWithMeetingNoSource: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Hi <strong>[Applicant's Name]</strong>,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>We want to invite you for an online interview on <strong>[Interview Schedule] [Start Time] to [End Time] (24hr Format)</strong></p>
    <p>Meeting Link: <a href="[Meeting Link]">Meeting Link</a></p>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateOnlineInterviewBulk: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Dear Candidate,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. I got your profile from <strong>[Source]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>We want to invite you for an online interview on <strong>[Interview Schedule] [Start Time] to [End Time] (24hr Format)</strong></p>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateOnlineInterviewBulkNoSource: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Dear Candidate,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>We want to invite you for an online interview on <strong>[Interview Schedule] [Start Time] to [End Time] (24hr Format)</strong></p>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateOnlineInterviewBulkWithMeeting: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Dear Candidate,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. I got your profile from <strong>[Source]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>We want to invite you for an online interview on <strong>[Interview Schedule] [Start Time] to [End Time] (24hr Format)</strong></p>
    <p>Meeting Link: <a href="[Meeting Link]">Meeting Link</a></p>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateOnlineInterviewBulkWithMeetingNoSource: {
    subject: "Online Interview Invitation at [Company Name]",
    body: `<body>
    <p>Dear Candidate,</p>
    <p>This is <strong>[Name of Recruiter]</strong> from <strong>[Company Name]</strong>. We're currently looking for a <strong>[Position Title]</strong> and thought you might be a great fit.</p>
    <p>We want to invite you for an online interview on <strong>[Interview Schedule] [Start Time] to [End Time] (24hr Format)</strong></p>
    <p>Meeting Link: <a href="[Meeting Link]">Meeting Link</a></p>
    <p>Thank you!</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    </body>`,
  },
  candidateAssessment: {
    subject: "Assessment Invitation from [Company Name]",
    body: `<body>
        <p>Hi <strong>[Applicant's Name]</strong>,</p>
        <p>I'm glad to inform you that we have shortlisted you for the <strong>[Position Title]</strong> post.</p>
        <p>We would like to invite you for our online assessment on <strong>[Date]</strong> at your most convenient time of the day.</p>
        <p>An email invitation will be sent to your email with a subject "Assessment Center invitation from [Company Name]". Please check your spam folder if you haven't received it and let us know if you need further assistance.</p>
        <p>Hope to hear from you soon. </p>
        <p>Note: Kindly disregard this message if you have taken our assessment in the past 6 months.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  candidateAssessmentBulk: {
    subject: "Assessment Invitation from [Company Name]",
    body: `<body>
        <p>Dear Candidate,</p>
        <p>I'm glad to inform you that we have shortlisted you for the <strong>[Position Title]</strong> post.</p>
        <p>We would like to invite you for our online assessment on <strong>[Interview Schedule]</strong> at your most convenient time of the day.</p>
        <p>Please click the link below to confirm your assessment schedule.</p>
        <a href="[Calendar Link]">Assessment Confirmation Form</a>
        <p>*Please indicate <strong>[Name of Recruiter]</strong> as your Recruiter. Further instructions will be sent to your e-mail after confirmation is received.</p>
        <p>Kindly disregard this message if you have taken our assessment in the past 6 months.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  candidateFaceToFace: {
    subject: "Face-to-Face Interview Invitation from [Company Name]",
    body: `<body>
        <p>Hi <strong>[Applicant's Name]</strong>,</p>
        <p>Greetings from [Company Name]!</p>
        <p>As we move forward with your application for the <strong> [Position Title]</strong> position, we would like to invite you for a face-to-face interview on <strong>[Date] [Start Time] to [End Time] (24hr Format)</strong>.</p>
        <p>Location: <a href="[Venue Link]">[Venue]</a></p>
        <p>Contact Person: [Name of Recruiter]</p>
        <p>Reminders:</p>
        <p>1. Please bring a resume and a valid ID.</p>
        <p>2. Wear a smart casual attire. Please note that open-toed shoes are not allowed.</p>
        <p>3. Please be at the location at least 10 minutes before your schedule.</p>
        <p>We look forward to seeing you.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  candidateFaceToFaceWithVenue: {
    subject: "Face-to-Face Interview Invitation from [Company Name]",
    body: `<body>
        <p>Hi <strong>[Applicant's Name]</strong>,</p>
        <p>Greetings from [Company Name]!</p>
        <p>As we move forward with your application for the <strong> [Position Title]</strong> position, we would like to invite you for a face-to-face interview on <strong>[Date] [Start Time] to [End Time] (24hr Format)</strong>.</p>
        <p>Location:<strong> [Venue]</strong></p>
        <p>Contact Person:<strong> [Name of Recruiter]</strong></p>
        <p>Reminders:</p>
        <p>1. Please bring a resume and a valid ID.</p>
        <p>2. Wear a smart casual attire. Please note that open-toed shoes are not allowed.</p>
        <p>3. Please be at the location at least 10 minutes before your schedule.</p>
        <p>We look forward to seeing you.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  candidateFaceToFaceCalendarLink: {
    subject: "Face-to-Face Interview Invitation from [Company Name]",
    body: `<body>
        <p>Hi <strong>[Applicant's Name]</strong>,</p>
        <p>Greetings from [Company Name]!</p>
        <p>As we move forward with your application for the <strong> [Position Title]</strong> position, we would like to invite you for a face-to-face interview in <a href="[Venue Link]">[Venue]</a>.</p>
        <p>Please select a date and time that are convenient for you and ensure that you provide accurate contact information using this link: <a href="[Calendar Link]">Calendar Link</a></p>
        <p style="text-align: left">We look forward to seeing you.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  candidateFaceToFaceCalendarLinkWithVenue: {
    subject: "Face-to-Face Interview Invitation from [Company Name]",
    body: `<body>
        <p>Hi <strong>[Applicant's Name]</strong>,</p>
        <p>Greetings from [Company Name]!</p>
        <p>As we move forward with your application for the <strong> [Position Title]</strong> position, we would like to invite you for a face-to-face interview in <strong>[Venue]</strong>.</p>
        <p>Please select a date and time that are convenient for you and ensure that you provide accurate contact information using this link: <a href="[Calendar Link]">Calendar Link</a> </p>
        <p style="text-align: left">We look forward to seeing you.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  candidateFaceToFaceBulk: {
    subject: "Face-to-Face Interview Invitation from [Company Name]",
    body: `<body>
        <p>Dear Candidate,</p>
        <p>I'm glad to inform you that we have shortlisted you for the <strong>[Position Title]</strong> post.</p>
        <p>We have scheduled a interview session. Please check the details below</p>
        <p style="text-align: left">Location: <a href="[Venue Link]">[Venue]</a></p> 
        <p style="text-align: left">Date: <strong>[Interview Schedule]</strong></p>
        <p style="text-align: left">Time: <strong>[Start Time] to [End Time] (24hr Format)</strong></p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  candidateFaceToFaceBulkWithVenue: {
    subject: "Face-to-Face Interview Invitation from [Company Name]",
    body: `<body>
        <p>Dear Candidate,</p>
        <p>I'm glad to inform you that we have shortlisted you for the <strong>[Position Title]</strong> post.</p>
        <p>We have scheduled a interview session. Please check the details below</p>
        <p style="text-align: left">Venue: <strong>[Venue Link]</strong></p> 
        <p style="text-align: left">Date: <strong>[Interview Schedule]</strong></p>
        <p style="text-align: left">Time: <strong>[Start Time] to [End Time] (24hr Format)</strong></p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  rejectedNotHiredCandidate: {
    subject: "Application Status at [Company Name]",
    body: `<p>Dear <strong>[Applicant's Name]</strong>,</p>
    <p>Thank you for your application for the <strong>[Position Title]</strong> position at <strong>[Company Name]</strong>. We really appreciate your interest in joining our company, and we want to thank you for the time and energy you invested in your job application.</p>
    <p>We received a large number of job applications, and after carefully reviewing all of them, unfortunately, we have to inform you that this time we won’t be able to invite you to the next phase of our selection process. Though your skills and working experience are impressive, we have decided to move forward with a candidate whose working experience better meets our needs for this particular role.</p>
    <p>We hope you’ll keep us in mind and apply again in the future.</p>
    <p>We wish you every personal and professional success in your future endeavors.</p>
    <p>Once again, thank you for your interest in working with us.</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    `,
  },
  hiredCandidate: {
    subject: "Application Status at [Company Name] | [Position Title]",
    body: `<p>Dear <strong>[Applicant's Name]</strong>,</p>
    <p>Congratulations! You've successfully cleared the last round, and we are now moving forward with the final steps to formalize your offer.</p>
    <p>No further action is required from your end at this time.</p>
    <p>Best Regards,</p>
    <p>[Name of Recruiter]</p>
    <p>[Company Name]</p>
    <p>[Company Address 1]</p>
    <p>[Company Address 2]</p>
    `,
  },
  roundCancelledCandidate: {
    subject: "Interview Notification from [Company Name]",
    body: `<body>
        <p>Dear <strong>[Applicant's Name]<strong>,</p>
        <p>This is to notify you that the <strong>[Round Name]</strong> round for the position of <strong>[Position Title]</strong> has been cancelled.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  candidateAcknowledgement: {
    subject: "Acknowledgment of Your Job Application",
    body: `<body>
        <p>Dear [Applicant's Name],</p>
        <p>I hope this email finds you well.</p> 
        <p>We have received your recent application for the [Position Title] position at [Company Name]. We appreciate the time and effort you invested in submitting your application. Our team is currently reviewing all applications, and we will contact candidates who move forward in the selection process.</p>
        <p>Thank you for your interest in our company.</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
    </body>`,
  },
  candidateNoShow: {
    subject: "Interview Reschedule at [Company Name]",
    body: `<body>
        <p>Dear <strong>[Applicant's Name]<strong>,</p>
        <p>I hope this message finds you in good spirits and enjoying a fantastic day. I wanted to reach out and inform you that we missed your presence at the scheduled interview for the <strong>[Position Title]</strong> position. However, please know that we are still genuinely interested in your application and would like to explore the possibility of rescheduling the interview.</p>
        <p>We're looking forward to reconnecting with you soon.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  roundCancelledPanelMember: {
    subject: "Interview Notification from [Company Name]",
    body: `<body>
        <p>Dear Panel Member,</p>
        <p>This is to notify you that the <strong>[Round Name]</strong> round for the position of <strong>[Position Title]</strong> has been cancelled.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
    </body>`,
  },
  panelMemberRemove: {
    subject: "Update on Interview Round for [Position Title]",
    body: `<body>
        <p>Dear Panel Member,</p>
        <p>Regrettably, you will not be required for the upcoming interview round on [Date]. We appreciate your time and effort.</p>
        <p>Thank you!</p>
        <p>Best Regards,</p>
        <p>[Name of Recruiter]</p>
    </body>`,
  },
  panelOnlineInterview: {
    subject: "Online Interview: Panel Invitation for [Applicant's Name]",
    body: `<body>
        <p>Dear Panel Member, </p>
        <p>I’m reaching out to inform you that you have been selected as a panel member for the virtual interview of [Applicant's Name].</p>
        <p>Please be prepared to join the interview scheduled on <strong>[Interview Schedule]</strong> from <strong>[Start Time] to [End Time] (24hr Format)</strong>.</p>
        <p>Thank you for your participation.</p>
        <p>Best Regards,</p>
        <p>HR Administrator</p>
    </body>`,
  },
  panelOnlineInterviewWithMeeting: {
    subject: "Online Interview: Panel Invitation for [Applicant's Name]",
    body: `<body>
        <p>Dear Panel Member, </p>
        <p>I’m reaching out to inform you that you have been selected as a panel member for the virtual interview of [Applicant's Name].</p>
        <p>Please be prepared to join the interview scheduled on <strong>[Interview Schedule]</strong> from <strong>[Start Time] to [End Time] (24hr Format)</strong>.</p>
        <p>Meeting Link: <a href="[Meeting Link]">Meeting Link</a></p>
        <p>Thank you for your participation.</p>
        <p>Best Regards,</p>
        <p>HR Administrator</p>
    </body>`,
  },
  panelMemberFaceToFaceInterview: {
    subject: "Face-to-Face Interview: Panel Invitation for [Applicant's Name]",
    body: `<body>
        <p>Dear Panel Member,</p>
        <p>I’m reaching out to inform you that you have been selected as a panel member for the face-to-face interview of <strong>[Applicant's Name]</strong>.</p>
        <p>The interview is set to take place in <a href="[Venue Link]">[Venue]</a> at <strong>[Date] [Start Time] to [End Time] (24hr Format)</strong>. Please be prepared to join the interview at the scheduled time.</p>
        <p>Thank you for your participation.</p>
        <p>Best Regards,</p>
        <p>HR Administrator</p>
    </body>`,
  },
  panelMemberFaceToFaceInterviewWithVenue: {
    subject: "Interview Notification from [Company Name]",
    body: `<body>
        <p>Dear Panel Member,</p>
        <p>I’m reaching out to inform you that you have been selected as a panel member for the face-to-face interview of <strong>[Applicant's Name]</strong>.</p>
        <p>The interview is set to take place in <strong>[Venue]</strong> at <strong>[Date] [Start Time] to [End Time] (24hr Format)</strong>. Please be prepared to join the interview at the scheduled time.</p>
        <p>Thank you for your participation.</p>
        <p>Best Regards,</p>
        <p>HR Administrator</p>
    </body>`,
  },
  panelMemberAssessment: {
    subject: "Assessment Panel Invitation for [Applicant's Name]",
    body: `<body>
        <p>Dear Panel Member,</p>
        <p>We are excited to invite you to participate as a panel member for the online assessment of <strong>[Applicant's Name]</strong>, scheduled on <strong>[Date]</strong>.</p>
        <p>We request your availability during this time. Your insights will be valuable.</p>
        <p>Thank you for your participation.</p>
        <p>Best Regards,</p>
        <p>HR Administrator</p>
    </body>`,
  },
  panelCalendarLinkInterview: {
    subject: "Online Interview: Panel Invitation for [Applicant's Name]",
    body: `<body>
        <p>Dear Panel Member</strong>,</p>
        <p>I’m reaching out to inform you that you have been selected as a panel member for the virtual interview of <strong> [Applicant's Name]</strong>.</p>
        <p>Please be prepared to join the interview at the scheduled time.</p>
        <p>Thank you for your participation.</p>
        <p>Best Regards,</p>
        <p>HR Administration</p>
    </body>`,
  },
  candidateFinishToDraft: {
    subject:
      "Action Required: Additional Information Needed for Your Onboarding",
    body: `<body>
      <p>Dear [Applicant's Name],</p>
      <p>Thank you for your recent submission of your details as part of the onboarding. Upon review, we have found that some information is either missing or incorrect.</p>
      <p>To proceed with your application, we kindly ask you to provide the necessary details or correct the information previously submitted. </p>
      <p>Please log in to your candidate portal following the link <a href="[Url]">here</a> and pin <b>[Pin]</b> to complete the required fields at your earliest convenience.</p>
      <p>If you have any questions or need assistance, please do not hesitate to contact us.</p>
      <p>Best Regards,</p>
      <p>[Name of Recruiter]</p>
  </body>`,
  },
  rejectedCandidateOnboarded: {
    subject: "Self-Onboarding: Candidate verification failed",
    body: `<body  style='text-align: left'>
      <p>Dear [Applicant's Name],</p>
      <p>We regret to inform you that, upon thorough review, we were unable to verify some of the details you provided during the onboarding process. As a result, we are unable to confirm the accuracy of your information within our system. </p>
      <p>We understand that discrepancies can occur, and we are here to assist you in resolving any issues. If you have questions or need further guidance, please don't hesitate to reach out to our HR department. They will provide you with the necessary support to address and rectify any discrepancies.</p>
      <p>We apologize for any inconvenience this may cause and appreciate your understanding in this matter. Your accuracy and completeness of information are essential to ensure a successful onboarding process.</p>
      <p>Best Regards,</p>
      <p>[Name of Recruiter]</p>
      <p>[Company Name]</p>
      <p>[Company Address 1]</p>
      <p>[Company Address 2]</p>
    </body>`,
  },
  sunfishDeploymentEmail: {
    subject: "Deployment Notification for  [Applicant's Name]",
    body: `<body  style='text-align: left'>
      <p>Dear [Name of Recruiter],</p>
      <p>We are pleased to announce that your official starting date is [Start Time]. We look forward to seeing you!. </p>
      <p>To keep track of the deployment process, please refer to the details below. Once you arrive on-site, please let me know. </p>
      <p style="text-align: center; background: #002060; color: #ffffff"><strong>DEPLOYMENT DETAILS</strong></p></t></tr></thead>
      <p><b>Office Location</b>: [Location] </p>
      <p><b>Date of Join</b>: [Start Time]</p>
      <p><b>Reporting Time</b>: </p>
      <p><b>Attire</b>: </p>
      <p style="padding-left: 0.5em;"><strong>For Non-Supervisory Employees: </strong></p>
        <p style="padding-left: 1em;">Blouse/Polo with Sleeves and Collar</p>
        <p style="padding-left: 1em;">Black Slacks/Skirt</p>
        <p style="padding-left: 1em;">Black Shoes</p>
      <p style="padding-left: 0.5em;"><strong>For Supervisory Employees: </strong></p>
        <p style="padding-left: 1em;">Business/Corporate Attire </p>
      <p><b>Contact Person</b>: </p>
      <p><b>Note</b>: Please bring your valid id</p>
      <p>Kindly confirm the receipt of your email by replying to [Company Name] through email [Specialist Email]. You may also get in touch with them if you have some questions.</p>
      <p>Thank you</p>
    </body>`,
  },
  onboardingHiringTeamNotification: {
    subject: "New_Hire_[Applicant Id]:[Applicant's Name]",
    body: `<body  style='text-align: left'>
      <p>Dear Sir/Ma’am,</p>
      <p>In line with the new employee that will be deployed to your unit. Please orient them regarding the following items: </p>
      <p style="padding-left: 1em;">a. Employee Undertaking on Data Privacy Act</p>
      <p style="padding-left: 1em;">b. Employee Manual and Code of Conduct</p>
      <p>Once done, have the employee sign a hard copy of the attached documents and submit it to HROD. Last date of submission is on the 5th business day from start/hiring date. Failure to comply would mean escalation to the Group Head.</p>
      <p>On the other hand,</p>
        <p style="padding-left: 1em;">1. Request laptop for new hire to ICT and SMD.</p>
        <p style="padding-left: 1em;">2. Request access to systems and infrastructure e.g. internal email, VPN, internet, wifi, etc.</p>
        <p style="padding-left: 1em;">3. Teach the new hire on how to apply for medication of logs, overtime, etc.</p>
        <p style="padding-left: 1em;">4. Inform the new hire of the payroll cut-off and pay schedules</p>
      <p><strong>Id Number:</strong> [Applicant Id]</p>  
      <p><strong>Name of Employee:</strong> [Applicant's Name]</p>
      <p><strong>Date of Hire:</strong> [Date Of Join]</p>
      <p><strong>Position:</strong> [Position Title]</p>
      <p><strong>Company:</strong> [Org Name]</p>
      <p><strong>Group:</strong> [Group Name]</p>
      <p><strong>Region/Division:</strong> [Division Name]</p>
      <p><strong>Area/Department:</strong> [Department Name]</p>
      <p><strong>Section/Branch:</strong> [Branch Name]</p>
      <p><strong>Section/Branch Code:</strong> [Branch Code]</p>
      <p><strong>Immediate Superior/Area Head:</strong> [First Line Manager]</p>
      <p><strong>Direct Manager/Regional Head:</strong> [Second Line Manager]</p>  
    </body>`,
  },
  hiringForecastSettings: {
    subject: "Hiring Forecast Released – Open Until [Date]",
    body: `<body>
        <p>We are pleased to inform you that the hiring forecast has been released and is now available for your updates. This forecast will assist you in projecting staffing requirements for recruiters to source and hire replacements or new positions. You can update the forecast until <strong> [Date]</strong>.</p>
        <p>Please ensure your hiring needs are updated before the forecasting window closes.</p> 
        <p><a href="[Url]">Click here</a> to view the hiring forecast details.</p>
        <p>Thank you for your attention.</p>
        <p>Best Regards,</p>
        <p>[Applicant's Name]</p>
        <p>[Company Name]</p>
        <p>[Company Address 1]</p>
        <p>[Company Address 2]</p>
    </body>`,
  },
  TOReviewMPP: {
    subject: "[Title] - [Position Title]",
    body: `<body>
      <p>Dear [Name of Recruiter],</p>
      <p>I hope this email finds you well.</p>
      <p>We are pleased to inform you that your request is currently being reviewed. Below are the key details of your request:</p>
      <p>• <strong>Request Date: </strong>[Date]</p>
      <p>• <strong>Position and Designation( Division, Department, Section): </strong>[Position Title], [Division Name]</p>
      <p>• <strong>Change Requested: </strong>Add a new position under [Division Name]</p>
      <p>We’ll let you know as soon as a decision has been reached. Please feel free to reach out to Norielyn Follero or Cherry Anne Friginal if you have any further questions or need additional information.</p>
      <p>Thank you for your cooperation and understanding.</p>
    </body>`,
  },
  TOChangesAcceptMPP: {
    subject: "[Title] - [Position Title]",
    body: `<body>
      <p>Dear [Name of Recruiter],</p>
      <p>I hope this email finds you well.</p>
      <p>We are pleased to inform you that your request has been approved. Below are the key details of your request:</p>
      <p>• <strong>Request Date: </strong>[Date]</p>
      <p>• <strong>Position and Designation( Division, Department, Section): </strong>[Position Title], [Division Name]</p>
      <p>• <strong>Change Requested: </strong>Add a new position under [Division Name]</p>
      <p>• <strong>Additional Remarks: </strong>[Remark]</p>
      <p>Please feel free to reach out if you have any further questions or need additional information. You may also use this link to see a summary of your entire request <a href="[Job Post Url]">MPP Request Dashboard</a>.</p>
      <p>Thank you for your cooperation and understanding.</p>
    </body>`,
  },
  TOChangesRejectMPP: {
    subject: "[Title] - [Position Title]",
    body: `<body>
      <p>Dear [Name of Recruiter],</p>
      <p>I hope this email finds you well.</p>
      <p>We regret to inform you that your request has been disapproved. Below are the key details of your request:</p>
      <p>• <strong>Request Date: </strong>[Date]</p>
      <p>• <strong>Position and Designation( Division, Department, Section): </strong>[Position Title], [Division Name]</p>
      <p>• <strong>Change Requested: </strong>Add a new position under [Division Name]</p>
      <p>• <strong>Additional Remarks: </strong>[Remark]</p>
      <p>Please feel free to reach out if you have any further questions or need additional information. You may also use this link to see a summary of your entire request <a href="[Job Post Url]">MPP Request Dashboard</a>.</p>
      <p>Thank you for your cooperation and understanding.</p>
    </body>`,
  },
  InitiateBackgroundInvestigation: {
    subject: "From [Org Name]: - Background Investigation_[Applicant's Name]",
    body: `<body>
      <p>Dear [Applicant's Name],</p>
      <p>Part of our hiring process is to conduct an extensive background investigation on our shortlisted candidate; thus, we would like to request for you to sign the letter of authorization so that we can begin with the process. All of the data that we will be using are the ones you provided during your application.</p>
      <p>Please read the statements carefully. You may use the attached document to sign the letter of authorization.</p>
      <p>Rest assured that the investigation will be conducted in a very discreet manner.</p>
      <p>Thank you.</p>
      <p>[Name of Recruiter]</p>
      <p>[Designation]</p>
    </body>`,
  },
  BIFailed: {
    subject: "From [Org Name] | Application Status",
    body: `<body>
      <p>Good day, [Applicant's Name].</p>
      <p>I hope this letter finds you well.</p>
      <p>We would like to extend our gratitude for the time and effort you have dedicated throughout our application process. After a thorough and careful consideration of all aspects of your application, including the recent completion of the background investigation process, we regret to inform you that we are unable to proceed with your employment offer for the Fraud Management Officer position at this time.</p>
      <p>This decision was not easy and involved several difficult considerations. Please understand that our decision is based on specific criteria and results that are in alignment with our company's policies and the requirements of the position.</p>
      <p>Thank you once again for considering a career with PJ Lhuillier Group of Companies. We wish you all the best in your future endeavors and professional journey.</p>
      <p>Should you have any questions or require further clarification, please do not hesitate to contact us.</p>
    </body>`,
  },
  ArchiveApplication: {
    subject:
      "From [Company Name]: Archiving of Your Application [Applicant's Name]",
    body: `<body>
      <p>Dear [Applicant's Name],</p>
      <p>Thank you for taking the time to apply for the [Position Title] position at [Company Name]. After careful consideration, we have decided to move forward with other candidates whose qualifications more closely match the requirements for this position.</p>
      <p>Please note that we will keep your resume and application on file for any future opportunities that may be better suited to your skills and experience. Should a relevant opening arise, we will not hesitate to contact you.</p>
      <p>We appreciate your interest in [Company Name] and wish you the best of luck in your job search.</p>
      <p>Thank you once again for considering us for your next career move.</p>
      <p>Best regards,</p>
      <p>[Name of Recruiter]</p>
      <p>[Designation]</p>
    </body>`,
  },
  candidateWithdrawn: {
    subject: "Confirmation of Your Withdrawal Request – [Position Title]",
    body: `<body>
      <p>Dear [Applicant's Name],</p>
      <p>Thank you for informing us of your decision to withdraw from consideration for the [Position Title] role at [Company Name]. We understand and respect your choice, and we are grateful for the time and interest you have shown in our team.</p>
      <p>While we will not be moving forward together in this hiring process, we hope that you will consider [Company Name] again in the future. We would welcome the opportunity to reconnect for other roles that align with your career goals.</p>
      <p>Thank you once again, and we wish you every success in your journey ahead.</p>
      <span>Best Regards,</span>
      <span>[Name of Recruiter]</span>
      </body>`,
  },
  ShortlistCandidate: {
    subject: "You’ve Been Shortlisted for the [Position Title] Role",
    body: `<body>
      <p>Hi [Applicant's Name],</p>
      <p>Thank you for applying for the [Position Title] position at [Company Name].</p>
      <p>We’re pleased to let you know that you have been shortlisted for the next stage of our hiring process. Our team was impressed with your profile, and we’re excited to continue the conversation.</p>
      <p>We’ll be reaching out soon with more details about the next steps.</p>
      <span>Best Regards,</span>
      <span>[Name of Recruiter]</span>
      </body>`,
  },
  CandidatePortalAccess: {
    subject: "Welcome to the Candidate Experience Portal",
    body: `<body>
      <p>Hi [Applicant's Name],</p>
      <p>We’re excited to have you take the next step with us!</p>
      <p>To help you stay informed and engaged throughout your hiring journey, we’ve created a Candidate Experience Portal just for you. You can log in to view your application status, update your profile information, apply for new opportunities, and more.</p>
      <p>Login to your portal: [Candidate Portal URL]</p>
      <p>If you have any questions or need assistance, feel free to reach out to us at [Specialist Email].</p>
      <p>Looking forward to connecting with you!</p>
      <span>Best Regards,</span>
      <span>[Name of Recruiter]</span>
      <span>[Designation]</span>
      <span>[Company Name]</span>
      </body>`,
  },
};

//Replacement tags with value fields
export const replacementTags = {
  "[Applicant's Name]": "Candidate_Name",
  "[Applicant Id]": "Applicant_Id",
  "[Company Name]": "Company_Name",
  "[Name of Recruiter]": "Recruiter_Name",
  "[Recruiter Id]": "Recruiter_Id",
  "[Position Title]": "Job_Post_Name",
  "[Interview Schedule]": "Date",
  "[Start Time]": "Start_Time",
  "[End Time]": "End_Time",
  "[Location]": "Location",
  "[Venue Link]": "Venue_Link",
  "[Venue]": "Venue",
  "[Date]": "Date",
  "[Calendar Link]": "Calendar_Link",
  "[Round Name]": "Round_Name",
  "[Url]": "Url",
  "[Pin]": "Pin",
  "[Time Zone]": "Time_Zone",
  "[Source]": "Source",
  "[Company Address 1]": "Company_Address_1",
  "[Company Address 2]": "Company_Address_2",
  "[Specialist Email]": "Specialist_Email",
  "[Date Of Join]": "Date_Of_Join",
  "[Group Name]": "Group_Name",
  "[Division Name]": "Division_Name",
  "[Branch Name]": "Branch_Name",
  "[Branch Code]": "Branch_Code",
  "[Department Name]": "Department_Name",
  "[First Line Manager]": "First_Line_Manager",
  "[Second Line Manager]": "Second_Line_Manager",
  "[Org Name]": "Org_Name",
  "[Org Code]": "Org_Code",
  "[Title]": "Title",
  "[Remark]": "Remark",
  "[Designation]": "Designation",
  "[Job Post Url]": "Job_Post_Url",
  "[Candidate Portal URL]": "Candidate_Portal_URL",
};
