<template>
  <div>
    <div
      class="d-flex justify-space-between align-center mt-5 text-subtitle-1 font-weight-bold"
    >
      <v-row>
        <v-col cols="12" class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="green"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
            >Insurance Details</span
          >
        </v-col>
      </v-row>
      <v-dialog
        transition="dialog-bottom-transition"
        v-model="showAddEditInsuranceForm"
        width="60%"
      >
        <template v-slot:activator="{ props }">
          <v-btn
            v-if="formAccess && formAccess.add"
            v-bind="props"
            color="primary"
            variant="text"
            @click="openAddForm()"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
          >
        </template>
        <AddEditInsuranceDetails
          :selectedInsuranceDetails="selectedInsuranceDetails"
          :selectedCandidateId="selectedCandidateId"
          @close-insurance-form="closeAddEditForm"
          @refetch-other-details="onUpdateAddSuccess"
        />
      </v-dialog>
    </div>
    <div class="d-flex justify center" v-if="!isMobileView">
      <v-slide-group
        class="px-4"
        selected-class="bg-primary"
        prev-icon="fas fa-chevron-circle-left"
        next-icon="fas fa-chevron-circle-right"
        show-arrows
      >
        <v-slide-group-item>
          <ViewInsurance
            :insuranceDetails="insuranceDetails"
            :formAccess="formAccess"
            @on-open-edit="openEditForm"
            @on-delete="showDeleteConfirmationModal"
          />
        </v-slide-group-item>
      </v-slide-group>
    </div>
    <div v-else class="d-flex flex-column mt-6 align-center justify-center">
      <ViewInsurance
        :insuranceDetails="insuranceDetails"
        :formAccess="formAccess"
        @on-open-edit="openEditForm"
        @on-delete="showDeleteConfirmationModal"
      />
    </div>
  </div>
  <AppWarningModal
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure you want to delete this record?"
    icon-name="fas fa-trash"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onDeleteInsurance()"
  >
  </AppWarningModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import ViewInsurance from "./ViewInsurance.vue";
const AddEditInsuranceDetails = defineAsyncComponent(() =>
  import("./AddEditInsuranceDetails.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
export default {
  name: "InsuranceMain",
  components: {
    AddEditInsuranceDetails,
    ViewInsurance,
  },
  props: {
    insuranceDetailsData: {
      type: Array,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
  },
  emits: ["submit-form", "refetch-other-details"],
  data: () => ({
    // add/edit
    insuranceDetails: [],
    selectedInsuranceDetails: {},
    showAddEditInsuranceForm: false,
    // delete
    selectedInsuranceDeleteRecord: {},
    openWarningModal: false,
    openBottomSheet: false,
  }),
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.insuranceDetailsData && this.insuranceDetailsData.length > 0) {
      this.insuranceDetails = this.insuranceDetailsData;
    }
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  methods: {
    onUpdateAddSuccess() {
      this.showAddEditInsuranceForm = false;
      this.$emit("refetch-other-details");
    },
    closeAddEditForm() {
      mixpanel.track("Onboarded-candidate-other-insurance-edit-add-closed");
      this.showAddEditInsuranceForm = false;
      this.selectedInsuranceDetails = {};
    },
    openAddForm() {
      mixpanel.track("Onboarded-candidate-other-insurance-add-opened");
      this.selectedInsuranceDetails = {};
      this.showAddEditInsuranceForm = true;
    },
    openEditForm(selectedItem) {
      mixpanel.track("Onboarded-candidate-other-insurance-edit-opened");
      this.selectedInsuranceDetails = selectedItem;
      this.showAddEditInsuranceForm = true;
    },
    showDeleteConfirmationModal(selectedItem) {
      this.selectedInsuranceDeleteRecord = selectedItem;
      this.openWarningModal = true;
    },
    onDeleteInsurance() {
      this.onCloseWarningModal();
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedInsuranceDeleteRecord = {};
    },
  },
};
</script>
