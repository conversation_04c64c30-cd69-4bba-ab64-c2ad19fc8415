<template>
  <Handle type="target" :position="targetPosition"> </Handle>
  <Handle type="source" :position="sourcePosition" />
  <div class="call_node">
    <div
      style="
        display: flex;
        align-items: center;
        position: relative;
        padding-bottom: 5px;
      "
    >
      <div
        style="
          background-color: #0047c81a;
          height: 16px;
          width: 18px;
          display: flex;
          border-radius: 5px;
          justify-content: center;
          align-items: center;
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 448 512"
          height="7"
          width="7"
          fill="#0047c8"
        >
          <path
            d="M429.6 92.1c4.9-11.9 2.1-25.6-7-34.7s-22.8-11.9-34.7-7l-352 144c-14.2 5.8-22.2 20.8-19.3 35.8s16.1 25.8 31.4 25.8H224V432c0 15.3 10.8 28.4 25.8 31.4s30-5.1 35.8-19.3l144-352z"
          />
        </svg>
      </div>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
        "
      >
        <v-text-field
          v-model="title"
          placeholder="Call Activity API"
          class="header_text px-1 py-0 workflow_input"
          label=""
          hide-details
          variant="underlined"
          single-line
          rounded="md"
          min-width="160"
          maxlength="100"
          density="compact"
          @blur="onChangedTitle"
        >
        </v-text-field>
        <div class="close_icon" @click="confirmationModel = true">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="#ffffff"
            height="7"
            width="7"
            viewBox="0 0 384 512"
          >
            <path
              d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            />
          </svg>
        </div>
      </div>
    </div>
    <v-divider></v-divider>
    <div class="d-flex justify-space-between item-center align-center">
      <!-- <span class="d-flex item-center header_text text-center my-1 mr-2">
        Service API:
      </span> -->
      <v-menu
        v-model="selectedNodeModal"
        id="approval-form-filter"
        :close-on-content-click="true"
        density="compact"
        transition="scale-transition"
        min-width="290px"
      >
        <template v-slot:activator="{ props }">
          <v-btn
            class="bg-white mt-2 mb-2 py-0 w-100 font-weight-regular"
            rounded="md"
            size="x-small"
            v-bind="props"
          >
            <!-- <span class="text-caption px-1">Form:</span> -->
            <span class="font-weight-regular header_text mr-1 py-0">
              {{ selectedOption ? selectedOption?.name : "Select API" }}
            </span>
            <v-icon color="primary" size="x-small" v-if="!selectedNodeModal"
              >fas fa-caret-down</v-icon
            >
            <v-icon color="primary" size="x-small" v-if="selectedNodeModal"
              >fas fa-caret-up</v-icon
            >
          </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="(nodeItem, i) in nodeData"
            :key="i"
            class="pages-list"
          >
            <v-list-item-title
              class="pa-2 cursor-pointer text-primary"
              :class="{
                'text-primary': selectedOption.id === nodeItem.data.id,
              }"
              @click="onSelectOption(nodeItem.data)"
            >
              {{ nodeItem.data.name }}</v-list-item-title
            >
          </v-list-item>
        </v-list>
      </v-menu>
    </div>

    <!-- <div class="" style="padding: 10px 5px"></div> -->
    <div
      style="
        position: absolute;
        bottom: -10px;
        z-index: 99999;
        left: 50%;
        transform: translate(-50%);
      "
      v-if="data.addNew"
    >
      <div class="" style="position: relative">
        <div
          :class="
            'glow-button' + (!showMenu ? ' glow-button-active' : ' dark-button')
          "
          v-click-outside="() => (showMenu = false)"
          @click="() => (showMenu = !showMenu)"
        >
          <v-icon class="white" size="8">fas fa-plus</v-icon>
        </div>
        <v-expand-x-transition>
          <div class="" style="position: absolute; top: -4.4rem; left: -0.6rem">
            <MenuItems
              v-if="showMenu"
              @handleProcessNode="handleToStartNode"
            ></MenuItems>
          </div>
        </v-expand-x-transition>
      </div>
    </div>
  </div>
  <AppWarningModal
    v-if="confirmationModel"
    :open-modal="confirmationModel"
    iconName="fas fa-trash"
    confirmation-heading="Are you sure to delete?"
    @close-warning-modal="confirmationModel = false"
    @accept-modal="deleteNode()"
  >
  </AppWarningModal>
</template>
<script>
import { Position, Handle } from "@vue-flow/core";
import MenuItems from "../components/menus/MainMenu.vue";
export default {
  name: "AddCallActivityNode",
  emits: ["handleToStart", "deleteNode", "updateNodeTitle"],
  props: {
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    sourcePosition: {
      type: String,
      required: true,
    },
    targetPosition: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      leftPosition: Position.Top,
      rightPosition: Position.Bottom,
      showMenu: false,
      addNewNode: false,
      title: "",
      confirmationModel: false,
    };
  },
  methods: {
    handleToStartNode(type) {
      this.$emit("handleToStart", type, this.data, false, 0);
    },
    deleteNode() {
      this.confirmationModel = false;
      this.$emit("deleteNode", this.data);
    },
    onChangedTitle() {
      this.$emit("updateNodeTitle", {
        title: this.title,
        nodeId: this.data.id,
      });
    },
  },

  components: {
    MenuItems,
    Handle,
  },
};
</script>
<style>
.call_node {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 5px;
  min-width: 120px;
  /* box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); */
}

.call_node::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  /* background-color: #0081ff; */
  background: linear-gradient(to right, #7f00b1, #e295fa);
  height: 4px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.call_node:hover .close_icon {
  visibility: visible !important;
}
</style>
