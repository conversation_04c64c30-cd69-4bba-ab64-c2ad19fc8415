<template>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppAccessDenied v-else-if="isAccessDenied"></AppAccessDenied>
</template>

<script>
export default {
  name: "TravelAndExpenses",
  data() {
    return {
      isLoading: true,
      isAccessDenied: false,
      forms: [
        {
          formId: 342,
          formName: "Travel and Expenses",
          formUrl: "/my-finance/travel-and-expenses/travel-request",
        },
        {
          formId: 339,
          formName: "Claim Request",
          formUrl: "/my-finance/travel-and-expenses/claim-request",
        },
      ],
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      return (formId) => {
        let formAccessRights = this.accessRights(formId);
        if (
          formAccessRights &&
          formAccessRights.accessRights &&
          formAccessRights.accessRights["view"]
        ) {
          return formAccessRights.accessRights;
        } else return false;
      };
    },
  },
  mounted() {
    let redirected = false;
    for (let form of this.forms) {
      if (this.formAccess(form.formId)?.view) {
        this.$router.push(form.formUrl);
        redirected = true;
        break;
      }
    }
    if (!redirected) {
      this.isAccessDenied = true;
      this.isLoading = false;
    }
  },
};
</script>
