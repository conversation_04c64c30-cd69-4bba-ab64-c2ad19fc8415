<template>
  <div>
    <div class="d-flex justify-space-between align-center">
      <v-row>
        <v-col cols="12" class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="primary"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
            >Air Ticket Settlement Summary</span
          >
        </v-col>
      </v-row>
    </div>
    <div class="d-flex justify center">
      <v-slide-group
        class="px-4"
        selected-class="bg-primary"
        prev-icon="fas fa-chevron-circle-left"
        next-icon="fas fa-chevron-circle-right"
        show-arrows
      >
        <v-slide-group-item>
          <div
            v-if="summaryData?.length === 0"
            class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
          >
            No Air Ticket Settlement Summary have been added
          </div>
          <v-card
            elevation="3"
            v-for="(data, index) in summaryData"
            :key="index"
            class="card-item d-flex pa-4 rounded-lg ma-2"
            color="grey-lighten-5"
            :style="
              !isMobileView
                ? `min-width: 500px;border-left: 7px solid ${generateRandomColor()}; min-height:auto;`
                : `border-left: 7px solid ${generateRandomColor()}`
            "
          >
            <div class="d-flex flex-column" style="width: 100%">
              <div class="card-columns w-100 mt-n6">
                <span
                  :style="!isMobileView ? 'width:55%' : 'width:100%'"
                  class="d-flex align-start flex-column"
                >
                  <v-card-text class="text-body-1 font-weight-regular">
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >Destination City</b
                      >
                      <span class="py-2">
                        {{ checkNullValue(data.Destination_City) }}</span
                      >
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >Air Ticket Category</b
                      >
                      <span class="py-2">
                        {{ checkNullValue(data.Air_Ticketing_Category) }}</span
                      >
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >Settlement Amount</b
                      >

                      <span class="py-2">
                        <span v-if="payrollCurrency">{{
                          payrollCurrency
                        }}</span>
                        {{ checkNullValue(data.Settlement_Amount) }}</span
                      >
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >No. of Tickets
                      </b>
                      <span class="py-2">
                        {{ checkNullValue(data.No_Of_Dependents + 1) }}
                      </span>
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start">Availed Date</b>
                      <span class="py-2">
                        {{ formatDate(data.Availed_Date) }}</span
                      >
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start">Payroll Month</b>
                      <span class="py-2">
                        {{ formatPayrollMonth(data.Payroll_Month) }}</span
                      >
                    </div>
                  </v-card-text>
                </span>
                <span
                  :style="
                    !isMobileView
                      ? 'width:50%'
                      : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
                  "
                  class="d-flex align-start flex-column"
                >
                  <v-card-text class="text-body-1 font-weight-regular">
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >Destination Country</b
                      >
                      <span class="py-2">
                        {{ checkNullValue(data.Destination_Country) }}</span
                      >
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start">Status</b>
                      <span class="py-2">
                        {{ checkNullValue(data.Settlement_Status) }}</span
                      >
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >No. of Dependents Eligible for Ticket
                      </b>
                      <span class="py-2">{{
                        data.No_Of_Dependents ? data.No_Of_Dependents : 0
                      }}</span>
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >Air Ticket to Dependent
                      </b>
                      <span class="py-2">{{
                        checkNullValue(data.Air_Ticket_To_Dependent)
                      }}</span>
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >Accrual Basis
                        <span v-if="payrollCurrency"
                          >(in {{ payrollCurrency }})</span
                        ></b
                      >
                      <span class="py-2">
                        <span
                          >Infant -
                          {{
                            data?.Infant_Policy_Amount
                              ? data?.Infant_Policy_Amount
                              : 0
                          }},</span
                        >
                        <span class="ml-1"
                          >Child -
                          {{
                            data?.Child_Policy_Amount
                              ? data?.Child_Policy_Amount
                              : 0
                          }},</span
                        >
                        <span class="m-1">
                          Adult -
                          {{
                            data?.Adult_Policy_Amount
                              ? data?.Adult_Policy_Amount
                              : 0
                          }}</span
                        >
                      </span>
                    </div>
                    <div class="mt-2 mr-2 d-flex flex-column justify-start">
                      <b class="mr-2 text-grey justify-start"
                        >Eligibility of Ticket Claim (In Months)</b
                      >
                      <span class="py-2">
                        {{
                          checkNullValue(
                            data.Eligibility_Of_Ticket_Claim_Months
                          )
                        }}</span
                      >
                    </div>
                  </v-card-text>
                </span>
              </div>
            </div>
          </v-card>
        </v-slide-group-item>
      </v-slide-group>
    </div>
  </div>
</template>
<script>
import { checkNullValue, generateRandomColor } from "@/helper";
import moment from "moment";

export default {
  name: "ViewAirticketSummary",
  props: {
    settlementSummaryData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      summaryData: [],
    };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  mounted() {
    this.summaryData = this.settlementSummaryData?.length
      ? this.settlementSummaryData
      : [];
  },

  methods: {
    checkNullValue,
    generateRandomColor,
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    formatPayrollMonth(value) {
      if (!value) return "-";
      const [month, year] = value.split(",");
      return moment(`${year}-${month}-01`).format("MMM, YYYY");
    },
  },
};
</script>
