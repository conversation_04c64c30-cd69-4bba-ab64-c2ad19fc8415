import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const LIST_SHORT_TIME_OFF = gql`
  query ListShortTimeOff(
    $employeeId: Int
    $startDate: String!
    $endDate: String!
    $formId: Int!
  ) {
    listShortTimeOff(
      employeeId: $employeeId
      startDate: $startDate
      endDate: $endDate
      formId: $formId
    ) {
      errorCode
      message
      shortTimeOffDetails {
        Short_Time_Off_Id
        Employee_Id
        Short_Time_Off_Date
        Start_Date_Time
        End_Date_Time
        Early_Checkout_Short_Time_Off_Id
        Auto_Short_Time_Off
        Total_Hours
        Process_Instance_Id
        Reason
        Request_For
        Alternate_Person
        Contact_Details
        Approval_Status
        Added_By
        Approver_Id
        Added_On
        Updated_On
        Approved_On
        View_Start_Date_Time
        View_End_Date_Time
        Late_Attendance
        Early_Checkout
        Early_Checkout_Hours
        User_Defined_EmpId
        AlternatePersonName
        Added_By_Name
        Updated_By_Name
        Approved_By_Name
        Service_Provider_Id
        Business_Unit_Id
        Business_Unit
        Location_Id
        Department_Id
        Designation_Id
        Designation_Code
        Manager_Id
        Location_Name
        Department_Name
        Designation_Name
        Employee_Type
        EmpType_Id
        Employee_Name
        Manager_Name
        Organization_Unit_Name
        Comment_Id
        comments {
          Comment_Id
          Parent_Id
          Emp_Comment
          Approval_Status
          Type
          Form_Id
          Added_By
          Added_On
        }
      }
    }
  }
`;
