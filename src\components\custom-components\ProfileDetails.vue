<template>
  <div>
    <div class="my-1">
      <span class="text-primary text-h5 font-weight-bold">{{
        employeeName ? employeeName : "-"
      }}</span>
    </div>
    <div v-if="empUserDefId" class="my-1">
      <span class="rounded-lg py-1 px-8 bg-pink-lighten-5"
        >E-ID:{{ empUserDefId }}</span
      >
    </div>
    <div class="my-1">
      <span class="text-black">{{ empDepartment ? empDepartment : "-" }}</span>
    </div>
    <div class="my-1">
      <span class="font-weight-bold">{{
        empDesignation ? empDesignation : "-"
      }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProfileDetails",
  props: {
    employeeName: {
      type: String,
      default: "",
    },
    empUserDefId: {
      type: String,
      default: "",
    },
    empDesignation: {
      type: String,
      default: "",
    },
    empDepartment: {
      type: String,
      default: "",
    },
  },
};
</script>
