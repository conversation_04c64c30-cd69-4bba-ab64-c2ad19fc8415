<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Award Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="primary" size="25" @click="$emit('close-award-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditAwardForm">
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="awardFormData.Award_Name"
                :rules="[
                  required('Award', awardFormData.Award_Name),
                  validateWithRulesAndReturnMessages(
                    awardFormData.Award_Name,
                    'awardName',
                    'Award'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  Award<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-menu
                v-model="ReceivedOnMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="Received On"
                    v-model="formattedReceivedOn"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Received On', formattedReceivedOn)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Received On<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="awardFormData.Received_On"
                  :min="selectedEmpDobDate"
                  :max="currentDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="awardFormData.Received_From"
                :rules="[
                  required('Received From', awardFormData.Received_From),
                  validateWithRulesAndReturnMessages(
                    awardFormData.Received_From,
                    'awardReceivedFrom',
                    'Received From'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  Received From<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="awardFormData.Received_For"
                :rules="[
                  required('Received For', awardFormData.Received_For),
                  validateWithRulesAndReturnMessages(
                    awardFormData.Received_For,
                    'awardReceivedFor',
                    'Received For'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  Received For<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="this.$emit('close-award-form')"
                  class="ma-2 pa-2"
                  color="primary"
                  rounded="lg"
                  elevation="4"
                  variant="text"
                  >Cancel</v-btn
                >
                <v-btn
                  :disabled="!isFormDirty"
                  class="ma-2 pa-2"
                  color="primary"
                  rounded="lg"
                  @click="validateAwardDetails"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import moment from "moment";
import { ADD_UPDATE_AWARD_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditAwardDetails",
  props: {
    selectedAwardDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
  },
  mixins: [validationRules],
  emits: ["refetch-career-details", "close-award-form"],
  data() {
    return {
      awardFormData: {
        Award_Name: "",
        Received_On: null,
        Received_From: "",
        Received_For: "",
      },
      isFormDirty: false,
      //Date-picker
      formattedReceivedOn: "",
      ReceivedOnMenu: false,
      selectedReceivedOn: null,
      // edit
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
    };
  },
  watch: {
    "awardFormData.Received_On": function (val) {
      if (val) {
        this.ReceivedOnMenu = false;
        this.formattedReceivedOn = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    selectedEmpDobDate() {
      if (
        this.selectedCandidateDOB &&
        this.selectedCandidateDOB !== "0000-00-00"
      ) {
        return moment(this.selectedCandidateDOB).format("YYYY-MM-DD");
      } else return null;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedAwardDetails &&
      Object.keys(this.selectedAwardDetails).length > 0
    ) {
      this.awardFormData = JSON.parse(
        JSON.stringify(this.selectedAwardDetails)
      );
      if (this.awardFormData.Received_On) {
        this.formattedReceivedOn = this.formatDate(
          this.awardFormData?.Received_On
        );
        this.awardFormData.Received_On = this.awardFormData.Received_On
          ? new Date(this.awardFormData.Received_On)
          : null;
      }
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
  },
  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },

    async validateAwardDetails() {
      const { valid } = await this.$refs.addEditAwardForm.validate();
      mixpanel.track("Onboarded-candidate-career-award-submit-click");
      if (valid) {
        this.updateAwardDetails();
      }
    },

    updateAwardDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_AWARD_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            awardId: vm.awardFormData.Award_Id,
            awardName: vm.awardFormData.Award_Name,
            receivedOn: moment(vm.awardFormData.Received_On).isValid()
              ? moment(vm.awardFormData.Received_On).format("YYYY-MM-DD")
              : null,
            receivedFrom: vm.awardFormData.Received_From,
            receivedFor: vm.awardFormData.Received_For,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-career-award-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Award details updated successfully"
                : "Award details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-career-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-career-award-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "award details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
