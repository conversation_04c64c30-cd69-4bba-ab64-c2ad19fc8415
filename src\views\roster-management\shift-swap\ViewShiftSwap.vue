<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" color="hover" size="35" variant="elevated">
            <v-icon class="text-primary" size="20">fas fa-file-alt</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              {{ landedFormName }}
            </div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-btn
            @click="$emit('open-edit-form')"
            size="small"
            color="primary"
            variant="elevated"
            rounded="lg"
            v-if="
              !formApprovalView &&
              accessRights.update &&
              !(
                selectedFormData.Approval_Status.toLowerCase() === 'approved' ||
                selectedFormData.Approval_Status.toLowerCase() === 'rejected'
              )
            "
            >Edit</v-btn
          >
          <v-icon class="mx-1" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <div
        :style="
          isMobileView
            ? 'height: calc(100vh - 400px); overflow: scroll'
            : 'min-height: 400px'
        "
      >
        <v-card-text>
          <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Employee Id</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.User_Defined_EmpId) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Employee Name</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Employee_Name) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Approver</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Approver_Name) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Swap Date</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ formatSwapDate(selectedFormData.Swap_Date) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Shift Type</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Shift_Name) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">Status</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Approval_Status) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                Shift assigned on the selected date
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Current_Shift_Name) }}
              </p>
            </v-col>
            <v-col cols="12">
              <p class="text-subtitle-1 text-grey-darken-1">Reason</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Reason) }}
              </p>
            </v-col>
          </v-row>
        </v-card-text>
      </div>
      <div>
        <v-row class="px-sm-8 px-md-10 mt-2 mb-2">
          <v-col v-if="moreDetailsList.length > 0" cols="12">
            <MoreDetails
              :more-details-list="moreDetailsList"
              :open-close-card="openMoreDetails"
              @on-open-close="openMoreDetails = $event"
            ></MoreDetails> </v-col
        ></v-row>
      </div>
    </v-card>
    <FilePreviewModal
      v-if="openModal"
      :fileName="selectedItem.fileName"
      folderName="Employee Document Download"
      fileRetrieveType="documents"
      @close-preview-modal="openModal = false"
    ></FilePreviewModal>
  </div>
</template>

<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import FilePreviewModal from "@/components/custom-components/FilePreviewModal.vue";
import moment from "moment";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "ViewShiftSwap",
  components: {
    MoreDetails,
    FilePreviewModal,
  },
  props: {
    formApprovalView: {
      type: Boolean,
      default: false,
    },
    selectedItem: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    selectedFormData: {},
    openModal: false,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    formatSwapDate() {
      return (date) => {
        if (date) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        } else {
          return "-";
        }
      };
    },
    instruction() {
      return this.selectedFormData.instruction !== "<p><br></p>"
        ? this.selectedFormData.instruction
        : "";
    },
  },

  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);
        this.prefillMoreDetails();
      },
    },
  },

  methods: {
    convertUTCToLocal,
    checkNullValue,
    closeEditForm() {
      this.$emit("close-form");
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      let addedDateLocal = convertUTCToLocal(this.selectedItem.Added_On);
      let updateDateLocal = convertUTCToLocal(this.selectedItem.Updated_On);
      const addedOn = addedDateLocal,
        addedByName = this.selectedItem.Added_By_Name,
        updatedByName = this.selectedItem.Updated_By_Name,
        updatedOn = updateDateLocal;
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>
