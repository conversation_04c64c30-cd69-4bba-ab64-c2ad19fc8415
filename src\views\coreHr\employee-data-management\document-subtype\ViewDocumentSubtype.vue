<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" color="hover" size="35" variant="elevated">
            <v-icon class="text-primary" size="20">fas fa-file-alt</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              {{ landedFormName }}
            </div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-btn
            @click="$emit('open-edit-form')"
            size="small"
            color="primary"
            variant="elevated"
            rounded="lg"
            v-if="accessRights.update"
            >{{ $t("coreHr.edit") }}</v-btn
          >
          <v-icon class="mx-1" @click="$emit('close-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <div
        :style="
          isMobileView
            ? 'height: calc(100vh - 400px); overflow: scroll'
            : 'min-height: 400px'
        "
      >
        <v-card-text>
          <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("coreHr.documentSubtype") }}
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.documentSubType) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("coreHr.documentType") }}
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.documentType) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("coreHr.documentCategory") }}
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.categoryFields) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("coreHr.enforcedInSelfOnboarding") }}
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.mandatory) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("coreHr.documentEnforcementGroup") }}
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(selectedFormData.Group_Names) }}
              </p>
            </v-col>
            <v-col cols="12" sm="6" lg="6" :class="isMobileView ? ' ml-4' : ''">
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("coreHr.instruction") }}
              </p>
              <p
                class="text-subtitle-1 font-weight-regular"
                v-html="checkNullValue(instruction)"
              ></p>
            </v-col>
            <v-col
              v-if="selectedItem.fileName"
              cols="12"
              sm="6"
              lg="6"
              :class="isMobileView ? ' ml-4' : ''"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                {{ $t("coreHr.document") }}
              </p>
              <span
                style="text-decoration: underline"
                @click="retrieveResumeDetails()"
                class="text-blue cursor-pointer mb-2"
              >
                {{ $t("coreHr.viewDocument") }}
              </span>
            </v-col>
          </v-row>
        </v-card-text>
      </div>
      <div>
        <v-row class="px-sm-8 px-md-10 mt-2 mb-2">
          <v-col v-if="moreDetailsList.length > 0" cols="12">
            <MoreDetails
              :more-details-list="moreDetailsList"
              :open-close-card="openMoreDetails"
              @on-open-close="openMoreDetails = $event"
            ></MoreDetails> </v-col
        ></v-row>
      </div>
    </v-card>
    <FilePreviewModal
      v-if="openModal"
      :fileName="selectedItem.fileName"
      folderName="Employee Document Download"
      fileRetrieveType="documents"
      @close-preview-modal="openModal = false"
    ></FilePreviewModal>
  </div>
</template>

<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import FilePreviewModal from "@/components/custom-components/FilePreviewModal.vue";
import moment from "moment";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "ViewOrganizationGroup",
  components: {
    MoreDetails,
    FilePreviewModal,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  emits: ["close-form", "open-edit-form"],
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    selectedFormData: {},
    openModal: false,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    instruction() {
      return this.selectedFormData.instruction !== "<p><br></p>"
        ? this.selectedFormData.instruction
        : "";
    },
  },

  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);
        this.prefillMoreDetails();
      },
    },
  },

  methods: {
    convertUTCToLocal,
    checkNullValue,
    closeEditForm() {
      this.$emit("close-form");
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      let addedDateLocal = this.selectedItem.addedOn;
      let updateDateLocal = this.selectedItem.updatedOn;
      const addedOn = this.formatDate(new Date(addedDateLocal + ".000Z")),
        addedByName = this.selectedItem.addedBy,
        updatedByName = this.selectedItem.updatedBy,
        updatedOn = this.formatDate(new Date(updateDateLocal + ".000Z"));
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: this.$t("coreHr.added"),
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: this.$t("coreHr.updated"),
        });
      }
    },

    retrieveResumeDetails() {
      let vm = this;
      vm.openModal = true;
    },
  },
};
</script>
