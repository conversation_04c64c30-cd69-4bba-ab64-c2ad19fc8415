<template>
  <div
    class="chart-container"
    ref="chartContainer"
    :style="'overflow: scroll; height: ' + $store.getters.getTableHeight(165)"
  ></div>
</template>

<script>
import { ref, onMounted, createApp } from "vue";
import * as d3 from "d3";
import { OrgChart } from "d3-org-chart";
import EmployeeCard from "./EmployeeCard.vue";

export default {
  name: "D3Chart",
  props: {
    orgList: {
      type: Array,
      required: true,
    },
  },
  setup(props) {
    const chartContainer = ref(null);
    function renderOrgChart(container, data) {
      if (!container.value || data.length === 0) {
        return;
      }
      const chart = new OrgChart()
        .container(container.value)
        .data(data)
        .scaleExtent([0.1, 2])
        .setActiveNodeCentered(true)
        .nodeHeight(() => 100)
        .nodeWidth(() => 240)
        .childrenMargin(() => 70)
        .compactMarginBetween(() => 20)
        .compactMarginPair(() => 120)
        .siblingsMargin(() => 120)
        .nodeUpdate(function (d) {
          d3.select(this)
            .select(".node-rect")
            .attr("width", 386)
            .attr("height", 155)
            .attr("stroke", (d) =>
              d.data._highlighted || d.data._upToTheRootHighlighted
                ? "#4285F4"
                : "none"
            )
            .attr(
              "stroke-width",
              d.data._highlighted || d.data._upToTheRootHighlighted ? 8 : 2
            )
            .attr("y", -3)
            .attr("x", -3)
            .attr("stroke-linejoin", "round")
            .style("stroke-alignment", "outer");
        })
        .linkUpdate(function (d) {
          d3.select(this)
            .attr("stroke", (d) =>
              d.data._upToTheRootHighlighted ? "#4285F4" : "#9e9e9e"
            )
            .attr("stroke-width", (d) =>
              d.data._upToTheRootHighlighted ? 15 : 1
            );
          if (d.data._upToTheRootHighlighted) {
            d3.select(this).raise();
          }
        })
        .onNodeClick((d) => {
          clickedNodeID.value = d.id;
          markNode(d.id);
          centerNode(d.id);
        })
        .nodeButtonX(() => -8)
        .nodeButtonY(() => -10)
        .buttonContent(({ node }) => {
          const foreignObject = d3.selectAll(".node-button-foreign-object");
          foreignObject.attr("width", 100).attr("height", 50);
          return `
     
          ${
            node.children
              ? `<div class="btn-rounded-org"><svg xmlns="http://www.w3.org/2000/svg" height="7" width="7" fill="#0c166e" viewBox="0 0 448 512"><path d="M416 208H32c-17.7 0-32 14.3-32 32v32c0 17.7 14.3 32 32 32h384c17.7 0 32-14.3 32-32v-32c0-17.7-14.3-32-32-32z"/></svg></div>`
              : `<div class="btn-rounded-org"><svg xmlns="http://www.w3.org/2000/svg" height="7" width="7" fill="#0c166e" viewBox="0 0 448 512"><path d="M416 208H272V64c0-17.7-14.3-32-32-32h-32c-17.7 0-32 14.3-32 32v144H32c-17.7 0-32 14.3-32 32v32c0 17.7 14.3 32 32 32h144v144c0 17.7 14.3 32 32 32h32c17.7 0 32-14.3 32-32V304h144c17.7 0 32-14.3 32-32v-32c0-17.7-14.3-32-32-32z"/></svg></div>`
          }
          `;
        })
        .nodeContent((d) => {
          // creating temp App to render ChartUI component and convert it to HTML
          const app = createApp(EmployeeCard, {
            customData: d.data,
          });
          const vm = app.mount(document.createElement("div"));
          const chartUIHTML = vm.$el.outerHTML;
          app.unmount();
          return chartUIHTML;
        })
        .render()
        .fit();
      return chart;
      // Chart functions
      function markNode(nodeID) {
        if (chart) {
          // Check if chart is defined
          chart.setHighlighted(nodeID).render();
        }
      }
      function centerNode(nodeId) {
        if (chart) {
          // chart.fit(nodeId).render();
          chart.setCentered(nodeId).render();
        }
      }
    }
    onMounted(async () => {
      try {
        chartContainer.value = renderOrgChart(chartContainer, props.orgList);
      } catch (error) {
        console.error("Error loading data:", error);
      }
    });
    return {
      chartContainer,
    };
  },
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  /* height: 80vh; */
  position: relative;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #e6e6e6;
}
</style>
