import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_PROVIDENT_FUND_RULES_SLAB_DATA = gql`
  query retrieveProvidentFundRulesSlabWiseData {
    retrieveProvidentFundRulesSlabWiseData {
      errorCode
      message
      slabWiseData
    }
  }
`;

export const RETRIEVE_PROVIDENT_FUND_RULES = gql`
  query retrieveProvidentFundRules {
    retrieveProvidentFundRules {
      errorCode
      message
      response
    }
  }
`;

export const LIST_INVESTMENT_CATEGORY = gql`
  query listInvestmentCategory {
    listInvestmentCategory {
      errorCode
      message
      investmentCategoriesData {
        Investment_Cat_Id
        Investment_Category
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const UPDATE_PROVIDENT_FUND_RULES = gql`
  mutation updateProvidentFundRules(
    $Restricted_PF_Wage_Amount: Float
    $Employee_Share: Float
    $Employer_Share: Float
    $Employees_Pension_Scheme: Float
    $EPF_Account_Share: Float
    $Admin_Charge: Float
    $Admin_Charge_Max_Amount: Float
    $EDLI_Charge: Float
    $EDLI_Charge_Max_Amount: Float
    $Auto_Declaration: String
    $Auto_Declaration_Applicable_For: String
    $Investment_Cat_Id: Int
  ) {
    updateProvidentFundRules(
      Restricted_PF_Wage_Amount: $Restricted_PF_Wage_Amount
      Employee_Share: $Employee_Share
      Employer_Share: $Employer_Share
      Employees_Pension_Scheme: $Employees_Pension_Scheme
      EPF_Account_Share: $EPF_Account_Share
      Admin_Charge: $Admin_Charge
      Admin_Charge_Max_Amount: $Admin_Charge_Max_Amount
      EDLI_Charge: $EDLI_Charge
      EDLI_Charge_Max_Amount: $EDLI_Charge_Max_Amount
      Auto_Declaration: $Auto_Declaration
      Auto_Declaration_Applicable_For: $Auto_Declaration_Applicable_For
      Investment_Cat_Id: $Investment_Cat_Id
    ) {
      errorCode
      message
    }
  }
`;
