<template style="height: 100%">
  <div>
    <div v-if="showAction" class="text-primary font-weight-bold my-2">
      {{ categoryType }}
      <v-tooltip
        v-if="!isLoading && isDataTracked"
        :text="noEditAccessMsg"
        location="right"
        max-width="400"
      >
        <template v-slot:activator="{ props }">
          <v-btn
            v-bind="canUpdate ? '' : props"
            class="mx-1"
            :class="{ 'pointer-block': !canUpdate }"
            :color="canUpdate ? 'primary' : 'hover'"
            variant="outlined"
            size="small"
            rounded="lg"
            @click="canUpdate ? $emit('edit-click') : {}"
            >{{ $t("settings.edit") }}</v-btn
          >
        </template>
      </v-tooltip>
    </div>
    <div v-if="isLoading" class="mt-4">
      <v-skeleton-loader ref="skeleton" type="text"></v-skeleton-loader>
    </div>
    <div v-else-if="isDataTracked" class="pt-1 pb-2">
      <div v-if="showCategories" class="d-flex align-center flex-wrap">
        <div class="d-flex align-center mr-2">
          <v-icon color="#22C600" size="18">fas fa-check-circle</v-icon>
          <span class="text-primary pl-2" style="font-size: 13px"
            >{{ $t("settings.blocked") }} - {{ productiveCount }}</span
          >
        </div>
        <div class="d-flex align-center mr-2">
          <v-icon color="#FF6C34" size="18">far fa-times-circle</v-icon>
          <span class="text-primary pl-2" style="font-size: 13px"
            >{{ $t("settings.unblocked") }} - {{ unproductiveCount }}</span
          >
        </div>
        <!-- <div class="d-flex align-center">
            <v-icon color="#FFBC00" size="15">fas fa-exclamation-triangle</v-icon>
            <span class="text-primary" style="font-size: 13px"
              >Uncategorized - {{ uncategorizedCount }}</span
            >
          </div> -->
      </div>
    </div>
    <div v-else>
      <div
        class="pa-2 mt-2 bg-blue-lighten-5 rounded-lg d-flex align-center"
        style="max-width: 250px"
      >
        <img
          :src="getImageUrl"
          alt="app_url_image"
          :width="categoryType === 'Apps' ? 25 : 40"
          height="20"
          class="mr-2"
        />
        <span class="text-caption"
          >{{ $t("settings.noItemsToCategorizePre") }}{{ categoryType
          }}{{ $t("settings.noItemsToCategorizePost") }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AppUrlCategoryCard",
  props: {
    productiveCount: {
      type: Number,
      default: 0,
    },
    unproductiveCount: {
      type: Number,
      default: 0,
    },
    uncategorizedCount: {
      type: Number,
      default: 0,
    },
    categoryType: {
      type: String,
      required: true,
    },
    canUpdate: {
      type: Boolean,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: true,
    },
    showAction: {
      type: Boolean,
      default: true,
    },
    showCategories: {
      type: Boolean,
      default: true,
    },
    isDataTracked: {
      type: Number,
      required: true,
    },
  },
  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getImageUrl() {
      let imageName = this.categoryType.toLowerCase();
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/settings/" + imageName + ".webp");
      else return require("@/assets/images/settings/" + imageName + ".png");
    },
  },
};
</script>
