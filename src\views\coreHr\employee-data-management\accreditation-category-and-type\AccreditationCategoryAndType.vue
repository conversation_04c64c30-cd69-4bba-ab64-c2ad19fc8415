<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="true"
                :is-default-filter="false"
                @apply-emp-filter="applyFilter()"
                @reset-emp-filter="resetFilter()"
              >
                <template #new-filter>
                  <v-row class="mr-2 mt-2">
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-radio-group inline color="primary" v-model="mandatory">
                        <template v-slot:label>
                          {{ $t("coreHr.mandatorySelfOnboarding") }}
                        </template>
                        <v-radio label="Yes" value="Yes"></v-radio>
                        <v-radio label="No" value="No"></v-radio>
                      </v-radio-group>
                    </v-col>
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-radio-group
                        inline
                        color="primary"
                        v-model="selectedEnforceDependent"
                      >
                        <template v-slot:label>
                          {{ $t("coreHr.enforceDependent") }}
                        </template>
                        <v-radio label="Yes" value="1"></v-radio>
                        <v-radio label="No" value="0"></v-radio>
                      </v-radio-group>
                    </v-col>
                    <v-col class="py-2" :cols="!isMobileView ? 6 : 12">
                      <v-autocomplete
                        v-model="selectedEnforcementGroup"
                        :label="$t('coreHr.accreditationEnforcementGroup')"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        itemTitle="Group_Name"
                        itemValue="Group_Id"
                        single-line
                        color="primary"
                        :items="EnforcementGroupList"
                        :loading="EnforcementGroupListLoading"
                      ></v-autocomplete>
                    </v-col>
                  </v-row>
                </template>
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="$t('coreHr.retry')"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      :notes="$t('coreHr.accreditationHelp1')"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      :notes="$t('coreHr.accreditationHelp2')"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0"
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onClickAdd()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span>{{ $t("coreHr.addNew") }}</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter()"
                    >
                      {{ $t("coreHr.resetFilterSearch") }}
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      class="mt-1"
                      color="transparent"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div
              v-if="originalList.length > 0"
              class="d-flex align-center my-3"
              :class="isMobileView ? 'justify-center ' : 'justify-end'"
            >
              <v-btn
                prepend-icon="fas fa-plus"
                variant="elevated"
                rounded="lg"
                class="mx-1 primary"
                :size="isMobileView ? 'small' : 'default'"
                @click="onClickAdd()"
              >
                <template v-slot:prepend>
                  <v-icon></v-icon>
                </template>
                <span>{{ $t("coreHr.addNew") }}</span>
              </v-btn>
              <v-btn
                rounded="lg"
                class="mt-1"
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    class="mt-1 ml-n3 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action"
                    @click="onMoreAction(action)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'bg-hover': isHovering,
                          }"
                        >
                          <v-tooltip :text="action.message">
                            <template v-slot:activator="{ props }">
                              <div v-bind="action.message ? props : ''">
                                <v-icon
                                  size="15"
                                  class="pr-2"
                                  color="primary"
                                  >{{ action.icon }}</v-icon
                                >
                                {{ action.key }}
                              </div>
                            </template>
                          </v-tooltip>
                        </v-list-item-title>
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>

            <v-row>
              <v-col v-if="originalList.length > 0" :cols="12" class="mb-12">
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("coreHr.accreditationCategory") }}
                        </div>
                        <section class="d-flex align-center">
                          <v-tooltip
                            :text="item.Accreditation_Category"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                :style="
                                  !isMobileView
                                    ? 'max-width: 200px; '
                                    : 'max-width: 200px; '
                                "
                                v-bind="
                                  item.Accreditation_Category &&
                                  item.Accreditation_Category.length > 20
                                    ? props
                                    : ''
                                "
                              >
                                {{
                                  checkNullValue(item.Accreditation_Category)
                                }}
                              </div>
                            </template>
                          </v-tooltip>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("coreHr.accreditationType") }}
                        </div>
                        <section class="d-flex align-center">
                          <v-tooltip
                            :text="item.Accreditation_Type"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                :style="
                                  !isMobileView
                                    ? 'max-width: 300px; '
                                    : 'max-width: 200px; '
                                "
                                v-bind="
                                  item.Accreditation_Type &&
                                  item.Accreditation_Type.length > 20
                                    ? props
                                    : ''
                                "
                              >
                                {{ checkNullValue(item.Accreditation_Type) }}
                              </div>
                            </template>
                          </v-tooltip>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("coreHr.mandatorySelfOnboarding") }}
                        </div>
                        <section
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 500px; '
                              : 'max-width: 200px; '
                          "
                        >
                          {{ checkNullValue(item.Mandatory) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("coreHr.enforceDependent") }}
                        </div>
                        <section
                          class="text-subtitle-1 font-weight-regular text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 500px; '
                              : 'max-width: 200px; '
                          "
                        >
                          {{ item.Enforce_Dependent ? "Yes" : "No" }}
                        </section>
                      </td>
                      <td
                        class="text-body-2"
                        style="width: 150px"
                        @click.stop="
                          {
                          }
                        "
                      >
                        <span
                          v-if="item.File_Name"
                          style="text-decoration: underline"
                          @click="retrieveDocument(item.File_Name)"
                          class="text-blue cursor-pointer mb-2"
                        >
                          {{ $t("coreHr.viewDocument") }}
                        </span>
                        <div v-else>-</div>
                      </td>
                      <td
                        class="text-body-2 text-center"
                        style="width: 150px"
                        @click.stop="
                          {
                          }
                        "
                      >
                        <ActionMenu
                          @selected-action="onActions($event, item)"
                          :actions="['Edit']"
                          :access-rights="{ update: formAccess.update }"
                          iconColor="grey"
                        ></ActionMenu>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <ViewAccreditationType
    v-if="showViewForm"
    :overlay-model="showViewForm"
    :selected-item="selectedItem"
    @on-edit="onClickEdit()"
    @close-view="closeAllForms()"
  ></ViewAccreditationType>
  <AddEditAccreditationType
    v-if="showAddEditForm"
    :is-edit="isEdit"
    :overlay-model="showAddEditForm"
    :selected-item="selectedItem"
    @on-form-edit="refetchList()"
    @close-view="closeAllForms()"
  ></AddEditAccreditationType>
  <FilePreviewModal
    v-if="openDocumentViewModel"
    :fileName="selectedFileName"
    folderName="Accreditations"
    fileRetrieveType="documents"
    @close-preview-modal="openDocumentViewModel = false"
  ></FilePreviewModal>
</template>
<script>
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import moment from "moment";
import { defineAsyncComponent } from "vue";
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ViewAccreditationType = defineAsyncComponent(() =>
  import("./ViewAccreditationType.vue")
);
const AddEditAccreditationType = defineAsyncComponent(() =>
  import("./AddEditAccreditationType.vue")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

import {
  LIST_ACCREDITATION_ENFORCEMENT_GROUPS,
  LIST_ACCREDITATION_CATEGORY_TYPES,
} from "@/graphql/corehr/accreditationCategoryAndTypeQueries";
export default {
  name: "AccreditationCategoryAndType",
  components: {
    ActionMenu,
    NotesCard,
    EmployeeDefaultFilterMenu,
    ViewAccreditationType,
    AddEditAccreditationType,
    FilePreviewModal,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      itemList: [],
      originalList: [],
      filterList: [],
      showAddEditForm: false,
      showViewForm: false,
      selectedItem: null,
      openMoreMenu: false,
      isFilterApplied: false,
      isSearchApplied: false,
      mandatory: null,
      selectedEnforceDependent: null,
      selectedEnforcementGroup: [],
      EnforcementGroupList: [],
      EnforcementGroupListLoading: false,
      isEdit: false,
      openDocumentViewModel: false,
      selectedFileName: "",
    };
  },
  computed: {
    landedFormName() {
      return this.$t("coreHr.accreditationCategoryAndType");
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = this.$t("coreHr.noAccreditationsForFilters");
      }
      return msgText;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    formAccess() {
      let formAccessRights = this.accessRights(351);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    empDataSetupFormAccess() {
      return this.$store.getters.empDataManagementTabs;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.empDataSetupFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access of formAccess) {
          if (access.havingAccess || access.formId === 351) {
            formAccessArray.push(this.$t(access.displayFormName));
          }
        }
        return formAccessArray;
      }
      return [];
    },
    moreActions() {
      return [{ key: this.$t("coreHr.export"), icon: "fas fa-file-export" }];
    },
    tableHeaders() {
      return [
        {
          title: this.$t("coreHr.accreditationCategory"),
          key: "Accreditation_Category",
          align: "start",
        },
        {
          title: this.$t("coreHr.accreditationType"),
          key: "Accreditation_Type",
        },
        {
          title: this.$t("coreHr.mandatorySelfOnboarding"),
          key: "Mandatory",
        },
        {
          title: this.$t("coreHr.enforceDependent"),
          key: "Enforce_Dependent",
        },
        { title: this.$t("coreHr.document"), key: "", sortable: false },
        {
          title: this.$t("coreHr.actions"),
          key: "",
          align: "center",
          sortable: false,
        },
      ];
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat + " HH:mm") : "";
        }
        return "";
      };
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchAccreditationCategoryAndType();
    this.fetchAccreditationEnforcementGroups();
  },
  methods: {
    checkNullValue,
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },
    refetchList() {
      this.resetFilter();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchAccreditationCategoryAndType();
    },
    resetFilter() {
      this.mandatory = null;
      this.selectedEnforceDependent = null;
      this.selectedEnforcementGroup = [];
      this.itemList = this.isSearchApplied
        ? this.filterList
        : this.originalList;
      this.isFilterApplied = false;
    },
    onApplySearch(val) {
      if (!val) {
        this.isSearchApplied = false;
        this.itemList = this.isFilterApplied
          ? this.filterList
          : this.originalList;
      } else {
        this.isSearchApplied = true;
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.isFilterApplied
          ? this.filterList
          : this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
        if (!this.isFilterApplied) this.filterList = searchItems;
      }
    },
    applyFilter() {
      let filteredArray = this.isSearchApplied
        ? this.filterList
        : this.originalList;
      this.isFilterApplied = true;
      if (this.mandatory) {
        filteredArray = filteredArray.filter((item) => {
          return this.mandatory === item.Mandatory;
        });
      }
      if (this.selectedEnforceDependent) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEnforceDependent == item.Enforce_Dependent;
        });
      }
      if (this.selectedEnforcementGroup.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          let groups = item.Group_Ids?.split(",") || [];
          return groups.some((group) =>
            this.selectedEnforcementGroup.includes(Number(group))
          );
        });
      }
      this.itemList = filteredArray;
      if (!this.isSearchApplied) {
        this.filterList = filteredArray;
      }
    },
    fetchAccreditationEnforcementGroups() {
      let vm = this;
      vm.EnforcementGroupListLoading = true;
      vm.$apollo
        .query({
          query: LIST_ACCREDITATION_ENFORCEMENT_GROUPS,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response?.data?.listAccreditationEnforcementGroups?.groups.length
          ) {
            vm.EnforcementGroupList =
              response.data.listAccreditationEnforcementGroups.groups;
          } else {
            vm.EnforcementGroupList = [];
          }
          vm.EnforcementGroupListLoading = false;
        })
        .catch(() => {
          vm.EnforcementGroupListLoading = false;
          vm.EnforcementGroupList = [];
        });
    },
    fetchAccreditationCategoryAndType() {
      let vm = this;
      this.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_ACCREDITATION_CATEGORY_TYPES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response?.data?.listAccreditationCategoryAndType
              ?.accreditationCategoryTypes
          ) {
            let { accreditationCategoryTypes } =
              response.data.listAccreditationCategoryAndType;
            vm.originalList = JSON.parse(accreditationCategoryTypes);
            vm.itemList = JSON.parse(accreditationCategoryTypes);
            vm.filterList = JSON.parse(accreditationCategoryTypes);
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.$t("coreHr.retrieving"),
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType.key === this.$t("coreHr.export")) {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onActions(action, item) {
      if (action?.toLowerCase() === "edit") {
        if (this.formAccess.update) {
          this.selectedItem = item;
          this.onClickEdit();
        } else {
          this.showAlert({
            isOpen: true,
            message: this.$t("coreHr.accessDeniedMessage"),
            type: "warning",
          });
        }
      }
    },

    exportReportFile() {
      let exportHeaders = [
        {
          header: this.$t("coreHr.accreditationCategory"),
          key: "Accreditation_Category",
        },
        {
          header: this.$t("coreHr.accreditationType"),
          key: "Accreditation_Type",
        },
        {
          header: this.$t("coreHr.mandatorySelfOnboarding"),
          key: "Mandatory",
        },
        {
          header: this.$t("coreHr.enforceDependent"),
          key: "Enforce_Dependent",
        },
        {
          header: this.$t("coreHr.accreditationEnforcementGroup"),
          key: "Group_Names",
        },
        {
          header: this.$t("coreHr.instruction"),
          key: "Instruction",
        },
        {
          header: this.$t("coreHr.addedBy"),
          key: "Added_By_Name",
        },
        {
          header: this.$t("coreHr.addedOn"),
          key: "Added_On",
        },
        {
          header: this.$t("coreHr.updatedBy"),
          key: "Updated_By_Name",
        },
        {
          header: this.$t("coreHr.updatedOn"),
          key: "Updated_On",
        },
      ];
      let exportData = this.itemList;
      exportData = exportData.map((item) => ({
        ...item,
        Enforce_Dependent: item.Enforce_Dependent ? "Yes" : "No",
        Added_On: item.Added_On
          ? this.formatDate(new Date(item.Added_On + ".000Z"))
          : "",
        Updated_On: item.Updated_On
          ? this.formatDate(new Date(item.Updated_On + ".000Z"))
          : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    onClickAdd() {
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    openViewForm(item) {
      this.isEdit = false;
      this.showAddEditForm = false;
      this.showViewForm = true;
      this.selectedItem = item;
    },
    onClickEdit() {
      this.showViewForm = false;
      this.showAddEditForm = true;
      this.isEdit = true;
    },
    retrieveDocument(fileName) {
      this.selectedFileName = fileName;
      this.openDocumentViewModel = true;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.empDataSetupFormAccess;
        let clickedTab = formAccess.find(
          (form) => this.$t(form.displayFormName) === tab
        );
        if (clickedTab.isVue3) {
          this.$router.push("/core-hr/" + clickedTab.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedTab.url;
        }
      }
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
