/* import the required styles */
@import '@vue-flow/core/dist/style.css';

/* import the default theme (optional) */
@import '@vue-flow/core/dist/theme-default.css';

@import '@vue-flow/controls/dist/style.css';

#layout-container
{
  margin: 0;
  height: 87%;
}

/* -------------- tlt start --------------- */

.ta-tooltip-left-top > .ta-tooltip {
    top: -33px !important;
  }
  
  .ta-tooltip-left-top > .ta-tooltip:before,
  .ta-tooltip-left-top > .ta-tooltip:after {
    bottom: 7px !important;
  }
  /* -------------- tlt end --------------- */
  
  /* -------------- tlb start --------------- */
  
  .ta-tooltip-left-bottom > .ta-tooltip {
    top: -2px !important;
  }
  
  .ta-tooltip-left-bottom > .ta-tooltip:before,
  .ta-tooltip-left-bottom > .ta-tooltip:after {
    bottom:10px !important;
  }
  
  /* -------------- tlb end --------------- */
  
  /* -------------- tl start --------------- */
  
  .ta-tooltip-left > .ta-tooltip {
    cursor: text;
    background: white;
    background-clip: padding-box;
    box-shadow: 0 0px 2px rgba(0, 0, 0, 0.16);
    border: 4px solid rgba(0, 0, 0, 0.16);
    border-radius: 6px;
    position: absolute;
    width: 100px;
    height: fit-content;
    color: black;
    padding: 10px;
    margin-right: 15px;
    right: 100%;
    top: -20px;
    margin-top: -8%;
    left: auto;
    visibility: visible;
    opacity: 1;
    -webkit-transition: opacity 0.5s linear;
    -ms-transition: opacity 0.5s linear;
    -o-transition: opacity 0.5s linear;
    transition: opacity 0.5s linear;
  }
  
  .ta-tooltip-left > .ta-tooltip:before,
  .ta-tooltip-left > .ta-tooltip:after {
    content: "";
    position: absolute;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid transparent;
    bottom: 20px;
    right: -19px;
    left: auto;
  }
  
  .ta-tooltip-left > .ta-tooltip:before {
    border-left: 10px solid rgba(0, 0, 0, 0.16);
    margin-right: -5px;
  }
  
  .ta-tooltip-left > .ta-tooltip:after {
    border-left: 10px solid white;
    z-index: 1;
  }
  
  .ta-tooltip-left:hover > .ta-tooltip {
    /* // visibility: visible; */
    opacity: 1.6;
  }
  
  /* -------------- tl end --------------- */
  
  /* -------------- tt start ------------- */
  
  .ta-tooltip-top-left > .ta-tooltip {
    left: -340% !important;
  }
  
  .ta-tooltip-top-left > .ta-tooltip:before,
  .ta-tooltip-top-left > .ta-tooltip:after {
    left: 90% !important;
  }
  
  .ta-tooltip-top-right > .ta-tooltip {
    left: 380% !important;
    bottom: 17px !important;
  }
  
  .ta-tooltip-top-right > .ta-tooltip:before,
  .ta-tooltip-top-right > .ta-tooltip:after {
    left: 14% !important;
  }
  
  .ta-tooltip-top > .ta-tooltip {
    cursor: text;
    background: white;
    background-clip: padding-box;
    box-shadow: 0 0px 2px rgba(0, 0, 0, 0.16);
    border: 4px solid rgba(0, 0, 0, 0.16);
    border-radius: 6px;
    position: absolute;
    width: 100px;
    height: fit-content;
    margin-left: -100px;
    color: black;
    left: 49.6%;
    padding: 10px;
    bottom: 100%;
    margin-bottom: 15px;
    visibility: visible;
    opacity: 1;
    -webkit-transition: opacity 0.5s linear;
    -ms-transition: opacity 0.5s linear;
    -o-transition: opacity 0.5s linear;
    transition: opacity 0.5s linear;
  }
  
  .ta-tooltip-top > .ta-tooltip:before,
  .ta-tooltip-top > .ta-tooltip:after {
    content: "";
    position: absolute;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    top: 100%;
    left: 50%;
    margin-left: -10px;
    padding-bottom: 5px;
  }
  
  .ta-tooltip-top > .ta-tooltip:before {
    border-top: 10px solid rgba(0, 0, 0, 0.16);
    margin-top: 4px;
  }
  
  .ta-tooltip-top > .ta-tooltip:after {
    border-top: 10px solid white;
    margin-top: -2px;
    z-index: 1;
  }
  
  .ta-tooltip-top:hover > .ta-tooltip {
    opacity: 1.6;
  }
  
  /* -------------- ta-tooltip-top end --------------- */
  
  /* -------------- ta-tooltip-bottom start ------------- */
  
  .ta-tooltip-bottom > .ta-tooltip {
    cursor: text;
    background: white;
    color: black;
    background-clip: padding-box;
    box-shadow: 0 0px 2px rgba(0, 0, 0, 0.16);
    border: 4px solid rgba(0, 0, 0, 0.16);
    border-radius: 6px;
    position: absolute;
    width: 100px;
    height: fit-content;
    margin-left: -100px;
    left: 49.9%;
    padding: 10px;
    top: 100%;
    margin-top: 15px;
    visibility: visible;
    opacity: 1;
    -webkit-transition: opacity 0.5s linear;
    -ms-transition: opacity 0.5s linear;
    -o-transition: opacity 0.5s linear;
    transition: opacity 0.5s linear;
  }
  
  .ta-tooltip-bottom > .ta-tooltip:before,
  .ta-tooltip-bottom > .ta-tooltip:after {
    content: "";
    position: absolute;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    bottom: 100%;
    left: 50%;
    margin-left: -10px;
  }
  
  .ta-tooltip-bottom > .ta-tooltip:before {
    border-bottom: 10px solid rgba(0, 0, 0, 0.16);
    margin-bottom: 4px;
    padding-top: 5px;
  }
  
  .ta-tooltip-bottom > .ta-tooltip:after {
    border-bottom: 10px solid white;
    margin-bottom: -2px;
    z-index: 1;
  }
  
  .ta-tooltip-bottom:hover > .ta-tooltip {
    opacity: 1.6;
  }
  
  /* -------------- ta-tooltip-bottom end --------------- */
  
  /* -------------- ta-tooltip-right start ------------- */
  
  .ta-tooltip-right > .ta-tooltip {
    cursor: text;
    background: white;
    background-clip: padding-box;
    box-shadow: 0 0px 2px rgba(0, 0, 0, 0.16);
    border: 4px solid rgba(0, 0, 0, 0.16);
    border-radius: 6px;
    position: absolute;
    width: 100px;
    height: fit-content;
    padding: 10px;
    color: black;
    margin-left: 25px;
    left: 60%;
    top: -20px;
    margin-top: -8%;
    right: auto;
    visibility: visible;
    opacity: 1;
    -webkit-transition: opacity 0.5s linear;
    -ms-transition: opacity 0.5s linear;
    -o-transition: opacity 0.5s linear;
    transition: opacity 0.5s linear;
  }
  
  .ta-tooltip-right > .ta-tooltip:before,
  .ta-tooltip-right > .ta-tooltip:after {
    content: "";
    position: absolute;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 10px solid transparent;
    bottom: 20px;
    /*.tr > i  top'un pozitif değeri*/
    left: -19px;
    right: auto;
  }
  
  .ta-tooltip-right > .ta-tooltip:before {
    border-right: 10px solid rgba(0, 0, 0, 0.16);
    margin-left: -5px;
  }
  
  .ta-tooltip-right > .ta-tooltip:after {
    border-right: 10px solid white;
    z-index: 1;
  }
  
  .ta-tooltip-right:hover > .ta-tooltip {
    opacity: 1.6;
  }
  
  /* -------------- ta-tooltip-right end --------------- */
  
  /* -------------- trt start --------------- */
  
  .ta-tooltip-right-top > .ta-tooltip {
    top: -33px !important;
  }
  
  .ta-tooltip-right-top > .ta-tooltip:before,
  .ta-tooltip-right-top > .ta-tooltip:after {
    bottom: 7px !important;
  }
  
  /* -------------- trt end --------------- */
  
  /* -------------- trb start --------------- */
  
  .ta-tooltip-right-bottom > .ta-tooltip {
    top: -2px !important;
  }
  
  .ta-tooltip-right-bottom > .ta-tooltip:before,
  .ta-tooltip-right-bottom > .ta-tooltip:after {
    bottom: 20px !important;
  }
  
  /* -------------- trb end --------------- */
  
  .ta-tooltip-bottom-left {
    top: 50px !important;
    left: 10px !important;
  }
  
  .ta-tooltip-bottom-right {
    top: 50px !important;
    right: 10px !important;
  }
  
  
  .ta-tooltip-bottom-left > .ta-tooltip {
    margin-left: -25px !important;
  }
  .ta-tooltip-bottom-left > .ta-tooltip:before,
  .ta-tooltip-bottom-left > .ta-tooltip:after {
    left: 10% !important;
  }
  
  
  .ta-tooltip-bottom-right > .ta-tooltip {
    margin-right: -125px !important;
    left:-75px;
  }
  .ta-tooltip-bottom-right > .ta-tooltip:before,
  .ta-tooltip-bottom-right > .ta-tooltip:after {
    margin-left: 68px !important;
  }
  
  
  .ta-tooltip-right-right {
    top: 5px !important;
    left: 20px !important;
  }

  /* custom css start  for workflow */

  .close_icon {
    position: absolute;
    top: -0.7rem;
    right: -0.7rem;
    background-color: red;
    border-radius: 10px;
    height: 0.7rem;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 0.7rem;
    cursor: pointer;
    visibility: hidden;
    transition: visibility 0.1ms ease-in-out;
}

.close_icons {
    position: absolute;
    top: -0.7rem;
    right: -0.7rem;
    background-color: red;
    border-radius: 10px;
    height: 0.7rem;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 0.7rem;
    cursor: pointer;
    visibility: hidden;
    transition: visibility 0.1ms ease-in-out;
}

.nodrag:hover .edgebutton{
  opacity: 1 !important;
}

.edgebutton{
    border-radius:999px;
    cursor:pointer;
    width: 20px;
    height: 20px;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.1;
}

.edgebutton:hover{
    width: 20px;
    transition:all ease .5s;
    box-shadow:0 0 0 0.5px #ff010180,0 0 0 2px #ffcbca
}
.header_text {
  font-size: 8px;
  text-transform: capitalize;
  margin-left: 0.4rem;
}

.workflow_input{
  margin-left: 2px !important;
}

.workflow_input input{
  padding: 0px 5px !important;
  font-size: 9px !important;
  min-width: 100px !important;
  min-height: 20px !important;
}
