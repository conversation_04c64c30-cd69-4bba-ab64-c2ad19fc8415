<template>
  <AppFetchErrorScreen
    v-if="
      (!originalList || originalList.length === 0) && !checkFormAccess('add')
    "
    key="no-data-screen"
    :isSmallImage="true"
    image-name=""
  >
    <template #contentSlot>
      <div style="max-width: 80%">
        <v-row style="background: white" class="rounded-lg pa-5 mb-4">
          <v-col cols="12">
            <NotesCard
              notes="You have the autonomy to add, edit, delete, and view your own timesheet records for any given week based on access you have."
              backgroundColor="transparent"
              class="mb-4"
            ></NotesCard>
            <NotesCard
              notes=" Easily switch between weeks to check and manage your past or upcoming timesheets. Whether you need to review last week's hours or plan for the next, the flexibility is in your hands."
              backgroundColor="transparent"
              class="mb-4"
            ></NotesCard>
          </v-col>
          <v-col cols="12" class="d-flex align-center justify-center mb-4">
            <v-card
              class="my-2"
              :size="isMobileView ? 'small' : 'default'"
              rounded="lg"
              color="white"
              style="width: 340px"
            >
              <v-icon
                color="primary"
                size="17"
                class="mx-2"
                @click="$emit('fetch-prev-week')"
              >
                fa fa-chevron-left
              </v-icon>
              <v-icon
                color="primary"
                size="17"
                class="mx-2"
                @click="$emit('fetch-next-week')"
              >
                fa fa-chevron-right
              </v-icon>
              <v-menu
                v-model="openDateMenu"
                transition="scale-transition"
                :close-on-content-click="false"
              >
                <template v-slot:activator="{ props }">
                  <v-btn
                    v-bind="props"
                    rounded="lg"
                    variant="flat"
                    class="mx-n1"
                  >
                    <v-icon color="grey" size="14" class="pr-1"
                      >fas fa-calendar-alt</v-icon
                    >{{ selectedWeekRange }}
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item>
                    <datepicker
                      inline
                      :format="orgDateFormat"
                      v-model="weekEndDate"
                      style="min-width: 100%"
                      :disabled-dates="{
                        dates: disabledDatesArray,
                      }"
                      @input="onChangeWeekRange"
                      @changed-month="onChangeMonth"
                      @changed-year="onChangeYear"
                    ></datepicker>
                  </v-list-item>
                </v-list>
              </v-menu>
              <v-tooltip location="bottom">
                <template v-slot:activator="{ props }">
                  <v-icon
                    v-if="!isCurrentWeek"
                    v-bind="props"
                    color="primary"
                    size="13"
                    @click="$emit('fetch-current-week')"
                    >fas fa-redo-alt</v-icon
                  >
                </template>
                <div>This week</div>
              </v-tooltip>
            </v-card>
            <v-btn
              rounded="lg"
              color="transparent"
              variant="flat"
              class="ml-2 mt-1"
              :size="isMobileView ? 'small' : 'default'"
              @click="$emit('fetch-current-week')"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </template>
  </AppFetchErrorScreen>
  <AppFetchErrorScreen
    v-else-if="searchValue && itemList.length == 0"
    key="no-results-screen"
    main-title="There are no timesheets are matched for the selected filters/searches."
    image-name="common/no-records"
  >
    <template #contentSlot>
      <div style="max-width: 80%">
        <v-row class="rounded-lg pa-5 mb-4">
          <v-col cols="12" class="d-flex align-center justify-center mb-4">
            <v-btn
              color="primary"
              variant="elevated"
              class="ml-4 mt-1"
              rounded="lg"
              :size="windowWidth <= 960 ? 'small' : 'default'"
              @click="resetSearch()"
            >
              Reset Filter/Search
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </template>
  </AppFetchErrorScreen>
  <div v-else>
    <div
      class="d-flex flex-wrap align-center my-2"
      :class="isMobileView ? 'flex-column' : ''"
      style="justify-content: space-between"
    >
      <div
        class="d-flex align-center flex-wrap"
        :class="isMobileView ? 'justify-center' : ''"
      >
        <v-card
          class="my-2"
          :size="isMobileView ? 'small' : 'default'"
          rounded="lg"
          style="width: 340px"
          color="white"
        >
          <v-icon
            v-if="action !== 'approval'"
            color="primary"
            size="17"
            class="mx-2"
            @click="$emit('fetch-prev-week')"
          >
            fa fa-chevron-left
          </v-icon>
          <v-icon
            v-if="action !== 'approval'"
            color="primary"
            size="17"
            class="mx-2"
            @click="$emit('fetch-next-week')"
          >
            fa fa-chevron-right
          </v-icon>
          <v-menu
            v-model="openDateMenu"
            transition="scale-transition"
            :close-on-content-click="false"
          >
            <template v-slot:activator="{ props }">
              <v-btn v-bind="props" rounded="lg" variant="flat" class="mx-n1">
                <v-icon color="grey" size="14" class="pr-1"
                  >fas fa-calendar-alt</v-icon
                >{{ selectedWeekRange }}
              </v-btn>
            </template>
            <v-list>
              <v-list-item>
                <datepicker
                  inline
                  :format="orgDateFormat"
                  v-model="weekEndDate"
                  style="min-width: 100%"
                  :disabled-dates="{
                    dates: disabledDatesArray,
                  }"
                  @input="onChangeWeekRange"
                  @changed-month="onChangeMonth"
                  @changed-year="onChangeYear"
                ></datepicker>
              </v-list-item>
            </v-list>
          </v-menu>
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <v-icon
                v-if="!isCurrentWeek && action !== 'approval'"
                v-bind="props"
                color="primary"
                size="13"
                @click="$emit('fetch-current-week')"
                >fas fa-redo-alt</v-icon
              >
            </template>
            <div>This week</div>
          </v-tooltip>
        </v-card>
        <div v-if="itemList.length > 0" class="d-flex align-center">
          <div class="d-flex align-center pl-2">
            <div
              style="border: 1px grey; width: 15px; height: 15px"
              class="weekOff-badge mr-1"
            ></div>
            WeekOff
          </div>
          <div class="d-flex align-center pl-2">
            <div
              style="border: 1px red; width: 15px; height: 15px"
              class="leave-badge mr-1"
            ></div>
            Leave
          </div>
          <div class="d-flex align-center pl-2">
            <div
              style="border: 1px amber; width: 15px; height: 15px"
              class="compOff-badge mr-1"
            ></div>
            CompOff
          </div>
          <div class="d-flex align-center pl-2">
            <div
              style="border: 1px amber; width: 15px; height: 15px"
              class="holiday-badge mr-1"
            ></div>
            Holiday
          </div>
        </div>
      </div>
      <div
        class="d-flex align-center"
        :class="isMobileView ? 'justify-center' : 'justify-end'"
      >
        <div v-if="itemList.length > 0 && approvalStatus">
          <v-menu
            v-if="canEdit"
            v-model="openStatusMenu"
            transition="scale-transition"
          >
            <template v-slot:activator="{ props }">
              <v-btn v-bind="props" rounded="lg">
                <v-tooltip location="bottom">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      v-if="returnedComment"
                      v-bind="returnedComment ? props : ''"
                      color="blue"
                      class="px-3"
                      >far fa-comment-alt</v-icon
                    >
                  </template>
                  <div>{{ returnedComment }}</div>
                </v-tooltip>
                Status:
                <span
                  :class="
                    approvalStatus === 'Applied'
                      ? 'text-blue'
                      : approvalStatus === 'Returned'
                      ? 'text-amber'
                      : 'text-primary'
                  "
                  >{{ approvalStatus }}</span
                >
                <v-icon class="pl-3" size="14" v-if="openStatusMenu"
                  >fas fa-chevron-up</v-icon
                >
                <v-icon class="pl-3" size="14" v-else
                  >fas fa-chevron-down</v-icon
                >
              </v-btn>
            </template>
            <v-list>
              <v-list-item @click="onStatusUpdate()">
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <v-list-item-title
                      v-bind="props"
                      class="pa-3"
                      :class="{
                        'pink-lighten-5': isHovering,
                      }"
                      >{{
                        approvalStatus === "Applied"
                          ? "Withdraw Submission"
                          : "Submit For Approval"
                      }}</v-list-item-title
                    >
                  </template>
                </v-hover>
              </v-list-item>
            </v-list>
          </v-menu>
          <v-btn v-else rounded="lg" variant="flat" style="cursor: text">
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  v-if="returnedComment"
                  v-bind="returnedComment ? props : ''"
                  color="blue"
                  class="px-3"
                  >far fa-comment-alt</v-icon
                >
              </template>
              <div>{{ returnedComment }}</div>
            </v-tooltip>
            Status:
            <span
              :class="
                approvalStatus === 'Applied'
                  ? 'text-blue'
                  : approvalStatus === 'Returned'
                  ? 'text-amber'
                  : approvalStatus === 'Approved'
                  ? 'text-green'
                  : approvalStatus === 'Rejected'
                  ? 'text-red'
                  : 'text-primary'
              "
              >{{ approvalStatus }}</span
            >
          </v-btn>
        </div>
        <v-btn
          color="transparent"
          class="ml-1 mt-1"
          variant="flat"
          size="small"
          @click="$emit('refetch-list')"
          ><v-icon color="grey">fas fa-redo-alt</v-icon></v-btn
        >
        <v-menu
          v-if="itemList.length > 0"
          v-model="openMoreMenu"
          transition="scale-transition"
        >
          <template v-slot:activator="{ props }">
            <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
              <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
              <v-icon v-else>fas fa-caret-up</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="action in moreActions"
              :key="action.key"
              @click="onMoreAction(action.key)"
            >
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <v-list-item-title
                    v-bind="props"
                    class="pa-3"
                    :class="{
                      'pink-lighten-5': isHovering,
                    }"
                    ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                    >{{ action.key }}</v-list-item-title
                  >
                </template>
              </v-hover>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </div>
    <div
      :style="'overflow: scroll; height: ' + $store.getters.getTableHeight(320)"
    >
      <v-form ref="addEditTimesheetsForm">
        <table>
          <thead>
            <tr>
              <th
                class="text-black font-weight-regular"
                style="background: #e1e1e1"
                v-for="header in listHeaders"
                :key="header"
              >
                {{ header.day }}
                <div
                  class="text-caption"
                  :class="{
                    'bg-orange text-white rounded-lg text-center':
                      header.isCurrentDay,
                  }"
                  style="width: 70px"
                >
                  {{ header.date }}
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in itemList"
              :key="index + 'tax-relief-declarations'"
              style="height: 50px; border-bottom: 1px solid #e7e7e4"
            >
              <td class="text-body-2" style="width: 150px">
                <CustomSelect
                  v-if="item.isAdd"
                  :items="projectList"
                  label=""
                  :itemSelected="
                    item.Project_Id ? parseInt(item.Project_Id) : null
                  "
                  :rules="[required(projectLabel, item.Project_Id)]"
                  variantType="outlined"
                  density="compact"
                  itemValue="projectId"
                  itemTitle="projectName"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :isLoading="projectListLoading"
                  :noDataText="
                    projectListLoading ? 'Loading...' : 'No data available'
                  "
                  @selected-item="
                    onChangeCustomSelectField($event, index, 'Project_Id')
                  "
                ></CustomSelect>
                <p v-else class="text-body-2 font-weight-regular">
                  {{ checkNullValue(item.Project_Name) }}
                </p>
              </td>
              <td class="text-body-2" style="width: 150px">
                <CustomSelect
                  v-if="item.isAdd"
                  :items="activityList"
                  label=""
                  :itemSelected="
                    item.Project_Activity_Id
                      ? parseInt(item.Project_Activity_Id)
                      : null
                  "
                  :rules="[required('Activity', item.Project_Activity_Id)]"
                  variantType="outlined"
                  density="compact"
                  itemValue="projectActivityId"
                  itemTitle="activityName"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :isLoading="activityListLoading"
                  :noDataText="
                    activityListLoading ? 'Loading...' : 'No data available'
                  "
                  @selected-item="
                    onChangeCustomSelectField(
                      $event,
                      index,
                      'Project_Activity_Id'
                    )
                  "
                ></CustomSelect>
                <p v-else class="text-body-2 font-weight-regular">
                  {{ checkNullValue(item.Activity_Name) }}
                </p>
              </td>
              <td
                v-for="(weekDay, windex) in weekDays"
                :key="weekDays + windex"
                class="text-body-2"
                style="width: 100px"
                :class="
                  !empAdditionalDetails['Day' + (windex + 1)]
                    ? ''
                    : empAdditionalDetails['Day' + (windex + 1)].includes(
                        'Week Off'
                      )
                    ? 'weekOff-cell'
                    : empAdditionalDetails['Day' + (windex + 1)].includes(
                        'Leave'
                      )
                    ? 'leave-cell'
                    : empAdditionalDetails['Day' + (windex + 1)].includes(
                        'Comp Off'
                      )
                    ? 'compOff-cell'
                    : empAdditionalDetails['Day' + (windex + 1)].includes(
                        'Holiday'
                      )
                    ? 'holiday-cell'
                    : ''
                "
              >
                <v-hover>
                  <template v-slot:default="{ isHovering = false, props }">
                    <div
                      v-bind="props"
                      class="cursor-pointer"
                      style="width: 100%"
                    >
                      <div
                        v-if="item['Day' + (windex + 1) + 'Edit']"
                        class="d-flex align-center"
                      >
                        <v-text-field
                          ref="textField"
                          variant="outlined"
                          density="compact"
                          placeholder="eg.1h 30m"
                          style="width: 100%; height: 50px"
                          v-model.trim="
                            item['Day' + (windex + 1)].totalFormatted
                          "
                          :rules="[
                            item['Day' + (windex + 1)].totalFormatted
                              ? timeFormatValidation(
                                  item['Day' + (windex + 1)].totalFormatted
                                )
                              : true,
                          ]"
                          @blur="dayUpdate(item, windex + 1, index)"
                        ></v-text-field>
                        <v-progress-circular
                          v-if="item['Day' + (windex + 1) + 'Loading']"
                          color="blue-lighten-2"
                          indeterminate
                          size="25"
                          class="mt-n2 pl-1"
                        ></v-progress-circular>
                      </div>
                      <div
                        v-else
                        class="d-flex align-center"
                        style="justify-content: space-between"
                      >
                        <v-tooltip location="bottom">
                          <template v-slot:activator="{ props }">
                            <p
                              v-bind="!item.Project_Activity_Id ? props : ''"
                              class="text-body-2 text-primary"
                              @click="
                                !item.Project_Activity_Id
                                  ? {}
                                  : item['Day' + (windex + 1)].details ||
                                    timeSlotEnabled
                                  ? onDayDetailsClick(
                                      item['Day' + (windex + 1)],
                                      item,
                                      windex + 1,
                                      index
                                    )
                                  : canEdit
                                  ? onDayClick(index, windex)
                                  : {}
                              "
                              style="width: 90%; height: 100%"
                              :style="
                                !item.Project_Activity_Id
                                  ? 'cursor: not-allowed !important'
                                  : item['Day' + (windex + 1)].details ||
                                    timeSlotEnabled
                                  ? 'cursor: pointer !important'
                                  : canEdit
                                  ? 'cursor: pointer !important'
                                  : 'cursor: not-allowed !important'
                              "
                            >
                              {{
                                item["Day" + (windex + 1)].details
                                  ? formTotalHoursFromDetails(
                                      item["Day" + (windex + 1)].details
                                    )
                                  : item["Day" + (windex + 1)].totalFormatted
                                  ? item["Day" + (windex + 1)].totalFormatted
                                  : "-"
                              }}
                            </p>
                          </template>
                          <div>
                            Please select {{ projectLabelSmallCase }} & activity
                          </div>
                        </v-tooltip>
                        <v-tooltip
                          v-if="
                            isHovering &&
                            item.Project_Activity_Id &&
                            (canEdit ||
                              (item['Day' + (windex + 1)].details &&
                                item['Day' + (windex + 1)].details
                                  .totalhoursIndividual &&
                                item['Day' + (windex + 1)].details
                                  .totalhoursIndividual.length > 0))
                          "
                          location="bottom"
                        >
                          <template v-slot:activator="{}">
                            <v-icon
                              size="13"
                              color="primary"
                              @click="
                                onDayDetailsClick(
                                  item['Day' + (windex + 1)],
                                  item,
                                  windex + 1,
                                  index
                                )
                              "
                              >{{
                                !canEdit ? "fas fa-eye" : "fas fa-edit"
                              }}</v-icon
                            >
                          </template>
                          <div>
                            {{ canEdit ? "Add" : "View" }} multiple time entries
                            with task description
                          </div>
                        </v-tooltip>
                      </div>
                    </div>
                  </template>
                </v-hover>
              </td>
              <td class="text-body-2" style="width: 150px">
                <div
                  class="d-flex align-center"
                  style="justify-content: space-between"
                >
                  <p class="text-body-2 font-weight-regular">
                    {{
                      projectLevelTotal[index] ? projectLevelTotal[index] : "-"
                    }}
                  </p>
                  <ActionMenu
                    v-if="canDelete && !item.clone && !item.isAdd"
                    @selected-action="onDelete(item)"
                    :actions="['Delete']"
                    :accessRights="checkAccess()"
                    iconColor="grey"
                  ></ActionMenu>
                </div>
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr v-if="canAdd && showAdd" style="height: 50px" class="mb-12">
              <v-btn
                variant="elevated"
                color="primary"
                density="comfortable"
                class="mt-2"
                @click="onAddNewRecord"
                ><v-icon class="pr-2" size="10">fas fa-plus</v-icon>
                <span class="text-body-2">Add New</span>
              </v-btn>
              <!-- <v-btn
                v-if="isCurrentWeek && itemList.length == 0"
                variant="text"
                color="primary"
                density="comfortable"
                class="mt-2"
                @click="addFromPrevWeek"
                ><v-icon class="pr-2" size="13">fas fa-sign-in-alt</v-icon>
                <span class="text-body-2">Add all rows from last week</span>
              </v-btn> -->
            </tr>
          </tbody>
          <tfoot
            v-if="itemList.length > 0"
            class="text-black font-weight-regular"
            style="height: 50px"
          >
            <tr>
              <td style="background: #e1e1e1">Total</td>
              <td style="background: #e1e1e1"></td>
              <td
                v-for="(weekDay, windex) in weekDays"
                :key="weekDays + windex"
                style="background: #e1e1e1"
              >
                {{ dayLevelTotal["Day" + (windex + 1)] }}
              </td>
              <td style="background: #e1e1e1">{{ allProjectTotal }}</td>
            </tr>
          </tfoot>
        </table>
      </v-form>
    </div>
    <!-- <div class="pt-1" v-if="itemList.length > 0">
      1 to {{ itemList.length }} of {{ itemList.length }}
    </div> -->
  </div>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    iconName="fas fa-trash"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="deleteTimeSheet()"
  ></AppWarningModal>
  <AppLoading v-if="isLoading"></AppLoading>
  <v-dialog v-model="openDetailsModal" max-width="1200">
    <v-card min-width="300" class="rounded-lg pa-4">
      <div class="d-flex justify-end">
        <v-icon
          color="primary"
          class="pr-4 pt-4 font-weight-bold"
          @click="closeDetailsModal()"
          >fas fa-times</v-icon
        >
      </div>
      <v-card-title class="mt-n4">
        <div class="text-primary font-weight-medium">
          <v-progress-circular
            model-value="100"
            color="purple"
            :size="22"
            class="mt-n1 mr-1"
          ></v-progress-circular>
          {{ !canEdit ? "Timesheet Details" : "Add/Update Timesheet Details" }}
        </div>
      </v-card-title>
      <v-card-text>
        <v-alert
          v-model="showDetailsMaxHoursValidation"
          text
          density="compact"
          type="warning"
          class="mb-0"
          closable
        >
          Total hours should not exceed
          {{ empAdditionalDetails.maxHours }} hours(sum of regular and overtime
          hours) per day. Please review and adjust accordingly.
        </v-alert>
        <v-card
          v-for="(details, dIndex) in editedDetailsArray"
          :key="'details-' + dIndex"
          class="mb-3 pa-2 rounded-lg"
          elevation="0"
          color="#FDFEFF"
        >
          <v-form :ref="'timesheetsDetailsForm' + dIndex">
            <v-row>
              <v-col
                v-if="labelList[308] && labelList[308].Field_Visiblity == 'Yes'"
                cols="12"
                sm="6"
                md="2"
              >
                <CustomSelect
                  v-if="labelList[308].Field_Visiblity == 'Yes'"
                  class="mt-4"
                  :items="roomList"
                  :label="labelList['308'].Field_Alias"
                  :itemSelected="
                    details.roomId ? parseInt(details.roomId) : null
                  "
                  :rules="[
                    labelList['308'].Mandatory_Field === 'Yes'
                      ? required(
                          `${labelList['308'].Field_Alias}`,
                          details.roomId
                        )
                      : true,
                  ]"
                  variant="solo"
                  itemValue="Room_Id"
                  itemTitle="Room_No"
                  :isRequired="
                    labelList['308'].Mandatory_Field === 'Yes' ? true : false
                  "
                  :isAutoComplete="true"
                  :isLoading="roomListLoading"
                  :noDataText="
                    roomListLoading ? 'Loading...' : 'No data available'
                  "
                  @selected-item="onChangeRoomId($event, dIndex)"
                ></CustomSelect>
                <v-btn
                  v-if="
                    labelList[308] && labelList[308].Field_Visiblity == 'Yes'
                  "
                  class="mt-n2"
                  color="primary"
                  variant="text"
                  @click="redirectToRoomsForm()"
                >
                  <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
                  {{ labelList["308"].Field_Alias }}</v-btn
                >
              </v-col>
              <v-col cols="12" sm="6" md="2" v-if="timeSlotEnabled">
                <v-text-field
                  label="Start Time"
                  v-model="details.startTime"
                  type="time"
                  pattern="[0-9]{2}:[0-9]{2}"
                  variant="solo"
                  :rules="[
                    required('Start Time', details.startTime),
                    validateStartTime(dIndex),
                  ]"
                  class="mt-4"
                  :disabled="!canEdit"
                  @update:model-value="findTotalHours(dIndex)"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="2" v-if="timeSlotEnabled">
                <v-text-field
                  label="End Time"
                  v-model="details.endTime"
                  type="time"
                  pattern="[0-9]{2}:[0-9]{2}"
                  variant="solo"
                  :rules="[
                    required('End Time', details.endTime),
                    validateEndTime(dIdnex),
                  ]"
                  class="mt-4"
                  :disabled="!canEdit"
                  @update:model-value="findTotalHours(dIndex)"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="2">
                <v-text-field
                  ref="textField"
                  variant="solo"
                  label="Total Hours"
                  placeholder="eg.1h 30m"
                  v-model.trim="details.totalHours"
                  :rules="
                    !timeSlotEnabled
                      ? [
                          required('Total Hours', details.totalHours),
                          timeFormatValidation(details.totalHours),
                        ]
                      : [true]
                  "
                  :disabled="!canEdit || timeSlotEnabled"
                  class="mt-4"
                  @update:model-value="validateDetailsTotalHours"
                ></v-text-field>
              </v-col>
              <v-col
                cols="11"
                sm="5"
                :md="
                  timeSlotEnabled ||
                  (labelList[308] && labelList[308].Field_Visiblity == 'Yes')
                    ? 4
                    : 8
                "
              >
                <v-textarea
                  v-model="details.notes"
                  variant="solo"
                  auto-grow
                  rows="1"
                  :rules="[
                    enforceNotes ? notesRules.required(details.notes) : true,
                    notesRules.regex(details.notes),
                    notesRules.min(details.notes),
                    notesRules.max(details.notes),
                  ]"
                  class="mt-4"
                  :disabled="!canEdit"
                >
                  <template v-slot:label>
                    Notes
                    <span v-if="enforceNotes" style="color: red">*</span>
                  </template>
                </v-textarea>
              </v-col>
              <v-col cols="1" v-if="canEdit">
                <v-icon
                  color="red"
                  size="16"
                  class="mt-8"
                  @click="onDeleteDetails(dIndex)"
                  >fas fa-trash</v-icon
                >
              </v-col>
            </v-row>
          </v-form>
        </v-card>
      </v-card-text>
      <v-card-actions v-if="canEdit">
        <v-btn
          variant="elevated"
          color="primary"
          density="comfortable"
          class="mt-2"
          @click="onAddNewDetails()"
          ><v-icon class="pr-2" size="10">fas fa-plus</v-icon>
          <span class="text-body-2">Add New</span>
        </v-btn>

        <v-spacer></v-spacer>
        <v-btn
          color="primary"
          rounded="lg"
          variant="elevated"
          @click="dayDetailsUpdate()"
        >
          Save
        </v-btn>
      </v-card-actions>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="overlayLoading"
        scrim="#fff"
      >
        <v-progress-circular color="primary" indeterminate size="64">
        </v-progress-circular>
      </v-overlay>
    </v-card>
  </v-dialog>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import {
  checkNullValue,
  decimalToHours,
  convertToDecimal,
  timeToMinutes,
  minutesToDecimalHours,
} from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import {
  ADD_UPDATE_TIMESHEETS,
  DELETE_TIMESHEETS,
  WITHDRAW_APPROVAL,
  SUBMIT_FOR_APPROVAL,
} from "@/graphql/my-team/timesheets";
import {
  LIST_PROJECTS,
  RETRIEVE_PROJECT_ACTIVITIES,
  LIST_ROOMS,
} from "@/graphql/corehr/projectsQueries";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);

export default defineComponent({
  name: "ListTimesheets",

  components: { CustomSelect, ActionMenu, NotesCard, Datepicker },

  emits: [
    "on-change-week-range",
    "refetch-list",
    "fetch-current-week",
    "fetch-prev-week",
    "fetch-next-week",
    "timesheet-added",
    "timesheet-deleted",
    "add-from-prev-week",
    "activities-retrieved",
    "projects-retrieved",
    "timesheet-submitted-approval",
    "timesheet-withdrawn",
  ],
  mixins: [FileExportMixin, validationRules],

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    originalList: {
      type: Array,
      required: true,
      default: () => [],
    },
    formAccess: {
      type: Object,
      required: true,
    },
    weekRange: {
      type: String,
      required: true,
    },
    empAdditionalDetails: {
      type: Object,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      required: true,
    },
    formId: {
      type: Number,
      required: true,
    },
    timeSlotEnabled: {
      type: Number,
      required: true,
    },
    enforceNotes: {
      type: Number,
      required: true,
    },
    action: {
      type: String,
      default: "",
    },
    addCount: {
      type: Number,
      default: 0,
    },
    selectedEmployeeUserDefId: {
      type: String,
      default: "",
    },
    selectedEmployeeName: {
      type: String,
      default: "",
    },
  },

  data: () => ({
    // list
    itemList: [],
    projectLevelTotal: [],
    dayLevelTotal: [],
    allProjectTotal: 0,
    openMoreMenu: false,
    openStatusMenu: false,
    openDateMenu: false,
    weekEndDate: "",
    disabledDatesArray: [],
    changedYear: "",
    // status
    approvalStatus: "",
    returnedComment: "",
    // delete/warning
    deleteItem: {},
    openWarningModal: false,
    selectedActionItem: null,
    // loaders
    isLoading: false,
    // date
    selectedWeekRange: "",
    // form
    projectList: [],
    projectListLoading: false,
    activityList: [],
    activityListLoading: false,
    isRefetchList: false,
    isInvalidHoursEntered: false,
    showValidationAlert: false,
    validationMessages: [],
    // details
    openDetailsModal: false,
    overlayLoading: false,
    selectedDetailsItem: {},
    selectedDetailsItemIndex: 0,
    selectedDetailsDayIndex: "",
    editedDetailsArray: [],
    showDetailsMaxHoursValidation: false,
    havingAccess: {},
    //room
    roomList: [],
    roomListLoading: false,
  }),
  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    projectLabel() {
      return this.$store.state.projectLabel;
    },
    projectLabelSmallCase() {
      let pLabel = this.$store.state.projectLabel;
      return pLabel ? pLabel.toLowerCase() : pLabel;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },

    notesRules() {
      const pattern = /^[\w\.\,\#\+\&\/\-\(\)\:\'\`\?\n ]*$/;
      var rules = {
        required: (value) => !!value || `Notes is required.`,
        min: (value) =>
          !value ||
          value.length >= 5 ||
          `Notes should be more than are equal to 5 characters`,
        max: (value) =>
          !value ||
          value.length <= 500 ||
          `Notes should not exceed 500 characters`,
        regex: (value) => {
          !value ||
            pattern.test(value) ||
            "Only alphanumeric, spaces, and symbols(. , # + & / - ( ) : ' ` ?) are allowed.";
        },
      };
      return rules;
    },

    orgDateFormat() {
      return this.$store.state.orgDetails.orgDateFormat;
    },

    weekDays() {
      const start = moment(
        this.selectedStartEndDates.start,
        this.orgDateFormat
      );
      const end = moment(this.selectedStartEndDates.end, this.orgDateFormat);
      const dateArray = [];
      const currentDate = moment();
      let dayIndex = 0;
      // Iterate through the date range
      for (
        let current = start.clone();
        current.isSameOrBefore(end);
        current.add(1, "days")
      ) {
        dayIndex += 1;
        const dateObject = {
          key: "Day" + dayIndex,
          date: current.format("DD MMM"), // Formatted date in "DD MMM" format,
          day: current.format("ddd"), // Three-letter abbreviation for the day (e.g., Sun, Mon)
          isCurrentDay: current.isSame(currentDate, "day"), // Check if it's the current day
        };
        dateArray.push(dateObject);
      }
      return dateArray;
    },

    listHeaders() {
      let wDays = this.weekDays,
        a1 = [{ day: this.projectLabel }, { day: "Activity" }],
        a2 = [{ day: "Total" }],
        finalArray = a1.concat(wDays);
      finalArray = finalArray.concat(a2);
      return finalArray;
    },

    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },

    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },

    selectedStartEndDates() {
      const date = this.selectedWeekRange;
      const dateSplit = date.split(" to ");
      const startDate = dateSplit[0];
      const endDate = dateSplit[1];
      return { start: startDate, end: endDate };
    },

    isCurrentWeek() {
      const startOfWeek = moment().clone().startOf("week");
      const endOfWeek = moment().clone().endOf("week");
      const rangeStart = moment(
        this.selectedStartEndDates.start,
        this.orgDateFormat
      );
      const rangeEnd = moment(
        this.selectedStartEndDates.end,
        this.orgDateFormat
      );

      return (
        rangeStart.isSameOrAfter(startOfWeek) &&
        rangeEnd.isSameOrBefore(endOfWeek)
      );
    },

    showAdd() {
      const isAddSetAsTrue = this.itemList.some((obj) => obj.isAdd === true);
      return !isAddSetAsTrue;
    },

    canAdd() {
      return (
        this.checkFormAccess("add") &&
        this.approvalStatus !== "Approved" &&
        this.action !== "approval"
      );
    },

    canEdit() {
      return (
        (this.checkFormAccess("add") || this.checkFormAccess("update")) &&
        this.approvalStatus !== "Approved" &&
        this.approvalStatus !== "Rejected" &&
        this.action !== "approval"
      );
    },

    canDelete() {
      return (
        this.checkFormAccess("delete") &&
        this.approvalStatus !== "Approved" &&
        this.approvalStatus !== "Rejected" &&
        this.action !== "approval"
      );
    },

    newItem() {
      let weekEndDate = moment(this.weekEndDate, this.orgDateFormat).format(
        "YYYY-MM-DD"
      );
      let addArray = this.itemList.filter((el) => el.isAdd == true);
      let projectId = null,
        projectActivityId = null;
      if (addArray && addArray.length > 0) {
        projectId = addArray[0].Project_Id;
        projectActivityId = addArray[0].Project_Activity_Id;
      }
      return {
        isAdd: true,
        Request_Id:
          this.itemList && this.itemList.length > 0
            ? this.itemList[0].Request_Id
            : 0,
        Employee_Id: this.selectedEmpId,
        Week_Ending_Date: weekEndDate,
        Timesheet_Id: 0,
        Project_Id: projectId,
        Timesheet_Type: "Regular",
        Project_Activity_Id: projectActivityId,
        Day1: { totalFormatted: null },
        Day2: { totalFormatted: null },
        Day3: { totalFormatted: null },
        Day4: { totalFormatted: null },
        Day5: { totalFormatted: null },
        Day6: { totalFormatted: null },
        Day7: { totalFormatted: null },
        Day1Edit: false,
        Day2Edit: false,
        Day3Edit: false,
        Day4Edit: false,
        Day5Edit: false,
        Day6Edit: false,
        Day7Edit: false,
        Description: null,
        Day1Loading: false,
        Day2Loading: false,
        Day3Loading: false,
        Day4Loading: false,
        Day5Loading: false,
        Day6Loading: false,
        Day7Loading: false,
      };
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    loginEmployeeUserDefId() {
      return this.$store.state.orgDetails.userDetails.userDefinedEmployeeId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.selectedWeekRange = this.weekRange;
    let weekEndDate =
      this.selectedWeekRange && this.selectedWeekRange.includes("to")
        ? this.selectedWeekRange.split(" to ")
        : this.selectedWeekRange;
    this.weekEndDate = weekEndDate =
      weekEndDate && weekEndDate.length >= 2
        ? weekEndDate[1]
        : weekEndDate && weekEndDate.length >= 1
        ? weekEndDate[0]
        : "";
    if (this.items.length) {
      this.approvalStatus = this.items[0].Approval_Status;
      this.returnedComment =
        this.items[0].Approval_Status === "Returned" &&
        this.items[0].Returned_Comment
          ? this.items[0].Returned_Comment
          : "";
      this.itemList = JSON.parse(JSON.stringify(this.items));
      this.onApplySearch();
    } else {
      if (this.addCount > 0) {
        this.onAddNewRecord();
      }
    }
    this.findNonSaturdayDatesInMonth(
      moment(this.weekEndDate, this.orgDateFormat)
    );
  },

  watch: {
    items(val) {
      this.approvalStatus =
        this.items.length > 0 ? this.items[0].Approval_Status : "Draft";
      this.returnedComment =
        this.items.length > 0 &&
        this.items[0].Approval_Status === "Returned" &&
        this.items[0].Returned_Comment
          ? this.items[0].Returned_Comment
          : "";
      this.itemList = JSON.parse(JSON.stringify(val));
      this.onApplySearch();
    },
    searchValue() {
      this.onApplySearch();
    },
    openDateMenu() {
      this.findNonSaturdayDatesInMonth(moment());
    },
  },

  methods: {
    checkNullValue,
    decimalToHours,
    redirectToRoomsForm() {
      window.open(this.baseUrl + "v3/core-hr/projects", "_blank");
    },
    checkAccess() {
      this.havingAccess["delete"] = this.canDelete ? 1 : 0;
      return this.havingAccess;
    },
    findNonSaturdayDatesInMonth(dateString = "2024-03-30") {
      const currentDate = moment(dateString);
      const month = currentDate.month(); // Get the month
      const year = currentDate.year(); // Get the year
      const daysInMonth = moment(
        `${year}-${month + 1}`,
        "YYYY-MM"
      ).daysInMonth(); // Get the number of days in the month

      const nonSaturdayDates = [];

      // Iterate over all dates in the month
      for (let i = 1; i <= daysInMonth; i++) {
        const date = moment(`${year}-${month + 1}-${i}`, "YYYY-MM-DD");
        // Check if the day is not Saturday (6 represents Saturday in Moment.js)
        if (date.day() !== 6) {
          nonSaturdayDates.push(new Date(date));
        }
      }
      this.disabledDatesArray = nonSaturdayDates;
    },
    checkFormAccess(accessName) {
      if (this.formAccess) {
        if (this.formId == 23) {
          const { admin, isManager } = this.formAccess;
          if ((this.formAccess[accessName] && admin === "admin") || isManager) {
            return true;
          }
          return false;
        } else {
          if (this.formAccess[accessName]) {
            return true;
          }
          return false;
        }
      }
      return false;
    },
    onChangeWeekRange() {
      this.openDateMenu = false;
      let prevSunday = moment(this.weekEndDate)
        .subtract("6", "days")
        .format(this.orgDateFormat);
      this.selectedWeekRange =
        prevSunday +
        " to " +
        moment(this.weekEndDate).format(this.orgDateFormat);
      this.$emit("on-change-week-range", this.selectedWeekRange);
    },
    onChangeMonth(monthDate) {
      if (monthDate && Object.keys(monthDate).length > 0) {
        const monthName = monthDate.month;
        const monthNumber = moment().month(monthName).format("M");
        const formattedDate = moment(`${monthNumber}-01-${this.changedYear}`);
        this.findNonSaturdayDatesInMonth(formattedDate);
      } else {
        this.findNonSaturdayDatesInMonth(monthDate);
      }
    },
    onChangeYear(year) {
      this.changedYear = year.year;
    },
    onChangeCustomSelectField(value, index, field) {
      this.itemList[index][field] = value;
      if (field === "Project_Id") {
        this.itemList[index]["Project_Activity_Id"] = null;
        this.fetchActivitiesBasedOnProject(value);
      }
    },
    onChangeRoomId(value, index) {
      this.editedDetailsArray[index].roomId = value;
    },
    onDayClick(index, dayIndex) {
      let snackbarData = {
        isOpen: false,
      };
      this.showAlert(snackbarData);
      if (this.isInvalidHoursEntered) {
        this.isInvalidHoursEntered = false;
        this.itemList = JSON.parse(JSON.stringify(this.items));
      }
      let dayKeys = ["Day1", "Day2", "Day3", "Day4", "Day5", "Day6", "Day7"];
      // Update isEdit property to false for all objects in the same array
      for (let i = 0; i < this.itemList.length; i++) {
        for (let dKey of dayKeys) {
          if (this.itemList[i]) {
            if (this.itemList[i][dKey + "Edit"]) {
              this.itemList[i] = this.items[i] ? this.items[i] : this.newItem;
            }
            this.itemList[i][dKey + "Edit"] = false;
          }
        }
      }
      this.itemList[index]["Day" + (dayIndex + 1) + "Edit"] = true;
    },

    onApplySearch(itemValues) {
      let val = this.searchValue;
      let itemList = itemValues ? itemValues : this.items;
      if (!val) {
        this.itemList = JSON.parse(JSON.stringify(itemList));
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = itemList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = JSON.parse(JSON.stringify(searchItems));
      }
      this.calculateTotalHours();
    },

    resetSearch() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = JSON.parse(JSON.stringify(this.items));
    },

    onMoreAction(actionType) {
      if (actionType === "Export") {
        mixpanel.track("Timesheets-export-click");
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },

    formTotalHoursFromDetails(details) {
      if (
        details &&
        details.totalhoursIndividual &&
        details.totalhoursIndividual.length > 0
      ) {
        const individualHours = details.totalhoursIndividual.map(Number);
        const dayTotal = individualHours.reduce((acc, hour) => acc + hour, 0);
        return this.decimalToHours(dayTotal);
      } else return "-";
    },

    calculateTotalHours() {
      // Initialize project and day level totals
      let projectTotalHours = [];
      let dayTotalHours = {};
      let allProjectTotal = 0;

      // Iterate through the projects in a1 array
      for (const project of this.itemList) {
        let projectTotal = 0;

        // Iterate through the days in the project
        for (let i = 1; i <= 7; i++) {
          const dayKey = `Day${i}`;
          if (project[dayKey] && project[dayKey].totalFormatted) {
            const dayTotal = Number(
              convertToDecimal(project[dayKey].totalFormatted)
            );
            dayTotalHours[dayKey] = (dayTotalHours[dayKey] || 0) + dayTotal;
            projectTotal += dayTotal;
          } else if (project[dayKey] && project[dayKey].details) {
            const individualHours =
              project[dayKey].details.totalhoursIndividual.map(Number);
            const dayTotal = individualHours.reduce(
              (acc, hour) => acc + hour,
              0
            );
            dayTotalHours[dayKey] = (dayTotalHours[dayKey] || 0) + dayTotal;
            projectTotal += dayTotal;
          }
        }
        allProjectTotal += projectTotal;
        // Update project level total hours in a1 array
        projectTotalHours.push(
          projectTotal ? this.decimalToHours(projectTotal) : 0
        );
      }
      for (let dayTotal in dayTotalHours) {
        dayTotalHours[dayTotal] = this.decimalToHours(dayTotalHours[dayTotal]);
      }
      this.allProjectTotal = this.decimalToHours(allProjectTotal);
      this.projectLevelTotal = projectTotalHours;
      this.dayLevelTotal = dayTotalHours;
    },

    onAddNewRecord() {
      this.itemList.push(this.newItem);
      this.listRooms();
      this.listProjects();
    },

    addFromPrevWeek() {
      this.$emit("add-from-prev-week");
    },

    listRooms() {
      let vm = this;
      vm.roomListLoading = true;
      vm.$apollo
        .query({
          query: LIST_ROOMS,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listRooms) {
            let mainData = response.data.listRooms.rooms;
            vm.roomList = mainData;
          }
          vm.roomListLoading = false;
        })
        .catch(() => {
          vm.roomListLoading = false;
        });
    },

    listProjects() {
      this.projectListLoading = true;
      this.$apollo
        .query({
          query: LIST_PROJECTS,
          client: "apolloClientI",
          variables: {
            formId: this.formId,
            employeeId: this.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data) {
            const { projectDetails } = res.data.listProjectDetails;
            this.projectList =
              projectDetails && projectDetails.length > 0 ? projectDetails : [];
            this.projectList = this.projectList.filter(
              (el) => el.status !== "Closed"
            );
            this.$emit("projects-retrieved", this.projectList);
          }
          this.projectListLoading = false;
        })
        .catch(() => {
          this.projectListLoading = false;
        });
    },

    fetchActivitiesBasedOnProject(projectId) {
      let vm = this;
      vm.activityListLoading = true;
      let weekEndDate = moment(vm.weekEndDate, this.orgDateFormat).format(
        "YYYY-MM-DD"
      );
      vm.$apollo
        .query({
          query: RETRIEVE_PROJECT_ACTIVITIES,
          variables: {
            projectId: parseInt(projectId),
            formId: vm.formId,
            weekendDate: weekEndDate,
          },
          fetchPolicy: "no-cache",
          client: "apolloClientI",
        })
        .then((response) => {
          if (response.data && response.data.retrieveProjectActivities) {
            vm.activityList =
              response.data.retrieveProjectActivities.activityDetails;
            this.$emit("activities-retrieved", this.activityList);
          }
          vm.activityListLoading = false;
        })
        .catch(() => {
          vm.activityListLoading = false;
        });
    },

    onDeleteDetails(indexToRemove) {
      this.editedDetailsArray.splice(indexToRemove, 1);
    },

    onDelete(item) {
      mixpanel.track("Timesheets-delete-triggered");
      this.openWarningModal = true;
      this.selectedActionItem = item;
    },

    // function close the warning modal
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedActionItem = null;
    },

    deleteTimeSheet() {
      let vm = this;
      vm.isLoading = true;
      const { Timesheet_Id, Request_Id } = this.selectedActionItem;
      let variableObj = {
        selfService: vm.formId == 262 ? 1 : 0,
        timesheetId: Timesheet_Id ? Timesheet_Id : 0,
        requestId: Request_Id ? Request_Id : 0,
        parentDelete: vm.itemList.length === 1 ? 1 : 0,
        detailsBytimeId: 0,
      };
      vm.$apollo
        .mutate({
          mutation: DELETE_TIMESHEETS,
          variables: variableObj,
          client: "apolloClientJ",
        })
        .then(() => {
          mixpanel.track("Timesheets-delete-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Timesheets details deleted successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("timesheet-deleted");
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      mixpanel.track("Timesheets-delete-error");
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "timesheet details",
        isListError: false,
      });
    },

    onDayDetailsClick(itemDayDetails, item, dayIndex, itemIndex) {
      let editedDetailsArray = [];
      if (
        itemDayDetails.details &&
        itemDayDetails.details.totalhoursIndividual &&
        itemDayDetails.details.totalhoursIndividual.length > 0
      ) {
        let loopCount = itemDayDetails.details.totalhoursIndividual.length;
        for (let i = 0; i < loopCount; i++) {
          let detailObj = {
            startTime: itemDayDetails.details.Start_Time[i],
            endTime: itemDayDetails.details.End_Time[i],
            notes: itemDayDetails.details.Notes[i],
            totalHours: this.decimalToHours(
              itemDayDetails.details.totalhoursIndividual[i]
            ),
            detailsBytimeId: parseInt(
              itemDayDetails.details.detailsBytimeId[i]
            ),
            roomId:
              itemDayDetails.details.roomId &&
              itemDayDetails.details.roomId.length &&
              itemDayDetails.details.roomId[i]
                ? parseInt(itemDayDetails.details.roomId[i])
                : null,
          };
          editedDetailsArray.push(detailObj);
        }
      } else {
        editedDetailsArray = [
          {
            detailsBytimeId: 0,
            roomId: 0,
            startTime: "",
            endTime: "",
            notes: "",
            totalHours: item["Day" + dayIndex].totalFormatted
              ? item["Day" + dayIndex].totalFormatted
              : "",
          },
        ];
      }
      this.editedDetailsArray = editedDetailsArray;
      this.selectedDetailsDayIndex = dayIndex;
      this.selectedDetailsItem = item;
      this.selectedDetailsItemIndex = itemIndex;
      this.overlayLoading = false;
      this.openDetailsModal = true;
      this.listRooms();
    },

    onAddNewDetails() {
      this.editedDetailsArray.push({
        startTime: "",
        endTime: "",
        notes: "",
        totalHours: "",
        detailsBytimeId: 0,
        roomId: 0,
      });
    },

    closeDetailsModal() {
      this.openDetailsModal = false;
      this.overlayLoading = false;
      this.editedDetailsArray = [];
      this.selectedDetailsDayIndex = 0;
      this.selectedDetailsItemIndex = 0;
      this.selectedDetailsItem = {};
    },

    async dayDetailsUpdate() {
      let item = this.selectedDetailsItem;
      let dayIndex = this.selectedDetailsDayIndex;
      let itemIndex = this.selectedDetailsItemIndex;
      mixpanel.track("Timesheets-details-save-triggered");
      this.isRefetchList = true;
      let details = this.editedDetailsArray,
        dayDetails = [],
        editedDayTotal = 0;
      for (let d = 0; d < details.length; d++) {
        const { valid } = await this.$refs[
          "timesheetsDetailsForm" + d
        ][0].validate();
        if (valid && !this.showDetailsMaxHoursValidation) {
          let dObj = {
            detailsBytimeId: parseInt(details[d].detailsBytimeId),
            roomId: parseInt(details[d].roomId),
            day: dayIndex,
            notes: details[d].notes,
            totalHours: convertToDecimal(details[d].totalHours),
            startTime: details[d].startTime,
            endTime: details[d].endTime,
          };
          editedDayTotal += dObj.totalHours;
          dayDetails.push(dObj);
        }
      }
      if (dayDetails.length == details.length) {
        this.addUpdateTimeSheets(
          item,
          dayIndex,
          editedDayTotal,
          dayDetails,
          itemIndex
        );
      }
    },

    findTotalHours(index) {
      if (
        this.editedDetailsArray[index].startTime &&
        this.editedDetailsArray[index].endTime
      ) {
        const startMinutes = timeToMinutes(
          this.editedDetailsArray[index].startTime
        );
        const endMinutes = timeToMinutes(
          this.editedDetailsArray[index].endTime
        );
        const totalMinutes = endMinutes - startMinutes;
        // Convert total minutes to decimal hours
        const totalMinsInDecimalHours = minutesToDecimalHours(totalMinutes);
        this.editedDetailsArray[index]["totalHours"] = this.decimalToHours(
          totalMinsInDecimalHours
        );
        this.validateDetailsTotalHours();
      }
    },

    validateDetailsTotalHours() {
      this.showDetailsMaxHoursValidation = false;
      let dayTotalHours = {};
      // Iterate through the projects in a1 array
      for (const i in this.itemList) {
        if (i != this.selectedDetailsItemIndex) {
          // Iterate through the days in the project
          for (let k = 1; k <= 7; k++) {
            const dayKey = `Day${k}`;
            if (
              this.itemList[i][dayKey] &&
              this.itemList[i][dayKey].totalFormatted
            ) {
              const dayTotal = Number(
                convertToDecimal(this.itemList[i][dayKey].totalFormatted)
              );
              dayTotalHours[dayKey] = (dayTotalHours[dayKey] || 0) + dayTotal;
            } else if (
              this.itemList[i][dayKey] &&
              this.itemList[i][dayKey].details
            ) {
              const individualHours =
                this.itemList[i][dayKey].details.totalhoursIndividual.map(
                  Number
                );
              const dayTotal = individualHours.reduce(
                (acc, hour) => acc + hour,
                0
              );
              dayTotalHours[dayKey] = (dayTotalHours[dayKey] || 0) + dayTotal;
            }
          }
        }
      }
      let maxHoursLimit = this.empAdditionalDetails.maxHours;
      let totalHoursDecimalSum = 0;
      this.editedDetailsArray.forEach((item) => {
        totalHoursDecimalSum += convertToDecimal(item.totalHours);
      });
      let totalValue =
        dayTotalHours["Day" + this.selectedDetailsDayIndex] +
        totalHoursDecimalSum;
      if (maxHoursLimit && totalValue > maxHoursLimit) {
        this.showDetailsMaxHoursValidation = true;
      }
    },

    validateStartTime(index) {
      if (this.editedDetailsArray[index]) {
        var startTime = this.editedDetailsArray[index].startTime;
        var endTime = this.editedDetailsArray[index].endTime;
        if (startTime && endTime) {
          var startTimeObj = new Date("2000-01-01 " + startTime);
          var endTimeObj = new Date("2000-01-01 " + endTime);
          if (startTimeObj > endTimeObj) {
            return "Start time should be less than end time.";
          }
        }
      }
      return true;
    },

    validateEndTime(index) {
      if (this.editedDetailsArray[index]) {
        var startTime = this.editedDetailsArray[index].startTime;
        var endTime = this.editedDetailsArray[index].endTime;
        if (startTime && endTime) {
          var startTimeObj = new Date("2000-01-01 " + startTime);
          var endTimeObj = new Date("2000-01-01 " + endTime);
          if (endTimeObj < startTimeObj) {
            return "End time should be less greater than start time.";
          }
        }
      }
      return true;
    },

    validateMaxHours(dayIndex) {
      let dayTotalHours = {};
      // Iterate through the projects in a1 array
      for (const project of this.itemList) {
        // Iterate through the days in the project
        for (let i = 1; i <= 7; i++) {
          const dayKey = `Day${i}`;
          if (project[dayKey] && project[dayKey].totalFormatted) {
            const dayTotal = Number(
              convertToDecimal(project[dayKey].totalFormatted)
            );
            dayTotalHours[dayKey] = (dayTotalHours[dayKey] || 0) + dayTotal;
          } else if (project[dayKey] && project[dayKey].details) {
            const individualHours =
              project[dayKey].details.totalhoursIndividual.map(Number);
            const dayTotal = individualHours.reduce(
              (acc, hour) => acc + hour,
              0
            );
            dayTotalHours[dayKey] = (dayTotalHours[dayKey] || 0) + dayTotal;
          }
        }
      }
      let maxHoursLimit = this.empAdditionalDetails.maxHours;
      if (maxHoursLimit && dayTotalHours["Day" + dayIndex] > maxHoursLimit) {
        let snackbarData = {
          isOpen: true,
          message: `Total hours should not exceed ${maxHoursLimit} hours(sum of regular and overtime hours) per day. Please review and adjust accordingly.`,
          type: "warning",
        };
        this.showAlert(snackbarData);
        return false;
      } else {
        return true;
      }
    },

    async dayUpdate(item, dayIndex, itemIndex) {
      mixpanel.track("Timesheets-add-edit-triggered");
      const { valid } = await this.$refs.addEditTimesheetsForm.validate();
      let modifiedValue = item["Day" + dayIndex].totalFormatted;
      let maxHoursValid = this.validateMaxHours(dayIndex);
      if (valid && maxHoursValid) {
        let unmodifiedItemValue = 0;
        if (this.items[itemIndex]) {
          unmodifiedItemValue = convertToDecimal(
            this.items[itemIndex]["Day" + dayIndex].totalFormatted
          );
        }
        if (modifiedValue) {
          modifiedValue = modifiedValue.toString().toLowerCase();
          modifiedValue = convertToDecimal(modifiedValue);
        } else modifiedValue = 0;
        if (modifiedValue !== unmodifiedItemValue) {
          if (!item.Request_Id) {
            this.isRefetchList = true;
          } else {
            this.isRefetchList = true;
          }
          this.addUpdateTimeSheets(
            item,
            dayIndex,
            modifiedValue,
            [],
            itemIndex
          );
        } else {
          let dayKeys = [
            "Day1",
            "Day2",
            "Day3",
            "Day4",
            "Day5",
            "Day6",
            "Day7",
          ];
          // Update isEdit property to false for all objects in the same array
          for (let i = 0; i < this.itemList.length; i++) {
            for (let dKey of dayKeys) {
              if (this.itemList[i][dKey + "Edit"]) {
                this.itemList[i] = this.items[i] ? this.items[i] : this.newItem;
              }
              this.itemList[i][dKey + "Edit"] = false;
            }
          }
        }
      } else {
        if (!maxHoursValid) {
          this.isInvalidHoursEntered = true;
        }
      }
    },

    addUpdateTimeSheets(
      item,
      dayIndex,
      dayValue = "",
      dayDetails = [],
      itemIndex
    ) {
      let vm = this;
      if (dayDetails.length > 0) {
        vm.overlayLoading = true;
      } else {
        vm.itemList[itemIndex]["Day" + dayIndex + "Loading"] = true;
        vm.isLoading = true;
      }
      let weekEndDate = moment(vm.weekEndDate, this.orgDateFormat).format(
        "YYYY-MM-DD"
      );
      let isAdd = !item.Request_Id ? true : false;
      let variableObj = {
        selfService: vm.formId == 262 ? 1 : 0,
        employeeId: vm.selectedEmpId,
        timesheetId: item.Timesheet_Id ? item.Timesheet_Id : 0,
        requestId: item.Request_Id ? item.Request_Id : 0,
        weekEndingDate: weekEndDate,
        projectId: item.Project_Id,
        timesheetType: item.Timesheet_Type ? item.Timesheet_Type : "",
        projectActivityId: item.Project_Activity_Id,
        dayValue: dayIndex,
        dayDetails: dayDetails,
      };
      for (let i = 1; i <= 7; i++) {
        if (dayIndex == i) {
          variableObj["day" + i] = dayValue ? parseFloat(dayValue) : 0;
        } else {
          variableObj["day" + i] = item["Day" + i].totalFormatted
            ? parseFloat(convertToDecimal(item["Day" + i].totalFormatted))
            : 0;
        }
      }
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_TIMESHEETS,
          variables: variableObj,
          client: "apolloClientJ",
        })
        .then(() => {
          mixpanel.track("Timesheets-add-update-success");
          let snackbarData = {
            isOpen: true,
            message: isAdd
              ? "Timesheets details added successfully"
              : "Timesheets details updated successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          if (dayDetails.length > 0) {
            vm.openDetailsModal = false;
            vm.overlayLoading = false;
          } else {
            vm.itemList[itemIndex]["Day" + dayIndex + "Loading"] = false;
            vm.isLoading = false;
          }
          vm.$emit("timesheet-added", [
            vm.itemList,
            vm.editedDetailsArray,
            vm.selectedDetailsDayIndex,
            vm.isRefetchList,
          ]);
        })
        .catch((err) => {
          vm.handleAddUpdateError(err, isAdd, dayIndex, itemIndex, dayDetails);
        });
    },

    handleAddUpdateError(err = "", isAdd, dayIndex, itemIndex, dayDetails) {
      mixpanel.track("Timesheets-add-update-error");
      if (dayDetails.length > 0) {
        this.openDetailsModal = false;
        this.overlayLoading = false;
      } else {
        this.itemList[itemIndex]["Day" + dayIndex + "Loading"] = false;
        this.isLoading = false;
      }
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: isAdd ? "adding" : "updating",
          form: "timesheet details",
          isListError: false,
        })
        .then((validationErrors) => {
          this.validationMessages = [validationErrors];
          this.showValidationAlert = true;
        });
      this.isInvalidHoursEntered = true;
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    onStatusUpdate() {
      if (this.approvalStatus === "Applied") {
        this.withDrawSubmission();
      } else {
        this.submitForApproval();
      }
    },

    withDrawSubmission() {
      let vm = this;
      vm.isLoading = true;
      const { Request_Id } = this.items[0];
      vm.$apollo
        .mutate({
          mutation: WITHDRAW_APPROVAL,
          variables: {
            selfService: vm.formId == 262 ? 1 : 0,
            requestId: Request_Id,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          mixpanel.track("Timesheets-withdrawn-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Timesheets submission withdrawn successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("timesheet-withdrawn");
        })
        .catch((err) => {
          vm.handleWithdrawError(err);
        });
    },

    handleWithdrawError(err = "") {
      mixpanel.track("Timesheets-withdrawn-error");
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "withdrawn submission",
        form: "timesheet details",
        isListError: false,
      });
    },

    submitForApproval() {
      let vm = this;
      vm.isLoading = true;
      const { Request_Id } = this.items[0];
      vm.$apollo
        .mutate({
          mutation: SUBMIT_FOR_APPROVAL,
          variables: {
            selfService: vm.formId == 262 ? 1 : 0,
            requestId: Request_Id,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          mixpanel.track("Timesheets-submit-for-approval");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Timesheets submitted for approval successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("timesheet-submitted-approval");
        })
        .catch((err) => {
          vm.handleSubmitApprovalError(err);
        });
    },

    handleSubmitApprovalError(err = "") {
      mixpanel.track("Timesheets-submit-for-approval-error");
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "submitting for approval",
        form: "timesheet details",
        isListError: false,
      });
    },

    checkAdditionalDetails(dayIndex, dayValue) {
      return !dayValue
        ? ""
        : !this.empAdditionalDetails[dayIndex]
        ? ""
        : this.empAdditionalDetails[dayIndex].includes("Week Off")
        ? "WO - "
        : this.empAdditionalDetails[dayIndex].includes("Leave")
        ? "L - "
        : this.empAdditionalDetails[dayIndex].includes("Comp Off")
        ? "CO - "
        : this.empAdditionalDetails[dayIndex].includes("Holiday")
        ? "H - "
        : "";
    },

    exportReportFile() {
      let exportHeaders1 = [];
      for (let header of this.weekDays) {
        exportHeaders1.push({
          header: header.day + " - " + header.date,
          key: header.key.toLowerCase(),
        });
      }
      let timesheetsList = this.itemList;
      timesheetsList = timesheetsList.map((item, index) => ({
        ...item,
        employeeName:
          this.formId == 262
            ? this.loginEmployeeName
            : this.selectedEmployeeName,
        userDefId:
          this.formId == 262
            ? this.loginEmployeeUserDefId
            : this.selectedEmployeeUserDefId,
        day1: item["Day1"].details
          ? this.formTotalHoursFromDetails(item["Day1"].details)
          : item["Day1"].totalFormatted
          ? item["Day1"].totalFormatted
          : "",
        day2: item["Day2"].details
          ? this.formTotalHoursFromDetails(item["Day2"].details)
          : item["Day2"].totalFormatted
          ? item["Day2"].totalFormatted
          : "",
        day3: item["Day3"].details
          ? this.formTotalHoursFromDetails(item["Day3"].details)
          : item["Day3"].totalFormatted
          ? item["Day3"].totalFormatted
          : "",
        day4: item["Day4"].details
          ? this.formTotalHoursFromDetails(item["Day4"].details)
          : item["Day4"].totalFormatted
          ? item["Day4"].totalFormatted
          : "",
        day5: item["Day5"].details
          ? this.formTotalHoursFromDetails(item["Day5"].details)
          : item["Day5"].totalFormatted
          ? item["Day5"].totalFormatted
          : "",
        day6: item["Day6"].details
          ? this.formTotalHoursFromDetails(item["Day6"].details)
          : item["Day6"].totalFormatted
          ? item["Day6"].totalFormatted
          : "",
        day7: item["Day7"].details
          ? this.formTotalHoursFromDetails(item["Day7"].details)
          : item["Day7"].totalFormatted
          ? item["Day7"].totalFormatted
          : "",
        projectTotal: this.projectLevelTotal[index],
      }));
      timesheetsList = timesheetsList.map((item) => ({
        ...item,
        day1: this.checkAdditionalDetails("Day1", item.day1) + item.day1,
        day2: this.checkAdditionalDetails("Day2", item.day2) + item.day2,
        day3: this.checkAdditionalDetails("Day3", item.day3) + item.day3,
        day4: this.checkAdditionalDetails("Day4", item.day4) + item.day4,
        day5: this.checkAdditionalDetails("Day5", item.day5) + item.day5,
        day6: this.checkAdditionalDetails("Day6", item.day6) + item.day6,
        day7: this.checkAdditionalDetails("Day7", item.day7) + item.day7,
      }));
      let exportHeaders = [
        {
          header: "Employee Id",
          key: "userDefId",
        },
        {
          header: "Employee Name",
          key: "employeeName",
        },
        {
          header: this.projectLabel,
          key: "Project_Name",
        },
        { header: "Activity", key: "Activity_Name" },
      ];
      exportHeaders = exportHeaders.concat(exportHeaders1);
      exportHeaders.push({ header: "Total", key: "projectTotal" });
      let exportOptions = {
        fileExportData: timesheetsList,
        fileName: "Employee Timesheets",
        sheetName: "Employee Timesheets",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
      mixpanel.track("Timesheets-exported");
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style scoped>
table {
  border-collapse: collapse;
  width: 100%;
}

th {
  text-align: left;
  padding: 8px;
}

td {
  text-align: left;
  padding: 8px;
  background-color: #ffffff;
}

th:first-child {
  position: sticky;
  left: 0;
  border: 0px;
}

th:last-child {
  border: 0px;
}

thead th {
  position: sticky;
  top: 0;
  z-index: 2000;
}

@media screen and (max-width: 600px) {
  thead {
    display: contents !important;
  }
  thead th {
    position: relative;
  }
  th:first-child {
    position: relative;
  }
}

.weekOff-cell {
  background: repeating-linear-gradient(
    -47deg,
    #fff 1%,
    #fff 2%,
    #c3f2a6 3%,
    #fff 4%,
    #fff 5%
  );
}
.weekOff-badge {
  background: #c3f2a6;
  border: 2px solid #c3f2a6;
}
.leave-cell {
  background: repeating-linear-gradient(
    -47deg,
    #fff 1%,
    #fff 2%,
    #fab7be 3%,
    #fff 4%,
    #fff 5%
  );
}
.leave-badge {
  background: #fab7be;
  border: 2px solid #fab7be;
}
.compOff-cell {
  background: repeating-linear-gradient(
    -47deg,
    #fff 1%,
    #fff 2%,
    #f5c895 3%,
    #fff 4%,
    #fff 5%
  );
}
.compOff-badge {
  background: #f5c895;
  border: 2px solid #f5c895;
}
.holiday-cell {
  background: repeating-linear-gradient(
    -47deg,
    #fff 1%,
    #fff 2%,
    #e2bcf8 3%,
    #fff 4%,
    #fff 5%
  );
}
.holiday-badge {
  background: #e2bcf8;
  border: 2px solid #e2bcf8;
}
</style>
