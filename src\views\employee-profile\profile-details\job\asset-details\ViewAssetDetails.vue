<template>
  <div
    v-if="assetDetails && assetDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No asset details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in assetDetails"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:400px;border-left: 7px solid ${generateRandomColor()}; height:180px`
        : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip :text="data.Asset_Name" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="data.Asset_Name ? props : ''"
                    >
                      {{ checkNullValue(data.Asset_Name) }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>

      <div class="card-columns w-100 mt-n6">
        <span
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Received Date </b>
              <span class="pb-1 pt-1">{{ formatDate(data.Receive_Date) }}</span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Serial No </b>
              <span class="mb-3">{{ checkNullValue(data.Serial_No) }}</span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-40px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Return Date </b>
              <span class="pb-1 pt-1">{{ formatDate(data.Return_Date) }}</span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewAssetDetails",
  data() {
    return {
      havingAccess: {},
    };
  },
  props: {
    assetDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  components: { ActionMenu },
  emits: ["on-delete", "on-open-edit"],
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    employeeEdit() {
      return this.$store.state.orgDetails.employeeEdit;
    },
    enableEdit() {
      return (
        (this.empFormUpdateAccess && this.employeeEdit) ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        (this.empFormUpdateAccess && this.employeeEdit) ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.assetDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
  },
};
</script>
