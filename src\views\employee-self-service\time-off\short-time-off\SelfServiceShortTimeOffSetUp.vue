<template>
  <div>
    <div v-if="mainTabs?.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :class="originalList?.length ? '' : 'mr-8'"
                :isFilter="false"
              />
              <ShortTimeOffFilter
                v-if="originalList?.length"
                ref="formFilterRef"
                :form-id="353"
                callingFrom="selfService"
                :item-list="originalList"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-container fluid>
            <ShortTimeOffList
              callingFrom="selfService"
              :form-access="formAccess"
              :form-id="353"
              :landed-form-name="landedFormName"
              :filtered-list="itemList"
              @send-list-data="updateList($event)"
              @reset-filter="resetFilter"
            />
          </v-container>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
</template>

<script>
const { defineAsyncComponent } = require("vue");
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);

const ShortTimeOffList = defineAsyncComponent(() =>
  import(
    "@/views/my-team/time-off/short-time-off/short-time-off-request/ShortTimeOffList.vue"
  )
);
const ShortTimeOffFilter = defineAsyncComponent(() =>
  import(
    "@/views/my-team/time-off/short-time-off/short-time-off-request/ShortTimeOffFilter.vue"
  )
);
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "ShortTimeOffRequestListSetup",
  components: {
    EmployeeDefaultFilterMenu,
    ShortTimeOffList,
    ShortTimeOffFilter,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      originalList: [],
      itemList: [],
      shortTimeOffSettings: null,
      showViewForm: false,
      showAddEditForm: false,
      isErrorInList: false,
      listLoading: false,
      openMoreMenu: false,
      isEdit: false,
      currentTabItem: "",
      isFilterApplied: false,
    };
  },
  computed: {
    landedFormName() {
      let form = this.accessRights(353);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Short Time Off";
    },
    selfServiceTimeOffFormAccess() {
      return this.$store.getters.selfServiceTimeOffFormAccess;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(353);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.selfServiceTimeOffFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf("Short Time Off");
    this.getShortTimeOffSettings();
  },
  methods: {
    checkNullValue,

    updateList(list) {
      this.originalList = list || [];
      this.itemList = list || [];
      this.resetFilter();
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
    },
    applyFilter(filter) {
      this.isFilterApplied = true;
      this.itemList = filter;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.selfServiceTimeOffFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/employee-self-service/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/employee-self-service/" + clickedForm.url;
        }
      }
    },
    getShortTimeOffSettings() {
      this.shortTimeOffSettings = null;
    },
  },
};
</script>

<style scoped>
.container {
  padding: 1em 0em 0em 0em;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 1em 1em 0em 1em;
  }
}
</style>
