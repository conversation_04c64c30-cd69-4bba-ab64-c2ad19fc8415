<template>
  <v-card rounded="lg" class="pa-2" max-height="80vh">
    <div
      class="d-flex align-center pa-1"
      style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
    >
      <div class="d-flex align-center">
        <div class="date-box pr-2">
          <p :class="{ 'text-h5': !isMobileView }" style="height: max-content">
            {{ getDate }}
          </p>
          <p :class="{ 'text-subtitle-2': !isMobileView }">{{ getDay }}</p>
        </div>
        <div class="ml-5">
          <p :class="{ 'text-h5': !isMobileView }">{{ workSchedule }}</p>
          <p :class="{ 'text-subtitle-2': !isMobileView }">
            {{ workScheduleTiming }}
          </p>
        </div>
      </div>
      <v-icon @click="$emit('onClose')" class="mr-1">fas fa-times</v-icon>
    </div>
    <div class="overflow-y-auto" style="max-height: 70vh">
      <v-expansion-panels
        class="my-2"
        variant="popout"
        multiple
        v-model="panel"
      >
        <v-expansion-panel rounded="lg">
          <v-expansion-panel-title class="bg-blue-lighten-5" :static="true">
            Attendance Summary
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-row>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">First In</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ firstIn }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Last Out</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ lastOut }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Effective Hours
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    lastOut !== "-"
                      ? getTimeAsString(selectedItem.Effective_Hours)
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Gross Hours</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    lastOut !== "-"
                      ? getTimeAsString(selectedItem.Gross_Hours)
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Break Hours</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    lastOut !== "-"
                      ? getTimeAsString(selectedItem.Break_Hours)
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Late Arrival Hours
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ getTimeAsString(selectedItem.Late_By) }}
                </p>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
        <v-expansion-panel v-if="selectedItem.details?.length" rounded="lg">
          <v-expansion-panel-title class="bg-blue-lighten-5" :static="true">
            Attendance Entries
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-row
              v-for="item in selectedItem.details"
              :key="item.Attendance_Id"
              class="pb-2"
              style="border-bottom: 1px solid #e5e5e5"
            >
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Check In
                  <v-tooltip
                    v-if="item.Checkin_Latitude && item.Checkin_Longitude"
                    text="Click to view the check in location of the employee"
                  >
                    <template v-slot:activator="{ props }">
                      <v-icon
                        size="20"
                        @click="showMap(item, 'checkin')"
                        v-bind="props"
                      >
                        fas fa-map-marker-alt
                      </v-icon>
                    </template>
                  </v-tooltip>
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ getHours(item.Attendance_PunchIn_Date) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Check Out
                  <v-tooltip
                    v-if="item.Checkout_Latitude && item.Checkout_Longitude"
                    text="Click to view the check out location of the employee"
                  >
                    <template v-slot:activator="{ props }">
                      <v-icon
                        size="20"
                        @click="showMap(item, 'checkout')"
                        v-bind="props"
                      >
                        fas fa-map-marker-alt
                      </v-icon>
                    </template>
                  </v-tooltip>
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ getHours(item.Attendance_PunchOut_Date) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                class="d-flex bg-yellow-lighten-3 py-0 align-center rounded-lg"
              >
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">
                  Actual Hours
                </p>
                <p class="text-subtitle-2 font-weight-regular">
                  {{ getTimeAsString(item.Actual_Total_Hours) }}
                </p>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
        <v-expansion-panel
          v-if="selectedItem.leaveCompOffObject?.leaveDetails?.length"
          rounded="lg"
        >
          <v-expansion-panel-title class="bg-blue-lighten-5" :static="true">
            Leave
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-row
              v-for="(item, index) in selectedItem.leaveCompOffObject
                .leaveDetails"
              :key="index"
              class="pb-2"
              style="border-bottom: 1px solid #e5e5e5"
            >
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Leave Type</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Leave_Name) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Leave Start Date
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ formatDate(item.Start_Date, orgDateFormat) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Leave End Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ formatDate(item.End_Date, orgDateFormat) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ duration(item.Duration) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">
                  Leave Period
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Leave_Period) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">Status</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Approval_Status) }}
                </p>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
        <v-expansion-panel
          v-if="selectedItem.leaveCompOffObject?.compensatoryOffDetails?.length"
          rounded="lg"
        >
          <v-expansion-panel-title class="bg-blue-lighten-5" :static="true">
            Compensatory Off
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-row
              v-for="(item, index) in selectedItem.leaveCompOffObject
                .compensatoryOffDetails"
              :key="index"
              class="pb-2"
              style="border-bottom: 1px solid #e5e5e5"
            >
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Worked Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Worked_Date) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Comp Off Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Compensatory_Date) }}
                </p>
              </v-col>

              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ duration(item.Duration) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">Period</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Period) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">Status</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Approval_Status) }}
                </p>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
        <v-expansion-panel
          v-if="selectedItem.leaveCompOffObject?.shortTimeOffDetails?.length"
          rounded="lg"
        >
          <v-expansion-panel-title class="bg-blue-lighten-5" :static="true">
            Short Time off(Permission)
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-row
              v-for="(item, index) in selectedItem.leaveCompOffObject
                .shortTimeOffDetails"
              :key="index"
              class="pb-2"
              style="border-bottom: 1px solid #e5e5e5"
            >
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Start Date & Time
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    formatDate(item.Start_Date_Time, orgDateFormat + " HH:mm")
                  }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  End Date & Time
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ formatDate(item.End_Date_Time, orgDateFormat + " HH:mm") }}
                </p>
              </v-col>

              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Total Hours</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ getTimeAsString(item.Total_Hours) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">Status</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Approval_Status) }}
                </p>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
        <v-expansion-panel
          v-if="
            selectedItem.leaveCompOffObject?.onDutyshortTimeOffDetails?.length
          "
          rounded="lg"
        >
          <v-expansion-panel-title class="bg-blue-lighten-5" :static="true">
            Short Time off(On Duty)
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-row
              v-for="(item, index) in selectedItem.leaveCompOffObject
                .onDutyshortTimeOffDetails"
              :key="index"
              class="pb-2"
              style="border-bottom: 1px solid #e5e5e5"
            >
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Start Date & Time
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    formatDate(item.Start_Date_Time, orgDateFormat + " HH:mm")
                  }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  End Date & Time
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ formatDate(item.End_Date_Time, orgDateFormat + " HH:mm") }}
                </p>
              </v-col>

              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Total Hours</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ getTimeAsString(item.Total_Hours) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">Status</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Approval_Status) }}
                </p>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
        <v-expansion-panel
          v-if="selectedItem.leaveCompOffObject?.onDutyLeaveDetails?.length"
          rounded="lg"
        >
          <v-expansion-panel-title
            class="bg-blue-lighten-5 rounded-lg"
            :static="true"
          >
            On Duty
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-row
              v-for="(item, index) in selectedItem.leaveCompOffObject
                .onDutyLeaveDetails"
              :key="index"
              class="pb-2"
              style="border-bottom: 1px solid #e5e5e5"
            >
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">On Duty Type</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Leave_Name) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  On Duty Start Date
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ formatDate(item.Start_Date, orgDateFormat) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">
                  On Duty End Date
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ formatDate(item.End_Date, orgDateFormat) }}
                </p>
              </v-col>

              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ duration(item.Duration) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">Period</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Leave_Period) }}
                </p>
              </v-col>
              <v-col cols="6" md="4" class="pb-0">
                <p class="text-subtitle-1 text-grey-darken-1 mr-3">Status</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Approval_Status) }}
                </p>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </div>
  </v-card>
  <AttendanceMap
    v-if="openMap"
    :openMapDialog="openMap"
    :selectedEmployee="selectedEmployee"
    :access-rights="formAccess"
    :landedFormName="'Attendance'"
    :selectedLogItem="selectedItem"
    :selectedProperties="selectedProperties"
    :employeeData="employeeData"
    @close-map-modal="openMap = false"
  ></AttendanceMap>
</template>
<script>
import { checkNullValue } from "@/helper";
import { defineAsyncComponent } from "vue";
import moment from "moment";
const AttendanceMap = defineAsyncComponent(() => import("./AttendanceMap.vue"));

export default {
  name: "CalendarViewDetails",
  components: { AttendanceMap },
  emits: ["onClose"],
  props: {
    selectedDate: {
      type: String,
      required: true,
    },
    selectedItem: {
      type: Object,
      required: true,
    },
    selectedEmployee: {
      type: Number,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    employeeData: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      summaryItems: [],
      panel: [0],
      openMap: false,
      selectedProperties: null,
    };
  },
  computed: {
    getDate() {
      return moment(this.selectedDate).format("DD");
    },
    getDay() {
      return moment(this.selectedDate).format("ddd");
    },
    workSchedule() {
      if (this.selectedItem?.workscheduleHolidayInputs?.Title) {
        return this.selectedItem.workscheduleHolidayInputs.Title;
      }
      return "";
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    workScheduleTiming() {
      if (this.selectedItem?.workscheduleHolidayInputs?.Regular_From) {
        let start = moment(
          this.selectedItem.workscheduleHolidayInputs.Regular_From
        ).format("HH:mm");
        let end = moment(
          this.selectedItem.workscheduleHolidayInputs.Regular_To
        ).format("HH:mm");
        return `${start} - ${end}`;
      }
      return "";
    },
    getHours() {
      return (hours) => {
        if (hours) return moment(hours).format("HH:mm");
        else return "-";
      };
    },
    getTime() {
      return (time) => {
        if (time) return moment(time, "HH:mm:ss").format("HH:mm");
        else return "-";
      };
    },
    getTimeAsString() {
      return (time) => {
        if (time) {
          let timeString = moment(time, "HH:mm:ss").format("HH:mm");
          if (timeString === "00:00") return "-";
          let [hours, minutes] = timeString.split(":");
          let formattedTime = "-";
          if (hours && minutes) {
            formattedTime = `${hours} Hrs ${minutes} Mins`;
          } else if (hours) {
            formattedTime = `${hours} Hrs`;
          } else if (minutes) {
            formattedTime = `0 Hrs ${minutes} Mins`;
          }
          return formattedTime;
        } else return "-";
      };
    },
    firstIn() {
      if (this.selectedItem.details?.[0]?.Attendance_PunchIn_Date) {
        return moment(
          this.selectedItem.details[0].Attendance_PunchIn_Date
        ).format("HH:mm");
      } else return "-";
    },
    lastOut() {
      if (this.selectedItem.details?.length) {
        let len = this.selectedItem.details.length;
        if (this.selectedItem.details[len - 1].Attendance_PunchOut_Date) {
          return moment(
            this.selectedItem.details[len - 1].Attendance_PunchOut_Date
          ).format("HH:mm");
        } else return "-";
      } else return "-";
    },
    orgDateFormat() {
      return this.$store.state.orgDetails.orgDateFormat;
    },
    formatDate() {
      return (date, format) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          return moment(date).format(format);
        }
        return "-";
      };
    },
    duration() {
      return (duration) => {
        if (parseFloat(duration) === 1) return "Full Day";
        else if (parseFloat(duration) === 0.5) return "Half Day";
        else if (parseFloat(duration) === 0.25) return "Quarter Day";
        else return "-";
      };
    },
  },
  watch: {
    selectedItem(val) {
      this.panel = [0];
      if (val) {
        let item = {
          Effective_Hours: val.Effective_Hours,
          Gross_Hours: val.Gross_Hours,
          Break_Hours: val.Break_Hours ? val.Break_Hours : "00:00",
          Late_Arrival_Hours: val.Late_Arrival_Hours
            ? val.Late_Arrival_Hours
            : "00:00",
        };
        this.summaryItems = [item];
      }
    },
  },
  mounted() {
    let val = this.selectedItem;
    if (val) {
      let item = {
        First_In: val.details?.[0]?.Display_PunchIn_Time,
        Last_Out: val.details?.[0]?.Display_PunchOut_Time,
        Effective_Hours: val.Effective_Hours,
        Gross_Hours: val.Gross_Hours,
        Break_Hours: val.Break_Hours ? val.Break_Hours : "00:00",
        Late_Arrival_Hours: val.Late_Arrival_Hours
          ? val.Late_Arrival_Hours
          : "00:00",
      };
      this.summaryItems = [item];
    }
  },
  methods: {
    checkNullValue,
    showMap(item, type) {
      this.selectedProperties = {
        lat:
          type === "checkin" ? item.Checkin_Latitude : item.Checkout_Latitude,
        long:
          type === "checkin" ? item.Checkin_Longitude : item.Checkout_Longitude,
        type: type,
        Attendance_Id: item.Attendance_Id,
      };
      this.openMap = true;
    },
  },
};
</script>
<style scoped>
.date-box {
  color: rgb(var(--v-theme-primary));
  border-right: 1px solid rgb(var(--v-theme-primary));
}
@media screen and (max-width: 600px) {
  thead {
    display: contents !important;
  }

  thead th {
    position: relative;
  }

  th:first-child {
    position: relative;
  }
}
</style>
