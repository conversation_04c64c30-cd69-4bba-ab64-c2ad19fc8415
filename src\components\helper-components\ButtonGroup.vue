<!-- =========================================================================================
    File Name: ButtonGroup.vue
    Description:  This component is used as group of button design. We may have this as 
                  selectable or non selectable group. In Selectable group selected button
                  was highlighted. The selected item will be emitted to parent component as well.
    ----------------------------------------------------------------------------------------
    * Property of Caprice Cloud Solutions Pvt Ltd. ,                                       *
    * Copyright (c) 2013 Caprice Cloud Solutions Pvt Ltd. ,                                *
    * Coimbatore, Tamilnadu, India.														                             *
    * All Rights Reserved.            														                         *
    * Use of this material without the express consent of Caprice Cloud Solutions          *
    * or assignees is unlawful and subject to prosecution to the fullest extent of the law.*
     ----------------------------------------------------------------------------------------
    Author: HRAPP
    Author URL: https://hrapp.in/
========================================================================================== -->
<template>
  <v-btn-toggle
    v-if="isSelectableButtonGroup"
    id="selectable-btn-group"
    v-model="btnGroupValue"
    rounded="lg"
    mandatory
    :class="isMobileView && buttonGroups.length > 2 ? 'd-flex flex-column' : ''"
    :style="{ borderRadius: buttonRadius }"
    @update:model-value="onGroupUpdate()"
  >
    <v-btn
      v-for="buttonGroup in buttonGroupsFormattedData"
      :id="'btn_group_button_' + buttonGroup.id"
      :key="buttonGroup.id"
      color="red"
      class="button-group-btn"
      :style="isWhiteBackground ? 'background: #fff' : ''"
      :disabled="isDisabled"
    >
      <span
        :class="buttonGroup.id === btnGroupValue ? activeButtonStyle : ''"
        class="px-4"
        :style="{ borderRadius: buttonRadius }"
      >
        {{ buttonGroup.text }}
      </span>
      <span class="px-4 red" :style="{ borderRadius: buttonRadius }"></span>
      <slot v-if="buttonGroup.text === slotPosition" name="actionSlot"></slot>
    </v-btn>
  </v-btn-toggle>
  <v-btn-toggle
    v-else
    id="non-selectable-btn-group"
    v-model="btnGroupValue"
    style="pointer-events: none"
    rounded="lg"
    class="rounded-lg"
  >
    <v-btn
      v-for="buttonGroup in buttonGroups"
      :id="'non-selectable-btn-' + buttonGroup.title"
      :key="buttonGroup.title"
      :color="buttonGroup.color"
      style="border: 1px solid #fff !important; max-height: 35px"
      :class="buttonGroup.class ? buttonGroup.class : ''"
      class="text-caption"
    >
      {{ buttonGroup.title }}

      <span class="font-weight-bold pl-1">{{ buttonGroup.subTitle }}</span>
    </v-btn>
  </v-btn-toggle>
</template>

<script>
export default {
  name: "ButtonGroup",
  props: {
    chosenData: {
      type: Number,
      required: true,
    },
    buttonGroups: {
      type: Array,
      required: true,
    },
    isSelectableButtonGroup: {
      type: Boolean,
      default: true,
    },
    activeColor: {
      type: String,
      default: "primary",
    },
    isWhiteBackground: {
      type: Boolean,
      default: false,
    },
    buttonRadius: {
      type: String,
      default: "25px",
    },
    slotPosition: {
      type: String,
      default: "",
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      btnGroupValue: 0, // in btn group component, the btn value will start from 0 only(so given 0 as default)
      isMounted: false,
    };
  },

  computed: {
    // format the btn group inputs to assign in component
    buttonGroupsFormattedData() {
      let formattedData = [];

      // format the button groups with id - to identify which option is chosen
      for (let btn in this.buttonGroups) {
        formattedData.push({
          id: parseInt(btn, 10),
          text: this.buttonGroups[btn],
        });
      }

      return formattedData;
    },
    //change active element class based on active color props
    activeButtonStyle() {
      return "active-button-group " + this.activeColor;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  watch: {
    chosenData: {
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          this.btnGroupValue = val;
        });
      },
    },
  },
  mounted() {
    this.isMounted = true;
  },
  methods: {
    // call this function when button group values was changed
    onGroupUpdate() {
      this.$emit("chosen-value", this.btnGroupValue);
    },
  },
};
</script>

<style>
.active-button-group {
  color: #ffffff !important;
  padding: 10px !important;
}
.button-group-btn {
  height: 40px !important;
}
::v-deep #selectable-btn-group > .v-btn-toggle > .v-btn.v-btn--active {
  opacity: inherit !important;
  background-color: #f5f5f5 !important;
}
.v-btn:not(.v-btn--text):not(.v-btn--outlined):hover:before {
  opacity: 0 !important;
}
.v-btn:not(.v-btn--text):not(.v-btn--outlined).v-btn--active:before {
  opacity: 0 !important;
}
.v-btn-toggle > .v-btn.v-btn {
  padding: 0px !important;
}
</style>
