<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedEmployee"
              :items="employeeList"
              item-title="employeeName"
              item-value="employeeId"
              label="Employee"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedWorkedDate"
              :items="workedDateList"
              item-title="workedDate"
              label="Worked Date"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedDuration"
              :items="durationList"
              label="Duration"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedCompOffDate"
              :items="compOffDateList"
              item-title="compOffDate"
              label="Comp Off Date"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedLocation"
              color="primary"
              :items="locationList"
              itemValue="Location_Id"
              itemTitle="Location_Name"
              :loading="dropdownListFetching"
              label="Location"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedDepartment"
              color="primary"
              :items="departmentList"
              itemValue="departmentId"
              itemTitle="departmentName"
              label="Department"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedDesignation"
              color="primary"
              :items="designationList"
              itemValue="designationId"
              itemTitle="designationName"
              label="Designation"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedEmployeeType"
              color="primary"
              :items="employeeTypeList"
              itemValue="employeeTypeId"
              itemTitle="employeeTypeName"
              label="Employee Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedBusinessUnit"
              color="primary"
              :items="businessUnitList"
              itemValue="businessUnitId"
              itemTitle="businessUnitName"
              label="Business Unit"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <!-- <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedCompOffApplicability"
              color="primary"
              :items="compOffApplicabilityList"
              label="Manager Name"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col> -->
          <v-col
            v-if="fieldForce"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedServiceProvider"
              color="primary"
              :items="serviceProviderList"
              item-value="Service_Provider_Id"
              item-title="Service_Provider_Name"
              :label="labelList[115]?.Field_Alias"
              :loading="dropdownListFetching"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              color="primary"
              :items="statusList"
              label="Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
export default defineComponent({
  name: "FormFilter",

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
  },

  data: () => ({
    openFormFilter: false,
    durationList: ["Full Day", "Half Day"],
    selectedDuration: [],
    locationList: [],
    departmentList: [],
    designationList: [],
    employeeTypeList: [],
    businessUnitList: [],
    serviceProviderList: [],
    statusList: [
      "Applied",
      "Approved",
      "Cancel Applied",
      "Cancelled",
      "Rejected",
      "Returned",
    ],
    selectedLocation: [],
    selectedServiceProvider: [],
    selectedStatus: [],
    selectedDepartment: [],
    selectedEmployeeType: [],
    selectedDesignation: [],
    selectedBusinessUnit: [],
    compOffDateList: [],
    selectedCompOffDate: [],
    workedDateList: [],
    selectedWorkedDate: [],
    employeeList: [],
    selectedEmployee: [],
    dropdownListFetching: false,
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    fieldForce() {
      const { fieldForce } = this.$store.state.orgDetails;
      return fieldForce;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.fnApplyFilter();
    this.formFilterData();
    this.retrieveDropdownDetails();
  },
  methods: {
    retrieveDropdownDetails() {
      this.dropdownListFetching = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: parseInt(this.formId) })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { locations, serviceProvider } =
              res.data.getDropDownBoxDetails;
            this.locationList = locations;
            this.serviceProviderList = serviceProvider;
          }
          this.dropdownListFetching = false;
        })
        .catch(() => {
          this.dropdownListFetching = false;
        });
    },
    // apply filter
    fnApplyFilter(listData = []) {
      this.openFormFilter = false;
      let filteredArray = [];
      if (listData && listData.length) {
        filteredArray = listData;
      } else {
        filteredArray = this.items;
      }

      if (this.selectedDuration.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDuration.includes(item.Duration);
        });
      }
      if (this.selectedLocation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLocation.includes(item.Location_Id);
        });
      }
      if (this.selectedDepartment.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDepartment.includes(item.Department_Id);
        });
      }
      if (this.selectedEmployeeType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmployeeType.includes(item.EmpType_Id);
        });
      }
      if (this.selectedDesignation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDesignation.includes(item.Designation_Id);
        });
      }
      if (this.selectedBusinessUnit.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedBusinessUnit.includes(item.Business_Unit_Id);
        });
      }
      if (this.selectedServiceProvider.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedServiceProvider.includes(
            item.Service_Provider_Id
          );
        });
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Approval_Status);
        });
      }
      if (this.selectedCompOffDate.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedCompOffDate.includes(item.Compensatory_Date);
        });
      }
      if (this.selectedWorkedDate.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedWorkedDate.includes(item.Compensated_Date);
        });
      }
      if (this.selectedEmployee.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmployee.includes(item.User_Defined_EmpId);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },
    formFilterData() {
      for (let item of this.items) {
        if (item && item.Employee_Name) {
          if (item && (item.Employee_Name || item.User_Defined_EmpId)) {
            this.employeeList.push({
              employeeName:
                item.Employee_Name + " - " + item.User_Defined_EmpId,
              employeeId: item.User_Defined_EmpId,
            });
          }
          this.workedDateList.push({
            workedDate: item.Compensated_Date,
          });
          this.compOffDateList.push({
            compOffDate: item.Compensatory_Date,
          });
          this.departmentList.push({
            departmentId: item.Department_Id,
            departmentName: item.Department_Name,
          });
          this.designationList.push({
            designationId: item.Designation_Id,
            designationName: item.Designation_Name,
          });
          this.employeeTypeList.push({
            employeeTypeId: item.EmpType_Id,
            employeeTypeName: item.Employee_Type,
          });
          this.businessUnitList.push({
            businessUnitId: item.Business_Unit_Id,
            businessUnitName: item.Business_Unit,
          });
        }
      }
      this.employeeList = this.removeDuplicatesFromArrayOfObject(
        this.employeeList,
        "employeeName"
      );
      this.workedDateList = this.removeDuplicatesFromArrayOfObject(
        this.workedDateList,
        "workedDate"
      );
      this.compOffDateList = this.removeDuplicatesFromArrayOfObject(
        this.compOffDateList,
        "compOffDate"
      );
      this.departmentList = this.removeDuplicatesFromArrayOfObject(
        this.departmentList,
        "departmentId"
      );
      this.designationList = this.removeDuplicatesFromArrayOfObject(
        this.designationList,
        "designationId"
      );
      this.employeeTypeList = this.removeDuplicatesFromArrayOfObject(
        this.employeeTypeList,
        "employeeTypeId"
      );
      this.businessUnitList = this.removeDuplicatesFromArrayOfObject(
        this.businessUnitList,
        "businessUnitId"
      );
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    // reset filter
    resetFilterValues() {
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedDuration = [];
      this.selectedLocation = [];
      this.selectedDepartment = [];
      this.selectedEmployeeType = [];
      this.selectedDesignation = [];
      this.selectedBusinessUnit = [];
      this.selectedServiceProvider = [];
      this.selectedStatus = [];
      this.selectedCompOffDate = [];
      this.selectedWorkedDate = [];
      this.selectedEmployee = [];
      this.openFormFilter = false;
    },
  },
});
</script>
