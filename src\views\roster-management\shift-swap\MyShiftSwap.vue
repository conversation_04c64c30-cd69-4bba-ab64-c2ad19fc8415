<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              />
              <ShiftSwapFilter
                v-if="!showAddEditForm && (itemList.length || isFilterApplied)"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="shift-swap-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12" v-if="originalList.length === 0">
                    <NotesCard
                      notes="Shift swap is a critical aspect of workforce management. It allows employees to apply for shift swap according to their need."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="To create an effective shift swap, please ensure that all relevant fields are filled out accurately. This includes selecting the type of shifts and selecting correct date"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="addShiftSwap()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span>Apply</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      class="mt-1"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div
              v-if="originalList.length > 0 && !isSmallTable"
              class="d-flex align-center my-3"
              :class="isMobileView ? 'justify-center ' : 'justify-end'"
            >
              <v-btn
                v-if="formAccess.add"
                prepend-icon="fas fa-plus"
                variant="elevated"
                rounded="lg"
                class="mx-1 primary"
                :size="isMobileView ? 'small' : 'default'"
                @click="addShiftSwap()"
              >
                <template v-slot:prepend>
                  <v-icon></v-icon>
                </template>
                <span>Apply</span>
              </v-btn>
              <v-btn
                rounded="lg"
                class="mt-1"
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    class="mt-1 ml-n3 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action"
                    @click="onMoreAction(action)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                        >
                          <v-tooltip :text="action.message">
                            <template v-slot:activator="{ props }">
                              <div v-bind="action.message ? props : ''">
                                <v-icon size="15" class="pr-2">{{
                                  action.icon
                                }}</v-icon>
                                {{ action.key }}
                              </div>
                            </template>
                          </v-tooltip>
                        </v-list-item-title>
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>

            <v-row>
              <v-col
                v-if="originalList.length > 0"
                :cols="isSmallTable ? 5 : 12"
                class="mb-12"
              >
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Employee Name
                        </div>
                        <section
                          style="height: 3em"
                          class="d-flex align-center"
                        >
                          <div
                            v-if="
                              isSmallTable &&
                              !isMobileView &&
                              selectedItem &&
                              selectedItem.Swap_Id === item.Swap_Id
                            "
                            class="data-table-side-border d-flex"
                          ></div>
                          <div>
                            <v-tooltip :text="item.Employee_Name">
                              <template v-slot:activator="{ props }">
                                <div
                                  v-bind="
                                    item.Employee_Name.length > 30 ? props : ''
                                  "
                                  class="text-truncate text-start text-primary"
                                  style="max-width: 200px"
                                >
                                  {{ checkNullValue(item.Employee_Name) }}
                                </div>
                              </template>
                            </v-tooltip>
                            <div
                              class="text-subtitle-2 font-weight-regular text-grey-darken-1 text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              {{ checkNullValue(item.User_Defined_EmpId) }}
                            </div>
                          </div>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Approver
                        </div>
                        <section
                          style="height: 3em"
                          class="d-flex align-center"
                        >
                          <v-tooltip :text="item.Approver_Name">
                            <template v-slot:activator="{ props }">
                              <div
                                v-bind="
                                  item.Approver_Name.length > 30 ? props : ''
                                "
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                style="max-width: 200px"
                              >
                                {{ checkNullValue(item.Approver_Name) }}
                              </div>
                            </template>
                          </v-tooltip>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Swap Date
                        </div>
                        <section
                          style="height: 3em"
                          class="d-flex align-center"
                        >
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            style="max-width: 200px"
                          >
                            {{ formatDate(item.Swap_Date) }}
                          </div>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Shift Type
                        </div>
                        <section
                          style="height: 3em"
                          class="d-flex align-center"
                        >
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            style="max-width: 200px"
                          >
                            {{ checkNullValue(item.Shift_Name) }}
                          </div>
                        </section>
                      </td>
                      <td
                        v-if="!isSmallTable"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Status
                        </div>
                        <section
                          style="height: 3em"
                          class="d-flex align-center"
                        >
                          <div
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :class="customClass(item.Approval_Status)"
                            style="max-width: 200px"
                          >
                            {{ checkNullValue(item.Approval_Status) }}
                          </div>
                        </section>
                      </td>
                    </tr></template
                  >
                </v-data-table>
              </v-col>
              <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                <ViewShiftSwap
                  :landedFormName="landedFormName"
                  :accessRights="formAccess"
                  :selectedItem="selectedItem"
                  @open-edit-form="onOpenEditForm()"
                  @close-form="closeForm()"
                ></ViewShiftSwap>
              </v-col>

              <v-col
                :cols="originalList.length === 0 ? 12 : 7"
                v-if="showAddEditForm && windowWidth >= 1264"
              >
                <AddEditShiftSwap
                  :isEdit="isEdit"
                  :selectedItem="selectedItem"
                  @close-form="closeForm()"
                  @added-record="refetchList()"
                ></AddEditShiftSwap>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeForm()"
    >
      <ViewShiftSwap
        v-if="showViewForm"
        :landedFormName="landedFormName"
        :accessRights="formAccess"
        :selectedItem="selectedItem"
        @close-form="closeForm()"
      ></ViewShiftSwap>
      <AddEditShiftSwap
        v-if="showAddEditForm"
        :selectedItem="selectedItem"
        @close-form="closeForm()"
        @added-record="refetchList()"
        :isEdit="isEdit"
      ></AddEditShiftSwap>
    </v-dialog>
  </div>
</template>
<script>
const { defineAsyncComponent } = require("vue");
import moment from "moment";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const AddEditShiftSwap = defineAsyncComponent(() =>
  import("./AddEditShiftSwap.vue")
);
const ViewShiftSwap = defineAsyncComponent(() => import("./ViewShiftSwap.vue"));
const ShiftSwapFilter = defineAsyncComponent(() =>
  import("./ShiftSwapFilter.vue")
);
import { LIST_SHIFT_SWAPPING } from "@/graphql/roster-management/TeamShiftSwapQueries.js";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "MyShiftSwap",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    AddEditShiftSwap,
    ViewShiftSwap,
    ShiftSwapFilter,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "",
      originalList: [],
      itemList: [],
      showViewForm: false,
      showAddEditForm: false,
      isErrorInList: false,
      listLoading: false,
      selectedItem: null,
      openMoreMenu: false,
      isFilterApplied: false,
      isEdit: false,
    };
  },
  computed: {
    landedFormName() {
      return "Shift Swap";
    },
    rosterManagementFormAccess() {
      return this.$store.getters.shiftSwapFormAccess;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(306);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.rosterManagementFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = "There are no Shift Swap for the selected filters/searches.";
      }
      return msgText;
    },
    moreActions() {
      return [{ key: "Export", icon: "fas fa-file-export" }];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        } else return "-";
      };
    },
    formatDateTime() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Employee Name",
            align: "start",
            key: "Employee_Name",
          },
          {
            title: "Swap Date",
            key: "Swap_Date",
          },
        ];
      } else {
        return [
          {
            title: "Employee Name",
            align: "start",
            key: "Employee_Name",
          },
          {
            title: "Approver",
            key: "Approver_Name",
          },
          {
            title: "Swap Date",
            key: "Swap_Date",
          },
          {
            title: "Shift Type",
            key: "Shift_Name",
          },
          {
            title: "Status",
            key: "Approval_Status",
          },
        ];
      }
    },
    customClass() {
      return (status) => {
        if (status && status.toLowerCase() === "approved") {
          return "text-green";
        } else if (status && status.toLowerCase() === "rejected") {
          return "text-red";
        } else if (status && status.toLowerCase() === "applied") {
          return "text-blue";
        }
      };
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.retrieveShiftSwapingList();
  },
  methods: {
    checkNullValue,
    addShiftSwap() {
      this.showAddEditForm = true;
      this.isEdit = false;
      this.showViewForm = false;
      this.selectedItem = null;
    },
    closeForm() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showAddEditForm = false;
      this.showViewForm = true;
    },
    onOpenEditForm() {
      this.showAddEditForm = true;
      this.showViewForm = false;
      this.isEdit = true;
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType.key === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeForm();
      this.retrieveShiftSwapingList();
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: "Employee Id",
          key: "User_Defined_EmpId",
        },
        {
          header: "Employee Name",
          key: "Employee_Name",
        },
        {
          header: "Approver",
          key: "Approver_Name",
        },
        {
          header: "Swap Date",
          key: "Swap_Date",
        },
        {
          header: "Shift Type",
          key: "Shift_Name",
        },
        {
          header: "Status",
          key: "Approval_Status",
        },
        {
          header: "Added On",
          key: "Added_On",
        },
        {
          header: "Added By",
          key: "Added_By_Name",
        },
        {
          header: "Updated On",
          key: "Updated_On",
        },
        {
          header: "Updated By",
          key: "Updated_By_Name",
        },
      ];
      let swapShift = this.itemList;
      swapShift = swapShift.map((item) => {
        item.Swap_Date = this.formatDate(item.Swap_Date);
        item.Added_On = this.formatDateTime(item.Added_On);
        return item;
      });
      let exportOptions = {
        fileExportData: swapShift,
        fileName: "Swap Shift",
        sheetName: "Swap shift",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
    },
    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter.selectedDate && filter.selectedDate != "") {
        filteredList = filteredList.filter((item) => {
          return (
            moment(filter.selectedDate).format("YYYY-MM-DD") == item.Swap_Date
          );
        });
      }
      if (filter.status && filter.status.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.status.includes(item.Approval_Status);
        });
      }
      if (filter.shiftType && filter.shiftType > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.shiftType.includes(item.Swap_Shift_Type_Id);
        });
      }
      this.isFilterApplied = true;
      this.itemList = filteredList;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.rosterManagementFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/roster-management/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/roster-management/" + clickedForm.url;
        }
      }
    },

    retrieveShiftSwapingList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_SHIFT_SWAPPING,
          client: "apolloClientL",
          fetchPolicy: "no-cache",
          variables: { formId: 306 },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listshiftSwapping &&
            response.data.listshiftSwapping.shiftSwapping &&
            response.data.listshiftSwapping.shiftSwapping.length > 0
          ) {
            vm.itemList = response.data.listshiftSwapping.shiftSwapping;
            vm.originalList = JSON.parse(JSON.stringify(vm.itemList));
            vm.listLoading = false;
          } else {
            vm.listLoading = false;
            vm.itemList = [];
            vm.originalList = [];
          }
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err) {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "shift swap",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
.shift-swap-container {
  padding: 5em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .shift-swap-container {
    padding: 10em 1em 0em 1em;
  }
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
