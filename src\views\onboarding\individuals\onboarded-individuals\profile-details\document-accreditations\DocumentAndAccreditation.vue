<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs('error')"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else class="fill-height">
    <div class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs('refresh')" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <Documents
      v-if="!openedEditForm"
      :documentDetailsData="documentDetails"
      :selectedCandidateId="selectedCandidateId"
      :customDocumentList="documentList"
      :formAccess="formAccess"
      @refetch-doc-accreditation-details="refetchAPIs('update')"
    ></Documents>
    <Accreditations
      v-if="!openedEditForm"
      :accreditationDetailsData="accreditationDetails"
      :selectedCandidateId="selectedCandidateId"
      :formAccess="formAccess"
      :selectedCandidateDOB="selectedCandidateDOB"
      @refetch-doc-accreditation-details="refetchAPIs('update')"
    ></Accreditations>
    <DrivingLicenseDetails
      v-if="!openedEditForm || openedEditForm === 'drivingLicenseDetails'"
      ref="drivingLicenseDetails"
      :licenseDetailsData="licenseDetailsData"
      :selectedCandidateId="selectedCandidateId"
      :formAccess="formAccess"
      :selectedCandidateDOB="selectedCandidateDOB"
      @refetch-personal-details="refetchAPIs('update')"
      @edit-opened="openedEditForm = 'drivingLicenseDetails'"
      @edit-closed="openedEditForm = ''"
    />
    <PassportDetails
      v-if="!openedEditForm || openedEditForm === 'passportDetails'"
      ref="passportDetails"
      :passportDetailsData="passportDetailsData"
      :selectedCandidateId="selectedCandidateId"
      :formAccess="formAccess"
      :selectedCandidateDOB="selectedCandidateDOB"
      @refetch-personal-details="refetchAPIs('update')"
      @edit-opened="openedEditForm = 'passportDetails'"
      @edit-closed="openedEditForm = ''"
    />
    <div v-if="!openedEditForm" id="educationDiv" class="mt-4">
      <div class="d-flex">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="blue-grey"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold">
            Education Details
          </span>
        </div>
        <span v-if="enableAdd" class="d-flex justify-end ml-auto">
          <v-btn
            color="primary"
            variant="text"
            @click="showAddEditEducationForm = true"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
          >
        </span>
      </div>
      <v-dialog
        v-model="showAddEditEducationForm"
        max-width="70%"
        @click:outside="closeEditForm"
      >
        <AddEditEducationDetails
          :selectedEducationDetails="selectedEducationDetails"
          :selectedCandidateId="selectedCandidateId"
          :selectedCandidateDOB="selectedCandidateDOB"
          @close-education-form="closeEditForm"
          @refetch-career-details="handleUpdateSuccess"
        >
        </AddEditEducationDetails>
      </v-dialog>
      <div v-if="!isMobileView" class="d-flex">
        <v-slide-group
          class="px-4"
          selected-class="bg-primary"
          prev-icon="fas fa-chevron-circle-left"
          next-icon="fas fa-chevron-circle-right"
          show-arrows
        >
          <v-slide-group-item>
            <ViewEducationDetails
              :educationDetails="educationDetails"
              :formAccess="formAccess"
              @on-open-edit="openEditForm($event)"
              @on-delete="onShowDeleteConfirmation($event)"
            />
          </v-slide-group-item>
        </v-slide-group>
      </div>
      <div v-else>
        <div class="card-container">
          <ViewEducationDetails
            :educationDetails="educationDetails"
            :formAccess="formAccess"
            @on-open-edit="openEditForm($event)"
            @on-delete="onShowDeleteConfirmation($event)"
          />
        </div>
      </div>
    </div>
    <div v-if="!openedEditForm" id="certificationDiv" class="mt-4">
      <div class="d-flex">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="brown"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
            >Certification Details</span
          >
        </div>
        <span v-if="enableAdd" class="d-flex justify-end ml-auto">
          <v-btn
            color="primary"
            variant="text"
            @click="showAddEditCertificationForm = true"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
          >
        </span>
      </div>
      <v-dialog
        v-model="showAddEditCertificationForm"
        max-width="70%"
        @click:outside="closeEditForm"
      >
        <AddEditCertificationDetails
          :selectedCertificationDetails="selectedCertificationDetails"
          :selectedCandidateId="selectedCandidateId"
          :selectedCandidateDOB="selectedCandidateDOB"
          @close-certification-form="closeEditForm"
          @refetch-career-details="handleUpdateSuccess"
        >
        </AddEditCertificationDetails>
      </v-dialog>
      <div v-if="!isMobileView" class="d-flex">
        <v-slide-group
          class="px-4"
          selected-class="bg-primary"
          prev-icon="fas fa-chevron-circle-left"
          next-icon="fas fa-chevron-circle-right"
          show-arrows
        >
          <v-slide-group-item>
            <ViewCertificationDetails
              :certificationDetails="certificationDetails"
              :formAccess="formAccess"
              @on-open-edit="openEditForm($event)"
              @on-delete="onShowDeleteConfirmation($event)"
            />
          </v-slide-group-item>
        </v-slide-group>
      </div>
      <div v-else>
        <div class="card-container">
          <ViewCertificationDetails
            :certificationDetails="certificationDetails"
            :formAccess="formAccess"
            @on-open-edit="openEditForm($event)"
            @on-delete="onShowDeleteConfirmation($event)"
          />
        </div>
      </div>
    </div>
    <div v-if="!openedEditForm" id="trainingDiv" class="mt-4">
      <div class="d-flex">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="light-green"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
            >Training Details</span
          >
        </div>
        <span v-if="enableAdd" class="d-flex justify-end ml-auto">
          <v-btn
            color="primary"
            variant="text"
            @click="showAddEditTrainingForm = true"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
          >
        </span>
      </div>
      <v-dialog
        v-model="showAddEditTrainingForm"
        max-width="70%"
        @click:outside="closeEditForm"
      >
        <AddEditTrainingDetails
          :selectedTrainingDetails="selectedTrainingDetails"
          :selectedCandidateId="selectedCandidateId"
          :selectedCandidateDOB="selectedCandidateDOB"
          @close-training-form="closeEditForm"
          @refetch-career-details="handleUpdateSuccess"
        >
        </AddEditTrainingDetails>
      </v-dialog>
      <div v-if="!isMobileView" class="d-flex">
        <v-slide-group
          class="px-4"
          selected-class="bg-success"
          prev-icon="fas fa-chevron-circle-left"
          next-icon="fas fa-chevron-circle-right"
          show-arrows
        >
          <v-slide-group-item>
            <ViewTrainingDetails
              :trainingDetails="trainingDetails"
              :formAccess="formAccess"
              @on-open-edit="openEditForm($event)"
              @on-delete="onShowDeleteConfirmation($event)"
            />
          </v-slide-group-item>
        </v-slide-group>
      </div>
      <div v-else class="card-container">
        <ViewTrainingDetails
          :trainingDetails="trainingDetails"
          :formAccess="formAccess"
          @on-open-edit="openEditForm($event)"
          @on-delete="onShowDeleteConfirmation($event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import Documents from "./document/Documents.vue";
import Accreditations from "./accreditations/Accreditations.vue";
import { RETRIEVE_EMP_DOCUMENT_ACCREDITATION_INFO } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
const AddEditEducationDetails = defineAsyncComponent(() =>
  import("./education/AddEditEducationDetails.vue")
);
const AddEditCertificationDetails = defineAsyncComponent(() =>
  import("./certification/AddEditCertificationDetails.vue")
);
const AddEditTrainingDetails = defineAsyncComponent(() =>
  import("./training/AddEditTrainingDetails.vue")
);
const PassportDetails = defineAsyncComponent(() =>
  import("./passport/PassportDetails.vue")
);
const DrivingLicenseDetails = defineAsyncComponent(() =>
  import("./license/DrivingLicenseDetails.vue")
);
import ViewEducationDetails from "./education/ViewEducationDetails";
import ViewCertificationDetails from "./certification/ViewCertificationDetails";
import ViewTrainingDetails from "./training/ViewTrainingDetails";
import { RETRIEVE_EMP_DOCUMENT_ASSOSIATED_FIELDS } from "@/graphql/employee-profile/profileQueries.js";

export default {
  name: "DocumentAndAccreditation",
  components: {
    Accreditations,
    Documents,
    AddEditEducationDetails,
    AddEditCertificationDetails,
    AddEditTrainingDetails,
    PassportDetails,
    DrivingLicenseDetails,
    ViewEducationDetails,
    ViewCertificationDetails,
    ViewTrainingDetails,
  },
  props: {
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
  },
  emits: ["details-retrieved"],
  data: () => ({
    documentDetails: [],
    accreditationDetails: [],
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    showAddEditEducationForm: false,
    showAddEditCertificationForm: false,
    showAddEditTrainingForm: false,
    selectedEducationDetails: {},
    selectedCertificationDetails: {},
    selectedTrainingDetails: {},
    educationDetails: [],
    certificationDetails: [],
    trainingDetails: [],
    selectedEducationDelateRecord: null,
    selectedCertificationDelateRecord: null,
    selectedTrainingDelateRecord: null,
    openedEditForm: "",
    licenseDetailsData: [],
    passportDetailsData: [],
    documentList: {},
  }),
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableAdd() {
      return this.formAccess && this.formAccess.add;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.getDocumentAndAccreditationDetails();
    this.getDocumentAssosiatedFields();
  },
  methods: {
    openEditForm(params) {
      mixpanel.track("Onboarded-candidate-career-edit-opened");
      let selectedItem = params[0],
        typeofSkill = params[1];
      // Set the form data to the selected card's data for editing
      if (typeofSkill == "education") {
        this.selectedEducationDetails = selectedItem;
        this.showAddEditEducationForm = true;
      } else if (typeofSkill == "certification") {
        this.selectedCertificationDetails = selectedItem;
        this.showAddEditCertificationForm = true;
      } else if (typeofSkill == "training") {
        this.selectedTrainingDetails = selectedItem;
        this.showAddEditTrainingForm = true;
      }
    },
    closeEditForm() {
      mixpanel.track("Onboarded-candidate-career-edit-closed");
      this.selectedEducationDetails = {};
      this.selectedCertificationDetails = {};
      this.selectedTrainingDetails = {};
      this.showAddEditEducationForm = false;
      this.showAddEditCertificationForm = false;
      this.showAddEditTrainingForm = false;
    },
    // this method opens the delete confirmation popup
    onShowDeleteConfirmation(params) {
      this.openWarningModal = true;
      let selectedItem = params[0],
        typeofSkill = params[1];
      // Set the form data to the selected card's data for editing
      if (typeofSkill == "education") {
        this.selectedEducationDelateRecord = selectedItem;
      } else if (typeofSkill == "certification") {
        this.selectedCertificationDelateRecord = selectedItem;
      } else if (typeofSkill == "training") {
        this.selectedTrainingDelateRecord = selectedItem;
      }
    },
    onDeleteSelectedCareerDetails() {
      this.onCloseWarningModal();
    },
    //this method closes the delete confirmation popup
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedEducationDelateRecord = null;
      this.selectedCertificationDelateRecord = null;
      this.selectedTrainingDelateRecord = null;
    },
    handleUpdateSuccess() {
      this.closeEditForm();
      this.refetchAPIs("update");
    },
    refetchAPIs(type) {
      this.isErrorInList = false;
      this.openedEditForm = "";
      mixpanel.track("Onboarded-candidate-docAcc-refetch");
      this.getDocumentAndAccreditationDetails(type);
    },
    getDocumentAndAccreditationDetails(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_DOCUMENT_ACCREDITATION_INFO,
          client: "apolloClientV",
          variables: {
            candidateId: vm.selectedCandidateId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("Onboarded-candidate-docAcc-fetch-success");
          if (response && response.data && response.data.retrieveDocumentInfo) {
            const {
              documentDetails,
              accreditationDetails,
              educationalInfoDetails,
              certificateInfoDetails,
              trainingInfoDetails,
              drivingLicenseDetails,
              passportDetails,
            } = response.data.retrieveDocumentInfo;
            vm.documentDetails = documentDetails
              ? JSON.parse(documentDetails)
              : [];
            vm.accreditationDetails = accreditationDetails
              ? JSON.parse(accreditationDetails)
              : [];
            vm.educationDetails = educationalInfoDetails
              ? JSON.parse(educationalInfoDetails)
              : [];
            vm.certificationDetails = certificateInfoDetails
              ? JSON.parse(certificateInfoDetails)
              : [];
            vm.trainingDetails = trainingInfoDetails
              ? JSON.parse(trainingInfoDetails)
              : [];
            vm.licenseDetailsData = drivingLicenseDetails
              ? JSON.parse(drivingLicenseDetails)
              : [];
            vm.passportDetailsData = passportDetails
              ? JSON.parse(passportDetails)
              : [];
            vm.$emit("details-retrieved", type);
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("Onboarded-candidate-docAcc-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "document & accreditation details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    getDocumentAssosiatedFields() {
      let vm = this;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_DOCUMENT_ASSOSIATED_FIELDS,
          client: "apolloClientAC",
          variables: {
            candidateId: this.selectedCandidateId,
          },
          fetchPolicy: "no-cache",
        })
        .then((data) => {
          data = data.data;
          if (
            data &&
            data.getDocumentAssociatedFields &&
            data.getDocumentAssociatedFields.response
          ) {
            this.documentList = JSON.parse(
              data.getDocumentAssociatedFields.response
            );
          }
        });
    },
  },
};
</script>

<style scoped>
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  /* The grid-gap property adds a 10-pixel gap between the cards. */
  grid-gap: 10px;
}
@media (max-width: 600px) {
  .card-container {
    grid-template-columns: 1fr;
  }
}
</style>
