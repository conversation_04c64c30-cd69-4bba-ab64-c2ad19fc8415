<template>
  <v-card class="rounded-lg ma-1" elevation="0">
    <div v-if="showEditForm">
      <EditJobDetails
        ref="editJobDetails"
        :jobDetails="jobDetails"
        :selectedCandidateDOB="selectedCandidateDOB"
        :selectedCandidateId="selectedCandidateId"
        :selectedCandidateDetails="selectedCandidateDetails"
        :selectedEmpDoj="selectedEmpDoj"
        @edit-updated="editUpdated"
        @close-edit-form="closeEditForm"
      >
      </EditJobDetails>
    </div>
    <div v-else>
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="secondary"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Job Details</span
          >
        </div>
        <div v-if="formAccess && formAccess.update">
          <v-btn @click="openEditDialog" color="primary" variant="text">
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
          </v-btn>
        </div>
      </div>

      <v-row class="pa-4 ma-2 card-blue-background">
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Role (Access Rights)</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Roles_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Date of Join</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ formatDate(jobDetails.Date_Of_Join) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Designation</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Designation_Name) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[425]?.Field_Visiblity.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[425]?.Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobRoleNames) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Department</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Department_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Location</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Location_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Work Schedule</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Work_Schedule_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Employee Type</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Employee_Type) }}
          </p>
        </v-col>
        <v-col v-if="jobDetails.Field_Force" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ getCustomFieldName(115, "Service Provider") }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Service_Provider_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Work Email</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Emp_Email) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList[446]?.Field_Visiblity === 'Yes'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[446]?.Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Profession_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Manager</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Manager_Name) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[384]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[384]?.Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Business_Unit) }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList[304].Field_Visiblity == 'Yes'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">PF Number</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Pf_PolicyNo) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Job Code</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Job_Code) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Probation Date</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ formatDate(jobDetails.Probation_Date) }}
          </p>
        </v-col>
        <v-col
          v-if="labelList[151] && labelList[151].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[151].Field_Alias }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(jobDetails.Organization_Group) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Confirmed</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ parseInt(jobDetails.Confirmed) ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col v-if="parseInt(jobDetails.Confirmed)" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Confirmation Date</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ formatDate(jobDetails.Confirmation_Date) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Commission Based Employee
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ parseInt(jobDetails.Commission_Employee) ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Attendance Enforced Payment
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              parseInt(jobDetails.Attendance_Enforced_Payment) ? "Yes" : "No"
            }}
          </p>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="6"
          v-if="labelList[306].Field_Visiblity == 'Yes'"
        >
          <p class="text-subtitle-1 text-grey-darken-1">TDS Exemption</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ parseInt(jobDetails.TDS_Exemption) ? "Yes" : "No" }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Employee Status</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ jobDetails.Emp_Status }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Previous Experience</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              convertMonthToYearMonthsDays(
                jobDetails.Previous_Employee_Experience
              )
            }}
          </p>
        </v-col>
      </v-row>
    </div>
  </v-card>
</template>

<script>
import { defineAsyncComponent } from "vue";
import {
  checkNullValue,
  convertMonthToYearMonthsDays,
  getCustomFieldName,
} from "@/helper";
import moment from "moment";
const EditJobDetails = defineAsyncComponent(() =>
  import("./EditJobDetails.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "JobDetails",
  components: {
    EditJobDetails,
  },
  props: {
    jobDetailsData: {
      type: [Object, Array],
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-job-details", "edit-opened", "edit-closed"],
  data() {
    return {
      showEditForm: false,
      jobDetails: {
        Employee_Type: "",
        Designation_Name: "",
        Department_Name: "",
        Location_Name: "",
        Work_Schedule_Name: "",
        Job_Code: "",
        Manager_Name: "",
        Manager_Id: 0,
        Date_Of_Join: null,
        Attendance_Enforced_Payment: 0,
        Probation_Date: null,
        Previous_Employee_Experience: 0,
        Emp_Email: "",
        Commission_Employee: 0,
        Emp_Status: "",
        Profession_Name: "",
        Emp_Profession: "Other Professionals",
        TDS_Exemption: 0,
        Confirmed: 0,
        Confirmation_Date: null,
        Field_Force: 0,
        Pf_PolicyNo: "",
      },
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    jobRoleNames() {
      return (
        this.jobDetails?.Job_Role_Details?.map(
          (role) => role.Job_Role_Name
        ).join(", ") || null
      );
    },
  },
  watch: {
    showEditForm(val) {
      let editFormOpened = this.$store.state.onboarding.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "onboarding/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.jobDetailsData && this.jobDetailsData.length > 0) {
      this.jobDetails = this.jobDetailsData[0];
    }
  },

  methods: {
    checkNullValue,
    convertMonthToYearMonthsDays,
    getCustomFieldName,
    editUpdated() {
      this.showEditForm = false;
      this.$emit("refetch-job-details");
    },
    openEditDialog() {
      this.showEditForm = true;
      mixpanel.track("Onboarded-candidate-job-edit-opened");
      this.$emit("edit-opened");
    },
    closeEditForm() {
      this.showEditForm = false;
      mixpanel.track("Onboarded-candidate-job-edit-closed");
      this.$emit("edit-closed");
    },
  },
};
</script>
