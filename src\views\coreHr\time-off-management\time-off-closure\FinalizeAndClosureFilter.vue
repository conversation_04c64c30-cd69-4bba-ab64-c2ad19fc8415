<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col class="py-2" :cols="12" :sm="6" :md="6">
            <v-autocomplete
              v-model="finalizeClosureMonth"
              label="Finalize and Closure Month"
              variant="solo"
              density="compact"
              multiple
              chips
              closable-chips
              :items="monthList"
            ></v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="12" :sm="6" :md="6">
            <v-autocomplete
              v-model="employeeType"
              label="Employee Type"
              variant="solo"
              density="compact"
              multiple
              chips
              closable-chips
              :items="dropdownList.empTypeList"
              item-title="Employee_Type"
              item-value="Employee_Type"
            ></v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="12" :sm="6" :md="6">
            <v-autocomplete
              v-model="location"
              label="Location"
              variant="solo"
              density="compact"
              multiple
              chips
              closable-chips
              item-title="Location_Name"
              item-value="Location_Id"
              :items="dropdownList.locationList"
            ></v-autocomplete>
          </v-col>

          <v-col class="py-2" :cols="12" :sm="6" :md="6">
            <v-autocomplete
              v-model="department"
              label="Department"
              variant="solo"
              density="compact"
              multiple
              chips
              closable-chips
              :items="dropdownList.departmentList"
              item-title="Department_Name"
              item-value="Department_Id"
            ></v-autocomplete>
          </v-col>

          <v-col
            class="py-2"
            :cols="12"
            :sm="6"
            :md="6"
            v-if="
              labelList[115]?.Field_Visiblity?.toLowerCase() === 'yes' &&
              fieldForce &&
              (isAdmin || isServiceProviderAdmin)
            "
          >
            <v-autocomplete
              v-model="serviceProvider"
              :label="labelList[115]?.Field_Alias || 'Service Provider'"
              variant="solo"
              density="compact"
              multiple
              chips
              closable-chips
              :items="dropdownList.serviceProviderList"
              item-title="Service_Provider_Name"
              item-value="Service_Provider_Id"
            ></v-autocomplete>
          </v-col>

          <slot name="bottom-filter-menu"></slot>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4"
          rounded="lg"
          color="primary"
          @click.stop="fnApplyFilter()"
        >
          Apply
        </v-btn>
        <v-btn
          color="primary"
          rounded="lg"
          variant="text"
          elevation="4"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "FinalizeAndClosureFilter",
  data: () => ({
    openFormFilter: false,
    // filter fields
    finalizeClosureMonth: [],
    employeeType: [],
    location: [],
    department: [],
    serviceProvider: [],
    fieldForce: 0,
  }),

  props: {
    dropdownList: {
      type: Object,
      required: true,
    },
  },

  computed: {
    monthList() {
      let monthList = this.$store.state.orgDetails.closureMonthJson;
      monthList = monthList.map((el) => {
        return moment(el, "MM,YYYY").format("MMM YYYY");
      });
      return monthList;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {},
  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filterObj = {
        finalizeClosureMonth: [...this.finalizeClosureMonth],
        employeeType: [...this.employeeType],
        location: [...this.location],
        department: [...this.department],
        serviceProvider: [...this.serviceProvider],
      };
      this.$emit("apply-filter", filterObj);
    },
    // reset filter
    resetFilterValues() {
      this.$emit("reset-filter");
      this.resetAllModelValues();
    },
    resetAllModelValues() {
      this.finalizeClosureMonth = null;
      this.employeeType = null;
      this.location = null;
      this.department = null;
      this.closureGeneration = null;
      this.serviceProvider = null;
    },
  },
};
</script>
