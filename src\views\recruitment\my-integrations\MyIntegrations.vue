<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      ></AppTopBarTab>
    </div>
    <v-container
      fluid
      class="recruitment-dashboard-container"
      v-if="formAccess.view"
    >
      <AppFetchErrorScreen
        v-if="!isLoading && !calendarsPresent && !meetingsPresent"
        image-name="common/common-error-image"
        :content="'Your organization has not enabled app synchronization.'"
      ></AppFetchErrorScreen>
      <div v-else>
        <div v-if="calendarsPresent" class="text-grey-darken-1 mb-3">
          Calendar Sync
        </div>
        <v-row>
          <v-col v-if="outlookVisible">
            <v-card
              :style="isMobileView ? '' : 'max-width:350px'"
              class="rounded-lg py-3"
            >
              <div
                class="ml-3 mr-6 d-flex justify-space-between"
                style="height: 30px"
              >
                <div class="d-flex align-center" style="width: 200px">
                  <img
                    :src="outlookLogo"
                    style="width: 20px; height: 20px"
                    alt="outlook"
                  />
                  <p
                    class="ml-2 font-weight-bold text-subtitle-1"
                    style="height: max-content"
                  >
                    Outlook 365 Calendar
                  </p>
                </div>
                <div class="d-flex justify-end align-center">
                  <v-switch
                    density="compact"
                    hide-details
                    color="primary"
                    v-model="outlookCalendarStatus"
                    true-value="Active"
                    false-value="Inactive"
                    @update:model-value="fetchAuthToken($event, 'microsoft')"
                  ></v-switch>
                </div>
              </div>
              <div style="background-color: rgb(247 247 247)" class="pa-3 my-2">
                <div bg-color="grey-lighten-2" style="font-size: small">
                  Outlook 365 allows organizations to maintaining schedules. Let
                  your talent acquisition team and hiring panel members stay in
                  sync with interview schedules. You can check interviewer's
                  availability, schedule or cancel interviews.
                </div>
              </div>
              <div
                class="ml-4 d-flex align-center"
                v-if="
                  microsoftEmail &&
                  outlookCalendarStatus?.toLowerCase() === 'active'
                "
              >
                <v-icon size="x-small" color="green" class="mr-1"
                  >fas fa-check-circle</v-icon
                >
                <span style="font-size: small"
                  >Integrated with {{ microsoftEmail }}</span
                >
              </div>
            </v-card>
          </v-col>
        </v-row>
        <div v-if="meetingsPresent" class="text-subtitle-1 text-grey mt-7">
          Online Meeting Platforms
        </div>
        <v-row>
          <v-col cols="12" sm="12" md="4" xl="3" v-if="teamsVisible">
            <v-card
              :style="isMobileView ? '' : 'width:350px'"
              class="rounded-lg mb-10 py-3"
            >
              <div
                class="ml-3 mr-6 d-flex justify-space-between"
                style="height: 30px"
              >
                <div
                  class="d-flex align-center text-subtitle-1"
                  style="width: 200px"
                >
                  <img
                    :src="teamsLogo"
                    style="width: 20px; height: 20px"
                    class=""
                    :alt="'Outlook'"
                  />
                  <p class="ml-2 font-weight-bold" style="height: max-content">
                    Microsoft Teams
                  </p>
                </div>
                <div class="d-flex justify-end align-center">
                  <v-tooltip
                    text="You don't have access to perform this action"
                  >
                    <template v-slot:activator="{ props }">
                      <v-switch
                        density="compact"
                        v-bind="formAccess.add ? '' : props"
                        :readonly="formAccess.add ? false : true"
                        color="primary"
                        true-value="Active"
                        false-value="Inactive"
                        hide-details
                        v-model="teamsStatus"
                        @update:model-value="fetchAuthToken($event, 'teams')"
                      ></v-switch>
                    </template>
                  </v-tooltip>
                </div>
              </div>
              <div style="background-color: rgb(247 247 247)" class="pa-3 my-2">
                <div bg-color="grey-lighten-2" style="font-size: small">
                  Microsoft Teams is a communication platform developed as part
                  of the Microsoft family of products. It is a video
                  conferencing and communication service.
                </div>
              </div>
              <div
                class="ml-4 d-flex align-center"
                v-if="microsoftEmail && teamsStatus == 'Active'"
              >
                <v-icon size="x-small" color="green" class="mr-1"
                  >fas fa-check-circle</v-icon
                >
                <span style="font-size: small"
                  >Integrated with {{ microsoftEmail }}</span
                >
              </div>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-container>
    <AppAccessDenied v-else></AppAccessDenied>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import outlookLogo from "../../../assets/images/common/outlook-logo.png";
import teamsLogo from "@/assets/images/common/teams-logo.png";
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
import {
  UPDATE_MY_INTEGRATION,
  GET_MY_INTEGRATION,
} from "@/graphql/recruitment/myIntegrationQueries.js";
import { GET_MICROSOFT_CREDENTIALS } from "@/graphql/settings/Integration/jobPostIntegrationQueries";
import { PublicClientApplication } from "@azure/msal-browser";
import Config from "../../../config.js";
export default {
  name: "MyIntegrations",
  data() {
    return {
      currentTabItem: "tab-0",
      outlookLogo: outlookLogo,
      teamsLogo: teamsLogo,
      outlookCalendarStatus: "",
      teamsStatus: "",
      outlookVisible: false,
      teamsVisible: false,
      clientId: "",
      microsoftEmail: "",
      isLoading: false,
    };
  },
  computed: {
    mainTabs() {
      return ["My Integrations"];
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formAccess() {
      let formAccess = this.accessRights("308");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    redirectUri() {
      return `${this.baseUrl}v3/recruitment/my-integration`;
    },
    calendarsPresent() {
      if (this.outlookVisible) {
        return true;
      } else {
        return false;
      }
    },
    meetingsPresent() {
      if (this.teamsVisible) {
        return true;
      } else {
        return false;
      }
    },
  },
  mounted() {
    this.fetchIntegrationStatus();
    this.getMicrosoftCredentials();
    this.retrieveIntegrationStatus();
  },
  methods: {
    fetchIntegrationStatus() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 242,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            let { getStatus } = response.data.jobBoardIntegrationStatus;
            vm.statusArray = getStatus;
            for (let iStatus of getStatus) {
              if (iStatus.Integration_Type === "Microsoft") {
                vm.outlookVisible =
                  iStatus.Integration_Status === "Active" ? true : false;
              } else if (iStatus.Integration_Type === "Teams Meeting") {
                vm.teamsVisible =
                  iStatus.Integration_Status === "Active" ? true : false;
              }
            }
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    retrieveIntegrationStatus() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_MY_INTEGRATION,
          variables: {
            formId: 308,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveMicrosoftIntegration
          ) {
            vm.microsoftEmail =
              response.data.retrieveMicrosoftIntegration.microsoftEmail;
            vm.outlookCalendarStatus =
              response.data.retrieveMicrosoftIntegration.calendarStatus;
            vm.teamsStatus =
              response.data.retrieveMicrosoftIntegration.teamsStatus;
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "integration status",
        isListError: false,
      });
    },
    updateMicrosoftIntegrationStatus(status) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_MY_INTEGRATION,
          client: "apolloClientAH",
          variables: {
            action: status.toLowerCase() == "inactive" ? "update" : "add",
            calendarStatus: vm.outlookCalendarStatus,
            microsoftEmail: vm.microsoftEmail,
            teamsStatus: vm.teamsStatus,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.addUpdateMicrosoftIntegration &&
            !response.data.addUpdateMicrosoftIntegration.errorCode
          ) {
            let snackbarData = {
              isOpen: true,
              message: "Integration status updated successfully.",
              type: "success",
            };
            vm.showAlert(snackbarData);
          }
          vm.retrieveIntegrationStatus();
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleUpdateIntegrationStatusError(err);
        });
    },
    handleUpdateIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "integration status",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getMicrosoftCredentials() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_MICROSOFT_CREDENTIALS,
          variables: {
            Type: "microsoft calendar",
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAWSCognitoIdentities &&
            response.data.getAWSCognitoIdentities.data &&
            response.data.getAWSCognitoIdentities.data.workwiselymsapplicationID
          ) {
            let { workwiselymsapplicationID } =
              response.data.getAWSCognitoIdentities.data;
            vm.clientId = workwiselymsapplicationID;
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleGetMicrosoftCredentialsError(err);
        });
    },
    handleGetMicrosoftCredentialsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "microsoft credentials",
        isListError: false,
      });
    },
    async fetchAuthToken(status, type) {
      if (status?.toLowerCase() === "active") {
        this.isLoading = true;
        const msalConfig = {
          auth: {
            clientId: this.clientId,
            authority: Config.microsoftLogin,
            redirectUri: this.redirectUri,
          },
          cache: {
            cacheLocation: "localStorage",
            storeAuthStateInCookie: true,
          },
        };
        let scopes = [];
        if (type?.toLowerCase() === "microsoft") {
          scopes = ["user.read", "calendars.readwrite"];
        } else if (type?.toLowerCase() === "teams") {
          scopes = ["user.read", "onlineMeetings.readwrite"];
        }
        const msalInstance = new PublicClientApplication(msalConfig);
        try {
          await msalInstance.initialize();
          let response = await msalInstance.loginPopup({
            scopes: scopes,
            prompt: "consent",
          });
          localStorage.setItem("outlookAccess", response.accessToken);
          this.microsoftEmail = response.account.username;
          this.updateMicrosoftIntegrationStatus(status);
        } catch (err) {
          this.isLoading = false;
          if (type?.toLowerCase() === "teams") {
            this.teamsStatus = "Inactive";
          }
          if (type?.toLowerCase() === "microsoft") {
            this.outlookCalendarStatus = "Inactive";
          }
          let error = err.message;
          if (!/(AADSTS65004|user_cancelled)/.test(error)) {
            let snackbarData = {
              isOpen: true,
              message: error,
              type: "warning",
            };
            this.showAlert(snackbarData);
          }
        }
      } else {
        this.updateMicrosoftIntegrationStatus(status);
      }
    },
  },
};
</script>
<style>
.recruitment-dashboard-container {
  padding: 5em 2em 0em 3em;
}
</style>
