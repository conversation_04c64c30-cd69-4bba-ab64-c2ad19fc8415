<template>
  <div class="end_node">
    <Handle type="target" :position="targetPosition"> </Handle>
    <div
      style="
        display: flex;
        align-items: center;
        position: relative;
        padding-bottom: 5px;
      "
    >
      <div
        style="
          background-color: #ffeee8;
          height: 16px;
          width: 20px;
          display: flex;
          border-radius: 5px;
          justify-content: center;
          align-items: center;
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          height="7"
          width="10"
          fill="#ff003a"
        >
          <path
            d="M464 256A208 208 0 1 0 48 256a208 208 0 1 0 416 0zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z"
          />
        </svg>
      </div>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
        "
      >
        <span class="d-flex item-center header_text text-center">End</span>
        <div class="close_icon" @click="confirmationModel = true">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="#ffffff"
            height="7"
            width="7"
            viewBox="0 0 384 512"
          >
            <path
              d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            />
          </svg>
        </div>
      </div>
    </div>

    <AppWarningModal
      v-if="confirmationModel"
      :open-modal="confirmationModel"
      iconName="fas fa-trash"
      confirmation-heading="Are you sure to delete?"
      @close-warning-modal="confirmationModel = false"
      @accept-modal="deleteNode()"
    >
    </AppWarningModal>
  </div>
</template>
<script>
import { Position, Handle } from "@vue-flow/core";
export default {
  name: "AddEndNode",
  emits: ["handleToStart", "deleteNode"],
  props: {
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    sourcePosition: {
      type: String,
      required: true,
    },
    targetPosition: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      leftPosition: Position.Top,
      rightPosition: Position.Bottom,
      showMenu: false,
      addNewNode: false,
      confirmationModel: false,
    };
  },
  methods: {
    handleToStartNode(type) {
      this.$emit("handleToStart", type, this.data, false, 0);
    },
    deleteNode() {
      this.confirmationModel = false;
      this.$emit("deleteNode", this.data);
    },
  },
  components: {
    Handle,
  },
};
</script>
<style>
.end_node {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 5px;
  min-width: 80px;
}

.end_node::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  /* background-color: #ff003a; */
  background: linear-gradient(to right, #fc2222, #ffa18c);
  height: 4px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.end_node:hover .close_icon {
  visibility: visible !important;
}
</style>
